{"core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayfanın bir bölümüne hızlıca odaklanmalarını sağlar. Gezinmenin düzgün bir şekilde gerçekleştirilebilmesi için her bir erişim anahtarının benzersiz olması gerekir. [<PERSON><PERSON><PERSON><PERSON> anahtarları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Her ARIA `role`, be<PERSON><PERSON><PERSON> bir `aria-*` özellik alt kümesini destekler. Bunların e<PERSON>şmemesi `aria-*` özelliklerini geçersiz kılar. [ARIA özelliklerini rolleriyle nasıl eşleştireceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` özellikleri rolleriyle eşleşiyor"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Bir öğenin erişilebilir özellikli adı olmadığında ekran okuyucular tarafından genel adla okunur ve öğe, ekran okuyuculardan yararlanan kullanıcılar için kullanılamaz hale gelir. [<PERSON><PERSON><PERSON> öğelerini nasıl daha erişilebilir hale getireceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link` ve `menuitem` öğelerinin erişilebilir özellikli adları yok."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link` ve `menuitem` öğelerinin erişilebilir özellikli adları var."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Ekran okuyucular gibi yardım<PERSON><PERSON> teknolojiler, `aria-hidden=\"true\"` öğ<PERSON> do<PERSON> `<body>` kısmında ayarlandığında tutarsız çalışır. [`aria-hidden` öğesinin doküman gövdesini nasıl etkilediğini öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `<body>` kı<PERSON><PERSON><PERSON> `[aria-hidden=\"true\"]` mevcut"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `<body>` kı<PERSON><PERSON><PERSON> `[aria-hidden=\"true\"]` mevcut değil"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "`[aria-hidden=\"true\"]` ö<PERSON><PERSON> içindeki odaklanabilir alt öğeler, bu etkileşimli öğelerin ekran okuyucu gibi yardımcı teknolojilerin kullanıcılarına sunulmasını engeller. [`aria-hidden` öğesinin odaklanılabilir öğeleri nasıl etkilediğini öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` <PERSON>ğ<PERSON>rinde odaklanabilir alt öğe bulunuyor"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` <PERSON>ğ<PERSON>rinde odaklanabilir alt öğe bulunmuyor"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Bir giriş alanının erişilebilir özellikli bir adı yoksa ekran okuyucular bu alanı genel adla okuyacağı için bu, ekran okuyuculardan yararlanan kullanıcılar için yararlı olmaz. [<PERSON><PERSON><PERSON> alanı etiketleri hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA giriş alanlarının erişilebilir özellikli adı yok"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA giriş alanları erişilebilir özellikli ada sahip"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Ölçüm öğesinin erişilebilir özellikli adı olmadığında ekran okuyucular tarafından genel adla okunur, dolayısıyla ekran okuyuculardan yararlananlar için kullanışsız olur. [`meter` öğ<PERSON>rini nasıl adlandıracağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` öğelerinin erişilebilir özellikli adları yok."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` öğelerinin erişilebilir özellikli adları var"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "`progressbar` ö<PERSON><PERSON>nin erişilebilir özellikli adı olmadığında ekran okuyucular tarafından genel adla okunur, dolayısıyla ekran okuyuculardan yararlananlar için kullanışsız olur. [`progressbar` ö<PERSON><PERSON>rini nasıl etiketleyeceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` öğelerinin erişilebilir özellikli adları yok."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` öğelerinin erişilebilir özellikli adları var"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON>z<PERSON> <PERSON>, <PERSON>ğenin durumunu ekran okuyuculara açıklayan gerekli özelliklere sahiptir. [Roller ve gerekli özellikler hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` <PERSON>i gereken tüm `[aria-*]` özelliklerine <PERSON>"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON>, gereken tüm`[aria-*]` özelliklerine sahip"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Bazı ARIA üst rollerinin amaçlanan erişilebilirlik işlevlerini gerçekleştirebilmek için belirli alt rolleri içermesi gerekir. [Roller ve gerekli alt öğeler hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ARIA `[role]` sa<PERSON><PERSON> olup alt öğelerin belirli bir `[role]` içermesini gerektiren öğelerde bu gerekli alt öğelerin bazıları veya hiçbiri bulunmuyor."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "ARIA `[role]` sa<PERSON><PERSON> olup alt öğelerin belirli bir `[role]` içermesini gerektiren öğelerde gerekli alt öğelerin hepsi bulunuyor."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Bazı ARIA alt rollerinin amaçlanan erişilebilirlik işlevlerini gerektiği gibi gerçekleştirebilmesi için belirli üst rollerin içinde bulunması gerekir. [ARIA rolleri ve gerekli üst öğe hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` <PERSON>i gerekli üst öğelerinin içinde bulunmuyor"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON> gerekli üst öğelerinin içinde bulunuyor"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA rollerinin amaçlanan erişilebilirlik işlevlerini gerçekleştirebilmesi için geçerli değerlere sahip olması gerekir. [Geçerli ARIA rolleri hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON> ge<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Bir açma/kapatma alanının erişilebilir özellikli bir adı yoksa ekran okuyucular bu alanı genel adla okuyacağı için bu, ekran okuyuculardan yararlanan kullanıcılar için yararlı olmaz. [Açma/kapatma alanları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA açma/kapatma alanlarının erişilebilir özellikli adı yok"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA açma/kapatma alanları erişilebilir özellikli ada sahip"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "İpucu öğesinin erişilebilir özellikli adı olmadığında ekran okuyucular tarafından genel adla okunur, dolayısıyla ekran okuyuculardan yararlananlar için kullanışsız olur. [`tooltip` öğelerini nasıl adlandıracağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` öğelerinin erişilebilir özellikli adları yok."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` öğelerinin erişilebilir özellikli adları var"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "`treeitem` öğesinin erişilebilir özellikli adı olmadığında ekran okuyucular tarafından genel adla okunur, dolayısıyla ekran okuyuculardan yararlananlar için kullanışsız olur. [`treeitem` ö<PERSON><PERSON><PERSON> etiketleme hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` öğelerinin erişilebilir özellikli adları yok."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` öğelerinin erişilebilir özellikli adları var"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Ekran okuyucular gibi yardımcı teknolojiler geçersiz değerlere sahip ARIA özelliklerini yorumlayamaz. [ARIA özellikleri için geçerli değerler hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` özellikleri geçerli değerlere sahip <PERSON>ğil"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` özelliklerinin geçerli değerleri var"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Ekran okuyucular gibi yardımcı teknolojiler geçersiz adlara sahip ARIA özelliklerini yorumlayamaz. [Geçerli ARIA özellikleri hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` özellikleri geçerli değil veya yanlış yazılmış"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` özellikleri geçerli ve yanlış yazılmamış"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Başarısız <PERSON>"}, "core/audits/accessibility/button-name.js | description": {"message": "Düğmelerin erişilebilir özellikli adı olmadığında ekran okuyucular bu düğmeyi yalnızca \"düğme\" olarak okur. Bu da, ekran okuyuculardan yararlanan kullanıcılar için yararlı olmaz. [Düğmeleri nasıl daha erişilebilir yapabileceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Düğmelerin erişilebilir adları yok"}, "core/audits/accessibility/button-name.js | title": {"message": "Düğmeler erişilebilir bir ada sahip"}, "core/audits/accessibility/bypass.js | description": {"message": "Tekrar eden içeriği atlamanın yollarını eklemek, klav<PERSON> kullanıcılarının sayfada daha verimli bir şekilde gezinmesini sağlar. [Atlama blokları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Bu sayfa bir ba<PERSON><PERSON><PERSON><PERSON>, atlama bağlantısı veya önemli nokta bölgesi içermiyor"}, "core/audits/accessibility/bypass.js | title": {"message": "Sayfa bir ba<PERSON><PERSON><PERSON><PERSON>, atlama bağlantısı veya önemli nokta bölgesi içeriyor"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Birçok kullanıcı, <PERSON><PERSON><PERSON><PERSON>k kontrastlı metni okumakta zorlanır veya okuyamaz. [Yet<PERSON><PERSON> renk kontrastını nasıl sağlayacağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Arka plan ve ön plan renkleri yeterli kontrast oranına sa<PERSON> değil."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Arka plan ve ön plan renkleri yeterli kontrast oranına sahip"}, "core/audits/accessibility/definition-list.js | description": {"message": "Tanım listeleri gerektiği gibi işaretlenmediğinde ekran okuyucular kafa karışıklığına yol açan veya yanlış çıkışlar sunabilir. [Tanımlama listelerini nasıl doğru yapılandıracağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` ö<PERSON><PERSON><PERSON> yalnızca gerektiği gibi sıralanmış `<dt>` ve `<dd>` gruplarını, `<script>`, `<template>` veya `<div>` ö<PERSON><PERSON>rini içermiyor."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` ö<PERSON><PERSON><PERSON> yalnızca gerektiği gibi sıralanmış `<dt>` ve `<dd>` gruplarını, `<script>`, `<template>` veya `<div>` öğ<PERSON>rini içeriyor."}, "core/audits/accessibility/dlitem.js | description": {"message": "<PERSON><PERSON>m listesi öğelerinin (`<dt>` ve `<dd>`) ekran okuyucular tarafından düzgün bir şekilde duyurulabilmesi için üst `<dl>` öğesinin içine yerleştirilmesi gerekir. [<PERSON><PERSON><PERSON><PERSON><PERSON> listelerini nasıl doğru yapılandıracağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> listesi <PERSON> `<dl>` öğ<PERSON>ri a<PERSON>ına <PERSON>"}, "core/audits/accessibility/dlitem.js | title": {"message": "<PERSON><PERSON><PERSON> listesi <PERSON> `<dl>` öğ<PERSON>ri a<PERSON>ına <PERSON>"}, "core/audits/accessibility/document-title.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucu kullanıcılarına sayfayla ilgili genel bir fikir verir ve arama motoru kullanıcılarının bir sayfanın aramalarıyla ilgili olup olmadığını belirlemeleri açısından son derece önemlidir. [<PERSON><PERSON><PERSON>man başlıkları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `<title>` ö<PERSON><PERSON> yok"}, "core/audits/accessibility/document-title.js | title": {"message": "Doküman geçerli bir `<title>` ö<PERSON><PERSON> içeriyor"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Odaklanabilir tüm öğelerde yardımcı teknolojilere görünür olmalarını sağlamak için benzersiz bir `id` olmalıdır. [Yinelenen `id` öğ<PERSON>rini nasıl düzelteceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, odaklanabilir öğelerdeki `[id]` özellikleri benzersiz <PERSON>ğil"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "<PERSON><PERSON><PERSON>, odaklanabilir öğelerdeki `[id]` özellikleri benzersiz"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA kimliğinin değeri benzersiz olmalıdır. <PERSON><PERSON><PERSON>, yardımcı teknolojilerin farkına varmadan diğer örnekleri atlamasını önlemektir. [Yinelenen ARIA kimliklerini nasıl düzelteceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA kimlikleri benzersiz <PERSON>"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA kimlikleri benzersiz"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Birden çok etikete sahip form alanları etiketlerin ilkini, sonuncusunu veya tümünü kullanan ekran okuyucular gibi yardımcı teknolojiler tarafından karışıklık yaratacak şekilde okunabilir. [Form etiketlerini nasıl kullanacağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Form alanlarında birden çok etiket var"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Hiçbir form alanının birden fazla etiketi yok"}, "core/audits/accessibility/frame-title.js | description": {"message": "Ekran okuyucu k<PERSON>, çerçevelerin içeriklerinin açıklanması için çerçeve başlıklarından yararlanır. [Çerçeve başlıkları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` veya `<iframe>` <PERSON><PERSON><PERSON><PERSON>n başlığı yok"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` veya `<iframe>` ö<PERSON><PERSON> bir başlığa sahip"}, "core/audits/accessibility/heading-order.js | description": {"message": "Düzey atlamayan gerektiği gibi sıralanmış başlıklar sayfanın anlamsal yapısını iletir. Böylece, yardımcı teknolojiler kullanılırken gezinmeyi ve anlaşılmayı kolaylaştırır. [Başlık sıralaması hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Başlık öğeleri sırayla azalan düzende sıralı değil"}, "core/audits/accessibility/heading-order.js | title": {"message": "Başlık öğeleri sırayla azalan düzende sıralanmış görüntülenir"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "<PERSON><PERSON> bir `lang` ö<PERSON><PERSON><PERSON>i belirtmezse ekran okuyucu, say<PERSON><PERSON>n kullanıcı ekran okuyucuyu kurarken seçtiği varsayılan dilde olduğunu varsayar. Say<PERSON> aslında varsayılan dilde değilse ekran okuyucu, say<PERSON><PERSON>n metnini doğru bir şekilde duyurmayabilir. [`lang` özelliği hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` <PERSON><PERSON><PERSON><PERSON> `[lang]` ö<PERSON><PERSON><PERSON><PERSON> yok"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` <PERSON><PERSON><PERSON> `[lang]` özelliği içeriyor"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Geç<PERSON><PERSON> bir [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) beli<PERSON><PERSON><PERSON><PERSON>, ekran okuyucuların metni düzgün bir şekilde duyurmasına yardımcı olur. [`lang` özelliğini nasıl kullanacağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` <PERSON><PERSON><PERSON>, `[lang]` ö<PERSON><PERSON>ği için geçerli bir değeri sahip değil."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` <PERSON><PERSON><PERSON>, `[lang]` ö<PERSON><PERSON>ği için geçerli bir değere sahip"}, "core/audits/accessibility/image-alt.js | description": {"message": "Bilgilendirme amaçlı öğelerin hede<PERSON>, kısa ve açıklayıcı alternatif metinler olmalıdır. Dekoratif öğeler boş bir alt özelliğiyle yok sayılabilir. [`alt` özelliği hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[alt]` özellikleri yok"}, "core/audits/accessibility/image-alt.js | title": {"message": "Resim <PERSON> `[alt]` özellikleri var"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Bir resim `<input>` düğ<PERSON>i olarak kullanıldığında alternatif metin sağ<PERSON>, ekran okuyucu kullanıcılarının düğmenin amacını anlamalarına yardımcı olabilir. [Giriş resmi alternatif metni hakkında bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` <PERSON><PERSON><PERSON><PERSON>n `[alt]` metni yok"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` <PERSON><PERSON><PERSON><PERSON>n `[alt]` metni var"}, "core/audits/accessibility/label.js | description": {"message": "Etiketler, form kontrollerinin ekran okuyucular gibi yardımcı teknolojiler tarafından düzgün bir şekilde duyurulmasını sağlar. [Form öğesi etiketleri hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Form öğelerinin ilişkili etiketleri yok"}, "core/audits/accessibility/label.js | title": {"message": "Form öğelerinin ilişkili etiketleri var"}, "core/audits/accessibility/link-name.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> edilebilir, ben<PERSON><PERSON> ve odaklanılabilir bağlant<PERSON> metni (ve bağlantı olarak kullanıldığında resimler için alternatif metin), ekran okuyucu kullanıcılarına daha iyi bir gezinme deneyimi sunar. [Bağlantıları nasıl erişilebilir hale getireceğinizi öğrenin](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Bağlantıların ayırt edilebilir adları yok"}, "core/audits/accessibility/link-name.js | title": {"message": "Bağlantıların ayırt edilebilir adları var"}, "core/audits/accessibility/list.js | description": {"message": "Ekran okuyucular listeleri duyurmak için belirli bir yöntem kullanır. Liste yapısının gerektiği gibi olmasını sağlamak, ekran okuyucu çıkışının düzgün olmasına yardımcı olur. [Düzgün liste yapısı hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listeler yalnızca `<li>` öğelerini ve komut dosyası destekleyen öğeleri (`<script>` ve `<template>`) içermiyor."}, "core/audits/accessibility/list.js | title": {"message": "Listeler yalnızca `<li>` öğelerini ve komut dosyası destekleyen öğeleri (`<script>` ve `<template>`) içeriyor."}, "core/audits/accessibility/listitem.js | description": {"message": "Ekran okuyucuların liste öğelerini (`<li>`) düzgün bir şekilde okuyabilmesi için liste öğelerinin, üst `<ul>`, `<ol>` veya `<menu>` öğesinde yer alması gerekir. [Düzgün liste yapısı hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Liste öğeleri (`<li>`), `<ul>`, `<ol>` veya `<menu>` üst öğelerinde yer almıyor."}, "core/audits/accessibility/listitem.js | title": {"message": "Liste öğeleri (`<li>`), `<ul>`, `<ol>` veya `<menu>` üst öğelerinde yer alıyor"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Kullanıcılar bir sayfanın otomatik olarak yenileneceğini düşünmez ve bu işlem yeniden sayfanın üst tarafına odaklanılmasına neden olur. Bu durum, can sıkıcı veya kafa karışıklığına yol açan bir deneyime yol açabilir. [Yenileme meta etiketi hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Doküman `<meta http-equiv=\"refresh\">` kullanıyor"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Doküman `<meta http-equiv=\"refresh\">` öğesini kullanmıyor"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Yakınlaştırmanın devre dışı bırakılması, az gören ve bir web sayfasının içeriğini düzgün bir şekilde görebilmek için ekran büyütme özelliğinden yararlanan kullanıcılar için sorun teşkil eder. [Görüntü alanı meta etiketi hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]`, `<meta name=\"viewport\">` öğesinde kullanılmış veya `[maximum-scale]` özelliği 5'ten küçük."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]`, `<meta name=\"viewport\">` öğesinde kullanılmamış ve `[maximum-scale]` özelliği 5'ten küçük değil."}, "core/audits/accessibility/object-alt.js | description": {"message": "Ekran okuyucular metin dışı içeriği çeviremez. `<object>` öğelerine alternatif metin ekle<PERSON>, ekran okuyucuların ilgili öğenin ne anlama geldiğini kullanıcılara söylemesine yardımcı olur. [`object` öğeleri için alternatif metinler hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` öğelerinin alternatif metni yok"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` öğelerinin alternatif metni var"}, "core/audits/accessibility/tabindex.js | description": {"message": "0'dan b<PERSON><PERSON><PERSON><PERSON> bir <PERSON>, açık bir gezinme sıralamasını belirtir. Bu durum teknik olarak geçerli olsa da yardımcı teknolojilerden yararlanan kullanıcıların genellikle sinir bozucu deneyimler yaşamalarına neden olur. [`tabindex` özelliği hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Bazı öğeler 0'dan büyük bir `[tabindex]` değeri içeriyor"}, "core/audits/accessibility/tabindex.js | title": {"message": "Hiçbir öğe 0'dan büyük `[tabindex]` değeri içermiyor"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Ekran okuyucuların tablolarda daha kolay gezinmeyi sağlayan özellikleri vardır. `[headers]` özelliğini kullanan `<td>` hücrelerinin yalnızca aynı tablodaki diğer hücrelere atıfta bulunmasını sağlamak, ekran okuyucu kullanıcılarına daha iyi bir deneyim sunabilir. [`headers` özelliği hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` öğesinde olu<PERSON> `[headers]` özelliğini kullanan hücrelerin atıfta bulunduğu öğe `id` aynı tabloda yer almıyor."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` öğ<PERSON><PERSON> olu<PERSON> `[headers]` özelliğini kullanan hücreler, aynı tablodaki tablo hücrelerine atıfta bulunuyor."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Ekran okuyucuların tablolarda daha kolay gezinmeyi sağlayan özellikleri vardır. Tablo başlıklarının her zaman bazı hücre kümelerine atıfta bulunmasını sağlamak, ekran okuyucu kullanıcılarına daha iyi bir deneyim sunabilir. [Tablo başlıkları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` <PERSON><PERSON><PERSON><PERSON><PERSON> ve `[role=\"columnheader\"/\"rowheader\"]` içeren öğelerin açıkladıkları veri hücreleri yok."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` <PERSON><PERSON><PERSON><PERSON><PERSON> ve`[role=\"columnheader\"/\"rowheader\"]` içeren öğelerin açıkladıkları veri hücreleri var."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Öğelerde geçerli bir [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) beli<PERSON><PERSON><PERSON><PERSON>, ekran okuyucunun metni doğru telaffuz etmesini sağlar. [`lang` özelliğini nasıl kullanacağınızı öğrenin](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` özellikleri geçerli bir değere sahip değil"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` özellikleri geçerli bir değere sahip"}, "core/audits/accessibility/video-caption.js | description": {"message": "Bir videoda altyazının yer alması işitme engelli ve işitme zorluğuna sahip kullanıcıların videonun bilgilerine daha kolay erişmelerini sağlar. [Video altyazıları hakkında daha fazla bilgi edinin](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]` i<PERSON>eren bir `<track>` öğ<PERSON> bulunmuyor."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]`i<PERSON>eren bir `<track>` ö<PERSON><PERSON> bulunuyor"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete`, kullanıcıların formları daha hızlı göndermesine yardımcı olur. Kullanıcının yapması gerekenleri azaltmak için `autocomplete` özelliğini geçerli bir değere ayarlayarak etkinleştirmeyi düşünün. [Formlardaki `autocomplete` hakkında daha fazla bilgi edinin](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` öğ<PERSON>rinin doğru `autocomplete` özellikleri yok"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON> in<PERSON> gere<PERSON>"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Jetonların sırasını inceleyin"}, "core/audits/autocomplete.js | title": {"message": "`<input>` <PERSON><PERSON><PERSON><PERSON>, do<PERSON><PERSON> bir <PERSON> `autocomplete` kullanıyor"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` jetonları: {snippet} içindeki \"{token}\" geçersiz"}, "core/audits/autocomplete.js | warningOrder": {"message": "Jetonların sırasını inceleyin: {snippet} içindeki \"{tokens}\"."}, "core/audits/bf-cache.js | actionableFailureType": {"message": "İşlem Yapılabilir"}, "core/audits/bf-cache.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> gez<PERSON><PERSON>, önceki sayfaya geri dönerek veya tekrar ileri giderek yapılır. Geri-il<PERSON> (bfcache), bu geri dönme gezinmelerini hızlandırabilir. [Bfcache hakkında daha fazla bilgi](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 hata nedeni}other{# hata nedeni}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON> ne<PERSON>i"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON><PERSON>, geri-<PERSON><PERSON> geri <PERSON><PERSON> engel<PERSON>i"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON> t<PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "İşlem yapılamaz"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Tarayıcı desteği beklemede"}, "core/audits/bf-cache.js | title": {"message": "<PERSON><PERSON>, geri-<PERSON><PERSON> geri <PERSON><PERSON> en<PERSON>i"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome uzantıları bu sayfanın yükleme performansını olumsuz etkilemiştir. Sayfayı gizli modda veya uzantı içermeyen bir Chrome profilinden denetlemeyi deneyin."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Komut Dosyası Değerlendirmesi"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Komut Dosyası Ayrıştırma"}, "core/audits/bootup-time.js | columnTotal": {"message": "Toplam CPU Süresi"}, "core/audits/bootup-time.js | description": {"message": "JS'y<PERSON>, derleme ve yürütme için harcanan zamanı kısaltmayı düşünün. Daha küçük JS yüklerinin sağlanması bu konuda yardımcı olabilir. [JavaScript yürütme süresini nasıl azaltacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "JavaScript yürütme süresini azaltın"}, "core/audits/bootup-time.js | title": {"message": "JavaScript yürütme süresi"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON>ğ<PERSON>n kullandığı bayt sayısını azaltmak için paketlerden büyük, yinelenen JavaScript modüllerini kaldırın. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "JavaScript paketlerindeki yinelenen modülleri kaldırın"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Büyük GIF'ler, animasyonlu içeriğin sunulmasında verimli olmaz. Ağ veri miktarından tasarruf etmek üzere animasyonlar için MPEG4/WebM videoları ve statik resimler için GIF yerine PNG/WebP kullanma seçeneğini değerlendirin. [Verimli video biçimleri hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)."}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Animasyonlu içerik için video biçimleri kullanın"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Çoklu dolgular ve <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eski taray<PERSON>ıların yeni JavaScript özelliklerini kullanmasını sağlar. Ancak, çoğu modern tarayıcılar için gerekli değildir. Eski tarayıcılara verilen desteği sü<PERSON>ürürken, modern tarayıcılara gönderilen kod miktarını azaltmak üzere modüllü/modülsüz özellik algılamayı kullanarak JavaScript paketiniz için modern bir komut dosyası dağıtım stratejisi kullanın. [Modern JavaScript'i nasıl kullanacağınızı öğrenin](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Modern tarayıcılara eski JavaScript sunmaktan kaçının"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP ve AVIF gibi resim biçimleri genellikle PNG veya JPEG'e kıyasla daha iyi sıkıştırma sağlar. Böylece indirme işlemleri daha hızlı tamamlanır ve veri tüketimi daha az olur. [Modern resim biçimleri hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Resimleri yeni nesil biçimlerde yayınlayın"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Etkileşim için hazır olma süresini kısaltmak için ekran dışı ve gizli resimleri, tüm kritik kaynakların yüklenmesi bittikten sonra gecikmeli olarak yükleme seçeneğini değerlendirin. [Ekran dışındaki resimleri nasıl geciktireceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Ekran dışındaki resimleri ertele"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, say<PERSON><PERSON>zda ilk boyamayı engelliyor. Kritik JS/CSS'yi satır içinde yayınlama ve kritik olmayan tüm JS/stilleri erteleme seçeneğini değerlendirin. [Oluşturmaları engelleyen kaynaklardan nasıl kaçınacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Oluşturmayı engelleyen kaynakları ortadan kaldırın"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Büyük ağ yükleri kullanıcılar için parasal maliyet anlamına gelir ve yükleme sürelerinin uzamasında yüksek bir etkiye sahiptir. [Yük boyutlarını nasıl düşüreceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Toplam boyut {totalBytes, number, bytes} KiB'tı"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Çok büyük ağ yüklerinden kaçının"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Çok büyük ağ yüklerini önler"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS dosyalarının küçültülmesi ağ yükü boyutlarını azaltabilir. [CSS'yi nasıl sadeleştireceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS'yi <PERSON>"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript dosyalarının küçültülmesi yük boyutlarını azaltıp komut dosyası ayrıştırma süresini kısaltabilir. [JavaScript'i nasıl sadeleştireceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript'i küçült"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON>liğ<PERSON>n kullandığı bayt sayısını azaltmak üzere stil sayfalarında kullanılmayan kuralları azaltıp ekranın üst kısmında içerik için kullanılmayan CSS'yi erteleyin. [Kullanılmayan CSS'yi nasıl azaltacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Kullanılmayan CSS'yi azaltın"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON>n kullandığı bayt sayısını azaltılmaları gerekene kadar kullanılmayan JavaScript'i azaltıp komut dosyalarını erteleyin. [Kullanılmayan JavaScript'i nasıl azaltacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Kullanılmayan JavaScript'i azaltın"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Uzun önbellek ömrü, say<PERSON><PERSON><PERSON>ın tekrar ziyaret edilmesi sürecini hızlandırabilir. [Verimli önbellek politikaları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 kaynak bulundu}other{# kaynak bulundu}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statik öğeleri verimli bir önbellek politikasıyla yayınlayın"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Statik öğelerde verimli önbellek politikası kullanır"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimize edilmiş resimler daha hızlı yüklenir ve daha az hücresel veri kullanır. [Resimleri verimli bir şekilde nasıl kodlayacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Resimleri verimli bir şekilde k<PERSON>layın"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Resimler gösterilen boy<PERSON>larından daha bü<PERSON>ük"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Resimler gösterilen boyutlar i<PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Hücresel veriden tasarruf etmek ve yükleme süresini kısaltmak için uygun boyutlu resimler sunun. [Resimleri nasıl boyutlandıracağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON><PERSON>uta sahip resimler"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON> kayna<PERSON>, toplam ağ baytı sayısını en aza indirmek için sıkıştırı<PERSON>ak (gizp, deflate veya brotli) yayınlanmalıdır. [Metin sıkıştırma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "<PERSON><PERSON> s<PERSON>ştırmayı etkinleştirin"}, "core/audits/content-width.js | description": {"message": "Uygulamanızın içeriğinin g<PERSON>ği, g<PERSON><PERSON><PERSON><PERSON><PERSON> alanının genişliğiyle eşleşmiyorsa uygulamanız mobil ekranlar için optimize edilmemiş olabilir. [İçerikleri görüntü alanına göre nasıl boyutlandıracağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "{innerWidth} piksel g<PERSON> boyut<PERSON>, {outerWidth} piksel pencere boyut<PERSON>la eşleşmiyor."}, "core/audits/content-width.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, görü<PERSON>ü alanı için doğru boyutlandırılmadı"}, "core/audits/content-width.js | title": {"message": "İçerik görüntü alanı için doğru boyutlandırıldı"}, "core/audits/critical-request-chains.js | description": {"message": "Aşağıdaki Kritik İstek Zincirleri, hangi kaynakların yüksek öncelikle yüklendiğini göstermektedir. Sayfa yüklemesini iyileştirmek için zincir uzunluğunu azaltma, kaynakların indirme boyutunu küçültme veya gereksiz kaynakların indirilmesini erteleme seçeneğini değerlendirin. [Birbirini takip eden kritik istekleri nasıl önleyeceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 zincir bulundu}other{# zincir bulundu}}"}, "core/audits/critical-request-chains.js | title": {"message": "Kritik istek zinciri oluşturmaktan kaçın"}, "core/audits/csp-xss.js | columnDirective": {"message": "Yönerge"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Önem derecesi"}, "core/audits/csp-xss.js | description": {"message": "Güçlü bir İçerik Güvenliği Politikası (İGP), siteler arası komut dosyası çalıştırma (XSS) saldırısı riskini önemli ölçüde azaltır. [XSS'i önlemek için İGP'yi nasıl kullanacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "<PERSON><PERSON><PERSON> dizimi"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Bu sayfa <meta> etikette tanımlanmış bir CSP içeriyor. CSP'yi HTTP başlığına taşımayı veya HTTP başlığında başka bir katı CSP tanımlamayı düşünebilirsiniz."}, "core/audits/csp-xss.js | noCsp": {"message": "Zorunlu modda hiç CSP bulunmadı"}, "core/audits/csp-xss.js | title": {"message": "XSS saldırıları karşısında CSP'nin etkinliğini sınayın"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Kullanı<PERSON>dan <PERSON> / Uyarı"}, "core/audits/deprecations.js | columnLine": {"message": "Satır"}, "core/audits/deprecations.js | description": {"message": "Desteği sonlandırılan API'ler zamanla tarayıcıdan kaldırılacaktır. [Desteği sonlandırılan API'ler hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 uyarı bulundu}other{# uyarı bulundu}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Kullanımdan kaldırılan API'leri kullanıyor"}, "core/audits/deprecations.js | title": {"message": "Kullanımdan kaldırılan API'leri içermiyor"}, "core/audits/dobetterweb/charset.js | description": {"message": "Karakter kodlama tanımlaması gerekiyor. Bu, HTML'nin ilk 1.024 baytı içinde veya İçerik Türü HTTP yanıt başlığında bir `<meta>` etiketi ile yapılabilir. [Karakter kodlamayı açıklama hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Karakter kümesi tanımlaması eksik veya HTML'de çok geç gerçekleşiyor"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON> kü<PERSON>ini düzgün şekilde tanımlıyor"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Bir DOCTYPE belirlemek, tarayıcının Quirks moduna geçmesini önler. [Doctype açıklaması hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype adı, `html` di<PERSON>i olmalıdır"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "<PERSON><PERSON><PERSON><PERSON>, `limited-quirks-mode` tetikleyicisi olan bir `doctype` içeriyor"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Doküman bir DOCTYPE içermelidir"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Beklenen publicId boş bir dize olmalı"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Beklenen systemId boş bir dize olmalı"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "<PERSON><PERSON><PERSON><PERSON>, `quirks-mode` tetikley<PERSON><PERSON> olan bir `doctype` içeriyor"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Sayfada HTML DOCTYPE eksik, bu nedenle Quirks modunu tetikliyor"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Sayfa HTML DOCTYPE içeriyor"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "İstatistik"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "B<PERSON><PERSON><PERSON>k bir DOM, bellek kullanımını artırarak daha uzun [stil hesaplamalarına](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) neden olabilir ve yüksek maliyetli [düzen yeniden düzenlemeleri](https://developers.google.com/speed/articles/reflow) üretebilir. [DOM'un aşırı bü<PERSON><PERSON><PERSON>ini nasıl önleyeceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 öğe}other{# öğe}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Aşırı büyük bir DOM boyutundan kaçının"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimum DOM Derinliği"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Tüm DOM Öğeleri"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON>um Alt Öğe"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Aşırı büyük bir DOM boyutunu önler"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Kullanıcılar herhangi bir bağlam olmadan konum bilgilerini isteyen sitelere şüpheyle bakarlar veya bu istek karşısında şaşırırlar. Onun yerine isteği bir kullanıcı işlemine bağlamayı değerlendirin. [Coğrafi konum izni hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "<PERSON><PERSON>mede coğrafi konum izni istiyor"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON>mede coğrafi konum izni istemiyor"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome Geliştirici Araçları'ndaki `Issues` paneline kaydedilen sorunlar, çözülmemiş sorunları ifade eder. Bunlar ağ istek hatalarından, yetersiz güvenlik kontrollerinden ve diğer tarayıcı sorunlarından gelebilir. <PERSON>üm sorunlarla ilgili ayrıntılı bilgiyi, Chrome Geliştirici Araçları'nın Issues panelinde bulabilirsiniz."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Geliştirici <PERSON>n<PERSON> `Issues` paneline kaydedildi"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Çapraz kaynak politikası tarafından engellendi"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Reklamlar tarafından yoğun kaynak kullanımı"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome Geliştirici <PERSON>'ndaki `Issues` panelinde hiç sorun yok"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "S<PERSON>r<PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Sayfadaki tüm JavaScript kitaplıkları kullanıcı arabirimleri algılandı. [Bu JavaScript kitaplığı belirleme teşhis denetlemesi hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript kitaplıkları algılandı"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "`document.write()` kullanılarak dinamik olarak enjekte edilen harici komut dosyaları, yavaş bağlantıya sahip kullanıcılar için sayfa yüklemeyi onlarca saniye geciktirebilir. [Kodunuzu document.write() yönteminden nasıl arındıracağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` kull<PERSON><PERSON><PERSON><PERSON> kaçının"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` öğesinden kaçınıyor"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Kullanıcılar herhangi bir bağlam olmadan bildirim göndermek isteyen sitelere şüpheyle bakarlar veya bu istek karşısında kafaları karışır. Onun yerine isteği kullanıcı hareketlerine bağlamayı değerlendirin. [Bildirimler için duyarlı bir şekilde izin alma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "<PERSON><PERSON>mede bildirim izni istiyor"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON> bildirim izni istemiyor"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2, HTTP/1.1'e kıyasla ikili başlıklar ve çoğullama gibi birçok avantaj sunar. [HTTP/2 hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 istek HTTP/2 üzerinden sunulmuyor}other{# istek HTTP/2 üzerinden sunulmuyor}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2'yi kullan"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Sayfanızın kaydırma performansını artırmak için dokunma ve tekerlek etkinliği işleyicilerini `passive` olarak işaretlemeyi değerlendirin. [Pasif etkinlik işleyicileri kullanma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Kaydırma performansını artırmak için pasif işleyicileri kullanmıyor"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Kaydırma performansını artırmak için pasif işleyicileri kullanıyor"}, "core/audits/errors-in-console.js | description": {"message": "Konsola kaydedilen hatalar çözülmemiş problemleri belirtir Bunlar, ağ istek hatalarından ve diğer tarayıcı sorunlarından gelebilir. [Konsol teşhis denetlemesindeki hatalar hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "Tarayıcı hataları konsola kaydedildi"}, "core/audits/errors-in-console.js | title": {"message": "Konsola tarayıcı hatası kaydedilmedi"}, "core/audits/font-display.js | description": {"message": "Web yazı tipleri yüklenirken metnin kullanıcı tarafından görünür olmasını sağlamak için `font-display` CSS özelliğinden yararlanın. [`font-display` hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Web yazı tipi yüklemesi sırasında metnin görün<PERSON>r halde kalmasını sağlayın"}, "core/audits/font-display.js | title": {"message": "Web yazı tipi yüklenirken tüm metin gör<PERSON><PERSON><PERSON>r halde kalır"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountF<PERSON><PERSON><PERSON><PERSON>,plural, =1{Lighthouse, {fontOrigin} kaynağı için `font-display` değerini otomatik olarak kontrol edemedi.}other{Lighthouse, {fontOrigin} kaynağı için `font-display` değerlerini otomatik olarak kontrol edemedi.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "En Boy Oranı (Gerçek)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "En Boy <PERSON> (Görüntülenen)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Resim görü<PERSON><PERSON><PERSON><PERSON> boy<PERSON>ları doğal en boy oran<PERSON><PERSON> eşleşmelidir. [Resim en boy oranı hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Yanlış en boy or<PERSON><PERSON>na sahip resimler görüntülüyor"}, "core/audits/image-aspect-ratio.js | title": {"message": "Resimleri doğru en boy oran<PERSON><PERSON> görüntülüyor"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON> boyut"}, "core/audits/image-size-responsive.js | description": {"message": "Resmin do<PERSON> boy<PERSON>, resmin netliğ<PERSON> en üst düzeye çıkarmak için görüntü boyutu ve piksel oranı ile orantılı olmalıdır. [Nasıl uyumlu resimler sağlayabileceğinizi öğrenin](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Düşük çözünürlüklü resimleri sunar"}, "core/audits/image-size-responsive.js | title": {"message": "Çözünürlüğü uygun olan resimleri sunar"}, "core/audits/installable-manifest.js | already-installed": {"message": "Uygulama zaten yüklenmiş"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Gereken bir simge manifest'ten indirilemedi"}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON> ne<PERSON>i"}, "core/audits/installable-manifest.js | description": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> çevrimdışı çalışma, ana ekrana ekleme ve push bildirimleri gibi pek çok Progresif Web Uygulaması (PWA) özelliğini kullanmasını sağlayan teknolojidir. Doğru hizmet çalışanı ve manifest uygulamalarıyla tarayıcılar proaktif olarak kullanıcılardan uygulamanızı ana ekranlarına eklemelerini isteyebilir, böylece daha yüksek etkileşim sağlanabilir. [Manifest yüklenebilirlik koşulları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 neden}other{# neden}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Web uygulaması manifest dosyası veya hizmet çalışanı, y<PERSON><PERSON>nebilir olma gerekliliklerini karşılamıyor"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Store uygulama URL'si ve Play Store kimliği eşleşmiyor"}, "core/audits/installable-manifest.js | in-incognito": {"message": "<PERSON><PERSON>, gizli pencer<PERSON> yü<PERSON>ş"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Manifest dosyasındaki \"display\" özelliği; \"standalone\", \"fullscreen\" veya \"minimal-ui\"den biri olmalıdır"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest dosyası \"display_override\" alanını içeriyor ve desteklenen ilk görüntü modunun \"standalone\", \"fullscreen\" veya \"minimal-ui\"den biri olması gerekiyor"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest dosyas<PERSON> getirilemedi, boş veya ayrıştırılamadı"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifest dosyası getirilirken manifest URL'si değiştirildi."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest dos<PERSON><PERSON> bir \"name\" veya \"short_name\" alan<PERSON> içermiyor"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest dosyası uygun bir simge içermiyor. En az {value0} piksel değerinde bir PNG, SVG veya WebP biçimi gerekli olup sizes özelliği ayarlanmalıdır. Ayrıca purpose özelliği ayarlanacaksa \"any\" ifadesini içermelidir."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Sağlananlar arasında PNG, SVG veya WebP biçiminde, en az {value0} piksel ölçüsünde bir kare şeklinde, am<PERSON><PERSON>i ayarlanmamış ya da \"any\" olarak ayarlanmış bir simge yok"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "İndirilen simge boş veya bozuktu"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Play Store kimliği sağlanmamış"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Sayfada hiç manifest <link> URL'si yok"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Eşleşen hizmet çalışanı algılanamadı. Sayfayı yeniden yüklemeniz veya mevcut sayfaya ait hizmet çalışanı kapsamının, manifest dosyasındaki kapsamı ve başlangıç URL'sini içerdiğinden emin olmanız gerekir."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Manifest dosyasında \"start_url\" alanı olmadığından hizmet çalışanı kontrol edilemedi"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Yüklenebilirlik hata kimliği \"{errorId}\" tanınmıyor"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "<PERSON><PERSON>, güvenli bir kaynaktan yayınlanmıyor"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "<PERSON><PERSON>, ana çerçeveye yüklenmiyor"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Sayfa çevrimdışı çalışmıyor"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA (Progresif Web Uygulaması) kaldırıldı ve yüklenebilirlik kontrolleri sıfırlanıyor."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "<PERSON><PERSON><PERSON><PERSON>, Android'de desteklenmiyor"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest dosyasında prefer_related_applications: true de<PERSON><PERSON> belirtilmiş"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications, Android'de yalnızca Chrome Beta ve Mevcut ürün kanallarında desteklenir."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse bir hizmet çalışanının mevcut olup olmadığını belirleyemedi. Lütfen Chrome'un daha yeni bir s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deney<PERSON>."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Manifest dosyasının URL şeması ({scheme}) Android'de desteklenmiyor."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Manifest başlangıç URL'si geçerli değil"}, "core/audits/installable-manifest.js | title": {"message": "Web uygulaması manifest dosyası ve hizmet çalışanı, yüklenebilir olma gerekliliklerini karşılıyor"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Manifest dosyasındaki bir URL; kullanıcı adı, şifre veya bağlantı noktası içeriyor"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Sayfa çevrimdışı çalışmıyor. Sayfa, Ağustos 2021'de çıkacak Chrome 93 kararlı sürümünden sonra yüklenebilir olarak değerlendirilmeyecek."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON> verildi"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON>gel<PERSON>di"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Güvenli olmayan URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Çözüm<PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Tüm siteler (hassas veriler işlemeyenler dahi) HTTPS ile korunmalıdır. Bu, ilk istek HTTPS üzerinden sunulsa bile bazı kaynakların HTTP üzerinden yüklendiği [karma içerikten](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) kaçınmayı içerir. HTTPS, izinsiz kişilerin uygulamanızla kullanıcılarınız arasındaki iletişime müdahale etmelerini veya bu iletişimi pasif olarak dinlemelerini önler; ayrıca HTTP/2 ve birçok yeni web platformu API'leri için ön koşuldur. [HTTPS hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Güvenli olmayan 1 istek bulundu}other{Güvenli olmayan # istek bulundu}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "HTTPS kullanmıyor"}, "core/audits/is-on-https.js | title": {"message": "HTTPS kullanıyor"}, "core/audits/is-on-https.js | upgraded": {"message": "Otomatik olarak HTTPS sürümüne geçildi"}, "core/audits/is-on-https.js | warning": {"message": "İzin verildi ancak uyarı içeriyor"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alanı içinde boyanan en büyük zengin içerikli öğedir. [Largest Contentful Paint öğesi hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Largest Contentful Paint öğesi"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS Katkısı"}, "core/audits/layout-shift-elements.js | description": {"message": "Sayfanın CLS'sine en fazla bu DOM öğeleri katkıda bulunur. [CLS'yi nasıl iyileştireceğinizi öğrenin](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Büyük düzen kaymalarından kaçının"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Ekranın üst kısmında olup geç yüklenen resimler sayfanın yaşam döngüsünde sonradan oluşturulur. Bu durum, Largest Contentful Paint'i geciktirebilir. [İdeal geç yükleme hakkında daha fazla bilgi edinin](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Largest Contentful Paint resmi geç yüklendi"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Largest Contentful Paint resmi geç yüklenmedi"}, "core/audits/long-tasks.js | description": {"message": "Ana ileti dizisindeki en uzun görevleri listeler; giri<PERSON> gecikmesine en çok katkıda bulunanları belirlemekte faydalıdır. [Uzun ana iş parçacığı görevlerinden nasıl kaçınacağınızı öğrenin](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# uzun görev bulundu}other{# uzun görev bulundu}}"}, "core/audits/long-tasks.js | title": {"message": "Uzun ana ileti dizisi görevlerinden kaçının"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "JS'y<PERSON>, derleme ve yürütme için harcanan zamanı kısaltabilirsiniz. Daha küçük JS yüklerinin sağlanması bu konuda yardımcı olabilir. [Ana iş parçacığının işini nasıl azaltacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Ana iş parçacığı çalışmasını en aza indir"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Ana iş parçacığının çalışmasını en aza indirir"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Mümkün olduğunca fazla sayıda kullanıcıya ulaşmak için sitelerin belli başlı her tarayıcıda çalışması gerekir. [Tarayıcılar arası uyumluluk hakkında bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Site farklı tarayıcılarda çalışıyor"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Her bir sayfaya URL aracılığıyla derin bağlantı verilebildiğinden ve URL'lerin sosyal medyada paylaşılabilmesi için benzersiz olduğundan emin olun. [Derin bağlantı sağlama hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Her sayfa bir URL'ye sahip"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Yavaş ağlarda bile sağa sola dokundukça geçişlerin hızlı gerçekleştiği hissedilmelidir. Bu deneyim, kullanıcının performans algısının temelinde yatar. [Sayfa geçişleri hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Sayfa geçişleri ağda takılıyorlarmış gibi hissedilmiyor"}, "core/audits/maskable-icon.js | description": {"message": "Maskelenebilir simge, uygulama bir cihaza yüklenirken resmin sinemaskop hale gelmeden tüm şekli doldurmasını sağlar. [Maskelenebilir manifest simgeleri hakkında bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest dosyası maskelenebilir bir simge içermiyor"}, "core/audits/maskable-icon.js | title": {"message": "Manifest dosyası maskelenebilir simge içeriyor"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative Layout Shift, g<PERSON><PERSON><PERSON><PERSON><PERSON> alanı içindeki görünür öğelerin hareketini ölçer. [Cumulative Layout Shift metriği hakkında daha fazla bilgi edinin](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Sonraki Boyamayla Et<PERSON>şim, say<PERSON><PERSON>n tepkisini ve kullanıcı girişine görün<PERSON>r şekilde yanıt vermesinin ne kadar sürdüğünü ölçer. [Sonraki Boyamayla Etkileşim metriği hakkında daha fazla bilgi edinin](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "İlk Zengin İçerikli Boyama, ilk metnin veya resmin boyandığı zamanı işaret eder. [İlk zengin içerikli boyama metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "İlk Anlamlı Boyama, bir sayfanın ana içeriğinin ne zaman görünür hale geldiğini ölçer. [İlk anlamlı boyama metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Etkileşime Hazır <PERSON>, say<PERSON><PERSON>n tamamen etkileşime hazır hale gelmesi için geçmesi gereken süreyi ifade eder. [Etkileşime Hazır Olma <PERSON>i metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "En Büyük Zengin İçerikli Boyama, en büyük metnin veya resmin boyandığı zamanı işaret eder. [Largest Contentful Paint metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Kullanıcılarınızın karşılaşabileceği olası maksimum İlk Giriş Gecikmesi, en uzun görevin süresidir. [Maximum Potential First Input Delay metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "<PERSON><PERSON><PERSON>, bir say<PERSON> içeriğinin görsel olarak ne kadar hızlı doldurulabildiğini gösterir. [Hız endeksi metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Görev süresi 50 ms.y<PERSON>, FCP ile Etkileşime Hazır Olma Süresi arasındaki tüm zaman aralıklarının milisaniye cinsinden toplamı. [Toplam Engelleme Süresi metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "<PERSON><PERSON> gid<PERSON><PERSON> dö<PERSON><PERSON><PERSON> s<PERSON> (RTT) performans üzerinde büyük bir etkisi vardır. Kaynağa RTT'nin fazla olması, kullanıcıya daha yakın olan sunucuların performansı iyileştirebileceğinin göstergesidir. [Gid<PERSON><PERSON> dönüş süresi hakkında daha fazla bilgi edinin](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON>ö<PERSON>üş Süreleri"}, "core/audits/network-server-latency.js | description": {"message": "Sunucudan kaynaklanan gecikmeler web performansını etkileyebilir. Bir kaynakta sunucu gecikmesinin fazla olması, sunucunun aşırı yüklendiğinin veya arka uç performansının kötü olduğunun göstergesidir. [Sun<PERSON>u yanıt süresi hakkında daha fazla bilgi edinin](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Sunucunun Arka Uç Gecikmeleri"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload` et<PERSON><PERSON><PERSON>i güvenilir bir şekilde tetiklenmez ve etkinliğin dinlenmesi, Geri-İleri Önbelleği gibi tarayıcı optimizasyonlarını engelleyebilir. Bunun yerine `pagehide` veya `visibilitychange` etkinliklerini kullanın. [Kaldırma etkinlik işleyiciler hakkında daha fazla bilgi edinin](https://web.dev/bfcache/#never-use-the-unload-event)."}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Bir `unload` i<PERSON><PERSON><PERSON><PERSON> bulunuyor"}, "core/audits/no-unload-listeners.js | title": {"message": "`unload` etkinlik işleyicisi içermiyor"}, "core/audits/non-composited-animations.js | description": {"message": "Birleştirilmemiş animasyonlar kalitesiz olabilir ve CLS'yi artırır. [Bileşik olmayan animasyonları nasıl önleyeceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Animasyonlu # öğe bulundu}other{Animasyonlu # öğe bulundu}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Filtre ile ilgili <PERSON>, pikselleri hareket ettirebilir"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Hedefte uyumlu olmayan başka bir animasyon var"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efektte \"değiştir\" dışında bileşik mod var"}, "core/audits/non-composited-animations.js | title": {"message": "Birleştirilmemiş animasyonlardan kaçının"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Dönüşümle ilgili özellik kutu boyutuna bağlıdır"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Desteklenmeyen CSS Özelliği: {properties}}other{Desteklenmeyen CSS Özellikleri: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efektin desteklenmeyen zamanlama parametreleri var"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>n miktarını ve bü<PERSON><PERSON>klüğünü, performans bütçesi tarafından belirlenen hedeflerin altında tutun. [Performans bütçeleri hakkında daha fazla bilgi edinin](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 istek}other{# istek}}"}, "core/audits/performance-budget.js | title": {"message": "Performans bütçesi"}, "core/audits/preload-fonts.js | description": {"message": "Sitenize ilk kez gelen ziyaretçilerin kullanma olasılığına karşı `optional` özelliği olan yazı tiplerini önceden yükleyin. [Yazı tiplerini önceden yükleme hakkında daha fazla bilgi edinin](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "`font-display: optional` özelliği olan yazı tipleri önceden yüklenmedi"}, "core/audits/preload-fonts.js | title": {"message": "`font-display: optional` özelliği olan yazı tipleri önceden yüklendi"}, "core/audits/prioritize-lcp-image.js | description": {"message": "LCP öğesinin sayfaya dinamik olarak eklendiği durumlarda LCP'yi iyileştirmek için resmi önceden yüklemeniz gerekir. [LCP öğelerini önceden yükleme hakkında daha fazla bilgi edinin](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Largest Contentful Paint resmi <PERSON>"}, "core/audits/redirects.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, say<PERSON><PERSON><PERSON> yüklenmesinden önce ek gecikmelere neden olur. [Say<PERSON> yönlendirmelerden nasıl kaçınacağınızı öğrenin](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Birden çok sayfa yönlendirmesini önleyin"}, "core/audits/resource-summary.js | description": {"message": "Sayfa kaynaklarının miktarı ve büyüklüğü için bütçeler belirlemek üzere bir budget.json dosyası ekleyin. [Performans bütçeleri hakkında daha fazla bilgi edinin](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 istek • {byteCount, number, bytes} KiB}other{# istek • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "İstek sayısını az ve aktarma boyutlarını küçük tutun"}, "core/audits/seo/canonical.js | description": {"message": "Standart ba<PERSON><PERSON><PERSON><PERSON>, arama sonuçlarında hangi URL'nin gösterileceğini belirtir. [Standart bağlantılar hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Birden fazla çakışan URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Geçersiz URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Başka bir `hreflang` konumuna ({url}) yönlendiriyor"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Mutlak URL değil ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "İçeriğe eşdeğer bir sayfanın yerine alan adının kök URL'sine (ana sayfa) yönlendiriyor"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Doküman geçerli bir `rel=canonical` de<PERSON><PERSON> içermiyor"}, "core/audits/seo/canonical.js | title": {"message": "Doküman geçerli bir `rel=canonical` öğesi içeriyor"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Taranamayan Bağlantı"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "<PERSON><PERSON>ı, web sitelerini taramak için bağlantılarda `href` özelliklerini kullanabilir. Sitenin daha fazla sayfasının keşfedilebilmesi için bağlantı öğelerinin `href` özelliğinin uygun hedefe bağlantı verdiğinden emin olun. [Bağlantıları nasıl taranabilir hale getireceğinizi öğrenin](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Bağlantılar taranabilir değil"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Bağlantılar taranabilir"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Okunabilir olmayan ek metin"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Yazı Tipi Boyutu"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Sayfa Metninin %'si"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Seçici"}, "core/audits/seo/font-size.js | description": {"message": "12 pikselden küçük yazı tipi boyutları okunamayacak kadar küçüktür ve mobil cihaz kullanıcılarının içeriği okuyabilmek için parmaklarıyla sıkıştırma hareketi yaparak yakınlaştırmalarını gerektirir. Sayfanın %60'ından fazlasının en az 12 piksel boyutunda olmasını sağlamaya çalışın. [Okunaklı yazı tipi boyutları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} kadar okunabilir metin"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Mobil ekranlar için optimize edilmiş görüntü alanı meta etiketi olmadığından metin okunaklı değil."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokümanda okunabilir yazı tipi boyutları kullanılmıyor"}, "core/audits/seo/font-size.js | legibleText": {"message": "Okunabilir metin"}, "core/audits/seo/font-size.js | title": {"message": "Dokümanda okunabilir yazı tipi boyutları kullanılıyor"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang ba<PERSON>lant<PERSON>ları, arama <PERSON>larına, belirli bir dildeki veya bölgedeki arama sonuçlarında bir sayfanın hangi sürümünün listeleneceğini bildirir. [`hreflang` hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Doküman geçerli bir `hreflang` öğesi içermiyor"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>"}, "core/audits/seo/hreflang.js | title": {"message": "Doküman geçerli bir `hreflang` öğesi içeriyor"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Beklenmeyen dil kodu"}, "core/audits/seo/http-status-code.js | description": {"message": "Başarısız HTTP durum kodlarına sahip olan sayfalar düzgün bir şekilde dizine eklenmeyebilir. [HTTP durum kodları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Sayfa başarısız bir HTTP durum koduna sahip"}, "core/audits/seo/http-status-code.js | title": {"message": "Sayfa başarılı bir HTTP durum koduna sahip"}, "core/audits/seo/is-crawlable.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tarama iznine sahip olmayan arama motorları tarafından arama sonuçlarına eklenemez. [Tarayıcı yönergeleri hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Sayfanın dizine eklenmesi engellenmiş"}, "core/audits/seo/is-crawlable.js | title": {"message": "Sayfanın dizine eklenmesi engellenmemiş"}, "core/audits/seo/link-text.js | description": {"message": "Açıklayıcı bağlant<PERSON> metni, arama motorlarının içeriğinizi anlamasına yardımcı olur. [Bağlantıları daha erişilebilir hale getirmeyi öğrenin](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 bağlantı bulundu}other{# bağlantı bulundu}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Bağlantılar açıklayıcı metin içermiyor"}, "core/audits/seo/link-text.js | title": {"message": "Bağlantılar açıklayıcı metin içeriyor"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Yapılandırılmış verileri doğrulamak için [Yapılandırılmış Veri Test Aracı](https://search.google.com/structured-data/testing-tool/)'nı ve [Structured Data Linter](http://linter.structured-data.org/)'ı çalıştırın. [Yapılandırılmış veriler hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Yapılandırılmış veriler geçerli"}, "core/audits/seo/meta-description.js | description": {"message": "Sayfa içeriğini kısa ve öz bir şekilde özetlemek amacıyla arama sonuçlarına meta tanımlar eklenebilir. [Meta tanım hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Açıklama metni boş."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Doküman meta tanım içermiyor"}, "core/audits/seo/meta-description.js | title": {"message": "Doküman meta tanım içeriyor"}, "core/audits/seo/plugins.js | description": {"message": "Arama motorları eklenti içeriğini dizine ekleyemez. Ayrıca birçok cihaz eklentileri kısıtlar veya desteklemez. [Eklentilerden kaçınma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, eklenti kullanıyor"}, "core/audits/seo/plugins.js | title": {"message": "Doküman eklenti içermiyor"}, "core/audits/seo/robots-txt.js | description": {"message": "robots.txt dosyanız yanlış biçimlendirilmişse, tarayıcılar web sitenizin nasıl taranmasını veya dizine eklenmesini istediğinizi anlayamayabilir. [Robots.txt hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Robots.txt isteği şu HTTP durumunu döndürdü: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 hata bulundu}other{# hata bulundu}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse bir robots.txt dosyasını indiremedi"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt dosyası geçerli değil"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt dosyası geçerli"}, "core/audits/seo/tap-targets.js | description": {"message": "Düğmeler ve bağlantılar gibi etkileş<PERSON>li <PERSON>, başka öğelerle üst üste binmeden rahatlıkla dokunulabilecek kadar büyük olması (48x48 piksel) ve etrafında yeterli boşluk bulunması gerekir. [Dokunma hedefleri hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> hede<PERSON>rinin {decimalProportion, number, percent} ka<PERSON><PERSON> uygun boyutta"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Mobil ekranlar için optimize edilmiş görüntü alanı meta etiketi olmadığından dokunma hedefleri çok küçük"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Çakış<PERSON>"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>a"}, "core/audits/server-response-time.js | description": {"message": "<PERSON><PERSON><PERSON> tüm istekler sunucu yanıt süresine bağlı olduğundan ana doküman için sunucu yanıt süresini kısa tutun. [Time to First Byte metriği hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Root doküman {timeInMs, number, milliseconds} ms. sürdü"}, "core/audits/server-response-time.js | failureTitle": {"message": "İlk sunucu yanıt süresini azaltın"}, "core/audits/server-response-time.js | title": {"message": "İlk sunucu yanıt süresi kısaydı"}, "core/audits/service-worker.js | description": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> çevrimdış<PERSON>, ana ekrana ekleme ve push bildirimleri gibi pek çok Progresif Web Uygulaması özelliğini kullanmasını sağlayan teknolojidir. [Hizmet çalışanları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Sayfa bir hizmet çalışanı tarafından kontrol ediliyor ancak manifest dosyası geçerli JSON olarak ayrışmadığından `start_url` bulunamadı"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Sayfa bir hizmet çalışanı tarafından kontrol ediliyor ancak `start_url` ({startUrl}) öğesi hizmet çalışanının kapsamında ({scopeUrl}) değil"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Bu sayfa bir hizmet çalışanı tarafından yönetiliyor ancak manifest dosyası getirilmediğinden `start_url` öğesi bulunamadı."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Bu kaynak bir veya daha fazla hizmet çalışanına sahip ancak sayfa ({pageUrl}) kapsam içinde değil."}, "core/audits/service-worker.js | failureTitle": {"message": "Sayfayı kontrol eden bir hizmet çalışanı ve `start_url` ö<PERSON><PERSON> kaydedilmiyor"}, "core/audits/service-worker.js | title": {"message": "Sayfayı kontrol eden bir hizmet çalışanı ve `start_url` ö<PERSON><PERSON> kaydediliyor"}, "core/audits/splash-screen.js | description": {"message": "Temalı başlang<PERSON>ç ekranı, kullanıcılar uygulamanızı ana ekranlarında başlattığında yüksek kaliteli bir deneyim sağlar. [Başlangıç ekranları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Özel ba<PERSON>langıç ekranı için yapılandırılmadı"}, "core/audits/splash-screen.js | title": {"message": "Özel başlangıç ekranı için ya<PERSON>ılandırıldı"}, "core/audits/themed-omnibox.js | description": {"message": "Sitenizle eşleşmesi için tarayıcı adres çubuğu temalı yapılabilir. [Adres çubuğuna tema uygulama hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> için tema rengi ayarlamıyor."}, "core/audits/themed-omnibox.js | title": {"message": "<PERSON><PERSON> i<PERSON> tema rengi a<PERSON>."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Müşteri Başarısı)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Pazarlama)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Sosyal)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Bazı üçüncü taraf yerleşik öğeler geç yüklenebilir. Gerekli olana kadar bunların yerine hafif bileşen kullanmayı düşünebilirsiniz. [Üçüncü tarafları hafif bileşenle nasıl geciktireceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# hafif bileşen alternatifi mevcut}other{# hafif bileşen alternatifi mevcut}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Bazı üçüncü taraf ka<PERSON>, hafif bile<PERSON>en ile geç yüklenebilir"}, "core/audits/third-party-facades.js | title": {"message": "<PERSON><PERSON><PERSON> bi<PERSON>en kullanarak üçüncü taraf kaynaklarını geç yükleme"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Üçünc<PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "Üçüncü taraf kodu, yükleme performansını önemli ölçüde etkileyebilir. Yedekli üçüncü taraf sağlayıcıların sayısını sınırlayın ve öncelikle sayfanızın yüklenmesi tamamlandıktan sonra üçüncü taraf kodunu yükleyin. [Üçüncü tarafların etkisini nasıl azaltacağınızı öğrenin](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Üçünc<PERSON> taraf kodu, ana ileti dizisini {timeInMs, number, milliseconds} ms. s<PERSON><PERSON><PERSON> engelledi"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Üçüncü taraf kodun etkisini azaltın"}, "core/audits/third-party-summary.js | title": {"message": "Üçüncü taraf kull<PERSON>ı<PERSON>ını en aza indirme"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Ölçüm"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Sitenizin performansını takip etmenize yardımcı olması için zamanlama bütçesi belirleyin. Performanslı siteler hızlı yüklenir ve kullanıcı giriş etkinliklerine hızlı yanıt verir. [Performans bütçeleri hakkında daha fazla bilgi edinin](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/unsized-images.js | description": {"message": "Düzen kaymalarını azaltıp CLS'yi iyileştirmek için görsel öğelerde genişliği ve yüksekliği açıkça belirtilmiş şekilde ayarlayın. [Resim boyutlarını nasıl belirleyeceğinizi öğrenin](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Resim öğelerinin açıkça belirtilmiş `width` ve `height` değerleri yok"}, "core/audits/unsized-images.js | title": {"message": "Resim öğelerinin açıkça belirtilmiş `width` ve `height` de<PERSON><PERSON><PERSON><PERSON> var"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Önemli kullanıcı deneyimleri esnasında uygulamanızın gerçek dünya performansını ölçmek için uygulamanıza User Timing API'yi ekleme seçeneğini değerlendirin. [Kullanıcı Zamanlamaları işaretlerini kullanma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 kullanıcı zamanlaması}other{# kullanıcı zamanlaması}}"}, "core/audits/user-timings.js | title": {"message": "Kullanıcı Zamanlaması işaretleri ve ölçüleri"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "\"{security<PERSON><PERSON><PERSON>}\" i<PERSON>in bir `<link rel=preconnect>` ö<PERSON><PERSON> bulundu ancak tarayıcı tarafından kullanılmadı. `crossorigin` özelliğini gerektiği gibi kullandığınızdan emin olun."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Önemli üçüncü taraf kaynaklarına erken bağlantılar oluşturmak için `preconnect` veya `dns-prefetch` kaynak ipuçları ekleme seçeneğini değerlendirin. [Zorunlu kaynaklara nasıl önceden bağlanabileceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Gerekli kaynaklara önceden bağlan"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "2'den fazla `<link rel=preconnect>` bağlantısı bulundu. Bunlar az kullanılıp sadece en önemli kaynaklar için tercih edilmelidir."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "\"{security<PERSON><PERSON>in}\" için bir `<link rel=preconnect>` öğesi bulundu ancak tarayıcı tarafından kullanılmadı. Yalnızca sayfanın kesinlikle isteyeceği önemli kaynaklar için `preconnect` öğ<PERSON> kullanın"}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "\"{preloadURL}\" için bir önceden yükleme `<link>` ö<PERSON><PERSON> bulundu ancak tarayıcı tarafından kullanılmadı. `crossorigin` özelliğini gerektiği gibi kullandığınızdan emin olun."}, "core/audits/uses-rel-preload.js | description": {"message": "Mevcut durumda sayfa yüklemesinden sonra istenen kaynakları daha önce getirmek için `<link rel=preload>` kullanmayı düşünün. [Önemli istekleri nasıl önceden yükleyeceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Önemli istekleri <PERSON>"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Eşleme URL'si"}, "core/audits/valid-source-maps.js | description": {"message": "Kay<PERSON>k eşlemeleri, küçültülmüş kodu orijinal kaynak koduna çevirir. Bu, geliştiricilerin üretimde hata ayıklamasına yardımcı olur. <PERSON>k o<PERSON>ak, Lighthouse daha detaylı bilgi sağlayabilir. Bu avantajlardan yararlanmak için kaynak eşlemelerini dağıtmayı düşünün. [Kaynak haritaları hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Büyük birinci taraf JavaScript'te kaynak eşlemeleri eksik"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "JavaScript dosyasında kaynak eşleme eksik"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Uyarı: `.sourcesContent` özelliğinde 1 öğe eksik}other{Uyarı: `.sourcesContent` özelliğinde # öğe eksik}}"}, "core/audits/valid-source-maps.js | title": {"message": "Sayfadaki kaynak eşlemeleri geçerli"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">`, uygulamanızı mobil ekran boyutlarına göre optimize etmekle kalmaz, ayrıca, [kullanıcı girişinde 300 milisaniyelik bir gecikmeyi de önler](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Görüntü alanı meta etiketini kullanma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` et<PERSON><PERSON> bulu<PERSON>adı"}, "core/audits/viewport.js | failureTitle": {"message": "`width` veya `initial-scale` de<PERSON><PERSON><PERSON><PERSON> olan bir `<meta name=\"viewport\">` etiketi yok"}, "core/audits/viewport.js | title": {"message": "`width` veya `initial-scale` de<PERSON><PERSON><PERSON><PERSON> olan bir `<meta name=\"viewport\">` etiketi var"}, "core/audits/work-during-interaction.js | description": {"message": "<PERSON><PERSON>, sonraki boyamayla etkileşim ölçümü sırasında ortaya çıkıp iş parçacıklarını engelleyen işi belirtir. [Sonraki Boyamayla Etkileşim metriği hakkında daha fazla bilgi edinin](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "\"{interactionType}\" etkinliğine {timeInMs, number, milliseconds} ms. harcandı"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Etkinlik hedefi"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Önemli etki<PERSON><PERSON><PERSON>ler sırasında işi azaltma"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON><PERSON> g<PERSON>"}, "core/audits/work-during-interaction.js | processingTime": {"message": "İşlem süresi"}, "core/audits/work-during-interaction.js | title": {"message": "Önemli etki<PERSON><PERSON><PERSON>ler sırasında işi azaltır"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, uygulamanızda ARIA kullanımının iyileştirilmesine olanak tanır. Bu da ekran okuyucu gibi yardımcı teknolojilerin kullanıcılarına daha iyi bir deneyim sunulmasını sağlayabilir."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, alternatif ses ve video içerikleri sağlanmasına olanak tanır. <PERSON><PERSON> durum, işitme veya görme bozukluğu olan kullanıcıların daha iyi bir deneyim yaşamalarını sağlayabilir."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Ses ve video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "<PERSON><PERSON>, erişilebilirlikle ilgili yaygın en iyi uygulamaları öne çıkarır."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "En iyi u<PERSON>r"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON> kontroller, [web uygulamanızın erişilebilirliğini iyileştirme](https://developer.chrome.com/docs/lighthouse/accessibility/) fırsatlarını ön plana çıkarır. Erişilebilirlik sorunlarının yalnızca bir alt kümesi otomatik olarak algılanabildiğinden manuel test yapılması da önerilir."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON>, otomatik test aracının kapsamında yer almayan alanları ele alır. [Erişilebilirlik incelemesi gerçekleştirme](https://web.dev/how-to-review/) hakkında daha fazla bilgiyi rehberimizde bulabilirsiniz."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Erişilebilirlik"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, içeriğinizin daha rahat okunmasına olanak tanır."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, içeriğinizin farklı ülkelerdeki kullanıcılar tarafından daha iyi yorumlanmasına olanak tanır."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Uluslararası hale getirme ve yerelleştirme"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, uygulamanızdaki kontrollerin anlamının iyileştirilmesine olanak tanır. Bu durum, ekran okuyucu gibi yardımcı teknolojilerin kullanıcılarına daha iyi bir deneyim sunulmasını sağlayabilir."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON> <PERSON>"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, uygulamanızda klavyeyle gezinmeyi kolaylaştırır."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, tablo veya liste halindeki verilerin ekran okuyucu gibi yardımcı teknolojiler kullanılarak daha rahat okunmasına olanak tanır."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>eler"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Tarayıcı Uyumluluğu"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "En İ<PERSON> Uygulamalar"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "G<PERSON><PERSON> ve Güvenlik"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Kullanıcı Deneyimi"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Performans b<PERSON><PERSON><PERSON><PERSON><PERSON>, sitenizin performansı için standartları belirler."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Bütçeler"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Uygulamanızın performansı hakkında daha fazla bilgi. Bu sayılar Performan skoruna [doğ<PERSON><PERSON> etki](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) etmezler."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Teşhis"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Performansın en kritik unsuru, piksellerin ekranda oluşturulma hızıdır. Önemli metrikler: <PERSON><PERSON>gin İçerikli <PERSON>, <PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "İlk Boya İyileştirmeleri"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> daha hızlı yüklenmesine yardımcı olabilir. Performan skoruna [do<PERSON><PERSON><PERSON> etki](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) etmezler."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Fırsatlar"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Toplam yükleme deneyimini geliştirerek sayfanın mümkün olan en kısa sürede duyarlı ve kullanıma hazır olmasını sağlayın. Önemli metrikler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Toplam İyileştirmeler"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performans"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON> den<PERSON>, progresif web uygulamasının (pwa) özelliklerini doğrular. [İyi bir progresif web uygulaması (pwa) için nelere dikkat etmeniz gerektiğini öğrenin](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Temel [Progresif Web Uygulaması Yapılacaklar Listesi](https://web.dev/pwa-checklist/) tarafından zorunlu tutulan bu denetimler, Lighthouse tarafından otomatik olarak kontrol edilmez. Bunlar skorunuzu etkilemez ancak manuel olarak doğrulamanız önemlidir."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA (Progresif Web Uygulaması)"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Yüklenebilir"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimize Edilmiş PWA (Progresif Web Uygulaması)"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> kontroller, say<PERSON><PERSON><PERSON><PERSON><PERSON> temel arama motoru optimizasyonu tavsiyesine uymasını sağlar. Burada Lighthouse'un puan vermediği ve [Önemli Web Verileri](https://web.dev/learn-core-web-vitals/)'ndeki performansınız da dahil arama sıralamanızı etkileyebilecek birçok ek faktör vardır. [Google Arama'nın Temel Özellikleri hakkında daha fazla bilgi edinin](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "SEO ile ilgili diğer en iyi uygulamaları kontrol etmek için sitenizde bu ek doğrulayıcıları çalıştırın."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Tarayıcıların uygulamanızın içeriğini daha iyi anlamasını sağlayacak şekilde HTML kodunuzu biçimlendirin."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "İçerik En İyi Uygulamaları"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Uygulamanızın arama sonuçlarında görünmesi için, tarayıcıların uygulamanıza erişmesi gerekir."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Tarama ve Dizine Ekleme"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Kullanıcıların küçültme veya yakınlaştırma hareketi yapmadan sayfadaki içerikleri okuyabilmelerini sağlamak için sayfalarınızın mobil uyumlu olduğundan emin olun. [Sayfaları nasıl mobil uyumlu hale getireceğinizi öğrenin](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON>"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Test edilen cihazın CPU'sun<PERSON>, Lighthouse'un beklediğinden daha yavaş olduğu anlaşılıyor. Bu durum, performans skorunuzu olumsuz etkileyebilir. [Uygun CPU yavaşlama katsayısını ayarlama](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling) hakkında daha fazla bilgi edinin."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Say<PERSON>, test URL'niz ({requested}) {final} adresine yönlendirildiğinden beklendiği gibi yüklenmiyor olabilir. İkinci URL'yi doğrudan test etmeyi deneyin."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "<PERSON><PERSON>, zaman sınırı içinde bitmeyecek kadar yavaş yüklendi. Sonuçlar eksik olabilir."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Tarayıcı önbelleğini temizleme işlemi zaman aşımına uğradı. Bu sayfayı yeniden denetlemeyi deneyin ve sorun devam ederse hata bildiriminde bulunun."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Şu konumda yükleme performansını etkileyen depolanmış veriler olabilir: {locations}. Bu kaynakların puanlarınızı etkilemesini engellemek için bu sayfayı gizli pencerede denetleyin.}other{Şu konumlarda yükleme performansını etkileyen depolanmış veriler olabilir: {locations}. Bu kaynakların puanlarınızı etkilemesini engellemek için bu sayfayı gizli pencerede denetleyin.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Kaynak verileri temizleme işlemi zaman aşımına uğradı. Bu sayfayı yeniden denetlemeyi deneyin ve sorun devam ederse hata bildiriminde bulunun."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Yalnızca GET isteği aracılığıyla yüklenen sayfalar geri-ileri önbelleğe alınmaya uygundur."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Yalnızca 2XX durum koduna sahip sayfalar önbelleğe alınabilir."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome, önbellekteyken JavaScript'i yürütme girişimi tespit etti."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "AppBanner isteğinde bulunan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, flag'lerle devre dışı bırakılmış. Özelliği yerel olarak bu cihazda etkinleştirmek için chrome://flags/#back-forward-cache adresine gidin."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, komut satırıyla devre dışı bırakılmış."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, yetersiz bellek nedeniyle devre dışı bırakılmış."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> verilmiş kullanıcı tarafından desteklenmiyor."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, önceden oluşturucu için devre dışı bırakılmış."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON><PERSON>, kayıtlı dinleyicileri olan bir BroadcastChannel örneği içerdiğinden önbelleğe alınamıyor."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "cache-control:no-store başlığı içeren sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Önbellek kasıtlı olarak temizlenmiş."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "<PERSON><PERSON>, başka bir sayfanın önbelleğe alınabilmesi için önbellekten çıkarılmış."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Eklenti içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Sayfadan <PERSON>ıkılırken medya içeriği oynatılıyormuş."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession API kullanan ve oynatma durumu tanımlamış olan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession API kullanan ve işlem işleyicileri tanımlamış olan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucu nedeniyle devre dışı bırakılmış."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "SecurityHandler kullanan say<PERSON>lar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthetication API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Özel bir çalışan veya iş akışı kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Yüklenmesi tamamlanmadan dokümandan çıkılmış."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Sayfadan çıktıktan sonra uygulama banner'ı görüntülendi."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Sayfadan <PERSON>ı<PERSON>ıktan sonra Chrome Şifre Yöneticisi görüntülendi."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Sayfadan çıktıktan sonra DOM ayrıştırma süreci devam etti."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Sayfadan çıktıktan sonra DOM Ayrıştırıcı Görünümü görüntülendi."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Messaging API'yi kullanan uzantılar nedeniyle geri-ileri önbellek devre dışı bırakıldı."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Uzun süreli bağlantısı olan uzantılar geri-ileri önbelleğe girmeden önce bağlantıyı kapatmalıdır."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Uzun süreli bağlantısı olan uzantılar geri-ileri önbellekteki çerçevelere mesaj göndermeyi denedi."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Geri-ileri önbellek uzantılar nedeniyle devre dışı bırakıldı."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Sayfadan çıktıktan sonra yeniden form gönderme veya http şifre iletişimi gibi bir modal iletişim gösterildi."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Sayfadan çıktıktan sonra çevrimdışı sayfa gösterildi."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Sayfadan çıktıktan sonra Yetersiz Bellek Müdahalesi çubuğu görüntülendi."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Sayfadan <PERSON>ı<PERSON>ıktan sonra izin istekleri alındı."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON>an sonra pop-up engel<PERSON><PERSON> görüntülendi."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Sayfadan çıktıktan sonra Güvenli Tarama ayrıntıları gösterildi."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Güvenli Tarama bu sayfayı kötüye kullanım amaçlı olarak değerlendirdi ve pop-up'ı engelledi."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Sayfa geri-ileri önbellekteyken bir hizmet çalışanı etkinleştirilmiş."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Doküman hatası nedeniyle geri-ileri önbellek devre dışı bırakıldı."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "FencedFrames kullanan sayfalar bfcache'te saklanamaz."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "<PERSON><PERSON>, başka bir sayfanın önbelleğe alınabilmesi için önbellekten çıkarılmış."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Me<PERSON>a yayınına erişim izni veren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Portal kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "IdleManager kullanan sayfalar şu an için geri-ileri <PERSON>bell<PERSON>ğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Açık bir IndexedDB bağlantısına sahip olan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Uygun olmayan API'ler kullanılmış."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Uzantılar aracılığıyla JavaScript'in yerleştirildiği sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Uzantılar aracılığıyla StyleSheet'in yerleştirildiği sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON> hata."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, keepalive isteği nedeniyle devre dışı bırakıldı."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Klavye kilidi kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | loading": {"message": "Yüklenmesi tamamlanmadan sayfadan çı<PERSON>ılmış."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON> kaynağında cache-control:no-cache bulunan sayfalar geri-il<PERSON>e g<PERSON>z."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON> kaynağında cache-control:no-store bulunan sayfalar geri-il<PERSON>e gire<PERSON>z."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON> geri-ileri <PERSON> geri yüklenemeden önce gezinme iptal edilmiş."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Etkin bir ağ bağlantısı çok fazla veri aldığından sayfa önbellekten çıkarılmış. Chrome, önbellekteki bir sayfanın alabileceği veri miktarını sınırlar."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "İletim aşamasında fetch() veya XHR içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "<PERSON><PERSON>, yönlendirmeyle ilgili etkin bir ağ isteği nedeniyle geri-ileri önbellekten çıkarılmış."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "<PERSON><PERSON>, çok uzun süre açık kalan bir ağ bağlantısı nedeniyle önbellekten çıkarılmış. Chrome, önbellekteki bir sayfanın veri alabileceği süreyi sınırlar."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Geçerli yanıt başlığı içermeyen sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON>, ana çerçeve dışında bir çerçevede gerçekleşmiş."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Devam etmekte olan dizine eklenmiş DB işlemleri içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "İletim aşamasında bir ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "İletim aşamasında bir getirme ağı isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "İletim aşamasında bir ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "İletim aşamasında bir XHR ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "PaymentManager kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Pencere içinde pencere özelliğini kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | portal": {"message": "Portal kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | printing": {"message": "Yazdırma kullanıcı arayüzü görüntüleyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "<PERSON><PERSON>, \"`window.open()`\" kullanılarak açılmış ve başka bir sekmede buna atıfta bulunuluyor ya da sayfa bir pencere açmış."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Geri-ileri önbellekteki sayfa için oluşturucu süreci kilitlenmiş."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Geri-ileri önbellekteki sayfa için oluşturucu süreci sonlandırılmış."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Ses kaydetme izni isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Sensör izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Arka plan senkronizasyonu veya getirme izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "MIDI izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Bildirim izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Depolama alanına eri<PERSON><PERSON> isteğinde bulunan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Video kaydetme izni isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Yalnızca URL şeması HTTP/HTTPS olan sayfalar önbelleğe alınabilir."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Bir hizmet çalışanı, geri-ileri önbellekteyken sayfa üzerinde hak talebinde bulunmuş."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Bir hizmet çalışanı, geri-ileri önbellekteki sayfaya `MessageEvent` gönderme girişiminde bulunmuş."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Bir sayfa geri-ileri önbellekteyken ServiceWorker kaydı iptal edilmiş."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON>, bir hizmet çalışanının etkinleştirilmesi nedeniyle geri-ileri önbellekten çıkarılmış."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome yeniden başlatılmış ve geri-ileri önbellek girişlerini temizlemiş."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "SharedWorker kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecognizer kullanan sayfalar şu an için geri-ileri <PERSON>belleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesis kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Sayfadaki bir iframe'in başlattığı gezinme işlemi tamamlanmamış."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Alt kaynağında cache-control:no-cache bulunan sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Alt kaynağında cache-control:no-store bulunan sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON><PERSON>, geri-ileri önbellekte azami süreyi aşmış ve geçerliliğini kaybetmiş."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "<PERSON><PERSON>, muhtemelen uzun süre çalışan sayfa gizleme işleyicileri nedeniyle, geri-ileri <PERSON>nbelleğe girerken zaman aşımına uğramış."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Sayfanın ana çerçevesinde kaldırma işleyicisi var."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Sayfanın alt çerçevelerinden birinde kaldırma işleyicisi var."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Tarayıcı, kullanıcı aracısını geçersiz kılma başlığını değiştirmiş."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Video veya ses kaydı erişimine izin veren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabase kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHID kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLocks kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfc kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPService kullanan sayfalar şu an için geri-ileri <PERSON>belleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "WebRTC içeren sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebShare kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "WebSocket içeren sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "WebTransport içeren sayfalar geri-ileri önbelleğe giremez."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXR kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Eski tarayıcılarla geriye dönük uyumluluk sağlamak için https: ve http: URL şemaları (\"strict-dynamic\" yönergesini destekleyen tarayıcılar tarafından yok sayılır) eklemeyi düşünün."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener <PERSON><PERSON><PERSON><PERSON><PERSON>, CSP3 çıktıktan sonra kullanımdan kaldırılmıştır. Lütfen onun yerine Cross-Origin-Opener-Policy başlığını kullanın."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer yönergesi CSP2'den beri kullanılmamaktadır. Lütfen onun yerine Referrer-Policy başlığını kullanın."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss yönergesi CSP2'den beri kullanılmamaktadır. Lütfen onun yerine X-XSS-Protection başlığını kullanın."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Base-uri yöner<PERSON><PERSON><PERSON>ması, yerleştirilen <base> etiketlerinin tüm göreli URL'ler (örneğin, komut dosyaları) için temel URL'yi saldırgan kontrolündeki bir alana ayarlamaya olanak tanır. Base-uri'yi \"none\" veya \"self\" olarak ayarlamayı düşünün."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Eksik object-src y<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> olma<PERSON> komut satırları yürüten eklentilerin yerleştirilmesine izin verir. Mümkünse object-src yöner<PERSON><PERSON> \"none\" olarak ayarlayın."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src yönergesi eksik. <PERSON><PERSON> durum, g<PERSON><PERSON><PERSON> o<PERSON> komut dosyalarının yürütülmesine izin verebilir."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Noktalı virgülü mü unuttunuz? {keyword}, anahtar kelimeden ziyade bir yönerge gibi görünüyor."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonce'lar i<PERSON>in base64 karakter kümesi kullanılmalıdır."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonce'lar en az 8 karakter uzunluğunda olmalıdır."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Bu yönergede düz URL şemaları ({keyword}) kullanmaktan kaçının. Düz URL şemaları, komut dosyalarının gü<PERSON>li olmayan bir alan adından alınmasına izin verir."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "<PERSON>u yönergede düz joker karakter ({keyword}) kullanmaktan kaçının. D<PERSON><PERSON> joker karakter<PERSON>, komut dosyalarının gü<PERSON>li olmayan bir alan adından alınmasına izin verir."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Raporlama hede<PERSON> yalnı<PERSON> report-to yönergesi üzerinden yapılandırılır. Bu yönerge yalnızca Chromium tabanlı tarayıcılarda desteklendiği için ayrıca bir report-uri yönergesi kullanılması önerilir."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Raporlama hedefi ya<PERSON> bir CSP yok. Bu durum, zaman içinde CSP'nin devamlılığının sağlanmasını ve hatalara karşı izlenmesini zorlaştırır."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Ana makine izin verilenler listeleri sıklıkla atlanabilir. Gerekirse bunun yerine \"strict-dynamic\" yönergesiyle birlikte CSP nonce veya hash değerleri kullanın."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Bilinmeyen CSP yönergesi."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} geç<PERSON><PERSON> bir anahtar kelime gibi görünüyor."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inline\", g<PERSON><PERSON><PERSON> olmayan sayfa içi komut dosyalarının ve etkinlik işleyicilerin yürütülmesine izin verir. Komut dosyalarına ayrı ayrı izin vermek için CSP nonce veya hash değerleri kullanmayı düşünün."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Eski tarayıcılarla geriye dönük uyumluluk sağlamak için \"unsafe-inline\" yönergesi (nonce/hash destekleyen tarayıcılar tarafından yok sayılır) eklemeyi düşünün."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "CORS `Access-Control-Allow-Headers` işlemlerinde kimlik doğrulama, joker karakter (*) simgesinin kapsamına girmeyecektir."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "URL'lerinde hem kaldırılan boşluk `(n|r|t)` karakterleri hem küçüktür karakterleri (`<`) bulunan kaynak istekleri engellenir. Bu kaynakları yüklemek için lütfen yeni satırları kaldırıp öğe özellik değeri gibi yerlerdeki küçüktür karakterlerini kodlayın."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` desteği sonlandırıldı. Onun yerine standart API: Navigation Timing 2'yi kullanın."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` desteği sonlandırıldı. Lütfen standart API: Paint Timing'i kullanın."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` desteği sonlandırıldı. Onun yerine Navigation Timing 2'de standart API: `nextHopProtocol` kullanın."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "`(0|r|n)` karakteri içeren çerezler kısaltılmayıp reddedilecektir."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "`document.domain` ayarlayarak aynı kaynak politikasını esnetme işleviyle ilgili destek sonlandırılmış olup varsayılan olarak devre dışı bırakılacaktır. Bu desteği sonlandırma uyarısı, `document.domain` ayarlamak suretiyle izin verilen bir çapraz kaynak erişimine yöneliktir."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Çapraz kaynak iframe'lerden {PH1} tetikleme işlevi artık kullanılmıyor olup ileride kaldırılacaktır."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Varsayılan Cast entegrasyonunu devre dışı bırakmak için `-internal-media-controls-overlay-cast-button` yerine `disableRemotePlayback` özelliği kullanılmalıdır."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} deste<PERSON>i sonlandırıldı. Lütfen bunun yerine {PH2} politikasını kullanın."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "<PERSON><PERSON>, deste<PERSON>i sonlandırma sorunu mesajı için ç<PERSON>."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "`document.domain` ayarlayarak aynı kaynak politikasını esnetme işleviyle ilgili destek sonlandırılmış olup varsayılan olarak devre dışı bırakılacaktır. Bu özelliği kullanmayı sürdürmek için lütfen doküman ve çerçevelere yönelik HTTP yanıtıyla birlikte `Origin-Agent-Cluster: ?0` üstbilgisi göndererek kaynak anahtarlı aracı kümelerini devre dışı bırakın. Ayrıntılı bilgi için https://developer.chrome.com/blog/immutable-document-domain/ sayfasına bakın."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` art<PERSON>k kullanılmıyor olup kaldırılacaktır. Lütfen bunun yerine `Event.composedPath()` politikasını kullanın."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` seçeneği artık kullanılmıyor olup kaldırılacaktır. Chrome, 30 Nisan 2018'den sonra düzenlenen ve herkes tarafından güvenilen tüm sertifikalar için Sertifika Şeffaflığı'nı zorunlu kılmaktadır."}, "core/lib/deprecations-strings.js | feature": {"message": "Daha fazla bilgi için özellik durumu sayfasına göz atın."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` ve `watchPosition()` g<PERSON><PERSON><PERSON>r olmayan kaynaklarda artık çalışmıyor. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` ve `watchPosition()` deste<PERSON>i güvenli olmayan kaynaklarda sonlandırıldı. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` güvenilir olmayan kaynaklarda artık çalışmıyor. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` desteği sonlandırıldı. <PERSON><PERSON> `RTCPeerConnectionIceErrorEvent.address` veya `RTCPeerConnectionIceErrorEvent.port` kullanın."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "`canmakepayment` hizmet çalışanı etkinliğindeki satıcı kaynağı ve rastgele veriler ile ilgili destek sonlandırılacak olup bunlar kaldırılacaktır: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Web sitesi, kullanıcılarının ayrıcalıklı ağ konumu sayesinde erişebildiği bir ağdan alt kaynak istedi. Bu istekler herkese açık olmayan cihazların ve sunucuların internette açığa çıkmasına neden olarak, siteler arası istek sahtekarlığı (CSRF) saldırısı ve/veya bilgi sızıntısı riskini artırır. Bu riskleri azaltmak amacıyla Chrome, gü<PERSON>li olmayan bağlamlardan herkese açık olmayan alt kaynaklara gönderilen isteklerle ilgili desteği sonlandırmıştır ve bu istekleri engellemeye başlayacaktır."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "`.css` dosya uzantısıyla bitmeyen `file:` URL'lerinden CSS yüklenemiyor."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Spesifikasyon değişimi nedeniyle artık `remove()` eşzamansız aralık kaldırma işlemini iptal etmek için `SourceBuffer.abort()` kullanılmamaktadır. Bu işlevle ilgili destek ileride kaldırılacaktır. <PERSON><PERSON><PERSON> yerine `updateend` etkinliğini dinlemeniz gerekir. `abort()` yalnızca eşzamansız medya eklerini iptal etmek veya ayrıştırıcı durumunu sıfırlamak için kullanılmalıdır."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Spesifikasyon değişikliği neden<PERSON>yle, arabellekte kodlanan çerçevelerdeki en yüksek sunum zaman damgasından düşük bir `MediaSource.duration` değeri ayarlama özelliğiyle ilgili destek sonlandırıldı. Arabellekteki kırpılmış medya öğelerinin dolaylı yoldan kaldırılması ileride desteklenmeyecektir. <PERSON><PERSON>, `newDuration < oldDuration` olan durum<PERSON>a her `sourceBuffers` üzerinde açık `remove(newDuration, oldDuration)` uygulamanız gerekir."}, "core/lib/deprecations-strings.js | milestone": {"message": "<PERSON><PERSON> <PERSON><PERSON>, {milestone} olan kilometre taşıyla geçerli olacak."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Sysex, `MIDIOptions` içinde belirtilmiş olmasa bile Web MIDI, sysex kullanmak için izin ister."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Artık Notification API güvenli olmayan kaynaklardan kullanılamaz. Uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Artık çapraz kaynak iframe'lerden Notification API izni istenemez. Bunun yerine izni üst düzey bir çerçeveden istemeniz veya yeni bir pencere açmanız gerekir."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "İş ortağınızın anlaşma yaptığı (D)TLS sürümü artık kullanılmıyor. Lütfen iş ortağınızla görüşüp bu durumu dü<PERSON>."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "Güvenli olmayan bağlamlarda WebSQL kullanımı kaldırılacak ve yakında mevcut olmayacak. Lütfen web depolama veya dizine eklenmiş veritabanı kullanın."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "img, video ve canvas etiketlerinde `overflow: visible` be<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> sınırlarının dışında görsel içerik oluşturulmasına neden olabilir. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md sayfasını inceleyin."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` deste<PERSON>i sonlandırıldı. <PERSON><PERSON><PERSON>, ödeme işleyiciler için lütfen tam zamanında yükleme özelliğini kullanın."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, İçerik Güvenliği Politikası (İGP) `connect-src` yönergesini atladı. Bu atlama işleminin desteği sonlandırılmıştır. Lütfen `PaymentRequest` API'deki (`supportedMethods` alanındaki) ödeme yöntemi tanımlayıcısını İGP `connect-src` yönergenize ekleyin."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` des<PERSON><PERSON><PERSON>landırıldı. Lütfen bunun yerine standart `navigator.storage` kullanın."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Üst öğesi `<picture>` olan `<source src>` öğeleri geçersiz olduğundan yoksayılır. Lütfen bunun yerine `<source srcset>` politikasını kullanın."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` desteği sonlandırıldı. Lütfen bunun yerine standart `navigator.storage` kullanın."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "URL'lerinde <PERSON>rilmiş kimlik bilgileri (ör. `**********************/`) olan alt kaynak istekleri engellenir."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "`DtlsSrtpKeyAgreement` kısıtlaması kaldırıldı. Bu kısıtlama için belirtti<PERSON>z `false` de<PERSON><PERSON>, kaldır<PERSON>lan `SDES key negotiation` yöntemini kullanmaya yönelik bir deneme olarak yorumlandı. Bu işlev kaldırılmıştır; onun yerine `DTLS key negotiation` destekleyen bir hizmet kullanın."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "`DtlsSrtpKeyAgreement` kısıtlaması kaldırıldı. Bu kısıtlama için belirttiğiniz `true` değeri etkisiz olmuştur ancak düzenli olmak adına bu kısıtlamayı kaldırabilirsiniz."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` hareketi algılandı. Bu `Session Description Protocol` sürümü artık desteklenmiyor. Lütfen bunun yerine `Unified Plan SDP` politikasını kullanın."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`{sdpSemantics:plan-b}` ile `RTCPeerConnection` oluştururken kullanılan `Plan B SDP semantics`; web platformundan kalıcı olarak silinmiş olan `Session Description Protocol` protokolünün eski ve standart dışı bir sürü<PERSON>ü<PERSON>ür. `IS_FUCHSIA` ile derleme yaparken hâlâ kullanılabilir olsa da bu protokolü en kısa sürede silmeyi düşünüyoruz. Artık bu protokolü kullanmayın. https://crbug.com/1302249 adresinden durumu takip edebilirsiniz."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` seçeneğinin desteği sonlandırılmış olup kaldırılacaktır."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`Shared<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`, k<PERSON><PERSON><PERSON> a<PERSON>ı eri<PERSON><PERSON><PERSON> ka<PERSON>tılma<PERSON>ını gerektiriyor. Daha fazla bilgi için https://developer.chrome.com/blog/enabling-shared-array-buffer/ adresini ziyaret edin."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Kullanıcı etkinliği olmadan `speechSynthesis.speak()` ça<PERSON><PERSON>rma işlevi artık kullanılmıyor olup kaldırılacaktır."}, "core/lib/deprecations-strings.js | title": {"message": "Desteği Sonlandırılan Özellik Kullanıldı"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Uzantıların `SharedArrayBuffer` kullanmayı sürdürmek için kökler arası erişimi kapatmaları gerekir. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ adresini ziyaret edin."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1}, satıcıya özgü bir işlevdir. <PERSON>un yerine lütfen standart {PH2} i<PERSON><PERSON><PERSON> kullanın."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "`XMLHttpRequest` içindeki yanıt JSON dosyası UTF-16'yı desteklemiyor."}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Son kullanıcı deneyimini o<PERSON>uz etkilem<PERSON> ne<PERSON>, ana iş <PERSON>ığında eşzamanlı `XMLHttpRequest` işleviyle ilgili destek sonlandırıldı. Daha fazla yardım için https://xhr.spec.whatwg.org/ adresini ziyaret edin."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` deste<PERSON><PERSON> sonlandırıldı. Onun yerine l<PERSON> `isSessionSupported()` kullanın ve çözümlenen boole değerini kontrol edin."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Ana İleti Dizisi Engelleme Süresi"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL'yi <PERSON>"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Başarısız <PERSON>"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnName": {"message": "Ad"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Bütçe Aşımı"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "İstek Sayısı"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Kaynak Tü<PERSON>ü"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Başlangıç <PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} KiB potansiyel tasarruf"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 öğe bulundu}other{# öğe bulundu}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} ms. potansi<PERSON>l tasarruf"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "İlk Anlamlı Boya"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Yazı tipi"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Resim"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Düşük"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Orta"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "<PERSON><PERSON><PERSON>um Olası İlk Giriş Gecikmesi"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms."}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sn."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stil Sayfası"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Üçüncü taraf"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Toplam"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Sayfa yüklemenizin izlemesi kaydedilirken bir sorun oluştu. Lütfen Lighthouse'u tekrar çalıştırın. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "İlk Hata Ayıklayıcı Protokolü bağlantısı beklenirken zaman aşımı oluştu."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome, sayfa yükleme sırasında ekran görüntüsü toplayamadı. Lütfen sayfada görülebilir içerik olduğundan emin olun ve sonra Lighthouse'u tekrar çalıştırmayı deneyin. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS sunucuları sağlanan alan adını çözümleyemedi."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Zorunlu {artifactName} toplayıcı bir hatayla karşılaştı: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Dahili bir Chrome hatası oluştu. Lütfen Chrome'u yeniden başlatıp Lighthouse'u tekrar çalıştırmayı deneyin."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Zorunlu {artifactName} toplayıcı çalışmadı."}, "core/lib/lh-error.js | noFcp": {"message": "Sayfa hiçbir içeriği boyamadı. Lütfen yükleme sırasında tarayıcı penceresini ön planda tuttuğunuzdan emin olup tekrar deneyin. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "<PERSON><PERSON>, Largest Contentful Paint (LCP) olarak nitelendirilen içeriği göstermedi. Sayfanın geçerli bir LCP öğesine sahip olduğundan emin olup tekrar deneyin. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Sağlanan sayfa HTML değil ({mimeType} MIME türü olarak sunuldu)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Chrome'un bu s<PERSON><PERSON><PERSON><PERSON><PERSON>, \"{featureName}\" özelliğini destekleyemeyecek kadar eski. Sonuçların tamamını görmek için daha yeni bir sürüm kullanın."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Sayfa yanıt vermeyi durdurduğu için Lighthouse istediğiniz URL'yi güvenli bir şekilde yükleyemedi."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Sağladığ<PERSON><PERSON><PERSON>z URL, geçerli güvenlik sertifikasına sahip değil. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome bir geçiş reklamıyla sayfa yüklemesini önledi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun. (Ayrıntılar: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun. (Durum kodu: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "<PERSON><PERSON>ın yüklenmesi çok uzun sürdü. <PERSON><PERSON><PERSON>zın yüklenme süresini azaltmak için lütfen rapordaki fırsatları uygulayın ve sonra Lighthouse'u tekrar çalıştırmayı deneyin. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools protokol yanıtının beklenmesi için ayrılan süre aşıldı. (Yöntem: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Kaynak içeriğinin getirilmesi için ayrılan süre aşıldı"}, "core/lib/lh-error.js | urlInvalid": {"message": "Sağladığınız URL'nin geçersiz olduğu anlaşılıyor."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Sayfanın MIME türü XHTML: Lighthouse bu doküman türünü açıkça desteklemez"}, "core/user-flow.js | defaultFlowName": {"message": "Kullanıcı işlemleri akışı ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON> ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "<PERSON><PERSON><PERSON><PERSON> gör<PERSON><PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Zaman aralığı raporu ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Erişilebilirlik"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "En İ<PERSON> Uygulamalar"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performans"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresif Web Uygulaması"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Masaüstü"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Lighthouse Akış Raporunu Anlama"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Akışları Anlama"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Gezinme raporlarını kullanarak.."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Anlık görüntü raporlarını kullanarak..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Etkileşim süresi raporlarını kullanarak..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Lighthouse Performans skoru elde edin."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Largest Contentful Paint ve Speed Index gibi sayfa yükleme performans metriklerini ölçün."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Progresif Web Uygulaması özelliklerini değerlendirin."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Tek sayfalık uygulamalardaki veya karmaşık formlardaki erişilebilirlik sorunlarını bulun."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Etkileşimin ardında yatan menülerin ve kullanıcı arayüzü öğelerinin en iyi uygulamalarını değerlendirin."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Bir dizi etkileşimdeki düzen kaymalarını ve JavaScript yürütme süresini ölçün."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Uzun ömürlü sayfalar ve tek sayfalık uygulamalar için deneyimi iyileştirecek performans fırsatlarını keşfedin."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "En yüksek etki"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} bilgilendirici denetim}other{{numInformative} bilgilendirici denetim}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Gezinme raporları, orijinal Lighthouse raporlarında olduğu gibi tek bir sayfa yükleme işlemini analiz eder."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON>u"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} gezinme raporu}other{{numNavigation} gezinme raporu}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{Geçme ihtimali olan {numPassableAudits} denetim}other{Geçme ihtimali olan {numPassableAudits} denetim}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} denetim başarılı oldu}other{{numPassed} denetim başarılı oldu}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Hatalı"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Başarısız"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Başarılı"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>ı<PERSON> durumu"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>ö<PERSON><PERSON><PERSON><PERSON>, gene<PERSON><PERSON> k<PERSON> etkileşimlerinden sonra olmak üzere sayfayı belirli bir durumda analiz eder."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "An<PERSON>ık görü<PERSON>ü raporu"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} anlık görüntü raporu}other{{numSnapshot} anlık görüntü raporu}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Özet"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Kullanıcı etkileşimleri"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Etkileşim sü<PERSON>i <PERSON>, genelde kullanıcı etkileşimlerini içeren herhangi bir zaman dilimini analiz eder."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sü<PERSON> raporu"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} etki<PERSON><PERSON><PERSON> süresi raporu}other{{numTimespan} etki<PERSON><PERSON><PERSON> süresi raporu}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse Kullanıcı İşlemleri Akışıyla İlgili Rapor"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Animasyonlu içerik için içerik ekran dışındayken CPU kullanımını en aza indirmek amacıyla [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) kullanın."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> için uygun bir yedek belirtirken tüm [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) bileşenlerinizin WebP biçimlerinde görüntülenmesi seçeneğini değerlendirin. [Daha fazla bilgi edinin](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Resimlerin otomatik olarak geç yüklenmesi için [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) kullandığınızdan emin olun. [Daha fazla bilgi edinin](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[Sunucu tarafı oluşturucu AMP düzenleri](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) için [AMP Optimize Edici](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) gibi araçları kullanın."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Tüm stillerinizin desteklendiğinden emin olma<PERSON> iç<PERSON> [AMP dokümanlarına](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) bakın."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) bileşeni, ekran boyutuna göre kullanılacak görüntü öğelerini belirtmek için [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) özelliğini destekler. [Daha fazla bilgi edinin](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Çok büyük listeler oluşturuluyorsa Bileşen Geliştirme Kiti (CDK) ile sanal kaydırma seçeneğini düşünün. [Daha fazla bilgi](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "JavaScript paketlerinizin boyutunu en aza indirmek için [rota düzeyinde kod bölmeyi](https://web.dev/route-level-code-splitting-in-angular/) uygulayın. Ayrıca, [Angular hizmet çalışanı](https://web.dev/precaching-with-the-angular-service-worker/) ile öğeleri önbelleğe alma seçeneğini değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Angular KSA kullanıyorsanız derlemelerin üretim modunda oluşturulduğundan emin olun. [Daha fazla bilgi](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Angular KSA kullanıyorsanız paketlerinizi incelemek için üretim derlemenize kaynak eşlemelerini ekleyin. [Daha fazla bilgi edinin](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Gezinmeyi hızlandırmak için rotaları önceden yükleyin. [Daha fazla bilgi](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Görüntü ayrılma noktalarını yönetmek için Bileşen Geliştirme Kiti'nde (CDK) `BreakpointObserver` yardımcı programını kullanmayı düşünün. [Daha fazla bilgi](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "GIF dosyanızı, HTML5 videosu olarak yerleştirmek için kullanılabilmesini sağlayacak bir hizmete yüklemeyi düşünün."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Temanızda özel yazı tipleri tanımlarken `@font-display` if<PERSON><PERSON> belirtin."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Sitenizde [WebP resim biçimlerini Convert resim stiliyle](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) yapılandırmanız faydalı olabilir."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Resimleri geç yükleyebilen [bir <PERSON><PERSON><PERSON> modülü](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) yükleyin. Bu tü<PERSON> modüller, performansı iyileştirmek için ekran dışındaki resimlerin yüklenmesini erteleme özelliği sunar."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "[Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg) modülü gibi bir modül kullanarak kritik CSS ve JavaScript'leri satır içi yapmayı veya JavaScript ile öğeleri potansiyel olarak eşzamansız bir şekilde yüklemeyi değerlendirin. Bu modülün sağlayacağı optimizasyonların sitenizi bozabileceğine dikkat edin. Bunu engellemek için muhtemelen kodda değişiklik yapmanız gerekecektir."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, modüller ve sunucu özellikleri öğelerinin tümü sunucunun yanıt süresini etkiler. Bir optimizayon modülünü dikkatle seçerek ve/veya sunucunuzu yeni sürüme geçirerek, daha ileri düzeyde optimize edilmiş bir tema bulmayı düşünün. Barındırma sunucularınız sorgu sürelerini azaltmak için Redis veya Memcached gibi sistemlerin kullandığı PHP işlem kodunu önbelleğe alma, belleği önbelleğe alma tekniklerinden ve sayfaları daha hızlı hazırlamak için optimize edilmiş uygulama mantığından yararlanmalıdır."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Sayfanıza yüklenen resimlerin boyutunu azaltmak için [Duyarlı Resim Stilleri](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)'ni kullanmayı değerlendirin. Bir sayfada birden çok içerik öğesini göstermek için Görünümler'i kullanıyorsanız belirli bir sayfada gösterilen içerik öğelerinin sayısını sınırlamak üzere sayfalara ayırma işlevini uygulamayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "\"Administration (Yönetim) » Configuration (Yapılandırma) » Development (Geliştirme)\" sayfasında \"Aggregate CSS files (CSS dosyalarını topla)\" ayarını etkinleştirdiğinizden emin olun. CSS stillerini birleştirerek, küçülterek ve sıkıştırarak sitenizi hızlandırmak için [ek modüller](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kullanarak daha gelişmiş toplama seçeneklerini de yapılandırabilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "\"Administration (Yönetim) » Configuration (Yapılandırma) » Development (Geliştirme)\" sayfasında \"Aggregate JavaScript files (JavaScript dosyalarını topla)\" ayarını etkinleştirdiğinizden emin olun. JavaScript öğelerinizi birleştirerek, küçülterek ve sıkıştırarak sitenizi hızlandırmak için [ek modüller](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) kullanarak daha gelişmiş toplama seçeneklerini de yapılandırabilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Kullanılmayan CSS kurallarını kaldırmayı ve gerekli Drupal kitaplıklarını yalnızca alakalı sayfaya veya bir sayfadaki alakalı bileşene eklemeyi değerlendirin. Ayrıntılar için [Drupal dokümanları bağlantısına](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) bakın. Gereksiz CSS ilave eden ekli kitaplıkları belirlemek için Chrome Geliştirici Araçları'nda [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı deneyin. Drupal sitenizde CSS toplama devre dışı bırakıldığında, sorumlu temayı/modülü stil sayfasının URL'sinden belirleyebilirsiniz. Listede çok sayıda stil sayfasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan temaları/modülleri arayın. Tema/modül sadece sayfada gerçekten kullanılan bir stil sayfasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Kullanılmayan JavaScript öğelerini kaldırmayı değerlendirin ve gerekli Drupal kitaplıklarını yalnızca alakalı sayfaya veya bir sayfadaki alakalı bileşene ekleyin. Ayrıntılar için [Drupal dokümanları bağlantısına](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) bakın. Gereksiz JavaScript'ler ilave eden ekli kitaplıkları belirlemek için Chrome Geliştirici Araçları'nda [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı deneyin. Drupal sitenizde JavaScript toplama devre dışı bırakıldığında, sorumlu temayı/modülü komut dosyasının URL'sinden belirleyebilirsiniz. Listede çok sayıda komut dosyasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan temalar/modüller arayın. Tema/modül sadece sayfada gerçekten kullanılan bir komut dosyasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "\"Administration (Yönetim) » Configuration (Yapılandırma) » Development (Geliştirme)\" sayfasında \"Browser and proxy cache maximum age (Tarayıcı ve proxy önbelleği maksimum yaşı)\" de<PERSON><PERSON><PERSON> ayarlayın. [Drupal önbelleği ve performans için optimize edilmesi](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources) ile ilgili bilgi edinin."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Site aracılığıyla yüklenen resimlerin kalitesini korurken boyutlarını otomatik olarak optimize eden ve küçülten [bir modül](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) kullanmayı değerlendirin. Ayrıca sitede oluşturulan tüm resimler için Drupal'ın sağladığı [Duyarlı Resim Stilleri](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)'ni kullandığınızdan emin olun (Drupal 8 ve sonraki sürümlerde kullanılabilir)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Önceden bağlanma veya DNS önceden getirme kaynak ipuçları, kullanıcı aracısı kaynak ipuçları için tamamlayıcı hizmetler sağlayan [bir modülü](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) yükleyerek ve yapılandırarak eklenebilir."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Drupal'ın sa<PERSON> yerel [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)'ni kullandığınızdan emin olun (Drupal 8 ve sonraki sürümlerde kullanılabilir). Görünüm modları, görünümler ile resim alanlarını veya WYSIWYG düzenleyicisi ile yüklenen resimleri oluştururken Duyarlı Resim Stilleri'ni kullanın."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Web yazı tipleri yüklenirken metnin kullanıcılar tarafından görülebilmesi için `font-display` CSS özelliğini otomatik olarak devreye sokmak üzere [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Optimize Fonts` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Görselleri WebP biçimine dönüştürmek için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Next-Gen Formats` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Ekran dışındaki görsellerin gerekene kadar yüklenmesini ertelemek için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Lazy Load Images` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Kritik olmayan JS/CSS'yi ertelem<PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kull<PERSON><PERSON> `Critical CSS` ve `Script Delay` ayarlarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Dünya genelindeki ağımızda içeriğinizi önbelleğe alarak ilk bayt zamanını iyileştirmek için [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching)'i kullanın."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "CSS'nizi otomatik olarak küçülterek ağ yükü boyutlarını azaltmak için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Minify CSS` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "JS'nizi otomatik olarak küçülterek ağ yükü boyutlarını azaltmak için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Minify Javascript` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "<PERSON>u sorunla ilgili yardım almak için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Remove Unused CSS` ayarını etkinleştirin. Sitenizin her bir sayfasında kullanılmakta olan CSS sınıfları tanımlanır ve dosya boyutunu küçük tutmak için diğerleri kaldırılır."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Statik öğeler için ö<PERSON>ğe alma başlığında önerilen değerleri ayarlamak üzere [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kull<PERSON><PERSON> `Efficient Static Cache Policy` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Görselleri WebP biçimine dönüştürmek için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Next-Gen Formats` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Önemli üçüncü taraf kaynaklara erkenden bağlantı oluşturmak amacıyla `preconnect` kaynak ipuçlarını otomatik olarak eklemek için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Pre-Connect Origins` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "<PERSON>u anda istenen kaynakların daha sonra sayfa yüklenirken getirilmesine öncelik vermek amacıyla `preload` bağlantılarını eklemek için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kull<PERSON><PERSON> `Preload Fonts` ve `Preload Background Images` ayarlarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Görselleri cihazlara uygun şekilde yeniden boyutlandırıp ağ yükü boyutlarını azaltmak için [Ezoic Leap](https://pubdash.ezoic.com/speed)'i kullanarak `Resize Images` ayarını etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "GIF dosyanızı, HTML5 videosu olarak yerleştirmek için kullanılabilmesini sağlayacak bir hizmete yüklemeyi düşünün."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Yüklediğiniz resimleri ideal biçimlere otomatik olarak dönüştürecek bir [eklenti](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) veya hizmet kullanmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Ekran dışı görüntüleri erteleme veya bu işlevselliği sağlayan bir şablona geçiş yapma olanağı veren bir [Joomla geç yükleme eklentisi](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) yükleyin. Joomla 4.0 sürümünden itibaren tüm yeni görüntüler [otomatik olarak](https://github.com/joomla/joomla-cms/pull/30748) çekirdekten `loading` özelliğini alacaktır."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "[Kritik öğeleri satır içi yapmanıza](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) veya [daha az önemli kaynakları ertelemenize](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) yardımcı olabilecek çeşitli Joomla eklentileri vardır. Bu eklentilerin sağlayacağı optimizasyonların şablon veya eklentilerinizin özelliklerini bozabileceğine dikkat edin. Bunu engellemek için bunları iyice test etmeniz gerekecektir."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, uzantılar ve sunucu özellikleri öğelerinin tümü sunucunun yanıt süresini etkiler. Bir optimizasyon uzantısını dikkatle seçerek ve/veya sunucunuzu yeni sürüme geçirerek, daha ileri düzeyde optimize edilmiş bir şablon bulmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Makale kategorilerinizde alıntılar göstermeyi (ör. daha fazla bilgi bağlantıları kullanarak), belirli bir sayfada gösterilen makalelerin sayısını azaltmayı, uzun yayınlarınızı birden fazla sayfaya bölmeyi veya yorumların geç yüklenmesi için bir eklenti kullanmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Çeşitli [<PERSON><PERSON><PERSON> u<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) CSS stilerinizi birleştirerek, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Bu işlevi sağlayan şablonlar da vardır."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Çeşitli [<PERSON><PERSON><PERSON> u<PERSON>tı<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) komut dosyalarınızı birleştirerek, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Bu işlevi sağlayan şablonlar da vardır."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Sayfanızda kullanılmayan CSS'ler yükleyen [<PERSON><PERSON><PERSON>n<PERSON>](https://extensions.joomla.org/) sayısını azaltmayı veya bu uzantıları değiştirmeyi değerlendirin. Gereksiz CSS ilave eden uzantıları belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı deneyin. Sorumlu temayı/eklentiyi stil sayfasının URL'sinden belirleyebilirsiniz. Listede çok sayıda stil sayfasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece sayfada gerçekten kullanılan bir stil sayfasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Sayfanızda kullanılmayan JavaScript'ler yükleyen [<PERSON><PERSON><PERSON> u<PERSON>ı<PERSON>ını<PERSON>](https://extensions.joomla.org/) sayısını azaltmayı veya bu eklentileri değiştirmeyi değerlendirin. Gereksiz JS ilave eden eklentileri belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı düşünün. Sorumlu uzantıyı komut dosyasının URL'sinden belirleyebilirsiniz. Listede çok sayıda komut dosyasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan uzantıları arayın. Bir uzantı sadece sayfada gerçekten kullanılan bir komut dosyasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON> Tarayı<PERSON>ı Önbelleğine Alma](https://docs.joomla.org/Cache) ile ilgili bilgi edinin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Kaliteden ödün vermeden resimlerinizi sıkıştıran bir [resim optimizasyonu eklentisi](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) kullanmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Bir [duyarlı resim eklentisi](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) ile içeriğinizde duyarlı resimler kullanmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Joomla'da Gzip Page Compression'ı (Gzip Sayfa Sıkıştırma) etkinleştirerek (System (Sistem) > Global configuration (Genel yapılandırma) > Server (Sunucu)) metin sıkıştırmayı etkinleştirebilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "JavaScript öğelerinizi grupluyorsanız [gruplayıcı](https://github.com/magento/baler) kullanmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Magento'nun yer<PERSON> [JavaScript gruplama ve küçültme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) özelliğini devre dışı bırakın ve bunun yerine [gruplayıcı](https://github.com/magento/baler/) kullanmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[Özel yazı tipleri tanımlarken](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) `@font-display` <PERSON><PERSON><PERSON><PERSON> belirtin."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Daha yeni görüntü biçimlerinden yararlanmak için [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp)'te çeşitli üçüncü taraf uzantılarını arama seçeneğini değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Ürün ve katalog şablonlarınızı, web platformunun [geç yükleme](https://web.dev/native-lazy-loading) özelliğinden yararlanacak şekilde değiştirme seçeneğini değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Ma<PERSON><PERSON>'nun [<PERSON><PERSON>](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) kullanın."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Mağazanızın Geliştirici ayarlarında \"CSS Dosyalarını Küçült\" seçeneğini etkinleştirin. [Daha fazla bilgi](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Statik içerik dağıtımındaki tüm JavaScript öğelerini küçültmek ve yerleşik küçültme özelliğini devre dışı bırakmak için [Terser](https://www.npmjs.com/package/terser)'ı kullanın."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Magento'nun <PERSON> [JavaScript gruplama](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) özelliğini devre dışı bırakın."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Görüntüleri optimize etmek için [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image)'te çeşitli üçüncü taraf uzantılarını arama seçeneğini değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Önceden bağlanma veya DNS önceden getirme kaynak ipuçları, [bir temanın düzeni değiştirilerek](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) eklenebilir."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` et<PERSON><PERSON><PERSON>, [bir te<PERSON>ın dü<PERSON>tiril<PERSON>](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) eklenebilir."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Resim biçimini otomatik olarak optimize etmek için `<img>` yerine `next/image` bileşenini kullanın. [Daha fazla bilgi](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Resimleri otomatik olarak geç yüklemek için `<img>` yerine `next/image` bileşenini kullanın. [Daha fazla bilgi](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "LCP görselini önceden yüklemek için `next/image` bileşenini kullanın ve \"önceliği\" doğru olarak ayarlayın. [Daha fazla bilgi](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Kritik olmayan üçü<PERSON><PERSON> taraf komut dosyalarının yüklenmesini ertelemek için `next/script` bileşenini kullanın. [Daha fazla bilgi](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> her zaman uygun şekilde boyutlandırılmasını sağlamak için `next/image` bileşenini kullanın. [Daha fazla bilgi](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Kullanılmayan kuralları stil sayfalarından kaldırmak için `Next.js` içerisinde `PurgeCSS` kurma seçeneğini değerlendirin. [Daha fazla bilgi](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Kullanılmayan JavaScript kodunu belirlemek için `Webpack Bundle Analyzer` kullanın. [Daha fazla bilgi](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Uygulamanızın gerçek dünya performansını ölçmek için `Next.js Analytics` kullanma seçeneğini değerlendirin. [Daha fazla bilgi](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Sabit öğeler ve `Server-side Rendered` (SSR) sayfaları için önbelleğe almayı yapılandırın. [Daha fazla bilgi](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON>si<PERSON> ka<PERSON><PERSON> dü<PERSON> `<img>` yerine `next/image` bileşenini kullanın. [<PERSON>ha fazla bilgi](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Uygun `sizes` kuru<PERSON><PERSON> i<PERSON> `next/image` bileşenini kullanın. [Daha fazla bilgi](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Next.js sunucunuzda sıkıştırmayı etkinleştirin. [Daha fazla bilgi](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "`nuxt/image` bileş<PERSON>ni k<PERSON> `format=\"webp\"` de<PERSON><PERSON><PERSON>. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Ekran dışı resimler için `nuxt/image` bileşenini kullanarak `loading=\"lazy\"` de<PERSON><PERSON><PERSON> a<PERSON>. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "`nuxt/image` bileşenini kullanarak LCP resmi için `preload` de<PERSON><PERSON><PERSON> belirtin. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "`nuxt/image` bile<PERSON><PERSON><PERSON> k<PERSON> `width` ve `height` de<PERSON><PERSON><PERSON><PERSON> açıkça belirtin. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "`nuxt/image` bileşenini kullanın ve uygun `quality` de<PERSON><PERSON>ni a<PERSON>ın. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "`nuxt/image` bileşenini kullanın ve uygun `sizes` de<PERSON><PERSON>ni a<PERSON>ın. [Daha fazla bilgi](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "Web sayfalarının daha hızlı yüklenmesi için [animasyonlu GIF'lerin yerine video koyun](https://web.dev/replace-gifs-with-videos/) ve mevcut son teknoloji video codec'i VP9'da sıkı<PERSON><PERSON><PERSON>rma etkinliğini %30'dan fazla artırmak için [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) veya [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) gibi modern dosya biçimlerini kullanın."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Yüklediğiniz resimleri ideal biçimlere otomatik olarak dönüştürecek bir [eklenti](https://octobercms.com/plugins?search=image) veya hizmet kullanmayı düşünün. [WebP kayıpsız görüntüler](https://developers.google.com/speed/webp), eşdeğer SSIM kalite endeksindeki benzer JPEG resimlerinden %25-34, PNG'lerden ise boyut olarak %26 daha küçüktür. Kullanmayı düşünebileceğiniz diğer bir yeni nesil resim biçimi de [AVIF](https://jakearchibald.com/2020/avif-has-landed/)'dir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Ekran dışı görüntüleri erteleme veya bu işlevi sağlayan bir temaya geçiş yapma olanağı veren [gör<PERSON>nt<PERSON> geç yükleme eklentisi](https://octobercms.com/plugins?search=lazy) yükleme seçeneğini düşünün. Ayrıca [AMP eklentisini](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) kullanmayı da düşünün."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "[Kritik öğelerin satır içi yapılmasına](https://octobercms.com/plugins?search=css) yardımcı olan birçok eklenti bulunmaktadır. Diğer eklentileri bozabileceğinden bu eklentileri iyice test etmeniz gerekir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, eklentiler ve sunucu özellikleri öğelerinin tümü sunucunun yanıt süresini etkiler. Bir optimizasyon eklentisini dikkatle seçerek ve/veya sunucuyu yeni sürüme geçirerek, daha ileri düzeyde optimize edilmiş bir tema bulmayı düşünün. Ekim CMS ayrıca geliştiricilerin e-posta gönderme gibi zaman alan görevlerin işlenmesini ertelemek için [`Queues`](https://octobercms.com/docs/services/queues) kullanmasına olanak tanır. Bu sayede web istekleri önemli ölçüde hızlanır."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Yayın listelerinde altınılar göstermeyi (ör. `show more` dü<PERSON><PERSON><PERSON> kullanarak), belirli bir web sayfasında gösterilen yayınların sayısını azaltmayı, uzun yayınları birden fazla web sayfasına bölmeyi veya yorumların geç yüklenmesi için bir eklenti kullanmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Stilleri birleştirerek, küçülterek ve sıkıştırarak bir web sitesini hızlandırabilen birçok [eklenti](https://octobercms.com/plugins?search=css) vardır. Baştan bu sadeleştirmeyi yapmak için bir derleme işlemi kullanmak, geliştirme sürecini hızlandırabilir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Komut dosyalarını birleştirerek, küçülterek ve sıkıştırarak bir web sitesini hızlandırabilen birçok [eklenti](https://octobercms.com/plugins?search=javascript) vardır. Baştan bu sadeleştirmeyi yapmak için bir derleme işlemi kullanmak, geliştirme sürecini hızlandırabilir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Web sitesinde kullanılmayan CSS'ler yükleyen [eklentileri](https://octobercms.com/plugins) gözden geçirin. Gereksiz CSS'ler ekleyen eklentileri belirlemek için Chrome Geliştirici Araçları'nda [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırın. Sorumlu temayı/eklentiyi stil sayfasının URL'sinden belirleyin. Çok sayıda stil sayfasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece web sayfasında gerçekten kullanılan bir stil sayfasını eklemelidir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Web sayfasında kullanılmayan JavaScript'ler yükleyen [eklentileri](https://octobercms.com/plugins?search=javascript) gözden geçirin. Gereksiz JavaScript'ler ekleyen eklentileri belirlemek için Chrome Geliştirici Araçları'nda [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırın. Sorumlu temayı/eklentiyi komut dosyasının URL'sinden belirleyin. Çok sayıda komut dosyasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece web sayfasında gerçekten kullanılan bir komut dosyasını eklemelidir."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[HTTP Önbelleği ile gereksiz ağ isteklerini önleme](https://web.dev/http-cache/#caching-checklist) hakkında bilgi edinin. Önbelleğe alma işlemini hızlandırmak için kullanılabilecek birçok [eklenti](https://octobercms.com/plugins?search=Caching) vardır."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Kaliteden ödün vermeden resimlerinizi sıkıştırmak için bir [resim optimizasyonu eklentisi](https://octobercms.com/plugins?search=image) kullanmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Gerekli resim boyutlarının kullanılabilmesi için resimleri doğrudan medya yöneticisine yükleyin. İdeal resim boyutlarının kullanıldığından emin olmak için [yeniden boyutlandırma filtresini](https://octobercms.com/docs/markup/filter-resize) veya bir [gör<PERSON>nt<PERSON> yeniden boyutlandırma eklentisi](https://octobercms.com/plugins?search=image) kullanabilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Web sunucusu yapılandırmasında metin sıkıştırmayı etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Sayfada çok sayıda yinelenen öğe oluşturuyorsanız oluşturulan DOM düğümlerinin sayısını en aza indirmek için `react-window` gibi bir \"pencereleme\" kitaplığı kullanmayı düşünün. [<PERSON>ha fazla bilgi edinin](https://web.dev/virtualize-long-lists-react-window/). Ayrıca, çalışma zamanı performansını yükseltmek için `Effect` kancası kullanıyorsanız bazı bağımlılıklar değişinceye kadar [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) veya [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) ve [atlama efektleri](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) kullanarak gereksiz yeniden oluşturma sayısını en aza indirin."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "React Router kullanıyorsanız [rota gezinmeleri](https://reacttraining.com/react-router/web/api/Redirect) için `<Redirect>` bileşeninin kullanımını en aza indirin."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON><PERSON>u tarafında React bileşenleri oluşturuyorsanız istemcinin tüm işaretleme parçalarını bir defada alması yerine farklı parçaları alıp birleştirmesine izin vermek için `renderToPipeableStream()` veya `renderToStaticNodeStream()` kullanmayı düşünün. [Daha fazla bilgi](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Derleme sisteminiz CSS dosyalarını otomatik olarak küçültüyorsa uygulamanızın üretim derlemesini dağıttığınızdan emin olun. Bunu React Geliştirici Araçları uzantısıyla kontrol edebilirsiniz. [Daha fazla bilgi edinin](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Derleme sisteminiz JS dosyalarını otomatik olarak küçültüyorsa uygulamanızın üretim derlemesini dağıttığınızdan emin olun. Bunu React Geliştirici Araçları uzantısıyla kontrol edebilirsiniz. [Daha fazla bilgi edinin](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON>u tarafında oluşturmuyorsanız `React.lazy()` ile [JavaScript paketlerinizi bölün](https://web.dev/code-splitting-suspense/). <PERSON><PERSON><PERSON> ha<PERSON>, [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/) gibi bir üçüncü taraf kitaplığı kullanarak kodu bölün."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Bileşenlerinizin oluşturma performansını ölçmek için Profil Oluşturucu API'sinden yararlanan React Geliştirme Araçları Profil Oluşturucu'yu kullanın. [Daha fazla bilgi](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "GIF dosyanızı, HTML5 videosu olarak yerleştirmek için kullanılabilmesini sağlayacak bir hizmete yüklemeyi düşünün."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Yüklenen JPEG resimlerinizi desteklenen yerlerde otomatik olarak WebP'ye dönüştürmek için [Performance Lab](https://wordpress.org/plugins/performance-lab/) eklentisini kullanabilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Ekran dışı görüntüleri erteleme veya bu işlevselliği sağlayan bir temaya geçiş yapma olanağı veren bir [WordPress geç yükleme (lazy-load) eklentisi](https://wordpress.org/plugins/search/lazy+load/) yükleyin. Ayrıca [AMP eklentisini](https://wordpress.org/plugins/amp/) kullanmayı da değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "[Kritik öğeleri satır içi yapmanıza](https://wordpress.org/plugins/search/critical+css/) veya [daha az önemli kaynakları ertelemenize](https://wordpress.org/plugins/search/defer+css+javascript/) yardımcı olabilecek çeşitli WordPress eklentileri vardır. Bu eklentilerin sağlayacağı optimizasyonların tema veya eklentilerinizin özelliklerini bozabileceğine dikkat edin. Bunu engellemek için muhtemelen kodda değişiklik yapmanız gerekecektir."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, eklentiler ve sunucu özellikleri öğelerinin tümü sunucunun yanıt süresini etkiler. Bir optimizayon eklentisini dikkatle seçerek ve/veya sunucunuzu yeni sürüme geç<PERSON>rek, daha ileri düzeyde optimize edilmiş bir tema bulmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Yayın listelerinizde alıntılar göstermeyi (ör. daha fazla etiket kullanarak), belirli bir sayfada gösterilen yayınların sayısını azaltmayı, uzun yayınlarınızı birden fazla sayfaya bölmeyi veya yorumların geç yüklenmesi için bir eklenti kullanmayı düşünün."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Çeşitli [WordPress eklentileri](https://wordpress.org/plugins/search/minify+css/) stillerinizi sıralayarak, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Ayrıca mümkünse bu küçültme yapmak için bir derleme işlemi kullanmak isteyebilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Çeşitli [WordPress eklentileri](https://wordpress.org/plugins/search/minify+javascript/) komut dosyalarınızı sıralayarak, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Ayrıca mümkünse bu sadeleştirmeyi yapmak için bir derleme işlemi kullanmak isteyebilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Sayfanızda kullanılmayan CSS'ler yükleyen [WordPress eklentilerinin](https://wordpress.org/plugins/) sayısını azaltmayı veya değiştirmeyi düşünün. Gereksiz CSS ekleyen eklentileri belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developer.chrome.com/docs/devtools/coverage/) çalıştırmayı deneyin. Sorumlu temayı/eklentiyi stil sayfasının URL'sinden belirleyebilirsiniz. Listede çok sayıda stil sayfasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece sayfada gerçekten kullanılan bir stil sayfasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Sayfanızda kullanılmayan JavaScript'ler yükleyen [WordPress eklentilerinin](https://wordpress.org/plugins/) sayısını azaltmayı veya değiştirmeyi değerlendirin. Gereksiz JS ekleyen eklentileri belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developer.chrome.com/docs/devtools/coverage/) çalıştırmayı düşünün. Sorumlu temayı/eklentiyi komut dosyasının URL'sinden belirleyebilirsiniz. Listede çok sayıda komut dosyasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece sayfada gerçekten kullanılan bir komut dosyasını kuyruğa almalıdır."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[WordPress'te Tarayı<PERSON>ı Önbelleği](https://wordpress.org/support/article/optimization/#browser-caching) ile ilgili bilgi edinin."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Kaliteden ödün vermeden resimlerinizi sıkıştıran bir [resim optimizasyonu WordPress eklentisi](https://wordpress.org/plugins/search/optimize+images/) kullanmayı değerlendirin."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Gerekli resim boyutlarının kullanılabilmesi için resimleri doğrudan [medya kitaplığından](https://wordpress.org/support/article/media-library-screen/) yükleyin, ardından ideal resim boyutlarının kullanıldığından (esnek ayrılma noktası için olanlar dahil) emin olmak için resimleri medya kitaplığından ekleyin veya resim widget'ını kullanın. Boyutları kullanım için yeterli değilse `Full Size` resimler kullanmaktan kaçının. [Daha fazla bilgi](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Web sunucunuzun yapılandırmasında metin sıkıştırmayı etkinleştirebilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Resimlerinizi WebP'ye dönüştürmek için \"WP Rocket\"teki Resim Optimizasyonu sekmesinden \"Imagify\" eklentisini etkinleştirin."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Bu öneriyi düzeltmek için WP Rocket'te [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) özelliğini etkinleştirin. Bu özellik, ziyaretçiler sayfayı aşağı kaydırıp resimleri gerçekten görmeleri gerekene kadar bu resimlerin yüklenmesini geciktirir."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Bu öneriyi uygulamak için \"WP Rocket\"te [Kullanılmayan CSS'yi kaldır](https://docs.wp-rocket.me/article/1529-remove-unused-css) ve [JavaScript'i ertelenmiş olarak yükle](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) özelliklerini etkinleştirin. Bu özellikler, say<PERSON><PERSON><PERSON>ın oluşturulmasını engellememeleri için CSS ve JavaScript dosyalarını sırasıyla optimize eder."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Bu sorunu düzel<PERSON>mek için \"WP Rocket\"te [CSS dosyalarını küçült](https://docs.wp-rocket.me/article/1350-css-minify-combine) özelliğini etkinleştirin. Sitenizin CSS dosyalarındaki tüm boşluklar ve yorumlar, dosya boyutunu küçültmek ve daha hızlı indirilmesini sağlamak için kaldırılır."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Bu sorunu düzel<PERSON><PERSON><PERSON> için \"WP Rocket\"te [JavaScript dosyalarını küçült](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) özelliğini etkinleştirin. JavaScript dosyalarındaki boş alanlar ve yorumlar, dosya boyutunu küçültmek ve daha hızlı indirilmesini sağlamak için kaldırılır."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Bu sorunu dü<PERSON><PERSON><PERSON><PERSON> için \"WP Rocket\"te [Kullanılmayan CSS'yi kaldır](https://docs.wp-rocket.me/article/1529-remove-unused-css) özelliğini etkinleştirin. Her sayfa için yalnızca kullanılan CSS'yi korurken kullanılmayan tüm CSS ve stil sayfalarını kaldırarak sayfa boyutunu azaltır."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Bu sorunu dü<PERSON><PERSON><PERSON><PERSON> iç<PERSON> \"WP Rocket\"te [JavaScript yürütmesini geciktir](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) özelliğini etkinleştirin. Bu özellik, komut dosyalarının yürütülmesini kullanıcı etkileşimine kadar geciktirerek sayfanızın yüklenme süresini iyileştirir. Sitenizde iframe'ler varsa WP Rocket'in [iframe'ler ve videolar için LazyLoad](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) ile [YouTube iframe'ini, önizleme resmi<PERSON>](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) özelliklerini kullanabilirsiniz."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Resimlerinizi sıkıştırmak için \"WP Rocket\"teki Resim Optimizasyonu sekmesinden \"Imagify\" eklentisini etkinleştirin ve Toplu Optimizasyon çalıştırın."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "\"dns-prefetch\" eklemek ve harici alanlarla bağlantıyı hızlandırmak için \"WP Rocket\"teki [DNS isteklerini önceden getir](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) özelliğini kullanın. \"WP Rocket\", [Google Fonts alanına](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) ve [CDN'yi etkinleştir](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) özelliği aracılığıyla eklenen tüm CNAME'lere otomatik olarak \"önceden bağlanma\" işlemi ekler."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Yazı tipleriyle ilgili bu sorunu düzeltmek için \"WP Rocket\"te [Kullanılmayan CSS'yi kaldır](https://docs.wp-rocket.me/article/1529-remove-unused-css) özelliğini etkinleştirin. Sitenizdeki kritik yazı tiplerine öncelik verilir ve bunlar önceden yüklenir."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON><PERSON> ma<PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> kritik yol gecikmesi:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSON nesnesini kopyala"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Koyu Temayı aç/kapat"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Yaz<PERSON><PERSON>rma <PERSON>"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Yaz<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON> o<PERSON><PERSON> ka<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "HTML olarak kaydet"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "JSON nesnesi olarak kaydet"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Görüntüleyicide aç"}, "report/renderer/report-utils.js | errorLabel": {"message": "Hata!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Bildirme hatası: denetim bilgisi yok"}, "report/renderer/report-utils.js | expandView": {"message": "Görünümü genişlet"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON> bi<PERSON>"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Test Verileri"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Mevcut sayfanın mobil ağ kullanılarak gerçekleştirilen [Lighthouse](https://developers.google.com/web/tools/lighthouse/) analizi. Değerler tahminidir ve değişiklik gösterebilir."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON> o<PERSON>ak kontrol edilecek ek öğeler"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Geç<PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Başarılı denetimler"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "İlk sayfa yü<PERSON>me"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "<PERSON><PERSON> k<PERSON>ı<PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emülasyonlu Ma<PERSON>ü<PERSON>ü"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Emülasyon yok"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe sürümü"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Kısıtlanmamış CPU/Bellek Gücü"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU kısıtlaması"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Cihaz"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "<PERSON><PERSON> sı<PERSON>ırl<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Ekran emülasyonu"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Kullanıcı aracısı (ağ)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Tek sayfa yükleme"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "<PERSON><PERSON> veriler, birçok oturumu özetleyen alan verilerinin aksine tek bir sayfa yükleme işleminden alınmıştır."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Yavaş 4G kısıtlaması"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Bilinmiyor"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Şunlarla alakalı denetimleri göster:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Snippe<PERSON>'<PERSON> daralt"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Snippe<PERSON>'<PERSON> geni<PERSON>let"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "3. <PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Ortam tarafından sağlandı"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Şu Lighthouse çalışmasını etkileyen sorunlar vardı:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "De<PERSON><PERSON>ler tahminidir ve değişiklik gösterebilir. [Performance skorunun hesaplanması ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/), do<PERSON><PERSON><PERSON> bu metriklerle yapılır."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Orijinal İzi Göster"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON>ğaç grafiği göster"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Kontrollerden geçti, ancak uyarılar var"}, "report/renderer/report-utils.js | warningHeader": {"message": "Uyarılar: "}, "treemap/app/src/util.js | allLabel": {"message": "Tümü"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Kaynak Baytları"}, "treemap/app/src/util.js | tableColumnName": {"message": "Ad"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Tabloyu Aç/Kapat"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Ku<PERSON><PERSON><PERSON><PERSON>"}}