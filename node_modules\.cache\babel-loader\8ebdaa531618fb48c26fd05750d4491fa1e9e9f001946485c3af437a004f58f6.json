{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport getScrollBarSize from '../getScrollBarSize';\nimport setStyle from '../setStyle';\nvar locks = [];\nvar scrollingEffectClassName = 'ant-scrolling-effect';\nvar scrollingEffectClassNameReg = new RegExp(\"\".concat(scrollingEffectClassName), 'g');\nvar uuid = 0; // https://github.com/ant-design/ant-design/issues/19340\n// https://github.com/ant-design/ant-design/issues/19332\n\nvar cacheStyle = new Map();\nvar ScrollLocker = /*#__PURE__*/_createClass(function ScrollLocker(_options) {\n  var _this = this;\n  _classCallCheck(this, <PERSON><PERSON>Locker);\n  this.lockTarget = void 0;\n  this.options = void 0;\n  this.getContainer = function () {\n    var _this$options;\n    return (_this$options = _this.options) === null || _this$options === void 0 ? void 0 : _this$options.container;\n  };\n  this.reLock = function (options) {\n    var findLock = locks.find(function (_ref) {\n      var target = _ref.target;\n      return target === _this.lockTarget;\n    });\n    if (findLock) {\n      _this.unLock();\n    }\n    _this.options = options;\n    if (findLock) {\n      findLock.options = options;\n      _this.lock();\n    }\n  };\n  this.lock = function () {\n    var _this$options3;\n\n    // If lockTarget exist return\n    if (locks.some(function (_ref2) {\n      var target = _ref2.target;\n      return target === _this.lockTarget;\n    })) {\n      return;\n    } // If same container effect, return\n\n    if (locks.some(function (_ref3) {\n      var _this$options2;\n      var options = _ref3.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_this$options2 = _this.options) === null || _this$options2 === void 0 ? void 0 : _this$options2.container);\n    })) {\n      locks = [].concat(_toConsumableArray(locks), [{\n        target: _this.lockTarget,\n        options: _this.options\n      }]);\n      return;\n    }\n    var scrollBarSize = 0;\n    var container = ((_this$options3 = _this.options) === null || _this$options3 === void 0 ? void 0 : _this$options3.container) || document.body;\n    if (container === document.body && window.innerWidth - document.documentElement.clientWidth > 0 || container.scrollHeight > container.clientHeight) {\n      scrollBarSize = getScrollBarSize();\n    }\n    var containerClassName = container.className;\n    if (locks.filter(function (_ref4) {\n      var _this$options4;\n      var options = _ref4.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_this$options4 = _this.options) === null || _this$options4 === void 0 ? void 0 : _this$options4.container);\n    }).length === 0) {\n      cacheStyle.set(container, setStyle({\n        width: scrollBarSize !== 0 ? \"calc(100% - \".concat(scrollBarSize, \"px)\") : undefined,\n        overflow: 'hidden',\n        overflowX: 'hidden',\n        overflowY: 'hidden'\n      }, {\n        element: container\n      }));\n    } // https://github.com/ant-design/ant-design/issues/19729\n\n    if (!scrollingEffectClassNameReg.test(containerClassName)) {\n      var addClassName = \"\".concat(containerClassName, \" \").concat(scrollingEffectClassName);\n      container.className = addClassName.trim();\n    }\n    locks = [].concat(_toConsumableArray(locks), [{\n      target: _this.lockTarget,\n      options: _this.options\n    }]);\n  };\n  this.unLock = function () {\n    var _this$options5;\n    var findLock = locks.find(function (_ref5) {\n      var target = _ref5.target;\n      return target === _this.lockTarget;\n    });\n    locks = locks.filter(function (_ref6) {\n      var target = _ref6.target;\n      return target !== _this.lockTarget;\n    });\n    if (!findLock || locks.some(function (_ref7) {\n      var _findLock$options;\n      var options = _ref7.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_findLock$options = findLock.options) === null || _findLock$options === void 0 ? void 0 : _findLock$options.container);\n    })) {\n      return;\n    } // Remove Effect\n\n    var container = ((_this$options5 = _this.options) === null || _this$options5 === void 0 ? void 0 : _this$options5.container) || document.body;\n    var containerClassName = container.className;\n    if (!scrollingEffectClassNameReg.test(containerClassName)) return;\n    setStyle(cacheStyle.get(container), {\n      element: container\n    });\n    cacheStyle.delete(container);\n    container.className = container.className.replace(scrollingEffectClassNameReg, '').trim();\n  };\n\n  // eslint-disable-next-line no-plusplus\n  this.lockTarget = uuid++;\n  this.options = _options;\n});\nexport { ScrollLocker as default };", "map": {"version": 3, "names": ["_toConsumableArray", "_createClass", "_classCallCheck", "getScrollBarSize", "setStyle", "locks", "scrollingEffectClassName", "scrollingEffectClassNameReg", "RegExp", "concat", "uuid", "cacheStyle", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_options", "_this", "<PERSON><PERSON><PERSON><PERSON>", "options", "getContainer", "_this$options", "container", "reLock", "findLock", "find", "_ref", "target", "unLock", "lock", "_this$options3", "some", "_ref2", "_ref3", "_this$options2", "scrollBarSize", "document", "body", "window", "innerWidth", "documentElement", "clientWidth", "scrollHeight", "clientHeight", "containerClassName", "className", "filter", "_ref4", "_this$options4", "length", "set", "width", "undefined", "overflow", "overflowX", "overflowY", "element", "test", "addClassName", "trim", "_this$options5", "_ref5", "_ref6", "_ref7", "_findLock$options", "get", "delete", "replace", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/scrollLocker.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport getScrollBarSize from '../getScrollBarSize';\nimport setStyle from '../setStyle';\nvar locks = [];\nvar scrollingEffectClassName = 'ant-scrolling-effect';\nvar scrollingEffectClassNameReg = new RegExp(\"\".concat(scrollingEffectClassName), 'g');\nvar uuid = 0; // https://github.com/ant-design/ant-design/issues/19340\n// https://github.com/ant-design/ant-design/issues/19332\n\nvar cacheStyle = new Map();\n\nvar ScrollLocker = /*#__PURE__*/_createClass(function ScrollLocker(_options) {\n  var _this = this;\n\n  _classCallCheck(this, <PERSON><PERSON>Locker);\n\n  this.lockTarget = void 0;\n  this.options = void 0;\n\n  this.getContainer = function () {\n    var _this$options;\n\n    return (_this$options = _this.options) === null || _this$options === void 0 ? void 0 : _this$options.container;\n  };\n\n  this.reLock = function (options) {\n    var findLock = locks.find(function (_ref) {\n      var target = _ref.target;\n      return target === _this.lockTarget;\n    });\n\n    if (findLock) {\n      _this.unLock();\n    }\n\n    _this.options = options;\n\n    if (findLock) {\n      findLock.options = options;\n\n      _this.lock();\n    }\n  };\n\n  this.lock = function () {\n    var _this$options3;\n\n    // If lockTarget exist return\n    if (locks.some(function (_ref2) {\n      var target = _ref2.target;\n      return target === _this.lockTarget;\n    })) {\n      return;\n    } // If same container effect, return\n\n\n    if (locks.some(function (_ref3) {\n      var _this$options2;\n\n      var options = _ref3.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_this$options2 = _this.options) === null || _this$options2 === void 0 ? void 0 : _this$options2.container);\n    })) {\n      locks = [].concat(_toConsumableArray(locks), [{\n        target: _this.lockTarget,\n        options: _this.options\n      }]);\n      return;\n    }\n\n    var scrollBarSize = 0;\n    var container = ((_this$options3 = _this.options) === null || _this$options3 === void 0 ? void 0 : _this$options3.container) || document.body;\n\n    if (container === document.body && window.innerWidth - document.documentElement.clientWidth > 0 || container.scrollHeight > container.clientHeight) {\n      scrollBarSize = getScrollBarSize();\n    }\n\n    var containerClassName = container.className;\n\n    if (locks.filter(function (_ref4) {\n      var _this$options4;\n\n      var options = _ref4.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_this$options4 = _this.options) === null || _this$options4 === void 0 ? void 0 : _this$options4.container);\n    }).length === 0) {\n      cacheStyle.set(container, setStyle({\n        width: scrollBarSize !== 0 ? \"calc(100% - \".concat(scrollBarSize, \"px)\") : undefined,\n        overflow: 'hidden',\n        overflowX: 'hidden',\n        overflowY: 'hidden'\n      }, {\n        element: container\n      }));\n    } // https://github.com/ant-design/ant-design/issues/19729\n\n\n    if (!scrollingEffectClassNameReg.test(containerClassName)) {\n      var addClassName = \"\".concat(containerClassName, \" \").concat(scrollingEffectClassName);\n      container.className = addClassName.trim();\n    }\n\n    locks = [].concat(_toConsumableArray(locks), [{\n      target: _this.lockTarget,\n      options: _this.options\n    }]);\n  };\n\n  this.unLock = function () {\n    var _this$options5;\n\n    var findLock = locks.find(function (_ref5) {\n      var target = _ref5.target;\n      return target === _this.lockTarget;\n    });\n    locks = locks.filter(function (_ref6) {\n      var target = _ref6.target;\n      return target !== _this.lockTarget;\n    });\n\n    if (!findLock || locks.some(function (_ref7) {\n      var _findLock$options;\n\n      var options = _ref7.options;\n      return (options === null || options === void 0 ? void 0 : options.container) === ((_findLock$options = findLock.options) === null || _findLock$options === void 0 ? void 0 : _findLock$options.container);\n    })) {\n      return;\n    } // Remove Effect\n\n\n    var container = ((_this$options5 = _this.options) === null || _this$options5 === void 0 ? void 0 : _this$options5.container) || document.body;\n    var containerClassName = container.className;\n    if (!scrollingEffectClassNameReg.test(containerClassName)) return;\n    setStyle(cacheStyle.get(container), {\n      element: container\n    });\n    cacheStyle.delete(container);\n    container.className = container.className.replace(scrollingEffectClassNameReg, '').trim();\n  };\n\n  // eslint-disable-next-line no-plusplus\n  this.lockTarget = uuid++;\n  this.options = _options;\n});\n\nexport { ScrollLocker as default };"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,QAAQ,MAAM,aAAa;AAClC,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,wBAAwB,GAAG,sBAAsB;AACrD,IAAIC,2BAA2B,GAAG,IAAIC,MAAM,CAAC,EAAE,CAACC,MAAM,CAACH,wBAAwB,CAAC,EAAE,GAAG,CAAC;AACtF,IAAII,IAAI,GAAG,CAAC,CAAC,CAAC;AACd;;AAEA,IAAIC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE1B,IAAIC,YAAY,GAAG,aAAaZ,YAAY,CAAC,SAASY,YAAYA,CAACC,QAAQ,EAAE;EAC3E,IAAIC,KAAK,GAAG,IAAI;EAEhBb,eAAe,CAAC,IAAI,EAAEW,YAAY,CAAC;EAEnC,IAAI,CAACG,UAAU,GAAG,KAAK,CAAC;EACxB,IAAI,CAACC,OAAO,GAAG,KAAK,CAAC;EAErB,IAAI,CAACC,YAAY,GAAG,YAAY;IAC9B,IAAIC,aAAa;IAEjB,OAAO,CAACA,aAAa,GAAGJ,KAAK,CAACE,OAAO,MAAM,IAAI,IAAIE,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,SAAS;EAChH,CAAC;EAED,IAAI,CAACC,MAAM,GAAG,UAAUJ,OAAO,EAAE;IAC/B,IAAIK,QAAQ,GAAGjB,KAAK,CAACkB,IAAI,CAAC,UAAUC,IAAI,EAAE;MACxC,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,OAAOA,MAAM,KAAKV,KAAK,CAACC,UAAU;IACpC,CAAC,CAAC;IAEF,IAAIM,QAAQ,EAAE;MACZP,KAAK,CAACW,MAAM,CAAC,CAAC;IAChB;IAEAX,KAAK,CAACE,OAAO,GAAGA,OAAO;IAEvB,IAAIK,QAAQ,EAAE;MACZA,QAAQ,CAACL,OAAO,GAAGA,OAAO;MAE1BF,KAAK,CAACY,IAAI,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAACA,IAAI,GAAG,YAAY;IACtB,IAAIC,cAAc;;IAElB;IACA,IAAIvB,KAAK,CAACwB,IAAI,CAAC,UAAUC,KAAK,EAAE;MAC9B,IAAIL,MAAM,GAAGK,KAAK,CAACL,MAAM;MACzB,OAAOA,MAAM,KAAKV,KAAK,CAACC,UAAU;IACpC,CAAC,CAAC,EAAE;MACF;IACF,CAAC,CAAC;;IAGF,IAAIX,KAAK,CAACwB,IAAI,CAAC,UAAUE,KAAK,EAAE;MAC9B,IAAIC,cAAc;MAElB,IAAIf,OAAO,GAAGc,KAAK,CAACd,OAAO;MAC3B,OAAO,CAACA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,OAAO,CAACY,cAAc,GAAGjB,KAAK,CAACE,OAAO,MAAM,IAAI,IAAIe,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACZ,SAAS,CAAC;IAC/L,CAAC,CAAC,EAAE;MACFf,KAAK,GAAG,EAAE,CAACI,MAAM,CAACT,kBAAkB,CAACK,KAAK,CAAC,EAAE,CAAC;QAC5CoB,MAAM,EAAEV,KAAK,CAACC,UAAU;QACxBC,OAAO,EAAEF,KAAK,CAACE;MACjB,CAAC,CAAC,CAAC;MACH;IACF;IAEA,IAAIgB,aAAa,GAAG,CAAC;IACrB,IAAIb,SAAS,GAAG,CAAC,CAACQ,cAAc,GAAGb,KAAK,CAACE,OAAO,MAAM,IAAI,IAAIW,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACR,SAAS,KAAKc,QAAQ,CAACC,IAAI;IAE7I,IAAIf,SAAS,KAAKc,QAAQ,CAACC,IAAI,IAAIC,MAAM,CAACC,UAAU,GAAGH,QAAQ,CAACI,eAAe,CAACC,WAAW,GAAG,CAAC,IAAInB,SAAS,CAACoB,YAAY,GAAGpB,SAAS,CAACqB,YAAY,EAAE;MAClJR,aAAa,GAAG9B,gBAAgB,CAAC,CAAC;IACpC;IAEA,IAAIuC,kBAAkB,GAAGtB,SAAS,CAACuB,SAAS;IAE5C,IAAItC,KAAK,CAACuC,MAAM,CAAC,UAAUC,KAAK,EAAE;MAChC,IAAIC,cAAc;MAElB,IAAI7B,OAAO,GAAG4B,KAAK,CAAC5B,OAAO;MAC3B,OAAO,CAACA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,OAAO,CAAC0B,cAAc,GAAG/B,KAAK,CAACE,OAAO,MAAM,IAAI,IAAI6B,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC1B,SAAS,CAAC;IAC/L,CAAC,CAAC,CAAC2B,MAAM,KAAK,CAAC,EAAE;MACfpC,UAAU,CAACqC,GAAG,CAAC5B,SAAS,EAAEhB,QAAQ,CAAC;QACjC6C,KAAK,EAAEhB,aAAa,KAAK,CAAC,GAAG,cAAc,CAACxB,MAAM,CAACwB,aAAa,EAAE,KAAK,CAAC,GAAGiB,SAAS;QACpFC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE;MACb,CAAC,EAAE;QACDC,OAAO,EAAElC;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;;IAGF,IAAI,CAACb,2BAA2B,CAACgD,IAAI,CAACb,kBAAkB,CAAC,EAAE;MACzD,IAAIc,YAAY,GAAG,EAAE,CAAC/C,MAAM,CAACiC,kBAAkB,EAAE,GAAG,CAAC,CAACjC,MAAM,CAACH,wBAAwB,CAAC;MACtFc,SAAS,CAACuB,SAAS,GAAGa,YAAY,CAACC,IAAI,CAAC,CAAC;IAC3C;IAEApD,KAAK,GAAG,EAAE,CAACI,MAAM,CAACT,kBAAkB,CAACK,KAAK,CAAC,EAAE,CAAC;MAC5CoB,MAAM,EAAEV,KAAK,CAACC,UAAU;MACxBC,OAAO,EAAEF,KAAK,CAACE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAACS,MAAM,GAAG,YAAY;IACxB,IAAIgC,cAAc;IAElB,IAAIpC,QAAQ,GAAGjB,KAAK,CAACkB,IAAI,CAAC,UAAUoC,KAAK,EAAE;MACzC,IAAIlC,MAAM,GAAGkC,KAAK,CAAClC,MAAM;MACzB,OAAOA,MAAM,KAAKV,KAAK,CAACC,UAAU;IACpC,CAAC,CAAC;IACFX,KAAK,GAAGA,KAAK,CAACuC,MAAM,CAAC,UAAUgB,KAAK,EAAE;MACpC,IAAInC,MAAM,GAAGmC,KAAK,CAACnC,MAAM;MACzB,OAAOA,MAAM,KAAKV,KAAK,CAACC,UAAU;IACpC,CAAC,CAAC;IAEF,IAAI,CAACM,QAAQ,IAAIjB,KAAK,CAACwB,IAAI,CAAC,UAAUgC,KAAK,EAAE;MAC3C,IAAIC,iBAAiB;MAErB,IAAI7C,OAAO,GAAG4C,KAAK,CAAC5C,OAAO;MAC3B,OAAO,CAACA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,OAAO,CAAC0C,iBAAiB,GAAGxC,QAAQ,CAACL,OAAO,MAAM,IAAI,IAAI6C,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC1C,SAAS,CAAC;IAC3M,CAAC,CAAC,EAAE;MACF;IACF,CAAC,CAAC;;IAGF,IAAIA,SAAS,GAAG,CAAC,CAACsC,cAAc,GAAG3C,KAAK,CAACE,OAAO,MAAM,IAAI,IAAIyC,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACtC,SAAS,KAAKc,QAAQ,CAACC,IAAI;IAC7I,IAAIO,kBAAkB,GAAGtB,SAAS,CAACuB,SAAS;IAC5C,IAAI,CAACpC,2BAA2B,CAACgD,IAAI,CAACb,kBAAkB,CAAC,EAAE;IAC3DtC,QAAQ,CAACO,UAAU,CAACoD,GAAG,CAAC3C,SAAS,CAAC,EAAE;MAClCkC,OAAO,EAAElC;IACX,CAAC,CAAC;IACFT,UAAU,CAACqD,MAAM,CAAC5C,SAAS,CAAC;IAC5BA,SAAS,CAACuB,SAAS,GAAGvB,SAAS,CAACuB,SAAS,CAACsB,OAAO,CAAC1D,2BAA2B,EAAE,EAAE,CAAC,CAACkD,IAAI,CAAC,CAAC;EAC3F,CAAC;;EAED;EACA,IAAI,CAACzC,UAAU,GAAGN,IAAI,EAAE;EACxB,IAAI,CAACO,OAAO,GAAGH,QAAQ;AACzB,CAAC,CAAC;AAEF,SAASD,YAAY,IAAIqD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}