{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\gestioneMagazzini.jsx\";\n/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneMagazzinieri - operazioni sugli operatori ed i responsabili di magazzino\n *\n */\nimport React, { Component } from \"react\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { AggiungiMagazzino } from \"../../aggiunta_dati/aggiungiMagazzino\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport \"../../css/DataTableDemo.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneMagazzini extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    this.state = {\n      results: [],\n      resultDialog: false,\n      loading: true\n    };\n    //Dichiarazione funzioni e metodi\n    this.aggiungiMag = this.aggiungiMag.bind(this);\n    this.hideaggiungiAggiungiMag = this.hideaggiungiAggiungiMag.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      this.setState({\n        results: res.data,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiMag() {\n    this.setState({\n      resultDialog: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAggiungiMag() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideaggiungiAggiungiMag,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: \"id\",\n      header: 'ID',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"warehouseName\",\n      header: Costanti.Magazzino,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"address\",\n      header: Costanti.Indirizzo,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"citta\",\n      header: Costanti.Città,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"cap\",\n      header: Costanti.CodPost,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"codDep\",\n      header: Costanti.CodDep,\n      sortable: true,\n      showHeader: true\n    }];\n    const items = [{\n      label: Costanti.AggMAgazzino,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiMag();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneMagazzini\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          fileNames: \"Magazzini\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.AggMAgazzino,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hideaggiungiAggiungiMag,\n        children: /*#__PURE__*/_jsxDEV(AggiungiMagazzino, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GestioneMagazzini;", "map": {"version": 3, "names": ["React", "Component", "APIRequest", "Toast", "<PERSON><PERSON>", "Dialog", "<PERSON><PERSON>", "AggiungiMagazzino", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "GestioneMagazzini", "constructor", "props", "state", "results", "resultDialog", "loading", "aggiungiMag", "bind", "hideaggiungiAggiungiMag", "componentDidMount", "then", "res", "setState", "data", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "render", "resultDialogFooter2", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "field", "header", "sortable", "showHeader", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "CodDep", "items", "label", "AggMAgazzino", "icon", "command", "ref", "el", "gestioneMagazzini", "dt", "value", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "splitButtonClass", "fileNames", "visible", "modal", "footer", "onHide"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/gestioneMagazzini.jsx"], "sourcesContent": ["/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneMagazzinieri - operazioni sugli operatori ed i responsabili di magazzino\n *\n */\nimport React, { Component } from \"react\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { AggiungiMagazzino } from \"../../aggiunta_dati/aggiungiMagazzino\";\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport \"../../css/DataTableDemo.css\";\n\nclass GestioneMagazzini extends Component {\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            results: [],\n            resultDialog: false,\n            loading: true,\n        };\n        //Dichiarazione funzioni e metodi\n        this.aggiungiMag = this.aggiungiMag.bind(this);\n        this.hideaggiungiAggiungiMag = this.hideaggiungiAggiungiMag.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                this.setState({\n                    results: res.data,\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    aggiungiMag() {\n        this.setState({\n            resultDialog: true,\n        });\n    }\n    //Chiusura dialogo aggiunta\n    hideaggiungiAggiungiMag() {\n        this.setState({\n            resultDialog: false,\n        });\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideaggiungiAggiungiMag}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            {\n                field: \"id\",\n                header: 'ID',\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"warehouseName\",\n                header: Costanti.Magazzino,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"address\",\n                header: Costanti.Indirizzo,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"citta\",\n                header: Costanti.Città,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"cap\",\n                header: Costanti.CodPost,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"codDep\",\n                header: Costanti.CodDep,\n                sortable: true,\n                showHeader: true,\n            },\n        ];\n        const items = [\n            {\n                label: Costanti.AggMAgazzino,\n                icon: 'pi pi-plus-circle',\n                command: () => {\n                    this.aggiungiMag()\n                }\n            },\n        ]\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => (this.toast = el)} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.gestioneMagazzini}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => (this.dt = el)}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        splitButtonClass={true}\n                        items={items}\n                        fileNames=\"Magazzini\"\n                    />\n                </div>\n                {/* Struttura dialogo per l'aggiunta operatore magazzino */}\n                <Dialog\n                    visible={this.state.resultDialog}\n                    header={Costanti.AggMAgazzino}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter2}\n                    onHide={this.hideaggiungiAggiungiMag}\n                >\n                    <AggiungiMagazzino />\n                </Dialog>\n            </div>\n        );\n    }\n}\n\nexport default GestioneMagazzini"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,SAASX,SAAS,CAAC;EACtCY,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IACA,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE;IACb,CAAC;IACD;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACA,uBAAuB,CAACD,IAAI,CAAC,IAAI,CAAC;EAC1E;EACA;EACA,MAAME,iBAAiBA,CAAA,EAAG;IACtB,MAAMpB,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCqB,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACC,QAAQ,CAAC;QACVT,OAAO,EAAEQ,GAAG,CAACE,IAAI;QACjBR,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDS,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACVC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKc,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGE,CAAC,CAACa,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACAvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACM,QAAQ,CAAC;MACVR,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAI,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACI,QAAQ,CAAC;MACVR,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA0B,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,mBAAmB,gBACrBjC,OAAA,CAACX,KAAK,CAAC6C,QAAQ;MAAAC,QAAA,eACXnC,OAAA,CAACP,MAAM;QAAC2C,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC3B,uBAAwB;QAAAyB,QAAA,GACnE,GAAG,EACHxC,QAAQ,CAAC2C,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMC,MAAM,GAAG,CACX;MACIC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIH,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAElD,QAAQ,CAACqD,SAAS;MAC1BF,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIH,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAElD,QAAQ,CAACsD,SAAS;MAC1BH,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIH,KAAK,EAAE,OAAO;MACdC,MAAM,EAAElD,QAAQ,CAACuD,KAAK;MACtBJ,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIH,KAAK,EAAE,KAAK;MACZC,MAAM,EAAElD,QAAQ,CAACwD,OAAO;MACxBL,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIH,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAElD,QAAQ,CAACyD,MAAM;MACvBN,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMM,KAAK,GAAG,CACV;MACIC,KAAK,EAAE3D,QAAQ,CAAC4D,YAAY;MAC5BC,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACjD,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,CACJ;IACD,oBACIR,OAAA;MAAKoC,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CnC,OAAA,CAACR,KAAK;QAACkE,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACrC,KAAK,GAAGqC;MAAI;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzC1C,OAAA,CAACH,GAAG;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP1C,OAAA;QAAKoC,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCnC,OAAA;UAAAmC,QAAA,EAAKxC,QAAQ,CAACiE;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACN1C,OAAA;QAAKoC,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBnC,OAAA,CAACF,eAAe;UACZ4D,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACE,EAAE,GAAGF,EAAI;UAC5BG,KAAK,EAAE,IAAI,CAAC1D,KAAK,CAACC,OAAQ;UAC1BsC,MAAM,EAAEA,MAAO;UACfpC,OAAO,EAAE,IAAI,CAACH,KAAK,CAACG,OAAQ;UAC5BwD,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBf,KAAK,EAAEA,KAAM;UACbgB,SAAS,EAAC;QAAW;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN1C,OAAA,CAACN,MAAM;QACH4E,OAAO,EAAE,IAAI,CAAClE,KAAK,CAACE,YAAa;QACjCuC,MAAM,EAAElD,QAAQ,CAAC4D,YAAa;QAC9BgB,KAAK;QACLnC,SAAS,EAAC,kBAAkB;QAC5BoC,MAAM,EAAEvC,mBAAoB;QAC5BwC,MAAM,EAAE,IAAI,CAAC/D,uBAAwB;QAAAyB,QAAA,eAErCnC,OAAA,CAACJ,iBAAiB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAezC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}