{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type {\n  ConditionalValue,\n  ExportsSubpaths,\n  Imports,\n} from 'resolve-import'\n\nexport type TshyConfigMaybeGlobExports = {\n  exports?: string | string[] | Record<string, TshyExport>\n  dialects?: Dialect[]\n  selfLink?: boolean\n  main?: boolean\n  module?: boolean\n  commonjsDialects?: string[]\n  esmDialects?: string[]\n  sourceDialects?: string[]\n  project?: string\n  exclude?: string[]\n  liveDev?: boolean\n}\n\nexport type TshyConfig = TshyConfigMaybeGlobExports & {\n  exports?: Record<string, TshyExport>\n}\n\nexport type Dialect = 'commonjs' | 'esm'\n\nexport type ExportDetail = {\n  default: string\n  [k: string]: string\n}\n\nexport type TshyExport = ConditionalValue\n\nexport type Package = {\n  name: string\n  version: string\n  main?: string\n  types?: string\n  type?: 'module' | 'commonjs'\n  bin?: string | Record<string, string>\n  exports?: ExportsSubpaths\n  tshy?: TshyConfigMaybeGlobExports\n  imports?: Imports\n  [k: string]: any\n}\n"]}