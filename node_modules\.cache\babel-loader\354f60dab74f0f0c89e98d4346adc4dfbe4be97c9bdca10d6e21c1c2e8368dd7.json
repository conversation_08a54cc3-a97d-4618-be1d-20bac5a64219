{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\respMagazzino\\\\gestioneOperatori.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneOperatori - operazioni sulla gestione degli operatori di magazzino\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\n/* import { confirmDialog } from \"primereact/confirmdialog\"; */\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { resp_magConfrontoDispQta } from \"../../components/route\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneOperatori extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: '',\n      referente: '',\n      deliveryDestination: '',\n      orderDate: '',\n      deliveryDate: '',\n      termsPayment: '',\n      paymentStatus: '',\n      status: ''\n    };\n    this.onRowSelect = () => {\n      if (this.state.selectedResult !== null) {\n        /* if (this.state.selectedResult.status !== 'create') {\n            confirmDialog({\n                message: Costanti.AlertOrdSel,\n                header: 'Attenzione',\n                icon: 'pi pi-exclamation-triangle',\n                acceptLabel: \"Si\",\n                rejectLabel: \"No\",\n                accept: this.addMessages,\n                reject: this.clearMessages\n            });\n        } else { */\n        var idFOROrd = this.state.selectedResult;\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        window.location.pathname = resp_magConfrontoDispQta;\n        /* } */\n      }\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      globalFilter: null,\n      deleteResultDialog: false,\n      selectedResult: null,\n      idEmployee: 0,\n      opMag: '',\n      mex: '',\n      firstName: '',\n      address: '',\n      indFatt: '',\n      loading: true\n    };\n    this.opMag = [];\n    this.onRowSelect = this.onRowSelect.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\").then(res => {\n      this.setState({\n        results: res.data,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  render() {\n    const fields = [{\n      field: 'first_name',\n      header: Costanti.Nome,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'address',\n      header: Costanti.Indirizzo,\n      body: 'address',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'city',\n      header: Costanti.Città,\n      body: 'city',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'cap',\n      header: Costanti.CodPost,\n      body: 'cap',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'p_iva',\n      header: Costanti.pIva,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'tel',\n      header: Costanti.Tel,\n      body: 'tel',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'email',\n      header: Costanti.Email,\n      body: 'email',\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'username',\n      header: Costanti.NomeUtente,\n      body: 'username',\n      sortable: true,\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Operatori\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          showExportCsvButton: true,\n          fileNames: \"OperatoriDiMagazzino\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GestioneOperatori;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "Nav", "CustomDataTable", "resp_magConfrontoDispQta", "jsxDEV", "_jsxDEV", "GestioneOperatori", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onRowSelect", "state", "selected<PERSON><PERSON><PERSON>", "idFOROrd", "localStorage", "setItem", "JSON", "stringify", "window", "location", "pathname", "results", "results2", "result", "resultDialog", "globalFilter", "deleteResultDialog", "idEmployee", "opMag", "mex", "address", "indFatt", "loading", "bind", "componentDidMount", "then", "res", "setState", "data", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "render", "fields", "field", "header", "Nome", "sortable", "showHeader", "<PERSON><PERSON><PERSON><PERSON>", "body", "Città", "CodPost", "pIva", "Tel", "Email", "NomeUtente", "className", "children", "ref", "el", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Operatori", "dt", "value", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "showExportCsvButton", "fileNames"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/respMagazzino/gestioneOperatori.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneOperatori - operazioni sulla gestione degli operatori di magazzino\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\n/* import { confirmDialog } from \"primereact/confirmdialog\"; */\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { resp_magConfrontoDispQta } from \"../../components/route\";\n\nclass GestioneOperatori extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: '',\n        referente: '',\n        deliveryDestination: '',\n        orderDate: '',\n        deliveryDate: '',\n        termsPayment: '',\n        paymentStatus: '',\n        status: ''\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            result: this.emptyResult,\n            resultDialog: false,\n            globalFilter: null,\n            deleteResultDialog: false,\n            selectedResult: null,\n            idEmployee: 0,\n            opMag: '',\n            mex: '',\n            firstName: '',\n            address: '',\n            indFatt: '',\n            loading: true\n        }\n        this.opMag = []\n        this.onRowSelect = this.onRowSelect.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"employees?employeesEffort=true&role=OP_MAG\")\n            .then((res) => {\n                this.setState({\n                    results: res.data,\n                    loading: false,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    onRowSelect = () => {\n        if (this.state.selectedResult !== null) {\n            /* if (this.state.selectedResult.status !== 'create') {\n                confirmDialog({\n                    message: Costanti.AlertOrdSel,\n                    header: 'Attenzione',\n                    icon: 'pi pi-exclamation-triangle',\n                    acceptLabel: \"Si\",\n                    rejectLabel: \"No\",\n                    accept: this.addMessages,\n                    reject: this.clearMessages\n                });\n            } else { */\n            var idFOROrd = this.state.selectedResult;\n            localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n            window.location.pathname = resp_magConfrontoDispQta;\n            /* } */\n        }\n    }\n    render() {\n        const fields = [\n            { field: 'first_name', header: Costanti.Nome, sortable: true, showHeader: true },\n            { field: 'address', header: Costanti.Indirizzo, body: 'address', sortable: true, showHeader: true },\n            { field: 'city', header: Costanti.Città, body: 'city', sortable: true, showHeader: true },\n            { field: 'cap', header: Costanti.CodPost, body: 'cap', sortable: true, showHeader: true },\n            { field: 'p_iva', header: Costanti.pIva, sortable: true, showHeader: true },\n            { field: 'tel', header: Costanti.Tel, body: 'tel', sortable: true, showHeader: true },\n            { field: 'email', header: Costanti.Email, body: 'email', sortable: true, showHeader: true },\n            { field: 'username', header: Costanti.NomeUtente, body: 'username', sortable: true, showHeader: true },\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.Operatori}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione e la visualizzazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        fileNames=\"OperatoriDiMagazzino\"\n                    />\n                </div>\n            </div>\n        )\n    }\n}\n\nexport default GestioneOperatori;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE;AACA,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,wBAAwB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,iBAAiB,SAAST,SAAS,CAAC;EAatCU,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAAA,KAyCDC,WAAW,GAAG,MAAM;MAChB,IAAI,IAAI,CAACC,KAAK,CAACC,cAAc,KAAK,IAAI,EAAE;QACpC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACY,IAAIC,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACC,cAAc;QACxCE,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC;QAC5DK,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG1B,wBAAwB;QACnD;MACJ;IACJ,CAAC;IAxDG,IAAI,CAACiB,KAAK,GAAG;MACTU,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAACvB,WAAW;MACxBwB,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBd,cAAc,EAAE,IAAI;MACpBe,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACP3B,SAAS,EAAE,EAAE;MACb4B,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACJ,KAAK,GAAG,EAAE;IACf,IAAI,CAAClB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACuB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,MAAM3C,UAAU,CAAC,KAAK,EAAE,4CAA4C,CAAC,CAChE4C,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACC,QAAQ,CAAC;QACVhB,OAAO,EAAEe,GAAG,CAACE,IAAI;QACjBN,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDO,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACVC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKc,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGE,CAAC,CAACa,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EAoBAC,MAAMA,CAAA,EAAG;IACL,MAAMC,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAEpE,QAAQ,CAACqE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAChF;MAAEJ,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAEpE,QAAQ,CAACwE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACnG;MAAEJ,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEpE,QAAQ,CAAC0E,KAAK;MAAED,IAAI,EAAE,MAAM;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACzF;MAAEJ,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEpE,QAAQ,CAAC2E,OAAO;MAAEF,IAAI,EAAE,KAAK;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACzF;MAAEJ,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEpE,QAAQ,CAAC4E,IAAI;MAAEN,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC3E;MAAEJ,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEpE,QAAQ,CAAC6E,GAAG;MAAEJ,IAAI,EAAE,KAAK;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACrF;MAAEJ,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEpE,QAAQ,CAAC8E,KAAK;MAAEL,IAAI,EAAE,OAAO;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC3F;MAAEJ,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAEpE,QAAQ,CAAC+E,UAAU;MAAEN,IAAI,EAAE,UAAU;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CACzG;IACD,oBACIjE,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE9C3E,OAAA,CAACP,KAAK;QAACmF,GAAG,EAAGC,EAAE,IAAK,IAAI,CAAC5B,KAAK,GAAG4B;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCjF,OAAA,CAACJ,GAAG;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPjF,OAAA;QAAK0E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACnC3E,OAAA;UAAA2E,QAAA,EAAKjF,QAAQ,CAACwF;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNjF,OAAA;QAAK0E,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEjB3E,OAAA,CAACH,eAAe;UACZ+E,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACM,EAAE,GAAGN,EAAG;UAC1BO,KAAK,EAAE,IAAI,CAACrE,KAAK,CAACU,OAAQ;UAC1BmC,MAAM,EAAEA,MAAO;UACfxB,OAAO,EAAE,IAAI,CAACrB,KAAK,CAACqB,OAAQ;UAC5BiD,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BC,SAAS,EAAC;QAAsB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAehF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}