{"ast": null, "code": "var global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\nvar Object = global.Object;\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, Object(it));\n};", "map": {"version": 3, "names": ["global", "require", "getBuiltIn", "isCallable", "isPrototypeOf", "USE_SYMBOL_AS_UID", "Object", "module", "exports", "it", "$Symbol", "prototype"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/is-symbol.js"], "sourcesContent": ["var global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Object = global.Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, Object(it));\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,UAAU,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AACrD,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIG,aAAa,GAAGH,OAAO,CAAC,qCAAqC,CAAC;AAClE,IAAII,iBAAiB,GAAGJ,OAAO,CAAC,gCAAgC,CAAC;AAEjE,IAAIK,MAAM,GAAGN,MAAM,CAACM,MAAM;AAE1BC,MAAM,CAACC,OAAO,GAAGH,iBAAiB,GAAG,UAAUI,EAAE,EAAE;EACjD,OAAO,OAAOA,EAAE,IAAI,QAAQ;AAC9B,CAAC,GAAG,UAAUA,EAAE,EAAE;EAChB,IAAIC,OAAO,GAAGR,UAAU,CAAC,QAAQ,CAAC;EAClC,OAAOC,UAAU,CAACO,OAAO,CAAC,IAAIN,aAAa,CAACM,OAAO,CAACC,SAAS,EAAEL,MAAM,CAACG,EAAE,CAAC,CAAC;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}