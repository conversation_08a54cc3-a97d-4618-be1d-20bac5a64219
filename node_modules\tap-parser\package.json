{"name": "tap-parser", "version": "16.0.1", "description": "parse the test anything protocol", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "bin": {"tap-parser": "bin/cmd.cjs"}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist", "bin"], "scripts": {"snap": "tap", "test": "tap", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "license": "BlueOak-1.0.0", "dependencies": {"events-to-array": "^2.0.3", "tap-yaml": "2.2.2"}, "tap": {"include": ["test/*.ts"], "coverage-map": "map.js", "typecheck": false}, "keywords": ["tap", "test", "parser"], "engines": {"node": "16 >=16.17.0 || 18 >= 18.6.0 || >=20"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/tapjs.git"}}