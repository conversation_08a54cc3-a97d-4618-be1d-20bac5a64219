{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar Notice = /*#__PURE__*/function (_Component) {\n  (0, _inherits2.default)(Notice, _Component);\n  var _super = (0, _createSuper2.default)(Notice);\n  function Notice() {\n    var _this;\n    (0, _classCallCheck2.default)(this, Notice);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n      _this.clearCloseTimer();\n      var _this$props = _this.props,\n        onClose = _this$props.onClose,\n        noticeKey = _this$props.noticeKey;\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n    return _this;\n  }\n  (0, _createClass2.default)(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark ||\n      // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closable = _this$props2.closable,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style,\n        onClick = _this$props2.onClick,\n        children = _this$props2.children,\n        holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", (0, _extends2.default)({\n        className: (0, _classnames.default)(componentClass, className, (0, _defineProperty2.default)({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n      if (holder) {\n        return /*#__PURE__*/_reactDom.default.createPortal(node, holder);\n      }\n      return node;\n    }\n  }]);\n  return Notice;\n}(React.Component);\nexports.default = Notice;\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "default", "_extends2", "_defineProperty2", "_classCallCheck2", "_createClass2", "_inherits2", "_createSuper2", "React", "_reactDom", "_classnames", "Notice", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "closeTimer", "close", "e", "stopPropagation", "clearCloseTimer", "_this$props", "props", "onClose", "<PERSON><PERSON><PERSON>", "startCloseTimer", "duration", "window", "setTimeout", "clearTimeout", "key", "componentDidMount", "componentDidUpdate", "prevProps", "updateMark", "visible", "restartCloseTimer", "componentWillUnmount", "render", "_this2", "_this$props2", "prefixCls", "className", "closable", "closeIcon", "style", "onClick", "children", "holder", "componentClass", "dataOrAriaAttributeProps", "keys", "reduce", "acc", "substr", "node", "createElement", "onMouseEnter", "onMouseLeave", "tabIndex", "createPortal", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-notification/lib/Notice.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _createSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createSuper\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar Notice = /*#__PURE__*/function (_Component) {\n  (0, _inherits2.default)(Notice, _Component);\n\n  var _super = (0, _createSuper2.default)(Notice);\n\n  function Notice() {\n    var _this;\n\n    (0, _classCallCheck2.default)(this, Notice);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n\n      _this.clearCloseTimer();\n\n      var _this$props = _this.props,\n          onClose = _this$props.onClose,\n          noticeKey = _this$props.noticeKey;\n\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n\n    return _this;\n  }\n\n  (0, _createClass2.default)(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark || // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          className = _this$props2.className,\n          closable = _this$props2.closable,\n          closeIcon = _this$props2.closeIcon,\n          style = _this$props2.style,\n          onClick = _this$props2.onClick,\n          children = _this$props2.children,\n          holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", (0, _extends2.default)({\n        className: (0, _classnames.default)(componentClass, className, (0, _defineProperty2.default)({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n\n      if (holder) {\n        return /*#__PURE__*/_reactDom.default.createPortal(node, holder);\n      }\n\n      return node;\n    }\n  }]);\n  return Notice;\n}(React.Component);\n\nexports.default = Notice;\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,SAAS,GAAGN,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIQ,gBAAgB,GAAGP,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,aAAa,GAAGT,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,aAAa,GAAGX,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIa,KAAK,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIc,SAAS,GAAGb,sBAAsB,CAACD,OAAO,CAAC,WAAW,CAAC,CAAC;AAE5D,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIgB,MAAM,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC9C,CAAC,CAAC,EAAEN,UAAU,CAACL,OAAO,EAAEU,MAAM,EAAEC,UAAU,CAAC;EAE3C,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAEN,aAAa,CAACN,OAAO,EAAEU,MAAM,CAAC;EAE/C,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IAET,CAAC,CAAC,EAAEV,gBAAgB,CAACH,OAAO,EAAE,IAAI,EAAEU,MAAM,CAAC;IAE3C,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACU,UAAU,GAAG,IAAI;IAEvBV,KAAK,CAACW,KAAK,GAAG,UAAUC,CAAC,EAAE;MACzB,IAAIA,CAAC,EAAE;QACLA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB;MAEAb,KAAK,CAACc,eAAe,CAAC,CAAC;MAEvB,IAAIC,WAAW,GAAGf,KAAK,CAACgB,KAAK;QACzBC,OAAO,GAAGF,WAAW,CAACE,OAAO;QAC7BC,SAAS,GAAGH,WAAW,CAACG,SAAS;MAErC,IAAID,OAAO,EAAE;QACXA,OAAO,CAACC,SAAS,CAAC;MACpB;IACF,CAAC;IAEDlB,KAAK,CAACmB,eAAe,GAAG,YAAY;MAClC,IAAInB,KAAK,CAACgB,KAAK,CAACI,QAAQ,EAAE;QACxBpB,KAAK,CAACU,UAAU,GAAGW,MAAM,CAACC,UAAU,CAAC,YAAY;UAC/CtB,KAAK,CAACW,KAAK,CAAC,CAAC;QACf,CAAC,EAAEX,KAAK,CAACgB,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;MACjC;IACF,CAAC;IAEDpB,KAAK,CAACc,eAAe,GAAG,YAAY;MAClC,IAAId,KAAK,CAACU,UAAU,EAAE;QACpBa,YAAY,CAACvB,KAAK,CAACU,UAAU,CAAC;QAC9BV,KAAK,CAACU,UAAU,GAAG,IAAI;MACzB;IACF,CAAC;IAED,OAAOV,KAAK;EACd;EAEA,CAAC,CAAC,EAAET,aAAa,CAACJ,OAAO,EAAEU,MAAM,EAAE,CAAC;IAClC2B,GAAG,EAAE,mBAAmB;IACxBtC,KAAK,EAAE,SAASuC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACN,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,oBAAoB;IACzBtC,KAAK,EAAE,SAASwC,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACX,KAAK,CAACI,QAAQ,KAAKO,SAAS,CAACP,QAAQ,IAAI,IAAI,CAACJ,KAAK,CAACY,UAAU,KAAKD,SAAS,CAACC,UAAU;MAAI;MACpG,IAAI,CAACZ,KAAK,CAACa,OAAO,KAAKF,SAAS,CAACE,OAAO,IAAI,IAAI,CAACb,KAAK,CAACa,OAAO,EAAE;QAC9D,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC1B;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,sBAAsB;IAC3BtC,KAAK,EAAE,SAAS6C,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACjB,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,mBAAmB;IACxBtC,KAAK,EAAE,SAAS4C,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAChB,eAAe,CAAC,CAAC;MACtB,IAAI,CAACK,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,QAAQ;IACbtC,KAAK,EAAE,SAAS8C,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAAClB,KAAK;QACzBmB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BC,OAAO,GAAGN,YAAY,CAACM,OAAO;QAC9BC,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,MAAM,GAAGR,YAAY,CAACQ,MAAM;MAChC,IAAIC,cAAc,GAAG,EAAE,CAAClC,MAAM,CAAC0B,SAAS,EAAE,SAAS,CAAC;MACpD,IAAIS,wBAAwB,GAAG7D,MAAM,CAAC8D,IAAI,CAAC,IAAI,CAAC7B,KAAK,CAAC,CAAC8B,MAAM,CAAC,UAAUC,GAAG,EAAEvB,GAAG,EAAE;QAChF,IAAIA,GAAG,CAACwB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAIxB,GAAG,CAACwB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAIxB,GAAG,KAAK,MAAM,EAAE;UAClFuB,GAAG,CAACvB,GAAG,CAAC,GAAGS,MAAM,CAACjB,KAAK,CAACQ,GAAG,CAAC;QAC9B;QAEA,OAAOuB,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAIE,IAAI,GAAG,aAAavD,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE9D,SAAS,CAACD,OAAO,EAAE;QACxEiD,SAAS,EAAE,CAAC,CAAC,EAAExC,WAAW,CAACT,OAAO,EAAEwD,cAAc,EAAEP,SAAS,EAAE,CAAC,CAAC,EAAE/C,gBAAgB,CAACF,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAACsB,MAAM,CAACkC,cAAc,EAAE,WAAW,CAAC,EAAEN,QAAQ,CAAC,CAAC;QACnJE,KAAK,EAAEA,KAAK;QACZY,YAAY,EAAE,IAAI,CAACrC,eAAe;QAClCsC,YAAY,EAAE,IAAI,CAACjC,eAAe;QAClCqB,OAAO,EAAEA;MACX,CAAC,EAAEI,wBAAwB,CAAC,EAAE,aAAalD,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;QACpEd,SAAS,EAAE,EAAE,CAAC3B,MAAM,CAACkC,cAAc,EAAE,UAAU;MACjD,CAAC,EAAEF,QAAQ,CAAC,EAAEJ,QAAQ,GAAG,aAAa3C,KAAK,CAACwD,aAAa,CAAC,GAAG,EAAE;QAC7DG,QAAQ,EAAE,CAAC;QACXb,OAAO,EAAE,IAAI,CAAC7B,KAAK;QACnByB,SAAS,EAAE,EAAE,CAAC3B,MAAM,CAACkC,cAAc,EAAE,QAAQ;MAC/C,CAAC,EAAEL,SAAS,IAAI,aAAa5C,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;QACvDd,SAAS,EAAE,EAAE,CAAC3B,MAAM,CAACkC,cAAc,EAAE,UAAU;MACjD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAEX,IAAID,MAAM,EAAE;QACV,OAAO,aAAa/C,SAAS,CAACR,OAAO,CAACmE,YAAY,CAACL,IAAI,EAAEP,MAAM,CAAC;MAClE;MAEA,OAAOO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOpD,MAAM;AACf,CAAC,CAACH,KAAK,CAAC6D,SAAS,CAAC;AAElBtE,OAAO,CAACE,OAAO,GAAGU,MAAM;AACxBA,MAAM,CAAC2D,YAAY,GAAG;EACpBvC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;EAC9BG,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}