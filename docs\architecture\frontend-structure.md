# 🎨 Struttura Frontend E-Procurement

Documentazione dettagliata dell'architettura e organizzazione del codice frontend.

## 📁 Struttura Directory

```
src/
├── components/                 # Componenti riutilizzabili
│   ├── generalizzazioni/      # Utility e helper globali
│   │   ├── apireq.jsx         # Client HTTP personalizzato
│   │   ├── const.js           # Costanti globali
│   │   └── utils.js           # Utility functions
│   ├── traduttore/            # Internazionalizzazione
│   │   └── const.js           # Costanti tradotte
│   └── ui/                    # Componenti UI base
├── common/                    # Componenti per ruoli specifici
│   ├── distributore/          # Pannello distributore
│   │   ├── gestioneClienti.jsx
│   │   └── dashboard.jsx
│   ├── chain/                 # Pannello chain
│   │   ├── gestioneClienti.jsx
│   │   └── dashboard.jsx
│   └── agente/                # Pannello agente
│       ├── gestionePDV.jsx
│       └── dashboard.jsx
├── aggiunta_dati/             # Form di inserimento
│   ├── aggiungiAnagrafica.jsx # Form principale anagrafica
│   └── aggiungiPDV.jsx        # Form PDV
├── css/                       # Stili personalizzati
│   ├── App.css               # Stili globali
│   ├── components.css        # Stili componenti
│   └── responsive.css        # Media queries
├── img/                       # Asset immagini
├── utils/                     # Utility functions
└── config/                    # Configurazioni
    ├── api.js                # Configurazione API
    └── constants.js          # Costanti applicazione
```

## 🔧 Componenti Principali

### **1. APIRequest (`components/generalizzazioni/apireq.jsx`)**

Client HTTP personalizzato che gestisce:
- **Configurazione automatica URL** (dev/prod)
- **Headers automatici** (auth, content-type)
- **Gestione errori globale**
- **Logging e debugging**

```javascript
// Utilizzo
const response = await APIRequest('GET', 'endpoint');
const data = await APIRequest('POST', 'endpoint', body);
```

**Caratteristiche**:
- ✅ Auto-configurazione ambiente
- ✅ Token JWT automatico
- ✅ Gestione errori 500 con redirect
- ✅ Logging dettagliato
- ✅ Timeout configurabile

### **2. Form Anagrafica (`aggiunta_dati/aggiungiAnagrafica.jsx`)**

Form completo per inserimento anagrafiche con:
- **Lookup P.IVA automatico** (VIES)
- **Validazione dinamica** (aziende vs persone)
- **Auto-compilazione campi**
- **Gestione errori specifica**

**Funzionalità**:
- ✅ Rilevamento P.IVA (11 cifre)
- ✅ Chiamata servizio VIES
- ✅ Auto-compilazione dati azienda
- ✅ Validazione intelligente cognome
- ✅ Gestione errori senza refresh

### **3. Gestione Clienti (`common/{role}/gestioneClienti.jsx`)**

Componenti per gestione anagrafiche esistenti:
- **Lista paginata** con filtri
- **Modale modifica** con validazione
- **Operazioni bulk** (export, delete)
- **Ricerca avanzata**

**Caratteristiche**:
- ✅ Rilevamento automatico aziende
- ✅ Validazione dinamica
- ✅ Gestione errori dettagliata
- ✅ UI responsive

## 🎨 Sistema di Styling

### **CSS Architecture**
```
css/
├── App.css              # Reset, variabili, layout globale
├── components.css       # Stili componenti specifici
├── responsive.css       # Media queries
└── themes/
    ├── light.css       # Tema chiaro
    └── dark.css        # Tema scuro (futuro)
```

### **CSS Variables**
```css
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}
```

### **Responsive Breakpoints**
```css
/* Mobile First */
@media (min-width: 576px) { /* Small */ }
@media (min-width: 768px) { /* Medium */ }
@media (min-width: 992px) { /* Large */ }
@media (min-width: 1200px) { /* Extra Large */ }
```

## 🔄 Gestione Stato

### **Redux Store Structure**
```javascript
store: {
  auth: {
    token: string,
    user: object,
    isAuthenticated: boolean
  },
  registry: {
    list: array,
    filters: object,
    pagination: object
  },
  ui: {
    loading: boolean,
    errors: array,
    notifications: array
  }
}
```

### **Actions Pattern**
```javascript
// Action Types
const FETCH_REGISTRY_REQUEST = 'FETCH_REGISTRY_REQUEST';
const FETCH_REGISTRY_SUCCESS = 'FETCH_REGISTRY_SUCCESS';
const FETCH_REGISTRY_FAILURE = 'FETCH_REGISTRY_FAILURE';

// Action Creators
const fetchRegistry = () => async (dispatch) => {
  dispatch({ type: FETCH_REGISTRY_REQUEST });
  try {
    const data = await APIRequest('GET', 'registry');
    dispatch({ type: FETCH_REGISTRY_SUCCESS, payload: data });
  } catch (error) {
    dispatch({ type: FETCH_REGISTRY_FAILURE, payload: error.message });
  }
};
```

## 🧩 Pattern di Sviluppo

### **1. Component Pattern**
```javascript
// Functional Component con Hooks
const MyComponent = ({ prop1, prop2 }) => {
  const [state, setState] = useState(initialState);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  const handleAction = useCallback(() => {
    // Event handlers
  }, [dependencies]);
  
  return (
    <div className="my-component">
      {/* JSX */}
    </div>
  );
};
```

### **2. Form Pattern**
```javascript
// React Final Form
<Form
  onSubmit={handleSubmit}
  initialValues={initialValues}
  validate={validate}
  render={({ handleSubmit, form, values }) => (
    <form onSubmit={handleSubmit}>
      <Field name="fieldName" render={({ input, meta }) => (
        <div>
          <input {...input} />
          {meta.error && meta.touched && <span>{meta.error}</span>}
        </div>
      )} />
    </form>
  )}
/>
```

### **3. Error Handling Pattern**
```javascript
try {
  const response = await APIRequest('POST', 'endpoint', data);
  // Success handling
} catch (error) {
  const errorStatus = error.response?.status;
  const errorData = error.response?.data;
  
  switch (errorStatus) {
    case 400:
      // Validation errors
      break;
    case 401:
      // Authentication errors
      break;
    case 500:
      // Server errors
      break;
    default:
      // Generic errors
  }
}
```

## 🔍 Convenzioni di Codice

### **Naming Conventions**
- **Components**: PascalCase (`MyComponent.jsx`)
- **Files**: camelCase (`myUtility.js`)
- **Variables**: camelCase (`myVariable`)
- **Constants**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **CSS Classes**: kebab-case (`my-class`)

### **File Organization**
- **One component per file**
- **Index files** per export multipli
- **Co-location** di file correlati
- **Separation of concerns**

### **Import Order**
```javascript
// 1. React imports
import React, { useState, useEffect } from 'react';

// 2. Third-party libraries
import axios from 'axios';
import { Toast } from 'primereact/toast';

// 3. Internal components
import { APIRequest } from '../components/generalizzazioni/apireq';

// 4. Utilities and constants
import { Costanti } from '../components/traduttore/const';

// 5. Styles
import './MyComponent.css';
```

## 🧪 Testing Strategy

### **Test Structure**
```
src/
├── __tests__/           # Test globali
├── components/
│   ├── Component.jsx
│   └── Component.test.js
└── utils/
    ├── utility.js
    └── utility.test.js
```

### **Test Types**
- **Unit Tests**: Componenti isolati
- **Integration Tests**: Flussi completi
- **E2E Tests**: Scenari utente reali

## 📱 Responsive Design

### **Mobile-First Approach**
```css
/* Base styles (mobile) */
.component {
  width: 100%;
  padding: 1rem;
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    width: 50%;
    padding: 2rem;
  }
}

/* Desktop and up */
@media (min-width: 1200px) {
  .component {
    width: 33.333%;
    padding: 3rem;
  }
}
```

### **Responsive Components**
- **Tables**: Horizontal scroll su mobile
- **Forms**: Layout colonne adattivo
- **Navigation**: Hamburger menu su mobile
- **Modals**: Full-screen su mobile

---

**Ultimo aggiornamento**: 16 Gennaio 2025
**Versione**: 1.0.0
