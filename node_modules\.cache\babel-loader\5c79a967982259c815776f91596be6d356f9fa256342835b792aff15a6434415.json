{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _utils = require(\"../utils\");\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n    secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || (0, _utils.getSecondaryColor)(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\nfunction getTwoToneColors() {\n  return (0, _objectSpread2.default)({}, twoToneColorPalette);\n}\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n    className = props.className,\n    onClick = props.onClick,\n    style = props.style,\n    primaryColor = props.primaryColor,\n    secondaryColor = props.secondaryColor,\n    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);\n  var colors = twoToneColorPalette;\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || (0, _utils.getSecondaryColor)(primaryColor)\n    };\n  }\n  (0, _utils.useInsertStyles)();\n  (0, _utils.warning)((0, _utils.isIconDefinition)(icon), \"icon should be icon definiton, but got \".concat(icon));\n  if (!(0, _utils.isIconDefinition)(icon)) {\n    return null;\n  }\n  var target = icon;\n  if (target && typeof target.icon === 'function') {\n    target = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n  return (0, _utils.generate)(target.icon, \"svg-\".concat(target.name), (0, _objectSpread2.default)({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps));\n};\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nvar _default = IconBase;\nexports.default = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_objectWithoutProperties2", "_objectSpread2", "_utils", "_excluded", "twoToneColorPalette", "primaryColor", "secondaryColor", "calculated", "setTwoToneColors", "_ref", "getSecondaryColor", "getTwoToneColors", "IconBase", "props", "icon", "className", "onClick", "style", "restProps", "colors", "useInsertStyles", "warning", "isIconDefinition", "concat", "target", "generate", "name", "width", "height", "fill", "displayName", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/lib/components/IconBase.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\n\nvar _utils = require(\"../utils\");\n\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\n\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n      secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || (0, _utils.getSecondaryColor)(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\n\nfunction getTwoToneColors() {\n  return (0, _objectSpread2.default)({}, twoToneColorPalette);\n}\n\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n      className = props.className,\n      onClick = props.onClick,\n      style = props.style,\n      primaryColor = props.primaryColor,\n      secondaryColor = props.secondaryColor,\n      restProps = (0, _objectWithoutProperties2.default)(props, _excluded);\n  var colors = twoToneColorPalette;\n\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || (0, _utils.getSecondaryColor)(primaryColor)\n    };\n  }\n\n  (0, _utils.useInsertStyles)();\n  (0, _utils.warning)((0, _utils.isIconDefinition)(icon), \"icon should be icon definiton, but got \".concat(icon));\n\n  if (!(0, _utils.isIconDefinition)(icon)) {\n    return null;\n  }\n\n  var target = icon;\n\n  if (target && typeof target.icon === 'function') {\n    target = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n\n  return (0, _utils.generate)(target.icon, \"svg-\".concat(target.name), (0, _objectSpread2.default)({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps));\n};\n\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nvar _default = IconBase;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,yBAAyB,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gDAAgD,CAAC,CAAC;AAEjH,IAAIO,cAAc,GAAGR,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE5F,IAAIQ,MAAM,GAAGR,OAAO,CAAC,UAAU,CAAC;AAEhC,IAAIS,SAAS,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,CAAC;AAC3F,IAAIC,mBAAmB,GAAG;EACxBC,YAAY,EAAE,MAAM;EACpBC,cAAc,EAAE,SAAS;EACzBC,UAAU,EAAE;AACd,CAAC;AAED,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAIJ,YAAY,GAAGI,IAAI,CAACJ,YAAY;IAChCC,cAAc,GAAGG,IAAI,CAACH,cAAc;EACxCF,mBAAmB,CAACC,YAAY,GAAGA,YAAY;EAC/CD,mBAAmB,CAACE,cAAc,GAAGA,cAAc,IAAI,CAAC,CAAC,EAAEJ,MAAM,CAACQ,iBAAiB,EAAEL,YAAY,CAAC;EAClGD,mBAAmB,CAACG,UAAU,GAAG,CAAC,CAACD,cAAc;AACnD;AAEA,SAASK,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,CAAC,CAAC,EAAEV,cAAc,CAACF,OAAO,EAAE,CAAC,CAAC,EAAEK,mBAAmB,CAAC;AAC7D;AAEA,IAAIQ,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACjBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBZ,YAAY,GAAGQ,KAAK,CAACR,YAAY;IACjCC,cAAc,GAAGO,KAAK,CAACP,cAAc;IACrCY,SAAS,GAAG,CAAC,CAAC,EAAElB,yBAAyB,CAACD,OAAO,EAAEc,KAAK,EAAEV,SAAS,CAAC;EACxE,IAAIgB,MAAM,GAAGf,mBAAmB;EAEhC,IAAIC,YAAY,EAAE;IAChBc,MAAM,GAAG;MACPd,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA,cAAc,IAAI,CAAC,CAAC,EAAEJ,MAAM,CAACQ,iBAAiB,EAAEL,YAAY;IAC9E,CAAC;EACH;EAEA,CAAC,CAAC,EAAEH,MAAM,CAACkB,eAAe,EAAE,CAAC;EAC7B,CAAC,CAAC,EAAElB,MAAM,CAACmB,OAAO,EAAE,CAAC,CAAC,EAAEnB,MAAM,CAACoB,gBAAgB,EAAER,IAAI,CAAC,EAAE,yCAAyC,CAACS,MAAM,CAACT,IAAI,CAAC,CAAC;EAE/G,IAAI,CAAC,CAAC,CAAC,EAAEZ,MAAM,CAACoB,gBAAgB,EAAER,IAAI,CAAC,EAAE;IACvC,OAAO,IAAI;EACb;EAEA,IAAIU,MAAM,GAAGV,IAAI;EAEjB,IAAIU,MAAM,IAAI,OAAOA,MAAM,CAACV,IAAI,KAAK,UAAU,EAAE;IAC/CU,MAAM,GAAG,CAAC,CAAC,EAAEvB,cAAc,CAACF,OAAO,EAAE,CAAC,CAAC,EAAEE,cAAc,CAACF,OAAO,EAAE,CAAC,CAAC,EAAEyB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MAChFV,IAAI,EAAEU,MAAM,CAACV,IAAI,CAACK,MAAM,CAACd,YAAY,EAAEc,MAAM,CAACb,cAAc;IAC9D,CAAC,CAAC;EACJ;EAEA,OAAO,CAAC,CAAC,EAAEJ,MAAM,CAACuB,QAAQ,EAAED,MAAM,CAACV,IAAI,EAAE,MAAM,CAACS,MAAM,CAACC,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAEzB,cAAc,CAACF,OAAO,EAAE;IAC/FgB,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBC,KAAK,EAAEA,KAAK;IACZ,WAAW,EAAEO,MAAM,CAACE,IAAI;IACxBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE;EACjB,CAAC,EAAEX,SAAS,CAAC,CAAC;AAChB,CAAC;AAEDN,QAAQ,CAACkB,WAAW,GAAG,WAAW;AAClClB,QAAQ,CAACD,gBAAgB,GAAGA,gBAAgB;AAC5CC,QAAQ,CAACJ,gBAAgB,GAAGA,gBAAgB;AAC5C,IAAIuB,QAAQ,GAAGnB,QAAQ;AACvBf,OAAO,CAACE,OAAO,GAAGgC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}