{"name": "stylus-lookup", "version": "5.0.1", "description": "Get the file associated with an imported/required Stylus partial", "main": "index.js", "files": ["bin/cli.js", "index.js"], "bin": {"stylus-lookup": "bin/cli.js"}, "scripts": {"lint": "xo", "fix": "xo --fix", "uvu": "uvu test -i fixtures", "test": "npm run lint && npm run uvu", "test:ci": "c8 npm run uvu"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-stylus-lookup.git"}, "keywords": ["stylus", "lookup", "dependency", "partial"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-stylus-lookup/issues"}, "homepage": "https://github.com/dependents/node-stylus-lookup", "engines": {"node": ">=14"}, "dependencies": {"commander": "^10.0.1"}, "devDependencies": {"c8": "^7.13.0", "mock-fs": "^5.2.0", "uvu": "^0.5.6", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}