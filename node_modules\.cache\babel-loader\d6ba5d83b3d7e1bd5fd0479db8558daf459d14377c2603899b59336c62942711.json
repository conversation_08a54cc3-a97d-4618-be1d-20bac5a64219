{"ast": null, "code": "import React from 'react';\nexport var ReactReduxContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\nexport default ReactReduxContext;", "map": {"version": 3, "names": ["React", "ReactReduxContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-redux/es/components/Context.js"], "sourcesContent": ["import React from 'react';\nexport var ReactReduxContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\n\nexport default ReactReduxContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAErE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,iBAAiB,CAACK,WAAW,GAAG,YAAY;AAC9C;AAEA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}