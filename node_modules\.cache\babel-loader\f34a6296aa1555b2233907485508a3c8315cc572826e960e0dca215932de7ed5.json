{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    var _classNames;\n    var prefixCls = _ref.prefixCls,\n      onExpand = _ref.onExpand,\n      record = _ref.record,\n      expanded = _ref.expanded,\n      expandable = _ref.expandable;\n    var iconPrefix = \"\".concat(prefixCls, \"-row-expand-icon\");\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: function onClick(e) {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(iconPrefix, \"-spaced\"), !expandable), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-expanded\"), expandable && expanded), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-collapsed\"), expandable && !expanded), _classNames)),\n      \"aria-label\": expanded ? locale.collapse : locale.expand\n    });\n  };\n}\nexport default renderExpandIcon;", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "renderExpandIcon", "locale", "expandIcon", "_ref", "_classNames", "prefixCls", "onExpand", "record", "expanded", "expandable", "iconPrefix", "concat", "createElement", "type", "onClick", "e", "stopPropagation", "className", "collapse", "expand"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/ExpandIcon.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\n\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    var _classNames;\n\n    var prefixCls = _ref.prefixCls,\n        onExpand = _ref.onExpand,\n        record = _ref.record,\n        expanded = _ref.expanded,\n        expandable = _ref.expandable;\n    var iconPrefix = \"\".concat(prefixCls, \"-row-expand-icon\");\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: function onClick(e) {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(iconPrefix, \"-spaced\"), !expandable), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-expanded\"), expandable && expanded), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-collapsed\"), expandable && !expanded), _classNames)),\n      \"aria-label\": expanded ? locale.collapse : locale.expand\n    });\n  };\n}\n\nexport default renderExpandIcon;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AAEnC,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChC,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;IAC/B,IAAIC,WAAW;IAEf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;MAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;MACxBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;MACpBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;MACxBC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAChC,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,kBAAkB,CAAC;IACzD,OAAO,aAAaP,KAAK,CAACc,aAAa,CAAC,QAAQ,EAAE;MAChDC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3BT,QAAQ,CAACC,MAAM,EAAEQ,CAAC,CAAC;QACnBA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB,CAAC;MACDC,SAAS,EAAElB,UAAU,CAACW,UAAU,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAEP,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,SAAS,CAAC,EAAE,CAACD,UAAU,CAAC,EAAEZ,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,WAAW,CAAC,EAAED,UAAU,IAAID,QAAQ,CAAC,EAAEX,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,YAAY,CAAC,EAAED,UAAU,IAAI,CAACD,QAAQ,CAAC,EAAEJ,WAAW,CAAC,CAAC;MACrU,YAAY,EAAEI,QAAQ,GAAGP,MAAM,CAACiB,QAAQ,GAAGjB,MAAM,CAACkB;IACpD,CAAC,CAAC;EACJ,CAAC;AACH;AAEA,eAAenB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}