{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default (function (stretch) {\n  var _React$useState = React.useState({\n      width: 0,\n      height: 0\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    targetSize = _React$useState2[0],\n    setTargetSize = _React$useState2[1];\n  function measureStretch(element) {\n    setTargetSize({\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    });\n  } // Merge stretch style\n\n  var style = React.useMemo(function () {\n    var sizeStyle = {};\n    if (stretch) {\n      var width = targetSize.width,\n        height = targetSize.height; // Stretch with target\n\n      if (stretch.indexOf('height') !== -1 && height) {\n        sizeStyle.height = height;\n      } else if (stretch.indexOf('minHeight') !== -1 && height) {\n        sizeStyle.minHeight = height;\n      }\n      if (stretch.indexOf('width') !== -1 && width) {\n        sizeStyle.width = width;\n      } else if (stretch.indexOf('minWidth') !== -1 && width) {\n        sizeStyle.minWidth = width;\n      }\n    }\n    return sizeStyle;\n  }, [stretch, targetSize]);\n  return [style, measureStretch];\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "stretch", "_React$useState", "useState", "width", "height", "_React$useState2", "targetSize", "setTargetSize", "measureStretch", "element", "offsetWidth", "offsetHeight", "style", "useMemo", "sizeStyle", "indexOf", "minHeight", "min<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-trigger/es/Popup/useStretchStyle.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default (function (stretch) {\n  var _React$useState = React.useState({\n    width: 0,\n    height: 0\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      targetSize = _React$useState2[0],\n      setTargetSize = _React$useState2[1];\n\n  function measureStretch(element) {\n    setTargetSize({\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    });\n  } // Merge stretch style\n\n\n  var style = React.useMemo(function () {\n    var sizeStyle = {};\n\n    if (stretch) {\n      var width = targetSize.width,\n          height = targetSize.height; // Stretch with target\n\n      if (stretch.indexOf('height') !== -1 && height) {\n        sizeStyle.height = height;\n      } else if (stretch.indexOf('minHeight') !== -1 && height) {\n        sizeStyle.minHeight = height;\n      }\n\n      if (stretch.indexOf('width') !== -1 && width) {\n        sizeStyle.width = width;\n      } else if (stretch.indexOf('minWidth') !== -1 && width) {\n        sizeStyle.minWidth = width;\n      }\n    }\n\n    return sizeStyle;\n  }, [stretch, targetSize]);\n  return [style, measureStretch];\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,gBAAgB,UAAUC,OAAO,EAAE;EACjC,IAAIC,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAAC;MACnCC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IACEC,gBAAgB,GAAGP,cAAc,CAACG,eAAe,EAAE,CAAC,CAAC;IACrDK,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,SAASG,cAAcA,CAACC,OAAO,EAAE;IAC/BF,aAAa,CAAC;MACZJ,KAAK,EAAEM,OAAO,CAACC,WAAW;MAC1BN,MAAM,EAAEK,OAAO,CAACE;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIC,KAAK,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY;IACpC,IAAIC,SAAS,GAAG,CAAC,CAAC;IAElB,IAAId,OAAO,EAAE;MACX,IAAIG,KAAK,GAAGG,UAAU,CAACH,KAAK;QACxBC,MAAM,GAAGE,UAAU,CAACF,MAAM,CAAC,CAAC;;MAEhC,IAAIJ,OAAO,CAACe,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAIX,MAAM,EAAE;QAC9CU,SAAS,CAACV,MAAM,GAAGA,MAAM;MAC3B,CAAC,MAAM,IAAIJ,OAAO,CAACe,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAIX,MAAM,EAAE;QACxDU,SAAS,CAACE,SAAS,GAAGZ,MAAM;MAC9B;MAEA,IAAIJ,OAAO,CAACe,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIZ,KAAK,EAAE;QAC5CW,SAAS,CAACX,KAAK,GAAGA,KAAK;MACzB,CAAC,MAAM,IAAIH,OAAO,CAACe,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIZ,KAAK,EAAE;QACtDW,SAAS,CAACG,QAAQ,GAAGd,KAAK;MAC5B;IACF;IAEA,OAAOW,SAAS;EAClB,CAAC,EAAE,CAACd,OAAO,EAAEM,UAAU,CAAC,CAAC;EACzB,OAAO,CAACM,KAAK,EAAEJ,cAAc,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}