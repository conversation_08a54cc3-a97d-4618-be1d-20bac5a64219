{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"colSpan\", \"rowSpan\", \"style\", \"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport { supportRef } from \"rc-util/es/ref\";\nimport { getPathValue, validateValue } from '../utils/valueUtil';\nimport StickyContext from '../context/StickyContext';\nimport HoverContext from '../context/HoverContext';\nimport warning from \"rc-util/es/warning\";\nimport PerfContext from '../context/PerfContext';\n/** Check if cell is in hover range */\n\nfunction inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {\n  var cellEndRow = cellStartRow + cellRowSpan - 1;\n  return cellStartRow <= endRow && cellEndRow >= startRow;\n}\nfunction isRenderCell(data) {\n  return data && _typeof(data) === 'object' && !Array.isArray(data) && ! /*#__PURE__*/React.isValidElement(data);\n}\nfunction isRefComponent(component) {\n  // String tag component also support ref\n  if (typeof component === 'string') {\n    return true;\n  }\n  return supportRef(component);\n}\nvar getTitleFromCellRenderChildren = function getTitleFromCellRenderChildren(_ref) {\n  var ellipsis = _ref.ellipsis,\n    rowType = _ref.rowType,\n    children = _ref.children;\n  var title;\n  var ellipsisConfig = ellipsis === true ? {\n    showTitle: true\n  } : ellipsis;\n  if (ellipsisConfig && (ellipsisConfig.showTitle || rowType === 'header')) {\n    if (typeof children === 'string' || typeof children === 'number') {\n      title = children.toString();\n    } else if (/*#__PURE__*/React.isValidElement(children) && typeof children.props.children === 'string') {\n      title = children.props.children;\n    }\n  }\n  return title;\n};\nfunction Cell(_ref2, ref) {\n  var _ref4, _ref5, _classNames;\n  var prefixCls = _ref2.prefixCls,\n    className = _ref2.className,\n    record = _ref2.record,\n    index = _ref2.index,\n    renderIndex = _ref2.renderIndex,\n    dataIndex = _ref2.dataIndex,\n    render = _ref2.render,\n    children = _ref2.children,\n    _ref2$component = _ref2.component,\n    Component = _ref2$component === void 0 ? 'td' : _ref2$component,\n    colSpan = _ref2.colSpan,\n    rowSpan = _ref2.rowSpan,\n    fixLeft = _ref2.fixLeft,\n    fixRight = _ref2.fixRight,\n    firstFixLeft = _ref2.firstFixLeft,\n    lastFixLeft = _ref2.lastFixLeft,\n    firstFixRight = _ref2.firstFixRight,\n    lastFixRight = _ref2.lastFixRight,\n    appendNode = _ref2.appendNode,\n    _ref2$additionalProps = _ref2.additionalProps,\n    additionalProps = _ref2$additionalProps === void 0 ? {} : _ref2$additionalProps,\n    ellipsis = _ref2.ellipsis,\n    align = _ref2.align,\n    rowType = _ref2.rowType,\n    isSticky = _ref2.isSticky,\n    hovering = _ref2.hovering,\n    onHover = _ref2.onHover;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var perfRecord = React.useContext(PerfContext);\n  var supportSticky = React.useContext(StickyContext); // ==================== Child Node ====================\n\n  var _React$useMemo = React.useMemo(function () {\n      if (validateValue(children)) {\n        return [children];\n      }\n      var value = getPathValue(record, dataIndex); // Customize render node\n\n      // Customize render node\n      var returnChildNode = value;\n      var returnCellProps = undefined;\n      if (render) {\n        var renderData = render(value, record, renderIndex);\n        if (isRenderCell(renderData)) {\n          if (process.env.NODE_ENV !== 'production') {\n            warning(false, '`columns.render` return cell props is deprecated with perf issue, please use `onCell` instead.');\n          }\n          returnChildNode = renderData.children;\n          returnCellProps = renderData.props;\n          perfRecord.renderWithProps = true;\n        } else {\n          returnChildNode = renderData;\n        }\n      }\n      return [returnChildNode, returnCellProps];\n    }, [/* eslint-disable react-hooks/exhaustive-deps */\n    // Always re-render if `renderWithProps`\n    perfRecord.renderWithProps ? Math.random() : 0, /* eslint-enable */\n    children, dataIndex, perfRecord, record, render, renderIndex]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    childNode = _React$useMemo2[0],\n    legacyCellProps = _React$useMemo2[1];\n  var mergedChildNode = childNode; // Not crash if final `childNode` is not validate ReactNode\n\n  if (_typeof(mergedChildNode) === 'object' && !Array.isArray(mergedChildNode) && ! /*#__PURE__*/React.isValidElement(mergedChildNode)) {\n    mergedChildNode = null;\n  }\n  if (ellipsis && (lastFixLeft || firstFixRight)) {\n    mergedChildNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(cellPrefixCls, \"-content\")\n    }, mergedChildNode);\n  }\n  var _ref3 = legacyCellProps || {},\n    cellColSpan = _ref3.colSpan,\n    cellRowSpan = _ref3.rowSpan,\n    cellStyle = _ref3.style,\n    cellClassName = _ref3.className,\n    restCellProps = _objectWithoutProperties(_ref3, _excluded);\n  var mergedColSpan = (_ref4 = cellColSpan !== undefined ? cellColSpan : colSpan) !== null && _ref4 !== void 0 ? _ref4 : 1;\n  var mergedRowSpan = (_ref5 = cellRowSpan !== undefined ? cellRowSpan : rowSpan) !== null && _ref5 !== void 0 ? _ref5 : 1;\n  if (mergedColSpan === 0 || mergedRowSpan === 0) {\n    return null;\n  } // ====================== Fixed =======================\n\n  var fixedStyle = {};\n  var isFixLeft = typeof fixLeft === 'number' && supportSticky;\n  var isFixRight = typeof fixRight === 'number' && supportSticky;\n  if (isFixLeft) {\n    fixedStyle.position = 'sticky';\n    fixedStyle.left = fixLeft;\n  }\n  if (isFixRight) {\n    fixedStyle.position = 'sticky';\n    fixedStyle.right = fixRight;\n  } // ====================== Align =======================\n\n  var alignStyle = {};\n  if (align) {\n    alignStyle.textAlign = align;\n  } // ====================== Hover =======================\n\n  var onMouseEnter = function onMouseEnter(event) {\n    var _additionalProps$onMo;\n    if (record) {\n      onHover(index, index + mergedRowSpan - 1);\n    }\n    additionalProps === null || additionalProps === void 0 ? void 0 : (_additionalProps$onMo = additionalProps.onMouseEnter) === null || _additionalProps$onMo === void 0 ? void 0 : _additionalProps$onMo.call(additionalProps, event);\n  };\n  var onMouseLeave = function onMouseLeave(event) {\n    var _additionalProps$onMo2;\n    if (record) {\n      onHover(-1, -1);\n    }\n    additionalProps === null || additionalProps === void 0 ? void 0 : (_additionalProps$onMo2 = additionalProps.onMouseLeave) === null || _additionalProps$onMo2 === void 0 ? void 0 : _additionalProps$onMo2.call(additionalProps, event);\n  }; // ====================== Render ======================\n\n  var title = getTitleFromCellRenderChildren({\n    rowType: rowType,\n    ellipsis: ellipsis,\n    children: childNode\n  });\n  var componentProps = _objectSpread(_objectSpread(_objectSpread({\n    title: title\n  }, restCellProps), additionalProps), {}, {\n    colSpan: mergedColSpan !== 1 ? mergedColSpan : null,\n    rowSpan: mergedRowSpan !== 1 ? mergedRowSpan : null,\n    className: classNames(cellPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left\"), isFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left-first\"), firstFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left-last\"), lastFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right\"), isFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right-first\"), firstFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right-last\"), lastFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-ellipsis\"), ellipsis), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-with-append\"), appendNode), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-sticky\"), (isFixLeft || isFixRight) && isSticky && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-row-hover\"), !legacyCellProps && hovering), _classNames), additionalProps.className, cellClassName),\n    style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, additionalProps.style), alignStyle), fixedStyle), cellStyle),\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    ref: isRefComponent(Component) ? ref : null\n  });\n  return /*#__PURE__*/React.createElement(Component, componentProps, appendNode, mergedChildNode);\n}\nvar RefCell = /*#__PURE__*/React.forwardRef(Cell);\nRefCell.displayName = 'Cell';\nvar comparePropList = ['expanded', 'className', 'hovering'];\nvar MemoCell = /*#__PURE__*/React.memo(RefCell, function (prev, next) {\n  if (next.shouldCellUpdate) {\n    return (\n      // Additional handle of expanded logic\n      comparePropList.every(function (propName) {\n        return prev[propName] === next[propName];\n      }) &&\n      // User control update logic\n      !next.shouldCellUpdate(next.record, prev.record)\n    );\n  }\n  return shallowEqual(prev, next);\n});\n/** Inject hover data here, we still wish MemoCell keep simple `shouldCellUpdate` logic */\n\nvar WrappedCell = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(HoverContext),\n    onHover = _React$useContext.onHover,\n    startRow = _React$useContext.startRow,\n    endRow = _React$useContext.endRow;\n  var index = props.index,\n    _props$additionalProp = props.additionalProps,\n    additionalProps = _props$additionalProp === void 0 ? {} : _props$additionalProp,\n    colSpan = props.colSpan,\n    rowSpan = props.rowSpan;\n  var cellColSpan = additionalProps.colSpan,\n    cellRowSpan = additionalProps.rowSpan;\n  var mergedColSpan = colSpan !== null && colSpan !== void 0 ? colSpan : cellColSpan;\n  var mergedRowSpan = rowSpan !== null && rowSpan !== void 0 ? rowSpan : cellRowSpan;\n  var hovering = inHoverRange(index, mergedRowSpan || 1, startRow, endRow);\n  return /*#__PURE__*/React.createElement(MemoCell, _extends({}, props, {\n    colSpan: mergedColSpan,\n    rowSpan: mergedRowSpan,\n    hovering: hovering,\n    ref: ref,\n    onHover: onHover\n  }));\n});\nWrappedCell.displayName = 'WrappedCell';\nexport default WrappedCell;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_objectWithoutProperties", "_slicedToArray", "_typeof", "_excluded", "React", "classNames", "shallowEqual", "supportRef", "getPathValue", "validate<PERSON><PERSON>ue", "StickyContext", "HoverContext", "warning", "PerfContext", "inHoverRange", "cellStartRow", "cellRowSpan", "startRow", "endRow", "cellEndRow", "isRenderCell", "data", "Array", "isArray", "isValidElement", "isRefComponent", "component", "getTitleFromCellRenderChildren", "_ref", "ellipsis", "rowType", "children", "title", "ellipsisConfig", "showTitle", "toString", "props", "Cell", "_ref2", "ref", "_ref4", "_ref5", "_classNames", "prefixCls", "className", "record", "index", "renderIndex", "dataIndex", "render", "_ref2$component", "Component", "colSpan", "rowSpan", "fixLeft", "fixRight", "firstFixLeft", "lastFixLeft", "firstFixRight", "lastFixRight", "appendNode", "_ref2$additionalProps", "additionalProps", "align", "isSticky", "hovering", "onHover", "cellPrefixCls", "concat", "perfRecord", "useContext", "supportSticky", "_React$useMemo", "useMemo", "value", "returnChildNode", "returnCellProps", "undefined", "renderData", "process", "env", "NODE_ENV", "renderWithProps", "Math", "random", "_React$useMemo2", "childNode", "legacyCellProps", "mergedChildNode", "createElement", "_ref3", "cellColSpan", "cellStyle", "style", "cellClassName", "restCellProps", "mergedColSpan", "mergedRowSpan", "fixedStyle", "isFixLeft", "isFixRight", "position", "left", "right", "alignStyle", "textAlign", "onMouseEnter", "event", "_additionalProps$onMo", "call", "onMouseLeave", "_additionalProps$onMo2", "componentProps", "Ref<PERSON>ell", "forwardRef", "displayName", "comparePropList", "MemoCell", "memo", "prev", "next", "shouldCellUpdate", "every", "propName", "WrappedCell", "_React$useContext", "_props$additionalProp"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Cell/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"colSpan\", \"rowSpan\", \"style\", \"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport { supportRef } from \"rc-util/es/ref\";\nimport { getPathValue, validateValue } from '../utils/valueUtil';\nimport StickyContext from '../context/StickyContext';\nimport HoverContext from '../context/HoverContext';\nimport warning from \"rc-util/es/warning\";\nimport PerfContext from '../context/PerfContext';\n/** Check if cell is in hover range */\n\nfunction inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {\n  var cellEndRow = cellStartRow + cellRowSpan - 1;\n  return cellStartRow <= endRow && cellEndRow >= startRow;\n}\n\nfunction isRenderCell(data) {\n  return data && _typeof(data) === 'object' && !Array.isArray(data) && ! /*#__PURE__*/React.isValidElement(data);\n}\n\nfunction isRefComponent(component) {\n  // String tag component also support ref\n  if (typeof component === 'string') {\n    return true;\n  }\n\n  return supportRef(component);\n}\n\nvar getTitleFromCellRenderChildren = function getTitleFromCellRenderChildren(_ref) {\n  var ellipsis = _ref.ellipsis,\n      rowType = _ref.rowType,\n      children = _ref.children;\n  var title;\n  var ellipsisConfig = ellipsis === true ? {\n    showTitle: true\n  } : ellipsis;\n\n  if (ellipsisConfig && (ellipsisConfig.showTitle || rowType === 'header')) {\n    if (typeof children === 'string' || typeof children === 'number') {\n      title = children.toString();\n    } else if ( /*#__PURE__*/React.isValidElement(children) && typeof children.props.children === 'string') {\n      title = children.props.children;\n    }\n  }\n\n  return title;\n};\n\nfunction Cell(_ref2, ref) {\n  var _ref4, _ref5, _classNames;\n\n  var prefixCls = _ref2.prefixCls,\n      className = _ref2.className,\n      record = _ref2.record,\n      index = _ref2.index,\n      renderIndex = _ref2.renderIndex,\n      dataIndex = _ref2.dataIndex,\n      render = _ref2.render,\n      children = _ref2.children,\n      _ref2$component = _ref2.component,\n      Component = _ref2$component === void 0 ? 'td' : _ref2$component,\n      colSpan = _ref2.colSpan,\n      rowSpan = _ref2.rowSpan,\n      fixLeft = _ref2.fixLeft,\n      fixRight = _ref2.fixRight,\n      firstFixLeft = _ref2.firstFixLeft,\n      lastFixLeft = _ref2.lastFixLeft,\n      firstFixRight = _ref2.firstFixRight,\n      lastFixRight = _ref2.lastFixRight,\n      appendNode = _ref2.appendNode,\n      _ref2$additionalProps = _ref2.additionalProps,\n      additionalProps = _ref2$additionalProps === void 0 ? {} : _ref2$additionalProps,\n      ellipsis = _ref2.ellipsis,\n      align = _ref2.align,\n      rowType = _ref2.rowType,\n      isSticky = _ref2.isSticky,\n      hovering = _ref2.hovering,\n      onHover = _ref2.onHover;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var perfRecord = React.useContext(PerfContext);\n  var supportSticky = React.useContext(StickyContext); // ==================== Child Node ====================\n\n  var _React$useMemo = React.useMemo(function () {\n    if (validateValue(children)) {\n      return [children];\n    }\n\n    var value = getPathValue(record, dataIndex); // Customize render node\n\n    // Customize render node\n    var returnChildNode = value;\n    var returnCellProps = undefined;\n\n    if (render) {\n      var renderData = render(value, record, renderIndex);\n\n      if (isRenderCell(renderData)) {\n        if (process.env.NODE_ENV !== 'production') {\n          warning(false, '`columns.render` return cell props is deprecated with perf issue, please use `onCell` instead.');\n        }\n\n        returnChildNode = renderData.children;\n        returnCellProps = renderData.props;\n        perfRecord.renderWithProps = true;\n      } else {\n        returnChildNode = renderData;\n      }\n    }\n\n    return [returnChildNode, returnCellProps];\n  }, [\n  /* eslint-disable react-hooks/exhaustive-deps */\n  // Always re-render if `renderWithProps`\n  perfRecord.renderWithProps ? Math.random() : 0,\n  /* eslint-enable */\n  children, dataIndex, perfRecord, record, render, renderIndex]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      childNode = _React$useMemo2[0],\n      legacyCellProps = _React$useMemo2[1];\n\n  var mergedChildNode = childNode; // Not crash if final `childNode` is not validate ReactNode\n\n  if (_typeof(mergedChildNode) === 'object' && !Array.isArray(mergedChildNode) && ! /*#__PURE__*/React.isValidElement(mergedChildNode)) {\n    mergedChildNode = null;\n  }\n\n  if (ellipsis && (lastFixLeft || firstFixRight)) {\n    mergedChildNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(cellPrefixCls, \"-content\")\n    }, mergedChildNode);\n  }\n\n  var _ref3 = legacyCellProps || {},\n      cellColSpan = _ref3.colSpan,\n      cellRowSpan = _ref3.rowSpan,\n      cellStyle = _ref3.style,\n      cellClassName = _ref3.className,\n      restCellProps = _objectWithoutProperties(_ref3, _excluded);\n\n  var mergedColSpan = (_ref4 = cellColSpan !== undefined ? cellColSpan : colSpan) !== null && _ref4 !== void 0 ? _ref4 : 1;\n  var mergedRowSpan = (_ref5 = cellRowSpan !== undefined ? cellRowSpan : rowSpan) !== null && _ref5 !== void 0 ? _ref5 : 1;\n\n  if (mergedColSpan === 0 || mergedRowSpan === 0) {\n    return null;\n  } // ====================== Fixed =======================\n\n\n  var fixedStyle = {};\n  var isFixLeft = typeof fixLeft === 'number' && supportSticky;\n  var isFixRight = typeof fixRight === 'number' && supportSticky;\n\n  if (isFixLeft) {\n    fixedStyle.position = 'sticky';\n    fixedStyle.left = fixLeft;\n  }\n\n  if (isFixRight) {\n    fixedStyle.position = 'sticky';\n    fixedStyle.right = fixRight;\n  } // ====================== Align =======================\n\n\n  var alignStyle = {};\n\n  if (align) {\n    alignStyle.textAlign = align;\n  } // ====================== Hover =======================\n\n\n  var onMouseEnter = function onMouseEnter(event) {\n    var _additionalProps$onMo;\n\n    if (record) {\n      onHover(index, index + mergedRowSpan - 1);\n    }\n\n    additionalProps === null || additionalProps === void 0 ? void 0 : (_additionalProps$onMo = additionalProps.onMouseEnter) === null || _additionalProps$onMo === void 0 ? void 0 : _additionalProps$onMo.call(additionalProps, event);\n  };\n\n  var onMouseLeave = function onMouseLeave(event) {\n    var _additionalProps$onMo2;\n\n    if (record) {\n      onHover(-1, -1);\n    }\n\n    additionalProps === null || additionalProps === void 0 ? void 0 : (_additionalProps$onMo2 = additionalProps.onMouseLeave) === null || _additionalProps$onMo2 === void 0 ? void 0 : _additionalProps$onMo2.call(additionalProps, event);\n  }; // ====================== Render ======================\n\n\n  var title = getTitleFromCellRenderChildren({\n    rowType: rowType,\n    ellipsis: ellipsis,\n    children: childNode\n  });\n\n  var componentProps = _objectSpread(_objectSpread(_objectSpread({\n    title: title\n  }, restCellProps), additionalProps), {}, {\n    colSpan: mergedColSpan !== 1 ? mergedColSpan : null,\n    rowSpan: mergedRowSpan !== 1 ? mergedRowSpan : null,\n    className: classNames(cellPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left\"), isFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left-first\"), firstFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-left-last\"), lastFixLeft && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right\"), isFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right-first\"), firstFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-right-last\"), lastFixRight && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-ellipsis\"), ellipsis), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-with-append\"), appendNode), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-fix-sticky\"), (isFixLeft || isFixRight) && isSticky && supportSticky), _defineProperty(_classNames, \"\".concat(cellPrefixCls, \"-row-hover\"), !legacyCellProps && hovering), _classNames), additionalProps.className, cellClassName),\n    style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, additionalProps.style), alignStyle), fixedStyle), cellStyle),\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    ref: isRefComponent(Component) ? ref : null\n  });\n\n  return /*#__PURE__*/React.createElement(Component, componentProps, appendNode, mergedChildNode);\n}\n\nvar RefCell = /*#__PURE__*/React.forwardRef(Cell);\nRefCell.displayName = 'Cell';\nvar comparePropList = ['expanded', 'className', 'hovering'];\nvar MemoCell = /*#__PURE__*/React.memo(RefCell, function (prev, next) {\n  if (next.shouldCellUpdate) {\n    return (// Additional handle of expanded logic\n      comparePropList.every(function (propName) {\n        return prev[propName] === next[propName];\n      }) && // User control update logic\n      !next.shouldCellUpdate(next.record, prev.record)\n    );\n  }\n\n  return shallowEqual(prev, next);\n});\n/** Inject hover data here, we still wish MemoCell keep simple `shouldCellUpdate` logic */\n\nvar WrappedCell = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(HoverContext),\n      onHover = _React$useContext.onHover,\n      startRow = _React$useContext.startRow,\n      endRow = _React$useContext.endRow;\n\n  var index = props.index,\n      _props$additionalProp = props.additionalProps,\n      additionalProps = _props$additionalProp === void 0 ? {} : _props$additionalProp,\n      colSpan = props.colSpan,\n      rowSpan = props.rowSpan;\n  var cellColSpan = additionalProps.colSpan,\n      cellRowSpan = additionalProps.rowSpan;\n  var mergedColSpan = colSpan !== null && colSpan !== void 0 ? colSpan : cellColSpan;\n  var mergedRowSpan = rowSpan !== null && rowSpan !== void 0 ? rowSpan : cellRowSpan;\n  var hovering = inHoverRange(index, mergedRowSpan || 1, startRow, endRow);\n  return /*#__PURE__*/React.createElement(MemoCell, _extends({}, props, {\n    colSpan: mergedColSpan,\n    rowSpan: mergedRowSpan,\n    hovering: hovering,\n    ref: ref,\n    onHover: onHover\n  }));\n});\nWrappedCell.displayName = 'WrappedCell';\nexport default WrappedCell;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,cAAc;AACvC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,YAAY,EAAEC,aAAa,QAAQ,oBAAoB;AAChE,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,WAAW,MAAM,wBAAwB;AAChD;;AAEA,SAASC,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACjE,IAAIC,UAAU,GAAGJ,YAAY,GAAGC,WAAW,GAAG,CAAC;EAC/C,OAAOD,YAAY,IAAIG,MAAM,IAAIC,UAAU,IAAIF,QAAQ;AACzD;AAEA,SAASG,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,IAAInB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,EAAE,aAAajB,KAAK,CAACoB,cAAc,CAACH,IAAI,CAAC;AAChH;AAEA,SAASI,cAAcA,CAACC,SAAS,EAAE;EACjC;EACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAO,IAAI;EACb;EAEA,OAAOnB,UAAU,CAACmB,SAAS,CAAC;AAC9B;AAEA,IAAIC,8BAA8B,GAAG,SAASA,8BAA8BA,CAACC,IAAI,EAAE;EACjF,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,KAAK;EACT,IAAIC,cAAc,GAAGJ,QAAQ,KAAK,IAAI,GAAG;IACvCK,SAAS,EAAE;EACb,CAAC,GAAGL,QAAQ;EAEZ,IAAII,cAAc,KAAKA,cAAc,CAACC,SAAS,IAAIJ,OAAO,KAAK,QAAQ,CAAC,EAAE;IACxE,IAAI,OAAOC,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChEC,KAAK,GAAGD,QAAQ,CAACI,QAAQ,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAK,aAAa/B,KAAK,CAACoB,cAAc,CAACO,QAAQ,CAAC,IAAI,OAAOA,QAAQ,CAACK,KAAK,CAACL,QAAQ,KAAK,QAAQ,EAAE;MACtGC,KAAK,GAAGD,QAAQ,CAACK,KAAK,CAACL,QAAQ;IACjC;EACF;EAEA,OAAOC,KAAK;AACd,CAAC;AAED,SAASK,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxB,IAAIC,KAAK,EAAEC,KAAK,EAAEC,WAAW;EAE7B,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBlB,QAAQ,GAAGO,KAAK,CAACP,QAAQ;IACzBmB,eAAe,GAAGZ,KAAK,CAACZ,SAAS;IACjCyB,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC/DE,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,OAAO,GAAGf,KAAK,CAACe,OAAO;IACvBC,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,YAAY,GAAGlB,KAAK,CAACkB,YAAY;IACjCC,WAAW,GAAGnB,KAAK,CAACmB,WAAW;IAC/BC,aAAa,GAAGpB,KAAK,CAACoB,aAAa;IACnCC,YAAY,GAAGrB,KAAK,CAACqB,YAAY;IACjCC,UAAU,GAAGtB,KAAK,CAACsB,UAAU;IAC7BC,qBAAqB,GAAGvB,KAAK,CAACwB,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;IAC/EhC,QAAQ,GAAGS,KAAK,CAACT,QAAQ;IACzBkC,KAAK,GAAGzB,KAAK,CAACyB,KAAK;IACnBjC,OAAO,GAAGQ,KAAK,CAACR,OAAO;IACvBkC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IACzBC,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;IACzBC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;EAC3B,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAI0B,UAAU,GAAGjE,KAAK,CAACkE,UAAU,CAACzD,WAAW,CAAC;EAC9C,IAAI0D,aAAa,GAAGnE,KAAK,CAACkE,UAAU,CAAC5D,aAAa,CAAC,CAAC,CAAC;;EAErD,IAAI8D,cAAc,GAAGpE,KAAK,CAACqE,OAAO,CAAC,YAAY;MAC7C,IAAIhE,aAAa,CAACsB,QAAQ,CAAC,EAAE;QAC3B,OAAO,CAACA,QAAQ,CAAC;MACnB;MAEA,IAAI2C,KAAK,GAAGlE,YAAY,CAACqC,MAAM,EAAEG,SAAS,CAAC,CAAC,CAAC;;MAE7C;MACA,IAAI2B,eAAe,GAAGD,KAAK;MAC3B,IAAIE,eAAe,GAAGC,SAAS;MAE/B,IAAI5B,MAAM,EAAE;QACV,IAAI6B,UAAU,GAAG7B,MAAM,CAACyB,KAAK,EAAE7B,MAAM,EAAEE,WAAW,CAAC;QAEnD,IAAI3B,YAAY,CAAC0D,UAAU,CAAC,EAAE;UAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCrE,OAAO,CAAC,KAAK,EAAE,gGAAgG,CAAC;UAClH;UAEA+D,eAAe,GAAGG,UAAU,CAAC/C,QAAQ;UACrC6C,eAAe,GAAGE,UAAU,CAAC1C,KAAK;UAClCiC,UAAU,CAACa,eAAe,GAAG,IAAI;QACnC,CAAC,MAAM;UACLP,eAAe,GAAGG,UAAU;QAC9B;MACF;MAEA,OAAO,CAACH,eAAe,EAAEC,eAAe,CAAC;IAC3C,CAAC,EAAE,CACH;IACA;IACAP,UAAU,CAACa,eAAe,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,EAC9C;IACArD,QAAQ,EAAEiB,SAAS,EAAEqB,UAAU,EAAExB,MAAM,EAAEI,MAAM,EAAEF,WAAW,CAAC,CAAC;IAC1DsC,eAAe,GAAGpF,cAAc,CAACuE,cAAc,EAAE,CAAC,CAAC;IACnDc,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC9BE,eAAe,GAAGF,eAAe,CAAC,CAAC,CAAC;EAExC,IAAIG,eAAe,GAAGF,SAAS,CAAC,CAAC;;EAEjC,IAAIpF,OAAO,CAACsF,eAAe,CAAC,KAAK,QAAQ,IAAI,CAAClE,KAAK,CAACC,OAAO,CAACiE,eAAe,CAAC,IAAI,EAAE,aAAapF,KAAK,CAACoB,cAAc,CAACgE,eAAe,CAAC,EAAE;IACpIA,eAAe,GAAG,IAAI;EACxB;EAEA,IAAI3D,QAAQ,KAAK4B,WAAW,IAAIC,aAAa,CAAC,EAAE;IAC9C8B,eAAe,GAAG,aAAapF,KAAK,CAACqF,aAAa,CAAC,MAAM,EAAE;MACzD7C,SAAS,EAAE,EAAE,CAACwB,MAAM,CAACD,aAAa,EAAE,UAAU;IAChD,CAAC,EAAEqB,eAAe,CAAC;EACrB;EAEA,IAAIE,KAAK,GAAGH,eAAe,IAAI,CAAC,CAAC;IAC7BI,WAAW,GAAGD,KAAK,CAACtC,OAAO;IAC3BpC,WAAW,GAAG0E,KAAK,CAACrC,OAAO;IAC3BuC,SAAS,GAAGF,KAAK,CAACG,KAAK;IACvBC,aAAa,GAAGJ,KAAK,CAAC9C,SAAS;IAC/BmD,aAAa,GAAG/F,wBAAwB,CAAC0F,KAAK,EAAEvF,SAAS,CAAC;EAE9D,IAAI6F,aAAa,GAAG,CAACxD,KAAK,GAAGmD,WAAW,KAAKd,SAAS,GAAGc,WAAW,GAAGvC,OAAO,MAAM,IAAI,IAAIZ,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EACxH,IAAIyD,aAAa,GAAG,CAACxD,KAAK,GAAGzB,WAAW,KAAK6D,SAAS,GAAG7D,WAAW,GAAGqC,OAAO,MAAM,IAAI,IAAIZ,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EAExH,IAAIuD,aAAa,KAAK,CAAC,IAAIC,aAAa,KAAK,CAAC,EAAE;IAC9C,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,SAAS,GAAG,OAAO7C,OAAO,KAAK,QAAQ,IAAIiB,aAAa;EAC5D,IAAI6B,UAAU,GAAG,OAAO7C,QAAQ,KAAK,QAAQ,IAAIgB,aAAa;EAE9D,IAAI4B,SAAS,EAAE;IACbD,UAAU,CAACG,QAAQ,GAAG,QAAQ;IAC9BH,UAAU,CAACI,IAAI,GAAGhD,OAAO;EAC3B;EAEA,IAAI8C,UAAU,EAAE;IACdF,UAAU,CAACG,QAAQ,GAAG,QAAQ;IAC9BH,UAAU,CAACK,KAAK,GAAGhD,QAAQ;EAC7B,CAAC,CAAC;;EAGF,IAAIiD,UAAU,GAAG,CAAC,CAAC;EAEnB,IAAIzC,KAAK,EAAE;IACTyC,UAAU,CAACC,SAAS,GAAG1C,KAAK;EAC9B,CAAC,CAAC;;EAGF,IAAI2C,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9C,IAAIC,qBAAqB;IAEzB,IAAI/D,MAAM,EAAE;MACVqB,OAAO,CAACpB,KAAK,EAAEA,KAAK,GAAGmD,aAAa,GAAG,CAAC,CAAC;IAC3C;IAEAnC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC8C,qBAAqB,GAAG9C,eAAe,CAAC4C,YAAY,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,IAAI,CAAC/C,eAAe,EAAE6C,KAAK,CAAC;EACrO,CAAC;EAED,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACH,KAAK,EAAE;IAC9C,IAAII,sBAAsB;IAE1B,IAAIlE,MAAM,EAAE;MACVqB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;IAEAJ,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACiD,sBAAsB,GAAGjD,eAAe,CAACgD,YAAY,MAAM,IAAI,IAAIC,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACF,IAAI,CAAC/C,eAAe,EAAE6C,KAAK,CAAC;EACxO,CAAC,CAAC,CAAC;;EAGH,IAAI3E,KAAK,GAAGL,8BAA8B,CAAC;IACzCG,OAAO,EAAEA,OAAO;IAChBD,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEuD;EACZ,CAAC,CAAC;EAEF,IAAI0B,cAAc,GAAGjH,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;IAC7DiC,KAAK,EAAEA;EACT,CAAC,EAAE+D,aAAa,CAAC,EAAEjC,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;IACvCV,OAAO,EAAE4C,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAG,IAAI;IACnD3C,OAAO,EAAE4C,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAG,IAAI;IACnDrD,SAAS,EAAEvC,UAAU,CAAC8D,aAAa,EAAEvB,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAE5C,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEgC,SAAS,IAAI5B,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,iBAAiB,CAAC,EAAEX,YAAY,IAAIe,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,gBAAgB,CAAC,EAAEV,WAAW,IAAIc,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,YAAY,CAAC,EAAEiC,UAAU,IAAI7B,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,kBAAkB,CAAC,EAAET,aAAa,IAAIa,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,iBAAiB,CAAC,EAAER,YAAY,IAAIY,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEtC,QAAQ,CAAC,EAAE/B,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,cAAc,CAAC,EAAEP,UAAU,CAAC,EAAE9D,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,aAAa,CAAC,EAAE,CAACgC,SAAS,IAAIC,UAAU,KAAKpC,QAAQ,IAAIO,aAAa,CAAC,EAAEzE,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAAC0B,MAAM,CAACD,aAAa,EAAE,YAAY,CAAC,EAAE,CAACoB,eAAe,IAAItB,QAAQ,CAAC,EAAEvB,WAAW,GAAGoB,eAAe,CAAClB,SAAS,EAAEkD,aAAa,CAAC;IAC5mCD,KAAK,EAAE9F,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+D,eAAe,CAAC+B,KAAK,CAAC,EAAEW,UAAU,CAAC,EAAEN,UAAU,CAAC,EAAEN,SAAS,CAAC;IAC/Hc,YAAY,EAAEA,YAAY;IAC1BI,YAAY,EAAEA,YAAY;IAC1BvE,GAAG,EAAEd,cAAc,CAAC0B,SAAS,CAAC,GAAGZ,GAAG,GAAG;EACzC,CAAC,CAAC;EAEF,OAAO,aAAanC,KAAK,CAACqF,aAAa,CAACtC,SAAS,EAAE6D,cAAc,EAAEpD,UAAU,EAAE4B,eAAe,CAAC;AACjG;AAEA,IAAIyB,OAAO,GAAG,aAAa7G,KAAK,CAAC8G,UAAU,CAAC7E,IAAI,CAAC;AACjD4E,OAAO,CAACE,WAAW,GAAG,MAAM;AAC5B,IAAIC,eAAe,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;AAC3D,IAAIC,QAAQ,GAAG,aAAajH,KAAK,CAACkH,IAAI,CAACL,OAAO,EAAE,UAAUM,IAAI,EAAEC,IAAI,EAAE;EACpE,IAAIA,IAAI,CAACC,gBAAgB,EAAE;IACzB;MAAQ;MACNL,eAAe,CAACM,KAAK,CAAC,UAAUC,QAAQ,EAAE;QACxC,OAAOJ,IAAI,CAACI,QAAQ,CAAC,KAAKH,IAAI,CAACG,QAAQ,CAAC;MAC1C,CAAC,CAAC;MAAI;MACN,CAACH,IAAI,CAACC,gBAAgB,CAACD,IAAI,CAAC3E,MAAM,EAAE0E,IAAI,CAAC1E,MAAM;IAAC;EAEpD;EAEA,OAAOvC,YAAY,CAACiH,IAAI,EAAEC,IAAI,CAAC;AACjC,CAAC,CAAC;AACF;;AAEA,IAAII,WAAW,GAAG,aAAaxH,KAAK,CAAC8G,UAAU,CAAC,UAAU9E,KAAK,EAAEG,GAAG,EAAE;EACpE,IAAIsF,iBAAiB,GAAGzH,KAAK,CAACkE,UAAU,CAAC3D,YAAY,CAAC;IAClDuD,OAAO,GAAG2D,iBAAiB,CAAC3D,OAAO;IACnCjD,QAAQ,GAAG4G,iBAAiB,CAAC5G,QAAQ;IACrCC,MAAM,GAAG2G,iBAAiB,CAAC3G,MAAM;EAErC,IAAI4B,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBgF,qBAAqB,GAAG1F,KAAK,CAAC0B,eAAe;IAC7CA,eAAe,GAAGgE,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;IAC/E1E,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;EAC3B,IAAIsC,WAAW,GAAG7B,eAAe,CAACV,OAAO;IACrCpC,WAAW,GAAG8C,eAAe,CAACT,OAAO;EACzC,IAAI2C,aAAa,GAAG5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGuC,WAAW;EAClF,IAAIM,aAAa,GAAG5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGrC,WAAW;EAClF,IAAIiD,QAAQ,GAAGnD,YAAY,CAACgC,KAAK,EAAEmD,aAAa,IAAI,CAAC,EAAEhF,QAAQ,EAAEC,MAAM,CAAC;EACxE,OAAO,aAAad,KAAK,CAACqF,aAAa,CAAC4B,QAAQ,EAAExH,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IACpEgB,OAAO,EAAE4C,aAAa;IACtB3C,OAAO,EAAE4C,aAAa;IACtBhC,QAAQ,EAAEA,QAAQ;IAClB1B,GAAG,EAAEA,GAAG;IACR2B,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF0D,WAAW,CAACT,WAAW,GAAG,aAAa;AACvC,eAAeS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}