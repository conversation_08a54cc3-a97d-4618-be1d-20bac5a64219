{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\storicoFornitori.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* StoricoFornitori - Storico fornitori\n*\n*/\nimport React, { Component } from \"react\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Calendar } from \"primereact/calendar\";\nimport { Button } from \"primereact/button\";\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { Toast } from 'primereact/toast';\nimport { Chart } from \"primereact/chart\";\nimport { Dialog } from \"primereact/dialog\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass StoricoFornitori extends Component {\n  constructor(props) {\n    super(props);\n    this.onSupplierChange = async e => {\n      this.setState({\n        selectedSupplier: e.value\n      });\n      window.sessionStorage.setItem(\"idSupplier\", e.value.code);\n      if (this.state.data2) {\n        var data = this.state.data.toLocaleDateString().split(\"/\");\n        var data2 = this.state.data2.toLocaleDateString().split(\"/\");\n      }\n      await APIRequest('GET', \"documents?idSupplying=\".concat(e.value.code, \"&documentType=FOR-ORDINE,FOR-FATACC&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page).concat(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            total: element.total,\n            totalPayed: element.totalPayed,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n      await APIRequest(\"GET\", \"supplyingagreements/?idSupplying=\".concat(e.value.code)).then(res => {\n        var agreementsFatturato = [];\n        var agreementsMerce = [];\n        var agreementsPagamento = [];\n        res.data.forEach(element => {\n          element.discount_payment.forEach(obj => {\n            obj.idPaymentMethod = this.state.pagamenti.find(item => item.id === parseInt(obj.idPaymentMethod)).description;\n          });\n          element.discount_payment.map(el => agreementsPagamento.push(el.correlati ? el.percent ? {\n            target: el.idPaymentMethod,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.idPaymentMethod,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.idPaymentMethod,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.idPaymentMethod,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }));\n          element.pfa.map(el => el.condizione.includes('€') ? agreementsFatturato.push(el.correlati ? el.percent ? {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.condizione,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }) : agreementsMerce.push(el.correlati ? el.percent ? {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.condizione,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }));\n        });\n        var fatturato = {\n          trimestre: [],\n          annuale: []\n        };\n        var merce = {\n          trimestre: [],\n          annuale: []\n        };\n        var annuale = [];\n        var index = null;\n        if (agreementsFatturato.length > 0) {\n          annuale = agreementsFatturato.filter(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          if (annuale.length > 0) {\n            fatturato.annuale = {\n              labels: [annuale.date_start, new Date().toLocaleDateString(), annuale.date_end],\n              datasets: [{\n                label: annuale.sconto,\n                data: [parseInt(annuale.target)],\n                fill: false,\n                yAxisID: 'y',\n                borderColor: '#42A5F5',\n                tension: .4\n              }]\n            };\n          }\n          index = agreementsFatturato.findIndex(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          for (var x = 0; x < agreementsFatturato.length; x++) {\n            if (x !== index) {\n              fatturato.trimestre.push({\n                labels: [agreementsFatturato[x].date_start, new Date().toLocaleDateString(), agreementsFatturato[x].date_end],\n                datasets: [{\n                  label: agreementsFatturato[x].sconto,\n                  data: [parseInt(agreementsFatturato[x].target)],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            }\n          }\n        }\n        if (agreementsMerce.length > 0) {\n          annuale = agreementsMerce.filter(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          if (annuale.length > 0) {\n            annuale.forEach(element => {\n              merce.annuale.push({\n                labels: [element.date_start, new Date().toLocaleDateString(), element.date_end],\n                datasets: [{\n                  label: element.sconto,\n                  data: [parseInt(element.target)],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            });\n          }\n          index = agreementsMerce.findIndex(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          for (var j = 0; j < agreementsMerce.length; j++) {\n            if (j !== index) {\n              merce.trimestre.push({\n                labels: [agreementsMerce[j].date_start, new Date().toLocaleDateString(), agreementsMerce[j].date_end],\n                datasets: [{\n                  label: agreementsMerce[j].sconto,\n                  data: [parseInt(agreementsMerce[j].target)],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            }\n          }\n        }\n        this.setState({\n          results3: agreementsFatturato,\n          results4: agreementsMerce,\n          results5: agreementsPagamento,\n          fatturato: fatturato,\n          merce: merce\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare gli accordi con il fornitore. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      pagamenti: null,\n      data: null,\n      data2: null,\n      displayed: true,\n      selectedSupplier: null,\n      search: '',\n      totalRecords: 0,\n      loading: false,\n      fatturato: [],\n      merce: [],\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterProd = e => {\n      this.setState({\n        search: e.item.label\n      });\n    };\n    this.warehouse = [];\n    this.supplier = [];\n    this.options = this.getLightTheme();\n    this.onSupplierChange = this.onSupplierChange.bind(this);\n    this.getLightTheme = this.getLightTheme.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.onDataChange = this.onDataChange.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    var pagamenti = [];\n    await APIRequest(\"GET\", \"supplying/?orders=true\").then(res => {\n      for (var entry of res.data) {\n        var x = {\n          name: entry.idRegistry.firstName,\n          code: entry.id\n        };\n        this.supplier.push(x);\n      }\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i fornitori. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'paymentmethods/').then(res => {\n      this.setState({\n        pagamenti: res.data\n      });\n      pagamenti = res.data;\n    }).catch(e => {\n      console.log(e);\n    });\n    var idSupplier = JSON.parse(window.sessionStorage.getItem(\"idSupplier\"));\n    if (idSupplier !== null && idSupplier !== 0) {\n      var find = this.supplier.find(el => el.code === idSupplier);\n      this.setState({\n        selectedSupplier: find,\n        displayed: false\n      });\n      await APIRequest(\"GET\", \"documents?idSupplying=\".concat(idSupplier, \"&documentType=FOR-ORDINE,FOR-FATACC&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page)).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            total: element.total,\n            totalPayed: element.totalPayed,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response7, _e$response8;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n          life: 3000\n        });\n      });\n      await APIRequest(\"GET\", \"supplyingagreements/?idSupplying=\".concat(idSupplier)).then(res => {\n        var agreementsFatturato = [];\n        var agreementsMerce = [];\n        var agreementsPagamento = [];\n        res.data.forEach(element => {\n          element.discount_payment.forEach(obj => {\n            obj.idPaymentMethod = pagamenti.find(item => item.id === parseInt(obj.idPaymentMethod)).description;\n          });\n          element.discount_payment.map(el => agreementsPagamento.push(el.correlati ? el.percent ? {\n            target: el.idPaymentMethod,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.idPaymentMethod,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.idPaymentMethod,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.idPaymentMethod,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }));\n          element.pfa.map(el => el.condizione.includes('€') ? agreementsFatturato.push(el.correlati ? el.percent ? {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.condizione,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }) : agreementsMerce.push(el.correlati ? el.percent ? {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            correlati: el.correlati,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : el.percent ? {\n            target: el.condizione,\n            sconto: \"\".concat(el.percent, \"%\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          } : {\n            target: el.condizione,\n            sconto: \"\".concat(el.fixed, \"\\u20AC\"),\n            date_start: new Date(element.date_start).toLocaleDateString(),\n            date_end: new Date(element.date_end).toLocaleDateString()\n          }));\n        });\n        var fatturato = {\n          trimestre: [],\n          annuale: []\n        };\n        var merce = {\n          trimestre: [],\n          annuale: []\n        };\n        var annuale = [];\n        var index = null;\n        if (agreementsFatturato.length > 0) {\n          annuale = agreementsFatturato.filter(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          if (annuale.length > 0) {\n            fatturato.annuale = {\n              labels: [annuale.date_start, new Date().toLocaleDateString(), annuale.date_end],\n              datasets: [{\n                label: annuale.sconto,\n                data: [parseInt(annuale.target)],\n                fill: false,\n                yAxisID: 'y',\n                borderColor: '#42A5F5',\n                tension: .4\n              }]\n            };\n          }\n          index = agreementsFatturato.findIndex(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          for (var x = 0; x < agreementsFatturato.length; x++) {\n            if (x !== index) {\n              fatturato.trimestre.push({\n                labels: [agreementsFatturato[x].date_start, new Date().toLocaleDateString(), agreementsFatturato[x].date_end],\n                datasets: [{\n                  label: agreementsFatturato[x].sconto,\n                  data: [parseInt(agreementsFatturato[x].target)],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            }\n          }\n        }\n        if (agreementsMerce.length > 0) {\n          annuale = agreementsMerce.filter(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          if (annuale.length > 0) {\n            annuale.forEach(element => {\n              merce.annuale.push({\n                labels: [element.date_start, new Date().toLocaleDateString(), element.date_end],\n                datasets: [{\n                  label: \"Target: \".concat(parseInt(element.target), \" Sconto: \").concat(element.sconto),\n                  data: [0, 1, 2],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            });\n          }\n          index = agreementsMerce.findIndex(el => el.date_start === \"1/1/\".concat(new Date().getFullYear()) && el.date_end === \"31/12/\".concat(new Date().getFullYear()));\n          for (var j = 0; j < agreementsMerce.length; j++) {\n            if (j !== index) {\n              merce.trimestre.push({\n                labels: [agreementsMerce[j].date_start, new Date().toLocaleDateString(), agreementsMerce[j].date_end],\n                datasets: [{\n                  label: agreementsMerce[j].sconto,\n                  data: [parseInt(agreementsMerce[j].target)],\n                  fill: false,\n                  yAxisID: 'y',\n                  borderColor: '#42A5F5',\n                  tension: .4\n                }]\n              });\n            }\n          }\n        }\n        this.setState({\n          results3: agreementsFatturato,\n          results4: agreementsMerce,\n          results5: agreementsPagamento,\n          fatturato: fatturato,\n          merce: merce\n        });\n      }).catch(e => {\n        var _e$response9, _e$response0;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare gli accordi con il fornitore. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n  }\n  getLightTheme() {\n    let multiAxisOptions = {\n      stacked: false,\n      maintainAspectRatio: false,\n      aspectRatio: .6,\n      plugins: {\n        legend: {\n          labels: {\n            color: '#495057'\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        },\n        y: {\n          type: 'linear',\n          display: true,\n          position: 'left',\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            color: '#ebedef'\n          }\n        },\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          ticks: {\n            color: '#495057'\n          },\n          grid: {\n            drawOnChartArea: false,\n            color: '#ebedef'\n          }\n        }\n      }\n    };\n    return {\n      multiAxisOptions\n    };\n  }\n  async cambiaDati(e) {\n    this.setState({\n      value1: e.value\n    });\n    /* if (this.state.data2) {\n        var data = this.state.data.toLocaleDateString().split(\"/\")\n        var data2 = this.state.data2.toLocaleDateString().split(\"/\")\n    }\n    await APIRequest('GET', `supplyingagreement?idSupplying${this.state.selectedSupplier.code}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`)\n        .then(res => {\n            this.setState({\n                results3: res.data\n            })\n        }).catch((e) => {\n            console.log(e);\n        }) */\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    if (this.state.data2) {\n      var data = this.state.data.toLocaleDateString().split(\"/\");\n      var data2 = this.state.data2.toLocaleDateString().split(\"/\");\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = \"documents?idSupplying=\".concat(this.state.selectedSupplier.code, \"&documentType=FOR-ORDINE,FOR-FATACC&take=\").concat(event.rows, \"&skip=\").concat(event.page).concat(this.state.lazyParams.sortField ? \"&field=\".concat(this.state.lazyParams.sortField, \"&sorting=\").concat(this.state.lazyParams.sortOrder === 1 ? 'ASC' : 'DESC') : '').concat(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            total: element.total,\n            totalPayed: element.totalPayed,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    if (this.state.data2) {\n      var data = this.state.data.toLocaleDateString().split(\"/\");\n      var data2 = this.state.data2.toLocaleDateString().split(\"/\");\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      await APIRequest(\"GET\", \"documents?idSupplying=\".concat(this.state.selectedSupplier.code, \"&documentType=FOR-ORDINE,FOR-FATACC&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page, \"&field=\").concat(field, \"&sorting=\").concat(event.sortOrder === 1 ? 'ASC' : 'DESC').concat(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            total: element.total,\n            totalPayed: element.totalPayed,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  async onDataChange(e) {\n    this.setState({\n      data2: e.target.value,\n      loading: true\n    });\n    if (this.state.data) {\n      var data = this.state.data.toLocaleDateString().split(\"/\");\n      var data2 = e.target.value.toLocaleDateString().split(\"/\");\n      await APIRequest(\"GET\", \"documents?idSupplying=\".concat(this.state.selectedSupplier.code, \"&documentType=FOR-ORDINE,FOR-FATACC&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page).concat(this.state.lazyParams.sortField ? \"&field=\".concat(this.state.lazyParams.sortField, \"&sorting=\").concat(this.state.lazyParams.sortOrder === 1 ? 'ASC' : 'DESC') : '').concat(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: element.idSupplying.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            total: element.total,\n            totalPayed: element.totalPayed,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results2: documento,\n          totalRecords: res.data.totalCount,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione\",\n        detail: \"Inserire entrambe le date prima di proseguire\",\n        life: 3000\n      });\n    }\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  reset() {\n    this.setState({\n      results2: this.state.results,\n      search: ''\n    });\n  }\n  /* Reselt filtro categorie */\n  resetDesc() {\n    this.setState({\n      results2: this.state.results,\n      search: ''\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedSupplier !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  render() {\n    var _this$state$fatturato, _this$state$fatturato2, _this$state$fatturato3, _this$state$fatturato4, _this$state$merce, _this$state$merce$ann, _this$state$merce2, _this$state$merce2$tr;\n    const {\n      multiAxisOptions\n    } = this.options;\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"supplying\",\n      header: Costanti.Fornitore,\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"total\",\n      header: Costanti.Tot,\n      body: \"total\",\n      showHeader: true\n    }, {\n      field: \"totalPayed\",\n      header: Costanti.TotPag,\n      body: \"totalPayed\",\n      showHeader: true\n    }, {\n      field: \"totalTaxed\",\n      header: Costanti.TotTax,\n      body: \"totalTaxed\",\n      showHeader: true\n    }];\n    const fields2 = [{\n      field: \"target\",\n      header: Costanti.targetFatturato,\n      body: \"target\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"correlati\",\n      header: Costanti.Condizione,\n      body: \"correlati\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"sconto\",\n      header: Costanti.Sconto,\n      body: \"sconto\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_start\",\n      header: Costanti.DataInizio,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_end\",\n      header: Costanti.DataFine,\n      sortable: true,\n      showHeader: true\n    }];\n    const fields3 = [{\n      field: \"target\",\n      header: Costanti.targetMerce,\n      body: \"target\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"correlati\",\n      header: Costanti.Condizione,\n      body: \"correlati\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"sconto\",\n      header: Costanti.Sconto,\n      body: \"sconto\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_start\",\n      header: Costanti.DataInizio,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_end\",\n      header: Costanti.DataFine,\n      sortable: true,\n      showHeader: true\n    }];\n    const fields4 = [{\n      field: \"target\",\n      header: Costanti.Pagamento,\n      body: \"target\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"correlati\",\n      header: Costanti.Condizione,\n      body: \"correlati\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"sconto\",\n      header: Costanti.Sconto,\n      body: \"sconto\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_start\",\n      header: Costanti.DataInizio,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"date_end\",\n      header: Costanti.DataFine,\n      sortable: true,\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.storicoFornitori\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tableMPrice border-left pl-0\",\n        children: [this.state.selectedSupplier !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activeFilterContainer p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0 w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-user mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 73\n                  }, this), Costanti.Fornitore, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"selWar w-100\",\n                  value: this.state.selectedSupplier,\n                  options: this.supplier,\n                  onChange: this.onSupplierChange,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona fornitore\",\n                  filter: true,\n                  filterBy: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-calendar mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 67\n                  }, this), Costanti.DataInizio, \": \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"mr-3 mb-0\",\n                  value: this.state.data,\n                  onChange: e => this.setState({\n                    data: e.target.value\n                  }),\n                  dateFormat: \"dd/mm/yy\",\n                  placeholder: new Date().toLocaleDateString(),\n                  showIcon: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-calendar mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 821,\n                    columnNumber: 67\n                  }, this), Costanti.DataFine, \": \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"mr-3 mb-0\",\n                  value: this.state.data2,\n                  onChange: e => this.onDataChange(e),\n                  dateFormat: \"dd/mm/yy\",\n                  placeholder: new Date().toLocaleDateString(),\n                  disabled: this.state.data ? false : true,\n                  showIcon: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 25\n        }, this), this.state.results && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pb-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card border-0\",\n            children: /*#__PURE__*/_jsxDEV(TabView, {\n              className: \"tabview-custom\",\n              children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n                header: Costanti.Grafici,\n                leftIcon: \"pi pi-chart-bar mr-2\",\n                children: [(((_this$state$fatturato = this.state.fatturato) === null || _this$state$fatturato === void 0 ? void 0 : (_this$state$fatturato2 = _this$state$fatturato.annuale) === null || _this$state$fatturato2 === void 0 ? void 0 : _this$state$fatturato2.length) > 0 || ((_this$state$fatturato3 = this.state.fatturato) === null || _this$state$fatturato3 === void 0 ? void 0 : (_this$state$fatturato4 = _this$state$fatturato3.trimestre) === null || _this$state$fatturato4 === void 0 ? void 0 : _this$state$fatturato4.length) > 0) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: Costanti.Fatturato\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 53\n                    }, this), this.state.fatturato.annuale.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: Costanti.Annuale\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 839,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"row mb-4\",\n                        children: this.state.fatturato.annuale.map((el, key) => {\n                          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"col-12\",\n                              children: /*#__PURE__*/_jsxDEV(Chart, {\n                                type: \"line\",\n                                data: el,\n                                options: multiAxisOptions\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 845,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 844,\n                              columnNumber: 77\n                            }, this)\n                          }, key, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 843,\n                            columnNumber: 73\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 840,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true), /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: this.state.fatturato.trimestre.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: Costanti.Mensile\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"row\",\n                        children: this.state.fatturato.trimestre.map((el, key) => {\n                          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"col-12 col-lg-3\",\n                              children: /*#__PURE__*/_jsxDEV(Chart, {\n                                type: \"line\",\n                                data: el,\n                                options: multiAxisOptions\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 864,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 863,\n                              columnNumber: 77\n                            }, this)\n                          }, key, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 862,\n                            columnNumber: 73\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false)]\n                }, void 0, true), (((_this$state$merce = this.state.merce) === null || _this$state$merce === void 0 ? void 0 : (_this$state$merce$ann = _this$state$merce.annuale) === null || _this$state$merce$ann === void 0 ? void 0 : _this$state$merce$ann.length) > 0 || ((_this$state$merce2 = this.state.merce) === null || _this$state$merce2 === void 0 ? void 0 : (_this$state$merce2$tr = _this$state$merce2.trimestre) === null || _this$state$merce2$tr === void 0 ? void 0 : _this$state$merce2$tr.length) > 0) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: Costanti.Merce\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 879,\n                      columnNumber: 53\n                    }, this), this.state.merce.annuale.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: Costanti.Annuale\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"row mb-4\",\n                        children: this.state.merce.annuale.map((el, key) => {\n                          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"col-12\",\n                              children: /*#__PURE__*/_jsxDEV(Chart, {\n                                type: \"line\",\n                                data: el,\n                                options: multiAxisOptions\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 888,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 887,\n                              columnNumber: 77\n                            }, this)\n                          }, key, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 886,\n                            columnNumber: 73\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true), this.state.merce.trimestre.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: Costanti.Mensile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 900,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: this.state.merce.trimestre.map((el, key) => {\n                        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-lg-3\",\n                            children: /*#__PURE__*/_jsxDEV(Chart, {\n                              type: \"line\",\n                              data: el,\n                              options: multiAxisOptions\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 906,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 905,\n                            columnNumber: 73\n                          }, this)\n                        }, key, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 904,\n                          columnNumber: 69\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 901,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n                header: Costanti.AreaComm,\n                leftIcon: \"pi pi-chart-bar mr-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row p-3 border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: Costanti.Fatturato\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 923,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\",\n                        children: \"10\\u20AC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 925,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row p-3 border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: Costanti.Ordinato\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 933,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 932,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\",\n                        children: \"10\\u20AC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row p-3 border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: Costanti.NoteDiCredito\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 942,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\",\n                        children: \"10\\u20AC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 945,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12 col-lg-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"datatable-responsive-demo wrapper mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: Costanti.Fatturato\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(CustomDataTable, {\n                        ref: el => this.dt = el,\n                        value: this.state.results3,\n                        loading: this.state.loading,\n                        fields: fields2,\n                        dataKey: \"id\",\n                        lazy: true,\n                        filterDisplay: \"row\",\n                        autoLayout: true,\n                        hideHeader: true,\n                        fileNames: \"Premi fine anno fatturato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 956,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12 col-lg-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"datatable-responsive-demo wrapper mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: Costanti.Merce\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 972,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(CustomDataTable, {\n                        ref: el => this.dt = el,\n                        value: this.state.results4,\n                        loading: this.state.loading,\n                        fields: fields3,\n                        dataKey: \"id\",\n                        lazy: true,\n                        filterDisplay: \"row\",\n                        autoLayout: true,\n                        hideHeader: true,\n                        fileNames: \"Premi fine anno merce\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 974,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 971,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"datatable-responsive-demo wrapper mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: Costanti.ScontoPag\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(CustomDataTable, {\n                        ref: el => this.dt = el,\n                        value: this.state.results5,\n                        loading: this.state.loading,\n                        fields: fields4,\n                        dataKey: \"id\",\n                        lazy: true,\n                        filterDisplay: \"row\",\n                        autoLayout: true,\n                        hideHeader: true,\n                        fileNames: \"Premi fine anno\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 992,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n                style: {\n                  padding: '0px'\n                },\n                header: Costanti.Documenti,\n                leftIcon: \"pi pi-th-large mr-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"datatable-responsive-demo wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n                    ref: el => this.dt = el,\n                    value: this.state.results,\n                    loading: this.state.loading,\n                    fields: fields,\n                    dataKey: \"id\",\n                    lazy: true,\n                    filterDisplay: \"row\",\n                    paginator: true,\n                    onPage: this.onPage,\n                    first: this.state.lazyParams.first,\n                    totalRecords: this.state.totalRecords,\n                    rows: this.state.lazyParams.rows,\n                    rowsPerPageOptions: [10, 20, 50],\n                    autoLayout: true,\n                    showExportCsvButton: true,\n                    onSort: this.onSort,\n                    sortField: this.state.lazyParams.sortField,\n                    sortOrder: this.state.lazyParams.sortOrder,\n                    onFilter: this.onFilter,\n                    filters: this.state.lazyParams.filters,\n                    classInputSearch: false,\n                    fileNames: \"Vendite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1009,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un fornitore \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 46\n            }, this), Costanti.Fornitore]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar w-100\",\n            value: this.state.selectedSupplier,\n            options: this.supplier,\n            onChange: this.onSupplierChange,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona fornitore\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default StoricoFornitori;", "map": {"version": 3, "names": ["React", "Component", "JoyrideGen", "APIRequest", "<PERSON><PERSON>", "Dropdown", "Calendar", "<PERSON><PERSON>", "TabView", "TabPanel", "Toast", "Chart", "Dialog", "CustomDataTable", "Nav", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StoricoFornitori", "constructor", "props", "onSupplierChange", "e", "setState", "selectedSupplier", "value", "window", "sessionStorage", "setItem", "code", "state", "data2", "data", "toLocaleDateString", "split", "concat", "lazyParams", "rows", "page", "then", "res", "documento", "documents", "for<PERSON>ach", "element", "x", "id", "number", "type", "supplying", "idSupplying", "idRegistry", "firstName", "documentDate", "tasks", "total", "totalPayed", "totalTaxed", "push", "results", "results2", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "response", "undefined", "message", "life", "agreementsFatturato", "agreementsMerce", "agreementsPagamento", "discount_payment", "obj", "idPaymentMethod", "pagamenti", "find", "item", "parseInt", "description", "map", "el", "correlati", "percent", "target", "sconto", "date_start", "Date", "date_end", "fixed", "pfa", "condizione", "includes", "fatturato", "trimestre", "annuale", "merce", "index", "length", "filter", "getFullYear", "labels", "datasets", "label", "fill", "yAxisID", "borderColor", "tension", "findIndex", "j", "results3", "results4", "results5", "_e$response3", "_e$response4", "resultDialog", "resultDialog2", "displayed", "search", "sortField", "sortOrder", "filters", "matchMode", "filterProd", "warehouse", "supplier", "options", "getLightTheme", "bind", "reset", "resetDesc", "onPage", "onSort", "onFilter", "onDataChange", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "entry", "name", "_e$response5", "_e$response6", "idSupplier", "JSON", "parse", "getItem", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "multiAxisOptions", "stacked", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "color", "scales", "ticks", "grid", "y", "display", "position", "y1", "drawOnChartArea", "cambiaDati", "value1", "event", "loadLazyTimeout", "clearTimeout", "setTimeout", "url", "_e$response1", "_e$response10", "Math", "random", "field", "_objectSpread", "_e$response11", "_e$response12", "loadLazyData", "_e$response13", "_e$response14", "render", "_this$state$fatturato", "_this$state$fatturato2", "_this$state$fatturato3", "_this$state$fatturato4", "_this$state$merce", "_this$state$merce$ann", "_this$state$merce2", "_this$state$merce2$tr", "resultD<PERSON><PERSON><PERSON><PERSON>er", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "header", "NDoc", "body", "sortable", "showHeader", "Fornitore", "DataDoc", "<PERSON><PERSON>", "TotPag", "TotTax", "fields2", "targetFatturato", "Condizione", "Sconto", "DataInizio", "DataFine", "fields3", "targetMerce", "fields4", "Pagamento", "ref", "storicoFornitori", "style", "onChange", "optionLabel", "placeholder", "filterBy", "dateFormat", "showIcon", "disabled", "<PERSON><PERSON>", "leftIcon", "<PERSON><PERSON><PERSON>", "Annuale", "key", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "AreaComm", "Ordinato", "NoteDiCredito", "dt", "dataKey", "lazy", "filterDisplay", "autoLayout", "<PERSON><PERSON>ead<PERSON>", "fileNames", "ScontoPag", "padding", "Documenti", "paginator", "rowsPerPageOptions", "showExportCsvButton", "classInputSearch", "visible", "Primadiproseguire", "modal", "onHide", "footer", "title", "content"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/storicoFornitori.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* StoricoFornitori - Storico fornitori\n*\n*/\nimport React, { Component } from \"react\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Calendar } from \"primereact/calendar\";\nimport { Button } from \"primereact/button\";\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { Toast } from 'primereact/toast';\nimport { Chart } from \"primereact/chart\";\nimport { Dialog } from \"primereact/dialog\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport Nav from \"../../components/navigation/Nav\";\n\nclass StoricoFornitori extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            pagamenti: null,\n            data: null,\n            data2: null,\n            displayed: true,\n            selectedSupplier: null,\n            search: '',\n            totalRecords: 0,\n            loading: false,\n            fatturato: [],\n            merce: [],\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        }\n        /* Ricerca elementi per categoria selezionata */\n        this.filterProd = e => {\n            this.setState({\n                search: e.item.label\n            });\n        };\n        this.warehouse = []\n        this.supplier = []\n        this.options = this.getLightTheme();\n        this.onSupplierChange = this.onSupplierChange.bind(this);\n        this.getLightTheme = this.getLightTheme.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.onDataChange = this.onDataChange.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        var pagamenti = []\n        await APIRequest(\"GET\", \"supplying/?orders=true\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    var x = {\n                        name: entry.idRegistry.firstName,\n                        code: entry.id\n                    };\n                    this.supplier.push(x);\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i fornitori. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'paymentmethods/')\n            .then(res => {\n                this.setState({ pagamenti: res.data })\n                pagamenti = res.data\n            }).catch((e) => {\n                console.log(e)\n            })\n        var idSupplier = JSON.parse(window.sessionStorage.getItem(\"idSupplier\"))\n        if (idSupplier !== null && idSupplier !== 0) {\n            var find = this.supplier.find(el => el.code === idSupplier)\n            this.setState({ selectedSupplier: find, displayed: false });\n            await APIRequest(\"GET\", `documents?idSupplying=${idSupplier}&documentType=FOR-ORDINE,FOR-FATACC&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            total: element.total,\n                            totalPayed: element.totalPayed,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            await APIRequest(\"GET\", `supplyingagreements/?idSupplying=${idSupplier}`)\n                .then((res) => {\n                    var agreementsFatturato = []\n                    var agreementsMerce = []\n                    var agreementsPagamento = []\n                    res.data.forEach(element => {\n                        element.discount_payment.forEach(obj => {\n                            obj.idPaymentMethod = pagamenti.find(item => item.id === parseInt(obj.idPaymentMethod)).description\n                        })\n                        element.discount_payment.map(el => agreementsPagamento.push(el.correlati ? (el.percent ? ({ target: el.idPaymentMethod, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.idPaymentMethod, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.idPaymentMethod, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.idPaymentMethod, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() })))\n                        element.pfa.map(el => el.condizione.includes('€') ? (agreementsFatturato.push(el.correlati ? (el.percent ? ({ target: el.condizione, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.condizione, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.condizione, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.condizione, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }))) : (agreementsMerce.push(el.correlati ? (el.percent ? ({ target: el.condizione, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.condizione, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.condizione, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.condizione, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }))))\n                    })\n                    var fatturato = { trimestre: [], annuale: [] }\n                    var merce = { trimestre: [], annuale: [] }\n                    var annuale = []\n                    var index = null\n                    if (agreementsFatturato.length > 0) {\n                        annuale = agreementsFatturato.filter(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                        if (annuale.length > 0) {\n                            fatturato.annuale = {\n                                labels: [annuale.date_start, new Date().toLocaleDateString(), annuale.date_end],\n                                datasets: [\n                                    {\n                                        label: annuale.sconto,\n                                        data: [parseInt(annuale.target)],\n                                        fill: false,\n                                        yAxisID: 'y',\n                                        borderColor: '#42A5F5',\n                                        tension: .4\n                                    }\n                                ]\n                            }\n                        }\n                        index = agreementsFatturato.findIndex(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                        for (var x = 0; x < agreementsFatturato.length; x++) {\n                            if (x !== index) {\n                                fatturato.trimestre.push({\n                                    labels: [agreementsFatturato[x].date_start, new Date().toLocaleDateString(), agreementsFatturato[x].date_end],\n                                    datasets: [\n                                        {\n                                            label: agreementsFatturato[x].sconto,\n                                            data: [parseInt(agreementsFatturato[x].target)],\n                                            fill: false,\n                                            yAxisID: 'y',\n                                            borderColor: '#42A5F5',\n                                            tension: .4\n                                        }\n                                    ]\n                                })\n                            }\n                        }\n                    }\n                    if (agreementsMerce.length > 0) {\n                        annuale = agreementsMerce.filter(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                        if (annuale.length > 0) {\n                            annuale.forEach(element => {\n                                merce.annuale.push({\n                                    labels: [element.date_start, new Date().toLocaleDateString(), element.date_end],\n                                    datasets: [\n                                        {\n                                            label: `Target: ${parseInt(element.target)} Sconto: ${element.sconto}`,\n                                            data: [0, 1, 2],\n                                            fill: false,\n                                            yAxisID: 'y',\n                                            borderColor: '#42A5F5',\n                                            tension: .4\n                                        }\n                                    ]\n                                })\n                            })\n                        }\n                        index = agreementsMerce.findIndex(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                        for (var j = 0; j < agreementsMerce.length; j++) {\n                            if (j !== index) {\n                                merce.trimestre.push({\n                                    labels: [agreementsMerce[j].date_start, new Date().toLocaleDateString(), agreementsMerce[j].date_end],\n                                    datasets: [\n                                        {\n                                            label: agreementsMerce[j].sconto,\n                                            data: [parseInt(agreementsMerce[j].target)],\n                                            fill: false,\n                                            yAxisID: 'y',\n                                            borderColor: '#42A5F5',\n                                            tension: .4\n                                        }\n                                    ]\n                                })\n                            }\n                        }\n                    }\n                    this.setState({ results3: agreementsFatturato, results4: agreementsMerce, results5: agreementsPagamento, fatturato: fatturato, merce: merce })\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare gli accordi con il fornitore. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n    }\n    getLightTheme() {\n        let multiAxisOptions = {\n            stacked: false,\n            maintainAspectRatio: false,\n            aspectRatio: .6,\n            plugins: {\n                legend: {\n                    labels: {\n                        color: '#495057'\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                },\n                y: {\n                    type: 'linear',\n                    display: true,\n                    position: 'left',\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        color: '#ebedef'\n                    }\n                },\n                y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    ticks: {\n                        color: '#495057'\n                    },\n                    grid: {\n                        drawOnChartArea: false,\n                        color: '#ebedef'\n                    }\n                }\n            }\n        }\n        return {\n            multiAxisOptions\n        }\n    }\n    async cambiaDati(e) {\n        this.setState({\n            value1: e.value\n        })\n        /* if (this.state.data2) {\n            var data = this.state.data.toLocaleDateString().split(\"/\")\n            var data2 = this.state.data2.toLocaleDateString().split(\"/\")\n        }\n        await APIRequest('GET', `supplyingagreement?idSupplying${this.state.selectedSupplier.code}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`)\n            .then(res => {\n                this.setState({\n                    results3: res.data\n                })\n            }).catch((e) => {\n                console.log(e);\n            }) */\n    }\n    onSupplierChange = async (e) => {\n        this.setState({ selectedSupplier: e.value })\n        window.sessionStorage.setItem(\"idSupplier\", e.value.code);\n        if (this.state.data2) {\n            var data = this.state.data.toLocaleDateString().split(\"/\")\n            var data2 = this.state.data2.toLocaleDateString().split(\"/\")\n        }\n        await APIRequest('GET', `documents?idSupplying=${e.value.code}&documentType=FOR-ORDINE,FOR-FATACC&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        tasks: element.tasks,\n                        total: element.total,\n                        totalPayed: element.totalPayed,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results2: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", `supplyingagreements/?idSupplying=${e.value.code}`)\n            .then((res) => {\n                var agreementsFatturato = []\n                var agreementsMerce = []\n                var agreementsPagamento = []\n                res.data.forEach(element => {\n                    element.discount_payment.forEach(obj => {\n                        obj.idPaymentMethod = this.state.pagamenti.find(item => item.id === parseInt(obj.idPaymentMethod)).description\n                    })\n                    element.discount_payment.map(el => agreementsPagamento.push(el.correlati ? (el.percent ? ({ target: el.idPaymentMethod, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.idPaymentMethod, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.idPaymentMethod, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.idPaymentMethod, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() })))\n                    element.pfa.map(el => el.condizione.includes('€') ? (agreementsFatturato.push(el.correlati ? (el.percent ? ({ target: el.condizione, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.condizione, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.condizione, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.condizione, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }))) : (agreementsMerce.push(el.correlati ? (el.percent ? ({ target: el.condizione, correlati: el.correlati, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : { target: el.condizione, correlati: el.correlati, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }) : (el.percent ? { target: el.condizione, sconto: `${el.percent}%`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() } : { target: el.condizione, sconto: `${el.fixed}€`, date_start: new Date(element.date_start).toLocaleDateString(), date_end: new Date(element.date_end).toLocaleDateString() }))))\n                })\n                var fatturato = { trimestre: [], annuale: [] }\n                var merce = { trimestre: [], annuale: [] }\n                var annuale = []\n                var index = null\n                if (agreementsFatturato.length > 0) {\n                    annuale = agreementsFatturato.filter(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                    if (annuale.length > 0) {\n                        fatturato.annuale = {\n                            labels: [annuale.date_start, new Date().toLocaleDateString(), annuale.date_end],\n                            datasets: [\n                                {\n                                    label: annuale.sconto,\n                                    data: [parseInt(annuale.target)],\n                                    fill: false,\n                                    yAxisID: 'y',\n                                    borderColor: '#42A5F5',\n                                    tension: .4\n                                }\n                            ]\n                        }\n                    }\n                    index = agreementsFatturato.findIndex(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                    for (var x = 0; x < agreementsFatturato.length; x++) {\n                        if (x !== index) {\n                            fatturato.trimestre.push({\n                                labels: [agreementsFatturato[x].date_start, new Date().toLocaleDateString(), agreementsFatturato[x].date_end],\n                                datasets: [\n                                    {\n                                        label: agreementsFatturato[x].sconto,\n                                        data: [parseInt(agreementsFatturato[x].target)],\n                                        fill: false,\n                                        yAxisID: 'y',\n                                        borderColor: '#42A5F5',\n                                        tension: .4\n                                    }\n                                ]\n                            })\n                        }\n                    }\n                }\n                if (agreementsMerce.length > 0) {\n                    annuale = agreementsMerce.filter(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                    if (annuale.length > 0) {\n                        annuale.forEach(element => {\n                            merce.annuale.push({\n                                labels: [element.date_start, new Date().toLocaleDateString(), element.date_end],\n                                datasets: [\n                                    {\n                                        label: element.sconto,\n                                        data: [parseInt(element.target)],\n                                        fill: false,\n                                        yAxisID: 'y',\n                                        borderColor: '#42A5F5',\n                                        tension: .4\n                                    }\n                                ]\n                            })\n                        })\n                    }\n                    index = agreementsMerce.findIndex(el => el.date_start === `1/1/${new Date().getFullYear()}` && el.date_end === `31/12/${new Date().getFullYear()}`)\n                    for (var j = 0; j < agreementsMerce.length; j++) {\n                        if (j !== index) {\n                            merce.trimestre.push({\n                                labels: [agreementsMerce[j].date_start, new Date().toLocaleDateString(), agreementsMerce[j].date_end],\n                                datasets: [\n                                    {\n                                        label: agreementsMerce[j].sconto,\n                                        data: [parseInt(agreementsMerce[j].target)],\n                                        fill: false,\n                                        yAxisID: 'y',\n                                        borderColor: '#42A5F5',\n                                        tension: .4\n                                    }\n                                ]\n                            })\n                        }\n                    }\n                }\n                this.setState({ results3: agreementsFatturato, results4: agreementsMerce, results5: agreementsPagamento, fatturato: fatturato, merce: merce })\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli accordi con il fornitore. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        if (this.state.data2) {\n            var data = this.state.data.toLocaleDateString().split(\"/\")\n            var data2 = this.state.data2.toLocaleDateString().split(\"/\")\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = `documents?idSupplying=${this.state.selectedSupplier.code}&documentType=FOR-ORDINE,FOR-FATACC&take=${event.rows}&skip=${event.page}${this.state.lazyParams.sortField ? `&field=${this.state.lazyParams.sortField}&sorting=${(this.state.lazyParams.sortOrder === 1 ? 'ASC' : 'DESC')}` : ''}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            total: element.total,\n                            totalPayed: element.totalPayed,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        if (this.state.data2) {\n            var data = this.state.data.toLocaleDateString().split(\"/\")\n            var data2 = this.state.data2.toLocaleDateString().split(\"/\")\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            await APIRequest(\"GET\", `documents?idSupplying=${this.state.selectedSupplier.code}&documentType=FOR-ORDINE,FOR-FATACC&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}&field=${field}&sorting=${(event.sortOrder === 1 ? 'ASC' : 'DESC')}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            total: element.total,\n                            totalPayed: element.totalPayed,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    async onDataChange(e) {\n        this.setState({ data2: e.target.value, loading: true })\n        if (this.state.data) {\n            var data = this.state.data.toLocaleDateString().split(\"/\")\n            var data2 = e.target.value.toLocaleDateString().split(\"/\")\n            await APIRequest(\"GET\", `documents?idSupplying=${this.state.selectedSupplier.code}&documentType=FOR-ORDINE,FOR-FATACC&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}${this.state.lazyParams.sortField ? `&field=${this.state.lazyParams.sortField}&sorting=${(this.state.lazyParams.sortOrder === 1 ? 'ASC' : 'DESC')}` : ''}${(data && data2 ? '&dateRif=true&dateFrom=' + data[2] + '-' + data[1] + '-' + data[0] + '&dateTo=' + data2[2] + '-' + data2[1] + '-' + data2[0] : '')}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            total: element.total,\n                            totalPayed: element.totalPayed,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results2: documento,\n                        totalRecords: res.data.totalCount,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione\",\n                detail: \"Inserire entrambe le date prima di proseguire\",\n                life: 3000,\n            });\n        }\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    reset() {\n        this.setState({\n            results2: this.state.results,\n            search: ''\n        })\n    }\n    /* Reselt filtro categorie */\n    resetDesc() {\n        this.setState({\n            results2: this.state.results,\n            search: ''\n        })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedSupplier !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog2: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog2: false\n        })\n    }\n    render() {\n        const { multiAxisOptions } = this.options;\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                showHeader: true,\n            },\n            {\n                field: \"supplying\",\n                header: Costanti.Fornitore,\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"total\",\n                header: Costanti.Tot,\n                body: \"total\",\n                showHeader: true,\n            },\n            {\n                field: \"totalPayed\",\n                header: Costanti.TotPag,\n                body: \"totalPayed\",\n                showHeader: true,\n            },\n            {\n                field: \"totalTaxed\",\n                header: Costanti.TotTax,\n                body: \"totalTaxed\",\n                showHeader: true,\n            },\n        ];\n        const fields2 = [\n            {\n                field: \"target\",\n                header: Costanti.targetFatturato,\n                body: \"target\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"correlati\",\n                header: Costanti.Condizione,\n                body: \"correlati\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"sconto\",\n                header: Costanti.Sconto,\n                body: \"sconto\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_start\",\n                header: Costanti.DataInizio,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_end\",\n                header: Costanti.DataFine,\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const fields3 = [\n            {\n                field: \"target\",\n                header: Costanti.targetMerce,\n                body: \"target\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"correlati\",\n                header: Costanti.Condizione,\n                body: \"correlati\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"sconto\",\n                header: Costanti.Sconto,\n                body: \"sconto\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_start\",\n                header: Costanti.DataInizio,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_end\",\n                header: Costanti.DataFine,\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const fields4 = [\n            {\n                field: \"target\",\n                header: Costanti.Pagamento,\n                body: \"target\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"correlati\",\n                header: Costanti.Condizione,\n                body: \"correlati\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"sconto\",\n                header: Costanti.Sconto,\n                body: \"sconto\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_start\",\n                header: Costanti.DataInizio,\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"date_end\",\n                header: Costanti.DataFine,\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        return (\n            <div className=\"card\">\n                <Nav />\n                <Toast ref={(el) => this.toast = el} />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.storicoFornitori}</h1>\n                </div>\n                <div className=\"tableMPrice border-left pl-0\">\n                    {this.state.selectedSupplier !== null &&\n                        <div className='activeFilterContainer p-2'>\n                            <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Fornitore}:</h5>\n                                        <Dropdown className='selWar w-100' value={this.state.selectedSupplier} options={this.supplier} onChange={this.onSupplierChange} optionLabel=\"name\" placeholder=\"Seleziona fornitore\" filter filterBy=\"name\" />\n                                    </div>\n                                </li>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0\"><i className=\"pi pi-calendar mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.DataInizio}: </h5>\n                                        <Calendar className=\"mr-3 mb-0\" value={this.state.data} onChange={(e) => this.setState({ data: e.target.value })} dateFormat=\"dd/mm/yy\" placeholder={new Date().toLocaleDateString()} showIcon />\n                                        <h5 className=\"mr-3 mb-0\"><i className=\"pi pi-calendar mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.DataFine}: </h5>\n                                        <Calendar className=\"mr-3 mb-0\" value={this.state.data2} onChange={(e) => this.onDataChange(e)} dateFormat=\"dd/mm/yy\" placeholder={new Date().toLocaleDateString()} disabled={this.state.data ? false : true} showIcon />\n                                    </div>\n                                </li>\n                            </ul>\n                        </div>\n                    }\n                    {this.state.results &&\n                        <div className=\"pb-0\">\n                            <div className=\"card border-0\">\n                                <TabView className=\"tabview-custom\">\n                                    <TabPanel header={Costanti.Grafici} leftIcon=\"pi pi-chart-bar mr-2\">\n                                        {(this.state.fatturato?.annuale?.length > 0 || this.state.fatturato?.trimestre?.length > 0) &&\n                                            <>\n                                                <>\n                                                    <h4>{Costanti.Fatturato}</h4>\n                                                    {this.state.fatturato.annuale.length > 0 &&\n                                                        <>\n                                                            <h5>{Costanti.Annuale}</h5>\n                                                            <div className=\"row mb-4\">\n                                                                {this.state.fatturato.annuale.map((el, key) => {\n                                                                    return (\n                                                                        <React.Fragment key={key}>\n                                                                            <div className=\"col-12\">\n                                                                                <Chart type=\"line\" data={el} options={multiAxisOptions} />\n                                                                            </div>\n                                                                        </React.Fragment>\n                                                                    )\n                                                                })\n                                                                }\n                                                            </div>\n                                                        </>\n                                                    }\n                                                </>\n                                                <>\n                                                    {this.state.fatturato.trimestre.length > 0 &&\n                                                        <>\n                                                            <h5>{Costanti.Mensile}</h5>\n                                                            <div className=\"row\">\n                                                                {this.state.fatturato.trimestre.map((el, key) => {\n                                                                    return (\n                                                                        <React.Fragment key={key}>\n                                                                            <div className=\"col-12 col-lg-3\">\n                                                                                <Chart type=\"line\" data={el} options={multiAxisOptions} />\n                                                                            </div>\n                                                                        </React.Fragment>\n                                                                    )\n                                                                })\n                                                                }\n                                                            </div>\n                                                        </>\n                                                    }\n                                                </>\n                                            </>\n                                        }\n                                        {(this.state.merce?.annuale?.length > 0 || this.state.merce?.trimestre?.length > 0) &&\n                                            <>\n                                                <>\n                                                    <h4>{Costanti.Merce}</h4>\n                                                    {this.state.merce.annuale.length > 0 &&\n                                                        <>\n                                                            <h5>{Costanti.Annuale}</h5>\n                                                            <div className=\"row mb-4\">\n                                                                {this.state.merce.annuale.map((el, key) => {\n                                                                    return (\n                                                                        <React.Fragment key={key}>\n                                                                            <div className=\"col-12\">\n                                                                                <Chart type=\"line\" data={el} options={multiAxisOptions} />\n                                                                            </div>\n                                                                        </React.Fragment>\n                                                                    )\n                                                                })\n                                                                }\n                                                            </div>\n                                                        </>\n                                                    }\n                                                </>\n                                                {this.state.merce.trimestre.length > 0 &&\n                                                    <>\n                                                        <h5>{Costanti.Mensile}</h5>\n                                                        <div className=\"row\">\n                                                            {this.state.merce.trimestre.map((el, key) => {\n                                                                return (\n                                                                    <React.Fragment key={key}>\n                                                                        <div className=\"col-12 col-lg-3\">\n                                                                            <Chart type=\"line\" data={el} options={multiAxisOptions} />\n                                                                        </div>\n                                                                    </React.Fragment>\n                                                                )\n                                                            })\n                                                            }\n                                                        </div>\n                                                    </>\n                                                }\n                                            </>\n                                        }\n                                    </TabPanel>\n                                    <TabPanel header={Costanti.AreaComm} leftIcon=\"pi pi-chart-bar mr-2\">\n                                        <div className=\"row\">\n                                            <div className=\"col-4\">\n                                                <div className=\"row p-3 border\">\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\">\n                                                        <p>{Costanti.Fatturato}</p>\n                                                    </div>\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\">\n                                                        10€\n                                                    </div>\n                                                </div>\n                                            </div>\n                                            <div className=\"col-4\">\n                                                <div className=\"row p-3 border\">\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\">\n                                                        <p>{Costanti.Ordinato}</p>\n                                                    </div>\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\">\n                                                        10€\n                                                    </div>\n                                                </div>\n                                            </div>\n                                            <div className=\"col-4\">\n                                                <div className=\"row p-3 border\">\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center border-bottom\">\n                                                        <p>{Costanti.NoteDiCredito}</p>\n                                                    </div>\n                                                    <div className=\"col-6 col-lg-12 d-flex flex-column align-items-center pt-3\">\n                                                        10€\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"row\">\n                                            <div className=\"col-12 col-lg-6\">\n                                                <div className=\"datatable-responsive-demo wrapper mt-5\">\n                                                    <h4>{Costanti.Fatturato}</h4>\n                                                    {/* Componente primereact per la creazione della tabella */}\n                                                    <CustomDataTable\n                                                        ref={(el) => this.dt = el}\n                                                        value={this.state.results3}\n                                                        loading={this.state.loading}\n                                                        fields={fields2}\n                                                        dataKey=\"id\"\n                                                        lazy\n                                                        filterDisplay=\"row\"\n                                                        autoLayout={true}\n                                                        hideHeader\n                                                        fileNames=\"Premi fine anno fatturato\"\n                                                    />\n                                                </div>\n                                            </div>\n                                            <div className=\"col-12 col-lg-6\">\n                                                <div className=\"datatable-responsive-demo wrapper mt-5\">\n                                                    <h4>{Costanti.Merce}</h4>\n                                                    {/* Componente primereact per la creazione della tabella */}\n                                                    <CustomDataTable\n                                                        ref={(el) => this.dt = el}\n                                                        value={this.state.results4}\n                                                        loading={this.state.loading}\n                                                        fields={fields3}\n                                                        dataKey=\"id\"\n                                                        lazy\n                                                        filterDisplay=\"row\"\n                                                        autoLayout={true}\n                                                        hideHeader\n                                                        fileNames=\"Premi fine anno merce\"\n                                                    />\n                                                </div>\n                                            </div>\n                                            <div className=\"col-12\">\n                                                <div className=\"datatable-responsive-demo wrapper mt-5\">\n                                                    <h4>{Costanti.ScontoPag}</h4>\n                                                    {/* Componente primereact per la creazione della tabella */}\n                                                    <CustomDataTable\n                                                        ref={(el) => this.dt = el}\n                                                        value={this.state.results5}\n                                                        loading={this.state.loading}\n                                                        fields={fields4}\n                                                        dataKey=\"id\"\n                                                        lazy\n                                                        filterDisplay=\"row\"\n                                                        autoLayout={true}\n                                                        hideHeader\n                                                        fileNames=\"Premi fine anno\"\n                                                    />\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </TabPanel>\n                                    <TabPanel style={{ padding: '0px' }} header={Costanti.Documenti} leftIcon=\"pi pi-th-large mr-2\">\n                                        <div className=\"datatable-responsive-demo wrapper\">\n                                            {/* Componente primereact per la creazione della tabella */}\n                                            <CustomDataTable\n                                                ref={(el) => this.dt = el}\n                                                value={this.state.results}\n                                                loading={this.state.loading}\n                                                fields={fields}\n                                                dataKey=\"id\"\n                                                lazy\n                                                filterDisplay=\"row\"\n                                                paginator\n                                                onPage={this.onPage}\n                                                first={this.state.lazyParams.first}\n                                                totalRecords={this.state.totalRecords}\n                                                rows={this.state.lazyParams.rows}\n                                                rowsPerPageOptions={[10, 20, 50]}\n                                                autoLayout={true}\n                                                showExportCsvButton={true}\n                                                onSort={this.onSort}\n                                                sortField={this.state.lazyParams.sortField}\n                                                sortOrder={this.state.lazyParams.sortOrder}\n                                                onFilter={this.onFilter}\n                                                filters={this.state.lazyParams.filters}\n                                                classInputSearch={false}\n                                                fileNames=\"Vendite\"\n                                            />\n                                        </div>\n                                    </TabPanel>\n                                </TabView>\n                            </div>\n                        </div>\n                    }\n                </div>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un fornitore ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Fornitore}</h5>\n                        <hr></hr>\n                        <Dropdown className='selWar w-100' value={this.state.selectedSupplier} options={this.supplier} onChange={this.onSupplierChange} optionLabel=\"name\" placeholder=\"Seleziona fornitore\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n            </div>\n        );\n    }\n}\n\nexport default StoricoFornitori;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,EAAEC,QAAQ,QAAQ,oBAAoB;AACtD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,gBAAgB,SAASlB,SAAS,CAAC;EACrCmB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAAC,KAmSjBC,gBAAgB,GAAG,MAAOC,CAAC,IAAK;MAC5B,IAAI,CAACC,QAAQ,CAAC;QAAEC,gBAAgB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC5CC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEN,CAAC,CAACG,KAAK,CAACI,IAAI,CAAC;MACzD,IAAI,IAAI,CAACC,KAAK,CAACC,KAAK,EAAE;QAClB,IAAIC,IAAI,GAAG,IAAI,CAACF,KAAK,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;QAC1D,IAAIH,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK,CAACE,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAChE;MACA,MAAMhC,UAAU,CAAC,KAAK,2BAAAiC,MAAA,CAA2Bb,CAAC,CAACG,KAAK,CAACI,IAAI,+CAAAM,MAAA,CAA4C,IAAI,CAACL,KAAK,CAACM,UAAU,CAACC,IAAI,YAAAF,MAAA,CAAS,IAAI,CAACL,KAAK,CAACM,UAAU,CAACE,IAAI,EAAAH,MAAA,CAAIH,IAAI,IAAID,KAAK,GAAG,yBAAyB,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAG,CAAC,CAC3TQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACR,IAAI,CAACU,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,SAAS,EAAEL,OAAO,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS;YACnDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,UAAU,EAAEZ,OAAO,CAACY,UAAU;YAC9BC,UAAU,EAAEb,OAAO,CAACa;UACxB,CAAC;UACDhB,SAAS,CAACiB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVoC,OAAO,EAAElB,SAAS;UAClBmB,QAAQ,EAAEnB,SAAS;UACnBoB,YAAY,EAAErB,GAAG,CAACR,IAAI,CAAC8B,UAAU;UACjC1B,UAAU,EAAE;YAAE2B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACM,UAAU,CAAC2B,KAAK;YAAE1B,IAAI,EAAE,IAAI,CAACP,KAAK,CAACM,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACR,KAAK,CAACM,UAAU,CAACE,IAAI;YAAE0B,SAAS,EAAExB,GAAG,CAACR,IAAI,CAAC8B,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACM,UAAU,CAACC;UAAM,CAAC;UACpL4B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAA6C,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAxC,MAAA,CAAmF,EAAAgC,WAAA,GAAA7C,CAAC,CAACsD,QAAQ,cAAAT,WAAA,uBAAVA,WAAA,CAAYnC,IAAI,MAAK6C,SAAS,IAAAT,YAAA,GAAG9C,CAAC,CAACsD,QAAQ,cAAAR,YAAA,uBAAVA,YAAA,CAAYpC,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,MAAM7E,UAAU,CAAC,KAAK,sCAAAiC,MAAA,CAAsCb,CAAC,CAACG,KAAK,CAACI,IAAI,CAAE,CAAC,CACtEU,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIwC,mBAAmB,GAAG,EAAE;QAC5B,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,mBAAmB,GAAG,EAAE;QAC5B1C,GAAG,CAACR,IAAI,CAACW,OAAO,CAACC,OAAO,IAAI;UACxBA,OAAO,CAACuC,gBAAgB,CAACxC,OAAO,CAACyC,GAAG,IAAI;YACpCA,GAAG,CAACC,eAAe,GAAG,IAAI,CAACvD,KAAK,CAACwD,SAAS,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC1C,EAAE,KAAK2C,QAAQ,CAACL,GAAG,CAACC,eAAe,CAAC,CAAC,CAACK,WAAW;UAClH,CAAC,CAAC;UACF9C,OAAO,CAACuC,gBAAgB,CAACQ,GAAG,CAACC,EAAE,IAAIV,mBAAmB,CAACxB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEQ,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEQ,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEW,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEW,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAC,CAAC;UAC52BW,OAAO,CAACyD,GAAG,CAACV,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACU,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAIvB,mBAAmB,CAACtB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAC,GAAKgD,eAAe,CAACvB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAE,CAAC;QACpqD,CAAC,CAAC;QACF,IAAIuE,SAAS,GAAG;UAAEC,SAAS,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAC9C,IAAIC,KAAK,GAAG;UAAEF,SAAS,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAC1C,IAAIA,OAAO,GAAG,EAAE;QAChB,IAAIE,KAAK,GAAG,IAAI;QAChB,IAAI5B,mBAAmB,CAAC6B,MAAM,GAAG,CAAC,EAAE;UAChCH,OAAO,GAAG1B,mBAAmB,CAAC8B,MAAM,CAAClB,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACtJ,IAAIL,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;YACpBL,SAAS,CAACE,OAAO,GAAG;cAChBM,MAAM,EAAE,CAACN,OAAO,CAACT,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEyE,OAAO,CAACP,QAAQ,CAAC;cAC/Ec,QAAQ,EAAE,CACN;gBACIC,KAAK,EAAER,OAAO,CAACV,MAAM;gBACrBhE,IAAI,EAAE,CAACyD,QAAQ,CAACiB,OAAO,CAACX,MAAM,CAAC,CAAC;gBAChCoB,IAAI,EAAE,KAAK;gBACXC,OAAO,EAAE,GAAG;gBACZC,WAAW,EAAE,SAAS;gBACtBC,OAAO,EAAE;cACb,CAAC;YAET,CAAC;UACL;UACAV,KAAK,GAAG5B,mBAAmB,CAACuC,SAAS,CAAC3B,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACvJ,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,mBAAmB,CAAC6B,MAAM,EAAEhE,CAAC,EAAE,EAAE;YACjD,IAAIA,CAAC,KAAK+D,KAAK,EAAE;cACbJ,SAAS,CAACC,SAAS,CAAC/C,IAAI,CAAC;gBACrBsD,MAAM,EAAE,CAAChC,mBAAmB,CAACnC,CAAC,CAAC,CAACoD,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAE+C,mBAAmB,CAACnC,CAAC,CAAC,CAACsD,QAAQ,CAAC;gBAC7Gc,QAAQ,EAAE,CACN;kBACIC,KAAK,EAAElC,mBAAmB,CAACnC,CAAC,CAAC,CAACmD,MAAM;kBACpChE,IAAI,EAAE,CAACyD,QAAQ,CAACT,mBAAmB,CAACnC,CAAC,CAAC,CAACkD,MAAM,CAAC,CAAC;kBAC/CoB,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN;UACJ;QACJ;QACA,IAAIrC,eAAe,CAAC4B,MAAM,GAAG,CAAC,EAAE;UAC5BH,OAAO,GAAGzB,eAAe,CAAC6B,MAAM,CAAClB,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UAClJ,IAAIL,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;YACpBH,OAAO,CAAC/D,OAAO,CAACC,OAAO,IAAI;cACvB+D,KAAK,CAACD,OAAO,CAAChD,IAAI,CAAC;gBACfsD,MAAM,EAAE,CAACpE,OAAO,CAACqD,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEW,OAAO,CAACuD,QAAQ,CAAC;gBAC/Ec,QAAQ,EAAE,CACN;kBACIC,KAAK,EAAEtE,OAAO,CAACoD,MAAM;kBACrBhE,IAAI,EAAE,CAACyD,QAAQ,CAAC7C,OAAO,CAACmD,MAAM,CAAC,CAAC;kBAChCoB,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN,CAAC,CAAC;UACN;UACAV,KAAK,GAAG3B,eAAe,CAACsC,SAAS,CAAC3B,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACnJ,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,eAAe,CAAC4B,MAAM,EAAEW,CAAC,EAAE,EAAE;YAC7C,IAAIA,CAAC,KAAKZ,KAAK,EAAE;cACbD,KAAK,CAACF,SAAS,CAAC/C,IAAI,CAAC;gBACjBsD,MAAM,EAAE,CAAC/B,eAAe,CAACuC,CAAC,CAAC,CAACvB,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEgD,eAAe,CAACuC,CAAC,CAAC,CAACrB,QAAQ,CAAC;gBACrGc,QAAQ,EAAE,CACN;kBACIC,KAAK,EAAEjC,eAAe,CAACuC,CAAC,CAAC,CAACxB,MAAM;kBAChChE,IAAI,EAAE,CAACyD,QAAQ,CAACR,eAAe,CAACuC,CAAC,CAAC,CAACzB,MAAM,CAAC,CAAC;kBAC3CoB,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN;UACJ;QACJ;QACA,IAAI,CAAC/F,QAAQ,CAAC;UAAEkG,QAAQ,EAAEzC,mBAAmB;UAAE0C,QAAQ,EAAEzC,eAAe;UAAE0C,QAAQ,EAAEzC,mBAAmB;UAAEsB,SAAS,EAAEA,SAAS;UAAEG,KAAK,EAAEA;QAAM,CAAC,CAAC;MAClJ,CAAC,CAAC,CACDzC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAAsG,YAAA,EAAAC,YAAA;QACVxD,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,2FAAAxC,MAAA,CAAwF,EAAAyF,YAAA,GAAAtG,CAAC,CAACsD,QAAQ,cAAAgD,YAAA,uBAAVA,YAAA,CAAY5F,IAAI,MAAK6C,SAAS,IAAAgD,YAAA,GAAGvG,CAAC,CAACsD,QAAQ,cAAAiD,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UAC7JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAhbG,IAAI,CAACjD,KAAK,GAAG;MACT6B,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACd6D,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdG,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBzC,SAAS,EAAE,IAAI;MACftD,IAAI,EAAE,IAAI;MACVD,KAAK,EAAE,IAAI;MACXiG,SAAS,EAAE,IAAI;MACfxG,gBAAgB,EAAE,IAAI;MACtByG,MAAM,EAAE,EAAE;MACVpE,YAAY,EAAE,CAAC;MACfI,OAAO,EAAE,KAAK;MACduC,SAAS,EAAE,EAAE;MACbG,KAAK,EAAE,EAAE;MACTvE,UAAU,EAAE;QACR2B,KAAK,EAAE,CAAC;QACR1B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACP4F,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAE3G,KAAK,EAAE,EAAE;YAAE4G,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE5G,KAAK,EAAE,EAAE;YAAE4G,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE5G,KAAK,EAAE,EAAE;YAAE4G,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,UAAU,GAAGhH,CAAC,IAAI;MACnB,IAAI,CAACC,QAAQ,CAAC;QACV0G,MAAM,EAAE3G,CAAC,CAACkE,IAAI,CAAC0B;MACnB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACqB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACnC,IAAI,CAACrH,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACsH,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACD,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACG,MAAM,GAAG,IAAI,CAACA,MAAM,CAACH,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACL,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACM,YAAY,GAAG,IAAI,CAACA,YAAY,CAACN,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACO,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACP,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACR,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACS,WAAW,GAAG,IAAI,CAACA,WAAW,CAACT,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMU,iBAAiBA,CAAA,EAAG;IACtB,IAAI/D,SAAS,GAAG,EAAE;IAClB,MAAMpF,UAAU,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAC5CqC,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAI8G,KAAK,IAAI9G,GAAG,CAACR,IAAI,EAAE;QACxB,IAAIa,CAAC,GAAG;UACJ0G,IAAI,EAAED,KAAK,CAACnG,UAAU,CAACC,SAAS;UAChCvB,IAAI,EAAEyH,KAAK,CAACxG;QAChB,CAAC;QACD,IAAI,CAAC0F,QAAQ,CAAC9E,IAAI,CAACb,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC,CACDqB,KAAK,CAAE5C,CAAC,IAAK;MAAA,IAAAkI,YAAA,EAAAC,YAAA;MACVpF,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;MACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAxC,MAAA,CAAuE,EAAAqH,YAAA,GAAAlI,CAAC,CAACsD,QAAQ,cAAA4E,YAAA,uBAAVA,YAAA,CAAYxH,IAAI,MAAK6C,SAAS,IAAA4E,YAAA,GAAGnI,CAAC,CAACsD,QAAQ,cAAA6E,YAAA,uBAAVA,YAAA,CAAYzH,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAM7E,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC,CACrCqC,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACjB,QAAQ,CAAC;QAAE+D,SAAS,EAAE9C,GAAG,CAACR;MAAK,CAAC,CAAC;MACtCsD,SAAS,GAAG9C,GAAG,CAACR,IAAI;IACxB,CAAC,CAAC,CAACkC,KAAK,CAAE5C,CAAC,IAAK;MACZ+C,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIoI,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAClI,MAAM,CAACC,cAAc,CAACkI,OAAO,CAAC,YAAY,CAAC,CAAC;IACxE,IAAIH,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,CAAC,EAAE;MACzC,IAAInE,IAAI,GAAG,IAAI,CAACiD,QAAQ,CAACjD,IAAI,CAACK,EAAE,IAAIA,EAAE,CAAC/D,IAAI,KAAK6H,UAAU,CAAC;MAC3D,IAAI,CAACnI,QAAQ,CAAC;QAAEC,gBAAgB,EAAE+D,IAAI;QAAEyC,SAAS,EAAE;MAAM,CAAC,CAAC;MAC3D,MAAM9H,UAAU,CAAC,KAAK,2BAAAiC,MAAA,CAA2BuH,UAAU,+CAAAvH,MAAA,CAA4C,IAAI,CAACL,KAAK,CAACM,UAAU,CAACC,IAAI,YAAAF,MAAA,CAAS,IAAI,CAACL,KAAK,CAACM,UAAU,CAACE,IAAI,CAAE,CAAC,CAClKC,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACR,IAAI,CAACU,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,SAAS,EAAEL,OAAO,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS;YACnDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,UAAU,EAAEZ,OAAO,CAACY,UAAU;YAC9BC,UAAU,EAAEb,OAAO,CAACa;UACxB,CAAC;UACDhB,SAAS,CAACiB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVoC,OAAO,EAAElB,SAAS;UAClBmB,QAAQ,EAAEnB,SAAS;UACnBoB,YAAY,EAAErB,GAAG,CAACR,IAAI,CAAC8B,UAAU;UACjC1B,UAAU,EAAE;YAAE2B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACM,UAAU,CAAC2B,KAAK;YAAE1B,IAAI,EAAE,IAAI,CAACP,KAAK,CAACM,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACR,KAAK,CAACM,UAAU,CAACE,IAAI;YAAE0B,SAAS,EAAExB,GAAG,CAACR,IAAI,CAAC8B,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACM,UAAU,CAACC;UAAM,CAAC;UACpL4B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAAwI,YAAA,EAAAC,YAAA;QACV1F,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAxC,MAAA,CAAmF,EAAA2H,YAAA,GAAAxI,CAAC,CAACsD,QAAQ,cAAAkF,YAAA,uBAAVA,YAAA,CAAY9H,IAAI,MAAK6C,SAAS,IAAAkF,YAAA,GAAGzI,CAAC,CAACsD,QAAQ,cAAAmF,YAAA,uBAAVA,YAAA,CAAY/H,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,MAAM7E,UAAU,CAAC,KAAK,sCAAAiC,MAAA,CAAsCuH,UAAU,CAAE,CAAC,CACpEnH,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIwC,mBAAmB,GAAG,EAAE;QAC5B,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,mBAAmB,GAAG,EAAE;QAC5B1C,GAAG,CAACR,IAAI,CAACW,OAAO,CAACC,OAAO,IAAI;UACxBA,OAAO,CAACuC,gBAAgB,CAACxC,OAAO,CAACyC,GAAG,IAAI;YACpCA,GAAG,CAACC,eAAe,GAAGC,SAAS,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC1C,EAAE,KAAK2C,QAAQ,CAACL,GAAG,CAACC,eAAe,CAAC,CAAC,CAACK,WAAW;UACvG,CAAC,CAAC;UACF9C,OAAO,CAACuC,gBAAgB,CAACQ,GAAG,CAACC,EAAE,IAAIV,mBAAmB,CAACxB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEQ,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEQ,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEW,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACP,eAAe;YAAEW,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAC,CAAC;UAC52BW,OAAO,CAACyD,GAAG,CAACV,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACU,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAIvB,mBAAmB,CAACtB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAC,GAAKgD,eAAe,CAACvB,IAAI,CAACkC,EAAE,CAACC,SAAS,GAAID,EAAE,CAACE,OAAO,GAAI;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAI;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAET,SAAS,EAAED,EAAE,CAACC,SAAS;YAAEG,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAK2D,EAAE,CAACE,OAAO,GAAG;YAAEC,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACE,OAAO,MAAG;YAAEG,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAC,GAAG;YAAE8D,MAAM,EAAEH,EAAE,CAACU,UAAU;YAAEN,MAAM,KAAA7D,MAAA,CAAKyD,EAAE,CAACQ,KAAK,WAAG;YAAEH,UAAU,EAAE,IAAIC,IAAI,CAACtD,OAAO,CAACqD,UAAU,CAAC,CAAChE,kBAAkB,CAAC,CAAC;YAAEkE,QAAQ,EAAE,IAAID,IAAI,CAACtD,OAAO,CAACuD,QAAQ,CAAC,CAAClE,kBAAkB,CAAC;UAAE,CAAE,CAAE,CAAC;QACpqD,CAAC,CAAC;QACF,IAAIuE,SAAS,GAAG;UAAEC,SAAS,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAC9C,IAAIC,KAAK,GAAG;UAAEF,SAAS,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAC1C,IAAIA,OAAO,GAAG,EAAE;QAChB,IAAIE,KAAK,GAAG,IAAI;QAChB,IAAI5B,mBAAmB,CAAC6B,MAAM,GAAG,CAAC,EAAE;UAChCH,OAAO,GAAG1B,mBAAmB,CAAC8B,MAAM,CAAClB,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACtJ,IAAIL,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;YACpBL,SAAS,CAACE,OAAO,GAAG;cAChBM,MAAM,EAAE,CAACN,OAAO,CAACT,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEyE,OAAO,CAACP,QAAQ,CAAC;cAC/Ec,QAAQ,EAAE,CACN;gBACIC,KAAK,EAAER,OAAO,CAACV,MAAM;gBACrBhE,IAAI,EAAE,CAACyD,QAAQ,CAACiB,OAAO,CAACX,MAAM,CAAC,CAAC;gBAChCoB,IAAI,EAAE,KAAK;gBACXC,OAAO,EAAE,GAAG;gBACZC,WAAW,EAAE,SAAS;gBACtBC,OAAO,EAAE;cACb,CAAC;YAET,CAAC;UACL;UACAV,KAAK,GAAG5B,mBAAmB,CAACuC,SAAS,CAAC3B,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACvJ,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,mBAAmB,CAAC6B,MAAM,EAAEhE,CAAC,EAAE,EAAE;YACjD,IAAIA,CAAC,KAAK+D,KAAK,EAAE;cACbJ,SAAS,CAACC,SAAS,CAAC/C,IAAI,CAAC;gBACrBsD,MAAM,EAAE,CAAChC,mBAAmB,CAACnC,CAAC,CAAC,CAACoD,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAE+C,mBAAmB,CAACnC,CAAC,CAAC,CAACsD,QAAQ,CAAC;gBAC7Gc,QAAQ,EAAE,CACN;kBACIC,KAAK,EAAElC,mBAAmB,CAACnC,CAAC,CAAC,CAACmD,MAAM;kBACpChE,IAAI,EAAE,CAACyD,QAAQ,CAACT,mBAAmB,CAACnC,CAAC,CAAC,CAACkD,MAAM,CAAC,CAAC;kBAC/CoB,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN;UACJ;QACJ;QACA,IAAIrC,eAAe,CAAC4B,MAAM,GAAG,CAAC,EAAE;UAC5BH,OAAO,GAAGzB,eAAe,CAAC6B,MAAM,CAAClB,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UAClJ,IAAIL,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;YACpBH,OAAO,CAAC/D,OAAO,CAACC,OAAO,IAAI;cACvB+D,KAAK,CAACD,OAAO,CAAChD,IAAI,CAAC;gBACfsD,MAAM,EAAE,CAACpE,OAAO,CAACqD,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEW,OAAO,CAACuD,QAAQ,CAAC;gBAC/Ec,QAAQ,EAAE,CACN;kBACIC,KAAK,aAAA/E,MAAA,CAAasD,QAAQ,CAAC7C,OAAO,CAACmD,MAAM,CAAC,eAAA5D,MAAA,CAAYS,OAAO,CAACoD,MAAM,CAAE;kBACtEhE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACfmF,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN,CAAC,CAAC;UACN;UACAV,KAAK,GAAG3B,eAAe,CAACsC,SAAS,CAAC3B,EAAE,IAAIA,EAAE,CAACK,UAAU,YAAA9D,MAAA,CAAY,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,IAAInB,EAAE,CAACO,QAAQ,cAAAhE,MAAA,CAAc,IAAI+D,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC,CAAE,CAAC;UACnJ,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,eAAe,CAAC4B,MAAM,EAAEW,CAAC,EAAE,EAAE;YAC7C,IAAIA,CAAC,KAAKZ,KAAK,EAAE;cACbD,KAAK,CAACF,SAAS,CAAC/C,IAAI,CAAC;gBACjBsD,MAAM,EAAE,CAAC/B,eAAe,CAACuC,CAAC,CAAC,CAACvB,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAC,EAAEgD,eAAe,CAACuC,CAAC,CAAC,CAACrB,QAAQ,CAAC;gBACrGc,QAAQ,EAAE,CACN;kBACIC,KAAK,EAAEjC,eAAe,CAACuC,CAAC,CAAC,CAACxB,MAAM;kBAChChE,IAAI,EAAE,CAACyD,QAAQ,CAACR,eAAe,CAACuC,CAAC,CAAC,CAACzB,MAAM,CAAC,CAAC;kBAC3CoB,IAAI,EAAE,KAAK;kBACXC,OAAO,EAAE,GAAG;kBACZC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAE;gBACb,CAAC;cAET,CAAC,CAAC;YACN;UACJ;QACJ;QACA,IAAI,CAAC/F,QAAQ,CAAC;UAAEkG,QAAQ,EAAEzC,mBAAmB;UAAE0C,QAAQ,EAAEzC,eAAe;UAAE0C,QAAQ,EAAEzC,mBAAmB;UAAEsB,SAAS,EAAEA,SAAS;UAAEG,KAAK,EAAEA;QAAM,CAAC,CAAC;MAClJ,CAAC,CAAC,CACDzC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAA0I,YAAA,EAAAC,YAAA;QACV5F,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,2FAAAxC,MAAA,CAAwF,EAAA6H,YAAA,GAAA1I,CAAC,CAACsD,QAAQ,cAAAoF,YAAA,uBAAVA,YAAA,CAAYhI,IAAI,MAAK6C,SAAS,IAAAoF,YAAA,GAAG3I,CAAC,CAACsD,QAAQ,cAAAqF,YAAA,uBAAVA,YAAA,CAAYjI,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UAC7JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACxD,QAAQ,CAAC;QAAEuG,YAAY,EAAE,IAAI;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;EACJ;EACAU,aAAaA,CAAA,EAAG;IACZ,IAAIwB,gBAAgB,GAAG;MACnBC,OAAO,EAAE,KAAK;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJvD,MAAM,EAAE;YACJwD,KAAK,EAAE;UACX;QACJ;MACJ,CAAC;MACDC,MAAM,EAAE;QACJ5H,CAAC,EAAE;UACC6H,KAAK,EAAE;YACHF,KAAK,EAAE;UACX,CAAC;UACDG,IAAI,EAAE;YACFH,KAAK,EAAE;UACX;QACJ,CAAC;QACDI,CAAC,EAAE;UACC5H,IAAI,EAAE,QAAQ;UACd6H,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,MAAM;UAChBJ,KAAK,EAAE;YACHF,KAAK,EAAE;UACX,CAAC;UACDG,IAAI,EAAE;YACFH,KAAK,EAAE;UACX;QACJ,CAAC;QACDO,EAAE,EAAE;UACA/H,IAAI,EAAE,QAAQ;UACd6H,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,OAAO;UACjBJ,KAAK,EAAE;YACHF,KAAK,EAAE;UACX,CAAC;UACDG,IAAI,EAAE;YACFK,eAAe,EAAE,KAAK;YACtBR,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC;IACD,OAAO;MACHN;IACJ,CAAC;EACL;EACA,MAAMe,UAAUA,CAAC3J,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MACV2J,MAAM,EAAE5J,CAAC,CAACG;IACd,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EAgJAqH,MAAMA,CAACqC,KAAK,EAAE;IACV,IAAI,CAAC5J,QAAQ,CAAC;MAAE0C,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAACmH,eAAe,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,eAAe,CAAC;IACtC;IACA,IAAI,IAAI,CAACtJ,KAAK,CAACC,KAAK,EAAE;MAClB,IAAIC,IAAI,GAAG,IAAI,CAACF,KAAK,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAC1D,IAAIH,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK,CAACE,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChE;IACA,IAAI,CAACkJ,eAAe,GAAGE,UAAU,CAAC,YAAY;MAC1C,IAAIC,GAAG,4BAAApJ,MAAA,CAA4B,IAAI,CAACL,KAAK,CAACN,gBAAgB,CAACK,IAAI,+CAAAM,MAAA,CAA4CgJ,KAAK,CAAC9I,IAAI,YAAAF,MAAA,CAASgJ,KAAK,CAAC7I,IAAI,EAAAH,MAAA,CAAG,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC8F,SAAS,aAAA/F,MAAA,CAAa,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC8F,SAAS,eAAA/F,MAAA,CAAa,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC+F,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,IAAM,EAAE,EAAAhG,MAAA,CAAIH,IAAI,IAAID,KAAK,GAAG,yBAAyB,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAG;MAC9b,MAAM7B,UAAU,CAAC,KAAK,EAAEqL,GAAG,CAAC,CACvBhJ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACR,IAAI,CAACU,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,SAAS,EAAEL,OAAO,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS;YACnDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,UAAU,EAAEZ,OAAO,CAACY,UAAU;YAC9BC,UAAU,EAAEb,OAAO,CAACa;UACxB,CAAC;UACDhB,SAAS,CAACiB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVoC,OAAO,EAAElB,SAAS;UAClBmB,QAAQ,EAAEnB,SAAS;UACnBoB,YAAY,EAAErB,GAAG,CAACR,IAAI,CAAC8B,UAAU;UACjC1B,UAAU,EAAE+I,KAAK;UACjBlH,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAAkK,YAAA,EAAAC,aAAA;QACVpH,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAxC,MAAA,CAAmF,EAAAqJ,YAAA,GAAAlK,CAAC,CAACsD,QAAQ,cAAA4G,YAAA,uBAAVA,YAAA,CAAYxJ,IAAI,MAAK6C,SAAS,IAAA4G,aAAA,GAAGnK,CAAC,CAACsD,QAAQ,cAAA6G,aAAA,uBAAVA,aAAA,CAAYzJ,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE2G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA5C,MAAMA,CAACoC,KAAK,EAAE;IACV,IAAI,CAAC5J,QAAQ,CAAC;MAAE0C,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI2H,KAAK,GAAGT,KAAK,CAACjD,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGiD,KAAK,CAACjD,SAAS;IAChG,IAAI,IAAI,CAACkD,eAAe,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,eAAe,CAAC;IACtC;IACA,IAAI,IAAI,CAACtJ,KAAK,CAACC,KAAK,EAAE;MAClB,IAAIC,IAAI,GAAG,IAAI,CAACF,KAAK,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAC1D,IAAIH,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK,CAACE,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChE;IACA,IAAI,CAACkJ,eAAe,GAAGE,UAAU,CAAC,YAAY;MAC1C,MAAMpL,UAAU,CAAC,KAAK,2BAAAiC,MAAA,CAA2B,IAAI,CAACL,KAAK,CAACN,gBAAgB,CAACK,IAAI,+CAAAM,MAAA,CAA4C,IAAI,CAACL,KAAK,CAACM,UAAU,CAACC,IAAI,YAAAF,MAAA,CAAS,IAAI,CAACL,KAAK,CAACM,UAAU,CAACE,IAAI,aAAAH,MAAA,CAAUyJ,KAAK,eAAAzJ,MAAA,CAAagJ,KAAK,CAAChD,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,EAAAhG,MAAA,CAAKH,IAAI,IAAID,KAAK,GAAG,yBAAyB,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAG,CAAC,CAClZQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACR,IAAI,CAACU,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,SAAS,EAAEL,OAAO,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS;YACnDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,UAAU,EAAEZ,OAAO,CAACY,UAAU;YAC9BC,UAAU,EAAEb,OAAO,CAACa;UACxB,CAAC;UACDhB,SAAS,CAACiB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVoC,OAAO,EAAElB,SAAS;UAClBmB,QAAQ,EAAEnB,SAAS;UACnBoB,YAAY,EAAErB,GAAG,CAACR,IAAI,CAAC8B,UAAU;UACjC1B,UAAU,EAAAyJ,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC/J,KAAK,CAACM,UAAU;YAAE8F,SAAS,EAAEiD,KAAK,CAACjD,SAAS;YAAEC,SAAS,EAAEgD,KAAK,CAAChD;UAAS,EAAE;UAChGlE,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAAwK,aAAA,EAAAC,aAAA;QACV1H,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAxC,MAAA,CAAmF,EAAA2J,aAAA,GAAAxK,CAAC,CAACsD,QAAQ,cAAAkH,aAAA,uBAAVA,aAAA,CAAY9J,IAAI,MAAK6C,SAAS,IAAAkH,aAAA,GAAGzK,CAAC,CAACsD,QAAQ,cAAAmH,aAAA,uBAAVA,aAAA,CAAY/J,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE2G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA3C,QAAQA,CAACmC,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC5J,QAAQ,CAAC;MAAEa,UAAU,EAAE+I;IAAM,CAAC,EAAE,IAAI,CAACa,YAAY,CAAC;EAC3D;EACA,MAAM/C,YAAYA,CAAC3H,CAAC,EAAE;IAClB,IAAI,CAACC,QAAQ,CAAC;MAAEQ,KAAK,EAAET,CAAC,CAACyE,MAAM,CAACtE,KAAK;MAAEwC,OAAO,EAAE;IAAK,CAAC,CAAC;IACvD,IAAI,IAAI,CAACnC,KAAK,CAACE,IAAI,EAAE;MACjB,IAAIA,IAAI,GAAG,IAAI,CAACF,KAAK,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAC1D,IAAIH,KAAK,GAAGT,CAAC,CAACyE,MAAM,CAACtE,KAAK,CAACQ,kBAAkB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAC1D,MAAMhC,UAAU,CAAC,KAAK,2BAAAiC,MAAA,CAA2B,IAAI,CAACL,KAAK,CAACN,gBAAgB,CAACK,IAAI,+CAAAM,MAAA,CAA4C,IAAI,CAACL,KAAK,CAACM,UAAU,CAACC,IAAI,YAAAF,MAAA,CAAS,IAAI,CAACL,KAAK,CAACM,UAAU,CAACE,IAAI,EAAAH,MAAA,CAAG,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC8F,SAAS,aAAA/F,MAAA,CAAa,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC8F,SAAS,eAAA/F,MAAA,CAAa,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC+F,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,IAAM,EAAE,EAAAhG,MAAA,CAAIH,IAAI,IAAID,KAAK,GAAG,yBAAyB,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAG,CAAC,CACxeQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACR,IAAI,CAACU,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIC,CAAC,GAAG;YACJC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,SAAS,EAAEL,OAAO,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS;YACnDC,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,KAAK,EAAEV,OAAO,CAACU,KAAK;YACpBC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,UAAU,EAAEZ,OAAO,CAACY,UAAU;YAC9BC,UAAU,EAAEb,OAAO,CAACa;UACxB,CAAC;UACDhB,SAAS,CAACiB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVoC,OAAO,EAAElB,SAAS;UAClBmB,QAAQ,EAAEnB,SAAS;UACnBoB,YAAY,EAAErB,GAAG,CAACR,IAAI,CAAC8B,UAAU;UACjCG,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE5C,CAAC,IAAK;QAAA,IAAA2K,aAAA,EAAAC,aAAA;QACV7H,OAAO,CAACC,GAAG,CAAChD,CAAC,CAAC;QACd,IAAI,CAACiD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAxC,MAAA,CAAmF,EAAA8J,aAAA,GAAA3K,CAAC,CAACsD,QAAQ,cAAAqH,aAAA,uBAAVA,aAAA,CAAYjK,IAAI,MAAK6C,SAAS,IAAAqH,aAAA,GAAG5K,CAAC,CAACsD,QAAQ,cAAAsH,aAAA,uBAAVA,aAAA,CAAYlK,IAAI,GAAGV,CAAC,CAACwD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE,+CAA+C;QACvDI,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACA6D,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACrH,QAAQ,CAAC;MACVqC,QAAQ,EAAE,IAAI,CAAC9B,KAAK,CAAC6B,OAAO;MAC5BsE,MAAM,EAAE;IACZ,CAAC,CAAC;EACN;EACA;EACAY,SAASA,CAAA,EAAG;IACR,IAAI,CAACtH,QAAQ,CAAC;MACVqC,QAAQ,EAAE,IAAI,CAAC9B,KAAK,CAAC6B,OAAO;MAC5BsE,MAAM,EAAE;IACZ,CAAC,CAAC;EACN;EACAiB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACpH,KAAK,CAACN,gBAAgB,KAAK,IAAI,EAAE;MACtC,IAAI,CAACD,QAAQ,CAAC;QACVuG,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACvD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEI,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAoE,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC5H,QAAQ,CAAC;MACVwG,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7H,QAAQ,CAAC;MACVwG,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAoE,MAAMA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA;IACL,MAAM;MAAEzC;IAAiB,CAAC,GAAG,IAAI,CAACzB,OAAO;IACzC,MAAMmE,kBAAkB,gBACpB7L,OAAA,CAAChB,KAAK,CAACiB,QAAQ;MAAA6L,QAAA,eACX9L,OAAA;QAAK+L,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1D9L,OAAA,CAACT,MAAM;UAACwM,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAAC7D,iBAAkB;UAAA2D,QAAA,GAAE,GAAC,EAAC1M,QAAQ,CAAC6M,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMC,MAAM,GAAG,CACX;MACIzB,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACoN,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,MAAM;MACb0B,MAAM,EAAEnN,QAAQ,CAAC6C,IAAI;MACrBwK,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,WAAW;MAClB0B,MAAM,EAAEnN,QAAQ,CAACwN,SAAS;MAC1BD,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,cAAc;MACrB0B,MAAM,EAAEnN,QAAQ,CAACyN,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,OAAO;MACd0B,MAAM,EAAEnN,QAAQ,CAAC0N,GAAG;MACpBL,IAAI,EAAE,OAAO;MACbE,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,YAAY;MACnB0B,MAAM,EAAEnN,QAAQ,CAAC2N,MAAM;MACvBN,IAAI,EAAE,YAAY;MAClBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,YAAY;MACnB0B,MAAM,EAAEnN,QAAQ,CAAC4N,MAAM;MACvBP,IAAI,EAAE,YAAY;MAClBE,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMM,OAAO,GAAG,CACZ;MACIpC,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAAC8N,eAAe;MAChCT,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,WAAW;MAClB0B,MAAM,EAAEnN,QAAQ,CAAC+N,UAAU;MAC3BV,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACgO,MAAM;MACvBX,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,YAAY;MACnB0B,MAAM,EAAEnN,QAAQ,CAACiO,UAAU;MAC3BX,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,UAAU;MACjB0B,MAAM,EAAEnN,QAAQ,CAACkO,QAAQ;MACzBZ,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMY,OAAO,GAAG,CACZ;MACI1C,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACoO,WAAW;MAC5Bf,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,WAAW;MAClB0B,MAAM,EAAEnN,QAAQ,CAAC+N,UAAU;MAC3BV,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACgO,MAAM;MACvBX,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,YAAY;MACnB0B,MAAM,EAAEnN,QAAQ,CAACiO,UAAU;MAC3BX,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,UAAU;MACjB0B,MAAM,EAAEnN,QAAQ,CAACkO,QAAQ;MACzBZ,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMc,OAAO,GAAG,CACZ;MACI5C,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACsO,SAAS;MAC1BjB,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,WAAW;MAClB0B,MAAM,EAAEnN,QAAQ,CAAC+N,UAAU;MAC3BV,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,QAAQ;MACf0B,MAAM,EAAEnN,QAAQ,CAACgO,MAAM;MACvBX,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,YAAY;MACnB0B,MAAM,EAAEnN,QAAQ,CAACiO,UAAU;MAC3BX,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI9B,KAAK,EAAE,UAAU;MACjB0B,MAAM,EAAEnN,QAAQ,CAACkO,QAAQ;MACzBZ,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,oBACI3M,OAAA;MAAK+L,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACjB9L,OAAA,CAACF,GAAG;QAAAoM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPrM,OAAA,CAACN,KAAK;QAACiO,GAAG,EAAG9I,EAAE,IAAK,IAAI,CAACrB,KAAK,GAAGqB;MAAG;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCrM,OAAA;QAAK+L,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC9L,OAAA;UAAA8L,QAAA,EAAK1M,QAAQ,CAACwO;QAAgB;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNrM,OAAA;QAAK+L,SAAS,EAAC,8BAA8B;QAAAD,QAAA,GACxC,IAAI,CAAC/K,KAAK,CAACN,gBAAgB,KAAK,IAAI,iBACjCT,OAAA;UAAK+L,SAAS,EAAC,2BAA2B;UAAAD,QAAA,eACtC9L,OAAA;YAAI+L,SAAS,EAAC,4DAA4D;YAAAD,QAAA,gBACtE9L,OAAA;cAAI+L,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjE9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7D9L,OAAA;kBAAI+L,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC9L,OAAA;oBAAG+L,SAAS,EAAC,iBAAiB;oBAAC8B,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAACjN,QAAQ,CAACwN,SAAS,EAAC,GAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5HrM,OAAA,CAACX,QAAQ;kBAAC0M,SAAS,EAAC,cAAc;kBAACrL,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,gBAAiB;kBAACiH,OAAO,EAAE,IAAI,CAACD,QAAS;kBAACqG,QAAQ,EAAE,IAAI,CAACxN,gBAAiB;kBAACyN,WAAW,EAAC,MAAM;kBAACC,WAAW,EAAC,qBAAqB;kBAACjI,MAAM;kBAACkI,QAAQ,EAAC;gBAAM;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7M;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACLrM,OAAA;cAAI+L,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjE9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7D9L,OAAA;kBAAI+L,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBAAC9L,OAAA;oBAAG+L,SAAS,EAAC,qBAAqB;oBAAC8B,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAACjN,QAAQ,CAACiO,UAAU,EAAC,IAAE;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5HrM,OAAA,CAACV,QAAQ;kBAACyM,SAAS,EAAC,WAAW;kBAACrL,KAAK,EAAE,IAAI,CAACK,KAAK,CAACE,IAAK;kBAAC6M,QAAQ,EAAGvN,CAAC,IAAK,IAAI,CAACC,QAAQ,CAAC;oBAAES,IAAI,EAAEV,CAAC,CAACyE,MAAM,CAACtE;kBAAM,CAAC,CAAE;kBAACwN,UAAU,EAAC,UAAU;kBAACF,WAAW,EAAE,IAAI7I,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAE;kBAACiN,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjMrM,OAAA;kBAAI+L,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBAAC9L,OAAA;oBAAG+L,SAAS,EAAC,qBAAqB;oBAAC8B,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAACjN,QAAQ,CAACkO,QAAQ,EAAC,IAAE;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1HrM,OAAA,CAACV,QAAQ;kBAACyM,SAAS,EAAC,WAAW;kBAACrL,KAAK,EAAE,IAAI,CAACK,KAAK,CAACC,KAAM;kBAAC8M,QAAQ,EAAGvN,CAAC,IAAK,IAAI,CAAC2H,YAAY,CAAC3H,CAAC,CAAE;kBAAC2N,UAAU,EAAC,UAAU;kBAACF,WAAW,EAAE,IAAI7I,IAAI,CAAC,CAAC,CAACjE,kBAAkB,CAAC,CAAE;kBAACkN,QAAQ,EAAE,IAAI,CAACrN,KAAK,CAACE,IAAI,GAAG,KAAK,GAAG,IAAK;kBAACkN,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAET,IAAI,CAACtL,KAAK,CAAC6B,OAAO,iBACf5C,OAAA;UAAK+L,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjB9L,OAAA;YAAK+L,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC1B9L,OAAA,CAACR,OAAO;cAACuM,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC/B9L,OAAA,CAACP,QAAQ;gBAAC8M,MAAM,EAAEnN,QAAQ,CAACiP,OAAQ;gBAACC,QAAQ,EAAC,sBAAsB;gBAAAxC,QAAA,GAC9D,CAAC,EAAAT,qBAAA,OAAI,CAACtK,KAAK,CAAC0E,SAAS,cAAA4F,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsB1F,OAAO,cAAA2F,sBAAA,uBAA7BA,sBAAA,CAA+BxF,MAAM,IAAG,CAAC,IAAI,EAAAyF,sBAAA,OAAI,CAACxK,KAAK,CAAC0E,SAAS,cAAA8F,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsB7F,SAAS,cAAA8F,sBAAA,uBAA/BA,sBAAA,CAAiC1F,MAAM,IAAG,CAAC,kBACtF9F,OAAA,CAAAE,SAAA;kBAAA4L,QAAA,gBACI9L,OAAA,CAAAE,SAAA;oBAAA4L,QAAA,gBACI9L,OAAA;sBAAA8L,QAAA,EAAK1M,QAAQ,CAACmP;oBAAS;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAC5B,IAAI,CAACtL,KAAK,CAAC0E,SAAS,CAACE,OAAO,CAACG,MAAM,GAAG,CAAC,iBACpC9F,OAAA,CAAAE,SAAA;sBAAA4L,QAAA,gBACI9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACoP;sBAAO;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3BrM,OAAA;wBAAK+L,SAAS,EAAC,UAAU;wBAAAD,QAAA,EACpB,IAAI,CAAC/K,KAAK,CAAC0E,SAAS,CAACE,OAAO,CAACf,GAAG,CAAC,CAACC,EAAE,EAAE4J,GAAG,KAAK;0BAC3C,oBACIzO,OAAA,CAAChB,KAAK,CAACiB,QAAQ;4BAAA6L,QAAA,eACX9L,OAAA;8BAAK+L,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eACnB9L,OAAA,CAACL,KAAK;gCAACsC,IAAI,EAAC,MAAM;gCAAChB,IAAI,EAAE4D,EAAG;gCAAC6C,OAAO,EAAEyB;8BAAiB;gCAAA+C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzD;0BAAC,GAHWoC,GAAG;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAIR,CAAC;wBAEzB,CAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAED,CAAC;oBAAA,eACR,CAAC;kBAAA,eAET,CAAC,eACHrM,OAAA,CAAAE,SAAA;oBAAA4L,QAAA,EACK,IAAI,CAAC/K,KAAK,CAAC0E,SAAS,CAACC,SAAS,CAACI,MAAM,GAAG,CAAC,iBACtC9F,OAAA,CAAAE,SAAA;sBAAA4L,QAAA,gBACI9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACsP;sBAAO;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3BrM,OAAA;wBAAK+L,SAAS,EAAC,KAAK;wBAAAD,QAAA,EACf,IAAI,CAAC/K,KAAK,CAAC0E,SAAS,CAACC,SAAS,CAACd,GAAG,CAAC,CAACC,EAAE,EAAE4J,GAAG,KAAK;0BAC7C,oBACIzO,OAAA,CAAChB,KAAK,CAACiB,QAAQ;4BAAA6L,QAAA,eACX9L,OAAA;8BAAK+L,SAAS,EAAC,iBAAiB;8BAAAD,QAAA,eAC5B9L,OAAA,CAACL,KAAK;gCAACsC,IAAI,EAAC,MAAM;gCAAChB,IAAI,EAAE4D,EAAG;gCAAC6C,OAAO,EAAEyB;8BAAiB;gCAAA+C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzD;0BAAC,GAHWoC,GAAG;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAIR,CAAC;wBAEzB,CAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAED,CAAC;oBAAA,eACR;kBAAC,gBAET,CAAC;gBAAA,eACL,CAAC,EAEN,CAAC,EAAAZ,iBAAA,OAAI,CAAC1K,KAAK,CAAC6E,KAAK,cAAA6F,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkB9F,OAAO,cAAA+F,qBAAA,uBAAzBA,qBAAA,CAA2B5F,MAAM,IAAG,CAAC,IAAI,EAAA6F,kBAAA,OAAI,CAAC5K,KAAK,CAAC6E,KAAK,cAAA+F,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBjG,SAAS,cAAAkG,qBAAA,uBAA3BA,qBAAA,CAA6B9F,MAAM,IAAG,CAAC,kBAC9E9F,OAAA,CAAAE,SAAA;kBAAA4L,QAAA,gBACI9L,OAAA,CAAAE,SAAA;oBAAA4L,QAAA,gBACI9L,OAAA;sBAAA8L,QAAA,EAAK1M,QAAQ,CAACuP;oBAAK;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACxB,IAAI,CAACtL,KAAK,CAAC6E,KAAK,CAACD,OAAO,CAACG,MAAM,GAAG,CAAC,iBAChC9F,OAAA,CAAAE,SAAA;sBAAA4L,QAAA,gBACI9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACoP;sBAAO;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3BrM,OAAA;wBAAK+L,SAAS,EAAC,UAAU;wBAAAD,QAAA,EACpB,IAAI,CAAC/K,KAAK,CAAC6E,KAAK,CAACD,OAAO,CAACf,GAAG,CAAC,CAACC,EAAE,EAAE4J,GAAG,KAAK;0BACvC,oBACIzO,OAAA,CAAChB,KAAK,CAACiB,QAAQ;4BAAA6L,QAAA,eACX9L,OAAA;8BAAK+L,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eACnB9L,OAAA,CAACL,KAAK;gCAACsC,IAAI,EAAC,MAAM;gCAAChB,IAAI,EAAE4D,EAAG;gCAAC6C,OAAO,EAAEyB;8BAAiB;gCAAA+C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzD;0BAAC,GAHWoC,GAAG;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAIR,CAAC;wBAEzB,CAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAED,CAAC;oBAAA,eACR,CAAC;kBAAA,eAET,CAAC,EACF,IAAI,CAACtL,KAAK,CAAC6E,KAAK,CAACF,SAAS,CAACI,MAAM,GAAG,CAAC,iBAClC9F,OAAA,CAAAE,SAAA;oBAAA4L,QAAA,gBACI9L,OAAA;sBAAA8L,QAAA,EAAK1M,QAAQ,CAACsP;oBAAO;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3BrM,OAAA;sBAAK+L,SAAS,EAAC,KAAK;sBAAAD,QAAA,EACf,IAAI,CAAC/K,KAAK,CAAC6E,KAAK,CAACF,SAAS,CAACd,GAAG,CAAC,CAACC,EAAE,EAAE4J,GAAG,KAAK;wBACzC,oBACIzO,OAAA,CAAChB,KAAK,CAACiB,QAAQ;0BAAA6L,QAAA,eACX9L,OAAA;4BAAK+L,SAAS,EAAC,iBAAiB;4BAAAD,QAAA,eAC5B9L,OAAA,CAACL,KAAK;8BAACsC,IAAI,EAAC,MAAM;8BAAChB,IAAI,EAAE4D,EAAG;8BAAC6C,OAAO,EAAEyB;4BAAiB;8BAAA+C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzD;wBAAC,GAHWoC,GAAG;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAIR,CAAC;sBAEzB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAED,CAAC;kBAAA,eACR,CAAC;gBAAA,eAET,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAED,CAAC,eACXrM,OAAA,CAACP,QAAQ;gBAAC8M,MAAM,EAAEnN,QAAQ,CAACwP,QAAS;gBAACN,QAAQ,EAAC,sBAAsB;gBAAAxC,QAAA,gBAChE9L,OAAA;kBAAK+L,SAAS,EAAC,KAAK;kBAAAD,QAAA,gBAChB9L,OAAA;oBAAK+L,SAAS,EAAC,OAAO;oBAAAD,QAAA,eAClB9L,OAAA;sBAAK+L,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,gBAC3B9L,OAAA;wBAAK+L,SAAS,EAAC,qEAAqE;wBAAAD,QAAA,eAChF9L,OAAA;0BAAA8L,QAAA,EAAI1M,QAAQ,CAACmP;wBAAS;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACNrM,OAAA;wBAAK+L,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNrM,OAAA;oBAAK+L,SAAS,EAAC,OAAO;oBAAAD,QAAA,eAClB9L,OAAA;sBAAK+L,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,gBAC3B9L,OAAA;wBAAK+L,SAAS,EAAC,qEAAqE;wBAAAD,QAAA,eAChF9L,OAAA;0BAAA8L,QAAA,EAAI1M,QAAQ,CAACyP;wBAAQ;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACNrM,OAAA;wBAAK+L,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNrM,OAAA;oBAAK+L,SAAS,EAAC,OAAO;oBAAAD,QAAA,eAClB9L,OAAA;sBAAK+L,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,gBAC3B9L,OAAA;wBAAK+L,SAAS,EAAC,qEAAqE;wBAAAD,QAAA,eAChF9L,OAAA;0BAAA8L,QAAA,EAAI1M,QAAQ,CAAC0P;wBAAa;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC,eACNrM,OAAA;wBAAK+L,SAAS,EAAC,4DAA4D;wBAAAD,QAAA,EAAC;sBAE5E;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNrM,OAAA;kBAAK+L,SAAS,EAAC,KAAK;kBAAAD,QAAA,gBAChB9L,OAAA;oBAAK+L,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,wCAAwC;sBAAAD,QAAA,gBACnD9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACmP;sBAAS;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAE7BrM,OAAA,CAACH,eAAe;wBACZ8N,GAAG,EAAG9I,EAAE,IAAK,IAAI,CAACkK,EAAE,GAAGlK,EAAG;wBAC1BnE,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC2F,QAAS;wBAC3BxD,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;wBAC5BoJ,MAAM,EAAEW,OAAQ;wBAChB+B,OAAO,EAAC,IAAI;wBACZC,IAAI;wBACJC,aAAa,EAAC,KAAK;wBACnBC,UAAU,EAAE,IAAK;wBACjBC,UAAU;wBACVC,SAAS,EAAC;sBAA2B;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNrM,OAAA;oBAAK+L,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,wCAAwC;sBAAAD,QAAA,gBACnD9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACuP;sBAAK;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEzBrM,OAAA,CAACH,eAAe;wBACZ8N,GAAG,EAAG9I,EAAE,IAAK,IAAI,CAACkK,EAAE,GAAGlK,EAAG;wBAC1BnE,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC4F,QAAS;wBAC3BzD,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;wBAC5BoJ,MAAM,EAAEiB,OAAQ;wBAChByB,OAAO,EAAC,IAAI;wBACZC,IAAI;wBACJC,aAAa,EAAC,KAAK;wBACnBC,UAAU,EAAE,IAAK;wBACjBC,UAAU;wBACVC,SAAS,EAAC;sBAAuB;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNrM,OAAA;oBAAK+L,SAAS,EAAC,QAAQ;oBAAAD,QAAA,eACnB9L,OAAA;sBAAK+L,SAAS,EAAC,wCAAwC;sBAAAD,QAAA,gBACnD9L,OAAA;wBAAA8L,QAAA,EAAK1M,QAAQ,CAACkQ;sBAAS;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAE7BrM,OAAA,CAACH,eAAe;wBACZ8N,GAAG,EAAG9I,EAAE,IAAK,IAAI,CAACkK,EAAE,GAAGlK,EAAG;wBAC1BnE,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC6F,QAAS;wBAC3B1D,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;wBAC5BoJ,MAAM,EAAEmB,OAAQ;wBAChBuB,OAAO,EAAC,IAAI;wBACZC,IAAI;wBACJC,aAAa,EAAC,KAAK;wBACnBC,UAAU,EAAE,IAAK;wBACjBC,UAAU;wBACVC,SAAS,EAAC;sBAAiB;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACXrM,OAAA,CAACP,QAAQ;gBAACoO,KAAK,EAAE;kBAAE0B,OAAO,EAAE;gBAAM,CAAE;gBAAChD,MAAM,EAAEnN,QAAQ,CAACoQ,SAAU;gBAAClB,QAAQ,EAAC,qBAAqB;gBAAAxC,QAAA,eAC3F9L,OAAA;kBAAK+L,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,eAE9C9L,OAAA,CAACH,eAAe;oBACZ8N,GAAG,EAAG9I,EAAE,IAAK,IAAI,CAACkK,EAAE,GAAGlK,EAAG;oBAC1BnE,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC6B,OAAQ;oBAC1BM,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;oBAC5BoJ,MAAM,EAAEA,MAAO;oBACf0C,OAAO,EAAC,IAAI;oBACZC,IAAI;oBACJC,aAAa,EAAC,KAAK;oBACnBO,SAAS;oBACT1H,MAAM,EAAE,IAAI,CAACA,MAAO;oBACpB/E,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACM,UAAU,CAAC2B,KAAM;oBACnCF,YAAY,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,YAAa;oBACtCxB,IAAI,EAAE,IAAI,CAACP,KAAK,CAACM,UAAU,CAACC,IAAK;oBACjCoO,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;oBACjCP,UAAU,EAAE,IAAK;oBACjBQ,mBAAmB,EAAE,IAAK;oBAC1B3H,MAAM,EAAE,IAAI,CAACA,MAAO;oBACpBb,SAAS,EAAE,IAAI,CAACpG,KAAK,CAACM,UAAU,CAAC8F,SAAU;oBAC3CC,SAAS,EAAE,IAAI,CAACrG,KAAK,CAACM,UAAU,CAAC+F,SAAU;oBAC3Ca,QAAQ,EAAE,IAAI,CAACA,QAAS;oBACxBZ,OAAO,EAAE,IAAI,CAACtG,KAAK,CAACM,UAAU,CAACgG,OAAQ;oBACvCuI,gBAAgB,EAAE,KAAM;oBACxBP,SAAS,EAAC;kBAAS;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET,CAAC,eACNrM,OAAA,CAACJ,MAAM;QAACiQ,OAAO,EAAE,IAAI,CAAC9O,KAAK,CAACgG,YAAa;QAACwF,MAAM,EAAEnN,QAAQ,CAAC0Q,iBAAkB;QAACC,KAAK;QAAChE,SAAS,EAAC,kBAAkB;QAACiE,MAAM,EAAE,IAAI,CAAC7H,iBAAkB;QAAC8H,MAAM,EAAEpE,kBAAmB;QAAAC,QAAA,GACvK,IAAI,CAAC/K,KAAK,CAACkG,SAAS,iBACjBjH,OAAA,CAACd,UAAU;UAACgR,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACnL,MAAM,EAAC;QAAS;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhGrM,OAAA;UAAK+L,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3D9L,OAAA;YAAI+L,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAAC9L,OAAA;cAAG+L,SAAS,EAAC,iBAAiB;cAAC8B,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACjN,QAAQ,CAACwN,SAAS;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHrM,OAAA;YAAAkM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrM,OAAA,CAACX,QAAQ;YAAC0M,SAAS,EAAC,cAAc;YAACrL,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,gBAAiB;YAACiH,OAAO,EAAE,IAAI,CAACD,QAAS;YAACqG,QAAQ,EAAE,IAAI,CAACxN,gBAAiB;YAACyN,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACjI,MAAM;YAACkI,QAAQ,EAAC;UAAM;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAelM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}