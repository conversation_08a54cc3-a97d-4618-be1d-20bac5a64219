{"ast": null, "code": "/*!\n * Chart.js v3.7.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || (args => Array.prototype.slice.call(args));\n  let ticking = false;\n  let args = [];\n  return function () {\n    for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n      rest[_key] = arguments[_key];\n    }\n    args = updateArgs(rest);\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\nfunction noop() {}\nconst uid = function () {\n  let id = 0;\n  return function () {\n    return id++;\n  };\n}();\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.substr(0, 7) === '[object' && type.substr(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\nconst isNumberFinite = value => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n    if (!isObject(source)) {\n      continue;\n    }\n    const keys = Object.keys(source);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\nconst emptyString = '';\nconst dot = '.';\nfunction indexOfDotOrLength(key, start) {\n  const idx = key.indexOf(dot, start);\n  return idx === -1 ? key.length : idx;\n}\nfunction resolveObjectKey(obj, key) {\n  if (key === emptyString) {\n    return obj;\n  }\n  let pos = 0;\n  let idx = indexOfDotOrLength(key, pos);\n  while (obj && idx > pos) {\n    obj = obj[key.substr(pos, idx - pos)];\n    pos = idx + 1;\n    idx = indexOfDotOrLength(key, pos);\n  }\n  return obj;\n}\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU;\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\nfunction _isBetween(value, start, end) {\n  let epsilon = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1e-6;\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\n\n/*!\n * @kurkle/color v0.1.9\n * https://github.com/kurkle/color#readme\n * (c) 2020 Jukka Kurkela\n * Released under the MIT License\n */\nconst map = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15,\n  a: 10,\n  b: 11,\n  c: 12,\n  d: 13,\n  e: 14,\n  f: 15\n};\nconst hex = '0123456789ABCDEF';\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => (b & 0xF0) >> 4 === (b & 0xF);\nfunction isShort(v) {\n  return eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\n}\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map[str[1]] * 17,\n        g: 255 & map[str[2]] * 17,\n        b: 255 & map[str[3]] * 17,\n        a: len === 5 ? map[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map[str[1]] << 4 | map[str[2]],\n        g: map[str[3]] << 4 | map[str[4]],\n        b: map[str[5]] << 4 | map[str[6]],\n        a: len === 9 ? map[str[7]] << 4 | map[str[8]] : 255\n      };\n    }\n  }\n  return ret;\n}\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v ? '#' + f(v.r) + f(v.g) + f(v.b) + (v.a < 255 ? f(v.a) : '') : v;\n}\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = 255 & (m[8] ? p2b(v) : v * 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : r);\n  g = 255 & (m[4] ? p2b(g) : g);\n  b = 255 & (m[6] ? p2b(b) : b);\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (v.a < 255 ? \"rgba(\".concat(v.r, \", \").concat(v.g, \", \").concat(v.b, \", \").concat(b2n(v.a), \")\") : \"rgb(\".concat(v.r, \", \").concat(v.g, \", \").concat(v.b, \")\"));\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = function (n) {\n    let k = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (n + h / 30) % 12;\n    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  };\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = function (n) {\n    let k = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (n + h / 60) % 6;\n    return v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  };\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = max === r ? (g - b) / d + (g < b ? 6 : 0) : max === g ? (b - r) / d + 2 : (r - g) / d + 4;\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (Array.isArray(a) ? f(a[0], a[1], a[2]) : f(a, b, c)).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255 ? \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(b2n(v.a), \")\") : \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\");\n}\nconst map$1 = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names);\n  const tkeys = Object.keys(map$1);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map$1[k]);\n    }\n    k = parseInt(names[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\nlet names$1;\nfunction nameParse(str) {\n  if (!names$1) {\n    names$1 = unpack();\n    names$1.transparent = [0, 0, 0, 0];\n  }\n  const a = names$1[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 255\n  };\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {\n        r: input[0],\n        g: input[1],\n        b: input[2],\n        a: 255\n      };\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    });\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : this._rgb;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : this._rgb;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : this._rgb;\n  }\n  mix(color, weight) {\n    const me = this;\n    if (color) {\n      const c1 = me.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      me.rgb = c1;\n    }\n    return me;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\nfunction index_esm(input) {\n  return new Color(input);\n}\nconst isPatternOrGradient = value => value instanceof CanvasGradient || value instanceof CanvasPattern;\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n}\nvar defaults = new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n});\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  let type, xOffset, yOffset, size, cornerRadius;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    default:\n      ctx.arc(x, y, radius, 0, TAU);\n      ctx.closePath();\n      break;\n    case 'triangle':\n      ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        ctx.rect(x - size, y - size, 2 * size, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    case 'rectRot':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    case 'cross':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'star':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      rad += QUARTER_PI;\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'line':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction renderText(ctx, text, x, y, font) {\n  let opts = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n  ctx.restore();\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\nconst _lookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] < value);\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value() {\n        for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n          args[_key3] = arguments[_key3];\n        }\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n  if (set.size === ilen) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction _createResolver(scopes) {\n  let prefixes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [''];\n  let rootScopes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : scopes;\n  let fallback = arguments.length > 3 ? arguments[3] : undefined;\n  let getTarget = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : () => scopes[0];\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n  });\n}\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n  });\n}\nfunction _descriptors(proxy) {\n  let defaults = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    scriptable: true,\n    indexable: true\n  };\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop];\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  value = value(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n  return target;\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (defined(value)) {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK) {\n  let indexAxis = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'x';\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[\"cp1\".concat(indexAxis)] = iPixel - delta;\n      pointCurrent[\"cp1\".concat(valueAxis)] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[\"cp2\".concat(indexAxis)] = iPixel + delta;\n      pointCurrent[\"cp2\".concat(valueAxis)] = vPixel + delta * mK[i];\n    }\n  }\n}\nfunction splineCurveMonotone(points) {\n  let indexAxis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => window.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\nfunction getCanvasPosition(evt, canvas) {\n  const e = evt.native || evt;\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\nfunction getRelativePosition(evt, chart) {\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n  return {\n    width,\n    height\n  };\n}\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = \"\".concat(chart.height, \"px\");\n    canvas.style.width = \"\".concat(chart.width, \"px\");\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {}\n  return passiveSupported;\n}();\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment(_ref) {\n  let {\n    start,\n    end,\n    count,\n    loop,\n    style\n  } = _ref;\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\nexport { _toLeftRightCenter as $, _rlookupByKey as A, getAngleFromPoint as B, toPadding as C, each as D, getMaximumSize as E, _getParentNode as F, readUsedSize as G, HALF_PI as H, throttled as I, supportsEventListenerOptions as J, _isDomSupported as K, log10 as L, _factorize as M, finiteOrDefault as N, callback as O, PI as P, _addGrace as Q, toDegrees as R, _measureText as S, TAU as T, _int16Range as U, _alignPixel as V, clipArea as W, renderText as X, unclipArea as Y, toFont as Z, _arrayUnique as _, resolve as a, _angleDiff as a$, _alignStartEnd as a0, overrides as a1, merge as a2, _capitalize as a3, descriptors as a4, isFunction as a5, _attachContext as a6, _createResolver as a7, _descriptors as a8, mergeIf as a9, restoreTextDirection as aA, noop as aB, distanceBetweenPoints as aC, _setMinAndMaxByKey as aD, niceNum as aE, almostWhole as aF, almostEquals as aG, _decimalPlaces as aH, _longestText as aI, _filterBetween as aJ, _lookup as aK, getHoverColor as aL, clone$1 as aM, _merger as aN, _mergerIf as aO, _deprecated as aP, toFontString as aQ, splineCurve as aR, splineCurveMonotone as aS, getStyle as aT, fontString as aU, toLineHeight as aV, PITAU as aW, INFINITY as aX, RAD_PER_DEG as aY, QUARTER_PI as aZ, TWO_THIRDS_PI as a_, uid as aa, debounce as ab, retinaScale as ac, clearCanvas as ad, setsEqual as ae, _elementsEqual as af, _isClickEvent as ag, _isBetween as ah, _readValueToProps as ai, _updateBezierControlPoints as aj, _computeSegments as ak, _boundSegments as al, _steppedInterpolation as am, _bezierInterpolation as an, _pointInLine as ao, _steppedLineTo as ap, _bezierCurveTo as aq, drawPoint as ar, addRoundedRectPath as as, toTRBL as at, toTRBLCorners as au, _boundSegment as av, _normalizeAngle as aw, getRtlAdapter as ax, overrideTextDirection as ay, _textX as az, isArray as b, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, isNumber as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _limitValue as w, _lookupByKey as x, getRelativePosition as y, _isPointInArea as z };", "map": {"version": 3, "names": ["fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "callback", "requestAnimationFrame", "throttled", "fn", "thisArg", "updateFn", "updateArgs", "args", "Array", "prototype", "slice", "call", "ticking", "_len", "arguments", "length", "rest", "_key", "apply", "debounce", "delay", "timeout", "_len2", "_key2", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "start", "end", "_textX", "left", "right", "rtl", "check", "noop", "uid", "id", "isNullOrUndef", "value", "isArray", "type", "Object", "toString", "substr", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "each", "loopable", "reverse", "i", "len", "keys", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone$1", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "current", "undefined", "console", "warn", "emptyString", "dot", "indexOfDotOrLength", "idx", "resolveObjectKey", "obj", "pos", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "niceNum", "range", "roundedRange", "round", "almostEquals", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "push", "sort", "pop", "isNumber", "n", "isNaN", "x", "y", "epsilon", "abs", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "A", "B", "C", "D", "E", "F", "c", "f", "hex", "h1", "h2", "eq", "isShort", "v", "r", "g", "hexParse", "ret", "hexString", "lim", "l", "h", "p2b", "n2b", "b2n", "n2p", "RGB_RE", "rgbParse", "exec", "rgbString", "concat", "HUE_RE", "hsl2rgbn", "hsv2rgbn", "hwb2rgbn", "w", "rgb", "rgb2hsl", "calln", "hsl2rgb", "hwb2rgb", "hsv2rgb", "hue", "hue<PERSON><PERSON><PERSON>", "p1", "p2", "rotate", "deg", "hslString", "map$1", "Z", "Y", "X", "W", "V", "U", "T", "S", "R", "Q", "P", "O", "N", "M", "L", "K", "G", "H", "I", "J", "names", "OiceXe", "antiquewEte", "aqua", "aquamarRe", "azuY", "beige", "bisque", "black", "blan<PERSON>ed<PERSON><PERSON>", "Xe", "XeviTet", "bPwn", "burlywood", "caMtXe", "<PERSON><PERSON><PERSON><PERSON>", "KocTate", "cSO", "cSnflowerXe", "cSnsilk", "crimson", "cyan", "xXe", "xcyan", "xgTMnPd", "xWay", "xgYF", "xgYy", "xkhaki", "xmagFta", "xTivegYF", "xSange", "xScEd", "xYd", "xsOmon", "xsHgYF", "xUXe", "xUWay", "xUgYy", "xQe", "xviTet", "dAppRk", "dApskyXe", "dim<PERSON>ay", "dimgYy", "dodgerXe", "fiYbrick", "flSOwEte", "foYstWAn", "fuKsia", "gaRsbSo", "ghostwEte", "gTd", "gTMnPd", "Way", "gYF", "gYFLw", "gYy", "honeyMw", "hotpRk", "RdianYd", "Rdigo", "ivSy", "khaki", "lavFMr", "lavFMrXsh", "lawngYF", "NmoncEffon", "ZXe", "ZcSO", "<PERSON><PERSON><PERSON>", "ZgTMnPdLw", "ZWay", "ZgYF", "ZgYy", "ZpRk", "ZsOmon", "ZsHgYF", "ZskyXe", "ZUWay", "ZUgYy", "ZstAlXe", "ZLw", "lime", "limegYF", "lRF", "magFta", "ma<PERSON><PERSON>", "VaquamarRe", "VXe", "VScEd", "VpurpN", "VsHgYF", "VUXe", "VsprRggYF", "VQe", "VviTetYd", "midnightXe", "mRtcYam", "misty<PERSON>e", "moccasR", "navajowEte", "navy", "Tdlace", "Tive", "TivedBb", "<PERSON><PERSON>", "SangeYd", "ScEd", "pOegTMnPd", "pOegYF", "pOeQe", "pOeviTetYd", "papayawEp", "pHKpuff", "peru", "pRk", "plum", "powMrXe", "purpN", "YbeccapurpN", "Yd", "Psybrown", "PyOXe", "saddNbPwn", "sOmon", "sandybPwn", "sHgYF", "sHshell", "siFna", "silver", "skyXe", "UXe", "UWay", "UgYy", "snow", "sprRggYF", "stAlXe", "tan", "teO", "tEstN", "tomato", "Qe", "viTet", "JHt", "wEte", "wEtesmoke", "Lw", "LwgYF", "unpack", "unpacked", "tkeys", "j", "ok", "nk", "replace", "parseInt", "names$1", "nameParse", "transparent", "toLowerCase", "modHSL", "ratio", "tmp", "clone", "proto", "assign", "fromObject", "input", "functionParse", "Color", "constructor", "_rgb", "_valid", "valid", "mix", "color", "weight", "me", "c1", "c2", "w2", "w1", "alpha", "clearer", "greyscale", "val", "opaquer", "negate", "lighten", "darken", "saturate", "desaturate", "index_esm", "isPatternOrGradient", "CanvasGradient", "CanvasPattern", "getHoverColor", "overrides", "descriptors", "getScope$1", "node", "split", "set", "root", "values", "De<PERSON>ults", "_descriptors", "animation", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "chart", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "describe", "get", "override", "route", "name", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "enumerable", "local", "defaults", "_scriptable", "startsWith", "_indexable", "_fallback", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "width", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "jlen", "thing", "nestedThing", "restore", "gcLen", "splice", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "xOffset", "yOffset", "cornerRadius", "pointStyle", "rotation", "radius", "rad", "translate", "drawImage", "beginPath", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "top", "bottom", "clipArea", "clip", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "renderText", "text", "opts", "lines", "strokeWidth", "strokeColor", "line", "setRenderOpts", "strokeStyle", "lineWidth", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "decorateText", "translation", "fillStyle", "textAlign", "textBaseline", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "decorationWidth", "addRoundedRectPath", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "RegExp", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "grace", "beginAtZero", "change", "keepZero", "add", "createContext", "parentContext", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "_rlookupByKey", "_filterBetween", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "for<PERSON>ach", "method", "base", "_len3", "_key3", "res", "object", "unlistenArrayEvents", "stub", "_arrayUnique", "items", "Set", "from", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "includes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "Error", "join", "delete", "createSubResolver", "arr", "filter", "resolver", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "EPSILON", "getPoint", "points", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "delta", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "loop", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "useOffsetPos", "shadowRoot", "getCanvasPosition", "evt", "native", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "Intl", "NumberFormat", "formatNumber", "num", "format", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "_ref", "count", "getSegment", "segment", "bounds", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "last", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "$", "_", "a$", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aA", "aB", "aC", "aD", "aE", "aF", "aG", "aH", "aI", "aJ", "aK", "aL", "aM", "aN", "aO", "aP", "aQ", "aR", "aS", "aT", "aU", "aV", "aW", "aX", "aY", "aZ", "a_", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az", "o", "q", "u", "z"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/chart.js/dist/chunks/helpers.segment.js"], "sourcesContent": ["/*!\n * Chart.js v3.7.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\nconst requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || ((args) => Array.prototype.slice.call(args));\n  let ticking = false;\n  let args = [];\n  return function(...rest) {\n    args = updateArgs(rest);\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function(...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\nconst _toLeftRightCenter = (align) => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\nfunction noop() {}\nconst uid = (function() {\n  let id = 0;\n  return function() {\n    return id++;\n  };\n}());\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.substr(0, 7) === '[object' && type.substr(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\nconst isNumberFinite = (value) => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : value / dimension;\nconst toDimension = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n    if (!isObject(source)) {\n      continue;\n    }\n    const keys = Object.keys(source);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  return merge(target, source, {merger: _mergerIf});\n}\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n\t\t\t'\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\nconst emptyString = '';\nconst dot = '.';\nfunction indexOfDotOrLength(key, start) {\n  const idx = key.indexOf(dot, start);\n  return idx === -1 ? key.length : idx;\n}\nfunction resolveObjectKey(obj, key) {\n  if (key === emptyString) {\n    return obj;\n  }\n  let pos = 0;\n  let idx = indexOfDotOrLength(key, pos);\n  while (obj && idx > pos) {\n    obj = obj[key.substr(pos, idx - pos)];\n    pos = idx + 1;\n    idx = indexOfDotOrLength(key, pos);\n  }\n  return obj;\n}\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = (value) => typeof value !== 'undefined';\nconst isFunction = (value) => typeof value === 'function';\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < (-0.5 * PI)) {\n    angle += TAU;\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nconst atEdge = (t) => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n  easeInOutBounce: t => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n};\n\n/*!\n * @kurkle/color v0.1.9\n * https://github.com/kurkle/color#readme\n * (c) 2020 Jukka Kurkela\n * Released under the MIT License\n */\nconst map = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = '0123456789ABCDEF';\nconst h1 = (b) => hex[b & 0xF];\nconst h2 = (b) => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = (b) => (((b & 0xF0) >> 4) === (b & 0xF));\nfunction isShort(v) {\n\treturn eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\n}\nfunction hexParse(str) {\n\tvar len = str.length;\n\tvar ret;\n\tif (str[0] === '#') {\n\t\tif (len === 4 || len === 5) {\n\t\t\tret = {\n\t\t\t\tr: 255 & map[str[1]] * 17,\n\t\t\t\tg: 255 & map[str[2]] * 17,\n\t\t\t\tb: 255 & map[str[3]] * 17,\n\t\t\t\ta: len === 5 ? map[str[4]] * 17 : 255\n\t\t\t};\n\t\t} else if (len === 7 || len === 9) {\n\t\t\tret = {\n\t\t\t\tr: map[str[1]] << 4 | map[str[2]],\n\t\t\t\tg: map[str[3]] << 4 | map[str[4]],\n\t\t\t\tb: map[str[5]] << 4 | map[str[6]],\n\t\t\t\ta: len === 9 ? (map[str[7]] << 4 | map[str[8]]) : 255\n\t\t\t};\n\t\t}\n\t}\n\treturn ret;\n}\nfunction hexString(v) {\n\tvar f = isShort(v) ? h1 : h2;\n\treturn v\n\t\t? '#' + f(v.r) + f(v.g) + f(v.b) + (v.a < 255 ? f(v.a) : '')\n\t\t: v;\n}\nfunction round(v) {\n\treturn v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n\treturn lim(round(v * 2.55), 0, 255);\n}\nfunction n2b(v) {\n\treturn lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n\treturn lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n\treturn lim(round(v * 100), 0, 100);\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n\tconst m = RGB_RE.exec(str);\n\tlet a = 255;\n\tlet r, g, b;\n\tif (!m) {\n\t\treturn;\n\t}\n\tif (m[7] !== r) {\n\t\tconst v = +m[7];\n\t\ta = 255 & (m[8] ? p2b(v) : v * 255);\n\t}\n\tr = +m[1];\n\tg = +m[3];\n\tb = +m[5];\n\tr = 255 & (m[2] ? p2b(r) : r);\n\tg = 255 & (m[4] ? p2b(g) : g);\n\tb = 255 & (m[6] ? p2b(b) : b);\n\treturn {\n\t\tr: r,\n\t\tg: g,\n\t\tb: b,\n\t\ta: a\n\t};\n}\nfunction rgbString(v) {\n\treturn v && (\n\t\tv.a < 255\n\t\t\t? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n\t\t\t: `rgb(${v.r}, ${v.g}, ${v.b})`\n\t);\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n\tconst a = s * Math.min(l, 1 - l);\n\tconst f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n\treturn [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n\tconst f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n\treturn [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n\tconst rgb = hsl2rgbn(h, 1, 0.5);\n\tlet i;\n\tif (w + b > 1) {\n\t\ti = 1 / (w + b);\n\t\tw *= i;\n\t\tb *= i;\n\t}\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] *= 1 - w - b;\n\t\trgb[i] += w;\n\t}\n\treturn rgb;\n}\nfunction rgb2hsl(v) {\n\tconst range = 255;\n\tconst r = v.r / range;\n\tconst g = v.g / range;\n\tconst b = v.b / range;\n\tconst max = Math.max(r, g, b);\n\tconst min = Math.min(r, g, b);\n\tconst l = (max + min) / 2;\n\tlet h, s, d;\n\tif (max !== min) {\n\t\td = max - min;\n\t\ts = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n\t\th = max === r\n\t\t\t? ((g - b) / d) + (g < b ? 6 : 0)\n\t\t\t: max === g\n\t\t\t\t? (b - r) / d + 2\n\t\t\t\t: (r - g) / d + 4;\n\t\th = h * 60 + 0.5;\n\t}\n\treturn [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n\treturn (\n\t\tArray.isArray(a)\n\t\t\t? f(a[0], a[1], a[2])\n\t\t\t: f(a, b, c)\n\t).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n\treturn calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n\treturn calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n\treturn calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n\treturn (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n\tconst m = HUE_RE.exec(str);\n\tlet a = 255;\n\tlet v;\n\tif (!m) {\n\t\treturn;\n\t}\n\tif (m[5] !== v) {\n\t\ta = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n\t}\n\tconst h = hue(+m[2]);\n\tconst p1 = +m[3] / 100;\n\tconst p2 = +m[4] / 100;\n\tif (m[1] === 'hwb') {\n\t\tv = hwb2rgb(h, p1, p2);\n\t} else if (m[1] === 'hsv') {\n\t\tv = hsv2rgb(h, p1, p2);\n\t} else {\n\t\tv = hsl2rgb(h, p1, p2);\n\t}\n\treturn {\n\t\tr: v[0],\n\t\tg: v[1],\n\t\tb: v[2],\n\t\ta: a\n\t};\n}\nfunction rotate(v, deg) {\n\tvar h = rgb2hsl(v);\n\th[0] = hue(h[0] + deg);\n\th = hsl2rgb(h);\n\tv.r = h[0];\n\tv.g = h[1];\n\tv.b = h[2];\n}\nfunction hslString(v) {\n\tif (!v) {\n\t\treturn;\n\t}\n\tconst a = rgb2hsl(v);\n\tconst h = a[0];\n\tconst s = n2p(a[1]);\n\tconst l = n2p(a[2]);\n\treturn v.a < 255\n\t\t? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n\t\t: `hsl(${h}, ${s}%, ${l}%)`;\n}\nconst map$1 = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nfunction unpack() {\n\tconst unpacked = {};\n\tconst keys = Object.keys(names);\n\tconst tkeys = Object.keys(map$1);\n\tlet i, j, k, ok, nk;\n\tfor (i = 0; i < keys.length; i++) {\n\t\tok = nk = keys[i];\n\t\tfor (j = 0; j < tkeys.length; j++) {\n\t\t\tk = tkeys[j];\n\t\t\tnk = nk.replace(k, map$1[k]);\n\t\t}\n\t\tk = parseInt(names[ok], 16);\n\t\tunpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n\t}\n\treturn unpacked;\n}\nlet names$1;\nfunction nameParse(str) {\n\tif (!names$1) {\n\t\tnames$1 = unpack();\n\t\tnames$1.transparent = [0, 0, 0, 0];\n\t}\n\tconst a = names$1[str.toLowerCase()];\n\treturn a && {\n\t\tr: a[0],\n\t\tg: a[1],\n\t\tb: a[2],\n\t\ta: a.length === 4 ? a[3] : 255\n\t};\n}\nfunction modHSL(v, i, ratio) {\n\tif (v) {\n\t\tlet tmp = rgb2hsl(v);\n\t\ttmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n\t\ttmp = hsl2rgb(tmp);\n\t\tv.r = tmp[0];\n\t\tv.g = tmp[1];\n\t\tv.b = tmp[2];\n\t}\n}\nfunction clone(v, proto) {\n\treturn v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n\tvar v = {r: 0, g: 0, b: 0, a: 255};\n\tif (Array.isArray(input)) {\n\t\tif (input.length >= 3) {\n\t\t\tv = {r: input[0], g: input[1], b: input[2], a: 255};\n\t\t\tif (input.length > 3) {\n\t\t\t\tv.a = n2b(input[3]);\n\t\t\t}\n\t\t}\n\t} else {\n\t\tv = clone(input, {r: 0, g: 0, b: 0, a: 1});\n\t\tv.a = n2b(v.a);\n\t}\n\treturn v;\n}\nfunction functionParse(str) {\n\tif (str.charAt(0) === 'r') {\n\t\treturn rgbParse(str);\n\t}\n\treturn hueParse(str);\n}\nclass Color {\n\tconstructor(input) {\n\t\tif (input instanceof Color) {\n\t\t\treturn input;\n\t\t}\n\t\tconst type = typeof input;\n\t\tlet v;\n\t\tif (type === 'object') {\n\t\t\tv = fromObject(input);\n\t\t} else if (type === 'string') {\n\t\t\tv = hexParse(input) || nameParse(input) || functionParse(input);\n\t\t}\n\t\tthis._rgb = v;\n\t\tthis._valid = !!v;\n\t}\n\tget valid() {\n\t\treturn this._valid;\n\t}\n\tget rgb() {\n\t\tvar v = clone(this._rgb);\n\t\tif (v) {\n\t\t\tv.a = b2n(v.a);\n\t\t}\n\t\treturn v;\n\t}\n\tset rgb(obj) {\n\t\tthis._rgb = fromObject(obj);\n\t}\n\trgbString() {\n\t\treturn this._valid ? rgbString(this._rgb) : this._rgb;\n\t}\n\thexString() {\n\t\treturn this._valid ? hexString(this._rgb) : this._rgb;\n\t}\n\thslString() {\n\t\treturn this._valid ? hslString(this._rgb) : this._rgb;\n\t}\n\tmix(color, weight) {\n\t\tconst me = this;\n\t\tif (color) {\n\t\t\tconst c1 = me.rgb;\n\t\t\tconst c2 = color.rgb;\n\t\t\tlet w2;\n\t\t\tconst p = weight === w2 ? 0.5 : weight;\n\t\t\tconst w = 2 * p - 1;\n\t\t\tconst a = c1.a - c2.a;\n\t\t\tconst w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\t\tw2 = 1 - w1;\n\t\t\tc1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n\t\t\tc1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n\t\t\tc1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n\t\t\tc1.a = p * c1.a + (1 - p) * c2.a;\n\t\t\tme.rgb = c1;\n\t\t}\n\t\treturn me;\n\t}\n\tclone() {\n\t\treturn new Color(this.rgb);\n\t}\n\talpha(a) {\n\t\tthis._rgb.a = n2b(a);\n\t\treturn this;\n\t}\n\tclearer(ratio) {\n\t\tconst rgb = this._rgb;\n\t\trgb.a *= 1 - ratio;\n\t\treturn this;\n\t}\n\tgreyscale() {\n\t\tconst rgb = this._rgb;\n\t\tconst val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n\t\trgb.r = rgb.g = rgb.b = val;\n\t\treturn this;\n\t}\n\topaquer(ratio) {\n\t\tconst rgb = this._rgb;\n\t\trgb.a *= 1 + ratio;\n\t\treturn this;\n\t}\n\tnegate() {\n\t\tconst v = this._rgb;\n\t\tv.r = 255 - v.r;\n\t\tv.g = 255 - v.g;\n\t\tv.b = 255 - v.b;\n\t\treturn this;\n\t}\n\tlighten(ratio) {\n\t\tmodHSL(this._rgb, 2, ratio);\n\t\treturn this;\n\t}\n\tdarken(ratio) {\n\t\tmodHSL(this._rgb, 2, -ratio);\n\t\treturn this;\n\t}\n\tsaturate(ratio) {\n\t\tmodHSL(this._rgb, 1, ratio);\n\t\treturn this;\n\t}\n\tdesaturate(ratio) {\n\t\tmodHSL(this._rgb, 1, -ratio);\n\t\treturn this;\n\t}\n\trotate(deg) {\n\t\trotate(this._rgb, deg);\n\t\treturn this;\n\t}\n}\nfunction index_esm(input) {\n\treturn new Color(input);\n}\n\nconst isPatternOrGradient = (value) => value instanceof CanvasGradient || value instanceof CanvasPattern;\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n}\nvar defaults = new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n});\n\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  let type, xOffset, yOffset, size, cornerRadius;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n  default:\n    ctx.arc(x, y, radius, 0, TAU);\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      ctx.rect(x - size, y - size, 2 * size, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n  ctx.restore();\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction addRoundedRectPath(ctx, rect) {\n  const {x, y, w, h, radius} = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n  case 'px':\n    return value;\n  case '%':\n    value /= 100;\n    break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\nfunction toTRBL(value) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {lo, hi};\n}\nconst _lookupByKey = (table, key, value) =>\n  _lookup(table, value, index => table[index][key] < value);\nconst _rlookupByKey = (table, key, value) =>\n  _lookup(table, value, index => table[index][key] >= value);\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n  if (set.size === ilen) {\n    return items;\n  }\n  return Array.from(set);\n}\n\nfunction _createResolver(scopes, prefixes = [''], rootScopes = scopes, fallback, getTarget = () => scopes[0]) {\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope) => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n    get(target, prop) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n  });\n}\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n    get(target, prop, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n  });\n}\nfunction _descriptors(proxy, defaults = {scriptable: true, indexable: true}) {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop];\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  value = value(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n  return target;\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (defined(value)) {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis) => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n      : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n      : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = (element) => window.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\nfunction getCanvasPosition(evt, canvas) {\n  const e = evt.native || evt;\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {offsetX, offsetY} = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\nfunction getRelativePosition(evt, chart) {\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n  return {\n    width,\n    height\n  };\n}\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\nconst supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n  }\n  return passiveSupported;\n}());\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n    : mode === 'after' ? t < 1 ? p1.y : p2.y\n    : t > 0 ? p2.y : p1.y\n  };\n}\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\n\nconst getRightToLeftAdapter = function(rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\nconst getLeftToRightAdapter = function() {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    },\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  let {start, end, loop} = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {start, end};\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\n\nexport { _toLeftRightCenter as $, _rlookupByKey as A, getAngleFromPoint as B, toPadding as C, each as D, getMaximumSize as E, _getParentNode as F, readUsedSize as G, HALF_PI as H, throttled as I, supportsEventListenerOptions as J, _isDomSupported as K, log10 as L, _factorize as M, finiteOrDefault as N, callback as O, PI as P, _addGrace as Q, toDegrees as R, _measureText as S, TAU as T, _int16Range as U, _alignPixel as V, clipArea as W, renderText as X, unclipArea as Y, toFont as Z, _arrayUnique as _, resolve as a, _angleDiff as a$, _alignStartEnd as a0, overrides as a1, merge as a2, _capitalize as a3, descriptors as a4, isFunction as a5, _attachContext as a6, _createResolver as a7, _descriptors as a8, mergeIf as a9, restoreTextDirection as aA, noop as aB, distanceBetweenPoints as aC, _setMinAndMaxByKey as aD, niceNum as aE, almostWhole as aF, almostEquals as aG, _decimalPlaces as aH, _longestText as aI, _filterBetween as aJ, _lookup as aK, getHoverColor as aL, clone$1 as aM, _merger as aN, _mergerIf as aO, _deprecated as aP, toFontString as aQ, splineCurve as aR, splineCurveMonotone as aS, getStyle as aT, fontString as aU, toLineHeight as aV, PITAU as aW, INFINITY as aX, RAD_PER_DEG as aY, QUARTER_PI as aZ, TWO_THIRDS_PI as a_, uid as aa, debounce as ab, retinaScale as ac, clearCanvas as ad, setsEqual as ae, _elementsEqual as af, _isClickEvent as ag, _isBetween as ah, _readValueToProps as ai, _updateBezierControlPoints as aj, _computeSegments as ak, _boundSegments as al, _steppedInterpolation as am, _bezierInterpolation as an, _pointInLine as ao, _steppedLineTo as ap, _bezierCurveTo as aq, drawPoint as ar, addRoundedRectPath as as, toTRBL as at, toTRBLCorners as au, _boundSegment as av, _normalizeAngle as aw, getRtlAdapter as ax, overrideTextDirection as ay, _textX as az, isArray as b, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, isNumber as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _limitValue as w, _lookupByKey as x, getRelativePosition as y, _isPointInArea as z };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACpD,OAAOD,SAAS,GAAG,GAAG,GAAGD,SAAS,GAAG,KAAK,GAAGE,UAAU;AACzD;AACA,MAAMC,gBAAgB,GAAI,YAAW;EACnC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,UAASC,QAAQ,EAAE;MACxB,OAAOA,QAAQ,CAAC,CAAC;IACnB,CAAC;EACH;EACA,OAAOD,MAAM,CAACE,qBAAqB;AACrC,CAAC,CAAC,CAAE;AACJ,SAASC,SAASA,CAACC,EAAE,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACxC,MAAMC,UAAU,GAAGD,QAAQ,KAAME,IAAI,IAAKC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC;EAC3E,IAAIK,OAAO,GAAG,KAAK;EACnB,IAAIL,IAAI,GAAG,EAAE;EACb,OAAO,YAAkB;IAAA,SAAAM,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAR,KAAA,CAAAK,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAJD,IAAI,CAAAC,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IACrBV,IAAI,GAAGD,UAAU,CAACU,IAAI,CAAC;IACvB,IAAI,CAACJ,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI;MACdd,gBAAgB,CAACa,IAAI,CAACZ,MAAM,EAAE,MAAM;QAClCa,OAAO,GAAG,KAAK;QACfT,EAAE,CAACe,KAAK,CAACd,OAAO,EAAEG,IAAI,CAAC;MACzB,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAASY,QAAQA,CAAChB,EAAE,EAAEiB,KAAK,EAAE;EAC3B,IAAIC,OAAO;EACX,OAAO,YAAkB;IAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAC,MAAA,EAANR,IAAI,OAAAC,KAAA,CAAAc,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJhB,IAAI,CAAAgB,KAAA,IAAAT,SAAA,CAAAS,KAAA;IAAA;IACrB,IAAIH,KAAK,EAAE;MACTI,YAAY,CAACH,OAAO,CAAC;MACrBA,OAAO,GAAGI,UAAU,CAACtB,EAAE,EAAEiB,KAAK,EAAEb,IAAI,CAAC;IACvC,CAAC,MAAM;MACLJ,EAAE,CAACe,KAAK,CAAC,IAAI,EAAEX,IAAI,CAAC;IACtB;IACA,OAAOa,KAAK;EACd,CAAC;AACH;AACA,MAAMM,kBAAkB,GAAIC,KAAK,IAAKA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAGA,KAAK,KAAK,KAAK,GAAG,OAAO,GAAG,QAAQ;AACvG,MAAMC,cAAc,GAAGA,CAACD,KAAK,EAAEE,KAAK,EAAEC,GAAG,KAAKH,KAAK,KAAK,OAAO,GAAGE,KAAK,GAAGF,KAAK,KAAK,KAAK,GAAGG,GAAG,GAAG,CAACD,KAAK,GAAGC,GAAG,IAAI,CAAC;AACnH,MAAMC,MAAM,GAAGA,CAACJ,KAAK,EAAEK,IAAI,EAAEC,KAAK,EAAEC,GAAG,KAAK;EAC1C,MAAMC,KAAK,GAAGD,GAAG,GAAG,MAAM,GAAG,OAAO;EACpC,OAAOP,KAAK,KAAKQ,KAAK,GAAGF,KAAK,GAAGN,KAAK,KAAK,QAAQ,GAAG,CAACK,IAAI,GAAGC,KAAK,IAAI,CAAC,GAAGD,IAAI;AACjF,CAAC;AAED,SAASI,IAAIA,CAAA,EAAG,CAAC;AACjB,MAAMC,GAAG,GAAI,YAAW;EACtB,IAAIC,EAAE,GAAG,CAAC;EACV,OAAO,YAAW;IAChB,OAAOA,EAAE,EAAE;EACb,CAAC;AACH,CAAC,CAAC,CAAE;AACJ,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW;AACvD;AACA,SAASC,OAAOA,CAACD,KAAK,EAAE;EACtB,IAAIhC,KAAK,CAACiC,OAAO,IAAIjC,KAAK,CAACiC,OAAO,CAACD,KAAK,CAAC,EAAE;IACzC,OAAO,IAAI;EACb;EACA,MAAME,IAAI,GAAGC,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACjC,IAAI,CAAC6B,KAAK,CAAC;EAClD,IAAIE,IAAI,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,IAAIH,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACnE,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AACA,SAASC,QAAQA,CAACN,KAAK,EAAE;EACvB,OAAOA,KAAK,KAAK,IAAI,IAAIG,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACjC,IAAI,CAAC6B,KAAK,CAAC,KAAK,iBAAiB;AACtF;AACA,MAAMO,cAAc,GAAIP,KAAK,IAAK,CAAC,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYQ,MAAM,KAAKC,QAAQ,CAAC,CAACT,KAAK,CAAC;AAC5G,SAASU,eAAeA,CAACV,KAAK,EAAEW,YAAY,EAAE;EAC5C,OAAOJ,cAAc,CAACP,KAAK,CAAC,GAAGA,KAAK,GAAGW,YAAY;AACrD;AACA,SAASC,cAAcA,CAACZ,KAAK,EAAEW,YAAY,EAAE;EAC3C,OAAO,OAAOX,KAAK,KAAK,WAAW,GAAGW,YAAY,GAAGX,KAAK;AAC5D;AACA,MAAMa,YAAY,GAAGA,CAACb,KAAK,EAAEc,SAAS,KACpC,OAAOd,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACe,QAAQ,CAAC,GAAG,CAAC,GAC9CC,UAAU,CAAChB,KAAK,CAAC,GAAG,GAAG,GACrBA,KAAK,GAAGc,SAAS;AACvB,MAAMG,WAAW,GAAGA,CAACjB,KAAK,EAAEc,SAAS,KACnC,OAAOd,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACe,QAAQ,CAAC,GAAG,CAAC,GAC9CC,UAAU,CAAChB,KAAK,CAAC,GAAG,GAAG,GAAGc,SAAS,GACjC,CAACd,KAAK;AACZ,SAASxC,QAAQA,CAACG,EAAE,EAAEI,IAAI,EAAEH,OAAO,EAAE;EACnC,IAAID,EAAE,IAAI,OAAOA,EAAE,CAACQ,IAAI,KAAK,UAAU,EAAE;IACvC,OAAOR,EAAE,CAACe,KAAK,CAACd,OAAO,EAAEG,IAAI,CAAC;EAChC;AACF;AACA,SAASmD,IAAIA,CAACC,QAAQ,EAAExD,EAAE,EAAEC,OAAO,EAAEwD,OAAO,EAAE;EAC5C,IAAIC,CAAC,EAAEC,GAAG,EAAEC,IAAI;EAChB,IAAItB,OAAO,CAACkB,QAAQ,CAAC,EAAE;IACrBG,GAAG,GAAGH,QAAQ,CAAC5C,MAAM;IACrB,IAAI6C,OAAO,EAAE;MACX,KAAKC,CAAC,GAAGC,GAAG,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC7B1D,EAAE,CAACQ,IAAI,CAACP,OAAO,EAAEuD,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MAClC;IACF,CAAC,MAAM;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACxB1D,EAAE,CAACQ,IAAI,CAACP,OAAO,EAAEuD,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MAClC;IACF;EACF,CAAC,MAAM,IAAIf,QAAQ,CAACa,QAAQ,CAAC,EAAE;IAC7BI,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACJ,QAAQ,CAAC;IAC5BG,GAAG,GAAGC,IAAI,CAAChD,MAAM;IACjB,KAAK8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxB1D,EAAE,CAACQ,IAAI,CAACP,OAAO,EAAEuD,QAAQ,CAACI,IAAI,CAACF,CAAC,CAAC,CAAC,EAAEE,IAAI,CAACF,CAAC,CAAC,CAAC;IAC9C;EACF;AACF;AACA,SAASG,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC9B,IAAIL,CAAC,EAAEM,IAAI,EAAEC,EAAE,EAAEC,EAAE;EACnB,IAAI,CAACJ,EAAE,IAAI,CAACC,EAAE,IAAID,EAAE,CAAClD,MAAM,KAAKmD,EAAE,CAACnD,MAAM,EAAE;IACzC,OAAO,KAAK;EACd;EACA,KAAK8C,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAGF,EAAE,CAAClD,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;IAC3CO,EAAE,GAAGH,EAAE,CAACJ,CAAC,CAAC;IACVQ,EAAE,GAAGH,EAAE,CAACL,CAAC,CAAC;IACV,IAAIO,EAAE,CAACE,YAAY,KAAKD,EAAE,CAACC,YAAY,IAAIF,EAAE,CAACG,KAAK,KAAKF,EAAE,CAACE,KAAK,EAAE;MAChE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,IAAIhC,OAAO,CAACgC,MAAM,CAAC,EAAE;IACnB,OAAOA,MAAM,CAACC,GAAG,CAACF,OAAO,CAAC;EAC5B;EACA,IAAI1B,QAAQ,CAAC2B,MAAM,CAAC,EAAE;IACpB,MAAME,MAAM,GAAGhC,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC;IAClC,MAAMb,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACU,MAAM,CAAC;IAChC,MAAMI,IAAI,GAAGd,IAAI,CAAChD,MAAM;IACxB,IAAI+D,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAE;MACpBH,MAAM,CAACZ,IAAI,CAACe,CAAC,CAAC,CAAC,GAAGN,OAAO,CAACC,MAAM,CAACV,IAAI,CAACe,CAAC,CAAC,CAAC,CAAC;IAC5C;IACA,OAAOH,MAAM;EACf;EACA,OAAOF,MAAM;AACf;AACA,SAASM,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;AACtE;AACA,SAASE,OAAOA,CAACF,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EAC7C,IAAI,CAACJ,UAAU,CAACC,GAAG,CAAC,EAAE;IACpB;EACF;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIlC,QAAQ,CAACsC,IAAI,CAAC,IAAItC,QAAQ,CAACuC,IAAI,CAAC,EAAE;IACpCC,KAAK,CAACF,IAAI,EAAEC,IAAI,EAAEF,OAAO,CAAC;EAC5B,CAAC,MAAM;IACLR,MAAM,CAACK,GAAG,CAAC,GAAGR,OAAO,CAACa,IAAI,CAAC;EAC7B;AACF;AACA,SAASC,KAAKA,CAACX,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EACtC,MAAMI,OAAO,GAAG9C,OAAO,CAACgC,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EACnD,MAAMN,IAAI,GAAGoB,OAAO,CAACxE,MAAM;EAC3B,IAAI,CAAC+B,QAAQ,CAAC6B,MAAM,CAAC,EAAE;IACrB,OAAOA,MAAM;EACf;EACAQ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAIN,OAAO;EACxC,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;IAC7BY,MAAM,GAAGc,OAAO,CAAC1B,CAAC,CAAC;IACnB,IAAI,CAACf,QAAQ,CAAC2B,MAAM,CAAC,EAAE;MACrB;IACF;IACA,MAAMV,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACU,MAAM,CAAC;IAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAED,IAAI,GAAGd,IAAI,CAAChD,MAAM,EAAE+D,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAE;MACjDU,MAAM,CAACzB,IAAI,CAACe,CAAC,CAAC,EAAEH,MAAM,EAAEF,MAAM,EAAEU,OAAO,CAAC;IAC1C;EACF;EACA,OAAOR,MAAM;AACf;AACA,SAASc,OAAOA,CAACd,MAAM,EAAEF,MAAM,EAAE;EAC/B,OAAOa,KAAK,CAACX,MAAM,EAAEF,MAAM,EAAE;IAACe,MAAM,EAAEE;EAAS,CAAC,CAAC;AACnD;AACA,SAASA,SAASA,CAACV,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAE;EACtC,IAAI,CAACM,UAAU,CAACC,GAAG,CAAC,EAAE;IACpB;EACF;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIlC,QAAQ,CAACsC,IAAI,CAAC,IAAItC,QAAQ,CAACuC,IAAI,CAAC,EAAE;IACpCI,OAAO,CAACL,IAAI,EAAEC,IAAI,CAAC;EACrB,CAAC,MAAM,IAAI,CAAC1C,MAAM,CAAClC,SAAS,CAACkF,cAAc,CAAChF,IAAI,CAACgE,MAAM,EAAEK,GAAG,CAAC,EAAE;IAC7DL,MAAM,CAACK,GAAG,CAAC,GAAGR,OAAO,CAACa,IAAI,CAAC;EAC7B;AACF;AACA,SAASO,WAAWA,CAACC,KAAK,EAAErD,KAAK,EAAEsD,QAAQ,EAAEC,OAAO,EAAE;EACpD,IAAIvD,KAAK,KAAKwD,SAAS,EAAE;IACvBC,OAAO,CAACC,IAAI,CAACL,KAAK,GAAG,KAAK,GAAGC,QAAQ,GACtC,+BAA+B,GAAGC,OAAO,GAAG,WAAW,CAAC;EACzD;AACF;AACA,MAAMI,WAAW,GAAG,EAAE;AACtB,MAAMC,GAAG,GAAG,GAAG;AACf,SAASC,kBAAkBA,CAACrB,GAAG,EAAEnD,KAAK,EAAE;EACtC,MAAMyE,GAAG,GAAGtB,GAAG,CAACC,OAAO,CAACmB,GAAG,EAAEvE,KAAK,CAAC;EACnC,OAAOyE,GAAG,KAAK,CAAC,CAAC,GAAGtB,GAAG,CAACjE,MAAM,GAAGuF,GAAG;AACtC;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAExB,GAAG,EAAE;EAClC,IAAIA,GAAG,KAAKmB,WAAW,EAAE;IACvB,OAAOK,GAAG;EACZ;EACA,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIH,GAAG,GAAGD,kBAAkB,CAACrB,GAAG,EAAEyB,GAAG,CAAC;EACtC,OAAOD,GAAG,IAAIF,GAAG,GAAGG,GAAG,EAAE;IACvBD,GAAG,GAAGA,GAAG,CAACxB,GAAG,CAACnC,MAAM,CAAC4D,GAAG,EAAEH,GAAG,GAAGG,GAAG,CAAC,CAAC;IACrCA,GAAG,GAAGH,GAAG,GAAG,CAAC;IACbA,GAAG,GAAGD,kBAAkB,CAACrB,GAAG,EAAEyB,GAAG,CAAC;EACpC;EACA,OAAOD,GAAG;AACZ;AACA,SAASE,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACjG,KAAK,CAAC,CAAC,CAAC;AACnD;AACA,MAAMoG,OAAO,GAAItE,KAAK,IAAK,OAAOA,KAAK,KAAK,WAAW;AACvD,MAAMuE,UAAU,GAAIvE,KAAK,IAAK,OAAOA,KAAK,KAAK,UAAU;AACzD,MAAMwE,SAAS,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC1B,IAAID,CAAC,CAACE,IAAI,KAAKD,CAAC,CAACC,IAAI,EAAE;IACrB,OAAO,KAAK;EACd;EACA,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAE;IACpB,IAAI,CAACC,CAAC,CAACG,GAAG,CAACD,IAAI,CAAC,EAAE;MAChB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASE,aAAaA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,CAAC7E,IAAI,KAAK,SAAS,IAAI6E,CAAC,CAAC7E,IAAI,KAAK,OAAO,IAAI6E,CAAC,CAAC7E,IAAI,KAAK,aAAa;AAC/E;AAEA,MAAM8E,EAAE,GAAGC,IAAI,CAACD,EAAE;AAClB,MAAME,GAAG,GAAG,CAAC,GAAGF,EAAE;AAClB,MAAMG,KAAK,GAAGD,GAAG,GAAGF,EAAE;AACtB,MAAMI,QAAQ,GAAG5E,MAAM,CAAC6E,iBAAiB;AACzC,MAAMC,WAAW,GAAGN,EAAE,GAAG,GAAG;AAC5B,MAAMO,OAAO,GAAGP,EAAE,GAAG,CAAC;AACtB,MAAMQ,UAAU,GAAGR,EAAE,GAAG,CAAC;AACzB,MAAMS,aAAa,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMU,KAAK,GAAGT,IAAI,CAACS,KAAK;AACxB,MAAMC,IAAI,GAAGV,IAAI,CAACU,IAAI;AACtB,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,MAAMC,YAAY,GAAGb,IAAI,CAACc,KAAK,CAACF,KAAK,CAAC;EACtCA,KAAK,GAAGG,YAAY,CAACH,KAAK,EAAEC,YAAY,EAAED,KAAK,GAAG,IAAI,CAAC,GAAGC,YAAY,GAAGD,KAAK;EAC9E,MAAMI,SAAS,GAAGhB,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAEjB,IAAI,CAACkB,KAAK,CAACT,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMO,QAAQ,GAAGP,KAAK,GAAGI,SAAS;EAClC,MAAMI,YAAY,GAAGD,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACnF,OAAOC,YAAY,GAAGJ,SAAS;AACjC;AACA,SAASK,UAAUA,CAACtG,KAAK,EAAE;EACzB,MAAMuG,MAAM,GAAG,EAAE;EACjB,MAAMC,IAAI,GAAGvB,IAAI,CAACuB,IAAI,CAACxG,KAAK,CAAC;EAC7B,IAAIqB,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,IAAI,EAAEnF,CAAC,EAAE,EAAE;IACzB,IAAIrB,KAAK,GAAGqB,CAAC,KAAK,CAAC,EAAE;MACnBkF,MAAM,CAACE,IAAI,CAACpF,CAAC,CAAC;MACdkF,MAAM,CAACE,IAAI,CAACzG,KAAK,GAAGqB,CAAC,CAAC;IACxB;EACF;EACA,IAAImF,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;IACvBD,MAAM,CAACE,IAAI,CAACD,IAAI,CAAC;EACnB;EACAD,MAAM,CAACG,IAAI,CAAC,CAACjC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAACiC,GAAG,CAAC,CAAC;EAClC,OAAOJ,MAAM;AACf;AACA,SAASK,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAO,CAACC,KAAK,CAAC9F,UAAU,CAAC6F,CAAC,CAAC,CAAC,IAAIpG,QAAQ,CAACoG,CAAC,CAAC;AAC7C;AACA,SAASb,YAAYA,CAACe,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACnC,OAAOhC,IAAI,CAACiC,GAAG,CAACH,CAAC,GAAGC,CAAC,CAAC,GAAGC,OAAO;AAClC;AACA,SAASE,WAAWA,CAACJ,CAAC,EAAEE,OAAO,EAAE;EAC/B,MAAMG,OAAO,GAAGnC,IAAI,CAACc,KAAK,CAACgB,CAAC,CAAC;EAC7B,OAASK,OAAO,GAAGH,OAAO,IAAKF,CAAC,IAAOK,OAAO,GAAGH,OAAO,IAAKF,CAAE;AACjE;AACA,SAASM,kBAAkBA,CAACC,KAAK,EAAEnF,MAAM,EAAEoF,QAAQ,EAAE;EACnD,IAAIlG,CAAC,EAAEM,IAAI,EAAE3B,KAAK;EAClB,KAAKqB,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAG2F,KAAK,CAAC/I,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAEN,CAAC,EAAE,EAAE;IAC9CrB,KAAK,GAAGsH,KAAK,CAACjG,CAAC,CAAC,CAACkG,QAAQ,CAAC;IAC1B,IAAI,CAACT,KAAK,CAAC9G,KAAK,CAAC,EAAE;MACjBmC,MAAM,CAACqF,GAAG,GAAGvC,IAAI,CAACuC,GAAG,CAACrF,MAAM,CAACqF,GAAG,EAAExH,KAAK,CAAC;MACxCmC,MAAM,CAACsF,GAAG,GAAGxC,IAAI,CAACwC,GAAG,CAACtF,MAAM,CAACsF,GAAG,EAAEzH,KAAK,CAAC;IAC1C;EACF;AACF;AACA,SAAS0H,SAASA,CAACC,OAAO,EAAE;EAC1B,OAAOA,OAAO,IAAI3C,EAAE,GAAG,GAAG,CAAC;AAC7B;AACA,SAAS4C,SAASA,CAACC,OAAO,EAAE;EAC1B,OAAOA,OAAO,IAAI,GAAG,GAAG7C,EAAE,CAAC;AAC7B;AACA,SAAS8C,cAAcA,CAACf,CAAC,EAAE;EACzB,IAAI,CAACxG,cAAc,CAACwG,CAAC,CAAC,EAAE;IACtB;EACF;EACA,IAAIhC,CAAC,GAAG,CAAC;EACT,IAAIgD,CAAC,GAAG,CAAC;EACT,OAAO9C,IAAI,CAACc,KAAK,CAACgB,CAAC,GAAGhC,CAAC,CAAC,GAAGA,CAAC,KAAKgC,CAAC,EAAE;IAClChC,CAAC,IAAI,EAAE;IACPgD,CAAC,EAAE;EACL;EACA,OAAOA,CAAC;AACV;AACA,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,UAAU,EAAE;EAClD,MAAMC,mBAAmB,GAAGD,UAAU,CAACnB,CAAC,GAAGkB,WAAW,CAAClB,CAAC;EACxD,MAAMqB,mBAAmB,GAAGF,UAAU,CAAClB,CAAC,GAAGiB,WAAW,CAACjB,CAAC;EACxD,MAAMqB,wBAAwB,GAAGpD,IAAI,CAACuB,IAAI,CAAC2B,mBAAmB,GAAGA,mBAAmB,GAAGC,mBAAmB,GAAGA,mBAAmB,CAAC;EACjI,IAAIE,KAAK,GAAGrD,IAAI,CAACsD,KAAK,CAACH,mBAAmB,EAAED,mBAAmB,CAAC;EAChE,IAAIG,KAAK,GAAI,CAAC,GAAG,GAAGtD,EAAG,EAAE;IACvBsD,KAAK,IAAIpD,GAAG;EACd;EACA,OAAO;IACLoD,KAAK;IACLE,QAAQ,EAAEH;EACZ,CAAC;AACH;AACA,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACvC,OAAO1D,IAAI,CAACuB,IAAI,CAACvB,IAAI,CAACiB,GAAG,CAACyC,GAAG,CAAC5B,CAAC,GAAG2B,GAAG,CAAC3B,CAAC,EAAE,CAAC,CAAC,GAAG9B,IAAI,CAACiB,GAAG,CAACyC,GAAG,CAAC3B,CAAC,GAAG0B,GAAG,CAAC1B,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E;AACA,SAAS4B,UAAUA,CAACnE,CAAC,EAAEC,CAAC,EAAE;EACxB,OAAO,CAACD,CAAC,GAAGC,CAAC,GAAGS,KAAK,IAAID,GAAG,GAAGF,EAAE;AACnC;AACA,SAAS6D,eAAeA,CAACpE,CAAC,EAAE;EAC1B,OAAO,CAACA,CAAC,GAAGS,GAAG,GAAGA,GAAG,IAAIA,GAAG;AAC9B;AACA,SAAS4D,aAAaA,CAACR,KAAK,EAAEjJ,KAAK,EAAEC,GAAG,EAAEyJ,qBAAqB,EAAE;EAC/D,MAAMtE,CAAC,GAAGoE,eAAe,CAACP,KAAK,CAAC;EAChC,MAAMU,CAAC,GAAGH,eAAe,CAACxJ,KAAK,CAAC;EAChC,MAAM0F,CAAC,GAAG8D,eAAe,CAACvJ,GAAG,CAAC;EAC9B,MAAM2J,YAAY,GAAGJ,eAAe,CAACG,CAAC,GAAGvE,CAAC,CAAC;EAC3C,MAAMyE,UAAU,GAAGL,eAAe,CAAC9D,CAAC,GAAGN,CAAC,CAAC;EACzC,MAAM0E,YAAY,GAAGN,eAAe,CAACpE,CAAC,GAAGuE,CAAC,CAAC;EAC3C,MAAMI,UAAU,GAAGP,eAAe,CAACpE,CAAC,GAAGM,CAAC,CAAC;EACzC,OAAON,CAAC,KAAKuE,CAAC,IAAIvE,CAAC,KAAKM,CAAC,IAAKgE,qBAAqB,IAAIC,CAAC,KAAKjE,CAAE,IACzDkE,YAAY,GAAGC,UAAU,IAAIC,YAAY,GAAGC,UAAW;AAC/D;AACA,SAASC,WAAWA,CAACrJ,KAAK,EAAEwH,GAAG,EAAEC,GAAG,EAAE;EACpC,OAAOxC,IAAI,CAACwC,GAAG,CAACD,GAAG,EAAEvC,IAAI,CAACuC,GAAG,CAACC,GAAG,EAAEzH,KAAK,CAAC,CAAC;AAC5C;AACA,SAASsJ,WAAWA,CAACtJ,KAAK,EAAE;EAC1B,OAAOqJ,WAAW,CAACrJ,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC1C;AACA,SAASuJ,UAAUA,CAACvJ,KAAK,EAAEX,KAAK,EAAEC,GAAG,EAAkB;EAAA,IAAhB2H,OAAO,GAAA3I,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,IAAI;EACnD,OAAO0B,KAAK,IAAIiF,IAAI,CAACuC,GAAG,CAACnI,KAAK,EAAEC,GAAG,CAAC,GAAG2H,OAAO,IAAIjH,KAAK,IAAIiF,IAAI,CAACwC,GAAG,CAACpI,KAAK,EAAEC,GAAG,CAAC,GAAG2H,OAAO;AAC3F;AAEA,MAAMuC,MAAM,GAAIC,CAAC,IAAKA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AACxC,MAAMC,SAAS,GAAGA,CAACD,CAAC,EAAET,CAAC,EAAEjB,CAAC,KAAK,EAAE9C,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIuD,CAAC,IAAI,CAAC,CAAC,CAAC,GAAGxE,IAAI,CAAC0E,GAAG,CAAC,CAACF,CAAC,GAAGT,CAAC,IAAI9D,GAAG,GAAG6C,CAAC,CAAC,CAAC;AAC1F,MAAM6B,UAAU,GAAGA,CAACH,CAAC,EAAET,CAAC,EAAEjB,CAAC,KAAK9C,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGuD,CAAC,CAAC,GAAGxE,IAAI,CAAC0E,GAAG,CAAC,CAACF,CAAC,GAAGT,CAAC,IAAI9D,GAAG,GAAG6C,CAAC,CAAC,GAAG,CAAC;AACtF,MAAM8B,OAAO,GAAG;EACdC,MAAM,EAAEL,CAAC,IAAIA,CAAC;EACdM,UAAU,EAAEN,CAAC,IAAIA,CAAC,GAAGA,CAAC;EACtBO,WAAW,EAAEP,CAAC,IAAI,CAACA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;EAC9BQ,aAAa,EAAER,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAC/B,GAAG,GAAGA,CAAC,GAAGA,CAAC,GACX,CAAC,GAAG,IAAK,EAAEA,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAChCS,WAAW,EAAET,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC3BU,YAAY,EAAEV,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC;EACvCW,cAAc,EAAEX,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACf,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAChCY,WAAW,EAAEZ,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC/Ba,YAAY,EAAEb,CAAC,IAAI,EAAE,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9Cc,cAAc,EAAEd,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACnB,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACrCe,WAAW,EAAEf,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EACnCgB,YAAY,EAAEhB,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;EAC/CiB,cAAc,EAAEjB,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACvB,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACxCkB,UAAU,EAAElB,CAAC,IAAI,CAACxE,IAAI,CAAC2F,GAAG,CAACnB,CAAC,GAAGlE,OAAO,CAAC,GAAG,CAAC;EAC3CsF,WAAW,EAAEpB,CAAC,IAAIxE,IAAI,CAAC0E,GAAG,CAACF,CAAC,GAAGlE,OAAO,CAAC;EACvCuF,aAAa,EAAErB,CAAC,IAAI,CAAC,GAAG,IAAIxE,IAAI,CAAC2F,GAAG,CAAC5F,EAAE,GAAGyE,CAAC,CAAC,GAAG,CAAC,CAAC;EACjDsB,UAAU,EAAEtB,CAAC,IAAKA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAGxE,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIuD,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1DuB,WAAW,EAAEvB,CAAC,IAAKA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,CAACxE,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGuD,CAAC,CAAC,GAAG,CAAC;EAC3DwB,aAAa,EAAExB,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GACvC,GAAG,GAAGxE,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIuD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GACnC,GAAG,IAAI,CAACxE,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIuD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC/CyB,UAAU,EAAEzB,CAAC,IAAKA,CAAC,IAAI,CAAC,GAAIA,CAAC,GAAG,EAAExE,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAGiD,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3D0B,WAAW,EAAE1B,CAAC,IAAIxE,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAG,CAACiD,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC;EAC7C2B,aAAa,EAAE3B,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAC/B,CAAC,GAAG,IAAIxE,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAGiD,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,GACjC,GAAG,IAAIxE,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAG,CAACiD,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3C4B,aAAa,EAAE5B,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGC,SAAS,CAACD,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC5D6B,cAAc,EAAE7B,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGG,UAAU,CAACH,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC9D8B,gBAAgBA,CAAC9B,CAAC,EAAE;IAClB,MAAMT,CAAC,GAAG,MAAM;IAChB,MAAMjB,CAAC,GAAG,IAAI;IACd,OAAOyB,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAClBA,CAAC,GAAG,GAAG,GACH,GAAG,GAAGC,SAAS,CAACD,CAAC,GAAG,CAAC,EAAET,CAAC,EAAEjB,CAAC,CAAC,GAC5B,GAAG,GAAG,GAAG,GAAG6B,UAAU,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC,EAAET,CAAC,EAAEjB,CAAC,CAAC;EAC/C,CAAC;EACDyD,UAAUA,CAAC/B,CAAC,EAAE;IACZ,MAAMT,CAAC,GAAG,OAAO;IACjB,OAAOS,CAAC,GAAGA,CAAC,IAAI,CAACT,CAAC,GAAG,CAAC,IAAIS,CAAC,GAAGT,CAAC,CAAC;EAClC,CAAC;EACDyC,WAAWA,CAAChC,CAAC,EAAE;IACb,MAAMT,CAAC,GAAG,OAAO;IACjB,OAAO,CAACS,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAACT,CAAC,GAAG,CAAC,IAAIS,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC;EAC7C,CAAC;EACD0C,aAAaA,CAACjC,CAAC,EAAE;IACf,IAAIT,CAAC,GAAG,OAAO;IACf,IAAI,CAACS,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;MAClB,OAAO,GAAG,IAAIA,CAAC,GAAGA,CAAC,IAAI,CAAC,CAACT,CAAC,IAAK,KAAM,IAAI,CAAC,IAAIS,CAAC,GAAGT,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,GAAG,IAAI,CAACS,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAACT,CAAC,IAAK,KAAM,IAAI,CAAC,IAAIS,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC,CAAC;EAClE,CAAC;EACD2C,YAAY,EAAElC,CAAC,IAAI,CAAC,GAAGI,OAAO,CAAC+B,aAAa,CAAC,CAAC,GAAGnC,CAAC,CAAC;EACnDmC,aAAaA,CAACnC,CAAC,EAAE;IACf,MAAMoC,CAAC,GAAG,MAAM;IAChB,MAAMC,CAAC,GAAG,IAAI;IACd,IAAIrC,CAAC,GAAI,CAAC,GAAGqC,CAAE,EAAE;MACf,OAAOD,CAAC,GAAGpC,CAAC,GAAGA,CAAC;IAClB;IACA,IAAIA,CAAC,GAAI,CAAC,GAAGqC,CAAE,EAAE;MACf,OAAOD,CAAC,IAAIpC,CAAC,IAAK,GAAG,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,IAAI;IACxC;IACA,IAAIA,CAAC,GAAI,GAAG,GAAGqC,CAAE,EAAE;MACjB,OAAOD,CAAC,IAAIpC,CAAC,IAAK,IAAI,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,MAAM;IAC3C;IACA,OAAOoC,CAAC,IAAIpC,CAAC,IAAK,KAAK,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,QAAQ;EAC9C,CAAC;EACDsC,eAAe,EAAEtC,CAAC,IAAKA,CAAC,GAAG,GAAG,GAC1BI,OAAO,CAAC8B,YAAY,CAAClC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GACjCI,OAAO,CAAC+B,aAAa,CAACnC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMvH,GAAG,GAAG;EAAC,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE8J,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAE5H,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAE4H,CAAC,EAAE,EAAE;EAAER,CAAC,EAAE,EAAE;EAAE/G,CAAC,EAAE,EAAE;EAAEwH,CAAC,EAAE;AAAE,CAAC;AAC5J,MAAMC,GAAG,GAAG,kBAAkB;AAC9B,MAAMC,EAAE,GAAI/H,CAAC,IAAK8H,GAAG,CAAC9H,CAAC,GAAG,GAAG,CAAC;AAC9B,MAAMgI,EAAE,GAAIhI,CAAC,IAAK8H,GAAG,CAAC,CAAC9H,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG8H,GAAG,CAAC9H,CAAC,GAAG,GAAG,CAAC;AACrD,MAAMiI,EAAE,GAAIjI,CAAC,IAAO,CAACA,CAAC,GAAG,IAAI,KAAK,CAAC,MAAOA,CAAC,GAAG,GAAG,CAAE;AACnD,SAASkI,OAAOA,CAACC,CAAC,EAAE;EACnB,OAAOF,EAAE,CAACE,CAAC,CAACC,CAAC,CAAC,IAAIH,EAAE,CAACE,CAAC,CAACE,CAAC,CAAC,IAAIJ,EAAE,CAACE,CAAC,CAACnI,CAAC,CAAC,IAAIiI,EAAE,CAACE,CAAC,CAACpI,CAAC,CAAC;AAChD;AACA,SAASuI,QAAQA,CAAC7I,GAAG,EAAE;EACtB,IAAI7C,GAAG,GAAG6C,GAAG,CAAC5F,MAAM;EACpB,IAAI0O,GAAG;EACP,IAAI9I,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACnB,IAAI7C,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC3B2L,GAAG,GAAG;QACLH,CAAC,EAAE,GAAG,GAAG5K,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QACzB4I,CAAC,EAAE,GAAG,GAAG7K,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QACzBO,CAAC,EAAE,GAAG,GAAGxC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QACzBM,CAAC,EAAEnD,GAAG,KAAK,CAAC,GAAGY,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG;MACnC,CAAC;IACF,CAAC,MAAM,IAAI7C,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAClC2L,GAAG,GAAG;QACLH,CAAC,EAAE5K,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGjC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjC4I,CAAC,EAAE7K,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGjC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjCO,CAAC,EAAExC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGjC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjCM,CAAC,EAAEnD,GAAG,KAAK,CAAC,GAAIY,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGjC,GAAG,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI;MACnD,CAAC;IACF;EACD;EACA,OAAO8I,GAAG;AACX;AACA,SAASC,SAASA,CAACL,CAAC,EAAE;EACrB,IAAIN,CAAC,GAAGK,OAAO,CAACC,CAAC,CAAC,GAAGJ,EAAE,GAAGC,EAAE;EAC5B,OAAOG,CAAC,GACL,GAAG,GAAGN,CAAC,CAACM,CAAC,CAACC,CAAC,CAAC,GAAGP,CAAC,CAACM,CAAC,CAACE,CAAC,CAAC,GAAGR,CAAC,CAACM,CAAC,CAACnI,CAAC,CAAC,IAAImI,CAAC,CAACpI,CAAC,GAAG,GAAG,GAAG8H,CAAC,CAACM,CAAC,CAACpI,CAAC,CAAC,GAAG,EAAE,CAAC,GAC1DoI,CAAC;AACL;AACA,SAAS9G,KAAKA,CAAC8G,CAAC,EAAE;EACjB,OAAOA,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB;AACA,MAAMM,GAAG,GAAGA,CAACN,CAAC,EAAEO,CAAC,EAAEC,CAAC,KAAKpI,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAACqF,CAAC,EAAEQ,CAAC,CAAC,EAAED,CAAC,CAAC;AACpD,SAASE,GAAGA,CAACT,CAAC,EAAE;EACf,OAAOM,GAAG,CAACpH,KAAK,CAAC8G,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpC;AACA,SAASU,GAAGA,CAACV,CAAC,EAAE;EACf,OAAOM,GAAG,CAACpH,KAAK,CAAC8G,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACnC;AACA,SAASW,GAAGA,CAACX,CAAC,EAAE;EACf,OAAOM,GAAG,CAACpH,KAAK,CAAC8G,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC;AACA,SAASY,GAAGA,CAACZ,CAAC,EAAE;EACf,OAAOM,GAAG,CAACpH,KAAK,CAAC8G,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACnC;AACA,MAAMa,MAAM,GAAG,sGAAsG;AACrH,SAASC,QAAQA,CAACxJ,GAAG,EAAE;EACtB,MAAM0H,CAAC,GAAG6B,MAAM,CAACE,IAAI,CAACzJ,GAAG,CAAC;EAC1B,IAAIM,CAAC,GAAG,GAAG;EACX,IAAIqI,CAAC,EAAEC,CAAC,EAAErI,CAAC;EACX,IAAI,CAACmH,CAAC,EAAE;IACP;EACD;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAKiB,CAAC,EAAE;IACf,MAAMD,CAAC,GAAG,CAAChB,CAAC,CAAC,CAAC,CAAC;IACfpH,CAAC,GAAG,GAAG,IAAIoH,CAAC,CAAC,CAAC,CAAC,GAAGyB,GAAG,CAACT,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAC;EACpC;EACAC,CAAC,GAAG,CAACjB,CAAC,CAAC,CAAC,CAAC;EACTkB,CAAC,GAAG,CAAClB,CAAC,CAAC,CAAC,CAAC;EACTnH,CAAC,GAAG,CAACmH,CAAC,CAAC,CAAC,CAAC;EACTiB,CAAC,GAAG,GAAG,IAAIjB,CAAC,CAAC,CAAC,CAAC,GAAGyB,GAAG,CAACR,CAAC,CAAC,GAAGA,CAAC,CAAC;EAC7BC,CAAC,GAAG,GAAG,IAAIlB,CAAC,CAAC,CAAC,CAAC,GAAGyB,GAAG,CAACP,CAAC,CAAC,GAAGA,CAAC,CAAC;EAC7BrI,CAAC,GAAG,GAAG,IAAImH,CAAC,CAAC,CAAC,CAAC,GAAGyB,GAAG,CAAC5I,CAAC,CAAC,GAAGA,CAAC,CAAC;EAC7B,OAAO;IACNoI,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJrI,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA;EACJ,CAAC;AACF;AACA,SAASoJ,SAASA,CAAChB,CAAC,EAAE;EACrB,OAAOA,CAAC,KACPA,CAAC,CAACpI,CAAC,GAAG,GAAG,WAAAqJ,MAAA,CACEjB,CAAC,CAACC,CAAC,QAAAgB,MAAA,CAAKjB,CAAC,CAACE,CAAC,QAAAe,MAAA,CAAKjB,CAAC,CAACnI,CAAC,QAAAoJ,MAAA,CAAKN,GAAG,CAACX,CAAC,CAACpI,CAAC,CAAC,gBAAAqJ,MAAA,CACjCjB,CAAC,CAACC,CAAC,QAAAgB,MAAA,CAAKjB,CAAC,CAACE,CAAC,QAAAe,MAAA,CAAKjB,CAAC,CAACnI,CAAC,MAAG,CAChC;AACF;AACA,MAAMqJ,MAAM,GAAG,8GAA8G;AAC7H,SAASC,QAAQA,CAACX,CAAC,EAAErE,CAAC,EAAEoE,CAAC,EAAE;EAC1B,MAAM3I,CAAC,GAAGuE,CAAC,GAAG/D,IAAI,CAACuC,GAAG,CAAC4F,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAMb,CAAC,GAAG,SAAAA,CAAC1F,CAAC;IAAA,IAAEvE,CAAC,GAAAhE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,CAACuI,CAAC,GAAGwG,CAAC,GAAG,EAAE,IAAI,EAAE;IAAA,OAAKD,CAAC,GAAG3I,CAAC,GAAGQ,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAAClF,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA;EACvF,OAAO,CAACiK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA,SAAS0B,QAAQA,CAACZ,CAAC,EAAErE,CAAC,EAAE6D,CAAC,EAAE;EAC1B,MAAMN,CAAC,GAAG,SAAAA,CAAC1F,CAAC;IAAA,IAAEvE,CAAC,GAAAhE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,CAACuI,CAAC,GAAGwG,CAAC,GAAG,EAAE,IAAI,CAAC;IAAA,OAAKR,CAAC,GAAGA,CAAC,GAAG7D,CAAC,GAAG/D,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAAClF,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA;EACrF,OAAO,CAACiK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA,SAAS2B,QAAQA,CAACb,CAAC,EAAEc,CAAC,EAAEzJ,CAAC,EAAE;EAC1B,MAAM0J,GAAG,GAAGJ,QAAQ,CAACX,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAC/B,IAAIhM,CAAC;EACL,IAAI8M,CAAC,GAAGzJ,CAAC,GAAG,CAAC,EAAE;IACdrD,CAAC,GAAG,CAAC,IAAI8M,CAAC,GAAGzJ,CAAC,CAAC;IACfyJ,CAAC,IAAI9M,CAAC;IACNqD,CAAC,IAAIrD,CAAC;EACP;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvB+M,GAAG,CAAC/M,CAAC,CAAC,IAAI,CAAC,GAAG8M,CAAC,GAAGzJ,CAAC;IACnB0J,GAAG,CAAC/M,CAAC,CAAC,IAAI8M,CAAC;EACZ;EACA,OAAOC,GAAG;AACX;AACA,SAASC,OAAOA,CAACxB,CAAC,EAAE;EACnB,MAAMhH,KAAK,GAAG,GAAG;EACjB,MAAMiH,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAGjH,KAAK;EACrB,MAAMkH,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAGlH,KAAK;EACrB,MAAMnB,CAAC,GAAGmI,CAAC,CAACnI,CAAC,GAAGmB,KAAK;EACrB,MAAM4B,GAAG,GAAGxC,IAAI,CAACwC,GAAG,CAACqF,CAAC,EAAEC,CAAC,EAAErI,CAAC,CAAC;EAC7B,MAAM8C,GAAG,GAAGvC,IAAI,CAACuC,GAAG,CAACsF,CAAC,EAAEC,CAAC,EAAErI,CAAC,CAAC;EAC7B,MAAM0I,CAAC,GAAG,CAAC3F,GAAG,GAAGD,GAAG,IAAI,CAAC;EACzB,IAAI6F,CAAC,EAAErE,CAAC,EAAE8C,CAAC;EACX,IAAIrE,GAAG,KAAKD,GAAG,EAAE;IAChBsE,CAAC,GAAGrE,GAAG,GAAGD,GAAG;IACbwB,CAAC,GAAGoE,CAAC,GAAG,GAAG,GAAGtB,CAAC,IAAI,CAAC,GAAGrE,GAAG,GAAGD,GAAG,CAAC,GAAGsE,CAAC,IAAIrE,GAAG,GAAGD,GAAG,CAAC;IACnD6F,CAAC,GAAG5F,GAAG,KAAKqF,CAAC,GACT,CAACC,CAAC,GAAGrI,CAAC,IAAIoH,CAAC,IAAKiB,CAAC,GAAGrI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAC/B+C,GAAG,KAAKsF,CAAC,GACR,CAACrI,CAAC,GAAGoI,CAAC,IAAIhB,CAAC,GAAG,CAAC,GACf,CAACgB,CAAC,GAAGC,CAAC,IAAIjB,CAAC,GAAG,CAAC;IACnBuB,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,GAAG;EACjB;EACA,OAAO,CAACA,CAAC,GAAG,CAAC,EAAErE,CAAC,IAAI,CAAC,EAAEoE,CAAC,CAAC;AAC1B;AACA,SAASkB,KAAKA,CAAC/B,CAAC,EAAE9H,CAAC,EAAEC,CAAC,EAAE4H,CAAC,EAAE;EAC1B,OAAO,CACNtO,KAAK,CAACiC,OAAO,CAACwE,CAAC,CAAC,GACb8H,CAAC,CAAC9H,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GACnB8H,CAAC,CAAC9H,CAAC,EAAEC,CAAC,EAAE4H,CAAC,CAAC,EACZpK,GAAG,CAACqL,GAAG,CAAC;AACX;AACA,SAASgB,OAAOA,CAAClB,CAAC,EAAErE,CAAC,EAAEoE,CAAC,EAAE;EACzB,OAAOkB,KAAK,CAACN,QAAQ,EAAEX,CAAC,EAAErE,CAAC,EAAEoE,CAAC,CAAC;AAChC;AACA,SAASoB,OAAOA,CAACnB,CAAC,EAAEc,CAAC,EAAEzJ,CAAC,EAAE;EACzB,OAAO4J,KAAK,CAACJ,QAAQ,EAAEb,CAAC,EAAEc,CAAC,EAAEzJ,CAAC,CAAC;AAChC;AACA,SAAS+J,OAAOA,CAACpB,CAAC,EAAErE,CAAC,EAAE6D,CAAC,EAAE;EACzB,OAAOyB,KAAK,CAACL,QAAQ,EAAEZ,CAAC,EAAErE,CAAC,EAAE6D,CAAC,CAAC;AAChC;AACA,SAAS6B,GAAGA,CAACrB,CAAC,EAAE;EACf,OAAO,CAACA,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG;AAC7B;AACA,SAASsB,QAAQA,CAACxK,GAAG,EAAE;EACtB,MAAM0H,CAAC,GAAGkC,MAAM,CAACH,IAAI,CAACzJ,GAAG,CAAC;EAC1B,IAAIM,CAAC,GAAG,GAAG;EACX,IAAIoI,CAAC;EACL,IAAI,CAAChB,CAAC,EAAE;IACP;EACD;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAKgB,CAAC,EAAE;IACfpI,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,GAAGyB,GAAG,CAAC,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG0B,GAAG,CAAC,CAAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC;EACA,MAAMwB,CAAC,GAAGqB,GAAG,CAAC,CAAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,MAAM+C,EAAE,GAAG,CAAC/C,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,MAAMgD,EAAE,GAAG,CAAChD,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IACnBgB,CAAC,GAAG2B,OAAO,CAACnB,CAAC,EAAEuB,EAAE,EAAEC,EAAE,CAAC;EACvB,CAAC,MAAM,IAAIhD,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IAC1BgB,CAAC,GAAG4B,OAAO,CAACpB,CAAC,EAAEuB,EAAE,EAAEC,EAAE,CAAC;EACvB,CAAC,MAAM;IACNhC,CAAC,GAAG0B,OAAO,CAAClB,CAAC,EAAEuB,EAAE,EAAEC,EAAE,CAAC;EACvB;EACA,OAAO;IACN/B,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IACPE,CAAC,EAAEF,CAAC,CAAC,CAAC,CAAC;IACPnI,CAAC,EAAEmI,CAAC,CAAC,CAAC,CAAC;IACPpI,CAAC,EAAEA;EACJ,CAAC;AACF;AACA,SAASqK,MAAMA,CAACjC,CAAC,EAAEkC,GAAG,EAAE;EACvB,IAAI1B,CAAC,GAAGgB,OAAO,CAACxB,CAAC,CAAC;EAClBQ,CAAC,CAAC,CAAC,CAAC,GAAGqB,GAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,GAAG0B,GAAG,CAAC;EACtB1B,CAAC,GAAGkB,OAAO,CAAClB,CAAC,CAAC;EACdR,CAAC,CAACC,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC;EACVR,CAAC,CAACE,CAAC,GAAGM,CAAC,CAAC,CAAC,CAAC;EACVR,CAAC,CAACnI,CAAC,GAAG2I,CAAC,CAAC,CAAC,CAAC;AACX;AACA,SAAS2B,SAASA,CAACnC,CAAC,EAAE;EACrB,IAAI,CAACA,CAAC,EAAE;IACP;EACD;EACA,MAAMpI,CAAC,GAAG4J,OAAO,CAACxB,CAAC,CAAC;EACpB,MAAMQ,CAAC,GAAG5I,CAAC,CAAC,CAAC,CAAC;EACd,MAAMuE,CAAC,GAAGyE,GAAG,CAAChJ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,MAAM2I,CAAC,GAAGK,GAAG,CAAChJ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOoI,CAAC,CAACpI,CAAC,GAAG,GAAG,WAAAqJ,MAAA,CACLT,CAAC,QAAAS,MAAA,CAAK9E,CAAC,SAAA8E,MAAA,CAAMV,CAAC,SAAAU,MAAA,CAAMN,GAAG,CAACX,CAAC,CAACpI,CAAC,CAAC,gBAAAqJ,MAAA,CAC7BT,CAAC,QAAAS,MAAA,CAAK9E,CAAC,SAAA8E,MAAA,CAAMV,CAAC,OAAI;AAC7B;AACA,MAAM6B,KAAK,GAAG;EACblI,CAAC,EAAE,MAAM;EACTmI,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,KAAK;EACRC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,QAAQ;EACXC,CAAC,EAAE,OAAO;EACVvD,CAAC,EAAE,IAAI;EACPwD,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPxD,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,OAAO;EACVuD,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,UAAU;EACbvD,CAAC,EAAE,IAAI;EACPwD,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACV3D,CAAC,EAAE,IAAI;EACP4D,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,MAAM;EACTC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE;AACJ,CAAC;AACD,MAAMC,KAAK,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,GAAG;EACVC,YAAY,EAAE,QAAQ;EACtBC,EAAE,EAAE,IAAI;EACRC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,MAAM;EACXC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,QAAQ;EACnBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE,QAAQ;EACbC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,QAAQ;EACrBC,EAAE,EAAE,QAAQ;EACZC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE;AACR,CAAC;AACD,SAASC,MAAMA,CAAA,EAAG;EACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMrY,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAAC+O,KAAK,CAAC;EAC/B,MAAMuJ,KAAK,GAAG1Z,MAAM,CAACoB,IAAI,CAAC0N,KAAK,CAAC;EAChC,IAAI5N,CAAC,EAAEyY,CAAC,EAAExX,CAAC,EAAEyX,EAAE,EAAEC,EAAE;EACnB,KAAK3Y,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAAChD,MAAM,EAAE8C,CAAC,EAAE,EAAE;IACjC0Y,EAAE,GAAGC,EAAE,GAAGzY,IAAI,CAACF,CAAC,CAAC;IACjB,KAAKyY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACtb,MAAM,EAAEub,CAAC,EAAE,EAAE;MAClCxX,CAAC,GAAGuX,KAAK,CAACC,CAAC,CAAC;MACZE,EAAE,GAAGA,EAAE,CAACC,OAAO,CAAC3X,CAAC,EAAE2M,KAAK,CAAC3M,CAAC,CAAC,CAAC;IAC7B;IACAA,CAAC,GAAG4X,QAAQ,CAAC5J,KAAK,CAACyJ,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3BH,QAAQ,CAACI,EAAE,CAAC,GAAG,CAAC1X,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,GAAG,IAAI,CAAC;EACzD;EACA,OAAOsX,QAAQ;AAChB;AACA,IAAIO,OAAO;AACX,SAASC,SAASA,CAACjW,GAAG,EAAE;EACvB,IAAI,CAACgW,OAAO,EAAE;IACbA,OAAO,GAAGR,MAAM,CAAC,CAAC;IAClBQ,OAAO,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnC;EACA,MAAM5V,CAAC,GAAG0V,OAAO,CAAChW,GAAG,CAACmW,WAAW,CAAC,CAAC,CAAC;EACpC,OAAO7V,CAAC,IAAI;IACXqI,CAAC,EAAErI,CAAC,CAAC,CAAC,CAAC;IACPsI,CAAC,EAAEtI,CAAC,CAAC,CAAC,CAAC;IACPC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IACPA,CAAC,EAAEA,CAAC,CAAClG,MAAM,KAAK,CAAC,GAAGkG,CAAC,CAAC,CAAC,CAAC,GAAG;EAC5B,CAAC;AACF;AACA,SAAS8V,MAAMA,CAAC1N,CAAC,EAAExL,CAAC,EAAEmZ,KAAK,EAAE;EAC5B,IAAI3N,CAAC,EAAE;IACN,IAAI4N,GAAG,GAAGpM,OAAO,CAACxB,CAAC,CAAC;IACpB4N,GAAG,CAACpZ,CAAC,CAAC,GAAG4D,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAExC,IAAI,CAACuC,GAAG,CAACiT,GAAG,CAACpZ,CAAC,CAAC,GAAGoZ,GAAG,CAACpZ,CAAC,CAAC,GAAGmZ,KAAK,EAAEnZ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1EoZ,GAAG,GAAGlM,OAAO,CAACkM,GAAG,CAAC;IAClB5N,CAAC,CAACC,CAAC,GAAG2N,GAAG,CAAC,CAAC,CAAC;IACZ5N,CAAC,CAACE,CAAC,GAAG0N,GAAG,CAAC,CAAC,CAAC;IACZ5N,CAAC,CAACnI,CAAC,GAAG+V,GAAG,CAAC,CAAC,CAAC;EACb;AACD;AACA,SAASC,KAAKA,CAAC7N,CAAC,EAAE8N,KAAK,EAAE;EACxB,OAAO9N,CAAC,GAAG1M,MAAM,CAACya,MAAM,CAACD,KAAK,IAAI,CAAC,CAAC,EAAE9N,CAAC,CAAC,GAAGA,CAAC;AAC7C;AACA,SAASgO,UAAUA,CAACC,KAAK,EAAE;EAC1B,IAAIjO,CAAC,GAAG;IAACC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAErI,CAAC,EAAE,CAAC;IAAED,CAAC,EAAE;EAAG,CAAC;EAClC,IAAIzG,KAAK,CAACiC,OAAO,CAAC6a,KAAK,CAAC,EAAE;IACzB,IAAIA,KAAK,CAACvc,MAAM,IAAI,CAAC,EAAE;MACtBsO,CAAC,GAAG;QAACC,CAAC,EAAEgO,KAAK,CAAC,CAAC,CAAC;QAAE/N,CAAC,EAAE+N,KAAK,CAAC,CAAC,CAAC;QAAEpW,CAAC,EAAEoW,KAAK,CAAC,CAAC,CAAC;QAAErW,CAAC,EAAE;MAAG,CAAC;MACnD,IAAIqW,KAAK,CAACvc,MAAM,GAAG,CAAC,EAAE;QACrBsO,CAAC,CAACpI,CAAC,GAAG8I,GAAG,CAACuN,KAAK,CAAC,CAAC,CAAC,CAAC;MACpB;IACD;EACD,CAAC,MAAM;IACNjO,CAAC,GAAG6N,KAAK,CAACI,KAAK,EAAE;MAAChO,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAErI,CAAC,EAAE,CAAC;MAAED,CAAC,EAAE;IAAC,CAAC,CAAC;IAC1CoI,CAAC,CAACpI,CAAC,GAAG8I,GAAG,CAACV,CAAC,CAACpI,CAAC,CAAC;EACf;EACA,OAAOoI,CAAC;AACT;AACA,SAASkO,aAAaA,CAAC5W,GAAG,EAAE;EAC3B,IAAIA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1B,OAAOuJ,QAAQ,CAACxJ,GAAG,CAAC;EACrB;EACA,OAAOwK,QAAQ,CAACxK,GAAG,CAAC;AACrB;AACA,MAAM6W,KAAK,CAAC;EACXC,WAAWA,CAACH,KAAK,EAAE;IAClB,IAAIA,KAAK,YAAYE,KAAK,EAAE;MAC3B,OAAOF,KAAK;IACb;IACA,MAAM5a,IAAI,GAAG,OAAO4a,KAAK;IACzB,IAAIjO,CAAC;IACL,IAAI3M,IAAI,KAAK,QAAQ,EAAE;MACtB2M,CAAC,GAAGgO,UAAU,CAACC,KAAK,CAAC;IACtB,CAAC,MAAM,IAAI5a,IAAI,KAAK,QAAQ,EAAE;MAC7B2M,CAAC,GAAGG,QAAQ,CAAC8N,KAAK,CAAC,IAAIV,SAAS,CAACU,KAAK,CAAC,IAAIC,aAAa,CAACD,KAAK,CAAC;IAChE;IACA,IAAI,CAACI,IAAI,GAAGrO,CAAC;IACb,IAAI,CAACsO,MAAM,GAAG,CAAC,CAACtO,CAAC;EAClB;EACA,IAAIuO,KAAKA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,MAAM;EACnB;EACA,IAAI/M,GAAGA,CAAA,EAAG;IACT,IAAIvB,CAAC,GAAG6N,KAAK,CAAC,IAAI,CAACQ,IAAI,CAAC;IACxB,IAAIrO,CAAC,EAAE;MACNA,CAAC,CAACpI,CAAC,GAAG+I,GAAG,CAACX,CAAC,CAACpI,CAAC,CAAC;IACf;IACA,OAAOoI,CAAC;EACT;EACA,IAAIuB,GAAGA,CAACpK,GAAG,EAAE;IACZ,IAAI,CAACkX,IAAI,GAAGL,UAAU,CAAC7W,GAAG,CAAC;EAC5B;EACA6J,SAASA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsN,MAAM,GAAGtN,SAAS,CAAC,IAAI,CAACqN,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI;EACtD;EACAhO,SAASA,CAAA,EAAG;IACX,OAAO,IAAI,CAACiO,MAAM,GAAGjO,SAAS,CAAC,IAAI,CAACgO,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI;EACtD;EACAlM,SAASA,CAAA,EAAG;IACX,OAAO,IAAI,CAACmM,MAAM,GAAGnM,SAAS,CAAC,IAAI,CAACkM,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI;EACtD;EACAG,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAE;IAClB,MAAMC,EAAE,GAAG,IAAI;IACf,IAAIF,KAAK,EAAE;MACV,MAAMG,EAAE,GAAGD,EAAE,CAACpN,GAAG;MACjB,MAAMsN,EAAE,GAAGJ,KAAK,CAAClN,GAAG;MACpB,IAAIuN,EAAE;MACN,MAAM5T,CAAC,GAAGwT,MAAM,KAAKI,EAAE,GAAG,GAAG,GAAGJ,MAAM;MACtC,MAAMpN,CAAC,GAAG,CAAC,GAAGpG,CAAC,GAAG,CAAC;MACnB,MAAMtD,CAAC,GAAGgX,EAAE,CAAChX,CAAC,GAAGiX,EAAE,CAACjX,CAAC;MACrB,MAAMmX,EAAE,GAAG,CAAC,CAACzN,CAAC,GAAG1J,CAAC,KAAK,CAAC,CAAC,GAAG0J,CAAC,GAAG,CAACA,CAAC,GAAG1J,CAAC,KAAK,CAAC,GAAG0J,CAAC,GAAG1J,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG;MACjEkX,EAAE,GAAG,CAAC,GAAGC,EAAE;MACXH,EAAE,CAAC3O,CAAC,GAAG,IAAI,GAAG8O,EAAE,GAAGH,EAAE,CAAC3O,CAAC,GAAG6O,EAAE,GAAGD,EAAE,CAAC5O,CAAC,GAAG,GAAG;MACzC2O,EAAE,CAAC1O,CAAC,GAAG,IAAI,GAAG6O,EAAE,GAAGH,EAAE,CAAC1O,CAAC,GAAG4O,EAAE,GAAGD,EAAE,CAAC3O,CAAC,GAAG,GAAG;MACzC0O,EAAE,CAAC/W,CAAC,GAAG,IAAI,GAAGkX,EAAE,GAAGH,EAAE,CAAC/W,CAAC,GAAGiX,EAAE,GAAGD,EAAE,CAAChX,CAAC,GAAG,GAAG;MACzC+W,EAAE,CAAChX,CAAC,GAAGsD,CAAC,GAAG0T,EAAE,CAAChX,CAAC,GAAG,CAAC,CAAC,GAAGsD,CAAC,IAAI2T,EAAE,CAACjX,CAAC;MAChC+W,EAAE,CAACpN,GAAG,GAAGqN,EAAE;IACZ;IACA,OAAOD,EAAE;EACV;EACAd,KAAKA,CAAA,EAAG;IACP,OAAO,IAAIM,KAAK,CAAC,IAAI,CAAC5M,GAAG,CAAC;EAC3B;EACAyN,KAAKA,CAACpX,CAAC,EAAE;IACR,IAAI,CAACyW,IAAI,CAACzW,CAAC,GAAG8I,GAAG,CAAC9I,CAAC,CAAC;IACpB,OAAO,IAAI;EACZ;EACAqX,OAAOA,CAACtB,KAAK,EAAE;IACd,MAAMpM,GAAG,GAAG,IAAI,CAAC8M,IAAI;IACrB9M,GAAG,CAAC3J,CAAC,IAAI,CAAC,GAAG+V,KAAK;IAClB,OAAO,IAAI;EACZ;EACAuB,SAASA,CAAA,EAAG;IACX,MAAM3N,GAAG,GAAG,IAAI,CAAC8M,IAAI;IACrB,MAAMc,GAAG,GAAGjW,KAAK,CAACqI,GAAG,CAACtB,CAAC,GAAG,GAAG,GAAGsB,GAAG,CAACrB,CAAC,GAAG,IAAI,GAAGqB,GAAG,CAAC1J,CAAC,GAAG,IAAI,CAAC;IAC5D0J,GAAG,CAACtB,CAAC,GAAGsB,GAAG,CAACrB,CAAC,GAAGqB,GAAG,CAAC1J,CAAC,GAAGsX,GAAG;IAC3B,OAAO,IAAI;EACZ;EACAC,OAAOA,CAACzB,KAAK,EAAE;IACd,MAAMpM,GAAG,GAAG,IAAI,CAAC8M,IAAI;IACrB9M,GAAG,CAAC3J,CAAC,IAAI,CAAC,GAAG+V,KAAK;IAClB,OAAO,IAAI;EACZ;EACA0B,MAAMA,CAAA,EAAG;IACR,MAAMrP,CAAC,GAAG,IAAI,CAACqO,IAAI;IACnBrO,CAAC,CAACC,CAAC,GAAG,GAAG,GAAGD,CAAC,CAACC,CAAC;IACfD,CAAC,CAACE,CAAC,GAAG,GAAG,GAAGF,CAAC,CAACE,CAAC;IACfF,CAAC,CAACnI,CAAC,GAAG,GAAG,GAAGmI,CAAC,CAACnI,CAAC;IACf,OAAO,IAAI;EACZ;EACAyX,OAAOA,CAAC3B,KAAK,EAAE;IACdD,MAAM,CAAC,IAAI,CAACW,IAAI,EAAE,CAAC,EAAEV,KAAK,CAAC;IAC3B,OAAO,IAAI;EACZ;EACA4B,MAAMA,CAAC5B,KAAK,EAAE;IACbD,MAAM,CAAC,IAAI,CAACW,IAAI,EAAE,CAAC,EAAE,CAACV,KAAK,CAAC;IAC5B,OAAO,IAAI;EACZ;EACA6B,QAAQA,CAAC7B,KAAK,EAAE;IACfD,MAAM,CAAC,IAAI,CAACW,IAAI,EAAE,CAAC,EAAEV,KAAK,CAAC;IAC3B,OAAO,IAAI;EACZ;EACA8B,UAAUA,CAAC9B,KAAK,EAAE;IACjBD,MAAM,CAAC,IAAI,CAACW,IAAI,EAAE,CAAC,EAAE,CAACV,KAAK,CAAC;IAC5B,OAAO,IAAI;EACZ;EACA1L,MAAMA,CAACC,GAAG,EAAE;IACXD,MAAM,CAAC,IAAI,CAACoM,IAAI,EAAEnM,GAAG,CAAC;IACtB,OAAO,IAAI;EACZ;AACD;AACA,SAASwN,SAASA,CAACzB,KAAK,EAAE;EACzB,OAAO,IAAIE,KAAK,CAACF,KAAK,CAAC;AACxB;AAEA,MAAM0B,mBAAmB,GAAIxc,KAAK,IAAKA,KAAK,YAAYyc,cAAc,IAAIzc,KAAK,YAAY0c,aAAa;AACxG,SAASpB,KAAKA,CAACtb,KAAK,EAAE;EACpB,OAAOwc,mBAAmB,CAACxc,KAAK,CAAC,GAAGA,KAAK,GAAGuc,SAAS,CAACvc,KAAK,CAAC;AAC9D;AACA,SAAS2c,aAAaA,CAAC3c,KAAK,EAAE;EAC5B,OAAOwc,mBAAmB,CAACxc,KAAK,CAAC,GAC7BA,KAAK,GACLuc,SAAS,CAACvc,KAAK,CAAC,CAACqc,QAAQ,CAAC,GAAG,CAAC,CAACD,MAAM,CAAC,GAAG,CAAC,CAAClP,SAAS,CAAC,CAAC;AAC5D;AAEA,MAAM0P,SAAS,GAAGzc,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC;AACrC,MAAMya,WAAW,GAAG1c,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC;AACvC,SAAS0a,UAAUA,CAACC,IAAI,EAAEva,GAAG,EAAE;EAC7B,IAAI,CAACA,GAAG,EAAE;IACR,OAAOua,IAAI;EACb;EACA,MAAMxb,IAAI,GAAGiB,GAAG,CAACwa,KAAK,CAAC,GAAG,CAAC;EAC3B,KAAK,IAAI3b,CAAC,GAAG,CAAC,EAAEwF,CAAC,GAAGtF,IAAI,CAAChD,MAAM,EAAE8C,CAAC,GAAGwF,CAAC,EAAE,EAAExF,CAAC,EAAE;IAC3C,MAAMiB,CAAC,GAAGf,IAAI,CAACF,CAAC,CAAC;IACjB0b,IAAI,GAAGA,IAAI,CAACza,CAAC,CAAC,KAAKya,IAAI,CAACza,CAAC,CAAC,GAAGnC,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC,CAAC;EACnD;EACA,OAAO2a,IAAI;AACb;AACA,SAASE,GAAGA,CAACC,IAAI,EAAE7Z,KAAK,EAAE8Z,MAAM,EAAE;EAChC,IAAI,OAAO9Z,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOP,KAAK,CAACga,UAAU,CAACI,IAAI,EAAE7Z,KAAK,CAAC,EAAE8Z,MAAM,CAAC;EAC/C;EACA,OAAOra,KAAK,CAACga,UAAU,CAACI,IAAI,EAAE,EAAE,CAAC,EAAE7Z,KAAK,CAAC;AAC3C;AACA,MAAM+Z,QAAQ,CAAC;EACbnC,WAAWA,CAACoC,YAAY,EAAE;IACxB,IAAI,CAACC,SAAS,GAAG9Z,SAAS;IAC1B,IAAI,CAAC+Z,eAAe,GAAG,iBAAiB;IACxC,IAAI,CAACC,WAAW,GAAG,iBAAiB;IACpC,IAAI,CAAClC,KAAK,GAAG,MAAM;IACnB,IAAI,CAACmC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAIC,OAAO,IAAKA,OAAO,CAACC,KAAK,CAACC,QAAQ,CAACC,mBAAmB,CAAC,CAAC;IACjF,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,MAAM,GAAG,CACZ,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EACZ,WAAW,CACZ;IACD,IAAI,CAACC,IAAI,GAAG;MACVC,MAAM,EAAE,oDAAoD;MAC5DvZ,IAAI,EAAE,EAAE;MACRwZ,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE,GAAG;MACf7C,MAAM,EAAE;IACV,CAAC;IACD,IAAI,CAAC8C,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAG,EAAE5b,OAAO,KAAKga,aAAa,CAACha,OAAO,CAAC4a,eAAe,CAAC;IACpF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAG,EAAE5b,OAAO,KAAKga,aAAa,CAACha,OAAO,CAAC6a,WAAW,CAAC;IAC5E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAG,EAAE5b,OAAO,KAAKga,aAAa,CAACha,OAAO,CAAC2Y,KAAK,CAAC;IAChE,IAAI,CAACoD,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE;IACb,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAG5b,SAAS;IACtB,IAAI,CAAC6b,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACC,QAAQ,CAACnC,YAAY,CAAC;EAC7B;EACAJ,GAAGA,CAAC5Z,KAAK,EAAE8Z,MAAM,EAAE;IACjB,OAAOF,GAAG,CAAC,IAAI,EAAE5Z,KAAK,EAAE8Z,MAAM,CAAC;EACjC;EACAsC,GAAGA,CAACpc,KAAK,EAAE;IACT,OAAOyZ,UAAU,CAAC,IAAI,EAAEzZ,KAAK,CAAC;EAChC;EACAmc,QAAQA,CAACnc,KAAK,EAAE8Z,MAAM,EAAE;IACtB,OAAOF,GAAG,CAACJ,WAAW,EAAExZ,KAAK,EAAE8Z,MAAM,CAAC;EACxC;EACAuC,QAAQA,CAACrc,KAAK,EAAE8Z,MAAM,EAAE;IACtB,OAAOF,GAAG,CAACL,SAAS,EAAEvZ,KAAK,EAAE8Z,MAAM,CAAC;EACtC;EACAwC,KAAKA,CAACtc,KAAK,EAAEuc,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC1C,MAAMC,WAAW,GAAGjD,UAAU,CAAC,IAAI,EAAEzZ,KAAK,CAAC;IAC3C,MAAM2c,iBAAiB,GAAGlD,UAAU,CAAC,IAAI,EAAE+C,WAAW,CAAC;IACvD,MAAMI,WAAW,GAAG,GAAG,GAAGL,IAAI;IAC9Bzf,MAAM,CAAC+f,gBAAgB,CAACH,WAAW,EAAE;MACnC,CAACE,WAAW,GAAG;QACbjgB,KAAK,EAAE+f,WAAW,CAACH,IAAI,CAAC;QACxBO,QAAQ,EAAE;MACZ,CAAC;MACD,CAACP,IAAI,GAAG;QACNQ,UAAU,EAAE,IAAI;QAChBX,GAAGA,CAAA,EAAG;UACJ,MAAMY,KAAK,GAAG,IAAI,CAACJ,WAAW,CAAC;UAC/B,MAAM9d,MAAM,GAAG6d,iBAAiB,CAACF,UAAU,CAAC;UAC5C,IAAIxf,QAAQ,CAAC+f,KAAK,CAAC,EAAE;YACnB,OAAOlgB,MAAM,CAACya,MAAM,CAAC,CAAC,CAAC,EAAEzY,MAAM,EAAEke,KAAK,CAAC;UACzC;UACA,OAAOzf,cAAc,CAACyf,KAAK,EAAEle,MAAM,CAAC;QACtC,CAAC;QACD8a,GAAGA,CAACjd,KAAK,EAAE;UACT,IAAI,CAACigB,WAAW,CAAC,GAAGjgB,KAAK;QAC3B;MACF;IACF,CAAC,CAAC;EACJ;AACF;AACA,IAAIsgB,QAAQ,GAAG,IAAIlD,QAAQ,CAAC;EAC1BmD,WAAW,EAAGX,IAAI,IAAK,CAACA,IAAI,CAACY,UAAU,CAAC,IAAI,CAAC;EAC7CC,UAAU,EAAGb,IAAI,IAAKA,IAAI,KAAK,QAAQ;EACvCvB,KAAK,EAAE;IACLqC,SAAS,EAAE;EACb,CAAC;EACD/B,WAAW,EAAE;IACX4B,WAAW,EAAE,KAAK;IAClBE,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,SAASE,YAAYA,CAAC1C,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,IAAIle,aAAa,CAACke,IAAI,CAACtZ,IAAI,CAAC,IAAI5E,aAAa,CAACke,IAAI,CAACC,MAAM,CAAC,EAAE;IACnE,OAAO,IAAI;EACb;EACA,OAAO,CAACD,IAAI,CAACE,KAAK,GAAGF,IAAI,CAACE,KAAK,GAAG,GAAG,GAAG,EAAE,KACvCF,IAAI,CAAC1C,MAAM,GAAG0C,IAAI,CAAC1C,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,GACtC0C,IAAI,CAACtZ,IAAI,GAAG,KAAK,GACjBsZ,IAAI,CAACC,MAAM;AACf;AACA,SAAS0C,YAAYA,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACpD,IAAIC,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC5B,IAAI,CAACC,SAAS,EAAE;IACdA,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC,GAAGzC,GAAG,CAAC2C,WAAW,CAACF,MAAM,CAAC,CAACG,KAAK;IACxDL,EAAE,CAACra,IAAI,CAACua,MAAM,CAAC;EACjB;EACA,IAAIC,SAAS,GAAGF,OAAO,EAAE;IACvBA,OAAO,GAAGE,SAAS;EACrB;EACA,OAAOF,OAAO;AAChB;AACA,SAASK,YAAYA,CAAC7C,GAAG,EAAEN,IAAI,EAAEoD,aAAa,EAAEC,KAAK,EAAE;EACrDA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIT,IAAI,GAAGS,KAAK,CAACT,IAAI,GAAGS,KAAK,CAACT,IAAI,IAAI,CAAC,CAAC;EACxC,IAAIC,EAAE,GAAGQ,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,EAAE;EAC1D,IAAID,KAAK,CAACrD,IAAI,KAAKA,IAAI,EAAE;IACvB4C,IAAI,GAAGS,KAAK,CAACT,IAAI,GAAG,CAAC,CAAC;IACtBC,EAAE,GAAGQ,KAAK,CAACC,cAAc,GAAG,EAAE;IAC9BD,KAAK,CAACrD,IAAI,GAAGA,IAAI;EACnB;EACAM,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAACN,IAAI,GAAGA,IAAI;EACf,IAAI8C,OAAO,GAAG,CAAC;EACf,MAAMpf,IAAI,GAAG0f,aAAa,CAAC9iB,MAAM;EACjC,IAAI8C,CAAC,EAAEyY,CAAC,EAAE2H,IAAI,EAAEC,KAAK,EAAEC,WAAW;EAClC,KAAKtgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,IAAI,EAAEN,CAAC,EAAE,EAAE;IACzBqgB,KAAK,GAAGL,aAAa,CAAChgB,CAAC,CAAC;IACxB,IAAIqgB,KAAK,KAAKle,SAAS,IAAIke,KAAK,KAAK,IAAI,IAAIzhB,OAAO,CAACyhB,KAAK,CAAC,KAAK,IAAI,EAAE;MACpEX,OAAO,GAAGH,YAAY,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEW,KAAK,CAAC;IACvD,CAAC,MAAM,IAAIzhB,OAAO,CAACyhB,KAAK,CAAC,EAAE;MACzB,KAAK5H,CAAC,GAAG,CAAC,EAAE2H,IAAI,GAAGC,KAAK,CAACnjB,MAAM,EAAEub,CAAC,GAAG2H,IAAI,EAAE3H,CAAC,EAAE,EAAE;QAC9C6H,WAAW,GAAGD,KAAK,CAAC5H,CAAC,CAAC;QACtB,IAAI6H,WAAW,KAAKne,SAAS,IAAIme,WAAW,KAAK,IAAI,IAAI,CAAC1hB,OAAO,CAAC0hB,WAAW,CAAC,EAAE;UAC9EZ,OAAO,GAAGH,YAAY,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEY,WAAW,CAAC;QAC7D;MACF;IACF;EACF;EACApD,GAAG,CAACqD,OAAO,CAAC,CAAC;EACb,MAAMC,KAAK,GAAGf,EAAE,CAACviB,MAAM,GAAG,CAAC;EAC3B,IAAIsjB,KAAK,GAAGR,aAAa,CAAC9iB,MAAM,EAAE;IAChC,KAAK8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwgB,KAAK,EAAExgB,CAAC,EAAE,EAAE;MAC1B,OAAOwf,IAAI,CAACC,EAAE,CAACzf,CAAC,CAAC,CAAC;IACpB;IACAyf,EAAE,CAACgB,MAAM,CAAC,CAAC,EAAED,KAAK,CAAC;EACrB;EACA,OAAOd,OAAO;AAChB;AACA,SAASgB,WAAWA,CAACnE,KAAK,EAAEoE,KAAK,EAAEb,KAAK,EAAE;EACxC,MAAMzD,gBAAgB,GAAGE,KAAK,CAACqE,uBAAuB;EACtD,MAAMC,SAAS,GAAGf,KAAK,KAAK,CAAC,GAAGlc,IAAI,CAACwC,GAAG,CAAC0Z,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EAC5D,OAAOlc,IAAI,CAACc,KAAK,CAAC,CAACic,KAAK,GAAGE,SAAS,IAAIxE,gBAAgB,CAAC,GAAGA,gBAAgB,GAAGwE,SAAS;AAC1F;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE7D,GAAG,EAAE;EAChCA,GAAG,GAAGA,GAAG,IAAI6D,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;EACpC9D,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAAC+D,cAAc,CAAC,CAAC;EACpB/D,GAAG,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAACI,MAAM,CAAC;EAChDjE,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAASa,SAASA,CAAClE,GAAG,EAAE5b,OAAO,EAAEoE,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAI9G,IAAI,EAAEwiB,OAAO,EAAEC,OAAO,EAAEhe,IAAI,EAAEie,YAAY;EAC9C,MAAMzE,KAAK,GAAGxb,OAAO,CAACkgB,UAAU;EAChC,MAAMC,QAAQ,GAAGngB,OAAO,CAACmgB,QAAQ;EACjC,MAAMC,MAAM,GAAGpgB,OAAO,CAACogB,MAAM;EAC7B,IAAIC,GAAG,GAAG,CAACF,QAAQ,IAAI,CAAC,IAAIxd,WAAW;EACvC,IAAI6Y,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtCje,IAAI,GAAGie,KAAK,CAAC/d,QAAQ,CAAC,CAAC;IACvB,IAAIF,IAAI,KAAK,2BAA2B,IAAIA,IAAI,KAAK,4BAA4B,EAAE;MACjFqe,GAAG,CAACiD,IAAI,CAAC,CAAC;MACVjD,GAAG,CAAC0E,SAAS,CAAClc,CAAC,EAAEC,CAAC,CAAC;MACnBuX,GAAG,CAACzP,MAAM,CAACkU,GAAG,CAAC;MACfzE,GAAG,CAAC2E,SAAS,CAAC/E,KAAK,EAAE,CAACA,KAAK,CAACgD,KAAK,GAAG,CAAC,EAAE,CAAChD,KAAK,CAACqE,MAAM,GAAG,CAAC,EAAErE,KAAK,CAACgD,KAAK,EAAEhD,KAAK,CAACqE,MAAM,CAAC;MACpFjE,GAAG,CAACqD,OAAO,CAAC,CAAC;MACb;IACF;EACF;EACA,IAAI9a,KAAK,CAACic,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IAChC;EACF;EACAxE,GAAG,CAAC4E,SAAS,CAAC,CAAC;EACf,QAAQhF,KAAK;IACb;MACEI,GAAG,CAAC6E,GAAG,CAACrc,CAAC,EAAEC,CAAC,EAAE+b,MAAM,EAAE,CAAC,EAAE7d,GAAG,CAAC;MAC7BqZ,GAAG,CAAC8E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACb9E,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG9B,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM,EAAE/b,CAAC,GAAG/B,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClEC,GAAG,IAAIvd,aAAa;MACpB8Y,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG9B,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM,EAAE/b,CAAC,GAAG/B,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClEC,GAAG,IAAIvd,aAAa;MACpB8Y,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG9B,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM,EAAE/b,CAAC,GAAG/B,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClExE,GAAG,CAAC8E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,aAAa;MAChBT,YAAY,GAAGG,MAAM,GAAG,KAAK;MAC7Bpe,IAAI,GAAGoe,MAAM,GAAGH,YAAY;MAC5BF,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,GAAGxd,UAAU,CAAC,GAAGb,IAAI;MAC3Cge,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,GAAGxd,UAAU,CAAC,GAAGb,IAAI;MAC3C4Z,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,EAAEC,YAAY,EAAEI,GAAG,GAAGhe,EAAE,EAAEge,GAAG,GAAGzd,OAAO,CAAC;MACxEgZ,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,EAAEE,YAAY,EAAEI,GAAG,GAAGzd,OAAO,EAAEyd,GAAG,CAAC;MACnEzE,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,EAAEC,YAAY,EAAEI,GAAG,EAAEA,GAAG,GAAGzd,OAAO,CAAC;MACnEgZ,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,EAAEE,YAAY,EAAEI,GAAG,GAAGzd,OAAO,EAAEyd,GAAG,GAAGhe,EAAE,CAAC;MACxEuZ,GAAG,CAAC8E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,MAAM;MACT,IAAI,CAACP,QAAQ,EAAE;QACbne,IAAI,GAAGM,IAAI,CAACue,OAAO,GAAGT,MAAM;QAC5BxE,GAAG,CAACkF,IAAI,CAAC1c,CAAC,GAAGpC,IAAI,EAAEqC,CAAC,GAAGrC,IAAI,EAAE,CAAC,GAAGA,IAAI,EAAE,CAAC,GAAGA,IAAI,CAAC;QAChD;MACF;MACAqe,GAAG,IAAIxd,UAAU;IACnB,KAAK,SAAS;MACZkd,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCnE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCnE,GAAG,CAAC8E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACbL,GAAG,IAAIxd,UAAU;IACnB,KAAK,OAAO;MACVkd,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCnE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCnE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCM,GAAG,IAAIxd,UAAU;MACjBkd,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpCnE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG4b,OAAO,EAAE3b,CAAC,GAAG0b,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGzd,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAG1d,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG2b,OAAO,EAAE1b,CAAC,GAAG2b,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTpE,GAAG,CAAC+E,MAAM,CAACvc,CAAC,EAAEC,CAAC,CAAC;MAChBuX,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAG9B,IAAI,CAAC2F,GAAG,CAACoY,GAAG,CAAC,GAAGD,MAAM,EAAE/b,CAAC,GAAG/B,IAAI,CAAC0E,GAAG,CAACqZ,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClE;EACF;EACAxE,GAAG,CAACmF,IAAI,CAAC,CAAC;EACV,IAAI/gB,OAAO,CAACghB,WAAW,GAAG,CAAC,EAAE;IAC3BpF,GAAG,CAACqF,MAAM,CAAC,CAAC;EACd;AACF;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC3CA,MAAM,GAAGA,MAAM,IAAI,GAAG;EACtB,OAAO,CAACD,IAAI,IAAKD,KAAK,IAAIA,KAAK,CAAC/c,CAAC,GAAGgd,IAAI,CAACvkB,IAAI,GAAGwkB,MAAM,IAAIF,KAAK,CAAC/c,CAAC,GAAGgd,IAAI,CAACtkB,KAAK,GAAGukB,MAAM,IACvFF,KAAK,CAAC9c,CAAC,GAAG+c,IAAI,CAACE,GAAG,GAAGD,MAAM,IAAIF,KAAK,CAAC9c,CAAC,GAAG+c,IAAI,CAACG,MAAM,GAAGF,MAAO;AAChE;AACA,SAASG,QAAQA,CAAC5F,GAAG,EAAEwF,IAAI,EAAE;EAC3BxF,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAAC4E,SAAS,CAAC,CAAC;EACf5E,GAAG,CAACkF,IAAI,CAACM,IAAI,CAACvkB,IAAI,EAAEukB,IAAI,CAACE,GAAG,EAAEF,IAAI,CAACtkB,KAAK,GAAGskB,IAAI,CAACvkB,IAAI,EAAEukB,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACE,GAAG,CAAC;EAC7E1F,GAAG,CAAC6F,IAAI,CAAC,CAAC;AACZ;AACA,SAASC,UAAUA,CAAC9F,GAAG,EAAE;EACvBA,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAAS0C,cAAcA,CAAC/F,GAAG,EAAEjb,QAAQ,EAAEnB,MAAM,EAAEoiB,IAAI,EAAE3F,IAAI,EAAE;EACzD,IAAI,CAACtb,QAAQ,EAAE;IACb,OAAOib,GAAG,CAACgF,MAAM,CAACphB,MAAM,CAAC4E,CAAC,EAAE5E,MAAM,CAAC6E,CAAC,CAAC;EACvC;EACA,IAAI4X,IAAI,KAAK,QAAQ,EAAE;IACrB,MAAM4F,QAAQ,GAAG,CAAClhB,QAAQ,CAACyD,CAAC,GAAG5E,MAAM,CAAC4E,CAAC,IAAI,GAAG;IAC9CwX,GAAG,CAACgF,MAAM,CAACiB,QAAQ,EAAElhB,QAAQ,CAAC0D,CAAC,CAAC;IAChCuX,GAAG,CAACgF,MAAM,CAACiB,QAAQ,EAAEriB,MAAM,CAAC6E,CAAC,CAAC;EAChC,CAAC,MAAM,IAAI4X,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC2F,IAAI,EAAE;IACtChG,GAAG,CAACgF,MAAM,CAACjgB,QAAQ,CAACyD,CAAC,EAAE5E,MAAM,CAAC6E,CAAC,CAAC;EAClC,CAAC,MAAM;IACLuX,GAAG,CAACgF,MAAM,CAACphB,MAAM,CAAC4E,CAAC,EAAEzD,QAAQ,CAAC0D,CAAC,CAAC;EAClC;EACAuX,GAAG,CAACgF,MAAM,CAACphB,MAAM,CAAC4E,CAAC,EAAE5E,MAAM,CAAC6E,CAAC,CAAC;AAChC;AACA,SAASyd,cAAcA,CAAClG,GAAG,EAAEjb,QAAQ,EAAEnB,MAAM,EAAEoiB,IAAI,EAAE;EACnD,IAAI,CAACjhB,QAAQ,EAAE;IACb,OAAOib,GAAG,CAACgF,MAAM,CAACphB,MAAM,CAAC4E,CAAC,EAAE5E,MAAM,CAAC6E,CAAC,CAAC;EACvC;EACAuX,GAAG,CAACmG,aAAa,CACfH,IAAI,GAAGjhB,QAAQ,CAACqhB,IAAI,GAAGrhB,QAAQ,CAACshB,IAAI,EACpCL,IAAI,GAAGjhB,QAAQ,CAACuhB,IAAI,GAAGvhB,QAAQ,CAACwhB,IAAI,EACpCP,IAAI,GAAGpiB,MAAM,CAACyiB,IAAI,GAAGziB,MAAM,CAACwiB,IAAI,EAChCJ,IAAI,GAAGpiB,MAAM,CAAC2iB,IAAI,GAAG3iB,MAAM,CAAC0iB,IAAI,EAChC1iB,MAAM,CAAC4E,CAAC,EACR5E,MAAM,CAAC6E,CAAC,CAAC;AACb;AACA,SAAS+d,UAAUA,CAACxG,GAAG,EAAEyG,IAAI,EAAEje,CAAC,EAAEC,CAAC,EAAEiX,IAAI,EAAa;EAAA,IAAXgH,IAAI,GAAA3mB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,CAAC,CAAC;EAClD,MAAM4mB,KAAK,GAAGjlB,OAAO,CAAC+kB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EAC3C,MAAMpB,MAAM,GAAGqB,IAAI,CAACE,WAAW,GAAG,CAAC,IAAIF,IAAI,CAACG,WAAW,KAAK,EAAE;EAC9D,IAAI/jB,CAAC,EAAEgkB,IAAI;EACX9G,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAACN,IAAI,GAAGA,IAAI,CAAC+C,MAAM;EACtBsE,aAAa,CAAC/G,GAAG,EAAE0G,IAAI,CAAC;EACxB,KAAK5jB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6jB,KAAK,CAAC3mB,MAAM,EAAE,EAAE8C,CAAC,EAAE;IACjCgkB,IAAI,GAAGH,KAAK,CAAC7jB,CAAC,CAAC;IACf,IAAIuiB,MAAM,EAAE;MACV,IAAIqB,IAAI,CAACG,WAAW,EAAE;QACpB7G,GAAG,CAACgH,WAAW,GAAGN,IAAI,CAACG,WAAW;MACpC;MACA,IAAI,CAACrlB,aAAa,CAACklB,IAAI,CAACE,WAAW,CAAC,EAAE;QACpC5G,GAAG,CAACiH,SAAS,GAAGP,IAAI,CAACE,WAAW;MAClC;MACA5G,GAAG,CAACkH,UAAU,CAACJ,IAAI,EAAEte,CAAC,EAAEC,CAAC,EAAEie,IAAI,CAACS,QAAQ,CAAC;IAC3C;IACAnH,GAAG,CAACoH,QAAQ,CAACN,IAAI,EAAEte,CAAC,EAAEC,CAAC,EAAEie,IAAI,CAACS,QAAQ,CAAC;IACvCE,YAAY,CAACrH,GAAG,EAAExX,CAAC,EAAEC,CAAC,EAAEqe,IAAI,EAAEJ,IAAI,CAAC;IACnCje,CAAC,IAAIiX,IAAI,CAACG,UAAU;EACtB;EACAG,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAAS0D,aAAaA,CAAC/G,GAAG,EAAE0G,IAAI,EAAE;EAChC,IAAIA,IAAI,CAACY,WAAW,EAAE;IACpBtH,GAAG,CAAC0E,SAAS,CAACgC,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC;EACzD;EACA,IAAI,CAAC9lB,aAAa,CAACklB,IAAI,CAACnC,QAAQ,CAAC,EAAE;IACjCvE,GAAG,CAACzP,MAAM,CAACmW,IAAI,CAACnC,QAAQ,CAAC;EAC3B;EACA,IAAImC,IAAI,CAAC3J,KAAK,EAAE;IACdiD,GAAG,CAACuH,SAAS,GAAGb,IAAI,CAAC3J,KAAK;EAC5B;EACA,IAAI2J,IAAI,CAACc,SAAS,EAAE;IAClBxH,GAAG,CAACwH,SAAS,GAAGd,IAAI,CAACc,SAAS;EAChC;EACA,IAAId,IAAI,CAACe,YAAY,EAAE;IACrBzH,GAAG,CAACyH,YAAY,GAAGf,IAAI,CAACe,YAAY;EACtC;AACF;AACA,SAASJ,YAAYA,CAACrH,GAAG,EAAExX,CAAC,EAAEC,CAAC,EAAEqe,IAAI,EAAEJ,IAAI,EAAE;EAC3C,IAAIA,IAAI,CAACgB,aAAa,IAAIhB,IAAI,CAACiB,SAAS,EAAE;IACxC,MAAMC,OAAO,GAAG5H,GAAG,CAAC2C,WAAW,CAACmE,IAAI,CAAC;IACrC,MAAM7lB,IAAI,GAAGuH,CAAC,GAAGof,OAAO,CAACC,qBAAqB;IAC9C,MAAM3mB,KAAK,GAAGsH,CAAC,GAAGof,OAAO,CAACE,sBAAsB;IAChD,MAAMpC,GAAG,GAAGjd,CAAC,GAAGmf,OAAO,CAACG,uBAAuB;IAC/C,MAAMpC,MAAM,GAAGld,CAAC,GAAGmf,OAAO,CAACI,wBAAwB;IACnD,MAAMC,WAAW,GAAGvB,IAAI,CAACgB,aAAa,GAAG,CAAChC,GAAG,GAAGC,MAAM,IAAI,CAAC,GAAGA,MAAM;IACpE3F,GAAG,CAACgH,WAAW,GAAGhH,GAAG,CAACuH,SAAS;IAC/BvH,GAAG,CAAC4E,SAAS,CAAC,CAAC;IACf5E,GAAG,CAACiH,SAAS,GAAGP,IAAI,CAACwB,eAAe,IAAI,CAAC;IACzClI,GAAG,CAAC+E,MAAM,CAAC9jB,IAAI,EAAEgnB,WAAW,CAAC;IAC7BjI,GAAG,CAACgF,MAAM,CAAC9jB,KAAK,EAAE+mB,WAAW,CAAC;IAC9BjI,GAAG,CAACqF,MAAM,CAAC,CAAC;EACd;AACF;AACA,SAAS8C,kBAAkBA,CAACnI,GAAG,EAAEkF,IAAI,EAAE;EACrC,MAAM;IAAC1c,CAAC;IAAEC,CAAC;IAAEmH,CAAC;IAAEd,CAAC;IAAE0V;EAAM,CAAC,GAAGU,IAAI;EACjClF,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAGgc,MAAM,CAAC4D,OAAO,EAAE3f,CAAC,GAAG+b,MAAM,CAAC4D,OAAO,EAAE5D,MAAM,CAAC4D,OAAO,EAAE,CAACphB,OAAO,EAAEP,EAAE,EAAE,IAAI,CAAC;EACnFuZ,GAAG,CAACgF,MAAM,CAACxc,CAAC,EAAEC,CAAC,GAAGqG,CAAC,GAAG0V,MAAM,CAAC6D,UAAU,CAAC;EACxCrI,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAGgc,MAAM,CAAC6D,UAAU,EAAE5f,CAAC,GAAGqG,CAAC,GAAG0V,MAAM,CAAC6D,UAAU,EAAE7D,MAAM,CAAC6D,UAAU,EAAE5hB,EAAE,EAAEO,OAAO,EAAE,IAAI,CAAC;EAC/FgZ,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAGoH,CAAC,GAAG4U,MAAM,CAAC8D,WAAW,EAAE7f,CAAC,GAAGqG,CAAC,CAAC;EAC7CkR,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAGoH,CAAC,GAAG4U,MAAM,CAAC8D,WAAW,EAAE7f,CAAC,GAAGqG,CAAC,GAAG0V,MAAM,CAAC8D,WAAW,EAAE9D,MAAM,CAAC8D,WAAW,EAAEthB,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;EACrGgZ,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAGoH,CAAC,EAAEnH,CAAC,GAAG+b,MAAM,CAAC+D,QAAQ,CAAC;EACtCvI,GAAG,CAAC6E,GAAG,CAACrc,CAAC,GAAGoH,CAAC,GAAG4U,MAAM,CAAC+D,QAAQ,EAAE9f,CAAC,GAAG+b,MAAM,CAAC+D,QAAQ,EAAE/D,MAAM,CAAC+D,QAAQ,EAAE,CAAC,EAAE,CAACvhB,OAAO,EAAE,IAAI,CAAC;EACzFgZ,GAAG,CAACgF,MAAM,CAACxc,CAAC,GAAGgc,MAAM,CAAC4D,OAAO,EAAE3f,CAAC,CAAC;AACnC;AAEA,MAAM+f,WAAW,GAAG,IAAIC,MAAM,CAAC,sCAAsC,CAAC;AACtE,MAAMC,UAAU,GAAG,IAAID,MAAM,CAAC,uEAAuE,CAAC;AACtG,SAASE,YAAYA,CAAClnB,KAAK,EAAE2E,IAAI,EAAE;EACjC,MAAMwiB,OAAO,GAAG,CAAC,EAAE,GAAGnnB,KAAK,EAAEonB,KAAK,CAACL,WAAW,CAAC;EAC/C,IAAI,CAACI,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACvC,OAAOxiB,IAAI,GAAG,GAAG;EACnB;EACA3E,KAAK,GAAG,CAACmnB,OAAO,CAAC,CAAC,CAAC;EACnB,QAAQA,OAAO,CAAC,CAAC,CAAC;IAClB,KAAK,IAAI;MACP,OAAOnnB,KAAK;IACd,KAAK,GAAG;MACNA,KAAK,IAAI,GAAG;MACZ;EACF;EACA,OAAO2E,IAAI,GAAG3E,KAAK;AACrB;AACA,MAAMqnB,YAAY,GAAGxa,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC;AACjC,SAASya,iBAAiBA,CAACtnB,KAAK,EAAEunB,KAAK,EAAE;EACvC,MAAMta,GAAG,GAAG,CAAC,CAAC;EACd,MAAMua,QAAQ,GAAGlnB,QAAQ,CAACinB,KAAK,CAAC;EAChC,MAAMhmB,IAAI,GAAGimB,QAAQ,GAAGrnB,MAAM,CAACoB,IAAI,CAACgmB,KAAK,CAAC,GAAGA,KAAK;EAClD,MAAME,IAAI,GAAGnnB,QAAQ,CAACN,KAAK,CAAC,GACxBwnB,QAAQ,GACNE,IAAI,IAAI9mB,cAAc,CAACZ,KAAK,CAAC0nB,IAAI,CAAC,EAAE1nB,KAAK,CAACunB,KAAK,CAACG,IAAI,CAAC,CAAC,CAAC,GACvDA,IAAI,IAAI1nB,KAAK,CAAC0nB,IAAI,CAAC,GACrB,MAAM1nB,KAAK;EACf,KAAK,MAAM0nB,IAAI,IAAInmB,IAAI,EAAE;IACvB0L,GAAG,CAACya,IAAI,CAAC,GAAGL,YAAY,CAACI,IAAI,CAACC,IAAI,CAAC,CAAC;EACtC;EACA,OAAOza,GAAG;AACZ;AACA,SAAS0a,MAAMA,CAAC3nB,KAAK,EAAE;EACrB,OAAOsnB,iBAAiB,CAACtnB,KAAK,EAAE;IAACikB,GAAG,EAAE,GAAG;IAAExkB,KAAK,EAAE,GAAG;IAAEykB,MAAM,EAAE,GAAG;IAAE1kB,IAAI,EAAE;EAAG,CAAC,CAAC;AACjF;AACA,SAASooB,aAAaA,CAAC5nB,KAAK,EAAE;EAC5B,OAAOsnB,iBAAiB,CAACtnB,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AACvF;AACA,SAAS6nB,SAASA,CAAC7nB,KAAK,EAAE;EACxB,MAAMgE,GAAG,GAAG2jB,MAAM,CAAC3nB,KAAK,CAAC;EACzBgE,GAAG,CAACmd,KAAK,GAAGnd,GAAG,CAACxE,IAAI,GAAGwE,GAAG,CAACvE,KAAK;EAChCuE,GAAG,CAACwe,MAAM,GAAGxe,GAAG,CAACigB,GAAG,GAAGjgB,GAAG,CAACkgB,MAAM;EACjC,OAAOlgB,GAAG;AACZ;AACA,SAAS8jB,MAAMA,CAACnlB,OAAO,EAAEolB,QAAQ,EAAE;EACjCplB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBolB,QAAQ,GAAGA,QAAQ,IAAIzH,QAAQ,CAACrC,IAAI;EACpC,IAAItZ,IAAI,GAAG/D,cAAc,CAAC+B,OAAO,CAACgC,IAAI,EAAEojB,QAAQ,CAACpjB,IAAI,CAAC;EACtD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAGuV,QAAQ,CAACvV,IAAI,EAAE,EAAE,CAAC;EAC3B;EACA,IAAIwZ,KAAK,GAAGvd,cAAc,CAAC+B,OAAO,CAACwb,KAAK,EAAE4J,QAAQ,CAAC5J,KAAK,CAAC;EACzD,IAAIA,KAAK,IAAI,CAAC,CAAC,EAAE,GAAGA,KAAK,EAAEiJ,KAAK,CAACH,UAAU,CAAC,EAAE;IAC5CxjB,OAAO,CAACC,IAAI,CAAC,iCAAiC,GAAGya,KAAK,GAAG,GAAG,CAAC;IAC7DA,KAAK,GAAG,EAAE;EACZ;EACA,MAAMF,IAAI,GAAG;IACXC,MAAM,EAAEtd,cAAc,CAAC+B,OAAO,CAACub,MAAM,EAAE6J,QAAQ,CAAC7J,MAAM,CAAC;IACvDE,UAAU,EAAE8I,YAAY,CAACtmB,cAAc,CAAC+B,OAAO,CAACyb,UAAU,EAAE2J,QAAQ,CAAC3J,UAAU,CAAC,EAAEzZ,IAAI,CAAC;IACvFA,IAAI;IACJwZ,KAAK;IACL5C,MAAM,EAAE3a,cAAc,CAAC+B,OAAO,CAAC4Y,MAAM,EAAEwM,QAAQ,CAACxM,MAAM,CAAC;IACvDyF,MAAM,EAAE;EACV,CAAC;EACD/C,IAAI,CAAC+C,MAAM,GAAGL,YAAY,CAAC1C,IAAI,CAAC;EAChC,OAAOA,IAAI;AACb;AACA,SAAS+J,OAAOA,CAACC,MAAM,EAAEtK,OAAO,EAAE5b,KAAK,EAAEmmB,IAAI,EAAE;EAC7C,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAI9mB,CAAC,EAAEM,IAAI,EAAE3B,KAAK;EAClB,KAAKqB,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAGsmB,MAAM,CAAC1pB,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;IAC/CrB,KAAK,GAAGioB,MAAM,CAAC5mB,CAAC,CAAC;IACjB,IAAIrB,KAAK,KAAKwD,SAAS,EAAE;MACvB;IACF;IACA,IAAIma,OAAO,KAAKna,SAAS,IAAI,OAAOxD,KAAK,KAAK,UAAU,EAAE;MACxDA,KAAK,GAAGA,KAAK,CAAC2d,OAAO,CAAC;MACtBwK,SAAS,GAAG,KAAK;IACnB;IACA,IAAIpmB,KAAK,KAAKyB,SAAS,IAAIvD,OAAO,CAACD,KAAK,CAAC,EAAE;MACzCA,KAAK,GAAGA,KAAK,CAAC+B,KAAK,GAAG/B,KAAK,CAACzB,MAAM,CAAC;MACnC4pB,SAAS,GAAG,KAAK;IACnB;IACA,IAAInoB,KAAK,KAAKwD,SAAS,EAAE;MACvB,IAAI0kB,IAAI,IAAI,CAACC,SAAS,EAAE;QACtBD,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB;MACA,OAAOnoB,KAAK;IACd;EACF;AACF;AACA,SAASooB,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC7C,MAAM;IAAC/gB,GAAG;IAAEC;EAAG,CAAC,GAAG4gB,MAAM;EACzB,MAAMG,MAAM,GAAGvnB,WAAW,CAACqnB,KAAK,EAAE,CAAC7gB,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAC;EAClD,MAAMihB,QAAQ,GAAGA,CAACzoB,KAAK,EAAE0oB,GAAG,KAAKH,WAAW,IAAIvoB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG0oB,GAAG;EAC7E,OAAO;IACLlhB,GAAG,EAAEihB,QAAQ,CAACjhB,GAAG,EAAE,CAACvC,IAAI,CAACiC,GAAG,CAACshB,MAAM,CAAC,CAAC;IACrC/gB,GAAG,EAAEghB,QAAQ,CAAChhB,GAAG,EAAE+gB,MAAM;EAC3B,CAAC;AACH;AACA,SAASG,aAAaA,CAACC,aAAa,EAAEjL,OAAO,EAAE;EAC7C,OAAOxd,MAAM,CAACya,MAAM,CAACza,MAAM,CAACiC,MAAM,CAACwmB,aAAa,CAAC,EAAEjL,OAAO,CAAC;AAC7D;AAEA,SAASkL,OAAOA,CAACC,KAAK,EAAE9oB,KAAK,EAAE+oB,GAAG,EAAE;EAClCA,GAAG,GAAGA,GAAG,KAAMhnB,KAAK,IAAK+mB,KAAK,CAAC/mB,KAAK,CAAC,GAAG/B,KAAK,CAAC;EAC9C,IAAIgpB,EAAE,GAAGF,KAAK,CAACvqB,MAAM,GAAG,CAAC;EACzB,IAAI0qB,EAAE,GAAG,CAAC;EACV,IAAIC,GAAG;EACP,OAAOF,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAE;IAClBC,GAAG,GAAID,EAAE,GAAGD,EAAE,IAAK,CAAC;IACpB,IAAID,GAAG,CAACG,GAAG,CAAC,EAAE;MACZD,EAAE,GAAGC,GAAG;IACV,CAAC,MAAM;MACLF,EAAE,GAAGE,GAAG;IACV;EACF;EACA,OAAO;IAACD,EAAE;IAAED;EAAE,CAAC;AACjB;AACA,MAAMG,YAAY,GAAGA,CAACL,KAAK,EAAEtmB,GAAG,EAAExC,KAAK,KACrC6oB,OAAO,CAACC,KAAK,EAAE9oB,KAAK,EAAE+B,KAAK,IAAI+mB,KAAK,CAAC/mB,KAAK,CAAC,CAACS,GAAG,CAAC,GAAGxC,KAAK,CAAC;AAC3D,MAAMopB,aAAa,GAAGA,CAACN,KAAK,EAAEtmB,GAAG,EAAExC,KAAK,KACtC6oB,OAAO,CAACC,KAAK,EAAE9oB,KAAK,EAAE+B,KAAK,IAAI+mB,KAAK,CAAC/mB,KAAK,CAAC,CAACS,GAAG,CAAC,IAAIxC,KAAK,CAAC;AAC5D,SAASqpB,cAAcA,CAAClM,MAAM,EAAE3V,GAAG,EAAEC,GAAG,EAAE;EACxC,IAAIpI,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG6d,MAAM,CAAC5e,MAAM;EACvB,OAAOc,KAAK,GAAGC,GAAG,IAAI6d,MAAM,CAAC9d,KAAK,CAAC,GAAGmI,GAAG,EAAE;IACzCnI,KAAK,EAAE;EACT;EACA,OAAOC,GAAG,GAAGD,KAAK,IAAI8d,MAAM,CAAC7d,GAAG,GAAG,CAAC,CAAC,GAAGmI,GAAG,EAAE;IAC3CnI,GAAG,EAAE;EACP;EACA,OAAOD,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAG6d,MAAM,CAAC5e,MAAM,GACnC4e,MAAM,CAACjf,KAAK,CAACmB,KAAK,EAAEC,GAAG,CAAC,GACxB6d,MAAM;AACZ;AACA,MAAMmM,WAAW,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;AACjE,SAASC,iBAAiBA,CAACjiB,KAAK,EAAEkiB,QAAQ,EAAE;EAC1C,IAAIliB,KAAK,CAACmiB,QAAQ,EAAE;IAClBniB,KAAK,CAACmiB,QAAQ,CAACC,SAAS,CAACjjB,IAAI,CAAC+iB,QAAQ,CAAC;IACvC;EACF;EACArpB,MAAM,CAACwpB,cAAc,CAACriB,KAAK,EAAE,UAAU,EAAE;IACvCsiB,YAAY,EAAE,IAAI;IAClBxJ,UAAU,EAAE,KAAK;IACjBpgB,KAAK,EAAE;MACL0pB,SAAS,EAAE,CAACF,QAAQ;IACtB;EACF,CAAC,CAAC;EACFF,WAAW,CAACO,OAAO,CAAErnB,GAAG,IAAK;IAC3B,MAAMsnB,MAAM,GAAG,SAAS,GAAG5lB,WAAW,CAAC1B,GAAG,CAAC;IAC3C,MAAMunB,IAAI,GAAGziB,KAAK,CAAC9E,GAAG,CAAC;IACvBrC,MAAM,CAACwpB,cAAc,CAACriB,KAAK,EAAE9E,GAAG,EAAE;MAChConB,YAAY,EAAE,IAAI;MAClBxJ,UAAU,EAAE,KAAK;MACjBpgB,KAAKA,CAAA,EAAU;QAAA,SAAAgqB,KAAA,GAAA1rB,SAAA,CAAAC,MAAA,EAANR,IAAI,OAAAC,KAAA,CAAAgsB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJlsB,IAAI,CAAAksB,KAAA,IAAA3rB,SAAA,CAAA2rB,KAAA;QAAA;QACX,MAAMC,GAAG,GAAGH,IAAI,CAACrrB,KAAK,CAAC,IAAI,EAAEX,IAAI,CAAC;QAClCuJ,KAAK,CAACmiB,QAAQ,CAACC,SAAS,CAACG,OAAO,CAAEM,MAAM,IAAK;UAC3C,IAAI,OAAOA,MAAM,CAACL,MAAM,CAAC,KAAK,UAAU,EAAE;YACxCK,MAAM,CAACL,MAAM,CAAC,CAAC,GAAG/rB,IAAI,CAAC;UACzB;QACF,CAAC,CAAC;QACF,OAAOmsB,GAAG;MACZ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASE,mBAAmBA,CAAC9iB,KAAK,EAAEkiB,QAAQ,EAAE;EAC5C,MAAMa,IAAI,GAAG/iB,KAAK,CAACmiB,QAAQ;EAC3B,IAAI,CAACY,IAAI,EAAE;IACT;EACF;EACA,MAAMX,SAAS,GAAGW,IAAI,CAACX,SAAS;EAChC,MAAM3nB,KAAK,GAAG2nB,SAAS,CAACjnB,OAAO,CAAC+mB,QAAQ,CAAC;EACzC,IAAIznB,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB2nB,SAAS,CAAC5H,MAAM,CAAC/f,KAAK,EAAE,CAAC,CAAC;EAC5B;EACA,IAAI2nB,SAAS,CAACnrB,MAAM,GAAG,CAAC,EAAE;IACxB;EACF;EACA+qB,WAAW,CAACO,OAAO,CAAErnB,GAAG,IAAK;IAC3B,OAAO8E,KAAK,CAAC9E,GAAG,CAAC;EACnB,CAAC,CAAC;EACF,OAAO8E,KAAK,CAACmiB,QAAQ;AACvB;AACA,SAASa,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMtN,GAAG,GAAG,IAAIuN,GAAG,CAAC,CAAC;EACrB,IAAInpB,CAAC,EAAEM,IAAI;EACX,KAAKN,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAG4oB,KAAK,CAAChsB,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;IAC9C4b,GAAG,CAACyL,GAAG,CAAC6B,KAAK,CAAClpB,CAAC,CAAC,CAAC;EACnB;EACA,IAAI4b,GAAG,CAACtY,IAAI,KAAKhD,IAAI,EAAE;IACrB,OAAO4oB,KAAK;EACd;EACA,OAAOvsB,KAAK,CAACysB,IAAI,CAACxN,GAAG,CAAC;AACxB;AAEA,SAASyN,eAAeA,CAACC,MAAM,EAA+E;EAAA,IAA7EC,QAAQ,GAAAtsB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,CAAC,EAAE,CAAC;EAAA,IAAEusB,UAAU,GAAAvsB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAGqsB,MAAM;EAAA,IAAE5C,QAAQ,GAAAzpB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAkF,SAAA;EAAA,IAAEsnB,SAAS,GAAAxsB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,MAAMqsB,MAAM,CAAC,CAAC,CAAC;EAC1G,IAAI,CAACrmB,OAAO,CAACyjB,QAAQ,CAAC,EAAE;IACtBA,QAAQ,GAAGgD,QAAQ,CAAC,WAAW,EAAEJ,MAAM,CAAC;EAC1C;EACA,MAAMrJ,KAAK,GAAG;IACZ,CAAC0J,MAAM,CAACC,WAAW,GAAG,QAAQ;IAC9BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAER,MAAM;IACfS,WAAW,EAAEP,UAAU;IACvBnK,SAAS,EAAEqH,QAAQ;IACnBsD,UAAU,EAAEP,SAAS;IACrBpL,QAAQ,EAAGrc,KAAK,IAAKqnB,eAAe,CAAC,CAACrnB,KAAK,EAAE,GAAGsnB,MAAM,CAAC,EAAEC,QAAQ,EAAEC,UAAU,EAAE9C,QAAQ;EACzF,CAAC;EACD,OAAO,IAAIuD,KAAK,CAAChK,KAAK,EAAE;IACtBiK,cAAcA,CAACppB,MAAM,EAAEulB,IAAI,EAAE;MAC3B,OAAOvlB,MAAM,CAACulB,IAAI,CAAC;MACnB,OAAOvlB,MAAM,CAACqpB,KAAK;MACnB,OAAOb,MAAM,CAAC,CAAC,CAAC,CAACjD,IAAI,CAAC;MACtB,OAAO,IAAI;IACb,CAAC;IACDjI,GAAGA,CAACtd,MAAM,EAAEulB,IAAI,EAAE;MAChB,OAAO+D,OAAO,CAACtpB,MAAM,EAAEulB,IAAI,EACzB,MAAMgE,oBAAoB,CAAChE,IAAI,EAAEkD,QAAQ,EAAED,MAAM,EAAExoB,MAAM,CAAC,CAAC;IAC/D,CAAC;IACDwpB,wBAAwBA,CAACxpB,MAAM,EAAEulB,IAAI,EAAE;MACrC,OAAOkE,OAAO,CAACD,wBAAwB,CAACxpB,MAAM,CAACgpB,OAAO,CAAC,CAAC,CAAC,EAAEzD,IAAI,CAAC;IAClE,CAAC;IACDmE,cAAcA,CAAA,EAAG;MACf,OAAOD,OAAO,CAACC,cAAc,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IACD9lB,GAAGA,CAAC1C,MAAM,EAAEulB,IAAI,EAAE;MAChB,OAAOoE,oBAAoB,CAAC3pB,MAAM,CAAC,CAAC4pB,QAAQ,CAACrE,IAAI,CAAC;IACpD,CAAC;IACDsE,OAAOA,CAAC7pB,MAAM,EAAE;MACd,OAAO2pB,oBAAoB,CAAC3pB,MAAM,CAAC;IACrC,CAAC;IACD8a,GAAGA,CAAC9a,MAAM,EAAEulB,IAAI,EAAE1nB,KAAK,EAAE;MACvB,MAAMisB,OAAO,GAAG9pB,MAAM,CAAC+pB,QAAQ,KAAK/pB,MAAM,CAAC+pB,QAAQ,GAAGpB,SAAS,CAAC,CAAC,CAAC;MAClE3oB,MAAM,CAACulB,IAAI,CAAC,GAAGuE,OAAO,CAACvE,IAAI,CAAC,GAAG1nB,KAAK;MACpC,OAAOmC,MAAM,CAACqpB,KAAK;MACnB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AACA,SAASW,cAAcA,CAACC,KAAK,EAAEzO,OAAO,EAAE0O,QAAQ,EAAEC,kBAAkB,EAAE;EACpE,MAAMhL,KAAK,GAAG;IACZ4J,UAAU,EAAE,KAAK;IACjBqB,MAAM,EAAEH,KAAK;IACbI,QAAQ,EAAE7O,OAAO;IACjB8O,SAAS,EAAEJ,QAAQ;IACnBK,MAAM,EAAE,IAAIlC,GAAG,CAAC,CAAC;IACjBnN,YAAY,EAAEA,YAAY,CAAC+O,KAAK,EAAEE,kBAAkB,CAAC;IACrDK,UAAU,EAAGpO,GAAG,IAAK4N,cAAc,CAACC,KAAK,EAAE7N,GAAG,EAAE8N,QAAQ,EAAEC,kBAAkB,CAAC;IAC7E5M,QAAQ,EAAGrc,KAAK,IAAK8oB,cAAc,CAACC,KAAK,CAAC1M,QAAQ,CAACrc,KAAK,CAAC,EAAEsa,OAAO,EAAE0O,QAAQ,EAAEC,kBAAkB;EAClG,CAAC;EACD,OAAO,IAAIhB,KAAK,CAAChK,KAAK,EAAE;IACtBiK,cAAcA,CAACppB,MAAM,EAAEulB,IAAI,EAAE;MAC3B,OAAOvlB,MAAM,CAACulB,IAAI,CAAC;MACnB,OAAO0E,KAAK,CAAC1E,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC;IACDjI,GAAGA,CAACtd,MAAM,EAAEulB,IAAI,EAAEkF,QAAQ,EAAE;MAC1B,OAAOnB,OAAO,CAACtpB,MAAM,EAAEulB,IAAI,EACzB,MAAMmF,mBAAmB,CAAC1qB,MAAM,EAAEulB,IAAI,EAAEkF,QAAQ,CAAC,CAAC;IACtD,CAAC;IACDjB,wBAAwBA,CAACxpB,MAAM,EAAEulB,IAAI,EAAE;MACrC,OAAOvlB,MAAM,CAACkb,YAAY,CAACyP,OAAO,GAC9BlB,OAAO,CAAC/mB,GAAG,CAACunB,KAAK,EAAE1E,IAAI,CAAC,GAAG;QAACtH,UAAU,EAAE,IAAI;QAAEwJ,YAAY,EAAE;MAAI,CAAC,GAAGpmB,SAAS,GAC7EooB,OAAO,CAACD,wBAAwB,CAACS,KAAK,EAAE1E,IAAI,CAAC;IACnD,CAAC;IACDmE,cAAcA,CAAA,EAAG;MACf,OAAOD,OAAO,CAACC,cAAc,CAACO,KAAK,CAAC;IACtC,CAAC;IACDvnB,GAAGA,CAAC1C,MAAM,EAAEulB,IAAI,EAAE;MAChB,OAAOkE,OAAO,CAAC/mB,GAAG,CAACunB,KAAK,EAAE1E,IAAI,CAAC;IACjC,CAAC;IACDsE,OAAOA,CAAA,EAAG;MACR,OAAOJ,OAAO,CAACI,OAAO,CAACI,KAAK,CAAC;IAC/B,CAAC;IACDnP,GAAGA,CAAC9a,MAAM,EAAEulB,IAAI,EAAE1nB,KAAK,EAAE;MACvBosB,KAAK,CAAC1E,IAAI,CAAC,GAAG1nB,KAAK;MACnB,OAAOmC,MAAM,CAACulB,IAAI,CAAC;MACnB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AACA,SAASrK,YAAYA,CAAC+O,KAAK,EAAkD;EAAA,IAAhD9L,QAAQ,GAAAhiB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG;IAACyuB,UAAU,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAI,CAAC;EACzE,MAAM;IAACzM,WAAW,GAAGD,QAAQ,CAACyM,UAAU;IAAEtM,UAAU,GAAGH,QAAQ,CAAC0M,SAAS;IAAEC,QAAQ,GAAG3M,QAAQ,CAACwM;EAAO,CAAC,GAAGV,KAAK;EAC/G,OAAO;IACLU,OAAO,EAAEG,QAAQ;IACjBF,UAAU,EAAExM,WAAW;IACvByM,SAAS,EAAEvM,UAAU;IACrByM,YAAY,EAAE3oB,UAAU,CAACgc,WAAW,CAAC,GAAGA,WAAW,GAAG,MAAMA,WAAW;IACvE4M,WAAW,EAAE5oB,UAAU,CAACkc,UAAU,CAAC,GAAGA,UAAU,GAAG,MAAMA;EAC3D,CAAC;AACH;AACA,MAAM2M,OAAO,GAAGA,CAACC,MAAM,EAAEzN,IAAI,KAAKyN,MAAM,GAAGA,MAAM,GAAGnpB,WAAW,CAAC0b,IAAI,CAAC,GAAGA,IAAI;AAC5E,MAAM0N,gBAAgB,GAAGA,CAAC5F,IAAI,EAAE1nB,KAAK,KAAKM,QAAQ,CAACN,KAAK,CAAC,IAAI0nB,IAAI,KAAK,UAAU,KAC7EvnB,MAAM,CAAC0rB,cAAc,CAAC7rB,KAAK,CAAC,KAAK,IAAI,IAAIA,KAAK,CAACib,WAAW,KAAK9a,MAAM,CAAC;AACzE,SAASsrB,OAAOA,CAACtpB,MAAM,EAAEulB,IAAI,EAAEM,OAAO,EAAE;EACtC,IAAI7nB,MAAM,CAAClC,SAAS,CAACkF,cAAc,CAAChF,IAAI,CAACgE,MAAM,EAAEulB,IAAI,CAAC,EAAE;IACtD,OAAOvlB,MAAM,CAACulB,IAAI,CAAC;EACrB;EACA,MAAM1nB,KAAK,GAAGgoB,OAAO,CAAC,CAAC;EACvB7lB,MAAM,CAACulB,IAAI,CAAC,GAAG1nB,KAAK;EACpB,OAAOA,KAAK;AACd;AACA,SAAS6sB,mBAAmBA,CAAC1qB,MAAM,EAAEulB,IAAI,EAAEkF,QAAQ,EAAE;EACnD,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEpP,YAAY,EAAER;EAAW,CAAC,GAAG1a,MAAM;EACvE,IAAInC,KAAK,GAAGusB,MAAM,CAAC7E,IAAI,CAAC;EACxB,IAAInjB,UAAU,CAACvE,KAAK,CAAC,IAAI6c,WAAW,CAACqQ,YAAY,CAACxF,IAAI,CAAC,EAAE;IACvD1nB,KAAK,GAAGutB,kBAAkB,CAAC7F,IAAI,EAAE1nB,KAAK,EAAEmC,MAAM,EAAEyqB,QAAQ,CAAC;EAC3D;EACA,IAAI3sB,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACzB,MAAM,EAAE;IAClCyB,KAAK,GAAGwtB,aAAa,CAAC9F,IAAI,EAAE1nB,KAAK,EAAEmC,MAAM,EAAE0a,WAAW,CAACsQ,WAAW,CAAC;EACrE;EACA,IAAIG,gBAAgB,CAAC5F,IAAI,EAAE1nB,KAAK,CAAC,EAAE;IACjCA,KAAK,GAAGmsB,cAAc,CAACnsB,KAAK,EAAEwsB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAAC/E,IAAI,CAAC,EAAE7K,WAAW,CAAC;EACpF;EACA,OAAO7c,KAAK;AACd;AACA,SAASutB,kBAAkBA,CAAC7F,IAAI,EAAE1nB,KAAK,EAAEmC,MAAM,EAAEyqB,QAAQ,EAAE;EACzD,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGvqB,MAAM;EACpD,IAAIuqB,MAAM,CAAC7nB,GAAG,CAAC6iB,IAAI,CAAC,EAAE;IACpB,MAAM,IAAI+F,KAAK,CAAC,sBAAsB,GAAGzvB,KAAK,CAACysB,IAAI,CAACiC,MAAM,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAGhG,IAAI,CAAC;EACvF;EACAgF,MAAM,CAAChE,GAAG,CAAChB,IAAI,CAAC;EAChB1nB,KAAK,GAAGA,KAAK,CAACwsB,QAAQ,EAAEC,SAAS,IAAIG,QAAQ,CAAC;EAC9CF,MAAM,CAACiB,MAAM,CAACjG,IAAI,CAAC;EACnB,IAAI4F,gBAAgB,CAAC5F,IAAI,EAAE1nB,KAAK,CAAC,EAAE;IACjCA,KAAK,GAAG4tB,iBAAiB,CAACrB,MAAM,CAACpB,OAAO,EAAEoB,MAAM,EAAE7E,IAAI,EAAE1nB,KAAK,CAAC;EAChE;EACA,OAAOA,KAAK;AACd;AACA,SAASwtB,aAAaA,CAAC9F,IAAI,EAAE1nB,KAAK,EAAEmC,MAAM,EAAEgrB,WAAW,EAAE;EACvD,MAAM;IAACZ,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEpP,YAAY,EAAER;EAAW,CAAC,GAAG1a,MAAM;EACvE,IAAImC,OAAO,CAACkoB,QAAQ,CAACzqB,KAAK,CAAC,IAAIorB,WAAW,CAACzF,IAAI,CAAC,EAAE;IAChD1nB,KAAK,GAAGA,KAAK,CAACwsB,QAAQ,CAACzqB,KAAK,GAAG/B,KAAK,CAACzB,MAAM,CAAC;EAC9C,CAAC,MAAM,IAAI+B,QAAQ,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,MAAM6tB,GAAG,GAAG7tB,KAAK;IACjB,MAAM2qB,MAAM,GAAG4B,MAAM,CAACpB,OAAO,CAAC2C,MAAM,CAAC9kB,CAAC,IAAIA,CAAC,KAAK6kB,GAAG,CAAC;IACpD7tB,KAAK,GAAG,EAAE;IACV,KAAK,MAAM4E,IAAI,IAAIipB,GAAG,EAAE;MACtB,MAAME,QAAQ,GAAGH,iBAAiB,CAACjD,MAAM,EAAE4B,MAAM,EAAE7E,IAAI,EAAE9iB,IAAI,CAAC;MAC9D5E,KAAK,CAACyG,IAAI,CAAC0lB,cAAc,CAAC4B,QAAQ,EAAEvB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAAC/E,IAAI,CAAC,EAAE7K,WAAW,CAAC,CAAC;IAC3F;EACF;EACA,OAAO7c,KAAK;AACd;AACA,SAASguB,eAAeA,CAACjG,QAAQ,EAAEL,IAAI,EAAE1nB,KAAK,EAAE;EAC9C,OAAOuE,UAAU,CAACwjB,QAAQ,CAAC,GAAGA,QAAQ,CAACL,IAAI,EAAE1nB,KAAK,CAAC,GAAG+nB,QAAQ;AAChE;AACA,MAAMkG,QAAQ,GAAGA,CAACzrB,GAAG,EAAE0rB,MAAM,KAAK1rB,GAAG,KAAK,IAAI,GAAG0rB,MAAM,GACnD,OAAO1rB,GAAG,KAAK,QAAQ,GAAGuB,gBAAgB,CAACmqB,MAAM,EAAE1rB,GAAG,CAAC,GAAGgB,SAAS;AACvE,SAAS2qB,SAASA,CAAClR,GAAG,EAAEmR,YAAY,EAAE5rB,GAAG,EAAE6rB,cAAc,EAAEruB,KAAK,EAAE;EAChE,KAAK,MAAMkuB,MAAM,IAAIE,YAAY,EAAE;IACjC,MAAM/qB,KAAK,GAAG4qB,QAAQ,CAACzrB,GAAG,EAAE0rB,MAAM,CAAC;IACnC,IAAI7qB,KAAK,EAAE;MACT4Z,GAAG,CAACyL,GAAG,CAACrlB,KAAK,CAAC;MACd,MAAM0kB,QAAQ,GAAGiG,eAAe,CAAC3qB,KAAK,CAACqd,SAAS,EAAEle,GAAG,EAAExC,KAAK,CAAC;MAC7D,IAAIsE,OAAO,CAACyjB,QAAQ,CAAC,IAAIA,QAAQ,KAAKvlB,GAAG,IAAIulB,QAAQ,KAAKsG,cAAc,EAAE;QACxE,OAAOtG,QAAQ;MACjB;IACF,CAAC,MAAM,IAAI1kB,KAAK,KAAK,KAAK,IAAIiB,OAAO,CAAC+pB,cAAc,CAAC,IAAI7rB,GAAG,KAAK6rB,cAAc,EAAE;MAC/E,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,SAAST,iBAAiBA,CAACQ,YAAY,EAAEL,QAAQ,EAAErG,IAAI,EAAE1nB,KAAK,EAAE;EAC9D,MAAM6qB,UAAU,GAAGkD,QAAQ,CAAC3C,WAAW;EACvC,MAAMrD,QAAQ,GAAGiG,eAAe,CAACD,QAAQ,CAACrN,SAAS,EAAEgH,IAAI,EAAE1nB,KAAK,CAAC;EACjE,MAAMsuB,SAAS,GAAG,CAAC,GAAGF,YAAY,EAAE,GAAGvD,UAAU,CAAC;EAClD,MAAM5N,GAAG,GAAG,IAAIuN,GAAG,CAAC,CAAC;EACrBvN,GAAG,CAACyL,GAAG,CAAC1oB,KAAK,CAAC;EACd,IAAIwC,GAAG,GAAG+rB,gBAAgB,CAACtR,GAAG,EAAEqR,SAAS,EAAE5G,IAAI,EAAEK,QAAQ,IAAIL,IAAI,EAAE1nB,KAAK,CAAC;EACzE,IAAIwC,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,KAAK;EACd;EACA,IAAI8B,OAAO,CAACyjB,QAAQ,CAAC,IAAIA,QAAQ,KAAKL,IAAI,EAAE;IAC1CllB,GAAG,GAAG+rB,gBAAgB,CAACtR,GAAG,EAAEqR,SAAS,EAAEvG,QAAQ,EAAEvlB,GAAG,EAAExC,KAAK,CAAC;IAC5D,IAAIwC,GAAG,KAAK,IAAI,EAAE;MAChB,OAAO,KAAK;IACd;EACF;EACA,OAAOkoB,eAAe,CAAC1sB,KAAK,CAACysB,IAAI,CAACxN,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE4N,UAAU,EAAE9C,QAAQ,EAChE,MAAMyG,YAAY,CAACT,QAAQ,EAAErG,IAAI,EAAE1nB,KAAK,CAAC,CAAC;AAC9C;AACA,SAASuuB,gBAAgBA,CAACtR,GAAG,EAAEqR,SAAS,EAAE9rB,GAAG,EAAEulB,QAAQ,EAAEnjB,IAAI,EAAE;EAC7D,OAAOpC,GAAG,EAAE;IACVA,GAAG,GAAG2rB,SAAS,CAAClR,GAAG,EAAEqR,SAAS,EAAE9rB,GAAG,EAAEulB,QAAQ,EAAEnjB,IAAI,CAAC;EACtD;EACA,OAAOpC,GAAG;AACZ;AACA,SAASgsB,YAAYA,CAACT,QAAQ,EAAErG,IAAI,EAAE1nB,KAAK,EAAE;EAC3C,MAAMkuB,MAAM,GAAGH,QAAQ,CAAC1C,UAAU,CAAC,CAAC;EACpC,IAAI,EAAE3D,IAAI,IAAIwG,MAAM,CAAC,EAAE;IACrBA,MAAM,CAACxG,IAAI,CAAC,GAAG,CAAC,CAAC;EACnB;EACA,MAAMvlB,MAAM,GAAG+rB,MAAM,CAACxG,IAAI,CAAC;EAC3B,IAAIznB,OAAO,CAACkC,MAAM,CAAC,IAAI7B,QAAQ,CAACN,KAAK,CAAC,EAAE;IACtC,OAAOA,KAAK;EACd;EACA,OAAOmC,MAAM;AACf;AACA,SAASupB,oBAAoBA,CAAChE,IAAI,EAAEkD,QAAQ,EAAED,MAAM,EAAEyB,KAAK,EAAE;EAC3D,IAAIpsB,KAAK;EACT,KAAK,MAAMqtB,MAAM,IAAIzC,QAAQ,EAAE;IAC7B5qB,KAAK,GAAG+qB,QAAQ,CAACqC,OAAO,CAACC,MAAM,EAAE3F,IAAI,CAAC,EAAEiD,MAAM,CAAC;IAC/C,IAAIrmB,OAAO,CAACtE,KAAK,CAAC,EAAE;MAClB,OAAOstB,gBAAgB,CAAC5F,IAAI,EAAE1nB,KAAK,CAAC,GAChC4tB,iBAAiB,CAACjD,MAAM,EAAEyB,KAAK,EAAE1E,IAAI,EAAE1nB,KAAK,CAAC,GAC7CA,KAAK;IACX;EACF;AACF;AACA,SAAS+qB,QAAQA,CAACvoB,GAAG,EAAEmoB,MAAM,EAAE;EAC7B,KAAK,MAAMtnB,KAAK,IAAIsnB,MAAM,EAAE;IAC1B,IAAI,CAACtnB,KAAK,EAAE;MACV;IACF;IACA,MAAMrD,KAAK,GAAGqD,KAAK,CAACb,GAAG,CAAC;IACxB,IAAI8B,OAAO,CAACtE,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK;IACd;EACF;AACF;AACA,SAAS8rB,oBAAoBA,CAAC3pB,MAAM,EAAE;EACpC,IAAIZ,IAAI,GAAGY,MAAM,CAACqpB,KAAK;EACvB,IAAI,CAACjqB,IAAI,EAAE;IACTA,IAAI,GAAGY,MAAM,CAACqpB,KAAK,GAAGiD,wBAAwB,CAACtsB,MAAM,CAACgpB,OAAO,CAAC;EAChE;EACA,OAAO5pB,IAAI;AACb;AACA,SAASktB,wBAAwBA,CAAC9D,MAAM,EAAE;EACxC,MAAM1N,GAAG,GAAG,IAAIuN,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMnnB,KAAK,IAAIsnB,MAAM,EAAE;IAC1B,KAAK,MAAMnoB,GAAG,IAAIrC,MAAM,CAACoB,IAAI,CAAC8B,KAAK,CAAC,CAACyqB,MAAM,CAACxrB,CAAC,IAAI,CAACA,CAAC,CAACke,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;MACpEvD,GAAG,CAACyL,GAAG,CAAClmB,GAAG,CAAC;IACd;EACF;EACA,OAAOxE,KAAK,CAACysB,IAAI,CAACxN,GAAG,CAAC;AACxB;AAEA,MAAMyR,OAAO,GAAGluB,MAAM,CAACkuB,OAAO,IAAI,KAAK;AACvC,MAAMC,QAAQ,GAAGA,CAACC,MAAM,EAAEvtB,CAAC,KAAKA,CAAC,GAAGutB,MAAM,CAACrwB,MAAM,IAAI,CAACqwB,MAAM,CAACvtB,CAAC,CAAC,CAACwtB,IAAI,IAAID,MAAM,CAACvtB,CAAC,CAAC;AACjF,MAAMytB,YAAY,GAAIpQ,SAAS,IAAKA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjE,SAASqQ,WAAWA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEzlB,CAAC,EAAE;EAC3D,MAAMnG,QAAQ,GAAG0rB,UAAU,CAACH,IAAI,GAAGI,WAAW,GAAGD,UAAU;EAC3D,MAAMzrB,OAAO,GAAG0rB,WAAW;EAC3B,MAAME,IAAI,GAAGD,UAAU,CAACL,IAAI,GAAGI,WAAW,GAAGC,UAAU;EACvD,MAAME,GAAG,GAAG3mB,qBAAqB,CAAClF,OAAO,EAAED,QAAQ,CAAC;EACpD,MAAM+rB,GAAG,GAAG5mB,qBAAqB,CAAC0mB,IAAI,EAAE5rB,OAAO,CAAC;EAChD,IAAI+rB,GAAG,GAAGF,GAAG,IAAIA,GAAG,GAAGC,GAAG,CAAC;EAC3B,IAAIE,GAAG,GAAGF,GAAG,IAAID,GAAG,GAAGC,GAAG,CAAC;EAC3BC,GAAG,GAAGxoB,KAAK,CAACwoB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1BC,GAAG,GAAGzoB,KAAK,CAACyoB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1B,MAAMC,EAAE,GAAG/lB,CAAC,GAAG6lB,GAAG;EAClB,MAAMG,EAAE,GAAGhmB,CAAC,GAAG8lB,GAAG;EAClB,OAAO;IACLjsB,QAAQ,EAAE;MACRyD,CAAC,EAAExD,OAAO,CAACwD,CAAC,GAAGyoB,EAAE,IAAIL,IAAI,CAACpoB,CAAC,GAAGzD,QAAQ,CAACyD,CAAC,CAAC;MACzCC,CAAC,EAAEzD,OAAO,CAACyD,CAAC,GAAGwoB,EAAE,IAAIL,IAAI,CAACnoB,CAAC,GAAG1D,QAAQ,CAAC0D,CAAC;IAC1C,CAAC;IACDmoB,IAAI,EAAE;MACJpoB,CAAC,EAAExD,OAAO,CAACwD,CAAC,GAAG0oB,EAAE,IAAIN,IAAI,CAACpoB,CAAC,GAAGzD,QAAQ,CAACyD,CAAC,CAAC;MACzCC,CAAC,EAAEzD,OAAO,CAACyD,CAAC,GAAGyoB,EAAE,IAAIN,IAAI,CAACnoB,CAAC,GAAG1D,QAAQ,CAAC0D,CAAC;IAC1C;EACF,CAAC;AACH;AACA,SAAS0oB,cAAcA,CAACd,MAAM,EAAEe,MAAM,EAAEC,EAAE,EAAE;EAC1C,MAAMC,SAAS,GAAGjB,MAAM,CAACrwB,MAAM;EAC/B,IAAIuxB,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,YAAY;EACvD,IAAIC,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;EACpC,KAAK,IAAIvtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwuB,SAAS,GAAG,CAAC,EAAE,EAAExuB,CAAC,EAAE;IACtC6uB,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAEvtB,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC6uB,YAAY,IAAI,CAACC,UAAU,EAAE;MAChC;IACF;IACA,IAAInqB,YAAY,CAAC2pB,MAAM,CAACtuB,CAAC,CAAC,EAAE,CAAC,EAAEqtB,OAAO,CAAC,EAAE;MACvCkB,EAAE,CAACvuB,CAAC,CAAC,GAAGuuB,EAAE,CAACvuB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACrB;IACF;IACAyuB,MAAM,GAAGF,EAAE,CAACvuB,CAAC,CAAC,GAAGsuB,MAAM,CAACtuB,CAAC,CAAC;IAC1B0uB,KAAK,GAAGH,EAAE,CAACvuB,CAAC,GAAG,CAAC,CAAC,GAAGsuB,MAAM,CAACtuB,CAAC,CAAC;IAC7B4uB,gBAAgB,GAAGhrB,IAAI,CAACiB,GAAG,CAAC4pB,MAAM,EAAE,CAAC,CAAC,GAAG7qB,IAAI,CAACiB,GAAG,CAAC6pB,KAAK,EAAE,CAAC,CAAC;IAC3D,IAAIE,gBAAgB,IAAI,CAAC,EAAE;MACzB;IACF;IACAD,IAAI,GAAG,CAAC,GAAG/qB,IAAI,CAACuB,IAAI,CAACypB,gBAAgB,CAAC;IACtCL,EAAE,CAACvuB,CAAC,CAAC,GAAGyuB,MAAM,GAAGE,IAAI,GAAGL,MAAM,CAACtuB,CAAC,CAAC;IACjCuuB,EAAE,CAACvuB,CAAC,GAAG,CAAC,CAAC,GAAG0uB,KAAK,GAAGC,IAAI,GAAGL,MAAM,CAACtuB,CAAC,CAAC;EACtC;AACF;AACA,SAAS+uB,eAAeA,CAACxB,MAAM,EAAEgB,EAAE,EAAmB;EAAA,IAAjBlR,SAAS,GAAApgB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,GAAG;EAClD,MAAM+xB,SAAS,GAAGvB,YAAY,CAACpQ,SAAS,CAAC;EACzC,MAAMmR,SAAS,GAAGjB,MAAM,CAACrwB,MAAM;EAC/B,IAAI+xB,KAAK,EAAEC,WAAW,EAAEL,YAAY;EACpC,IAAIC,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;EACpC,KAAK,IAAIvtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwuB,SAAS,EAAE,EAAExuB,CAAC,EAAE;IAClCkvB,WAAW,GAAGL,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAEvtB,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC6uB,YAAY,EAAE;MACjB;IACF;IACA,MAAMM,MAAM,GAAGN,YAAY,CAACxR,SAAS,CAAC;IACtC,MAAM+R,MAAM,GAAGP,YAAY,CAACG,SAAS,CAAC;IACtC,IAAIE,WAAW,EAAE;MACfD,KAAK,GAAG,CAACE,MAAM,GAAGD,WAAW,CAAC7R,SAAS,CAAC,IAAI,CAAC;MAC7CwR,YAAY,OAAApiB,MAAA,CAAO4Q,SAAS,EAAG,GAAG8R,MAAM,GAAGF,KAAK;MAChDJ,YAAY,OAAApiB,MAAA,CAAOuiB,SAAS,EAAG,GAAGI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAACvuB,CAAC,CAAC;IAC1D;IACA,IAAI8uB,UAAU,EAAE;MACdG,KAAK,GAAG,CAACH,UAAU,CAACzR,SAAS,CAAC,GAAG8R,MAAM,IAAI,CAAC;MAC5CN,YAAY,OAAApiB,MAAA,CAAO4Q,SAAS,EAAG,GAAG8R,MAAM,GAAGF,KAAK;MAChDJ,YAAY,OAAApiB,MAAA,CAAOuiB,SAAS,EAAG,GAAGI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAACvuB,CAAC,CAAC;IAC1D;EACF;AACF;AACA,SAASqvB,mBAAmBA,CAAC9B,MAAM,EAAmB;EAAA,IAAjBlQ,SAAS,GAAApgB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkF,SAAA,GAAAlF,SAAA,MAAG,GAAG;EAClD,MAAM+xB,SAAS,GAAGvB,YAAY,CAACpQ,SAAS,CAAC;EACzC,MAAMmR,SAAS,GAAGjB,MAAM,CAACrwB,MAAM;EAC/B,MAAMoxB,MAAM,GAAG3xB,KAAK,CAAC6xB,SAAS,CAAC,CAACnM,IAAI,CAAC,CAAC,CAAC;EACvC,MAAMkM,EAAE,GAAG5xB,KAAK,CAAC6xB,SAAS,CAAC;EAC3B,IAAIxuB,CAAC,EAAEkvB,WAAW,EAAEL,YAAY;EAChC,IAAIC,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAE,CAAC,CAAC;EACpC,KAAKvtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwuB,SAAS,EAAE,EAAExuB,CAAC,EAAE;IAC9BkvB,WAAW,GAAGL,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGxB,QAAQ,CAACC,MAAM,EAAEvtB,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC6uB,YAAY,EAAE;MACjB;IACF;IACA,IAAIC,UAAU,EAAE;MACd,MAAMQ,UAAU,GAAGR,UAAU,CAACzR,SAAS,CAAC,GAAGwR,YAAY,CAACxR,SAAS,CAAC;MAClEiR,MAAM,CAACtuB,CAAC,CAAC,GAAGsvB,UAAU,KAAK,CAAC,GAAG,CAACR,UAAU,CAACE,SAAS,CAAC,GAAGH,YAAY,CAACG,SAAS,CAAC,IAAIM,UAAU,GAAG,CAAC;IACnG;IACAf,EAAE,CAACvuB,CAAC,CAAC,GAAG,CAACkvB,WAAW,GAAGZ,MAAM,CAACtuB,CAAC,CAAC,GAC5B,CAAC8uB,UAAU,GAAGR,MAAM,CAACtuB,CAAC,GAAG,CAAC,CAAC,GAC1BsE,IAAI,CAACgqB,MAAM,CAACtuB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAKsE,IAAI,CAACgqB,MAAM,CAACtuB,CAAC,CAAC,CAAC,GAAI,CAAC,GAC7C,CAACsuB,MAAM,CAACtuB,CAAC,GAAG,CAAC,CAAC,GAAGsuB,MAAM,CAACtuB,CAAC,CAAC,IAAI,CAAC;EACrC;EACAquB,cAAc,CAACd,MAAM,EAAEe,MAAM,EAAEC,EAAE,CAAC;EAClCQ,eAAe,CAACxB,MAAM,EAAEgB,EAAE,EAAElR,SAAS,CAAC;AACxC;AACA,SAASkS,eAAeA,CAACC,EAAE,EAAErpB,GAAG,EAAEC,GAAG,EAAE;EACrC,OAAOxC,IAAI,CAACwC,GAAG,CAACxC,IAAI,CAACuC,GAAG,CAACqpB,EAAE,EAAEppB,GAAG,CAAC,EAAED,GAAG,CAAC;AACzC;AACA,SAASspB,eAAeA,CAAClC,MAAM,EAAE7K,IAAI,EAAE;EACrC,IAAI1iB,CAAC,EAAEM,IAAI,EAAEmiB,KAAK,EAAEiN,MAAM,EAAEC,UAAU;EACtC,IAAIC,UAAU,GAAGpN,cAAc,CAAC+K,MAAM,CAAC,CAAC,CAAC,EAAE7K,IAAI,CAAC;EAChD,KAAK1iB,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAGitB,MAAM,CAACrwB,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;IAC/C2vB,UAAU,GAAGD,MAAM;IACnBA,MAAM,GAAGE,UAAU;IACnBA,UAAU,GAAG5vB,CAAC,GAAGM,IAAI,GAAG,CAAC,IAAIkiB,cAAc,CAAC+K,MAAM,CAACvtB,CAAC,GAAG,CAAC,CAAC,EAAE0iB,IAAI,CAAC;IAChE,IAAI,CAACgN,MAAM,EAAE;MACX;IACF;IACAjN,KAAK,GAAG8K,MAAM,CAACvtB,CAAC,CAAC;IACjB,IAAI2vB,UAAU,EAAE;MACdlN,KAAK,CAACa,IAAI,GAAGiM,eAAe,CAAC9M,KAAK,CAACa,IAAI,EAAEZ,IAAI,CAACvkB,IAAI,EAAEukB,IAAI,CAACtkB,KAAK,CAAC;MAC/DqkB,KAAK,CAACe,IAAI,GAAG+L,eAAe,CAAC9M,KAAK,CAACe,IAAI,EAAEd,IAAI,CAACE,GAAG,EAAEF,IAAI,CAACG,MAAM,CAAC;IACjE;IACA,IAAI+M,UAAU,EAAE;MACdnN,KAAK,CAACc,IAAI,GAAGgM,eAAe,CAAC9M,KAAK,CAACc,IAAI,EAAEb,IAAI,CAACvkB,IAAI,EAAEukB,IAAI,CAACtkB,KAAK,CAAC;MAC/DqkB,KAAK,CAACgB,IAAI,GAAG8L,eAAe,CAAC9M,KAAK,CAACgB,IAAI,EAAEf,IAAI,CAACE,GAAG,EAAEF,IAAI,CAACG,MAAM,CAAC;IACjE;EACF;AACF;AACA,SAASgN,0BAA0BA,CAACtC,MAAM,EAAEjsB,OAAO,EAAEohB,IAAI,EAAEoN,IAAI,EAAEzS,SAAS,EAAE;EAC1E,IAAIrd,CAAC,EAAEM,IAAI,EAAEmiB,KAAK,EAAEsN,aAAa;EACjC,IAAIzuB,OAAO,CAAC0uB,QAAQ,EAAE;IACpBzC,MAAM,GAAGA,MAAM,CAACd,MAAM,CAAE+C,EAAE,IAAK,CAACA,EAAE,CAAChC,IAAI,CAAC;EAC1C;EACA,IAAIlsB,OAAO,CAAC2uB,sBAAsB,KAAK,UAAU,EAAE;IACjDZ,mBAAmB,CAAC9B,MAAM,EAAElQ,SAAS,CAAC;EACxC,CAAC,MAAM;IACL,IAAI6S,IAAI,GAAGJ,IAAI,GAAGvC,MAAM,CAACA,MAAM,CAACrwB,MAAM,GAAG,CAAC,CAAC,GAAGqwB,MAAM,CAAC,CAAC,CAAC;IACvD,KAAKvtB,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAGitB,MAAM,CAACrwB,MAAM,EAAE8C,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;MAC/CyiB,KAAK,GAAG8K,MAAM,CAACvtB,CAAC,CAAC;MACjB+vB,aAAa,GAAGrC,WAAW,CACzBwC,IAAI,EACJzN,KAAK,EACL8K,MAAM,CAAC3pB,IAAI,CAACuC,GAAG,CAACnG,CAAC,GAAG,CAAC,EAAEM,IAAI,IAAIwvB,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGxvB,IAAI,CAAC,EACrDgB,OAAO,CAAC6uB,OACV,CAAC;MACD1N,KAAK,CAACa,IAAI,GAAGyM,aAAa,CAAC9tB,QAAQ,CAACyD,CAAC;MACrC+c,KAAK,CAACe,IAAI,GAAGuM,aAAa,CAAC9tB,QAAQ,CAAC0D,CAAC;MACrC8c,KAAK,CAACc,IAAI,GAAGwM,aAAa,CAACjC,IAAI,CAACpoB,CAAC;MACjC+c,KAAK,CAACgB,IAAI,GAAGsM,aAAa,CAACjC,IAAI,CAACnoB,CAAC;MACjCuqB,IAAI,GAAGzN,KAAK;IACd;EACF;EACA,IAAInhB,OAAO,CAACmuB,eAAe,EAAE;IAC3BA,eAAe,CAAClC,MAAM,EAAE7K,IAAI,CAAC;EAC/B;AACF;AAEA,SAAS0N,eAAeA,CAAA,EAAG;EACzB,OAAO,OAAOl0B,MAAM,KAAK,WAAW,IAAI,OAAOm0B,QAAQ,KAAK,WAAW;AACzE;AACA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAI1D,MAAM,GAAG0D,OAAO,CAACC,UAAU;EAC/B,IAAI3D,MAAM,IAAIA,MAAM,CAAC9tB,QAAQ,CAAC,CAAC,KAAK,qBAAqB,EAAE;IACzD8tB,MAAM,GAAGA,MAAM,CAAC4D,IAAI;EACtB;EACA,OAAO5D,MAAM;AACf;AACA,SAAS6D,aAAaA,CAACC,UAAU,EAAEjV,IAAI,EAAEkV,cAAc,EAAE;EACvD,IAAIC,aAAa;EACjB,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAClCE,aAAa,GAAGhY,QAAQ,CAAC8X,UAAU,EAAE,EAAE,CAAC;IACxC,IAAIA,UAAU,CAACvvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAClCyvB,aAAa,GAAGA,aAAa,GAAG,GAAG,GAAGnV,IAAI,CAAC8U,UAAU,CAACI,cAAc,CAAC;IACvE;EACF,CAAC,MAAM;IACLC,aAAa,GAAGF,UAAU;EAC5B;EACA,OAAOE,aAAa;AACtB;AACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK70B,MAAM,CAAC40B,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;AAC5E,SAASC,QAAQA,CAACC,EAAE,EAAE/qB,QAAQ,EAAE;EAC9B,OAAO4qB,gBAAgB,CAACG,EAAE,CAAC,CAACC,gBAAgB,CAAChrB,QAAQ,CAAC;AACxD;AACA,MAAMirB,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AACpD,SAASC,kBAAkBA,CAACC,MAAM,EAAEvU,KAAK,EAAEwU,MAAM,EAAE;EACjD,MAAMpsB,MAAM,GAAG,CAAC,CAAC;EACjBosB,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;EACnC,KAAK,IAAItxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1B,MAAM4C,GAAG,GAAGuuB,SAAS,CAACnxB,CAAC,CAAC;IACxBkF,MAAM,CAACtC,GAAG,CAAC,GAAGjD,UAAU,CAAC0xB,MAAM,CAACvU,KAAK,GAAG,GAAG,GAAGla,GAAG,GAAG0uB,MAAM,CAAC,CAAC,IAAI,CAAC;EACnE;EACApsB,MAAM,CAAC4a,KAAK,GAAG5a,MAAM,CAAC/G,IAAI,GAAG+G,MAAM,CAAC9G,KAAK;EACzC8G,MAAM,CAACic,MAAM,GAAGjc,MAAM,CAAC0d,GAAG,GAAG1d,MAAM,CAAC2d,MAAM;EAC1C,OAAO3d,MAAM;AACf;AACA,MAAMqsB,YAAY,GAAGA,CAAC7rB,CAAC,EAAEC,CAAC,EAAE7E,MAAM,KAAK,CAAC4E,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAG,CAAC,MAAM,CAAC7E,MAAM,IAAI,CAACA,MAAM,CAAC0wB,UAAU,CAAC;AAC1F,SAASC,iBAAiBA,CAACC,GAAG,EAAE3Q,MAAM,EAAE;EACtC,MAAMrd,CAAC,GAAGguB,GAAG,CAACC,MAAM,IAAID,GAAG;EAC3B,MAAME,OAAO,GAAGluB,CAAC,CAACkuB,OAAO;EACzB,MAAMhxB,MAAM,GAAGgxB,OAAO,IAAIA,OAAO,CAAC10B,MAAM,GAAG00B,OAAO,CAAC,CAAC,CAAC,GAAGluB,CAAC;EACzD,MAAM;IAACmuB,OAAO;IAAEC;EAAO,CAAC,GAAGlxB,MAAM;EACjC,IAAImxB,GAAG,GAAG,KAAK;EACf,IAAIrsB,CAAC,EAAEC,CAAC;EACR,IAAI4rB,YAAY,CAACM,OAAO,EAAEC,OAAO,EAAEpuB,CAAC,CAAC5C,MAAM,CAAC,EAAE;IAC5C4E,CAAC,GAAGmsB,OAAO;IACXlsB,CAAC,GAAGmsB,OAAO;EACb,CAAC,MAAM;IACL,MAAM1P,IAAI,GAAGrB,MAAM,CAACiR,qBAAqB,CAAC,CAAC;IAC3CtsB,CAAC,GAAG9E,MAAM,CAACqxB,OAAO,GAAG7P,IAAI,CAACjkB,IAAI;IAC9BwH,CAAC,GAAG/E,MAAM,CAACsxB,OAAO,GAAG9P,IAAI,CAACQ,GAAG;IAC7BmP,GAAG,GAAG,IAAI;EACZ;EACA,OAAO;IAACrsB,CAAC;IAAEC,CAAC;IAAEosB;EAAG,CAAC;AACpB;AACA,SAASI,mBAAmBA,CAACT,GAAG,EAAEnV,KAAK,EAAE;EACvC,MAAM;IAACwE,MAAM;IAAEH;EAAuB,CAAC,GAAGrE,KAAK;EAC/C,MAAMO,KAAK,GAAGgU,gBAAgB,CAAC/P,MAAM,CAAC;EACtC,MAAMqR,SAAS,GAAGtV,KAAK,CAACuV,SAAS,KAAK,YAAY;EAClD,MAAMC,QAAQ,GAAGlB,kBAAkB,CAACtU,KAAK,EAAE,SAAS,CAAC;EACrD,MAAMyV,OAAO,GAAGnB,kBAAkB,CAACtU,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC5D,MAAM;IAACpX,CAAC;IAAEC,CAAC;IAAEosB;EAAG,CAAC,GAAGN,iBAAiB,CAACC,GAAG,EAAE3Q,MAAM,CAAC;EAClD,MAAMM,OAAO,GAAGiR,QAAQ,CAACn0B,IAAI,IAAI4zB,GAAG,IAAIQ,OAAO,CAACp0B,IAAI,CAAC;EACrD,MAAMmjB,OAAO,GAAGgR,QAAQ,CAAC1P,GAAG,IAAImP,GAAG,IAAIQ,OAAO,CAAC3P,GAAG,CAAC;EACnD,IAAI;IAAC9C,KAAK;IAAEqB;EAAM,CAAC,GAAG5E,KAAK;EAC3B,IAAI6V,SAAS,EAAE;IACbtS,KAAK,IAAIwS,QAAQ,CAACxS,KAAK,GAAGyS,OAAO,CAACzS,KAAK;IACvCqB,MAAM,IAAImR,QAAQ,CAACnR,MAAM,GAAGoR,OAAO,CAACpR,MAAM;EAC5C;EACA,OAAO;IACLzb,CAAC,EAAE9B,IAAI,CAACc,KAAK,CAAC,CAACgB,CAAC,GAAG2b,OAAO,IAAIvB,KAAK,GAAGiB,MAAM,CAACjB,KAAK,GAAGc,uBAAuB,CAAC;IAC7Ejb,CAAC,EAAE/B,IAAI,CAACc,KAAK,CAAC,CAACiB,CAAC,GAAG2b,OAAO,IAAIH,MAAM,GAAGJ,MAAM,CAACI,MAAM,GAAGP,uBAAuB;EAChF,CAAC;AACH;AACA,SAAS4R,gBAAgBA,CAACzR,MAAM,EAAEjB,KAAK,EAAEqB,MAAM,EAAE;EAC/C,IAAIkD,QAAQ,EAAEoO,SAAS;EACvB,IAAI3S,KAAK,KAAK3d,SAAS,IAAIgf,MAAM,KAAKhf,SAAS,EAAE;IAC/C,MAAMuwB,SAAS,GAAGpC,cAAc,CAACvP,MAAM,CAAC;IACxC,IAAI,CAAC2R,SAAS,EAAE;MACd5S,KAAK,GAAGiB,MAAM,CAAC4R,WAAW;MAC1BxR,MAAM,GAAGJ,MAAM,CAAC6R,YAAY;IAC9B,CAAC,MAAM;MACL,MAAMxQ,IAAI,GAAGsQ,SAAS,CAACV,qBAAqB,CAAC,CAAC;MAC9C,MAAMa,cAAc,GAAG/B,gBAAgB,CAAC4B,SAAS,CAAC;MAClD,MAAMI,eAAe,GAAG1B,kBAAkB,CAACyB,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC7E,MAAME,gBAAgB,GAAG3B,kBAAkB,CAACyB,cAAc,EAAE,SAAS,CAAC;MACtE/S,KAAK,GAAGsC,IAAI,CAACtC,KAAK,GAAGiT,gBAAgB,CAACjT,KAAK,GAAGgT,eAAe,CAAChT,KAAK;MACnEqB,MAAM,GAAGiB,IAAI,CAACjB,MAAM,GAAG4R,gBAAgB,CAAC5R,MAAM,GAAG2R,eAAe,CAAC3R,MAAM;MACvEkD,QAAQ,GAAGqM,aAAa,CAACmC,cAAc,CAACxO,QAAQ,EAAEqO,SAAS,EAAE,aAAa,CAAC;MAC3ED,SAAS,GAAG/B,aAAa,CAACmC,cAAc,CAACJ,SAAS,EAAEC,SAAS,EAAE,cAAc,CAAC;IAChF;EACF;EACA,OAAO;IACL5S,KAAK;IACLqB,MAAM;IACNkD,QAAQ,EAAEA,QAAQ,IAAItgB,QAAQ;IAC9B0uB,SAAS,EAAEA,SAAS,IAAI1uB;EAC1B,CAAC;AACH;AACA,MAAMivB,MAAM,GAAGxnB,CAAC,IAAI5H,IAAI,CAACc,KAAK,CAAC8G,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3C,SAASynB,cAAcA,CAAClS,MAAM,EAAEmS,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC9D,MAAMtW,KAAK,GAAGgU,gBAAgB,CAAC/P,MAAM,CAAC;EACtC,MAAMsS,OAAO,GAAGjC,kBAAkB,CAACtU,KAAK,EAAE,QAAQ,CAAC;EACnD,MAAMuH,QAAQ,GAAGqM,aAAa,CAAC5T,KAAK,CAACuH,QAAQ,EAAEtD,MAAM,EAAE,aAAa,CAAC,IAAIhd,QAAQ;EACjF,MAAM0uB,SAAS,GAAG/B,aAAa,CAAC5T,KAAK,CAAC2V,SAAS,EAAE1R,MAAM,EAAE,cAAc,CAAC,IAAIhd,QAAQ;EACpF,MAAMuvB,aAAa,GAAGd,gBAAgB,CAACzR,MAAM,EAAEmS,OAAO,EAAEC,QAAQ,CAAC;EACjE,IAAI;IAACrT,KAAK;IAAEqB;EAAM,CAAC,GAAGmS,aAAa;EACnC,IAAIxW,KAAK,CAACuV,SAAS,KAAK,aAAa,EAAE;IACrC,MAAME,OAAO,GAAGnB,kBAAkB,CAACtU,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC5D,MAAMwV,QAAQ,GAAGlB,kBAAkB,CAACtU,KAAK,EAAE,SAAS,CAAC;IACrDgD,KAAK,IAAIwS,QAAQ,CAACxS,KAAK,GAAGyS,OAAO,CAACzS,KAAK;IACvCqB,MAAM,IAAImR,QAAQ,CAACnR,MAAM,GAAGoR,OAAO,CAACpR,MAAM;EAC5C;EACArB,KAAK,GAAGlc,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE0Z,KAAK,GAAGuT,OAAO,CAACvT,KAAK,CAAC;EAC1CqB,MAAM,GAAGvd,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAEgtB,WAAW,GAAGxvB,IAAI,CAACkB,KAAK,CAACgb,KAAK,GAAGsT,WAAW,CAAC,GAAGjS,MAAM,GAAGkS,OAAO,CAAClS,MAAM,CAAC;EAC7FrB,KAAK,GAAGkT,MAAM,CAACpvB,IAAI,CAACuC,GAAG,CAAC2Z,KAAK,EAAEuE,QAAQ,EAAEiP,aAAa,CAACjP,QAAQ,CAAC,CAAC;EACjElD,MAAM,GAAG6R,MAAM,CAACpvB,IAAI,CAACuC,GAAG,CAACgb,MAAM,EAAEsR,SAAS,EAAEa,aAAa,CAACb,SAAS,CAAC,CAAC;EACrE,IAAI3S,KAAK,IAAI,CAACqB,MAAM,EAAE;IACpBA,MAAM,GAAG6R,MAAM,CAAClT,KAAK,GAAG,CAAC,CAAC;EAC5B;EACA,OAAO;IACLA,KAAK;IACLqB;EACF,CAAC;AACH;AACA,SAASoS,WAAWA,CAAChX,KAAK,EAAEiX,UAAU,EAAEC,UAAU,EAAE;EAClD,MAAMC,UAAU,GAAGF,UAAU,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAG/vB,IAAI,CAACkB,KAAK,CAACyX,KAAK,CAAC4E,MAAM,GAAGuS,UAAU,CAAC;EAC1D,MAAME,WAAW,GAAGhwB,IAAI,CAACkB,KAAK,CAACyX,KAAK,CAACuD,KAAK,GAAG4T,UAAU,CAAC;EACxDnX,KAAK,CAAC4E,MAAM,GAAGwS,YAAY,GAAGD,UAAU;EACxCnX,KAAK,CAACuD,KAAK,GAAG8T,WAAW,GAAGF,UAAU;EACtC,MAAM3S,MAAM,GAAGxE,KAAK,CAACwE,MAAM;EAC3B,IAAIA,MAAM,CAACjE,KAAK,KAAK2W,UAAU,IAAK,CAAC1S,MAAM,CAACjE,KAAK,CAACqE,MAAM,IAAI,CAACJ,MAAM,CAACjE,KAAK,CAACgD,KAAM,CAAC,EAAE;IACjFiB,MAAM,CAACjE,KAAK,CAACqE,MAAM,MAAA1U,MAAA,CAAM8P,KAAK,CAAC4E,MAAM,OAAI;IACzCJ,MAAM,CAACjE,KAAK,CAACgD,KAAK,MAAArT,MAAA,CAAM8P,KAAK,CAACuD,KAAK,OAAI;EACzC;EACA,IAAIvD,KAAK,CAACqE,uBAAuB,KAAK8S,UAAU,IACzC3S,MAAM,CAACI,MAAM,KAAKwS,YAAY,IAC9B5S,MAAM,CAACjB,KAAK,KAAK8T,WAAW,EAAE;IACnCrX,KAAK,CAACqE,uBAAuB,GAAG8S,UAAU;IAC1C3S,MAAM,CAACI,MAAM,GAAGwS,YAAY;IAC5B5S,MAAM,CAACjB,KAAK,GAAG8T,WAAW;IAC1BrX,KAAK,CAACW,GAAG,CAAC2W,YAAY,CAACH,UAAU,EAAE,CAAC,EAAE,CAAC,EAAEA,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AACA,MAAMI,4BAA4B,GAAI,YAAW;EAC/C,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAI;IACF,MAAMzyB,OAAO,GAAG;MACd,IAAI0yB,OAAOA,CAAA,EAAG;QACZD,gBAAgB,GAAG,IAAI;QACvB,OAAO,KAAK;MACd;IACF,CAAC;IACD73B,MAAM,CAAC+3B,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE3yB,OAAO,CAAC;IAC9CpF,MAAM,CAACg4B,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE5yB,OAAO,CAAC;EACnD,CAAC,CAAC,OAAOoC,CAAC,EAAE,CACZ;EACA,OAAOqwB,gBAAgB;AACzB,CAAC,CAAC,CAAE;AACJ,SAASI,YAAYA,CAACpD,OAAO,EAAE7qB,QAAQ,EAAE;EACvC,MAAMvH,KAAK,GAAGqyB,QAAQ,CAACD,OAAO,EAAE7qB,QAAQ,CAAC;EACzC,MAAM4f,OAAO,GAAGnnB,KAAK,IAAIA,KAAK,CAAConB,KAAK,CAAC,mBAAmB,CAAC;EACzD,OAAOD,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG3jB,SAAS;AAC1C;AAEA,SAASiyB,YAAYA,CAAC7mB,EAAE,EAAEC,EAAE,EAAEpF,CAAC,EAAEmV,IAAI,EAAE;EACrC,OAAO;IACL7X,CAAC,EAAE6H,EAAE,CAAC7H,CAAC,GAAG0C,CAAC,IAAIoF,EAAE,CAAC9H,CAAC,GAAG6H,EAAE,CAAC7H,CAAC,CAAC;IAC3BC,CAAC,EAAE4H,EAAE,CAAC5H,CAAC,GAAGyC,CAAC,IAAIoF,EAAE,CAAC7H,CAAC,GAAG4H,EAAE,CAAC5H,CAAC;EAC5B,CAAC;AACH;AACA,SAAS0uB,qBAAqBA,CAAC9mB,EAAE,EAAEC,EAAE,EAAEpF,CAAC,EAAEmV,IAAI,EAAE;EAC9C,OAAO;IACL7X,CAAC,EAAE6H,EAAE,CAAC7H,CAAC,GAAG0C,CAAC,IAAIoF,EAAE,CAAC9H,CAAC,GAAG6H,EAAE,CAAC7H,CAAC,CAAC;IAC3BC,CAAC,EAAE4X,IAAI,KAAK,QAAQ,GAAGnV,CAAC,GAAG,GAAG,GAAGmF,EAAE,CAAC5H,CAAC,GAAG6H,EAAE,CAAC7H,CAAC,GAC1C4X,IAAI,KAAK,OAAO,GAAGnV,CAAC,GAAG,CAAC,GAAGmF,EAAE,CAAC5H,CAAC,GAAG6H,EAAE,CAAC7H,CAAC,GACtCyC,CAAC,GAAG,CAAC,GAAGoF,EAAE,CAAC7H,CAAC,GAAG4H,EAAE,CAAC5H;EACtB,CAAC;AACH;AACA,SAAS2uB,oBAAoBA,CAAC/mB,EAAE,EAAEC,EAAE,EAAEpF,CAAC,EAAEmV,IAAI,EAAE;EAC7C,MAAMgX,GAAG,GAAG;IAAC7uB,CAAC,EAAE6H,EAAE,CAACgW,IAAI;IAAE5d,CAAC,EAAE4H,EAAE,CAACkW;EAAI,CAAC;EACpC,MAAM+Q,GAAG,GAAG;IAAC9uB,CAAC,EAAE8H,EAAE,CAAC8V,IAAI;IAAE3d,CAAC,EAAE6H,EAAE,CAACgW;EAAI,CAAC;EACpC,MAAMpgB,CAAC,GAAGgxB,YAAY,CAAC7mB,EAAE,EAAEgnB,GAAG,EAAEnsB,CAAC,CAAC;EAClC,MAAM/E,CAAC,GAAG+wB,YAAY,CAACG,GAAG,EAAEC,GAAG,EAAEpsB,CAAC,CAAC;EACnC,MAAM6C,CAAC,GAAGmpB,YAAY,CAACI,GAAG,EAAEhnB,EAAE,EAAEpF,CAAC,CAAC;EAClC,MAAMqC,CAAC,GAAG2pB,YAAY,CAAChxB,CAAC,EAAEC,CAAC,EAAE+E,CAAC,CAAC;EAC/B,MAAM1E,CAAC,GAAG0wB,YAAY,CAAC/wB,CAAC,EAAE4H,CAAC,EAAE7C,CAAC,CAAC;EAC/B,OAAOgsB,YAAY,CAAC3pB,CAAC,EAAE/G,CAAC,EAAE0E,CAAC,CAAC;AAC9B;AAEA,MAAMqsB,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,SAASC,eAAeA,CAACC,MAAM,EAAEtzB,OAAO,EAAE;EACxCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMuzB,QAAQ,GAAGD,MAAM,GAAGE,IAAI,CAACC,SAAS,CAACzzB,OAAO,CAAC;EACjD,IAAI0zB,SAAS,GAAGP,SAAS,CAACrW,GAAG,CAACyW,QAAQ,CAAC;EACvC,IAAI,CAACG,SAAS,EAAE;IACdA,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAACN,MAAM,EAAEtzB,OAAO,CAAC;IAClDmzB,SAAS,CAAC7Y,GAAG,CAACiZ,QAAQ,EAAEG,SAAS,CAAC;EACpC;EACA,OAAOA,SAAS;AAClB;AACA,SAASG,YAAYA,CAACC,GAAG,EAAER,MAAM,EAAEtzB,OAAO,EAAE;EAC1C,OAAOqzB,eAAe,CAACC,MAAM,EAAEtzB,OAAO,CAAC,CAAC+zB,MAAM,CAACD,GAAG,CAAC;AACrD;AAEA,MAAME,qBAAqB,GAAG,SAAAA,CAASC,KAAK,EAAEzV,KAAK,EAAE;EACnD,OAAO;IACLpa,CAACA,CAACA,CAAC,EAAE;MACH,OAAO6vB,KAAK,GAAGA,KAAK,GAAGzV,KAAK,GAAGpa,CAAC;IAClC,CAAC;IACD8vB,QAAQA,CAAC1oB,CAAC,EAAE;MACVgT,KAAK,GAAGhT,CAAC;IACX,CAAC;IACD4X,SAASA,CAAC5mB,KAAK,EAAE;MACf,IAAIA,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAOA,KAAK;MACd;MACA,OAAOA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC7C,CAAC;IACD23B,KAAKA,CAAC/vB,CAAC,EAAE/G,KAAK,EAAE;MACd,OAAO+G,CAAC,GAAG/G,KAAK;IAClB,CAAC;IACD+2B,UAAUA,CAAChwB,CAAC,EAAEiwB,SAAS,EAAE;MACvB,OAAOjwB,CAAC,GAAGiwB,SAAS;IACtB;EACF,CAAC;AACH,CAAC;AACD,MAAMC,qBAAqB,GAAG,SAAAA,CAAA,EAAW;EACvC,OAAO;IACLlwB,CAACA,CAACA,CAAC,EAAE;MACH,OAAOA,CAAC;IACV,CAAC;IACD8vB,QAAQA,CAAC1oB,CAAC,EAAE,CACZ,CAAC;IACD4X,SAASA,CAAC5mB,KAAK,EAAE;MACf,OAAOA,KAAK;IACd,CAAC;IACD23B,KAAKA,CAAC/vB,CAAC,EAAE/G,KAAK,EAAE;MACd,OAAO+G,CAAC,GAAG/G,KAAK;IAClB,CAAC;IACD+2B,UAAUA,CAAChwB,CAAC,EAAEmwB,UAAU,EAAE;MACxB,OAAOnwB,CAAC;IACV;EACF,CAAC;AACH,CAAC;AACD,SAASowB,aAAaA,CAACz3B,GAAG,EAAEk3B,KAAK,EAAEzV,KAAK,EAAE;EACxC,OAAOzhB,GAAG,GAAGi3B,qBAAqB,CAACC,KAAK,EAAEzV,KAAK,CAAC,GAAG8V,qBAAqB,CAAC,CAAC;AAC5E;AACA,SAASG,qBAAqBA,CAAC7Y,GAAG,EAAE8Y,SAAS,EAAE;EAC7C,IAAIlZ,KAAK,EAAEmZ,QAAQ;EACnB,IAAID,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,EAAE;IAC9ClZ,KAAK,GAAGI,GAAG,CAAC6D,MAAM,CAACjE,KAAK;IACxBmZ,QAAQ,GAAG,CACTnZ,KAAK,CAACoU,gBAAgB,CAAC,WAAW,CAAC,EACnCpU,KAAK,CAACoZ,mBAAmB,CAAC,WAAW,CAAC,CACvC;IACDpZ,KAAK,CAACqZ,WAAW,CAAC,WAAW,EAAEH,SAAS,EAAE,WAAW,CAAC;IACtD9Y,GAAG,CAACkZ,iBAAiB,GAAGH,QAAQ;EAClC;AACF;AACA,SAASI,oBAAoBA,CAACnZ,GAAG,EAAE+Y,QAAQ,EAAE;EAC3C,IAAIA,QAAQ,KAAK9zB,SAAS,EAAE;IAC1B,OAAO+a,GAAG,CAACkZ,iBAAiB;IAC5BlZ,GAAG,CAAC6D,MAAM,CAACjE,KAAK,CAACqZ,WAAW,CAAC,WAAW,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrE;AACF;AAEA,SAASK,UAAUA,CAACpwB,QAAQ,EAAE;EAC5B,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO;MACLqwB,OAAO,EAAE9uB,aAAa;MACtB+uB,OAAO,EAAEjvB,UAAU;MACnBkvB,SAAS,EAAEjvB;IACb,CAAC;EACH;EACA,OAAO;IACL+uB,OAAO,EAAEruB,UAAU;IACnBsuB,OAAO,EAAEA,CAACpzB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC;IACxBozB,SAAS,EAAE/wB,CAAC,IAAIA;EAClB,CAAC;AACH;AACA,SAASgxB,gBAAgBA,CAAAC,IAAA,EAAmC;EAAA,IAAlC;IAAC34B,KAAK;IAAEC,GAAG;IAAE24B,KAAK;IAAE9G,IAAI;IAAEhT;EAAK,CAAC,GAAA6Z,IAAA;EACxD,OAAO;IACL34B,KAAK,EAAEA,KAAK,GAAG44B,KAAK;IACpB34B,GAAG,EAAEA,GAAG,GAAG24B,KAAK;IAChB9G,IAAI,EAAEA,IAAI,IAAI,CAAC7xB,GAAG,GAAGD,KAAK,GAAG,CAAC,IAAI44B,KAAK,KAAK,CAAC;IAC7C9Z;EACF,CAAC;AACH;AACA,SAAS+Z,UAAUA,CAACC,OAAO,EAAEvJ,MAAM,EAAEwJ,MAAM,EAAE;EAC3C,MAAM;IAAC7wB,QAAQ;IAAElI,KAAK,EAAEg5B,UAAU;IAAE/4B,GAAG,EAAEg5B;EAAQ,CAAC,GAAGF,MAAM;EAC3D,MAAM;IAACR,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAU,CAACpwB,QAAQ,CAAC;EACjD,MAAM0wB,KAAK,GAAGrJ,MAAM,CAACrwB,MAAM;EAC3B,IAAI;IAACc,KAAK;IAAEC,GAAG;IAAE6xB;EAAI,CAAC,GAAGgH,OAAO;EAChC,IAAI92B,CAAC,EAAEM,IAAI;EACX,IAAIwvB,IAAI,EAAE;IACR9xB,KAAK,IAAI44B,KAAK;IACd34B,GAAG,IAAI24B,KAAK;IACZ,KAAK52B,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAGs2B,KAAK,EAAE52B,CAAC,GAAGM,IAAI,EAAE,EAAEN,CAAC,EAAE;MACvC,IAAI,CAACu2B,OAAO,CAACE,SAAS,CAAClJ,MAAM,CAACvvB,KAAK,GAAG44B,KAAK,CAAC,CAAC1wB,QAAQ,CAAC,CAAC,EAAE8wB,UAAU,EAAEC,QAAQ,CAAC,EAAE;QAC9E;MACF;MACAj5B,KAAK,EAAE;MACPC,GAAG,EAAE;IACP;IACAD,KAAK,IAAI44B,KAAK;IACd34B,GAAG,IAAI24B,KAAK;EACd;EACA,IAAI34B,GAAG,GAAGD,KAAK,EAAE;IACfC,GAAG,IAAI24B,KAAK;EACd;EACA,OAAO;IAAC54B,KAAK;IAAEC,GAAG;IAAE6xB,IAAI;IAAEhT,KAAK,EAAEga,OAAO,CAACha;EAAK,CAAC;AACjD;AACA,SAASoa,aAAaA,CAACJ,OAAO,EAAEvJ,MAAM,EAAEwJ,MAAM,EAAE;EAC9C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAACD,OAAO,CAAC;EAClB;EACA,MAAM;IAAC5wB,QAAQ;IAAElI,KAAK,EAAEg5B,UAAU;IAAE/4B,GAAG,EAAEg5B;EAAQ,CAAC,GAAGF,MAAM;EAC3D,MAAMH,KAAK,GAAGrJ,MAAM,CAACrwB,MAAM;EAC3B,MAAM;IAACs5B,OAAO;IAAED,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAU,CAACpwB,QAAQ,CAAC;EAC1D,MAAM;IAAClI,KAAK;IAAEC,GAAG;IAAE6xB,IAAI;IAAEhT;EAAK,CAAC,GAAG+Z,UAAU,CAACC,OAAO,EAAEvJ,MAAM,EAAEwJ,MAAM,CAAC;EACrE,MAAM7xB,MAAM,GAAG,EAAE;EACjB,IAAIiyB,MAAM,GAAG,KAAK;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIz4B,KAAK,EAAE8jB,KAAK,EAAE4U,SAAS;EAC3B,MAAMC,aAAa,GAAGA,CAAA,KAAMf,OAAO,CAACS,UAAU,EAAEK,SAAS,EAAE14B,KAAK,CAAC,IAAI63B,OAAO,CAACQ,UAAU,EAAEK,SAAS,CAAC,KAAK,CAAC;EACzG,MAAME,WAAW,GAAGA,CAAA,KAAMf,OAAO,CAACS,QAAQ,EAAEt4B,KAAK,CAAC,KAAK,CAAC,IAAI43B,OAAO,CAACU,QAAQ,EAAEI,SAAS,EAAE14B,KAAK,CAAC;EAC/F,MAAM64B,WAAW,GAAGA,CAAA,KAAML,MAAM,IAAIG,aAAa,CAAC,CAAC;EACnD,MAAMG,UAAU,GAAGA,CAAA,KAAM,CAACN,MAAM,IAAII,WAAW,CAAC,CAAC;EACjD,KAAK,IAAIv3B,CAAC,GAAGhC,KAAK,EAAEkyB,IAAI,GAAGlyB,KAAK,EAAEgC,CAAC,IAAI/B,GAAG,EAAE,EAAE+B,CAAC,EAAE;IAC/CyiB,KAAK,GAAG8K,MAAM,CAACvtB,CAAC,GAAG42B,KAAK,CAAC;IACzB,IAAInU,KAAK,CAAC+K,IAAI,EAAE;MACd;IACF;IACA7uB,KAAK,GAAG83B,SAAS,CAAChU,KAAK,CAACvc,QAAQ,CAAC,CAAC;IAClC,IAAIvH,KAAK,KAAK04B,SAAS,EAAE;MACvB;IACF;IACAF,MAAM,GAAGZ,OAAO,CAAC53B,KAAK,EAAEq4B,UAAU,EAAEC,QAAQ,CAAC;IAC7C,IAAIG,QAAQ,KAAK,IAAI,IAAII,WAAW,CAAC,CAAC,EAAE;MACtCJ,QAAQ,GAAGZ,OAAO,CAAC73B,KAAK,EAAEq4B,UAAU,CAAC,KAAK,CAAC,GAAGh3B,CAAC,GAAGkwB,IAAI;IACxD;IACA,IAAIkH,QAAQ,KAAK,IAAI,IAAIK,UAAU,CAAC,CAAC,EAAE;MACrCvyB,MAAM,CAACE,IAAI,CAACsxB,gBAAgB,CAAC;QAAC14B,KAAK,EAAEo5B,QAAQ;QAAEn5B,GAAG,EAAE+B,CAAC;QAAE8vB,IAAI;QAAE8G,KAAK;QAAE9Z;MAAK,CAAC,CAAC,CAAC;MAC5Esa,QAAQ,GAAG,IAAI;IACjB;IACAlH,IAAI,GAAGlwB,CAAC;IACRq3B,SAAS,GAAG14B,KAAK;EACnB;EACA,IAAIy4B,QAAQ,KAAK,IAAI,EAAE;IACrBlyB,MAAM,CAACE,IAAI,CAACsxB,gBAAgB,CAAC;MAAC14B,KAAK,EAAEo5B,QAAQ;MAAEn5B,GAAG;MAAE6xB,IAAI;MAAE8G,KAAK;MAAE9Z;IAAK,CAAC,CAAC,CAAC;EAC3E;EACA,OAAO5X,MAAM;AACf;AACA,SAASwyB,cAAcA,CAAC1T,IAAI,EAAE+S,MAAM,EAAE;EACpC,MAAM7xB,MAAM,GAAG,EAAE;EACjB,MAAMyyB,QAAQ,GAAG3T,IAAI,CAAC2T,QAAQ;EAC9B,KAAK,IAAI33B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG23B,QAAQ,CAACz6B,MAAM,EAAE8C,CAAC,EAAE,EAAE;IACxC,MAAM43B,GAAG,GAAGV,aAAa,CAACS,QAAQ,CAAC33B,CAAC,CAAC,EAAEgkB,IAAI,CAACuJ,MAAM,EAAEwJ,MAAM,CAAC;IAC3D,IAAIa,GAAG,CAAC16B,MAAM,EAAE;MACdgI,MAAM,CAACE,IAAI,CAAC,GAAGwyB,GAAG,CAAC;IACrB;EACF;EACA,OAAO1yB,MAAM;AACf;AACA,SAAS2yB,eAAeA,CAACtK,MAAM,EAAEqJ,KAAK,EAAE9G,IAAI,EAAEE,QAAQ,EAAE;EACtD,IAAIhyB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG24B,KAAK,GAAG,CAAC;EACnB,IAAI9G,IAAI,IAAI,CAACE,QAAQ,EAAE;IACrB,OAAOhyB,KAAK,GAAG44B,KAAK,IAAI,CAACrJ,MAAM,CAACvvB,KAAK,CAAC,CAACwvB,IAAI,EAAE;MAC3CxvB,KAAK,EAAE;IACT;EACF;EACA,OAAOA,KAAK,GAAG44B,KAAK,IAAIrJ,MAAM,CAACvvB,KAAK,CAAC,CAACwvB,IAAI,EAAE;IAC1CxvB,KAAK,EAAE;EACT;EACAA,KAAK,IAAI44B,KAAK;EACd,IAAI9G,IAAI,EAAE;IACR7xB,GAAG,IAAID,KAAK;EACd;EACA,OAAOC,GAAG,GAAGD,KAAK,IAAIuvB,MAAM,CAACtvB,GAAG,GAAG24B,KAAK,CAAC,CAACpJ,IAAI,EAAE;IAC9CvvB,GAAG,EAAE;EACP;EACAA,GAAG,IAAI24B,KAAK;EACZ,OAAO;IAAC54B,KAAK;IAAEC;EAAG,CAAC;AACrB;AACA,SAAS65B,aAAaA,CAACvK,MAAM,EAAEvvB,KAAK,EAAEoI,GAAG,EAAE0pB,IAAI,EAAE;EAC/C,MAAM8G,KAAK,GAAGrJ,MAAM,CAACrwB,MAAM;EAC3B,MAAMgI,MAAM,GAAG,EAAE;EACjB,IAAI6yB,IAAI,GAAG/5B,KAAK;EAChB,IAAIkyB,IAAI,GAAG3C,MAAM,CAACvvB,KAAK,CAAC;EACxB,IAAIC,GAAG;EACP,KAAKA,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAEC,GAAG,IAAImI,GAAG,EAAE,EAAEnI,GAAG,EAAE;IACvC,MAAM+5B,GAAG,GAAGzK,MAAM,CAACtvB,GAAG,GAAG24B,KAAK,CAAC;IAC/B,IAAIoB,GAAG,CAACxK,IAAI,IAAIwK,GAAG,CAACC,IAAI,EAAE;MACxB,IAAI,CAAC/H,IAAI,CAAC1C,IAAI,EAAE;QACdsC,IAAI,GAAG,KAAK;QACZ5qB,MAAM,CAACE,IAAI,CAAC;UAACpH,KAAK,EAAEA,KAAK,GAAG44B,KAAK;UAAE34B,GAAG,EAAE,CAACA,GAAG,GAAG,CAAC,IAAI24B,KAAK;UAAE9G;QAAI,CAAC,CAAC;QACjE9xB,KAAK,GAAG+5B,IAAI,GAAGC,GAAG,CAACC,IAAI,GAAGh6B,GAAG,GAAG,IAAI;MACtC;IACF,CAAC,MAAM;MACL85B,IAAI,GAAG95B,GAAG;MACV,IAAIiyB,IAAI,CAAC1C,IAAI,EAAE;QACbxvB,KAAK,GAAGC,GAAG;MACb;IACF;IACAiyB,IAAI,GAAG8H,GAAG;EACZ;EACA,IAAID,IAAI,KAAK,IAAI,EAAE;IACjB7yB,MAAM,CAACE,IAAI,CAAC;MAACpH,KAAK,EAAEA,KAAK,GAAG44B,KAAK;MAAE34B,GAAG,EAAE85B,IAAI,GAAGnB,KAAK;MAAE9G;IAAI,CAAC,CAAC;EAC9D;EACA,OAAO5qB,MAAM;AACf;AACA,SAASgzB,gBAAgBA,CAAClU,IAAI,EAAEmU,cAAc,EAAE;EAC9C,MAAM5K,MAAM,GAAGvJ,IAAI,CAACuJ,MAAM;EAC1B,MAAMyC,QAAQ,GAAGhM,IAAI,CAAC1iB,OAAO,CAAC0uB,QAAQ;EACtC,MAAM4G,KAAK,GAAGrJ,MAAM,CAACrwB,MAAM;EAC3B,IAAI,CAAC05B,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,MAAM9G,IAAI,GAAG,CAAC,CAAC9L,IAAI,CAACoU,KAAK;EACzB,MAAM;IAACp6B,KAAK;IAAEC;EAAG,CAAC,GAAG45B,eAAe,CAACtK,MAAM,EAAEqJ,KAAK,EAAE9G,IAAI,EAAEE,QAAQ,CAAC;EACnE,IAAIA,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAOqI,aAAa,CAACrU,IAAI,EAAE,CAAC;MAAChmB,KAAK;MAAEC,GAAG;MAAE6xB;IAAI,CAAC,CAAC,EAAEvC,MAAM,EAAE4K,cAAc,CAAC;EAC1E;EACA,MAAM/xB,GAAG,GAAGnI,GAAG,GAAGD,KAAK,GAAGC,GAAG,GAAG24B,KAAK,GAAG34B,GAAG;EAC3C,MAAMq6B,YAAY,GAAG,CAAC,CAACtU,IAAI,CAACuU,SAAS,IAAIv6B,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAK24B,KAAK,GAAG,CAAC;EACzE,OAAOyB,aAAa,CAACrU,IAAI,EAAE8T,aAAa,CAACvK,MAAM,EAAEvvB,KAAK,EAAEoI,GAAG,EAAEkyB,YAAY,CAAC,EAAE/K,MAAM,EAAE4K,cAAc,CAAC;AACrG;AACA,SAASE,aAAaA,CAACrU,IAAI,EAAE2T,QAAQ,EAAEpK,MAAM,EAAE4K,cAAc,EAAE;EAC7D,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAAC7M,UAAU,IAAI,CAACiC,MAAM,EAAE;IAC5D,OAAOoK,QAAQ;EACjB;EACA,OAAOa,eAAe,CAACxU,IAAI,EAAE2T,QAAQ,EAAEpK,MAAM,EAAE4K,cAAc,CAAC;AAChE;AACA,SAASK,eAAeA,CAACxU,IAAI,EAAE2T,QAAQ,EAAEpK,MAAM,EAAE4K,cAAc,EAAE;EAC/D,MAAMM,YAAY,GAAGzU,IAAI,CAAC0U,MAAM,CAAC1X,UAAU,CAAC,CAAC;EAC7C,MAAM2X,SAAS,GAAGC,SAAS,CAAC5U,IAAI,CAAC1iB,OAAO,CAAC;EACzC,MAAM;IAACu3B,aAAa,EAAEp4B,YAAY;IAAEa,OAAO,EAAE;MAAC0uB;IAAQ;EAAC,CAAC,GAAGhM,IAAI;EAC/D,MAAM4S,KAAK,GAAGrJ,MAAM,CAACrwB,MAAM;EAC3B,MAAMgI,MAAM,GAAG,EAAE;EACjB,IAAI4zB,SAAS,GAAGH,SAAS;EACzB,IAAI36B,KAAK,GAAG25B,QAAQ,CAAC,CAAC,CAAC,CAAC35B,KAAK;EAC7B,IAAIgC,CAAC,GAAGhC,KAAK;EACb,SAAS+6B,QAAQA,CAACpxB,CAAC,EAAEjE,CAAC,EAAEqI,CAAC,EAAEitB,EAAE,EAAE;IAC7B,MAAMC,GAAG,GAAGjJ,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B,IAAIroB,CAAC,KAAKjE,CAAC,EAAE;MACX;IACF;IACAiE,CAAC,IAAIivB,KAAK;IACV,OAAOrJ,MAAM,CAAC5lB,CAAC,GAAGivB,KAAK,CAAC,CAACpJ,IAAI,EAAE;MAC7B7lB,CAAC,IAAIsxB,GAAG;IACV;IACA,OAAO1L,MAAM,CAAC7pB,CAAC,GAAGkzB,KAAK,CAAC,CAACpJ,IAAI,EAAE;MAC7B9pB,CAAC,IAAIu1B,GAAG;IACV;IACA,IAAItxB,CAAC,GAAGivB,KAAK,KAAKlzB,CAAC,GAAGkzB,KAAK,EAAE;MAC3B1xB,MAAM,CAACE,IAAI,CAAC;QAACpH,KAAK,EAAE2J,CAAC,GAAGivB,KAAK;QAAE34B,GAAG,EAAEyF,CAAC,GAAGkzB,KAAK;QAAE9G,IAAI,EAAE/jB,CAAC;QAAE+Q,KAAK,EAAEkc;MAAE,CAAC,CAAC;MACnEF,SAAS,GAAGE,EAAE;MACdh7B,KAAK,GAAG0F,CAAC,GAAGkzB,KAAK;IACnB;EACF;EACA,KAAK,MAAME,OAAO,IAAIa,QAAQ,EAAE;IAC9B35B,KAAK,GAAGgyB,QAAQ,GAAGhyB,KAAK,GAAG84B,OAAO,CAAC94B,KAAK;IACxC,IAAIkyB,IAAI,GAAG3C,MAAM,CAACvvB,KAAK,GAAG44B,KAAK,CAAC;IAChC,IAAI9Z,KAAK;IACT,KAAK9c,CAAC,GAAGhC,KAAK,GAAG,CAAC,EAAEgC,CAAC,IAAI82B,OAAO,CAAC74B,GAAG,EAAE+B,CAAC,EAAE,EAAE;MACzC,MAAMwvB,EAAE,GAAGjC,MAAM,CAACvtB,CAAC,GAAG42B,KAAK,CAAC;MAC5B9Z,KAAK,GAAG8b,SAAS,CAACT,cAAc,CAAC7M,UAAU,CAAChE,aAAa,CAACmR,YAAY,EAAE;QACtE55B,IAAI,EAAE,SAAS;QACfq6B,EAAE,EAAEhJ,IAAI;QACR3iB,EAAE,EAAEiiB,EAAE;QACN2J,WAAW,EAAE,CAACn5B,CAAC,GAAG,CAAC,IAAI42B,KAAK;QAC5BwC,WAAW,EAAEp5B,CAAC,GAAG42B,KAAK;QACtBn2B;MACF,CAAC,CAAC,CAAC,CAAC;MACJ,IAAI44B,YAAY,CAACvc,KAAK,EAAEgc,SAAS,CAAC,EAAE;QAClCC,QAAQ,CAAC/6B,KAAK,EAAEgC,CAAC,GAAG,CAAC,EAAE82B,OAAO,CAAChH,IAAI,EAAEgJ,SAAS,CAAC;MACjD;MACA5I,IAAI,GAAGV,EAAE;MACTsJ,SAAS,GAAGhc,KAAK;IACnB;IACA,IAAI9e,KAAK,GAAGgC,CAAC,GAAG,CAAC,EAAE;MACjB+4B,QAAQ,CAAC/6B,KAAK,EAAEgC,CAAC,GAAG,CAAC,EAAE82B,OAAO,CAAChH,IAAI,EAAEgJ,SAAS,CAAC;IACjD;EACF;EACA,OAAO5zB,MAAM;AACf;AACA,SAAS0zB,SAASA,CAACt3B,OAAO,EAAE;EAC1B,OAAO;IACL4a,eAAe,EAAE5a,OAAO,CAAC4a,eAAe;IACxCod,cAAc,EAAEh4B,OAAO,CAACg4B,cAAc;IACtCC,UAAU,EAAEj4B,OAAO,CAACi4B,UAAU;IAC9BC,gBAAgB,EAAEl4B,OAAO,CAACk4B,gBAAgB;IAC1CC,eAAe,EAAEn4B,OAAO,CAACm4B,eAAe;IACxCnX,WAAW,EAAEhhB,OAAO,CAACghB,WAAW;IAChCnG,WAAW,EAAE7a,OAAO,CAAC6a;EACvB,CAAC;AACH;AACA,SAASkd,YAAYA,CAACvc,KAAK,EAAEgc,SAAS,EAAE;EACtC,OAAOA,SAAS,IAAIhE,IAAI,CAACC,SAAS,CAACjY,KAAK,CAAC,KAAKgY,IAAI,CAACC,SAAS,CAAC+D,SAAS,CAAC;AACzE;AAEA,SAASj7B,kBAAkB,IAAI67B,CAAC,EAAE3R,aAAa,IAAIpd,CAAC,EAAEhE,iBAAiB,IAAIiE,CAAC,EAAE4b,SAAS,IAAI3b,CAAC,EAAEhL,IAAI,IAAIiL,CAAC,EAAEmoB,cAAc,IAAIloB,CAAC,EAAEulB,cAAc,IAAItlB,CAAC,EAAEmpB,YAAY,IAAItlB,CAAC,EAAE3K,OAAO,IAAI4K,CAAC,EAAEzS,SAAS,IAAI0S,CAAC,EAAE+kB,4BAA4B,IAAI9kB,CAAC,EAAEohB,eAAe,IAAIxhB,CAAC,EAAEvK,KAAK,IAAIsK,CAAC,EAAE1J,UAAU,IAAIyJ,CAAC,EAAErP,eAAe,IAAIoP,CAAC,EAAEtS,QAAQ,IAAIqS,CAAC,EAAE7K,EAAE,IAAI4K,CAAC,EAAEwY,SAAS,IAAIzY,CAAC,EAAE/H,SAAS,IAAI8H,CAAC,EAAEkR,YAAY,IAAInR,CAAC,EAAEvK,GAAG,IAAIsK,CAAC,EAAElG,WAAW,IAAIiG,CAAC,EAAEwS,WAAW,IAAIzS,CAAC,EAAE6U,QAAQ,IAAI9U,CAAC,EAAE0V,UAAU,IAAI3V,CAAC,EAAEiV,UAAU,IAAIlV,CAAC,EAAE2Y,MAAM,IAAI5Y,CAAC,EAAEob,YAAY,IAAI0Q,CAAC,EAAEhT,OAAO,IAAIvjB,CAAC,EAAEmE,UAAU,IAAIqyB,EAAE,EAAE77B,cAAc,IAAIqC,EAAE,EAAEmb,SAAS,IAAIlb,EAAE,EAAEoB,KAAK,IAAIo4B,EAAE,EAAEh3B,WAAW,IAAIi3B,EAAE,EAAEte,WAAW,IAAIue,EAAE,EAAE72B,UAAU,IAAI82B,EAAE,EAAElP,cAAc,IAAImP,EAAE,EAAE5Q,eAAe,IAAI6Q,EAAE,EAAEle,YAAY,IAAIme,EAAE,EAAEv4B,OAAO,IAAIw4B,EAAE,EAAE/D,oBAAoB,IAAIgE,EAAE,EAAE97B,IAAI,IAAI+7B,EAAE,EAAElzB,qBAAqB,IAAImzB,EAAE,EAAEv0B,kBAAkB,IAAIw0B,EAAE,EAAEj2B,OAAO,IAAIk2B,EAAE,EAAE30B,WAAW,IAAI40B,EAAE,EAAE/1B,YAAY,IAAIg2B,EAAE,EAAEl0B,cAAc,IAAIm0B,EAAE,EAAE7a,YAAY,IAAI8a,EAAE,EAAE7S,cAAc,IAAI8S,EAAE,EAAEtT,OAAO,IAAIuT,EAAE,EAAEzf,aAAa,IAAI0f,EAAE,EAAEr6B,OAAO,IAAIs6B,EAAE,EAAE55B,OAAO,IAAI65B,EAAE,EAAEr5B,SAAS,IAAIs5B,EAAE,EAAEp5B,WAAW,IAAIq5B,EAAE,EAAE9b,YAAY,IAAI+b,EAAE,EAAE3N,WAAW,IAAI4N,EAAE,EAAEjM,mBAAmB,IAAIkM,EAAE,EAAEvK,QAAQ,IAAIwK,EAAE,EAAE3/B,UAAU,IAAI4/B,EAAE,EAAE5V,YAAY,IAAI6V,EAAE,EAAE53B,KAAK,IAAI63B,EAAE,EAAE53B,QAAQ,IAAI63B,EAAE,EAAE33B,WAAW,IAAI43B,EAAE,EAAE13B,UAAU,IAAI23B,EAAE,EAAE13B,aAAa,IAAI23B,EAAE,EAAEv9B,GAAG,IAAIw9B,EAAE,EAAE1+B,QAAQ,IAAI2+B,EAAE,EAAE1I,WAAW,IAAI2I,EAAE,EAAEpb,WAAW,IAAIqb,EAAE,EAAEh5B,SAAS,IAAIi5B,EAAE,EAAEj8B,cAAc,IAAIk8B,EAAE,EAAE54B,aAAa,IAAI64B,EAAE,EAAEp0B,UAAU,IAAIq0B,EAAE,EAAEtW,iBAAiB,IAAIuW,EAAE,EAAE3M,0BAA0B,IAAI4M,EAAE,EAAEvE,gBAAgB,IAAIwE,EAAE,EAAEhF,cAAc,IAAIiF,EAAE,EAAEtI,qBAAqB,IAAIuI,EAAE,EAAEtI,oBAAoB,IAAIuI,EAAE,EAAEzI,YAAY,IAAI0I,EAAE,EAAE7Z,cAAc,IAAI8Z,EAAE,EAAE3Z,cAAc,IAAI4Z,EAAE,EAAE5b,SAAS,IAAI6b,EAAE,EAAE5X,kBAAkB,IAAI6X,EAAE,EAAE5W,MAAM,IAAI6W,EAAE,EAAE5W,aAAa,IAAI6W,EAAE,EAAElG,aAAa,IAAImG,EAAE,EAAE71B,eAAe,IAAI81B,EAAE,EAAExH,aAAa,IAAIyH,EAAE,EAAExH,qBAAqB,IAAIyH,EAAE,EAAEt/B,MAAM,IAAIu/B,EAAE,EAAE7+B,OAAO,IAAIyE,CAAC,EAAE4W,KAAK,IAAIhP,CAAC,EAAEgU,QAAQ,IAAIxU,CAAC,EAAEjC,OAAO,IAAI9E,CAAC,EAAEhB,gBAAgB,IAAIwI,CAAC,EAAEhM,cAAc,IAAIwM,CAAC,EAAE4b,aAAa,IAAItb,CAAC,EAAE/M,QAAQ,IAAIe,CAAC,EAAEiD,OAAO,IAAIwV,CAAC,EAAE/Z,aAAa,IAAIuC,CAAC,EAAEinB,iBAAiB,IAAInc,CAAC,EAAEvM,YAAY,IAAIgL,CAAC,EAAE5K,WAAW,IAAI4F,CAAC,EAAE2vB,YAAY,IAAIuI,CAAC,EAAEj2B,aAAa,IAAIf,CAAC,EAAEnB,QAAQ,IAAIo4B,CAAC,EAAE1hC,gBAAgB,IAAIwP,CAAC,EAAEnH,IAAI,IAAIqD,CAAC,EAAEtB,SAAS,IAAI+B,CAAC,EAAE2gB,mBAAmB,IAAI6U,CAAC,EAAEr+B,cAAc,IAAIiM,CAAC,EAAExD,WAAW,IAAI8E,CAAC,EAAEgb,YAAY,IAAIpiB,CAAC,EAAEysB,mBAAmB,IAAIxsB,CAAC,EAAE6c,cAAc,IAAIqb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}