import { OPEN_BRACE_EOL } from './brace-patterns.js';
import { parseDirective } from './parse-directive.js';
/**
 * A representation of a TestPoint result, with diagnostics if present.
 */
export class Result {
    ok;
    name = '';
    id = 0;
    buffered = false;
    tapError = null;
    skip = false;
    todo = false;
    previous = null;
    plan = null;
    diag = null;
    time = null;
    fullname = '';
    closingTestPoint = false;
    constructor(parsed, parser) {
        const ok = !parsed[1];
        const id = +(parsed[2] || 0);
        let buffered = parsed[4];
        this.ok = ok;
        if (parsed[2])
            this.id = id;
        let rest = parsed[3] || '';
        let name;
        // We know at this point the parsed result cannot contain \n,
        // so we can leverage that as a placeholder.
        // first, replace any PAIR of \ chars with \n
        // then, split on any # that is not preceeded by \
        // the first of these is definitely the description
        // the rest is the directive, if recognized, otherwise
        // we just lump it onto the description, but escaped.
        // then any \n chars in either are turned into \ (just one)
        // escape \ with \
        // swap out escaped \ with \n, then swap back
        rest = rest.replace(/(\\\\)/g, '\n');
        const [h, ...r] = rest.split(/(?<=\s|^)(?<!\\)#/g);
        name = (h || '').replace(/\\#/g, '#').replace(/\n/g, '\\');
        rest = r.join('#').replace(/\\#/g, '#').replace(/\n/g, '\\');
        // now, let's see if there's a directive in there.
        const dir = parseDirective(rest.trim());
        if (!dir)
            name += (rest ? '#' + rest : '') + buffered;
        else {
            // handle buffered subtests with todo/skip on them, like
            // ok 1 - bar # todo foo {\n
            const dirKey = dir[0];
            const dirValue = dir[1];
            if (dirKey === 'todo' || dirKey === 'skip') {
                this[dirKey] = dirValue;
            }
            else {
                if (dirKey === 'time') {
                    this.time = parseFloat(dirValue);
                    /* c8 ignore start */
                }
                /* c8 ignore stop */
            }
        }
        if (OPEN_BRACE_EOL.test(name)) {
            name = name.replace(OPEN_BRACE_EOL, '');
            buffered = '{';
        }
        if (buffered === '{')
            this.buffered = true;
        if (name)
            this.name = name.trim();
        const n = [];
        if (parser.fullname)
            n.push(parser.fullname);
        if (this.name)
            n.push(this.name);
        this.fullname = n.join(' > ').trim();
    }
}
//# sourceMappingURL=result.js.map