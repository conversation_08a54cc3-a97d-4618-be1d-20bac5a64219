{"name": "gonzales-pe", "description": "Gonzales Preprocessor Edition (fast CSS parser)", "version": "4.3.0", "homepage": "http://github.com/tonyganch/gonzales-pe", "bugs": "http://github.com/tonyganch/gonzales-pe/issues", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tonyganch.com"}, "main": "./lib/gonzales", "repository": {"type": "git", "url": "http://github.com/tonyganch/gonzales-pe.git"}, "scripts": {"autofix-tests": "bash ./scripts/build.sh && bash ./scripts/autofix-tests.sh", "build": "bash ./scripts/build.sh", "init": "bash ./scripts/init.sh", "lint": "bash ./scripts/lint.sh", "log": "bash ./scripts/log.sh", "prepublishOnly": "bash ./scripts/build.sh", "test": "bash ./scripts/test.sh", "watch": "bash ./scripts/watch.sh"}, "bin": {"gonzales": "./bin/gonzales.js"}, "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"babel-core": "^6.18.2", "babel-loader": "^6.2.7", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-es2015": "^6.18.0", "coffee-script": "~1.7.1", "eslint": "^3.0.0", "jscs": "2.1.0", "jshint": "2.10.2", "json-loader": "^0.5.3", "mocha": "2.2.x", "webpack": "^1.12.2", "webpack-closure-compiler": "^2.0.2"}, "engines": {"node": ">=0.6.0"}, "files": ["MIT-LICENSE.txt", "bin/gonzales.js", "lib/gonzales.js"]}