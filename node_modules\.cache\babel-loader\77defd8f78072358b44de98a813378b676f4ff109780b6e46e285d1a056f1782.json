{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\handleError.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { Costanti } from \"./traduttore/const\";\nimport { basePath } from \"./route\";\nimport logo from '../img/tm_logo-01.svg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NoMatch() {\n  setTimeout(function () {\n    window.location.pathname = basePath;\n  }, 15000);\n  var error = '';\n  var codeError = 0;\n  error = window.sessionStorage.getItem(\"Error\");\n  codeError = window.sessionStorage.getItem(\"CodeError\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container handleError\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row align-items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"fontLogo\",\n          className: \"logo text-center mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: logo,\n            onError: e => e.target.src = logo,\n            alt: \"Logo\",\n            width: \"200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 70\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: Costanti.Error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"big\", {\n              children: [Costanti.ErrServ, \" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 52\n              }, this), \" \", Costanti.CodErr, \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: codeError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 77\n              }, this), \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 28\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-3\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-2\",\n            children: Costanti.Redirect\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-2\",\n            children: Costanti.OppureClicca\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            className: \"p-button p-component p-button\",\n            to: basePath,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-chevron-circle-left mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 88\n            }, this), Costanti.GoBack]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n}\n_c = NoMatch;\nexport default NoMatch;\nvar _c;\n$RefreshReg$(_c, \"NoMatch\");", "map": {"version": 3, "names": ["React", "Link", "<PERSON><PERSON>", "basePath", "logo", "jsxDEV", "_jsxDEV", "NoMatch", "setTimeout", "window", "location", "pathname", "error", "codeError", "sessionStorage", "getItem", "className", "children", "id", "src", "onError", "e", "target", "alt", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Error", "ErrServ", "CodErr", "Redirect", "OppureClicca", "to", "GoBack", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/handleError.jsx"], "sourcesContent": ["import React from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { <PERSON>nti } from \"./traduttore/const\";\nimport { basePath } from \"./route\";\nimport logo from '../img/tm_logo-01.svg';\n\nfunction NoMatch() {\n\n    setTimeout(function () {\n        window.location.pathname = basePath;\n    }, 15000);\n\n    var error = ''\n    var codeError = 0\n    error = window.sessionStorage.getItem(\"Error\");\n    codeError = window.sessionStorage.getItem(\"CodeError\");\n\n    return (\n        <div className=\"container handleError\">\n            <div className=\"row align-items-center\">\n                <div className=\"col-12 mx-auto\">\n                <div id=\"fontLogo\" className=\"logo text-center mb-2\"><img src={logo} onError={(e) => e.target.src = logo} alt=\"Logo\" width=\"200\" />{/* <span>central</span> unit <span className=\"payoff text-center\">food & beverage • e-procurement system</span> */}</div>\n                    <hr></hr>\n                    <div className=\"text-center\">\n                        <h2>{Costanti.Error}</h2>\n                        <p><big>{Costanti.ErrServ} <br /> {Costanti.CodErr} <strong>{codeError}</strong> </big></p>\n                        <p className=\"mb-3\">{error}</p>\n                        <hr></hr>\n                        <p className=\"mb-2\">{Costanti.Redirect}</p>\n                        <p className=\"mb-2\">{Costanti.OppureClicca}</p>\n                        <Link className=\"p-button p-component p-button\" to={basePath} ><i className=\"pi pi-chevron-circle-left mr-2\"></i>{Costanti.GoBack}</Link>\n                    </div>\n                </div>\n            </div>\n        </div>\n    )\n\n}\n\nexport default NoMatch;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,IAAI,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,OAAOA,CAAA,EAAG;EAEfC,UAAU,CAAC,YAAY;IACnBC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGR,QAAQ;EACvC,CAAC,EAAE,KAAK,CAAC;EAET,IAAIS,KAAK,GAAG,EAAE;EACd,IAAIC,SAAS,GAAG,CAAC;EACjBD,KAAK,GAAGH,MAAM,CAACK,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;EAC9CF,SAAS,GAAGJ,MAAM,CAACK,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;EAEtD,oBACIT,OAAA;IAAKU,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eAClCX,OAAA;MAAKU,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACnCX,OAAA;QAAKU,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC/BX,OAAA;UAAKY,EAAE,EAAC,UAAU;UAACF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAACX,OAAA;YAAKa,GAAG,EAAEf,IAAK;YAACgB,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAGf,IAAK;YAACmB,GAAG,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAyH,CAAC,eACzPtB,OAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtB,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBX,OAAA;YAAAW,QAAA,EAAKf,QAAQ,CAAC2B;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzBtB,OAAA;YAAAW,QAAA,eAAGX,OAAA;cAAAW,QAAA,GAAMf,QAAQ,CAAC4B,OAAO,EAAC,GAAC,eAAAxB,OAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,KAAC,EAAC1B,QAAQ,CAAC6B,MAAM,EAAC,GAAC,eAAAzB,OAAA;gBAAAW,QAAA,EAASJ;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3FtB,OAAA;YAAGU,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAEL;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BtB,OAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtB,OAAA;YAAGU,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAEf,QAAQ,CAAC8B;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtB,OAAA;YAAGU,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAEf,QAAQ,CAAC+B;UAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CtB,OAAA,CAACL,IAAI;YAACe,SAAS,EAAC,+BAA+B;YAACkB,EAAE,EAAE/B,QAAS;YAAAc,QAAA,gBAAEX,OAAA;cAAGU,SAAS,EAAC;YAAgC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC1B,QAAQ,CAACiC,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAGd;AAACQ,EAAA,GA/BQ7B,OAAO;AAiChB,eAAeA,OAAO;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}