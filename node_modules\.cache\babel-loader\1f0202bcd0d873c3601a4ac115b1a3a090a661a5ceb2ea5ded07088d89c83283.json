{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { offset } from '../../util';\nimport MemoChildren from './MemoChildren';\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none'\n};\nvar Content = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var closable = props.closable,\n    prefixCls = props.prefixCls,\n    width = props.width,\n    height = props.height,\n    footer = props.footer,\n    title = props.title,\n    closeIcon = props.closeIcon,\n    style = props.style,\n    className = props.className,\n    visible = props.visible,\n    forceRender = props.forceRender,\n    bodyStyle = props.bodyStyle,\n    bodyProps = props.bodyProps,\n    children = props.children,\n    destroyOnClose = props.destroyOnClose,\n    modalRender = props.modalRender,\n    motionName = props.motionName,\n    ariaId = props.ariaId,\n    onClose = props.onClose,\n    onVisibleChanged = props.onVisibleChanged,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    mousePosition = props.mousePosition;\n  var sentinelStartRef = useRef();\n  var sentinelEndRef = useRef();\n  var dialogRef = useRef(); // ============================== Ref ===============================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _sentinelStartRef$cur;\n        (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus();\n      },\n      changeActive: function changeActive(next) {\n        var _document = document,\n          activeElement = _document.activeElement;\n        if (next && activeElement === sentinelEndRef.current) {\n          sentinelStartRef.current.focus();\n        } else if (!next && activeElement === sentinelStartRef.current) {\n          sentinelEndRef.current.focus();\n        }\n      }\n    };\n  }); // ============================= Style ==============================\n\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    transformOrigin = _React$useState2[0],\n    setTransformOrigin = _React$useState2[1];\n  var contentStyle = {};\n  if (width !== undefined) {\n    contentStyle.width = width;\n  }\n  if (height !== undefined) {\n    contentStyle.height = height;\n  }\n  if (transformOrigin) {\n    contentStyle.transformOrigin = transformOrigin;\n  }\n  function onPrepare() {\n    var elementOffset = offset(dialogRef.current);\n    setTransformOrigin(mousePosition ? \"\".concat(mousePosition.x - elementOffset.left, \"px \").concat(mousePosition.y - elementOffset.top, \"px\") : '');\n  } // ============================= Render =============================\n\n  var footerNode;\n  if (footer) {\n    footerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  var headerNode;\n  if (title) {\n    headerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-header\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-title\"),\n      id: ariaId\n    }, title));\n  }\n  var closer;\n  if (closable) {\n    closer = /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: onClose,\n      \"aria-label\": \"Close\",\n      className: \"\".concat(prefixCls, \"-close\")\n    }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-close-x\")\n    }));\n  }\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, closer, headerNode, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-body\"),\n    style: bodyStyle\n  }, bodyProps), children), footerNode);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    onVisibleChanged: onVisibleChanged,\n    onAppearPrepare: onPrepare,\n    onEnterPrepare: onPrepare,\n    forceRender: forceRender,\n    motionName: motionName,\n    removeOnLeave: destroyOnClose,\n    ref: dialogRef\n  }, function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: \"dialog-element\",\n      role: \"dialog\",\n      \"aria-modal\": \"true\",\n      ref: motionRef,\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), style), contentStyle),\n      className: classNames(prefixCls, className, motionClassName),\n      onMouseDown: onMouseDown,\n      onMouseUp: onMouseUp\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      tabIndex: 0,\n      ref: sentinelStartRef,\n      style: sentinelStyle,\n      \"aria-hidden\": \"true\"\n    }), /*#__PURE__*/React.createElement(MemoChildren, {\n      shouldUpdate: visible || forceRender\n    }, modalRender ? modalRender(content) : content), /*#__PURE__*/React.createElement(\"div\", {\n      tabIndex: 0,\n      ref: sentinelEndRef,\n      style: sentinelStyle,\n      \"aria-hidden\": \"true\"\n    }));\n  });\n});\nContent.displayName = 'Content';\nexport default Content;", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_slicedToArray", "React", "useRef", "classNames", "CSSMotion", "offset", "MemoC<PERSON><PERSON>n", "sentinelStyle", "width", "height", "overflow", "outline", "Content", "forwardRef", "props", "ref", "closable", "prefixCls", "footer", "title", "closeIcon", "style", "className", "visible", "forceRender", "bodyStyle", "bodyProps", "children", "destroyOnClose", "modalRender", "motionName", "ariaId", "onClose", "onVisibleChanged", "onMouseDown", "onMouseUp", "mousePosition", "sentinelStartRef", "sentinelEndRef", "dialogRef", "useImperativeHandle", "focus", "_sentinelStartRef$cur", "current", "changeActive", "next", "_document", "document", "activeElement", "_React$useState", "useState", "_React$useState2", "transform<PERSON><PERSON>in", "setTransformOrigin", "contentStyle", "undefined", "onPrepare", "elementOffset", "concat", "x", "left", "y", "top", "footerNode", "createElement", "headerNode", "id", "closer", "type", "onClick", "content", "onAppearPrepare", "onEnterPrepare", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "key", "role", "tabIndex", "shouldUpdate", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-dialog/es/Dialog/Content/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { offset } from '../../util';\nimport MemoChildren from './MemoChildren';\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none'\n};\nvar Content = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var closable = props.closable,\n      prefixCls = props.prefixCls,\n      width = props.width,\n      height = props.height,\n      footer = props.footer,\n      title = props.title,\n      closeIcon = props.closeIcon,\n      style = props.style,\n      className = props.className,\n      visible = props.visible,\n      forceRender = props.forceRender,\n      bodyStyle = props.bodyStyle,\n      bodyProps = props.bodyProps,\n      children = props.children,\n      destroyOnClose = props.destroyOnClose,\n      modalRender = props.modalRender,\n      motionName = props.motionName,\n      ariaId = props.ariaId,\n      onClose = props.onClose,\n      onVisibleChanged = props.onVisibleChanged,\n      onMouseDown = props.onMouseDown,\n      onMouseUp = props.onMouseUp,\n      mousePosition = props.mousePosition;\n  var sentinelStartRef = useRef();\n  var sentinelEndRef = useRef();\n  var dialogRef = useRef(); // ============================== Ref ===============================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _sentinelStartRef$cur;\n\n        (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus();\n      },\n      changeActive: function changeActive(next) {\n        var _document = document,\n            activeElement = _document.activeElement;\n\n        if (next && activeElement === sentinelEndRef.current) {\n          sentinelStartRef.current.focus();\n        } else if (!next && activeElement === sentinelStartRef.current) {\n          sentinelEndRef.current.focus();\n        }\n      }\n    };\n  }); // ============================= Style ==============================\n\n  var _React$useState = React.useState(),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      transformOrigin = _React$useState2[0],\n      setTransformOrigin = _React$useState2[1];\n\n  var contentStyle = {};\n\n  if (width !== undefined) {\n    contentStyle.width = width;\n  }\n\n  if (height !== undefined) {\n    contentStyle.height = height;\n  }\n\n  if (transformOrigin) {\n    contentStyle.transformOrigin = transformOrigin;\n  }\n\n  function onPrepare() {\n    var elementOffset = offset(dialogRef.current);\n    setTransformOrigin(mousePosition ? \"\".concat(mousePosition.x - elementOffset.left, \"px \").concat(mousePosition.y - elementOffset.top, \"px\") : '');\n  } // ============================= Render =============================\n\n\n  var footerNode;\n\n  if (footer) {\n    footerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n\n  var headerNode;\n\n  if (title) {\n    headerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-header\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-title\"),\n      id: ariaId\n    }, title));\n  }\n\n  var closer;\n\n  if (closable) {\n    closer = /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: onClose,\n      \"aria-label\": \"Close\",\n      className: \"\".concat(prefixCls, \"-close\")\n    }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-close-x\")\n    }));\n  }\n\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, closer, headerNode, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-body\"),\n    style: bodyStyle\n  }, bodyProps), children), footerNode);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    onVisibleChanged: onVisibleChanged,\n    onAppearPrepare: onPrepare,\n    onEnterPrepare: onPrepare,\n    forceRender: forceRender,\n    motionName: motionName,\n    removeOnLeave: destroyOnClose,\n    ref: dialogRef\n  }, function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: \"dialog-element\",\n      role: \"dialog\",\n      \"aria-modal\": \"true\",\n      ref: motionRef,\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), style), contentStyle),\n      className: classNames(prefixCls, className, motionClassName),\n      onMouseDown: onMouseDown,\n      onMouseUp: onMouseUp\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      tabIndex: 0,\n      ref: sentinelStartRef,\n      style: sentinelStyle,\n      \"aria-hidden\": \"true\"\n    }), /*#__PURE__*/React.createElement(MemoChildren, {\n      shouldUpdate: visible || forceRender\n    }, modalRender ? modalRender(content) : content), /*#__PURE__*/React.createElement(\"div\", {\n      tabIndex: 0,\n      ref: sentinelEndRef,\n      style: sentinelStyle,\n      \"aria-hidden\": \"true\"\n    }));\n  });\n});\nContent.displayName = 'Content';\nexport default Content;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,aAAa,GAAG;EAClBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,OAAO,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAChE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BT,KAAK,GAAGM,KAAK,CAACN,KAAK;IACnBC,MAAM,GAAGK,KAAK,CAACL,MAAM;IACrBS,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,cAAc,GAAGd,KAAK,CAACc,cAAc;IACrCC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,WAAW,GAAGpB,KAAK,CAACoB,WAAW;IAC/BC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,aAAa,GAAGtB,KAAK,CAACsB,aAAa;EACvC,IAAIC,gBAAgB,GAAGnC,MAAM,CAAC,CAAC;EAC/B,IAAIoC,cAAc,GAAGpC,MAAM,CAAC,CAAC;EAC7B,IAAIqC,SAAS,GAAGrC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE1BD,KAAK,CAACuC,mBAAmB,CAACzB,GAAG,EAAE,YAAY;IACzC,OAAO;MACL0B,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,IAAIC,qBAAqB;QAEzB,CAACA,qBAAqB,GAAGL,gBAAgB,CAACM,OAAO,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,KAAK,CAAC,CAAC;MAC1I,CAAC;MACDG,YAAY,EAAE,SAASA,YAAYA,CAACC,IAAI,EAAE;QACxC,IAAIC,SAAS,GAAGC,QAAQ;UACpBC,aAAa,GAAGF,SAAS,CAACE,aAAa;QAE3C,IAAIH,IAAI,IAAIG,aAAa,KAAKV,cAAc,CAACK,OAAO,EAAE;UACpDN,gBAAgB,CAACM,OAAO,CAACF,KAAK,CAAC,CAAC;QAClC,CAAC,MAAM,IAAI,CAACI,IAAI,IAAIG,aAAa,KAAKX,gBAAgB,CAACM,OAAO,EAAE;UAC9DL,cAAc,CAACK,OAAO,CAACF,KAAK,CAAC,CAAC;QAChC;MACF;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIQ,eAAe,GAAGhD,KAAK,CAACiD,QAAQ,CAAC,CAAC;IAClCC,gBAAgB,GAAGnD,cAAc,CAACiD,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE5C,IAAIG,YAAY,GAAG,CAAC,CAAC;EAErB,IAAI9C,KAAK,KAAK+C,SAAS,EAAE;IACvBD,YAAY,CAAC9C,KAAK,GAAGA,KAAK;EAC5B;EAEA,IAAIC,MAAM,KAAK8C,SAAS,EAAE;IACxBD,YAAY,CAAC7C,MAAM,GAAGA,MAAM;EAC9B;EAEA,IAAI2C,eAAe,EAAE;IACnBE,YAAY,CAACF,eAAe,GAAGA,eAAe;EAChD;EAEA,SAASI,SAASA,CAAA,EAAG;IACnB,IAAIC,aAAa,GAAGpD,MAAM,CAACkC,SAAS,CAACI,OAAO,CAAC;IAC7CU,kBAAkB,CAACjB,aAAa,GAAG,EAAE,CAACsB,MAAM,CAACtB,aAAa,CAACuB,CAAC,GAAGF,aAAa,CAACG,IAAI,EAAE,KAAK,CAAC,CAACF,MAAM,CAACtB,aAAa,CAACyB,CAAC,GAAGJ,aAAa,CAACK,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;EACnJ,CAAC,CAAC;;EAGF,IAAIC,UAAU;EAEd,IAAI7C,MAAM,EAAE;IACV6C,UAAU,GAAG,aAAa9D,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MACnD1C,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAEC,MAAM,CAAC;EACZ;EAEA,IAAI+C,UAAU;EAEd,IAAI9C,KAAK,EAAE;IACT8C,UAAU,GAAG,aAAahE,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MACnD1C,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE,aAAahB,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MACzC1C,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,QAAQ,CAAC;MACzCiD,EAAE,EAAEnC;IACN,CAAC,EAAEZ,KAAK,CAAC,CAAC;EACZ;EAEA,IAAIgD,MAAM;EAEV,IAAInD,QAAQ,EAAE;IACZmD,MAAM,GAAG,aAAalE,KAAK,CAAC+D,aAAa,CAAC,QAAQ,EAAE;MAClDI,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAErC,OAAO;MAChB,YAAY,EAAE,OAAO;MACrBV,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,QAAQ;IAC1C,CAAC,EAAEG,SAAS,IAAI,aAAanB,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAE;MACvD1C,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,UAAU;IAC5C,CAAC,CAAC,CAAC;EACL;EAEA,IAAIqD,OAAO,GAAG,aAAarE,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;IACpD1C,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEkD,MAAM,EAAEF,UAAU,EAAE,aAAahE,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAEjE,QAAQ,CAAC;IACtEuB,SAAS,EAAE,EAAE,CAACoC,MAAM,CAACzC,SAAS,EAAE,OAAO,CAAC;IACxCI,KAAK,EAAEI;EACT,CAAC,EAAEC,SAAS,CAAC,EAAEC,QAAQ,CAAC,EAAEoC,UAAU,CAAC;EACrC,OAAO,aAAa9D,KAAK,CAAC+D,aAAa,CAAC5D,SAAS,EAAE;IACjDmB,OAAO,EAAEA,OAAO;IAChBU,gBAAgB,EAAEA,gBAAgB;IAClCsC,eAAe,EAAEf,SAAS;IAC1BgB,cAAc,EAAEhB,SAAS;IACzBhC,WAAW,EAAEA,WAAW;IACxBM,UAAU,EAAEA,UAAU;IACtB2C,aAAa,EAAE7C,cAAc;IAC7Bb,GAAG,EAAEwB;EACP,CAAC,EAAE,UAAUmC,IAAI,EAAEC,SAAS,EAAE;IAC5B,IAAIC,eAAe,GAAGF,IAAI,CAACpD,SAAS;MAChCuD,WAAW,GAAGH,IAAI,CAACrD,KAAK;IAC5B,OAAO,aAAapB,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MAC7Cc,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,QAAQ;MACd,YAAY,EAAE,MAAM;MACpBhE,GAAG,EAAE4D,SAAS;MACdtD,KAAK,EAAEvB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+E,WAAW,CAAC,EAAExD,KAAK,CAAC,EAAEiC,YAAY,CAAC;MACxFhC,SAAS,EAAEnB,UAAU,CAACc,SAAS,EAAEK,SAAS,EAAEsD,eAAe,CAAC;MAC5D1C,WAAW,EAAEA,WAAW;MACxBC,SAAS,EAAEA;IACb,CAAC,EAAE,aAAalC,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MACzCgB,QAAQ,EAAE,CAAC;MACXjE,GAAG,EAAEsB,gBAAgB;MACrBhB,KAAK,EAAEd,aAAa;MACpB,aAAa,EAAE;IACjB,CAAC,CAAC,EAAE,aAAaN,KAAK,CAAC+D,aAAa,CAAC1D,YAAY,EAAE;MACjD2E,YAAY,EAAE1D,OAAO,IAAIC;IAC3B,CAAC,EAAEK,WAAW,GAAGA,WAAW,CAACyC,OAAO,CAAC,GAAGA,OAAO,CAAC,EAAE,aAAarE,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MACxFgB,QAAQ,EAAE,CAAC;MACXjE,GAAG,EAAEuB,cAAc;MACnBjB,KAAK,EAAEd,aAAa;MACpB,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFK,OAAO,CAACsE,WAAW,GAAG,SAAS;AAC/B,eAAetE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}