{"ast": null, "code": "var baseIsEqualDeep = require('./_baseIsEqualDeep'),\n  isObjectLike = require('./isObjectLike');\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\nmodule.exports = baseIsEqual;", "map": {"version": 3, "names": ["baseIsEqualDeep", "require", "isObjectLike", "baseIsEqual", "value", "other", "bitmask", "customizer", "stack", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/lodash/_baseIsEqual.js"], "sourcesContent": ["var baseIsEqualDeep = require('./_baseIsEqualDeep'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nmodule.exports = baseIsEqual;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAC/CC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC7D,IAAIJ,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAID,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAK,CAACH,YAAY,CAACE,KAAK,CAAC,IAAI,CAACF,YAAY,CAACG,KAAK,CAAE,EAAE;IACpF,OAAOD,KAAK,KAAKA,KAAK,IAAIC,KAAK,KAAKA,KAAK;EAC3C;EACA,OAAOL,eAAe,CAACI,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEJ,WAAW,EAAEK,KAAK,CAAC;AAC/E;AAEAC,MAAM,CAACC,OAAO,GAAGP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}