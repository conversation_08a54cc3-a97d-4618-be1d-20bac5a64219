"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockRequire = exports.type = exports.todo = exports.timeout = exports.throws = exports.test = exports.strictNotSame = exports.skip = exports.same = exports.pragma = exports.plan = exports.pass = exports.only = exports.ok = exports.notSame = exports.notOk = exports.notMatchStrict = exports.notMatchOnlyStrict = exports.notMatchOnly = exports.notMatch = exports.notHasStrict = exports.notHas = exports.not = exports.matchStrict = exports.matchOnlyStrict = exports.matchOnly = exports.match = exports.hasStrict = exports.has = exports.fail = exports.error = exports.end = exports.emits = exports.doesNotThrow = exports.comment = exports.beforeEach = exports.before = exports.bailout = exports.afterEach = exports.after = exports.Test = exports.Worker = exports.TestBase = exports.TapFile = exports.Stdin = exports.Spawn = exports.Minimal = exports.Lists = exports.Counts = exports.Base = void 0;
exports.t = exports.worker = exports.spawn = exports.stdinOnly = exports.stdin = exports.matchSnapshot = exports.fixture = exports.testdir = exports.capture = exports.captureFn = exports.intercept = exports.createMock = exports.mockImport = void 0;
const core_1 = require("@tapjs/core");
var core_2 = require("@tapjs/core");
Object.defineProperty(exports, "Base", { enumerable: true, get: function () { return core_2.Base; } });
Object.defineProperty(exports, "Counts", { enumerable: true, get: function () { return core_2.Counts; } });
Object.defineProperty(exports, "Lists", { enumerable: true, get: function () { return core_2.Lists; } });
Object.defineProperty(exports, "Minimal", { enumerable: true, get: function () { return core_2.Minimal; } });
Object.defineProperty(exports, "Spawn", { enumerable: true, get: function () { return core_2.Spawn; } });
Object.defineProperty(exports, "Stdin", { enumerable: true, get: function () { return core_2.Stdin; } });
Object.defineProperty(exports, "TapFile", { enumerable: true, get: function () { return core_2.TapFile; } });
Object.defineProperty(exports, "TestBase", { enumerable: true, get: function () { return core_2.TestBase; } });
Object.defineProperty(exports, "Worker", { enumerable: true, get: function () { return core_2.Worker; } });
var test_1 = require("@tapjs/test");
Object.defineProperty(exports, "Test", { enumerable: true, get: function () { return test_1.Test; } });
exports.t = (0, core_1.tap)();
// People really like doing `import { test }`
// this makes that work by exporting these methods as named exports.
//
// All methods on a Test object are bound to the appropriate plugin
// as the this-context if called without a this context.
//
// Technically these types aren't accurate if a plugin is disabled, but
// that's sort of what you buy into when changing types dynamically.
//
// Plugins other than the builtins that are added do not have their methods
// exported here, because we can't reasonably know what they are, and named
// exports must be explicitly named.
// Methods provided by the {@link @tapjs/core!test-base.TestBase class,
// always available
const { bailout, comment, end, fail, pass, plan, pragma, skip, stdinOnly, test, timeout, todo, } = exports.t;
exports.bailout = bailout;
exports.comment = comment;
exports.end = end;
exports.fail = fail;
exports.pass = pass;
exports.plan = plan;
exports.pragma = pragma;
exports.skip = skip;
exports.stdinOnly = stdinOnly;
exports.test = test;
exports.timeout = timeout;
exports.todo = todo;
// conditional exports, only available if plugins loaded
// it'll just be undefined if that particular plugin is disabled.
const { only } = exports.t;
exports.only = only;
const { after } = exports.t;
exports.after = after;
const { before } = exports.t;
exports.before = before;
const { afterEach } = exports.t;
exports.afterEach = afterEach;
const { beforeEach } = exports.t;
exports.beforeEach = beforeEach;
const { ok, notOk, not, type, same, notSame, strictNotSame, has, notHas, hasStrict, notHasStrict, match, notMatch, matchOnly, notMatchOnly, matchStrict, notMatchStrict, matchOnlyStrict, notMatchOnlyStrict, throws, doesNotThrow, emits, error, } = exports.t;
exports.ok = ok;
exports.notOk = notOk;
exports.not = not;
exports.type = type;
exports.same = same;
exports.notSame = notSame;
exports.strictNotSame = strictNotSame;
exports.has = has;
exports.notHas = notHas;
exports.hasStrict = hasStrict;
exports.notHasStrict = notHasStrict;
exports.match = match;
exports.notMatch = notMatch;
exports.matchOnly = matchOnly;
exports.notMatchOnly = notMatchOnly;
exports.matchStrict = matchStrict;
exports.notMatchStrict = notMatchStrict;
exports.matchOnlyStrict = matchOnlyStrict;
exports.notMatchOnlyStrict = notMatchOnlyStrict;
exports.throws = throws;
exports.doesNotThrow = doesNotThrow;
exports.emits = emits;
exports.error = error;
const { mockRequire, mockImport, createMock } = exports.t;
exports.mockRequire = mockRequire;
exports.mockImport = mockImport;
exports.createMock = createMock;
const { captureFn, capture, intercept } = exports.t;
exports.captureFn = captureFn;
exports.capture = capture;
exports.intercept = intercept;
const { matchSnapshot } = exports.t;
exports.matchSnapshot = matchSnapshot;
const { spawn } = exports.t;
exports.spawn = spawn;
const { stdin } = exports.t;
exports.stdin = stdin;
const { worker } = exports.t;
exports.worker = worker;
const { testdir, fixture } = exports.t;
exports.testdir = testdir;
exports.fixture = fixture;
//# sourceMappingURL=main.js.map