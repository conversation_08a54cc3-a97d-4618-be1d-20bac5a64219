{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ResizableTextArea from './ResizableTextArea';\nvar TextArea = /*#__PURE__*/function (_React$Component) {\n  _inherits(TextArea, _React$Component);\n  var _super = _createSuper(TextArea);\n  function TextArea(props) {\n    var _this;\n    _classCallCheck(this, TextArea);\n    _this = _super.call(this, props);\n    _this.resizableTextArea = void 0;\n    _this.focus = function () {\n      _this.resizableTextArea.textArea.focus();\n    };\n    _this.saveTextArea = function (resizableTextArea) {\n      _this.resizableTextArea = resizableTextArea;\n    };\n    _this.handleChange = function (e) {\n      var onChange = _this.props.onChange;\n      _this.setValue(e.target.value, function () {\n        _this.resizableTextArea.resizeTextarea();\n      });\n      if (onChange) {\n        onChange(e);\n      }\n    };\n    _this.handleKeyDown = function (e) {\n      var _this$props = _this.props,\n        onPressEnter = _this$props.onPressEnter,\n        onKeyDown = _this$props.onKeyDown;\n      if (e.keyCode === 13 && onPressEnter) {\n        onPressEnter(e);\n      }\n      if (onKeyDown) {\n        onKeyDown(e);\n      }\n    };\n    var value = typeof props.value === 'undefined' || props.value === null ? props.defaultValue : props.value;\n    _this.state = {\n      value: value\n    };\n    return _this;\n  }\n  _createClass(TextArea, [{\n    key: \"setValue\",\n    value: function setValue(value, callback) {\n      if (!('value' in this.props)) {\n        this.setState({\n          value: value\n        }, callback);\n      }\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.resizableTextArea.textArea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, this.props, {\n        value: this.state.value,\n        onKeyDown: this.handleKeyDown,\n        onChange: this.handleChange,\n        ref: this.saveTextArea\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      if ('value' in nextProps) {\n        return {\n          value: nextProps.value\n        };\n      }\n      return null;\n    }\n  }]);\n  return TextArea;\n}(React.Component);\nexport { ResizableTextArea };\nexport default TextArea;", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "ResizableTextArea", "TextArea", "_React$Component", "_super", "props", "_this", "call", "resizableTextArea", "focus", "textArea", "saveTextArea", "handleChange", "e", "onChange", "setValue", "target", "value", "resizeTextarea", "handleKeyDown", "_this$props", "onPressEnter", "onKeyDown", "keyCode", "defaultValue", "state", "key", "callback", "setState", "blur", "render", "createElement", "ref", "getDerivedStateFromProps", "nextProps", "Component"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-textarea/es/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ResizableTextArea from './ResizableTextArea';\n\nvar TextArea = /*#__PURE__*/function (_React$Component) {\n  _inherits(TextArea, _React$Component);\n\n  var _super = _createSuper(TextArea);\n\n  function TextArea(props) {\n    var _this;\n\n    _classCallCheck(this, TextArea);\n\n    _this = _super.call(this, props);\n    _this.resizableTextArea = void 0;\n\n    _this.focus = function () {\n      _this.resizableTextArea.textArea.focus();\n    };\n\n    _this.saveTextArea = function (resizableTextArea) {\n      _this.resizableTextArea = resizableTextArea;\n    };\n\n    _this.handleChange = function (e) {\n      var onChange = _this.props.onChange;\n\n      _this.setValue(e.target.value, function () {\n        _this.resizableTextArea.resizeTextarea();\n      });\n\n      if (onChange) {\n        onChange(e);\n      }\n    };\n\n    _this.handleKeyDown = function (e) {\n      var _this$props = _this.props,\n          onPressEnter = _this$props.onPressEnter,\n          onKeyDown = _this$props.onKeyDown;\n\n      if (e.keyCode === 13 && onPressEnter) {\n        onPressEnter(e);\n      }\n\n      if (onKeyDown) {\n        onKeyDown(e);\n      }\n    };\n\n    var value = typeof props.value === 'undefined' || props.value === null ? props.defaultValue : props.value;\n    _this.state = {\n      value: value\n    };\n    return _this;\n  }\n\n  _createClass(TextArea, [{\n    key: \"setValue\",\n    value: function setValue(value, callback) {\n      if (!('value' in this.props)) {\n        this.setState({\n          value: value\n        }, callback);\n      }\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.resizableTextArea.textArea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, this.props, {\n        value: this.state.value,\n        onKeyDown: this.handleKeyDown,\n        onChange: this.handleChange,\n        ref: this.saveTextArea\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      if ('value' in nextProps) {\n        return {\n          value: nextProps.value\n        };\n      }\n\n      return null;\n    }\n  }]);\n\n  return TextArea;\n}(React.Component);\n\nexport { ResizableTextArea };\nexport default TextArea;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtDL,SAAS,CAACI,QAAQ,EAAEC,gBAAgB,CAAC;EAErC,IAAIC,MAAM,GAAGL,YAAY,CAACG,QAAQ,CAAC;EAEnC,SAASA,QAAQA,CAACG,KAAK,EAAE;IACvB,IAAIC,KAAK;IAETV,eAAe,CAAC,IAAI,EAAEM,QAAQ,CAAC;IAE/BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,iBAAiB,GAAG,KAAK,CAAC;IAEhCF,KAAK,CAACG,KAAK,GAAG,YAAY;MACxBH,KAAK,CAACE,iBAAiB,CAACE,QAAQ,CAACD,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEDH,KAAK,CAACK,YAAY,GAAG,UAAUH,iBAAiB,EAAE;MAChDF,KAAK,CAACE,iBAAiB,GAAGA,iBAAiB;IAC7C,CAAC;IAEDF,KAAK,CAACM,YAAY,GAAG,UAAUC,CAAC,EAAE;MAChC,IAAIC,QAAQ,GAAGR,KAAK,CAACD,KAAK,CAACS,QAAQ;MAEnCR,KAAK,CAACS,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,YAAY;QACzCX,KAAK,CAACE,iBAAiB,CAACU,cAAc,CAAC,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAIJ,QAAQ,EAAE;QACZA,QAAQ,CAACD,CAAC,CAAC;MACb;IACF,CAAC;IAEDP,KAAK,CAACa,aAAa,GAAG,UAAUN,CAAC,EAAE;MACjC,IAAIO,WAAW,GAAGd,KAAK,CAACD,KAAK;QACzBgB,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,SAAS,GAAGF,WAAW,CAACE,SAAS;MAErC,IAAIT,CAAC,CAACU,OAAO,KAAK,EAAE,IAAIF,YAAY,EAAE;QACpCA,YAAY,CAACR,CAAC,CAAC;MACjB;MAEA,IAAIS,SAAS,EAAE;QACbA,SAAS,CAACT,CAAC,CAAC;MACd;IACF,CAAC;IAED,IAAII,KAAK,GAAG,OAAOZ,KAAK,CAACY,KAAK,KAAK,WAAW,IAAIZ,KAAK,CAACY,KAAK,KAAK,IAAI,GAAGZ,KAAK,CAACmB,YAAY,GAAGnB,KAAK,CAACY,KAAK;IACzGX,KAAK,CAACmB,KAAK,GAAG;MACZR,KAAK,EAAEA;IACT,CAAC;IACD,OAAOX,KAAK;EACd;EAEAT,YAAY,CAACK,QAAQ,EAAE,CAAC;IACtBwB,GAAG,EAAE,UAAU;IACfT,KAAK,EAAE,SAASF,QAAQA,CAACE,KAAK,EAAEU,QAAQ,EAAE;MACxC,IAAI,EAAE,OAAO,IAAI,IAAI,CAACtB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACuB,QAAQ,CAAC;UACZX,KAAK,EAAEA;QACT,CAAC,EAAEU,QAAQ,CAAC;MACd;IACF;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,MAAM;IACXT,KAAK,EAAE,SAASY,IAAIA,CAAA,EAAG;MACrB,IAAI,CAACrB,iBAAiB,CAACE,QAAQ,CAACmB,IAAI,CAAC,CAAC;IACxC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbT,KAAK,EAAE,SAASa,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAAC9B,iBAAiB,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACU,KAAK,EAAE;QAClFY,KAAK,EAAE,IAAI,CAACQ,KAAK,CAACR,KAAK;QACvBK,SAAS,EAAE,IAAI,CAACH,aAAa;QAC7BL,QAAQ,EAAE,IAAI,CAACF,YAAY;QAC3BoB,GAAG,EAAE,IAAI,CAACrB;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,EAAE,CAAC;IACHe,GAAG,EAAE,0BAA0B;IAC/BT,KAAK,EAAE,SAASgB,wBAAwBA,CAACC,SAAS,EAAE;MAClD,IAAI,OAAO,IAAIA,SAAS,EAAE;QACxB,OAAO;UACLjB,KAAK,EAAEiB,SAAS,CAACjB;QACnB,CAAC;MACH;MAEA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EAEH,OAAOf,QAAQ;AACjB,CAAC,CAACF,KAAK,CAACmC,SAAS,CAAC;AAElB,SAASlC,iBAAiB;AAC1B,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}