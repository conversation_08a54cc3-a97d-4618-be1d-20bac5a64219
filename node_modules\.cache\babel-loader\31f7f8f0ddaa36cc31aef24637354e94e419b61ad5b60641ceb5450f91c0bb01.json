{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useRaf, { useRafState } from '../hooks/useRaf';\nimport TabNode from './TabNode';\nimport useOffsets from '../hooks/useOffsets';\nimport useVisibleRange from '../hooks/useVisibleRange';\nimport OperationNode from './OperationNode';\nimport TabContext from '../TabContext';\nimport useTouchMove from '../hooks/useTouchMove';\nimport useRefs from '../hooks/useRefs';\nimport AddButton from './AddButton';\nimport useSyncState from '../hooks/useSyncState';\nvar ExtraContent = function ExtraContent(_ref) {\n  var position = _ref.position,\n    prefixCls = _ref.prefixCls,\n    extra = _ref.extra;\n  if (!extra) return null;\n  var content; // Parse extra\n\n  var assertExtra = {};\n  if (extra && _typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\")\n  }, content) : null;\n};\nfunction TabNavList(props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll;\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n  var _useRefs = useRefs(),\n    _useRefs2 = _slicedToArray(_useRefs, 2),\n    getBtnRef = _useRefs2[0],\n    removeBtnRef = _useRefs2[1];\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    wrapperScrollWidth = _useState2[0],\n    setWrapperScrollWidth = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    wrapperScrollHeight = _useState4[0],\n    setWrapperScrollHeight = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    wrapperWidth = _useState6[0],\n    setWrapperWidth = _useState6[1];\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    wrapperHeight = _useState8[0],\n    setWrapperHeight = _useState8[1];\n  var _useState9 = useState(0),\n    _useState10 = _slicedToArray(_useState9, 2),\n    addWidth = _useState10[0],\n    setAddWidth = _useState10[1];\n  var _useState11 = useState(0),\n    _useState12 = _slicedToArray(_useState11, 2),\n    addHeight = _useState12[0],\n    setAddHeight = _useState12[1];\n  var _useRafState = useRafState(new Map()),\n    _useRafState2 = _slicedToArray(_useRafState, 2),\n    tabSizes = _useRafState2[0],\n    setTabSizes = _useRafState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, wrapperScrollWidth); // ========================== Util =========================\n\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, wrapperHeight - wrapperScrollHeight);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, wrapperScrollWidth - wrapperWidth);\n  } else {\n    transformMin = Math.min(0, wrapperWidth - wrapperScrollWidth);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  } // ========================= Mobile ========================\n\n  var touchMovingRef = useRef();\n  var _useState13 = useState(),\n    _useState14 = _slicedToArray(_useState13, 2),\n    lockAnimation = _useState14[0],\n    setLockAnimation = _useState14[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n    if (tabPositionTopOrBottom) {\n      // Skip scroll if place is enough\n      if (wrapperWidth >= wrapperScrollWidth) {\n        return false;\n      }\n      doMove(setTransformLeft, offsetX);\n    } else {\n      if (wrapperHeight >= wrapperScrollHeight) {\n        return false;\n      }\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]); // ========================= Scroll ========================\n\n  function scrollToTab() {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft; // RTL\n\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + wrapperWidth) {\n          newTransform = tabOffset.right + tabOffset.width - wrapperWidth;\n        }\n      } // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + wrapperWidth) {\n        newTransform = -(tabOffset.left + tabOffset.width - wrapperWidth);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + wrapperHeight) {\n        _newTransform = -(tabOffset.top + tabOffset.height - wrapperHeight);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  } // ========================== Tab ==========================\n  // Render tab node & collect tab offset\n\n  var _useVisibleRange = useVisibleRange(tabOffsets, {\n      width: wrapperWidth,\n      height: wrapperHeight,\n      left: transformLeft,\n      top: transformTop\n    }, {\n      width: wrapperScrollWidth,\n      height: wrapperScrollHeight\n    }, {\n      width: addWidth,\n      height: addHeight\n    }, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n  var tabNodeStyle = {};\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      ref: getBtnRef(key),\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onRemove: function onRemove() {\n        removeBtnRef(key);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        } // Focus element will make scrollLeft change which we should reset back\n\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n  var onListHolderResize = useRaf(function () {\n    var _tabsWrapperRef$curre, _tabsWrapperRef$curre2, _innerAddButtonRef$cu, _innerAddButtonRef$cu2, _tabListRef$current, _tabListRef$current2;\n\n    // Update wrapper records\n    var offsetWidth = ((_tabsWrapperRef$curre = tabsWrapperRef.current) === null || _tabsWrapperRef$curre === void 0 ? void 0 : _tabsWrapperRef$curre.offsetWidth) || 0;\n    var offsetHeight = ((_tabsWrapperRef$curre2 = tabsWrapperRef.current) === null || _tabsWrapperRef$curre2 === void 0 ? void 0 : _tabsWrapperRef$curre2.offsetHeight) || 0;\n    var newAddWidth = ((_innerAddButtonRef$cu = innerAddButtonRef.current) === null || _innerAddButtonRef$cu === void 0 ? void 0 : _innerAddButtonRef$cu.offsetWidth) || 0;\n    var newAddHeight = ((_innerAddButtonRef$cu2 = innerAddButtonRef.current) === null || _innerAddButtonRef$cu2 === void 0 ? void 0 : _innerAddButtonRef$cu2.offsetHeight) || 0;\n    setWrapperWidth(offsetWidth);\n    setWrapperHeight(offsetHeight);\n    setAddWidth(newAddWidth);\n    setAddHeight(newAddHeight);\n    var newWrapperScrollWidth = (((_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.offsetWidth) || 0) - newAddWidth;\n    var newWrapperScrollHeight = (((_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.offsetHeight) || 0) - newAddHeight;\n    setWrapperScrollWidth(newWrapperScrollWidth);\n    setWrapperScrollHeight(newWrapperScrollHeight); // Update buttons records\n\n    setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var key = _ref2.key;\n        var btnNode = getBtnRef(key).current;\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  }); // ======================== Dropdown =======================\n\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs)); // =================== Link & Operations ===================\n\n  var _useState15 = useState(),\n    _useState16 = _slicedToArray(_useState15, 2),\n    inkStyle = _useState16[0],\n    setInkStyle = _useState16[1];\n  var activeTabOffset = tabOffsets.get(activeKey); // Delay set ink style to avoid remove tab blink\n\n  var inkBarRafRef = useRef();\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]); // ========================= Effect ========================\n\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, activeTabOffset, tabOffsets, tabPositionTopOrBottom]); // Should recalculate when rtl changed\n\n  useEffect(function () {\n    onListHolderResize();\n  }, [rtl, tabBarGutter, activeKey, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]); // ========================= Render ========================\n\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft + wrapperWidth < wrapperScrollWidth;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = -transformLeft + wrapperWidth < wrapperScrollWidth;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = -transformTop + wrapperHeight < wrapperScrollHeight;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  }));\n  /* eslint-enable */\n}\nexport default /*#__PURE__*/React.forwardRef(TabNavList);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "_objectSpread", "_slicedToArray", "_typeof", "React", "useState", "useRef", "useEffect", "classNames", "raf", "ResizeObserver", "useRaf", "useRafState", "TabNode", "useOffsets", "useVisibleRange", "OperationNode", "TabContext", "useTouchMove", "useRefs", "AddButton", "useSyncState", "ExtraContent", "_ref", "position", "prefixCls", "extra", "content", "assertExtra", "isValidElement", "right", "left", "createElement", "className", "concat", "TabNavList", "props", "ref", "_classNames", "_React$useContext", "useContext", "tabs", "style", "id", "animated", "active<PERSON><PERSON>", "rtl", "editable", "locale", "tabPosition", "tabBarGutter", "children", "onTabClick", "onTabScroll", "tabsWrapperRef", "tabListRef", "operationsRef", "innerAddButtonRef", "_useRefs", "_useRefs2", "getBtnRef", "removeBtnRef", "tabPositionTopOrBottom", "_useSyncState", "next", "prev", "direction", "_useSyncState2", "transformLeft", "setTransformLeft", "_useSyncState3", "_useSyncState4", "transformTop", "setTransformTop", "_useState", "_useState2", "wrapperScrollWidth", "setWrapperScrollWidth", "_useState3", "_useState4", "wrapperScrollHeight", "setWrapperScrollHeight", "_useState5", "_useState6", "wrapperWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_useState7", "_useState8", "wrapperHeight", "setWrapperHeight", "_useState9", "_useState10", "addWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_useState11", "_useState12", "addHeight", "setAddHeight", "_useRafState", "Map", "_useRafState2", "tabSizes", "setTabSizes", "tabOffsets", "operationsHiddenClassName", "transformMin", "transformMax", "Math", "min", "max", "alignInRange", "value", "touchMovingRef", "_useState13", "_useState14", "lockAnimation", "setLockAnimation", "doLockAnimation", "Date", "now", "clearTouchMoving", "window", "clearTimeout", "current", "offsetX", "offsetY", "do<PERSON>ove", "setState", "offset", "newValue", "setTimeout", "scrollToTab", "key", "arguments", "length", "undefined", "tabOffset", "get", "width", "height", "top", "newTransform", "_newTransform", "_useVisibleRange", "_useVisibleRange2", "visibleStart", "visibleEnd", "tabNodeStyle", "marginTop", "tabNodes", "map", "tab", "i", "closable", "active", "renderWrapper", "removeAriaLabel", "onClick", "e", "onRemove", "onFocus", "scrollLeft", "scrollTop", "onListHolderResize", "_tabsWrapperRef$curre", "_tabsWrapperRef$curre2", "_innerAddButtonRef$cu", "_innerAddButtonRef$cu2", "_tabListRef$current", "_tabListRef$current2", "offsetWidth", "offsetHeight", "newAddWidth", "newAddHeight", "newWrapperScrollWidth", "newWrapperScrollHeight", "newSizes", "for<PERSON>ach", "_ref2", "btnNode", "set", "offsetLeft", "offsetTop", "startHiddenTabs", "slice", "endHiddenTabs", "hiddenTabs", "_useState15", "_useState16", "inkStyle", "setInkStyle", "activeTabOffset", "inkBarRafRef", "cleanInkBarRaf", "cancel", "newInkStyle", "join", "hasDropdown", "wrapPrefix", "pingLeft", "pingRight", "pingTop", "pingBottom", "role", "onKeyDown", "onResize", "transform", "transition", "visibility", "inkBar", "tabMoving", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/TabNavList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useRaf, { useRafState } from '../hooks/useRaf';\nimport TabNode from './TabNode';\nimport useOffsets from '../hooks/useOffsets';\nimport useVisibleRange from '../hooks/useVisibleRange';\nimport OperationNode from './OperationNode';\nimport TabContext from '../TabContext';\nimport useTouchMove from '../hooks/useTouchMove';\nimport useRefs from '../hooks/useRefs';\nimport AddButton from './AddButton';\nimport useSyncState from '../hooks/useSyncState';\n\nvar ExtraContent = function ExtraContent(_ref) {\n  var position = _ref.position,\n      prefixCls = _ref.prefixCls,\n      extra = _ref.extra;\n  if (!extra) return null;\n  var content; // Parse extra\n\n  var assertExtra = {};\n\n  if (extra && _typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\")\n  }, content) : null;\n};\n\nfunction TabNavList(props, ref) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(TabContext),\n      prefixCls = _React$useContext.prefixCls,\n      tabs = _React$useContext.tabs;\n\n  var className = props.className,\n      style = props.style,\n      id = props.id,\n      animated = props.animated,\n      activeKey = props.activeKey,\n      rtl = props.rtl,\n      extra = props.extra,\n      editable = props.editable,\n      locale = props.locale,\n      tabPosition = props.tabPosition,\n      tabBarGutter = props.tabBarGutter,\n      children = props.children,\n      onTabClick = props.onTabClick,\n      onTabScroll = props.onTabScroll;\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n\n  var _useRefs = useRefs(),\n      _useRefs2 = _slicedToArray(_useRefs, 2),\n      getBtnRef = _useRefs2[0],\n      removeBtnRef = _useRefs2[1];\n\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n\n  var _useSyncState = useSyncState(0, function (next, prev) {\n    if (tabPositionTopOrBottom && onTabScroll) {\n      onTabScroll({\n        direction: next > prev ? 'left' : 'right'\n      });\n    }\n  }),\n      _useSyncState2 = _slicedToArray(_useSyncState, 2),\n      transformLeft = _useSyncState2[0],\n      setTransformLeft = _useSyncState2[1];\n\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n    if (!tabPositionTopOrBottom && onTabScroll) {\n      onTabScroll({\n        direction: next > prev ? 'top' : 'bottom'\n      });\n    }\n  }),\n      _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n      transformTop = _useSyncState4[0],\n      setTransformTop = _useSyncState4[1];\n\n  var _useState = useState(0),\n      _useState2 = _slicedToArray(_useState, 2),\n      wrapperScrollWidth = _useState2[0],\n      setWrapperScrollWidth = _useState2[1];\n\n  var _useState3 = useState(0),\n      _useState4 = _slicedToArray(_useState3, 2),\n      wrapperScrollHeight = _useState4[0],\n      setWrapperScrollHeight = _useState4[1];\n\n  var _useState5 = useState(null),\n      _useState6 = _slicedToArray(_useState5, 2),\n      wrapperWidth = _useState6[0],\n      setWrapperWidth = _useState6[1];\n\n  var _useState7 = useState(null),\n      _useState8 = _slicedToArray(_useState7, 2),\n      wrapperHeight = _useState8[0],\n      setWrapperHeight = _useState8[1];\n\n  var _useState9 = useState(0),\n      _useState10 = _slicedToArray(_useState9, 2),\n      addWidth = _useState10[0],\n      setAddWidth = _useState10[1];\n\n  var _useState11 = useState(0),\n      _useState12 = _slicedToArray(_useState11, 2),\n      addHeight = _useState12[0],\n      setAddHeight = _useState12[1];\n\n  var _useRafState = useRafState(new Map()),\n      _useRafState2 = _slicedToArray(_useRafState, 2),\n      tabSizes = _useRafState2[0],\n      setTabSizes = _useRafState2[1];\n\n  var tabOffsets = useOffsets(tabs, tabSizes, wrapperScrollWidth); // ========================== Util =========================\n\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, wrapperHeight - wrapperScrollHeight);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, wrapperScrollWidth - wrapperWidth);\n  } else {\n    transformMin = Math.min(0, wrapperWidth - wrapperScrollWidth);\n    transformMax = 0;\n  }\n\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n\n    if (value > transformMax) {\n      return transformMax;\n    }\n\n    return value;\n  } // ========================= Mobile ========================\n\n\n  var touchMovingRef = useRef();\n\n  var _useState13 = useState(),\n      _useState14 = _slicedToArray(_useState13, 2),\n      lockAnimation = _useState14[0],\n      setLockAnimation = _useState14[1];\n\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    if (tabPositionTopOrBottom) {\n      // Skip scroll if place is enough\n      if (wrapperWidth >= wrapperScrollWidth) {\n        return false;\n      }\n\n      doMove(setTransformLeft, offsetX);\n    } else {\n      if (wrapperHeight >= wrapperScrollHeight) {\n        return false;\n      }\n\n      doMove(setTransformTop, offsetY);\n    }\n\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n\n    return clearTouchMoving;\n  }, [lockAnimation]); // ========================= Scroll ========================\n\n  function scrollToTab() {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft; // RTL\n\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + wrapperWidth) {\n          newTransform = tabOffset.right + tabOffset.width - wrapperWidth;\n        }\n      } // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + wrapperWidth) {\n        newTransform = -(tabOffset.left + tabOffset.width - wrapperWidth);\n      }\n\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + wrapperHeight) {\n        _newTransform = -(tabOffset.top + tabOffset.height - wrapperHeight);\n      }\n\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  } // ========================== Tab ==========================\n  // Render tab node & collect tab offset\n\n\n  var _useVisibleRange = useVisibleRange(tabOffsets, {\n    width: wrapperWidth,\n    height: wrapperHeight,\n    left: transformLeft,\n    top: transformTop\n  }, {\n    width: wrapperScrollWidth,\n    height: wrapperScrollHeight\n  }, {\n    width: addWidth,\n    height: addHeight\n  }, _objectSpread(_objectSpread({}, props), {}, {\n    tabs: tabs\n  })),\n      _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n      visibleStart = _useVisibleRange2[0],\n      visibleEnd = _useVisibleRange2[1];\n\n  var tabNodeStyle = {};\n\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */\n      ,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      ref: getBtnRef(key),\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onRemove: function onRemove() {\n        removeBtnRef(key);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n\n        if (!tabsWrapperRef.current) {\n          return;\n        } // Focus element will make scrollLeft change which we should reset back\n\n\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n  var onListHolderResize = useRaf(function () {\n    var _tabsWrapperRef$curre, _tabsWrapperRef$curre2, _innerAddButtonRef$cu, _innerAddButtonRef$cu2, _tabListRef$current, _tabListRef$current2;\n\n    // Update wrapper records\n    var offsetWidth = ((_tabsWrapperRef$curre = tabsWrapperRef.current) === null || _tabsWrapperRef$curre === void 0 ? void 0 : _tabsWrapperRef$curre.offsetWidth) || 0;\n    var offsetHeight = ((_tabsWrapperRef$curre2 = tabsWrapperRef.current) === null || _tabsWrapperRef$curre2 === void 0 ? void 0 : _tabsWrapperRef$curre2.offsetHeight) || 0;\n    var newAddWidth = ((_innerAddButtonRef$cu = innerAddButtonRef.current) === null || _innerAddButtonRef$cu === void 0 ? void 0 : _innerAddButtonRef$cu.offsetWidth) || 0;\n    var newAddHeight = ((_innerAddButtonRef$cu2 = innerAddButtonRef.current) === null || _innerAddButtonRef$cu2 === void 0 ? void 0 : _innerAddButtonRef$cu2.offsetHeight) || 0;\n    setWrapperWidth(offsetWidth);\n    setWrapperHeight(offsetHeight);\n    setAddWidth(newAddWidth);\n    setAddHeight(newAddHeight);\n    var newWrapperScrollWidth = (((_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.offsetWidth) || 0) - newAddWidth;\n    var newWrapperScrollHeight = (((_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.offsetHeight) || 0) - newAddHeight;\n    setWrapperScrollWidth(newWrapperScrollWidth);\n    setWrapperScrollHeight(newWrapperScrollHeight); // Update buttons records\n\n    setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var key = _ref2.key;\n        var btnNode = getBtnRef(key).current;\n\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  }); // ======================== Dropdown =======================\n\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs)); // =================== Link & Operations ===================\n\n  var _useState15 = useState(),\n      _useState16 = _slicedToArray(_useState15, 2),\n      inkStyle = _useState16[0],\n      setInkStyle = _useState16[1];\n\n  var activeTabOffset = tabOffsets.get(activeKey); // Delay set ink style to avoid remove tab blink\n\n  var inkBarRafRef = useRef();\n\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n\n  useEffect(function () {\n    var newInkStyle = {};\n\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]); // ========================= Effect ========================\n\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, activeTabOffset, tabOffsets, tabPositionTopOrBottom]); // Should recalculate when rtl changed\n\n  useEffect(function () {\n    onListHolderResize();\n  }, [rtl, tabBarGutter, activeKey, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]); // ========================= Render ========================\n\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft + wrapperWidth < wrapperScrollWidth;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = -transformLeft + wrapperWidth < wrapperScrollWidth;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = -transformTop + wrapperHeight < wrapperScrollHeight;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  }));\n  /* eslint-enable */\n}\n\nexport default /*#__PURE__*/React.forwardRef(TabNavList);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,MAAM,IAAIC,WAAW,QAAQ,iBAAiB;AACrD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,uBAAuB;AAEhD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,KAAK,GAAGH,IAAI,CAACG,KAAK;EACtB,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,IAAIC,OAAO,CAAC,CAAC;;EAEb,IAAIC,WAAW,GAAG,CAAC,CAAC;EAEpB,IAAIF,KAAK,IAAIvB,OAAO,CAACuB,KAAK,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAatB,KAAK,CAACyB,cAAc,CAACH,KAAK,CAAC,EAAE;IACtFE,WAAW,GAAGF,KAAK;EACrB,CAAC,MAAM;IACLE,WAAW,CAACE,KAAK,GAAGJ,KAAK;EAC3B;EAEA,IAAIF,QAAQ,KAAK,OAAO,EAAE;IACxBG,OAAO,GAAGC,WAAW,CAACE,KAAK;EAC7B;EAEA,IAAIN,QAAQ,KAAK,MAAM,EAAE;IACvBG,OAAO,GAAGC,WAAW,CAACG,IAAI;EAC5B;EAEA,OAAOJ,OAAO,GAAG,aAAavB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAEE,OAAO,CAAC,GAAG,IAAI;AACpB,CAAC;AAED,SAASQ,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGnC,KAAK,CAACoC,UAAU,CAACvB,UAAU,CAAC;IAChDQ,SAAS,GAAGc,iBAAiB,CAACd,SAAS;IACvCgB,IAAI,GAAGF,iBAAiB,CAACE,IAAI;EAEjC,IAAIR,SAAS,GAAGG,KAAK,CAACH,SAAS;IAC3BS,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,EAAE,GAAGP,KAAK,CAACO,EAAE;IACbC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,GAAG,GAAGV,KAAK,CAACU,GAAG;IACfpB,KAAK,GAAGU,KAAK,CAACV,KAAK;IACnBqB,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,MAAM,GAAGZ,KAAK,CAACY,MAAM;IACrBC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,WAAW,GAAGjB,KAAK,CAACiB,WAAW;EACnC,IAAIC,cAAc,GAAGhD,MAAM,CAAC,CAAC;EAC7B,IAAIiD,UAAU,GAAGjD,MAAM,CAAC,CAAC;EACzB,IAAIkD,aAAa,GAAGlD,MAAM,CAAC,CAAC;EAC5B,IAAImD,iBAAiB,GAAGnD,MAAM,CAAC,CAAC;EAEhC,IAAIoD,QAAQ,GAAGvC,OAAO,CAAC,CAAC;IACpBwC,SAAS,GAAGzD,cAAc,CAACwD,QAAQ,EAAE,CAAC,CAAC;IACvCE,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC;IACxBE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE/B,IAAIG,sBAAsB,GAAGb,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ;EAE9E,IAAIc,aAAa,GAAG1C,YAAY,CAAC,CAAC,EAAE,UAAU2C,IAAI,EAAEC,IAAI,EAAE;MACxD,IAAIH,sBAAsB,IAAIT,WAAW,EAAE;QACzCA,WAAW,CAAC;UACVa,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,MAAM,GAAG;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACEE,cAAc,GAAGjE,cAAc,CAAC6D,aAAa,EAAE,CAAC,CAAC;IACjDK,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;IACjCE,gBAAgB,GAAGF,cAAc,CAAC,CAAC,CAAC;EAExC,IAAIG,cAAc,GAAGjD,YAAY,CAAC,CAAC,EAAE,UAAU2C,IAAI,EAAEC,IAAI,EAAE;MACzD,IAAI,CAACH,sBAAsB,IAAIT,WAAW,EAAE;QAC1CA,WAAW,CAAC;UACVa,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,KAAK,GAAG;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACEM,cAAc,GAAGrE,cAAc,CAACoE,cAAc,EAAE,CAAC,CAAC;IAClDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;IAChCE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EAEvC,IAAIG,SAAS,GAAGrE,QAAQ,CAAC,CAAC,CAAC;IACvBsE,UAAU,GAAGzE,cAAc,CAACwE,SAAS,EAAE,CAAC,CAAC;IACzCE,kBAAkB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAClCE,qBAAqB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEzC,IAAIG,UAAU,GAAGzE,QAAQ,CAAC,CAAC,CAAC;IACxB0E,UAAU,GAAG7E,cAAc,CAAC4E,UAAU,EAAE,CAAC,CAAC;IAC1CE,mBAAmB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACnCE,sBAAsB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE1C,IAAIG,UAAU,GAAG7E,QAAQ,CAAC,IAAI,CAAC;IAC3B8E,UAAU,GAAGjF,cAAc,CAACgF,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAGjF,QAAQ,CAAC,IAAI,CAAC;IAC3BkF,UAAU,GAAGrF,cAAc,CAACoF,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIG,UAAU,GAAGrF,QAAQ,CAAC,CAAC,CAAC;IACxBsF,WAAW,GAAGzF,cAAc,CAACwF,UAAU,EAAE,CAAC,CAAC;IAC3CE,QAAQ,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAIG,WAAW,GAAGzF,QAAQ,CAAC,CAAC,CAAC;IACzB0F,WAAW,GAAG7F,cAAc,CAAC4F,WAAW,EAAE,CAAC,CAAC;IAC5CE,SAAS,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC1BE,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEjC,IAAIG,YAAY,GAAGtF,WAAW,CAAC,IAAIuF,GAAG,CAAC,CAAC,CAAC;IACrCC,aAAa,GAAGlG,cAAc,CAACgG,YAAY,EAAE,CAAC,CAAC;IAC/CG,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;IAC3BE,WAAW,GAAGF,aAAa,CAAC,CAAC,CAAC;EAElC,IAAIG,UAAU,GAAGzF,UAAU,CAAC2B,IAAI,EAAE4D,QAAQ,EAAEzB,kBAAkB,CAAC,CAAC,CAAC;;EAEjE,IAAI4B,yBAAyB,GAAG,EAAE,CAACtE,MAAM,CAACT,SAAS,EAAE,wBAAwB,CAAC;EAC9E,IAAIgF,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EAEpB,IAAI,CAAC5C,sBAAsB,EAAE;IAC3B2C,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,aAAa,GAAGR,mBAAmB,CAAC;IAC/D0B,YAAY,GAAG,CAAC;EAClB,CAAC,MAAM,IAAI5D,GAAG,EAAE;IACd2D,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEjC,kBAAkB,GAAGQ,YAAY,CAAC;EAC/D,CAAC,MAAM;IACLqB,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExB,YAAY,GAAGR,kBAAkB,CAAC;IAC7D8B,YAAY,GAAG,CAAC;EAClB;EAEA,SAASI,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,GAAGN,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IAEA,IAAIM,KAAK,GAAGL,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IAEA,OAAOK,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIC,cAAc,GAAG1G,MAAM,CAAC,CAAC;EAE7B,IAAI2G,WAAW,GAAG5G,QAAQ,CAAC,CAAC;IACxB6G,WAAW,GAAGhH,cAAc,CAAC+G,WAAW,EAAE,CAAC,CAAC;IAC5CE,aAAa,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC9BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC;EAErC,SAASG,eAAeA,CAAA,EAAG;IACzBD,gBAAgB,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC9B;EAEA,SAASC,gBAAgBA,CAAA,EAAG;IAC1BC,MAAM,CAACC,YAAY,CAACV,cAAc,CAACW,OAAO,CAAC;EAC7C;EAEAzG,YAAY,CAACoC,cAAc,EAAE,UAAUsE,OAAO,EAAEC,OAAO,EAAE;IACvD,SAASC,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MAChCD,QAAQ,CAAC,UAAUhB,KAAK,EAAE;QACxB,IAAIkB,QAAQ,GAAGnB,YAAY,CAACC,KAAK,GAAGiB,MAAM,CAAC;QAC3C,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ;IAEA,IAAInE,sBAAsB,EAAE;MAC1B;MACA,IAAIsB,YAAY,IAAIR,kBAAkB,EAAE;QACtC,OAAO,KAAK;MACd;MAEAkD,MAAM,CAACzD,gBAAgB,EAAEuD,OAAO,CAAC;IACnC,CAAC,MAAM;MACL,IAAIpC,aAAa,IAAIR,mBAAmB,EAAE;QACxC,OAAO,KAAK;MACd;MAEA8C,MAAM,CAACrD,eAAe,EAAEoD,OAAO,CAAC;IAClC;IAEAL,gBAAgB,CAAC,CAAC;IAClBH,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACb,CAAC,CAAC;EACF9G,SAAS,CAAC,YAAY;IACpBiH,gBAAgB,CAAC,CAAC;IAElB,IAAIL,aAAa,EAAE;MACjBH,cAAc,CAACW,OAAO,GAAGF,MAAM,CAACS,UAAU,CAAC,YAAY;QACrDd,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,OAAOI,gBAAgB;EACzB,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB,SAASgB,WAAWA,CAAA,EAAG;IACrB,IAAIC,GAAG,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGxF,SAAS;IACvF,IAAI2F,SAAS,GAAGjC,UAAU,CAACkC,GAAG,CAACL,GAAG,CAAC,IAAI;MACrCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACT5G,IAAI,EAAE,CAAC;MACPD,KAAK,EAAE,CAAC;MACR8G,GAAG,EAAE;IACP,CAAC;IAED,IAAI9E,sBAAsB,EAAE;MAC1B;MACA,IAAI+E,YAAY,GAAGzE,aAAa,CAAC,CAAC;;MAElC,IAAItB,GAAG,EAAE;QACP,IAAI0F,SAAS,CAAC1G,KAAK,GAAGsC,aAAa,EAAE;UACnCyE,YAAY,GAAGL,SAAS,CAAC1G,KAAK;QAChC,CAAC,MAAM,IAAI0G,SAAS,CAAC1G,KAAK,GAAG0G,SAAS,CAACE,KAAK,GAAGtE,aAAa,GAAGgB,YAAY,EAAE;UAC3EyD,YAAY,GAAGL,SAAS,CAAC1G,KAAK,GAAG0G,SAAS,CAACE,KAAK,GAAGtD,YAAY;QACjE;MACF,CAAC,CAAC;MAAA,KACG,IAAIoD,SAAS,CAACzG,IAAI,GAAG,CAACqC,aAAa,EAAE;QACxCyE,YAAY,GAAG,CAACL,SAAS,CAACzG,IAAI;MAChC,CAAC,MAAM,IAAIyG,SAAS,CAACzG,IAAI,GAAGyG,SAAS,CAACE,KAAK,GAAG,CAACtE,aAAa,GAAGgB,YAAY,EAAE;QAC3EyD,YAAY,GAAG,EAAEL,SAAS,CAACzG,IAAI,GAAGyG,SAAS,CAACE,KAAK,GAAGtD,YAAY,CAAC;MACnE;MAEAX,eAAe,CAAC,CAAC,CAAC;MAClBJ,gBAAgB,CAACyC,YAAY,CAAC+B,YAAY,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACA,IAAIC,aAAa,GAAGtE,YAAY;MAEhC,IAAIgE,SAAS,CAACI,GAAG,GAAG,CAACpE,YAAY,EAAE;QACjCsE,aAAa,GAAG,CAACN,SAAS,CAACI,GAAG;MAChC,CAAC,MAAM,IAAIJ,SAAS,CAACI,GAAG,GAAGJ,SAAS,CAACG,MAAM,GAAG,CAACnE,YAAY,GAAGgB,aAAa,EAAE;QAC3EsD,aAAa,GAAG,EAAEN,SAAS,CAACI,GAAG,GAAGJ,SAAS,CAACG,MAAM,GAAGnD,aAAa,CAAC;MACrE;MAEAnB,gBAAgB,CAAC,CAAC,CAAC;MACnBI,eAAe,CAACqC,YAAY,CAACgC,aAAa,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;EACF;;EAGA,IAAIC,gBAAgB,GAAGhI,eAAe,CAACwF,UAAU,EAAE;MACjDmC,KAAK,EAAEtD,YAAY;MACnBuD,MAAM,EAAEnD,aAAa;MACrBzD,IAAI,EAAEqC,aAAa;MACnBwE,GAAG,EAAEpE;IACP,CAAC,EAAE;MACDkE,KAAK,EAAE9D,kBAAkB;MACzB+D,MAAM,EAAE3D;IACV,CAAC,EAAE;MACD0D,KAAK,EAAE9C,QAAQ;MACf+C,MAAM,EAAE3C;IACV,CAAC,EAAE/F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7CK,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;IACCuG,iBAAiB,GAAG9I,cAAc,CAAC6I,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAErC,IAAIG,YAAY,GAAG,CAAC,CAAC;EAErB,IAAIlG,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,EAAE;IACrDkG,YAAY,CAACrG,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,GAAGI,YAAY;EACjE,CAAC,MAAM;IACLiG,YAAY,CAACC,SAAS,GAAGlG,YAAY;EACvC;EAEA,IAAImG,QAAQ,GAAG5G,IAAI,CAAC6G,GAAG,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;IACxC,IAAIpB,GAAG,GAAGmB,GAAG,CAACnB,GAAG;IACjB,OAAO,aAAahI,KAAK,CAAC4B,aAAa,CAACnB,OAAO,EAAE;MAC/C8B,EAAE,EAAEA,EAAE;MACNlB,SAAS,EAAEA,SAAS;MACpB2G,GAAG,EAAEA,GAAG;MACRmB,GAAG,EAAEA;MACL;;MAEA7G,KAAK,EAAE8G,CAAC,KAAK,CAAC,GAAGjB,SAAS,GAAGY,YAAY;MACzCM,QAAQ,EAAEF,GAAG,CAACE,QAAQ;MACtB1G,QAAQ,EAAEA,QAAQ;MAClB2G,MAAM,EAAEtB,GAAG,KAAKvF,SAAS;MACzB8G,aAAa,EAAExG,QAAQ;MACvByG,eAAe,EAAE5G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4G,eAAe;MACvFvH,GAAG,EAAEuB,SAAS,CAACwE,GAAG,CAAC;MACnByB,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3B1G,UAAU,CAACgF,GAAG,EAAE0B,CAAC,CAAC;MACpB,CAAC;MACDC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5BlG,YAAY,CAACuE,GAAG,CAAC;MACnB,CAAC;MACD4B,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B7B,WAAW,CAACC,GAAG,CAAC;QAChBf,eAAe,CAAC,CAAC;QAEjB,IAAI,CAAC/D,cAAc,CAACqE,OAAO,EAAE;UAC3B;QACF,CAAC,CAAC;;QAGF,IAAI,CAAC7E,GAAG,EAAE;UACRQ,cAAc,CAACqE,OAAO,CAACsC,UAAU,GAAG,CAAC;QACvC;QAEA3G,cAAc,CAACqE,OAAO,CAACuC,SAAS,GAAG,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIC,kBAAkB,GAAGxJ,MAAM,CAAC,YAAY;IAC1C,IAAIyJ,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,oBAAoB;;IAE3I;IACA,IAAIC,WAAW,GAAG,CAAC,CAACN,qBAAqB,GAAG9G,cAAc,CAACqE,OAAO,MAAM,IAAI,IAAIyC,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACM,WAAW,KAAK,CAAC;IACnK,IAAIC,YAAY,GAAG,CAAC,CAACN,sBAAsB,GAAG/G,cAAc,CAACqE,OAAO,MAAM,IAAI,IAAI0C,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACM,YAAY,KAAK,CAAC;IACxK,IAAIC,WAAW,GAAG,CAAC,CAACN,qBAAqB,GAAG7G,iBAAiB,CAACkE,OAAO,MAAM,IAAI,IAAI2C,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,WAAW,KAAK,CAAC;IACtK,IAAIG,YAAY,GAAG,CAAC,CAACN,sBAAsB,GAAG9G,iBAAiB,CAACkE,OAAO,MAAM,IAAI,IAAI4C,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACI,YAAY,KAAK,CAAC;IAC3KtF,eAAe,CAACqF,WAAW,CAAC;IAC5BjF,gBAAgB,CAACkF,YAAY,CAAC;IAC9B9E,WAAW,CAAC+E,WAAW,CAAC;IACxB3E,YAAY,CAAC4E,YAAY,CAAC;IAC1B,IAAIC,qBAAqB,GAAG,CAAC,CAAC,CAACN,mBAAmB,GAAGjH,UAAU,CAACoE,OAAO,MAAM,IAAI,IAAI6C,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACE,WAAW,KAAK,CAAC,IAAIE,WAAW;IACnL,IAAIG,sBAAsB,GAAG,CAAC,CAAC,CAACN,oBAAoB,GAAGlH,UAAU,CAACoE,OAAO,MAAM,IAAI,IAAI8C,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACE,YAAY,KAAK,CAAC,IAAIE,YAAY;IACzLhG,qBAAqB,CAACiG,qBAAqB,CAAC;IAC5C7F,sBAAsB,CAAC8F,sBAAsB,CAAC,CAAC,CAAC;;IAEhDzE,WAAW,CAAC,YAAY;MACtB,IAAI0E,QAAQ,GAAG,IAAI7E,GAAG,CAAC,CAAC;MACxB1D,IAAI,CAACwI,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC5B,IAAI9C,GAAG,GAAG8C,KAAK,CAAC9C,GAAG;QACnB,IAAI+C,OAAO,GAAGvH,SAAS,CAACwE,GAAG,CAAC,CAACT,OAAO;QAEpC,IAAIwD,OAAO,EAAE;UACXH,QAAQ,CAACI,GAAG,CAAChD,GAAG,EAAE;YAChBM,KAAK,EAAEyC,OAAO,CAACT,WAAW;YAC1B/B,MAAM,EAAEwC,OAAO,CAACR,YAAY;YAC5B5I,IAAI,EAAEoJ,OAAO,CAACE,UAAU;YACxBzC,GAAG,EAAEuC,OAAO,CAACG;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF,OAAON,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIO,eAAe,GAAG9I,IAAI,CAAC+I,KAAK,CAAC,CAAC,EAAEvC,YAAY,CAAC;EACjD,IAAIwC,aAAa,GAAGhJ,IAAI,CAAC+I,KAAK,CAACtC,UAAU,GAAG,CAAC,CAAC;EAC9C,IAAIwC,UAAU,GAAG,EAAE,CAACxJ,MAAM,CAAClC,kBAAkB,CAACuL,eAAe,CAAC,EAAEvL,kBAAkB,CAACyL,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEpG,IAAIE,WAAW,GAAGtL,QAAQ,CAAC,CAAC;IACxBuL,WAAW,GAAG1L,cAAc,CAACyL,WAAW,EAAE,CAAC,CAAC;IAC5CE,QAAQ,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAIG,eAAe,GAAGxF,UAAU,CAACkC,GAAG,CAAC5F,SAAS,CAAC,CAAC,CAAC;;EAEjD,IAAImJ,YAAY,GAAG1L,MAAM,CAAC,CAAC;EAE3B,SAAS2L,cAAcA,CAAA,EAAG;IACxBxL,GAAG,CAACyL,MAAM,CAACF,YAAY,CAACrE,OAAO,CAAC;EAClC;EAEApH,SAAS,CAAC,YAAY;IACpB,IAAI4L,WAAW,GAAG,CAAC,CAAC;IAEpB,IAAIJ,eAAe,EAAE;MACnB,IAAIjI,sBAAsB,EAAE;QAC1B,IAAIhB,GAAG,EAAE;UACPqJ,WAAW,CAACrK,KAAK,GAAGiK,eAAe,CAACjK,KAAK;QAC3C,CAAC,MAAM;UACLqK,WAAW,CAACpK,IAAI,GAAGgK,eAAe,CAAChK,IAAI;QACzC;QAEAoK,WAAW,CAACzD,KAAK,GAAGqD,eAAe,CAACrD,KAAK;MAC3C,CAAC,MAAM;QACLyD,WAAW,CAACvD,GAAG,GAAGmD,eAAe,CAACnD,GAAG;QACrCuD,WAAW,CAACxD,MAAM,GAAGoD,eAAe,CAACpD,MAAM;MAC7C;IACF;IAEAsD,cAAc,CAAC,CAAC;IAChBD,YAAY,CAACrE,OAAO,GAAGlH,GAAG,CAAC,YAAY;MACrCqL,WAAW,CAACK,WAAW,CAAC;IAC1B,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACF,eAAe,EAAEjI,sBAAsB,EAAEhB,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEpDvC,SAAS,CAAC,YAAY;IACpB4H,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACtF,SAAS,EAAEkJ,eAAe,EAAExF,UAAU,EAAEzC,sBAAsB,CAAC,CAAC,CAAC,CAAC;;EAEtEvD,SAAS,CAAC,YAAY;IACpB4J,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACrH,GAAG,EAAEI,YAAY,EAAEL,SAAS,EAAEJ,IAAI,CAAC6G,GAAG,CAAC,UAAUC,GAAG,EAAE;IACxD,OAAOA,GAAG,CAACnB,GAAG;EAChB,CAAC,CAAC,CAACgE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAIC,WAAW,GAAG,CAAC,CAACX,UAAU,CAACpD,MAAM;EACrC,IAAIgE,UAAU,GAAG,EAAE,CAACpK,MAAM,CAACT,SAAS,EAAE,WAAW,CAAC;EAClD,IAAI8K,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,UAAU;EAEd,IAAI5I,sBAAsB,EAAE;IAC1B,IAAIhB,GAAG,EAAE;MACP0J,SAAS,GAAGpI,aAAa,GAAG,CAAC;MAC7BmI,QAAQ,GAAGnI,aAAa,GAAGgB,YAAY,GAAGR,kBAAkB;IAC9D,CAAC,MAAM;MACL2H,QAAQ,GAAGnI,aAAa,GAAG,CAAC;MAC5BoI,SAAS,GAAG,CAACpI,aAAa,GAAGgB,YAAY,GAAGR,kBAAkB;IAChE;EACF,CAAC,MAAM;IACL6H,OAAO,GAAGjI,YAAY,GAAG,CAAC;IAC1BkI,UAAU,GAAG,CAAClI,YAAY,GAAGgB,aAAa,GAAGR,mBAAmB;EAClE;EAEA,OAAO,aAAa5E,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC7CK,GAAG,EAAEA,GAAG;IACRsK,IAAI,EAAE,SAAS;IACf1K,SAAS,EAAEzB,UAAU,CAAC,EAAE,CAAC0B,MAAM,CAACT,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,CAAC;IAC9DS,KAAK,EAAEA,KAAK;IACZkK,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;MAC9B;MACAvF,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,aAAajH,KAAK,CAAC4B,aAAa,CAACV,YAAY,EAAE;IAChDE,QAAQ,EAAE,MAAM;IAChBE,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAarB,KAAK,CAAC4B,aAAa,CAACtB,cAAc,EAAE;IACnDmM,QAAQ,EAAE1C;EACZ,CAAC,EAAE,aAAa/J,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAEzB,UAAU,CAAC8L,UAAU,GAAGhK,WAAW,GAAG,CAAC,CAAC,EAAEvC,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACJ,MAAM,CAACoK,UAAU,EAAE,YAAY,CAAC,EAAEC,QAAQ,CAAC,EAAExM,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACJ,MAAM,CAACoK,UAAU,EAAE,aAAa,CAAC,EAAEE,SAAS,CAAC,EAAEzM,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACJ,MAAM,CAACoK,UAAU,EAAE,WAAW,CAAC,EAAEG,OAAO,CAAC,EAAE1M,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACJ,MAAM,CAACoK,UAAU,EAAE,cAAc,CAAC,EAAEI,UAAU,CAAC,EAAEpK,WAAW,CAAC,CAAC;IAC1XD,GAAG,EAAEiB;EACP,CAAC,EAAE,aAAalD,KAAK,CAAC4B,aAAa,CAACtB,cAAc,EAAE;IAClDmM,QAAQ,EAAE1C;EACZ,CAAC,EAAE,aAAa/J,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACzCK,GAAG,EAAEkB,UAAU;IACftB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,WAAW,CAAC;IAC5CiB,KAAK,EAAE;MACLoK,SAAS,EAAE,YAAY,CAAC5K,MAAM,CAACkC,aAAa,EAAE,MAAM,CAAC,CAAClC,MAAM,CAACsC,YAAY,EAAE,KAAK,CAAC;MACjFuI,UAAU,EAAE5F,aAAa,GAAG,MAAM,GAAGoB;IACvC;EACF,CAAC,EAAEc,QAAQ,EAAE,aAAajJ,KAAK,CAAC4B,aAAa,CAACZ,SAAS,EAAE;IACvDiB,GAAG,EAAEoB,iBAAiB;IACtBhC,SAAS,EAAEA,SAAS;IACpBuB,MAAM,EAAEA,MAAM;IACdD,QAAQ,EAAEA,QAAQ;IAClBL,KAAK,EAAEzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoJ,QAAQ,CAACf,MAAM,KAAK,CAAC,GAAGC,SAAS,GAAGY,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5F6D,UAAU,EAAEX,WAAW,GAAG,QAAQ,GAAG;IACvC,CAAC;EACH,CAAC,CAAC,EAAE,aAAajM,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC1CC,SAAS,EAAEzB,UAAU,CAAC,EAAE,CAAC0B,MAAM,CAACT,SAAS,EAAE,UAAU,CAAC,EAAE1B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmC,MAAM,CAACT,SAAS,EAAE,mBAAmB,CAAC,EAAEmB,QAAQ,CAACqK,MAAM,CAAC,CAAC;IACxIvK,KAAK,EAAEmJ;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAazL,KAAK,CAAC4B,aAAa,CAAChB,aAAa,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE;IAC1EwH,eAAe,EAAE5G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4G,eAAe;IACvFvH,GAAG,EAAEmB,aAAa;IAClB/B,SAAS,EAAEA,SAAS;IACpBgB,IAAI,EAAEiJ,UAAU;IAChBzJ,SAAS,EAAE,CAACoK,WAAW,IAAI7F,yBAAyB;IACpD0G,SAAS,EAAE,CAAC,CAAC/F;EACf,CAAC,CAAC,CAAC,EAAE,aAAa/G,KAAK,CAAC4B,aAAa,CAACV,YAAY,EAAE;IAClDE,QAAQ,EAAE,OAAO;IACjBE,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;EACH;AACF;AAEA,eAAe,aAAarB,KAAK,CAAC+M,UAAU,CAAChL,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}