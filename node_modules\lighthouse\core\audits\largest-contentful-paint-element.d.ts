export default LargestContentfulPaintElement;
declare class LargestContentfulPaintElement extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=largest-contentful-paint-element.d.ts.map