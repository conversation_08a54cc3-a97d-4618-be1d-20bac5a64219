{"ast": null, "code": "var asciiToArray = require('./_asciiToArray'),\n  hasUnicode = require('./_hasUnicode'),\n  unicodeToArray = require('./_unicodeToArray');\n\n/**\n * Converts `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction stringToArray(string) {\n  return hasUnicode(string) ? unicodeToArray(string) : asciiToArray(string);\n}\nmodule.exports = stringToArray;", "map": {"version": 3, "names": ["asciiToArray", "require", "hasUnicode", "unicodeToArray", "stringToArray", "string", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/lodash/_stringToArray.js"], "sourcesContent": ["var asciiToArray = require('./_asciiToArray'),\n    hasUnicode = require('./_hasUnicode'),\n    unicodeToArray = require('./_unicodeToArray');\n\n/**\n * Converts `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction stringToArray(string) {\n  return hasUnicode(string)\n    ? unicodeToArray(string)\n    : asciiToArray(string);\n}\n\nmodule.exports = stringToArray;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,cAAc,GAAGF,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,MAAM,EAAE;EAC7B,OAAOH,UAAU,CAACG,MAAM,CAAC,GACrBF,cAAc,CAACE,MAAM,CAAC,GACtBL,YAAY,CAACK,MAAM,CAAC;AAC1B;AAEAC,MAAM,CAACC,OAAO,GAAGH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}