{"ast": null, "code": "var global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\nvar Object = global.Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : Object(it);\n} : Object;", "map": {"version": 3, "names": ["global", "require", "uncurryThis", "fails", "classof", "Object", "split", "module", "exports", "propertyIsEnumerable", "it"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/indexed-object.js"], "sourcesContent": ["var global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar Object = global.Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : Object(it);\n} : Object;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIE,KAAK,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIG,OAAO,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AAEjD,IAAII,MAAM,GAAGL,MAAM,CAACK,MAAM;AAC1B,IAAIC,KAAK,GAAGJ,WAAW,CAAC,EAAE,CAACI,KAAK,CAAC;;AAEjC;AACAC,MAAM,CAACC,OAAO,GAAGL,KAAK,CAAC,YAAY;EACjC;EACA;EACA,OAAO,CAACE,MAAM,CAAC,GAAG,CAAC,CAACI,oBAAoB,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,GAAG,UAAUC,EAAE,EAAE;EACjB,OAAON,OAAO,CAACM,EAAE,CAAC,IAAI,QAAQ,GAAGJ,KAAK,CAACI,EAAE,EAAE,EAAE,CAAC,GAAGL,MAAM,CAACK,EAAE,CAAC;AAC7D,CAAC,GAAGL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}