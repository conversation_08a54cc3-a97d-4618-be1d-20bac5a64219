{"ast": null, "code": "// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType(type) {\n  return function (value) {\n    return typeof value === type;\n  };\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport var isFunction = isOfType('function');\nexport var isNull = function (value) {\n  return value === null;\n};\nexport var isRegex = function (value) {\n  return Object.prototype.toString.call(value).slice(8, -1) === 'RegExp';\n};\nexport var isObject = function (value) {\n  return !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value === 'object');\n};\nexport var isUndefined = isOfType('undefined');", "map": {"version": 3, "names": ["isOfType", "type", "value", "isFunction", "isNull", "isRegex", "Object", "prototype", "toString", "call", "slice", "isObject", "isUndefined"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\@gilbarbara\\deep-equal\\src\\helpers.ts"], "sourcesContent": ["import { AnyObject, Primitive } from './types';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType<T extends Primitive | Function>(type: string) {\n  return (value: unknown): value is T => typeof value === type;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = isOfType<Function>('function');\n\nexport const isNull = (value: unknown): value is null => {\n  return value === null;\n};\n\nexport const isRegex = (value: unknown): value is RegExp => {\n  return Object.prototype.toString.call(value).slice(8, -1) === 'RegExp';\n};\n\nexport const isObject = (value: unknown): value is AnyObject => {\n  return !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value === 'object');\n};\n\nexport const isUndefined = isOfType<undefined>('undefined');\n"], "mappings": "AAEA;AACA,SAASA,QAAQA,CAAiCC,IAAY;EAC5D,OAAO,UAACC,KAAc;IAAiB,cAAOA,KAAK,KAAKD,IAAI;EAArB,CAAqB;AAC9D;AAEA;AACA,OAAO,IAAME,UAAU,GAAGH,QAAQ,CAAW,UAAU,CAAC;AAExD,OAAO,IAAMI,MAAM,GAAG,SAAAA,CAACF,KAAc;EACnC,OAAOA,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,OAAO,IAAMG,OAAO,GAAG,SAAAA,CAACH,KAAc;EACpC,OAAOI,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ;AACxE,CAAC;AAED,OAAO,IAAMC,QAAQ,GAAG,SAAAA,CAACT,KAAc;EACrC,OAAO,CAACU,WAAW,CAACV,KAAK,CAAC,IAAI,CAACE,MAAM,CAACF,KAAK,CAAC,KAAKC,UAAU,CAACD,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC;AAClG,CAAC;AAED,OAAO,IAAMU,WAAW,GAAGZ,QAAQ,CAAY,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}