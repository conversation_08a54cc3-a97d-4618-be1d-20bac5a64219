{"ast": null, "code": "export default function contains(root, n) {\n  if (!root) {\n    return false;\n  }\n  return root.contains(n);\n}", "map": {"version": 3, "names": ["contains", "root", "n"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/contains.js"], "sourcesContent": ["export default function contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  return root.contains(n);\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,IAAI,EAAEC,CAAC,EAAE;EACxC,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,KAAK;EACd;EAEA,OAAOA,IAAI,CAACD,QAAQ,CAACE,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}