{"ast": null, "code": "var aCallable = require('../internals/a-callable');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};", "map": {"version": 3, "names": ["aCallable", "require", "module", "exports", "V", "P", "func", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/get-method.js"], "sourcesContent": ["var aCallable = require('../internals/a-callable');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,yBAAyB,CAAC;;AAElD;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIC,IAAI,GAAGF,CAAC,CAACC,CAAC,CAAC;EACf,OAAOC,IAAI,IAAI,IAAI,GAAGC,SAAS,GAAGP,SAAS,CAACM,IAAI,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}