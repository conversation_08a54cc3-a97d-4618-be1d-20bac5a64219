{"ast": null, "code": "'use strict';\n\nvar enhanceError = require('./enhanceError');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nmodule.exports = function createError(message, config, code, request, response) {\n  var error = new Error(message);\n  return enhanceError(error, config, code, request, response);\n};", "map": {"version": 3, "names": ["enhanceError", "require", "module", "exports", "createError", "message", "config", "code", "request", "response", "error", "Error"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/axios/lib/core/createError.js"], "sourcesContent": ["'use strict';\n\nvar enhanceError = require('./enhanceError');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nmodule.exports = function createError(message, config, code, request, response) {\n  var error = new Error(message);\n  return enhanceError(error, config, code, request, response);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC9E,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAACN,OAAO,CAAC;EAC9B,OAAOL,YAAY,CAACU,KAAK,EAAEJ,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}