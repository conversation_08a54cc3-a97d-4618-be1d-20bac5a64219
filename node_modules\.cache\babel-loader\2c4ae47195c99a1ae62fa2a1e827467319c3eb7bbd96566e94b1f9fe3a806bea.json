{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\agenti\\\\navIcon.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* NavIcon - icone per la navigazione nella preparazione dell'ordine\n*\n*/\nimport React, { Component } from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { agenteCompletaOrdine, agenteCreaOrdine, agenteDettagliOrdine } from '../../components/route';\nimport { Costanti } from '../../components/traduttore/const';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass NavAgenteOrdini extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-md-12 iconAgent co-toolbar\",\n      children: /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"c-stepper\",\n        children: [/*#__PURE__*/_jsxDEV(NavLink, {\n          id: \"cliAgent\",\n          className: \"c-stepper__item\",\n          to: agenteDettagliOrdine,\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"c-stepper__title\",\n              children: Costanti.Testata\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n          id: \"ordAgent\",\n          className: \"c-stepper__item\",\n          to: agenteCreaOrdine,\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"c-stepper__title\",\n              children: Costanti.Corpo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n          id: \"prodAgent\",\n          className: \"c-stepper__item\",\n          to: agenteCompletaOrdine,\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"c-stepper__title\",\n              children: Costanti.Riepilogo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default NavAgenteOrdini;", "map": {"version": 3, "names": ["React", "Component", "NavLink", "agenteCompletaOrdine", "agenteCreaOrdine", "agenteDettagliOrdine", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "NavAgenteOrdini", "constructor", "props", "state", "render", "className", "children", "id", "to", "Testata", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Corpo", "Riepilogo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/agenti/navIcon.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* NavIcon - icone per la navigazione nella preparazione dell'ordine\n*\n*/\nimport React, { Component } from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { agenteCompletaOrdine, agenteCreaOrdine, agenteDettagliOrdine } from '../../components/route';\nimport { Costanti } from '../../components/traduttore/const';\n\nclass NavAgenteOrdini extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {}\n    }\n    render() {\n        return (\n            <div className=\"col-md-12 iconAgent co-toolbar\">\n                <ol className=\"c-stepper\">\n                    <NavLink id=\"cliAgent\" className=\"c-stepper__item\" to={agenteDettagliOrdine}>\n                        <li>\n                            <h3 className=\"c-stepper__title\">{Costanti.Testata}</h3>\n                            {/* <p className=\"c-stepper__desc\">Testata dell'ordine</p> */}\n                        </li>\n                    </NavLink>\n                    <NavLink id=\"ordAgent\" className=\"c-stepper__item\" to={agenteCreaOrdine}>\n                        <li>\n                            <h3 className=\"c-stepper__title\">{Costanti.Corpo}</h3>\n                            {/* <p className=\"c-stepper__desc\">Corpo dell'ordine</p> */}\n                        </li>\n                    </NavLink>\n                    <NavLink id=\"prodAgent\" className=\"c-stepper__item\" to={agenteCompletaOrdine}>\n                        <li>\n                            <h3 className=\"c-stepper__title\">{Costanti.Riepilogo}</h3>\n                            {/* <p className=\"c-stepper__desc\">Riepilogo dell'ordine</p> */}\n                        </li>\n                    </NavLink>\n                </ol>\n            </div>\n        )\n    }\n}\n\nexport default NavAgenteOrdini;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,oBAAoB,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,wBAAwB;AACrG,SAASC,QAAQ,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,eAAe,SAASR,SAAS,CAAC;EACpCS,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,MAAMA,CAAA,EAAG;IACL,oBACIL,OAAA;MAAKM,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC3CP,OAAA;QAAIM,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACrBP,OAAA,CAACN,OAAO;UAACc,EAAE,EAAC,UAAU;UAACF,SAAS,EAAC,iBAAiB;UAACG,EAAE,EAAEZ,oBAAqB;UAAAU,QAAA,eACxEP,OAAA;YAAAO,QAAA,eACIP,OAAA;cAAIM,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAET,QAAQ,CAACY;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAExD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACVd,OAAA,CAACN,OAAO;UAACc,EAAE,EAAC,UAAU;UAACF,SAAS,EAAC,iBAAiB;UAACG,EAAE,EAAEb,gBAAiB;UAAAW,QAAA,eACpEP,OAAA;YAAAO,QAAA,eACIP,OAAA;cAAIM,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAET,QAAQ,CAACiB;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACVd,OAAA,CAACN,OAAO;UAACc,EAAE,EAAC,WAAW;UAACF,SAAS,EAAC,iBAAiB;UAACG,EAAE,EAAEd,oBAAqB;UAAAY,QAAA,eACzEP,OAAA;YAAAO,QAAA,eACIP,OAAA;cAAIM,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAET,QAAQ,CAACkB;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEd;AACJ;AAEA,eAAeb,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}