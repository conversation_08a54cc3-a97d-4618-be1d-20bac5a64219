{"version": 3, "file": "valid-exclude.js", "sourceRoot": "", "sources": ["../../src/valid-exclude.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,WAAW,CAAA;AAE5B,eAAe,CACb,CAAM,EAC0C,EAAE;IAClD,IACE,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,MAAM;QACR,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,EACnC,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IACD,IAAI,CACF,oEAAoE;QAClE,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC9B,CAAA;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAA", "sourcesContent": ["import fail from './fail.js'\nimport { TshyConfig } from './types.js'\nexport default (\n  d: any,\n): d is Exclude<TshyConfig['exclude'], undefined> => {\n  if (\n    !!d &&\n    Array.isArray(d) &&\n    d.length &&\n    !d.some(d => typeof d !== 'string')\n  ) {\n    return true\n  }\n  fail(\n    `tshy.exclude must be an array of string glob patterns if defined, ` +\n      `got: ${JSON.stringify(d)}`,\n  )\n  return process.exit(1)\n}\n"]}