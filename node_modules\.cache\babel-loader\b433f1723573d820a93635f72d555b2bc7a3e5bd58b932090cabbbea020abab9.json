{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar Steps = function Steps(props) {\n  var size = props.size,\n    steps = props.steps,\n    _props$percent = props.percent,\n    percent = _props$percent === void 0 ? 0 : _props$percent,\n    _props$strokeWidth = props.strokeWidth,\n    strokeWidth = _props$strokeWidth === void 0 ? 8 : _props$strokeWidth,\n    strokeColor = props.strokeColor,\n    trailColor = props.trailColor,\n    prefixCls = props.prefixCls,\n    children = props.children;\n  var current = Math.round(steps * (percent / 100));\n  var stepWidth = size === 'small' ? 2 : 14;\n  var styledSteps = [];\n  for (var i = 0; i < steps; i += 1) {\n    styledSteps.push(/*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(\"\".concat(prefixCls, \"-steps-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-steps-item-active\"), i <= current - 1)),\n      style: {\n        backgroundColor: i <= current - 1 ? strokeColor : trailColor,\n        width: stepWidth,\n        height: strokeWidth\n      }\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-steps-outer\")\n  }, styledSteps, children);\n};\nexport default Steps;", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "Steps", "props", "size", "steps", "_props$percent", "percent", "_props$strokeWidth", "strokeWidth", "strokeColor", "trailColor", "prefixCls", "children", "current", "Math", "round", "<PERSON><PERSON><PERSON><PERSON>", "styledSteps", "i", "push", "createElement", "key", "className", "concat", "style", "backgroundColor", "width", "height"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/progress/Steps.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\n\nvar Steps = function Steps(props) {\n  var size = props.size,\n      steps = props.steps,\n      _props$percent = props.percent,\n      percent = _props$percent === void 0 ? 0 : _props$percent,\n      _props$strokeWidth = props.strokeWidth,\n      strokeWidth = _props$strokeWidth === void 0 ? 8 : _props$strokeWidth,\n      strokeColor = props.strokeColor,\n      trailColor = props.trailColor,\n      prefixCls = props.prefixCls,\n      children = props.children;\n  var current = Math.round(steps * (percent / 100));\n  var stepWidth = size === 'small' ? 2 : 14;\n  var styledSteps = [];\n\n  for (var i = 0; i < steps; i += 1) {\n    styledSteps.push( /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(\"\".concat(prefixCls, \"-steps-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-steps-item-active\"), i <= current - 1)),\n      style: {\n        backgroundColor: i <= current - 1 ? strokeColor : trailColor,\n        width: stepWidth,\n        height: strokeWidth\n      }\n    }));\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-steps-outer\")\n  }, styledSteps, children);\n};\n\nexport default Steps;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AAEnC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACjBC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,cAAc,GAAGH,KAAK,CAACI,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc;IACxDE,kBAAkB,GAAGL,KAAK,CAACM,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IACpEE,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;EAC7B,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACX,KAAK,IAAIE,OAAO,GAAG,GAAG,CAAC,CAAC;EACjD,IAAIU,SAAS,GAAGb,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;EACzC,IAAIc,WAAW,GAAG,EAAE;EAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,KAAK,EAAEc,CAAC,IAAI,CAAC,EAAE;IACjCD,WAAW,CAACE,IAAI,CAAE,aAAapB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACxDC,GAAG,EAAEH,CAAC;MACNI,SAAS,EAAEtB,UAAU,CAAC,EAAE,CAACuB,MAAM,CAACZ,SAAS,EAAE,aAAa,CAAC,EAAEb,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyB,MAAM,CAACZ,SAAS,EAAE,oBAAoB,CAAC,EAAEO,CAAC,IAAIL,OAAO,GAAG,CAAC,CAAC,CAAC;MAC7IW,KAAK,EAAE;QACLC,eAAe,EAAEP,CAAC,IAAIL,OAAO,GAAG,CAAC,GAAGJ,WAAW,GAAGC,UAAU;QAC5DgB,KAAK,EAAEV,SAAS;QAChBW,MAAM,EAAEnB;MACV;IACF,CAAC,CAAC,CAAC;EACL;EAEA,OAAO,aAAaT,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC7CE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEM,WAAW,EAAEL,QAAQ,CAAC;AAC3B,CAAC;AAED,eAAeX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}