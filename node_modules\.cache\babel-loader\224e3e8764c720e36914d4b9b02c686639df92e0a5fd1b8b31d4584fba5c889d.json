{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Handles from './Handles';\nimport useDrag from './hooks/useDrag';\nimport SliderContext from './context';\nimport Tracks from './Tracks';\nimport Marks from './Marks';\nimport Steps from './Steps';\nimport useOffset from './hooks/useOffset';\nimport warning from \"rc-util/es/warning\";\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    autoFocus = props.autoFocus,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    _props$min = props.min,\n    min = _props$min === void 0 ? 0 : _props$min,\n    _props$max = props.max,\n    max = _props$max === void 0 ? 100 : _props$max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    range = props.range,\n    count = props.count,\n    onChange = props.onChange,\n    onBeforeChange = props.onBeforeChange,\n    onAfterChange = props.onAfterChange,\n    _props$allowCross = props.allowCross,\n    allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n    _props$pushable = props.pushable,\n    pushable = _props$pushable === void 0 ? false : _props$pushable,\n    draggableTrack = props.draggableTrack,\n    reverse = props.reverse,\n    vertical = props.vertical,\n    _props$included = props.included,\n    included = _props$included === void 0 ? true : _props$included,\n    startPoint = props.startPoint,\n    trackStyle = props.trackStyle,\n    handleStyle = props.handleStyle,\n    railStyle = props.railStyle,\n    dotStyle = props.dotStyle,\n    activeDotStyle = props.activeDotStyle,\n    marks = props.marks,\n    dots = props.dots,\n    handleRender = props.handleRender,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    ariaLabelForHandle = props.ariaLabelForHandle,\n    ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n    ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef();\n  var containerRef = React.useRef();\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]); // ============================ Range =============================\n\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]); // ============================= Step =============================\n\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]); // ============================= Push =============================\n\n  var mergedPush = React.useMemo(function () {\n    if (pushable === true) {\n      return mergedStep;\n    }\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]); // ============================ Marks =============================\n\n  var markList = React.useMemo(function () {\n    var keys = Object.keys(marks || {});\n    return keys.map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]); // ============================ Format ============================\n\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n    _useOffset2 = _slicedToArray(_useOffset, 2),\n    formatValue = _useOffset2[0],\n    offsetValues = _useOffset2[1]; // ============================ Values ============================\n\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n    var _valueList = _slicedToArray(valueList, 1),\n      _valueList$ = _valueList[0],\n      val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n    var returnValues = mergedValue === null ? [] : [val0]; // Format as range\n\n    if (range) {\n      returnValues = _toConsumableArray(valueList); // When count provided or value is `undefined`, we fill values\n\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount); // Fill with count\n\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    } // Align in range\n\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, range, mergedMin, count, formatValue]); // =========================== onChange ===========================\n\n  var rawValuesRef = React.useRef(rawValues);\n  rawValuesRef.current = rawValues;\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return range ? triggerValues : triggerValues[0];\n  };\n  var triggerChange = function triggerChange(nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    }); // Trigger event if needed\n\n    if (onChange && !shallowEqual(cloneNextValues, rawValuesRef.current)) {\n      onChange(getTriggerValue(cloneNextValues));\n    } // We set this later since it will re-render component immediately\n\n    setValue(cloneNextValues);\n  };\n  var changeToCloseValue = function changeToCloseValue(newValue) {\n    if (!disabled) {\n      var valueIndex = 0;\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n      }); // Create new values\n\n      var cloneNextValues = _toConsumableArray(rawValues);\n      cloneNextValues[valueIndex] = newValue; // Fill value to match default 2\n\n      if (range && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n      onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(cloneNextValues));\n      triggerChange(cloneNextValues);\n      onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(cloneNextValues));\n    }\n  }; // ============================ Click =============================\n\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      width = _containerRef$current.width,\n      height = _containerRef$current.height,\n      left = _containerRef$current.left,\n      top = _containerRef$current.top,\n      bottom = _containerRef$current.bottom,\n      right = _containerRef$current.right;\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var percent;\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n      default:\n        percent = (clientX - left) / width;\n    }\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue));\n  }; // =========================== Keyboard ===========================\n\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardValue = _React$useState2[0],\n    setKeyboardValue = _React$useState2[1];\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(next.values));\n      setKeyboardValue(next.value);\n    }\n  };\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n    setKeyboardValue(null);\n  }, [keyboardValue]); // ============================= Drag =============================\n\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (draggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n      return false;\n    }\n    return draggableTrack;\n  }, [draggableTrack, mergedStep]);\n  var finishChange = function finishChange() {\n    onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(rawValuesRef.current));\n  };\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues),\n    _useDrag2 = _slicedToArray(_useDrag, 4),\n    draggingIndex = _useDrag2[0],\n    draggingValue = _useDrag2[1],\n    cacheValues = _useDrag2[2],\n    onStartDrag = _useDrag2[3];\n  var onStartMove = function onStartMove(e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(rawValuesRef.current));\n  }; // Auto focus for updated handle\n\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]); // =========================== Included ===========================\n\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]); // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n\n  var _React$useMemo = React.useMemo(function () {\n      if (!range) {\n        return [mergedMin, sortedCacheValues[0]];\n      }\n      return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n    }, [sortedCacheValues, range, mergedMin]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    includedStart = _React$useMemo2[0],\n    includedEnd = _React$useMemo2[1]; // ============================= Refs =============================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _document = document,\n          activeElement = _document.activeElement;\n        if (containerRef.current.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur();\n        }\n      }\n    };\n  }); // ========================== Auto Focus ==========================\n\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []); // =========================== Context ============================\n\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: range,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle\n    };\n  }, [mergedMin, mergedMax, direction, disabled, mergedStep, included, includedStart, includedEnd, range, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaValueTextFormatterForHandle]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-vertical\"), vertical), _defineProperty(_classNames, \"\".concat(prefixCls, \"-horizontal\"), !vertical), _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-marks\"), markList.length), _classNames)),\n    style: style,\n    onMouseDown: onSliderMouseDown\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-rail\"),\n    style: railStyle\n  }), /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: sortedCacheValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : null\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;", "map": {"version": 3, "names": ["_defineProperty", "_toConsumableArray", "_slicedToArray", "_typeof", "React", "classNames", "shallowEqual", "useMergedState", "<PERSON><PERSON>", "useDrag", "SliderContext", "Tracks", "Marks", "Steps", "useOffset", "warning", "Slide<PERSON>", "forwardRef", "props", "ref", "_classNames", "_props$prefixCls", "prefixCls", "className", "style", "_props$disabled", "disabled", "autoFocus", "onFocus", "onBlur", "_props$min", "min", "_props$max", "max", "_props$step", "step", "value", "defaultValue", "range", "count", "onChange", "onBeforeChange", "onAfterChange", "_props$allowCross", "allowCross", "_props$pushable", "pushable", "draggableTrack", "reverse", "vertical", "_props$included", "included", "startPoint", "trackStyle", "handleStyle", "railStyle", "dotStyle", "activeDotStyle", "marks", "dots", "handleRender", "_props$tabIndex", "tabIndex", "ariaLabelFor<PERSON>andle", "ariaLabelledByForHandle", "ariaValueTextFormatterForHandle", "handlesRef", "useRef", "containerRef", "direction", "useMemo", "mergedMin", "isFinite", "mergedMax", "mergedStep", "mergedPush", "markList", "keys", "Object", "map", "key", "mark", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isValidElement", "label", "filter", "_ref", "sort", "a", "b", "_useOffset", "_useOffset2", "formatValue", "offsetValues", "_useMergedState", "_useMergedState2", "mergedValue", "setValue", "rawValues", "valueList", "undefined", "Array", "isArray", "_valueList", "_valueList$", "val0", "returnV<PERSON>ues", "pointCount", "slice", "length", "_returnValues", "push", "for<PERSON>ach", "val", "index", "rawValuesRef", "current", "getTriggerValue", "triggerValues", "trigger<PERSON>hange", "nextV<PERSON>ues", "cloneNextValues", "changeToCloseValue", "newValue", "valueIndex", "valueDist", "dist", "Math", "abs", "onSliderMouseDown", "e", "preventDefault", "_containerRef$current", "getBoundingClientRect", "width", "height", "left", "top", "bottom", "right", "clientX", "clientY", "percent", "nextValue", "_React$useState", "useState", "_React$useState2", "keyboardValue", "setKeyboardValue", "onHandleOffsetChange", "offset", "next", "values", "useEffect", "indexOf", "focus", "mergedDraggableTrack", "process", "env", "NODE_ENV", "finishChange", "_useDrag", "_useDrag2", "draggingIndex", "draggingValue", "cacheValues", "onStartDrag", "onStartMove", "dragging", "lastIndexOf", "sortedCacheValues", "_React$useMemo", "_React$useMemo2", "includedStart", "includedEnd", "useImperativeHandle", "blur", "_document", "document", "activeElement", "contains", "context", "createElement", "Provider", "concat", "onMouseDown", "activeStyle", "onOffsetChange", "onClick", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-slider/es/Slider.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport shallowEqual from 'shallowequal';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Handles from './Handles';\nimport useDrag from './hooks/useDrag';\nimport SliderContext from './context';\nimport Tracks from './Tracks';\nimport Marks from './Marks';\nimport Steps from './Steps';\nimport useOffset from './hooks/useOffset';\nimport warning from \"rc-util/es/warning\";\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n      className = props.className,\n      style = props.style,\n      _props$disabled = props.disabled,\n      disabled = _props$disabled === void 0 ? false : _props$disabled,\n      autoFocus = props.autoFocus,\n      onFocus = props.onFocus,\n      onBlur = props.onBlur,\n      _props$min = props.min,\n      min = _props$min === void 0 ? 0 : _props$min,\n      _props$max = props.max,\n      max = _props$max === void 0 ? 100 : _props$max,\n      _props$step = props.step,\n      step = _props$step === void 0 ? 1 : _props$step,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      range = props.range,\n      count = props.count,\n      onChange = props.onChange,\n      onBeforeChange = props.onBeforeChange,\n      onAfterChange = props.onAfterChange,\n      _props$allowCross = props.allowCross,\n      allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n      _props$pushable = props.pushable,\n      pushable = _props$pushable === void 0 ? false : _props$pushable,\n      draggableTrack = props.draggableTrack,\n      reverse = props.reverse,\n      vertical = props.vertical,\n      _props$included = props.included,\n      included = _props$included === void 0 ? true : _props$included,\n      startPoint = props.startPoint,\n      trackStyle = props.trackStyle,\n      handleStyle = props.handleStyle,\n      railStyle = props.railStyle,\n      dotStyle = props.dotStyle,\n      activeDotStyle = props.activeDotStyle,\n      marks = props.marks,\n      dots = props.dots,\n      handleRender = props.handleRender,\n      _props$tabIndex = props.tabIndex,\n      tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n      ariaLabelForHandle = props.ariaLabelForHandle,\n      ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n      ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef();\n  var containerRef = React.useRef();\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]); // ============================ Range =============================\n\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]); // ============================= Step =============================\n\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]); // ============================= Push =============================\n\n  var mergedPush = React.useMemo(function () {\n    if (pushable === true) {\n      return mergedStep;\n    }\n\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]); // ============================ Marks =============================\n\n  var markList = React.useMemo(function () {\n    var keys = Object.keys(marks || {});\n    return keys.map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]); // ============================ Format ============================\n\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n      _useOffset2 = _slicedToArray(_useOffset, 2),\n      formatValue = _useOffset2[0],\n      offsetValues = _useOffset2[1]; // ============================ Values ============================\n\n\n  var _useMergedState = useMergedState(defaultValue, {\n    value: value\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedValue = _useMergedState2[0],\n      setValue = _useMergedState2[1];\n\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n\n    var _valueList = _slicedToArray(valueList, 1),\n        _valueList$ = _valueList[0],\n        val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n\n    var returnValues = mergedValue === null ? [] : [val0]; // Format as range\n\n    if (range) {\n      returnValues = _toConsumableArray(valueList); // When count provided or value is `undefined`, we fill values\n\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount); // Fill with count\n\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    } // Align in range\n\n\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, range, mergedMin, count, formatValue]); // =========================== onChange ===========================\n\n  var rawValuesRef = React.useRef(rawValues);\n  rawValuesRef.current = rawValues;\n\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return range ? triggerValues : triggerValues[0];\n  };\n\n  var triggerChange = function triggerChange(nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    }); // Trigger event if needed\n\n\n    if (onChange && !shallowEqual(cloneNextValues, rawValuesRef.current)) {\n      onChange(getTriggerValue(cloneNextValues));\n    } // We set this later since it will re-render component immediately\n\n\n    setValue(cloneNextValues);\n  };\n\n  var changeToCloseValue = function changeToCloseValue(newValue) {\n    if (!disabled) {\n      var valueIndex = 0;\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n      }); // Create new values\n\n      var cloneNextValues = _toConsumableArray(rawValues);\n\n      cloneNextValues[valueIndex] = newValue; // Fill value to match default 2\n\n      if (range && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n\n      onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(cloneNextValues));\n      triggerChange(cloneNextValues);\n      onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(cloneNextValues));\n    }\n  }; // ============================ Click =============================\n\n\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height,\n        left = _containerRef$current.left,\n        top = _containerRef$current.top,\n        bottom = _containerRef$current.bottom,\n        right = _containerRef$current.right;\n\n    var clientX = e.clientX,\n        clientY = e.clientY;\n    var percent;\n\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n\n      default:\n        percent = (clientX - left) / width;\n    }\n\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue));\n  }; // =========================== Keyboard ===========================\n\n\n  var _React$useState = React.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      keyboardValue = _React$useState2[0],\n      setKeyboardValue = _React$useState2[1];\n\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(next.values));\n      setKeyboardValue(next.value);\n    }\n  };\n\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n\n    setKeyboardValue(null);\n  }, [keyboardValue]); // ============================= Drag =============================\n\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (draggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n\n      return false;\n    }\n\n    return draggableTrack;\n  }, [draggableTrack, mergedStep]);\n\n  var finishChange = function finishChange() {\n    onAfterChange === null || onAfterChange === void 0 ? void 0 : onAfterChange(getTriggerValue(rawValuesRef.current));\n  };\n\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues),\n      _useDrag2 = _slicedToArray(_useDrag, 4),\n      draggingIndex = _useDrag2[0],\n      draggingValue = _useDrag2[1],\n      cacheValues = _useDrag2[2],\n      onStartDrag = _useDrag2[3];\n\n  var onStartMove = function onStartMove(e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 ? void 0 : onBeforeChange(getTriggerValue(rawValuesRef.current));\n  }; // Auto focus for updated handle\n\n\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]); // =========================== Included ===========================\n\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]); // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n\n  var _React$useMemo = React.useMemo(function () {\n    if (!range) {\n      return [mergedMin, sortedCacheValues[0]];\n    }\n\n    return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n  }, [sortedCacheValues, range, mergedMin]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      includedStart = _React$useMemo2[0],\n      includedEnd = _React$useMemo2[1]; // ============================= Refs =============================\n\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _document = document,\n            activeElement = _document.activeElement;\n\n        if (containerRef.current.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur();\n        }\n      }\n    };\n  }); // ========================== Auto Focus ==========================\n\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []); // =========================== Context ============================\n\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: range,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle\n    };\n  }, [mergedMin, mergedMax, direction, disabled, mergedStep, included, includedStart, includedEnd, range, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaValueTextFormatterForHandle]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-vertical\"), vertical), _defineProperty(_classNames, \"\".concat(prefixCls, \"-horizontal\"), !vertical), _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-marks\"), markList.length), _classNames)),\n    style: style,\n    onMouseDown: onSliderMouseDown\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-rail\"),\n    style: railStyle\n  }), /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: sortedCacheValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : null\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\n\nexport default Slider;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,MAAM,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,WAAW;EAEf,IAAIC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,eAAe,GAAGP,KAAK,CAACQ,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,UAAU,GAAGZ,KAAK,CAACa,GAAG;IACtBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;IAC5CE,UAAU,GAAGd,KAAK,CAACe,GAAG;IACtBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,UAAU;IAC9CE,WAAW,GAAGhB,KAAK,CAACiB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC/CE,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,YAAY,GAAGnB,KAAK,CAACmB,YAAY;IACjCC,KAAK,GAAGpB,KAAK,CAACoB,KAAK;IACnBC,KAAK,GAAGrB,KAAK,CAACqB,KAAK;IACnBC,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,cAAc,GAAGvB,KAAK,CAACuB,cAAc;IACrCC,aAAa,GAAGxB,KAAK,CAACwB,aAAa;IACnCC,iBAAiB,GAAGzB,KAAK,CAAC0B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,eAAe,GAAG3B,KAAK,CAAC4B,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,cAAc,GAAG7B,KAAK,CAAC6B,cAAc;IACrCC,OAAO,GAAG9B,KAAK,CAAC8B,OAAO;IACvBC,QAAQ,GAAG/B,KAAK,CAAC+B,QAAQ;IACzBC,eAAe,GAAGhC,KAAK,CAACiC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,UAAU,GAAGlC,KAAK,CAACkC,UAAU;IAC7BC,UAAU,GAAGnC,KAAK,CAACmC,UAAU;IAC7BC,WAAW,GAAGpC,KAAK,CAACoC,WAAW;IAC/BC,SAAS,GAAGrC,KAAK,CAACqC,SAAS;IAC3BC,QAAQ,GAAGtC,KAAK,CAACsC,QAAQ;IACzBC,cAAc,GAAGvC,KAAK,CAACuC,cAAc;IACrCC,KAAK,GAAGxC,KAAK,CAACwC,KAAK;IACnBC,IAAI,GAAGzC,KAAK,CAACyC,IAAI;IACjBC,YAAY,GAAG1C,KAAK,CAAC0C,YAAY;IACjCC,eAAe,GAAG3C,KAAK,CAAC4C,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,kBAAkB,GAAG7C,KAAK,CAAC6C,kBAAkB;IAC7CC,uBAAuB,GAAG9C,KAAK,CAAC8C,uBAAuB;IACvDC,+BAA+B,GAAG/C,KAAK,CAAC+C,+BAA+B;EAC3E,IAAIC,UAAU,GAAG9D,KAAK,CAAC+D,MAAM,CAAC,CAAC;EAC/B,IAAIC,YAAY,GAAGhE,KAAK,CAAC+D,MAAM,CAAC,CAAC;EACjC,IAAIE,SAAS,GAAGjE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACxC,IAAIrB,QAAQ,EAAE;MACZ,OAAOD,OAAO,GAAG,KAAK,GAAG,KAAK;IAChC;IAEA,OAAOA,OAAO,GAAG,KAAK,GAAG,KAAK;EAChC,CAAC,EAAE,CAACA,OAAO,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEzB,IAAIsB,SAAS,GAAGnE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACxC,OAAOE,QAAQ,CAACzC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC;EAChC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,IAAI0C,SAAS,GAAGrE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACxC,OAAOE,QAAQ,CAACvC,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG;EAClC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEX,IAAIyC,UAAU,GAAGtE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACzC,OAAOnC,IAAI,KAAK,IAAI,IAAIA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI;EAC9C,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAIwC,UAAU,GAAGvE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACzC,IAAIxB,QAAQ,KAAK,IAAI,EAAE;MACrB,OAAO4B,UAAU;IACnB;IAEA,OAAO5B,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,KAAK;EACzC,CAAC,EAAE,CAACA,QAAQ,EAAE4B,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE5B,IAAIE,QAAQ,GAAGxE,KAAK,CAACkE,OAAO,CAAC,YAAY;IACvC,IAAIO,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACnB,KAAK,IAAI,CAAC,CAAC,CAAC;IACnC,OAAOmB,IAAI,CAACE,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC7B,IAAIC,IAAI,GAAGvB,KAAK,CAACsB,GAAG,CAAC;MACrB,IAAIE,OAAO,GAAG;QACZ9C,KAAK,EAAE+C,MAAM,CAACH,GAAG;MACnB,CAAC;MAED,IAAIC,IAAI,IAAI9E,OAAO,CAAC8E,IAAI,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAa7E,KAAK,CAACgF,cAAc,CAACH,IAAI,CAAC,KAAK,OAAO,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI,CAAC,EAAE;QAC3HC,OAAO,CAAC1D,KAAK,GAAGyD,IAAI,CAACzD,KAAK;QAC1B0D,OAAO,CAACG,KAAK,GAAGJ,IAAI,CAACI,KAAK;MAC5B,CAAC,MAAM;QACLH,OAAO,CAACG,KAAK,GAAGJ,IAAI;MACtB;MAEA,OAAOC,OAAO;IAChB,CAAC,CAAC,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE;MACxB,IAAIF,KAAK,GAAGE,IAAI,CAACF,KAAK;MACtB,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;IAC3C,CAAC,CAAC,CAACG,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOD,CAAC,CAACrD,KAAK,GAAGsD,CAAC,CAACtD,KAAK;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEb,IAAIiC,UAAU,GAAG7E,SAAS,CAACyD,SAAS,EAAEE,SAAS,EAAEC,UAAU,EAAEE,QAAQ,EAAEhC,UAAU,EAAE+B,UAAU,CAAC;IAC1FiB,WAAW,GAAG1F,cAAc,CAACyF,UAAU,EAAE,CAAC,CAAC;IAC3CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC5BE,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGnC,IAAIG,eAAe,GAAGxF,cAAc,CAAC8B,YAAY,EAAE;MACjDD,KAAK,EAAEA;IACT,CAAC,CAAC;IACE4D,gBAAgB,GAAG9F,cAAc,CAAC6F,eAAe,EAAE,CAAC,CAAC;IACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIG,SAAS,GAAG/F,KAAK,CAACkE,OAAO,CAAC,YAAY;IACxC,IAAI8B,SAAS,GAAGH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKI,SAAS,GAAG,EAAE,GAAGC,KAAK,CAACC,OAAO,CAACN,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;IAEjI,IAAIO,UAAU,GAAGtG,cAAc,CAACkG,SAAS,EAAE,CAAC,CAAC;MACzCK,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;MAC3BE,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGlC,SAAS,GAAGkC,WAAW;IAE3D,IAAIE,YAAY,GAAGV,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,CAACS,IAAI,CAAC,CAAC,CAAC;;IAEvD,IAAIpE,KAAK,EAAE;MACTqE,YAAY,GAAG1G,kBAAkB,CAACmG,SAAS,CAAC,CAAC,CAAC;;MAE9C,IAAI7D,KAAK,IAAI0D,WAAW,KAAKI,SAAS,EAAE;QACtC,IAAIO,UAAU,GAAGrE,KAAK,IAAI,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC;QAC3CoE,YAAY,GAAGA,YAAY,CAACE,KAAK,CAAC,CAAC,EAAED,UAAU,CAAC,CAAC,CAAC;;QAElD,OAAOD,YAAY,CAACG,MAAM,GAAGF,UAAU,EAAE;UACvC,IAAIG,aAAa;UAEjBJ,YAAY,CAACK,IAAI,CAAC,CAACD,aAAa,GAAGJ,YAAY,CAACA,YAAY,CAACG,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIC,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGxC,SAAS,CAAC;QAC7I;MACF;MAEAoC,YAAY,CAACnB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAChC,OAAOD,CAAC,GAAGC,CAAC;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGFiB,YAAY,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;MACzCR,YAAY,CAACQ,KAAK,CAAC,GAAGtB,WAAW,CAACqB,GAAG,CAAC;IACxC,CAAC,CAAC;IACF,OAAOP,YAAY;EACrB,CAAC,EAAE,CAACV,WAAW,EAAE3D,KAAK,EAAEiC,SAAS,EAAEhC,KAAK,EAAEsD,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEzD,IAAIuB,YAAY,GAAGhH,KAAK,CAAC+D,MAAM,CAACgC,SAAS,CAAC;EAC1CiB,YAAY,CAACC,OAAO,GAAGlB,SAAS;EAEhC,IAAImB,eAAe,GAAG,SAASA,eAAeA,CAACC,aAAa,EAAE;IAC5D,OAAOjF,KAAK,GAAGiF,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC;EACjD,CAAC;EAED,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAE;IACrD;IACA,IAAIC,eAAe,GAAGzH,kBAAkB,CAACwH,UAAU,CAAC,CAACjC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACxE,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC;;IAGJ,IAAIlD,QAAQ,IAAI,CAAClC,YAAY,CAACoH,eAAe,EAAEN,YAAY,CAACC,OAAO,CAAC,EAAE;MACpE7E,QAAQ,CAAC8E,eAAe,CAACI,eAAe,CAAC,CAAC;IAC5C,CAAC,CAAC;;IAGFxB,QAAQ,CAACwB,eAAe,CAAC;EAC3B,CAAC;EAED,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;IAC7D,IAAI,CAAClG,QAAQ,EAAE;MACb,IAAImG,UAAU,GAAG,CAAC;MAClB,IAAIC,SAAS,GAAGrD,SAAS,GAAGF,SAAS;MACrC4B,SAAS,CAACc,OAAO,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;QACtC,IAAIY,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACL,QAAQ,GAAGV,GAAG,CAAC;QAEnC,IAAIa,IAAI,IAAID,SAAS,EAAE;UACrBA,SAAS,GAAGC,IAAI;UAChBF,UAAU,GAAGV,KAAK;QACpB;MACF,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIO,eAAe,GAAGzH,kBAAkB,CAACkG,SAAS,CAAC;MAEnDuB,eAAe,CAACG,UAAU,CAAC,GAAGD,QAAQ,CAAC,CAAC;;MAExC,IAAItF,KAAK,IAAI,CAAC6D,SAAS,CAACW,MAAM,IAAIvE,KAAK,KAAK8D,SAAS,EAAE;QACrDqB,eAAe,CAACV,IAAI,CAACY,QAAQ,CAAC;MAChC;MAEAnF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC6E,eAAe,CAACI,eAAe,CAAC,CAAC;MAChHF,aAAa,CAACE,eAAe,CAAC;MAC9BhF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4E,eAAe,CAACI,eAAe,CAAC,CAAC;IAC/G;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,CAAC,EAAE;IACpDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAIC,qBAAqB,GAAGjE,YAAY,CAACiD,OAAO,CAACiB,qBAAqB,CAAC,CAAC;MACpEC,KAAK,GAAGF,qBAAqB,CAACE,KAAK;MACnCC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;MACrCC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;MACjCC,GAAG,GAAGL,qBAAqB,CAACK,GAAG;MAC/BC,MAAM,GAAGN,qBAAqB,CAACM,MAAM;MACrCC,KAAK,GAAGP,qBAAqB,CAACO,KAAK;IAEvC,IAAIC,OAAO,GAAGV,CAAC,CAACU,OAAO;MACnBC,OAAO,GAAGX,CAAC,CAACW,OAAO;IACvB,IAAIC,OAAO;IAEX,QAAQ1E,SAAS;MACf,KAAK,KAAK;QACR0E,OAAO,GAAG,CAACJ,MAAM,GAAGG,OAAO,IAAIN,MAAM;QACrC;MAEF,KAAK,KAAK;QACRO,OAAO,GAAG,CAACD,OAAO,GAAGJ,GAAG,IAAIF,MAAM;QAClC;MAEF,KAAK,KAAK;QACRO,OAAO,GAAG,CAACH,KAAK,GAAGC,OAAO,IAAIN,KAAK;QACnC;MAEF;QACEQ,OAAO,GAAG,CAACF,OAAO,GAAGJ,IAAI,IAAIF,KAAK;IACtC;IAEA,IAAIS,SAAS,GAAGzE,SAAS,GAAGwE,OAAO,IAAItE,SAAS,GAAGF,SAAS,CAAC;IAC7DoD,kBAAkB,CAAC9B,WAAW,CAACmD,SAAS,CAAC,CAAC;EAC5C,CAAC,CAAC,CAAC;;EAGH,IAAIC,eAAe,GAAG7I,KAAK,CAAC8I,QAAQ,CAAC,IAAI,CAAC;IACtCC,gBAAgB,GAAGjJ,cAAc,CAAC+I,eAAe,EAAE,CAAC,CAAC;IACrDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE1C,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,MAAM,EAAE1B,UAAU,EAAE;IAC3E,IAAI,CAACnG,QAAQ,EAAE;MACb,IAAI8H,IAAI,GAAG1D,YAAY,CAACK,SAAS,EAAEoD,MAAM,EAAE1B,UAAU,CAAC;MACtDpF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC6E,eAAe,CAACnB,SAAS,CAAC,CAAC;MAC1GqB,aAAa,CAACgC,IAAI,CAACC,MAAM,CAAC;MAC1B/G,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4E,eAAe,CAACkC,IAAI,CAACC,MAAM,CAAC,CAAC;MACzGJ,gBAAgB,CAACG,IAAI,CAACpH,KAAK,CAAC;IAC9B;EACF,CAAC;EAEDhC,KAAK,CAACsJ,SAAS,CAAC,YAAY;IAC1B,IAAIN,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAIvB,UAAU,GAAG1B,SAAS,CAACwD,OAAO,CAACP,aAAa,CAAC;MAEjD,IAAIvB,UAAU,IAAI,CAAC,EAAE;QACnB3D,UAAU,CAACmD,OAAO,CAACuC,KAAK,CAAC/B,UAAU,CAAC;MACtC;IACF;IAEAwB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB,IAAIS,oBAAoB,GAAGzJ,KAAK,CAACkE,OAAO,CAAC,YAAY;IACnD,IAAIvB,cAAc,IAAI2B,UAAU,KAAK,IAAI,EAAE;MACzC,IAAIoF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCjJ,OAAO,CAAC,KAAK,EAAE,0DAA0D,CAAC;MAC5E;MAEA,OAAO,KAAK;IACd;IAEA,OAAOgC,cAAc;EACvB,CAAC,EAAE,CAACA,cAAc,EAAE2B,UAAU,CAAC,CAAC;EAEhC,IAAIuF,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzCvH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4E,eAAe,CAACF,YAAY,CAACC,OAAO,CAAC,CAAC;EACpH,CAAC;EAED,IAAI6C,QAAQ,GAAGzJ,OAAO,CAAC2D,YAAY,EAAEC,SAAS,EAAE8B,SAAS,EAAE5B,SAAS,EAAEE,SAAS,EAAEoB,WAAW,EAAE2B,aAAa,EAAEyC,YAAY,EAAEnE,YAAY,CAAC;IACpIqE,SAAS,GAAGjK,cAAc,CAACgK,QAAQ,EAAE,CAAC,CAAC;IACvCE,aAAa,GAAGD,SAAS,CAAC,CAAC,CAAC;IAC5BE,aAAa,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC5BG,WAAW,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC1BI,WAAW,GAAGJ,SAAS,CAAC,CAAC,CAAC;EAE9B,IAAIK,WAAW,GAAG,SAASA,WAAWA,CAACrC,CAAC,EAAEN,UAAU,EAAE;IACpD0C,WAAW,CAACpC,CAAC,EAAEN,UAAU,CAAC;IAC1BpF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC6E,eAAe,CAACF,YAAY,CAACC,OAAO,CAAC,CAAC;EACvH,CAAC,CAAC,CAAC;;EAGH,IAAIoD,QAAQ,GAAGL,aAAa,KAAK,CAAC,CAAC;EACnChK,KAAK,CAACsJ,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACe,QAAQ,EAAE;MACb,IAAI5C,UAAU,GAAG1B,SAAS,CAACuE,WAAW,CAACL,aAAa,CAAC;MACrDnG,UAAU,CAACmD,OAAO,CAACuC,KAAK,CAAC/B,UAAU,CAAC;IACtC;EACF,CAAC,EAAE,CAAC4C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAIE,iBAAiB,GAAGvK,KAAK,CAACkE,OAAO,CAAC,YAAY;IAChD,OAAOrE,kBAAkB,CAACqK,WAAW,CAAC,CAAC9E,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC1D,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC4E,WAAW,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,IAAIM,cAAc,GAAGxK,KAAK,CAACkE,OAAO,CAAC,YAAY;MAC7C,IAAI,CAAChC,KAAK,EAAE;QACV,OAAO,CAACiC,SAAS,EAAEoG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC1C;MAEA,OAAO,CAACA,iBAAiB,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAACA,iBAAiB,CAAC7D,MAAM,GAAG,CAAC,CAAC,CAAC;IAChF,CAAC,EAAE,CAAC6D,iBAAiB,EAAErI,KAAK,EAAEiC,SAAS,CAAC,CAAC;IACrCsG,eAAe,GAAG3K,cAAc,CAAC0K,cAAc,EAAE,CAAC,CAAC;IACnDE,aAAa,GAAGD,eAAe,CAAC,CAAC,CAAC;IAClCE,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGtCzK,KAAK,CAAC4K,mBAAmB,CAAC7J,GAAG,EAAE,YAAY;IACzC,OAAO;MACLyI,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB1F,UAAU,CAACmD,OAAO,CAACuC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC;MACDqB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIC,SAAS,GAAGC,QAAQ;UACpBC,aAAa,GAAGF,SAAS,CAACE,aAAa;QAE3C,IAAIhH,YAAY,CAACiD,OAAO,CAACgE,QAAQ,CAACD,aAAa,CAAC,EAAE;UAChDA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACH,IAAI,CAAC,CAAC;QACpF;MACF;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ7K,KAAK,CAACsJ,SAAS,CAAC,YAAY;IAC1B,IAAI/H,SAAS,EAAE;MACbuC,UAAU,CAACmD,OAAO,CAACuC,KAAK,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAI0B,OAAO,GAAGlL,KAAK,CAACkE,OAAO,CAAC,YAAY;IACtC,OAAO;MACLvC,GAAG,EAAEwC,SAAS;MACdtC,GAAG,EAAEwC,SAAS;MACdJ,SAAS,EAAEA,SAAS;MACpB3C,QAAQ,EAAEA,QAAQ;MAClBS,IAAI,EAAEuC,UAAU;MAChBvB,QAAQ,EAAEA,QAAQ;MAClB2H,aAAa,EAAEA,aAAa;MAC5BC,WAAW,EAAEA,WAAW;MACxBzI,KAAK,EAAEA,KAAK;MACZwB,QAAQ,EAAEA,QAAQ;MAClBC,kBAAkB,EAAEA,kBAAkB;MACtCC,uBAAuB,EAAEA,uBAAuB;MAChDC,+BAA+B,EAAEA;IACnC,CAAC;EACH,CAAC,EAAE,CAACM,SAAS,EAAEE,SAAS,EAAEJ,SAAS,EAAE3C,QAAQ,EAAEgD,UAAU,EAAEvB,QAAQ,EAAE2H,aAAa,EAAEC,WAAW,EAAEzI,KAAK,EAAEwB,QAAQ,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,+BAA+B,CAAC,CAAC,CAAC,CAAC;;EAElM,OAAO,aAAa7D,KAAK,CAACmL,aAAa,CAAC7K,aAAa,CAAC8K,QAAQ,EAAE;IAC9DpJ,KAAK,EAAEkJ;EACT,CAAC,EAAE,aAAalL,KAAK,CAACmL,aAAa,CAAC,KAAK,EAAE;IACzCpK,GAAG,EAAEiD,YAAY;IACjB7C,SAAS,EAAElB,UAAU,CAACiB,SAAS,EAAEC,SAAS,GAAGH,WAAW,GAAG,CAAC,CAAC,EAAEpB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACqK,MAAM,CAACnK,SAAS,EAAE,WAAW,CAAC,EAAEI,QAAQ,CAAC,EAAE1B,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACqK,MAAM,CAACnK,SAAS,EAAE,WAAW,CAAC,EAAE2B,QAAQ,CAAC,EAAEjD,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACqK,MAAM,CAACnK,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC2B,QAAQ,CAAC,EAAEjD,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACqK,MAAM,CAACnK,SAAS,EAAE,aAAa,CAAC,EAAEsD,QAAQ,CAACkC,MAAM,CAAC,EAAE1F,WAAW,CAAC,CAAC;IACpYI,KAAK,EAAEA,KAAK;IACZkK,WAAW,EAAExD;EACf,CAAC,EAAE,aAAa9H,KAAK,CAACmL,aAAa,CAAC,KAAK,EAAE;IACzChK,SAAS,EAAE,EAAE,CAACkK,MAAM,CAACnK,SAAS,EAAE,OAAO,CAAC;IACxCE,KAAK,EAAE+B;EACT,CAAC,CAAC,EAAE,aAAanD,KAAK,CAACmL,aAAa,CAAC5K,MAAM,EAAE;IAC3CW,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAE6B,UAAU;IACjBoG,MAAM,EAAEkB,iBAAiB;IACzBvH,UAAU,EAAEA,UAAU;IACtBoH,WAAW,EAAEX,oBAAoB,GAAGW,WAAW,GAAG;EACpD,CAAC,CAAC,EAAE,aAAapK,KAAK,CAACmL,aAAa,CAAC1K,KAAK,EAAE;IAC1CS,SAAS,EAAEA,SAAS;IACpBoC,KAAK,EAAEkB,QAAQ;IACfjB,IAAI,EAAEA,IAAI;IACVnC,KAAK,EAAEgC,QAAQ;IACfmI,WAAW,EAAElI;EACf,CAAC,CAAC,EAAE,aAAarD,KAAK,CAACmL,aAAa,CAAC/K,OAAO,EAAE;IAC5CW,GAAG,EAAE+C,UAAU;IACf5C,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAE8B,WAAW;IAClBmG,MAAM,EAAEa,WAAW;IACnBF,aAAa,EAAEA,aAAa;IAC5BI,WAAW,EAAEA,WAAW;IACxBoB,cAAc,EAAEtC,oBAAoB;IACpC1H,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACd+B,YAAY,EAAEA;EAChB,CAAC,CAAC,EAAE,aAAaxD,KAAK,CAACmL,aAAa,CAAC3K,KAAK,EAAE;IAC1CU,SAAS,EAAEA,SAAS;IACpBoC,KAAK,EAAEkB,QAAQ;IACfiH,OAAO,EAAElE;EACX,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,IAAImC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChJ,MAAM,CAAC8K,WAAW,GAAG,QAAQ;AAC/B;AAEA,eAAe9K,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}