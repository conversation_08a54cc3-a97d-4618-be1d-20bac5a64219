{"ast": null, "code": "/* eslint-disable no-console */\nvar warned = {};\nexport function warning(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.error(\"Warning: \".concat(message));\n  }\n}\nexport function note(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.warn(\"Note: \".concat(message));\n  }\n}\nexport function resetWarned() {\n  warned = {};\n}\nexport function call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\nexport function warningOnce(valid, message) {\n  call(warning, valid, message);\n}\nexport function noteOnce(valid, message) {\n  call(note, valid, message);\n}\nexport default warningOnce;\n/* eslint-enable */", "map": {"version": 3, "names": ["warned", "warning", "valid", "message", "process", "env", "NODE_ENV", "console", "undefined", "error", "concat", "note", "warn", "resetWarned", "call", "method", "warningOnce", "noteOnce"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/warning.js"], "sourcesContent": ["/* eslint-disable no-console */\nvar warned = {};\nexport function warning(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.error(\"Warning: \".concat(message));\n  }\n}\nexport function note(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.warn(\"Note: \".concat(message));\n  }\n}\nexport function resetWarned() {\n  warned = {};\n}\nexport function call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\nexport function warningOnce(valid, message) {\n  call(warning, valid, message);\n}\nexport function noteOnce(valid, message) {\n  call(note, valid, message);\n}\nexport default warningOnce;\n/* eslint-enable */"], "mappings": "AAAA;AACA,IAAIA,MAAM,GAAG,CAAC,CAAC;AACf,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5ED,OAAO,CAACE,KAAK,CAAC,WAAW,CAACC,MAAM,CAACP,OAAO,CAAC,CAAC;EAC5C;AACF;AACA,OAAO,SAASQ,IAAIA,CAACT,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5ED,OAAO,CAACK,IAAI,CAAC,QAAQ,CAACF,MAAM,CAACP,OAAO,CAAC,CAAC;EACxC;AACF;AACA,OAAO,SAASU,WAAWA,CAAA,EAAG;EAC5Bb,MAAM,GAAG,CAAC,CAAC;AACb;AACA,OAAO,SAASc,IAAIA,CAACC,MAAM,EAAEb,KAAK,EAAEC,OAAO,EAAE;EAC3C,IAAI,CAACD,KAAK,IAAI,CAACF,MAAM,CAACG,OAAO,CAAC,EAAE;IAC9BY,MAAM,CAAC,KAAK,EAAEZ,OAAO,CAAC;IACtBH,MAAM,CAACG,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;AACA,OAAO,SAASa,WAAWA,CAACd,KAAK,EAAEC,OAAO,EAAE;EAC1CW,IAAI,CAACb,OAAO,EAAEC,KAAK,EAAEC,OAAO,CAAC;AAC/B;AACA,OAAO,SAASc,QAAQA,CAACf,KAAK,EAAEC,OAAO,EAAE;EACvCW,IAAI,CAACH,IAAI,EAAET,KAAK,EAAEC,OAAO,CAAC;AAC5B;AACA,eAAea,WAAW;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}