{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\utils\\\\caricamento.jsx\";\nimport Loader from \"react-loader-spinner\";\nimport React from 'react';\nimport { <PERSON>nti } from \"../components/traduttore/const\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default class Caricamento extends React.Component {\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"pleaseWait\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"elementLoading\",\n        children: [/*#__PURE__*/_jsxDEV(Loader, {\n          className: \"nowLoading\",\n          type: \"TailSpin\",\n          color: \"#292C4B\",\n          height: 80,\n          width: 80\n          /* timeout={1000} */ //10 secs\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"txtLoading mt-3 mb-0\",\n          children: Costanti.FetchDataAPI\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["Loader", "React", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Caricamento", "Component", "render", "id", "children", "className", "type", "color", "height", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FetchDataAPI"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/utils/caricamento.jsx"], "sourcesContent": ["import Loader from \"react-loader-spinner\";\nimport React from 'react';\nimport { <PERSON><PERSON> } from \"../components/traduttore/const\";\n\nexport default class Caricamento extends React.Component {\n    render() {\n        return (\n            <div id=\"pleaseWait\">\n                <div className=\"elementLoading\">\n                    <Loader\n                        className=\"nowLoading\"\n                        type=\"TailSpin\"\n                        color=\"#292C4B\"\n                        height={80}\n                        width={80}\n                    /* timeout={1000} */ //10 secs\n                    />\n                    <p className=\"txtLoading mt-3 mb-0\">{Costanti.FetchDataAPI}</p>\n                </div>\n            </div>\n        );\n    }\n}"], "mappings": ";AAAA,OAAOA,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,eAAe,MAAMC,WAAW,SAASJ,KAAK,CAACK,SAAS,CAAC;EACrDC,MAAMA,CAAA,EAAG;IACL,oBACIH,OAAA;MAAKI,EAAE,EAAC,YAAY;MAAAC,QAAA,eAChBL,OAAA;QAAKM,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC3BL,OAAA,CAACJ,MAAM;UACHU,SAAS,EAAC,YAAY;UACtBC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,SAAS;UACfC,MAAM,EAAE,EAAG;UACXC,KAAK,EAAE;UACX,qBAAqB;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFd,OAAA;UAAGM,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAAEP,QAAQ,CAACiB;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}