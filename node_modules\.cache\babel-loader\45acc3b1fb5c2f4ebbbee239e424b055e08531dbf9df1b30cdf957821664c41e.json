{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\tabellaOrdiniGiornalieri.jsx\";\nimport React, { Component } from \"react\";\nimport { Costanti } from \"../traduttore/const\";\nimport { APIRequest } from \"./apireq\";\n\n/* GET CURRENT DATE */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar newDate = new Date();\nfunction formatDate(date) {\n  var d = new Date(date),\n    month = '' + (d.getMonth() + 1),\n    day = '' + d.getDate(),\n    year = d.getFullYear();\n  if (month.length < 2) month = '0' + month;\n  if (day.length < 2) day = '0' + day;\n  return [day, month, year].join('/');\n}\nclass OrdiniGiornalieri extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: []\n    };\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    APIRequest('GET', 'orders/?dailystat=true').then(res => {\n      this.setState({\n        results: res.data\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n  }\n  render() {\n    var _this$state$results$, _this$state$results$2;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-sm-12 col-md-12 col-lg-6 mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row counter\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 py-2 counterHead titleBox\",\n            children: [Costanti.DatiGiornalieri, /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatDate(newDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 101\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5 py-2 counterTitle d-flex align-items-center\",\n            children: Costanti.OrdiniRicevuti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-7 py-1 counterData d-flex flex-column justify-content-center\",\n            children: this.state.results.length !== 0 ? (_this$state$results$ = this.state.results[0]) === null || _this$state$results$ === void 0 ? void 0 : _this$state$results$.ordini_ricevuti : 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-5 py-2 counterTitle d-flex align-items-center\",\n            children: Costanti.TotOrdini\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-7 py-1 counterData d-flex flex-column justify-content-center\",\n            children: new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(this.state.results.length !== 0 ? (_this$state$results$2 = this.state.results[0]) === null || _this$state$results$2 === void 0 ? void 0 : _this$state$results$2.fatturato : 0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 py-1 counterFooter d-flex justify-content-end align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"lastUpdate d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-2 textSync\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.dAggiornamento\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-2 textSync\",\n                children: new Intl.DateTimeFormat(\"it-IT\", {\n                  day: '2-digit',\n                  month: '2-digit',\n                  year: 'numeric',\n                  hour: '2-digit',\n                  minute: '2-digit',\n                  second: '2-digit'\n                }).format(new Date(newDate))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"anPulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default OrdiniGiornalieri;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "jsxDEV", "_jsxDEV", "newDate", "Date", "formatDate", "date", "d", "month", "getMonth", "day", "getDate", "year", "getFullYear", "length", "join", "<PERSON><PERSON><PERSON>", "constructor", "props", "state", "results", "componentDidMount", "then", "res", "setState", "data", "catch", "e", "console", "log", "render", "_this$state$results$", "_this$state$results$2", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "OrdiniRicevuti", "ordini_ricevuti", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Intl", "NumberFormat", "style", "currency", "format", "fatturato", "dAggiornamento", "DateTimeFormat", "hour", "minute", "second"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/tabellaOrdiniGiornalieri.jsx"], "sourcesContent": ["import React, { Component } from \"react\";\nimport { <PERSON><PERSON> } from \"../traduttore/const\";\nimport { APIRequest } from \"./apireq\";\n\n/* GET CURRENT DATE */\nvar newDate = new Date();\nfunction formatDate(date) {\n    var d = new Date(date),\n        month = '' + (d.getMonth() + 1),\n        day = '' + d.getDate(),\n        year = d.getFullYear();\n\n    if (month.length < 2)\n        month = '0' + month;\n    if (day.length < 2)\n        day = '0' + day;\n\n    return [day, month, year].join('/');\n}\n\nclass OrdiniGiornalieri extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: []\n        }\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        APIRequest('GET', 'orders/?dailystat=true')\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                })\n            }).catch((e) => {\n                console.log((e))\n            })\n    }\n    render() {\n        return (\n            <div className=\"row mb-4\">\n                <div className=\"col-12 col-sm-12 col-md-12 col-lg-6 mx-auto px-4\">\n                    <div className=\"row counter\">\n                        <div className=\"col-12 py-2 counterHead titleBox\">{Costanti.DatiGiornalieri}<span>{formatDate(newDate)}</span></div>\n                        <div className=\"col-5 py-2 counterTitle d-flex align-items-center\">{Costanti.OrdiniRicevuti}</div>\n                        <div className=\"col-7 py-1 counterData d-flex flex-column justify-content-center\">{this.state.results.length !== 0 ? this.state.results[0]?.ordini_ricevuti : 0}</div>\n                        <div className=\"col-5 py-2 counterTitle d-flex align-items-center\">{Costanti.TotOrdini}</div>\n                        <div className=\"col-7 py-1 counterData d-flex flex-column justify-content-center\">{new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(this.state.results.length !== 0 ? this.state.results[0]?.fatturato : 0)}</div>\n                        <div className=\"col-12 py-1 counterFooter d-flex justify-content-end align-items-center\">\n                            <span className=\"lastUpdate d-flex align-items-center\">\n                                <div className=\"ml-2 textSync\">\n                                    <strong>{Costanti.dAggiornamento}</strong>\n                                </div>\n                                <div className=\"ml-2 textSync\">\n                                    {new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).format(new Date(newDate))}\n                                </div>\n                            </span>\n                            <div className=\"anPulse\"></div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n}\n\nexport default OrdiniGiornalieri;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,UAAU;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;AACxB,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,IAAIC,CAAC,GAAG,IAAIH,IAAI,CAACE,IAAI,CAAC;IAClBE,KAAK,GAAG,EAAE,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/BC,GAAG,GAAG,EAAE,GAAGH,CAAC,CAACI,OAAO,CAAC,CAAC;IACtBC,IAAI,GAAGL,CAAC,CAACM,WAAW,CAAC,CAAC;EAE1B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAChBN,KAAK,GAAG,GAAG,GAAGA,KAAK;EACvB,IAAIE,GAAG,CAACI,MAAM,GAAG,CAAC,EACdJ,GAAG,GAAG,GAAG,GAAGA,GAAG;EAEnB,OAAO,CAACA,GAAG,EAAEF,KAAK,EAAEI,IAAI,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;AACvC;AAEA,MAAMC,iBAAiB,SAASlB,SAAS,CAAC;EACtCmB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE;IACb,CAAC;EACL;EACA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtBrB,UAAU,CAAC,KAAK,EAAE,wBAAwB,CAAC,CACtCsB,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACC,QAAQ,CAAC;QACVJ,OAAO,EAAEG,GAAG,CAACE;MACjB,CAAC,CAAC;IACN,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAAEF,CAAE,CAAC;IACpB,CAAC,CAAC;EACV;EACAG,MAAMA,CAAA,EAAG;IAAA,IAAAC,oBAAA,EAAAC,qBAAA;IACL,oBACI9B,OAAA;MAAK+B,SAAS,EAAC,UAAU;MAAAC,QAAA,eACrBhC,OAAA;QAAK+B,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC7DhC,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBhC,OAAA;YAAK+B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAEnC,QAAQ,CAACoC,eAAe,eAACjC,OAAA;cAAAgC,QAAA,EAAO7B,UAAU,CAACF,OAAO;YAAC;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpHrC,OAAA;YAAK+B,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAEnC,QAAQ,CAACyC;UAAc;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClGrC,OAAA;YAAK+B,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAAE,IAAI,CAACf,KAAK,CAACC,OAAO,CAACN,MAAM,KAAK,CAAC,IAAAiB,oBAAA,GAAG,IAAI,CAACZ,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,cAAAW,oBAAA,uBAArBA,oBAAA,CAAuBU,eAAe,GAAG;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtKrC,OAAA;YAAK+B,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAEnC,QAAQ,CAAC2C;UAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7FrC,OAAA;YAAK+B,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAAE,IAAIS,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC5B,KAAK,CAACC,OAAO,CAACN,MAAM,KAAK,CAAC,IAAAkB,qBAAA,GAAG,IAAI,CAACb,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,cAAAY,qBAAA,uBAArBA,qBAAA,CAAuBgB,SAAS,GAAG,CAAC;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/OrC,OAAA;YAAK+B,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACpFhC,OAAA;cAAM+B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBAClDhC,OAAA;gBAAK+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1BhC,OAAA;kBAAAgC,QAAA,EAASnC,QAAQ,CAACkD;gBAAc;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNrC,OAAA;gBAAK+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EACzB,IAAIS,IAAI,CAACO,cAAc,CAAC,OAAO,EAAE;kBAAExC,GAAG,EAAE,SAAS;kBAAEF,KAAK,EAAE,SAAS;kBAAEI,IAAI,EAAE,SAAS;kBAAEuC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC,CAAC,CAACN,MAAM,CAAC,IAAI3C,IAAI,CAACD,OAAO,CAAC;cAAC;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACPrC,OAAA;cAAK+B,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AAEJ;AAEA,eAAevB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}