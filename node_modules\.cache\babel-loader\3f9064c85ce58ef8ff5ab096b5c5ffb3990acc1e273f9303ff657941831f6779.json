{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\approvvigionamento.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CreaOrdine - operazioni sulla creazione di un ordine per il punto vendita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { InputText } from \"primereact/inputtext\";\nimport { SplitButton } from 'primereact/splitbutton';\nimport { affiliatoRiepilogo } from \"../../components/route\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport Nav from \"../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass Approvvigionamento extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      area: \"\",\n      scaffale: \"\",\n      ripiano: \"\",\n      posizione: \"\",\n      eanCode: \"\"\n    };\n    this.state = {\n      results: null,\n      result: this.emptyResult,\n      resultDialog: false,\n      warehouse: null,\n      selectedProducts: null,\n      selectedRetailer: null,\n      displayed: false,\n      loading: true,\n      retailer: [],\n      selectedWarehouse: null\n    };\n    this.retailer = [];\n    this.warehouse = [];\n    this.unitMisBodyTemplate = this.unitMisBodyTemplate.bind(this);\n    this.colliBodyTemplate = this.colliBodyTemplate.bind(this);\n    this.colliEditor = this.colliEditor.bind(this);\n    this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n    this.redirect = this.redirect.bind(this);\n    this.onRetailerSelect = this.onRetailerSelect.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    await APIRequest(\"GET\", \"retailers/\").then(res => {\n      res.data.forEach(element => {\n        var x = {\n          name: element.idRegistry.firstName,\n          code: element.id\n        };\n        this.retailer.push(x);\n      });\n      this.setState({\n        retailer: res.data\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    await APIRequest(\"GET\", \"warehouses/cross\").then(res => {\n      res.data.forEach(element => {\n        if (element && element.idWarehouse) {\n          var x = {\n            name: element.idWarehouse.warehouseName || 'Magazzino sconosciuto',\n            code: element.idWarehouse.id || 0\n          };\n          this.warehouse.push(x);\n        }\n      });\n      this.setState({\n        warehouse: res.data\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    var idWarehouse = JSON.parse(sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      this.setState({\n        displayed: false,\n        selectedWarehouse: idWarehouse\n      });\n      var url = 'statistic/productpositionstock/?warehouse=' + idWarehouse.code;\n      await APIRequest(\"GET\", url).then(res => {\n        var products = [];\n        res.data.forEach(element => {\n          if (element.giacenza_effettiva > 0) {\n            var x = _objectSpread(_objectSpread({}, element.id_product_packaging.idProduct.supplyingProducts[0]), {}, {\n              id: element.id_product_packaging,\n              idProduct: element.id_product_packaging.idProduct.id,\n              idProduct2: element.id_product_packaging.idProduct,\n              giacenza_effettiva: element.giacenza_effettiva,\n              ordinato_fornitore: element.ordinato_fornitore,\n              idProductsPackaging: element.id_product_packaging,\n              price: '0,00',\n              visibility: true,\n              newColli: 0\n            });\n            products.push(x);\n          }\n        });\n        this.setState({\n          results: products,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n  }\n  /* Reperiamo il codice formato del prodotto */\n  unitMisBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UnitMis\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 17\n      }, this), results.idProductsPackaging.unitMeasure]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo i colli ordinati per il prodotto */\n  colliBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Colli\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this), results.newColli]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 13\n    }, this);\n  }\n  /* InputNumber per la modifica dei colli */\n  colliEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.rowData['newColli'],\n      onValueChange: e => this.onRowEditComplete(e, options)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 16\n    }, this);\n  }\n  /* Modifico quantità del prodotto */\n  onRowEditComplete(e, options, key) {\n    let results = [...this.state.results];\n    options.rowData.newColli = e.value;\n    this.setState({\n      results: results\n    });\n  }\n  selectionChangeHandler(e) {\n    this.setState({\n      selectedProducts: e.value\n    });\n  }\n  onRetailerSelect(e) {\n    this.setState({\n      selectedRetailer: e.value\n    });\n    window.sessionStorage.setItem(\"idRetailer\", JSON.stringify(e.value));\n  }\n  async onWarehouseSelect(e) {\n    this.setState({\n      selectedWarehouse: e.value\n    });\n    sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value));\n    var url = 'statistic/productpositionstock/?warehouse=' + e.value.code;\n    await APIRequest(\"GET\", url).then(res => {\n      var products = [];\n      res.data.forEach(element => {\n        if (element.giacenza_effettiva > 0) {\n          var x = _objectSpread(_objectSpread({}, element.id_product_packaging.idProduct.supplyingProducts[0]), {}, {\n            id: element.id_product_packaging,\n            idProduct: element.id_product_packaging.idProduct.id,\n            idProduct2: element.id_product_packaging.idProduct,\n            giacenza_effettiva: element.giacenza_effettiva,\n            ordinato_fornitore: element.ordinato_fornitore,\n            idProductsPackaging: element.id_product_packaging,\n            price: '0,00',\n            visibility: true,\n            newColli: 0\n          });\n          products.push(x);\n        }\n      });\n      this.setState({\n        results: products,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  redirect() {\n    if (this.state.selectedRetailer !== null) {\n      var find = this.state.retailer.find(el => el.id === this.state.selectedRetailer.code);\n      var filter = this.state.selectedProducts.filter(element => element.newColli > 0);\n      if (find !== undefined && this.state.selectedProducts.length === filter.length) {\n        localStorage.setItem(\"DatiConsegna\", JSON.stringify(find.idRegistry)); // anagrafica retailer\n        localStorage.setItem(\"Cart\", JSON.stringify(this.state.selectedProducts)); // prodotti selezionati\n        window.location.pathname = affiliatoRiepilogo;\n      } else {\n        this.toast.show({\n          severity: \"warn\",\n          summary: \"Attenzione!\",\n          detail: \"Per proseguire con l'approvvigionamento è necessario inserire le quantità ed il formato per i prodotti selezionati\",\n          life: 3000\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"Per proseguire con l'approvvigionamento è necessario inserire il cliente per il quale si vuole effettuare l'approvvigionamento\",\n        life: 3000\n      });\n    }\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    var _this$state$selectedP;\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 13\n    }, this);\n    const items = [{\n      label: Costanti.SpostaMerce,\n      icon: 'pi pi-send',\n      command: () => {\n        this.redirect();\n      }\n    }];\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              className: \"w-100\",\n              type: \"search\",\n              onInput: e => this.setState({\n                globalFilter: e.target.value\n              }),\n              placeholder: \"Cerca...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 21\n        }, this), ((_this$state$selectedP = this.state.selectedProducts) === null || _this$state$selectedP === void 0 ? void 0 : _this$state$selectedP.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: /*#__PURE__*/_jsxDEV(SplitButton, {\n              label: \"Azioni\",\n              icon: \"pi pi-cog\",\n              model: items,\n              className: \"splitButtonGen mr-2 mb-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 13\n    }, this);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DepositoContoTerzi\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activeFilterContainer p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0 w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-home mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 73\n                  }, this), Costanti.Magazzino, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"selWar\",\n                  value: this.state.selectedWarehouse,\n                  options: this.warehouse,\n                  onChange: this.onWarehouseSelect,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona magazzino\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mr-3 mb-0 w-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-user mr-2\",\n                    style: {\n                      'fontSize': '.8em'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 73\n                  }, this), Costanti.cliente]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  value: this.state.selectedRetailer,\n                  options: this.retailer,\n                  onChange: this.onRetailerSelect,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona cliente\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(DataTable, {\n            className: \"p-datatable-responsive-demo editable-prices-table\",\n            ref: el => this.dt = el,\n            value: this.state.results,\n            globalFilter: this.state.globalFilter,\n            header: header,\n            dataKey: \"id\",\n            editMode: \"row\",\n            onRowEditComplete: this.onRowEditComplete,\n            responsiveLayout: \"scroll\",\n            autoLayout: \"true\",\n            paginator: true,\n            rows: 20,\n            rowsPerPageOptions: [10, 20, 50],\n            selection: this.state.selectedProducts,\n            onSelectionChange: e => this.selectionChangeHandler(e),\n            emptyMessage: \"Non ci sono elementi da visualizzare per questo magazzino\",\n            selectionMode: \"checkbox\",\n            csvSeparator: \";\",\n            children: [/*#__PURE__*/_jsxDEV(Column, {\n              selectionMode: \"multiple\",\n              headerStyle: {\n                width: \"3em\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"idProduct2.externalCode\",\n              header: Costanti.exCode,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"idProduct2.description\",\n              header: Costanti.Prodotto,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"idProductsPackaging.unitMeasure\",\n              header: Costanti.UnitMis,\n              body: this.unitMisBodyTemplate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"idProductsPackaging.pcsXPackage\",\n              header: \"Pezzi per package\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"giacenza_effettiva\",\n              header: Costanti.Colli,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"ordinato_fornitore\",\n              header: Costanti.OrdinatoAlFornitore,\n              sortable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              field: \"newColli\",\n              header: Costanti.Quantità,\n              body: this.colliBodyTemplate,\n              editor: options => this.colliEditor(options)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Column, {\n              className: \"modActionColumn\",\n              rowEditor: true,\n              headerStyle: {\n                width: '7rem'\n              },\n              bodyStyle: {\n                textAlign: 'center'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona il magazzino ed il cliente per il quale vuoi effettuare la lavorazione\",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 46\n            }, this), Costanti.cliente]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: this.state.selectedRetailer,\n            options: this.retailer,\n            onChange: this.onRetailerSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona cliente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Approvvigionamento;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "DataTable", "Column", "InputNumber", "Dropdown", "JoyrideGen", "InputText", "SplitButton", "affiliatoRiepilogo", "Dialog", "<PERSON><PERSON>", "Nav", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Approvvigionamento", "constructor", "props", "emptyResult", "id", "area", "scaffale", "<PERSON><PERSON>", "posizione", "eanCode", "state", "results", "result", "resultDialog", "warehouse", "selectedProducts", "<PERSON><PERSON><PERSON><PERSON>", "displayed", "loading", "retailer", "selectedWarehouse", "unitMisBodyTemplate", "bind", "colliBodyTemplate", "colliEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redirect", "onRetailerSelect", "onWarehouseSelect", "closeSelectBefore", "componentDidMount", "then", "res", "data", "for<PERSON>ach", "element", "x", "name", "idRegistry", "firstName", "code", "push", "setState", "catch", "e", "console", "log", "idWarehouse", "warehouseName", "JSON", "parse", "sessionStorage", "getItem", "url", "products", "giacenza_effettiva", "_objectSpread", "id_product_packaging", "idProduct", "supplyingProducts", "idProduct2", "ordinato_fornitore", "idProductsPackaging", "price", "visibility", "new<PERSON><PERSON><PERSON>", "_e$response", "_e$response2", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "children", "className", "UnitMis", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "unitMeasure", "<PERSON><PERSON>", "options", "value", "rowData", "onValueChange", "onRowEditComplete", "key", "window", "setItem", "stringify", "_e$response3", "_e$response4", "find", "el", "filter", "length", "localStorage", "location", "pathname", "render", "_this$state$selectedP", "resultD<PERSON><PERSON><PERSON><PERSON>er", "onClick", "<PERSON><PERSON>", "items", "label", "SpostaMerce", "icon", "command", "header", "type", "onInput", "globalFilter", "target", "placeholder", "model", "ref", "DepositoContoTerzi", "style", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "optionLabel", "cliente", "dt", "dataKey", "editMode", "responsiveLayout", "autoLayout", "paginator", "rows", "rowsPerPageOptions", "selection", "onSelectionChange", "emptyMessage", "selectionMode", "csvSeparator", "headerStyle", "width", "field", "exCode", "sortable", "<PERSON><PERSON><PERSON>", "body", "OrdinatoAlFornitore", "Quantità", "editor", "rowEditor", "bodyStyle", "textAlign", "visible", "Primadiproseguire", "modal", "onHide", "footer", "title", "content"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/approvvigionamento.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CreaOrdine - operazioni sulla creazione di un ordine per il punto vendita\n*\n*/\nimport React, { Component } from \"react\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from \"primereact/dropdown\";\nimport { JoyrideGen } from \"../../components/footer/joyride\";\nimport { InputText } from \"primereact/inputtext\";\nimport { SplitButton } from 'primereact/splitbutton';\nimport { affiliatoRiepilogo } from \"../../components/route\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport Nav from \"../../components/navigation/Nav\";\n\nclass Approvvigionamento extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        area: \"\",\n        scaffale: \"\",\n        ripiano: \"\",\n        posizione: \"\",\n        eanCode: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            result: this.emptyResult,\n            resultDialog: false,\n            warehouse: null,\n            selectedProducts: null,\n            selectedRetailer: null,\n            displayed: false,\n            loading: true,\n            retailer: [],\n            selectedWarehouse: null\n        }\n        this.retailer = []\n        this.warehouse = []\n        this.unitMisBodyTemplate = this.unitMisBodyTemplate.bind(this);\n        this.colliBodyTemplate = this.colliBodyTemplate.bind(this);\n        this.colliEditor = this.colliEditor.bind(this);\n        this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n        this.redirect = this.redirect.bind(this);\n        this.onRetailerSelect = this.onRetailerSelect.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        await APIRequest(\"GET\", \"retailers/\")\n            .then((res) => {\n                res.data.forEach(element => {\n                    var x = {\n                        name: element.idRegistry.firstName,\n                        code: element.id\n                    }\n                    this.retailer.push(x)\n                })\n                this.setState({ retailer: res.data })\n            }).catch((e) => {\n                console.log(e)\n            })\n        await APIRequest(\"GET\", \"warehouses/cross\")\n            .then((res) => {\n                res.data.forEach(element => {\n                    if (element && element.idWarehouse) {\n                        var x = {\n                            name: element.idWarehouse.warehouseName || 'Magazzino sconosciuto',\n                            code: element.idWarehouse.id || 0\n                        }\n                        this.warehouse.push(x)\n                    }\n                })\n                this.setState({ warehouse: res.data })\n            }).catch((e) => {\n                console.log(e)\n            })\n        var idWarehouse = JSON.parse(sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            this.setState({ displayed: false, selectedWarehouse: idWarehouse })\n            var url = 'statistic/productpositionstock/?warehouse=' + idWarehouse.code\n            await APIRequest(\"GET\", url)\n                .then(res => {\n                    var products = []\n                    res.data.forEach(element => {\n                        if (element.giacenza_effettiva > 0) {\n                            var x = {\n                                ...element.id_product_packaging.idProduct.supplyingProducts[0],\n                                id: element.id_product_packaging,\n                                idProduct: element.id_product_packaging.idProduct.id,\n                                idProduct2: element.id_product_packaging.idProduct,\n                                giacenza_effettiva: element.giacenza_effettiva,\n                                ordinato_fornitore: element.ordinato_fornitore,\n                                idProductsPackaging: element.id_product_packaging,\n                                price: '0,00',\n                                visibility: true,\n                                newColli: 0\n                            }\n                            products.push(x)\n                        }\n                    })\n                    this.setState({\n                        results: products,\n                        loading: false\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                })\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n    }\n    /* Reperiamo il codice formato del prodotto */\n    unitMisBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UnitMis}</span>\n                {results.idProductsPackaging.unitMeasure}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    colliBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Colli}</span>\n                {results.newColli}\n            </React.Fragment>\n        );\n    }\n    /* InputNumber per la modifica dei colli */\n    colliEditor(options) {\n        return <InputNumber value={options.rowData['newColli']} onValueChange={(e) => this.onRowEditComplete(e, options)} />\n\n    }\n    /* Modifico quantità del prodotto */\n    onRowEditComplete(e, options, key) {\n        let results = [...this.state.results];\n        options.rowData.newColli = e.value\n        this.setState({ results: results });\n    }\n    selectionChangeHandler(e) {\n        this.setState({ selectedProducts: e.value })\n    }\n    onRetailerSelect(e) {\n        this.setState({ selectedRetailer: e.value })\n        window.sessionStorage.setItem(\"idRetailer\", JSON.stringify(e.value));\n    }\n    async onWarehouseSelect(e) {\n        this.setState({ selectedWarehouse: e.value })\n        sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value))\n        var url = 'statistic/productpositionstock/?warehouse=' + e.value.code\n        await APIRequest(\"GET\", url)\n            .then(res => {\n                var products = []\n                res.data.forEach(element => {\n                    if (element.giacenza_effettiva > 0) {\n                        var x = {\n                            ...element.id_product_packaging.idProduct.supplyingProducts[0],\n                            id: element.id_product_packaging,\n                            idProduct: element.id_product_packaging.idProduct.id,\n                            idProduct2: element.id_product_packaging.idProduct,\n                            giacenza_effettiva: element.giacenza_effettiva,\n                            ordinato_fornitore: element.ordinato_fornitore,\n                            idProductsPackaging: element.id_product_packaging,\n                            price: '0,00',\n                            visibility: true,\n                            newColli: 0\n                        }\n                        products.push(x)\n                    }\n                })\n                this.setState({\n                    results: products,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    redirect() {\n        if (this.state.selectedRetailer !== null) {\n            var find = this.state.retailer.find(el => el.id === this.state.selectedRetailer.code)\n            var filter = this.state.selectedProducts.filter(element => element.newColli > 0)\n            if (find !== undefined && this.state.selectedProducts.length === filter.length) {\n                localStorage.setItem(\"DatiConsegna\", JSON.stringify(find.idRegistry)); // anagrafica retailer\n                localStorage.setItem(\"Cart\", JSON.stringify(this.state.selectedProducts)); // prodotti selezionati\n                window.location.pathname = affiliatoRiepilogo\n            } else {\n                this.toast.show({\n                    severity: \"warn\",\n                    summary: \"Attenzione!\",\n                    detail: \"Per proseguire con l'approvvigionamento è necessario inserire le quantità ed il formato per i prodotti selezionati\",\n                    life: 3000,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"Per proseguire con l'approvvigionamento è necessario inserire il cliente per il quale si vuole effettuare l'approvvigionamento\",\n                life: 3000,\n            });\n        }\n\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const items = [\n            {\n                label: Costanti.SpostaMerce,\n                icon: 'pi pi-send',\n                command: () => {\n                    this.redirect()\n                }\n            },\n        ]\n        const header = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <i className=\"pi pi-search mr-2\" />\n                            <InputText className=\"w-100\" type=\"search\" onInput={(e) => this.setState({ globalFilter: e.target.value })} placeholder=\"Cerca...\" />\n                        </span>\n                    </div>\n                    {this.state.selectedProducts?.length > 0 &&\n                        <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                            <div className=\"d-flex justify-content-end\">\n                                <SplitButton label=\"Azioni\" icon='pi pi-cog' model={items} className=\"splitButtonGen mr-2 mb-0\"></SplitButton>\n                                {/* <Button className=\"p-button w-auto mb-0\" onClick={() => this.redirect()}><i className=\"pi pi-check mr-2\"></i>{Costanti.Prosegui}</Button> */}\n                            </div>\n                        </div>\n                    }\n                </div>\n            </div>\n        );\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DepositoContoTerzi}</h1>\n                </div>\n                {this.state.selectedWarehouse &&\n                    <>\n                        <div className='activeFilterContainer p-2'>\n                            <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" />\n                                    </div>\n                                </li>\n                                <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                    <div className='d-flex justify-content-center align-items-center'>\n                                        <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.cliente}</h5>\n                                        <Dropdown value={this.state.selectedRetailer} options={this.retailer} onChange={this.onRetailerSelect} optionLabel=\"name\" placeholder=\"Seleziona cliente\" />\n                                    </div>\n                                </li>\n                            </ul>\n                        </div>\n                        <div className=\"card\">\n                            <DataTable\n                                className=\"p-datatable-responsive-demo editable-prices-table\"\n                                ref={(el) => this.dt = el}\n                                value={this.state.results}\n                                globalFilter={this.state.globalFilter}\n                                header={header}\n                                dataKey=\"id\"\n                                editMode=\"row\"\n                                onRowEditComplete={this.onRowEditComplete}\n                                responsiveLayout=\"scroll\"\n                                autoLayout=\"true\"\n                                paginator\n                                rows={20}\n                                rowsPerPageOptions={[10, 20, 50]}\n                                selection={this.state.selectedProducts}\n                                onSelectionChange={(e) => this.selectionChangeHandler(e)}\n                                emptyMessage=\"Non ci sono elementi da visualizzare per questo magazzino\"\n                                selectionMode='checkbox'\n                                csvSeparator=\";\"\n                            >\n                                <Column selectionMode=\"multiple\" headerStyle={{ width: \"3em\" }} ></Column>\n                                <Column field=\"idProduct2.externalCode\" header={Costanti.exCode} sortable ></Column>\n                                <Column field=\"idProduct2.description\" header={Costanti.Prodotto} sortable ></Column>\n                                <Column field=\"idProductsPackaging.unitMeasure\" header={Costanti.UnitMis} body={this.unitMisBodyTemplate} ></Column>\n                                <Column field=\"idProductsPackaging.pcsXPackage\" header=\"Pezzi per package\" ></Column>\n                                <Column field=\"giacenza_effettiva\" header={Costanti.Colli} sortable></Column>\n                                <Column field=\"ordinato_fornitore\" header={Costanti.OrdinatoAlFornitore} sortable></Column>\n                                <Column field=\"newColli\" header={Costanti.Quantità} body={this.colliBodyTemplate} editor={(options) => this.colliEditor(options)}  ></Column>\n                                <Column className=\"modActionColumn\" rowEditor headerStyle={{ width: '7rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                            </DataTable>\n                        </div>\n                    </>\n                }\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona il magazzino ed il cliente per il quale vuoi effettuare la lavorazione' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" />\n                    </div>\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.cliente}</h5>\n                        <hr></hr>\n                        <Dropdown value={this.state.selectedRetailer} options={this.retailer} onChange={this.onRetailerSelect} optionLabel=\"name\" placeholder=\"Seleziona cliente\" />\n                    </div>\n                </Dialog>\n            </div>\n        )\n    }\n}\n\nexport default Approvvigionamento;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,kBAAkB,SAASnB,SAAS,CAAC;EAUvCoB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAVhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;IACb,CAAC;IAGG,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI,CAACT,WAAW;MACxBU,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,EAAE;MACZC,iBAAiB,EAAE;IACvB,CAAC;IACD,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACL,SAAS,GAAG,EAAE;IACnB,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACH,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACL,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACO,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACP,IAAI,CAAC,IAAI,CAAC;EAC9D;EACA;EACA,MAAMQ,iBAAiBA,CAAA,EAAG;IACtB,MAAM9C,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC+C,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIC,CAAC,GAAG;UACJC,IAAI,EAAEF,OAAO,CAACG,UAAU,CAACC,SAAS;UAClCC,IAAI,EAAEL,OAAO,CAAC/B;QAClB,CAAC;QACD,IAAI,CAACe,QAAQ,CAACsB,IAAI,CAACL,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAACM,QAAQ,CAAC;QAAEvB,QAAQ,EAAEa,GAAG,CAACC;MAAK,CAAC,CAAC;IACzC,CAAC,CAAC,CAACU,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,MAAM5D,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC,CACtC+C,IAAI,CAAEC,GAAG,IAAK;MACXA,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACY,WAAW,EAAE;UAChC,IAAIX,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACY,WAAW,CAACC,aAAa,IAAI,uBAAuB;YAClER,IAAI,EAAEL,OAAO,CAACY,WAAW,CAAC3C,EAAE,IAAI;UACpC,CAAC;UACD,IAAI,CAACU,SAAS,CAAC2B,IAAI,CAACL,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;MACF,IAAI,CAACM,QAAQ,CAAC;QAAE5B,SAAS,EAAEkB,GAAG,CAACC;MAAK,CAAC,CAAC;IAC1C,CAAC,CAAC,CAACU,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAIG,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;IACnE,IAAIL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI,CAACL,QAAQ,CAAC;QAAEzB,SAAS,EAAE,KAAK;QAAEG,iBAAiB,EAAE2B;MAAY,CAAC,CAAC;MACnE,IAAIM,GAAG,GAAG,4CAA4C,GAAGN,WAAW,CAACP,IAAI;MACzE,MAAMxD,UAAU,CAAC,KAAK,EAAEqE,GAAG,CAAC,CACvBtB,IAAI,CAACC,GAAG,IAAI;QACT,IAAIsB,QAAQ,GAAG,EAAE;QACjBtB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIA,OAAO,CAACoB,kBAAkB,GAAG,CAAC,EAAE;YAChC,IAAInB,CAAC,GAAAoB,aAAA,CAAAA,aAAA,KACErB,OAAO,CAACsB,oBAAoB,CAACC,SAAS,CAACC,iBAAiB,CAAC,CAAC,CAAC;cAC9DvD,EAAE,EAAE+B,OAAO,CAACsB,oBAAoB;cAChCC,SAAS,EAAEvB,OAAO,CAACsB,oBAAoB,CAACC,SAAS,CAACtD,EAAE;cACpDwD,UAAU,EAAEzB,OAAO,CAACsB,oBAAoB,CAACC,SAAS;cAClDH,kBAAkB,EAAEpB,OAAO,CAACoB,kBAAkB;cAC9CM,kBAAkB,EAAE1B,OAAO,CAAC0B,kBAAkB;cAC9CC,mBAAmB,EAAE3B,OAAO,CAACsB,oBAAoB;cACjDM,KAAK,EAAE,MAAM;cACbC,UAAU,EAAE,IAAI;cAChBC,QAAQ,EAAE;YAAC,EACd;YACDX,QAAQ,CAACb,IAAI,CAACL,CAAC,CAAC;UACpB;QACJ,CAAC,CAAC;QACF,IAAI,CAACM,QAAQ,CAAC;UACV/B,OAAO,EAAE2C,QAAQ;UACjBpC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACyB,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAsB,WAAA,EAAAC,YAAA;QACZtB,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;QACd,IAAI,CAACwB,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAP,WAAA,GAAAtB,CAAC,CAAC8B,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYjC,IAAI,MAAK0C,SAAS,IAAAR,YAAA,GAAGvB,CAAC,CAAC8B,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYlC,IAAI,GAAGW,CAAC,CAACgC,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACnC,QAAQ,CAAC;QAAE7B,YAAY,EAAE,IAAI;QAAEI,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;EACJ;EACA;EACAI,mBAAmBA,CAACV,OAAO,EAAE;IACzB,oBACId,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAgF,QAAA,gBACXjF,OAAA;QAAMkF,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE/F,QAAQ,CAACiG;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDzE,OAAO,CAACmD,mBAAmB,CAACuB,WAAW;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAEzB;EACA;EACA7D,iBAAiBA,CAACZ,OAAO,EAAE;IACvB,oBACId,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAgF,QAAA,gBACXjF,OAAA;QAAMkF,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE/F,QAAQ,CAACuG;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvDzE,OAAO,CAACsD,QAAQ;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACA;EACA5D,WAAWA,CAAC+D,OAAO,EAAE;IACjB,oBAAO1F,OAAA,CAACV,WAAW;MAACqG,KAAK,EAAED,OAAO,CAACE,OAAO,CAAC,UAAU,CAAE;MAACC,aAAa,EAAG9C,CAAC,IAAK,IAAI,CAAC+C,iBAAiB,CAAC/C,CAAC,EAAE2C,OAAO;IAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAExH;EACA;EACAO,iBAAiBA,CAAC/C,CAAC,EAAE2C,OAAO,EAAEK,GAAG,EAAE;IAC/B,IAAIjF,OAAO,GAAG,CAAC,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAAC;IACrC4E,OAAO,CAACE,OAAO,CAACxB,QAAQ,GAAGrB,CAAC,CAAC4C,KAAK;IAClC,IAAI,CAAC9C,QAAQ,CAAC;MAAE/B,OAAO,EAAEA;IAAQ,CAAC,CAAC;EACvC;EACAc,sBAAsBA,CAACmB,CAAC,EAAE;IACtB,IAAI,CAACF,QAAQ,CAAC;MAAE3B,gBAAgB,EAAE6B,CAAC,CAAC4C;IAAM,CAAC,CAAC;EAChD;EACA7D,gBAAgBA,CAACiB,CAAC,EAAE;IAChB,IAAI,CAACF,QAAQ,CAAC;MAAE1B,gBAAgB,EAAE4B,CAAC,CAAC4C;IAAM,CAAC,CAAC;IAC5CK,MAAM,CAAC1C,cAAc,CAAC2C,OAAO,CAAC,YAAY,EAAE7C,IAAI,CAAC8C,SAAS,CAACnD,CAAC,CAAC4C,KAAK,CAAC,CAAC;EACxE;EACA,MAAM5D,iBAAiBA,CAACgB,CAAC,EAAE;IACvB,IAAI,CAACF,QAAQ,CAAC;MAAEtB,iBAAiB,EAAEwB,CAAC,CAAC4C;IAAM,CAAC,CAAC;IAC7CrC,cAAc,CAAC2C,OAAO,CAAC,aAAa,EAAE7C,IAAI,CAAC8C,SAAS,CAACnD,CAAC,CAAC4C,KAAK,CAAC,CAAC;IAC9D,IAAInC,GAAG,GAAG,4CAA4C,GAAGT,CAAC,CAAC4C,KAAK,CAAChD,IAAI;IACrE,MAAMxD,UAAU,CAAC,KAAK,EAAEqE,GAAG,CAAC,CACvBtB,IAAI,CAACC,GAAG,IAAI;MACT,IAAIsB,QAAQ,GAAG,EAAE;MACjBtB,GAAG,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,CAACoB,kBAAkB,GAAG,CAAC,EAAE;UAChC,IAAInB,CAAC,GAAAoB,aAAA,CAAAA,aAAA,KACErB,OAAO,CAACsB,oBAAoB,CAACC,SAAS,CAACC,iBAAiB,CAAC,CAAC,CAAC;YAC9DvD,EAAE,EAAE+B,OAAO,CAACsB,oBAAoB;YAChCC,SAAS,EAAEvB,OAAO,CAACsB,oBAAoB,CAACC,SAAS,CAACtD,EAAE;YACpDwD,UAAU,EAAEzB,OAAO,CAACsB,oBAAoB,CAACC,SAAS;YAClDH,kBAAkB,EAAEpB,OAAO,CAACoB,kBAAkB;YAC9CM,kBAAkB,EAAE1B,OAAO,CAAC0B,kBAAkB;YAC9CC,mBAAmB,EAAE3B,OAAO,CAACsB,oBAAoB;YACjDM,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE;UAAC,EACd;UACDX,QAAQ,CAACb,IAAI,CAACL,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;MACF,IAAI,CAACM,QAAQ,CAAC;QACV/B,OAAO,EAAE2C,QAAQ;QACjBpC,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CAACyB,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAoD,YAAA,EAAAC,YAAA;MACZpD,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACd,IAAI,CAACwB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,4FAAAC,MAAA,CAAyF,EAAAuB,YAAA,GAAApD,CAAC,CAAC8B,QAAQ,cAAAsB,YAAA,uBAAVA,YAAA,CAAY/D,IAAI,MAAK0C,SAAS,IAAAsB,YAAA,GAAGrD,CAAC,CAAC8B,QAAQ,cAAAuB,YAAA,uBAAVA,YAAA,CAAYhE,IAAI,GAAGW,CAAC,CAACgC,OAAO,CAAE;QAC9JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAnD,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAChB,KAAK,CAACM,gBAAgB,KAAK,IAAI,EAAE;MACtC,IAAIkF,IAAI,GAAG,IAAI,CAACxF,KAAK,CAACS,QAAQ,CAAC+E,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC/F,EAAE,KAAK,IAAI,CAACM,KAAK,CAACM,gBAAgB,CAACwB,IAAI,CAAC;MACrF,IAAI4D,MAAM,GAAG,IAAI,CAAC1F,KAAK,CAACK,gBAAgB,CAACqF,MAAM,CAACjE,OAAO,IAAIA,OAAO,CAAC8B,QAAQ,GAAG,CAAC,CAAC;MAChF,IAAIiC,IAAI,KAAKvB,SAAS,IAAI,IAAI,CAACjE,KAAK,CAACK,gBAAgB,CAACsF,MAAM,KAAKD,MAAM,CAACC,MAAM,EAAE;QAC5EC,YAAY,CAACR,OAAO,CAAC,cAAc,EAAE7C,IAAI,CAAC8C,SAAS,CAACG,IAAI,CAAC5D,UAAU,CAAC,CAAC,CAAC,CAAC;QACvEgE,YAAY,CAACR,OAAO,CAAC,MAAM,EAAE7C,IAAI,CAAC8C,SAAS,CAAC,IAAI,CAACrF,KAAK,CAACK,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC3E8E,MAAM,CAACU,QAAQ,CAACC,QAAQ,GAAGhH,kBAAkB;MACjD,CAAC,MAAM;QACH,IAAI,CAAC4E,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,aAAa;UACtBC,MAAM,EAAE,oHAAoH;UAC5HK,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,gIAAgI;QACxIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EAEJ;EACAhD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnB,KAAK,CAACU,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACsB,QAAQ,CAAC;QACV7B,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACuD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA4B,MAAMA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACL,MAAMC,kBAAkB,gBACpB9G,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAgF,QAAA,eACXjF,OAAA;QAAKkF,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DjF,OAAA,CAACH,MAAM;UAACqF,SAAS,EAAC,0BAA0B;UAAC6B,OAAO,EAAE,IAAI,CAAC/E,iBAAkB;UAAAiD,QAAA,GAAE,GAAC,EAAC/F,QAAQ,CAAC8H,MAAM,EAAC,GAAC;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAM0B,KAAK,GAAG,CACV;MACIC,KAAK,EAAEhI,QAAQ,CAACiI,WAAW;MAC3BC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACxF,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,CACJ;IACD,MAAMyF,MAAM,gBACRtH,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAD,QAAA,eAC3BjF,OAAA;QAAKkF,SAAS,EAAC,2DAA2D;QAAAD,QAAA,gBACtEjF,OAAA;UAAKkF,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eACzCjF,OAAA;YAAMkF,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAC/CjF,OAAA;cAAGkF,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCvF,OAAA,CAACP,SAAS;cAACyF,SAAS,EAAC,OAAO;cAACqC,IAAI,EAAC,QAAQ;cAACC,OAAO,EAAGzE,CAAC,IAAK,IAAI,CAACF,QAAQ,CAAC;gBAAE4E,YAAY,EAAE1E,CAAC,CAAC2E,MAAM,CAAC/B;cAAM,CAAC,CAAE;cAACgC,WAAW,EAAC;YAAU;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL,EAAAsB,qBAAA,OAAI,CAAChG,KAAK,CAACK,gBAAgB,cAAA2F,qBAAA,uBAA3BA,qBAAA,CAA6BL,MAAM,IAAG,CAAC,iBACpCxG,OAAA;UAAKkF,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eACzCjF,OAAA;YAAKkF,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACvCjF,OAAA,CAACN,WAAW;cAACwH,KAAK,EAAC,QAAQ;cAACE,IAAI,EAAC,WAAW;cAACQ,KAAK,EAAEX,KAAM;cAAC/B,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE7G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD,oBACIvF,OAAA;MAAKkF,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CjF,OAAA,CAACf,KAAK;QAAC4I,GAAG,EAAGvB,EAAE,IAAK,IAAI,CAAC/B,KAAK,GAAG+B;MAAG;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCvF,OAAA,CAACF,GAAG;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPvF,OAAA;QAAKkF,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCjF,OAAA;UAAAiF,QAAA,EAAK/F,QAAQ,CAAC4I;QAAkB;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,EACL,IAAI,CAAC1E,KAAK,CAACU,iBAAiB,iBACzBvB,OAAA,CAAAE,SAAA;QAAA+E,QAAA,gBACIjF,OAAA;UAAKkF,SAAS,EAAC,2BAA2B;UAAAD,QAAA,eACtCjF,OAAA;YAAIkF,SAAS,EAAC,4DAA4D;YAAAD,QAAA,gBACtEjF,OAAA;cAAIkF,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjEjF,OAAA;gBAAKkF,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7DjF,OAAA;kBAAIkF,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAACjF,OAAA;oBAAGkF,SAAS,EAAC,iBAAiB;oBAAC6C,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAACrG,QAAQ,CAAC8I,SAAS,EAAC,GAAC;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5HvF,OAAA,CAACT,QAAQ;kBAAC2F,SAAS,EAAC,QAAQ;kBAACS,KAAK,EAAE,IAAI,CAAC9E,KAAK,CAACU,iBAAkB;kBAACmE,OAAO,EAAE,IAAI,CAACzE,SAAU;kBAACgH,QAAQ,EAAE,IAAI,CAAClG,iBAAkB;kBAACmG,WAAW,EAAC,MAAM;kBAACP,WAAW,EAAC;gBAAqB;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACLvF,OAAA;cAAIkF,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACjEjF,OAAA;gBAAKkF,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,gBAC7DjF,OAAA;kBAAIkF,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAACjF,OAAA;oBAAGkF,SAAS,EAAC,iBAAiB;oBAAC6C,KAAK,EAAE;sBAAE,UAAU,EAAE;oBAAO;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAACrG,QAAQ,CAACiJ,OAAO;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzHvF,OAAA,CAACT,QAAQ;kBAACoG,KAAK,EAAE,IAAI,CAAC9E,KAAK,CAACM,gBAAiB;kBAACuE,OAAO,EAAE,IAAI,CAACpE,QAAS;kBAAC2G,QAAQ,EAAE,IAAI,CAACnG,gBAAiB;kBAACoG,WAAW,EAAC,MAAM;kBAACP,WAAW,EAAC;gBAAmB;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvF,OAAA;UAAKkF,SAAS,EAAC,MAAM;UAAAD,QAAA,eACjBjF,OAAA,CAACZ,SAAS;YACN8F,SAAS,EAAC,mDAAmD;YAC7D2C,GAAG,EAAGvB,EAAE,IAAK,IAAI,CAAC8B,EAAE,GAAG9B,EAAG;YAC1BX,KAAK,EAAE,IAAI,CAAC9E,KAAK,CAACC,OAAQ;YAC1B2G,YAAY,EAAE,IAAI,CAAC5G,KAAK,CAAC4G,YAAa;YACtCH,MAAM,EAAEA,MAAO;YACfe,OAAO,EAAC,IAAI;YACZC,QAAQ,EAAC,KAAK;YACdxC,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;YAC1CyC,gBAAgB,EAAC,QAAQ;YACzBC,UAAU,EAAC,MAAM;YACjBC,SAAS;YACTC,IAAI,EAAE,EAAG;YACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;YACjCC,SAAS,EAAE,IAAI,CAAC/H,KAAK,CAACK,gBAAiB;YACvC2H,iBAAiB,EAAG9F,CAAC,IAAK,IAAI,CAACnB,sBAAsB,CAACmB,CAAC,CAAE;YACzD+F,YAAY,EAAC,2DAA2D;YACxEC,aAAa,EAAC,UAAU;YACxBC,YAAY,EAAC,GAAG;YAAA/D,QAAA,gBAEhBjF,OAAA,CAACX,MAAM;cAAC0J,aAAa,EAAC,UAAU;cAACE,WAAW,EAAE;gBAAEC,KAAK,EAAE;cAAM;YAAE;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1EvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,yBAAyB;cAAC7B,MAAM,EAAEpI,QAAQ,CAACkK,MAAO;cAACC,QAAQ;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpFvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,wBAAwB;cAAC7B,MAAM,EAAEpI,QAAQ,CAACoK,QAAS;cAACD,QAAQ;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrFvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,iCAAiC;cAAC7B,MAAM,EAAEpI,QAAQ,CAACiG,OAAQ;cAACoE,IAAI,EAAE,IAAI,CAAC/H;YAAoB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpHvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,iCAAiC;cAAC7B,MAAM,EAAC;YAAmB;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrFvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,oBAAoB;cAAC7B,MAAM,EAAEpI,QAAQ,CAACuG,KAAM;cAAC4D,QAAQ;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC7EvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,oBAAoB;cAAC7B,MAAM,EAAEpI,QAAQ,CAACsK,mBAAoB;cAACH,QAAQ;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC3FvF,OAAA,CAACX,MAAM;cAAC8J,KAAK,EAAC,UAAU;cAAC7B,MAAM,EAAEpI,QAAQ,CAACuK,QAAS;cAACF,IAAI,EAAE,IAAI,CAAC7H,iBAAkB;cAACgI,MAAM,EAAGhE,OAAO,IAAK,IAAI,CAAC/D,WAAW,CAAC+D,OAAO;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7IvF,OAAA,CAACX,MAAM;cAAC6F,SAAS,EAAC,iBAAiB;cAACyE,SAAS;cAACV,WAAW,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAACU,SAAS,EAAE;gBAAEC,SAAS,EAAE;cAAS;YAAE;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA,eACR,CAAC,eAEPvF,OAAA,CAACJ,MAAM;QAACkK,OAAO,EAAE,IAAI,CAACjJ,KAAK,CAACG,YAAa;QAACsG,MAAM,EAAEpI,QAAQ,CAAC6K,iBAAkB;QAACC,KAAK;QAAC9E,SAAS,EAAC,kBAAkB;QAAC+E,MAAM,EAAE,IAAI,CAACjI,iBAAkB;QAACkI,MAAM,EAAEpD,kBAAmB;QAAA7B,QAAA,GACvK,IAAI,CAACpE,KAAK,CAACO,SAAS,iBACjBpB,OAAA,CAACR,UAAU;UAAC2K,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,kFAAkF;UAAC1C,MAAM,EAAC;QAAS;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzJvF,OAAA;UAAKkF,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DjF,OAAA;YAAIkF,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACjF,OAAA;cAAGkF,SAAS,EAAC,iBAAiB;cAAC6C,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACrG,QAAQ,CAAC8I,SAAS;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChHvF,OAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvF,OAAA,CAACT,QAAQ;YAAC2F,SAAS,EAAC,QAAQ;YAACS,KAAK,EAAE,IAAI,CAAC9E,KAAK,CAACU,iBAAkB;YAACmE,OAAO,EAAE,IAAI,CAACzE,SAAU;YAACgH,QAAQ,EAAE,IAAI,CAAClG,iBAAkB;YAACmG,WAAW,EAAC,MAAM;YAACP,WAAW,EAAC;UAAqB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnL,CAAC,eACNvF,OAAA;UAAKkF,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DjF,OAAA;YAAIkF,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACjF,OAAA;cAAGkF,SAAS,EAAC,iBAAiB;cAAC6C,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACrG,QAAQ,CAACiJ,OAAO;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9GvF,OAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvF,OAAA,CAACT,QAAQ;YAACoG,KAAK,EAAE,IAAI,CAAC9E,KAAK,CAACM,gBAAiB;YAACuE,OAAO,EAAE,IAAI,CAACpE,QAAS;YAAC2G,QAAQ,EAAE,IAAI,CAACnG,gBAAiB;YAACoG,WAAW,EAAC,MAAM;YAACP,WAAW,EAAC;UAAmB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAepF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}