{"author": "GitHub Inc.", "name": "which", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "version": "4.0.0", "repository": {"type": "git", "url": "https://github.com/npm/node-which.git"}, "main": "lib/index.js", "bin": {"node-which": "./bin/which.js"}, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.18.0", "tap": "^16.3.0"}, "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "files": ["bin/", "lib/"], "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "ciVersions": ["16.13.0", "16.x", "18.0.0", "18.x"], "version": "4.18.0", "publish": "true"}}