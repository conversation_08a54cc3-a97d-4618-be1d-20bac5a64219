{"ast": null, "code": "import canUseDom from './canUseDom';\nvar MARK_KEY = \"rc-util-key\";\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    mark = _ref.mark;\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  var head = document.querySelector('head');\n  return head || document.body;\n}\nexport function injectCSS(css) {\n  var _option$csp;\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!canUseDom()) {\n    return null;\n  }\n  var styleNode = document.createElement('style');\n  if ((_option$csp = option.csp) === null || _option$csp === void 0 ? void 0 : _option$csp.nonce) {\n    var _option$csp2;\n    styleNode.nonce = (_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce;\n  }\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n  if (option.prepend && container.prepend) {\n    // Use `prepend` first\n    container.prepend(styleNode);\n  } else if (option.prepend && firstChild) {\n    // Fallback to `insertBefore` like IE not support `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nvar containerCache = new Map();\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return Array.from(containerCache.get(container).children).find(function (node) {\n    return node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key;\n  });\n}\nexport function removeCSS(key) {\n  var _existNode$parentNode;\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n  existNode === null || existNode === void 0 ? void 0 : (_existNode$parentNode = existNode.parentNode) === null || _existNode$parentNode === void 0 ? void 0 : _existNode$parentNode.removeChild(existNode);\n}\nexport function updateCSS(css, key) {\n  var option = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(option); // Get real parent\n\n  if (!containerCache.has(container)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    parentNode.removeChild(placeholderStyle);\n  }\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var _option$csp3, _option$csp4;\n    if (((_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce) && existNode.nonce !== ((_option$csp4 = option.csp) === null || _option$csp4 === void 0 ? void 0 : _option$csp4.nonce)) {\n      var _option$csp5;\n      existNode.nonce = (_option$csp5 = option.csp) === null || _option$csp5 === void 0 ? void 0 : _option$csp5.nonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}", "map": {"version": 3, "names": ["canUseDom", "MARK_KEY", "getMark", "_ref", "arguments", "length", "undefined", "mark", "startsWith", "concat", "getContainer", "option", "attachTo", "head", "document", "querySelector", "body", "injectCSS", "css", "_option$csp", "styleNode", "createElement", "csp", "nonce", "_option$csp2", "innerHTML", "container", "<PERSON><PERSON><PERSON><PERSON>", "prepend", "insertBefore", "append<PERSON><PERSON><PERSON>", "containerCache", "Map", "findExistNode", "key", "Array", "from", "get", "children", "find", "node", "tagName", "getAttribute", "removeCSS", "_existNode$parentNode", "existNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "updateCSS", "has", "placeholder<PERSON><PERSON><PERSON>", "set", "_option$csp3", "_option$csp4", "_option$csp5", "newNode", "setAttribute"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/dynamicCSS.js"], "sourcesContent": ["import canUseDom from './canUseDom';\nvar MARK_KEY = \"rc-util-key\";\n\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      mark = _ref.mark;\n\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n\n  return MARK_KEY;\n}\n\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n\n  var head = document.querySelector('head');\n  return head || document.body;\n}\n\nexport function injectCSS(css) {\n  var _option$csp;\n\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!canUseDom()) {\n    return null;\n  }\n\n  var styleNode = document.createElement('style');\n\n  if ((_option$csp = option.csp) === null || _option$csp === void 0 ? void 0 : _option$csp.nonce) {\n    var _option$csp2;\n\n    styleNode.nonce = (_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce;\n  }\n\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n\n  if (option.prepend && container.prepend) {\n    // Use `prepend` first\n    container.prepend(styleNode);\n  } else if (option.prepend && firstChild) {\n    // Fallback to `insertBefore` like IE not support `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n\n  return styleNode;\n}\nvar containerCache = new Map();\n\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return Array.from(containerCache.get(container).children).find(function (node) {\n    return node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key;\n  });\n}\n\nexport function removeCSS(key) {\n  var _existNode$parentNode;\n\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n  existNode === null || existNode === void 0 ? void 0 : (_existNode$parentNode = existNode.parentNode) === null || _existNode$parentNode === void 0 ? void 0 : _existNode$parentNode.removeChild(existNode);\n}\nexport function updateCSS(css, key) {\n  var option = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(option); // Get real parent\n\n  if (!containerCache.has(container)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    parentNode.removeChild(placeholderStyle);\n  }\n\n  var existNode = findExistNode(key, option);\n\n  if (existNode) {\n    var _option$csp3, _option$csp4;\n\n    if (((_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce) && existNode.nonce !== ((_option$csp4 = option.csp) === null || _option$csp4 === void 0 ? void 0 : _option$csp4.nonce)) {\n      var _option$csp5;\n\n      existNode.nonce = (_option$csp5 = option.csp) === null || _option$csp5 === void 0 ? void 0 : _option$csp5.nonce;\n    }\n\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n\n    return existNode;\n  }\n\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,IAAIC,QAAQ,GAAG,aAAa;AAE5B,SAASC,OAAOA,CAAA,EAAG;EACjB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7EG,IAAI,GAAGJ,IAAI,CAACI,IAAI;EAEpB,IAAIA,IAAI,EAAE;IACR,OAAOA,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC,GAAGD,IAAI,GAAG,OAAO,CAACE,MAAM,CAACF,IAAI,CAAC;EAC/D;EAEA,OAAON,QAAQ;AACjB;AAEA,SAASS,YAAYA,CAACC,MAAM,EAAE;EAC5B,IAAIA,MAAM,CAACC,QAAQ,EAAE;IACnB,OAAOD,MAAM,CAACC,QAAQ;EACxB;EAEA,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EACzC,OAAOF,IAAI,IAAIC,QAAQ,CAACE,IAAI;AAC9B;AAEA,OAAO,SAASC,SAASA,CAACC,GAAG,EAAE;EAC7B,IAAIC,WAAW;EAEf,IAAIR,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEnF,IAAI,CAACJ,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,IAAIoB,SAAS,GAAGN,QAAQ,CAACO,aAAa,CAAC,OAAO,CAAC;EAE/C,IAAI,CAACF,WAAW,GAAGR,MAAM,CAACW,GAAG,MAAM,IAAI,IAAIH,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,KAAK,EAAE;IAC9F,IAAIC,YAAY;IAEhBJ,SAAS,CAACG,KAAK,GAAG,CAACC,YAAY,GAAGb,MAAM,CAACW,GAAG,MAAM,IAAI,IAAIE,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACD,KAAK;EACjH;EAEAH,SAAS,CAACK,SAAS,GAAGP,GAAG;EACzB,IAAIQ,SAAS,GAAGhB,YAAY,CAACC,MAAM,CAAC;EACpC,IAAIgB,UAAU,GAAGD,SAAS,CAACC,UAAU;EAErC,IAAIhB,MAAM,CAACiB,OAAO,IAAIF,SAAS,CAACE,OAAO,EAAE;IACvC;IACAF,SAAS,CAACE,OAAO,CAACR,SAAS,CAAC;EAC9B,CAAC,MAAM,IAAIT,MAAM,CAACiB,OAAO,IAAID,UAAU,EAAE;IACvC;IACAD,SAAS,CAACG,YAAY,CAACT,SAAS,EAAEO,UAAU,CAAC;EAC/C,CAAC,MAAM;IACLD,SAAS,CAACI,WAAW,CAACV,SAAS,CAAC;EAClC;EAEA,OAAOA,SAAS;AAClB;AACA,IAAIW,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE9B,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAIvB,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIsB,SAAS,GAAGhB,YAAY,CAACC,MAAM,CAAC;EACpC,OAAOwB,KAAK,CAACC,IAAI,CAACL,cAAc,CAACM,GAAG,CAACX,SAAS,CAAC,CAACY,QAAQ,CAAC,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IAC7E,OAAOA,IAAI,CAACC,OAAO,KAAK,OAAO,IAAID,IAAI,CAACE,YAAY,CAACxC,OAAO,CAACS,MAAM,CAAC,CAAC,KAAKuB,GAAG;EAC/E,CAAC,CAAC;AACJ;AAEA,OAAO,SAASS,SAASA,CAACT,GAAG,EAAE;EAC7B,IAAIU,qBAAqB;EAEzB,IAAIjC,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIyC,SAAS,GAAGZ,aAAa,CAACC,GAAG,EAAEvB,MAAM,CAAC;EAC1CkC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACD,qBAAqB,GAAGC,SAAS,CAACC,UAAU,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,WAAW,CAACF,SAAS,CAAC;AAC3M;AACA,OAAO,SAASG,SAASA,CAAC9B,GAAG,EAAEgB,GAAG,EAAE;EAClC,IAAIvB,MAAM,GAAGP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIsB,SAAS,GAAGhB,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC;;EAEtC,IAAI,CAACoB,cAAc,CAACkB,GAAG,CAACvB,SAAS,CAAC,EAAE;IAClC,IAAIwB,gBAAgB,GAAGjC,SAAS,CAAC,EAAE,EAAEN,MAAM,CAAC;IAC5C,IAAImC,UAAU,GAAGI,gBAAgB,CAACJ,UAAU;IAC5Cf,cAAc,CAACoB,GAAG,CAACzB,SAAS,EAAEoB,UAAU,CAAC;IACzCA,UAAU,CAACC,WAAW,CAACG,gBAAgB,CAAC;EAC1C;EAEA,IAAIL,SAAS,GAAGZ,aAAa,CAACC,GAAG,EAAEvB,MAAM,CAAC;EAE1C,IAAIkC,SAAS,EAAE;IACb,IAAIO,YAAY,EAAEC,YAAY;IAE9B,IAAI,CAAC,CAACD,YAAY,GAAGzC,MAAM,CAACW,GAAG,MAAM,IAAI,IAAI8B,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC7B,KAAK,KAAKsB,SAAS,CAACtB,KAAK,MAAM,CAAC8B,YAAY,GAAG1C,MAAM,CAACW,GAAG,MAAM,IAAI,IAAI+B,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9B,KAAK,CAAC,EAAE;MAC1N,IAAI+B,YAAY;MAEhBT,SAAS,CAACtB,KAAK,GAAG,CAAC+B,YAAY,GAAG3C,MAAM,CAACW,GAAG,MAAM,IAAI,IAAIgC,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC/B,KAAK;IACjH;IAEA,IAAIsB,SAAS,CAACpB,SAAS,KAAKP,GAAG,EAAE;MAC/B2B,SAAS,CAACpB,SAAS,GAAGP,GAAG;IAC3B;IAEA,OAAO2B,SAAS;EAClB;EAEA,IAAIU,OAAO,GAAGtC,SAAS,CAACC,GAAG,EAAEP,MAAM,CAAC;EACpC4C,OAAO,CAACC,YAAY,CAACtD,OAAO,CAACS,MAAM,CAAC,EAAEuB,GAAG,CAAC;EAC1C,OAAOqB,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}