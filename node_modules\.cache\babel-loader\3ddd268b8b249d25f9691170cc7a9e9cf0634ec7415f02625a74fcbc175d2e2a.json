{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Oval = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Oval = function Oval(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 38 38\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    strokeOpacity: \".5\",\n    cx: \"18\",\n    cy: \"18\",\n    r: props.radius\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"path\", {\n    d: \"M36 18c0-9.94-8.06-18-18-18\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"1s\",\n    repeatCount: \"indefinite\"\n  })))));\n};\nexports.Oval = Oval;\nOval.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nOval.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 18\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Oval", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "viewBox", "xmlns", "stroke", "color", "label", "fill", "fillRule", "transform", "strokeWidth", "strokeOpacity", "cx", "cy", "r", "radius", "d", "attributeName", "type", "from", "to", "dur", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Oval.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Oval = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Oval = function Oval(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 38 38\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    strokeOpacity: \".5\",\n    cx: \"18\",\n    cy: \"18\",\n    r: props.radius\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"path\", {\n    d: \"M36 18c0-9.94-8.06-18-18-18\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    from: \"0 18 18\",\n    to: \"360 18 18\",\n    dur: \"1s\",\n    repeatCount: \"indefinite\"\n  })))));\n};\n\nexports.Oval = Oval;\nOval.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nOval.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 18\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AAErB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,IAAI,GAAG,SAASA,IAAIA,CAACO,KAAK,EAAE;EAC9B,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAEN,KAAK,CAACO,KAAK;IACnB,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDQ,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAahB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDU,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE;EACf,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDY,aAAa,EAAE,IAAI;IACnBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEhB,KAAK,CAACiB;EACX,CAAC,CAAC,EAAE,aAAavB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACvDiB,CAAC,EAAE;EACL,CAAC,EAAE,aAAaxB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClEkB,aAAa,EAAE,WAAW;IAC1BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AAEDjC,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBA,IAAI,CAACgC,SAAS,GAAG;EACftB,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACrG1B,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAAC6B,SAAS,CAAC,CAAC7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAAC;EACpGrB,KAAK,EAAEV,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCnB,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM;EACnCV,MAAM,EAAEpB,UAAU,CAAC,SAAS,CAAC,CAAC+B;AAChC,CAAC;AACDnC,IAAI,CAACoC,YAAY,GAAG;EAClB1B,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTK,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,eAAe;EACtBS,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}