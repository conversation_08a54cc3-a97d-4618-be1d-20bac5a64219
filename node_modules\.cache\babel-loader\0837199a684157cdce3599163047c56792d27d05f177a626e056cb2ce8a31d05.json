{"ast": null, "code": "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react')) : typeof define === 'function' && define.amd ? define(['exports', 'react'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ReactErrorBoundary = {}, global.React));\n})(this, function (exports, React) {\n  'use strict';\n\n  function _interopNamespace(e) {\n    if (e && e.__esModule) return e;\n    var n = Object.create(null);\n    if (e) {\n      Object.keys(e).forEach(function (k) {\n        if (k !== 'default') {\n          var d = Object.getOwnPropertyDescriptor(e, k);\n          Object.defineProperty(n, k, d.get ? d : {\n            enumerable: true,\n            get: function () {\n              return e[k];\n            }\n          });\n        }\n      });\n    }\n    n[\"default\"] = e;\n    return Object.freeze(n);\n  }\n  var React__namespace = /*#__PURE__*/_interopNamespace(React);\n  function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n      o.__proto__ = p;\n      return o;\n    };\n    return _setPrototypeOf(o, p);\n  }\n  function _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    _setPrototypeOf(subClass, superClass);\n  }\n  var changedArray = function changedArray(a, b) {\n    if (a === void 0) {\n      a = [];\n    }\n    if (b === void 0) {\n      b = [];\n    }\n    return a.length !== b.length || a.some(function (item, index) {\n      return !Object.is(item, b[index]);\n    });\n  };\n  var initialState = {\n    error: null\n  };\n  var ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n    _inheritsLoose(ErrorBoundary, _React$Component);\n    function ErrorBoundary() {\n      var _this;\n      for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n        _args[_key] = arguments[_key];\n      }\n      _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n      _this.state = initialState;\n      _this.resetErrorBoundary = function () {\n        var _this$props;\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);\n        _this.reset();\n      };\n      return _this;\n    }\n    ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n      return {\n        error: error\n      };\n    };\n    var _proto = ErrorBoundary.prototype;\n    _proto.reset = function reset() {\n      this.setState(initialState);\n    };\n    _proto.componentDidCatch = function componentDidCatch(error, info) {\n      var _this$props$onError, _this$props2;\n      (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n      var error = this.state.error;\n      var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error\n      // happens to *also* be in the resetKeys array, we'd end up resetting\n      // the error boundary immediately. This would likely trigger a second\n      // error to be thrown.\n      // So we make sure that we don't check the resetKeys on the first call\n      // of cDU after the error is set\n\n      if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {\n        var _this$props$onResetKe, _this$props3;\n        (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);\n        this.reset();\n      }\n    };\n    _proto.render = function render() {\n      var error = this.state.error;\n      var _this$props4 = this.props,\n        fallbackRender = _this$props4.fallbackRender,\n        FallbackComponent = _this$props4.FallbackComponent,\n        fallback = _this$props4.fallback;\n      if (error !== null) {\n        var _props = {\n          error: error,\n          resetErrorBoundary: this.resetErrorBoundary\n        };\n        if (/*#__PURE__*/React__namespace.isValidElement(fallback)) {\n          return fallback;\n        } else if (typeof fallbackRender === 'function') {\n          return fallbackRender(_props);\n        } else if (FallbackComponent) {\n          return /*#__PURE__*/React__namespace.createElement(FallbackComponent, _props);\n        } else {\n          throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');\n        }\n      }\n      return this.props.children;\n    };\n    return ErrorBoundary;\n  }(React__namespace.Component);\n  function withErrorBoundary(Component, errorBoundaryProps) {\n    var Wrapped = function Wrapped(props) {\n      return /*#__PURE__*/React__namespace.createElement(ErrorBoundary, errorBoundaryProps, /*#__PURE__*/React__namespace.createElement(Component, props));\n    }; // Format for display in DevTools\n\n    var name = Component.displayName || Component.name || 'Unknown';\n    Wrapped.displayName = \"withErrorBoundary(\" + name + \")\";\n    return Wrapped;\n  }\n  function useErrorHandler(givenError) {\n    var _React$useState = React__namespace.useState(null),\n      error = _React$useState[0],\n      setError = _React$useState[1];\n    if (givenError != null) throw givenError;\n    if (error != null) throw error;\n    return setError;\n  }\n  /*\n  eslint\n    @typescript-eslint/sort-type-union-intersection-members: \"off\",\n    @typescript-eslint/no-throw-literal: \"off\",\n    @typescript-eslint/prefer-nullish-coalescing: \"off\"\n  */\n\n  exports.ErrorBoundary = ErrorBoundary;\n  exports.useErrorHandler = useErrorHandler;\n  exports.withErrorBoundary = withErrorBoundary;\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n});", "map": {"version": 3, "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "create", "constructor", "changedArray", "a", "b", "length", "some", "item", "index", "is", "initialState", "error", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "state", "resetErrorBoundary", "_this$props", "_len2", "arguments", "args", "Array", "_key2", "_this", "props", "onReset", "apply", "reset", "getDerivedStateFromError", "setState", "componentDidCatch", "info", "_this$props$onError", "_this$props2", "onError", "call", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "_this$props$onResetKe", "_this$props3", "onResetKeysChange", "render", "_this$props4", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "_props", "React__namespace", "isValidElement", "createElement", "Error", "children", "Component", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorBoundaryProps", "Wrapped", "name", "displayName", "useErrorHandler", "given<PERSON><PERSON><PERSON>", "_React$useState", "useState", "setError"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-error-boundary\\node_modules\\@babel\\runtime\\helpers\\esm\\setPrototypeOf.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-error-boundary\\node_modules\\@babel\\runtime\\helpers\\esm\\inheritsLoose.js", "C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-error-boundary\\src\\index.tsx"], "sourcesContent": ["export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import * as React from 'react'\n\nconst changedArray = (a: Array<unknown> = [], b: Array<unknown> = []) =>\n  a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n\ninterface FallbackProps {\n  error: Error\n  resetErrorBoundary: (...args: Array<unknown>) => void\n}\n\ninterface ErrorBoundaryPropsWithComponent {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent: React.ComponentType<FallbackProps>\n  fallbackRender?: never\n}\n\ndeclare function FallbackRender(\n  props: FallbackProps,\n): React.ReactElement<\n  unknown,\n  string | React.FunctionComponent | typeof React.Component\n> | null\n\ninterface ErrorBoundaryPropsWithRender {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent?: never\n  fallbackRender: typeof FallbackRender\n}\n\ninterface ErrorBoundaryPropsWithFallback {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback: React.ReactElement<\n    unknown,\n    string | React.FunctionComponent | typeof React.Component\n  > | null\n  FallbackComponent?: never\n  fallbackRender?: never\n}\n\ntype ErrorBoundaryProps =\n  | ErrorBoundaryPropsWithFallback\n  | ErrorBoundaryPropsWithComponent\n  | ErrorBoundaryPropsWithRender\n\ntype ErrorBoundaryState = {error: Error | null}\n\nconst initialState: ErrorBoundaryState = {error: null}\n\nclass ErrorBoundary extends React.Component<\n  React.PropsWithRef<React.PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  static getDerivedStateFromError(error: Error) {\n    return {error}\n  }\n\n  state = initialState\n  resetErrorBoundary = (...args: Array<unknown>) => {\n    this.props.onReset?.(...args)\n    this.reset()\n  }\n\n  reset() {\n    this.setState(initialState)\n  }\n\n  componentDidCatch(error: Error, info: React.ErrorInfo) {\n    this.props.onError?.(error, info)\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState,\n  ) {\n    const {error} = this.state\n    const {resetKeys} = this.props\n\n    // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (\n      error !== null &&\n      prevState.error !== null &&\n      changedArray(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onResetKeysChange?.(prevProps.resetKeys, resetKeys)\n      this.reset()\n    }\n  }\n\n  render() {\n    const {error} = this.state\n\n    const {fallbackRender, FallbackComponent, fallback} = this.props\n\n    if (error !== null) {\n      const props = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      }\n      if (React.isValidElement(fallback)) {\n        return fallback\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(props)\n      } else if (FallbackComponent) {\n        return <FallbackComponent {...props} />\n      } else {\n        throw new Error(\n          'react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop',\n        )\n      }\n    }\n\n    return this.props.children\n  }\n}\n\nfunction withErrorBoundary<P>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps: ErrorBoundaryProps,\n): React.ComponentType<P> {\n  const Wrapped: React.ComponentType<P> = props => {\n    return (\n      <ErrorBoundary {...errorBoundaryProps}>\n        <Component {...props} />\n      </ErrorBoundary>\n    )\n  }\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || 'Unknown'\n  Wrapped.displayName = `withErrorBoundary(${name})`\n\n  return Wrapped\n}\n\nfunction useErrorHandler(givenError?: unknown): (error: unknown) => void {\n  const [error, setError] = React.useState<unknown>(null)\n  if (givenError != null) throw givenError\n  if (error != null) throw error\n  return setError\n}\n\nexport {ErrorBoundary, withErrorBoundary, useErrorHandler}\nexport type {\n  FallbackProps,\n  ErrorBoundaryPropsWithComponent,\n  ErrorBoundaryPropsWithRender,\n  ErrorBoundaryPropsWithFallback,\n  ErrorBoundaryProps,\n}\n\n/*\neslint\n  @typescript-eslint/sort-type-union-intersection-members: \"off\",\n  @typescript-eslint/no-throw-literal: \"off\",\n  @typescript-eslint/prefer-nullish-coalescing: \"off\"\n*/\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;EAAe,SAASA,eAATA,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;IAC5CF,eAAe,GAAGG,MAAM,CAACC,cAAP,IAAyB,SAASJ,eAATA,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;MACxED,CAAC,CAACI,SAAF,GAAcH,CAAd;MACA,OAAOD,CAAP;IACD,CAHD;IAKA,OAAOD,eAAe,CAACC,CAAD,EAAIC,CAAJ,CAAtB;EACD;ECNc,SAASI,cAATA,CAAwBC,QAAxB,EAAkCC,UAAlC,EAA8C;IAC3DD,QAAQ,CAACE,SAAT,GAAqBN,MAAM,CAACO,MAAP,CAAcF,UAAU,CAACC,SAAzB,CAArB;IACAF,QAAQ,CAACE,SAAT,CAAmBE,WAAnB,GAAiCJ,QAAjC;IACAP,eAAc,CAACO,QAAD,EAAWC,UAAX,CAAd;EACD;ECHD,IAAMI,YAAY,GAAG,SAAfA,YAAeA,CAACC,CAAD,EAAyBC,CAAzB;IAAA,IAACD,CAAD;MAACA,CAAD,GAAqB,EAArB;IAAA;IAAA,IAAyBC,CAAzB;MAAyBA,CAAzB,GAA6C,EAA7C;IAAA;IAAA,OACnBD,CAAC,CAACE,MAAF,KAAaD,CAAC,CAACC,MAAf,IAAyBF,CAAC,CAACG,IAAF,CAAO,UAACC,IAAD,EAAOC,KAAP;MAAA,OAAiB,CAACf,MAAM,CAACgB,EAAP,CAAUF,IAAV,EAAgBH,CAAC,CAACI,KAAD,CAAjB,CAAlB;IAAA,CAAP,CADN;EAAA,CAArB;EAgEA,IAAME,YAAgC,GAAG;IAACC,KAAK,EAAE;EAAR,CAAzC;MAEMC,aAAA,0BAAAC,gBAAA;;;;;;;;YAQJC,KAAA,GAAQJ,YAAA;YACRK,kBAAA,GAAqB,YAA6B;QAAA,IAAAC,WAAA;QAAA,SAAAC,KAAA,GAAAC,SAAA,CAAAb,MAAA,EAAzBc,IAAyB,OAAAC,KAAA,CAAAH,KAAA,GAAAI,KAAA,MAAAA,KAAA,GAAAJ,KAAA,EAAAI,KAAA;UAAzBF,IAAyB,CAAAE,KAAA,IAAAH,SAAA,CAAAG,KAAA;QAAA;QAChDC,KAAA,CAAKC,KAAL,CAAWC,OAAX,qBAAAR,WAAA,GAAAM,KAAA,CAAKC,KAAL,EAAWC,OAAX,CAAAC,KAAA,CAAAT,WAAA,EAAwBG,IAAxB;QACAG,KAAA,CAAKI,KAAL;MACD;;;kBARMC,wBAAA,GAAP,SAAAA,yBAAgChB,KAAhC,EAA8C;MAC5C,OAAO;QAACA,KAAK,EAALA;MAAD,CAAP;IACD;;WAQDe,KAAA,YAAAA,MAAA,EAAQ;MACN,KAAKE,QAAL,CAAclB,YAAd;IACD;WAEDmB,iBAAA,YAAAA,kBAAkBlB,KAAlB,EAAgCmB,IAAhC,EAAuD;MAAA,IAAAC,mBAAA,EAAAC,YAAA;MACrD,CAAAD,mBAAA,IAAAC,YAAA,QAAKT,KAAL,EAAWU,OAAX,qBAAAF,mBAAA,CAAAG,IAAA,CAAAF,YAAA,EAAqBrB,KAArB,EAA4BmB,IAA5B;IACD;WAEDK,kBAAA,YAAAA,mBACEC,SADF,EAEEC,SAFF,EAGE;MACA,IAAO1B,KAAP,GAAgB,KAAKG,KAArB,CAAOH,KAAP;MACA,IAAO2B,SAAP,GAAoB,KAAKf,KAAzB,CAAOe,SAAP,CAFA;MAKA;MACA;MACA;MACA;MACA;;MAEA,IACE3B,KAAK,KAAK,IAAV,IACA0B,SAAS,CAAC1B,KAAV,KAAoB,IADpB,IAEAT,YAAY,CAACkC,SAAS,CAACE,SAAX,EAAsBA,SAAtB,CAHd,EAIE;QAAA,IAAAC,qBAAA,EAAAC,YAAA;QACA,CAAAD,qBAAA,IAAAC,YAAA,QAAKjB,KAAL,EAAWkB,iBAAX,qBAAAF,qBAAA,CAAAL,IAAA,CAAAM,YAAA,EAA+BJ,SAAS,CAACE,SAAzC,EAAoDA,SAApD;QACA,KAAKZ,KAAL;MACD;IACF;WAEDgB,MAAA,YAAAA,OAAA,EAAS;MACP,IAAO/B,KAAP,GAAgB,KAAKG,KAArB,CAAOH,KAAP;MAEA,IAAAgC,YAAA,GAAsD,KAAKpB,KAA3D;QAAOqB,cAAP,GAAAD,YAAA,CAAOC,cAAP;QAAuBC,iBAAvB,GAAAF,YAAA,CAAuBE,iBAAvB;QAA0CC,QAA1C,GAAAH,YAAA,CAA0CG,QAA1C;MAEA,IAAInC,KAAK,KAAK,IAAd,EAAoB;QAClB,IAAMoC,MAAK,GAAG;UACZpC,KAAK,EAALA,KADY;UAEZI,kBAAkB,EAAE,KAAKA;QAFb,CAAd;QAIA,iBAAIiC,gBAAK,CAACC,cAAN,CAAqBH,QAArB,CAAJ,EAAoC;UAClC,OAAOA,QAAP;QACD,CAFD,MAEO,IAAI,OAAOF,cAAP,KAA0B,UAA9B,EAA0C;UAC/C,OAAOA,cAAc,CAACG,MAAD,CAArB;QACD,CAFM,MAEA,IAAIF,iBAAJ,EAAuB;UAC5B,oBAAOG,gBAAA,CAAAE,aAAA,CAACL,iBAAD,EAAuBE,MAAvB,CAAP;QACD,CAFM,MAEA;UACL,MAAM,IAAII,KAAJ,CACJ,4FADI,CAAN;QAGD;MACF;MAED,OAAO,KAAK5B,KAAL,CAAW6B,QAAlB;IACD;;IAtEyBJ,gBAAK,CAACK,SAAA;EAyElC,SAASC,iBAATA,CACED,SADF,EAEEE,kBAFF,EAG0B;IACxB,IAAMC,OAA+B,GAAG,SAAlCA,OAAkCA,CAAAjC,KAAK,EAAI;MAC/C,oBACEyB,gBAAA,CAAAE,aAAA,CAACtC,aAAD,EAAmB2C,kBAAnB,eACEP,gBAAA,CAAAE,aAAA,CAACG,SAAD,EAAe9B,KAAf,CADF,CADF;IAKD,CAND,CADwB;;IAUxB,IAAMkC,IAAI,GAAGJ,SAAS,CAACK,WAAV,IAAyBL,SAAS,CAACI,IAAnC,IAA2C,SAAxD;IACAD,OAAO,CAACE,WAAR,0BAA2CD,IAA3C;IAEA,OAAOD,OAAP;EACD;EAED,SAASG,eAATA,CAAyBC,UAAzB,EAAyE;IACvE,IAAAC,eAAA,GAA0Bb,gBAAK,CAACc,QAAN,CAAwB,IAAxB,CAA1B;MAAOnD,KAAP,GAAAkD,eAAA;MAAcE,QAAd,GAAAF,eAAA;IACA,IAAID,UAAU,IAAI,IAAlB,EAAwB,MAAMA,UAAN;IACxB,IAAIjD,KAAK,IAAI,IAAb,EAAmB,MAAMA,KAAN;IACnB,OAAOoD,QAAP;EACD;EAWD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}