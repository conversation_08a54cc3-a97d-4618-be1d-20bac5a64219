{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\agenti\\\\completaOrdine.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CompletaOrdine - operazioni per il completamento dell'ordine dall'agente \n*\n*/\nimport React, { Component } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport Nav from \"../../components/navigation/Nav\";\nimport NavAgenteOrdini from './navIcon';\nimport CarrelloGen from '../../components/generalizzazioni/marketplace/carrello';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass CompletaOrdine extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      results2: null,\n      datiConsegna: null,\n      loading: true\n    };\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    var prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n    var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n    var url = 'retailers/?id=' + JSON.parse(localStorage.getItem(\"datiComodo\")).id;\n    for (var i = 0; i < prodotti.length; i++) {\n      if (prodotti[i].total === undefined) {\n        var totale = [];\n        totale = prodotti;\n        totale[i].totale = parseFloat(prodotti[i].price).toFixed(2) * prodotti[i].quantity;\n        prodotti = totale;\n      } else {\n        prodotti[i].idProduct2 = prodotti[i].product;\n        prodotti[i].price = prodotti[i].unitPrice;\n        prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity;\n      }\n    }\n    await APIRequest('GET', url).then(res => {\n      this.setState({\n        results: res.data,\n        results2: prodotti,\n        datiConsegna: dati,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Si \\xE8 verificato un errore presto sar\\xE0 reindirizzato alla dashboard. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  render() {\n    if (this.state.loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card form-complete border-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card form-complete border-0 creaOrdine\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.RiepOrd\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CarrelloGen, {\n        results: this.state.results,\n        results2: this.state.results2,\n        datiConsegna: this.state.datiConsegna\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row bg-white border-top pt-3 mt-0 sticky-bottom steppingOrderDetail\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-12\",\n          children: /*#__PURE__*/_jsxDEV(NavAgenteOrdini, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default CompletaOrdine;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "Nav", "NavAgenteOrdini", "CarrelloGen", "jsxDEV", "_jsxDEV", "CompletaOrdine", "constructor", "props", "state", "results", "results2", "dati<PERSON>ons<PERSON>na", "loading", "componentDidMount", "prodotti", "JSON", "parse", "localStorage", "getItem", "dati", "url", "id", "i", "length", "total", "undefined", "totale", "parseFloat", "price", "toFixed", "quantity", "idProduct2", "product", "unitPrice", "then", "res", "setState", "data", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "message", "life", "render", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "ref", "el", "<PERSON><PERSON><PERSON><PERSON>rd"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/agenti/completaOrdine.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* CompletaOrdine - operazioni per il completamento dell'ordine dall'agente \n*\n*/\nimport React, { Component } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { <PERSON>nti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport Nav from \"../../components/navigation/Nav\";\nimport NavAgenteOrdini from './navIcon';\nimport CarrelloGen from '../../components/generalizzazioni/marketplace/carrello';\n\nclass CompletaOrdine extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            datiConsegna: null,\n            loading: true\n        }\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        var prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n        var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n        var url = 'retailers/?id=' + JSON.parse(localStorage.getItem(\"datiComodo\")).id;\n        for (var i = 0; i < prodotti.length; i++) {\n            if (prodotti[i].total === undefined) {\n                var totale = [];\n                totale = prodotti;\n                totale[i].totale = parseFloat(prodotti[i].price).toFixed(2) * prodotti[i].quantity;\n                prodotti = totale;\n            } else {\n                prodotti[i].idProduct2 = prodotti[i].product;\n                prodotti[i].price = prodotti[i].unitPrice\n                prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity\n            }\n        }\n        await APIRequest('GET', url)\n            .then(res => {\n                this.setState({\n                    results: res.data,\n                    results2: prodotti,\n                    datiConsegna: dati,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e);\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Si è verificato un errore presto sarà reindirizzato alla dashboard. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n\n            })\n    }\n    render() {\n        if (this.state.loading) {\n            return (\n                <div className=\"card form-complete border-0\"></div>\n            )\n        }\n        return (\n            <div className=\"card form-complete border-0 creaOrdine\">\n                <Nav />\n                <Toast ref={(el) => this.toast = el} />\n                <div className=\"col-12 solid-head\">\n                    <h1>{Costanti.RiepOrd}</h1>\n                </div>\n                <CarrelloGen results={this.state.results} results2={this.state.results2} datiConsegna={this.state.datiConsegna} />\n                <div className=\"row bg-white border-top pt-3 mt-0 sticky-bottom steppingOrderDetail\">\n                    <div className=\"col-md-12\">\n                        <NavAgenteOrdini />\n                    </div>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default CompletaOrdine;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,WAAW,MAAM,wDAAwD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjF,MAAMC,cAAc,SAAST,SAAS,CAAC;EACnCU,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACb,CAAC;EACL;EACA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvD,IAAIC,IAAI,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC3D,IAAIE,GAAG,GAAG,gBAAgB,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACG,EAAE;IAC9E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIR,QAAQ,CAACQ,CAAC,CAAC,CAACE,KAAK,KAAKC,SAAS,EAAE;QACjC,IAAIC,MAAM,GAAG,EAAE;QACfA,MAAM,GAAGZ,QAAQ;QACjBY,MAAM,CAACJ,CAAC,CAAC,CAACI,MAAM,GAAGC,UAAU,CAACb,QAAQ,CAACQ,CAAC,CAAC,CAACM,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGf,QAAQ,CAACQ,CAAC,CAAC,CAACQ,QAAQ;QAClFhB,QAAQ,GAAGY,MAAM;MACrB,CAAC,MAAM;QACHZ,QAAQ,CAACQ,CAAC,CAAC,CAACS,UAAU,GAAGjB,QAAQ,CAACQ,CAAC,CAAC,CAACU,OAAO;QAC5ClB,QAAQ,CAACQ,CAAC,CAAC,CAACM,KAAK,GAAGd,QAAQ,CAACQ,CAAC,CAAC,CAACW,SAAS;QACzCnB,QAAQ,CAACQ,CAAC,CAAC,CAACI,MAAM,GAAGC,UAAU,CAACb,QAAQ,CAACQ,CAAC,CAAC,CAACW,SAAS,CAAC,CAACJ,OAAO,CAAC,CAAC,CAAC,GAAGf,QAAQ,CAACQ,CAAC,CAAC,CAACQ,QAAQ;MAC5F;IACJ;IACA,MAAM/B,UAAU,CAAC,KAAK,EAAEqB,GAAG,CAAC,CACvBc,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACC,QAAQ,CAAC;QACV3B,OAAO,EAAE0B,GAAG,CAACE,IAAI;QACjB3B,QAAQ,EAAEI,QAAQ;QAClBH,YAAY,EAAEQ,IAAI;QAClBP,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CAAC0B,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACZC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,iGAAAC,MAAA,CAA2F,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYH,IAAI,MAAKZ,SAAS,IAAAgB,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,GAAGE,CAAC,CAACY,OAAO,CAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAEpP,CAAC,CAAC;EACV;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC7C,KAAK,CAACI,OAAO,EAAE;MACpB,oBACIR,OAAA;QAAKkD,SAAS,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAE3D;IACA,oBACItD,OAAA;MAAKkD,SAAS,EAAC,wCAAwC;MAAAK,QAAA,gBACnDvD,OAAA,CAACJ,GAAG;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPtD,OAAA,CAACP,KAAK;QAAC+D,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACjB,KAAK,GAAGiB;MAAG;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCtD,OAAA;QAAKkD,SAAS,EAAC,mBAAmB;QAAAK,QAAA,eAC9BvD,OAAA;UAAAuD,QAAA,EAAK7D,QAAQ,CAACgE;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACNtD,OAAA,CAACF,WAAW;QAACO,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAQ;QAACC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE,QAAS;QAACC,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG;MAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClHtD,OAAA;QAAKkD,SAAS,EAAC,qEAAqE;QAAAK,QAAA,eAChFvD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAK,QAAA,eACtBvD,OAAA,CAACH,eAAe;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAerD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}