{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\navigation\\\\dashboard.jsx\";\n/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Dashboard - dashboard generalizzata\n*\n*/\nimport React, { Component, Fragment } from \"react\";\nimport { agente, distributore, sistemiEsterni } from \"../route\";\nimport { NavLink } from \"react-router-dom\";\nimport { Costanti } from \"../traduttore/const\";\nimport { APIRequest } from \"../generalizzazioni/apireq\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from 'primereact/toast';\nimport menu from '../generalizzazioni/menu.json';\nimport Caricamento from \"../../utils/caricamento\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Dashboard extends Component {\n  constructor(props) {\n    super(props);\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n    };\n    this.state = {\n      classCard: '',\n      iconPR: true,\n      dropdown: false,\n      selectedWarehouse: null,\n      nDoc: [],\n      isLoading: true\n    };\n    this.warehouse = [];\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n  }\n  async componentDidMount() {\n    var user = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : '';\n    if (user && (user.role === distributore || user.role === agente)) {\n      try {\n        await APIRequest('GET', 'externalsystems/').then(res => {\n          if (res && res.data) {\n            var role = localStorage.getItem(\"role\").toLowerCase();\n            Object.entries(menu[role]).forEach(element => {\n              if (element[0] === sistemiEsterni) {\n                element[1] = [];\n                element[1].visibility = 'true';\n                element[1].iconMain = 'logo-ionic';\n                res.data.forEach(el => {\n                  if (el && el.externalSistemName) {\n                    element[1][\"externalsystems/\" + el.externalSistemName.toLowerCase()] = {\n                      'icona': 'cloud-download-outline',\n                      'visibility': 'true'\n                    };\n                  }\n                });\n                menu[role][element[0]] = element[1];\n                if (role === 'agente') {\n                  /*  menu[role].sistemiEsterni = [] */\n                  if (menu[role].sistemiEsterni && menu[role].sistemiEsterni['externalsystems/alyante']) {\n                    menu[role].sistemiEsterni = {\n                      'externalsystems/alyante': menu[role].sistemiEsterni['externalsystems/alyante'],\n                      'visibility': menu[role].sistemiEsterni.visibility,\n                      'iconMain': menu[role].sistemiEsterni.iconMain\n                    };\n                  }\n                }\n              }\n            });\n          }\n        });\n      } catch (e) {\n        console.warn('External systems endpoint not available:', e.message);\n        // Continue without external systems - this is not critical for basic functionality\n      }\n    }\n    if (this.props.dashAgent === true) {\n      this.setState({\n        classCard: 'dashAgent card-columns',\n        dropdown: true\n      });\n    } else if (this.props.dash2col === true) {\n      this.setState({\n        classCard: 'dashAgent dash2col card-columns',\n        dropdown: false\n      });\n    } else if (this.props.dashResp === true) {\n      var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n      await APIRequest(\"GET\", \"warehouses/\").then(res => {\n        for (var entry of res.data) {\n          this.warehouse.push({\n            name: entry.warehouseName,\n            value: entry.id\n          });\n        }\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n      if (idWarehouse !== null) {\n        this.setState({\n          selectedWarehouse: idWarehouse\n        });\n      }\n      this.setState({\n        classCard: 'dashAgent card-columns',\n        dropdown: true\n      });\n    } else {\n      this.setState({\n        classCard: 'card-columns'\n      });\n    }\n    // Definisco gli elementi che non devono essere visualizzati nella dashboard\n    if (user && user.userGuiInhibition !== null && user.userGuiInhibition !== undefined && user.userGuiInhibition.inhibition) {\n      Object.entries(menu).forEach(items => {\n        var _user$role;\n        if (items[0] === ((_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.toLowerCase())) {\n          Object.entries(user.userGuiInhibition.inhibition).forEach(element => {\n            Object.entries(items[1]).forEach(el => {\n              if (el[0] === element[0]) {\n                if (el[1].visibility !== element[1].visibility) {\n                  el[1].visibility = element[1].visibility;\n                } else {\n                  Object.entries(element[1]).forEach(object => {\n                    if (object[0] !== \"visibility\") {\n                      Object.entries(el[1]).forEach(obj => {\n                        if (obj[0] !== \"visibility\") {\n                          if (obj[0] === object[0]) {\n                            if (obj[1].visibility !== undefined && object[1].visibility !== undefined) {\n                              obj[1].visibility = object[1].visibility;\n                              obj[1].disabled = object[1].disabled;\n                            }\n                          }\n                        }\n                      });\n                    }\n                  });\n                }\n              }\n            });\n          });\n        }\n      });\n    }\n    this.setState({\n      isLoading: false\n    });\n  }\n  render() {\n    var role = localStorage.getItem(\"role\").toLowerCase();\n    var trad = Costanti;\n    var classe = '';\n    var classBox = '';\n    if (this.state.isLoading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 20\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: this.state.classCard,\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 17\n      }, this), Object.entries(menu[role]).map((element, index) => {\n        // Return the element. Also pass key\n        if (element[1].visibility === \"true\") {\n          classBox = 'card mb-4 shadow-sm';\n        } else {\n          classBox = 'd-none card mb-4 shadow-sm';\n        }\n        return /*#__PURE__*/_jsxDEV(Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: classBox,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"titleBox cardHeadBox border-bottom bg-custom-gray justify-content-between row m-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"theTitle d-flex align-items-center col-12 col-lg-6 p-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"icona d-flex align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                    size: 50,\n                    name: element[1].iconMain\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"titolo\",\n                  children: trad[element[0]]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 37\n              }, this), this.state.dropdown && element[0] === 'Magazzino' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"areaSelezione col-12 col-lg-6 p-0 my-2 my-lg-0\",\n                style: {\n                  backgroundColor: \"#f3f4f5\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"selWar mw-100 w-100\",\n                  value: this.state.selectedWarehouse,\n                  options: this.warehouse,\n                  onChange: this.onWarehouseSelect,\n                  optionLabel: \"name\",\n                  placeholder: \"Seleziona magazzino\",\n                  filter: true,\n                  filterBy: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"accordion\",\n                children: Object.entries(element[1]).map((item, base) => {\n                  if (item[0] !== \"visibility\") {\n                    var _item$, _item$2;\n                    if (((_item$ = item[1]) === null || _item$ === void 0 ? void 0 : _item$.disabled) === 'true') {\n                      classe = \"disabled btn btn-link col-12 dash-btn\";\n                    } else if (this.props.disabled === true) {\n                      classe = \"disabled btn btn-link col-12 dash-btn\";\n                    } else {\n                      classe = \"btn btn-link col-12 dash-btn border\";\n                    }\n                    if (((_item$2 = item[1]) === null || _item$2 === void 0 ? void 0 : _item$2.visibility) !== 'false') {\n                      var _item$3, _item$4, _item$5, _item$6;\n                      return /*#__PURE__*/_jsxDEV(Fragment, {\n                        children: item[0] !== 'iconMain' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"card\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"serviceBox\",\n                            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                              className: \"areaToggle mb-0\",\n                              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                                to: \"/\" + role + \"/\" + item[0],\n                                className: classe,\n                                children: [item[0].includes('/') ? item[0].split('/')[1] : trad[item[0]], item[0] !== 'iconMain' && ((_item$3 = item[1]) === null || _item$3 === void 0 ? void 0 : _item$3.icona.includes('btn-icon')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: (_item$4 = item[1]) === null || _item$4 === void 0 ? void 0 : _item$4.icona\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 209,\n                                  columnNumber: 85\n                                }, this), item[0] !== 'iconMain' && !((_item$5 = item[1]) !== null && _item$5 !== void 0 && _item$5.icona.includes('btn-icon')) && /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                                  name: (_item$6 = item[1]) === null || _item$6 === void 0 ? void 0 : _item$6.icona\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 212,\n                                  columnNumber: 85\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 207,\n                                columnNumber: 77\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 206,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 205,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 65\n                        }, this)\n                      }, base, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 57\n                      }, this);\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"span\", {}, item, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 57\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"span\", {}, base, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 53\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 29\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 25\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Dashboard;", "map": {"version": 3, "names": ["React", "Component", "Fragment", "agente", "distributore", "<PERSON><PERSON><PERSON>", "NavLink", "<PERSON><PERSON>", "APIRequest", "Dropdown", "Toast", "menu", "Caricamento", "jsxDEV", "_jsxDEV", "Dashboard", "constructor", "props", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "window", "sessionStorage", "setItem", "state", "classCard", "iconPR", "dropdown", "nDoc", "isLoading", "warehouse", "bind", "componentDidMount", "user", "localStorage", "getItem", "JSON", "parse", "role", "then", "res", "data", "toLowerCase", "Object", "entries", "for<PERSON>ach", "element", "visibility", "iconMain", "el", "externalSistemName", "console", "warn", "message", "dashAgent", "dash2col", "dashResp", "idWarehouse", "entry", "push", "name", "warehouseName", "id", "catch", "_e$response", "_e$response2", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "life", "userGuiInhibition", "inhibition", "items", "_user$role", "object", "obj", "disabled", "render", "trad", "classe", "classBox", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "map", "index", "size", "style", "backgroundColor", "options", "onChange", "optionLabel", "placeholder", "filter", "filterBy", "item", "base", "_item$", "_item$2", "_item$3", "_item$4", "_item$5", "_item$6", "to", "includes", "split", "icona"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/navigation/dashboard.jsx"], "sourcesContent": ["/*\n*\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Dashboard - dashboard generalizzata\n*\n*/\nimport React, { Component, Fragment } from \"react\";\nimport { agente, distributore, sistemiEsterni } from \"../route\";\nimport { NavLink } from \"react-router-dom\";\nimport { <PERSON>nti } from \"../traduttore/const\";\nimport { APIRequest } from \"../generalizzazioni/apireq\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from 'primereact/toast';\nimport menu from '../generalizzazioni/menu.json';\nimport Caricamento from \"../../utils/caricamento\";\n\nclass Dashboard extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            classCard: '',\n            iconPR: true,\n            dropdown: false,\n            selectedWarehouse: null,\n            nDoc: [],\n            isLoading: true,\n        }\n        this.warehouse = []\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    }\n\n    async componentDidMount() {\n        var user = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : ''\n        if (user && (user.role === distributore || user.role === agente)) {\n            try {\n                await APIRequest('GET', 'externalsystems/')\n                    .then(res => {\n                        if (res && res.data) {\n                            var role = localStorage.getItem(\"role\").toLowerCase()\n                            Object.entries(menu[role]).forEach(element => {\n                                if (element[0] === sistemiEsterni) {\n                                    element[1] = []\n                                    element[1].visibility = 'true'\n                                    element[1].iconMain = 'logo-ionic'\n                                    res.data.forEach(el => {\n                                        if (el && el.externalSistemName) {\n                                            element[1][\"externalsystems/\" + el.externalSistemName.toLowerCase()] = {\n                                                'icona': 'cloud-download-outline',\n                                                'visibility': 'true'\n                                            }\n                                        }\n                                    })\n                                    menu[role][element[0]] = element[1]\n                                    if (role === 'agente') {\n                                        /*  menu[role].sistemiEsterni = [] */\n                                        if (menu[role].sistemiEsterni && menu[role].sistemiEsterni['externalsystems/alyante']) {\n                                            menu[role].sistemiEsterni = { 'externalsystems/alyante': menu[role].sistemiEsterni['externalsystems/alyante'], 'visibility': menu[role].sistemiEsterni.visibility, 'iconMain': menu[role].sistemiEsterni.iconMain }\n                                        }\n                                    }\n                                }\n                            })\n                        }\n                    })\n            } catch (e) {\n                console.warn('External systems endpoint not available:', e.message);\n                // Continue without external systems - this is not critical for basic functionality\n            }\n        }\n        if (this.props.dashAgent === true) {\n            this.setState({\n                classCard: 'dashAgent card-columns',\n                dropdown: true\n            })\n        } else if (this.props.dash2col === true) {\n            this.setState({\n                classCard: 'dashAgent dash2col card-columns',\n                dropdown: false\n            })\n        } else if (this.props.dashResp === true) {\n            var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n\n            await APIRequest(\"GET\", \"warehouses/\")\n                .then((res) => {\n                    for (var entry of res.data) {\n                        this.warehouse.push({\n                            name: entry.warehouseName,\n                            value: entry.id\n                        })\n                    }\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            if (idWarehouse !== null) {\n                this.setState({ selectedWarehouse: idWarehouse });\n            }\n            this.setState({\n                classCard: 'dashAgent card-columns',\n                dropdown: true\n            })\n        } else {\n            this.setState({\n                classCard: 'card-columns'\n            })\n        }\n        // Definisco gli elementi che non devono essere visualizzati nella dashboard\n        if (user && user.userGuiInhibition !== null && user.userGuiInhibition !== undefined && user.userGuiInhibition.inhibition) {\n            Object.entries(menu).forEach(items => {\n                if (items[0] === user.role?.toLowerCase()) {\n                    Object.entries(user.userGuiInhibition.inhibition).forEach(element => {\n                        Object.entries(items[1]).forEach(el => {\n                            if (el[0] === element[0]) {\n                                if (el[1].visibility !== element[1].visibility) {\n                                    el[1].visibility = element[1].visibility\n                                } else {\n                                    Object.entries(element[1]).forEach(object => {\n                                        if (object[0] !== \"visibility\") {\n                                            Object.entries(el[1]).forEach(obj => {\n                                                if (obj[0] !== \"visibility\") {\n                                                    if (obj[0] === object[0]) {\n                                                        if (obj[1].visibility !== undefined && object[1].visibility !== undefined) {\n                                                            obj[1].visibility = object[1].visibility\n                                                            obj[1].disabled = object[1].disabled\n                                                        }\n                                                    }\n                                                }\n                                            })\n                                        }\n                                    })\n                                }\n                            }\n                        })\n                    })\n                }\n            })\n        }\n        this.setState({ isLoading: false });\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n    }\n    render() {\n        var role = localStorage.getItem(\"role\").toLowerCase()\n        var trad = Costanti\n        var classe = ''\n        var classBox = ''\n        if (this.state.isLoading) {\n            return <div className=\"App\"><Caricamento /></div>;\n        }\n        return (\n            <div className={this.state.classCard}>\n                <Toast ref={(el) => this.toast = el} />\n                {Object.entries(menu[role]).map((element, index) => {\n                    // Return the element. Also pass key\n                    if (element[1].visibility === \"true\") {\n                        classBox = 'card mb-4 shadow-sm'\n                    } else {\n                        classBox = 'd-none card mb-4 shadow-sm'\n                    }\n                    return (\n                        <Fragment key={index}>\n                            <div className={classBox}>\n                                <div className=\"titleBox cardHeadBox border-bottom bg-custom-gray justify-content-between row m-0\">\n                                    <div className=\"theTitle d-flex align-items-center col-12 col-lg-6 p-0\">\n                                        <div className=\"icona d-flex align-items-center\">\n                                            <ion-icon size={50} name={element[1].iconMain} />\n                                        </div>\n                                        <div className=\"titolo\">\n                                            {trad[element[0]]}\n                                            {/* <hr className=\"mb-0\" /> */}\n                                        </div>\n                                    </div>\n                                    {this.state.dropdown && element[0] === 'Magazzino' &&\n                                        <div className=\"areaSelezione col-12 col-lg-6 p-0 my-2 my-lg-0\" style={{ backgroundColor: \"#f3f4f5\" }}>\n                                            <Dropdown className=\"selWar mw-100 w-100\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                        </div>\n                                    }\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        {Object.entries(element[1]).map((item, base) => {\n                                            if (item[0] !== \"visibility\") {\n                                                if (item[1]?.disabled === 'true') {\n                                                    classe = \"disabled btn btn-link col-12 dash-btn\"\n                                                } else if (this.props.disabled === true) {\n                                                    classe = \"disabled btn btn-link col-12 dash-btn\"\n                                                } else {\n                                                    classe = \"btn btn-link col-12 dash-btn border\"\n                                                }\n                                                if (item[1]?.visibility !== 'false') {\n                                                    return (\n                                                        <Fragment key={base}>\n                                                            {item[0] !== 'iconMain' &&\n                                                                <div className=\"card\">\n                                                                    <div className=\"serviceBox\" >\n                                                                        <h5 className=\"areaToggle mb-0\">\n                                                                            <NavLink to={\"/\" + role + \"/\" + item[0]} className={classe}>{item[0].includes('/') ? item[0].split('/')[1] : trad[item[0]]}\n                                                                                {item[0] !== 'iconMain' && item[1]?.icona.includes('btn-icon') &&\n                                                                                    <span className={item[1]?.icona}></span>\n                                                                                }\n                                                                                {item[0] !== 'iconMain' && !item[1]?.icona.includes('btn-icon') &&\n                                                                                    <ion-icon name={item[1]?.icona}></ion-icon>\n                                                                                }\n                                                                            </NavLink>\n                                                                        </h5>\n                                                                    </div>\n                                                                </div>\n                                                            }\n                                                        </Fragment>\n                                                    )\n                                                }\n                                                return (<span key={item}></span>)\n                                            }\n                                            return (<span key={base}></span>)\n                                        })\n                                        }\n                                    </div>\n                                </div>\n                            </div>\n                        </Fragment>\n                    )\n                })\n                }\n            </div>\n        );\n    }\n}\n\nexport default Dashboard;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,YAAY,EAAEC,cAAc,QAAQ,UAAU;AAC/D,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,IAAI,MAAM,+BAA+B;AAChD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,SAASd,SAAS,CAAC;EAC9Be,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IA8HhB;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7CC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEN,CAAC,CAACG,KAAK,CAAC;IACzD,CAAC;IAjIG,IAAI,CAACI,KAAK,GAAG;MACTC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfR,iBAAiB,EAAE,IAAI;MACvBS,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE;IACf,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACd,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACe,IAAI,CAAC,IAAI,CAAC;EAC9D;EAEA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;IACvF,IAAIF,IAAI,KAAKA,IAAI,CAACK,IAAI,KAAKpC,YAAY,IAAI+B,IAAI,CAACK,IAAI,KAAKrC,MAAM,CAAC,EAAE;MAC9D,IAAI;QACA,MAAMK,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC,CACtCiC,IAAI,CAACC,GAAG,IAAI;UACT,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE;YACjB,IAAIH,IAAI,GAAGJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAACO,WAAW,CAAC,CAAC;YACrDC,MAAM,CAACC,OAAO,CAACnC,IAAI,CAAC6B,IAAI,CAAC,CAAC,CAACO,OAAO,CAACC,OAAO,IAAI;cAC1C,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK3C,cAAc,EAAE;gBAC/B2C,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;gBACfA,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU,GAAG,MAAM;gBAC9BD,OAAO,CAAC,CAAC,CAAC,CAACE,QAAQ,GAAG,YAAY;gBAClCR,GAAG,CAACC,IAAI,CAACI,OAAO,CAACI,EAAE,IAAI;kBACnB,IAAIA,EAAE,IAAIA,EAAE,CAACC,kBAAkB,EAAE;oBAC7BJ,OAAO,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAGG,EAAE,CAACC,kBAAkB,CAACR,WAAW,CAAC,CAAC,CAAC,GAAG;sBACnE,OAAO,EAAE,wBAAwB;sBACjC,YAAY,EAAE;oBAClB,CAAC;kBACL;gBACJ,CAAC,CAAC;gBACFjC,IAAI,CAAC6B,IAAI,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;gBACnC,IAAIR,IAAI,KAAK,QAAQ,EAAE;kBACnB;kBACA,IAAI7B,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,IAAIM,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,CAAC,yBAAyB,CAAC,EAAE;oBACnFM,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,GAAG;sBAAE,yBAAyB,EAAEM,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,CAAC,yBAAyB,CAAC;sBAAE,YAAY,EAAEM,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,CAAC4C,UAAU;sBAAE,UAAU,EAAEtC,IAAI,CAAC6B,IAAI,CAAC,CAACnC,cAAc,CAAC6C;oBAAS,CAAC;kBACvN;gBACJ;cACJ;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACV,CAAC,CAAC,OAAO/B,CAAC,EAAE;QACRkC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEnC,CAAC,CAACoC,OAAO,CAAC;QACnE;MACJ;IACJ;IACA,IAAI,IAAI,CAACtC,KAAK,CAACuC,SAAS,KAAK,IAAI,EAAE;MAC/B,IAAI,CAACpC,QAAQ,CAAC;QACVO,SAAS,EAAE,wBAAwB;QACnCE,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACZ,KAAK,CAACwC,QAAQ,KAAK,IAAI,EAAE;MACrC,IAAI,CAACrC,QAAQ,CAAC;QACVO,SAAS,EAAE,iCAAiC;QAC5CE,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACZ,KAAK,CAACyC,QAAQ,KAAK,IAAI,EAAE;MACrC,IAAIC,WAAW,GAAGrB,IAAI,CAACC,KAAK,CAAChB,MAAM,CAACC,cAAc,CAACa,OAAO,CAAC,aAAa,CAAC,CAAC;MAE1E,MAAM7B,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCiC,IAAI,CAAEC,GAAG,IAAK;QACX,KAAK,IAAIkB,KAAK,IAAIlB,GAAG,CAACC,IAAI,EAAE;UACxB,IAAI,CAACX,SAAS,CAAC6B,IAAI,CAAC;YAChBC,IAAI,EAAEF,KAAK,CAACG,aAAa;YACzBzC,KAAK,EAAEsC,KAAK,CAACI;UACjB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,CACDC,KAAK,CAAE9C,CAAC,IAAK;QAAA,IAAA+C,WAAA,EAAAC,YAAA;QACVd,OAAO,CAACe,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAR,WAAA,GAAA/C,CAAC,CAACwD,QAAQ,cAAAT,WAAA,uBAAVA,WAAA,CAAYvB,IAAI,MAAKiC,SAAS,IAAAT,YAAA,GAAGhD,CAAC,CAACwD,QAAQ,cAAAR,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,GAAGxB,CAAC,CAACoC,OAAO,CAAE;UAC5IsB,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,IAAIlB,WAAW,KAAK,IAAI,EAAE;QACtB,IAAI,CAACvC,QAAQ,CAAC;UAAEC,iBAAiB,EAAEsC;QAAY,CAAC,CAAC;MACrD;MACA,IAAI,CAACvC,QAAQ,CAAC;QACVO,SAAS,EAAE,wBAAwB;QACnCE,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACT,QAAQ,CAAC;QACVO,SAAS,EAAE;MACf,CAAC,CAAC;IACN;IACA;IACA,IAAIQ,IAAI,IAAIA,IAAI,CAAC2C,iBAAiB,KAAK,IAAI,IAAI3C,IAAI,CAAC2C,iBAAiB,KAAKF,SAAS,IAAIzC,IAAI,CAAC2C,iBAAiB,CAACC,UAAU,EAAE;MACtHlC,MAAM,CAACC,OAAO,CAACnC,IAAI,CAAC,CAACoC,OAAO,CAACiC,KAAK,IAAI;QAAA,IAAAC,UAAA;QAClC,IAAID,KAAK,CAAC,CAAC,CAAC,OAAAC,UAAA,GAAK9C,IAAI,CAACK,IAAI,cAAAyC,UAAA,uBAATA,UAAA,CAAWrC,WAAW,CAAC,CAAC,GAAE;UACvCC,MAAM,CAACC,OAAO,CAACX,IAAI,CAAC2C,iBAAiB,CAACC,UAAU,CAAC,CAAChC,OAAO,CAACC,OAAO,IAAI;YACjEH,MAAM,CAACC,OAAO,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACjC,OAAO,CAACI,EAAE,IAAI;cACnC,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,EAAE;gBACtB,IAAIG,EAAE,CAAC,CAAC,CAAC,CAACF,UAAU,KAAKD,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE;kBAC5CE,EAAE,CAAC,CAAC,CAAC,CAACF,UAAU,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU;gBAC5C,CAAC,MAAM;kBACHJ,MAAM,CAACC,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAACD,OAAO,CAACmC,MAAM,IAAI;oBACzC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;sBAC5BrC,MAAM,CAACC,OAAO,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC,CAACJ,OAAO,CAACoC,GAAG,IAAI;wBACjC,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;0BACzB,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAKD,MAAM,CAAC,CAAC,CAAC,EAAE;4BACtB,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAClC,UAAU,KAAK2B,SAAS,IAAIM,MAAM,CAAC,CAAC,CAAC,CAACjC,UAAU,KAAK2B,SAAS,EAAE;8BACvEO,GAAG,CAAC,CAAC,CAAC,CAAClC,UAAU,GAAGiC,MAAM,CAAC,CAAC,CAAC,CAACjC,UAAU;8BACxCkC,GAAG,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACE,QAAQ;4BACxC;0BACJ;wBACJ;sBACJ,CAAC,CAAC;oBACN;kBACJ,CAAC,CAAC;gBACN;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAChE,QAAQ,CAAC;MAAEW,SAAS,EAAE;IAAM,CAAC,CAAC;EACvC;EAMAsD,MAAMA,CAAA,EAAG;IACL,IAAI7C,IAAI,GAAGJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAACO,WAAW,CAAC,CAAC;IACrD,IAAI0C,IAAI,GAAG/E,QAAQ;IACnB,IAAIgF,MAAM,GAAG,EAAE;IACf,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAI,IAAI,CAAC9D,KAAK,CAACK,SAAS,EAAE;MACtB,oBAAOjB,OAAA;QAAK2E,SAAS,EAAC,KAAK;QAAAC,QAAA,eAAC5E,OAAA,CAACF,WAAW;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACrD;IACA,oBACIhF,OAAA;MAAK2E,SAAS,EAAE,IAAI,CAAC/D,KAAK,CAACC,SAAU;MAAA+D,QAAA,gBACjC5E,OAAA,CAACJ,KAAK;QAACqF,GAAG,EAAG5C,EAAE,IAAK,IAAI,CAACkB,KAAK,GAAGlB;MAAG;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtCjD,MAAM,CAACC,OAAO,CAACnC,IAAI,CAAC6B,IAAI,CAAC,CAAC,CAACwD,GAAG,CAAC,CAAChD,OAAO,EAAEiD,KAAK,KAAK;QAChD;QACA,IAAIjD,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU,KAAK,MAAM,EAAE;UAClCuC,QAAQ,GAAG,qBAAqB;QACpC,CAAC,MAAM;UACHA,QAAQ,GAAG,4BAA4B;QAC3C;QACA,oBACI1E,OAAA,CAACZ,QAAQ;UAAAwF,QAAA,eACL5E,OAAA;YAAK2E,SAAS,EAAED,QAAS;YAAAE,QAAA,gBACrB5E,OAAA;cAAK2E,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAC9F5E,OAAA;gBAAK2E,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACnE5E,OAAA;kBAAK2E,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,eAC5C5E,OAAA;oBAAUoF,IAAI,EAAE,EAAG;oBAACpC,IAAI,EAAEd,OAAO,CAAC,CAAC,CAAC,CAACE;kBAAS;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAClBJ,IAAI,CAACtC,OAAO,CAAC,CAAC,CAAC;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACL,IAAI,CAACpE,KAAK,CAACG,QAAQ,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,iBAC9ClC,OAAA;gBAAK2E,SAAS,EAAC,gDAAgD;gBAACU,KAAK,EAAE;kBAAEC,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,eAClG5E,OAAA,CAACL,QAAQ;kBAACgF,SAAS,EAAC,qBAAqB;kBAACnE,KAAK,EAAE,IAAI,CAACI,KAAK,CAACL,iBAAkB;kBAACgF,OAAO,EAAE,IAAI,CAACrE,SAAU;kBAACsE,QAAQ,EAAE,IAAI,CAACpF,iBAAkB;kBAACqF,WAAW,EAAC,MAAM;kBAACC,WAAW,EAAC,qBAAqB;kBAACC,MAAM;kBAACC,QAAQ,EAAC;gBAAM;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtB5E,OAAA;gBAAKkD,EAAE,EAAC,WAAW;gBAAA0B,QAAA,EACd7C,MAAM,CAACC,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAACgD,GAAG,CAAC,CAACW,IAAI,EAAEC,IAAI,KAAK;kBAC5C,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;oBAAA,IAAAE,MAAA,EAAAC,OAAA;oBAC1B,IAAI,EAAAD,MAAA,GAAAF,IAAI,CAAC,CAAC,CAAC,cAAAE,MAAA,uBAAPA,MAAA,CAASzB,QAAQ,MAAK,MAAM,EAAE;sBAC9BG,MAAM,GAAG,uCAAuC;oBACpD,CAAC,MAAM,IAAI,IAAI,CAACtE,KAAK,CAACmE,QAAQ,KAAK,IAAI,EAAE;sBACrCG,MAAM,GAAG,uCAAuC;oBACpD,CAAC,MAAM;sBACHA,MAAM,GAAG,qCAAqC;oBAClD;oBACA,IAAI,EAAAuB,OAAA,GAAAH,IAAI,CAAC,CAAC,CAAC,cAAAG,OAAA,uBAAPA,OAAA,CAAS7D,UAAU,MAAK,OAAO,EAAE;sBAAA,IAAA8D,OAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,OAAA;sBACjC,oBACIpG,OAAA,CAACZ,QAAQ;wBAAAwF,QAAA,EACJiB,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,iBACnB7F,OAAA;0BAAK2E,SAAS,EAAC,MAAM;0BAAAC,QAAA,eACjB5E,OAAA;4BAAK2E,SAAS,EAAC,YAAY;4BAAAC,QAAA,eACvB5E,OAAA;8BAAI2E,SAAS,EAAC,iBAAiB;8BAAAC,QAAA,eAC3B5E,OAAA,CAACR,OAAO;gCAAC6G,EAAE,EAAE,GAAG,GAAG3E,IAAI,GAAG,GAAG,GAAGmE,IAAI,CAAC,CAAC,CAAE;gCAAClB,SAAS,EAAEF,MAAO;gCAAAG,QAAA,GAAEiB,IAAI,CAAC,CAAC,CAAC,CAACS,QAAQ,CAAC,GAAG,CAAC,GAAGT,IAAI,CAAC,CAAC,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG/B,IAAI,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC,EACrHA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,MAAAI,OAAA,GAAIJ,IAAI,CAAC,CAAC,CAAC,cAAAI,OAAA,uBAAPA,OAAA,CAASO,KAAK,CAACF,QAAQ,CAAC,UAAU,CAAC,kBAC1DtG,OAAA;kCAAM2E,SAAS,GAAAuB,OAAA,GAAEL,IAAI,CAAC,CAAC,CAAC,cAAAK,OAAA,uBAAPA,OAAA,CAASM;gCAAM;kCAAA3B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,EAE3Ca,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,IAAI,GAAAM,OAAA,GAACN,IAAI,CAAC,CAAC,CAAC,cAAAM,OAAA,eAAPA,OAAA,CAASK,KAAK,CAACF,QAAQ,CAAC,UAAU,CAAC,kBAC3DtG,OAAA;kCAAUgD,IAAI,GAAAoD,OAAA,GAAEP,IAAI,CAAC,CAAC,CAAC,cAAAO,OAAA,uBAAPA,OAAA,CAASI;gCAAM;kCAAA3B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAW,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAE1C;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC,GAfCc,IAAI;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBT,CAAC;oBAEnB;oBACA,oBAAQhF,OAAA,aAAW6F,IAAI;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBACpC;kBACA,oBAAQhF,OAAA,aAAW8F,IAAI;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBACpC,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAED;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GA1DKG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2DV,CAAC;MAEnB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAED,CAAC;EAEd;AACJ;AAEA,eAAe/E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}