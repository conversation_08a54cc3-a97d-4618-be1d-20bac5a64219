"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinalResults = void 0;
const final_plan_js_1 = require("./final-plan.js");
/**
 * The summary results provided in the `complete` event when the TAP
 * stream ends.
 */
class FinalResults {
    ok;
    count;
    pass;
    fail;
    bailout;
    todo;
    skip;
    failures;
    time;
    passes;
    plan;
    skips;
    todos;
    constructor(skipAll, parser) {
        this.ok = parser.ok;
        this.count = parser.count;
        this.pass = parser.pass;
        this.fail = parser.fail || 0;
        this.bailout = parser.bailedOut || false;
        this.todo = parser.todo || 0;
        this.skip = skipAll ? parser.count : parser.skip || 0;
        this.plan = new final_plan_js_1.FinalPlan(skipAll, parser);
        this.failures = parser.failures;
        this.passes = parser.passes || undefined;
        this.time = parser.time;
        this.skips = parser.skips;
        this.todos = parser.todos;
    }
}
exports.FinalResults = FinalResults;
//# sourceMappingURL=final-results.js.map