# 🚀 Setup Iniziale E-Procurement Frontend

Guida completa per configurare l'ambiente di sviluppo del progetto E-Procurement Frontend.

## 📋 Prerequisiti

### Software Richiesto
- **Node.js** (versione 16.x o superiore)
- **npm** (versione 8.x o superiore)
- **Git** (versione 2.x o superiore)
- **Docker** (opzionale, per deployment)

### Account Necessari
- **GitHub** (per version control)
- **Vercel** (per deployment)

## 🔧 Installazione

### 1. Clone del Repository
```bash
git clone https://github.com/Vincenzo-krsc/ep-frontend.git
cd ep-frontend
```

### 2. Installazione Dipendenze
```bash
npm install
```

### 3. Configurazione Ambiente

#### File `.env.local` (Development)
```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_DEBUG=true
REACT_APP_ENVIRONMENT=development
```

#### File `.env.production` (Production)
```env
REACT_APP_API_URL=https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app
REACT_APP_DEBUG=false
REACT_APP_ENVIRONMENT=production
```

## 🏃‍♂️ Avvio Progetto

### Development Server
```bash
npm start
```
Il progetto sarà disponibile su `http://localhost:3000`

### Build Production
```bash
npm run build
```

### Test
```bash
npm test
```

## 🌐 Configurazione Backend

### Backend Locale
- URL: `http://localhost:3001`
- Porta: `3001`
- Autenticazione: Header `auth` con JWT token

### Backend Production
- URL: `https://epbackend-kekl2s5de-vincenzo-2210s-projects.vercel.app`
- HTTPS: Obbligatorio
- CORS: Abilitato
- Autenticazione: Bearer token

## 📁 Struttura Progetto

```
ep-frontend/
├── docs/                    # Documentazione
├── public/                  # File statici
├── src/
│   ├── components/         # Componenti riutilizzabili
│   ├── common/            # Componenti comuni per ruoli
│   ├── aggiunta_dati/     # Form di inserimento
│   ├── css/               # Stili CSS
│   └── utils/             # Utility functions
├── tests/                 # Test automatizzati
└── cypress/              # Test E2E
```

## 🔑 Variabili Ambiente

| Variabile | Descrizione | Valore Dev | Valore Prod |
|-----------|-------------|------------|-------------|
| `REACT_APP_API_URL` | URL Backend | `http://localhost:3001` | `https://epbackend-...` |
| `REACT_APP_DEBUG` | Debug mode | `true` | `false` |
| `REACT_APP_ENVIRONMENT` | Ambiente | `development` | `production` |

## 🐛 Troubleshooting

### Problemi Comuni

#### 1. Errore "Module not found"
```bash
rm -rf node_modules package-lock.json
npm install
```

#### 2. Errore CORS
Verificare configurazione backend e URL in `.env`

#### 3. Errore di autenticazione
Controllare token JWT e header `auth`

### Log di Debug
Abilitare debug con `REACT_APP_DEBUG=true` per log dettagliati nella console.

## 📞 Supporto

- **Documentazione**: [docs/README.md](../README.md)
- **Testing**: [docs/testing/](../testing/)
- **Development Log**: [docs/development/development-log.md](../development/development-log.md)

---

**Ultimo aggiornamento**: 2025-01-16
