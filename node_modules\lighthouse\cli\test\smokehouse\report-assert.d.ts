/// <reference path="../../../../../types/internal/lighthouse-logger.d.ts" />
export type Difference = {
    path: string;
    actual: any;
    expected: any;
};
export type Comparison = {
    name: string;
    actual: any;
    expected: any;
    equal: boolean;
    diffs: Difference[] | null;
};
/**
 * Log all the comparisons between actual and expected test results, then print
 * summary. Returns count of passed and failed tests.
 * @param {{lhr: LH.Result, artifacts: LH.Artifacts, networkRequests?: string[]}} actual
 * @param {Smokehouse.ExpectedRunnerResult} expected
 * @param {{runner?: string, isDebug?: boolean, useLegacyNavigation?: boolean}=} reportOptions
 * @return {{passed: number, failed: number, log: string}}
 */
export function getAssertionReport(actual: {
    lhr: LH.Result;
    artifacts: LH.Artifacts;
    networkRequests?: string[];
}, expected: Smokehouse.ExpectedRunnerResult, reportOptions?: {
    runner?: string;
    isDebug?: boolean;
    useLegacyNavigation?: boolean;
} | undefined): {
    passed: number;
    failed: number;
    log: string;
};
/**
 * Walk down expected result, comparing to actual result. If a difference is found,
 * the path to the difference is returned, along with the expected primitive value
 * and the value actually found at that location. If no difference is found, returns
 * null.
 *
 * Only checks own enumerable properties, not object prototypes, and will loop
 * until the stack is exhausted, so works best with simple objects (e.g. parsed JSON).
 * @param {string} path
 * @param {*} actual
 * @param {*} expected
 * @return {Difference[]|null}
 */
export function findDifferences(path: string, actual: any, expected: any): Difference[] | null;
import log from "lighthouse-logger";
//# sourceMappingURL=report-assert.d.ts.map