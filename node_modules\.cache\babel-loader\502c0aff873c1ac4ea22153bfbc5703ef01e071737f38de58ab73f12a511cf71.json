{"ast": null, "code": "import Provider from './components/Provider';\nimport connectAdvanced from './components/connectAdvanced';\nimport { ReactReduxContext } from './components/Context';\nimport connect from './connect/connect';\nimport { useDispatch, createDispatchHook } from './hooks/useDispatch';\nimport { useSelector, createSelectorHook } from './hooks/useSelector';\nimport { useStore, createStoreHook } from './hooks/useStore';\nimport shallowEqual from './utils/shallowEqual';\nexport { Provider, connectAdvanced, ReactReduxContext, connect, useDispatch, createDispatchHook, useSelector, createSelectorHook, useStore, createStoreHook, shallowEqual };", "map": {"version": 3, "names": ["Provider", "connectAdvanced", "ReactReduxContext", "connect", "useDispatch", "createDispatchHook", "useSelector", "createSelectorHook", "useStore", "createStoreHook", "shallowEqual"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-redux/es/exports.js"], "sourcesContent": ["import Provider from './components/Provider';\nimport connectAdvanced from './components/connectAdvanced';\nimport { ReactReduxContext } from './components/Context';\nimport connect from './connect/connect';\nimport { useDispatch, createDispatchHook } from './hooks/useDispatch';\nimport { useSelector, createSelectorHook } from './hooks/useSelector';\nimport { useStore, createStoreHook } from './hooks/useStore';\nimport shallowEqual from './utils/shallowEqual';\nexport { Provider, connectAdvanced, ReactReduxContext, connect, useDispatch, createDispatchHook, useSelector, createSelectorHook, useStore, createStoreHook, shallowEqual };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,qBAAqB;AACrE,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,qBAAqB;AACrE,SAASC,QAAQ,EAAEC,eAAe,QAAQ,kBAAkB;AAC5D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASV,QAAQ,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}