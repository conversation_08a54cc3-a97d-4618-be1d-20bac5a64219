{"version": 3, "file": "valid-project.js", "sourceRoot": "", "sources": ["../../src/valid-project.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAC9B,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAA;AACtC,OAAO,IAAI,MAAM,WAAW,CAAA;AAG5B,eAAe,CAAC,CAAM,EAA8B,EAAE;IACpD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;YAChC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;IAChB,CAAC;IAED,IAAI,CACF,sDAAsD;QACpD,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC9B,CAAA;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAA", "sourcesContent": ["import { resolve } from 'path'\nimport { readFileSync } from 'node:fs'\nimport fail from './fail.js'\nimport { TshyConfig } from './types.js'\n\nexport default (p: any): p is TshyConfig['project'] => {\n  if (typeof p === 'string') {\n    try {\n      readFileSync(resolve(p), 'utf8')\n      return true\n    } catch (_) {}\n  }\n\n  fail(\n    `tshy.project must point to a tsconfig file on disk, ` +\n      `got: ${JSON.stringify(p)}`,\n  )\n  return process.exit(1)\n}\n"]}