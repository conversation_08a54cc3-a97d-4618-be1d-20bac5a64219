/**
 * @license
 * Copyright 2017 The Lighthouse Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS-IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Logs messages via a UI butter.
 */
export class Logger {
    /**
     * @param {HTMLElement} element - expected to have id #lh-log
     */
    constructor(element: HTMLElement);
    el: HTMLElement;
    _id: number | undefined;
    /**
     * Shows a butter bar.
     * @param {string} msg The message to show.
     * @param {boolean=} autoHide True to hide the message after a duration.
     *     Default is true.
     */
    log(msg: string, autoHide?: boolean | undefined): void;
    /**
     * @param {string} msg
     */
    warn(msg: string): void;
    /**
     * @param {string} msg
     */
    error(msg: string): void;
    /**
     * Explicitly hides the butter bar.
     */
    hide(): void;
}
//# sourceMappingURL=logger.d.ts.map