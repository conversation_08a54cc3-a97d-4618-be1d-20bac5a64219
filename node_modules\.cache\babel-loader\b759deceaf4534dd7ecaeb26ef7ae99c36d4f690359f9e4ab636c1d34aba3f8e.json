{"ast": null, "code": "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar TypeError = global.TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};", "map": {"version": 3, "names": ["global", "require", "call", "isObject", "isSymbol", "getMethod", "ordinaryToPrimitive", "wellKnownSymbol", "TypeError", "TO_PRIMITIVE", "module", "exports", "input", "pref", "exoticToPrim", "result", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/to-primitive.js"], "sourcesContent": ["var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TypeError = global.TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,QAAQ,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAII,SAAS,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAClD,IAAIK,mBAAmB,GAAGL,OAAO,CAAC,oCAAoC,CAAC;AACvE,IAAIM,eAAe,GAAGN,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAIO,SAAS,GAAGR,MAAM,CAACQ,SAAS;AAChC,IAAIC,YAAY,GAAGF,eAAe,CAAC,aAAa,CAAC;;AAEjD;AACA;AACAG,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;EACtC,IAAI,CAACV,QAAQ,CAACS,KAAK,CAAC,IAAIR,QAAQ,CAACQ,KAAK,CAAC,EAAE,OAAOA,KAAK;EACrD,IAAIE,YAAY,GAAGT,SAAS,CAACO,KAAK,EAAEH,YAAY,CAAC;EACjD,IAAIM,MAAM;EACV,IAAID,YAAY,EAAE;IAChB,IAAID,IAAI,KAAKG,SAAS,EAAEH,IAAI,GAAG,SAAS;IACxCE,MAAM,GAAGb,IAAI,CAACY,YAAY,EAAEF,KAAK,EAAEC,IAAI,CAAC;IACxC,IAAI,CAACV,QAAQ,CAACY,MAAM,CAAC,IAAIX,QAAQ,CAACW,MAAM,CAAC,EAAE,OAAOA,MAAM;IACxD,MAAMP,SAAS,CAAC,yCAAyC,CAAC;EAC5D;EACA,IAAIK,IAAI,KAAKG,SAAS,EAAEH,IAAI,GAAG,QAAQ;EACvC,OAAOP,mBAAmB,CAACM,KAAK,EAAEC,IAAI,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}