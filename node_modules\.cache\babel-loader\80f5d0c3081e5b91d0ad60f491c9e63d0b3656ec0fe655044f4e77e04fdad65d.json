{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { ItemGroup } from 'rc-menu';\nimport SubMenu from '../SubMenu';\nimport MenuDivider from '../MenuDivider';\nimport MenuItem from '../MenuItem';\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _a = opt,\n        label = _a.label,\n        children = _a.children,\n        key = _a.key,\n        type = _a.type,\n        restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index); // MenuItemGroup & SubMenuItem\n\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(ItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        } // Sub Menu\n\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      } // MenuItem & Divider\n\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MenuDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n} // FIXME: Move logic here in v5\n\n/**\n * We simply convert `items` to ReactNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v5\n */\n\nexport default function useItems(items) {\n  return React.useMemo(function () {\n    if (!items) {\n      return items;\n    }\n    return convertItemsToNodes(items);\n  }, [items]);\n}", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "ItemGroup", "SubMenu", "MenuDivider", "MenuItem", "convertItemsToNodes", "list", "map", "opt", "index", "_a", "label", "children", "key", "type", "restProps", "mergedKey", "concat", "createElement", "title", "filter", "useItems", "items", "useMemo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/menu/hooks/useItems.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport { ItemGroup } from 'rc-menu';\nimport SubMenu from '../SubMenu';\nimport MenuDivider from '../MenuDivider';\nimport MenuItem from '../MenuItem';\n\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _a = opt,\n          label = _a.label,\n          children = _a.children,\n          key = _a.key,\n          type = _a.type,\n          restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index); // MenuItemGroup & SubMenuItem\n\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(ItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        } // Sub Menu\n\n\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      } // MenuItem & Divider\n\n\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MenuDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n} // FIXME: Move logic here in v5\n\n/**\n * We simply convert `items` to ReactNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v5\n */\n\n\nexport default function useItems(items) {\n  return React.useMemo(function () {\n    if (!items) {\n      return items;\n    }\n\n    return convertItemsToNodes(items);\n  }, [items]);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AAEvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAElC,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAID,GAAG,IAAIvB,OAAO,CAACuB,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIE,EAAE,GAAGF,GAAG;QACRG,KAAK,GAAGD,EAAE,CAACC,KAAK;QAChBC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;QACtBC,GAAG,GAAGH,EAAE,CAACG,GAAG;QACZC,IAAI,GAAGJ,EAAE,CAACI,IAAI;QACdC,SAAS,GAAG7B,MAAM,CAACwB,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;MAEhE,IAAIM,SAAS,GAAGH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,MAAM,CAACI,MAAM,CAACR,KAAK,CAAC,CAAC,CAAC;;MAE7E,IAAIG,QAAQ,IAAIE,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB;UACA,OAAO,aAAad,KAAK,CAACkB,aAAa,CAACjB,SAAS,EAAEjB,QAAQ,CAAC;YAC1D6B,GAAG,EAAEG;UACP,CAAC,EAAED,SAAS,EAAE;YACZI,KAAK,EAAER;UACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;;QAGF,OAAO,aAAaZ,KAAK,CAACkB,aAAa,CAAChB,OAAO,EAAElB,QAAQ,CAAC;UACxD6B,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,EAAE;UACZI,KAAK,EAAER;QACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;MACpC,CAAC,CAAC;;MAGF,IAAIE,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,aAAad,KAAK,CAACkB,aAAa,CAACf,WAAW,EAAEnB,QAAQ,CAAC;UAC5D6B,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,CAAC,CAAC;MAChB;MAEA,OAAO,aAAaf,KAAK,CAACkB,aAAa,CAACd,QAAQ,EAAEpB,QAAQ,CAAC;QACzD6B,GAAG,EAAEG;MACP,CAAC,EAAED,SAAS,CAAC,EAAEJ,KAAK,CAAC;IACvB;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,CAACS,MAAM,CAAC,UAAUZ,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;;AAGA,eAAe,SAASa,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOtB,KAAK,CAACuB,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACD,KAAK,EAAE;MACV,OAAOA,KAAK;IACd;IAEA,OAAOjB,mBAAmB,CAACiB,KAAK,CAAC;EACnC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}