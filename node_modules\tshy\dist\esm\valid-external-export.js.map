{"version": 3, "file": "valid-external-export.js", "sourceRoot": "", "sources": ["../../src/valid-external-export.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAEjC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAA;AAEnD,eAAe,CAAC,GAAQ,EAAiC,EAAE;IACzD,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;IACxC,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;IACzC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;QAAE,OAAO,KAAK,CAAA;IACjD,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;QAAE,OAAO,KAAK,CAAA;IACjD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["import { join } from 'path/posix'\nimport type { ConditionalValueObject } from 'resolve-import'\nimport { resolveExport } from './resolve-export.js'\n\nexport default (exp: any): exp is ConditionalValueObject => {\n  const i = resolveExport(exp, ['import'])\n  const r = resolveExport(exp, ['require'])\n  if (i && join(i).startsWith('src/')) return false\n  if (r && join(r).startsWith('src/')) return false\n  return true\n}\n"]}