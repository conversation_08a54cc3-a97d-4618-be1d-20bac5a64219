export default PreloadFontsAudit;
declare class PreloadFontsAudit extends Audit {
    /**
     * Finds which font URLs were attempted to be preloaded,
     * ignoring those that failed to be reused and were requested again.
     * Note: document.fonts.load() is a valid way to preload fonts,
     * but we are not currently checking for that.
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Set<string>}
     */
    static getURLsAttemptedToPreload(networkRecords: Array<LH.Artifacts.NetworkRequest>): Set<string>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit_(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
    /**
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=preload-fonts.d.ts.map