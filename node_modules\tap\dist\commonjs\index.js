"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
/**
 * The main tap export, CJS style
 *
 * The only different between this and the ESM export is that, because a
 * CJS default export cannot co-exist with exported types, we have to make
 * the types available as a global namespace. Which, isn't exactly the most
 * elegant thing in the world, since it can conflict with any other module
 * that defines a `tap` global namespace, but at least it's common enough
 * that it doesn't read as too strange or unintuitive.
 *
 * @module
 */
//@ts-ignore
const items = __importStar(require("./main.js"));
//@ts-ignore
const main_js_1 = require("./main.js");
// this has to be done in a somewhat tricky way, because Test objects are
// actually proxies, in order to host all of their plugins. Those proxies
// are necessarily clever about providing methods that are bound to the
// appropriate plugin object, so we have to define them here as getters.
// This is all to prevent the need for `const t = require('tap').default`,
// which just offends me esthetically.
//
// The unfortunate side effect of this hybrid approach with a default
// export is that TS does not provide a way to export both types AND
// a default defined with `export =`
for (const [key, val] of Object.entries(items)) {
    Object.defineProperty(main_js_1.t, key, {
        get: () => val,
        enumerable: true,
        configurable: true,
    });
}
Object.defineProperty(main_js_1.t, 'default', {
    get: () => main_js_1.t,
    enumerable: true,
    configurable: true,
});
module.exports = main_js_1.t;
//# sourceMappingURL=index-cjs.cjs.map