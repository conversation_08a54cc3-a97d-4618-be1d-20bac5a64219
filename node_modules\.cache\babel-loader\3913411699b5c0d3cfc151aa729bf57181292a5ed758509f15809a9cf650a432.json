{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { warning } from \"rc-util/es/warning\";\nimport SingleObserver from './SingleObserver';\nimport { Collection } from './Collection';\nvar INTERNAL_PREFIX_KEY = 'rc-observer-key';\nfunction ResizeObserver(props) {\n  var children = props.children;\n  var childNodes = typeof children === 'function' ? [children] : toArray(children);\n  if (process.env.NODE_ENV !== 'production') {\n    if (childNodes.length > 1) {\n      warning(false, 'Find more than one child node with `children` in ResizeObserver. Please use ResizeObserver.Collection instead.');\n    } else if (childNodes.length === 0) {\n      warning(false, '`children` of ResizeObserver is empty. Nothing is in observe.');\n    }\n  }\n  return childNodes.map(function (child, index) {\n    var key = (child === null || child === void 0 ? void 0 : child.key) || \"\".concat(INTERNAL_PREFIX_KEY, \"-\").concat(index);\n    return /*#__PURE__*/React.createElement(SingleObserver, _extends({}, props, {\n      key: key\n    }), child);\n  });\n}\nResizeObserver.Collection = Collection;\nexport default ResizeObserver;", "map": {"version": 3, "names": ["_extends", "React", "toArray", "warning", "SingleObserver", "Collection", "INTERNAL_PREFIX_KEY", "ResizeObserver", "props", "children", "childNodes", "process", "env", "NODE_ENV", "length", "map", "child", "index", "key", "concat", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-resize-observer/es/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { warning } from \"rc-util/es/warning\";\nimport SingleObserver from './SingleObserver';\nimport { Collection } from './Collection';\nvar INTERNAL_PREFIX_KEY = 'rc-observer-key';\n\nfunction ResizeObserver(props) {\n  var children = props.children;\n  var childNodes = typeof children === 'function' ? [children] : toArray(children);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (childNodes.length > 1) {\n      warning(false, 'Find more than one child node with `children` in ResizeObserver. Please use ResizeObserver.Collection instead.');\n    } else if (childNodes.length === 0) {\n      warning(false, '`children` of ResizeObserver is empty. Nothing is in observe.');\n    }\n  }\n\n  return childNodes.map(function (child, index) {\n    var key = (child === null || child === void 0 ? void 0 : child.key) || \"\".concat(INTERNAL_PREFIX_KEY, \"-\").concat(index);\n    return /*#__PURE__*/React.createElement(SingleObserver, _extends({}, props, {\n      key: key\n    }), child);\n  });\n}\n\nResizeObserver.Collection = Collection;\nexport default ResizeObserver;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,IAAIC,mBAAmB,GAAG,iBAAiB;AAE3C,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAIC,UAAU,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAG,CAACA,QAAQ,CAAC,GAAGP,OAAO,CAACO,QAAQ,CAAC;EAEhF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACzBX,OAAO,CAAC,KAAK,EAAE,gHAAgH,CAAC;IAClI,CAAC,MAAM,IAAIO,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;MAClCX,OAAO,CAAC,KAAK,EAAE,+DAA+D,CAAC;IACjF;EACF;EAEA,OAAOO,UAAU,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC5C,IAAIC,GAAG,GAAG,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,KAAK,EAAE,CAACC,MAAM,CAACb,mBAAmB,EAAE,GAAG,CAAC,CAACa,MAAM,CAACF,KAAK,CAAC;IACxH,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAAChB,cAAc,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;MAC1EU,GAAG,EAAEA;IACP,CAAC,CAAC,EAAEF,KAAK,CAAC;EACZ,CAAC,CAAC;AACJ;AAEAT,cAAc,CAACF,UAAU,GAAGA,UAAU;AACtC,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}