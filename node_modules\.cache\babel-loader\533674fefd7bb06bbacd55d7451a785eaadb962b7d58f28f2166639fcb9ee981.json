{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport defaultRenderEmpty from './renderEmpty';\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\nexport var ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  renderEmpty: defaultRenderEmpty\n});\nexport var ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\n\nexport function withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, _extends({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    return SFC;\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "defaultRenderEmpty", "defaultGetPrefixCls", "suffixCls", "customizePrefixCls", "concat", "ConfigContext", "createContext", "getPrefixCls", "renderEmpty", "ConfigConsumer", "Consumer", "withConfigConsumer", "config", "withConfigConsumerFunc", "Component", "SFC", "props", "createElement", "configProps", "basicPrefixCls", "prefixCls", "cons", "constructor", "name", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/config-provider/context.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport defaultRenderEmpty from './renderEmpty';\n\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffixCls, customizePrefixCls) {\n  if (customizePrefixCls) return customizePrefixCls;\n  return suffixCls ? \"ant-\".concat(suffixCls) : 'ant';\n};\n\nexport var ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  renderEmpty: defaultRenderEmpty\n});\nexport var ConfigConsumer = ConfigContext.Consumer;\n/** @deprecated Use hooks instead. This is a legacy function */\n\nexport function withConfigConsumer(config) {\n  return function withConfigConsumerFunc(Component) {\n    // Wrap with ConfigConsumer. Since we need compatible with react 15, be care when using ref methods\n    var SFC = function SFC(props) {\n      return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (configProps) {\n        var basicPrefixCls = config.prefixCls;\n        var getPrefixCls = configProps.getPrefixCls;\n        var customizePrefixCls = props.prefixCls;\n        var prefixCls = getPrefixCls(basicPrefixCls, customizePrefixCls);\n        return /*#__PURE__*/React.createElement(Component, _extends({}, configProps, props, {\n          prefixCls: prefixCls\n        }));\n      });\n    };\n\n    var cons = Component.constructor;\n    var name = cons && cons.displayName || Component.name || 'Component';\n    SFC.displayName = \"withConfigConsumer(\".concat(name, \")\");\n    return SFC;\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,eAAe;AAE9C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,SAAS,EAAEC,kBAAkB,EAAE;EACpF,IAAIA,kBAAkB,EAAE,OAAOA,kBAAkB;EACjD,OAAOD,SAAS,GAAG,MAAM,CAACE,MAAM,CAACF,SAAS,CAAC,GAAG,KAAK;AACrD,CAAC;AAED,OAAO,IAAIG,aAAa,GAAG,aAAaN,KAAK,CAACO,aAAa,CAAC;EAC1D;EACAC,YAAY,EAAEN,mBAAmB;EACjCO,WAAW,EAAER;AACf,CAAC,CAAC;AACF,OAAO,IAAIS,cAAc,GAAGJ,aAAa,CAACK,QAAQ;AAClD;;AAEA,OAAO,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,SAASC,sBAAsBA,CAACC,SAAS,EAAE;IAChD;IACA,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,KAAK,EAAE;MAC5B,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACR,cAAc,EAAE,IAAI,EAAE,UAAUS,WAAW,EAAE;QACnF,IAAIC,cAAc,GAAGP,MAAM,CAACQ,SAAS;QACrC,IAAIb,YAAY,GAAGW,WAAW,CAACX,YAAY;QAC3C,IAAIJ,kBAAkB,GAAGa,KAAK,CAACI,SAAS;QACxC,IAAIA,SAAS,GAAGb,YAAY,CAACY,cAAc,EAAEhB,kBAAkB,CAAC;QAChE,OAAO,aAAaJ,KAAK,CAACkB,aAAa,CAACH,SAAS,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,WAAW,EAAEF,KAAK,EAAE;UAClFI,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC;IAED,IAAIC,IAAI,GAAGP,SAAS,CAACQ,WAAW;IAChC,IAAIC,IAAI,GAAGF,IAAI,IAAIA,IAAI,CAACG,WAAW,IAAIV,SAAS,CAACS,IAAI,IAAI,WAAW;IACpER,GAAG,CAACS,WAAW,GAAG,qBAAqB,CAACpB,MAAM,CAACmB,IAAI,EAAE,GAAG,CAAC;IACzD,OAAOR,GAAG;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}