{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\PDV\\\\carrello\\\\riepilogoOrdinePDV.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Carrello - operazioni sul carrello\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from '../../../components/navigation/Nav';\nimport CarrelloGen from '../../../components/generalizzazioni/marketplace/carrello';\nimport { Costanti } from '../../../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../../../css/carrello.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Cart extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      results2: null,\n      datiConsegna: null,\n      loading: true\n    };\n  }\n  //Chiamata axios effettuata una sola volta grazie a componentDidMount\n  async componentDidMount() {\n    var prodotti = [];\n    if (localStorage.getItem(\"Cart\") !== '') {\n      prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n    } else {\n      prodotti = [];\n    }\n    if (prodotti.length > 0) {\n      for (var i = 0; i < prodotti.length; i++) {\n        if (prodotti[i].total === undefined) {\n          var totale = [];\n          totale = prodotti;\n          totale[i].totale = parseFloat(prodotti[i].price).toFixed(2) * prodotti[i].quantity;\n          prodotti = totale;\n        } else {\n          prodotti[i].idProduct2 = prodotti[i].product;\n          prodotti[i].price = prodotti[i].unitPrice;\n          prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity;\n        }\n      }\n    }\n    if (localStorage.getItem(\"DatiConsegna\") !== '') {\n      var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n    }\n    var userid = JSON.parse(localStorage.getItem(\"userid\"));\n    this.setState({\n      results: userid,\n      results2: prodotti,\n      datiConsegna: dati,\n      loading: false\n    });\n  }\n  render() {\n    if (this.state.loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card form-complete border-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card form-complete border-0\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {\n        disabled: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Riepilogo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CarrelloGen, {\n        results: this.state.results,\n        results2: this.state.results2,\n        datiConsegna: this.state.datiConsegna\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Cart;", "map": {"version": 3, "names": ["React", "Component", "Nav", "CarrelloGen", "<PERSON><PERSON>", "Toast", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "constructor", "props", "state", "results", "results2", "dati<PERSON>ons<PERSON>na", "loading", "componentDidMount", "prodotti", "localStorage", "getItem", "JSON", "parse", "length", "i", "total", "undefined", "totale", "parseFloat", "price", "toFixed", "quantity", "idProduct2", "product", "unitPrice", "dati", "userid", "setState", "render", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "disabled", "ref", "el", "toast", "Riepilogo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/riepilogoOrdinePDV.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Carrello - operazioni sul carrello\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from '../../../components/navigation/Nav';\nimport CarrelloGen from '../../../components/generalizzazioni/marketplace/carrello';\nimport { Costanti } from '../../../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../../../css/carrello.css';\n\nclass Cart extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            datiConsegna: null,\n            loading: true\n        }\n    }\n    //Chiamata axios effettuata una sola volta grazie a componentDidMount\n    async componentDidMount() {\n        var prodotti = []\n        if (localStorage.getItem(\"Cart\") !== '') {\n            prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n        } else {\n            prodotti = []\n        }\n        if (prodotti.length > 0) {\n            for (var i = 0; i < prodotti.length; i++) {\n                if (prodotti[i].total === undefined) {\n                    var totale = [];\n                    totale = prodotti;\n                    totale[i].totale = parseFloat(prodotti[i].price).toFixed(2) * prodotti[i].quantity;\n                    prodotti = totale;\n                } else {\n                    prodotti[i].idProduct2 = prodotti[i].product;\n                    prodotti[i].price = prodotti[i].unitPrice\n                    prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity\n                }\n            }\n        }\n        if (localStorage.getItem(\"DatiConsegna\") !== '') {\n            var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n        }\n        var userid = JSON.parse(localStorage.getItem(\"userid\"));\n        this.setState({\n            results: userid,\n            results2: prodotti,\n            datiConsegna: dati,\n            loading: false\n        })\n    }\n    render() {\n        if (this.state.loading) {\n            return (\n                <div className=\"card form-complete border-0\"></div>\n            )\n        }\n        return (\n            <div className=\"card form-complete border-0\">\n                <Nav disabled={false} />\n                <Toast ref={(el) => this.toast = el} />\n                <div className=\"col-12\">\n                    <h1>{Costanti.Riepilogo}</h1>\n                </div>\n                <hr />\n                <CarrelloGen results={this.state.results} results2={this.state.results2} datiConsegna={this.state.datiConsegna} />\n            </div>\n        );\n    }\n}\n\nexport default Cart;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,oCAAoC;AACpD,OAAOC,WAAW,MAAM,2DAA2D;AACnF,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,IAAI,SAASP,SAAS,CAAC;EACzBQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACb,CAAC;EACL;EACA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACrCF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC,MAAM;MACHF,QAAQ,GAAG,EAAE;IACjB;IACA,IAAIA,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAE;MACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACK,MAAM,EAAEC,CAAC,EAAE,EAAE;QACtC,IAAIN,QAAQ,CAACM,CAAC,CAAC,CAACC,KAAK,KAAKC,SAAS,EAAE;UACjC,IAAIC,MAAM,GAAG,EAAE;UACfA,MAAM,GAAGT,QAAQ;UACjBS,MAAM,CAACH,CAAC,CAAC,CAACG,MAAM,GAAGC,UAAU,CAACV,QAAQ,CAACM,CAAC,CAAC,CAACK,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGZ,QAAQ,CAACM,CAAC,CAAC,CAACO,QAAQ;UAClFb,QAAQ,GAAGS,MAAM;QACrB,CAAC,MAAM;UACHT,QAAQ,CAACM,CAAC,CAAC,CAACQ,UAAU,GAAGd,QAAQ,CAACM,CAAC,CAAC,CAACS,OAAO;UAC5Cf,QAAQ,CAACM,CAAC,CAAC,CAACK,KAAK,GAAGX,QAAQ,CAACM,CAAC,CAAC,CAACU,SAAS;UACzChB,QAAQ,CAACM,CAAC,CAAC,CAACG,MAAM,GAAGC,UAAU,CAACV,QAAQ,CAACM,CAAC,CAAC,CAACU,SAAS,CAAC,CAACJ,OAAO,CAAC,CAAC,CAAC,GAAGZ,QAAQ,CAACM,CAAC,CAAC,CAACO,QAAQ;QAC5F;MACJ;IACJ;IACA,IAAIZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE;MAC7C,IAAIe,IAAI,GAAGd,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC/D;IACA,IAAIgB,MAAM,GAAGf,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACvD,IAAI,CAACiB,QAAQ,CAAC;MACVxB,OAAO,EAAEuB,MAAM;MACftB,QAAQ,EAAEI,QAAQ;MAClBH,YAAY,EAAEoB,IAAI;MAClBnB,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACAsB,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC1B,KAAK,CAACI,OAAO,EAAE;MACpB,oBACIR,OAAA;QAAK+B,SAAS,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAE3D;IACA,oBACInC,OAAA;MAAK+B,SAAS,EAAC,6BAA6B;MAAAK,QAAA,gBACxCpC,OAAA,CAACL,GAAG;QAAC0C,QAAQ,EAAE;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxBnC,OAAA,CAACF,KAAK;QAACwC,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACC,KAAK,GAAGD;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCnC,OAAA;QAAK+B,SAAS,EAAC,QAAQ;QAAAK,QAAA,eACnBpC,OAAA;UAAAoC,QAAA,EAAKvC,QAAQ,CAAC4C;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnC,OAAA,CAACJ,WAAW;QAACS,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAQ;QAACC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE,QAAS;QAACC,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjH,CAAC;EAEd;AACJ;AAEA,eAAelC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}