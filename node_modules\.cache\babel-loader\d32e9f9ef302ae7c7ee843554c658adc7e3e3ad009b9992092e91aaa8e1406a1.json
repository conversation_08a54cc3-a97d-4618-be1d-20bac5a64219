{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiDocReso.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { Costanti } from '../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport { Button } from 'primereact/button';\nimport { Dropdown } from 'primereact/dropdown';\nimport { AutoComplete } from 'primereact/autocomplete';\nimport CustomDataTable from '../components/customDataTable';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Calendar } from 'primereact/calendar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggiungiDocReso = props => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [totProducts, setTotProducts] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [product, setProduct] = useState(null);\n  const [warehouses, setWarehouses] = useState([]);\n  const [retailers, setRetailers] = useState([]);\n  const [retailer, setRetailer] = useState(null);\n  const [documents, setDocuments] = useState([]);\n  const [data, setData] = useState(null);\n  const [data2, setData2] = useState(null);\n  const [selectedProducts, setSelectedProducts] = useState(null);\n  const [selectedFinalProducts, setSelectedFinalProducts] = useState([]);\n  const [globalFilter, setGlobalFilter] = useState(null);\n  const dt = useRef(null);\n  const toast = useRef(null);\n  useEffect(() => {\n    async function trovaRisultato() {\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'retailers/').then(res => {\n        var x = [];\n        res.data.map(el => x.push({\n          name: el.idRegistry.firstName,\n          code: el.id\n        }));\n        setRetailers(x);\n      }).catch(e => {\n        console.log(e);\n      });\n      await APIRequest('GET', 'warehouses/').then(res => {\n        var x = [];\n        res.data.map(el => x.push({\n          name: el.warehouseName,\n          code: el.id\n        }));\n        setWarehouses(x);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, [props]);\n  const onRetailerSelect = async e => {\n    setLoading(true);\n    setRetailer(e.value);\n  };\n  const onProductSelect = async e => {\n    setProduct(e.value);\n  };\n  const selectHandler = async e => {\n    var prod = selectedProducts !== null ? [...selectedProducts, e.value] : [e.value];\n    setSelectedProducts(prod);\n    await APIRequest('GET', \"statistic/productindocument?idproduct=\".concat(e.value.code, \"&documentType=CLI-ORDINE&idretailer=\").concat(retailer.code).concat(data ? \"&dateFrom=\".concat(data).concat(data2 ? \"&dateTo=\".concat(data2) : '') : '')).then(res => {\n      let documents = [];\n      res.data.forEach(element => {\n        var find = documents.length ? documents.find(el => el.warehouse.code === element.warehouse) : undefined;\n        if (find) {\n          find.venduto_unitario += element.venduto_unitario;\n        } else {\n          documents.push({\n            venduto_unitario: element.venduto_unitario,\n            warehouse: warehouses.find(el => el.code === element.warehouse)\n          });\n        }\n      });\n      setDocuments(documents);\n      setLoading(false);\n    }).catch(e => {\n      console.log(e);\n      setLoading(false);\n    });\n  };\n  const searchProduct = async event => {\n    if (event.query.length > 2) {\n      //Chiamata axios per la visualizzazione dei prodotti\n      await APIRequest('GET', \"products/?find=\".concat(event.query)).then(res => {\n        console.log(res.data);\n        var x = [];\n        res.data.map(el => x.push({\n          name: el.description,\n          code: el.id\n        }));\n        setProducts(x);\n        setTotProducts(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n  };\n  const onRowSelect = async result => {\n    var selProd = [...selectedFinalProducts];\n    var find = totProducts.find(el => el.id === product.code);\n    find.newColli = 0;\n    find.pcsXPackage = 0;\n    find.unitMeasure = '';\n    find.warehouse = result.data.warehouse;\n    var found = selProd.find(el => el.id === find.id);\n    if (found) {\n      toast.current.show({\n        severity: \"warn\",\n        summary: \"Attenzione\",\n        detail: \"Il prodotto selezionato è già stato aggiunto al documento\",\n        life: 3000\n      });\n    } else {\n      selProd.push(find);\n      setSelectedFinalProducts(selProd);\n      setDocuments([]);\n      setProduct(null);\n      setSelectedProducts(null);\n      toast.current.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Il prodotto selezionato è stato aggiunto al documento\",\n        life: 3000\n      });\n    }\n  };\n  /* Reperiamo i colli ordinati per il prodotto */\n  const colliBodyTemplate = results => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Colli\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this), results.newColli]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this);\n  };\n  /* InputNumber per la modifica dei colli */\n  const colliEditor = options => {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.rowData['newColli'],\n      onValueChange: e => onRowEditComplete(e, options)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 16\n    }, this);\n  };\n  /* Modifico quantità del prodotto */\n  const onRowEditComplete = (e, options) => {\n    options.rowData.newColli = e.value;\n  };\n  const crea = async () => {\n    var find = selectedFinalProducts.find(el => el.newColli < 1 || el.unitMeasure === '');\n    var warehouses = selectedFinalProducts.map(el => el.warehouse.code);\n    warehouses = [...new Set(warehouses)];\n    if (!find) {\n      for (const ids of warehouses) {\n        let totProd = [];\n        var filter = selectedFinalProducts.filter(el => el.warehouse.code === ids);\n        filter.forEach(element => {\n          var x = {\n            id: element.id,\n            quantity: element.newColli,\n            idProductsPackaging: element.idProductsPackaging,\n            //Find pkg\n            colliPreventivo: element.newColli,\n            colliConsuntivo: 0,\n            tax: \"\".concat(element.iva, \"%\"),\n            unitPrice: 0,\n            total: 0,\n            totalTaxed: 0\n          };\n          totProd.push(x);\n        });\n        var body = {\n          type: props.documentType,\n          documentDate: new Date(),\n          total: 0,\n          idRetailer: retailer.code,\n          rowBody: totProd\n        };\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', \"documents/?idWarehouses=\".concat(ids), body).then(res => {\n          console.log(res.data);\n          toast.current.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento con id \".concat(res.data.result.id, \" \\xE8 stato inserito con successo\"),\n            life: 3000\n          });\n          /* setTimeout(() => {\n              window.location.reload()\n          }, 3000) */\n        }).catch(e => {\n          var _e$response, _e$response2;\n          console.log(e);\n          toast.current.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n            life: 3000\n          });\n        });\n      }\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    } else {\n      toast.current.show({\n        severity: 'warn',\n        summary: 'Attenzione',\n        detail: \"Indicare i colli ed il confezionamento dei prodotti resi dal cliente prima di creare il documento\",\n        life: 3000\n      });\n    }\n  };\n  const unitMisBodyTemplate = results => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UnitMis\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this), results.unitMeasure]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this);\n  };\n  const onEditComplete = (e, options) => {\n    console.log(options);\n    var finalProducts = [...selectedFinalProducts];\n    finalProducts.forEach(element => {\n      if (element.id === options.rowData.id) {\n        var find = options.rowData.productsPackagings.find(el => el.pcsXPackage === e);\n        options.rowData.pcsXPackage = find.pcsXPackage;\n        options.rowData.unitMeasure = find.unitMeasure;\n        options.rowData.idProductsPackaging = find;\n      }\n    });\n    setSelectedFinalProducts(finalProducts);\n  };\n  const unitMisEditor = options => {\n    var formato = [];\n    for (const obj of options.rowData.productsPackagings) {\n      formato.push({\n        unitMeasure: obj.unitMeasure,\n        pcsXPackage: obj.pcsXPackage\n      });\n    }\n    var placeholder = 'Formato';\n    if (options.rowData.pcsXPackage !== 0) {\n      placeholder = options.rowData.productsPackagings.find(element => element.pcsXPackage === options.rowData.pcsXPackage).unitMeasure;\n    }\n    return /*#__PURE__*/_jsxDEV(Dropdown, {\n      value: options.rowData['pcsXPackage'],\n      options: formato,\n      optionLabel: \"unitMeasure\",\n      optionValue: \"pcsXPackage\",\n      onChange: e => onEditComplete(e.value, options, formato),\n      style: {\n        width: '100%'\n      },\n      placeholder: placeholder,\n      itemTemplate: option => {\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"product-badge status-\".concat(option.unitMeasure.toLowerCase()),\n          children: option.unitMeasure\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 28\n        }, this);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this);\n  };\n  /* Icona per eliminare un prodotto */\n  const actionBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-rounded\",\n        onClick: () => confirmDeleteResult(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 13\n    }, this);\n  };\n  //Apertura dialogo elimina\n  const confirmDeleteResult = result => {\n    confirmDialog({\n      message: Costanti.RimProd,\n      header: 'Attenzione',\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: \"Si\",\n      rejectLabel: \"No\",\n      accept: () => deleteResult(result),\n      reject: null\n    });\n  };\n  const deleteResult = result => {\n    var filter = selectedFinalProducts.filter(el => el.id !== result.id);\n    setSelectedFinalProducts(filter);\n  };\n  const fields = [{\n    field: 'warehouse.name',\n    header: Costanti.Magazzino,\n    /* body: 'documentDate', */showHeader: true\n  }, {\n    field: 'venduto_unitario',\n    header: Costanti.VendutoUnitario,\n    /* body: 'documentDate', */showHeader: true\n  }];\n  const header = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-rows\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n        children: Costanti.ProdAgg\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-input-icon-left d-block mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-search mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            className: \"w-100\",\n            type: \"search\",\n            onInput: e => setGlobalFilter(e.target.value),\n            placeholder: \"Cerca...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mt-3\",\n            children: Costanti.DataInizio\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"mb-3\",\n            value: data,\n            onChange: e => setData(e.target.value),\n            dateFormat: \"dd/mm/yy\",\n            placeholder: new Date().toLocaleDateString(),\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3 h-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: Costanti.DataFine\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n            value: data2,\n            onChange: e => setData2(e.target.value),\n            dateFormat: \"dd/mm/yy\",\n            placeholder: new Date().toLocaleDateString(),\n            disabled: data ? false : true,\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-0 ml-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 51\n            }, this), Costanti.cliente]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-2\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              value: retailer,\n              options: retailers,\n              onChange: e => onRetailerSelect(e),\n              optionLabel: \"name\",\n              placeholder: \"Seleziona cliente\",\n              filter: true,\n              filterBy: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-0 ml-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-tag mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 51\n            }, this), Costanti.Prodotto]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-2\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-float-label\",\n              children: [/*#__PURE__*/_jsxDEV(AutoComplete, {\n                value: product,\n                suggestions: products,\n                completeMethod: searchProduct,\n                field: \"name\",\n                onSelect: e => selectHandler(e),\n                onChange: e => onProductSelect(e)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"autocomplete\",\n                children: Costanti.SelezionaProdotto\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }, this), selectedProducts && documents && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper datatable-rowexpansion-demo\",\n      children: [!data && !data2 && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-danger\",\n        children: Costanti.PerRifNonSpec\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(CustomDataTable, {\n        ref: dt,\n        value: documents,\n        fields: fields,\n        loading: loading,\n        dataKey: \"number\",\n        paginator: true,\n        rows: 10,\n        rowsPerPageOptions: [10, 20, 50],\n        autoLayout: true,\n        selectionMode: \"single\",\n        onRowSelect: onRowSelect,\n        header: Costanti.DocPerProdSel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 17\n    }, this), (selectedFinalProducts === null || selectedFinalProducts === void 0 ? void 0 : selectedFinalProducts.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        ref: dt,\n        value: selectedFinalProducts,\n        globalFilter: globalFilter,\n        header: header,\n        selection: selectedProducts,\n        autoLayout: true,\n        className: \"p-datatable-responsive-demo editable-prices-table\",\n        editMode: \"row\",\n        dataKey: \"id\",\n        onRowEditComplete: onRowEditComplete,\n        paginator: true,\n        rows: 10,\n        rowsPerPageOptions: [5, 10, 20, 50],\n        responsiveLayout: \"scroll\",\n        emptyMessage: \"Non ci sono elementi da visualizzare per questo magazzino\",\n        children: [/*#__PURE__*/_jsxDEV(Column, {\n          field: \"description\",\n          header: Costanti.Nome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"externalCode\",\n          header: Costanti.exCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"unitMeasure\",\n          header: Costanti.UnitMis,\n          body: unitMisBodyTemplate,\n          editor: options => unitMisEditor(options)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"newColli\",\n          header: Costanti.Quantità,\n          body: colliBodyTemplate,\n          editor: options => colliEditor(options)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          className: \"modActionColumn\",\n          rowEditor: true,\n          headerStyle: {\n            width: '7rem'\n          },\n          bodyStyle: {\n            textAlign: 'center'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          body: actionBodyTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 17\n    }, this), (selectedFinalProducts === null || selectedFinalProducts === void 0 ? void 0 : selectedFinalProducts.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button w-auto mb-0\",\n        onClick: () => crea(),\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-check mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 85\n        }, this), Costanti.CreaDoc]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiDocReso, \"H+3zHyFpN30qneJtPRNofJheJNA=\");\n_c = AggiungiDocReso;\nvar _c;\n$RefreshReg$(_c, \"AggiungiDocReso\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Toast", "APIRequest", "stopLoading", "<PERSON><PERSON>", "DataTable", "InputNumber", "Column", "InputText", "<PERSON><PERSON>", "Dropdown", "AutoComplete", "CustomDataTable", "confirmDialog", "Calendar", "jsxDEV", "_jsxDEV", "AggiungiDocReso", "props", "_s", "loading", "setLoading", "totProducts", "setTotProducts", "products", "setProducts", "product", "setProduct", "warehouses", "setWarehouses", "retailers", "setRetailers", "retailer", "setRetailer", "documents", "setDocuments", "data", "setData", "data2", "setData2", "selectedProducts", "setSelectedProducts", "selectedFinalProducts", "setSelectedFinalProducts", "globalFilter", "setGlobalFilter", "dt", "toast", "trovaRisultato", "then", "res", "x", "map", "el", "push", "name", "idRegistry", "firstName", "code", "id", "catch", "e", "console", "log", "warehouseName", "onRetailerSelect", "value", "onProductSelect", "<PERSON><PERSON><PERSON><PERSON>", "prod", "concat", "for<PERSON>ach", "element", "find", "length", "warehouse", "undefined", "venduto_unitario", "searchProduct", "event", "query", "description", "onRowSelect", "result", "<PERSON><PERSON><PERSON><PERSON>", "new<PERSON><PERSON><PERSON>", "pcsXPackage", "unitMeasure", "found", "current", "show", "severity", "summary", "detail", "life", "colliBodyTemplate", "results", "Fragment", "children", "className", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "colliEditor", "options", "rowData", "onValueChange", "onRowEditComplete", "crea", "Set", "ids", "totProd", "filter", "quantity", "idProductsPackaging", "colliPreventivo", "colliConsuntivo", "tax", "iva", "unitPrice", "total", "totalTaxed", "body", "type", "documentType", "documentDate", "Date", "idRetailer", "rowBody", "_e$response", "_e$response2", "response", "message", "setTimeout", "window", "location", "reload", "unitMisBodyTemplate", "UnitMis", "onEditComplete", "finalProducts", "productsPackagings", "unitMisEditor", "formato", "obj", "placeholder", "optionLabel", "optionValue", "onChange", "style", "width", "itemTemplate", "option", "toLowerCase", "actionBodyTemplate", "icon", "onClick", "confirmDeleteResult", "<PERSON><PERSON><PERSON><PERSON>", "header", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "deleteResult", "reject", "fields", "field", "<PERSON><PERSON><PERSON><PERSON>", "showHeader", "VendutoUnitario", "ProdAgg", "onInput", "target", "ref", "DataInizio", "dateFormat", "toLocaleDateString", "showIcon", "DataFine", "disabled", "cliente", "filterBy", "<PERSON><PERSON><PERSON>", "suggestions", "completeMethod", "onSelect", "htmlFor", "SelezionaProdotto", "PerRifNonSpec", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "selectionMode", "DocPerProdSel", "selection", "editMode", "responsiveLayout", "emptyMessage", "Nome", "exCode", "editor", "Quantità", "rowEditor", "headerStyle", "bodyStyle", "textAlign", "CreaDoc", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiDocReso.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { <PERSON><PERSON> } from '../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Column } from 'primereact/column';\nimport { InputText } from 'primereact/inputtext';\nimport { Button } from 'primereact/button';\nimport { Dropdown } from 'primereact/dropdown';\nimport { AutoComplete } from 'primereact/autocomplete';\nimport CustomDataTable from '../components/customDataTable';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Calendar } from 'primereact/calendar';\n\nexport const AggiungiDocReso = (props) => {\n    const [loading, setLoading] = useState(false);\n\n    const [totProducts, setTotProducts] = useState([]);\n    const [products, setProducts] = useState([]);\n    const [product, setProduct] = useState(null);\n\n    const [warehouses, setWarehouses] = useState([]);\n\n    const [retailers, setRetailers] = useState([]);\n    const [retailer, setRetailer] = useState(null);\n\n    const [documents, setDocuments] = useState([]);\n    const [data, setData] = useState(null);\n    const [data2, setData2] = useState(null);\n\n    const [selectedProducts, setSelectedProducts] = useState(null);\n    const [selectedFinalProducts, setSelectedFinalProducts] = useState([]);\n\n    const [globalFilter, setGlobalFilter] = useState(null);\n\n    const dt = useRef(null);\n    const toast = useRef(null);\n\n    useEffect(() => {\n        async function trovaRisultato() {\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'retailers/')\n                .then(res => {\n                    var x = []\n                    res.data.map(el => x.push({ name: el.idRegistry.firstName, code: el.id }))\n                    setRetailers(x);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            await APIRequest('GET', 'warehouses/')\n                .then(res => {\n                    var x = []\n                    res.data.map(el => x.push({ name: el.warehouseName, code: el.id }))\n                    setWarehouses(x);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, [props])\n    const onRetailerSelect = async (e) => {\n        setLoading(true)\n        setRetailer(e.value)\n    }\n    const onProductSelect = async (e) => {\n        setProduct(e.value)\n    }\n    const selectHandler = async (e) => {\n        var prod = selectedProducts !== null ? [...selectedProducts, e.value] : [e.value]\n        setSelectedProducts(prod)\n        await APIRequest('GET', `statistic/productindocument?idproduct=${e.value.code}&documentType=CLI-ORDINE&idretailer=${retailer.code}${data ? `&dateFrom=${data}${data2 ? `&dateTo=${data2}` : ''}` : ''}`)\n            .then(res => {\n                let documents = []\n                res.data.forEach(element => {\n                    var find = documents.length ? documents.find(el => el.warehouse.code === element.warehouse) : undefined\n                    if (find) {\n                        find.venduto_unitario += element.venduto_unitario\n                    } else {\n                        documents.push({ venduto_unitario: element.venduto_unitario, warehouse: warehouses.find(el => el.code === element.warehouse) })\n                    }\n                })\n                setDocuments(documents)\n                setLoading(false)\n            }).catch((e) => {\n                console.log(e);\n                setLoading(false)\n            })\n    }\n    const searchProduct = async (event) => {\n        if (event.query.length > 2) {\n            //Chiamata axios per la visualizzazione dei prodotti\n            await APIRequest('GET', `products/?find=${event.query}`)\n                .then(res => {\n                    console.log(res.data)\n                    var x = []\n                    res.data.map(el => x.push({ name: el.description, code: el.id }))\n                    setProducts(x)\n                    setTotProducts(res.data)\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n    }\n    const onRowSelect = async (result) => {\n        var selProd = [...selectedFinalProducts]\n        var find = totProducts.find(el => el.id === product.code)\n        find.newColli = 0\n        find.pcsXPackage = 0\n        find.unitMeasure = ''\n        find.warehouse = result.data.warehouse\n        var found = selProd.find(el => el.id === find.id)\n        if (found) {\n            toast.current.show({\n                severity: \"warn\",\n                summary: \"Attenzione\",\n                detail: \"Il prodotto selezionato è già stato aggiunto al documento\",\n                life: 3000,\n            });\n        } else {\n            selProd.push(find)\n            setSelectedFinalProducts(selProd)\n            setDocuments([])\n            setProduct(null)\n            setSelectedProducts(null)\n            toast.current.show({\n                severity: \"success\",\n                summary: \"Ottimo\",\n                detail: \"Il prodotto selezionato è stato aggiunto al documento\",\n                life: 3000,\n            });\n        }\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    const colliBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Colli}</span>\n                {results.newColli}\n            </React.Fragment>\n        );\n    }\n    /* InputNumber per la modifica dei colli */\n    const colliEditor = (options) => {\n        return <InputNumber value={options.rowData['newColli']} onValueChange={(e) => onRowEditComplete(e, options)} />\n\n    }\n    /* Modifico quantità del prodotto */\n    const onRowEditComplete = (e, options) => {\n        options.rowData.newColli = e.value\n    }\n    const crea = async () => {\n        var find = selectedFinalProducts.find(el => el.newColli < 1 || el.unitMeasure === '')\n        var warehouses = selectedFinalProducts.map(el => el.warehouse.code)\n        warehouses = [...new Set(warehouses)]\n        if (!find) {\n            for (const ids of warehouses) {\n                let totProd = []\n                var filter = selectedFinalProducts.filter(el => el.warehouse.code === ids)\n                filter.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        quantity: element.newColli,\n                        idProductsPackaging: element.idProductsPackaging, //Find pkg\n                        colliPreventivo: element.newColli,\n                        colliConsuntivo: 0,\n                        tax: `${element.iva}%`,\n                        unitPrice: 0,\n                        total: 0,\n                        totalTaxed: 0\n                    }\n                    totProd.push(x)\n                });\n\n                var body = {\n                    type: props.documentType,\n                    documentDate: new Date(),\n                    total: 0,\n                    idRetailer: retailer.code,\n                    rowBody: totProd\n                }\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', `documents/?idWarehouses=${ids}`, body)\n                    .then(res => {\n                        console.log(res.data);\n                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: `Il documento con id ${res.data.result.id} è stato inserito con successo`, life: 3000 });\n                        /* setTimeout(() => {\n                            window.location.reload()\n                        }, 3000) */\n                    }).catch((e) => {\n                        console.log(e)\n                        toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n            setTimeout(() => {\n                window.location.reload()\n            }, 3000)\n        } else {\n            toast.current.show({ severity: 'warn', summary: 'Attenzione', detail: \"Indicare i colli ed il confezionamento dei prodotti resi dal cliente prima di creare il documento\", life: 3000 });\n        }\n    }\n    const unitMisBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UnitMis}</span>\n                {results.unitMeasure}\n            </React.Fragment>\n        );\n    }\n    const onEditComplete = (e, options) => {\n        console.log(options)\n        var finalProducts = [...selectedFinalProducts]\n        finalProducts.forEach(element => {\n            if (element.id === options.rowData.id) {\n                var find = options.rowData.productsPackagings.find(el => el.pcsXPackage === e)\n                options.rowData.pcsXPackage = find.pcsXPackage\n                options.rowData.unitMeasure = find.unitMeasure\n                options.rowData.idProductsPackaging = find\n            }\n        })\n        setSelectedFinalProducts(finalProducts)\n    }\n    const unitMisEditor = (options) => {\n        var formato = []\n        for (const obj of options.rowData.productsPackagings) {\n            formato.push({\n                unitMeasure: obj.unitMeasure,\n                pcsXPackage: obj.pcsXPackage\n            })\n        }\n        var placeholder = 'Formato'\n        if (options.rowData.pcsXPackage !== 0) {\n            placeholder = options.rowData.productsPackagings.find(element => element.pcsXPackage === options.rowData.pcsXPackage).unitMeasure\n        }\n        return (\n            <Dropdown value={options.rowData['pcsXPackage']} options={formato} optionLabel=\"unitMeasure\" optionValue=\"pcsXPackage\"\n                onChange={(e) => onEditComplete(e.value, options, formato)} style={{ width: '100%' }} placeholder={placeholder}\n                itemTemplate={(option) => {\n                    return <span className={`product-badge status-${option.unitMeasure.toLowerCase()}`}>{option.unitMeasure}</span>\n                }} />\n        );\n    }\n    /* Icona per eliminare un prodotto */\n    const actionBodyTemplate = (rowData) => {\n        return (\n            <React.Fragment>\n                <Button icon=\"pi pi-trash\" className=\"p-button-rounded\" onClick={() => confirmDeleteResult(rowData)} />\n            </React.Fragment>\n        );\n    }\n    //Apertura dialogo elimina\n    const confirmDeleteResult = (result) => {\n        confirmDialog({\n            message: Costanti.RimProd,\n            header: 'Attenzione',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: () => deleteResult(result),\n            reject: null\n        });\n    }\n    const deleteResult = (result) => {\n        var filter = selectedFinalProducts.filter(el => el.id !== result.id)\n        setSelectedFinalProducts(filter)\n    }\n    const fields = [\n        { field: 'warehouse.name', header: Costanti.Magazzino, /* body: 'documentDate', */  showHeader: true },\n        { field: 'venduto_unitario', header: Costanti.VendutoUnitario, /* body: 'documentDate', */  showHeader: true },\n    ];\n    const header = (\n        <div className=\"container-rows\">\n            <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                    {Costanti.ProdAgg}\n                </div>\n                <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                    <span className=\"p-input-icon-left d-block mx-auto\">\n                        <i className=\"pi pi-search mr-2\" />\n                        <InputText className=\"w-100\" type=\"search\" onInput={(e) => setGlobalFilter(e.target.value)} placeholder=\"Cerca...\" />\n                    </span>\n                </div>\n            </div>\n        </div>\n    );\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n\n            <div className='row'>\n                <div className='col-12 col-md-6'>\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h4 className=\"mt-3\">{Costanti.DataInizio}</h4>\n                        <hr></hr>\n                        <Calendar className=\"mb-3\" value={data} onChange={(e) => setData(e.target.value)} dateFormat=\"dd/mm/yy\" placeholder={new Date().toLocaleDateString()} showIcon />\n                    </div>\n                </div>\n                <div className='col-12 col-md-6'>\n                    <div className='d-flex justify-content-center flex-column pb-3 h-100'>\n                        <h4>{Costanti.DataFine}</h4>\n                        <hr></hr>\n                        <Calendar value={data2} onChange={(e) => setData2(e.target.value)} dateFormat=\"dd/mm/yy\" placeholder={new Date().toLocaleDateString()} disabled={data ? false : true} showIcon />\n                    </div>\n                </div>\n                <div className='col-12 col-md-6'>\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h4 className=\"mb-0 ml-1\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.cliente}</h4>\n                        <hr></hr>\n                        <div className='py-2'>\n                            <Dropdown value={retailer} options={retailers} onChange={(e) => onRetailerSelect(e)} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy='name' />\n                        </div>\n                    </div>\n                </div>\n                <div className='col-12 col-md-6'>\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h4 className=\"mb-0 ml-1\"><i className=\"pi pi-tag mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Prodotto}</h4>\n                        <hr></hr>\n                        <div className='py-2'>\n                            <span className=\"p-float-label\">\n                                <AutoComplete value={product} suggestions={products} completeMethod={searchProduct} field=\"name\" onSelect={(e) => selectHandler(e)} onChange={(e) => onProductSelect(e)} />\n                                <label htmlFor=\"autocomplete\">{Costanti.SelezionaProdotto}</label>\n                            </span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {selectedProducts && documents &&\n                <div className=\"datatable-responsive-demo wrapper datatable-rowexpansion-demo\">\n                    {!data && !data2 &&\n                        <p className='text-danger'>{Costanti.PerRifNonSpec}</p>\n                    }\n                    <CustomDataTable\n                        ref={dt}\n                        value={documents}\n                        fields={fields}\n                        loading={loading}\n                        dataKey=\"number\"\n                        paginator\n                        rows={10}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        selectionMode=\"single\"\n                        onRowSelect={onRowSelect}\n                        header={Costanti.DocPerProdSel}\n                    />\n                </div>\n            }\n\n            {selectedFinalProducts?.length > 0 &&\n                <div className=\"datatable-responsive-demo wrapper\">\n                    <hr />\n                    <DataTable\n                        ref={dt}\n                        value={selectedFinalProducts}\n                        globalFilter={globalFilter}\n                        header={header}\n                        selection={selectedProducts}\n                        autoLayout={true}\n                        className=\"p-datatable-responsive-demo editable-prices-table\"\n                        editMode=\"row\"\n                        dataKey=\"id\"\n                        onRowEditComplete={onRowEditComplete}\n                        paginator\n                        rows={10}\n                        rowsPerPageOptions={[5, 10, 20, 50]}\n                        responsiveLayout=\"scroll\"\n                        emptyMessage=\"Non ci sono elementi da visualizzare per questo magazzino\"\n                    >\n                        <Column field=\"description\" header={Costanti.Nome} ></Column>\n                        <Column field=\"externalCode\" header={Costanti.exCode} ></Column>\n                        <Column field=\"unitMeasure\" header={Costanti.UnitMis} body={unitMisBodyTemplate} editor={(options) => unitMisEditor(options)} ></Column>\n                        <Column field=\"newColli\" header={Costanti.Quantità} body={colliBodyTemplate} editor={(options) => colliEditor(options)}  ></Column>\n                        <Column className=\"modActionColumn\" rowEditor headerStyle={{ width: '7rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                        <Column body={actionBodyTemplate} ></Column>\n                    </DataTable>\n                </div>\n            }\n\n            {selectedFinalProducts?.length > 0 &&\n                <div className='d-flex justify-content-center mt-3'>\n                    <Button className=\"p-button w-auto mb-0\" onClick={() => crea()}><i className=\"pi pi-check mr-2\"></i>{Costanti.CreaDoc}</Button>\n                </div>\n            }\n\n        </div>\n    )\n\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,OAAO,MAAMC,eAAe,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM8C,EAAE,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACvB,MAAMgD,KAAK,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAE1BD,SAAS,CAAC,MAAM;IACZ,eAAekD,cAAcA,CAAA,EAAG;MAC5B;MACA,MAAM9C,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC+C,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,CAAC,GAAG,EAAE;QACVD,GAAG,CAACd,IAAI,CAACgB,GAAG,CAACC,EAAE,IAAIF,CAAC,CAACG,IAAI,CAAC;UAAEC,IAAI,EAAEF,EAAE,CAACG,UAAU,CAACC,SAAS;UAAEC,IAAI,EAAEL,EAAE,CAACM;QAAG,CAAC,CAAC,CAAC;QAC1E5B,YAAY,CAACoB,CAAC,CAAC;MACnB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACN,MAAM3D,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC+C,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,CAAC,GAAG,EAAE;QACVD,GAAG,CAACd,IAAI,CAACgB,GAAG,CAACC,EAAE,IAAIF,CAAC,CAACG,IAAI,CAAC;UAAEC,IAAI,EAAEF,EAAE,CAACW,aAAa;UAAEN,IAAI,EAAEL,EAAE,CAACM;QAAG,CAAC,CAAC,CAAC;QACnE9B,aAAa,CAACsB,CAAC,CAAC;MACpB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACN1D,WAAW,CAAC,CAAC;IACjB;IACA6C,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EACX,MAAM+C,gBAAgB,GAAG,MAAOJ,CAAC,IAAK;IAClCxC,UAAU,CAAC,IAAI,CAAC;IAChBY,WAAW,CAAC4B,CAAC,CAACK,KAAK,CAAC;EACxB,CAAC;EACD,MAAMC,eAAe,GAAG,MAAON,CAAC,IAAK;IACjClC,UAAU,CAACkC,CAAC,CAACK,KAAK,CAAC;EACvB,CAAC;EACD,MAAME,aAAa,GAAG,MAAOP,CAAC,IAAK;IAC/B,IAAIQ,IAAI,GAAG7B,gBAAgB,KAAK,IAAI,GAAG,CAAC,GAAGA,gBAAgB,EAAEqB,CAAC,CAACK,KAAK,CAAC,GAAG,CAACL,CAAC,CAACK,KAAK,CAAC;IACjFzB,mBAAmB,CAAC4B,IAAI,CAAC;IACzB,MAAMnE,UAAU,CAAC,KAAK,2CAAAoE,MAAA,CAA2CT,CAAC,CAACK,KAAK,CAACR,IAAI,0CAAAY,MAAA,CAAuCtC,QAAQ,CAAC0B,IAAI,EAAAY,MAAA,CAAGlC,IAAI,gBAAAkC,MAAA,CAAgBlC,IAAI,EAAAkC,MAAA,CAAGhC,KAAK,cAAAgC,MAAA,CAAchC,KAAK,IAAK,EAAE,IAAK,EAAE,CAAE,CAAC,CACnMW,IAAI,CAACC,GAAG,IAAI;MACT,IAAIhB,SAAS,GAAG,EAAE;MAClBgB,GAAG,CAACd,IAAI,CAACmC,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIC,IAAI,GAAGvC,SAAS,CAACwC,MAAM,GAAGxC,SAAS,CAACuC,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACsB,SAAS,CAACjB,IAAI,KAAKc,OAAO,CAACG,SAAS,CAAC,GAAGC,SAAS;QACvG,IAAIH,IAAI,EAAE;UACNA,IAAI,CAACI,gBAAgB,IAAIL,OAAO,CAACK,gBAAgB;QACrD,CAAC,MAAM;UACH3C,SAAS,CAACoB,IAAI,CAAC;YAAEuB,gBAAgB,EAAEL,OAAO,CAACK,gBAAgB;YAAEF,SAAS,EAAE/C,UAAU,CAAC6C,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACK,IAAI,KAAKc,OAAO,CAACG,SAAS;UAAE,CAAC,CAAC;QACnI;MACJ,CAAC,CAAC;MACFxC,YAAY,CAACD,SAAS,CAAC;MACvBb,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CAACuC,KAAK,CAAEC,CAAC,IAAK;MACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdxC,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC;EACD,MAAMyD,aAAa,GAAG,MAAOC,KAAK,IAAK;IACnC,IAAIA,KAAK,CAACC,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;MACxB;MACA,MAAMxE,UAAU,CAAC,KAAK,oBAAAoE,MAAA,CAAoBS,KAAK,CAACC,KAAK,CAAE,CAAC,CACnD/B,IAAI,CAACC,GAAG,IAAI;QACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACd,IAAI,CAAC;QACrB,IAAIe,CAAC,GAAG,EAAE;QACVD,GAAG,CAACd,IAAI,CAACgB,GAAG,CAACC,EAAE,IAAIF,CAAC,CAACG,IAAI,CAAC;UAAEC,IAAI,EAAEF,EAAE,CAAC4B,WAAW;UAAEvB,IAAI,EAAEL,EAAE,CAACM;QAAG,CAAC,CAAC,CAAC;QACjElC,WAAW,CAAC0B,CAAC,CAAC;QACd5B,cAAc,CAAC2B,GAAG,CAACd,IAAI,CAAC;MAC5B,CAAC,CAAC,CAACwB,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;EACJ,CAAC;EACD,MAAMqB,WAAW,GAAG,MAAOC,MAAM,IAAK;IAClC,IAAIC,OAAO,GAAG,CAAC,GAAG1C,qBAAqB,CAAC;IACxC,IAAI+B,IAAI,GAAGnD,WAAW,CAACmD,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACM,EAAE,KAAKjC,OAAO,CAACgC,IAAI,CAAC;IACzDe,IAAI,CAACY,QAAQ,GAAG,CAAC;IACjBZ,IAAI,CAACa,WAAW,GAAG,CAAC;IACpBb,IAAI,CAACc,WAAW,GAAG,EAAE;IACrBd,IAAI,CAACE,SAAS,GAAGQ,MAAM,CAAC/C,IAAI,CAACuC,SAAS;IACtC,IAAIa,KAAK,GAAGJ,OAAO,CAACX,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACM,EAAE,KAAKc,IAAI,CAACd,EAAE,CAAC;IACjD,IAAI6B,KAAK,EAAE;MACPzC,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE,2DAA2D;QACnEC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,MAAM;MACHV,OAAO,CAAC9B,IAAI,CAACmB,IAAI,CAAC;MAClB9B,wBAAwB,CAACyC,OAAO,CAAC;MACjCjD,YAAY,CAAC,EAAE,CAAC;MAChBR,UAAU,CAAC,IAAI,CAAC;MAChBc,mBAAmB,CAAC,IAAI,CAAC;MACzBM,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,uDAAuD;QAC/DC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ,CAAC;EACD;EACA,MAAMC,iBAAiB,GAAIC,OAAO,IAAK;IACnC,oBACIhF,OAAA,CAACnB,KAAK,CAACoG,QAAQ;MAAAC,QAAA,gBACXlF,OAAA;QAAMmF,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE9F,QAAQ,CAACgG;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvDR,OAAO,CAACX,QAAQ;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB,CAAC;EACD;EACA,MAAMC,WAAW,GAAIC,OAAO,IAAK;IAC7B,oBAAO1F,OAAA,CAACV,WAAW;MAAC4D,KAAK,EAAEwC,OAAO,CAACC,OAAO,CAAC,UAAU,CAAE;MAACC,aAAa,EAAG/C,CAAC,IAAKgD,iBAAiB,CAAChD,CAAC,EAAE6C,OAAO;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEnH,CAAC;EACD;EACA,MAAMK,iBAAiB,GAAGA,CAAChD,CAAC,EAAE6C,OAAO,KAAK;IACtCA,OAAO,CAACC,OAAO,CAACtB,QAAQ,GAAGxB,CAAC,CAACK,KAAK;EACtC,CAAC;EACD,MAAM4C,IAAI,GAAG,MAAAA,CAAA,KAAY;IACrB,IAAIrC,IAAI,GAAG/B,qBAAqB,CAAC+B,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACgC,QAAQ,GAAG,CAAC,IAAIhC,EAAE,CAACkC,WAAW,KAAK,EAAE,CAAC;IACrF,IAAI3D,UAAU,GAAGc,qBAAqB,CAACU,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACsB,SAAS,CAACjB,IAAI,CAAC;IACnE9B,UAAU,GAAG,CAAC,GAAG,IAAImF,GAAG,CAACnF,UAAU,CAAC,CAAC;IACrC,IAAI,CAAC6C,IAAI,EAAE;MACP,KAAK,MAAMuC,GAAG,IAAIpF,UAAU,EAAE;QAC1B,IAAIqF,OAAO,GAAG,EAAE;QAChB,IAAIC,MAAM,GAAGxE,qBAAqB,CAACwE,MAAM,CAAC7D,EAAE,IAAIA,EAAE,CAACsB,SAAS,CAACjB,IAAI,KAAKsD,GAAG,CAAC;QAC1EE,MAAM,CAAC3C,OAAO,CAACC,OAAO,IAAI;UACtB,IAAIrB,CAAC,GAAG;YACJQ,EAAE,EAAEa,OAAO,CAACb,EAAE;YACdwD,QAAQ,EAAE3C,OAAO,CAACa,QAAQ;YAC1B+B,mBAAmB,EAAE5C,OAAO,CAAC4C,mBAAmB;YAAE;YAClDC,eAAe,EAAE7C,OAAO,CAACa,QAAQ;YACjCiC,eAAe,EAAE,CAAC;YAClBC,GAAG,KAAAjD,MAAA,CAAKE,OAAO,CAACgD,GAAG,MAAG;YACtBC,SAAS,EAAE,CAAC;YACZC,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE;UAChB,CAAC;UACDV,OAAO,CAAC3D,IAAI,CAACH,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,IAAIyE,IAAI,GAAG;UACPC,IAAI,EAAE3G,KAAK,CAAC4G,YAAY;UACxBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC;UACxBN,KAAK,EAAE,CAAC;UACRO,UAAU,EAAEjG,QAAQ,CAAC0B,IAAI;UACzBwE,OAAO,EAAEjB;QACb,CAAC;QACD;QACA,MAAM/G,UAAU,CAAC,MAAM,6BAAAoE,MAAA,CAA6B0C,GAAG,GAAIY,IAAI,CAAC,CAC3D3E,IAAI,CAACC,GAAG,IAAI;UACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACd,IAAI,CAAC;UACrBW,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,yBAAAvB,MAAA,CAAyBpB,GAAG,CAACd,IAAI,CAAC+C,MAAM,CAACxB,EAAE,sCAAgC;YAAEmC,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7J;AACxB;AACA;QACoB,CAAC,CAAC,CAAClC,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAsE,WAAA,EAAAC,YAAA;UACZtE,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;UACdd,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAvB,MAAA,CAAsE,EAAA6D,WAAA,GAAAtE,CAAC,CAACwE,QAAQ,cAAAF,WAAA,uBAAVA,WAAA,CAAY/F,IAAI,MAAKwC,SAAS,IAAAwD,YAAA,GAAGvE,CAAC,CAACwE,QAAQ,cAAAD,YAAA,uBAAVA,YAAA,CAAYhG,IAAI,GAAGyB,CAAC,CAACyE,OAAO,CAAE;YAAExC,IAAI,EAAE;UAAK,CAAC,CAAC;QAClO,CAAC,CAAC;MACV;MACAyC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,MAAM;MACH3F,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEC,OAAO,EAAE,YAAY;QAAEC,MAAM,EAAE,mGAAmG;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC5L;EACJ,CAAC;EACD,MAAM6C,mBAAmB,GAAI3C,OAAO,IAAK;IACrC,oBACIhF,OAAA,CAACnB,KAAK,CAACoG,QAAQ;MAAAC,QAAA,gBACXlF,OAAA;QAAMmF,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE9F,QAAQ,CAACwI;MAAO;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDR,OAAO,CAACT,WAAW;IAAA;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEzB,CAAC;EACD,MAAMqC,cAAc,GAAGA,CAAChF,CAAC,EAAE6C,OAAO,KAAK;IACnC5C,OAAO,CAACC,GAAG,CAAC2C,OAAO,CAAC;IACpB,IAAIoC,aAAa,GAAG,CAAC,GAAGpG,qBAAqB,CAAC;IAC9CoG,aAAa,CAACvE,OAAO,CAACC,OAAO,IAAI;MAC7B,IAAIA,OAAO,CAACb,EAAE,KAAK+C,OAAO,CAACC,OAAO,CAAChD,EAAE,EAAE;QACnC,IAAIc,IAAI,GAAGiC,OAAO,CAACC,OAAO,CAACoC,kBAAkB,CAACtE,IAAI,CAACpB,EAAE,IAAIA,EAAE,CAACiC,WAAW,KAAKzB,CAAC,CAAC;QAC9E6C,OAAO,CAACC,OAAO,CAACrB,WAAW,GAAGb,IAAI,CAACa,WAAW;QAC9CoB,OAAO,CAACC,OAAO,CAACpB,WAAW,GAAGd,IAAI,CAACc,WAAW;QAC9CmB,OAAO,CAACC,OAAO,CAACS,mBAAmB,GAAG3C,IAAI;MAC9C;IACJ,CAAC,CAAC;IACF9B,wBAAwB,CAACmG,aAAa,CAAC;EAC3C,CAAC;EACD,MAAME,aAAa,GAAItC,OAAO,IAAK;IAC/B,IAAIuC,OAAO,GAAG,EAAE;IAChB,KAAK,MAAMC,GAAG,IAAIxC,OAAO,CAACC,OAAO,CAACoC,kBAAkB,EAAE;MAClDE,OAAO,CAAC3F,IAAI,CAAC;QACTiC,WAAW,EAAE2D,GAAG,CAAC3D,WAAW;QAC5BD,WAAW,EAAE4D,GAAG,CAAC5D;MACrB,CAAC,CAAC;IACN;IACA,IAAI6D,WAAW,GAAG,SAAS;IAC3B,IAAIzC,OAAO,CAACC,OAAO,CAACrB,WAAW,KAAK,CAAC,EAAE;MACnC6D,WAAW,GAAGzC,OAAO,CAACC,OAAO,CAACoC,kBAAkB,CAACtE,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACc,WAAW,KAAKoB,OAAO,CAACC,OAAO,CAACrB,WAAW,CAAC,CAACC,WAAW;IACrI;IACA,oBACIvE,OAAA,CAACN,QAAQ;MAACwD,KAAK,EAAEwC,OAAO,CAACC,OAAO,CAAC,aAAa,CAAE;MAACD,OAAO,EAAEuC,OAAQ;MAACG,WAAW,EAAC,aAAa;MAACC,WAAW,EAAC,aAAa;MAClHC,QAAQ,EAAGzF,CAAC,IAAKgF,cAAc,CAAChF,CAAC,CAACK,KAAK,EAAEwC,OAAO,EAAEuC,OAAO,CAAE;MAACM,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAACL,WAAW,EAAEA,WAAY;MAC/GM,YAAY,EAAGC,MAAM,IAAK;QACtB,oBAAO1I,OAAA;UAAMmF,SAAS,0BAAA7B,MAAA,CAA0BoF,MAAM,CAACnE,WAAW,CAACoE,WAAW,CAAC,CAAC,CAAG;UAAAzD,QAAA,EAAEwD,MAAM,CAACnE;QAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACnH;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEjB,CAAC;EACD;EACA,MAAMoD,kBAAkB,GAAIjD,OAAO,IAAK;IACpC,oBACI3F,OAAA,CAACnB,KAAK,CAACoG,QAAQ;MAAAC,QAAA,eACXlF,OAAA,CAACP,MAAM;QAACoJ,IAAI,EAAC,aAAa;QAAC1D,SAAS,EAAC,kBAAkB;QAAC2D,OAAO,EAAEA,CAAA,KAAMC,mBAAmB,CAACpD,OAAO;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAEzB,CAAC;EACD;EACA,MAAMuD,mBAAmB,GAAI5E,MAAM,IAAK;IACpCtE,aAAa,CAAC;MACVyH,OAAO,EAAElI,QAAQ,CAAC4J,OAAO;MACzBC,MAAM,EAAE,YAAY;MACpBJ,IAAI,EAAE,4BAA4B;MAClCK,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAEA,CAAA,KAAMC,YAAY,CAAClF,MAAM,CAAC;MAClCmF,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EACD,MAAMD,YAAY,GAAIlF,MAAM,IAAK;IAC7B,IAAI+B,MAAM,GAAGxE,qBAAqB,CAACwE,MAAM,CAAC7D,EAAE,IAAIA,EAAE,CAACM,EAAE,KAAKwB,MAAM,CAACxB,EAAE,CAAC;IACpEhB,wBAAwB,CAACuE,MAAM,CAAC;EACpC,CAAC;EACD,MAAMqD,MAAM,GAAG,CACX;IAAEC,KAAK,EAAE,gBAAgB;IAAEP,MAAM,EAAE7J,QAAQ,CAACqK,SAAS;IAAE,2BAA6BC,UAAU,EAAE;EAAK,CAAC,EACtG;IAAEF,KAAK,EAAE,kBAAkB;IAAEP,MAAM,EAAE7J,QAAQ,CAACuK,eAAe;IAAE,2BAA6BD,UAAU,EAAE;EAAK,CAAC,CACjH;EACD,MAAMT,MAAM,gBACRjJ,OAAA;IAAKmF,SAAS,EAAC,gBAAgB;IAAAD,QAAA,eAC3BlF,OAAA;MAAKmF,SAAS,EAAC,2DAA2D;MAAAD,QAAA,gBACtElF,OAAA;QAAKmF,SAAS,EAAC,8BAA8B;QAAAD,QAAA,EACxC9F,QAAQ,CAACwK;MAAO;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACNxF,OAAA;QAAKmF,SAAS,EAAC,8BAA8B;QAAAD,QAAA,eACzClF,OAAA;UAAMmF,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAC/ClF,OAAA;YAAGmF,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCxF,OAAA,CAACR,SAAS;YAAC2F,SAAS,EAAC,OAAO;YAAC0B,IAAI,EAAC,QAAQ;YAACgD,OAAO,EAAGhH,CAAC,IAAKhB,eAAe,CAACgB,CAAC,CAACiH,MAAM,CAAC5G,KAAK,CAAE;YAACiF,WAAW,EAAC;UAAU;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EACD,oBACIxF,OAAA;IAAKmF,SAAS,EAAC,WAAW;IAAAD,QAAA,gBACtBlF,OAAA,CAACf,KAAK;MAAC8K,GAAG,EAAEhI;IAAM;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErBxF,OAAA;MAAKmF,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAChBlF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC5BlF,OAAA;UAAKmF,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DlF,OAAA;YAAImF,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAE9F,QAAQ,CAAC4K;UAAU;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CxF,OAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA,CAACF,QAAQ;YAACqF,SAAS,EAAC,MAAM;YAACjC,KAAK,EAAE9B,IAAK;YAACkH,QAAQ,EAAGzF,CAAC,IAAKxB,OAAO,CAACwB,CAAC,CAACiH,MAAM,CAAC5G,KAAK,CAAE;YAAC+G,UAAU,EAAC,UAAU;YAAC9B,WAAW,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACkD,kBAAkB,CAAC,CAAE;YAACC,QAAQ;UAAA;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC5BlF,OAAA;UAAKmF,SAAS,EAAC,sDAAsD;UAAAD,QAAA,gBACjElF,OAAA;YAAAkF,QAAA,EAAK9F,QAAQ,CAACgL;UAAQ;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5BxF,OAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA,CAACF,QAAQ;YAACoD,KAAK,EAAE5B,KAAM;YAACgH,QAAQ,EAAGzF,CAAC,IAAKtB,QAAQ,CAACsB,CAAC,CAACiH,MAAM,CAAC5G,KAAK,CAAE;YAAC+G,UAAU,EAAC,UAAU;YAAC9B,WAAW,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACkD,kBAAkB,CAAC,CAAE;YAACG,QAAQ,EAAEjJ,IAAI,GAAG,KAAK,GAAG,IAAK;YAAC+I,QAAQ;UAAA;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC5BlF,OAAA;UAAKmF,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DlF,OAAA;YAAImF,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAAClF,OAAA;cAAGmF,SAAS,EAAC,iBAAiB;cAACoD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACpG,QAAQ,CAACkL,OAAO;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnHxF,OAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YAAKmF,SAAS,EAAC,MAAM;YAAAD,QAAA,eACjBlF,OAAA,CAACN,QAAQ;cAACwD,KAAK,EAAElC,QAAS;cAAC0E,OAAO,EAAE5E,SAAU;cAACwH,QAAQ,EAAGzF,CAAC,IAAKI,gBAAgB,CAACJ,CAAC,CAAE;cAACuF,WAAW,EAAC,MAAM;cAACD,WAAW,EAAC,mBAAmB;cAACjC,MAAM;cAACqE,QAAQ,EAAC;YAAM;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC5BlF,OAAA;UAAKmF,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DlF,OAAA;YAAImF,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAAClF,OAAA;cAAGmF,SAAS,EAAC,gBAAgB;cAACoD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACpG,QAAQ,CAACoL,QAAQ;UAAA;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnHxF,OAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YAAKmF,SAAS,EAAC,MAAM;YAAAD,QAAA,eACjBlF,OAAA;cAAMmF,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC3BlF,OAAA,CAACL,YAAY;gBAACuD,KAAK,EAAExC,OAAQ;gBAAC+J,WAAW,EAAEjK,QAAS;gBAACkK,cAAc,EAAE5G,aAAc;gBAAC0F,KAAK,EAAC,MAAM;gBAACmB,QAAQ,EAAG9H,CAAC,IAAKO,aAAa,CAACP,CAAC,CAAE;gBAACyF,QAAQ,EAAGzF,CAAC,IAAKM,eAAe,CAACN,CAAC;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3KxF,OAAA;gBAAO4K,OAAO,EAAC,cAAc;gBAAA1F,QAAA,EAAE9F,QAAQ,CAACyL;cAAiB;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELhE,gBAAgB,IAAIN,SAAS,iBAC1BlB,OAAA;MAAKmF,SAAS,EAAC,+DAA+D;MAAAD,QAAA,GACzE,CAAC9D,IAAI,IAAI,CAACE,KAAK,iBACZtB,OAAA;QAAGmF,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAE9F,QAAQ,CAAC0L;MAAa;QAAAzF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3DxF,OAAA,CAACJ,eAAe;QACZmK,GAAG,EAAEjI,EAAG;QACRoB,KAAK,EAAEhC,SAAU;QACjBqI,MAAM,EAAEA,MAAO;QACfnJ,OAAO,EAAEA,OAAQ;QACjB2K,OAAO,EAAC,QAAQ;QAChBC,SAAS;QACTC,IAAI,EAAE,EAAG;QACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACjCC,UAAU,EAAE,IAAK;QACjBC,aAAa,EAAC,QAAQ;QACtBlH,WAAW,EAAEA,WAAY;QACzB+E,MAAM,EAAE7J,QAAQ,CAACiM;MAAc;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGT,CAAA9D,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEgC,MAAM,IAAG,CAAC,iBAC9B1D,OAAA;MAAKmF,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAC9ClF,OAAA;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNxF,OAAA,CAACX,SAAS;QACN0K,GAAG,EAAEjI,EAAG;QACRoB,KAAK,EAAExB,qBAAsB;QAC7BE,YAAY,EAAEA,YAAa;QAC3BqH,MAAM,EAAEA,MAAO;QACfqC,SAAS,EAAE9J,gBAAiB;QAC5B2J,UAAU,EAAE,IAAK;QACjBhG,SAAS,EAAC,mDAAmD;QAC7DoG,QAAQ,EAAC,KAAK;QACdR,OAAO,EAAC,IAAI;QACZlF,iBAAiB,EAAEA,iBAAkB;QACrCmF,SAAS;QACTC,IAAI,EAAE,EAAG;QACTC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACpCM,gBAAgB,EAAC,QAAQ;QACzBC,YAAY,EAAC,2DAA2D;QAAAvG,QAAA,gBAExElF,OAAA,CAACT,MAAM;UAACiK,KAAK,EAAC,aAAa;UAACP,MAAM,EAAE7J,QAAQ,CAACsM;QAAK;UAAArG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC7DxF,OAAA,CAACT,MAAM;UAACiK,KAAK,EAAC,cAAc;UAACP,MAAM,EAAE7J,QAAQ,CAACuM;QAAO;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAChExF,OAAA,CAACT,MAAM;UAACiK,KAAK,EAAC,aAAa;UAACP,MAAM,EAAE7J,QAAQ,CAACwI,OAAQ;UAAChB,IAAI,EAAEe,mBAAoB;UAACiE,MAAM,EAAGlG,OAAO,IAAKsC,aAAa,CAACtC,OAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxIxF,OAAA,CAACT,MAAM;UAACiK,KAAK,EAAC,UAAU;UAACP,MAAM,EAAE7J,QAAQ,CAACyM,QAAS;UAACjF,IAAI,EAAE7B,iBAAkB;UAAC6G,MAAM,EAAGlG,OAAO,IAAKD,WAAW,CAACC,OAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACnIxF,OAAA,CAACT,MAAM;UAAC4F,SAAS,EAAC,iBAAiB;UAAC2G,SAAS;UAACC,WAAW,EAAE;YAAEvD,KAAK,EAAE;UAAO,CAAE;UAACwD,SAAS,EAAE;YAAEC,SAAS,EAAE;UAAS;QAAE;UAAA5G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC5HxF,OAAA,CAACT,MAAM;UAACqH,IAAI,EAAEgC;QAAmB;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,EAGT,CAAA9D,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEgC,MAAM,IAAG,CAAC,iBAC9B1D,OAAA;MAAKmF,SAAS,EAAC,oCAAoC;MAAAD,QAAA,eAC/ClF,OAAA,CAACP,MAAM;QAAC0F,SAAS,EAAC,sBAAsB;QAAC2D,OAAO,EAAEA,CAAA,KAAMhD,IAAI,CAAC,CAAE;QAAAZ,QAAA,gBAAClF,OAAA;UAAGmF,SAAS,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAACpG,QAAQ,CAAC8M,OAAO;MAAA;QAAA7G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9H,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGT,CAAC;AAGd,CAAC;AAAArF,EAAA,CAtXYF,eAAe;AAAAkM,EAAA,GAAflM,eAAe;AAAA,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}