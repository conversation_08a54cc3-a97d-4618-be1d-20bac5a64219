{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = omit;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nfunction omit(obj, fields) {\n  var clone = (0, _objectSpread2.default)({}, obj);\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n  return clone;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "omit", "_objectSpread2", "obj", "fields", "clone", "Array", "isArray", "for<PERSON>ach", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/lib/omit.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = omit;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\n\nfunction omit(obj, fields) {\n  var clone = (0, _objectSpread2.default)({}, obj);\n\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n\n  return clone;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,IAAIC,cAAc,GAAGR,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE5F,SAASM,IAAIA,CAACE,GAAG,EAAEC,MAAM,EAAE;EACzB,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAEH,cAAc,CAACF,OAAO,EAAE,CAAC,CAAC,EAAEG,GAAG,CAAC;EAEhD,IAAIG,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACzBA,MAAM,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC5B,OAAOJ,KAAK,CAACI,GAAG,CAAC;IACnB,CAAC,CAAC;EACJ;EAEA,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}