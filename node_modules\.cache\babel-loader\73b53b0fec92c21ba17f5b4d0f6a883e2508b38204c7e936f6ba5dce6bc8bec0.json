{"ast": null, "code": "var DescriptionsItem = function DescriptionsItem(_ref) {\n  var children = _ref.children;\n  return children;\n};\nexport default DescriptionsItem;", "map": {"version": 3, "names": ["DescriptionsItem", "_ref", "children"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/descriptions/Item.js"], "sourcesContent": ["var DescriptionsItem = function DescriptionsItem(_ref) {\n  var children = _ref.children;\n  return children;\n};\n\nexport default DescriptionsItem;"], "mappings": "AAAA,IAAIA,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EACrD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC;AAED,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}