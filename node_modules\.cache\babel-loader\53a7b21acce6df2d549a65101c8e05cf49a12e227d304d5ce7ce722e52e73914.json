{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Watch = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Watch = function Watch(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    version: \"1.1\",\n    id: \"L2\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    x: \"0px\",\n    y: \"0px\",\n    viewBox: \"0 0 100 100\",\n    enableBackground: \"new 0 0 100 100\",\n    xmlSpace: \"preserve\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"none\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    cx: \"50\",\n    cy: \"50\",\n    r: props.radius\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"line\", {\n    fill: \"none\",\n    strokeLinecap: \"round\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    x1: \"50\",\n    y1: \"50\",\n    x2: \"85\",\n    y2: \"50.5\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"2s\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"360 50 50\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"line\", {\n    fill: \"none\",\n    strokeLinecap: \"round\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    x1: \"50\",\n    y1: \"50\",\n    x2: \"49.5\",\n    y2: \"74\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"15s\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"360 50 50\",\n    repeatCount: \"indefinite\"\n  })));\n};\nexports.Watch = Watch;\nWatch.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nWatch.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 48\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Watch", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "version", "id", "xmlns", "x", "y", "viewBox", "enableBackground", "xmlSpace", "label", "fill", "stroke", "color", "strokeWidth", "strokeMiterlimit", "cx", "cy", "r", "radius", "strokeLinecap", "x1", "y1", "x2", "y2", "attributeName", "dur", "type", "from", "to", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Watch.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Watch = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Watch = function Watch(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    version: \"1.1\",\n    id: \"L2\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    x: \"0px\",\n    y: \"0px\",\n    viewBox: \"0 0 100 100\",\n    enableBackground: \"new 0 0 100 100\",\n    xmlSpace: \"preserve\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    fill: \"none\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    cx: \"50\",\n    cy: \"50\",\n    r: props.radius\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"line\", {\n    fill: \"none\",\n    strokeLinecap: \"round\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    x1: \"50\",\n    y1: \"50\",\n    x2: \"85\",\n    y2: \"50.5\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"2s\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"360 50 50\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"line\", {\n    fill: \"none\",\n    strokeLinecap: \"round\",\n    stroke: props.color,\n    strokeWidth: \"4\",\n    strokeMiterlimit: \"10\",\n    x1: \"50\",\n    y1: \"50\",\n    x2: \"49.5\",\n    y2: \"74\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"15s\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"360 50 50\",\n    repeatCount: \"indefinite\"\n  })));\n};\n\nexports.Watch = Watch;\nWatch.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nWatch.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 48\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AAEtB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,KAAK,GAAG,SAASA,KAAKA,CAACO,KAAK,EAAE;EAChC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,KAAK;IACdC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,4BAA4B;IACnCC,CAAC,EAAE,KAAK;IACRC,CAAC,EAAE,KAAK;IACRC,OAAO,EAAE,aAAa;IACtBC,gBAAgB,EAAE,iBAAiB;IACnCC,QAAQ,EAAE,UAAU;IACpB,YAAY,EAAEX,KAAK,CAACY;EACtB,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDY,IAAI,EAAE,MAAM;IACZC,MAAM,EAAEd,KAAK,CAACe,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEpB,KAAK,CAACqB;EACX,CAAC,CAAC,EAAE,aAAa3B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACvDY,IAAI,EAAE,MAAM;IACZS,aAAa,EAAE,OAAO;IACtBR,MAAM,EAAEd,KAAK,CAACe,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,gBAAgB,EAAE,IAAI;IACtBM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACN,CAAC,EAAE,aAAahC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClE0B,aAAa,EAAE,WAAW;IAC1BC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAatC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,MAAM,EAAE;IACxDY,IAAI,EAAE,MAAM;IACZS,aAAa,EAAE,OAAO;IACtBR,MAAM,EAAEd,KAAK,CAACe,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,gBAAgB,EAAE,IAAI;IACtBM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE;EACN,CAAC,EAAE,aAAahC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClE0B,aAAa,EAAE,WAAW;IAC1BC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAEDzC,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBA,KAAK,CAACwC,SAAS,GAAG;EAChB9B,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACqC,SAAS,CAAC,CAACrC,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM,EAAEtC,UAAU,CAAC,SAAS,CAAC,CAACuC,MAAM,CAAC,CAAC;EACrGlC,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACqC,SAAS,CAAC,CAACrC,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM,EAAEtC,UAAU,CAAC,SAAS,CAAC,CAACuC,MAAM,CAAC,CAAC;EACpGrB,KAAK,EAAElB,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM;EACnCvB,KAAK,EAAEf,UAAU,CAAC,SAAS,CAAC,CAACsC,MAAM;EACnCd,MAAM,EAAExB,UAAU,CAAC,SAAS,CAAC,CAACuC;AAChC,CAAC;AACD3C,KAAK,CAAC4C,YAAY,GAAG;EACnBlC,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTa,KAAK,EAAE,OAAO;EACdH,KAAK,EAAE,eAAe;EACtBS,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}