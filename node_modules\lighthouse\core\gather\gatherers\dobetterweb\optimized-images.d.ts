export default OptimizedImages;
export type SimplifiedNetworkRecord = {
    requestId: string;
    url: string;
    mimeType: string;
    resourceSize: number;
};
/** @typedef {{requestId: string, url: string, mimeType: string, resourceSize: number}} SimplifiedNetworkRecord */
declare class OptimizedImages extends FRGatherer {
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Array<SimplifiedNetworkRecord>}
     */
    static filterImageRequests(networkRecords: Array<LH.Artifacts.NetworkRequest>): Array<SimplifiedNetworkRecord>;
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    _encodingStartAt: number;
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} requestId
     * @param {'jpeg'|'webp'} encoding Either webp or jpeg.
     * @return {Promise<LH.Crdp.Audits.GetEncodedResponseResponse>}
     */
    _getEncodedResponse(session: LH.Gatherer.FRProtocolSession, requestId: string, encoding: 'jpeg' | 'webp'): Promise<LH.Crdp.Audits.GetEncodedResponseResponse>;
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {SimplifiedNetworkRecord} networkRecord
     * @return {Promise<{originalSize: number, jpegSize?: number, webpSize?: number}>}
     */
    calculateImageStats(session: LH.Gatherer.FRProtocolSession, networkRecord: SimplifiedNetworkRecord): Promise<{
        originalSize: number;
        jpegSize?: number | undefined;
        webpSize?: number | undefined;
    }>;
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {Array<SimplifiedNetworkRecord>} imageRecords
     * @return {Promise<LH.Artifacts['OptimizedImages']>}
     */
    computeOptimizedImages(session: LH.Gatherer.FRProtocolSession, imageRecords: Array<SimplifiedNetworkRecord>): Promise<LH.Artifacts['OptimizedImages']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @return {Promise<LH.Artifacts['OptimizedImages']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, networkRecords: LH.Artifacts.NetworkRequest[]): Promise<LH.Artifacts['OptimizedImages']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['OptimizedImages']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['OptimizedImages']>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts['OptimizedImages']>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts['OptimizedImages']>;
}
import FRGatherer from "../../base-gatherer.js";
import { NetworkRequest } from "../../../lib/network-request.js";
//# sourceMappingURL=optimized-images.d.ts.map