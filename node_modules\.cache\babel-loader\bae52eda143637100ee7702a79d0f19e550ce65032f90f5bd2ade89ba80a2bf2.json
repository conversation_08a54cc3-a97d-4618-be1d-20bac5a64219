{"ast": null, "code": "import axios from \"axios\";\nimport { error } from \"../route\";\n\n// Backend Endpoints Configuration\nconst vercelBackend = 'https://ep-backend-zeta.vercel.app'; // Primary staging backend\nconst localBackend = 'http://localhost:3001'; // Local backend\nconst localBackendAlt = 'http://localhost:3002'; // Alternative local backend\n\n// Smart backend selection with fallback\nlet currentBackend = null;\n\n// Initialize currentBackend immediately\nconst initializeBackend = () => {\n  if (process.env.REACT_APP_API_URL) {\n    currentBackend = process.env.REACT_APP_API_URL;\n  } else {\n    currentBackend = process.env.NODE_ENV === 'production' ? vercelBackend : localBackend;\n  }\n  return currentBackend;\n};\n\n// Initialize on module load\ninitializeBackend();\nconst getBaseURL = () => {\n  // Return current backend or initialize if needed\n  return currentBackend || initializeBackend();\n};\nexport const baseURL = getBaseURL();\n\n// Debug logging\nconsole.log('🔧 API Configuration:');\nconsole.log('📍 Base URL:', baseURL);\nconsole.log('🌍 Environment:', process.env.NODE_ENV);\nconsole.log('🔗 API URL Override:', process.env.REACT_APP_API_URL);\n\n// Proxy configuration for different environments\nexport const baseProxy = (() => {\n  // For Vercel backend, no proxy needed\n  if (baseURL.includes('vercel.app')) {\n    return '';\n  }\n  // For production domains, use /api/ prefix\n  if (window.location.href.includes('eprocurement.winet') || window.location.href.includes('eprocurement.tmselezioni')) {\n    return '/api/';\n  }\n  // For local development, no prefix\n  return '';\n})();\n\n// Debug logging for proxy\nconsole.log('🔀 Proxy Configuration:', baseProxy);\nconsole.log('🌐 Window Location:', window.location.href);\nconsole.log('🎯 Final API Endpoint:', baseURL + baseProxy);\n\n// Backend Health Check\nlet backendHealthStatus = 'unknown';\nlet lastHealthCheck = null;\nconst checkBackendHealth = async function () {\n  let showToast = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  const backendsToTry = [localBackend,\n  // Try local first (3001)\n  localBackendAlt,\n  // Then alternative local (3002)\n  vercelBackend // Then staging as fallback\n  ];\n  for (const backend of backendsToTry) {\n    try {\n      console.log(\"\\uD83D\\uDD0D Checking backend health: \".concat(backend));\n      const response = await fetch(backend + '/health', {\n        method: 'GET',\n        timeout: 3000\n      });\n      if (response.ok) {\n        // Update current backend if different\n        if (currentBackend !== backend) {\n          console.log(\"\\uD83D\\uDD04 Switching backend from \".concat(currentBackend, \" to \").concat(backend));\n          currentBackend = backend;\n        }\n        backendHealthStatus = 'online';\n        console.log(\"\\u2705 Backend is online: \".concat(backend));\n        updateStatusIndicator('online', backend);\n        if (showToast) {\n          showBackendNotification('success', '✅ Backend Connected', \"Successfully connected to \".concat(backend));\n        }\n        lastHealthCheck = new Date();\n        return; // Exit on first successful connection\n      }\n    } catch (error) {\n      console.log(\"\\u274C Backend \".concat(backend, \" failed: \").concat(error.message));\n    }\n  }\n\n  // All backends failed\n  backendHealthStatus = 'offline';\n  console.error('❌ All backends failed');\n  updateStatusIndicator('offline', currentBackend);\n  if (showToast) {\n    showBackendNotification('error', '❌ All Backends Offline', 'Cannot connect to any backend. Please check your connection.');\n  }\n  lastHealthCheck = new Date();\n};\nconst showBackendNotification = (type, title, message) => {\n  // Create a temporary notification element\n  const notification = document.createElement('div');\n  notification.style.cssText = \"\\n    position: fixed;\\n    top: 80px;\\n    right: 20px;\\n    z-index: 99999;\\n    padding: 20px 25px;\\n    border-radius: 12px;\\n    color: white;\\n    font-family: 'Segoe UI', Arial, sans-serif;\\n    font-size: 15px;\\n    max-width: 450px;\\n    min-width: 300px;\\n    box-shadow: 0 8px 32px rgba(0,0,0,0.4);\\n    background: \".concat(type === 'success' ? 'linear-gradient(135deg, #4CAF50, #45a049)' : 'linear-gradient(135deg, #f44336, #d32f2f)', \";\\n    border: 2px solid \").concat(type === 'success' ? '#4CAF50' : '#f44336', \";\\n    animation: slideIn 0.5s ease-out;\\n  \");\n  notification.innerHTML = \"\\n    <div style=\\\"font-weight: bold; margin-bottom: 8px; font-size: 16px;\\\">\".concat(title, \"</div>\\n    <div style=\\\"font-size: 13px; opacity: 0.95; line-height: 1.4;\\\">\").concat(message, \"</div>\\n    <div style=\\\"margin-top: 10px; font-size: 11px; opacity: 0.8;\\\">\\n      \").concat(new Date().toLocaleTimeString(), \" - Backend: \").concat(baseURL, \"\\n    </div>\\n  \");\n\n  // Add CSS animation\n  const style = document.createElement('style');\n  style.textContent = \"\\n    @keyframes slideIn {\\n      from { transform: translateX(100%); opacity: 0; }\\n      to { transform: translateX(0); opacity: 1; }\\n    }\\n  \";\n  document.head.appendChild(style);\n  document.body.appendChild(notification);\n\n  // Remove after 5 seconds\n  setTimeout(() => {\n    notification.style.animation = 'slideIn 0.3s ease-out reverse';\n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 300);\n  }, 5000);\n};\n\n// Create permanent status indicator\nconst createStatusIndicator = () => {\n  const indicator = document.createElement('div');\n  indicator.id = 'backend-status-indicator';\n  indicator.style.cssText = \"\\n    position: fixed;\\n    top: 10px;\\n    left: 50%;\\n    transform: translateX(-50%);\\n    z-index: 99999;\\n    padding: 8px 16px;\\n    border-radius: 20px;\\n    color: white;\\n    font-family: 'Segoe UI', Arial, sans-serif;\\n    font-size: 12px;\\n    font-weight: bold;\\n    background: #666;\\n    box-shadow: 0 2px 8px rgba(0,0,0,0.3);\\n    cursor: pointer;\\n    transition: all 0.3s ease;\\n  \";\n  indicator.innerHTML = '🔄 Checking Backend...';\n  indicator.onclick = () => checkBackendHealth(true);\n  document.body.appendChild(indicator);\n  return indicator;\n};\nconst updateStatusIndicator = (status, url) => {\n  const indicator = document.getElementById('backend-status-indicator');\n  if (!indicator) return;\n  if (status === 'online') {\n    indicator.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';\n    indicator.innerHTML = \"\\u2705 Backend Online - \".concat(url);\n  } else {\n    indicator.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';\n    indicator.innerHTML = \"\\u274C Backend Offline - \".concat(url);\n  }\n};\n\n// Check backend health on load\nsetTimeout(() => {\n  createStatusIndicator();\n  checkBackendHealth(true);\n}, 1000);\n\n/* Google reCaptcha v2 Site Key */\nconst gKeyLocal = \"6LfpdiIeAAAAACLH86utUFKqupLyW7ZcQZJb0N03\"; // localhost\nconst gKeyColl = \"6LdiNykeAAAAACcZzR_bhiiTC7eePzj7lMrsRANx\"; // collaudo\nconst gKeyProd = \"6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU\"; // produzione\nexport const SITE_KEY = window.location.href.includes('eprocurement.winet') ? gKeyProd : window.location.href.includes('centralunit.viniexport') ? gKeyColl : gKeyLocal;\nexport const APIRequest = (method, path, data) => {\n  return new Promise((resolve, reject) => {\n    if (typeof method === \"string\" && typeof path === \"string\") {\n      // Get current backend URL (dynamic) with safe fallback\n      const currentURL = currentBackend || baseURL || 'http://localhost:3002';\n      const safeProxy = baseProxy || '';\n\n      // Simple and safe URL construction\n      const parts = [currentURL.replace(/\\/$/, ''),\n      // Remove trailing slash\n      safeProxy.replace(/^\\/|\\/$/g, ''),\n      // Remove leading/trailing slashes\n      path.replace(/^\\//, '') // Remove leading slash\n      ].filter(part => part.length > 0); // Remove empty parts\n\n      const fullURL = parts.join('/');\n\n      // Log solo in modalità debug\n      if (process.env.REACT_APP_DEBUG === 'true') {\n        console.log('🔗 API Request:', method, fullURL);\n      }\n\n      // Validate URL before making request\n      if (!fullURL || fullURL === 'nullundefinedauth/') {\n        console.error('❌ Invalid URL constructed:', fullURL);\n        throw new Error('Invalid API URL constructed');\n      }\n      axios({\n        timeout: 60 * 60 * 1000,\n        method: method,\n        url: fullURL,\n        data: data,\n        headers: {\n          auth: localStorage.login_token,\n          accept: \"application/json\",\n          \"Content-Type\": \"application/json\"\n        }\n      }).then(res => {\n        var newToken = res.headers.token !== undefined ? res.headers.token : res.data.token !== undefined ? res.data.token : localStorage.getItem(\"login_token\");\n        if (newToken !== \"\") {\n          localStorage.setItem(\"login_token\", newToken);\n        }\n        resolve(res);\n      }).catch(e => {\n        // Enhanced error logging - solo in modalità debug\n        if (process.env.REACT_APP_DEBUG === 'true') {\n          var _e$response, _e$response2;\n          console.error('🚨 API Request Error:', {\n            url: baseURL + baseProxy + path,\n            method: method,\n            error: e,\n            response: e.response,\n            status: (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status,\n            data: (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data,\n            message: e.message\n          });\n        }\n\n        // Check if this is a connection error (backend offline)\n        if (!e.response || e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {\n          if (backendHealthStatus !== 'offline') {\n            backendHealthStatus = 'offline';\n            // Log silenzioso per connessione persa\n            console.warn('⚠️ Backend connection lost:', currentURL);\n\n            // Try to switch to a working backend\n            checkBackendHealth(false).then(() => {\n              // If we found a working backend, retry the request\n              if (backendHealthStatus === 'online' && currentBackend !== currentURL) {\n                if (process.env.REACT_APP_DEBUG === 'true') {\n                  console.log('🔄 Retrying request with new backend:', currentBackend);\n                }\n                // Retry the same request with new backend\n                APIRequest(method, path, data).then(resolve).catch(reject);\n                return;\n              }\n            });\n          }\n        }\n        if (e.response !== undefined) {\n          if (e.response.data === \"invalid password\" || e.response.data === \"username not found\") {\n            alert(e.response.data);\n          } else if (e.response.status === 401) {\n            localStorage.setItem(\"login_token\", \"\");\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          } else if (e.response.status === 500) {\n            window.sessionStorage.setItem(\"Error\", JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data));\n            window.sessionStorage.setItem(\"CodeError\", e.response.status);\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          }\n        } else {\n          if (e.response !== undefined) {\n            window.sessionStorage.setItem(\"Error\", JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data));\n            window.sessionStorage.setItem(\"CodeError\", e.response.status);\n            setTimeout(() => {\n              window.location.pathname = error;\n            }, 4000);\n          }\n        }\n        reject(e);\n      });\n    }\n  });\n};\n_c = APIRequest;\nvar _c;\n$RefreshReg$(_c, \"APIRequest\");", "map": {"version": 3, "names": ["axios", "error", "vercelBackend", "localBackend", "localBackendAlt", "currentBackend", "initializeBackend", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "getBaseURL", "baseURL", "console", "log", "baseProxy", "includes", "window", "location", "href", "backendHealthStatus", "lastHealthCheck", "checkBackendHealth", "showToast", "arguments", "length", "undefined", "backendsToTry", "backend", "concat", "response", "fetch", "method", "timeout", "ok", "updateStatusIndicator", "showBackendNotification", "Date", "message", "type", "title", "notification", "document", "createElement", "style", "cssText", "innerHTML", "toLocaleTimeString", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "setTimeout", "animation", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createStatusIndicator", "indicator", "id", "onclick", "status", "url", "getElementById", "background", "gKeyLocal", "gKeyColl", "gKeyProd", "SITE_KEY", "APIRequest", "path", "data", "Promise", "resolve", "reject", "currentURL", "safeProxy", "parts", "replace", "filter", "part", "fullURL", "join", "REACT_APP_DEBUG", "Error", "headers", "auth", "localStorage", "login_token", "accept", "then", "res", "newToken", "token", "getItem", "setItem", "catch", "e", "_e$response", "_e$response2", "code", "warn", "alert", "pathname", "sessionStorage", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/apireq.jsx"], "sourcesContent": ["import axios from \"axios\";\nimport { error } from \"../route\";\n\n// Backend Endpoints Configuration\nconst vercelBackend = 'https://ep-backend-zeta.vercel.app'  // Primary staging backend\nconst localBackend = 'http://localhost:3001'  // Local backend\nconst localBackendAlt = 'http://localhost:3002'  // Alternative local backend\n\n// Smart backend selection with fallback\nlet currentBackend = null;\n\n// Initialize currentBackend immediately\nconst initializeBackend = () => {\n  if (process.env.REACT_APP_API_URL) {\n    currentBackend = process.env.REACT_APP_API_URL;\n  } else {\n    currentBackend = process.env.NODE_ENV === 'production' ? vercelBackend : localBackend;\n  }\n\n  return currentBackend;\n};\n\n// Initialize on module load\ninitializeBackend();\n\nconst getBaseURL = () => {\n  // Return current backend or initialize if needed\n  return currentBackend || initializeBackend();\n};\n\nexport const baseURL = getBaseURL();\n\n// Debug logging\nconsole.log('🔧 API Configuration:');\nconsole.log('📍 Base URL:', baseURL);\nconsole.log('🌍 Environment:', process.env.NODE_ENV);\nconsole.log('🔗 API URL Override:', process.env.REACT_APP_API_URL);\n\n// Proxy configuration for different environments\nexport const baseProxy = (() => {\n  // For Vercel backend, no proxy needed\n  if (baseURL.includes('vercel.app')) {\n    return '';\n  }\n  // For production domains, use /api/ prefix\n  if (window.location.href.includes('eprocurement.winet') ||\n      window.location.href.includes('eprocurement.tmselezioni')) {\n    return '/api/';\n  }\n  // For local development, no prefix\n  return '';\n})();\n\n// Debug logging for proxy\nconsole.log('🔀 Proxy Configuration:', baseProxy);\nconsole.log('🌐 Window Location:', window.location.href);\nconsole.log('🎯 Final API Endpoint:', baseURL + baseProxy);\n\n// Backend Health Check\nlet backendHealthStatus = 'unknown';\nlet lastHealthCheck = null;\n\nconst checkBackendHealth = async (showToast = false) => {\n  const backendsToTry = [\n    localBackend,     // Try local first (3001)\n    localBackendAlt,  // Then alternative local (3002)\n    vercelBackend     // Then staging as fallback\n  ];\n\n  for (const backend of backendsToTry) {\n    try {\n      console.log(`🔍 Checking backend health: ${backend}`);\n      const response = await fetch(backend + '/health', {\n        method: 'GET',\n        timeout: 3000\n      });\n\n      if (response.ok) {\n        // Update current backend if different\n        if (currentBackend !== backend) {\n          console.log(`🔄 Switching backend from ${currentBackend} to ${backend}`);\n          currentBackend = backend;\n        }\n\n        backendHealthStatus = 'online';\n        console.log(`✅ Backend is online: ${backend}`);\n        updateStatusIndicator('online', backend);\n\n        if (showToast) {\n          showBackendNotification('success', '✅ Backend Connected',\n            `Successfully connected to ${backend}`);\n        }\n\n        lastHealthCheck = new Date();\n        return; // Exit on first successful connection\n      }\n    } catch (error) {\n      console.log(`❌ Backend ${backend} failed: ${error.message}`);\n    }\n  }\n\n  // All backends failed\n  backendHealthStatus = 'offline';\n  console.error('❌ All backends failed');\n  updateStatusIndicator('offline', currentBackend);\n\n  if (showToast) {\n    showBackendNotification('error', '❌ All Backends Offline',\n      'Cannot connect to any backend. Please check your connection.');\n  }\n\n  lastHealthCheck = new Date();\n};\n\nconst showBackendNotification = (type, title, message) => {\n  // Create a temporary notification element\n  const notification = document.createElement('div');\n  notification.style.cssText = `\n    position: fixed;\n    top: 80px;\n    right: 20px;\n    z-index: 99999;\n    padding: 20px 25px;\n    border-radius: 12px;\n    color: white;\n    font-family: 'Segoe UI', Arial, sans-serif;\n    font-size: 15px;\n    max-width: 450px;\n    min-width: 300px;\n    box-shadow: 0 8px 32px rgba(0,0,0,0.4);\n    background: ${type === 'success' ? 'linear-gradient(135deg, #4CAF50, #45a049)' : 'linear-gradient(135deg, #f44336, #d32f2f)'};\n    border: 2px solid ${type === 'success' ? '#4CAF50' : '#f44336'};\n    animation: slideIn 0.5s ease-out;\n  `;\n\n  notification.innerHTML = `\n    <div style=\"font-weight: bold; margin-bottom: 8px; font-size: 16px;\">${title}</div>\n    <div style=\"font-size: 13px; opacity: 0.95; line-height: 1.4;\">${message}</div>\n    <div style=\"margin-top: 10px; font-size: 11px; opacity: 0.8;\">\n      ${new Date().toLocaleTimeString()} - Backend: ${baseURL}\n    </div>\n  `;\n\n  // Add CSS animation\n  const style = document.createElement('style');\n  style.textContent = `\n    @keyframes slideIn {\n      from { transform: translateX(100%); opacity: 0; }\n      to { transform: translateX(0); opacity: 1; }\n    }\n  `;\n  document.head.appendChild(style);\n\n  document.body.appendChild(notification);\n\n  // Remove after 5 seconds\n  setTimeout(() => {\n    notification.style.animation = 'slideIn 0.3s ease-out reverse';\n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 300);\n  }, 5000);\n};\n\n// Create permanent status indicator\nconst createStatusIndicator = () => {\n  const indicator = document.createElement('div');\n  indicator.id = 'backend-status-indicator';\n  indicator.style.cssText = `\n    position: fixed;\n    top: 10px;\n    left: 50%;\n    transform: translateX(-50%);\n    z-index: 99999;\n    padding: 8px 16px;\n    border-radius: 20px;\n    color: white;\n    font-family: 'Segoe UI', Arial, sans-serif;\n    font-size: 12px;\n    font-weight: bold;\n    background: #666;\n    box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n    cursor: pointer;\n    transition: all 0.3s ease;\n  `;\n  indicator.innerHTML = '🔄 Checking Backend...';\n  indicator.onclick = () => checkBackendHealth(true);\n  document.body.appendChild(indicator);\n  return indicator;\n};\n\nconst updateStatusIndicator = (status, url) => {\n  const indicator = document.getElementById('backend-status-indicator');\n  if (!indicator) return;\n\n  if (status === 'online') {\n    indicator.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';\n    indicator.innerHTML = `✅ Backend Online - ${url}`;\n  } else {\n    indicator.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';\n    indicator.innerHTML = `❌ Backend Offline - ${url}`;\n  }\n};\n\n// Check backend health on load\nsetTimeout(() => {\n  createStatusIndicator();\n  checkBackendHealth(true);\n}, 1000);\n\n/* Google reCaptcha v2 Site Key */\nconst gKeyLocal = \"6LfpdiIeAAAAACLH86utUFKqupLyW7ZcQZJb0N03\" // localhost\nconst gKeyColl = \"6LdiNykeAAAAACcZzR_bhiiTC7eePzj7lMrsRANx\" // collaudo\nconst gKeyProd = \"6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU\" // produzione\nexport const SITE_KEY = window.location.href.includes('eprocurement.winet') ? gKeyProd : (window.location.href.includes('centralunit.viniexport') ? gKeyColl : gKeyLocal) ;\n\nexport const APIRequest = (method, path, data) => {\n\n  return new Promise((resolve, reject) => {\n    if (typeof method === \"string\" && typeof path === \"string\") {\n\n      // Get current backend URL (dynamic) with safe fallback\n      const currentURL = currentBackend || baseURL || 'http://localhost:3002';\n      const safeProxy = baseProxy || '';\n\n      // Simple and safe URL construction\n      const parts = [\n        currentURL.replace(/\\/$/, ''), // Remove trailing slash\n        safeProxy.replace(/^\\/|\\/$/g, ''), // Remove leading/trailing slashes\n        path.replace(/^\\//, '') // Remove leading slash\n      ].filter(part => part.length > 0); // Remove empty parts\n\n      const fullURL = parts.join('/');\n\n      // Log solo in modalità debug\n      if (process.env.REACT_APP_DEBUG === 'true') {\n        console.log('🔗 API Request:', method, fullURL);\n      }\n\n      // Validate URL before making request\n      if (!fullURL || fullURL === 'nullundefinedauth/') {\n        console.error('❌ Invalid URL constructed:', fullURL);\n        throw new Error('Invalid API URL constructed');\n      }\n\n      axios({\n        timeout: 60 * 60 * 1000,\n        method: method,\n        url: fullURL,\n        data: data,\n        headers: {\n          auth: localStorage.login_token,\n          accept: \"application/json\",\n          \"Content-Type\": \"application/json\"\n        },\n\n      })\n        .then((res) => {\n          var newToken = res.headers.token !== undefined ? res.headers.token : (res.data.token !== undefined ? res.data.token : localStorage.getItem(\"login_token\"));\n          if (newToken !== \"\") {\n            localStorage.setItem(\"login_token\", newToken);\n          }\n          resolve(res);\n        })\n        .catch((e) => {\n          // Enhanced error logging - solo in modalità debug\n          if (process.env.REACT_APP_DEBUG === 'true') {\n            console.error('🚨 API Request Error:', {\n              url: baseURL + baseProxy + path,\n              method: method,\n              error: e,\n              response: e.response,\n              status: e.response?.status,\n              data: e.response?.data,\n              message: e.message\n            });\n          }\n\n          // Check if this is a connection error (backend offline)\n          if (!e.response || e.code === 'NETWORK_ERROR' || e.message.includes('Network Error')) {\n            if (backendHealthStatus !== 'offline') {\n              backendHealthStatus = 'offline';\n              // Log silenzioso per connessione persa\n              console.warn('⚠️ Backend connection lost:', currentURL);\n\n              // Try to switch to a working backend\n              checkBackendHealth(false).then(() => {\n                // If we found a working backend, retry the request\n                if (backendHealthStatus === 'online' && currentBackend !== currentURL) {\n                  if (process.env.REACT_APP_DEBUG === 'true') {\n                    console.log('🔄 Retrying request with new backend:', currentBackend);\n                  }\n                  // Retry the same request with new backend\n                  APIRequest(method, path, data).then(resolve).catch(reject);\n                  return;\n                }\n              });\n            }\n          }\n\n          if (e.response !== undefined) {\n            if (\n              e.response.data === \"invalid password\" ||\n              e.response.data === \"username not found\"\n            ) {\n              alert(e.response.data);\n            } else if (e.response.status === 401) {\n              localStorage.setItem(\"login_token\", \"\");\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            } else if (e.response.status === 500) {\n              window.sessionStorage.setItem(\n                \"Error\",\n                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)\n              );\n              window.sessionStorage.setItem(\"CodeError\", e.response.status);\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            }\n          } else {\n            if (e.response !== undefined) {\n              window.sessionStorage.setItem(\n                \"Error\",\n                JSON.stringify(e.response.data.message !== undefined ? e.response.data.message : e.response.data)\n              );\n              window.sessionStorage.setItem(\"CodeError\", e.response.status);\n              setTimeout(() => {\n                window.location.pathname = error;\n              }, 4000);\n            }\n          }\n          reject(e);\n        });\n    }\n  });\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,UAAU;;AAEhC;AACA,MAAMC,aAAa,GAAG,oCAAoC,EAAE;AAC5D,MAAMC,YAAY,GAAG,uBAAuB,EAAE;AAC9C,MAAMC,eAAe,GAAG,uBAAuB,EAAE;;AAEjD;AACA,IAAIC,cAAc,GAAG,IAAI;;AAEzB;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;IACjCJ,cAAc,GAAGE,OAAO,CAACC,GAAG,CAACC,iBAAiB;EAChD,CAAC,MAAM;IACLJ,cAAc,GAAGE,OAAO,CAACC,GAAG,CAACE,QAAQ,KAAK,YAAY,GAAGR,aAAa,GAAGC,YAAY;EACvF;EAEA,OAAOE,cAAc;AACvB,CAAC;;AAED;AACAC,iBAAiB,CAAC,CAAC;AAEnB,MAAMK,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,OAAON,cAAc,IAAIC,iBAAiB,CAAC,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMM,OAAO,GAAGD,UAAU,CAAC,CAAC;;AAEnC;AACAE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;AACpCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;AACpCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,OAAO,CAACC,GAAG,CAACE,QAAQ,CAAC;AACpDG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEP,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAElE;AACA,OAAO,MAAMM,SAAS,GAAG,CAAC,MAAM;EAC9B;EACA,IAAIH,OAAO,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;IAClC,OAAO,EAAE;EACX;EACA;EACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,oBAAoB,CAAC,IACnDC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IAC7D,OAAO,OAAO;EAChB;EACA;EACA,OAAO,EAAE;AACX,CAAC,EAAE,CAAC;;AAEJ;AACAH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,SAAS,CAAC;AACjDF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;AACxDN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,OAAO,GAAGG,SAAS,CAAC;;AAE1D;AACA,IAAIK,mBAAmB,GAAG,SAAS;AACnC,IAAIC,eAAe,GAAG,IAAI;AAE1B,MAAMC,kBAAkB,GAAG,eAAAA,CAAA,EAA6B;EAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACjD,MAAMG,aAAa,GAAG,CACpBxB,YAAY;EAAM;EAClBC,eAAe;EAAG;EAClBF,aAAa,CAAK;EAAA,CACnB;EAED,KAAK,MAAM0B,OAAO,IAAID,aAAa,EAAE;IACnC,IAAI;MACFd,OAAO,CAACC,GAAG,0CAAAe,MAAA,CAAgCD,OAAO,CAAE,CAAC;MACrD,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAACH,OAAO,GAAG,SAAS,EAAE;QAChDI,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;QACf;QACA,IAAI7B,cAAc,KAAKuB,OAAO,EAAE;UAC9Bf,OAAO,CAACC,GAAG,wCAAAe,MAAA,CAA8BxB,cAAc,UAAAwB,MAAA,CAAOD,OAAO,CAAE,CAAC;UACxEvB,cAAc,GAAGuB,OAAO;QAC1B;QAEAR,mBAAmB,GAAG,QAAQ;QAC9BP,OAAO,CAACC,GAAG,8BAAAe,MAAA,CAAyBD,OAAO,CAAE,CAAC;QAC9CO,qBAAqB,CAAC,QAAQ,EAAEP,OAAO,CAAC;QAExC,IAAIL,SAAS,EAAE;UACba,uBAAuB,CAAC,SAAS,EAAE,qBAAqB,+BAAAP,MAAA,CACzBD,OAAO,CAAE,CAAC;QAC3C;QAEAP,eAAe,GAAG,IAAIgB,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC;MACV;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdY,OAAO,CAACC,GAAG,mBAAAe,MAAA,CAAcD,OAAO,eAAAC,MAAA,CAAY5B,KAAK,CAACqC,OAAO,CAAE,CAAC;IAC9D;EACF;;EAEA;EACAlB,mBAAmB,GAAG,SAAS;EAC/BP,OAAO,CAACZ,KAAK,CAAC,uBAAuB,CAAC;EACtCkC,qBAAqB,CAAC,SAAS,EAAE9B,cAAc,CAAC;EAEhD,IAAIkB,SAAS,EAAE;IACba,uBAAuB,CAAC,OAAO,EAAE,wBAAwB,EACvD,8DAA8D,CAAC;EACnE;EAEAf,eAAe,GAAG,IAAIgB,IAAI,CAAC,CAAC;AAC9B,CAAC;AAED,MAAMD,uBAAuB,GAAGA,CAACG,IAAI,EAAEC,KAAK,EAAEF,OAAO,KAAK;EACxD;EACA,MAAMG,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAClDF,YAAY,CAACG,KAAK,CAACC,OAAO,6UAAAhB,MAAA,CAaVU,IAAI,KAAK,SAAS,GAAG,2CAA2C,GAAG,2CAA2C,+BAAAV,MAAA,CACxGU,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,iDAE/D;EAEDE,YAAY,CAACK,SAAS,mFAAAjB,MAAA,CACmDW,KAAK,mFAAAX,MAAA,CACXS,OAAO,0FAAAT,MAAA,CAEpE,IAAIQ,IAAI,CAAC,CAAC,CAACU,kBAAkB,CAAC,CAAC,kBAAAlB,MAAA,CAAejB,OAAO,qBAE1D;;EAED;EACA,MAAMgC,KAAK,GAAGF,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAC7CC,KAAK,CAACI,WAAW,uJAKhB;EACDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACN,KAAK,CAAC;EAEhCF,QAAQ,CAACS,IAAI,CAACD,WAAW,CAACT,YAAY,CAAC;;EAEvC;EACAW,UAAU,CAAC,MAAM;IACfX,YAAY,CAACG,KAAK,CAACS,SAAS,GAAG,+BAA+B;IAC9DD,UAAU,CAAC,MAAM;MACf,IAAIX,YAAY,CAACa,UAAU,EAAE;QAC3Bb,YAAY,CAACa,UAAU,CAACC,WAAW,CAACd,YAAY,CAAC;MACnD;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;EAClC,MAAMC,SAAS,GAAGf,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC/Cc,SAAS,CAACC,EAAE,GAAG,0BAA0B;EACzCD,SAAS,CAACb,KAAK,CAACC,OAAO,mZAgBtB;EACDY,SAAS,CAACX,SAAS,GAAG,wBAAwB;EAC9CW,SAAS,CAACE,OAAO,GAAG,MAAMrC,kBAAkB,CAAC,IAAI,CAAC;EAClDoB,QAAQ,CAACS,IAAI,CAACD,WAAW,CAACO,SAAS,CAAC;EACpC,OAAOA,SAAS;AAClB,CAAC;AAED,MAAMtB,qBAAqB,GAAGA,CAACyB,MAAM,EAAEC,GAAG,KAAK;EAC7C,MAAMJ,SAAS,GAAGf,QAAQ,CAACoB,cAAc,CAAC,0BAA0B,CAAC;EACrE,IAAI,CAACL,SAAS,EAAE;EAEhB,IAAIG,MAAM,KAAK,QAAQ,EAAE;IACvBH,SAAS,CAACb,KAAK,CAACmB,UAAU,GAAG,2CAA2C;IACxEN,SAAS,CAACX,SAAS,8BAAAjB,MAAA,CAAyBgC,GAAG,CAAE;EACnD,CAAC,MAAM;IACLJ,SAAS,CAACb,KAAK,CAACmB,UAAU,GAAG,2CAA2C;IACxEN,SAAS,CAACX,SAAS,+BAAAjB,MAAA,CAA0BgC,GAAG,CAAE;EACpD;AACF,CAAC;;AAED;AACAT,UAAU,CAAC,MAAM;EACfI,qBAAqB,CAAC,CAAC;EACvBlC,kBAAkB,CAAC,IAAI,CAAC;AAC1B,CAAC,EAAE,IAAI,CAAC;;AAER;AACA,MAAM0C,SAAS,GAAG,0CAA0C,EAAC;AAC7D,MAAMC,QAAQ,GAAG,0CAA0C,EAAC;AAC5D,MAAMC,QAAQ,GAAG,0CAA0C,EAAC;AAC5D,OAAO,MAAMC,QAAQ,GAAGlD,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,oBAAoB,CAAC,GAAGkD,QAAQ,GAAIjD,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,wBAAwB,CAAC,GAAGiD,QAAQ,GAAGD,SAAU;AAEzK,OAAO,MAAMI,UAAU,GAAGA,CAACpC,MAAM,EAAEqC,IAAI,EAAEC,IAAI,KAAK;EAEhD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,OAAOzC,MAAM,KAAK,QAAQ,IAAI,OAAOqC,IAAI,KAAK,QAAQ,EAAE;MAE1D;MACA,MAAMK,UAAU,GAAGrE,cAAc,IAAIO,OAAO,IAAI,uBAAuB;MACvE,MAAM+D,SAAS,GAAG5D,SAAS,IAAI,EAAE;;MAEjC;MACA,MAAM6D,KAAK,GAAG,CACZF,UAAU,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAAE;MAC/BF,SAAS,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAAE;MACnCR,IAAI,CAACQ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAAA,CACzB,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACtD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEnC,MAAMuD,OAAO,GAAGJ,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;;MAE/B;MACA,IAAI1E,OAAO,CAACC,GAAG,CAAC0E,eAAe,KAAK,MAAM,EAAE;QAC1CrE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkB,MAAM,EAAEgD,OAAO,CAAC;MACjD;;MAEA;MACA,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,oBAAoB,EAAE;QAChDnE,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAE+E,OAAO,CAAC;QACpD,MAAM,IAAIG,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEAnF,KAAK,CAAC;QACJiC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACvBD,MAAM,EAAEA,MAAM;QACd6B,GAAG,EAAEmB,OAAO;QACZV,IAAI,EAAEA,IAAI;QACVc,OAAO,EAAE;UACPC,IAAI,EAAEC,YAAY,CAACC,WAAW;UAC9BC,MAAM,EAAE,kBAAkB;UAC1B,cAAc,EAAE;QAClB;MAEF,CAAC,CAAC,CACCC,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIC,QAAQ,GAAGD,GAAG,CAACN,OAAO,CAACQ,KAAK,KAAKlE,SAAS,GAAGgE,GAAG,CAACN,OAAO,CAACQ,KAAK,GAAIF,GAAG,CAACpB,IAAI,CAACsB,KAAK,KAAKlE,SAAS,GAAGgE,GAAG,CAACpB,IAAI,CAACsB,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,aAAa,CAAE;QAC1J,IAAIF,QAAQ,KAAK,EAAE,EAAE;UACnBL,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAEH,QAAQ,CAAC;QAC/C;QACAnB,OAAO,CAACkB,GAAG,CAAC;MACd,CAAC,CAAC,CACDK,KAAK,CAAEC,CAAC,IAAK;QACZ;QACA,IAAIzF,OAAO,CAACC,GAAG,CAAC0E,eAAe,KAAK,MAAM,EAAE;UAAA,IAAAe,WAAA,EAAAC,YAAA;UAC1CrF,OAAO,CAACZ,KAAK,CAAC,uBAAuB,EAAE;YACrC4D,GAAG,EAAEjD,OAAO,GAAGG,SAAS,GAAGsD,IAAI;YAC/BrC,MAAM,EAAEA,MAAM;YACd/B,KAAK,EAAE+F,CAAC;YACRlE,QAAQ,EAAEkE,CAAC,CAAClE,QAAQ;YACpB8B,MAAM,GAAAqC,WAAA,GAAED,CAAC,CAAClE,QAAQ,cAAAmE,WAAA,uBAAVA,WAAA,CAAYrC,MAAM;YAC1BU,IAAI,GAAA4B,YAAA,GAAEF,CAAC,CAAClE,QAAQ,cAAAoE,YAAA,uBAAVA,YAAA,CAAY5B,IAAI;YACtBhC,OAAO,EAAE0D,CAAC,CAAC1D;UACb,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI,CAAC0D,CAAC,CAAClE,QAAQ,IAAIkE,CAAC,CAACG,IAAI,KAAK,eAAe,IAAIH,CAAC,CAAC1D,OAAO,CAACtB,QAAQ,CAAC,eAAe,CAAC,EAAE;UACpF,IAAII,mBAAmB,KAAK,SAAS,EAAE;YACrCA,mBAAmB,GAAG,SAAS;YAC/B;YACAP,OAAO,CAACuF,IAAI,CAAC,6BAA6B,EAAE1B,UAAU,CAAC;;YAEvD;YACApD,kBAAkB,CAAC,KAAK,CAAC,CAACmE,IAAI,CAAC,MAAM;cACnC;cACA,IAAIrE,mBAAmB,KAAK,QAAQ,IAAIf,cAAc,KAAKqE,UAAU,EAAE;gBACrE,IAAInE,OAAO,CAACC,GAAG,CAAC0E,eAAe,KAAK,MAAM,EAAE;kBAC1CrE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAET,cAAc,CAAC;gBACtE;gBACA;gBACA+D,UAAU,CAACpC,MAAM,EAAEqC,IAAI,EAAEC,IAAI,CAAC,CAACmB,IAAI,CAACjB,OAAO,CAAC,CAACuB,KAAK,CAACtB,MAAM,CAAC;gBAC1D;cACF;YACF,CAAC,CAAC;UACJ;QACF;QAEA,IAAIuB,CAAC,CAAClE,QAAQ,KAAKJ,SAAS,EAAE;UAC5B,IACEsE,CAAC,CAAClE,QAAQ,CAACwC,IAAI,KAAK,kBAAkB,IACtC0B,CAAC,CAAClE,QAAQ,CAACwC,IAAI,KAAK,oBAAoB,EACxC;YACA+B,KAAK,CAACL,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAAC;UACxB,CAAC,MAAM,IAAI0B,CAAC,CAAClE,QAAQ,CAAC8B,MAAM,KAAK,GAAG,EAAE;YACpC0B,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;YACvC1C,UAAU,CAAC,MAAM;cACfnC,MAAM,CAACC,QAAQ,CAACoF,QAAQ,GAAGrG,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM,IAAI+F,CAAC,CAAClE,QAAQ,CAAC8B,MAAM,KAAK,GAAG,EAAE;YACpC3C,MAAM,CAACsF,cAAc,CAACT,OAAO,CAC3B,OAAO,EACPU,IAAI,CAACC,SAAS,CAACT,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAAChC,OAAO,KAAKZ,SAAS,GAAGsE,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAAChC,OAAO,GAAG0D,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAClG,CAAC;YACDrD,MAAM,CAACsF,cAAc,CAACT,OAAO,CAAC,WAAW,EAAEE,CAAC,CAAClE,QAAQ,CAAC8B,MAAM,CAAC;YAC7DR,UAAU,CAAC,MAAM;cACfnC,MAAM,CAACC,QAAQ,CAACoF,QAAQ,GAAGrG,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,MAAM;UACL,IAAI+F,CAAC,CAAClE,QAAQ,KAAKJ,SAAS,EAAE;YAC5BT,MAAM,CAACsF,cAAc,CAACT,OAAO,CAC3B,OAAO,EACPU,IAAI,CAACC,SAAS,CAACT,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAAChC,OAAO,KAAKZ,SAAS,GAAGsE,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAAChC,OAAO,GAAG0D,CAAC,CAAClE,QAAQ,CAACwC,IAAI,CAClG,CAAC;YACDrD,MAAM,CAACsF,cAAc,CAACT,OAAO,CAAC,WAAW,EAAEE,CAAC,CAAClE,QAAQ,CAAC8B,MAAM,CAAC;YAC7DR,UAAU,CAAC,MAAM;cACfnC,MAAM,CAACC,QAAQ,CAACoF,QAAQ,GAAGrG,KAAK;YAClC,CAAC,EAAE,IAAI,CAAC;UACV;QACF;QACAwE,MAAM,CAACuB,CAAC,CAAC;MACX,CAAC,CAAC;IACN;EACF,CAAC,CAAC;AACJ,CAAC;AAACU,EAAA,GAzHWtC,UAAU;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}