{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Triangle = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Triangle = function Triangle(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-svg\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    id: \"triangle\",\n    width: props.width,\n    height: props.height,\n    viewBox: \"-3 -4 39 39\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"polygon\", {\n    fill: \"transparent\",\n    stroke: props.color,\n    strokeWidth: \"1\",\n    points: \"16,0 32,32 0,32\"\n  })));\n};\nexports.Triangle = Triangle;\nTriangle.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string\n};\nTriangle.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Triangle", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "className", "id", "width", "height", "viewBox", "label", "fill", "stroke", "color", "strokeWidth", "points", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Triangle.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Triangle = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Triangle = function Triangle(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-svg\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    id: \"triangle\",\n    width: props.width,\n    height: props.height,\n    viewBox: \"-3 -4 39 39\",\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"polygon\", {\n    fill: \"transparent\",\n    stroke: props.color,\n    strokeWidth: \"1\",\n    points: \"16,0 32,32 0,32\"\n  })));\n};\n\nexports.Triangle = Triangle;\nTriangle.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string\n};\nTriangle.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AAEzB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,QAAQ,GAAG,SAASA,QAAQA,CAACO,KAAK,EAAE;EACtC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,SAAS,EAAE;EACb,CAAC,EAAE,aAAaR,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACrDE,EAAE,EAAE,UAAU;IACdC,KAAK,EAAEJ,KAAK,CAACI,KAAK;IAClBC,MAAM,EAAEL,KAAK,CAACK,MAAM;IACpBC,OAAO,EAAE,aAAa;IACtB,YAAY,EAAEN,KAAK,CAACO;EACtB,CAAC,EAAE,aAAab,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDO,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAET,KAAK,CAACU,KAAK;IACnBC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAEDrB,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3BA,QAAQ,CAACoB,SAAS,GAAG;EACnBR,MAAM,EAAER,UAAU,CAAC,SAAS,CAAC,CAACiB,SAAS,CAAC,CAACjB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM,EAAElB,UAAU,CAAC,SAAS,CAAC,CAACmB,MAAM,CAAC,CAAC;EACrGZ,KAAK,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACiB,SAAS,CAAC,CAACjB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM,EAAElB,UAAU,CAAC,SAAS,CAAC,CAACmB,MAAM,CAAC,CAAC;EACpGN,KAAK,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACnCR,KAAK,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACkB;AAC/B,CAAC;AACDtB,QAAQ,CAACwB,YAAY,GAAG;EACtBZ,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTM,KAAK,EAAE,OAAO;EACdH,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}