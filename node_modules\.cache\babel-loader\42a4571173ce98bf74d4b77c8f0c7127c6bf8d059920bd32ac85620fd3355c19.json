{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcCollapse from 'rc-collapse';\nimport classNames from 'classnames';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport CollapsePanel from './CollapsePanel';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nvar Collapse = function Collapse(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$bordered = props.bordered,\n    bordered = _props$bordered === void 0 ? true : _props$bordered,\n    ghost = props.ghost;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var getIconPosition = function getIconPosition() {\n    var expandIconPosition = props.expandIconPosition;\n    if (expandIconPosition !== undefined) {\n      return expandIconPosition;\n    }\n    return direction === 'rtl' ? 'right' : 'left';\n  };\n  var renderExpandIcon = function renderExpandIcon() {\n    var panelProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expandIcon = props.expandIcon;\n    var icon = expandIcon ? expandIcon(panelProps) : /*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? 90 : undefined\n    });\n    return (/*#__PURE__*/\n      // Create additional div here to make arrow align to center of first line\n      React.createElement(\"div\", null, cloneElement(icon, function () {\n        return {\n          className: classNames(icon.props.className, \"\".concat(prefixCls, \"-arrow\"))\n        };\n      }))\n    );\n  };\n  var iconPosition = getIconPosition();\n  var collapseClassName = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-position-\").concat(iconPosition), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), !!ghost), _classNames), className);\n  var openMotion = _extends(_extends({}, collapseMotion), {\n    motionAppear: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  });\n  var getItems = function getItems() {\n    var children = props.children;\n    return toArray(children).map(function (child, index) {\n      var _a;\n      if ((_a = child.props) === null || _a === void 0 ? void 0 : _a.disabled) {\n        var key = child.key || String(index);\n        var _child$props = child.props,\n          disabled = _child$props.disabled,\n          collapsible = _child$props.collapsible;\n        var childProps = _extends(_extends({}, omit(child.props, ['disabled'])), {\n          key: key,\n          collapsible: collapsible !== null && collapsible !== void 0 ? collapsible : disabled ? 'disabled' : undefined\n        });\n        return cloneElement(child, childProps);\n      }\n      return child;\n    });\n  };\n  return /*#__PURE__*/React.createElement(RcCollapse, _extends({\n    openMotion: openMotion\n  }, props, {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName\n  }), getItems());\n};\nCollapse.Panel = CollapsePanel;\nexport default Collapse;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "RcCollapse", "classNames", "RightOutlined", "toArray", "omit", "CollapsePanel", "ConfigContext", "collapseMotion", "cloneElement", "Collapse", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$className", "className", "_props$bordered", "bordered", "ghost", "getIconPosition", "expandIconPosition", "undefined", "renderExpandIcon", "panelProps", "arguments", "length", "expandIcon", "icon", "createElement", "rotate", "isActive", "concat", "iconPosition", "collapseClassName", "openMotion", "motionAppear", "leavedClassName", "getItems", "children", "map", "child", "index", "_a", "disabled", "key", "String", "_child$props", "collapsible", "childProps", "Panel"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/collapse/Collapse.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcCollapse from 'rc-collapse';\nimport classNames from 'classnames';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport CollapsePanel from './CollapsePanel';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\n\nvar Collapse = function Collapse(props) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var customizePrefixCls = props.prefixCls,\n      _props$className = props.className,\n      className = _props$className === void 0 ? '' : _props$className,\n      _props$bordered = props.bordered,\n      bordered = _props$bordered === void 0 ? true : _props$bordered,\n      ghost = props.ghost;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n\n  var getIconPosition = function getIconPosition() {\n    var expandIconPosition = props.expandIconPosition;\n\n    if (expandIconPosition !== undefined) {\n      return expandIconPosition;\n    }\n\n    return direction === 'rtl' ? 'right' : 'left';\n  };\n\n  var renderExpandIcon = function renderExpandIcon() {\n    var panelProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expandIcon = props.expandIcon;\n    var icon = expandIcon ? expandIcon(panelProps) : /*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? 90 : undefined\n    });\n    return (\n      /*#__PURE__*/\n      // Create additional div here to make arrow align to center of first line\n      React.createElement(\"div\", null, cloneElement(icon, function () {\n        return {\n          className: classNames(icon.props.className, \"\".concat(prefixCls, \"-arrow\"))\n        };\n      }))\n    );\n  };\n\n  var iconPosition = getIconPosition();\n  var collapseClassName = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-position-\").concat(iconPosition), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), !!ghost), _classNames), className);\n\n  var openMotion = _extends(_extends({}, collapseMotion), {\n    motionAppear: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  });\n\n  var getItems = function getItems() {\n    var children = props.children;\n    return toArray(children).map(function (child, index) {\n      var _a;\n\n      if ((_a = child.props) === null || _a === void 0 ? void 0 : _a.disabled) {\n        var key = child.key || String(index);\n        var _child$props = child.props,\n            disabled = _child$props.disabled,\n            collapsible = _child$props.collapsible;\n\n        var childProps = _extends(_extends({}, omit(child.props, ['disabled'])), {\n          key: key,\n          collapsible: collapsible !== null && collapsible !== void 0 ? collapsible : disabled ? 'disabled' : undefined\n        });\n\n        return cloneElement(child, childProps);\n      }\n\n      return child;\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(RcCollapse, _extends({\n    openMotion: openMotion\n  }, props, {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName\n  }), getItems());\n};\n\nCollapse.Panel = CollapsePanel;\nexport default Collapse;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACP,aAAa,CAAC;IACnDQ,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,kBAAkB,GAAGN,KAAK,CAACO,SAAS;IACpCC,gBAAgB,GAAGR,KAAK,CAACS,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,eAAe,GAAGV,KAAK,CAACW,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,KAAK,GAAGZ,KAAK,CAACY,KAAK;EACvB,IAAIL,SAAS,GAAGH,YAAY,CAAC,UAAU,EAAEE,kBAAkB,CAAC;EAE5D,IAAIO,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIC,kBAAkB,GAAGd,KAAK,CAACc,kBAAkB;IAEjD,IAAIA,kBAAkB,KAAKC,SAAS,EAAE;MACpC,OAAOD,kBAAkB;IAC3B;IAEA,OAAOT,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;EAC/C,CAAC;EAED,IAAIW,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKH,SAAS,GAAGG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvF,IAAIE,UAAU,GAAGpB,KAAK,CAACoB,UAAU;IACjC,IAAIC,IAAI,GAAGD,UAAU,GAAGA,UAAU,CAACH,UAAU,CAAC,GAAG,aAAa5B,KAAK,CAACiC,aAAa,CAAC9B,aAAa,EAAE;MAC/F+B,MAAM,EAAEN,UAAU,CAACO,QAAQ,GAAG,EAAE,GAAGT;IACrC,CAAC,CAAC;IACF,QACE;MACA;MACA1B,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAExB,YAAY,CAACuB,IAAI,EAAE,YAAY;QAC9D,OAAO;UACLZ,SAAS,EAAElB,UAAU,CAAC8B,IAAI,CAACrB,KAAK,CAACS,SAAS,EAAE,EAAE,CAACgB,MAAM,CAAClB,SAAS,EAAE,QAAQ,CAAC;QAC5E,CAAC;MACH,CAAC,CAAC;IAAC;EAEP,CAAC;EAED,IAAImB,YAAY,GAAGb,eAAe,CAAC,CAAC;EACpC,IAAIc,iBAAiB,GAAGpC,UAAU,EAAEU,WAAW,GAAG,CAAC,CAAC,EAAEb,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAAClB,SAAS,EAAE,aAAa,CAAC,EAAE,CAACI,QAAQ,CAAC,EAAEvB,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAAClB,SAAS,EAAE,iBAAiB,CAAC,CAACkB,MAAM,CAACC,YAAY,CAAC,EAAE,IAAI,CAAC,EAAEtC,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAAClB,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAEjB,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAAClB,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAACK,KAAK,CAAC,EAAEX,WAAW,GAAGQ,SAAS,CAAC;EAEtZ,IAAImB,UAAU,GAAGzC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEU,cAAc,CAAC,EAAE;IACtDgC,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE,EAAE,CAACL,MAAM,CAAClB,SAAS,EAAE,iBAAiB;EACzD,CAAC,CAAC;EAEF,IAAIwB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,IAAIC,QAAQ,GAAGhC,KAAK,CAACgC,QAAQ;IAC7B,OAAOvC,OAAO,CAACuC,QAAQ,CAAC,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MACnD,IAAIC,EAAE;MAEN,IAAI,CAACA,EAAE,GAAGF,KAAK,CAAClC,KAAK,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,EAAE;QACvE,IAAIC,GAAG,GAAGJ,KAAK,CAACI,GAAG,IAAIC,MAAM,CAACJ,KAAK,CAAC;QACpC,IAAIK,YAAY,GAAGN,KAAK,CAAClC,KAAK;UAC1BqC,QAAQ,GAAGG,YAAY,CAACH,QAAQ;UAChCI,WAAW,GAAGD,YAAY,CAACC,WAAW;QAE1C,IAAIC,UAAU,GAAGvD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,IAAI,CAACwC,KAAK,CAAClC,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;UACvEsC,GAAG,EAAEA,GAAG;UACRG,WAAW,EAAEA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGJ,QAAQ,GAAG,UAAU,GAAGtB;QACtG,CAAC,CAAC;QAEF,OAAOjB,YAAY,CAACoC,KAAK,EAAEQ,UAAU,CAAC;MACxC;MAEA,OAAOR,KAAK;IACd,CAAC,CAAC;EACJ,CAAC;EAED,OAAO,aAAa7C,KAAK,CAACiC,aAAa,CAAChC,UAAU,EAAEH,QAAQ,CAAC;IAC3DyC,UAAU,EAAEA;EACd,CAAC,EAAE5B,KAAK,EAAE;IACRoB,UAAU,EAAEJ,gBAAgB;IAC5BT,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEkB;EACb,CAAC,CAAC,EAAEI,QAAQ,CAAC,CAAC,CAAC;AACjB,CAAC;AAEDhC,QAAQ,CAAC4C,KAAK,GAAGhD,aAAa;AAC9B,eAAeI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}