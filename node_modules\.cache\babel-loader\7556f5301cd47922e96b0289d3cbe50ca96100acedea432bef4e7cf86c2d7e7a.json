{"ast": null, "code": "import * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { FormProvider as RcFormProvider } from 'rc-field-form';\nimport { useMemo } from 'react';\nexport var FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexport var NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport var FormProvider = function FormProvider(props) {\n  var providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, providerProps);\n};\nexport var FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport var FormItemInputContext = /*#__PURE__*/React.createContext({});\nexport var NoFormStatus = function NoFormStatus(_ref) {\n  var children = _ref.children;\n  var emptyContext = useMemo(function () {\n    return {};\n  }, []);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: emptyContext\n  }, children);\n};", "map": {"version": 3, "names": ["React", "omit", "FormProvider", "RcFormProvider", "useMemo", "FormContext", "createContext", "labelAlign", "vertical", "itemRef", "NoStyleItemContext", "props", "providerProps", "createElement", "FormItemPrefixContext", "prefixCls", "FormItemInputContext", "NoFormStatus", "_ref", "children", "emptyContext", "Provider", "value"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/context.js"], "sourcesContent": ["import * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { FormProvider as RcFormProvider } from 'rc-field-form';\nimport { useMemo } from 'react';\nexport var FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  vertical: false,\n  itemRef: function itemRef() {}\n});\nexport var NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport var FormProvider = function FormProvider(props) {\n  var providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, providerProps);\n};\nexport var FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport var FormItemInputContext = /*#__PURE__*/React.createContext({});\nexport var NoFormStatus = function NoFormStatus(_ref) {\n  var children = _ref.children;\n  var emptyContext = useMemo(function () {\n    return {};\n  }, []);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: emptyContext\n  }, children);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,YAAY,IAAIC,cAAc,QAAQ,eAAe;AAC9D,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAO,IAAIC,WAAW,GAAG,aAAaL,KAAK,CAACM,aAAa,CAAC;EACxDC,UAAU,EAAE,OAAO;EACnBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;AAC/B,CAAC,CAAC;AACF,OAAO,IAAIC,kBAAkB,GAAG,aAAaV,KAAK,CAACM,aAAa,CAAC,IAAI,CAAC;AACtE,OAAO,IAAIJ,YAAY,GAAG,SAASA,YAAYA,CAACS,KAAK,EAAE;EACrD,IAAIC,aAAa,GAAGX,IAAI,CAACU,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC9C,OAAO,aAAaX,KAAK,CAACa,aAAa,CAACV,cAAc,EAAES,aAAa,CAAC;AACxE,CAAC;AACD,OAAO,IAAIE,qBAAqB,GAAG,aAAad,KAAK,CAACM,aAAa,CAAC;EAClES,SAAS,EAAE;AACb,CAAC,CAAC;AACF,OAAO,IAAIC,oBAAoB,GAAG,aAAahB,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;AACtE,OAAO,IAAIW,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EACpD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAIC,YAAY,GAAGhB,OAAO,CAAC,YAAY;IACrC,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaJ,KAAK,CAACa,aAAa,CAACG,oBAAoB,CAACK,QAAQ,EAAE;IACrEC,KAAK,EAAEF;EACT,CAAC,EAAED,QAAQ,CAAC;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}