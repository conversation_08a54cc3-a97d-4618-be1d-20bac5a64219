"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = getAllConfigs;
var _defaultConfigStubJs = _interopRequireDefault(require("../../stubs/defaultConfig.stub.js"));
var _featureFlags = require("../featureFlags");
function getAllConfigs(config) {
    var ref;
    const configs = ((ref = config === null || config === void 0 ? void 0 : config.presets) !== null && ref !== void 0 ? ref : [
        _defaultConfigStubJs.default
    ]).slice().reverse().flatMap((preset)=>getAllConfigs(preset instanceof Function ? preset() : preset)
    );
    const features = {
    };
    const experimentals = Object.keys(features).filter((feature)=>(0, _featureFlags).flagEnabled(config, feature)
    ).map((feature)=>features[feature]
    );
    return [
        config,
        ...experimentals,
        ...configs
    ];
}
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
