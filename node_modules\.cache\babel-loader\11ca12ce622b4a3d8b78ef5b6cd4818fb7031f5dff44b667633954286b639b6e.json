{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport Align from 'rc-align';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport useVisibleStatus from './useVisibleStatus';\nimport { getMotion } from '../utils/legacyUtil';\nimport useStretchStyle from './useStretchStyle';\nvar PopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var visible = props.visible,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    zIndex = props.zIndex,\n    stretch = props.stretch,\n    destroyPopupOnHide = props.destroyPopupOnHide,\n    forceRender = props.forceRender,\n    align = props.align,\n    point = props.point,\n    getRootDomNode = props.getRootDomNode,\n    getClassNameFromAlign = props.getClassNameFromAlign,\n    onAlign = props.onAlign,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onMouseDown = props.onMouseDown,\n    onTouchStart = props.onTouchStart;\n  var alignRef = useRef();\n  var elementRef = useRef();\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    alignedClassName = _useState2[0],\n    setAlignedClassName = _useState2[1]; // ======================= Measure ========================\n\n  var _useStretchStyle = useStretchStyle(stretch),\n    _useStretchStyle2 = _slicedToArray(_useStretchStyle, 2),\n    stretchStyle = _useStretchStyle2[0],\n    measureStretchStyle = _useStretchStyle2[1];\n  function doMeasure() {\n    if (stretch) {\n      measureStretchStyle(getRootDomNode());\n    }\n  } // ======================== Status ========================\n\n  var _useVisibleStatus = useVisibleStatus(visible, doMeasure),\n    _useVisibleStatus2 = _slicedToArray(_useVisibleStatus, 2),\n    status = _useVisibleStatus2[0],\n    goNextStatus = _useVisibleStatus2[1]; // ======================== Aligns ========================\n\n  /**\n   * `alignedClassName` may modify `source` size,\n   * which means one time align may not move to the correct position at once.\n   *\n   * We will reset `alignTimes` for each status switch to `alignPre`\n   * and let `rc-align` to align for multiple times to ensure get final stable place.\n   * Currently we mark `alignTimes < 2` repeat align, it will increase if user report for align issue.\n   */\n\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    alignTimes = _useState4[0],\n    setAlignTimes = _useState4[1];\n  var prepareResolveRef = useRef();\n  useLayoutEffect(function () {\n    if (status === 'alignPre') {\n      setAlignTimes(0);\n    }\n  }, [status]); // `target` on `rc-align` can accept as a function to get the bind element or a point.\n  // ref: https://www.npmjs.com/package/rc-align\n\n  function getAlignTarget() {\n    if (point) {\n      return point;\n    }\n    return getRootDomNode;\n  }\n  function forceAlign() {\n    var _alignRef$current;\n    (_alignRef$current = alignRef.current) === null || _alignRef$current === void 0 ? void 0 : _alignRef$current.forceAlign();\n  }\n  function onInternalAlign(popupDomNode, matchAlign) {\n    var nextAlignedClassName = getClassNameFromAlign(matchAlign);\n    if (alignedClassName !== nextAlignedClassName) {\n      setAlignedClassName(nextAlignedClassName);\n    } // We will retry multi times to make sure that the element has been align in the right position.\n\n    setAlignTimes(function (val) {\n      return val + 1;\n    });\n    if (status === 'align') {\n      onAlign === null || onAlign === void 0 ? void 0 : onAlign(popupDomNode, matchAlign);\n    }\n  } // Delay to go to next status\n\n  useLayoutEffect(function () {\n    if (status === 'align') {\n      // Repeat until not more align needed\n      if (alignTimes < 2) {\n        forceAlign();\n      } else {\n        goNextStatus(function () {\n          var _prepareResolveRef$cu;\n          (_prepareResolveRef$cu = prepareResolveRef.current) === null || _prepareResolveRef$cu === void 0 ? void 0 : _prepareResolveRef$cu.call(prepareResolveRef);\n        });\n      }\n    }\n  }, [alignTimes]); // ======================== Motion ========================\n\n  var motion = _objectSpread({}, getMotion(props));\n  ['onAppearEnd', 'onEnterEnd', 'onLeaveEnd'].forEach(function (eventName) {\n    var originHandler = motion[eventName];\n    motion[eventName] = function (element, event) {\n      goNextStatus();\n      return originHandler === null || originHandler === void 0 ? void 0 : originHandler(element, event);\n    };\n  });\n  function onShowPrepare() {\n    return new Promise(function (resolve) {\n      prepareResolveRef.current = resolve;\n    });\n  } // Go to stable directly when motion not provided\n\n  React.useEffect(function () {\n    if (!motion.motionName && status === 'motion') {\n      goNextStatus();\n    }\n  }, [motion.motionName, status]); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: forceAlign,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread(_objectSpread({}, stretchStyle), {}, {\n    zIndex: zIndex,\n    opacity: status === 'motion' || status === 'stable' || !visible ? undefined : 0,\n    // Cannot interact with disappearing elements\n    // https://github.com/ant-design/ant-design/issues/35051#issuecomment-1101340714\n    pointerEvents: !visible && status !== 'stable' ? 'none' : undefined\n  }, style); // Align status\n\n  var alignDisabled = true;\n  if ((align === null || align === void 0 ? void 0 : align.points) && (status === 'align' || status === 'stable')) {\n    alignDisabled = false;\n  }\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n  }, motion, {\n    onAppearPrepare: onShowPrepare,\n    onEnterPrepare: onShowPrepare,\n    removeOnLeave: destroyPopupOnHide,\n    forceRender: forceRender\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, className, alignedClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(Align, {\n      target: getAlignTarget(),\n      key: \"popup\",\n      ref: alignRef,\n      monitorWindowResize: true,\n      disabled: alignDisabled,\n      align: align,\n      onAlign: onInternalAlign\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onMouseDownCapture: onMouseDown,\n      onTouchStartCapture: onTouchStart,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode));\n  });\n});\nPopupInner.displayName = 'PopupInner';\nexport default PopupInner;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "React", "useRef", "useState", "Align", "useLayoutEffect", "CSSMotion", "classNames", "useVisibleStatus", "getMotion", "useStretchStyle", "PopupInner", "forwardRef", "props", "ref", "visible", "prefixCls", "className", "style", "children", "zIndex", "stretch", "destroyPopupOnHide", "forceRender", "align", "point", "getRootDomNode", "getClassNameFromAlign", "onAlign", "onMouseEnter", "onMouseLeave", "onMouseDown", "onTouchStart", "alignRef", "elementRef", "_useState", "_useState2", "alignedClassName", "setAlignedClassName", "_useStretchStyle", "_useStretchStyle2", "stretchStyle", "measureStretchStyle", "doMeasure", "_useVisibleStatus", "_useVisibleStatus2", "status", "goNextStatus", "_useState3", "_useState4", "alignTimes", "setAlignTimes", "prepareResolveRef", "getAlignTarget", "forceAlign", "_alignRef$current", "current", "onInternalAlign", "popupDomNode", "matchAlign", "nextAlignedClassName", "val", "_prepareResolveRef$cu", "call", "motion", "for<PERSON>ach", "eventName", "<PERSON><PERSON><PERSON><PERSON>", "element", "event", "onShowPrepare", "Promise", "resolve", "useEffect", "motionName", "useImperativeHandle", "getElement", "mergedStyle", "opacity", "undefined", "pointerEvents", "alignDisabled", "points", "childNode", "Children", "count", "createElement", "concat", "leavedClassName", "onAppearPrepare", "onEnterPrepare", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "mergedClassName", "target", "key", "monitorWindowResize", "disabled", "onMouseDownCapture", "onTouchStartCapture", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-trigger/es/Popup/PopupInner.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport Align from 'rc-align';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport CSSMotion from 'rc-motion';\nimport classNames from 'classnames';\nimport useVisibleStatus from './useVisibleStatus';\nimport { getMotion } from '../utils/legacyUtil';\nimport useStretchStyle from './useStretchStyle';\nvar PopupInner = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var visible = props.visible,\n      prefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      children = props.children,\n      zIndex = props.zIndex,\n      stretch = props.stretch,\n      destroyPopupOnHide = props.destroyPopupOnHide,\n      forceRender = props.forceRender,\n      align = props.align,\n      point = props.point,\n      getRootDomNode = props.getRootDomNode,\n      getClassNameFromAlign = props.getClassNameFromAlign,\n      onAlign = props.onAlign,\n      onMouseEnter = props.onMouseEnter,\n      onMouseLeave = props.onMouseLeave,\n      onMouseDown = props.onMouseDown,\n      onTouchStart = props.onTouchStart;\n  var alignRef = useRef();\n  var elementRef = useRef();\n\n  var _useState = useState(),\n      _useState2 = _slicedToArray(_useState, 2),\n      alignedClassName = _useState2[0],\n      setAlignedClassName = _useState2[1]; // ======================= Measure ========================\n\n\n  var _useStretchStyle = useStretchStyle(stretch),\n      _useStretchStyle2 = _slicedToArray(_useStretchStyle, 2),\n      stretchStyle = _useStretchStyle2[0],\n      measureStretchStyle = _useStretchStyle2[1];\n\n  function doMeasure() {\n    if (stretch) {\n      measureStretchStyle(getRootDomNode());\n    }\n  } // ======================== Status ========================\n\n\n  var _useVisibleStatus = useVisibleStatus(visible, doMeasure),\n      _useVisibleStatus2 = _slicedToArray(_useVisibleStatus, 2),\n      status = _useVisibleStatus2[0],\n      goNextStatus = _useVisibleStatus2[1]; // ======================== Aligns ========================\n\n  /**\n   * `alignedClassName` may modify `source` size,\n   * which means one time align may not move to the correct position at once.\n   *\n   * We will reset `alignTimes` for each status switch to `alignPre`\n   * and let `rc-align` to align for multiple times to ensure get final stable place.\n   * Currently we mark `alignTimes < 2` repeat align, it will increase if user report for align issue.\n   */\n\n\n  var _useState3 = useState(0),\n      _useState4 = _slicedToArray(_useState3, 2),\n      alignTimes = _useState4[0],\n      setAlignTimes = _useState4[1];\n\n  var prepareResolveRef = useRef();\n  useLayoutEffect(function () {\n    if (status === 'alignPre') {\n      setAlignTimes(0);\n    }\n  }, [status]); // `target` on `rc-align` can accept as a function to get the bind element or a point.\n  // ref: https://www.npmjs.com/package/rc-align\n\n  function getAlignTarget() {\n    if (point) {\n      return point;\n    }\n\n    return getRootDomNode;\n  }\n\n  function forceAlign() {\n    var _alignRef$current;\n\n    (_alignRef$current = alignRef.current) === null || _alignRef$current === void 0 ? void 0 : _alignRef$current.forceAlign();\n  }\n\n  function onInternalAlign(popupDomNode, matchAlign) {\n    var nextAlignedClassName = getClassNameFromAlign(matchAlign);\n\n    if (alignedClassName !== nextAlignedClassName) {\n      setAlignedClassName(nextAlignedClassName);\n    } // We will retry multi times to make sure that the element has been align in the right position.\n\n\n    setAlignTimes(function (val) {\n      return val + 1;\n    });\n\n    if (status === 'align') {\n      onAlign === null || onAlign === void 0 ? void 0 : onAlign(popupDomNode, matchAlign);\n    }\n  } // Delay to go to next status\n\n\n  useLayoutEffect(function () {\n    if (status === 'align') {\n      // Repeat until not more align needed\n      if (alignTimes < 2) {\n        forceAlign();\n      } else {\n        goNextStatus(function () {\n          var _prepareResolveRef$cu;\n\n          (_prepareResolveRef$cu = prepareResolveRef.current) === null || _prepareResolveRef$cu === void 0 ? void 0 : _prepareResolveRef$cu.call(prepareResolveRef);\n        });\n      }\n    }\n  }, [alignTimes]); // ======================== Motion ========================\n\n  var motion = _objectSpread({}, getMotion(props));\n\n  ['onAppearEnd', 'onEnterEnd', 'onLeaveEnd'].forEach(function (eventName) {\n    var originHandler = motion[eventName];\n\n    motion[eventName] = function (element, event) {\n      goNextStatus();\n      return originHandler === null || originHandler === void 0 ? void 0 : originHandler(element, event);\n    };\n  });\n\n  function onShowPrepare() {\n    return new Promise(function (resolve) {\n      prepareResolveRef.current = resolve;\n    });\n  } // Go to stable directly when motion not provided\n\n\n  React.useEffect(function () {\n    if (!motion.motionName && status === 'motion') {\n      goNextStatus();\n    }\n  }, [motion.motionName, status]); // ========================= Refs =========================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: forceAlign,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  }); // ======================== Render ========================\n\n  var mergedStyle = _objectSpread(_objectSpread({}, stretchStyle), {}, {\n    zIndex: zIndex,\n    opacity: status === 'motion' || status === 'stable' || !visible ? undefined : 0,\n    // Cannot interact with disappearing elements\n    // https://github.com/ant-design/ant-design/issues/35051#issuecomment-1101340714\n    pointerEvents: !visible && status !== 'stable' ? 'none' : undefined\n  }, style); // Align status\n\n\n  var alignDisabled = true;\n\n  if ((align === null || align === void 0 ? void 0 : align.points) && (status === 'align' || status === 'stable')) {\n    alignDisabled = false;\n  }\n\n  var childNode = children; // Wrapper when multiple children\n\n  if (React.Children.count(children) > 1) {\n    childNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, children);\n  }\n\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: visible,\n    ref: elementRef,\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n  }, motion, {\n    onAppearPrepare: onShowPrepare,\n    onEnterPrepare: onShowPrepare,\n    removeOnLeave: destroyPopupOnHide,\n    forceRender: forceRender\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n    var mergedClassName = classNames(prefixCls, className, alignedClassName, motionClassName);\n    return /*#__PURE__*/React.createElement(Align, {\n      target: getAlignTarget(),\n      key: \"popup\",\n      ref: alignRef,\n      monitorWindowResize: true,\n      disabled: alignDisabled,\n      align: align,\n      onAlign: onInternalAlign\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      ref: motionRef,\n      className: mergedClassName,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onMouseDownCapture: onMouseDown,\n      onTouchStartCapture: onTouchStart,\n      style: _objectSpread(_objectSpread({}, motionStyle), mergedStyle)\n    }, childNode));\n  });\n});\nPopupInner.displayName = 'PopupInner';\nexport default PopupInner;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,kBAAkB,GAAGT,KAAK,CAACS,kBAAkB;IAC7CC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,KAAK,GAAGZ,KAAK,CAACY,KAAK;IACnBC,cAAc,GAAGb,KAAK,CAACa,cAAc;IACrCC,qBAAqB,GAAGd,KAAK,CAACc,qBAAqB;IACnDC,OAAO,GAAGf,KAAK,CAACe,OAAO;IACvBC,YAAY,GAAGhB,KAAK,CAACgB,YAAY;IACjCC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAC/BC,YAAY,GAAGnB,KAAK,CAACmB,YAAY;EACrC,IAAIC,QAAQ,GAAG/B,MAAM,CAAC,CAAC;EACvB,IAAIgC,UAAU,GAAGhC,MAAM,CAAC,CAAC;EAEzB,IAAIiC,SAAS,GAAGhC,QAAQ,CAAC,CAAC;IACtBiC,UAAU,GAAGpC,cAAc,CAACmC,SAAS,EAAE,CAAC,CAAC;IACzCE,gBAAgB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChCE,mBAAmB,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzC,IAAIG,gBAAgB,GAAG7B,eAAe,CAACW,OAAO,CAAC;IAC3CmB,iBAAiB,GAAGxC,cAAc,CAACuC,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAE9C,SAASG,SAASA,CAAA,EAAG;IACnB,IAAItB,OAAO,EAAE;MACXqB,mBAAmB,CAAChB,cAAc,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,CAAC;;EAGF,IAAIkB,iBAAiB,GAAGpC,gBAAgB,CAACO,OAAO,EAAE4B,SAAS,CAAC;IACxDE,kBAAkB,GAAG7C,cAAc,CAAC4C,iBAAiB,EAAE,CAAC,CAAC;IACzDE,MAAM,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IAC9BE,YAAY,GAAGF,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE1C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,IAAIG,UAAU,GAAG7C,QAAQ,CAAC,CAAC,CAAC;IACxB8C,UAAU,GAAGjD,cAAc,CAACgD,UAAU,EAAE,CAAC,CAAC;IAC1CE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEjC,IAAIG,iBAAiB,GAAGlD,MAAM,CAAC,CAAC;EAChCG,eAAe,CAAC,YAAY;IAC1B,IAAIyC,MAAM,KAAK,UAAU,EAAE;MACzBK,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;EACd;;EAEA,SAASO,cAAcA,CAAA,EAAG;IACxB,IAAI5B,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;IAEA,OAAOC,cAAc;EACvB;EAEA,SAAS4B,UAAUA,CAAA,EAAG;IACpB,IAAIC,iBAAiB;IAErB,CAACA,iBAAiB,GAAGtB,QAAQ,CAACuB,OAAO,MAAM,IAAI,IAAID,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACD,UAAU,CAAC,CAAC;EAC3H;EAEA,SAASG,eAAeA,CAACC,YAAY,EAAEC,UAAU,EAAE;IACjD,IAAIC,oBAAoB,GAAGjC,qBAAqB,CAACgC,UAAU,CAAC;IAE5D,IAAItB,gBAAgB,KAAKuB,oBAAoB,EAAE;MAC7CtB,mBAAmB,CAACsB,oBAAoB,CAAC;IAC3C,CAAC,CAAC;;IAGFT,aAAa,CAAC,UAAUU,GAAG,EAAE;MAC3B,OAAOA,GAAG,GAAG,CAAC;IAChB,CAAC,CAAC;IAEF,IAAIf,MAAM,KAAK,OAAO,EAAE;MACtBlB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC8B,YAAY,EAAEC,UAAU,CAAC;IACrF;EACF,CAAC,CAAC;;EAGFtD,eAAe,CAAC,YAAY;IAC1B,IAAIyC,MAAM,KAAK,OAAO,EAAE;MACtB;MACA,IAAII,UAAU,GAAG,CAAC,EAAE;QAClBI,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLP,YAAY,CAAC,YAAY;UACvB,IAAIe,qBAAqB;UAEzB,CAACA,qBAAqB,GAAGV,iBAAiB,CAACI,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,IAAI,CAACX,iBAAiB,CAAC;QAC3J,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,IAAIc,MAAM,GAAGjE,aAAa,CAAC,CAAC,CAAC,EAAEU,SAAS,CAACI,KAAK,CAAC,CAAC;EAEhD,CAAC,aAAa,EAAE,YAAY,EAAE,YAAY,CAAC,CAACoD,OAAO,CAAC,UAAUC,SAAS,EAAE;IACvE,IAAIC,aAAa,GAAGH,MAAM,CAACE,SAAS,CAAC;IAErCF,MAAM,CAACE,SAAS,CAAC,GAAG,UAAUE,OAAO,EAAEC,KAAK,EAAE;MAC5CtB,YAAY,CAAC,CAAC;MACd,OAAOoB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,OAAO,EAAEC,KAAK,CAAC;IACpG,CAAC;EACH,CAAC,CAAC;EAEF,SAASC,aAAaA,CAAA,EAAG;IACvB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpCpB,iBAAiB,CAACI,OAAO,GAAGgB,OAAO;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGFvE,KAAK,CAACwE,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACT,MAAM,CAACU,UAAU,IAAI5B,MAAM,KAAK,QAAQ,EAAE;MAC7CC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACiB,MAAM,CAACU,UAAU,EAAE5B,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEjC7C,KAAK,CAAC0E,mBAAmB,CAAC7D,GAAG,EAAE,YAAY;IACzC,OAAO;MACLwC,UAAU,EAAEA,UAAU;MACtBsB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAO1C,UAAU,CAACsB,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIqB,WAAW,GAAG9E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;IACnErB,MAAM,EAAEA,MAAM;IACd0D,OAAO,EAAEhC,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,QAAQ,IAAI,CAAC/B,OAAO,GAAGgE,SAAS,GAAG,CAAC;IAC/E;IACA;IACAC,aAAa,EAAE,CAACjE,OAAO,IAAI+B,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAGiC;EAC5D,CAAC,EAAE7D,KAAK,CAAC,CAAC,CAAC;;EAGX,IAAI+D,aAAa,GAAG,IAAI;EAExB,IAAI,CAACzD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC0D,MAAM,MAAMpC,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;IAC/GmC,aAAa,GAAG,KAAK;EACvB;EAEA,IAAIE,SAAS,GAAGhE,QAAQ,CAAC,CAAC;;EAE1B,IAAIlB,KAAK,CAACmF,QAAQ,CAACC,KAAK,CAAClE,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtCgE,SAAS,GAAG,aAAalF,KAAK,CAACqF,aAAa,CAAC,KAAK,EAAE;MAClDrE,SAAS,EAAE,EAAE,CAACsE,MAAM,CAACvE,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAEG,QAAQ,CAAC;EACd;EAEA,OAAO,aAAalB,KAAK,CAACqF,aAAa,CAAChF,SAAS,EAAER,QAAQ,CAAC;IAC1DiB,OAAO,EAAEA,OAAO;IAChBD,GAAG,EAAEoB,UAAU;IACfsD,eAAe,EAAE,EAAE,CAACD,MAAM,CAACvE,SAAS,EAAE,SAAS;EACjD,CAAC,EAAEgD,MAAM,EAAE;IACTyB,eAAe,EAAEnB,aAAa;IAC9BoB,cAAc,EAAEpB,aAAa;IAC7BqB,aAAa,EAAErE,kBAAkB;IACjCC,WAAW,EAAEA;EACf,CAAC,CAAC,EAAE,UAAUqE,IAAI,EAAEC,SAAS,EAAE;IAC7B,IAAIC,eAAe,GAAGF,IAAI,CAAC3E,SAAS;MAChC8E,WAAW,GAAGH,IAAI,CAAC1E,KAAK;IAC5B,IAAI8E,eAAe,GAAGzF,UAAU,CAACS,SAAS,EAAEC,SAAS,EAAEoB,gBAAgB,EAAEyD,eAAe,CAAC;IACzF,OAAO,aAAa7F,KAAK,CAACqF,aAAa,CAAClF,KAAK,EAAE;MAC7C6F,MAAM,EAAE5C,cAAc,CAAC,CAAC;MACxB6C,GAAG,EAAE,OAAO;MACZpF,GAAG,EAAEmB,QAAQ;MACbkE,mBAAmB,EAAE,IAAI;MACzBC,QAAQ,EAAEnB,aAAa;MACvBzD,KAAK,EAAEA,KAAK;MACZI,OAAO,EAAE6B;IACX,CAAC,EAAE,aAAaxD,KAAK,CAACqF,aAAa,CAAC,KAAK,EAAE;MACzCxE,GAAG,EAAE+E,SAAS;MACd5E,SAAS,EAAE+E,eAAe;MAC1BnE,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BuE,kBAAkB,EAAEtE,WAAW;MAC/BuE,mBAAmB,EAAEtE,YAAY;MACjCd,KAAK,EAAEnB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgG,WAAW,CAAC,EAAElB,WAAW;IAClE,CAAC,EAAEM,SAAS,CAAC,CAAC;EAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AACFxE,UAAU,CAAC4F,WAAW,GAAG,YAAY;AACrC,eAAe5F,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}