{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useState } from 'react';\nexport var DEFAULT_PAGE_SIZE = 10;\nexport function getPaginationParam(pagination, mergedPagination) {\n  var param = {\n    current: mergedPagination.current,\n    pageSize: mergedPagination.pageSize\n  };\n  var paginationObj = pagination && _typeof(pagination) === 'object' ? pagination : {};\n  Object.keys(paginationObj).forEach(function (pageProp) {\n    var value = mergedPagination[pageProp];\n    if (typeof value !== 'function') {\n      param[pageProp] = value;\n    }\n  });\n  return param;\n}\nfunction extendsObject() {\n  var result = {};\n  for (var _len = arguments.length, list = new Array(_len), _key = 0; _key < _len; _key++) {\n    list[_key] = arguments[_key];\n  }\n  list.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          result[key] = val;\n        }\n      });\n    }\n  });\n  return result;\n}\nexport default function usePagination(total, pagination, onChange) {\n  var _a = pagination && _typeof(pagination) === 'object' ? pagination : {},\n    _a$total = _a.total,\n    paginationTotal = _a$total === void 0 ? 0 : _a$total,\n    paginationObj = __rest(_a, [\"total\"]);\n  var _useState = useState(function () {\n      return {\n        current: 'defaultCurrent' in paginationObj ? paginationObj.defaultCurrent : 1,\n        pageSize: 'defaultPageSize' in paginationObj ? paginationObj.defaultPageSize : DEFAULT_PAGE_SIZE\n      };\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    innerPagination = _useState2[0],\n    setInnerPagination = _useState2[1]; // ============ Basic Pagination Config ============\n\n  var mergedPagination = extendsObject(innerPagination, paginationObj, {\n    total: paginationTotal > 0 ? paginationTotal : total\n  }); // Reset `current` if data length or pageSize changed\n\n  var maxPage = Math.ceil((paginationTotal || total) / mergedPagination.pageSize);\n  if (mergedPagination.current > maxPage) {\n    // Prevent a maximum page count of 0\n    mergedPagination.current = maxPage || 1;\n  }\n  var refreshPagination = function refreshPagination(current, pageSize) {\n    setInnerPagination({\n      current: current !== null && current !== void 0 ? current : 1,\n      pageSize: pageSize || mergedPagination.pageSize\n    });\n  };\n  var onInternalChange = function onInternalChange(current, pageSize) {\n    var _a;\n    if (pagination) {\n      (_a = pagination.onChange) === null || _a === void 0 ? void 0 : _a.call(pagination, current, pageSize);\n    }\n    refreshPagination(current, pageSize);\n    onChange(current, pageSize || (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize));\n  };\n  if (pagination === false) {\n    return [{}, function () {}];\n  }\n  return [_extends(_extends({}, mergedPagination), {\n    onChange: onInternalChange\n  }), refreshPagination];\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "useState", "DEFAULT_PAGE_SIZE", "getPaginationParam", "pagination", "mergedPagination", "param", "current", "pageSize", "paginationObj", "keys", "for<PERSON>ach", "pageProp", "value", "extendsObject", "result", "_len", "arguments", "list", "Array", "_key", "obj", "key", "val", "undefined", "usePagination", "total", "onChange", "_a", "_a$total", "paginationTotal", "_useState", "defaultCurrent", "defaultPageSize", "_useState2", "innerPagination", "setInnerPagination", "maxPage", "Math", "ceil", "refreshPagination", "onInternalChange"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/hooks/usePagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport { useState } from 'react';\nexport var DEFAULT_PAGE_SIZE = 10;\nexport function getPaginationParam(pagination, mergedPagination) {\n  var param = {\n    current: mergedPagination.current,\n    pageSize: mergedPagination.pageSize\n  };\n  var paginationObj = pagination && _typeof(pagination) === 'object' ? pagination : {};\n  Object.keys(paginationObj).forEach(function (pageProp) {\n    var value = mergedPagination[pageProp];\n\n    if (typeof value !== 'function') {\n      param[pageProp] = value;\n    }\n  });\n  return param;\n}\n\nfunction extendsObject() {\n  var result = {};\n\n  for (var _len = arguments.length, list = new Array(_len), _key = 0; _key < _len; _key++) {\n    list[_key] = arguments[_key];\n  }\n\n  list.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n\n        if (val !== undefined) {\n          result[key] = val;\n        }\n      });\n    }\n  });\n  return result;\n}\n\nexport default function usePagination(total, pagination, onChange) {\n  var _a = pagination && _typeof(pagination) === 'object' ? pagination : {},\n      _a$total = _a.total,\n      paginationTotal = _a$total === void 0 ? 0 : _a$total,\n      paginationObj = __rest(_a, [\"total\"]);\n\n  var _useState = useState(function () {\n    return {\n      current: 'defaultCurrent' in paginationObj ? paginationObj.defaultCurrent : 1,\n      pageSize: 'defaultPageSize' in paginationObj ? paginationObj.defaultPageSize : DEFAULT_PAGE_SIZE\n    };\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      innerPagination = _useState2[0],\n      setInnerPagination = _useState2[1]; // ============ Basic Pagination Config ============\n\n\n  var mergedPagination = extendsObject(innerPagination, paginationObj, {\n    total: paginationTotal > 0 ? paginationTotal : total\n  }); // Reset `current` if data length or pageSize changed\n\n  var maxPage = Math.ceil((paginationTotal || total) / mergedPagination.pageSize);\n\n  if (mergedPagination.current > maxPage) {\n    // Prevent a maximum page count of 0\n    mergedPagination.current = maxPage || 1;\n  }\n\n  var refreshPagination = function refreshPagination(current, pageSize) {\n    setInnerPagination({\n      current: current !== null && current !== void 0 ? current : 1,\n      pageSize: pageSize || mergedPagination.pageSize\n    });\n  };\n\n  var onInternalChange = function onInternalChange(current, pageSize) {\n    var _a;\n\n    if (pagination) {\n      (_a = pagination.onChange) === null || _a === void 0 ? void 0 : _a.call(pagination, current, pageSize);\n    }\n\n    refreshPagination(current, pageSize);\n    onChange(current, pageSize || (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize));\n  };\n\n  if (pagination === false) {\n    return [{}, function () {}];\n  }\n\n  return [_extends(_extends({}, mergedPagination), {\n    onChange: onInternalChange\n  }), refreshPagination];\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AAEvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,SAASW,QAAQ,QAAQ,OAAO;AAChC,OAAO,IAAIC,iBAAiB,GAAG,EAAE;AACjC,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAEC,gBAAgB,EAAE;EAC/D,IAAIC,KAAK,GAAG;IACVC,OAAO,EAAEF,gBAAgB,CAACE,OAAO;IACjCC,QAAQ,EAAEH,gBAAgB,CAACG;EAC7B,CAAC;EACD,IAAIC,aAAa,GAAGL,UAAU,IAAIlB,OAAO,CAACkB,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;EACpFZ,MAAM,CAACkB,IAAI,CAACD,aAAa,CAAC,CAACE,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACrD,IAAIC,KAAK,GAAGR,gBAAgB,CAACO,QAAQ,CAAC;IAEtC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE;MAC/BP,KAAK,CAACM,QAAQ,CAAC,GAAGC,KAAK;IACzB;EACF,CAAC,CAAC;EACF,OAAOP,KAAK;AACd;AAEA,SAASQ,aAAaA,CAAA,EAAG;EACvB,IAAIC,MAAM,GAAG,CAAC,CAAC;EAEf,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAAClB,MAAM,EAAEmB,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;EAC9B;EAEAF,IAAI,CAACP,OAAO,CAAC,UAAUU,GAAG,EAAE;IAC1B,IAAIA,GAAG,EAAE;MACP7B,MAAM,CAACkB,IAAI,CAACW,GAAG,CAAC,CAACV,OAAO,CAAC,UAAUW,GAAG,EAAE;QACtC,IAAIC,GAAG,GAAGF,GAAG,CAACC,GAAG,CAAC;QAElB,IAAIC,GAAG,KAAKC,SAAS,EAAE;UACrBT,MAAM,CAACO,GAAG,CAAC,GAAGC,GAAG;QACnB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AAEA,eAAe,SAASU,aAAaA,CAACC,KAAK,EAAEtB,UAAU,EAAEuB,QAAQ,EAAE;EACjE,IAAIC,EAAE,GAAGxB,UAAU,IAAIlB,OAAO,CAACkB,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;IACrEyB,QAAQ,GAAGD,EAAE,CAACF,KAAK;IACnBI,eAAe,GAAGD,QAAQ,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,QAAQ;IACpDpB,aAAa,GAAGtB,MAAM,CAACyC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;EAEzC,IAAIG,SAAS,GAAG9B,QAAQ,CAAC,YAAY;MACnC,OAAO;QACLM,OAAO,EAAE,gBAAgB,IAAIE,aAAa,GAAGA,aAAa,CAACuB,cAAc,GAAG,CAAC;QAC7ExB,QAAQ,EAAE,iBAAiB,IAAIC,aAAa,GAAGA,aAAa,CAACwB,eAAe,GAAG/B;MACjF,CAAC;IACH,CAAC,CAAC;IACEgC,UAAU,GAAGjD,cAAc,CAAC8C,SAAS,EAAE,CAAC,CAAC;IACzCI,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGxC,IAAI7B,gBAAgB,GAAGS,aAAa,CAACqB,eAAe,EAAE1B,aAAa,EAAE;IACnEiB,KAAK,EAAEI,eAAe,GAAG,CAAC,GAAGA,eAAe,GAAGJ;EACjD,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIW,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACT,eAAe,IAAIJ,KAAK,IAAIrB,gBAAgB,CAACG,QAAQ,CAAC;EAE/E,IAAIH,gBAAgB,CAACE,OAAO,GAAG8B,OAAO,EAAE;IACtC;IACAhC,gBAAgB,CAACE,OAAO,GAAG8B,OAAO,IAAI,CAAC;EACzC;EAEA,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACjC,OAAO,EAAEC,QAAQ,EAAE;IACpE4B,kBAAkB,CAAC;MACjB7B,OAAO,EAAEA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC;MAC7DC,QAAQ,EAAEA,QAAQ,IAAIH,gBAAgB,CAACG;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,IAAIiC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClC,OAAO,EAAEC,QAAQ,EAAE;IAClE,IAAIoB,EAAE;IAEN,IAAIxB,UAAU,EAAE;MACd,CAACwB,EAAE,GAAGxB,UAAU,CAACuB,QAAQ,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,IAAI,CAACS,UAAU,EAAEG,OAAO,EAAEC,QAAQ,CAAC;IACxG;IAEAgC,iBAAiB,CAACjC,OAAO,EAAEC,QAAQ,CAAC;IACpCmB,QAAQ,CAACpB,OAAO,EAAEC,QAAQ,KAAKH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG,QAAQ,CAAC,CAAC;EAChI,CAAC;EAED,IAAIJ,UAAU,KAAK,KAAK,EAAE;IACxB,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;EAC7B;EAEA,OAAO,CAACpB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqB,gBAAgB,CAAC,EAAE;IAC/CsB,QAAQ,EAAEc;EACZ,CAAC,CAAC,EAAED,iBAAiB,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}