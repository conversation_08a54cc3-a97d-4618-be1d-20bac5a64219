{"ast": null, "code": "export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nexport default function getScroll(target, top) {\n  var _a;\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n  var method = top ? 'scrollTop' : 'scrollLeft';\n  var result = 0;\n  if (isWindow(target)) {\n    result = target[top ? 'pageYOffset' : 'pageXOffset'];\n  } else if (target instanceof Document) {\n    result = target.documentElement[method];\n  } else if (target) {\n    result = target[method];\n  }\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_a = (target.ownerDocument || target).documentElement) === null || _a === void 0 ? void 0 : _a[method];\n  }\n  return result;\n}", "map": {"version": 3, "names": ["isWindow", "obj", "undefined", "window", "getScroll", "target", "top", "_a", "method", "result", "Document", "documentElement", "ownerDocument"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/getScroll.js"], "sourcesContent": ["export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nexport default function getScroll(target, top) {\n  var _a;\n\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n\n  var method = top ? 'scrollTop' : 'scrollLeft';\n  var result = 0;\n\n  if (isWindow(target)) {\n    result = target[top ? 'pageYOffset' : 'pageXOffset'];\n  } else if (target instanceof Document) {\n    result = target.documentElement[method];\n  } else if (target) {\n    result = target[method];\n  }\n\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_a = (target.ownerDocument || target).documentElement) === null || _a === void 0 ? void 0 : _a[method];\n  }\n\n  return result;\n}"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAKA,GAAG,CAACE,MAAM;AAChE;AACA,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC7C,IAAIC,EAAE;EAEN,IAAI,OAAOJ,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,CAAC;EACV;EAEA,IAAIK,MAAM,GAAGF,GAAG,GAAG,WAAW,GAAG,YAAY;EAC7C,IAAIG,MAAM,GAAG,CAAC;EAEd,IAAIT,QAAQ,CAACK,MAAM,CAAC,EAAE;IACpBI,MAAM,GAAGJ,MAAM,CAACC,GAAG,GAAG,aAAa,GAAG,aAAa,CAAC;EACtD,CAAC,MAAM,IAAID,MAAM,YAAYK,QAAQ,EAAE;IACrCD,MAAM,GAAGJ,MAAM,CAACM,eAAe,CAACH,MAAM,CAAC;EACzC,CAAC,MAAM,IAAIH,MAAM,EAAE;IACjBI,MAAM,GAAGJ,MAAM,CAACG,MAAM,CAAC;EACzB;EAEA,IAAIH,MAAM,IAAI,CAACL,QAAQ,CAACK,MAAM,CAAC,IAAI,OAAOI,MAAM,KAAK,QAAQ,EAAE;IAC7DA,MAAM,GAAG,CAACF,EAAE,GAAG,CAACF,MAAM,CAACO,aAAa,IAAIP,MAAM,EAAEM,eAAe,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,MAAM,CAAC;EAClH;EAEA,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}