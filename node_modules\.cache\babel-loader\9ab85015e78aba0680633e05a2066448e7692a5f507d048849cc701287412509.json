{"ast": null, "code": "import _regeneratorRuntime from \"@babel/runtime/regenerator\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useState from \"rc-util/es/hooks/useState\";\nvar StatusQueue = ['measure', 'alignPre', 'align', null, 'motion'];\nexport default (function (visible, doMeasure) {\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    status = _useState2[0],\n    setInternalStatus = _useState2[1];\n  var rafRef = useRef();\n  function setStatus(nextStatus) {\n    setInternalStatus(nextStatus, true);\n  }\n  function cancelRaf() {\n    raf.cancel(rafRef.current);\n  }\n  function goNextStatus(callback) {\n    cancelRaf();\n    rafRef.current = raf(function () {\n      // Only align should be manually trigger\n      setStatus(function (prev) {\n        switch (status) {\n          case 'align':\n            return 'motion';\n          case 'motion':\n            return 'stable';\n          default:\n        }\n        return prev;\n      });\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  } // Init status\n\n  useEffect(function () {\n    setStatus('measure');\n  }, [visible]); // Go next status\n\n  useEffect(function () {\n    switch (status) {\n      case 'measure':\n        doMeasure();\n        break;\n      default:\n    }\n    if (status) {\n      rafRef.current = raf(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var index, nextStatus;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                index = StatusQueue.indexOf(status);\n                nextStatus = StatusQueue[index + 1];\n                if (nextStatus && index !== -1) {\n                  setStatus(nextStatus);\n                }\n              case 3:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      })));\n    }\n  }, [status]);\n  useEffect(function () {\n    return function () {\n      cancelRaf();\n    };\n  }, []);\n  return [status, goNextStatus];\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "_asyncToGenerator", "_slicedToArray", "useEffect", "useRef", "raf", "useState", "StatusQueue", "visible", "doMeasure", "_useState", "_useState2", "status", "setInternalStatus", "rafRef", "setStatus", "nextStatus", "cancelRaf", "cancel", "current", "goNextStatus", "callback", "prev", "mark", "_callee", "index", "wrap", "_callee$", "_context", "next", "indexOf", "stop"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-trigger/es/Popup/useVisibleStatus.js"], "sourcesContent": ["import _regeneratorRuntime from \"@babel/runtime/regenerator\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport useState from \"rc-util/es/hooks/useState\";\nvar StatusQueue = ['measure', 'alignPre', 'align', null, 'motion'];\nexport default (function (visible, doMeasure) {\n  var _useState = useState(null),\n      _useState2 = _slicedToArray(_useState, 2),\n      status = _useState2[0],\n      setInternalStatus = _useState2[1];\n\n  var rafRef = useRef();\n\n  function setStatus(nextStatus) {\n    setInternalStatus(nextStatus, true);\n  }\n\n  function cancelRaf() {\n    raf.cancel(rafRef.current);\n  }\n\n  function goNextStatus(callback) {\n    cancelRaf();\n    rafRef.current = raf(function () {\n      // Only align should be manually trigger\n      setStatus(function (prev) {\n        switch (status) {\n          case 'align':\n            return 'motion';\n\n          case 'motion':\n            return 'stable';\n\n          default:\n        }\n\n        return prev;\n      });\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  } // Init status\n\n\n  useEffect(function () {\n    setStatus('measure');\n  }, [visible]); // Go next status\n\n  useEffect(function () {\n    switch (status) {\n      case 'measure':\n        doMeasure();\n        break;\n\n      default:\n    }\n\n    if (status) {\n      rafRef.current = raf( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var index, nextStatus;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                index = StatusQueue.indexOf(status);\n                nextStatus = StatusQueue[index + 1];\n\n                if (nextStatus && index !== -1) {\n                  setStatus(nextStatus);\n                }\n\n              case 3:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee);\n      })));\n    }\n  }, [status]);\n  useEffect(function () {\n    return function () {\n      cancelRaf();\n    };\n  }, []);\n  return [status, goNextStatus];\n});"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,4BAA4B;AAC5D,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,IAAIC,WAAW,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC;AAClE,gBAAgB,UAAUC,OAAO,EAAEC,SAAS,EAAE;EAC5C,IAAIC,SAAS,GAAGJ,QAAQ,CAAC,IAAI,CAAC;IAC1BK,UAAU,GAAGT,cAAc,CAACQ,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,iBAAiB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAErC,IAAIG,MAAM,GAAGV,MAAM,CAAC,CAAC;EAErB,SAASW,SAASA,CAACC,UAAU,EAAE;IAC7BH,iBAAiB,CAACG,UAAU,EAAE,IAAI,CAAC;EACrC;EAEA,SAASC,SAASA,CAAA,EAAG;IACnBZ,GAAG,CAACa,MAAM,CAACJ,MAAM,CAACK,OAAO,CAAC;EAC5B;EAEA,SAASC,YAAYA,CAACC,QAAQ,EAAE;IAC9BJ,SAAS,CAAC,CAAC;IACXH,MAAM,CAACK,OAAO,GAAGd,GAAG,CAAC,YAAY;MAC/B;MACAU,SAAS,CAAC,UAAUO,IAAI,EAAE;QACxB,QAAQV,MAAM;UACZ,KAAK,OAAO;YACV,OAAO,QAAQ;UAEjB,KAAK,QAAQ;YACX,OAAO,QAAQ;UAEjB;QACF;QAEA,OAAOU,IAAI;MACb,CAAC,CAAC;MACFD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGFlB,SAAS,CAAC,YAAY;IACpBY,SAAS,CAAC,SAAS,CAAC;EACtB,CAAC,EAAE,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEfL,SAAS,CAAC,YAAY;IACpB,QAAQS,MAAM;MACZ,KAAK,SAAS;QACZH,SAAS,CAAC,CAAC;QACX;MAEF;IACF;IAEA,IAAIG,MAAM,EAAE;MACVE,MAAM,CAACK,OAAO,GAAGd,GAAG,CAAE,aAAaJ,iBAAiB,CAAE,aAAaD,mBAAmB,CAACuB,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;QAC7G,IAAIC,KAAK,EAAET,UAAU;QACrB,OAAOhB,mBAAmB,CAAC0B,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;UAC1D,OAAO,CAAC,EAAE;YACR,QAAQA,QAAQ,CAACN,IAAI,GAAGM,QAAQ,CAACC,IAAI;cACnC,KAAK,CAAC;gBACJJ,KAAK,GAAGlB,WAAW,CAACuB,OAAO,CAAClB,MAAM,CAAC;gBACnCI,UAAU,GAAGT,WAAW,CAACkB,KAAK,GAAG,CAAC,CAAC;gBAEnC,IAAIT,UAAU,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;kBAC9BV,SAAS,CAACC,UAAU,CAAC;gBACvB;cAEF,KAAK,CAAC;cACN,KAAK,KAAK;gBACR,OAAOY,QAAQ,CAACG,IAAI,CAAC,CAAC;YAC1B;UACF;QACF,CAAC,EAAEP,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACZT,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBc,SAAS,CAAC,CAAC;IACb,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACL,MAAM,EAAEQ,YAAY,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}