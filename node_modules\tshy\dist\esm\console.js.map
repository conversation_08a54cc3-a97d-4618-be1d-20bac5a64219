{"version": 3, "file": "console.js", "sourceRoot": "", "sources": ["../../src/console.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAE7D,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,CAAA;AAEvD,MAAM,MAAM,GAAY,EAAE,CAAA;AAC1B,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAQ,EAAE,EAAE;IACnC,IAAI,OAAO,IAAI,CAAC;QAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;;QAChC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACrB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAQ,EAAE,EAAE;IACnC,IAAI,OAAO,IAAI,CAAC;QAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;;QAChC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACrB,CAAC,CAAA;AAED,yCAAyC;AACzC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAQ,EAAE,EAAE;IACjC,IAAI,OAAO,IAAI,CAAC;QAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACrC,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE;IACxB,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;IACrB,CAAC;IACD,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;AACnB,CAAC,CAAA", "sourcesContent": ["// only print the logs if it fails, or if TSHY_VERBOSE is set\n\nlet verbose = parseInt(process.env.TSHY_VERBOSE || '0')\n\nconst errors: any[][] = []\nexport const error = (...a: any[]) => {\n  if (verbose >= 1) console.error(...a)\n  else errors.push(a)\n}\nexport const debug = (...a: any[]) => {\n  if (verbose >= 2) console.error(...a)\n  else errors.push(a)\n}\n\n// we only print stdout on success anyway\nexport const log = (...a: any[]) => {\n  if (verbose >= 1) console.log(...a)\n}\n\nexport const print = () => {\n  for (const a of errors) {\n    console.error(...a)\n  }\n  errors.length = 0\n}\n"]}