{"version": 3, "file": "prevent-verbatim-module-syntax.js", "sourceRoot": "", "sources": ["../../src/prevent-verbatim-module-syntax.ts"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,0DAA0D;AAC1D,uCAAuC;AACvC,4DAA4D;AAC5D,aAAa;AACb,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,IAAI,MAAM,WAAW,CAAA;AAC5B,OAAO,oBAAoB,MAAM,6BAA6B,CAAA;AAE9D,eAAe,GAAG,EAAE;IAClB,MAAM,MAAM,GAAG,oBAAoB,EAAE,CAAA;IACrC,IAAI,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;QACxC,IAAI,CAAC,+BAA+B,CAAC,CAAA;QACrC,OAAO,CAAC,KAAK,CACX;;;;;;;;;;;;;;;;;CAiBL,CACI,CAAA;QACD,OAAO,CAAC,KAAK,EAAE,CAAA;QACf,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;AACH,CAAC,CAAA", "sourcesContent": ["// prevent the use of verbatimModuleSyntax: true when\n// more than one dialect is in use, since this cannot ever\n// be made to work in a hybrid context.\n// Note: cannot just use JSON.parse, because ts config files\n// are jsonc.\nimport * as console from './console.js'\nimport fail from './fail.js'\nimport readTypescriptConfig from './read-typescript-config.js'\n\nexport default () => {\n  const config = readTypescriptConfig()\n  if (config.options.verbatimModuleSyntax) {\n    fail('verbatimModuleSyntax detected')\n    console.error(\n      `verbatimModuleSyntax is incompatible with multi-dialect builds. Either remove\nthis field from tsconfig.json, or set a single dialect in the \"dialects\"\nfield in package.json, for example:\n\n{\n  \"tshy\": {\n    \"dialects\": [\"esm\"]\n  }\n}\n\nor\n\n{\n  \"tshy\": {\n    \"dialects\": [\"commonjs\"]\n  }\n}\n`,\n    )\n    console.print()\n    process.exit(1)\n  }\n}\n"]}