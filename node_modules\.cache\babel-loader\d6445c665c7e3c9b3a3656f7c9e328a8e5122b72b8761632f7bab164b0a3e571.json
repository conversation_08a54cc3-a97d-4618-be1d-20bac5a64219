{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CradleLoader = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar CradleLoader = function CradleLoader(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    \"aria-label\": props.label,\n    role: \"presentation\",\n    className: \"container\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing-l\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing-r\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow-l\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow-r\"\n  })));\n};\nexports.CradleLoader = CradleLoader;\nCradleLoader.propTypes = {\n  label: _propTypes[\"default\"].string\n};\nCradleLoader.defaultProps = {\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "label", "role", "className", "propTypes", "string", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/CradleLoader.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CradleLoader = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar CradleLoader = function CradleLoader(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    \"aria-label\": props.label,\n    role: \"presentation\",\n    className: \"container\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing-l\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-swing-r\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow-l\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", null), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    className: \"react-spinner-loader-shadow-r\"\n  })));\n};\n\nexports.CradleLoader = CradleLoader;\nCradleLoader.propTypes = {\n  label: _propTypes[\"default\"].string\n};\nCradleLoader.defaultProps = {\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,KAAK,EAAE;EAC9C,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzD,YAAY,EAAED,KAAK,CAACE,KAAK;IACzBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;EACb,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACrDG,SAAS,EAAE;EACb,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACrDG,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7VG,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACvDG,SAAS,EAAE;EACb,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACrDG,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7VG,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAEDb,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCA,YAAY,CAACY,SAAS,GAAG;EACvBH,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACS;AAC/B,CAAC;AACDb,YAAY,CAACc,YAAY,GAAG;EAC1BL,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}