{"version": 3, "file": "result.js", "sourceRoot": "", "sources": ["../../src/result.ts"], "names": [], "mappings": ";;;AAAA,2DAAoD;AAEpD,6DAAgE;AAYhE;;GAEG;AACH,MAAa,MAAM;IACV,EAAE,CAAS;IACX,IAAI,GAAW,EAAE,CAAA;IACjB,EAAE,GAAW,CAAC,CAAA;IACd,QAAQ,GAAY,KAAK,CAAA;IACzB,QAAQ,GAAkB,IAAI,CAAA;IAC9B,IAAI,GAAqB,KAAK,CAAA;IAC9B,IAAI,GAAqB,KAAK,CAAA;IAC9B,QAAQ,GAAkB,IAAI,CAAA;IAC9B,IAAI,GAAgB,IAAI,CAAA;IACxB,IAAI,GAAe,IAAI,CAAA;IACvB,IAAI,GAAkB,IAAI,CAAA;IAC1B,QAAQ,GAAW,EAAE,CAAA;IACrB,gBAAgB,GAAY,KAAK,CAAA;IAExC,YAAY,MAAgB,EAAE,MAAc;QAC1C,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5B,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;QACxB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,MAAM,CAAC,CAAC,CAAC;YAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QAE3B,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QAC1B,IAAI,IAAY,CAAA;QAChB,6DAA6D;QAC7D,4CAA4C;QAC5C,6CAA6C;QAC7C,kDAAkD;QAClD,mDAAmD;QACnD,sDAAsD;QACtD,qDAAqD;QACrD,2DAA2D;QAE3D,kBAAkB;QAClB,6CAA6C;QAC7C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QACpC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAClD,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAC1D,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAE5D,kDAAkD;QAClD,MAAM,GAAG,GAAG,IAAA,mCAAc,EAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG;YAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAA;aAChD,CAAC;YACJ,wDAAwD;YACxD,4BAA4B;YAC5B,MAAM,MAAM,GAAc,GAAG,CAAC,CAAC,CAAC,CAAA;YAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACvB,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;oBACtB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAA;oBAChC,qBAAqB;gBACvB,CAAC;gBACD,oBAAoB;YACtB,CAAC;QACH,CAAC;QAED,IAAI,kCAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kCAAc,EAAE,EAAE,CAAC,CAAA;YACvC,QAAQ,GAAG,GAAG,CAAA;QAChB,CAAC;QAED,IAAI,QAAQ,KAAK,GAAG;YAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QAE1C,IAAI,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;QACjC,MAAM,CAAC,GAAa,EAAE,CAAA;QACtB,IAAI,MAAM,CAAC,QAAQ;YAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,IAAI,CAAC,IAAI;YAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;IACtC,CAAC;CACF;AAxED,wBAwEC", "sourcesContent": ["import { OPEN_BRACE_EOL } from './brace-patterns.js'\nimport { Parser } from './index.js'\nimport { Directive, parseDirective } from './parse-directive.js'\nimport { Plan } from './plan.js'\n\n/**\n * An indication that a violation of the TAP specification has occurred\n *\n * This can indicate a test point that exceeds the plan, a test point\n * encountered after a trailing plan, or in the case of `pragma +strict`,\n * any non-TAP data.\n */\nexport type TapError = Result | { tapError: string; [k: string]: any }\n\n/**\n * A representation of a TestPoint result, with diagnostics if present.\n */\nexport class Result {\n  public ok: boolean\n  public name: string = ''\n  public id: number = 0\n  public buffered: boolean = false\n  public tapError: string | null = null\n  public skip: boolean | string = false\n  public todo: boolean | string = false\n  public previous: Result | null = null\n  public plan: Plan | null = null\n  public diag: any | null = null\n  public time: number | null = null\n  public fullname: string = ''\n  public closingTestPoint: boolean = false\n\n  constructor(parsed: string[], parser: Parser) {\n    const ok = !parsed[1]\n    const id = +(parsed[2] || 0)\n    let buffered = parsed[4]\n    this.ok = ok\n    if (parsed[2]) this.id = id\n\n    let rest = parsed[3] || ''\n    let name: string\n    // We know at this point the parsed result cannot contain \\n,\n    // so we can leverage that as a placeholder.\n    // first, replace any PAIR of \\ chars with \\n\n    // then, split on any # that is not preceeded by \\\n    // the first of these is definitely the description\n    // the rest is the directive, if recognized, otherwise\n    // we just lump it onto the description, but escaped.\n    // then any \\n chars in either are turned into \\ (just one)\n\n    // escape \\ with \\\n    // swap out escaped \\ with \\n, then swap back\n    rest = rest.replace(/(\\\\\\\\)/g, '\\n')\n    const [h, ...r] = rest.split(/(?<=\\s|^)(?<!\\\\)#/g)\n    name = (h || '').replace(/\\\\#/g, '#').replace(/\\n/g, '\\\\')\n    rest = r.join('#').replace(/\\\\#/g, '#').replace(/\\n/g, '\\\\')\n\n    // now, let's see if there's a directive in there.\n    const dir = parseDirective(rest.trim())\n    if (!dir) name += (rest ? '#' + rest : '') + buffered\n    else {\n      // handle buffered subtests with todo/skip on them, like\n      // ok 1 - bar # todo foo {\\n\n      const dirKey: Directive = dir[0]\n      const dirValue = dir[1]\n      if (dirKey === 'todo' || dirKey === 'skip') {\n        this[dirKey] = dirValue\n      } else {\n        if (dirKey === 'time') {\n          this.time = parseFloat(dirValue)\n          /* c8 ignore start */\n        }\n        /* c8 ignore stop */\n      }\n    }\n\n    if (OPEN_BRACE_EOL.test(name)) {\n      name = name.replace(OPEN_BRACE_EOL, '')\n      buffered = '{'\n    }\n\n    if (buffered === '{') this.buffered = true\n\n    if (name) this.name = name.trim()\n    const n: string[] = []\n    if (parser.fullname) n.push(parser.fullname)\n    if (this.name) n.push(this.name)\n    this.fullname = n.join(' > ').trim()\n  }\n}\n"]}