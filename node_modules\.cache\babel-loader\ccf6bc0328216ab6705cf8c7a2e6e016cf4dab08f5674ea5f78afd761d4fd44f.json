{"ast": null, "code": "import * as React from 'react';\nvar Content = function Content(props) {\n  var overlay = props.overlay,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    overlayInnerStyle = props.overlayInnerStyle;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\"),\n    id: id,\n    role: \"tooltip\",\n    style: overlayInnerStyle\n  }, typeof overlay === 'function' ? overlay() : overlay);\n};\nexport default Content;", "map": {"version": 3, "names": ["React", "Content", "props", "overlay", "prefixCls", "id", "overlayInnerStyle", "createElement", "className", "concat", "role", "style"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tooltip/es/Content.js"], "sourcesContent": ["import * as React from 'react';\n\nvar Content = function Content(props) {\n  var overlay = props.overlay,\n      prefixCls = props.prefixCls,\n      id = props.id,\n      overlayInnerStyle = props.overlayInnerStyle;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\"),\n    id: id,\n    role: \"tooltip\",\n    style: overlayInnerStyle\n  }, typeof overlay === 'function' ? overlay() : overlay);\n};\n\nexport default Content;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,iBAAiB,GAAGJ,KAAK,CAACI,iBAAiB;EAC/C,OAAO,aAAaN,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,QAAQ,CAAC;IACzCC,EAAE,EAAEA,EAAE;IACNK,IAAI,EAAE,SAAS;IACfC,KAAK,EAAEL;EACT,CAAC,EAAE,OAAOH,OAAO,KAAK,UAAU,GAAGA,OAAO,CAAC,CAAC,GAAGA,OAAO,CAAC;AACzD,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}