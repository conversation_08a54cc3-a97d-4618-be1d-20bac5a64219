/**
 * Merge client rects together and remove small ones. This may result in a larger overall
 * size than that of the individual client rects.
 * We use this to simulate a finger tap on those targets later on.
 * @param {LH.Artifacts.Rect[]} clientRects
 */
export function getTappableRectsFromClientRects(clientRects: LH.Artifacts.Rect[]): import("../../types/lhr/audit-details.js").default.Rect[];
//# sourceMappingURL=tappable-rects.d.ts.map