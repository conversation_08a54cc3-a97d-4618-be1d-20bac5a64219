export namespace Sentry {
    export { init };
    export { noop as captureMessage };
    export { noop as captureBreadcrumb };
    export { noop as getContext };
    export const captureException: (error: Error, options: {
        level?: string | undefined;
        tags?: {
            [key: string]: any;
        } | undefined;
        extra?: {
            [key: string]: any;
        } | undefined;
    }) => Promise<void>;
    export function _shouldSample(): boolean;
}
export type Breadcrumb = import('@sentry/node').Breadcrumb;
export type NodeClient = import('@sentry/node').NodeClient;
export type NodeOptions = import('@sentry/node').NodeOptions;
export type Severity = import('@sentry/node').Severity;
/**
 * When called, replaces noops with actual Sentry implementation.
 * @param {{url: string, flags: LH.CliFlags, environmentData: NodeOptions}} opts
 */
declare function init(opts: {
    url: string;
    flags: LH.CliFlags;
    environmentData: NodeOptions;
}): Promise<void>;
declare function noop(): void;
export {};
//# sourceMappingURL=sentry.d.ts.map