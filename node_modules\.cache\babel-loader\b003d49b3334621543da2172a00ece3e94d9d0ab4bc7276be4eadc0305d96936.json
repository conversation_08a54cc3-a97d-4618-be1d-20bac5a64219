{"ast": null, "code": "export * from './exports';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch'; // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };", "map": {"version": 3, "names": ["unstable_batchedUpdates", "batch", "setBatch"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-redux/es/index.js"], "sourcesContent": ["export * from './exports';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch'; // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };"], "mappings": "AAAA,cAAc,WAAW;AACzB,SAASA,uBAAuB,IAAIC,KAAK,QAAQ,6BAA6B;AAC9E,SAASC,QAAQ,QAAQ,eAAe,CAAC,CAAC;AAC1C;;AAEAA,QAAQ,CAACD,KAAK,CAAC;AACf,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}