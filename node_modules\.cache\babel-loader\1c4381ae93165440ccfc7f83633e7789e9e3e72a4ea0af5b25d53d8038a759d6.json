{"ast": null, "code": "/**\n * Copyright (C) 2017-present by <PERSON> - @WebReflection\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nvar replace = ''.replace;\nvar ca = /[&<>'\"]/g;\nvar es = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34);/g;\nvar esca = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  \"'\": '&#39;',\n  '\"': '&quot;'\n};\nvar unes = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"'\n};\nexport function escape(es) {\n  return replace.call(es, ca, pe);\n}\n;\nexport function unescape(un) {\n  return replace.call(un, es, cape);\n}\n;\nfunction pe(m) {\n  return esca[m];\n}\nfunction cape(m) {\n  return unes[m];\n}", "map": {"version": 3, "names": ["replace", "ca", "es", "esca", "unes", "escape", "call", "pe", "unescape", "un", "cape", "m"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/html-escaper/esm/index.js"], "sourcesContent": ["/**\n * Copyright (C) 2017-present by <PERSON> - @WebReflection\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nvar replace = ''.replace;\n\nvar ca = /[&<>'\"]/g;\nvar es = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34);/g;\n\nvar esca = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  \"'\": '&#39;',\n  '\"': '&quot;'\n};\nvar unes = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"'\n};\n\nexport function escape(es) {\n  return replace.call(es, ca, pe);\n};\n\nexport function unescape(un) {\n  return replace.call(un, es, cape);\n};\n\nfunction pe(m) {\n  return esca[m];\n}\n\nfunction cape(m) {\n  return unes[m];\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,OAAO,GAAG,EAAE,CAACA,OAAO;AAExB,IAAIC,EAAE,GAAG,UAAU;AACnB,IAAIC,EAAE,GAAG,gDAAgD;AAEzD,IAAIC,IAAI,GAAG;EACT,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,IAAI,GAAG;EACT,OAAO,EAAE,GAAG;EACZ,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE;AACX,CAAC;AAED,OAAO,SAASC,MAAMA,CAACH,EAAE,EAAE;EACzB,OAAOF,OAAO,CAACM,IAAI,CAACJ,EAAE,EAAED,EAAE,EAAEM,EAAE,CAAC;AACjC;AAAC;AAED,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAE;EAC3B,OAAOT,OAAO,CAACM,IAAI,CAACG,EAAE,EAAEP,EAAE,EAAEQ,IAAI,CAAC;AACnC;AAAC;AAED,SAASH,EAAEA,CAACI,CAAC,EAAE;EACb,OAAOR,IAAI,CAACQ,CAAC,CAAC;AAChB;AAEA,SAASD,IAAIA,CAACC,CAAC,EAAE;EACf,OAAOP,IAAI,CAACO,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}