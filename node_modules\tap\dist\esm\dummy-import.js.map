{"version": 3, "file": "dummy-import.js", "sourceRoot": "", "sources": ["../../src/dummy-import.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,OAAO,cAAc,CAAA;AACrB,OAAO,mBAAmB,CAAA;AAC1B,OAAO,gBAAgB,CAAA;AACvB,OAAO,eAAe,CAAA;AACtB,OAAO,oBAAoB,CAAA;AAC3B,OAAO,cAAc,CAAA;AACrB,OAAO,eAAe,CAAA;AACtB,OAAO,gBAAgB,CAAA;AACvB,OAAO,kBAAkB,CAAA;AACzB,OAAO,aAAa,CAAA;AACpB,OAAO,uBAAuB,CAAA;AAC9B,OAAO,iBAAiB,CAAA;AACxB,OAAO,cAAc,CAAA;AACrB,OAAO,cAAc,CAAA;AACrB,OAAO,mBAAmB,CAAA;AAC1B,OAAO,eAAe,CAAA;AACtB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA", "sourcesContent": ["// just here to ensure all of these are forced to be deps\nimport '@tapjs/after'\nimport '@tapjs/after-each'\nimport '@tapjs/asserts'\nimport '@tapjs/before'\nimport '@tapjs/before-each'\nimport '@tapjs/chdir'\nimport '@tapjs/filter'\nimport '@tapjs/fixture'\nimport '@tapjs/intercept'\nimport '@tapjs/mock'\nimport '@tapjs/node-serialize'\nimport '@tapjs/snapshot'\nimport '@tapjs/spawn'\nimport '@tapjs/stdin'\nimport '@tapjs/typescript'\nimport '@tapjs/worker'\nthrow new Error('this module should not be loaded')\n"]}