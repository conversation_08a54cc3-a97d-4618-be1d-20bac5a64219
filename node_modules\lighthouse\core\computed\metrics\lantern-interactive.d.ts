export { LanternInteractiveComputed as LanternInteractive };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternInteractiveComputed: typeof LanternInteractive & {
    /**
     * @param {LH.Artifacts.MetricComputationDataInput} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.LanternMetric>}
     */
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
declare class LanternInteractive extends LanternMetric {
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {LH.Gatherer.Simulation.Result['nodeTimings']} nodeTimings
     * @return {number}
     */
    static getLastLongTaskEndTime(nodeTimings: LH.Gatherer.Simulation.Result['nodeTimings'], duration?: number): number;
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-interactive.d.ts.map