{"ast": null, "code": "export default (function (element) {\n  if (!element) {\n    return false;\n  }\n  if (element.offsetParent) {\n    return true;\n  }\n  if (element.getBBox) {\n    var box = element.getBBox();\n    if (box.width || box.height) {\n      return true;\n    }\n  }\n  if (element.getBoundingClientRect) {\n    var _box = element.getBoundingClientRect();\n    if (_box.width || _box.height) {\n      return true;\n    }\n  }\n  return false;\n});", "map": {"version": 3, "names": ["element", "offsetParent", "getBBox", "box", "width", "height", "getBoundingClientRect", "_box"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/isVisible.js"], "sourcesContent": ["export default (function (element) {\n  if (!element) {\n    return false;\n  }\n\n  if (element.offsetParent) {\n    return true;\n  }\n\n  if (element.getBBox) {\n    var box = element.getBBox();\n\n    if (box.width || box.height) {\n      return true;\n    }\n  }\n\n  if (element.getBoundingClientRect) {\n    var _box = element.getBoundingClientRect();\n\n    if (_box.width || _box.height) {\n      return true;\n    }\n  }\n\n  return false;\n});"], "mappings": "AAAA,gBAAgB,UAAUA,OAAO,EAAE;EACjC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,KAAK;EACd;EAEA,IAAIA,OAAO,CAACC,YAAY,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAID,OAAO,CAACE,OAAO,EAAE;IACnB,IAAIC,GAAG,GAAGH,OAAO,CAACE,OAAO,CAAC,CAAC;IAE3B,IAAIC,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,MAAM,EAAE;MAC3B,OAAO,IAAI;IACb;EACF;EAEA,IAAIL,OAAO,CAACM,qBAAqB,EAAE;IACjC,IAAIC,IAAI,GAAGP,OAAO,CAACM,qBAAqB,CAAC,CAAC;IAE1C,IAAIC,IAAI,CAACH,KAAK,IAAIG,IAAI,CAACF,MAAM,EAAE;MAC7B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}