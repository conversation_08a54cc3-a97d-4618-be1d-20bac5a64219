{"version": 3, "file": "of.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/observable/of.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAS3C,wBAAgB,EAAE,CAAC,KAAK,EAAE,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAClD,wBAAgB,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AAE5D,8IAA8I;AAC9I,wBAAgB,EAAE,CAAC,SAAS,EAAE,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAChE,8IAA8I;AAC9I,wBAAgB,EAAE,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,EAAE,GAAG,kBAAkB,EAAE,CAAC,GAAG,CAAC,EAAE,aAAa,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AAE9H,wBAAgB,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACxC,0IAA0I;AAC1I,wBAAgB,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,wBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/C,wBAAgB,EAAE,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC"}