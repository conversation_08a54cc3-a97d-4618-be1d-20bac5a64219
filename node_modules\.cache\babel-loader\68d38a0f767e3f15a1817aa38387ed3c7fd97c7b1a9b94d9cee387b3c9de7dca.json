{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiAffiliati.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAffiliati - operazioni sull'aggiunta affiliati\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport CustomDataTable from '../components/customDataTable';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiAffiliati = () => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState([]);\n  const [selectedAffiliate, setSelectedAffiliate] = useState(null);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    async function trovaRisultato() {\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'registry/').then(res => {\n        setResults(res.data);\n      }).catch(e => {\n        console.log(e);\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, []);\n  if (results.length === 0) {\n    return null;\n  }\n  const Invia = async e => {\n    setSelectedAffiliate(e.value);\n    var corpo = {\n      idRegistry: e.value.id\n    };\n    //Chiamata axios per la creazione dell'affiliato\n    await APIRequest('POST', 'affiliate/', corpo).then(res => {\n      console.log(res.data);\n      toast.current.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"L'affiliato è stato inserito con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile aggiungere l'affiliato. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  const fields = [{\n    field: 'firstName',\n    header: Costanti.Nome,\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'pIva',\n    header: Costanti.pIva,\n    sortable: true,\n    showHeader: true\n  }, {\n    field: 'address',\n    header: Costanti.Indirizzo,\n    sortable: true,\n    showHeader: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n        value: results,\n        fields: fields,\n        dataKey: \"id\",\n        paginator: true,\n        rows: 5,\n        rowsPerPageOptions: [5, 10, 20, 50],\n        selectionMode: \"single\",\n        selection: selectedAffiliate,\n        onSelectionChange: e => Invia(e),\n        responsiveLayout: \"scroll\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiAffiliati, \"7Q2I2ZjKRAYMmFmc+TX3VHS59B8=\");\n_c = AggiungiAffiliati;\nexport default AggiungiAffiliati;\nvar _c;\n$RefreshReg$(_c, \"AggiungiAffiliati\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "CustomDataTable", "<PERSON><PERSON>", "APIRequest", "stopLoading", "Toast", "jsxDEV", "_jsxDEV", "AggiungiAffiliati", "_s", "results", "setResults", "selectedAffiliate", "setSelectedAffiliate", "toast", "trovaRisultato", "then", "res", "data", "catch", "e", "console", "log", "length", "Invia", "value", "corpo", "idRegistry", "id", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "concat", "response", "undefined", "message", "fields", "field", "header", "Nome", "sortable", "showHeader", "pIva", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dataKey", "paginator", "rows", "rowsPerPageOptions", "selectionMode", "selection", "onSelectionChange", "responsiveLayout", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiAffiliati.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAffiliati - operazioni sull'aggiunta affiliati\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport CustomDataTable from '../components/customDataTable';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\n\nconst AggiungiAffiliati = () => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState([]);\n    const [selectedAffiliate, setSelectedAffiliate] = useState(null);\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        async function trovaRisultato() {\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'registry/')\n                .then(res => {\n                    setResults(res.data);\n                }).catch((e) => {\n                    console.log(e)\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, []);\n    if (results.length === 0) {\n        return null;\n    }\n    const Invia = async (e) => {\n        setSelectedAffiliate(e.value)\n        var corpo = {\n            idRegistry: e.value.id\n        }\n        //Chiamata axios per la creazione dell'affiliato\n        await APIRequest('POST', 'affiliate/', corpo)\n            .then(res => {\n                console.log(res.data);\n                toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"L'affiliato è stato inserito con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere l'affiliato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    };\n    const fields = [\n        { field: 'firstName', header: Costanti.Nome, sortable: true, showHeader: true },\n        { field: 'pIva', header: Costanti.pIva, sortable: true, showHeader: true },\n        { field: 'address', header: Costanti.Indirizzo, sortable: true, showHeader: true }\n    ];\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"row\">\n                <CustomDataTable\n                    value={results}\n                    fields={fields}\n                    dataKey=\"id\"\n                    paginator\n                    rows={5}\n                    rowsPerPageOptions={[5, 10, 20, 50]}\n                    selectionMode=\"single\"\n                    selection={selectedAffiliate}\n                    onSelectionChange={e => Invia(e)}\n                    responsiveLayout=\"scroll\"\n                />\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiAffiliati;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAMgB,KAAK,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZ,eAAegB,cAAcA,CAAA,EAAG;MAC5B;MACA,MAAMZ,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,CAC/Ba,IAAI,CAACC,GAAG,IAAI;QACTN,UAAU,CAACM,GAAG,CAACC,IAAI,CAAC;MACxB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;MACNhB,WAAW,CAAC,CAAC;IACjB;IACAW,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIL,OAAO,CAACa,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACf;EACA,MAAMC,KAAK,GAAG,MAAOJ,CAAC,IAAK;IACvBP,oBAAoB,CAACO,CAAC,CAACK,KAAK,CAAC;IAC7B,IAAIC,KAAK,GAAG;MACRC,UAAU,EAAEP,CAAC,CAACK,KAAK,CAACG;IACxB,CAAC;IACD;IACA,MAAMzB,UAAU,CAAC,MAAM,EAAE,YAAY,EAAEuB,KAAK,CAAC,CACxCV,IAAI,CAACC,GAAG,IAAI;MACTI,OAAO,CAACC,GAAG,CAACL,GAAG,CAACC,IAAI,CAAC;MACrBJ,KAAK,CAACe,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,2CAA2C;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MAC/HC,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACnB,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAmB,WAAA,EAAAC,YAAA;MACZnB,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdN,KAAK,CAACe,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,wEAAAQ,MAAA,CAAqE,EAAAF,WAAA,GAAAnB,CAAC,CAACsB,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAYrB,IAAI,MAAKyB,SAAS,IAAAH,YAAA,GAAGpB,CAAC,CAACsB,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAYtB,IAAI,GAAGE,CAAC,CAACwB,OAAO,CAAE;QAAEV,IAAI,EAAE;MAAK,CAAC,CAAC;IACjO,CAAC,CAAC;EACV,CAAC;EACD,MAAMW,MAAM,GAAG,CACX;IAAEC,KAAK,EAAE,WAAW;IAAEC,MAAM,EAAE7C,QAAQ,CAAC8C,IAAI;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC/E;IAAEJ,KAAK,EAAE,MAAM;IAAEC,MAAM,EAAE7C,QAAQ,CAACiD,IAAI;IAAEF,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC1E;IAAEJ,KAAK,EAAE,SAAS;IAAEC,MAAM,EAAE7C,QAAQ,CAACkD,SAAS;IAAEH,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,CACrF;EACD,oBACI3C,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtB/C,OAAA,CAACF,KAAK;MAACkD,GAAG,EAAEzC;IAAM;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBpD,OAAA;MAAK8C,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChB/C,OAAA,CAACN,eAAe;QACZwB,KAAK,EAAEf,OAAQ;QACfmC,MAAM,EAAEA,MAAO;QACfe,OAAO,EAAC,IAAI;QACZC,SAAS;QACTC,IAAI,EAAE,CAAE;QACRC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACpCC,aAAa,EAAC,QAAQ;QACtBC,SAAS,EAAErD,iBAAkB;QAC7BsD,iBAAiB,EAAE9C,CAAC,IAAII,KAAK,CAACJ,CAAC,CAAE;QACjC+C,gBAAgB,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAlD,EAAA,CAhEKD,iBAAiB;AAAA4D,EAAA,GAAjB5D,iBAAiB;AAkEvB,eAAeA,iBAAiB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}