export type Connection = import('./legacy/gather/connections/connection.js').Connection;
export type ArbitraryEqualityMap = import('./lib/arbitrary-equality-map.js').ArbitraryEqualityMap;
export type Config = LH.Config.LegacyResolvedConfig;
/** @typedef {import('./legacy/gather/connections/connection.js').Connection} Connection */
/** @typedef {import('./lib/arbitrary-equality-map.js').ArbitraryEqualityMap} ArbitraryEqualityMap */
/** @typedef {LH.Config.LegacyResolvedConfig} Config */
export class Runner {
    /**
     * @template {LH.Config.LegacyResolvedConfig | LH.Config.ResolvedConfig} TConfig
     * @param {LH.Artifacts} artifacts
     * @param {{resolvedConfig: TConfig, driverMock?: Driver, computedCache: Map<string, ArbitraryEqualityMap>}} options
     * @return {Promise<LH.RunnerResult|undefined>}
     */
    static audit<TConfig extends import("../types/config.js").default.ResolvedConfig | import("../types/config.js").default.LegacyResolvedConfig>(artifacts: LH.Artifacts, options: {
        resolvedConfig: TConfig;
        driverMock?: Driver | undefined;
        computedCache: Map<string, ArbitraryEqualityMap>;
    }): Promise<LH.RunnerResult | undefined>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Artifacts.ComputedContext} context
     */
    static getEntityClassification(artifacts: LH.Artifacts, context: LH.Artifacts.ComputedContext): Promise<import("../types/lhr/lhr.js").default.LhrEntity[] | undefined>;
    /**
     * User can run -G solo, -A solo, or -GA together
     * -G and -A will run partial lighthouse pipelines,
     * and -GA will run everything plus save artifacts and lhr to disk.
     *
     * @template {LH.Config.LegacyResolvedConfig | LH.Config.ResolvedConfig} TConfig
     * @param {(runnerData: {resolvedConfig: TConfig, driverMock?: Driver}) => Promise<LH.Artifacts>} gatherFn
     * @param {{resolvedConfig: TConfig, driverMock?: Driver, computedCache: Map<string, ArbitraryEqualityMap>}} options
     * @return {Promise<LH.Artifacts>}
     */
    static gather<TConfig_1 extends import("../types/config.js").default.ResolvedConfig | import("../types/config.js").default.LegacyResolvedConfig>(gatherFn: (runnerData: {
        resolvedConfig: TConfig_1;
        driverMock?: Driver | undefined;
    }) => Promise<LH.Artifacts>, options: {
        resolvedConfig: TConfig_1;
        driverMock?: Driver | undefined;
        computedCache: Map<string, ArbitraryEqualityMap>;
    }): Promise<LH.Artifacts>;
    /**
     * @param {any} err
     * @param {LH.Config.Settings} settings
     */
    static createRunnerError(err: any, settings: LH.Config.Settings): any;
    /**
     * This handles both the auditMode case where gatherer entries need to be merged in and
     * the gather/audit case where timingEntriesFromRunner contains all entries from this run,
     * including those also in timingEntriesFromArtifacts.
     * @param {LH.Artifacts} artifacts
     * @return {LH.Result.Timing}
     */
    static _getTiming(artifacts: LH.Artifacts): LH.Result.Timing;
    /**
     * Establish connection, load page and collect all required artifacts
     * @param {string} requestedUrl
     * @param {{resolvedConfig: Config, computedCache: Map<string, ArbitraryEqualityMap>, driverMock?: Driver}} runnerOpts
     * @param {Connection} connection
     * @return {Promise<LH.Artifacts>}
     */
    static _gatherArtifactsFromBrowser(requestedUrl: string, runnerOpts: {
        resolvedConfig: Config;
        computedCache: Map<string, ArbitraryEqualityMap>;
        driverMock?: Driver;
    }, connection: Connection): Promise<LH.Artifacts>;
    /**
     * Run all audits with specified settings and artifacts.
     * @param {LH.Config.Settings} settings
     * @param {Array<LH.Config.AuditDefn>} audits
     * @param {LH.Artifacts} artifacts
     * @param {Array<string | LH.IcuMessage>} runWarnings
     * @param {Map<string, ArbitraryEqualityMap>} computedCache
     * @return {Promise<Record<string, LH.RawIcu<LH.Audit.Result>>>}
     */
    static _runAudits(settings: LH.Config.Settings, audits: Array<LH.Config.AuditDefn>, artifacts: LH.Artifacts, runWarnings: Array<string | LH.IcuMessage>, computedCache: Map<string, ArbitraryEqualityMap>): Promise<Record<string, LH.RawIcu<LH.Audit.Result>>>;
    /**
     * Checks that the audit's required artifacts exist and runs the audit if so.
     * Otherwise returns error audit result.
     * @param {LH.Config.AuditDefn} auditDefn
     * @param {LH.Artifacts} artifacts
     * @param {Pick<LH.Audit.Context, 'settings'|'computedCache'>} sharedAuditContext
     * @param {Array<string | LH.IcuMessage>} runWarnings
     * @return {Promise<LH.RawIcu<LH.Audit.Result>>}
     * @private
     */
    private static _runAudit;
    /**
     * Searches a pass's artifacts for any `lhrRuntimeError` error artifacts.
     * Returns the first one found or `null` if none found.
     * @param {LH.Artifacts} artifacts
     * @return {LH.RawIcu<LH.Result['runtimeError']>|undefined}
     */
    static getArtifactRuntimeError(artifacts: LH.Artifacts): LH.RawIcu<LH.Result['runtimeError']> | undefined;
    /**
     * Returns list of audit names for external querying.
     * @return {Array<string>}
     */
    static getAuditList(): Array<string>;
    /**
     * Returns list of gatherer names for external querying.
     * @return {Array<string>}
     */
    static getGathererList(): Array<string>;
    /**
     * Get path to use for -G and -A modes. Defaults to $CWD/latest-run
     * @param {LH.Config.Settings} settings
     * @return {string}
     */
    static _getDataSavePath(settings: LH.Config.Settings): string;
}
import { Driver } from "./legacy/gather/driver.js";
import { Audit } from "./audits/audit.js";
//# sourceMappingURL=runner.d.ts.map