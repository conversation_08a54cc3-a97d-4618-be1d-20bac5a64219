{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { STEP_PREPARE, STEP_ACTIVE, STEP_START, STEP_ACTIVATED, STEP_NONE } from '../interface';\nimport useNextFrame from './useNextFrame';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nvar STEP_QUEUE = [STEP_PREPARE, STEP_START, STEP_ACTIVE, STEP_ACTIVATED];\n/** Skip current step */\n\nexport var SkipStep = false;\n/** Current step should be update in */\n\nexport var DoStep = true;\nexport function isActive(step) {\n  return step === STEP_ACTIVE || step === STEP_ACTIVATED;\n}\nexport default (function (status, callback) {\n  var _useState = useState(STEP_NONE),\n    _useState2 = _slicedToArray(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = useNextFrame(),\n    _useNextFrame2 = _slicedToArray(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(STEP_PREPARE, true);\n  }\n  useIsomorphicLayoutEffect(function () {\n    if (step !== STEP_NONE && step !== STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "useState", "STEP_PREPARE", "STEP_ACTIVE", "STEP_START", "STEP_ACTIVATED", "STEP_NONE", "useNextFrame", "useIsomorphicLayoutEffect", "STEP_QUEUE", "SkipStep", "DoStep", "isActive", "step", "status", "callback", "_useState", "_useState2", "setStep", "_useNextFrame", "_useNextFrame2", "next<PERSON><PERSON><PERSON>", "cancelNextFrame", "startQueue", "index", "indexOf", "nextStep", "result", "info", "doNext", "isCanceled", "Promise", "resolve", "then", "useEffect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/hooks/useStepQueue.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { STEP_PREPARE, STEP_ACTIVE, STEP_START, STEP_ACTIVATED, STEP_NONE } from '../interface';\nimport useNextFrame from './useNextFrame';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nvar STEP_QUEUE = [STEP_PREPARE, STEP_START, STEP_ACTIVE, STEP_ACTIVATED];\n/** Skip current step */\n\nexport var SkipStep = false;\n/** Current step should be update in */\n\nexport var DoStep = true;\nexport function isActive(step) {\n  return step === STEP_ACTIVE || step === STEP_ACTIVATED;\n}\nexport default (function (status, callback) {\n  var _useState = useState(STEP_NONE),\n      _useState2 = _slicedToArray(_useState, 2),\n      step = _useState2[0],\n      setStep = _useState2[1];\n\n  var _useNextFrame = useNextFrame(),\n      _useNextFrame2 = _slicedToArray(_useNextFrame, 2),\n      nextFrame = _useNextFrame2[0],\n      cancelNextFrame = _useNextFrame2[1];\n\n  function startQueue() {\n    setStep(STEP_PREPARE, true);\n  }\n\n  useIsomorphicLayoutEffect(function () {\n    if (step !== STEP_NONE && step !== STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAEC,SAAS,QAAQ,cAAc;AAC/F,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,IAAIC,UAAU,GAAG,CAACP,YAAY,EAAEE,UAAU,EAAED,WAAW,EAAEE,cAAc,CAAC;AACxE;;AAEA,OAAO,IAAIK,QAAQ,GAAG,KAAK;AAC3B;;AAEA,OAAO,IAAIC,MAAM,GAAG,IAAI;AACxB,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAKV,WAAW,IAAIU,IAAI,KAAKR,cAAc;AACxD;AACA,gBAAgB,UAAUS,MAAM,EAAEC,QAAQ,EAAE;EAC1C,IAAIC,SAAS,GAAGf,QAAQ,CAACK,SAAS,CAAC;IAC/BW,UAAU,GAAGlB,cAAc,CAACiB,SAAS,EAAE,CAAC,CAAC;IACzCH,IAAI,GAAGI,UAAU,CAAC,CAAC,CAAC;IACpBC,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIE,aAAa,GAAGZ,YAAY,CAAC,CAAC;IAC9Ba,cAAc,GAAGrB,cAAc,CAACoB,aAAa,EAAE,CAAC,CAAC;IACjDE,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EAEvC,SAASG,UAAUA,CAAA,EAAG;IACpBL,OAAO,CAAChB,YAAY,EAAE,IAAI,CAAC;EAC7B;EAEAM,yBAAyB,CAAC,YAAY;IACpC,IAAIK,IAAI,KAAKP,SAAS,IAAIO,IAAI,KAAKR,cAAc,EAAE;MACjD,IAAImB,KAAK,GAAGf,UAAU,CAACgB,OAAO,CAACZ,IAAI,CAAC;MACpC,IAAIa,QAAQ,GAAGjB,UAAU,CAACe,KAAK,GAAG,CAAC,CAAC;MACpC,IAAIG,MAAM,GAAGZ,QAAQ,CAACF,IAAI,CAAC;MAE3B,IAAIc,MAAM,KAAKjB,QAAQ,EAAE;QACvB;QACAQ,OAAO,CAACQ,QAAQ,EAAE,IAAI,CAAC;MACzB,CAAC,MAAM;QACL;QACAL,SAAS,CAAC,UAAUO,IAAI,EAAE;UACxB,SAASC,MAAMA,CAAA,EAAG;YAChB;YACA,IAAID,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;YACvBZ,OAAO,CAACQ,QAAQ,EAAE,IAAI,CAAC;UACzB;UAEA,IAAIC,MAAM,KAAK,IAAI,EAAE;YACnBE,MAAM,CAAC,CAAC;UACV,CAAC,MAAM;YACL;YACAE,OAAO,CAACC,OAAO,CAACL,MAAM,CAAC,CAACM,IAAI,CAACJ,MAAM,CAAC;UACtC;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACf,MAAM,EAAED,IAAI,CAAC,CAAC;EAClBb,KAAK,CAACkC,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBZ,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACC,UAAU,EAAEV,IAAI,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}