{"ast": null, "code": "import { createContext } from 'react';\nvar MenuContext = /*#__PURE__*/createContext({\n  prefixCls: '',\n  firstLevel: true,\n  inlineCollapsed: false\n});\nexport default MenuContext;", "map": {"version": 3, "names": ["createContext", "MenuContext", "prefixCls", "firstLevel", "inlineCollapsed"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/menu/MenuContext.js"], "sourcesContent": ["import { createContext } from 'react';\nvar MenuContext = /*#__PURE__*/createContext({\n  prefixCls: '',\n  firstLevel: true,\n  inlineCollapsed: false\n});\nexport default MenuContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,IAAIC,WAAW,GAAG,aAAaD,aAAa,CAAC;EAC3CE,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}