{"version": 3, "file": "usage.js", "sourceRoot": "", "sources": ["../../src/usage.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,IAAI,MAAM,WAAW,CAAA;AAE5B,eAAe,CAAC,GAAY,EAAE,EAAE;IAC9B,MAAM,GAAG,GAAG,gCAAgC,CAAA;IAC5C,MAAM,IAAI,GACR,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAA;IACpE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;;;;;;qCAMI,IAAI,EAAE,CAAC,CAAA;IAC1C,IAAI,GAAG;QAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3B,CAAC,CAAA", "sourcesContent": ["import chalk from 'chalk'\nimport fail from './fail.js'\n\nexport default (err?: string) => {\n  const url = 'https://github.com/isaacs/tshy'\n  const link =\n    chalk.level > 0 ? `\\x1b]8;;${url}\\x1b\\\\${url}\\x1b]8;;\\x1b\\\\` : url\n  console[err ? 'error' : 'log'](`Usage: tshy [--help]\n  --help -h   Print this message and exit.\n  --watch -w  Watch files in ./src and build when they change.\n\nDefault behavior: build project according to tshy config in package.json\n\nSee the docs for more information: ${link}`)\n  if (err) fail(err)\n  process.exit(err ? 1 : 0)\n}\n"]}