{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useFrameSetState(initial) {\n  var frame = React.useRef(null);\n  var _React$useState = React.useState(initial),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    state = _React$useState2[0],\n    setState = _React$useState2[1];\n  var queue = React.useRef([]);\n  var setFrameState = function setFrameState(newState) {\n    if (frame.current === null) {\n      queue.current = [];\n      frame.current = raf(function () {\n        setState(function (preState) {\n          var memoState = preState;\n          queue.current.forEach(function (queueState) {\n            memoState = _objectSpread(_objectSpread({}, memoState), queueState);\n          });\n          frame.current = null;\n          return memoState;\n        });\n      });\n    }\n    queue.current.push(newState);\n  };\n  React.useEffect(function () {\n    return function () {\n      return frame.current && raf.cancel(frame.current);\n    };\n  }, []);\n  return [state, setFrameState];\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "React", "raf", "useFrameSetState", "initial", "frame", "useRef", "_React$useState", "useState", "_React$useState2", "state", "setState", "queue", "setFrameState", "newState", "current", "preState", "memoState", "for<PERSON>ach", "queueState", "push", "useEffect", "cancel"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-image/es/hooks/useFrameSetState.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useFrameSetState(initial) {\n  var frame = React.useRef(null);\n\n  var _React$useState = React.useState(initial),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      state = _React$useState2[0],\n      setState = _React$useState2[1];\n\n  var queue = React.useRef([]);\n\n  var setFrameState = function setFrameState(newState) {\n    if (frame.current === null) {\n      queue.current = [];\n      frame.current = raf(function () {\n        setState(function (preState) {\n          var memoState = preState;\n          queue.current.forEach(function (queueState) {\n            memoState = _objectSpread(_objectSpread({}, memoState), queueState);\n          });\n          frame.current = null;\n          return memoState;\n        });\n      });\n    }\n\n    queue.current.push(newState);\n  };\n\n  React.useEffect(function () {\n    return function () {\n      return frame.current && raf.cancel(frame.current);\n    };\n  }, []);\n  return [state, setFrameState];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAChD,IAAIC,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;EAE9B,IAAIC,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAACJ,OAAO,CAAC;IACzCK,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIG,KAAK,GAAGX,KAAK,CAACK,MAAM,CAAC,EAAE,CAAC;EAE5B,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACC,QAAQ,EAAE;IACnD,IAAIT,KAAK,CAACU,OAAO,KAAK,IAAI,EAAE;MAC1BH,KAAK,CAACG,OAAO,GAAG,EAAE;MAClBV,KAAK,CAACU,OAAO,GAAGb,GAAG,CAAC,YAAY;QAC9BS,QAAQ,CAAC,UAAUK,QAAQ,EAAE;UAC3B,IAAIC,SAAS,GAAGD,QAAQ;UACxBJ,KAAK,CAACG,OAAO,CAACG,OAAO,CAAC,UAAUC,UAAU,EAAE;YAC1CF,SAAS,GAAGlB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,SAAS,CAAC,EAAEE,UAAU,CAAC;UACrE,CAAC,CAAC;UACFd,KAAK,CAACU,OAAO,GAAG,IAAI;UACpB,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAL,KAAK,CAACG,OAAO,CAACK,IAAI,CAACN,QAAQ,CAAC;EAC9B,CAAC;EAEDb,KAAK,CAACoB,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB,OAAOhB,KAAK,CAACU,OAAO,IAAIb,GAAG,CAACoB,MAAM,CAACjB,KAAK,CAACU,OAAO,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACL,KAAK,EAAEG,aAAa,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}