{"ast": null, "code": "/** This is a placeholder, not real render in dom */\nvar OptGroup = function OptGroup() {\n  return null;\n};\nOptGroup.isSelectOptGroup = true;\nexport default OptGroup;", "map": {"version": 3, "names": ["OptGroup", "isSelectOptGroup"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/OptGroup.js"], "sourcesContent": ["/** This is a placeholder, not real render in dom */\nvar OptGroup = function OptGroup() {\n  return null;\n};\n\nOptGroup.isSelectOptGroup = true;\nexport default OptGroup;"], "mappings": "AAAA;AACA,IAAIA,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO,IAAI;AACb,CAAC;AAEDA,QAAQ,CAACC,gBAAgB,GAAG,IAAI;AAChC,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}