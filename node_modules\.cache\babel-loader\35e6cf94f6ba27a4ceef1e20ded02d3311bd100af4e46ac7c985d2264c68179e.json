{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"listHeight\", \"listItemHeight\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\"];\n\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport BaseSelect, { isMultiple } from './BaseSelect';\nimport OptionList from './OptionList';\nimport Option from './Option';\nimport OptGroup from './OptGroup';\nimport useOptions from './hooks/useOptions';\nimport SelectContext from './SelectContext';\nimport useId from './hooks/useId';\nimport useRefFunc from './hooks/useRefFunc';\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from './utils/valueUtil';\nimport warningProps from './utils/warningPropsUtil';\nimport { toArray } from './utils/commonUtil';\nimport useFilterOptions from './hooks/useFilterOptions';\nimport useCache from './hooks/useCache';\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]); // ========================= FieldNames =========================\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]); // =========================== Search ===========================\n\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1]; // =========================== Option ===========================\n\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options; // ========================= Wrap Value =========================\n\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues); // Convert to labelInValue type\n\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled; // Fill label & value\n\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled; // Warning if label not same as provided\n\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]); // =========================== Values ===========================\n\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1]; // Merged value with LabelValueType\n\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var values = convert2LabelValues(internalValue); // combobox no need save value when it's empty\n\n    if (mode === 'combobox' && !((_values$ = values[0]) === null || _values$ === void 0 ? void 0 : _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode]); // Fill label with cache to avoid option remove\n\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mode, mergedValues]);\n  /** Convert `displayValues` to raw value type set */\n\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      if (strValue !== undefined && strValue !== null) {\n        setSearchValue(String(strValue));\n      }\n    }\n  }, [mergedValues]); // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n\n  var createTagOption = useRefFunc(function (val, label) {\n    var _ref;\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _ref = {}, _defineProperty(_ref, mergedFieldNames.value, val), _defineProperty(_ref, mergedFieldNames.label, mergedLabel), _ref;\n  }); // Fill tag as option if mode is `tags`\n\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    } // >>> Tag mode\n\n    var cloneOptions = _toConsumableArray(mergedOptions); // Check if value exist in options (include new patch item)\n\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    }; // Fill current value as option\n\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp); // Fill options with search value if needed\n\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    } // Fill search value as option\n\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue]);\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return _toConsumableArray(filledSearchOptions).sort(function (a, b) {\n      return filterSort(a, b);\n    });\n  }, [filledSearchOptions, filterSort]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]); // =========================== Change ===========================\n\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  }; // ======================= Accessibility ========================\n\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref2$source = _ref2.source,\n      source = _ref2$source === void 0 ? 'keyboard' : _ref2$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]); // ========================= OptionList =========================\n\n  var triggerSelect = function triggerSelect(val, selected) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        option = _getSelectEnt2[1];\n      onSelect(wrappedValue, option);\n    } else if (!selected && onDeselect) {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option);\n    }\n  }; // Used for OptionList selection\n\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues; // Single mode always trigger select only with option list\n\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect); // Clean search value if single or configured\n\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  }); // ======================= Display Change =======================\n  // BaseSelect display values change\n\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    if (info.type === 'remove' || info.type === 'clear') {\n      info.values.forEach(function (item) {\n        triggerSelect(item.value, false);\n      });\n    }\n  }; // =========================== Search ===========================\n\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null); // [Submit] Tag mode should flush input\n\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim(); // prevent empty tags from appearing when you click the Enter button\n\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  }; // ========================== Context ===========================\n\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData\n    });\n  }, [parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, childrenAsData]); // ========================== Warning ===========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode // >>> Values\n    ,\n\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth // >>> OptionList\n    ,\n\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length // >>> Accessibility\n    ,\n\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_defineProperty", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_typeof", "_excluded", "React", "warning", "useMergedState", "BaseSelect", "isMultiple", "OptionList", "Option", "OptGroup", "useOptions", "SelectContext", "useId", "useRefFunc", "fillFieldNames", "flattenOptions", "injectPropsWithOption", "warningProps", "toArray", "useFilterOptions", "useCache", "OMIT_DOM_PROPS", "isRawValue", "value", "Select", "forwardRef", "props", "ref", "id", "mode", "_props$prefixCls", "prefixCls", "backfill", "fieldNames", "inputValue", "searchValue", "onSearch", "_props$autoClearSearc", "autoClearSearchValue", "onSelect", "onDeselect", "_props$dropdownMatchS", "dropdownMatchSelectWidth", "filterOption", "filterSort", "optionFilterProp", "optionLabelProp", "options", "children", "defaultActiveFirstOption", "menuItemSelectedIcon", "virtual", "_props$listHeight", "listHeight", "_props$listItemHeight", "listItemHeight", "defaultValue", "labelInValue", "onChange", "restProps", "mergedId", "multiple", "childrenAsData", "mergedFilterOption", "useMemo", "undefined", "mergedFieldNames", "JSON", "stringify", "_useMergedState", "postState", "search", "_useMergedState2", "mergedSearchValue", "setSearchValue", "parsedOptions", "valueOptions", "labelOptions", "mergedOptions", "convert2LabelValues", "useCallback", "draftV<PERSON><PERSON>", "valueList", "map", "val", "rawValue", "rawLabel", "<PERSON><PERSON><PERSON>", "rawDisabled", "_val$value", "key", "label", "option", "get", "_option$key", "disabled", "process", "env", "NODE_ENV", "optionLabel", "_useMergedState3", "_useMergedState4", "internalValue", "setInternalValue", "rawLabeledValues", "_values$", "values", "_useCache", "_useCache2", "mergedValues", "getMixedOption", "displayValues", "length", "firstValue", "item", "_item$label", "rawValues", "Set", "useEffect", "_mergedValues$", "strValue", "String", "createTagOption", "_ref", "mergedLabel", "filledTagOptions", "cloneOptions", "existOptions", "has", "sort", "a", "b", "for<PERSON>ach", "push", "filteredOptions", "filledSearchOptions", "some", "concat", "orderedFilteredOptions", "displayOptions", "trigger<PERSON>hange", "labeledV<PERSON>ues", "newVal", "index", "_mergedValues$index", "returnV<PERSON>ues", "v", "returnOptions", "_React$useState", "useState", "_React$useState2", "activeValue", "setActiveValue", "_React$useState3", "_React$useState4", "accessibilityIndex", "setAccessibilityIndex", "mergedDefaultActiveFirstOption", "onActiveValue", "active", "_ref2", "arguments", "_ref2$source", "source", "triggerSelect", "selected", "getSelectEnt", "_option$key2", "_getSelectEnt", "_getSelectEnt2", "wrappedValue", "_getSelectEnt3", "_getSelectEnt4", "_wrappedValue", "_option", "onInternalSelect", "info", "clone<PERSON><PERSON>ues", "mergedSelect", "filter", "onDisplayValuesChange", "nextV<PERSON>ues", "type", "onInternalSearch", "searchText", "formatted", "trim", "newRawValues", "Array", "from", "onInternalSearchSplit", "words", "patchValues", "word", "opt", "newRawValue", "selectContext", "realVirtual", "createElement", "Provider", "omitDomProps", "onSearchSplit", "emptyOptions", "activeDescendantId", "displayName", "TypedSelect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/Select.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"listHeight\", \"listItemHeight\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\"];\n\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport BaseSelect, { isMultiple } from './BaseSelect';\nimport OptionList from './OptionList';\nimport Option from './Option';\nimport OptGroup from './OptGroup';\nimport useOptions from './hooks/useOptions';\nimport SelectContext from './SelectContext';\nimport useId from './hooks/useId';\nimport useRefFunc from './hooks/useRefFunc';\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from './utils/valueUtil';\nimport warningProps from './utils/warningPropsUtil';\nimport { toArray } from './utils/commonUtil';\nimport useFilterOptions from './hooks/useFilterOptions';\nimport useCache from './hooks/useCache';\nvar OMIT_DOM_PROPS = ['inputValue'];\n\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\n\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n      mode = props.mode,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n      backfill = props.backfill,\n      fieldNames = props.fieldNames,\n      inputValue = props.inputValue,\n      searchValue = props.searchValue,\n      onSearch = props.onSearch,\n      _props$autoClearSearc = props.autoClearSearchValue,\n      autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n      onSelect = props.onSelect,\n      onDeselect = props.onDeselect,\n      _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n      dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n      filterOption = props.filterOption,\n      filterSort = props.filterSort,\n      optionFilterProp = props.optionFilterProp,\n      optionLabelProp = props.optionLabelProp,\n      options = props.options,\n      children = props.children,\n      defaultActiveFirstOption = props.defaultActiveFirstOption,\n      menuItemSelectedIcon = props.menuItemSelectedIcon,\n      virtual = props.virtual,\n      _props$listHeight = props.listHeight,\n      listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n      _props$listItemHeight = props.listItemHeight,\n      listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      labelInValue = props.labelInValue,\n      onChange = props.onChange,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n\n    return filterOption;\n  }, [filterOption, mode]); // ========================= FieldNames =========================\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  },\n  /* eslint-disable react-hooks/exhaustive-deps */\n  [// We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]); // =========================== Search ===========================\n\n  var _useMergedState = useMergedState('', {\n    value: searchValue !== undefined ? searchValue : inputValue,\n    postState: function postState(search) {\n      return search || '';\n    }\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedSearchValue = _useMergedState2[0],\n      setSearchValue = _useMergedState2[1]; // =========================== Option ===========================\n\n\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n      labelOptions = parsedOptions.labelOptions,\n      mergedOptions = parsedOptions.options; // ========================= Wrap Value =========================\n\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues); // Convert to labelInValue type\n\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled; // Fill label & value\n\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n\n      var option = valueOptions.get(rawValue);\n\n      if (option) {\n        var _option$key;\n\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled; // Warning if label not same as provided\n\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n\n          if (optionLabel !== undefined && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]); // =========================== Values ===========================\n\n  var _useMergedState3 = useMergedState(defaultValue, {\n    value: value\n  }),\n      _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n      internalValue = _useMergedState4[0],\n      setInternalValue = _useMergedState4[1]; // Merged value with LabelValueType\n\n\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n\n    var values = convert2LabelValues(internalValue); // combobox no need save value when it's empty\n\n    if (mode === 'combobox' && !((_values$ = values[0]) === null || _values$ === void 0 ? void 0 : _values$.value)) {\n      return [];\n    }\n\n    return values;\n  }, [internalValue, convert2LabelValues, mode]); // Fill label with cache to avoid option remove\n\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n      _useCache2 = _slicedToArray(_useCache, 2),\n      mergedValues = _useCache2[0],\n      getMixedOption = _useCache2[1];\n\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n\n    return mergedValues.map(function (item) {\n      var _item$label;\n\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mode, mergedValues]);\n  /** Convert `displayValues` to raw value type set */\n\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n\n      if (strValue !== undefined && strValue !== null) {\n        setSearchValue(String(strValue));\n      }\n    }\n  }, [mergedValues]); // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n\n  var createTagOption = useRefFunc(function (val, label) {\n    var _ref;\n\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _ref = {}, _defineProperty(_ref, mergedFieldNames.value, val), _defineProperty(_ref, mergedFieldNames.label, mergedLabel), _ref;\n  }); // Fill tag as option if mode is `tags`\n\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    } // >>> Tag mode\n\n\n    var cloneOptions = _toConsumableArray(mergedOptions); // Check if value exist in options (include new patch item)\n\n\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    }; // Fill current value as option\n\n\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp); // Fill options with search value if needed\n\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    } // Fill search value as option\n\n\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue]);\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n\n    return _toConsumableArray(filledSearchOptions).sort(function (a, b) {\n      return filterSort(a, b);\n    });\n  }, [filledSearchOptions, filterSort]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]); // =========================== Change ===========================\n\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n\n    if (onChange && ( // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange( // Value\n      multiple ? returnValues : returnValues[0], // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  }; // ======================= Accessibility ========================\n\n\n  var _React$useState = React.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      activeValue = _React$useState2[0],\n      setActiveValue = _React$useState2[1];\n\n  var _React$useState3 = React.useState(0),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      accessibilityIndex = _React$useState4[0],\n      setAccessibilityIndex = _React$useState4[1];\n\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n        _ref2$source = _ref2.source,\n        source = _ref2$source === void 0 ? 'keyboard' : _ref2$source;\n\n    setAccessibilityIndex(index);\n\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]); // ========================= OptionList =========================\n\n  var triggerSelect = function triggerSelect(val, selected) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n          _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n          wrappedValue = _getSelectEnt2[0],\n          option = _getSelectEnt2[1];\n\n      onSelect(wrappedValue, option);\n    } else if (!selected && onDeselect) {\n      var _getSelectEnt3 = getSelectEnt(),\n          _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n          _wrappedValue = _getSelectEnt4[0],\n          _option = _getSelectEnt4[1];\n\n      onDeselect(_wrappedValue, _option);\n    }\n  }; // Used for OptionList selection\n\n\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues; // Single mode always trigger select only with option list\n\n    var mergedSelect = multiple ? info.selected : true;\n\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect); // Clean search value if single or configured\n\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  }); // ======================= Display Change =======================\n  // BaseSelect display values change\n\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n\n    if (info.type === 'remove' || info.type === 'clear') {\n      info.values.forEach(function (item) {\n        triggerSelect(item.value, false);\n      });\n    }\n  }; // =========================== Search ===========================\n\n\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null); // [Submit] Tag mode should flush input\n\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim(); // prevent empty tags from appearing when you click the Enter button\n\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n\n      return;\n    }\n\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n\n      onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n    }\n  };\n\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  }; // ========================== Context ===========================\n\n\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData\n    });\n  }, [parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, childrenAsData]); // ========================== Warning ===========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n\n\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode // >>> Values\n    ,\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth // >>> OptionList\n    ,\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length // >>> Accessibility\n    ,\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\n\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,0BAA0B,EAAE,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,CAAC;;AAE5a;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,IAAIC,UAAU,QAAQ,cAAc;AACrD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,SAASC,cAAc,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,mBAAmB;AACzF,OAAOC,YAAY,MAAM,0BAA0B;AACnD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,IAAIC,cAAc,GAAG,CAAC,YAAY,CAAC;AAEnC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAACA,KAAK,IAAIvB,OAAO,CAACuB,KAAK,CAAC,KAAK,QAAQ;AAC9C;AAEA,IAAIC,MAAM,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACbC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,qBAAqB,GAAGX,KAAK,CAACY,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACtFE,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,qBAAqB,GAAGf,KAAK,CAACgB,wBAAwB;IACtDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FE,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,eAAe,GAAGpB,KAAK,CAACoB,eAAe;IACvCC,OAAO,GAAGrB,KAAK,CAACqB,OAAO;IACvBC,QAAQ,GAAGtB,KAAK,CAACsB,QAAQ;IACzBC,wBAAwB,GAAGvB,KAAK,CAACuB,wBAAwB;IACzDC,oBAAoB,GAAGxB,KAAK,CAACwB,oBAAoB;IACjDC,OAAO,GAAGzB,KAAK,CAACyB,OAAO;IACvBC,iBAAiB,GAAG1B,KAAK,CAAC2B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,qBAAqB,GAAG5B,KAAK,CAAC6B,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9E/B,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBiC,YAAY,GAAG9B,KAAK,CAAC8B,YAAY;IACjCC,YAAY,GAAG/B,KAAK,CAAC+B,YAAY;IACjCC,QAAQ,GAAGhC,KAAK,CAACgC,QAAQ;IACzBC,SAAS,GAAG5D,wBAAwB,CAAC2B,KAAK,EAAEzB,SAAS,CAAC;EAE1D,IAAI2D,QAAQ,GAAGhD,KAAK,CAACgB,EAAE,CAAC;EACxB,IAAIiC,QAAQ,GAAGvD,UAAU,CAACuB,IAAI,CAAC;EAC/B,IAAIiC,cAAc,GAAG,CAAC,EAAE,CAACf,OAAO,IAAIC,QAAQ,CAAC;EAC7C,IAAIe,kBAAkB,GAAG7D,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACjD,IAAIrB,YAAY,KAAKsB,SAAS,IAAIpC,IAAI,KAAK,UAAU,EAAE;MACrD,OAAO,KAAK;IACd;IAEA,OAAOc,YAAY;EACrB,CAAC,EAAE,CAACA,YAAY,EAAEd,IAAI,CAAC,CAAC,CAAC,CAAC;;EAE1B,IAAIqC,gBAAgB,GAAGhE,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,OAAOlD,cAAc,CAACmB,UAAU,EAAE6B,cAAc,CAAC;EACnD,CAAC,EACD;EACA;EAAC;EACDK,IAAI,CAACC,SAAS,CAACnC,UAAU,CAAC,EAAE6B,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE9C,IAAIO,eAAe,GAAGjE,cAAc,CAAC,EAAE,EAAE;MACvCmB,KAAK,EAAEY,WAAW,KAAK8B,SAAS,GAAG9B,WAAW,GAAGD,UAAU;MAC3DoC,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;QACpC,OAAOA,MAAM,IAAI,EAAE;MACrB;IACF,CAAC,CAAC;IACEC,gBAAgB,GAAG1E,cAAc,CAACuE,eAAe,EAAE,CAAC,CAAC;IACrDI,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG1C,IAAIG,aAAa,GAAGjE,UAAU,CAACqC,OAAO,EAAEC,QAAQ,EAAEkB,gBAAgB,EAAErB,gBAAgB,EAAEC,eAAe,CAAC;EACtG,IAAI8B,YAAY,GAAGD,aAAa,CAACC,YAAY;IACzCC,YAAY,GAAGF,aAAa,CAACE,YAAY;IACzCC,aAAa,GAAGH,aAAa,CAAC5B,OAAO,CAAC,CAAC;;EAE3C,IAAIgC,mBAAmB,GAAG7E,KAAK,CAAC8E,WAAW,CAAC,UAAUC,WAAW,EAAE;IACjE;IACA,IAAIC,SAAS,GAAGhE,OAAO,CAAC+D,WAAW,CAAC,CAAC,CAAC;;IAEtC,OAAOC,SAAS,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIC,QAAQ;MACZ,IAAIC,QAAQ;MACZ,IAAIC,MAAM;MACV,IAAIC,WAAW,CAAC,CAAC;;MAEjB,IAAIlE,UAAU,CAAC8D,GAAG,CAAC,EAAE;QACnBC,QAAQ,GAAGD,GAAG;MAChB,CAAC,MAAM;QACL,IAAIK,UAAU;QAEdF,MAAM,GAAGH,GAAG,CAACM,GAAG;QAChBJ,QAAQ,GAAGF,GAAG,CAACO,KAAK;QACpBN,QAAQ,GAAG,CAACI,UAAU,GAAGL,GAAG,CAAC7D,KAAK,MAAM,IAAI,IAAIkE,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGF,MAAM;MAC7F;MAEA,IAAIK,MAAM,GAAGhB,YAAY,CAACiB,GAAG,CAACR,QAAQ,CAAC;MAEvC,IAAIO,MAAM,EAAE;QACV,IAAIE,WAAW;;QAEf;QACA,IAAIR,QAAQ,KAAKrB,SAAS,EAAEqB,QAAQ,GAAGM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC9C,eAAe,IAAIoB,gBAAgB,CAACyB,KAAK,CAAC;QACxI,IAAIJ,MAAM,KAAKtB,SAAS,EAAEsB,MAAM,GAAG,CAACO,WAAW,GAAGF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAII,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGT,QAAQ;QACzKG,WAAW,GAAGI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,QAAQ,CAAC,CAAC;;QAE/E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACpD,eAAe,EAAE;UAC7D,IAAIqD,WAAW,GAAGP,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC1B,gBAAgB,CAACyB,KAAK,CAAC;UAEhG,IAAIQ,WAAW,KAAKlC,SAAS,IAAIkC,WAAW,KAAKb,QAAQ,EAAE;YACzDnF,OAAO,CAAC,KAAK,EAAE,8DAA8D,CAAC;UAChF;QACF;MACF;MAEA,OAAO;QACLwF,KAAK,EAAEL,QAAQ;QACf/D,KAAK,EAAE8D,QAAQ;QACfK,GAAG,EAAEH,MAAM;QACXQ,QAAQ,EAAEP;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,gBAAgB,EAAEpB,eAAe,EAAE8B,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEvD,IAAIwB,gBAAgB,GAAGhG,cAAc,CAACoD,YAAY,EAAE;MAClDjC,KAAK,EAAEA;IACT,CAAC,CAAC;IACE8E,gBAAgB,GAAGvG,cAAc,CAACsG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,gBAAgB,GAAGtG,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,IAAIyC,QAAQ;IAEZ,IAAIC,MAAM,GAAG3B,mBAAmB,CAACuB,aAAa,CAAC,CAAC,CAAC;;IAEjD,IAAIzE,IAAI,KAAK,UAAU,IAAI,EAAE,CAAC4E,QAAQ,GAAGC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAClF,KAAK,CAAC,EAAE;MAC9G,OAAO,EAAE;IACX;IAEA,OAAOmF,MAAM;EACf,CAAC,EAAE,CAACJ,aAAa,EAAEvB,mBAAmB,EAAElD,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAI8E,SAAS,GAAGvF,QAAQ,CAACoF,gBAAgB,EAAE5B,YAAY,CAAC;IACpDgC,UAAU,GAAG9G,cAAc,CAAC6G,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAElC,IAAIG,aAAa,GAAG7G,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC5C;IACA;IACA,IAAI,CAACnC,IAAI,IAAIgF,YAAY,CAACG,MAAM,KAAK,CAAC,EAAE;MACtC,IAAIC,UAAU,GAAGJ,YAAY,CAAC,CAAC,CAAC;MAEhC,IAAII,UAAU,CAAC1F,KAAK,KAAK,IAAI,KAAK0F,UAAU,CAACtB,KAAK,KAAK,IAAI,IAAIsB,UAAU,CAACtB,KAAK,KAAK1B,SAAS,CAAC,EAAE;QAC9F,OAAO,EAAE;MACX;IACF;IAEA,OAAO4C,YAAY,CAAC1B,GAAG,CAAC,UAAU+B,IAAI,EAAE;MACtC,IAAIC,WAAW;MAEf,OAAOtH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqH,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDvB,KAAK,EAAE,CAACwB,WAAW,GAAGD,IAAI,CAACvB,KAAK,MAAM,IAAI,IAAIwB,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGD,IAAI,CAAC3F;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACM,IAAI,EAAEgF,YAAY,CAAC,CAAC;EACxB;;EAEA,IAAIO,SAAS,GAAGlH,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACxC,OAAO,IAAIqD,GAAG,CAACR,YAAY,CAAC1B,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC7C,OAAOA,GAAG,CAAC7D,KAAK;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACsF,YAAY,CAAC,CAAC;EAClB3G,KAAK,CAACoH,SAAS,CAAC,YAAY;IAC1B,IAAIzF,IAAI,KAAK,UAAU,EAAE;MACvB,IAAI0F,cAAc;MAElB,IAAIC,QAAQ,GAAG,CAACD,cAAc,GAAGV,YAAY,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIU,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAChG,KAAK;MAEvH,IAAIiG,QAAQ,KAAKvD,SAAS,IAAIuD,QAAQ,KAAK,IAAI,EAAE;QAC/C9C,cAAc,CAAC+C,MAAM,CAACD,QAAQ,CAAC,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,IAAIa,eAAe,GAAG7G,UAAU,CAAC,UAAUuE,GAAG,EAAEO,KAAK,EAAE;IACrD,IAAIgC,IAAI;IAER,IAAIC,WAAW,GAAGjC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGP,GAAG;IAClE,OAAOuC,IAAI,GAAG,CAAC,CAAC,EAAE/H,eAAe,CAAC+H,IAAI,EAAEzD,gBAAgB,CAAC3C,KAAK,EAAE6D,GAAG,CAAC,EAAExF,eAAe,CAAC+H,IAAI,EAAEzD,gBAAgB,CAACyB,KAAK,EAAEiC,WAAW,CAAC,EAAED,IAAI;EACxI,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIE,gBAAgB,GAAG3H,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC/C,IAAInC,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOiD,aAAa;IACtB,CAAC,CAAC;;IAGF,IAAIgD,YAAY,GAAGnI,kBAAkB,CAACmF,aAAa,CAAC,CAAC,CAAC;;IAGtD,IAAIiD,YAAY,GAAG,SAASA,YAAYA,CAAC3C,GAAG,EAAE;MAC5C,OAAOR,YAAY,CAACoD,GAAG,CAAC5C,GAAG,CAAC;IAC9B,CAAC,CAAC,CAAC;;IAGHzF,kBAAkB,CAACkH,YAAY,CAAC,CAACoB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpD,OAAOD,CAAC,CAAC3G,KAAK,GAAG4G,CAAC,CAAC5G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC,CAAC6G,OAAO,CAAC,UAAUlB,IAAI,EAAE;MACzB,IAAI9B,GAAG,GAAG8B,IAAI,CAAC3F,KAAK;MAEpB,IAAI,CAACwG,YAAY,CAAC3C,GAAG,CAAC,EAAE;QACtB0C,YAAY,CAACO,IAAI,CAACX,eAAe,CAACtC,GAAG,EAAE8B,IAAI,CAACvB,KAAK,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IAEF,OAAOmC,YAAY;EACrB,CAAC,EAAE,CAACJ,eAAe,EAAE5C,aAAa,EAAEF,YAAY,EAAEiC,YAAY,EAAEhF,IAAI,CAAC,CAAC;EACtE,IAAIyG,eAAe,GAAGnH,gBAAgB,CAAC0G,gBAAgB,EAAE3D,gBAAgB,EAAEO,iBAAiB,EAAEV,kBAAkB,EAAElB,gBAAgB,CAAC,CAAC,CAAC;;EAErI,IAAI0F,mBAAmB,GAAGrI,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAClD,IAAInC,IAAI,KAAK,MAAM,IAAI,CAAC4C,iBAAiB,IAAI6D,eAAe,CAACE,IAAI,CAAC,UAAUtB,IAAI,EAAE;MAChF,OAAOA,IAAI,CAACrE,gBAAgB,IAAI,OAAO,CAAC,KAAK4B,iBAAiB;IAChE,CAAC,CAAC,EAAE;MACF,OAAO6D,eAAe;IACxB,CAAC,CAAC;;IAGF,OAAO,CAACZ,eAAe,CAACjD,iBAAiB,CAAC,CAAC,CAACgE,MAAM,CAAC9I,kBAAkB,CAAC2I,eAAe,CAAC,CAAC;EACzF,CAAC,EAAE,CAACZ,eAAe,EAAE7E,gBAAgB,EAAEhB,IAAI,EAAEyG,eAAe,EAAE7D,iBAAiB,CAAC,CAAC;EACjF,IAAIiE,sBAAsB,GAAGxI,KAAK,CAAC8D,OAAO,CAAC,YAAY;IACrD,IAAI,CAACpB,UAAU,EAAE;MACf,OAAO2F,mBAAmB;IAC5B;IAEA,OAAO5I,kBAAkB,CAAC4I,mBAAmB,CAAC,CAACN,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAClE,OAAOvF,UAAU,CAACsF,CAAC,EAAEC,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACI,mBAAmB,EAAE3F,UAAU,CAAC,CAAC;EACrC,IAAI+F,cAAc,GAAGzI,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC7C,OAAOjD,cAAc,CAAC2H,sBAAsB,EAAE;MAC5CzG,UAAU,EAAEiC,gBAAgB;MAC5BJ,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC4E,sBAAsB,EAAExE,gBAAgB,EAAEJ,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEhE,IAAI8E,aAAa,GAAG,SAASA,aAAaA,CAAClC,MAAM,EAAE;IACjD,IAAImC,aAAa,GAAG9D,mBAAmB,CAAC2B,MAAM,CAAC;IAC/CH,gBAAgB,CAACsC,aAAa,CAAC;IAE/B,IAAInF,QAAQ;IAAM;IAClBmF,aAAa,CAAC7B,MAAM,KAAKH,YAAY,CAACG,MAAM,IAAI6B,aAAa,CAACL,IAAI,CAAC,UAAUM,MAAM,EAAEC,KAAK,EAAE;MAC1F,IAAIC,mBAAmB;MAEvB,OAAO,CAAC,CAACA,mBAAmB,GAAGnC,YAAY,CAACkC,KAAK,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACzH,KAAK,OAAOuH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvH,KAAK,CAAC;IACzM,CAAC,CAAC,CAAC,EAAE;MACH,IAAI0H,YAAY,GAAGxF,YAAY,GAAGoF,aAAa,GAAGA,aAAa,CAAC1D,GAAG,CAAC,UAAU+D,CAAC,EAAE;QAC/E,OAAOA,CAAC,CAAC3H,KAAK;MAChB,CAAC,CAAC;MACF,IAAI4H,aAAa,GAAGN,aAAa,CAAC1D,GAAG,CAAC,UAAU+D,CAAC,EAAE;QACjD,OAAOlI,qBAAqB,CAAC8F,cAAc,CAACoC,CAAC,CAAC3H,KAAK,CAAC,CAAC;MACvD,CAAC,CAAC;MACFmC,QAAQ;MAAE;MACVG,QAAQ,GAAGoF,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC;MAAE;MAC3CpF,QAAQ,GAAGsF,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,eAAe,GAAGlJ,KAAK,CAACmJ,QAAQ,CAAC,IAAI,CAAC;IACtCC,gBAAgB,GAAGxJ,cAAc,CAACsJ,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,gBAAgB,GAAGvJ,KAAK,CAACmJ,QAAQ,CAAC,CAAC,CAAC;IACpCK,gBAAgB,GAAG5J,cAAc,CAAC2J,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE/C,IAAIG,8BAA8B,GAAG5G,wBAAwB,KAAKgB,SAAS,GAAGhB,wBAAwB,GAAGpB,IAAI,KAAK,UAAU;EAC5H,IAAIiI,aAAa,GAAG5J,KAAK,CAAC8E,WAAW,CAAC,UAAU+E,MAAM,EAAEhB,KAAK,EAAE;IAC7D,IAAIiB,KAAK,GAAGC,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKhG,SAAS,GAAGgG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9EC,YAAY,GAAGF,KAAK,CAACG,MAAM;MAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,YAAY;IAEhEN,qBAAqB,CAACb,KAAK,CAAC;IAE5B,IAAI/G,QAAQ,IAAIH,IAAI,KAAK,UAAU,IAAIkI,MAAM,KAAK,IAAI,IAAII,MAAM,KAAK,UAAU,EAAE;MAC/EX,cAAc,CAAC/B,MAAM,CAACsC,MAAM,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAAC/H,QAAQ,EAAEH,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEtB,IAAIuI,aAAa,GAAG,SAASA,aAAaA,CAAChF,GAAG,EAAEiF,QAAQ,EAAE;IACxD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;MACzC,IAAIC,YAAY;MAEhB,IAAI3E,MAAM,GAAGkB,cAAc,CAAC1B,GAAG,CAAC;MAChC,OAAO,CAAC3B,YAAY,GAAG;QACrBkC,KAAK,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC1B,gBAAgB,CAACyB,KAAK,CAAC;QACrFpE,KAAK,EAAE6D,GAAG;QACVM,GAAG,EAAE,CAAC6E,YAAY,GAAG3E,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAI6E,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGnF;MACxI,CAAC,GAAGA,GAAG,EAAEpE,qBAAqB,CAAC4E,MAAM,CAAC,CAAC;IACzC,CAAC;IAED,IAAIyE,QAAQ,IAAI9H,QAAQ,EAAE;MACxB,IAAIiI,aAAa,GAAGF,YAAY,CAAC,CAAC;QAC9BG,cAAc,GAAG3K,cAAc,CAAC0K,aAAa,EAAE,CAAC,CAAC;QACjDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;QAChC7E,MAAM,GAAG6E,cAAc,CAAC,CAAC,CAAC;MAE9BlI,QAAQ,CAACmI,YAAY,EAAE9E,MAAM,CAAC;IAChC,CAAC,MAAM,IAAI,CAACyE,QAAQ,IAAI7H,UAAU,EAAE;MAClC,IAAImI,cAAc,GAAGL,YAAY,CAAC,CAAC;QAC/BM,cAAc,GAAG9K,cAAc,CAAC6K,cAAc,EAAE,CAAC,CAAC;QAClDE,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;QACjCE,OAAO,GAAGF,cAAc,CAAC,CAAC,CAAC;MAE/BpI,UAAU,CAACqI,aAAa,EAAEC,OAAO,CAAC;IACpC;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,gBAAgB,GAAGlK,UAAU,CAAC,UAAUuE,GAAG,EAAE4F,IAAI,EAAE;IACrD,IAAIC,WAAW,CAAC,CAAC;;IAEjB,IAAIC,YAAY,GAAGrH,QAAQ,GAAGmH,IAAI,CAACX,QAAQ,GAAG,IAAI;IAElD,IAAIa,YAAY,EAAE;MAChBD,WAAW,GAAGpH,QAAQ,GAAG,EAAE,CAAC4E,MAAM,CAAC9I,kBAAkB,CAACkH,YAAY,CAAC,EAAE,CAACzB,GAAG,CAAC,CAAC,GAAG,CAACA,GAAG,CAAC;IACrF,CAAC,MAAM;MACL6F,WAAW,GAAGpE,YAAY,CAACsE,MAAM,CAAC,UAAUjC,CAAC,EAAE;QAC7C,OAAOA,CAAC,CAAC3H,KAAK,KAAK6D,GAAG;MACxB,CAAC,CAAC;IACJ;IAEAwD,aAAa,CAACqC,WAAW,CAAC;IAC1Bb,aAAa,CAAChF,GAAG,EAAE8F,YAAY,CAAC,CAAC,CAAC;;IAElC,IAAIrJ,IAAI,KAAK,UAAU,EAAE;MACvB;MACA2H,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,MAAM,IAAI,CAAClJ,UAAU,IAAIgC,oBAAoB,EAAE;MAC9CoC,cAAc,CAAC,EAAE,CAAC;MAClB8E,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEA,IAAI4B,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,UAAU,EAAEL,IAAI,EAAE;IAC3EpC,aAAa,CAACyC,UAAU,CAAC;IAEzB,IAAIL,IAAI,CAACM,IAAI,KAAK,QAAQ,IAAIN,IAAI,CAACM,IAAI,KAAK,OAAO,EAAE;MACnDN,IAAI,CAACtE,MAAM,CAAC0B,OAAO,CAAC,UAAUlB,IAAI,EAAE;QAClCkD,aAAa,CAAClD,IAAI,CAAC3F,KAAK,EAAE,KAAK,CAAC;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIgK,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAER,IAAI,EAAE;IACjEtG,cAAc,CAAC8G,UAAU,CAAC;IAC1BhC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEtB,IAAIwB,IAAI,CAACb,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIsB,SAAS,GAAG,CAACD,UAAU,IAAI,EAAE,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC;;MAE3C,IAAID,SAAS,EAAE;QACb,IAAIE,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIxE,GAAG,CAAC,EAAE,CAACoB,MAAM,CAAC9I,kBAAkB,CAACyH,SAAS,CAAC,EAAE,CAACqE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7F7C,aAAa,CAAC+C,YAAY,CAAC;QAC3BvB,aAAa,CAACqB,SAAS,EAAE,IAAI,CAAC;QAC9B/G,cAAc,CAAC,EAAE,CAAC;MACpB;MAEA;IACF;IAEA,IAAIsG,IAAI,CAACb,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAItI,IAAI,KAAK,UAAU,EAAE;QACvB+G,aAAa,CAAC4C,UAAU,CAAC;MAC3B;MAEApJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACoJ,UAAU,CAAC;IAC1E;EACF,CAAC;EAED,IAAIM,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAE;IAChE,IAAIC,WAAW,GAAGD,KAAK;IAEvB,IAAIlK,IAAI,KAAK,MAAM,EAAE;MACnBmK,WAAW,GAAGD,KAAK,CAAC5G,GAAG,CAAC,UAAU8G,IAAI,EAAE;QACtC,IAAIC,GAAG,GAAGrH,YAAY,CAACgB,GAAG,CAACoG,IAAI,CAAC;QAChC,OAAOC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC3K,KAAK;MAC5D,CAAC,CAAC,CAAC4J,MAAM,CAAC,UAAU/F,GAAG,EAAE;QACvB,OAAOA,GAAG,KAAKnB,SAAS;MAC1B,CAAC,CAAC;IACJ;IAEA,IAAI0H,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIxE,GAAG,CAAC,EAAE,CAACoB,MAAM,CAAC9I,kBAAkB,CAACyH,SAAS,CAAC,EAAEzH,kBAAkB,CAACqM,WAAW,CAAC,CAAC,CAAC,CAAC;IACjHpD,aAAa,CAAC+C,YAAY,CAAC;IAC3BA,YAAY,CAACvD,OAAO,CAAC,UAAU+D,WAAW,EAAE;MAC1C/B,aAAa,CAAC+B,WAAW,EAAE,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGH,IAAIC,aAAa,GAAGlM,KAAK,CAAC8D,OAAO,CAAC,YAAY;IAC5C,IAAIqI,WAAW,GAAGlJ,OAAO,KAAK,KAAK,IAAIT,wBAAwB,KAAK,KAAK;IACzE,OAAO7C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8E,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzD5D,cAAc,EAAE4H,cAAc;MAC9BmB,aAAa,EAAEA,aAAa;MAC5B7G,wBAAwB,EAAE4G,8BAA8B;MACxDtH,QAAQ,EAAEwI,gBAAgB;MAC1B7H,oBAAoB,EAAEA,oBAAoB;MAC1CkE,SAAS,EAAEA,SAAS;MACpBnF,UAAU,EAAEiC,gBAAgB;MAC5Bf,OAAO,EAAEkJ,WAAW;MACpBhJ,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9BO,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,aAAa,EAAEgE,cAAc,EAAEmB,aAAa,EAAED,8BAA8B,EAAEkB,gBAAgB,EAAE7H,oBAAoB,EAAEkE,SAAS,EAAElD,gBAAgB,EAAEf,OAAO,EAAET,wBAAwB,EAAEW,UAAU,EAAEE,cAAc,EAAEO,cAAc,CAAC,CAAC,CAAC,CAAC;;EAExO,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjF,YAAY,CAACS,KAAK,CAAC;EACrB,CAAC,CAAC;EACF;EACA;;EAGA,OAAO,aAAaxB,KAAK,CAACoM,aAAa,CAAC3L,aAAa,CAAC4L,QAAQ,EAAE;IAC9DhL,KAAK,EAAE6K;EACT,CAAC,EAAE,aAAalM,KAAK,CAACoM,aAAa,CAACjM,UAAU,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEiE,SAAS,EAAE;IACtE;IACA/B,EAAE,EAAEgC,QAAQ;IACZ7B,SAAS,EAAEA,SAAS;IACpBJ,GAAG,EAAEA,GAAG;IACR6K,YAAY,EAAEnL,cAAc;IAC5BQ,IAAI,EAAEA,IAAI,CAAC;IAAA;;IAEXkF,aAAa,EAAEA,aAAa;IAC5BqE,qBAAqB,EAAEA,qBAAqB,CAAC;IAAA;;IAE7CjJ,WAAW,EAAEsC,iBAAiB;IAC9BrC,QAAQ,EAAEmJ,gBAAgB;IAC1BkB,aAAa,EAAEX,qBAAqB;IACpCpJ,wBAAwB,EAAEA,wBAAwB,CAAC;IAAA;;IAEnDnC,UAAU,EAAEA,UAAU;IACtBmM,YAAY,EAAE,CAAC/D,cAAc,CAAC3B,MAAM,CAAC;IAAA;;IAErCuC,WAAW,EAAEA,WAAW;IACxBoD,kBAAkB,EAAE,EAAE,CAAClE,MAAM,CAAC7E,QAAQ,EAAE,QAAQ,CAAC,CAAC6E,MAAM,CAACkB,kBAAkB;EAC7E,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,IAAI3D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC1E,MAAM,CAACoL,WAAW,GAAG,QAAQ;AAC/B;AAEA,IAAIC,WAAW,GAAGrL,MAAM;AACxBqL,WAAW,CAACrM,MAAM,GAAGA,MAAM;AAC3BqM,WAAW,CAACpM,QAAQ,GAAGA,QAAQ;AAC/B,eAAeoM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}