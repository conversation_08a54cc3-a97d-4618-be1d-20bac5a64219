{"ast": null, "code": "var global = require('../internals/global');\nvar TypeError = global.TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};", "map": {"version": 3, "names": ["global", "require", "TypeError", "module", "exports", "it", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/require-object-coercible.js"], "sourcesContent": ["var global = require('../internals/global');\n\nvar TypeError = global.TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE3C,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS;;AAEhC;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,IAAIA,EAAE,IAAIC,SAAS,EAAE,MAAMJ,SAAS,CAAC,uBAAuB,GAAGG,EAAE,CAAC;EAClE,OAAOA,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}