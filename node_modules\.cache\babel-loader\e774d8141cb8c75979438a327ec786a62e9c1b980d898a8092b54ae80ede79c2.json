{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React, { cloneElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { hasAddon, hasPrefixSuffix } from './utils/commonUtils';\nvar BaseInput = function BaseInput(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    addonBefore = props.addonBefore,\n    addonAfter = props.addonAfter,\n    className = props.className,\n    style = props.style,\n    affixWrapperClassName = props.affixWrapperClassName,\n    groupClassName = props.groupClassName,\n    wrapperClassName = props.wrapperClassName,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    focused = props.focused,\n    triggerFocus = props.triggerFocus,\n    allowClear = props.allowClear,\n    value = props.value,\n    handleReset = props.handleReset,\n    hidden = props.hidden;\n  var containerRef = useRef(null);\n  var onInputMouseDown = function onInputMouseDown(e) {\n    var _containerRef$current;\n    if ((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.contains(e.target)) {\n      triggerFocus === null || triggerFocus === void 0 ? void 0 : triggerFocus();\n    }\n  }; // ================== Clear Icon ================== //\n\n  var getClearIcon = function getClearIcon() {\n    var _classNames;\n    if (!allowClear) {\n      return null;\n    }\n    var needClear = !disabled && !readOnly && value;\n    var clearIconCls = \"\".concat(prefixCls, \"-clear-icon\");\n    var iconNode = _typeof(allowClear) === 'object' && (allowClear === null || allowClear === void 0 ? void 0 : allowClear.clearIcon) ? allowClear.clearIcon : '✖';\n    return /*#__PURE__*/React.createElement(\"span\", {\n      onClick: handleReset,\n      // Do not trigger onBlur when clear input\n      // https://github.com/ant-design/ant-design/issues/31200\n      onMouseDown: function onMouseDown(e) {\n        return e.preventDefault();\n      },\n      className: classNames(clearIconCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(clearIconCls, \"-hidden\"), !needClear), _defineProperty(_classNames, \"\".concat(clearIconCls, \"-has-suffix\"), !!suffix), _classNames)),\n      role: \"button\",\n      tabIndex: -1\n    }, iconNode);\n  };\n  var element = /*#__PURE__*/cloneElement(inputElement, {\n    value: value,\n    hidden: hidden\n  }); // ================== Prefix & Suffix ================== //\n\n  if (hasPrefixSuffix(props)) {\n    var _classNames2;\n    var affixWrapperPrefixCls = \"\".concat(prefixCls, \"-affix-wrapper\");\n    var affixWrapperCls = classNames(affixWrapperPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-focused\"), focused), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-input-with-clear-btn\"), suffix && allowClear && value), _classNames2), !hasAddon(props) && className, affixWrapperClassName);\n    var suffixNode = (suffix || allowClear) && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-suffix\")\n    }, getClearIcon(), suffix);\n    element = /*#__PURE__*/React.createElement(\"span\", {\n      className: affixWrapperCls,\n      style: style,\n      hidden: !hasAddon(props) && hidden,\n      onMouseDown: onInputMouseDown,\n      ref: containerRef\n    }, prefix && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-prefix\")\n    }, prefix), /*#__PURE__*/cloneElement(inputElement, {\n      style: null,\n      value: value,\n      hidden: null\n    }), suffixNode);\n  } // ================== Addon ================== //\n\n  if (hasAddon(props)) {\n    var wrapperCls = \"\".concat(prefixCls, \"-group\");\n    var addonCls = \"\".concat(wrapperCls, \"-addon\");\n    var mergedWrapperClassName = classNames(\"\".concat(prefixCls, \"-wrapper\"), wrapperCls, wrapperClassName);\n    var mergedGroupClassName = classNames(\"\".concat(prefixCls, \"-group-wrapper\"), className, groupClassName); // Need another wrapper for changing display:table to display:inline-block\n    // and put style prop in wrapper\n\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: mergedGroupClassName,\n      style: style,\n      hidden: hidden\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: mergedWrapperClassName\n    }, addonBefore && /*#__PURE__*/React.createElement(\"span\", {\n      className: addonCls\n    }, addonBefore), /*#__PURE__*/cloneElement(element, {\n      style: null,\n      hidden: null\n    }), addonAfter && /*#__PURE__*/React.createElement(\"span\", {\n      className: addonCls\n    }, addonAfter)));\n  }\n  return element;\n};\nexport default BaseInput;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "React", "cloneElement", "useRef", "classNames", "hasAddon", "hasPrefixSuffix", "BaseInput", "props", "inputElement", "prefixCls", "prefix", "suffix", "addonBefore", "addonAfter", "className", "style", "affixWrapperClassName", "groupClassName", "wrapperClassName", "disabled", "readOnly", "focused", "triggerFocus", "allowClear", "value", "handleReset", "hidden", "containerRef", "onInputMouseDown", "e", "_containerRef$current", "current", "contains", "target", "getClearIcon", "_classNames", "needClear", "clearIconCls", "concat", "iconNode", "clearIcon", "createElement", "onClick", "onMouseDown", "preventDefault", "role", "tabIndex", "element", "_classNames2", "affixWrapperPrefixCls", "affixWrapperCls", "suffixNode", "ref", "wrapperCls", "addonCls", "mergedWrapperClassName", "mergedGroupClassName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input/es/BaseInput.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React, { cloneElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { hasAddon, hasPrefixSuffix } from './utils/commonUtils';\n\nvar BaseInput = function BaseInput(props) {\n  var inputElement = props.inputElement,\n      prefixCls = props.prefixCls,\n      prefix = props.prefix,\n      suffix = props.suffix,\n      addonBefore = props.addonBefore,\n      addonAfter = props.addonAfter,\n      className = props.className,\n      style = props.style,\n      affixWrapperClassName = props.affixWrapperClassName,\n      groupClassName = props.groupClassName,\n      wrapperClassName = props.wrapperClassName,\n      disabled = props.disabled,\n      readOnly = props.readOnly,\n      focused = props.focused,\n      triggerFocus = props.triggerFocus,\n      allowClear = props.allowClear,\n      value = props.value,\n      handleReset = props.handleReset,\n      hidden = props.hidden;\n  var containerRef = useRef(null);\n\n  var onInputMouseDown = function onInputMouseDown(e) {\n    var _containerRef$current;\n\n    if ((_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.contains(e.target)) {\n      triggerFocus === null || triggerFocus === void 0 ? void 0 : triggerFocus();\n    }\n  }; // ================== Clear Icon ================== //\n\n\n  var getClearIcon = function getClearIcon() {\n    var _classNames;\n\n    if (!allowClear) {\n      return null;\n    }\n\n    var needClear = !disabled && !readOnly && value;\n    var clearIconCls = \"\".concat(prefixCls, \"-clear-icon\");\n    var iconNode = _typeof(allowClear) === 'object' && (allowClear === null || allowClear === void 0 ? void 0 : allowClear.clearIcon) ? allowClear.clearIcon : '✖';\n    return /*#__PURE__*/React.createElement(\"span\", {\n      onClick: handleReset,\n      // Do not trigger onBlur when clear input\n      // https://github.com/ant-design/ant-design/issues/31200\n      onMouseDown: function onMouseDown(e) {\n        return e.preventDefault();\n      },\n      className: classNames(clearIconCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(clearIconCls, \"-hidden\"), !needClear), _defineProperty(_classNames, \"\".concat(clearIconCls, \"-has-suffix\"), !!suffix), _classNames)),\n      role: \"button\",\n      tabIndex: -1\n    }, iconNode);\n  };\n\n  var element = /*#__PURE__*/cloneElement(inputElement, {\n    value: value,\n    hidden: hidden\n  }); // ================== Prefix & Suffix ================== //\n\n  if (hasPrefixSuffix(props)) {\n    var _classNames2;\n\n    var affixWrapperPrefixCls = \"\".concat(prefixCls, \"-affix-wrapper\");\n    var affixWrapperCls = classNames(affixWrapperPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-focused\"), focused), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames2, \"\".concat(affixWrapperPrefixCls, \"-input-with-clear-btn\"), suffix && allowClear && value), _classNames2), !hasAddon(props) && className, affixWrapperClassName);\n    var suffixNode = (suffix || allowClear) && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-suffix\")\n    }, getClearIcon(), suffix);\n    element = /*#__PURE__*/React.createElement(\"span\", {\n      className: affixWrapperCls,\n      style: style,\n      hidden: !hasAddon(props) && hidden,\n      onMouseDown: onInputMouseDown,\n      ref: containerRef\n    }, prefix && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-prefix\")\n    }, prefix), /*#__PURE__*/cloneElement(inputElement, {\n      style: null,\n      value: value,\n      hidden: null\n    }), suffixNode);\n  } // ================== Addon ================== //\n\n\n  if (hasAddon(props)) {\n    var wrapperCls = \"\".concat(prefixCls, \"-group\");\n    var addonCls = \"\".concat(wrapperCls, \"-addon\");\n    var mergedWrapperClassName = classNames(\"\".concat(prefixCls, \"-wrapper\"), wrapperCls, wrapperClassName);\n    var mergedGroupClassName = classNames(\"\".concat(prefixCls, \"-group-wrapper\"), className, groupClassName); // Need another wrapper for changing display:table to display:inline-block\n    // and put style prop in wrapper\n\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: mergedGroupClassName,\n      style: style,\n      hidden: hidden\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: mergedWrapperClassName\n    }, addonBefore && /*#__PURE__*/React.createElement(\"span\", {\n      className: addonCls\n    }, addonBefore), /*#__PURE__*/cloneElement(element, {\n      style: null,\n      hidden: null\n    }), addonAfter && /*#__PURE__*/React.createElement(\"span\", {\n      className: addonCls\n    }, addonAfter)));\n  }\n\n  return element;\n};\n\nexport default BaseInput;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,IAAIC,YAAY,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,eAAe,QAAQ,qBAAqB;AAE/D,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACjCC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,qBAAqB,GAAGT,KAAK,CAACS,qBAAqB;IACnDC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,gBAAgB,GAAGX,KAAK,CAACW,gBAAgB;IACzCC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,KAAK,GAAGjB,KAAK,CAACiB,KAAK;IACnBC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAC/BC,MAAM,GAAGnB,KAAK,CAACmB,MAAM;EACzB,IAAIC,YAAY,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAE/B,IAAI0B,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,CAAC,EAAE;IAClD,IAAIC,qBAAqB;IAEzB,IAAI,CAACA,qBAAqB,GAAGH,YAAY,CAACI,OAAO,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,QAAQ,CAACH,CAAC,CAACI,MAAM,CAAC,EAAE;MACnJX,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC;IAC5E;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIY,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIC,WAAW;IAEf,IAAI,CAACZ,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IAEA,IAAIa,SAAS,GAAG,CAACjB,QAAQ,IAAI,CAACC,QAAQ,IAAII,KAAK;IAC/C,IAAIa,YAAY,GAAG,EAAE,CAACC,MAAM,CAAC7B,SAAS,EAAE,aAAa,CAAC;IACtD,IAAI8B,QAAQ,GAAGxC,OAAO,CAACwB,UAAU,CAAC,KAAK,QAAQ,KAAKA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACiB,SAAS,CAAC,GAAGjB,UAAU,CAACiB,SAAS,GAAG,GAAG;IAC9J,OAAO,aAAaxC,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MAC9CC,OAAO,EAAEjB,WAAW;MACpB;MACA;MACAkB,WAAW,EAAE,SAASA,WAAWA,CAACd,CAAC,EAAE;QACnC,OAAOA,CAAC,CAACe,cAAc,CAAC,CAAC;MAC3B,CAAC;MACD9B,SAAS,EAAEX,UAAU,CAACkC,YAAY,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAErC,eAAe,CAACqC,WAAW,EAAE,EAAE,CAACG,MAAM,CAACD,YAAY,EAAE,SAAS,CAAC,EAAE,CAACD,SAAS,CAAC,EAAEtC,eAAe,CAACqC,WAAW,EAAE,EAAE,CAACG,MAAM,CAACD,YAAY,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC1B,MAAM,CAAC,EAAEwB,WAAW,CAAC,CAAC;MAClOU,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,CAAC;IACb,CAAC,EAAEP,QAAQ,CAAC;EACd,CAAC;EAED,IAAIQ,OAAO,GAAG,aAAa9C,YAAY,CAACO,YAAY,EAAE;IACpDgB,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIrB,eAAe,CAACE,KAAK,CAAC,EAAE;IAC1B,IAAIyC,YAAY;IAEhB,IAAIC,qBAAqB,GAAG,EAAE,CAACX,MAAM,CAAC7B,SAAS,EAAE,gBAAgB,CAAC;IAClE,IAAIyC,eAAe,GAAG/C,UAAU,CAAC8C,qBAAqB,GAAGD,YAAY,GAAG,CAAC,CAAC,EAAElD,eAAe,CAACkD,YAAY,EAAE,EAAE,CAACV,MAAM,CAACW,qBAAqB,EAAE,WAAW,CAAC,EAAE9B,QAAQ,CAAC,EAAErB,eAAe,CAACkD,YAAY,EAAE,EAAE,CAACV,MAAM,CAACW,qBAAqB,EAAE,UAAU,CAAC,EAAE5B,OAAO,CAAC,EAAEvB,eAAe,CAACkD,YAAY,EAAE,EAAE,CAACV,MAAM,CAACW,qBAAqB,EAAE,WAAW,CAAC,EAAE7B,QAAQ,CAAC,EAAEtB,eAAe,CAACkD,YAAY,EAAE,EAAE,CAACV,MAAM,CAACW,qBAAqB,EAAE,uBAAuB,CAAC,EAAEtC,MAAM,IAAIY,UAAU,IAAIC,KAAK,CAAC,EAAEwB,YAAY,GAAG,CAAC5C,QAAQ,CAACG,KAAK,CAAC,IAAIO,SAAS,EAAEE,qBAAqB,CAAC;IAC/gB,IAAImC,UAAU,GAAG,CAACxC,MAAM,IAAIY,UAAU,KAAK,aAAavB,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MAClF3B,SAAS,EAAE,EAAE,CAACwB,MAAM,CAAC7B,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAEyB,YAAY,CAAC,CAAC,EAAEvB,MAAM,CAAC;IAC1BoC,OAAO,GAAG,aAAa/C,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MACjD3B,SAAS,EAAEoC,eAAe;MAC1BnC,KAAK,EAAEA,KAAK;MACZW,MAAM,EAAE,CAACtB,QAAQ,CAACG,KAAK,CAAC,IAAImB,MAAM;MAClCiB,WAAW,EAAEf,gBAAgB;MAC7BwB,GAAG,EAAEzB;IACP,CAAC,EAAEjB,MAAM,IAAI,aAAaV,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MACpD3B,SAAS,EAAE,EAAE,CAACwB,MAAM,CAAC7B,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAEC,MAAM,CAAC,EAAE,aAAaT,YAAY,CAACO,YAAY,EAAE;MAClDO,KAAK,EAAE,IAAI;MACXS,KAAK,EAAEA,KAAK;MACZE,MAAM,EAAE;IACV,CAAC,CAAC,EAAEyB,UAAU,CAAC;EACjB,CAAC,CAAC;;EAGF,IAAI/C,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,IAAI8C,UAAU,GAAG,EAAE,CAACf,MAAM,CAAC7B,SAAS,EAAE,QAAQ,CAAC;IAC/C,IAAI6C,QAAQ,GAAG,EAAE,CAAChB,MAAM,CAACe,UAAU,EAAE,QAAQ,CAAC;IAC9C,IAAIE,sBAAsB,GAAGpD,UAAU,CAAC,EAAE,CAACmC,MAAM,CAAC7B,SAAS,EAAE,UAAU,CAAC,EAAE4C,UAAU,EAAEnC,gBAAgB,CAAC;IACvG,IAAIsC,oBAAoB,GAAGrD,UAAU,CAAC,EAAE,CAACmC,MAAM,CAAC7B,SAAS,EAAE,gBAAgB,CAAC,EAAEK,SAAS,EAAEG,cAAc,CAAC,CAAC,CAAC;IAC1G;;IAEA,OAAO,aAAajB,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MAC9C3B,SAAS,EAAE0C,oBAAoB;MAC/BzC,KAAK,EAAEA,KAAK;MACZW,MAAM,EAAEA;IACV,CAAC,EAAE,aAAa1B,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MAC1C3B,SAAS,EAAEyC;IACb,CAAC,EAAE3C,WAAW,IAAI,aAAaZ,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MACzD3B,SAAS,EAAEwC;IACb,CAAC,EAAE1C,WAAW,CAAC,EAAE,aAAaX,YAAY,CAAC8C,OAAO,EAAE;MAClDhC,KAAK,EAAE,IAAI;MACXW,MAAM,EAAE;IACV,CAAC,CAAC,EAAEb,UAAU,IAAI,aAAab,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MACzD3B,SAAS,EAAEwC;IACb,CAAC,EAAEzC,UAAU,CAAC,CAAC,CAAC;EAClB;EAEA,OAAOkC,OAAO;AAChB,CAAC;AAED,eAAezC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}