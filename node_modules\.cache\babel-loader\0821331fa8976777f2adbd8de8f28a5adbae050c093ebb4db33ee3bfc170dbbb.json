{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"controls\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport getMiniDecimal, { toFixed } from './utils/MiniDecimal';\nimport <PERSON><PERSON>and<PERSON> from './StepHandler';\nimport { getNumberPrecision, num2str, validateNumber } from './utils/numberUtil';\nimport useCursor from './hooks/useCursor';\nimport useUpdateEffect from './hooks/useUpdateEffect';\nimport useFrame from './hooks/useFrame';\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n */\n\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n  return decimalValue.toNumber();\n};\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    min = props.min,\n    max = props.max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    upHandler = props.upHandler,\n    downHandler = props.downHandler,\n    keyboard = props.keyboard,\n    _props$controls = props.controls,\n    controls = _props$controls === void 0 ? true : _props$controls,\n    stringMode = props.stringMode,\n    parser = props.parser,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    onPressEnter = props.onPressEnter,\n    onStep = props.onStep,\n    inputProps = _objectWithoutProperties(props, _excluded);\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focus = _React$useState2[0],\n    setFocus = _React$useState2[1];\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false); // ============================ Value =============================\n  // Real value control\n\n  var _React$useState3 = React.useState(function () {\n      return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    decimalValue = _React$useState4[0],\n    setDecimalValue = _React$useState4[1];\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  } // ====================== Parser & Formatter ======================\n\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n    if (precision >= 0) {\n      return precision;\n    }\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]); // >>> Parser\n\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n    if (parser) {\n      return parser(numStr);\n    }\n    var parsedStr = numStr;\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    } // [Legacy] We still support auto convert `$ 123,456` to `123456`\n\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]); // >>> Formatter\n\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n    var str = typeof number === 'number' ? num2str(number) : number; // User typing will not auto format with precision directly\n\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]); // ========================== InputValue ==========================\n\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It update with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n\n  var _React$useState5 = React.useState(function () {\n      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n      if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n        return Number.isNaN(initValue) ? '' : initValue;\n      }\n      return mergedFormatter(decimalValue.toString(), false);\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inputValue = _React$useState6[0],\n    setInternalInputValue = _React$useState6[1];\n  inputValueRef.current = inputValue; // Should always be string\n\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter(\n    // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  } // >>> Max & Min limit\n\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]); // Cursor controller\n\n  var _useCursor = useCursor(inputRef.current, focus),\n    _useCursor2 = _slicedToArray(_useCursor, 2),\n    recordCursor = _useCursor2[0],\n    restoreCursor = _useCursor2[1]; // ============================= Data =============================\n\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    } // target < min\n\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n    return null;\n  };\n  /**\n   * Check value is in [min, max] range\n   */\n\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty(); // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n      } // Trigger event\n\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 ? void 0 : onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue)); // Reformat input if value is not controlled\n\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n      return updateValue;\n    }\n    return decimalValue;\n  }; // ========================== User Input ==========================\n\n  var onNextPromise = useFrame(); // >>> Collect input value\n\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor(); // Update inputValue incase input can not parse as number\n\n    setInternalInputValue(inputStr); // Parse number\n\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    } // Trigger onInput later to let user customize value if they want do handle something after onChange\n\n    onInput === null || onInput === void 0 ? void 0 : onInput(inputStr); // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  }; // >>> Composition\n\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  }; // >>> Input\n\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  }; // ============================= Step =============================\n\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    } // Clear typing status since it may caused by up & down key.\n    // We should sync with input value.\n\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(step);\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 ? void 0 : onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  }; // ============================ Flush =============================\n\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed\n   */\n\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue = parsedValue;\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = decimalValue;\n    }\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var which = event.which;\n    userTypingRef.current = true;\n    if (which === KeyCode.ENTER) {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 ? void 0 : onPressEnter(event);\n    }\n    if (keyboard === false) {\n      return;\n    } // Do step\n\n    if (!compositionRef.current && [KeyCode.UP, KeyCode.DOWN].includes(which)) {\n      onInternalStep(KeyCode.UP === which);\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n  }; // >>> Focus & Blur\n\n  var onBlur = function onBlur() {\n    flushInputValue(false);\n    setFocus(false);\n    userTypingRef.current = false;\n  }; // ========================== Controlled ==========================\n  // Input by precision\n\n  useUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision]); // Input by value\n\n  useUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue)); // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]); // ============================ Cursor ============================\n\n  useUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames, \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), _defineProperty(_classNames, \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue)), _classNames)),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nInputNumber.displayName = 'InputNumber';\nexport default InputNumber;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "classNames", "KeyCode", "composeRef", "getMiniDecimal", "toFixed", "<PERSON><PERSON><PERSON><PERSON>", "getNumberPrecision", "num2str", "validateNumber", "useCursor", "useUpdateEffect", "useFrame", "getDecimalValue", "stringMode", "decimalValue", "isEmpty", "toString", "toNumber", "getDecimalIfValidate", "value", "decimal", "isInvalidate", "InputNumber", "forwardRef", "props", "ref", "_classNames", "_props$prefixCls", "prefixCls", "className", "style", "min", "max", "_props$step", "step", "defaultValue", "disabled", "readOnly", "up<PERSON><PERSON><PERSON>", "downHandler", "keyboard", "_props$controls", "controls", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep", "inputProps", "inputClassName", "concat", "inputRef", "useRef", "_React$useState", "useState", "_React$useState2", "focus", "setFocus", "userTypingRef", "compositionRef", "_React$useState3", "_React$useState4", "setDecimalValue", "setUncontrolledDecimalValue", "newDecimal", "undefined", "getPrecision", "useCallback", "numStr", "userTyping", "Math", "mergedParser", "num", "String", "parsedStr", "replace", "inputValueRef", "mergedFormatter", "number", "input", "current", "str", "mergedPrecision", "separatorStr", "_React$useState5", "initValue", "includes", "Number", "isNaN", "_React$useState6", "inputValue", "setInternalInputValue", "setInputValue", "newValue", "maxDecimal", "useMemo", "minDecimal", "upDisabled", "lessEquals", "downDisabled", "_useCursor", "_useCursor2", "recordCursor", "restoreCursor", "getRangeValue", "target", "isInRange", "triggerValueUpdate", "updateValue", "isRangeValidate", "equals", "onNextPromise", "collectInputValue", "inputStr", "finalValue", "finalDecimal", "nextInputStr", "onCompositionStart", "onCompositionEnd", "onInternalInput", "e", "onInternalStep", "up", "_inputRef$current", "stepDecimal", "negate", "add", "updatedValue", "offset", "type", "flushInputValue", "parsedValue", "formatValue", "onKeyDown", "event", "which", "ENTER", "UP", "DOWN", "preventDefault", "onKeyUp", "onBlur", "currentParsedValue", "createElement", "onFocus", "upNode", "downNode", "autoComplete", "role", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-input-number/es/InputNumber.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"controls\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport getMiniDecimal, { toFixed } from './utils/MiniDecimal';\nimport <PERSON><PERSON>and<PERSON> from './StepHandler';\nimport { getNumberPrecision, num2str, validateNumber } from './utils/numberUtil';\nimport useCursor from './hooks/useCursor';\nimport useUpdateEffect from './hooks/useUpdateEffect';\nimport useFrame from './hooks/useFrame';\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n */\n\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n\n  return decimalValue.toNumber();\n};\n\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\n\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n\n  var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n      className = props.className,\n      style = props.style,\n      min = props.min,\n      max = props.max,\n      _props$step = props.step,\n      step = _props$step === void 0 ? 1 : _props$step,\n      defaultValue = props.defaultValue,\n      value = props.value,\n      disabled = props.disabled,\n      readOnly = props.readOnly,\n      upHandler = props.upHandler,\n      downHandler = props.downHandler,\n      keyboard = props.keyboard,\n      _props$controls = props.controls,\n      controls = _props$controls === void 0 ? true : _props$controls,\n      stringMode = props.stringMode,\n      parser = props.parser,\n      formatter = props.formatter,\n      precision = props.precision,\n      decimalSeparator = props.decimalSeparator,\n      onChange = props.onChange,\n      onInput = props.onInput,\n      onPressEnter = props.onPressEnter,\n      onStep = props.onStep,\n      inputProps = _objectWithoutProperties(props, _excluded);\n\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      focus = _React$useState2[0],\n      setFocus = _React$useState2[1];\n\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false); // ============================ Value =============================\n  // Real value control\n\n  var _React$useState3 = React.useState(function () {\n    return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      decimalValue = _React$useState4[0],\n      setDecimalValue = _React$useState4[1];\n\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  } // ====================== Parser & Formatter ======================\n\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n\n\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n\n    if (precision >= 0) {\n      return precision;\n    }\n\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]); // >>> Parser\n\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n\n    if (parser) {\n      return parser(numStr);\n    }\n\n    var parsedStr = numStr;\n\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    } // [Legacy] We still support auto convert `$ 123,456` to `123456`\n\n\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]); // >>> Formatter\n\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n\n    var str = typeof number === 'number' ? num2str(number) : number; // User typing will not auto format with precision directly\n\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]); // ========================== InputValue ==========================\n\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It update with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n\n  var _React$useState5 = React.useState(function () {\n    var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n\n    if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n      return Number.isNaN(initValue) ? '' : initValue;\n    }\n\n    return mergedFormatter(decimalValue.toString(), false);\n  }),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      inputValue = _React$useState6[0],\n      setInternalInputValue = _React$useState6[1];\n\n  inputValueRef.current = inputValue; // Should always be string\n\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter( // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  } // >>> Max & Min limit\n\n\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]); // Cursor controller\n\n  var _useCursor = useCursor(inputRef.current, focus),\n      _useCursor2 = _slicedToArray(_useCursor, 2),\n      recordCursor = _useCursor2[0],\n      restoreCursor = _useCursor2[1]; // ============================= Data =============================\n\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n\n\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    } // target < min\n\n\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n\n    return null;\n  };\n  /**\n   * Check value is in [min, max] range\n   */\n\n\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n\n\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty(); // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n      } // Trigger event\n\n\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 ? void 0 : onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue)); // Reformat input if value is not controlled\n\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n\n      return updateValue;\n    }\n\n    return decimalValue;\n  }; // ========================== User Input ==========================\n\n\n  var onNextPromise = useFrame(); // >>> Collect input value\n\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor(); // Update inputValue incase input can not parse as number\n\n    setInternalInputValue(inputStr); // Parse number\n\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    } // Trigger onInput later to let user customize value if they want do handle something after onChange\n\n\n    onInput === null || onInput === void 0 ? void 0 : onInput(inputStr); // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  }; // >>> Composition\n\n\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  }; // >>> Input\n\n\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  }; // ============================= Step =============================\n\n\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    } // Clear typing status since it may caused by up & down key.\n    // We should sync with input value.\n\n\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(step);\n\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 ? void 0 : onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  }; // ============================ Flush =============================\n\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed\n   */\n\n\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue = parsedValue;\n\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = decimalValue;\n    }\n\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n\n  var onKeyDown = function onKeyDown(event) {\n    var which = event.which;\n    userTypingRef.current = true;\n\n    if (which === KeyCode.ENTER) {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 ? void 0 : onPressEnter(event);\n    }\n\n    if (keyboard === false) {\n      return;\n    } // Do step\n\n\n    if (!compositionRef.current && [KeyCode.UP, KeyCode.DOWN].includes(which)) {\n      onInternalStep(KeyCode.UP === which);\n      event.preventDefault();\n    }\n  };\n\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n  }; // >>> Focus & Blur\n\n\n  var onBlur = function onBlur() {\n    flushInputValue(false);\n    setFocus(false);\n    userTypingRef.current = false;\n  }; // ========================== Controlled ==========================\n  // Input by precision\n\n\n  useUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision]); // Input by value\n\n  useUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue)); // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]); // ============================ Cursor ============================\n\n  useUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]); // ============================ Render ============================\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames, \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), _defineProperty(_classNames, \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue)), _classNames)),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nInputNumber.displayName = 'InputNumber';\nexport default InputNumber;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,CAAC;AACrS,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,cAAc,IAAIC,OAAO,QAAQ,qBAAqB;AAC7D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,EAAEC,OAAO,EAAEC,cAAc,QAAQ,oBAAoB;AAChF,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC;AACA;AACA;;AAEA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,UAAU,EAAEC,YAAY,EAAE;EACvE,IAAID,UAAU,IAAIC,YAAY,CAACC,OAAO,CAAC,CAAC,EAAE;IACxC,OAAOD,YAAY,CAACE,QAAQ,CAAC,CAAC;EAChC;EAEA,OAAOF,YAAY,CAACG,QAAQ,CAAC,CAAC;AAChC,CAAC;AAED,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAE;EAC9D,IAAIC,OAAO,GAAGjB,cAAc,CAACgB,KAAK,CAAC;EACnC,OAAOC,OAAO,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGD,OAAO;AAChD,CAAC;AAED,IAAIE,WAAW,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIC,WAAW;EAEf,IAAIC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EE,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,GAAG,GAAGP,KAAK,CAACO,GAAG;IACfC,GAAG,GAAGR,KAAK,CAACQ,GAAG;IACfC,WAAW,GAAGT,KAAK,CAACU,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC/CE,YAAY,GAAGX,KAAK,CAACW,YAAY;IACjChB,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnBiB,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,eAAe,GAAGjB,KAAK,CAACkB,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9D5B,UAAU,GAAGW,KAAK,CAACX,UAAU;IAC7B8B,MAAM,GAAGnB,KAAK,CAACmB,MAAM;IACrBC,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IAC3BC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,gBAAgB,GAAGtB,KAAK,CAACsB,gBAAgB;IACzCC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;IACzBC,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;IACrBC,UAAU,GAAGtD,wBAAwB,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;EAE3D,IAAIsD,cAAc,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,QAAQ,CAAC;EACnD,IAAI0B,QAAQ,GAAGvD,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EAEjC,IAAIC,eAAe,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAG9D,cAAc,CAAC4D,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIG,aAAa,GAAG9D,KAAK,CAACwD,MAAM,CAAC,KAAK,CAAC;EACvC,IAAIO,cAAc,GAAG/D,KAAK,CAACwD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1C;;EAEA,IAAIQ,gBAAgB,GAAGhE,KAAK,CAAC0D,QAAQ,CAAC,YAAY;MAChD,OAAOtD,cAAc,CAACgB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGgB,YAAY,CAAC;IAClF,CAAC,CAAC;IACE6B,gBAAgB,GAAGpE,cAAc,CAACmE,gBAAgB,EAAE,CAAC,CAAC;IACtDjD,YAAY,GAAGkD,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAEzC,SAASE,2BAA2BA,CAACC,UAAU,EAAE;IAC/C,IAAIhD,KAAK,KAAKiD,SAAS,EAAE;MACvBH,eAAe,CAACE,UAAU,CAAC;IAC7B;EACF,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,IAAIE,YAAY,GAAGtE,KAAK,CAACuE,WAAW,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IACjE,IAAIA,UAAU,EAAE;MACd,OAAOJ,SAAS;IAClB;IAEA,IAAIvB,SAAS,IAAI,CAAC,EAAE;MAClB,OAAOA,SAAS;IAClB;IAEA,OAAO4B,IAAI,CAACzC,GAAG,CAAC1B,kBAAkB,CAACiE,MAAM,CAAC,EAAEjE,kBAAkB,CAAC4B,IAAI,CAAC,CAAC;EACvE,CAAC,EAAE,CAACW,SAAS,EAAEX,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEvB,IAAIwC,YAAY,GAAG3E,KAAK,CAACuE,WAAW,CAAC,UAAUK,GAAG,EAAE;IAClD,IAAIJ,MAAM,GAAGK,MAAM,CAACD,GAAG,CAAC;IAExB,IAAIhC,MAAM,EAAE;MACV,OAAOA,MAAM,CAAC4B,MAAM,CAAC;IACvB;IAEA,IAAIM,SAAS,GAAGN,MAAM;IAEtB,IAAIzB,gBAAgB,EAAE;MACpB+B,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAChC,gBAAgB,EAAE,GAAG,CAAC;IACtD,CAAC,CAAC;;IAGF,OAAO+B,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC3C,CAAC,EAAE,CAACnC,MAAM,EAAEG,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIiC,aAAa,GAAGhF,KAAK,CAACwD,MAAM,CAAC,EAAE,CAAC;EACpC,IAAIyB,eAAe,GAAGjF,KAAK,CAACuE,WAAW,CAAC,UAAUW,MAAM,EAAET,UAAU,EAAE;IACpE,IAAI5B,SAAS,EAAE;MACb,OAAOA,SAAS,CAACqC,MAAM,EAAE;QACvBT,UAAU,EAAEA,UAAU;QACtBU,KAAK,EAAEN,MAAM,CAACG,aAAa,CAACI,OAAO;MACrC,CAAC,CAAC;IACJ;IAEA,IAAIC,GAAG,GAAG,OAAOH,MAAM,KAAK,QAAQ,GAAG1E,OAAO,CAAC0E,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC;;IAEjE,IAAI,CAACT,UAAU,EAAE;MACf,IAAIa,eAAe,GAAGhB,YAAY,CAACe,GAAG,EAAEZ,UAAU,CAAC;MAEnD,IAAIhE,cAAc,CAAC4E,GAAG,CAAC,KAAKtC,gBAAgB,IAAIuC,eAAe,IAAI,CAAC,CAAC,EAAE;QACrE;QACA,IAAIC,YAAY,GAAGxC,gBAAgB,IAAI,GAAG;QAC1CsC,GAAG,GAAGhF,OAAO,CAACgF,GAAG,EAAEE,YAAY,EAAED,eAAe,CAAC;MACnD;IACF;IAEA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAACxC,SAAS,EAAEyB,YAAY,EAAEvB,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAEjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAIyC,gBAAgB,GAAGxF,KAAK,CAAC0D,QAAQ,CAAC,YAAY;MAChD,IAAI+B,SAAS,GAAGrD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGhB,KAAK;MAEvF,IAAIL,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACoE,QAAQ,CAAC9F,OAAO,CAAC6F,SAAS,CAAC,CAAC,EAAE;QACpF,OAAOE,MAAM,CAACC,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE,GAAGA,SAAS;MACjD;MAEA,OAAOR,eAAe,CAAClE,YAAY,CAACE,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;IACxD,CAAC,CAAC;IACE4E,gBAAgB,GAAGhG,cAAc,CAAC2F,gBAAgB,EAAE,CAAC,CAAC;IACtDM,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE/Cb,aAAa,CAACI,OAAO,GAAGU,UAAU,CAAC,CAAC;;EAEpC,SAASE,aAAaA,CAACC,QAAQ,EAAExB,UAAU,EAAE;IAC3CsB,qBAAqB,CAACd,eAAe;IAAE;IACvC;IACA;IACAgB,QAAQ,CAAC3E,YAAY,CAAC,CAAC,GAAG2E,QAAQ,CAAChF,QAAQ,CAAC,KAAK,CAAC,GAAGgF,QAAQ,CAAChF,QAAQ,CAAC,CAACwD,UAAU,CAAC,EAAEA,UAAU,CAAC,CAAC;EACnG,CAAC,CAAC;;EAGF,IAAIyB,UAAU,GAAGlG,KAAK,CAACmG,OAAO,CAAC,YAAY;IACzC,OAAOhF,oBAAoB,CAACc,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,IAAImE,UAAU,GAAGpG,KAAK,CAACmG,OAAO,CAAC,YAAY;IACzC,OAAOhF,oBAAoB,CAACa,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,IAAIqE,UAAU,GAAGrG,KAAK,CAACmG,OAAO,CAAC,YAAY;IACzC,IAAI,CAACD,UAAU,IAAI,CAACnF,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IAEA,OAAO4E,UAAU,CAACI,UAAU,CAACvF,YAAY,CAAC;EAC5C,CAAC,EAAE,CAACmF,UAAU,EAAEnF,YAAY,CAAC,CAAC;EAC9B,IAAIwF,YAAY,GAAGvG,KAAK,CAACmG,OAAO,CAAC,YAAY;IAC3C,IAAI,CAACC,UAAU,IAAI,CAACrF,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IAEA,OAAOP,YAAY,CAACuF,UAAU,CAACF,UAAU,CAAC;EAC5C,CAAC,EAAE,CAACA,UAAU,EAAErF,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIyF,UAAU,GAAG9F,SAAS,CAAC6C,QAAQ,CAAC6B,OAAO,EAAExB,KAAK,CAAC;IAC/C6C,WAAW,GAAG5G,cAAc,CAAC2G,UAAU,EAAE,CAAC,CAAC;IAC3CE,YAAY,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC7BE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpC;AACF;AACA;AACA;AACA;AACA;AACA;;EAGE,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAE;IACjD;IACA,IAAIX,UAAU,IAAI,CAACW,MAAM,CAACP,UAAU,CAACJ,UAAU,CAAC,EAAE;MAChD,OAAOA,UAAU;IACnB,CAAC,CAAC;;IAGF,IAAIE,UAAU,IAAI,CAACA,UAAU,CAACE,UAAU,CAACO,MAAM,CAAC,EAAE;MAChD,OAAOT,UAAU;IACnB;IAEA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;;EAGE,IAAIU,SAAS,GAAG,SAASA,SAASA,CAACD,MAAM,EAAE;IACzC,OAAO,CAACD,aAAa,CAACC,MAAM,CAAC;EAC/B,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAACd,QAAQ,EAAExB,UAAU,EAAE;IACzE,IAAIuC,WAAW,GAAGf,QAAQ;IAC1B,IAAIgB,eAAe,GAAGH,SAAS,CAACE,WAAW,CAAC,IAAIA,WAAW,CAAChG,OAAO,CAAC,CAAC,CAAC,CAAC;IACvE;IACA;;IAEA,IAAI,CAACgG,WAAW,CAAChG,OAAO,CAAC,CAAC,IAAI,CAACyD,UAAU,EAAE;MACzC;MACAuC,WAAW,GAAGJ,aAAa,CAACI,WAAW,CAAC,IAAIA,WAAW;MACvDC,eAAe,GAAG,IAAI;IACxB;IAEA,IAAI,CAAC3E,QAAQ,IAAI,CAACD,QAAQ,IAAI4E,eAAe,EAAE;MAC7C,IAAIzC,MAAM,GAAGwC,WAAW,CAAC/F,QAAQ,CAAC,CAAC;MACnC,IAAIqE,eAAe,GAAGhB,YAAY,CAACE,MAAM,EAAEC,UAAU,CAAC;MAEtD,IAAIa,eAAe,IAAI,CAAC,EAAE;QACxB0B,WAAW,GAAG5G,cAAc,CAACC,OAAO,CAACmE,MAAM,EAAE,GAAG,EAAEc,eAAe,CAAC,CAAC;MACrE,CAAC,CAAC;;MAGF,IAAI,CAAC0B,WAAW,CAACE,MAAM,CAACnG,YAAY,CAAC,EAAE;QACrCoD,2BAA2B,CAAC6C,WAAW,CAAC;QACxChE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACgE,WAAW,CAAChG,OAAO,CAAC,CAAC,GAAG,IAAI,GAAGH,eAAe,CAACC,UAAU,EAAEkG,WAAW,CAAC,CAAC,CAAC,CAAC;;QAEvI,IAAI5F,KAAK,KAAKiD,SAAS,EAAE;UACvB2B,aAAa,CAACgB,WAAW,EAAEvC,UAAU,CAAC;QACxC;MACF;MAEA,OAAOuC,WAAW;IACpB;IAEA,OAAOjG,YAAY;EACrB,CAAC,CAAC,CAAC;;EAGH,IAAIoG,aAAa,GAAGvG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIwG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;IAC3DX,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhBX,qBAAqB,CAACsB,QAAQ,CAAC,CAAC,CAAC;;IAEjC,IAAI,CAACtD,cAAc,CAACqB,OAAO,EAAE;MAC3B,IAAIkC,UAAU,GAAG3C,YAAY,CAAC0C,QAAQ,CAAC;MACvC,IAAIE,YAAY,GAAGnH,cAAc,CAACkH,UAAU,CAAC;MAE7C,IAAI,CAACC,YAAY,CAAC3B,KAAK,CAAC,CAAC,EAAE;QACzBmB,kBAAkB,CAACQ,YAAY,EAAE,IAAI,CAAC;MACxC;IACF,CAAC,CAAC;;IAGFtE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoE,QAAQ,CAAC,CAAC,CAAC;IACrE;;IAEAF,aAAa,CAAC,YAAY;MACxB,IAAIK,YAAY,GAAGH,QAAQ;MAE3B,IAAI,CAACzE,MAAM,EAAE;QACX4E,YAAY,GAAGH,QAAQ,CAACtC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5C;MAEA,IAAIyC,YAAY,KAAKH,QAAQ,EAAE;QAC7BD,iBAAiB,CAACI,YAAY,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGH,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD1D,cAAc,CAACqB,OAAO,GAAG,IAAI;EAC/B,CAAC;EAED,IAAIsC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD3D,cAAc,CAACqB,OAAO,GAAG,KAAK;IAC9BgC,iBAAiB,CAAC7D,QAAQ,CAAC6B,OAAO,CAAChE,KAAK,CAAC;EAC3C,CAAC,CAAC,CAAC;;EAGH,IAAIuG,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;IAChDR,iBAAiB,CAACQ,CAAC,CAACf,MAAM,CAACzF,KAAK,CAAC;EACnC,CAAC,CAAC,CAAC;;EAGH,IAAIyG,cAAc,GAAG,SAASA,cAAcA,CAACC,EAAE,EAAE;IAC/C,IAAIC,iBAAiB;;IAErB;IACA,IAAID,EAAE,IAAIzB,UAAU,IAAI,CAACyB,EAAE,IAAIvB,YAAY,EAAE;MAC3C;IACF,CAAC,CAAC;IACF;;IAGAzC,aAAa,CAACsB,OAAO,GAAG,KAAK;IAC7B,IAAI4C,WAAW,GAAG5H,cAAc,CAAC+B,IAAI,CAAC;IAEtC,IAAI,CAAC2F,EAAE,EAAE;MACPE,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC,CAAC;IACpC;IAEA,IAAIpB,MAAM,GAAG,CAAC9F,YAAY,IAAIX,cAAc,CAAC,CAAC,CAAC,EAAE8H,GAAG,CAACF,WAAW,CAAC/G,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIkH,YAAY,GAAGpB,kBAAkB,CAACF,MAAM,EAAE,KAAK,CAAC;IACpD1D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACtC,eAAe,CAACC,UAAU,EAAEqH,YAAY,CAAC,EAAE;MAChGC,MAAM,EAAEjG,IAAI;MACZkG,IAAI,EAAEP,EAAE,GAAG,IAAI,GAAG;IACpB,CAAC,CAAC;IACF,CAACC,iBAAiB,GAAGxE,QAAQ,CAAC6B,OAAO,MAAM,IAAI,IAAI2C,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACnE,KAAK,CAAC,CAAC;EACtH,CAAC,CAAC,CAAC;;EAEH;AACF;AACA;;EAGE,IAAI0E,eAAe,GAAG,SAASA,eAAeA,CAAC7D,UAAU,EAAE;IACzD,IAAI8D,WAAW,GAAGnI,cAAc,CAACuE,YAAY,CAACmB,UAAU,CAAC,CAAC;IAC1D,IAAI0C,WAAW,GAAGD,WAAW;IAE7B,IAAI,CAACA,WAAW,CAAC3C,KAAK,CAAC,CAAC,EAAE;MACxB;MACA;MACA4C,WAAW,GAAGzB,kBAAkB,CAACwB,WAAW,EAAE9D,UAAU,CAAC;IAC3D,CAAC,MAAM;MACL+D,WAAW,GAAGzH,YAAY;IAC5B;IAEA,IAAIK,KAAK,KAAKiD,SAAS,EAAE;MACvB;MACA2B,aAAa,CAACjF,YAAY,EAAE,KAAK,CAAC;IACpC,CAAC,MAAM,IAAI,CAACyH,WAAW,CAAC5C,KAAK,CAAC,CAAC,EAAE;MAC/B;MACAI,aAAa,CAACwC,WAAW,EAAE,KAAK,CAAC;IACnC;EACF,CAAC;EAED,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACvB7E,aAAa,CAACsB,OAAO,GAAG,IAAI;IAE5B,IAAIuD,KAAK,KAAKzI,OAAO,CAAC0I,KAAK,EAAE;MAC3B,IAAI,CAAC7E,cAAc,CAACqB,OAAO,EAAE;QAC3BtB,aAAa,CAACsB,OAAO,GAAG,KAAK;MAC/B;MAEAkD,eAAe,CAAC,KAAK,CAAC;MACtBpF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACwF,KAAK,CAAC;IACjF;IAEA,IAAIjG,QAAQ,KAAK,KAAK,EAAE;MACtB;IACF,CAAC,CAAC;;IAGF,IAAI,CAACsB,cAAc,CAACqB,OAAO,IAAI,CAAClF,OAAO,CAAC2I,EAAE,EAAE3I,OAAO,CAAC4I,IAAI,CAAC,CAACpD,QAAQ,CAACiD,KAAK,CAAC,EAAE;MACzEd,cAAc,CAAC3H,OAAO,CAAC2I,EAAE,KAAKF,KAAK,CAAC;MACpCD,KAAK,CAACK,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EAED,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BlF,aAAa,CAACsB,OAAO,GAAG,KAAK;EAC/B,CAAC,CAAC,CAAC;;EAGH,IAAI6D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7BX,eAAe,CAAC,KAAK,CAAC;IACtBzE,QAAQ,CAAC,KAAK,CAAC;IACfC,aAAa,CAACsB,OAAO,GAAG,KAAK;EAC/B,CAAC,CAAC,CAAC;EACH;;EAGAzE,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACI,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAChC0E,aAAa,CAACjF,YAAY,EAAE,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEjBnC,eAAe,CAAC,YAAY;IAC1B,IAAIsF,QAAQ,GAAG7F,cAAc,CAACgB,KAAK,CAAC;IACpC8C,eAAe,CAAC+B,QAAQ,CAAC;IACzB,IAAIiD,kBAAkB,GAAG9I,cAAc,CAACuE,YAAY,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;IACnE;;IAEA,IAAI,CAACG,QAAQ,CAACiB,MAAM,CAACgC,kBAAkB,CAAC,IAAI,CAACpF,aAAa,CAACsB,OAAO,IAAIvC,SAAS,EAAE;MAC/E;MACAmD,aAAa,CAACC,QAAQ,EAAEnC,aAAa,CAACsB,OAAO,CAAC;IAChD;EACF,CAAC,EAAE,CAAChE,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEbT,eAAe,CAAC,YAAY;IAC1B,IAAIkC,SAAS,EAAE;MACb8D,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,OAAO,aAAa9F,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IAC7CrH,SAAS,EAAE7B,UAAU,CAAC4B,SAAS,EAAEC,SAAS,GAAGH,WAAW,GAAG,CAAC,CAAC,EAAEhC,eAAe,CAACgC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,UAAU,CAAC,EAAE+B,KAAK,CAAC,EAAEjE,eAAe,CAACgC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,WAAW,CAAC,EAAEQ,QAAQ,CAAC,EAAE1C,eAAe,CAACgC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,WAAW,CAAC,EAAES,QAAQ,CAAC,EAAE3C,eAAe,CAACgC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC,EAAEd,YAAY,CAAC6E,KAAK,CAAC,CAAC,CAAC,EAAEjG,eAAe,CAACgC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC,EAAE,CAACd,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAACwF,SAAS,CAAC/F,YAAY,CAAC,CAAC,EAAEY,WAAW,CAAC,CAAC;IACngBI,KAAK,EAAEA,KAAK;IACZqH,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1BvF,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC;IACDoF,MAAM,EAAEA,MAAM;IACdR,SAAS,EAAEA,SAAS;IACpBO,OAAO,EAAEA,OAAO;IAChBvB,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA;EACpB,CAAC,EAAE/E,QAAQ,IAAI,aAAa3C,KAAK,CAACmJ,aAAa,CAAC7I,WAAW,EAAE;IAC3DuB,SAAS,EAAEA,SAAS;IACpBwH,MAAM,EAAE9G,SAAS;IACjB+G,QAAQ,EAAE9G,WAAW;IACrB6D,UAAU,EAAEA,UAAU;IACtBE,YAAY,EAAEA,YAAY;IAC1BpD,MAAM,EAAE0E;EACV,CAAC,CAAC,EAAE,aAAa7H,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IAC1CrH,SAAS,EAAE,EAAE,CAACwB,MAAM,CAACD,cAAc,EAAE,OAAO;EAC9C,CAAC,EAAE,aAAarD,KAAK,CAACmJ,aAAa,CAAC,OAAO,EAAEzJ,QAAQ,CAAC;IACpD6J,YAAY,EAAE,KAAK;IACnBC,IAAI,EAAE,YAAY;IAClB,eAAe,EAAExH,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAElB,YAAY,CAACO,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGP,YAAY,CAACE,QAAQ,CAAC,CAAC;IAC7EkB,IAAI,EAAEA;EACR,CAAC,EAAEiB,UAAU,EAAE;IACb1B,GAAG,EAAEvB,UAAU,CAACoD,QAAQ,EAAE7B,GAAG,CAAC;IAC9BI,SAAS,EAAEuB,cAAc;IACzBjC,KAAK,EAAE0E,UAAU;IACjB9C,QAAQ,EAAE2E,eAAe;IACzBtF,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACFf,WAAW,CAACkI,WAAW,GAAG,aAAa;AACvC,eAAelI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}