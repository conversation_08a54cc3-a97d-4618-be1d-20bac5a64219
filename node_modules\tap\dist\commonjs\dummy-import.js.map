{"version": 3, "file": "dummy-import.js", "sourceRoot": "", "sources": ["../../src/dummy-import.ts"], "names": [], "mappings": ";;AAAA,yDAAyD;AACzD,wBAAqB;AACrB,6BAA0B;AAC1B,0BAAuB;AACvB,yBAAsB;AACtB,8BAA2B;AAC3B,wBAAqB;AACrB,yBAAsB;AACtB,0BAAuB;AACvB,4BAAyB;AACzB,uBAAoB;AACpB,iCAA8B;AAC9B,2BAAwB;AACxB,wBAAqB;AACrB,wBAAqB;AACrB,6BAA0B;AAC1B,yBAAsB;AACtB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA", "sourcesContent": ["// just here to ensure all of these are forced to be deps\nimport '@tapjs/after'\nimport '@tapjs/after-each'\nimport '@tapjs/asserts'\nimport '@tapjs/before'\nimport '@tapjs/before-each'\nimport '@tapjs/chdir'\nimport '@tapjs/filter'\nimport '@tapjs/fixture'\nimport '@tapjs/intercept'\nimport '@tapjs/mock'\nimport '@tapjs/node-serialize'\nimport '@tapjs/snapshot'\nimport '@tapjs/spawn'\nimport '@tapjs/stdin'\nimport '@tapjs/typescript'\nimport '@tapjs/worker'\nthrow new Error('this module should not be loaded')\n"]}