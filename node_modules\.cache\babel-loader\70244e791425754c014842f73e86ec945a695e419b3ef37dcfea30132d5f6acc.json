{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Base from './Base';\nvar Paragraph = function Paragraph(props, ref) {\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, props, {\n    component: \"div\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Paragraph);", "map": {"version": 3, "names": ["_extends", "React", "Base", "Paragraph", "props", "ref", "createElement", "component", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/typography/Paragraph.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Base from './Base';\n\nvar Paragraph = function Paragraph(props, ref) {\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, props, {\n    component: \"div\"\n  }));\n};\n\nexport default /*#__PURE__*/React.forwardRef(Paragraph);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AAEzB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7C,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAACJ,IAAI,EAAEF,QAAQ,CAAC;IACrDK,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRG,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,aAAaN,KAAK,CAACO,UAAU,CAACL,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}