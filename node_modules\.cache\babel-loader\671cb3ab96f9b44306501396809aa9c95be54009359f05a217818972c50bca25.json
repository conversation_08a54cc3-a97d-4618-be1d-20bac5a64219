{"ast": null, "code": "// export this package's api\nimport Drawer from './DrawerWrapper';\nexport default Drawer;", "map": {"version": 3, "names": ["Drawer"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-drawer/es/index.js"], "sourcesContent": ["// export this package's api\nimport Drawer from './DrawerWrapper';\nexport default Drawer;"], "mappings": "AAAA;AACA,OAAOA,MAAM,MAAM,iBAAiB;AACpC,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}