{"ast": null, "code": "import * as React from 'react';\nvar PrivateContext = /*#__PURE__*/React.createContext({});\nexport default PrivateContext;", "map": {"version": 3, "names": ["React", "PrivateContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-menu/es/context/PrivateContext.js"], "sourcesContent": ["import * as React from 'react';\nvar PrivateContext = /*#__PURE__*/React.createContext({});\nexport default PrivateContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACzD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}