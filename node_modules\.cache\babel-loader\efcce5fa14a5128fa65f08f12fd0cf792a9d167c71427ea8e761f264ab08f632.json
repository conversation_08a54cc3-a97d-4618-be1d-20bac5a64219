{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Rings = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar Rings = function Rings(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 45 45\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\",\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius,\n    strokeOpacity: \"0\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"6;22\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"stroke-opacity\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"1;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"stroke-width\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"2;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius,\n    strokeOpacity: \"0\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"6;22\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"1;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeWidth\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"2;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius + 2\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"0s\",\n    dur: \"1.5s\",\n    values: \"6;1;2;3;4;5;6\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }))));\n};\nexports.Rings = Rings;\nRings.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nRings.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 6,\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Rings", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "viewBox", "xmlns", "stroke", "color", "label", "fill", "fillRule", "transform", "strokeWidth", "cx", "cy", "r", "radius", "strokeOpacity", "attributeName", "begin", "dur", "values", "calcMode", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/Rings.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Rings = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar Rings = function Rings(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 45 45\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    stroke: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\",\n    transform: \"translate(1 1)\",\n    strokeWidth: \"2\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius,\n    strokeOpacity: \"0\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"6;22\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"stroke-opacity\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"1;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"stroke-width\",\n    begin: \"1.5s\",\n    dur: \"3s\",\n    values: \"2;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius,\n    strokeOpacity: \"0\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"6;22\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeOpacity\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"1;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"strokeWidth\",\n    begin: \"3s\",\n    dur: \"3s\",\n    values: \"2;0\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"22\",\n    cy: \"22\",\n    r: props.radius + 2\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    begin: \"0s\",\n    dur: \"1.5s\",\n    values: \"6;1;2;3;4;5;6\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }))));\n};\n\nexports.Rings = Rings;\nRings.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nRings.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  radius: 6,\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AAEtB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,KAAK,GAAG,SAASA,KAAKA,CAACO,KAAK,EAAE;EAChC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAEN,KAAK,CAACO,KAAK;IACnB,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACnDQ,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE;EACf,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC,EAAE,aAAavB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDiB,aAAa,EAAE,GAAG;IAClBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DiB,aAAa,EAAE,gBAAgB;IAC/BC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DiB,aAAa,EAAE,cAAc;IAC7BC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC,EAAE,aAAavB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDiB,aAAa,EAAE,GAAG;IAClBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DiB,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DiB,aAAa,EAAE,aAAa;IAC5BC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa7B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DY,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEf,KAAK,CAACgB,MAAM,GAAG;EACpB,CAAC,EAAE,aAAatB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDiB,aAAa,EAAE,GAAG;IAClBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,eAAe;IACvBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAEDhC,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBA,KAAK,CAAC+B,SAAS,GAAG;EAChBrB,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAAC4B,SAAS,CAAC,CAAC5B,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,CAAC,CAAC;EACrGzB,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAAC4B,SAAS,CAAC,CAAC5B,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAAC8B,MAAM,CAAC,CAAC;EACpGpB,KAAK,EAAEV,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM;EACnClB,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC6B,MAAM;EACnCV,MAAM,EAAEnB,UAAU,CAAC,SAAS,CAAC,CAAC8B;AAChC,CAAC;AACDlC,KAAK,CAACmC,YAAY,GAAG;EACnBzB,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTK,KAAK,EAAE,OAAO;EACdS,MAAM,EAAE,CAAC;EACTR,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}