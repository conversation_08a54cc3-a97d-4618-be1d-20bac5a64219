{"name": "@cypress/vue", "version": "0.0.0-development", "description": "Browser-based Component Testing for Vue.js with Cypress.io ✌️🌲", "main": "dist/cypress-vue.cjs.js", "scripts": {"build-prod": "yarn build", "cy:open": "node ../../scripts/cypress.js open --component --project ${PWD}", "cy:run": "node ../../scripts/cypress.js run --component --project ${PWD}", "build": "rimraf dist && rollup -c rollup.config.mjs", "postbuild": "node --require @packages/ts/register ./inline-types.ts && node ../../scripts/sync-exported-npm-with-cli.js", "typecheck": "yarn tsd && vue-tsc --noEmit", "test": "yarn cy:run", "tsd": "yarn build && yarn tsc -p test-tsd/tsconfig.tsd.json", "watch": "yarn build --watch --watch.exclude ./dist/**/*"}, "devDependencies": {"@cypress/mount-utils": "0.0.0-development", "@vitejs/plugin-vue": "2.3.1", "@vue/compiler-sfc": "3.2.31", "@vue/test-utils": "2.0.2", "axios": "0.21.2", "cypress": "0.0.0-development", "debug": "^4.3.2", "globby": "^11.0.1", "tailwindcss": "1.1.4", "typescript": "^4.7.4", "vite": "3.1.0", "vue": "3.2.31", "vue-i18n": "9.0.0-rc.6", "vue-router": "^4.0.0", "vue-tsc": "^0.3.0", "vuex": "^4.0.0"}, "peerDependencies": {"@cypress/webpack-dev-server": "*", "cypress": ">=7.0.0", "vue": ">=3.0.0"}, "files": ["dist/**/*", "src/**/*.js"], "engines": {"node": ">=8"}, "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/vue/#readme", "author": "<PERSON><PERSON><PERSON> <gleb.bah<PERSON><PERSON>@gmail.com>", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Fvue&template=1-bug-report.md&title=", "keywords": ["cypress", "vue"], "contributors": [{"name": "<PERSON>", "social": "@JessicaSachs"}, {"name": "<PERSON>", "social": "@amirrustam"}, {"name": "<PERSON><PERSON><PERSON>", "social": "@Lachlan19900"}], "module": "dist/cypress-vue.esm-bundler.js", "peerDependenciesMeta": {"@cypress/webpack-dev-server": {"optional": true}}, "publishConfig": {"access": "public"}}