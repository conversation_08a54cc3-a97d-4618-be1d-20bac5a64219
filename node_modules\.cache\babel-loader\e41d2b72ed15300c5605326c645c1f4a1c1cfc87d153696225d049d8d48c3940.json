{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport RcTree, { TreeNode } from 'rc-tree';\nimport classNames from 'classnames';\nimport DirectoryTree from './DirectoryTree';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport renderSwitcherIcon from './utils/iconUtil';\nimport dropIndicatorRender from './utils/dropIndicator';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    virtual = _React$useContext.virtual;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    showIcon = props.showIcon,\n    showLine = props.showLine,\n    _switcherIcon = props.switcherIcon,\n    blockNode = props.blockNode,\n    children = props.children,\n    checkable = props.checkable,\n    selectable = props.selectable,\n    draggable = props.draggable;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n  var newProps = _extends(_extends({}, props), {\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n    var mergedDraggable = {};\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n      default: // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block-node\"), blockNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-unselectable\"), !selectable), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nTree.TreeNode = TreeNode;\nTree.DirectoryTree = DirectoryTree;\nTree.defaultProps = {\n  checkable: false,\n  selectable: true,\n  showIcon: false,\n  motion: _extends(_extends({}, collapseMotion), {\n    motionAppear: false\n  }),\n  blockNode: false\n};\nexport default Tree;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "_extends", "React", "Holder<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TreeNode", "classNames", "DirectoryTree", "ConfigContext", "collapseMotion", "renderSwitcherIcon", "dropIndicatorRender", "Tree", "forwardRef", "props", "ref", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "virtual", "customizePrefixCls", "prefixCls", "className", "showIcon", "showLine", "_switcherIcon", "switcherIcon", "blockNode", "children", "checkable", "selectable", "draggable", "newProps", "Boolean", "draggableConfig", "useMemo", "mergedDraggable", "nodeDraggable", "icon", "createElement", "itemHeight", "concat", "nodeProps", "defaultProps", "motion", "motionAppear"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/tree/Tree.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport RcTree, { TreeNode } from 'rc-tree';\nimport classNames from 'classnames';\nimport DirectoryTree from './DirectoryTree';\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport renderSwitcherIcon from './utils/iconUtil';\nimport dropIndicatorRender from './utils/dropIndicator';\nvar Tree = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction,\n      virtual = _React$useContext.virtual;\n\n  var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      showIcon = props.showIcon,\n      showLine = props.showLine,\n      _switcherIcon = props.switcherIcon,\n      blockNode = props.blockNode,\n      children = props.children,\n      checkable = props.checkable,\n      selectable = props.selectable,\n      draggable = props.draggable;\n  var prefixCls = getPrefixCls('tree', customizePrefixCls);\n\n  var newProps = _extends(_extends({}, props), {\n    showLine: Boolean(showLine),\n    dropIndicatorRender: dropIndicatorRender\n  });\n\n  var draggableConfig = React.useMemo(function () {\n    if (!draggable) {\n      return false;\n    }\n\n    var mergedDraggable = {};\n\n    switch (_typeof(draggable)) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n\n      case 'object':\n        mergedDraggable = _extends({}, draggable);\n        break;\n\n      default: // Do nothing\n\n    }\n\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n\n    return mergedDraggable;\n  }, [draggable]);\n  return /*#__PURE__*/React.createElement(RcTree, _extends({\n    itemHeight: 20,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    prefixCls: prefixCls,\n    className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-icon-hide\"), !showIcon), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block-node\"), blockNode), _defineProperty(_classNames, \"\".concat(prefixCls, \"-unselectable\"), !selectable), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-checkbox-inner\")\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: function switcherIcon(nodeProps) {\n      return renderSwitcherIcon(prefixCls, _switcherIcon, showLine, nodeProps);\n    },\n    draggable: draggableConfig\n  }), children);\n});\nTree.TreeNode = TreeNode;\nTree.DirectoryTree = DirectoryTree;\nTree.defaultProps = {\n  checkable: false,\n  selectable: true,\n  showIcon: false,\n  motion: _extends(_extends({}, collapseMotion), {\n    motionAppear: false\n  }),\n  blockNode: false\n};\nexport default Tree;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,MAAM,IAAIC,QAAQ,QAAQ,SAAS;AAC1C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,OAAOC,kBAAkB,MAAM,kBAAkB;AACjD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,IAAIC,IAAI,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGf,KAAK,CAACgB,UAAU,CAACV,aAAa,CAAC;IACnDW,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;IACvCC,OAAO,GAAGJ,iBAAiB,CAACI,OAAO;EAEvC,IAAIC,kBAAkB,GAAGR,KAAK,CAACS,SAAS;IACpCC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,aAAa,GAAGb,KAAK,CAACc,YAAY;IAClCC,SAAS,GAAGf,KAAK,CAACe,SAAS;IAC3BC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,SAAS,GAAGnB,KAAK,CAACmB,SAAS;EAC/B,IAAIV,SAAS,GAAGJ,YAAY,CAAC,MAAM,EAAEG,kBAAkB,CAAC;EAExD,IAAIY,QAAQ,GAAGjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,CAAC,EAAE;IAC3CY,QAAQ,EAAES,OAAO,CAACT,QAAQ,CAAC;IAC3Bf,mBAAmB,EAAEA;EACvB,CAAC,CAAC;EAEF,IAAIyB,eAAe,GAAGlC,KAAK,CAACmC,OAAO,CAAC,YAAY;IAC9C,IAAI,CAACJ,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IAEA,IAAIK,eAAe,GAAG,CAAC,CAAC;IAExB,QAAQtC,OAAO,CAACiC,SAAS,CAAC;MACxB,KAAK,UAAU;QACbK,eAAe,CAACC,aAAa,GAAGN,SAAS;QACzC;MAEF,KAAK,QAAQ;QACXK,eAAe,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,SAAS,CAAC;QACzC;MAEF,QAAQ,CAAC;IAEX;IAEA,IAAIK,eAAe,CAACE,IAAI,KAAK,KAAK,EAAE;MAClCF,eAAe,CAACE,IAAI,GAAGF,eAAe,CAACE,IAAI,IAAI,aAAatC,KAAK,CAACuC,aAAa,CAACtC,cAAc,EAAE,IAAI,CAAC;IACvG;IAEA,OAAOmC,eAAe;EACxB,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EACf,OAAO,aAAa/B,KAAK,CAACuC,aAAa,CAACrC,MAAM,EAAEH,QAAQ,CAAC;IACvDyC,UAAU,EAAE,EAAE;IACd3B,GAAG,EAAEA,GAAG;IACRM,OAAO,EAAEA;EACX,CAAC,EAAEa,QAAQ,EAAE;IACXX,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAElB,UAAU,EAAEU,WAAW,GAAG,CAAC,CAAC,EAAEjB,eAAe,CAACiB,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACpB,SAAS,EAAE,YAAY,CAAC,EAAE,CAACE,QAAQ,CAAC,EAAE1B,eAAe,CAACiB,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACpB,SAAS,EAAE,aAAa,CAAC,EAAEM,SAAS,CAAC,EAAE9B,eAAe,CAACiB,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACpB,SAAS,EAAE,eAAe,CAAC,EAAE,CAACS,UAAU,CAAC,EAAEjC,eAAe,CAACiB,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACpB,SAAS,EAAE,MAAM,CAAC,EAAEH,SAAS,KAAK,KAAK,CAAC,EAAEJ,WAAW,GAAGQ,SAAS,CAAC;IAC/XJ,SAAS,EAAEA,SAAS;IACpBW,SAAS,EAAEA,SAAS,GAAG,aAAa7B,KAAK,CAACuC,aAAa,CAAC,MAAM,EAAE;MAC9DjB,SAAS,EAAE,EAAE,CAACmB,MAAM,CAACpB,SAAS,EAAE,iBAAiB;IACnD,CAAC,CAAC,GAAGQ,SAAS;IACdC,UAAU,EAAEA,UAAU;IACtBJ,YAAY,EAAE,SAASA,YAAYA,CAACgB,SAAS,EAAE;MAC7C,OAAOlC,kBAAkB,CAACa,SAAS,EAAEI,aAAa,EAAED,QAAQ,EAAEkB,SAAS,CAAC;IAC1E,CAAC;IACDX,SAAS,EAAEG;EACb,CAAC,CAAC,EAAEN,QAAQ,CAAC;AACf,CAAC,CAAC;AACFlB,IAAI,CAACP,QAAQ,GAAGA,QAAQ;AACxBO,IAAI,CAACL,aAAa,GAAGA,aAAa;AAClCK,IAAI,CAACiC,YAAY,GAAG;EAClBd,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE,IAAI;EAChBP,QAAQ,EAAE,KAAK;EACfqB,MAAM,EAAE7C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEQ,cAAc,CAAC,EAAE;IAC7CsC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFlB,SAAS,EAAE;AACb,CAAC;AACD,eAAejB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}