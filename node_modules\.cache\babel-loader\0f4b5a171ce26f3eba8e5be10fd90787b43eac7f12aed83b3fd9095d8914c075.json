{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/**\n * Cursor rule:\n * 1. Only `showSearch` enabled\n * 2. Only `open` is `true`\n * 3. When typing, set `open` to `true` which hit rule of 2\n *\n * Accessibility:\n * - https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html\n */\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport MultipleSelector from './MultipleSelector';\nimport SingleSelector from './SingleSelector';\nimport useLock from '../hooks/useLock';\nimport { isValidateOpenKey } from '../utils/keyUtil';\nvar Selector = function Selector(props, ref) {\n  var inputRef = useRef(null);\n  var compositionStatusRef = useRef(false);\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    mode = props.mode,\n    showSearch = props.showSearch,\n    tokenWithEnter = props.tokenWithEnter,\n    onSearch = props.onSearch,\n    onSearchSubmit = props.onSearchSubmit,\n    onToggleOpen = props.onToggleOpen,\n    onInputKeyDown = props.onInputKeyDown,\n    domRef = props.domRef; // ======================= Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        inputRef.current.focus();\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  }); // ====================== Input ======================\n\n  var _useLock = useLock(0),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getInputMouseDown = _useLock2[0],\n    setInputMouseDown = _useLock2[1];\n  var onInternalInputKeyDown = function onInternalInputKeyDown(event) {\n    var which = event.which;\n    if (which === KeyCode.UP || which === KeyCode.DOWN) {\n      event.preventDefault();\n    }\n    if (onInputKeyDown) {\n      onInputKeyDown(event);\n    }\n    if (which === KeyCode.ENTER && mode === 'tags' && !compositionStatusRef.current && !open) {\n      // When menu isn't open, OptionList won't trigger a value change\n      // So when enter is pressed, the tag's input value should be emitted here to let selector know\n      onSearchSubmit === null || onSearchSubmit === void 0 ? void 0 : onSearchSubmit(event.target.value);\n    }\n    if (isValidateOpenKey(which)) {\n      onToggleOpen(true);\n    }\n  };\n  /**\n   * We can not use `findDOMNode` sine it will get warning,\n   * have to use timer to check if is input element.\n   */\n\n  var onInternalInputMouseDown = function onInternalInputMouseDown() {\n    setInputMouseDown(true);\n  }; // When paste come, ignore next onChange\n\n  var pastedTextRef = useRef(null);\n  var triggerOnSearch = function triggerOnSearch(value) {\n    if (onSearch(value, true, compositionStatusRef.current) !== false) {\n      onToggleOpen(true);\n    }\n  };\n  var onInputCompositionStart = function onInputCompositionStart() {\n    compositionStatusRef.current = true;\n  };\n  var onInputCompositionEnd = function onInputCompositionEnd(e) {\n    compositionStatusRef.current = false; // Trigger search again to support `tokenSeparators` with typewriting\n\n    if (mode !== 'combobox') {\n      triggerOnSearch(e.target.value);\n    }\n  };\n  var onInputChange = function onInputChange(event) {\n    var value = event.target.value; // Pasted text should replace back to origin content\n\n    if (tokenWithEnter && pastedTextRef.current && /[\\r\\n]/.test(pastedTextRef.current)) {\n      // CRLF will be treated as a single space for input element\n      var replacedText = pastedTextRef.current.replace(/[\\r\\n]+$/, '').replace(/\\r\\n/g, ' ').replace(/[\\r\\n]/g, ' ');\n      value = value.replace(replacedText, pastedTextRef.current);\n    }\n    pastedTextRef.current = null;\n    triggerOnSearch(value);\n  };\n  var onInputPaste = function onInputPaste(e) {\n    var clipboardData = e.clipboardData;\n    var value = clipboardData.getData('text');\n    pastedTextRef.current = value;\n  };\n  var onClick = function onClick(_ref) {\n    var target = _ref.target;\n    if (target !== inputRef.current) {\n      // Should focus input if click the selector\n      var isIE = document.body.style.msTouchAction !== undefined;\n      if (isIE) {\n        setTimeout(function () {\n          inputRef.current.focus();\n        });\n      } else {\n        inputRef.current.focus();\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    var inputMouseDown = getInputMouseDown();\n    if (event.target !== inputRef.current && !inputMouseDown) {\n      event.preventDefault();\n    }\n    if (mode !== 'combobox' && (!showSearch || !inputMouseDown) || !open) {\n      if (open) {\n        onSearch('', true, false);\n      }\n      onToggleOpen();\n    }\n  }; // ================= Inner Selector ==================\n\n  var sharedProps = {\n    inputRef: inputRef,\n    onInputKeyDown: onInternalInputKeyDown,\n    onInputMouseDown: onInternalInputMouseDown,\n    onInputChange: onInputChange,\n    onInputPaste: onInputPaste,\n    onInputCompositionStart: onInputCompositionStart,\n    onInputCompositionEnd: onInputCompositionEnd\n  };\n  var selectNode = mode === 'multiple' || mode === 'tags' ? /*#__PURE__*/React.createElement(MultipleSelector, _extends({}, props, sharedProps)) : /*#__PURE__*/React.createElement(SingleSelector, _extends({}, props, sharedProps));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: \"\".concat(prefixCls, \"-selector\"),\n    onClick: onClick,\n    onMouseDown: onMouseDown\n  }, selectNode);\n};\nvar ForwardSelector = /*#__PURE__*/React.forwardRef(Selector);\nForwardSelector.displayName = 'Selector';\nexport default ForwardSelector;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "useRef", "KeyCode", "MultipleSelector", "SingleSelector", "useLock", "isValidateOpenKey", "Selector", "props", "ref", "inputRef", "compositionStatusRef", "prefixCls", "open", "mode", "showSearch", "tokenWithEnter", "onSearch", "onSearchSubmit", "onToggleOpen", "onInputKeyDown", "domRef", "useImperativeHandle", "focus", "current", "blur", "_useLock", "_useLock2", "getInputMouseDown", "setInputMouseDown", "onInternalInputKeyDown", "event", "which", "UP", "DOWN", "preventDefault", "ENTER", "target", "value", "onInternalInputMouseDown", "pastedTextRef", "triggerOnSearch", "onInputCompositionStart", "onInputCompositionEnd", "e", "onInputChange", "test", "replacedText", "replace", "onInputPaste", "clipboardData", "getData", "onClick", "_ref", "isIE", "document", "body", "style", "msTouchAction", "undefined", "setTimeout", "onMouseDown", "inputMouseDown", "sharedProps", "onInputMouseDown", "selectNode", "createElement", "className", "concat", "ForwardSelector", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/Selector/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/**\n * Cursor rule:\n * 1. Only `showSearch` enabled\n * 2. Only `open` is `true`\n * 3. When typing, set `open` to `true` which hit rule of 2\n *\n * Accessibility:\n * - https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html\n */\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport MultipleSelector from './MultipleSelector';\nimport SingleSelector from './SingleSelector';\nimport useLock from '../hooks/useLock';\nimport { isValidateOpenKey } from '../utils/keyUtil';\n\nvar Selector = function Selector(props, ref) {\n  var inputRef = useRef(null);\n  var compositionStatusRef = useRef(false);\n  var prefixCls = props.prefixCls,\n      open = props.open,\n      mode = props.mode,\n      showSearch = props.showSearch,\n      tokenWithEnter = props.tokenWithEnter,\n      onSearch = props.onSearch,\n      onSearchSubmit = props.onSearchSubmit,\n      onToggleOpen = props.onToggleOpen,\n      onInputKeyDown = props.onInputKeyDown,\n      domRef = props.domRef; // ======================= Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        inputRef.current.focus();\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  }); // ====================== Input ======================\n\n  var _useLock = useLock(0),\n      _useLock2 = _slicedToArray(_useLock, 2),\n      getInputMouseDown = _useLock2[0],\n      setInputMouseDown = _useLock2[1];\n\n  var onInternalInputKeyDown = function onInternalInputKeyDown(event) {\n    var which = event.which;\n\n    if (which === KeyCode.UP || which === KeyCode.DOWN) {\n      event.preventDefault();\n    }\n\n    if (onInputKeyDown) {\n      onInputKeyDown(event);\n    }\n\n    if (which === KeyCode.ENTER && mode === 'tags' && !compositionStatusRef.current && !open) {\n      // When menu isn't open, OptionList won't trigger a value change\n      // So when enter is pressed, the tag's input value should be emitted here to let selector know\n      onSearchSubmit === null || onSearchSubmit === void 0 ? void 0 : onSearchSubmit(event.target.value);\n    }\n\n    if (isValidateOpenKey(which)) {\n      onToggleOpen(true);\n    }\n  };\n  /**\n   * We can not use `findDOMNode` sine it will get warning,\n   * have to use timer to check if is input element.\n   */\n\n\n  var onInternalInputMouseDown = function onInternalInputMouseDown() {\n    setInputMouseDown(true);\n  }; // When paste come, ignore next onChange\n\n\n  var pastedTextRef = useRef(null);\n\n  var triggerOnSearch = function triggerOnSearch(value) {\n    if (onSearch(value, true, compositionStatusRef.current) !== false) {\n      onToggleOpen(true);\n    }\n  };\n\n  var onInputCompositionStart = function onInputCompositionStart() {\n    compositionStatusRef.current = true;\n  };\n\n  var onInputCompositionEnd = function onInputCompositionEnd(e) {\n    compositionStatusRef.current = false; // Trigger search again to support `tokenSeparators` with typewriting\n\n    if (mode !== 'combobox') {\n      triggerOnSearch(e.target.value);\n    }\n  };\n\n  var onInputChange = function onInputChange(event) {\n    var value = event.target.value; // Pasted text should replace back to origin content\n\n    if (tokenWithEnter && pastedTextRef.current && /[\\r\\n]/.test(pastedTextRef.current)) {\n      // CRLF will be treated as a single space for input element\n      var replacedText = pastedTextRef.current.replace(/[\\r\\n]+$/, '').replace(/\\r\\n/g, ' ').replace(/[\\r\\n]/g, ' ');\n      value = value.replace(replacedText, pastedTextRef.current);\n    }\n\n    pastedTextRef.current = null;\n    triggerOnSearch(value);\n  };\n\n  var onInputPaste = function onInputPaste(e) {\n    var clipboardData = e.clipboardData;\n    var value = clipboardData.getData('text');\n    pastedTextRef.current = value;\n  };\n\n  var onClick = function onClick(_ref) {\n    var target = _ref.target;\n\n    if (target !== inputRef.current) {\n      // Should focus input if click the selector\n      var isIE = document.body.style.msTouchAction !== undefined;\n\n      if (isIE) {\n        setTimeout(function () {\n          inputRef.current.focus();\n        });\n      } else {\n        inputRef.current.focus();\n      }\n    }\n  };\n\n  var onMouseDown = function onMouseDown(event) {\n    var inputMouseDown = getInputMouseDown();\n\n    if (event.target !== inputRef.current && !inputMouseDown) {\n      event.preventDefault();\n    }\n\n    if (mode !== 'combobox' && (!showSearch || !inputMouseDown) || !open) {\n      if (open) {\n        onSearch('', true, false);\n      }\n\n      onToggleOpen();\n    }\n  }; // ================= Inner Selector ==================\n\n\n  var sharedProps = {\n    inputRef: inputRef,\n    onInputKeyDown: onInternalInputKeyDown,\n    onInputMouseDown: onInternalInputMouseDown,\n    onInputChange: onInputChange,\n    onInputPaste: onInputPaste,\n    onInputCompositionStart: onInputCompositionStart,\n    onInputCompositionEnd: onInputCompositionEnd\n  };\n  var selectNode = mode === 'multiple' || mode === 'tags' ? /*#__PURE__*/React.createElement(MultipleSelector, _extends({}, props, sharedProps)) : /*#__PURE__*/React.createElement(SingleSelector, _extends({}, props, sharedProps));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: \"\".concat(prefixCls, \"-selector\"),\n    onClick: onClick,\n    onMouseDown: onMouseDown\n  }, selectNode);\n};\n\nvar ForwardSelector = /*#__PURE__*/React.forwardRef(Selector);\nForwardSelector.displayName = 'Selector';\nexport default ForwardSelector;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,iBAAiB,QAAQ,kBAAkB;AAEpD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3C,IAAIC,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIU,oBAAoB,GAAGV,MAAM,CAAC,KAAK,CAAC;EACxC,IAAIW,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,IAAI,GAAGL,KAAK,CAACK,IAAI;IACjBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,YAAY,GAAGX,KAAK,CAACW,YAAY;IACjCC,cAAc,GAAGZ,KAAK,CAACY,cAAc;IACrCC,MAAM,GAAGb,KAAK,CAACa,MAAM,CAAC,CAAC;;EAE3BrB,KAAK,CAACsB,mBAAmB,CAACb,GAAG,EAAE,YAAY;IACzC,OAAO;MACLc,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtBb,QAAQ,CAACc,OAAO,CAACD,KAAK,CAAC,CAAC;MAC1B,CAAC;MACDE,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBf,QAAQ,CAACc,OAAO,CAACC,IAAI,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,QAAQ,GAAGrB,OAAO,CAAC,CAAC,CAAC;IACrBsB,SAAS,GAAG5B,cAAc,CAAC2B,QAAQ,EAAE,CAAC,CAAC;IACvCE,iBAAiB,GAAGD,SAAS,CAAC,CAAC,CAAC;IAChCE,iBAAiB,GAAGF,SAAS,CAAC,CAAC,CAAC;EAEpC,IAAIG,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAE;IAClE,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IAEvB,IAAIA,KAAK,KAAK9B,OAAO,CAAC+B,EAAE,IAAID,KAAK,KAAK9B,OAAO,CAACgC,IAAI,EAAE;MAClDH,KAAK,CAACI,cAAc,CAAC,CAAC;IACxB;IAEA,IAAIf,cAAc,EAAE;MAClBA,cAAc,CAACW,KAAK,CAAC;IACvB;IAEA,IAAIC,KAAK,KAAK9B,OAAO,CAACkC,KAAK,IAAItB,IAAI,KAAK,MAAM,IAAI,CAACH,oBAAoB,CAACa,OAAO,IAAI,CAACX,IAAI,EAAE;MACxF;MACA;MACAK,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACa,KAAK,CAACM,MAAM,CAACC,KAAK,CAAC;IACpG;IAEA,IAAIhC,iBAAiB,CAAC0B,KAAK,CAAC,EAAE;MAC5Bb,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIoB,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACjEV,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC,CAAC;;EAGH,IAAIW,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAEhC,IAAIwC,eAAe,GAAG,SAASA,eAAeA,CAACH,KAAK,EAAE;IACpD,IAAIrB,QAAQ,CAACqB,KAAK,EAAE,IAAI,EAAE3B,oBAAoB,CAACa,OAAO,CAAC,KAAK,KAAK,EAAE;MACjEL,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;EAED,IAAIuB,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;IAC/D/B,oBAAoB,CAACa,OAAO,GAAG,IAAI;EACrC,CAAC;EAED,IAAImB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,CAAC,EAAE;IAC5DjC,oBAAoB,CAACa,OAAO,GAAG,KAAK,CAAC,CAAC;;IAEtC,IAAIV,IAAI,KAAK,UAAU,EAAE;MACvB2B,eAAe,CAACG,CAAC,CAACP,MAAM,CAACC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACd,KAAK,EAAE;IAChD,IAAIO,KAAK,GAAGP,KAAK,CAACM,MAAM,CAACC,KAAK,CAAC,CAAC;;IAEhC,IAAItB,cAAc,IAAIwB,aAAa,CAAChB,OAAO,IAAI,QAAQ,CAACsB,IAAI,CAACN,aAAa,CAAChB,OAAO,CAAC,EAAE;MACnF;MACA,IAAIuB,YAAY,GAAGP,aAAa,CAAChB,OAAO,CAACwB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC9GV,KAAK,GAAGA,KAAK,CAACU,OAAO,CAACD,YAAY,EAAEP,aAAa,CAAChB,OAAO,CAAC;IAC5D;IAEAgB,aAAa,CAAChB,OAAO,GAAG,IAAI;IAC5BiB,eAAe,CAACH,KAAK,CAAC;EACxB,CAAC;EAED,IAAIW,YAAY,GAAG,SAASA,YAAYA,CAACL,CAAC,EAAE;IAC1C,IAAIM,aAAa,GAAGN,CAAC,CAACM,aAAa;IACnC,IAAIZ,KAAK,GAAGY,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACzCX,aAAa,CAAChB,OAAO,GAAGc,KAAK;EAC/B,CAAC;EAED,IAAIc,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;IACnC,IAAIhB,MAAM,GAAGgB,IAAI,CAAChB,MAAM;IAExB,IAAIA,MAAM,KAAK3B,QAAQ,CAACc,OAAO,EAAE;MAC/B;MACA,IAAI8B,IAAI,GAAGC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,aAAa,KAAKC,SAAS;MAE1D,IAAIL,IAAI,EAAE;QACRM,UAAU,CAAC,YAAY;UACrBlD,QAAQ,CAACc,OAAO,CAACD,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLb,QAAQ,CAACc,OAAO,CAACD,KAAK,CAAC,CAAC;MAC1B;IACF;EACF,CAAC;EAED,IAAIsC,WAAW,GAAG,SAASA,WAAWA,CAAC9B,KAAK,EAAE;IAC5C,IAAI+B,cAAc,GAAGlC,iBAAiB,CAAC,CAAC;IAExC,IAAIG,KAAK,CAACM,MAAM,KAAK3B,QAAQ,CAACc,OAAO,IAAI,CAACsC,cAAc,EAAE;MACxD/B,KAAK,CAACI,cAAc,CAAC,CAAC;IACxB;IAEA,IAAIrB,IAAI,KAAK,UAAU,KAAK,CAACC,UAAU,IAAI,CAAC+C,cAAc,CAAC,IAAI,CAACjD,IAAI,EAAE;MACpE,IAAIA,IAAI,EAAE;QACRI,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;MAC3B;MAEAE,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC,CAAC;;EAGH,IAAI4C,WAAW,GAAG;IAChBrD,QAAQ,EAAEA,QAAQ;IAClBU,cAAc,EAAEU,sBAAsB;IACtCkC,gBAAgB,EAAEzB,wBAAwB;IAC1CM,aAAa,EAAEA,aAAa;IAC5BI,YAAY,EAAEA,YAAY;IAC1BP,uBAAuB,EAAEA,uBAAuB;IAChDC,qBAAqB,EAAEA;EACzB,CAAC;EACD,IAAIsB,UAAU,GAAGnD,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,GAAG,aAAad,KAAK,CAACkE,aAAa,CAAC/D,gBAAgB,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAEuD,WAAW,CAAC,CAAC,GAAG,aAAa/D,KAAK,CAACkE,aAAa,CAAC9D,cAAc,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAEuD,WAAW,CAAC,CAAC;EACnO,OAAO,aAAa/D,KAAK,CAACkE,aAAa,CAAC,KAAK,EAAE;IAC7CzD,GAAG,EAAEY,MAAM;IACX8C,SAAS,EAAE,EAAE,CAACC,MAAM,CAACxD,SAAS,EAAE,WAAW,CAAC;IAC5CwC,OAAO,EAAEA,OAAO;IAChBS,WAAW,EAAEA;EACf,CAAC,EAAEI,UAAU,CAAC;AAChB,CAAC;AAED,IAAII,eAAe,GAAG,aAAarE,KAAK,CAACsE,UAAU,CAAC/D,QAAQ,CAAC;AAC7D8D,eAAe,CAACE,WAAW,GAAG,UAAU;AACxC,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}