{"ast": null, "code": "import InternalAvatar from './avatar';\nimport Group from './group';\nvar Avatar = InternalAvatar;\nAvatar.Group = Group;\nexport { Group };\nexport default Avatar;", "map": {"version": 3, "names": ["InternalAvatar", "Group", "Avatar"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/avatar/index.js"], "sourcesContent": ["import InternalAvatar from './avatar';\nimport Group from './group';\nvar Avatar = InternalAvatar;\nAvatar.Group = Group;\nexport { Group };\nexport default Avatar;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,UAAU;AACrC,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,MAAM,GAAGF,cAAc;AAC3BE,MAAM,CAACD,KAAK,GAAGA,KAAK;AACpB,SAASA,KAAK;AACd,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}