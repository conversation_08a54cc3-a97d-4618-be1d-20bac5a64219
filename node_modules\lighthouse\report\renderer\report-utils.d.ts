export class ReportUtils {
    /**
     * Returns a new LHR that's reshaped for slightly better ergonomics within the report rendereer.
     * Also, sets up the localized UI strings used within renderer and makes changes to old LHRs to be
     * compatible with current renderer.
     * The LHR passed in is not mutated.
     * TOD<PERSON>(team): we all agree the LHR shape change is technical debt we should fix
     * @param {LH.Result} lhr
     * @return {LH.ReportResult}
     */
    static prepareReportResult(lhr: LH.Result): LH.ReportResult;
    /**
     * Given an audit's details, identify and return a URL locator function that
     * can be called later with an `item` to extract the URL of it.
     * @param {LH.FormattedIcu<LH.Audit.Details.TableColumnHeading[]>} headings
     * @return {((item: LH.FormattedIcu<LH.Audit.Details.TableItem>) => string|undefined)=}
     */
    static getUrlLocatorFn(headings: LH.FormattedIcu<LH.Audit.Details.TableColumnHeading[]>): ((item: LH.FormattedIcu<LH.Audit.Details.TableItem>) => string | undefined) | undefined;
    /**
     * Mark TableItems/OpportunityItems with entity names.
     * @param {LH.Result.Entities} entities
     * @param {LH.FormattedIcu<LH.Audit.Details.Opportunity|LH.Audit.Details.Table>} details
     */
    static classifyEntities(entities: LH.Result.Entities, details: LH.FormattedIcu<LH.Audit.Details.Opportunity | LH.Audit.Details.Table>): void;
    /**
     * Returns a comparator created from the supplied list of keys
     * @param {Array<string>} sortedBy
     * @return {((a: LH.Audit.Details.TableItem, b: LH.Audit.Details.TableItem) => number)}
     */
    static getTableItemSortComparator(sortedBy: Array<string>): (a: LH.Audit.Details.TableItem, b: LH.Audit.Details.TableItem) => number;
    /**
     * @param {LH.Result['configSettings']} settings
     * @return {!{deviceEmulation: string, screenEmulation?: string, networkThrottling: string, cpuThrottling: string, summary: string}}
     */
    static getEmulationDescriptions(settings: LH.Result['configSettings']): {
        deviceEmulation: string;
        screenEmulation?: string | undefined;
        networkThrottling: string;
        cpuThrottling: string;
        summary: string;
    };
    /**
     * Used to determine if the "passed" for the purposes of showing up in the "failed" or "passed"
     * sections of the report.
     *
     * @param {{score: (number|null), scoreDisplayMode: string}} audit
     * @return {boolean}
     */
    static showAsPassed(audit: {
        score: (number | null);
        scoreDisplayMode: string;
    }): boolean;
    /**
     * Convert a score to a rating label.
     * TODO: Return `'error'` for `score === null && !scoreDisplayMode`.
     *
     * @param {number|null} score
     * @param {string=} scoreDisplayMode
     * @return {string}
     */
    static calculateRating(score: number | null, scoreDisplayMode?: string | undefined): string;
    /**
     * @param {LH.ReportResult.Category} category
     */
    static calculateCategoryFraction(category: LH.ReportResult.Category): {
        numPassed: number;
        numPassableAudits: number;
        numInformative: number;
        totalWeight: number;
    };
    /**
     * @param {string} categoryId
     */
    static isPluginCategory(categoryId: string): boolean;
    /**
     * @param {LH.Result.GatherMode} gatherMode
     */
    static shouldDisplayAsFraction(gatherMode: LH.Result.GatherMode): boolean;
}
export namespace UIStrings {
    const varianceDisclaimer: string;
    const calculatorLink: string;
    const showRelevantAudits: string;
    const opportunityResourceColumnLabel: string;
    const opportunitySavingsColumnLabel: string;
    const errorMissingAuditInfo: string;
    const errorLabel: string;
    const warningHeader: string;
    const warningAuditsGroupTitle: string;
    const passedAuditsGroupTitle: string;
    const notApplicableAuditsGroupTitle: string;
    const manualAuditsGroupTitle: string;
    const toplevelWarningsMessage: string;
    const crcInitialNavigation: string;
    const crcLongestDurationLabel: string;
    const snippetExpandButtonLabel: string;
    const snippetCollapseButtonLabel: string;
    const lsPerformanceCategoryDescription: string;
    const labDataTitle: string;
    const thirdPartyResourcesLabel: string;
    const viewTreemapLabel: string;
    const viewTraceLabel: string;
    const viewOriginalTraceLabel: string;
    const dropdownPrintSummary: string;
    const dropdownPrintExpanded: string;
    const dropdownCopyJSON: string;
    const dropdownSaveHTML: string;
    const dropdownSaveJSON: string;
    const dropdownViewer: string;
    const dropdownSaveGist: string;
    const dropdownDarkTheme: string;
    const runtimeSettingsDevice: string;
    const runtimeSettingsNetworkThrottling: string;
    const runtimeSettingsCPUThrottling: string;
    const runtimeSettingsUANetwork: string;
    const runtimeSettingsBenchmark: string;
    const runtimeSettingsAxeVersion: string;
    const runtimeSettingsScreenEmulation: string;
    const footerIssue: string;
    const runtimeNoEmulation: string;
    const runtimeMobileEmulation: string;
    const runtimeDesktopEmulation: string;
    const runtimeUnknown: string;
    const runtimeSingleLoad: string;
    const runtimeAnalysisWindow: string;
    const runtimeSingleLoadTooltip: string;
    const throttlingProvided: string;
    const show: string;
    const hide: string;
    const expandView: string;
    const collapseView: string;
    const runtimeSlow4g: string;
    const runtimeCustom: string;
    const firstPartyChipLabel: string;
    const openInANewTabTooltip: string;
    const unattributable: string;
}
//# sourceMappingURL=report-utils.d.ts.map