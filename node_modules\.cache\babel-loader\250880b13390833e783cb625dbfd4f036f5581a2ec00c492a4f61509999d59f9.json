{"ast": null, "code": "import GestioneOdiniAgente from '../common/agenti/gestioneOrdiniAgenti';\nimport GestioneLogistica from '../common/logistica/gestioneLogistica';\nimport GestioneConsegne from '../common/autista/gestioneConsegne';\nimport DashboardDistributore from '../common/distributore/dashboard/dashboardDistributore';\nimport DashboardAffiliato from '../common/affiliato/dashboard/dashboardAffiliato';\nimport DashboardPDV from '../common/PDV/dashboardPDV/dashboardPDV';\nimport { APIRequest /* baseURL */ } from '../components/generalizzazioni/apireq';\nimport dashboardAmministratore from '../common/amministratore/DashboardAmministratore';\nimport Dashboard<PERSON>hain from '../common/chain/dashboard/dashboardChain';\nimport DashboardRING from '../common/ring/dashboard/dashboardRing';\n/* import axios from 'axios'; */\n\nconst TOKEN_KEY = 'login_token';\nconst USERROLE = 'role';\nexport const login = async props => {\n  let request = {\n    username: document.getElementById('loginEmail').value,\n    password: document.getElementById('loginPassword').value\n  };\n  try {\n    var resp = await APIRequest('POST', 'auth/', request);\n    console.log('Login response:', resp.status, resp.data);\n    if (resp.status === 200) {\n      var _resp$data$data, _resp$data$data2;\n      // Handle different response structures\n      const token = resp.data.token || ((_resp$data$data = resp.data.data) === null || _resp$data$data === void 0 ? void 0 : _resp$data$data.token);\n      const user = resp.data.user || ((_resp$data$data2 = resp.data.data) === null || _resp$data$data2 === void 0 ? void 0 : _resp$data$data2.user);\n      console.log('Login response data:', {\n        token: token ? 'present' : 'missing',\n        user: user ? 'present' : 'missing',\n        fullResponse: resp.data\n      });\n      if (!token || !user) {\n        console.error('Invalid response structure:', resp.data);\n        throw new Error('Risposta del server non valida');\n      }\n\n      // Validate token is not corrupted\n      if (typeof token !== 'string' || token.length < 10) {\n        console.error('Invalid token:', token);\n        throw new Error('Token non valido ricevuto dal server');\n      }\n\n      // Validate user object\n      if (!user.role || !user.username) {\n        console.error('Invalid user object:', user);\n        throw new Error('Dati utente non validi');\n      }\n      localStorage.setItem(TOKEN_KEY, token);\n      localStorage.setItem(USERROLE, user.role);\n      localStorage.setItem(\"user\", JSON.stringify(user));\n      localStorage.setItem(\"userid\", user.idRegistry ? JSON.stringify(user.idRegistry) : \"\");\n      localStorage.setItem(\"OrdineRecuperato\", '');\n      localStorage.setItem(\"Prodotti\", []);\n      localStorage.setItem(\"Cart\", []);\n      localStorage.setItem(\"datiComodo\", 0);\n      localStorage.setItem(\"DatiConsegna\", '');\n      localStorage.setItem(\"geoHistory\", {});\n      var body = {\n        passeggeri: 10,\n        data: new Date(),\n        notFormatted: new Date(),\n        stato: false\n      };\n      localStorage.setItem(\"PredictionFn\", JSON.stringify(body));\n      window.sessionStorage.setItem(\"Carrello\", 0);\n      window.sessionStorage.setItem(\"totCart\", \"0,00 €\");\n      window.sessionStorage.setItem(\"Error\", '');\n      window.sessionStorage.setItem(\"documentType\", '');\n      window.sessionStorage.setItem(\"CodeError\", 0);\n      window.sessionStorage.setItem(\"idWarehouse\", 0);\n      window.sessionStorage.setItem(\"idSupplier\", 0);\n      window.sessionStorage.setItem(\"idRetailer\", 0);\n      window.sessionStorage.setItem(\"idDocument\", 0);\n      window.sessionStorage.setItem(\"PDV\", null);\n      window.sessionStorage.setItem(\"affForOrd\", false);\n      sessionStorage.setItem('numberOfCustomer', 0);\n      const userRole = user.role;\n      const components = {\n        DISTRIBUTORE: DashboardDistributore,\n        AFFILIATO: DashboardAffiliato,\n        AGENTE: GestioneOdiniAgente,\n        LOGISTICA: GestioneLogistica,\n        AUTISTA: GestioneConsegne,\n        PDV: DashboardPDV,\n        ADMIN: dashboardAmministratore,\n        CHAIN: DashboardChain,\n        RING: DashboardRING\n      };\n      const Component = components[userRole];\n      console.log(Component);\n\n      //  <Component />\n\n      // Redirect on success\n      if (props && props.history) {\n        props.history.push('/');\n      } else {\n        window.location.href = '/';\n      }\n    } else {\n      throw new Error('Credenziali non valide');\n    }\n  } catch (error) {\n    throw error; // Re-throw to be handled by component\n  }\n};\nexport const logout = () => {\n  localStorage.removeItem(TOKEN_KEY);\n  localStorage.removeItem(USERROLE);\n};\nexport const isLogin = () => {\n  if (localStorage.getItem(TOKEN_KEY) && localStorage.getItem(USERROLE)) {\n    return true;\n  }\n  return false;\n};", "map": {"version": 3, "names": ["GestioneOdiniAgente", "GestioneLogistica", "GestioneConsegne", "DashboardDistributore", "DashboardAffiliato", "DashboardPDV", "APIRequest", "dashboardAmministratore", "DashboardChain", "DashboardRING", "TOKEN_KEY", "USERROLE", "login", "props", "request", "username", "document", "getElementById", "value", "password", "resp", "console", "log", "status", "data", "_resp$data$data", "_resp$data$data2", "token", "user", "fullResponse", "error", "Error", "length", "role", "localStorage", "setItem", "JSON", "stringify", "idRegistry", "body", "<PERSON><PERSON><PERSON><PERSON>", "Date", "notFormatted", "stato", "window", "sessionStorage", "userRole", "components", "DISTRIBUTORE", "AFFILIATO", "AGENTE", "LOGISTICA", "AUTISTA", "PDV", "ADMIN", "CHAIN", "RING", "Component", "history", "push", "location", "href", "logout", "removeItem", "is<PERSON>ogin", "getItem"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/utils/index.js"], "sourcesContent": ["import GestioneOdiniAgente from '../common/agenti/gestioneOrdiniAgenti';\nimport GestioneLogistica from '../common/logistica/gestioneLogistica';\nimport GestioneConsegne from '../common/autista/gestioneConsegne';\nimport DashboardDistributore from '../common/distributore/dashboard/dashboardDistributore';\nimport DashboardAffiliato from '../common/affiliato/dashboard/dashboardAffiliato';\nimport DashboardPDV from '../common/PDV/dashboardPDV/dashboardPDV';\nimport { APIRequest/* baseURL */ } from '../components/generalizzazioni/apireq';\nimport dashboardAmministratore from '../common/amministratore/DashboardAmministratore';\nimport DashboardChain from '../common/chain/dashboard/dashboardChain';\nimport DashboardRING from '../common/ring/dashboard/dashboardRing';\n/* import axios from 'axios'; */\n\nconst TOKEN_KEY = 'login_token';\nconst USERROLE = 'role';\n\nexport const login = async (props) => {\n    let request = {\n        username: document.getElementById('loginEmail').value,\n        password: document.getElementById('loginPassword').value\n    }\n\n    try {\n        var resp = await APIRequest('POST', 'auth/', request);\n        console.log('Login response:', resp.status, resp.data);\n\n    if (resp.status === 200) {\n        // Handle different response structures\n        const token = resp.data.token || resp.data.data?.token;\n        const user = resp.data.user || resp.data.data?.user;\n\n        console.log('Login response data:', {\n            token: token ? 'present' : 'missing',\n            user: user ? 'present' : 'missing',\n            fullResponse: resp.data\n        });\n\n        if (!token || !user) {\n            console.error('Invalid response structure:', resp.data);\n            throw new Error('Risposta del server non valida');\n        }\n\n        // Validate token is not corrupted\n        if (typeof token !== 'string' || token.length < 10) {\n            console.error('Invalid token:', token);\n            throw new Error('Token non valido ricevuto dal server');\n        }\n\n        // Validate user object\n        if (!user.role || !user.username) {\n            console.error('Invalid user object:', user);\n            throw new Error('Dati utente non validi');\n        }\n\n        localStorage.setItem(TOKEN_KEY, token);\n        localStorage.setItem(USERROLE, user.role);\n        localStorage.setItem(\"user\", JSON.stringify(user));\n        localStorage.setItem(\"userid\", user.idRegistry ? JSON.stringify(user.idRegistry) : \"\");\n        localStorage.setItem(\"OrdineRecuperato\", '');\n        localStorage.setItem(\"Prodotti\", []);\n        localStorage.setItem(\"Cart\", []);\n        localStorage.setItem(\"datiComodo\", 0);\n        localStorage.setItem(\"DatiConsegna\", '');\n        localStorage.setItem(\"geoHistory\", {});\n        var body = { passeggeri: 10, data: new Date(), notFormatted: new Date(), stato: false }\n        localStorage.setItem(\"PredictionFn\", JSON.stringify(body));\n        window.sessionStorage.setItem(\"Carrello\", 0);\n        window.sessionStorage.setItem(\"totCart\", \"0,00 €\");\n        window.sessionStorage.setItem(\"Error\", '');\n        window.sessionStorage.setItem(\"documentType\", '')\n        window.sessionStorage.setItem(\"CodeError\", 0);\n        window.sessionStorage.setItem(\"idWarehouse\", 0);\n        window.sessionStorage.setItem(\"idSupplier\", 0);\n        window.sessionStorage.setItem(\"idRetailer\", 0);\n        window.sessionStorage.setItem(\"idDocument\", 0)\n        window.sessionStorage.setItem(\"PDV\", null)\n        window.sessionStorage.setItem(\"affForOrd\", false)\n        sessionStorage.setItem('numberOfCustomer', 0)\n        const userRole = user.role;\n\n        const components = {\n            DISTRIBUTORE: DashboardDistributore,\n            AFFILIATO: DashboardAffiliato,\n            AGENTE: GestioneOdiniAgente,\n            LOGISTICA: GestioneLogistica,\n            AUTISTA: GestioneConsegne,\n            PDV: DashboardPDV,\n            ADMIN: dashboardAmministratore,\n            CHAIN: DashboardChain,\n            RING: DashboardRING\n        }\n\n        const Component = components[userRole];\n        console.log(Component)\n\n        //  <Component />\n\n        // Redirect on success\n        if (props && props.history) {\n            props.history.push('/');\n        } else {\n            window.location.href = '/';\n        }\n    } else {\n        throw new Error('Credenziali non valide');\n    }\n    } catch (error) {\n        throw error; // Re-throw to be handled by component\n    }\n}\n\nexport const logout = () => {\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USERROLE);\n}\n\nexport const isLogin = () => {\n    if (localStorage.getItem(TOKEN_KEY) && localStorage.getItem(USERROLE)) {\n        return true;\n    }\n    return false;\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAOC,qBAAqB,MAAM,wDAAwD;AAC1F,OAAOC,kBAAkB,MAAM,kDAAkD;AACjF,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,UAAU,sBAAqB,uCAAuC;AAC/E,OAAOC,uBAAuB,MAAM,kDAAkD;AACtF,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,wCAAwC;AAClE;;AAEA,MAAMC,SAAS,GAAG,aAAa;AAC/B,MAAMC,QAAQ,GAAG,MAAM;AAEvB,OAAO,MAAMC,KAAK,GAAG,MAAOC,KAAK,IAAK;EAClC,IAAIC,OAAO,GAAG;IACVC,QAAQ,EAAEC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK;IACrDC,QAAQ,EAAEH,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAACC;EACvD,CAAC;EAED,IAAI;IACA,IAAIE,IAAI,GAAG,MAAMd,UAAU,CAAC,MAAM,EAAE,OAAO,EAAEQ,OAAO,CAAC;IACrDO,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACI,IAAI,CAAC;IAE1D,IAAIJ,IAAI,CAACG,MAAM,KAAK,GAAG,EAAE;MAAA,IAAAE,eAAA,EAAAC,gBAAA;MACrB;MACA,MAAMC,KAAK,GAAGP,IAAI,CAACI,IAAI,CAACG,KAAK,MAAAF,eAAA,GAAIL,IAAI,CAACI,IAAI,CAACA,IAAI,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,KAAK;MACtD,MAAMC,IAAI,GAAGR,IAAI,CAACI,IAAI,CAACI,IAAI,MAAAF,gBAAA,GAAIN,IAAI,CAACI,IAAI,CAACA,IAAI,cAAAE,gBAAA,uBAAdA,gBAAA,CAAgBE,IAAI;MAEnDP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAChCK,KAAK,EAAEA,KAAK,GAAG,SAAS,GAAG,SAAS;QACpCC,IAAI,EAAEA,IAAI,GAAG,SAAS,GAAG,SAAS;QAClCC,YAAY,EAAET,IAAI,CAACI;MACvB,CAAC,CAAC;MAEF,IAAI,CAACG,KAAK,IAAI,CAACC,IAAI,EAAE;QACjBP,OAAO,CAACS,KAAK,CAAC,6BAA6B,EAAEV,IAAI,CAACI,IAAI,CAAC;QACvD,MAAM,IAAIO,KAAK,CAAC,gCAAgC,CAAC;MACrD;;MAEA;MACA,IAAI,OAAOJ,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACK,MAAM,GAAG,EAAE,EAAE;QAChDX,OAAO,CAACS,KAAK,CAAC,gBAAgB,EAAEH,KAAK,CAAC;QACtC,MAAM,IAAII,KAAK,CAAC,sCAAsC,CAAC;MAC3D;;MAEA;MACA,IAAI,CAACH,IAAI,CAACK,IAAI,IAAI,CAACL,IAAI,CAACb,QAAQ,EAAE;QAC9BM,OAAO,CAACS,KAAK,CAAC,sBAAsB,EAAEF,IAAI,CAAC;QAC3C,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC7C;MAEAG,YAAY,CAACC,OAAO,CAACzB,SAAS,EAAEiB,KAAK,CAAC;MACtCO,YAAY,CAACC,OAAO,CAACxB,QAAQ,EAAEiB,IAAI,CAACK,IAAI,CAAC;MACzCC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC,CAAC;MAClDM,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEP,IAAI,CAACU,UAAU,GAAGF,IAAI,CAACC,SAAS,CAACT,IAAI,CAACU,UAAU,CAAC,GAAG,EAAE,CAAC;MACtFJ,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;MAC5CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MAChCD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MACrCD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;MACtC,IAAII,IAAI,GAAG;QAAEC,UAAU,EAAE,EAAE;QAAEhB,IAAI,EAAE,IAAIiB,IAAI,CAAC,CAAC;QAAEC,YAAY,EAAE,IAAID,IAAI,CAAC,CAAC;QAAEE,KAAK,EAAE;MAAM,CAAC;MACvFT,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACE,IAAI,CAAC,CAAC;MAC1DK,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;MAC5CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC;MAClDS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAC1CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACjDS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;MAC7CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;MAC/CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MAC9CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MAC9CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;MAC9CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;MAC1CS,MAAM,CAACC,cAAc,CAACV,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;MACjDU,cAAc,CAACV,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;MAC7C,MAAMW,QAAQ,GAAGlB,IAAI,CAACK,IAAI;MAE1B,MAAMc,UAAU,GAAG;QACfC,YAAY,EAAE7C,qBAAqB;QACnC8C,SAAS,EAAE7C,kBAAkB;QAC7B8C,MAAM,EAAElD,mBAAmB;QAC3BmD,SAAS,EAAElD,iBAAiB;QAC5BmD,OAAO,EAAElD,gBAAgB;QACzBmD,GAAG,EAAEhD,YAAY;QACjBiD,KAAK,EAAE/C,uBAAuB;QAC9BgD,KAAK,EAAE/C,cAAc;QACrBgD,IAAI,EAAE/C;MACV,CAAC;MAED,MAAMgD,SAAS,GAAGV,UAAU,CAACD,QAAQ,CAAC;MACtCzB,OAAO,CAACC,GAAG,CAACmC,SAAS,CAAC;;MAEtB;;MAEA;MACA,IAAI5C,KAAK,IAAIA,KAAK,CAAC6C,OAAO,EAAE;QACxB7C,KAAK,CAAC6C,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MAC3B,CAAC,MAAM;QACHf,MAAM,CAACgB,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACJ,CAAC,MAAM;MACH,MAAM,IAAI9B,KAAK,CAAC,wBAAwB,CAAC;IAC7C;EACA,CAAC,CAAC,OAAOD,KAAK,EAAE;IACZ,MAAMA,KAAK,CAAC,CAAC;EACjB;AACJ,CAAC;AAED,OAAO,MAAMgC,MAAM,GAAGA,CAAA,KAAM;EACxB5B,YAAY,CAAC6B,UAAU,CAACrD,SAAS,CAAC;EAClCwB,YAAY,CAAC6B,UAAU,CAACpD,QAAQ,CAAC;AACrC,CAAC;AAED,OAAO,MAAMqD,OAAO,GAAGA,CAAA,KAAM;EACzB,IAAI9B,YAAY,CAAC+B,OAAO,CAACvD,SAAS,CAAC,IAAIwB,YAAY,CAAC+B,OAAO,CAACtD,QAAQ,CAAC,EAAE;IACnE,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}