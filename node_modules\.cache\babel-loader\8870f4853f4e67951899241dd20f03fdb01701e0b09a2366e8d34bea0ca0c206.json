{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ThreeDots = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar ThreeDots = function ThreeDots(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 120 30\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"15\",\n    cy: \"15\",\n    r: props.radius + 6\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"15\",\n    to: \"15\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"15;9;15\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"1\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"1;.5;1\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"60\",\n    cy: \"15\",\n    r: props.radius,\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"0.3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"9\",\n    to: \"9\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"9;15;9\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"0.5\",\n    to: \"0.5\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \".5;1;.5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"105\",\n    cy: \"15\",\n    r: props.radius + 6\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"15\",\n    to: \"15\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"15;9;15\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"1\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"1;.5;1\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })));\n};\nexports.ThreeDots = ThreeDots;\nThreeDots.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nThreeDots.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 9\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ThreeDots", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "width", "height", "viewBox", "xmlns", "fill", "color", "label", "cx", "cy", "r", "radius", "attributeName", "from", "to", "begin", "dur", "values", "calcMode", "repeatCount", "propTypes", "oneOfType", "string", "number", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/ThreeDots.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ThreeDots = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar ThreeDots = function ThreeDots(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    width: props.width,\n    height: props.height,\n    viewBox: \"0 0 120 30\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: props.color,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"15\",\n    cy: \"15\",\n    r: props.radius + 6\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"15\",\n    to: \"15\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"15;9;15\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"1\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"1;.5;1\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"60\",\n    cy: \"15\",\n    r: props.radius,\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"0.3\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"9\",\n    to: \"9\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"9;15;9\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"0.5\",\n    to: \"0.5\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \".5;1;.5\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"105\",\n    cy: \"15\",\n    r: props.radius + 6\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"r\",\n    from: \"15\",\n    to: \"15\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"15;9;15\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    attributeName: \"fillOpacity\",\n    from: \"1\",\n    to: \"1\",\n    begin: \"0s\",\n    dur: \"0.8s\",\n    values: \"1;.5;1\",\n    calcMode: \"linear\",\n    repeatCount: \"indefinite\"\n  })));\n};\n\nexports.ThreeDots = ThreeDots;\nThreeDots.propTypes = {\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  color: _propTypes[\"default\"].string,\n  label: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number\n};\nThreeDots.defaultProps = {\n  height: 80,\n  width: 80,\n  color: \"green\",\n  label: \"audio-loading\",\n  radius: 9\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAE1B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,SAAS,GAAG,SAASA,SAASA,CAACO,KAAK,EAAE;EACxC,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAEN,KAAK,CAACO,KAAK;IACjB,YAAY,EAAEP,KAAK,CAACQ;EACtB,CAAC,EAAE,aAAad,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEX,KAAK,CAACY,MAAM,GAAG;EACpB,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDY,aAAa,EAAE,GAAG;IAClBC,IAAI,EAAE,IAAI;IACVC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa1B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DY,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,GAAG;IACTC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa1B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEX,KAAK,CAACY,MAAM;IACfC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,GAAG;IACTC,EAAE,EAAE;EACN,CAAC,EAAE,aAAarB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDY,aAAa,EAAE,GAAG;IAClBC,IAAI,EAAE,GAAG;IACTC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa1B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DY,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,KAAK;IACXC,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,EAAE,aAAa1B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEX,KAAK,CAACY,MAAM,GAAG;EACpB,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDY,aAAa,EAAE,GAAG;IAClBC,IAAI,EAAE,IAAI;IACVC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAa1B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IAC1DY,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,GAAG;IACTC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAED7B,OAAO,CAACE,SAAS,GAAGA,SAAS;AAC7BA,SAAS,CAAC4B,SAAS,GAAG;EACpBlB,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACyB,SAAS,CAAC,CAACzB,UAAU,CAAC,SAAS,CAAC,CAAC0B,MAAM,EAAE1B,UAAU,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAC,CAAC;EACrGtB,KAAK,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACyB,SAAS,CAAC,CAACzB,UAAU,CAAC,SAAS,CAAC,CAAC0B,MAAM,EAAE1B,UAAU,CAAC,SAAS,CAAC,CAAC2B,MAAM,CAAC,CAAC;EACpGjB,KAAK,EAAEV,UAAU,CAAC,SAAS,CAAC,CAAC0B,MAAM;EACnCf,KAAK,EAAEX,UAAU,CAAC,SAAS,CAAC,CAAC0B,MAAM;EACnCX,MAAM,EAAEf,UAAU,CAAC,SAAS,CAAC,CAAC2B;AAChC,CAAC;AACD/B,SAAS,CAACgC,YAAY,GAAG;EACvBtB,MAAM,EAAE,EAAE;EACVD,KAAK,EAAE,EAAE;EACTK,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,eAAe;EACtBI,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}