{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\gestioneAgenti.jsx\";\n/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneAgenti - operazioni sugli agenti\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport UtenteAgente from \"../../aggiunta_dati/utenteAgente\";\nimport AggiungiAgenti from \"../../aggiunta_dati/aggiungiAgenti\";\nimport VisualizzaPDV from \"../../aggiunta_dati/visualizzaPDV\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { Toast } from \"primereact/toast\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { agente } from \"../../components/route\";\nimport \"../../css/DataTableDemo.css\";\nimport 'antd/dist/antd.min.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GestioneAgenti extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      customerName: \"\",\n      address: \"\",\n      pIva: \"\",\n      email: \"\",\n      isValid: \"\",\n      createAt: \"\",\n      updateAt: \"\"\n    };\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiAgente = this.aggiungiAgente.bind(this);\n    this.hideaggiungiAgente = this.hideaggiungiAgente.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAgente = this.hideUtenteAgente.bind(this);\n    this.hideVisualizzaPDV = this.hideVisualizzaPDV.bind(this);\n    this.viewPDV = this.viewPDV.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount(results) {\n    await APIRequest(\"GET\", \"affiliate/\").then(res => {\n      for (var entry of res.data) {\n        var x = {\n          id: entry.id,\n          idRegistry: entry.idRegistry,\n          firstName: entry.idRegistry2.firstName,\n          lastName: entry.idRegistry2.lastName,\n          address: entry.idRegistry2.address,\n          pIva: entry.idRegistry2.pIva,\n          email: entry.idRegistry2.email,\n          cap: entry.idRegistry2.cap,\n          city: entry.idRegistry2.city,\n          externalCode: entry.idRegistry2.externalCode,\n          idAgente: entry.idRegistry2.idAgente,\n          tel: entry.idRegistry2.tel,\n          isValid: entry.idRegistry2.isValid,\n          createdAt: entry.createdAt,\n          updateAt: entry.updateAt,\n          users: entry.idRegistry2.users\n        };\n        let controllo = false;\n        if (x.users.length > 0) {\n          if (x.users.length > 1) {\n            x.users.forEach(element => {\n              if (element.role === agente) {\n                controllo = true;\n              }\n            });\n            if (controllo === true) {\n              this.state.results.push(x);\n            }\n          } else {\n            if (x.users[0].role === agente) {\n              this.state.results.push(x);\n            }\n          }\n        } else {\n          this.state.results.push(x);\n        }\n      }\n      this.setState(state => _objectSpread(_objectSpread(_objectSpread({}, state), results), {}, {\n        loading: false\n      }));\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli agenti. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData) {\n    localStorage.setItem(\"datiComodo\", rowData.idRegistry);\n    this.setState({\n      resultDialog3: true\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAgente() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiAgente() {\n    this.setState({\n      resultDialog2: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAgente() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"affiliate/?id=\" + this.state.result.id;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Agente eliminato con successo\",\n      life: 3000\n    });\n    window.location.reload();\n  }\n  viewPDV(result) {\n    this.setState({\n      result,\n      resultDialog: true\n    });\n  }\n  hideVisualizzaPDV() {\n    this.setState({\n      resultDialog: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideVisualizzaPDV,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideaggiungiAgente,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideUtenteAgente,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this);\n    const fields = [{\n      field: \"firstName\",\n      header: Costanti.rSociale,\n      body: \"firstName\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"address\",\n      header: Costanti.Indirizzo,\n      body: \"address\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"city\",\n      header: Costanti.Città,\n      body: \"city\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"cap\",\n      header: Costanti.CodPost,\n      body: \"cap\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"pIva\",\n      header: Costanti.pIva,\n      body: \"pIva\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tel\",\n      header: Costanti.Tel,\n      body: \"tel\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"email\",\n      header: Costanti.Email,\n      body: \"email\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"isValid\",\n      header: Costanti.Validità,\n      body: \"isValid\",\n      showHeader: true\n    }, {\n      field: \"user\",\n      header: Costanti.AssUser,\n      body: \"userBodyTemplate\",\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisCli,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 37\n      }, this),\n      handler: this.viewPDV\n    }, {\n      name: Costanti.addUser,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-user-plus\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 38\n      }, this),\n      handler: this.addUser\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 38\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    const items = [{\n      label: Costanti.AggAgg,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiAgente();\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.gestioneAgenti\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          splitButtonClass: true,\n          items: items,\n          selectionMode: \"single\",\n          cellSelection: true,\n          fileNames: \"Agenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: Costanti.AggUtAg,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideUtenteAgente,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UtenteAgente, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.AggAgg,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hideaggiungiAgente,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AggiungiAgenti, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.PDV,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideVisualizzaPDV,\n        children: [/*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(VisualizzaPDV, {\n          result: this.state.result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteCli, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.firstName, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default GestioneAgenti;", "map": {"version": 3, "names": ["React", "Component", "Nav", "Caricamento", "UtenteAgente", "AggiungiAgenti", "VisualizzaPDV", "CustomDataTable", "Toast", "<PERSON><PERSON>", "Dialog", "<PERSON><PERSON>", "APIRequest", "agente", "jsxDEV", "_jsxDEV", "GestioneAgenti", "constructor", "props", "emptyResult", "id", "customerName", "address", "pIva", "email", "<PERSON><PERSON><PERSON><PERSON>", "createAt", "updateAt", "state", "results", "resultDialog", "resultDialog2", "resultDialog3", "deleteResultDialog", "submitted", "result", "globalFilter", "loading", "confirmDeleteResult", "bind", "aggiungiAgente", "hideaggiungiAgente", "deleteResult", "hideDeleteResultDialog", "addUser", "hideUtenteAgente", "hideVisualizzaPDV", "viewPDV", "componentDidMount", "then", "res", "entry", "data", "x", "idRegistry", "firstName", "idRegistry2", "lastName", "cap", "city", "externalCode", "idAgente", "tel", "createdAt", "users", "controllo", "length", "for<PERSON>ach", "element", "role", "push", "setState", "_objectSpread", "catch", "e", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "rowData", "localStorage", "setItem", "filter", "val", "url", "window", "location", "reload", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "resultDialogFooter3", "deleteResultDialogFooter", "label", "icon", "Si", "fields", "field", "header", "rSociale", "body", "sortable", "showHeader", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Tel", "Email", "Validità", "AssUser", "actionFields", "name", "<PERSON><PERSON><PERSON><PERSON>", "handler", "Elimina", "items", "<PERSON>gg<PERSON>gg", "command", "ref", "el", "gestioneAgenti", "dt", "value", "dataKey", "paginator", "rows", "rowsPerPageOptions", "actionsColumn", "autoLayout", "splitButtonClass", "selectionMode", "cellSelection", "fileNames", "visible", "AggUtAg", "modal", "footer", "onHide", "PDV", "Conferma", "style", "fontSize", "ResDeleteCli"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/gestioneAgenti.jsx"], "sourcesContent": ["/**\n * Winet e-procurement GUI\n * 2020 - Viniexport.com (C)\n *\n * GestioneAgenti - operazioni sugli agenti\n *\n */\nimport React, { Component } from \"react\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Caricamento from \"../../utils/caricamento\";\nimport UtenteAgente from \"../../aggiunta_dati/utenteAgente\";\nimport AggiungiAgenti from \"../../aggiunta_dati/aggiungiAgenti\";\nimport VisualizzaPDV from \"../../aggiunta_dati/visualizzaPDV\";\nimport CustomDataTable from \"../../components/customDataTable\";\nimport { Toast } from \"primereact/toast\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { APIRequest } from \"../../components/generalizzazioni/apireq\";\nimport { agente } from \"../../components/route\";\nimport \"../../css/DataTableDemo.css\";\nimport 'antd/dist/antd.min.css';\n\nclass GestioneAgenti extends Component {\n  //Stato iniziale elementi tabella\n  emptyResult = {\n    id: null,\n    customerName: \"\",\n    address: \"\",\n    pIva: \"\",\n    email: \"\",\n    isValid: \"\",\n    createAt: \"\",\n    updateAt: \"\",\n  };\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    this.state = {\n      results: [],\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      submitted: false,\n      result: this.emptyResult,\n      globalFilter: null,\n      loading: true,\n    };\n    //Dichiarazione funzioni e metodi\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.aggiungiAgente = this.aggiungiAgente.bind(this);\n    this.hideaggiungiAgente = this.hideaggiungiAgente.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.addUser = this.addUser.bind(this);\n    this.hideUtenteAgente = this.hideUtenteAgente.bind(this);\n    this.hideVisualizzaPDV = this.hideVisualizzaPDV.bind(this);\n    this.viewPDV = this.viewPDV.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount(results) {\n    await APIRequest(\"GET\", \"affiliate/\")\n      .then((res) => {\n        for (var entry of res.data) {\n          var x = {\n            id: entry.id,\n            idRegistry: entry.idRegistry,\n            firstName: entry.idRegistry2.firstName,\n            lastName: entry.idRegistry2.lastName,\n            address: entry.idRegistry2.address,\n            pIva: entry.idRegistry2.pIva,\n            email: entry.idRegistry2.email,\n            cap: entry.idRegistry2.cap,\n            city: entry.idRegistry2.city,\n            externalCode: entry.idRegistry2.externalCode,\n            idAgente: entry.idRegistry2.idAgente,\n            tel: entry.idRegistry2.tel,\n            isValid: entry.idRegistry2.isValid,\n            createdAt: entry.createdAt,\n            updateAt: entry.updateAt,\n            users: entry.idRegistry2.users,\n          };\n          let controllo = false;\n          if (x.users.length > 0) {\n            if (x.users.length > 1) {\n              x.users.forEach((element) => {\n                if (element.role === agente) {\n                  controllo = true;\n                }\n              });\n              if (controllo === true) {\n                this.state.results.push(x);\n              }\n            } else {\n              if (x.users[0].role === agente) {\n                this.state.results.push(x);\n              }\n            }\n          } else {\n            this.state.results.push(x);\n          }\n        }\n        this.setState((state) => ({ ...state, ...results, loading: false }));\n      })\n      .catch((e) => {\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: `Non è stato possibile visualizzare gli agenti. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n          life: 3000,\n        });\n      });\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({ deleteResultDialog: false });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true,\n    });\n  }\n  //Apertura dialogo aggiunta utente\n  addUser(rowData) {\n    localStorage.setItem(\"datiComodo\", rowData.idRegistry);\n    this.setState({\n      resultDialog3: true,\n    });\n  }\n  //Chiusura dialogo aggiunta utente\n  hideUtenteAgente() {\n    this.setState({\n      resultDialog3: false,\n    });\n  }\n  //Apertura dialogo aggiunta\n  aggiungiAgente() {\n    this.setState({\n      resultDialog2: true,\n    });\n  }\n  //Chiusura dialogo aggiunta\n  hideaggiungiAgente() {\n    this.setState({\n      resultDialog2: false,\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(\n      (val) => val.id !== this.state.result.id\n    );\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult,\n    });\n    let url = \"affiliate/?id=\" + this.state.result.id;\n    var res = await APIRequest(\"DELETE\", url);\n    console.log(res.data);\n    this.toast.show({\n      severity: \"success\",\n      summary: \"Successful\",\n      detail: \"Agente eliminato con successo\",\n      life: 3000,\n    });\n    window.location.reload();\n  }\n  viewPDV(result) {\n    this.setState({\n      result,\n      resultDialog: true,\n    });\n  }\n  hideVisualizzaPDV() {\n    this.setState({\n      resultDialog: false,\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideVisualizzaPDV}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter2 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideaggiungiAgente}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta\n    const resultDialogFooter3 = (\n      <React.Fragment>\n        <Button className=\"p-button-text\" onClick={this.hideUtenteAgente}>\n          {\" \"}\n          {Costanti.Chiudi}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = (\n      <React.Fragment>\n        <Button\n          label=\"No\"\n          icon=\"pi pi-times\"\n          className=\"p-button-text\"\n          onClick={this.hideDeleteResultDialog}\n        />\n        <Button className=\"p-button-text\" onClick={this.deleteResult}>\n          {\" \"}\n          {Costanti.Si}{\" \"}\n        </Button>\n      </React.Fragment>\n    );\n    const fields = [\n      {\n        field: \"firstName\",\n        header: Costanti.rSociale,\n        body: \"firstName\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"address\",\n        header: Costanti.Indirizzo,\n        body: \"address\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"city\",\n        header: Costanti.Città,\n        body: \"city\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"cap\",\n        header: Costanti.CodPost,\n        body: \"cap\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"pIva\",\n        header: Costanti.pIva,\n        body: \"pIva\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"tel\",\n        header: Costanti.Tel,\n        body: \"tel\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"email\",\n        header: Costanti.Email,\n        body: \"email\",\n        sortable: true,\n        showHeader: true,\n      },\n      {\n        field: \"isValid\",\n        header: Costanti.Validità,\n        body: \"isValid\",\n        showHeader: true,\n      },\n      {\n        field: \"user\",\n        header: Costanti.AssUser,\n        body: \"userBodyTemplate\",\n        showHeader: true,\n      },\n    ];\n    const actionFields = [\n      { name: Costanti.VisCli,icon: <i className=\"pi pi-eye\" />, handler: this.viewPDV },\n      { name: Costanti.addUser,icon: <i className=\"pi pi-user-plus\" />, handler: this.addUser },\n      { name: Costanti.Elimina,icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n    ];\n    const items = [\n      {\n        label: Costanti.AggAgg,\n        icon: 'pi pi-plus-circle',\n        command: () => {\n          this.aggiungiAgente()\n        }\n      },\n    ]\n    return (\n      <div className=\"datatable-responsive-demo wrapper\">\n        {/* Il componente Toast permette di creare e visualizzare messaggi */}\n        <Toast ref={(el) => (this.toast = el)} />\n        {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n        <Nav />\n        <div className=\"col-12 px-0 solid-head\">\n          <h1>{Costanti.gestioneAgenti}</h1>\n        </div>\n        <div className=\"card\">\n          {/* Componente primereact per la creazione della tabella */}\n          <CustomDataTable\n            ref={(el) => (this.dt = el)}\n            value={this.state.results}\n            fields={fields}\n            loading={this.state.loading}\n            dataKey=\"id\"\n            paginator\n            rows={20}\n            rowsPerPageOptions={[10, 20, 50]}\n            actionsColumn={actionFields}\n            autoLayout={true}\n            splitButtonClass={true}\n            items={items}\n            selectionMode=\"single\"\n            cellSelection={true}\n            fileNames=\"Agenti\"\n          />\n        </div>\n        {/* Struttura dialogo per l'aggiunta utente */}\n        <Dialog\n          visible={this.state.resultDialog3}\n          header={Costanti.AggUtAg}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter3}\n          onHide={this.hideUtenteAgente}\n        >\n          <Caricamento />\n          <UtenteAgente />\n        </Dialog>\n        {/* Struttura dialogo per l'aggiunta */}\n        <Dialog\n          visible={this.state.resultDialog2}\n          header={Costanti.AggAgg}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter2}\n          onHide={this.hideaggiungiAgente}\n        >\n          <Caricamento />\n          <AggiungiAgenti />\n        </Dialog>\n        <Dialog\n          visible={this.state.resultDialog}\n          header={Costanti.PDV}\n          modal\n          className=\"p-fluid modalBox\"\n          footer={resultDialogFooter}\n          onHide={this.hideVisualizzaPDV}\n        >\n          <Caricamento />\n          <VisualizzaPDV result={this.state.result} />\n        </Dialog>\n        {/* Struttura dialogo per la cancellazione */}\n        <Dialog\n          visible={this.state.deleteResultDialog}\n          header={Costanti.Conferma}\n          modal\n          footer={deleteResultDialogFooter}\n          onHide={this.hideDeleteResultDialog}\n        >\n          <div className=\"confirmation-content\">\n            <i\n              className=\"pi pi-exclamation-triangle p-mr-3\"\n              style={{ fontSize: \"2rem\" }}\n            />\n            {this.state.result && (\n              <span>\n                {Costanti.ResDeleteCli} <b>{this.state.result.firstName}?</b>\n              </span>\n            )}\n          </div>\n        </Dialog>\n      </div>\n    );\n  }\n}\n\nexport default GestioneAgenti;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,OAAO,6BAA6B;AACpC,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,cAAc,SAASf,SAAS,CAAC;EAYrCgB,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ;IAbF;IAAA,KACAC,WAAW,GAAG;MACZC,EAAE,EAAE,IAAI;MACRC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IAIC,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,IAAI,CAAChB,WAAW;MACxBiB,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACX,CAAC;IACD;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACD,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACI,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACJ,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACN,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACO,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACP,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACA,OAAO,CAACR,IAAI,CAAC,IAAI,CAAC;EACxC;EACA;EACA,MAAMS,iBAAiBA,CAACnB,OAAO,EAAE;IAC/B,MAAMjB,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAClCqC,IAAI,CAAEC,GAAG,IAAK;MACb,KAAK,IAAIC,KAAK,IAAID,GAAG,CAACE,IAAI,EAAE;QAC1B,IAAIC,CAAC,GAAG;UACNjC,EAAE,EAAE+B,KAAK,CAAC/B,EAAE;UACZkC,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BC,SAAS,EAAEJ,KAAK,CAACK,WAAW,CAACD,SAAS;UACtCE,QAAQ,EAAEN,KAAK,CAACK,WAAW,CAACC,QAAQ;UACpCnC,OAAO,EAAE6B,KAAK,CAACK,WAAW,CAAClC,OAAO;UAClCC,IAAI,EAAE4B,KAAK,CAACK,WAAW,CAACjC,IAAI;UAC5BC,KAAK,EAAE2B,KAAK,CAACK,WAAW,CAAChC,KAAK;UAC9BkC,GAAG,EAAEP,KAAK,CAACK,WAAW,CAACE,GAAG;UAC1BC,IAAI,EAAER,KAAK,CAACK,WAAW,CAACG,IAAI;UAC5BC,YAAY,EAAET,KAAK,CAACK,WAAW,CAACI,YAAY;UAC5CC,QAAQ,EAAEV,KAAK,CAACK,WAAW,CAACK,QAAQ;UACpCC,GAAG,EAAEX,KAAK,CAACK,WAAW,CAACM,GAAG;UAC1BrC,OAAO,EAAE0B,KAAK,CAACK,WAAW,CAAC/B,OAAO;UAClCsC,SAAS,EAAEZ,KAAK,CAACY,SAAS;UAC1BpC,QAAQ,EAAEwB,KAAK,CAACxB,QAAQ;UACxBqC,KAAK,EAAEb,KAAK,CAACK,WAAW,CAACQ;QAC3B,CAAC;QACD,IAAIC,SAAS,GAAG,KAAK;QACrB,IAAIZ,CAAC,CAACW,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;UACtB,IAAIb,CAAC,CAACW,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;YACtBb,CAAC,CAACW,KAAK,CAACG,OAAO,CAAEC,OAAO,IAAK;cAC3B,IAAIA,OAAO,CAACC,IAAI,KAAKxD,MAAM,EAAE;gBAC3BoD,SAAS,GAAG,IAAI;cAClB;YACF,CAAC,CAAC;YACF,IAAIA,SAAS,KAAK,IAAI,EAAE;cACtB,IAAI,CAACrC,KAAK,CAACC,OAAO,CAACyC,IAAI,CAACjB,CAAC,CAAC;YAC5B;UACF,CAAC,MAAM;YACL,IAAIA,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKxD,MAAM,EAAE;cAC9B,IAAI,CAACe,KAAK,CAACC,OAAO,CAACyC,IAAI,CAACjB,CAAC,CAAC;YAC5B;UACF;QACF,CAAC,MAAM;UACL,IAAI,CAACzB,KAAK,CAACC,OAAO,CAACyC,IAAI,CAACjB,CAAC,CAAC;QAC5B;MACF;MACA,IAAI,CAACkB,QAAQ,CAAE3C,KAAK,IAAA4C,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAW5C,KAAK,GAAKC,OAAO;QAAEQ,OAAO,EAAE;MAAK,EAAG,CAAC;IACtE,CAAC,CAAC,CACDoC,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAC,WAAA,EAAAC,YAAA;MACZC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACd,IAAI,CAACK,KAAK,CAACC,IAAI,CAAC;QACdC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yEAAAC,MAAA,CAAsE,EAAAT,WAAA,GAAAD,CAAC,CAACW,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYvB,IAAI,MAAKkC,SAAS,IAAAV,YAAA,GAAGF,CAAC,CAACW,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,GAAGsB,CAAC,CAACa,OAAO,CAAE;QAC3IC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EACA;EACA7C,sBAAsBA,CAAA,EAAG;IACvB,IAAI,CAAC4B,QAAQ,CAAC;MAAEtC,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAC9C;EACA;EACAK,mBAAmBA,CAACH,MAAM,EAAE;IAC1B,IAAI,CAACoC,QAAQ,CAAC;MACZpC,MAAM;MACNF,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;EACA;EACAW,OAAOA,CAAC6C,OAAO,EAAE;IACfC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEF,OAAO,CAACnC,UAAU,CAAC;IACtD,IAAI,CAACiB,QAAQ,CAAC;MACZvC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAa,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAC0B,QAAQ,CAAC;MACZvC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAQ,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC+B,QAAQ,CAAC;MACZxC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACAU,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC8B,QAAQ,CAAC;MACZxC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA;EACA,MAAMW,YAAYA,CAAA,EAAG;IACnB,IAAIb,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAAC+D,MAAM,CACpCC,GAAG,IAAKA,GAAG,CAACzE,EAAE,KAAK,IAAI,CAACQ,KAAK,CAACO,MAAM,CAACf,EACxC,CAAC;IACD,IAAI,CAACmD,QAAQ,CAAC;MACZ1C,OAAO;MACPI,kBAAkB,EAAE,KAAK;MACzBE,MAAM,EAAE,IAAI,CAAChB;IACf,CAAC,CAAC;IACF,IAAI2E,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAAClE,KAAK,CAACO,MAAM,CAACf,EAAE;IACjD,IAAI8B,GAAG,GAAG,MAAMtC,UAAU,CAAC,QAAQ,EAAEkF,GAAG,CAAC;IACzCjB,OAAO,CAACC,GAAG,CAAC5B,GAAG,CAACE,IAAI,CAAC;IACrB,IAAI,CAAC2B,KAAK,CAACC,IAAI,CAAC;MACdC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,+BAA+B;MACvCK,IAAI,EAAE;IACR,CAAC,CAAC;IACFO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACAlD,OAAOA,CAACZ,MAAM,EAAE;IACd,IAAI,CAACoC,QAAQ,CAAC;MACZpC,MAAM;MACNL,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACAgB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACyB,QAAQ,CAAC;MACZzC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACAoE,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,kBAAkB,gBACtBpF,OAAA,CAACf,KAAK,CAACoG,QAAQ;MAAAC,QAAA,eACbtF,OAAA,CAACN,MAAM;QAAC6F,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACzD,iBAAkB;QAAAuD,QAAA,GAC/D,GAAG,EACH1F,QAAQ,CAAC6F,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMC,mBAAmB,gBACvB9F,OAAA,CAACf,KAAK,CAACoG,QAAQ;MAAAC,QAAA,eACbtF,OAAA,CAACN,MAAM;QAAC6F,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC9D,kBAAmB;QAAA4D,QAAA,GAChE,GAAG,EACH1F,QAAQ,CAAC6F,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAME,mBAAmB,gBACvB/F,OAAA,CAACf,KAAK,CAACoG,QAAQ;MAAAC,QAAA,eACbtF,OAAA,CAACN,MAAM;QAAC6F,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC1D,gBAAiB;QAAAwD,QAAA,GAC9D,GAAG,EACH1F,QAAQ,CAAC6F,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD;IACA,MAAMG,wBAAwB,gBAC5BhG,OAAA,CAACf,KAAK,CAACoG,QAAQ;MAAAC,QAAA,gBACbtF,OAAA,CAACN,MAAM;QACLuG,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBX,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAC5D;MAAuB;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACF7F,OAAA,CAACN,MAAM;QAAC6F,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC7D,YAAa;QAAA2D,QAAA,GAC1D,GAAG,EACH1F,QAAQ,CAACuG,EAAE,EAAE,GAAG;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACjB;IACD,MAAMO,MAAM,GAAG,CACb;MACEC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE1G,QAAQ,CAAC2G,QAAQ;MACzBC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE1G,QAAQ,CAAC+G,SAAS;MAC1BH,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE1G,QAAQ,CAACgH,KAAK;MACtBJ,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE1G,QAAQ,CAACiH,OAAO;MACxBL,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE1G,QAAQ,CAACY,IAAI;MACrBgG,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE1G,QAAQ,CAACkH,GAAG;MACpBN,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE1G,QAAQ,CAACmH,KAAK;MACtBP,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE1G,QAAQ,CAACoH,QAAQ;MACzBR,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IACd,CAAC,EACD;MACEL,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE1G,QAAQ,CAACqH,OAAO;MACxBT,IAAI,EAAE,kBAAkB;MACxBE,UAAU,EAAE;IACd,CAAC,CACF;IACD,MAAMQ,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAEvH,QAAQ,CAACwH,MAAM;MAAClB,IAAI,eAAElG,OAAA;QAAGuF,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEwB,OAAO,EAAE,IAAI,CAACrF;IAAQ,CAAC,EAClF;MAAEmF,IAAI,EAAEvH,QAAQ,CAACiC,OAAO;MAACqE,IAAI,eAAElG,OAAA;QAAGuF,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEwB,OAAO,EAAE,IAAI,CAACxF;IAAQ,CAAC,EACzF;MAAEsF,IAAI,EAAEvH,QAAQ,CAAC0H,OAAO;MAACpB,IAAI,eAAElG,OAAA;QAAGuF,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEwB,OAAO,EAAE,IAAI,CAAC9F;IAAoB,CAAC,CAClG;IACD,MAAMgG,KAAK,GAAG,CACZ;MACEtB,KAAK,EAAErG,QAAQ,CAAC4H,MAAM;MACtBtB,IAAI,EAAE,mBAAmB;MACzBuB,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAAChG,cAAc,CAAC,CAAC;MACvB;IACF,CAAC,CACF;IACD,oBACEzB,OAAA;MAAKuF,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAEhDtF,OAAA,CAACP,KAAK;QAACiI,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC3D,KAAK,GAAG2D;MAAI;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEzC7F,OAAA,CAACb,GAAG;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP7F,OAAA;QAAKuF,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrCtF,OAAA;UAAAsF,QAAA,EAAK1F,QAAQ,CAACgI;QAAc;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACN7F,OAAA;QAAKuF,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEnBtF,OAAA,CAACR,eAAe;UACdkI,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACE,EAAE,GAAGF,EAAI;UAC5BG,KAAK,EAAE,IAAI,CAACjH,KAAK,CAACC,OAAQ;UAC1BsF,MAAM,EAAEA,MAAO;UACf9E,OAAO,EAAE,IAAI,CAACT,KAAK,CAACS,OAAQ;UAC5ByG,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEjB,YAAa;UAC5BkB,UAAU,EAAE,IAAK;UACjBC,gBAAgB,EAAE,IAAK;UACvBd,KAAK,EAAEA,KAAM;UACbe,aAAa,EAAC,QAAQ;UACtBC,aAAa,EAAE,IAAK;UACpBC,SAAS,EAAC;QAAQ;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7F,OAAA,CAACL,MAAM;QACL8I,OAAO,EAAE,IAAI,CAAC5H,KAAK,CAACI,aAAc;QAClCqF,MAAM,EAAE1G,QAAQ,CAAC8I,OAAQ;QACzBC,KAAK;QACLpD,SAAS,EAAC,kBAAkB;QAC5BqD,MAAM,EAAE7C,mBAAoB;QAC5B8C,MAAM,EAAE,IAAI,CAAC/G,gBAAiB;QAAAwD,QAAA,gBAE9BtF,OAAA,CAACZ,WAAW;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf7F,OAAA,CAACX,YAAY;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAET7F,OAAA,CAACL,MAAM;QACL8I,OAAO,EAAE,IAAI,CAAC5H,KAAK,CAACG,aAAc;QAClCsF,MAAM,EAAE1G,QAAQ,CAAC4H,MAAO;QACxBmB,KAAK;QACLpD,SAAS,EAAC,kBAAkB;QAC5BqD,MAAM,EAAE9C,mBAAoB;QAC5B+C,MAAM,EAAE,IAAI,CAACnH,kBAAmB;QAAA4D,QAAA,gBAEhCtF,OAAA,CAACZ,WAAW;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf7F,OAAA,CAACV,cAAc;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACT7F,OAAA,CAACL,MAAM;QACL8I,OAAO,EAAE,IAAI,CAAC5H,KAAK,CAACE,YAAa;QACjCuF,MAAM,EAAE1G,QAAQ,CAACkJ,GAAI;QACrBH,KAAK;QACLpD,SAAS,EAAC,kBAAkB;QAC5BqD,MAAM,EAAExD,kBAAmB;QAC3ByD,MAAM,EAAE,IAAI,CAAC9G,iBAAkB;QAAAuD,QAAA,gBAE/BtF,OAAA,CAACZ,WAAW;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf7F,OAAA,CAACT,aAAa;UAAC6B,MAAM,EAAE,IAAI,CAACP,KAAK,CAACO;QAAO;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAET7F,OAAA,CAACL,MAAM;QACL8I,OAAO,EAAE,IAAI,CAAC5H,KAAK,CAACK,kBAAmB;QACvCoF,MAAM,EAAE1G,QAAQ,CAACmJ,QAAS;QAC1BJ,KAAK;QACLC,MAAM,EAAE5C,wBAAyB;QACjC6C,MAAM,EAAE,IAAI,CAACjH,sBAAuB;QAAA0D,QAAA,eAEpCtF,OAAA;UAAKuF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnCtF,OAAA;YACEuF,SAAS,EAAC,mCAAmC;YAC7CyD,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,EACD,IAAI,CAAChF,KAAK,CAACO,MAAM,iBAChBpB,OAAA;YAAAsF,QAAA,GACG1F,QAAQ,CAACsJ,YAAY,EAAC,GAAC,eAAAlJ,OAAA;cAAAsF,QAAA,GAAI,IAAI,CAACzE,KAAK,CAACO,MAAM,CAACoB,SAAS,EAAC,GAAC;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;AACF;AAEA,eAAe5F,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}