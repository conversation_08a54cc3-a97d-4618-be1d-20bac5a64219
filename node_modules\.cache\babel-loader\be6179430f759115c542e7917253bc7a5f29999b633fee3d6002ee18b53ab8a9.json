{"ast": null, "code": "var parent = require('../stable/global-this');\nmodule.exports = parent;", "map": {"version": 3, "names": ["parent", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/actual/global-this.js"], "sourcesContent": ["var parent = require('../stable/global-this');\n\nmodule.exports = parent;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAE7CC,MAAM,CAACC,OAAO,GAAGH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}