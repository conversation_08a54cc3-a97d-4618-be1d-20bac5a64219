{"name": "eslint", "version": "8.14.0", "author": "<PERSON> <<EMAIL>>", "description": "An AST-based pattern checker for JavaScript.", "bin": {"eslint": "./bin/eslint.js"}, "main": "./lib/api.js", "exports": {"./package.json": "./package.json", ".": "./lib/api.js", "./use-at-your-own-risk": "./lib/unsupported-api.js"}, "scripts": {"test": "node Makefile.js test", "test:cli": "mocha", "lint": "node Makefile.js lint", "fix": "node Makefile.js lint -- fix", "fuzz": "node Makefile.js fuzz", "generate-release": "node Makefile.js generateRelease", "generate-alpharelease": "node Makefile.js generatePrerelease -- alpha", "generate-betarelease": "node Makefile.js generatePrerelease -- beta", "generate-rcrelease": "node Makefile.js generatePrerelease -- rc", "publish-release": "node Makefile.js publishRelease", "gensite": "node Makefile.js gensite", "webpack": "node Makefile.js webpack", "perf": "node Makefile.js perf"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": "eslint --fix", "*.md": "markdownlint --fix"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "repository": "eslint/eslint", "funding": "https://opencollective.com/eslint", "homepage": "https://eslint.org", "bugs": "https://github.com/eslint/eslint/issues/", "dependencies": {"@eslint/eslintrc": "^1.2.2", "@humanwhocodes/config-array": "^0.9.2", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.1.1", "eslint-utils": "^3.0.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^6.0.1", "globals": "^13.6.0", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "regexpp": "^3.2.0", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "babel-loader": "^8.0.5", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "dateformat": "^4.5.1", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-eslint-plugin": "^4.0.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^37.0.0", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fs-teardown": "^0.1.3", "glob": "^7.1.6", "jsdoc": "^3.5.5", "karma": "^6.1.1", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-webpack": "^5.0.0", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdownlint": "^0.24.0", "markdownlint-cli": "^0.30.0", "marked": "^4.0.8", "memfs": "^3.0.1", "mocha": "^8.3.2", "mocha-junit-reporter": "^2.0.0", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "nyc": "^15.0.1", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "puppeteer": "^9.1.1", "recast": "^0.20.4", "regenerator-runtime": "^0.13.2", "semver": "^7.3.5", "shelljs": "^0.8.2", "sinon": "^11.0.0", "temp": "^0.9.0", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}