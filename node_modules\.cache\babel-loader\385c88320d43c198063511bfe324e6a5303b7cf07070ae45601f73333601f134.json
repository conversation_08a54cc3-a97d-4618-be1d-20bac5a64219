{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setTwoToneColor = setTwoToneColor;\nexports.getTwoToneColor = getTwoToneColor;\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar _IconBase = _interopRequireDefault(require(\"./IconBase\"));\nvar _utils = require(\"../utils\");\nfunction setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = (0, _utils.normalizeTwoToneColors)(twoToneColor),\n    _normalizeTwoToneColo2 = (0, _slicedToArray2.default)(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return _IconBase.default.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nfunction getTwoToneColor() {\n  var colors = _IconBase.default.getTwoToneColors();\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n  return [colors.primaryColor, colors.secondaryColor];\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "setTwoToneColor", "getTwoToneColor", "_slicedToArray2", "_IconBase", "_utils", "twoToneColor", "_normalizeTwoToneColo", "normalizeTwoToneColors", "_normalizeTwoToneColo2", "default", "primaryColor", "secondaryColor", "setTwoToneColors", "colors", "getTwoToneColors", "calculated"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/lib/components/twoTonePrimaryColor.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.setTwoToneColor = setTwoToneColor;\nexports.getTwoToneColor = getTwoToneColor;\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar _IconBase = _interopRequireDefault(require(\"./IconBase\"));\n\nvar _utils = require(\"../utils\");\n\nfunction setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = (0, _utils.normalizeTwoToneColors)(twoToneColor),\n      _normalizeTwoToneColo2 = (0, _slicedToArray2.default)(_normalizeTwoToneColo, 2),\n      primaryColor = _normalizeTwoToneColo2[0],\n      secondaryColor = _normalizeTwoToneColo2[1];\n\n  return _IconBase.default.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\n\nfunction getTwoToneColor() {\n  var colors = _IconBase.default.getTwoToneColors();\n\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n\n  return [colors.primaryColor, colors.secondaryColor];\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzCF,OAAO,CAACG,eAAe,GAAGA,eAAe;AAEzC,IAAIC,eAAe,GAAGR,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE7F,IAAIQ,SAAS,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE7D,IAAIS,MAAM,GAAGT,OAAO,CAAC,UAAU,CAAC;AAEhC,SAASK,eAAeA,CAACK,YAAY,EAAE;EACrC,IAAIC,qBAAqB,GAAG,CAAC,CAAC,EAAEF,MAAM,CAACG,sBAAsB,EAAEF,YAAY,CAAC;IACxEG,sBAAsB,GAAG,CAAC,CAAC,EAAEN,eAAe,CAACO,OAAO,EAAEH,qBAAqB,EAAE,CAAC,CAAC;IAC/EI,YAAY,GAAGF,sBAAsB,CAAC,CAAC,CAAC;IACxCG,cAAc,GAAGH,sBAAsB,CAAC,CAAC,CAAC;EAE9C,OAAOL,SAAS,CAACM,OAAO,CAACG,gBAAgB,CAAC;IACxCF,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA;EAClB,CAAC,CAAC;AACJ;AAEA,SAASV,eAAeA,CAAA,EAAG;EACzB,IAAIY,MAAM,GAAGV,SAAS,CAACM,OAAO,CAACK,gBAAgB,CAAC,CAAC;EAEjD,IAAI,CAACD,MAAM,CAACE,UAAU,EAAE;IACtB,OAAOF,MAAM,CAACH,YAAY;EAC5B;EAEA,OAAO,CAACG,MAAM,CAACH,YAAY,EAAEG,MAAM,CAACF,cAAc,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}