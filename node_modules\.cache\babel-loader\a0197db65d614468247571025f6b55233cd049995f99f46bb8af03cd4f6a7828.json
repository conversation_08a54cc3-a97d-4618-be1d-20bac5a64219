{"ast": null, "code": "export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[colEnd];\n  }\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight;\n  }\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}", "map": {"version": 3, "names": ["getCellFixedInfo", "colStart", "colEnd", "columns", "stickyOffsets", "direction", "startColumn", "endColumn", "fixLeft", "fixRight", "fixed", "left", "right", "lastFixLeft", "firstFixRight", "lastFixRight", "firstFixLeft", "nextColumn", "prevColumn", "undefined", "prevFixLeft", "nextFixRight", "nextFixLeft", "prevFixRight", "isSticky"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/utils/fixUtil.js"], "sourcesContent": ["export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[colEnd];\n  }\n\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight;\n  }\n\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,SAAS,EAAE;EACpF,IAAIC,WAAW,GAAGH,OAAO,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzC,IAAIM,SAAS,GAAGJ,OAAO,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC;EACrC,IAAIM,OAAO;EACX,IAAIC,QAAQ;EAEZ,IAAIH,WAAW,CAACI,KAAK,KAAK,MAAM,EAAE;IAChCF,OAAO,GAAGJ,aAAa,CAACO,IAAI,CAACV,QAAQ,CAAC;EACxC,CAAC,MAAM,IAAIM,SAAS,CAACG,KAAK,KAAK,OAAO,EAAE;IACtCD,QAAQ,GAAGL,aAAa,CAACQ,KAAK,CAACV,MAAM,CAAC;EACxC;EAEA,IAAIW,WAAW,GAAG,KAAK;EACvB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,UAAU,GAAGd,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC;EACpC,IAAIgB,UAAU,GAAGf,OAAO,CAACF,QAAQ,GAAG,CAAC,CAAC;EAEtC,IAAII,SAAS,KAAK,KAAK,EAAE;IACvB,IAAIG,OAAO,KAAKW,SAAS,EAAE;MACzB,IAAIC,WAAW,GAAGF,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,MAAM;MAC3DM,YAAY,GAAG,CAACI,WAAW;IAC7B,CAAC,MAAM,IAAIX,QAAQ,KAAKU,SAAS,EAAE;MACjC,IAAIE,YAAY,GAAGJ,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,OAAO;MAC7DK,YAAY,GAAG,CAACM,YAAY;IAC9B;EACF,CAAC,MAAM,IAAIb,OAAO,KAAKW,SAAS,EAAE;IAChC,IAAIG,WAAW,GAAGL,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,MAAM;IAC3DG,WAAW,GAAG,CAACS,WAAW;EAC5B,CAAC,MAAM,IAAIb,QAAQ,KAAKU,SAAS,EAAE;IACjC,IAAII,YAAY,GAAGL,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,OAAO;IAC7DI,aAAa,GAAG,CAACS,YAAY;EAC/B;EAEA,OAAO;IACLf,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ;IAClBI,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BC,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BQ,QAAQ,EAAEpB,aAAa,CAACoB;EAC1B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}