export default WebAppManifest;
declare class WebAppManifest extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @return {Promise<{url: string, data: string}|null>}
     */
    static fetchAppManifest(session: LH.Gatherer.FRProtocolSession): Promise<{
        url: string;
        data: string;
    } | null>;
    /**
     * Uses the debugger protocol to fetch the manifest from within the context of
     * the target page, reusing any credentials, emulation, etc, already established
     * there.
     *
     * Returns the parsed manifest or null if the page had no manifest. If the manifest
     * was unparseable as JSON, manifest.value will be undefined and manifest.warning
     * will have the reason. See manifest-parser.js for more information.
     *
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} pageUrl
     * @return {Promise<LH.Artifacts.Manifest|null>}
     */
    static getWebAppManifest(session: LH.Gatherer.FRProtocolSession, pageUrl: string): Promise<LH.Artifacts.Manifest | null>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['WebAppManifest']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['WebAppManifest']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=web-app-manifest.d.ts.map