{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"selectable\", \"checkable\", \"expandedKeys\", \"selectedKeys\", \"checkedKeys\", \"loadedKeys\", \"loadingKeys\", \"halfCheckedKeys\", \"keyEntities\", \"disabled\", \"dragging\", \"dragOverNodeKey\", \"dropPosition\", \"motion\", \"height\", \"itemHeight\", \"virtual\", \"focusable\", \"activeItem\", \"focused\", \"tabIndex\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onActiveChange\", \"onListChangeStart\", \"onListChangeEnd\"];\n\n/**\n * Handle virtual list of the TreeNodes.\n */\nimport * as React from 'react';\nimport VirtualList from 'rc-virtual-list';\nimport MotionTreeNode from './MotionTreeNode';\nimport { findExpandedKeys, getExpandRange } from './utils/diffUtil';\nimport { getTreeNodeProps, getKey } from './utils/treeUtil';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar noop = function noop() {};\nexport var MOTION_KEY = \"RC_TREE_MOTION_\".concat(Math.random());\nvar MotionNode = {\n  key: MOTION_KEY\n};\nexport var MotionEntity = {\n  key: MOTION_KEY,\n  level: 0,\n  index: 0,\n  pos: '0',\n  node: MotionNode,\n  nodes: [MotionNode]\n};\nvar MotionFlattenData = {\n  parent: null,\n  children: [],\n  pos: MotionEntity.pos,\n  data: MotionNode,\n  title: null,\n  key: MOTION_KEY,\n  /** Hold empty list here since we do not use it */\n  isStart: [],\n  isEnd: []\n};\n/**\n * We only need get visible content items to play the animation.\n */\n\nexport function getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {\n  if (virtual === false || !height) {\n    return list;\n  }\n  return list.slice(0, Math.ceil(height / itemHeight) + 1);\n}\nfunction itemKey(item) {\n  var key = item.key,\n    pos = item.pos;\n  return getKey(key, pos);\n}\nfunction getAccessibilityPath(item) {\n  var path = String(item.data.key);\n  var current = item;\n  while (current.parent) {\n    current = current.parent;\n    path = \"\".concat(current.data.key, \" > \").concat(path);\n  }\n  return path;\n}\nvar RefNodeList = function RefNodeList(props, ref) {\n  var prefixCls = props.prefixCls,\n    data = props.data,\n    selectable = props.selectable,\n    checkable = props.checkable,\n    expandedKeys = props.expandedKeys,\n    selectedKeys = props.selectedKeys,\n    checkedKeys = props.checkedKeys,\n    loadedKeys = props.loadedKeys,\n    loadingKeys = props.loadingKeys,\n    halfCheckedKeys = props.halfCheckedKeys,\n    keyEntities = props.keyEntities,\n    disabled = props.disabled,\n    dragging = props.dragging,\n    dragOverNodeKey = props.dragOverNodeKey,\n    dropPosition = props.dropPosition,\n    motion = props.motion,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    virtual = props.virtual,\n    focusable = props.focusable,\n    activeItem = props.activeItem,\n    focused = props.focused,\n    tabIndex = props.tabIndex,\n    onKeyDown = props.onKeyDown,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onActiveChange = props.onActiveChange,\n    onListChangeStart = props.onListChangeStart,\n    onListChangeEnd = props.onListChangeEnd,\n    domProps = _objectWithoutProperties(props, _excluded); // =============================== Ref ================================\n\n  var listRef = React.useRef(null);\n  var indentMeasurerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: function scrollTo(scroll) {\n        listRef.current.scrollTo(scroll);\n      },\n      getIndentWidth: function getIndentWidth() {\n        return indentMeasurerRef.current.offsetWidth;\n      }\n    };\n  }); // ============================== Motion ==============================\n\n  var _React$useState = React.useState(expandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevExpandedKeys = _React$useState2[0],\n    setPrevExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(data),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevData = _React$useState4[0],\n    setPrevData = _React$useState4[1];\n  var _React$useState5 = React.useState(data),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    transitionData = _React$useState6[0],\n    setTransitionData = _React$useState6[1];\n  var _React$useState7 = React.useState([]),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    transitionRange = _React$useState8[0],\n    setTransitionRange = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    motionType = _React$useState10[0],\n    setMotionType = _React$useState10[1];\n  function onMotionEnd() {\n    setPrevData(data);\n    setTransitionData(data);\n    setTransitionRange([]);\n    setMotionType(null);\n    onListChangeEnd();\n  } // Do animation if expanded keys changed\n\n  React.useEffect(function () {\n    setPrevExpandedKeys(expandedKeys);\n    var diffExpanded = findExpandedKeys(prevExpandedKeys, expandedKeys);\n    if (diffExpanded.key !== null) {\n      if (diffExpanded.add) {\n        var keyIndex = prevData.findIndex(function (_ref) {\n          var key = _ref.key;\n          return key === diffExpanded.key;\n        });\n        var rangeNodes = getMinimumRangeTransitionRange(getExpandRange(prevData, data, diffExpanded.key), virtual, height, itemHeight);\n        var newTransitionData = prevData.slice();\n        newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(newTransitionData);\n        setTransitionRange(rangeNodes);\n        setMotionType('show');\n      } else {\n        var _keyIndex = data.findIndex(function (_ref2) {\n          var key = _ref2.key;\n          return key === diffExpanded.key;\n        });\n        var _rangeNodes = getMinimumRangeTransitionRange(getExpandRange(data, prevData, diffExpanded.key), virtual, height, itemHeight);\n        var _newTransitionData = data.slice();\n        _newTransitionData.splice(_keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(_newTransitionData);\n        setTransitionRange(_rangeNodes);\n        setMotionType('hide');\n      }\n    } else if (prevData !== data) {\n      // If whole data changed, we just refresh the list\n      setPrevData(data);\n      setTransitionData(data);\n    }\n  }, [expandedKeys, data]); // We should clean up motion if is changed by dragging\n\n  React.useEffect(function () {\n    if (!dragging) {\n      onMotionEnd();\n    }\n  }, [dragging]);\n  var mergedData = motion ? transitionData : data;\n  var treeNodeRequiredProps = {\n    expandedKeys: expandedKeys,\n    selectedKeys: selectedKeys,\n    loadedKeys: loadedKeys,\n    loadingKeys: loadingKeys,\n    checkedKeys: checkedKeys,\n    halfCheckedKeys: halfCheckedKeys,\n    dragOverNodeKey: dragOverNodeKey,\n    dropPosition: dropPosition,\n    keyEntities: keyEntities\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, focused && activeItem && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, getAccessibilityPath(activeItem)), /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"input\", {\n    style: HIDDEN_STYLE,\n    disabled: focusable === false || disabled,\n    tabIndex: focusable !== false ? tabIndex : null,\n    onKeyDown: onKeyDown,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    value: \"\",\n    onChange: noop,\n    \"aria-label\": \"for screen reader\"\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-treenode\"),\n    \"aria-hidden\": true,\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      visibility: 'hidden',\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: indentMeasurerRef,\n    className: \"\".concat(prefixCls, \"-indent-unit\")\n  }))), /*#__PURE__*/React.createElement(VirtualList, _extends({}, domProps, {\n    data: mergedData,\n    itemKey: itemKey,\n    height: height,\n    fullHeight: false,\n    virtual: virtual,\n    itemHeight: itemHeight,\n    prefixCls: \"\".concat(prefixCls, \"-list\"),\n    ref: listRef,\n    onVisibleChange: function onVisibleChange(originList, fullList) {\n      var originSet = new Set(originList);\n      var restList = fullList.filter(function (item) {\n        return !originSet.has(item);\n      }); // Motion node is not render. Skip motion\n\n      if (restList.some(function (item) {\n        return itemKey(item) === MOTION_KEY;\n      })) {\n        onMotionEnd();\n      }\n    }\n  }), function (treeNode) {\n    var pos = treeNode.pos,\n      restProps = _extends({}, treeNode.data),\n      title = treeNode.title,\n      key = treeNode.key,\n      isStart = treeNode.isStart,\n      isEnd = treeNode.isEnd;\n    var mergedKey = getKey(key, pos);\n    delete restProps.key;\n    delete restProps.children;\n    var treeNodeProps = getTreeNodeProps(mergedKey, treeNodeRequiredProps);\n    return /*#__PURE__*/React.createElement(MotionTreeNode, _extends({}, restProps, treeNodeProps, {\n      title: title,\n      active: !!activeItem && key === activeItem.key,\n      pos: pos,\n      data: treeNode.data,\n      isStart: isStart,\n      isEnd: isEnd,\n      motion: motion,\n      motionNodes: key === MOTION_KEY ? transitionRange : null,\n      motionType: motionType,\n      onMotionStart: onListChangeStart,\n      onMotionEnd: onMotionEnd,\n      treeNodeRequiredProps: treeNodeRequiredProps,\n      onMouseMove: function onMouseMove() {\n        onActiveChange(null);\n      }\n    }));\n  }));\n};\nvar NodeList = /*#__PURE__*/React.forwardRef(RefNodeList);\nNodeList.displayName = 'NodeList';\nexport default NodeList;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "VirtualList", "MotionTreeNode", "findExpandedKeys", "getExpandRange", "getTreeNodeProps", "<PERSON><PERSON><PERSON>", "HIDDEN_STYLE", "width", "height", "display", "overflow", "opacity", "border", "padding", "margin", "noop", "MOTION_KEY", "concat", "Math", "random", "MotionNode", "key", "MotionEntity", "level", "index", "pos", "node", "nodes", "MotionFlattenData", "parent", "children", "data", "title", "isStart", "isEnd", "getMinimumRangeTransitionRange", "list", "virtual", "itemHeight", "slice", "ceil", "itemKey", "item", "getAccessibilityPath", "path", "String", "current", "RefNodeList", "props", "ref", "prefixCls", "selectable", "checkable", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON>", "checked<PERSON>eys", "loadedKeys", "loadingKeys", "halfC<PERSON>cked<PERSON>eys", "keyEntities", "disabled", "dragging", "dragOverNodeKey", "dropPosition", "motion", "focusable", "activeItem", "focused", "tabIndex", "onKeyDown", "onFocus", "onBlur", "onActiveChange", "onListChangeStart", "onListChangeEnd", "domProps", "listRef", "useRef", "indentMeasurerRef", "useImperativeHandle", "scrollTo", "scroll", "getIndentWidth", "offsetWidth", "_React$useState", "useState", "_React$useState2", "prevExpandedKeys", "setPrevExpandedKeys", "_React$useState3", "_React$useState4", "prevData", "setPrevData", "_React$useState5", "_React$useState6", "transitionData", "setTransitionData", "_React$useState7", "_React$useState8", "transitionRange", "setTransitionRange", "_React$useState9", "_React$useState10", "motionType", "setMotionType", "onMotionEnd", "useEffect", "diffExpanded", "add", "keyIndex", "findIndex", "_ref", "rangeNodes", "newTransitionData", "splice", "_keyIndex", "_ref2", "_rangeNodes", "_newTransitionData", "mergedData", "treeNodeRequiredProps", "createElement", "Fragment", "style", "value", "onChange", "className", "position", "pointerEvents", "visibility", "fullHeight", "onVisibleChange", "originList", "fullList", "originSet", "Set", "restList", "filter", "has", "some", "treeNode", "restProps", "mergedKey", "treeNodeProps", "active", "motionNodes", "onMotionStart", "onMouseMove", "NodeList", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree/es/NodeList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"selectable\", \"checkable\", \"expandedKeys\", \"selectedKeys\", \"checkedKeys\", \"loadedKeys\", \"loadingKeys\", \"halfCheckedKeys\", \"keyEntities\", \"disabled\", \"dragging\", \"dragOverNodeKey\", \"dropPosition\", \"motion\", \"height\", \"itemHeight\", \"virtual\", \"focusable\", \"activeItem\", \"focused\", \"tabIndex\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onActiveChange\", \"onListChangeStart\", \"onListChangeEnd\"];\n\n/**\n * Handle virtual list of the TreeNodes.\n */\nimport * as React from 'react';\nimport VirtualList from 'rc-virtual-list';\nimport MotionTreeNode from './MotionTreeNode';\nimport { findExpandedKeys, getExpandRange } from './utils/diffUtil';\nimport { getTreeNodeProps, getKey } from './utils/treeUtil';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\n\nvar noop = function noop() {};\n\nexport var MOTION_KEY = \"RC_TREE_MOTION_\".concat(Math.random());\nvar MotionNode = {\n  key: MOTION_KEY\n};\nexport var MotionEntity = {\n  key: MOTION_KEY,\n  level: 0,\n  index: 0,\n  pos: '0',\n  node: MotionNode,\n  nodes: [MotionNode]\n};\nvar MotionFlattenData = {\n  parent: null,\n  children: [],\n  pos: MotionEntity.pos,\n  data: MotionNode,\n  title: null,\n  key: MOTION_KEY,\n\n  /** Hold empty list here since we do not use it */\n  isStart: [],\n  isEnd: []\n};\n/**\n * We only need get visible content items to play the animation.\n */\n\nexport function getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {\n  if (virtual === false || !height) {\n    return list;\n  }\n\n  return list.slice(0, Math.ceil(height / itemHeight) + 1);\n}\n\nfunction itemKey(item) {\n  var key = item.key,\n      pos = item.pos;\n  return getKey(key, pos);\n}\n\nfunction getAccessibilityPath(item) {\n  var path = String(item.data.key);\n  var current = item;\n\n  while (current.parent) {\n    current = current.parent;\n    path = \"\".concat(current.data.key, \" > \").concat(path);\n  }\n\n  return path;\n}\n\nvar RefNodeList = function RefNodeList(props, ref) {\n  var prefixCls = props.prefixCls,\n      data = props.data,\n      selectable = props.selectable,\n      checkable = props.checkable,\n      expandedKeys = props.expandedKeys,\n      selectedKeys = props.selectedKeys,\n      checkedKeys = props.checkedKeys,\n      loadedKeys = props.loadedKeys,\n      loadingKeys = props.loadingKeys,\n      halfCheckedKeys = props.halfCheckedKeys,\n      keyEntities = props.keyEntities,\n      disabled = props.disabled,\n      dragging = props.dragging,\n      dragOverNodeKey = props.dragOverNodeKey,\n      dropPosition = props.dropPosition,\n      motion = props.motion,\n      height = props.height,\n      itemHeight = props.itemHeight,\n      virtual = props.virtual,\n      focusable = props.focusable,\n      activeItem = props.activeItem,\n      focused = props.focused,\n      tabIndex = props.tabIndex,\n      onKeyDown = props.onKeyDown,\n      onFocus = props.onFocus,\n      onBlur = props.onBlur,\n      onActiveChange = props.onActiveChange,\n      onListChangeStart = props.onListChangeStart,\n      onListChangeEnd = props.onListChangeEnd,\n      domProps = _objectWithoutProperties(props, _excluded); // =============================== Ref ================================\n\n\n  var listRef = React.useRef(null);\n  var indentMeasurerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: function scrollTo(scroll) {\n        listRef.current.scrollTo(scroll);\n      },\n      getIndentWidth: function getIndentWidth() {\n        return indentMeasurerRef.current.offsetWidth;\n      }\n    };\n  }); // ============================== Motion ==============================\n\n  var _React$useState = React.useState(expandedKeys),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      prevExpandedKeys = _React$useState2[0],\n      setPrevExpandedKeys = _React$useState2[1];\n\n  var _React$useState3 = React.useState(data),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      prevData = _React$useState4[0],\n      setPrevData = _React$useState4[1];\n\n  var _React$useState5 = React.useState(data),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      transitionData = _React$useState6[0],\n      setTransitionData = _React$useState6[1];\n\n  var _React$useState7 = React.useState([]),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      transitionRange = _React$useState8[0],\n      setTransitionRange = _React$useState8[1];\n\n  var _React$useState9 = React.useState(null),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      motionType = _React$useState10[0],\n      setMotionType = _React$useState10[1];\n\n  function onMotionEnd() {\n    setPrevData(data);\n    setTransitionData(data);\n    setTransitionRange([]);\n    setMotionType(null);\n    onListChangeEnd();\n  } // Do animation if expanded keys changed\n\n\n  React.useEffect(function () {\n    setPrevExpandedKeys(expandedKeys);\n    var diffExpanded = findExpandedKeys(prevExpandedKeys, expandedKeys);\n\n    if (diffExpanded.key !== null) {\n      if (diffExpanded.add) {\n        var keyIndex = prevData.findIndex(function (_ref) {\n          var key = _ref.key;\n          return key === diffExpanded.key;\n        });\n        var rangeNodes = getMinimumRangeTransitionRange(getExpandRange(prevData, data, diffExpanded.key), virtual, height, itemHeight);\n        var newTransitionData = prevData.slice();\n        newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(newTransitionData);\n        setTransitionRange(rangeNodes);\n        setMotionType('show');\n      } else {\n        var _keyIndex = data.findIndex(function (_ref2) {\n          var key = _ref2.key;\n          return key === diffExpanded.key;\n        });\n\n        var _rangeNodes = getMinimumRangeTransitionRange(getExpandRange(data, prevData, diffExpanded.key), virtual, height, itemHeight);\n\n        var _newTransitionData = data.slice();\n\n        _newTransitionData.splice(_keyIndex + 1, 0, MotionFlattenData);\n\n        setTransitionData(_newTransitionData);\n        setTransitionRange(_rangeNodes);\n        setMotionType('hide');\n      }\n    } else if (prevData !== data) {\n      // If whole data changed, we just refresh the list\n      setPrevData(data);\n      setTransitionData(data);\n    }\n  }, [expandedKeys, data]); // We should clean up motion if is changed by dragging\n\n  React.useEffect(function () {\n    if (!dragging) {\n      onMotionEnd();\n    }\n  }, [dragging]);\n  var mergedData = motion ? transitionData : data;\n  var treeNodeRequiredProps = {\n    expandedKeys: expandedKeys,\n    selectedKeys: selectedKeys,\n    loadedKeys: loadedKeys,\n    loadingKeys: loadingKeys,\n    checkedKeys: checkedKeys,\n    halfCheckedKeys: halfCheckedKeys,\n    dragOverNodeKey: dragOverNodeKey,\n    dropPosition: dropPosition,\n    keyEntities: keyEntities\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, focused && activeItem && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, getAccessibilityPath(activeItem)), /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"input\", {\n    style: HIDDEN_STYLE,\n    disabled: focusable === false || disabled,\n    tabIndex: focusable !== false ? tabIndex : null,\n    onKeyDown: onKeyDown,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    value: \"\",\n    onChange: noop,\n    \"aria-label\": \"for screen reader\"\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-treenode\"),\n    \"aria-hidden\": true,\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      visibility: 'hidden',\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: indentMeasurerRef,\n    className: \"\".concat(prefixCls, \"-indent-unit\")\n  }))), /*#__PURE__*/React.createElement(VirtualList, _extends({}, domProps, {\n    data: mergedData,\n    itemKey: itemKey,\n    height: height,\n    fullHeight: false,\n    virtual: virtual,\n    itemHeight: itemHeight,\n    prefixCls: \"\".concat(prefixCls, \"-list\"),\n    ref: listRef,\n    onVisibleChange: function onVisibleChange(originList, fullList) {\n      var originSet = new Set(originList);\n      var restList = fullList.filter(function (item) {\n        return !originSet.has(item);\n      }); // Motion node is not render. Skip motion\n\n      if (restList.some(function (item) {\n        return itemKey(item) === MOTION_KEY;\n      })) {\n        onMotionEnd();\n      }\n    }\n  }), function (treeNode) {\n    var pos = treeNode.pos,\n        restProps = _extends({}, treeNode.data),\n        title = treeNode.title,\n        key = treeNode.key,\n        isStart = treeNode.isStart,\n        isEnd = treeNode.isEnd;\n\n    var mergedKey = getKey(key, pos);\n    delete restProps.key;\n    delete restProps.children;\n    var treeNodeProps = getTreeNodeProps(mergedKey, treeNodeRequiredProps);\n    return /*#__PURE__*/React.createElement(MotionTreeNode, _extends({}, restProps, treeNodeProps, {\n      title: title,\n      active: !!activeItem && key === activeItem.key,\n      pos: pos,\n      data: treeNode.data,\n      isStart: isStart,\n      isEnd: isEnd,\n      motion: motion,\n      motionNodes: key === MOTION_KEY ? transitionRange : null,\n      motionType: motionType,\n      onMotionStart: onListChangeStart,\n      onMotionEnd: onMotionEnd,\n      treeNodeRequiredProps: treeNodeRequiredProps,\n      onMouseMove: function onMouseMove() {\n        onActiveChange(null);\n      }\n    }));\n  }));\n};\n\nvar NodeList = /*#__PURE__*/React.forwardRef(RefNodeList);\nNodeList.displayName = 'NodeList';\nexport default NodeList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;;AAEpa;AACA;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,kBAAkB;AACnE,SAASC,gBAAgB,EAAEC,MAAM,QAAQ,kBAAkB;AAC3D,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE;AACV,CAAC;AAED,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG,CAAC,CAAC;AAE7B,OAAO,IAAIC,UAAU,GAAG,iBAAiB,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;AAC/D,IAAIC,UAAU,GAAG;EACfC,GAAG,EAAEL;AACP,CAAC;AACD,OAAO,IAAIM,YAAY,GAAG;EACxBD,GAAG,EAAEL,UAAU;EACfO,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAEN,UAAU;EAChBO,KAAK,EAAE,CAACP,UAAU;AACpB,CAAC;AACD,IAAIQ,iBAAiB,GAAG;EACtBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,EAAE;EACZL,GAAG,EAAEH,YAAY,CAACG,GAAG;EACrBM,IAAI,EAAEX,UAAU;EAChBY,KAAK,EAAE,IAAI;EACXX,GAAG,EAAEL,UAAU;EAEf;EACAiB,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE;AACT,CAAC;AACD;AACA;AACA;;AAEA,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAEC,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,EAAE;EAChF,IAAID,OAAO,KAAK,KAAK,IAAI,CAAC7B,MAAM,EAAE;IAChC,OAAO4B,IAAI;EACb;EAEA,OAAOA,IAAI,CAACG,KAAK,CAAC,CAAC,EAAErB,IAAI,CAACsB,IAAI,CAAChC,MAAM,GAAG8B,UAAU,CAAC,GAAG,CAAC,CAAC;AAC1D;AAEA,SAASG,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIrB,GAAG,GAAGqB,IAAI,CAACrB,GAAG;IACdI,GAAG,GAAGiB,IAAI,CAACjB,GAAG;EAClB,OAAOpB,MAAM,CAACgB,GAAG,EAAEI,GAAG,CAAC;AACzB;AAEA,SAASkB,oBAAoBA,CAACD,IAAI,EAAE;EAClC,IAAIE,IAAI,GAAGC,MAAM,CAACH,IAAI,CAACX,IAAI,CAACV,GAAG,CAAC;EAChC,IAAIyB,OAAO,GAAGJ,IAAI;EAElB,OAAOI,OAAO,CAACjB,MAAM,EAAE;IACrBiB,OAAO,GAAGA,OAAO,CAACjB,MAAM;IACxBe,IAAI,GAAG,EAAE,CAAC3B,MAAM,CAAC6B,OAAO,CAACf,IAAI,CAACV,GAAG,EAAE,KAAK,CAAC,CAACJ,MAAM,CAAC2B,IAAI,CAAC;EACxD;EAEA,OAAOA,IAAI;AACb;AAEA,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BnB,IAAI,GAAGiB,KAAK,CAACjB,IAAI;IACjBoB,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,eAAe,GAAGV,KAAK,CAACU,eAAe;IACvCC,WAAW,GAAGX,KAAK,CAACW,WAAW;IAC/BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,eAAe,GAAGd,KAAK,CAACc,eAAe;IACvCC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IACrBxD,MAAM,GAAGwC,KAAK,CAACxC,MAAM;IACrB8B,UAAU,GAAGU,KAAK,CAACV,UAAU;IAC7BD,OAAO,GAAGW,KAAK,CAACX,OAAO;IACvB4B,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,OAAO,GAAGnB,KAAK,CAACmB,OAAO;IACvBC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,OAAO,GAAGtB,KAAK,CAACsB,OAAO;IACvBC,MAAM,GAAGvB,KAAK,CAACuB,MAAM;IACrBC,cAAc,GAAGxB,KAAK,CAACwB,cAAc;IACrCC,iBAAiB,GAAGzB,KAAK,CAACyB,iBAAiB;IAC3CC,eAAe,GAAG1B,KAAK,CAAC0B,eAAe;IACvCC,QAAQ,GAAG9E,wBAAwB,CAACmD,KAAK,EAAElD,SAAS,CAAC,CAAC,CAAC;;EAG3D,IAAI8E,OAAO,GAAG7E,KAAK,CAAC8E,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,iBAAiB,GAAG/E,KAAK,CAAC8E,MAAM,CAAC,IAAI,CAAC;EAC1C9E,KAAK,CAACgF,mBAAmB,CAAC9B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL+B,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClCL,OAAO,CAAC9B,OAAO,CAACkC,QAAQ,CAACC,MAAM,CAAC;MAClC,CAAC;MACDC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;QACxC,OAAOJ,iBAAiB,CAAChC,OAAO,CAACqC,WAAW;MAC9C;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,eAAe,GAAGrF,KAAK,CAACsF,QAAQ,CAAChC,YAAY,CAAC;IAC9CiC,gBAAgB,GAAG1F,cAAc,CAACwF,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE7C,IAAIG,gBAAgB,GAAG1F,KAAK,CAACsF,QAAQ,CAACtD,IAAI,CAAC;IACvC2D,gBAAgB,GAAG9F,cAAc,CAAC6F,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAErC,IAAIG,gBAAgB,GAAG9F,KAAK,CAACsF,QAAQ,CAACtD,IAAI,CAAC;IACvC+D,gBAAgB,GAAGlG,cAAc,CAACiG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,gBAAgB,GAAGlG,KAAK,CAACsF,QAAQ,CAAC,EAAE,CAAC;IACrCa,gBAAgB,GAAGtG,cAAc,CAACqG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE5C,IAAIG,gBAAgB,GAAGtG,KAAK,CAACsF,QAAQ,CAAC,IAAI,CAAC;IACvCiB,iBAAiB,GAAG1G,cAAc,CAACyG,gBAAgB,EAAE,CAAC,CAAC;IACvDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,aAAa,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAExC,SAASG,WAAWA,CAAA,EAAG;IACrBb,WAAW,CAAC7D,IAAI,CAAC;IACjBiE,iBAAiB,CAACjE,IAAI,CAAC;IACvBqE,kBAAkB,CAAC,EAAE,CAAC;IACtBI,aAAa,CAAC,IAAI,CAAC;IACnB9B,eAAe,CAAC,CAAC;EACnB,CAAC,CAAC;;EAGF3E,KAAK,CAAC2G,SAAS,CAAC,YAAY;IAC1BlB,mBAAmB,CAACnC,YAAY,CAAC;IACjC,IAAIsD,YAAY,GAAGzG,gBAAgB,CAACqF,gBAAgB,EAAElC,YAAY,CAAC;IAEnE,IAAIsD,YAAY,CAACtF,GAAG,KAAK,IAAI,EAAE;MAC7B,IAAIsF,YAAY,CAACC,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAGlB,QAAQ,CAACmB,SAAS,CAAC,UAAUC,IAAI,EAAE;UAChD,IAAI1F,GAAG,GAAG0F,IAAI,CAAC1F,GAAG;UAClB,OAAOA,GAAG,KAAKsF,YAAY,CAACtF,GAAG;QACjC,CAAC,CAAC;QACF,IAAI2F,UAAU,GAAG7E,8BAA8B,CAAChC,cAAc,CAACwF,QAAQ,EAAE5D,IAAI,EAAE4E,YAAY,CAACtF,GAAG,CAAC,EAAEgB,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,CAAC;QAC9H,IAAI2E,iBAAiB,GAAGtB,QAAQ,CAACpD,KAAK,CAAC,CAAC;QACxC0E,iBAAiB,CAACC,MAAM,CAACL,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAEjF,iBAAiB,CAAC;QAC5DoE,iBAAiB,CAACiB,iBAAiB,CAAC;QACpCb,kBAAkB,CAACY,UAAU,CAAC;QAC9BR,aAAa,CAAC,MAAM,CAAC;MACvB,CAAC,MAAM;QACL,IAAIW,SAAS,GAAGpF,IAAI,CAAC+E,SAAS,CAAC,UAAUM,KAAK,EAAE;UAC9C,IAAI/F,GAAG,GAAG+F,KAAK,CAAC/F,GAAG;UACnB,OAAOA,GAAG,KAAKsF,YAAY,CAACtF,GAAG;QACjC,CAAC,CAAC;QAEF,IAAIgG,WAAW,GAAGlF,8BAA8B,CAAChC,cAAc,CAAC4B,IAAI,EAAE4D,QAAQ,EAAEgB,YAAY,CAACtF,GAAG,CAAC,EAAEgB,OAAO,EAAE7B,MAAM,EAAE8B,UAAU,CAAC;QAE/H,IAAIgF,kBAAkB,GAAGvF,IAAI,CAACQ,KAAK,CAAC,CAAC;QAErC+E,kBAAkB,CAACJ,MAAM,CAACC,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEvF,iBAAiB,CAAC;QAE9DoE,iBAAiB,CAACsB,kBAAkB,CAAC;QACrClB,kBAAkB,CAACiB,WAAW,CAAC;QAC/Bb,aAAa,CAAC,MAAM,CAAC;MACvB;IACF,CAAC,MAAM,IAAIb,QAAQ,KAAK5D,IAAI,EAAE;MAC5B;MACA6D,WAAW,CAAC7D,IAAI,CAAC;MACjBiE,iBAAiB,CAACjE,IAAI,CAAC;IACzB;EACF,CAAC,EAAE,CAACsB,YAAY,EAAEtB,IAAI,CAAC,CAAC,CAAC,CAAC;;EAE1BhC,KAAK,CAAC2G,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC7C,QAAQ,EAAE;MACb4C,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC5C,QAAQ,CAAC,CAAC;EACd,IAAI0D,UAAU,GAAGvD,MAAM,GAAG+B,cAAc,GAAGhE,IAAI;EAC/C,IAAIyF,qBAAqB,GAAG;IAC1BnE,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BE,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA,WAAW;IACxBF,WAAW,EAAEA,WAAW;IACxBG,eAAe,EAAEA,eAAe;IAChCI,eAAe,EAAEA,eAAe;IAChCC,YAAY,EAAEA,YAAY;IAC1BJ,WAAW,EAAEA;EACf,CAAC;EACD,OAAO,aAAa5D,KAAK,CAAC0H,aAAa,CAAC1H,KAAK,CAAC2H,QAAQ,EAAE,IAAI,EAAEvD,OAAO,IAAID,UAAU,IAAI,aAAanE,KAAK,CAAC0H,aAAa,CAAC,MAAM,EAAE;IAC9HE,KAAK,EAAErH,YAAY;IACnB,WAAW,EAAE;EACf,CAAC,EAAEqC,oBAAoB,CAACuB,UAAU,CAAC,CAAC,EAAE,aAAanE,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa1H,KAAK,CAAC0H,aAAa,CAAC,OAAO,EAAE;IAC5HE,KAAK,EAAErH,YAAY;IACnBsD,QAAQ,EAAEK,SAAS,KAAK,KAAK,IAAIL,QAAQ;IACzCQ,QAAQ,EAAEH,SAAS,KAAK,KAAK,GAAGG,QAAQ,GAAG,IAAI;IAC/CC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdqD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE9G,IAAI;IACd,YAAY,EAAE;EAChB,CAAC,CAAC,CAAC,EAAE,aAAahB,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;IAC3CK,SAAS,EAAE,EAAE,CAAC7G,MAAM,CAACiC,SAAS,EAAE,WAAW,CAAC;IAC5C,aAAa,EAAE,IAAI;IACnByE,KAAK,EAAE;MACLI,QAAQ,EAAE,UAAU;MACpBC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE,QAAQ;MACpBzH,MAAM,EAAE,CAAC;MACTE,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,aAAaX,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;IACzCK,SAAS,EAAE,EAAE,CAAC7G,MAAM,CAACiC,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAanD,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;IACzCxE,GAAG,EAAE6B,iBAAiB;IACtBgD,SAAS,EAAE,EAAE,CAAC7G,MAAM,CAACiC,SAAS,EAAE,cAAc;EAChD,CAAC,CAAC,CAAC,CAAC,EAAE,aAAanD,KAAK,CAAC0H,aAAa,CAACzH,WAAW,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEgF,QAAQ,EAAE;IACzE5C,IAAI,EAAEwF,UAAU;IAChB9E,OAAO,EAAEA,OAAO;IAChBjC,MAAM,EAAEA,MAAM;IACd0H,UAAU,EAAE,KAAK;IACjB7F,OAAO,EAAEA,OAAO;IAChBC,UAAU,EAAEA,UAAU;IACtBY,SAAS,EAAE,EAAE,CAACjC,MAAM,CAACiC,SAAS,EAAE,OAAO,CAAC;IACxCD,GAAG,EAAE2B,OAAO;IACZuD,eAAe,EAAE,SAASA,eAAeA,CAACC,UAAU,EAAEC,QAAQ,EAAE;MAC9D,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAACH,UAAU,CAAC;MACnC,IAAII,QAAQ,GAAGH,QAAQ,CAACI,MAAM,CAAC,UAAU/F,IAAI,EAAE;QAC7C,OAAO,CAAC4F,SAAS,CAACI,GAAG,CAAChG,IAAI,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAI8F,QAAQ,CAACG,IAAI,CAAC,UAAUjG,IAAI,EAAE;QAChC,OAAOD,OAAO,CAACC,IAAI,CAAC,KAAK1B,UAAU;MACrC,CAAC,CAAC,EAAE;QACFyF,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,CAAC,EAAE,UAAUmC,QAAQ,EAAE;IACtB,IAAInH,GAAG,GAAGmH,QAAQ,CAACnH,GAAG;MAClBoH,SAAS,GAAGlJ,QAAQ,CAAC,CAAC,CAAC,EAAEiJ,QAAQ,CAAC7G,IAAI,CAAC;MACvCC,KAAK,GAAG4G,QAAQ,CAAC5G,KAAK;MACtBX,GAAG,GAAGuH,QAAQ,CAACvH,GAAG;MAClBY,OAAO,GAAG2G,QAAQ,CAAC3G,OAAO;MAC1BC,KAAK,GAAG0G,QAAQ,CAAC1G,KAAK;IAE1B,IAAI4G,SAAS,GAAGzI,MAAM,CAACgB,GAAG,EAAEI,GAAG,CAAC;IAChC,OAAOoH,SAAS,CAACxH,GAAG;IACpB,OAAOwH,SAAS,CAAC/G,QAAQ;IACzB,IAAIiH,aAAa,GAAG3I,gBAAgB,CAAC0I,SAAS,EAAEtB,qBAAqB,CAAC;IACtE,OAAO,aAAazH,KAAK,CAAC0H,aAAa,CAACxH,cAAc,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEkJ,SAAS,EAAEE,aAAa,EAAE;MAC7F/G,KAAK,EAAEA,KAAK;MACZgH,MAAM,EAAE,CAAC,CAAC9E,UAAU,IAAI7C,GAAG,KAAK6C,UAAU,CAAC7C,GAAG;MAC9CI,GAAG,EAAEA,GAAG;MACRM,IAAI,EAAE6G,QAAQ,CAAC7G,IAAI;MACnBE,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA,KAAK;MACZ8B,MAAM,EAAEA,MAAM;MACdiF,WAAW,EAAE5H,GAAG,KAAKL,UAAU,GAAGmF,eAAe,GAAG,IAAI;MACxDI,UAAU,EAAEA,UAAU;MACtB2C,aAAa,EAAEzE,iBAAiB;MAChCgC,WAAW,EAAEA,WAAW;MACxBe,qBAAqB,EAAEA,qBAAqB;MAC5C2B,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC3E,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI4E,QAAQ,GAAG,aAAarJ,KAAK,CAACsJ,UAAU,CAACtG,WAAW,CAAC;AACzDqG,QAAQ,CAACE,WAAW,GAAG,UAAU;AACjC,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}