{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useBaseProps } from 'rc-select';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { SEARCH_MARK } from '../hooks/useSearchOptions';\nexport default (function (ref, options, fieldNames, activeValueCells, setActiveValueCells, onKeyBoardSelect) {\n  var _useBaseProps = useBaseProps(),\n    direction = _useBaseProps.direction,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open;\n  var rtl = direction === 'rtl';\n  var _React$useMemo = React.useMemo(function () {\n      var activeIndex = -1;\n      var currentOptions = options;\n      var mergedActiveIndexes = [];\n      var mergedActiveValueCells = [];\n      var len = activeValueCells.length; // Fill validate active value cells and index\n\n      var _loop = function _loop(i) {\n        // Mark the active index for current options\n        var nextActiveIndex = currentOptions.findIndex(function (option) {\n          return option[fieldNames.value] === activeValueCells[i];\n        });\n        if (nextActiveIndex === -1) {\n          return \"break\";\n        }\n        activeIndex = nextActiveIndex;\n        mergedActiveIndexes.push(activeIndex);\n        mergedActiveValueCells.push(activeValueCells[i]);\n        currentOptions = currentOptions[activeIndex][fieldNames.children];\n      };\n\n      // Fill validate active value cells and index\n      for (var i = 0; i < len && currentOptions; i += 1) {\n        var _ret = _loop(i);\n        if (_ret === \"break\") break;\n      } // Fill last active options\n\n      // Fill last active options\n      var activeOptions = options;\n      for (var _i = 0; _i < mergedActiveIndexes.length - 1; _i += 1) {\n        activeOptions = activeOptions[mergedActiveIndexes[_i]][fieldNames.children];\n      }\n      return [mergedActiveValueCells, activeIndex, activeOptions];\n    }, [activeValueCells, fieldNames, options]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 3),\n    validActiveValueCells = _React$useMemo2[0],\n    lastActiveIndex = _React$useMemo2[1],\n    lastActiveOptions = _React$useMemo2[2]; // Update active value cells and scroll to target element\n\n  var internalSetActiveValueCells = function internalSetActiveValueCells(next) {\n    setActiveValueCells(next);\n  }; // Same options offset\n\n  var offsetActiveOption = function offsetActiveOption(offset) {\n    var len = lastActiveOptions.length;\n    var currentIndex = lastActiveIndex;\n    if (currentIndex === -1 && offset < 0) {\n      currentIndex = len;\n    }\n    for (var i = 0; i < len; i += 1) {\n      currentIndex = (currentIndex + offset + len) % len;\n      var option = lastActiveOptions[currentIndex];\n      if (option && !option.disabled) {\n        var value = option[fieldNames.value];\n        var nextActiveCells = validActiveValueCells.slice(0, -1).concat(value);\n        internalSetActiveValueCells(nextActiveCells);\n        return;\n      }\n    }\n  }; // Different options offset\n\n  var prevColumn = function prevColumn() {\n    if (validActiveValueCells.length > 1) {\n      var nextActiveCells = validActiveValueCells.slice(0, -1);\n      internalSetActiveValueCells(nextActiveCells);\n    } else {\n      toggleOpen(false);\n    }\n  };\n  var nextColumn = function nextColumn() {\n    var _lastActiveOptions$la;\n    var nextOptions = ((_lastActiveOptions$la = lastActiveOptions[lastActiveIndex]) === null || _lastActiveOptions$la === void 0 ? void 0 : _lastActiveOptions$la[fieldNames.children]) || [];\n    var nextOption = nextOptions.find(function (option) {\n      return !option.disabled;\n    });\n    if (nextOption) {\n      var nextActiveCells = [].concat(_toConsumableArray(validActiveValueCells), [nextOption[fieldNames.value]]);\n      internalSetActiveValueCells(nextActiveCells);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      // scrollTo: treeRef.current?.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              }\n              if (offset !== 0) {\n                offsetActiveOption(offset);\n              }\n              break;\n            }\n          case KeyCode.LEFT:\n            {\n              if (rtl) {\n                nextColumn();\n              } else {\n                prevColumn();\n              }\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              if (rtl) {\n                prevColumn();\n              } else {\n                nextColumn();\n              }\n              break;\n            }\n          case KeyCode.BACKSPACE:\n            {\n              if (!searchValue) {\n                prevColumn();\n              }\n              break;\n            }\n          // >>> Select\n\n          case KeyCode.ENTER:\n            {\n              if (validActiveValueCells.length) {\n                var option = lastActiveOptions[lastActiveIndex]; // Search option should revert back of origin options\n\n                var originOptions = (option === null || option === void 0 ? void 0 : option[SEARCH_MARK]) || [];\n                if (originOptions.length) {\n                  onKeyBoardSelect(originOptions.map(function (opt) {\n                    return opt[fieldNames.value];\n                  }), originOptions[originOptions.length - 1]);\n                } else {\n                  onKeyBoardSelect(validActiveValueCells, lastActiveOptions[lastActiveIndex]);\n                }\n              }\n              break;\n            }\n          // >>> Close\n\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n});", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "useBaseProps", "KeyCode", "SEARCH_MARK", "ref", "options", "fieldNames", "activeValueCells", "setActiveValueCells", "onKeyBoardSelect", "_useBaseProps", "direction", "searchValue", "toggle<PERSON><PERSON>", "open", "rtl", "_React$useMemo", "useMemo", "activeIndex", "currentOptions", "mergedActiveIndexes", "mergedActiveValueCells", "len", "length", "_loop", "i", "nextActiveIndex", "findIndex", "option", "value", "push", "children", "_ret", "activeOptions", "_i", "_React$useMemo2", "validActiveValueCells", "lastActiveIndex", "lastActiveOptions", "internalSetActiveValueCells", "next", "offsetActiveOption", "offset", "currentIndex", "disabled", "nextActiveCells", "slice", "concat", "prevColumn", "nextColumn", "_lastActiveOptions$la", "nextOptions", "nextOption", "find", "useImperativeHandle", "onKeyDown", "event", "which", "UP", "DOWN", "LEFT", "RIGHT", "BACKSPACE", "ENTER", "originOptions", "map", "opt", "ESC", "stopPropagation", "onKeyUp"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-cascader/es/OptionList/useKeyboard.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useBaseProps } from 'rc-select';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { SEARCH_MARK } from '../hooks/useSearchOptions';\nexport default (function (ref, options, fieldNames, activeValueCells, setActiveValueCells, onKeyBoardSelect) {\n  var _useBaseProps = useBaseProps(),\n      direction = _useBaseProps.direction,\n      searchValue = _useBaseProps.searchValue,\n      toggleOpen = _useBaseProps.toggleOpen,\n      open = _useBaseProps.open;\n\n  var rtl = direction === 'rtl';\n\n  var _React$useMemo = React.useMemo(function () {\n    var activeIndex = -1;\n    var currentOptions = options;\n    var mergedActiveIndexes = [];\n    var mergedActiveValueCells = [];\n    var len = activeValueCells.length; // Fill validate active value cells and index\n\n    var _loop = function _loop(i) {\n      // Mark the active index for current options\n      var nextActiveIndex = currentOptions.findIndex(function (option) {\n        return option[fieldNames.value] === activeValueCells[i];\n      });\n\n      if (nextActiveIndex === -1) {\n        return \"break\";\n      }\n\n      activeIndex = nextActiveIndex;\n      mergedActiveIndexes.push(activeIndex);\n      mergedActiveValueCells.push(activeValueCells[i]);\n      currentOptions = currentOptions[activeIndex][fieldNames.children];\n    };\n\n    // Fill validate active value cells and index\n    for (var i = 0; i < len && currentOptions; i += 1) {\n      var _ret = _loop(i);\n\n      if (_ret === \"break\") break;\n    } // Fill last active options\n\n\n    // Fill last active options\n    var activeOptions = options;\n\n    for (var _i = 0; _i < mergedActiveIndexes.length - 1; _i += 1) {\n      activeOptions = activeOptions[mergedActiveIndexes[_i]][fieldNames.children];\n    }\n\n    return [mergedActiveValueCells, activeIndex, activeOptions];\n  }, [activeValueCells, fieldNames, options]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 3),\n      validActiveValueCells = _React$useMemo2[0],\n      lastActiveIndex = _React$useMemo2[1],\n      lastActiveOptions = _React$useMemo2[2]; // Update active value cells and scroll to target element\n\n\n  var internalSetActiveValueCells = function internalSetActiveValueCells(next) {\n    setActiveValueCells(next);\n  }; // Same options offset\n\n\n  var offsetActiveOption = function offsetActiveOption(offset) {\n    var len = lastActiveOptions.length;\n    var currentIndex = lastActiveIndex;\n\n    if (currentIndex === -1 && offset < 0) {\n      currentIndex = len;\n    }\n\n    for (var i = 0; i < len; i += 1) {\n      currentIndex = (currentIndex + offset + len) % len;\n      var option = lastActiveOptions[currentIndex];\n\n      if (option && !option.disabled) {\n        var value = option[fieldNames.value];\n        var nextActiveCells = validActiveValueCells.slice(0, -1).concat(value);\n        internalSetActiveValueCells(nextActiveCells);\n        return;\n      }\n    }\n  }; // Different options offset\n\n\n  var prevColumn = function prevColumn() {\n    if (validActiveValueCells.length > 1) {\n      var nextActiveCells = validActiveValueCells.slice(0, -1);\n      internalSetActiveValueCells(nextActiveCells);\n    } else {\n      toggleOpen(false);\n    }\n  };\n\n  var nextColumn = function nextColumn() {\n    var _lastActiveOptions$la;\n\n    var nextOptions = ((_lastActiveOptions$la = lastActiveOptions[lastActiveIndex]) === null || _lastActiveOptions$la === void 0 ? void 0 : _lastActiveOptions$la[fieldNames.children]) || [];\n    var nextOption = nextOptions.find(function (option) {\n      return !option.disabled;\n    });\n\n    if (nextOption) {\n      var nextActiveCells = [].concat(_toConsumableArray(validActiveValueCells), [nextOption[fieldNames.value]]);\n      internalSetActiveValueCells(nextActiveCells);\n    }\n  };\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      // scrollTo: treeRef.current?.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which;\n\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              }\n\n              if (offset !== 0) {\n                offsetActiveOption(offset);\n              }\n\n              break;\n            }\n\n          case KeyCode.LEFT:\n            {\n              if (rtl) {\n                nextColumn();\n              } else {\n                prevColumn();\n              }\n\n              break;\n            }\n\n          case KeyCode.RIGHT:\n            {\n              if (rtl) {\n                prevColumn();\n              } else {\n                nextColumn();\n              }\n\n              break;\n            }\n\n          case KeyCode.BACKSPACE:\n            {\n              if (!searchValue) {\n                prevColumn();\n              }\n\n              break;\n            }\n          // >>> Select\n\n          case KeyCode.ENTER:\n            {\n              if (validActiveValueCells.length) {\n                var option = lastActiveOptions[lastActiveIndex]; // Search option should revert back of origin options\n\n                var originOptions = (option === null || option === void 0 ? void 0 : option[SEARCH_MARK]) || [];\n\n                if (originOptions.length) {\n                  onKeyBoardSelect(originOptions.map(function (opt) {\n                    return opt[fieldNames.value];\n                  }), originOptions[originOptions.length - 1]);\n                } else {\n                  onKeyBoardSelect(validActiveValueCells, lastActiveOptions[lastActiveIndex]);\n                }\n              }\n\n              break;\n            }\n          // >>> Close\n\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,gBAAgB,UAAUC,GAAG,EAAEC,OAAO,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAE;EAC3G,IAAIC,aAAa,GAAGT,YAAY,CAAC,CAAC;IAC9BU,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,WAAW,GAAGF,aAAa,CAACE,WAAW;IACvCC,UAAU,GAAGH,aAAa,CAACG,UAAU;IACrCC,IAAI,GAAGJ,aAAa,CAACI,IAAI;EAE7B,IAAIC,GAAG,GAAGJ,SAAS,KAAK,KAAK;EAE7B,IAAIK,cAAc,GAAGhB,KAAK,CAACiB,OAAO,CAAC,YAAY;MAC7C,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,IAAIC,cAAc,GAAGd,OAAO;MAC5B,IAAIe,mBAAmB,GAAG,EAAE;MAC5B,IAAIC,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,GAAG,GAAGf,gBAAgB,CAACgB,MAAM,CAAC,CAAC;;MAEnC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,CAAC,EAAE;QAC5B;QACA,IAAIC,eAAe,GAAGP,cAAc,CAACQ,SAAS,CAAC,UAAUC,MAAM,EAAE;UAC/D,OAAOA,MAAM,CAACtB,UAAU,CAACuB,KAAK,CAAC,KAAKtB,gBAAgB,CAACkB,CAAC,CAAC;QACzD,CAAC,CAAC;QAEF,IAAIC,eAAe,KAAK,CAAC,CAAC,EAAE;UAC1B,OAAO,OAAO;QAChB;QAEAR,WAAW,GAAGQ,eAAe;QAC7BN,mBAAmB,CAACU,IAAI,CAACZ,WAAW,CAAC;QACrCG,sBAAsB,CAACS,IAAI,CAACvB,gBAAgB,CAACkB,CAAC,CAAC,CAAC;QAChDN,cAAc,GAAGA,cAAc,CAACD,WAAW,CAAC,CAACZ,UAAU,CAACyB,QAAQ,CAAC;MACnE,CAAC;;MAED;MACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,IAAIH,cAAc,EAAEM,CAAC,IAAI,CAAC,EAAE;QACjD,IAAIO,IAAI,GAAGR,KAAK,CAACC,CAAC,CAAC;QAEnB,IAAIO,IAAI,KAAK,OAAO,EAAE;MACxB,CAAC,CAAC;;MAGF;MACA,IAAIC,aAAa,GAAG5B,OAAO;MAE3B,KAAK,IAAI6B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGd,mBAAmB,CAACG,MAAM,GAAG,CAAC,EAAEW,EAAE,IAAI,CAAC,EAAE;QAC7DD,aAAa,GAAGA,aAAa,CAACb,mBAAmB,CAACc,EAAE,CAAC,CAAC,CAAC5B,UAAU,CAACyB,QAAQ,CAAC;MAC7E;MAEA,OAAO,CAACV,sBAAsB,EAAEH,WAAW,EAAEe,aAAa,CAAC;IAC7D,CAAC,EAAE,CAAC1B,gBAAgB,EAAED,UAAU,EAAED,OAAO,CAAC,CAAC;IACvC8B,eAAe,GAAGpC,cAAc,CAACiB,cAAc,EAAE,CAAC,CAAC;IACnDoB,qBAAqB,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC1CE,eAAe,GAAGF,eAAe,CAAC,CAAC,CAAC;IACpCG,iBAAiB,GAAGH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAII,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,IAAI,EAAE;IAC3EhC,mBAAmB,CAACgC,IAAI,CAAC;EAC3B,CAAC,CAAC,CAAC;;EAGH,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,MAAM,EAAE;IAC3D,IAAIpB,GAAG,GAAGgB,iBAAiB,CAACf,MAAM;IAClC,IAAIoB,YAAY,GAAGN,eAAe;IAElC,IAAIM,YAAY,KAAK,CAAC,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;MACrCC,YAAY,GAAGrB,GAAG;IACpB;IAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,IAAI,CAAC,EAAE;MAC/BkB,YAAY,GAAG,CAACA,YAAY,GAAGD,MAAM,GAAGpB,GAAG,IAAIA,GAAG;MAClD,IAAIM,MAAM,GAAGU,iBAAiB,CAACK,YAAY,CAAC;MAE5C,IAAIf,MAAM,IAAI,CAACA,MAAM,CAACgB,QAAQ,EAAE;QAC9B,IAAIf,KAAK,GAAGD,MAAM,CAACtB,UAAU,CAACuB,KAAK,CAAC;QACpC,IAAIgB,eAAe,GAAGT,qBAAqB,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAC;QACtEU,2BAA2B,CAACM,eAAe,CAAC;QAC5C;MACF;IACF;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIZ,qBAAqB,CAACb,MAAM,GAAG,CAAC,EAAE;MACpC,IAAIsB,eAAe,GAAGT,qBAAqB,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxDP,2BAA2B,CAACM,eAAe,CAAC;IAC9C,CAAC,MAAM;MACLhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIoC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIC,qBAAqB;IAEzB,IAAIC,WAAW,GAAG,CAAC,CAACD,qBAAqB,GAAGZ,iBAAiB,CAACD,eAAe,CAAC,MAAM,IAAI,IAAIa,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC5C,UAAU,CAACyB,QAAQ,CAAC,KAAK,EAAE;IACzL,IAAIqB,UAAU,GAAGD,WAAW,CAACE,IAAI,CAAC,UAAUzB,MAAM,EAAE;MAClD,OAAO,CAACA,MAAM,CAACgB,QAAQ;IACzB,CAAC,CAAC;IAEF,IAAIQ,UAAU,EAAE;MACd,IAAIP,eAAe,GAAG,EAAE,CAACE,MAAM,CAACjD,kBAAkB,CAACsC,qBAAqB,CAAC,EAAE,CAACgB,UAAU,CAAC9C,UAAU,CAACuB,KAAK,CAAC,CAAC,CAAC;MAC1GU,2BAA2B,CAACM,eAAe,CAAC;IAC9C;EACF,CAAC;EAED7C,KAAK,CAACsD,mBAAmB,CAAClD,GAAG,EAAE,YAAY;IACzC,OAAO;MACL;MACAmD,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;QACnC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;QAEvB,QAAQA,KAAK;UACX;UACA,KAAKvD,OAAO,CAACwD,EAAE;UACf,KAAKxD,OAAO,CAACyD,IAAI;YACf;cACE,IAAIjB,MAAM,GAAG,CAAC;cAEd,IAAIe,KAAK,KAAKvD,OAAO,CAACwD,EAAE,EAAE;gBACxBhB,MAAM,GAAG,CAAC,CAAC;cACb,CAAC,MAAM,IAAIe,KAAK,KAAKvD,OAAO,CAACyD,IAAI,EAAE;gBACjCjB,MAAM,GAAG,CAAC;cACZ;cAEA,IAAIA,MAAM,KAAK,CAAC,EAAE;gBAChBD,kBAAkB,CAACC,MAAM,CAAC;cAC5B;cAEA;YACF;UAEF,KAAKxC,OAAO,CAAC0D,IAAI;YACf;cACE,IAAI7C,GAAG,EAAE;gBACPkC,UAAU,CAAC,CAAC;cACd,CAAC,MAAM;gBACLD,UAAU,CAAC,CAAC;cACd;cAEA;YACF;UAEF,KAAK9C,OAAO,CAAC2D,KAAK;YAChB;cACE,IAAI9C,GAAG,EAAE;gBACPiC,UAAU,CAAC,CAAC;cACd,CAAC,MAAM;gBACLC,UAAU,CAAC,CAAC;cACd;cAEA;YACF;UAEF,KAAK/C,OAAO,CAAC4D,SAAS;YACpB;cACE,IAAI,CAAClD,WAAW,EAAE;gBAChBoC,UAAU,CAAC,CAAC;cACd;cAEA;YACF;UACF;;UAEA,KAAK9C,OAAO,CAAC6D,KAAK;YAChB;cACE,IAAI3B,qBAAqB,CAACb,MAAM,EAAE;gBAChC,IAAIK,MAAM,GAAGU,iBAAiB,CAACD,eAAe,CAAC,CAAC,CAAC;;gBAEjD,IAAI2B,aAAa,GAAG,CAACpC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACzB,WAAW,CAAC,KAAK,EAAE;gBAE/F,IAAI6D,aAAa,CAACzC,MAAM,EAAE;kBACxBd,gBAAgB,CAACuD,aAAa,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;oBAChD,OAAOA,GAAG,CAAC5D,UAAU,CAACuB,KAAK,CAAC;kBAC9B,CAAC,CAAC,EAAEmC,aAAa,CAACA,aAAa,CAACzC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC9C,CAAC,MAAM;kBACLd,gBAAgB,CAAC2B,qBAAqB,EAAEE,iBAAiB,CAACD,eAAe,CAAC,CAAC;gBAC7E;cACF;cAEA;YACF;UACF;;UAEA,KAAKnC,OAAO,CAACiE,GAAG;YACd;cACEtD,UAAU,CAAC,KAAK,CAAC;cAEjB,IAAIC,IAAI,EAAE;gBACR0C,KAAK,CAACY,eAAe,CAAC,CAAC;cACzB;YACF;QACJ;MACF,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;IAC/B,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}