{"ast": null, "code": "/* eslint-disable es-x/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nmodule.exports = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == 'symbol';", "map": {"version": 3, "names": ["NATIVE_SYMBOL", "require", "module", "exports", "Symbol", "sham", "iterator"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/use-symbol-as-uid.js"], "sourcesContent": ["/* eslint-disable es-x/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAEzDC,MAAM,CAACC,OAAO,GAAGH,aAAa,IACzB,CAACI,MAAM,CAACC,IAAI,IACZ,OAAOD,MAAM,CAACE,QAAQ,IAAI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}