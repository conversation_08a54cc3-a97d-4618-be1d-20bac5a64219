{"ast": null, "code": "import getScrollBarSize from './getScrollBarSize';\nimport setStyle from './setStyle';\nfunction isBodyOverflowing() {\n  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}\nvar cacheStyle = {};\nexport default (function (close) {\n  if (!isBodyOverflowing() && !close) {\n    return;\n  } // https://github.com/ant-design/ant-design/issues/19729\n\n  var scrollingEffectClassName = 'ant-scrolling-effect';\n  var scrollingEffectClassNameReg = new RegExp(\"\".concat(scrollingEffectClassName), 'g');\n  var bodyClassName = document.body.className;\n  if (close) {\n    if (!scrollingEffectClassNameReg.test(bodyClassName)) return;\n    setStyle(cacheStyle);\n    cacheStyle = {};\n    document.body.className = bodyClassName.replace(scrollingEffectClassNameReg, '').trim();\n    return;\n  }\n  var scrollBarSize = getScrollBarSize();\n  if (scrollBarSize) {\n    cacheStyle = setStyle({\n      position: 'relative',\n      width: \"calc(100% - \".concat(scrollBarSize, \"px)\")\n    });\n    if (!scrollingEffectClassNameReg.test(bodyClassName)) {\n      var addClassName = \"\".concat(bodyClassName, \" \").concat(scrollingEffectClassName);\n      document.body.className = addClassName.trim();\n    }\n  }\n});", "map": {"version": 3, "names": ["getScrollBarSize", "setStyle", "isBodyOverflowing", "document", "body", "scrollHeight", "window", "innerHeight", "documentElement", "clientHeight", "innerWidth", "offsetWidth", "cacheStyle", "close", "scrollingEffectClassName", "scrollingEffectClassNameReg", "RegExp", "concat", "bodyClassName", "className", "test", "replace", "trim", "scrollBarSize", "position", "width", "addClassName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/switchScrollingEffect.js"], "sourcesContent": ["import getScrollBarSize from './getScrollBarSize';\nimport setStyle from './setStyle';\n\nfunction isBodyOverflowing() {\n  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}\n\nvar cacheStyle = {};\nexport default (function (close) {\n  if (!isBodyOverflowing() && !close) {\n    return;\n  } // https://github.com/ant-design/ant-design/issues/19729\n\n\n  var scrollingEffectClassName = 'ant-scrolling-effect';\n  var scrollingEffectClassNameReg = new RegExp(\"\".concat(scrollingEffectClassName), 'g');\n  var bodyClassName = document.body.className;\n\n  if (close) {\n    if (!scrollingEffectClassNameReg.test(bodyClassName)) return;\n    setStyle(cacheStyle);\n    cacheStyle = {};\n    document.body.className = bodyClassName.replace(scrollingEffectClassNameReg, '').trim();\n    return;\n  }\n\n  var scrollBarSize = getScrollBarSize();\n\n  if (scrollBarSize) {\n    cacheStyle = setStyle({\n      position: 'relative',\n      width: \"calc(100% - \".concat(scrollBarSize, \"px)\")\n    });\n\n    if (!scrollingEffectClassNameReg.test(bodyClassName)) {\n      var addClassName = \"\".concat(bodyClassName, \" \").concat(scrollingEffectClassName);\n      document.body.className = addClassName.trim();\n    }\n  }\n});"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,YAAY;AAEjC,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,OAAOC,QAAQ,CAACC,IAAI,CAACC,YAAY,IAAIC,MAAM,CAACC,WAAW,IAAIJ,QAAQ,CAACK,eAAe,CAACC,YAAY,CAAC,IAAIH,MAAM,CAACI,UAAU,GAAGP,QAAQ,CAACC,IAAI,CAACO,WAAW;AACpJ;AAEA,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,gBAAgB,UAAUC,KAAK,EAAE;EAC/B,IAAI,CAACX,iBAAiB,CAAC,CAAC,IAAI,CAACW,KAAK,EAAE;IAClC;EACF,CAAC,CAAC;;EAGF,IAAIC,wBAAwB,GAAG,sBAAsB;EACrD,IAAIC,2BAA2B,GAAG,IAAIC,MAAM,CAAC,EAAE,CAACC,MAAM,CAACH,wBAAwB,CAAC,EAAE,GAAG,CAAC;EACtF,IAAII,aAAa,GAAGf,QAAQ,CAACC,IAAI,CAACe,SAAS;EAE3C,IAAIN,KAAK,EAAE;IACT,IAAI,CAACE,2BAA2B,CAACK,IAAI,CAACF,aAAa,CAAC,EAAE;IACtDjB,QAAQ,CAACW,UAAU,CAAC;IACpBA,UAAU,GAAG,CAAC,CAAC;IACfT,QAAQ,CAACC,IAAI,CAACe,SAAS,GAAGD,aAAa,CAACG,OAAO,CAACN,2BAA2B,EAAE,EAAE,CAAC,CAACO,IAAI,CAAC,CAAC;IACvF;EACF;EAEA,IAAIC,aAAa,GAAGvB,gBAAgB,CAAC,CAAC;EAEtC,IAAIuB,aAAa,EAAE;IACjBX,UAAU,GAAGX,QAAQ,CAAC;MACpBuB,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,cAAc,CAACR,MAAM,CAACM,aAAa,EAAE,KAAK;IACnD,CAAC,CAAC;IAEF,IAAI,CAACR,2BAA2B,CAACK,IAAI,CAACF,aAAa,CAAC,EAAE;MACpD,IAAIQ,YAAY,GAAG,EAAE,CAACT,MAAM,CAACC,aAAa,EAAE,GAAG,CAAC,CAACD,MAAM,CAACH,wBAAwB,CAAC;MACjFX,QAAQ,CAACC,IAAI,CAACe,SAAS,GAAGO,YAAY,CAACJ,IAAI,CAAC,CAAC;IAC/C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}