{"name": "dependency-tree", "version": "10.0.9", "description": "Get the dependency tree of a module", "main": "index.js", "types": "index.d.ts", "files": ["bin/cli.js", "lib/*.js", "index.d.ts", "index.js"], "bin": {"dependency-tree": "bin/cli.js"}, "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-dependency-tree.git"}, "keywords": ["dependency", "tree", "graph", "module", "ast", "requirejs", "AMD", "commonjs", "es6", "sass", "stylus", "less", "typescript"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-dependency-tree/issues"}, "homepage": "https://github.com/dependents/node-dependency-tree", "engines": {"node": ">=14"}, "dependencies": {"commander": "^10.0.1", "filing-cabinet": "^4.1.6", "precinct": "^11.0.5", "typescript": "^5.0.4"}, "devDependencies": {"c8": "^7.13.0", "debug": "^4.3.4", "mocha": "^10.2.0", "mock-fs": "^5.2.0", "resolve": "^1.22.3", "sinon": "^15.1.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["index.d.ts", "test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}