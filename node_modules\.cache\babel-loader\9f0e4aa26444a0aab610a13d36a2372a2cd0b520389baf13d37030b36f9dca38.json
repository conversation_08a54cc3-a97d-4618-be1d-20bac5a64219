{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PaperClipOutlinedSvg from \"@ant-design/icons-svg/es/asn/PaperClipOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar PaperClipOutlined = function PaperClipOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PaperClipOutlinedSvg\n  }));\n};\nPaperClipOutlined.displayName = 'PaperClipOutlined';\nexport default /*#__PURE__*/React.forwardRef(PaperClipOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "PaperClipOutlinedSvg", "AntdIcon", "PaperClipOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@ant-design/icons/es/icons/PaperClipOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PaperClipOutlinedSvg from \"@ant-design/icons-svg/es/asn/PaperClipOutlined\";\nimport AntdIcon from '../components/AntdIcon';\n\nvar PaperClipOutlined = function PaperClipOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PaperClipOutlinedSvg\n  }));\n};\n\nPaperClipOutlined.displayName = 'PaperClipOutlined';\nexport default /*#__PURE__*/React.forwardRef(PaperClipOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAE7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AAEDE,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACnD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}