{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"prefixCls\", \"value\", \"defaultValue\", \"onChange\", \"onSelect\", \"onDeselect\", \"searchValue\", \"inputValue\", \"onSearch\", \"autoClearSearchValue\", \"filterTreeNode\", \"treeNodeFilterProp\", \"showCheckedStrategy\", \"treeNodeLabelProp\", \"multiple\", \"treeCheckable\", \"treeCheckStrictly\", \"labelInValue\", \"fieldNames\", \"treeDataSimpleMode\", \"treeData\", \"children\", \"loadData\", \"treeLoadedKeys\", \"onTreeLoad\", \"treeDefaultExpandAll\", \"treeExpandedKeys\", \"treeDefaultExpandedKeys\", \"onTreeExpand\", \"virtual\", \"listHeight\", \"listItemHeight\", \"onDropdownVisibleChange\", \"dropdownMatchSelectWidth\", \"treeLine\", \"treeIcon\", \"showTreeIcon\", \"switcherIcon\", \"treeMotion\"];\nimport * as React from 'react';\nimport { BaseSelect } from 'rc-select';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport useId from \"rc-select/es/hooks/useId\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport OptionList from './OptionList';\nimport TreeNode from './TreeNode';\nimport { formatStrategyValues, SHOW_ALL, SHOW_PARENT, SHOW_CHILD } from './utils/strategyUtil';\nimport TreeSelectContext from './TreeSelectContext';\nimport LegacyContext from './LegacyContext';\nimport useTreeData from './hooks/useTreeData';\nimport { toArray, fillFieldNames, isNil } from './utils/valueUtil';\nimport useCache from './hooks/useCache';\nimport useRefFunc from './hooks/useRefFunc';\nimport useDataEntities from './hooks/useDataEntities';\nimport { fillAdditionalInfo, fillLegacyProps } from './utils/legacyUtil';\nimport useCheckedKeys from './hooks/useCheckedKeys';\nimport useFilterTreeData from './hooks/useFilterTreeData';\nimport warningProps from './utils/warningPropsUtil';\nimport warning from \"rc-util/es/warning\";\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar TreeSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    onChange = props.onChange,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    searchValue = props.searchValue,\n    inputValue = props.inputValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    filterTreeNode = props.filterTreeNode,\n    _props$treeNodeFilter = props.treeNodeFilterProp,\n    treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,\n    _props$showCheckedStr = props.showCheckedStrategy,\n    showCheckedStrategy = _props$showCheckedStr === void 0 ? SHOW_CHILD : _props$showCheckedStr,\n    treeNodeLabelProp = props.treeNodeLabelProp,\n    multiple = props.multiple,\n    treeCheckable = props.treeCheckable,\n    treeCheckStrictly = props.treeCheckStrictly,\n    labelInValue = props.labelInValue,\n    fieldNames = props.fieldNames,\n    treeDataSimpleMode = props.treeDataSimpleMode,\n    treeData = props.treeData,\n    children = props.children,\n    loadData = props.loadData,\n    treeLoadedKeys = props.treeLoadedKeys,\n    onTreeLoad = props.onTreeLoad,\n    treeDefaultExpandAll = props.treeDefaultExpandAll,\n    treeExpandedKeys = props.treeExpandedKeys,\n    treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,\n    onTreeExpand = props.onTreeExpand,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    treeLine = props.treeLine,\n    treeIcon = props.treeIcon,\n    showTreeIcon = props.showTreeIcon,\n    switcherIcon = props.switcherIcon,\n    treeMotion = props.treeMotion,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var treeConduction = treeCheckable && !treeCheckStrictly;\n  var mergedCheckable = treeCheckable || treeCheckStrictly;\n  var mergedLabelInValue = treeCheckStrictly || labelInValue;\n  var mergedMultiple = mergedCheckable || multiple; // ========================== Warning ===========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ========================= FieldNames =========================\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [JSON.stringify(fieldNames)]); // =========================== Search ===========================\n\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n  var onInternalSearch = function onInternalSearch(searchText) {\n    setSearchValue(searchText);\n    onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n  }; // ============================ Data ============================\n  // `useTreeData` only do convert of `children` or `simpleMode`.\n  // Else will return origin `treeData` for perf consideration.\n  // Do not do anything to loop the data.\n\n  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);\n  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),\n    keyEntities = _useDataEntities.keyEntities,\n    valueEntities = _useDataEntities.valueEntities;\n  /** Get `missingRawValues` which not exist in the tree yet */\n\n  var splitRawValues = React.useCallback(function (newRawValues) {\n    var missingRawValues = [];\n    var existRawValues = []; // Keep missing value in the cache\n\n    newRawValues.forEach(function (val) {\n      if (valueEntities.has(val)) {\n        existRawValues.push(val);\n      } else {\n        missingRawValues.push(val);\n      }\n    });\n    return {\n      missingRawValues: missingRawValues,\n      existRawValues: existRawValues\n    };\n  }, [valueEntities]); // Filtered Tree\n\n  var filteredTreeData = useFilterTreeData(mergedTreeData, mergedSearchValue, {\n    fieldNames: mergedFieldNames,\n    treeNodeFilterProp: treeNodeFilterProp,\n    filterTreeNode: filterTreeNode\n  }); // =========================== Label ============================\n\n  var getLabel = React.useCallback(function (item) {\n    if (item) {\n      if (treeNodeLabelProp) {\n        return item[treeNodeLabelProp];\n      } // Loop from fieldNames\n\n      var titleList = mergedFieldNames._title;\n      for (var i = 0; i < titleList.length; i += 1) {\n        var title = item[titleList[i]];\n        if (title !== undefined) {\n          return title;\n        }\n      }\n    }\n  }, [mergedFieldNames, treeNodeLabelProp]); // ========================= Wrap Value =========================\n\n  var toLabeledValues = React.useCallback(function (draftValues) {\n    var values = toArray(draftValues);\n    return values.map(function (val) {\n      if (isRawValue(val)) {\n        return {\n          value: val\n        };\n      }\n      return val;\n    });\n  }, []);\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    var values = toLabeledValues(draftValues);\n    return values.map(function (item) {\n      var rawLabel = item.label;\n      var rawValue = item.value,\n        rawHalfChecked = item.halfChecked;\n      var rawDisabled;\n      var entity = valueEntities.get(rawValue); // Fill missing label & status\n\n      if (entity) {\n        var _rawLabel;\n        rawLabel = (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);\n        rawDisabled = entity.node.disabled;\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        halfChecked: rawHalfChecked,\n        disabled: rawDisabled\n      };\n    });\n  }, [valueEntities, getLabel, toLabeledValues]); // =========================== Values ===========================\n\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n  var rawMixedLabeledValues = React.useMemo(function () {\n    return toLabeledValues(internalValue);\n  }, [toLabeledValues, internalValue]); // Split value into full check and half check\n\n  var _React$useMemo = React.useMemo(function () {\n      var fullCheckValues = [];\n      var halfCheckValues = [];\n      rawMixedLabeledValues.forEach(function (item) {\n        if (item.halfChecked) {\n          halfCheckValues.push(item);\n        } else {\n          fullCheckValues.push(item);\n        }\n      });\n      return [fullCheckValues, halfCheckValues];\n    }, [rawMixedLabeledValues]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    rawLabeledValues = _React$useMemo2[0],\n    rawHalfLabeledValues = _React$useMemo2[1]; // const [mergedValues] = useCache(rawLabeledValues);\n\n  var rawValues = React.useMemo(function () {\n    return rawLabeledValues.map(function (item) {\n      return item.value;\n    });\n  }, [rawLabeledValues]); // Convert value to key. Will fill missed keys for conduct check.\n\n  var _useCheckedKeys = useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),\n    _useCheckedKeys2 = _slicedToArray(_useCheckedKeys, 2),\n    rawCheckedValues = _useCheckedKeys2[0],\n    rawHalfCheckedValues = _useCheckedKeys2[1]; // Convert rawCheckedKeys to check strategy related values\n\n  var displayValues = React.useMemo(function () {\n    // Collect keys which need to show\n    var displayKeys = formatStrategyValues(rawCheckedValues, showCheckedStrategy, keyEntities, mergedFieldNames); // Convert to value and filled with label\n\n    var values = displayKeys.map(function (key) {\n      var _keyEntities$key$node, _keyEntities$key, _keyEntities$key$node2;\n      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 ? void 0 : (_keyEntities$key$node2 = _keyEntities$key.node) === null || _keyEntities$key$node2 === void 0 ? void 0 : _keyEntities$key$node2[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;\n    }); // Back fill with origin label\n\n    var labeledValues = values.map(function (val) {\n      var targetItem = rawLabeledValues.find(function (item) {\n        return item.value === val;\n      });\n      return {\n        value: val,\n        label: targetItem === null || targetItem === void 0 ? void 0 : targetItem.label\n      };\n    });\n    var rawDisplayValues = convert2LabelValues(labeledValues);\n    var firstVal = rawDisplayValues[0];\n    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {\n      return [];\n    }\n    return rawDisplayValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, showCheckedStrategy, keyEntities]);\n  var _useCache = useCache(displayValues),\n    _useCache2 = _slicedToArray(_useCache, 1),\n    cachedDisplayValues = _useCache2[0]; // =========================== Change ===========================\n\n  var triggerChange = useRefFunc(function (newRawValues, extra, source) {\n    var labeledValues = convert2LabelValues(newRawValues);\n    setInternalValue(labeledValues); // Clean up if needed\n\n    if (autoClearSearchValue) {\n      setSearchValue('');\n    } // Generate rest parameters is costly, so only do it when necessary\n\n    if (onChange) {\n      var eventValues = newRawValues;\n      if (treeConduction) {\n        var formattedKeyList = formatStrategyValues(newRawValues, showCheckedStrategy, keyEntities, mergedFieldNames);\n        eventValues = formattedKeyList.map(function (key) {\n          var entity = valueEntities.get(key);\n          return entity ? entity.node[mergedFieldNames.value] : key;\n        });\n      }\n      var _ref = extra || {\n          triggerValue: undefined,\n          selected: undefined\n        },\n        triggerValue = _ref.triggerValue,\n        selected = _ref.selected;\n      var returnRawValues = eventValues; // We need fill half check back\n\n      if (treeCheckStrictly) {\n        var halfValues = rawHalfLabeledValues.filter(function (item) {\n          return !eventValues.includes(item.value);\n        });\n        returnRawValues = [].concat(_toConsumableArray(returnRawValues), _toConsumableArray(halfValues));\n      }\n      var returnLabeledValues = convert2LabelValues(returnRawValues);\n      var additionalInfo = {\n        // [Legacy] Always return as array contains label & value\n        preValue: rawLabeledValues,\n        triggerValue: triggerValue\n      }; // [Legacy] Fill legacy data if user query.\n      // This is expansive that we only fill when user query\n      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx\n\n      var showPosition = true;\n      if (treeCheckStrictly || source === 'selection' && !selected) {\n        showPosition = false;\n      }\n      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);\n      if (mergedCheckable) {\n        additionalInfo.checked = selected;\n      } else {\n        additionalInfo.selected = selected;\n      }\n      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {\n        return item.value;\n      });\n      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {\n        return item.label;\n      }), additionalInfo);\n    }\n  }); // ========================== Options ===========================\n\n  /** Trigger by option list */\n\n  var onOptionSelect = React.useCallback(function (selectedKey, _ref2) {\n    var _node$mergedFieldName;\n    var selected = _ref2.selected,\n      source = _ref2.source;\n    var entity = keyEntities[selectedKey];\n    var node = entity === null || entity === void 0 ? void 0 : entity.node;\n    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey; // Never be falsy but keep it safe\n\n    if (!mergedMultiple) {\n      // Single mode always set value\n      triggerChange([selectedValue], {\n        selected: true,\n        triggerValue: selectedValue\n      }, 'option');\n    } else {\n      var newRawValues = selected ? [].concat(_toConsumableArray(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {\n        return v !== selectedValue;\n      }); // Add keys if tree conduction\n\n      if (treeConduction) {\n        // Should keep missing values\n        var _splitRawValues = splitRawValues(newRawValues),\n          missingRawValues = _splitRawValues.missingRawValues,\n          existRawValues = _splitRawValues.existRawValues;\n        var keyList = existRawValues.map(function (val) {\n          return valueEntities.get(val).key;\n        }); // Conduction by selected or not\n\n        var checkedKeys;\n        if (selected) {\n          var _conductCheck = conductCheck(keyList, true, keyEntities);\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(keyList, {\n            checked: false,\n            halfCheckedKeys: rawHalfCheckedValues\n          }, keyEntities);\n          checkedKeys = _conductCheck2.checkedKeys;\n        } // Fill back of keys\n\n        newRawValues = [].concat(_toConsumableArray(missingRawValues), _toConsumableArray(checkedKeys.map(function (key) {\n          return keyEntities[key].node[mergedFieldNames.value];\n        })));\n      }\n      triggerChange(newRawValues, {\n        selected: selected,\n        triggerValue: selectedValue\n      }, source || 'option');\n    } // Trigger select event\n\n    if (selected || !mergedMultiple) {\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedValue, fillLegacyProps(node));\n    } else {\n      onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectedValue, fillLegacyProps(node));\n    }\n  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues]); // ========================== Dropdown ==========================\n\n  var onInternalDropdownVisibleChange = React.useCallback(function (open) {\n    if (onDropdownVisibleChange) {\n      var legacyParam = {};\n      Object.defineProperty(legacyParam, 'documentClickClose', {\n        get: function get() {\n          warning(false, 'Second param of `onDropdownVisibleChange` has been removed.');\n          return false;\n        }\n      });\n      onDropdownVisibleChange(open, legacyParam);\n    }\n  }, [onDropdownVisibleChange]); // ====================== Display Change ========================\n\n  var onDisplayValuesChange = useRefFunc(function (newValues, info) {\n    var newRawValues = newValues.map(function (item) {\n      return item.value;\n    });\n    if (info.type === 'clear') {\n      triggerChange(newRawValues, {}, 'selection');\n      return;\n    } // TreeSelect only have multiple mode which means display change only has remove\n\n    if (info.values.length) {\n      onOptionSelect(info.values[0].value, {\n        selected: false,\n        source: 'selection'\n      });\n    }\n  }); // ========================== Context ===========================\n\n  var treeSelectContext = React.useMemo(function () {\n    return {\n      virtual: virtual,\n      dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      treeData: filteredTreeData,\n      fieldNames: mergedFieldNames,\n      onSelect: onOptionSelect\n    };\n  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, filteredTreeData, mergedFieldNames, onOptionSelect]); // ======================= Legacy Context =======================\n\n  var legacyContext = React.useMemo(function () {\n    return {\n      checkable: mergedCheckable,\n      loadData: loadData,\n      treeLoadedKeys: treeLoadedKeys,\n      onTreeLoad: onTreeLoad,\n      checkedKeys: rawCheckedValues,\n      halfCheckedKeys: rawHalfCheckedValues,\n      treeDefaultExpandAll: treeDefaultExpandAll,\n      treeExpandedKeys: treeExpandedKeys,\n      treeDefaultExpandedKeys: treeDefaultExpandedKeys,\n      onTreeExpand: onTreeExpand,\n      treeIcon: treeIcon,\n      treeMotion: treeMotion,\n      showTreeIcon: showTreeIcon,\n      switcherIcon: switcherIcon,\n      treeLine: treeLine,\n      treeNodeFilterProp: treeNodeFilterProp,\n      keyEntities: keyEntities\n    };\n  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]); // =========================== Render ===========================\n\n  return /*#__PURE__*/React.createElement(TreeSelectContext.Provider, {\n    value: treeSelectContext\n  }, /*#__PURE__*/React.createElement(LegacyContext.Provider, {\n    value: legacyContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({\n    ref: ref\n  }, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    mode: mergedMultiple ? 'multiple' : undefined // >>> Display Value\n    ,\n\n    displayValues: cachedDisplayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch // >>> Options\n    ,\n\n    OptionList: OptionList,\n    emptyOptions: !mergedTreeData.length,\n    onDropdownVisibleChange: onInternalDropdownVisibleChange,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }))));\n}); // Assign name for Debug\n\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\nvar GenericTreeSelect = TreeSelect;\nGenericTreeSelect.TreeNode = TreeNode;\nGenericTreeSelect.SHOW_ALL = SHOW_ALL;\nGenericTreeSelect.SHOW_PARENT = SHOW_PARENT;\nGenericTreeSelect.SHOW_CHILD = SHOW_CHILD;\nexport default GenericTreeSelect;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_typeof", "_excluded", "React", "BaseSelect", "conduct<PERSON>heck", "useId", "useMergedState", "OptionList", "TreeNode", "formatStrategyValues", "SHOW_ALL", "SHOW_PARENT", "SHOW_CHILD", "TreeSelectContext", "LegacyContext", "useTreeData", "toArray", "fillFieldNames", "isNil", "useCache", "useRefFunc", "useDataEntities", "fillAdditionalInfo", "fillLegacyProps", "useCheckedKeys", "useFilterTreeData", "warningProps", "warning", "isRawValue", "value", "TreeSelect", "forwardRef", "props", "ref", "id", "_props$prefixCls", "prefixCls", "defaultValue", "onChange", "onSelect", "onDeselect", "searchValue", "inputValue", "onSearch", "_props$autoClearSearc", "autoClearSearchValue", "filterTreeNode", "_props$treeNodeFilter", "treeNodeFilterProp", "_props$showCheckedStr", "showCheckedStrategy", "treeNodeLabelProp", "multiple", "treeCheckable", "treeCheckStrictly", "labelInValue", "fieldNames", "treeDataSimpleMode", "treeData", "children", "loadData", "treeLoaded<PERSON><PERSON>s", "onTreeLoad", "treeDefaultExpandAll", "treeExpandedKeys", "treeDefaultExpandedKeys", "onTreeExpand", "virtual", "_props$listHeight", "listHeight", "_props$listItemHeight", "listItemHeight", "onDropdownVisibleChange", "_props$dropdownMatchS", "dropdownMatchSelectWidth", "treeLine", "treeIcon", "showTreeIcon", "switcherIcon", "treeMotion", "restProps", "mergedId", "treeConduction", "mergedCheckable", "mergedLabelInValue", "mergedMultiple", "process", "env", "NODE_ENV", "mergedFieldNames", "useMemo", "JSON", "stringify", "_useMergedState", "undefined", "postState", "search", "_useMergedState2", "mergedSearchValue", "setSearchValue", "onInternalSearch", "searchText", "mergedTreeData", "_useDataEntities", "keyEntities", "valueEntities", "splitRawValues", "useCallback", "newRawValues", "missing<PERSON>aw<PERSON><PERSON><PERSON>", "existRawValues", "for<PERSON>ach", "val", "has", "push", "filteredTreeData", "get<PERSON><PERSON><PERSON>", "item", "titleList", "_title", "i", "length", "title", "to<PERSON><PERSON>led<PERSON><PERSON><PERSON>", "draftV<PERSON><PERSON>", "values", "map", "convert2LabelValues", "rawLabel", "label", "rawValue", "rawHalfChecked", "halfChecked", "rawDisabled", "entity", "get", "_raw<PERSON>abel", "node", "disabled", "_useMergedState3", "_useMergedState4", "internalValue", "setInternalValue", "rawMixedLabeledValues", "_React$useMemo", "fullCheckV<PERSON>ues", "half<PERSON><PERSON>ck<PERSON><PERSON><PERSON>", "_React$useMemo2", "rawLabeledValues", "rawHalfLabeledValues", "rawValues", "_useCheckedKeys", "_useCheckedKeys2", "rawCheckedValues", "rawHalfCheckedValues", "displayValues", "displayKeys", "key", "_keyEntities$key$node", "_keyEntities$key", "_keyEntities$key$node2", "labeledV<PERSON>ues", "targetItem", "find", "rawDisplayValues", "firstVal", "_item$label", "_useCache", "_useCache2", "cachedDisplayValues", "trigger<PERSON>hange", "extra", "source", "eventValues", "formattedKeyList", "_ref", "triggerValue", "selected", "returnRawValues", "halfV<PERSON>ues", "filter", "includes", "concat", "return<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalInfo", "preValue", "showPosition", "checked", "returnV<PERSON>ues", "onOptionSelect", "<PERSON><PERSON><PERSON>", "_ref2", "_node$mergedFieldName", "selected<PERSON><PERSON><PERSON>", "v", "_splitRawValues", "keyList", "checked<PERSON>eys", "_conductCheck", "_conductCheck2", "halfC<PERSON>cked<PERSON>eys", "onInternalDropdownVisibleChange", "open", "legacyParam", "Object", "defineProperty", "onDisplayValuesChange", "newValues", "info", "type", "treeSelectContext", "legacyContext", "checkable", "createElement", "Provider", "mode", "emptyOptions", "displayName", "GenericTreeSelect"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tree-select/es/TreeSelect.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"prefixCls\", \"value\", \"defaultValue\", \"onChange\", \"onSelect\", \"onDeselect\", \"searchValue\", \"inputValue\", \"onSearch\", \"autoClearSearchValue\", \"filterTreeNode\", \"treeNodeFilterProp\", \"showCheckedStrategy\", \"treeNodeLabelProp\", \"multiple\", \"treeCheckable\", \"treeCheckStrictly\", \"labelInValue\", \"fieldNames\", \"treeDataSimpleMode\", \"treeData\", \"children\", \"loadData\", \"treeLoadedKeys\", \"onTreeLoad\", \"treeDefaultExpandAll\", \"treeExpandedKeys\", \"treeDefaultExpandedKeys\", \"onTreeExpand\", \"virtual\", \"listHeight\", \"listItemHeight\", \"onDropdownVisibleChange\", \"dropdownMatchSelectWidth\", \"treeLine\", \"treeIcon\", \"showTreeIcon\", \"switcherIcon\", \"treeMotion\"];\nimport * as React from 'react';\nimport { BaseSelect } from 'rc-select';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport useId from \"rc-select/es/hooks/useId\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport OptionList from './OptionList';\nimport TreeNode from './TreeNode';\nimport { formatStrategyValues, SHOW_ALL, SHOW_PARENT, SHOW_CHILD } from './utils/strategyUtil';\nimport TreeSelectContext from './TreeSelectContext';\nimport LegacyContext from './LegacyContext';\nimport useTreeData from './hooks/useTreeData';\nimport { toArray, fillFieldNames, isNil } from './utils/valueUtil';\nimport useCache from './hooks/useCache';\nimport useRefFunc from './hooks/useRefFunc';\nimport useDataEntities from './hooks/useDataEntities';\nimport { fillAdditionalInfo, fillLegacyProps } from './utils/legacyUtil';\nimport useCheckedKeys from './hooks/useCheckedKeys';\nimport useFilterTreeData from './hooks/useFilterTreeData';\nimport warningProps from './utils/warningPropsUtil';\nimport warning from \"rc-util/es/warning\";\n\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\n\nvar TreeSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      onChange = props.onChange,\n      onSelect = props.onSelect,\n      onDeselect = props.onDeselect,\n      searchValue = props.searchValue,\n      inputValue = props.inputValue,\n      onSearch = props.onSearch,\n      _props$autoClearSearc = props.autoClearSearchValue,\n      autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n      filterTreeNode = props.filterTreeNode,\n      _props$treeNodeFilter = props.treeNodeFilterProp,\n      treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,\n      _props$showCheckedStr = props.showCheckedStrategy,\n      showCheckedStrategy = _props$showCheckedStr === void 0 ? SHOW_CHILD : _props$showCheckedStr,\n      treeNodeLabelProp = props.treeNodeLabelProp,\n      multiple = props.multiple,\n      treeCheckable = props.treeCheckable,\n      treeCheckStrictly = props.treeCheckStrictly,\n      labelInValue = props.labelInValue,\n      fieldNames = props.fieldNames,\n      treeDataSimpleMode = props.treeDataSimpleMode,\n      treeData = props.treeData,\n      children = props.children,\n      loadData = props.loadData,\n      treeLoadedKeys = props.treeLoadedKeys,\n      onTreeLoad = props.onTreeLoad,\n      treeDefaultExpandAll = props.treeDefaultExpandAll,\n      treeExpandedKeys = props.treeExpandedKeys,\n      treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,\n      onTreeExpand = props.onTreeExpand,\n      virtual = props.virtual,\n      _props$listHeight = props.listHeight,\n      listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n      _props$listItemHeight = props.listItemHeight,\n      listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n      onDropdownVisibleChange = props.onDropdownVisibleChange,\n      _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n      dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n      treeLine = props.treeLine,\n      treeIcon = props.treeIcon,\n      showTreeIcon = props.showTreeIcon,\n      switcherIcon = props.switcherIcon,\n      treeMotion = props.treeMotion,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var mergedId = useId(id);\n  var treeConduction = treeCheckable && !treeCheckStrictly;\n  var mergedCheckable = treeCheckable || treeCheckStrictly;\n  var mergedLabelInValue = treeCheckStrictly || labelInValue;\n  var mergedMultiple = mergedCheckable || multiple; // ========================== Warning ===========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ========================= FieldNames =========================\n\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames);\n  },\n  /* eslint-disable react-hooks/exhaustive-deps */\n  [JSON.stringify(fieldNames)]); // =========================== Search ===========================\n\n  var _useMergedState = useMergedState('', {\n    value: searchValue !== undefined ? searchValue : inputValue,\n    postState: function postState(search) {\n      return search || '';\n    }\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedSearchValue = _useMergedState2[0],\n      setSearchValue = _useMergedState2[1];\n\n  var onInternalSearch = function onInternalSearch(searchText) {\n    setSearchValue(searchText);\n    onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n  }; // ============================ Data ============================\n  // `useTreeData` only do convert of `children` or `simpleMode`.\n  // Else will return origin `treeData` for perf consideration.\n  // Do not do anything to loop the data.\n\n\n  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);\n\n  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),\n      keyEntities = _useDataEntities.keyEntities,\n      valueEntities = _useDataEntities.valueEntities;\n  /** Get `missingRawValues` which not exist in the tree yet */\n\n\n  var splitRawValues = React.useCallback(function (newRawValues) {\n    var missingRawValues = [];\n    var existRawValues = []; // Keep missing value in the cache\n\n    newRawValues.forEach(function (val) {\n      if (valueEntities.has(val)) {\n        existRawValues.push(val);\n      } else {\n        missingRawValues.push(val);\n      }\n    });\n    return {\n      missingRawValues: missingRawValues,\n      existRawValues: existRawValues\n    };\n  }, [valueEntities]); // Filtered Tree\n\n  var filteredTreeData = useFilterTreeData(mergedTreeData, mergedSearchValue, {\n    fieldNames: mergedFieldNames,\n    treeNodeFilterProp: treeNodeFilterProp,\n    filterTreeNode: filterTreeNode\n  }); // =========================== Label ============================\n\n  var getLabel = React.useCallback(function (item) {\n    if (item) {\n      if (treeNodeLabelProp) {\n        return item[treeNodeLabelProp];\n      } // Loop from fieldNames\n\n\n      var titleList = mergedFieldNames._title;\n\n      for (var i = 0; i < titleList.length; i += 1) {\n        var title = item[titleList[i]];\n\n        if (title !== undefined) {\n          return title;\n        }\n      }\n    }\n  }, [mergedFieldNames, treeNodeLabelProp]); // ========================= Wrap Value =========================\n\n  var toLabeledValues = React.useCallback(function (draftValues) {\n    var values = toArray(draftValues);\n    return values.map(function (val) {\n      if (isRawValue(val)) {\n        return {\n          value: val\n        };\n      }\n\n      return val;\n    });\n  }, []);\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    var values = toLabeledValues(draftValues);\n    return values.map(function (item) {\n      var rawLabel = item.label;\n      var rawValue = item.value,\n          rawHalfChecked = item.halfChecked;\n      var rawDisabled;\n      var entity = valueEntities.get(rawValue); // Fill missing label & status\n\n      if (entity) {\n        var _rawLabel;\n\n        rawLabel = (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);\n        rawDisabled = entity.node.disabled;\n      }\n\n      return {\n        label: rawLabel,\n        value: rawValue,\n        halfChecked: rawHalfChecked,\n        disabled: rawDisabled\n      };\n    });\n  }, [valueEntities, getLabel, toLabeledValues]); // =========================== Values ===========================\n\n  var _useMergedState3 = useMergedState(defaultValue, {\n    value: value\n  }),\n      _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n      internalValue = _useMergedState4[0],\n      setInternalValue = _useMergedState4[1];\n\n  var rawMixedLabeledValues = React.useMemo(function () {\n    return toLabeledValues(internalValue);\n  }, [toLabeledValues, internalValue]); // Split value into full check and half check\n\n  var _React$useMemo = React.useMemo(function () {\n    var fullCheckValues = [];\n    var halfCheckValues = [];\n    rawMixedLabeledValues.forEach(function (item) {\n      if (item.halfChecked) {\n        halfCheckValues.push(item);\n      } else {\n        fullCheckValues.push(item);\n      }\n    });\n    return [fullCheckValues, halfCheckValues];\n  }, [rawMixedLabeledValues]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      rawLabeledValues = _React$useMemo2[0],\n      rawHalfLabeledValues = _React$useMemo2[1]; // const [mergedValues] = useCache(rawLabeledValues);\n\n\n  var rawValues = React.useMemo(function () {\n    return rawLabeledValues.map(function (item) {\n      return item.value;\n    });\n  }, [rawLabeledValues]); // Convert value to key. Will fill missed keys for conduct check.\n\n  var _useCheckedKeys = useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),\n      _useCheckedKeys2 = _slicedToArray(_useCheckedKeys, 2),\n      rawCheckedValues = _useCheckedKeys2[0],\n      rawHalfCheckedValues = _useCheckedKeys2[1]; // Convert rawCheckedKeys to check strategy related values\n\n\n  var displayValues = React.useMemo(function () {\n    // Collect keys which need to show\n    var displayKeys = formatStrategyValues(rawCheckedValues, showCheckedStrategy, keyEntities, mergedFieldNames); // Convert to value and filled with label\n\n    var values = displayKeys.map(function (key) {\n      var _keyEntities$key$node, _keyEntities$key, _keyEntities$key$node2;\n\n      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 ? void 0 : (_keyEntities$key$node2 = _keyEntities$key.node) === null || _keyEntities$key$node2 === void 0 ? void 0 : _keyEntities$key$node2[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;\n    }); // Back fill with origin label\n\n    var labeledValues = values.map(function (val) {\n      var targetItem = rawLabeledValues.find(function (item) {\n        return item.value === val;\n      });\n      return {\n        value: val,\n        label: targetItem === null || targetItem === void 0 ? void 0 : targetItem.label\n      };\n    });\n    var rawDisplayValues = convert2LabelValues(labeledValues);\n    var firstVal = rawDisplayValues[0];\n\n    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {\n      return [];\n    }\n\n    return rawDisplayValues.map(function (item) {\n      var _item$label;\n\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, showCheckedStrategy, keyEntities]);\n\n  var _useCache = useCache(displayValues),\n      _useCache2 = _slicedToArray(_useCache, 1),\n      cachedDisplayValues = _useCache2[0]; // =========================== Change ===========================\n\n\n  var triggerChange = useRefFunc(function (newRawValues, extra, source) {\n    var labeledValues = convert2LabelValues(newRawValues);\n    setInternalValue(labeledValues); // Clean up if needed\n\n    if (autoClearSearchValue) {\n      setSearchValue('');\n    } // Generate rest parameters is costly, so only do it when necessary\n\n\n    if (onChange) {\n      var eventValues = newRawValues;\n\n      if (treeConduction) {\n        var formattedKeyList = formatStrategyValues(newRawValues, showCheckedStrategy, keyEntities, mergedFieldNames);\n        eventValues = formattedKeyList.map(function (key) {\n          var entity = valueEntities.get(key);\n          return entity ? entity.node[mergedFieldNames.value] : key;\n        });\n      }\n\n      var _ref = extra || {\n        triggerValue: undefined,\n        selected: undefined\n      },\n          triggerValue = _ref.triggerValue,\n          selected = _ref.selected;\n\n      var returnRawValues = eventValues; // We need fill half check back\n\n      if (treeCheckStrictly) {\n        var halfValues = rawHalfLabeledValues.filter(function (item) {\n          return !eventValues.includes(item.value);\n        });\n        returnRawValues = [].concat(_toConsumableArray(returnRawValues), _toConsumableArray(halfValues));\n      }\n\n      var returnLabeledValues = convert2LabelValues(returnRawValues);\n      var additionalInfo = {\n        // [Legacy] Always return as array contains label & value\n        preValue: rawLabeledValues,\n        triggerValue: triggerValue\n      }; // [Legacy] Fill legacy data if user query.\n      // This is expansive that we only fill when user query\n      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx\n\n      var showPosition = true;\n\n      if (treeCheckStrictly || source === 'selection' && !selected) {\n        showPosition = false;\n      }\n\n      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);\n\n      if (mergedCheckable) {\n        additionalInfo.checked = selected;\n      } else {\n        additionalInfo.selected = selected;\n      }\n\n      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {\n        return item.value;\n      });\n      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {\n        return item.label;\n      }), additionalInfo);\n    }\n  }); // ========================== Options ===========================\n\n  /** Trigger by option list */\n\n  var onOptionSelect = React.useCallback(function (selectedKey, _ref2) {\n    var _node$mergedFieldName;\n\n    var selected = _ref2.selected,\n        source = _ref2.source;\n    var entity = keyEntities[selectedKey];\n    var node = entity === null || entity === void 0 ? void 0 : entity.node;\n    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey; // Never be falsy but keep it safe\n\n    if (!mergedMultiple) {\n      // Single mode always set value\n      triggerChange([selectedValue], {\n        selected: true,\n        triggerValue: selectedValue\n      }, 'option');\n    } else {\n      var newRawValues = selected ? [].concat(_toConsumableArray(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {\n        return v !== selectedValue;\n      }); // Add keys if tree conduction\n\n      if (treeConduction) {\n        // Should keep missing values\n        var _splitRawValues = splitRawValues(newRawValues),\n            missingRawValues = _splitRawValues.missingRawValues,\n            existRawValues = _splitRawValues.existRawValues;\n\n        var keyList = existRawValues.map(function (val) {\n          return valueEntities.get(val).key;\n        }); // Conduction by selected or not\n\n        var checkedKeys;\n\n        if (selected) {\n          var _conductCheck = conductCheck(keyList, true, keyEntities);\n\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(keyList, {\n            checked: false,\n            halfCheckedKeys: rawHalfCheckedValues\n          }, keyEntities);\n\n          checkedKeys = _conductCheck2.checkedKeys;\n        } // Fill back of keys\n\n\n        newRawValues = [].concat(_toConsumableArray(missingRawValues), _toConsumableArray(checkedKeys.map(function (key) {\n          return keyEntities[key].node[mergedFieldNames.value];\n        })));\n      }\n\n      triggerChange(newRawValues, {\n        selected: selected,\n        triggerValue: selectedValue\n      }, source || 'option');\n    } // Trigger select event\n\n\n    if (selected || !mergedMultiple) {\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedValue, fillLegacyProps(node));\n    } else {\n      onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectedValue, fillLegacyProps(node));\n    }\n  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues]); // ========================== Dropdown ==========================\n\n  var onInternalDropdownVisibleChange = React.useCallback(function (open) {\n    if (onDropdownVisibleChange) {\n      var legacyParam = {};\n      Object.defineProperty(legacyParam, 'documentClickClose', {\n        get: function get() {\n          warning(false, 'Second param of `onDropdownVisibleChange` has been removed.');\n          return false;\n        }\n      });\n      onDropdownVisibleChange(open, legacyParam);\n    }\n  }, [onDropdownVisibleChange]); // ====================== Display Change ========================\n\n  var onDisplayValuesChange = useRefFunc(function (newValues, info) {\n    var newRawValues = newValues.map(function (item) {\n      return item.value;\n    });\n\n    if (info.type === 'clear') {\n      triggerChange(newRawValues, {}, 'selection');\n      return;\n    } // TreeSelect only have multiple mode which means display change only has remove\n\n\n    if (info.values.length) {\n      onOptionSelect(info.values[0].value, {\n        selected: false,\n        source: 'selection'\n      });\n    }\n  }); // ========================== Context ===========================\n\n  var treeSelectContext = React.useMemo(function () {\n    return {\n      virtual: virtual,\n      dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      treeData: filteredTreeData,\n      fieldNames: mergedFieldNames,\n      onSelect: onOptionSelect\n    };\n  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, filteredTreeData, mergedFieldNames, onOptionSelect]); // ======================= Legacy Context =======================\n\n  var legacyContext = React.useMemo(function () {\n    return {\n      checkable: mergedCheckable,\n      loadData: loadData,\n      treeLoadedKeys: treeLoadedKeys,\n      onTreeLoad: onTreeLoad,\n      checkedKeys: rawCheckedValues,\n      halfCheckedKeys: rawHalfCheckedValues,\n      treeDefaultExpandAll: treeDefaultExpandAll,\n      treeExpandedKeys: treeExpandedKeys,\n      treeDefaultExpandedKeys: treeDefaultExpandedKeys,\n      onTreeExpand: onTreeExpand,\n      treeIcon: treeIcon,\n      treeMotion: treeMotion,\n      showTreeIcon: showTreeIcon,\n      switcherIcon: switcherIcon,\n      treeLine: treeLine,\n      treeNodeFilterProp: treeNodeFilterProp,\n      keyEntities: keyEntities\n    };\n  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]); // =========================== Render ===========================\n\n  return /*#__PURE__*/React.createElement(TreeSelectContext.Provider, {\n    value: treeSelectContext\n  }, /*#__PURE__*/React.createElement(LegacyContext.Provider, {\n    value: legacyContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({\n    ref: ref\n  }, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    mode: mergedMultiple ? 'multiple' : undefined // >>> Display Value\n    ,\n    displayValues: cachedDisplayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch // >>> Options\n    ,\n    OptionList: OptionList,\n    emptyOptions: !mergedTreeData.length,\n    onDropdownVisibleChange: onInternalDropdownVisibleChange,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }))));\n}); // Assign name for Debug\n\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\n\nvar GenericTreeSelect = TreeSelect;\nGenericTreeSelect.TreeNode = TreeNode;\nGenericTreeSelect.SHOW_ALL = SHOW_ALL;\nGenericTreeSelect.SHOW_PARENT = SHOW_PARENT;\nGenericTreeSelect.SHOW_CHILD = SHOW_CHILD;\nexport default GenericTreeSelect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC;AAChqB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,oBAAoB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC9F,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,OAAO,EAAEC,cAAc,EAAEC,KAAK,QAAQ,mBAAmB;AAClE,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,oBAAoB;AACxE,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,OAAO,MAAM,oBAAoB;AAExC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAACA,KAAK,IAAI7B,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ;AAC9C;AAEA,IAAIC,UAAU,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACbC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,gBAAgB,GAAGA,gBAAgB;IAC7EN,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBQ,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,qBAAqB,GAAGZ,KAAK,CAACa,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACtFE,cAAc,GAAGd,KAAK,CAACc,cAAc;IACrCC,qBAAqB,GAAGf,KAAK,CAACgB,kBAAkB;IAChDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;IACvFE,qBAAqB,GAAGjB,KAAK,CAACkB,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrC,UAAU,GAAGqC,qBAAqB;IAC3FE,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,iBAAiB,GAAGtB,KAAK,CAACsB,iBAAiB;IAC3CC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,UAAU,GAAGxB,KAAK,CAACwB,UAAU;IAC7BC,kBAAkB,GAAGzB,KAAK,CAACyB,kBAAkB;IAC7CC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IACzBC,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;IACzBC,QAAQ,GAAG5B,KAAK,CAAC4B,QAAQ;IACzBC,cAAc,GAAG7B,KAAK,CAAC6B,cAAc;IACrCC,UAAU,GAAG9B,KAAK,CAAC8B,UAAU;IAC7BC,oBAAoB,GAAG/B,KAAK,CAAC+B,oBAAoB;IACjDC,gBAAgB,GAAGhC,KAAK,CAACgC,gBAAgB;IACzCC,uBAAuB,GAAGjC,KAAK,CAACiC,uBAAuB;IACvDC,YAAY,GAAGlC,KAAK,CAACkC,YAAY;IACjCC,OAAO,GAAGnC,KAAK,CAACmC,OAAO;IACvBC,iBAAiB,GAAGpC,KAAK,CAACqC,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,qBAAqB,GAAGtC,KAAK,CAACuC,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9EE,uBAAuB,GAAGxC,KAAK,CAACwC,uBAAuB;IACvDC,qBAAqB,GAAGzC,KAAK,CAAC0C,wBAAwB;IACtDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FE,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ;IACzBC,QAAQ,GAAG5C,KAAK,CAAC4C,QAAQ;IACzBC,YAAY,GAAG7C,KAAK,CAAC6C,YAAY;IACjCC,YAAY,GAAG9C,KAAK,CAAC8C,YAAY;IACjCC,UAAU,GAAG/C,KAAK,CAAC+C,UAAU;IAC7BC,SAAS,GAAGjF,wBAAwB,CAACiC,KAAK,EAAE/B,SAAS,CAAC;EAE1D,IAAIgF,QAAQ,GAAG5E,KAAK,CAAC6B,EAAE,CAAC;EACxB,IAAIgD,cAAc,GAAG7B,aAAa,IAAI,CAACC,iBAAiB;EACxD,IAAI6B,eAAe,GAAG9B,aAAa,IAAIC,iBAAiB;EACxD,IAAI8B,kBAAkB,GAAG9B,iBAAiB,IAAIC,YAAY;EAC1D,IAAI8B,cAAc,GAAGF,eAAe,IAAI/B,QAAQ,CAAC,CAAC;;EAElD,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC9D,YAAY,CAACM,KAAK,CAAC;EACrB,CAAC,CAAC;;EAGF,IAAIyD,gBAAgB,GAAGvF,KAAK,CAACwF,OAAO,CAAC,YAAY;IAC/C,OAAOzE,cAAc,CAACuC,UAAU,CAAC;EACnC,CAAC,EACD;EACA,CAACmC,IAAI,CAACC,SAAS,CAACpC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIqC,eAAe,GAAGvF,cAAc,CAAC,EAAE,EAAE;MACvCuB,KAAK,EAAEY,WAAW,KAAKqD,SAAS,GAAGrD,WAAW,GAAGC,UAAU;MAC3DqD,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;QACpC,OAAOA,MAAM,IAAI,EAAE;MACrB;IACF,CAAC,CAAC;IACEC,gBAAgB,GAAGnG,cAAc,CAAC+F,eAAe,EAAE,CAAC,CAAC;IACrDK,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;IAC3DF,cAAc,CAACE,UAAU,CAAC;IAC1B1D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC0D,UAAU,CAAC;EAC1E,CAAC,CAAC,CAAC;EACH;EACA;EACA;;EAGA,IAAIC,cAAc,GAAGvF,WAAW,CAAC2C,QAAQ,EAAEC,QAAQ,EAAEF,kBAAkB,CAAC;EAExE,IAAI8C,gBAAgB,GAAGlF,eAAe,CAACiF,cAAc,EAAEb,gBAAgB,CAAC;IACpEe,WAAW,GAAGD,gBAAgB,CAACC,WAAW;IAC1CC,aAAa,GAAGF,gBAAgB,CAACE,aAAa;EAClD;;EAGA,IAAIC,cAAc,GAAGxG,KAAK,CAACyG,WAAW,CAAC,UAAUC,YAAY,EAAE;IAC7D,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,cAAc,GAAG,EAAE,CAAC,CAAC;;IAEzBF,YAAY,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIP,aAAa,CAACQ,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BF,cAAc,CAACI,IAAI,CAACF,GAAG,CAAC;MAC1B,CAAC,MAAM;QACLH,gBAAgB,CAACK,IAAI,CAACF,GAAG,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,OAAO;MACLH,gBAAgB,EAAEA,gBAAgB;MAClCC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB,IAAIU,gBAAgB,GAAG1F,iBAAiB,CAAC6E,cAAc,EAAEJ,iBAAiB,EAAE;IAC1E1C,UAAU,EAAEiC,gBAAgB;IAC5BzC,kBAAkB,EAAEA,kBAAkB;IACtCF,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIsE,QAAQ,GAAGlH,KAAK,CAACyG,WAAW,CAAC,UAAUU,IAAI,EAAE;IAC/C,IAAIA,IAAI,EAAE;MACR,IAAIlE,iBAAiB,EAAE;QACrB,OAAOkE,IAAI,CAAClE,iBAAiB,CAAC;MAChC,CAAC,CAAC;;MAGF,IAAImE,SAAS,GAAG7B,gBAAgB,CAAC8B,MAAM;MAEvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAIE,KAAK,GAAGL,IAAI,CAACC,SAAS,CAACE,CAAC,CAAC,CAAC;QAE9B,IAAIE,KAAK,KAAK5B,SAAS,EAAE;UACvB,OAAO4B,KAAK;QACd;MACF;IACF;EACF,CAAC,EAAE,CAACjC,gBAAgB,EAAEtC,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE3C,IAAIwE,eAAe,GAAGzH,KAAK,CAACyG,WAAW,CAAC,UAAUiB,WAAW,EAAE;IAC7D,IAAIC,MAAM,GAAG7G,OAAO,CAAC4G,WAAW,CAAC;IACjC,OAAOC,MAAM,CAACC,GAAG,CAAC,UAAUd,GAAG,EAAE;MAC/B,IAAIpF,UAAU,CAACoF,GAAG,CAAC,EAAE;QACnB,OAAO;UACLnF,KAAK,EAAEmF;QACT,CAAC;MACH;MAEA,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIe,mBAAmB,GAAG7H,KAAK,CAACyG,WAAW,CAAC,UAAUiB,WAAW,EAAE;IACjE,IAAIC,MAAM,GAAGF,eAAe,CAACC,WAAW,CAAC;IACzC,OAAOC,MAAM,CAACC,GAAG,CAAC,UAAUT,IAAI,EAAE;MAChC,IAAIW,QAAQ,GAAGX,IAAI,CAACY,KAAK;MACzB,IAAIC,QAAQ,GAAGb,IAAI,CAACxF,KAAK;QACrBsG,cAAc,GAAGd,IAAI,CAACe,WAAW;MACrC,IAAIC,WAAW;MACf,IAAIC,MAAM,GAAG7B,aAAa,CAAC8B,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC;;MAE1C,IAAII,MAAM,EAAE;QACV,IAAIE,SAAS;QAEbR,QAAQ,GAAG,CAACQ,SAAS,GAAGR,QAAQ,MAAM,IAAI,IAAIQ,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGpB,QAAQ,CAACkB,MAAM,CAACG,IAAI,CAAC;QACtGJ,WAAW,GAAGC,MAAM,CAACG,IAAI,CAACC,QAAQ;MACpC;MAEA,OAAO;QACLT,KAAK,EAAED,QAAQ;QACfnG,KAAK,EAAEqG,QAAQ;QACfE,WAAW,EAAED,cAAc;QAC3BO,QAAQ,EAAEL;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5B,aAAa,EAAEW,QAAQ,EAAEO,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAIgB,gBAAgB,GAAGrI,cAAc,CAAC+B,YAAY,EAAE;MAClDR,KAAK,EAAEA;IACT,CAAC,CAAC;IACE+G,gBAAgB,GAAG9I,cAAc,CAAC6I,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE1C,IAAIG,qBAAqB,GAAG7I,KAAK,CAACwF,OAAO,CAAC,YAAY;IACpD,OAAOiC,eAAe,CAACkB,aAAa,CAAC;EACvC,CAAC,EAAE,CAAClB,eAAe,EAAEkB,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEtC,IAAIG,cAAc,GAAG9I,KAAK,CAACwF,OAAO,CAAC,YAAY;MAC7C,IAAIuD,eAAe,GAAG,EAAE;MACxB,IAAIC,eAAe,GAAG,EAAE;MACxBH,qBAAqB,CAAChC,OAAO,CAAC,UAAUM,IAAI,EAAE;QAC5C,IAAIA,IAAI,CAACe,WAAW,EAAE;UACpBc,eAAe,CAAChC,IAAI,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACL4B,eAAe,CAAC/B,IAAI,CAACG,IAAI,CAAC;QAC5B;MACF,CAAC,CAAC;MACF,OAAO,CAAC4B,eAAe,EAAEC,eAAe,CAAC;IAC3C,CAAC,EAAE,CAACH,qBAAqB,CAAC,CAAC;IACvBI,eAAe,GAAGrJ,cAAc,CAACkJ,cAAc,EAAE,CAAC,CAAC;IACnDI,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACrCE,oBAAoB,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/C,IAAIG,SAAS,GAAGpJ,KAAK,CAACwF,OAAO,CAAC,YAAY;IACxC,OAAO0D,gBAAgB,CAACtB,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC1C,OAAOA,IAAI,CAACxF,KAAK;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACuH,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAExB,IAAIG,eAAe,GAAG/H,cAAc,CAAC4H,gBAAgB,EAAEC,oBAAoB,EAAEnE,cAAc,EAAEsB,WAAW,CAAC;IACrGgD,gBAAgB,GAAG1J,cAAc,CAACyJ,eAAe,EAAE,CAAC,CAAC;IACrDE,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGhD,IAAIG,aAAa,GAAGzJ,KAAK,CAACwF,OAAO,CAAC,YAAY;IAC5C;IACA,IAAIkE,WAAW,GAAGnJ,oBAAoB,CAACgJ,gBAAgB,EAAEvG,mBAAmB,EAAEsD,WAAW,EAAEf,gBAAgB,CAAC,CAAC,CAAC;;IAE9G,IAAIoC,MAAM,GAAG+B,WAAW,CAAC9B,GAAG,CAAC,UAAU+B,GAAG,EAAE;MAC1C,IAAIC,qBAAqB,EAAEC,gBAAgB,EAAEC,sBAAsB;MAEnE,OAAO,CAACF,qBAAqB,GAAG,CAACC,gBAAgB,GAAGvD,WAAW,CAACqD,GAAG,CAAC,MAAM,IAAI,IAAIE,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,gBAAgB,CAACtB,IAAI,MAAM,IAAI,IAAIuB,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACvE,gBAAgB,CAAC5D,KAAK,CAAC,MAAM,IAAI,IAAIiI,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,GAAG;IAC/V,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAII,aAAa,GAAGpC,MAAM,CAACC,GAAG,CAAC,UAAUd,GAAG,EAAE;MAC5C,IAAIkD,UAAU,GAAGd,gBAAgB,CAACe,IAAI,CAAC,UAAU9C,IAAI,EAAE;QACrD,OAAOA,IAAI,CAACxF,KAAK,KAAKmF,GAAG;MAC3B,CAAC,CAAC;MACF,OAAO;QACLnF,KAAK,EAAEmF,GAAG;QACViB,KAAK,EAAEiC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACjC;MAC5E,CAAC;IACH,CAAC,CAAC;IACF,IAAImC,gBAAgB,GAAGrC,mBAAmB,CAACkC,aAAa,CAAC;IACzD,IAAII,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAElC,IAAI,CAAC/E,cAAc,IAAIgF,QAAQ,IAAInJ,KAAK,CAACmJ,QAAQ,CAACxI,KAAK,CAAC,IAAIX,KAAK,CAACmJ,QAAQ,CAACpC,KAAK,CAAC,EAAE;MACjF,OAAO,EAAE;IACX;IAEA,OAAOmC,gBAAgB,CAACtC,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC1C,IAAIiD,WAAW;MAEf,OAAOzK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwH,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDY,KAAK,EAAE,CAACqC,WAAW,GAAGjD,IAAI,CAACY,KAAK,MAAM,IAAI,IAAIqC,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGjD,IAAI,CAACxF;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC4D,gBAAgB,EAAEJ,cAAc,EAAEoE,gBAAgB,EAAEL,gBAAgB,EAAErB,mBAAmB,EAAE7E,mBAAmB,EAAEsD,WAAW,CAAC,CAAC;EAEjI,IAAI+D,SAAS,GAAGpJ,QAAQ,CAACwI,aAAa,CAAC;IACnCa,UAAU,GAAG1K,cAAc,CAACyK,SAAS,EAAE,CAAC,CAAC;IACzCE,mBAAmB,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzC,IAAIE,aAAa,GAAGtJ,UAAU,CAAC,UAAUwF,YAAY,EAAE+D,KAAK,EAAEC,MAAM,EAAE;IACpE,IAAIX,aAAa,GAAGlC,mBAAmB,CAACnB,YAAY,CAAC;IACrDkC,gBAAgB,CAACmB,aAAa,CAAC,CAAC,CAAC;;IAEjC,IAAIpH,oBAAoB,EAAE;MACxBsD,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;;IAGF,IAAI7D,QAAQ,EAAE;MACZ,IAAIuI,WAAW,GAAGjE,YAAY;MAE9B,IAAI1B,cAAc,EAAE;QAClB,IAAI4F,gBAAgB,GAAGrK,oBAAoB,CAACmG,YAAY,EAAE1D,mBAAmB,EAAEsD,WAAW,EAAEf,gBAAgB,CAAC;QAC7GoF,WAAW,GAAGC,gBAAgB,CAAChD,GAAG,CAAC,UAAU+B,GAAG,EAAE;UAChD,IAAIvB,MAAM,GAAG7B,aAAa,CAAC8B,GAAG,CAACsB,GAAG,CAAC;UACnC,OAAOvB,MAAM,GAAGA,MAAM,CAACG,IAAI,CAAChD,gBAAgB,CAAC5D,KAAK,CAAC,GAAGgI,GAAG;QAC3D,CAAC,CAAC;MACJ;MAEA,IAAIkB,IAAI,GAAGJ,KAAK,IAAI;UAClBK,YAAY,EAAElF,SAAS;UACvBmF,QAAQ,EAAEnF;QACZ,CAAC;QACGkF,YAAY,GAAGD,IAAI,CAACC,YAAY;QAChCC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MAE5B,IAAIC,eAAe,GAAGL,WAAW,CAAC,CAAC;;MAEnC,IAAIvH,iBAAiB,EAAE;QACrB,IAAI6H,UAAU,GAAG9B,oBAAoB,CAAC+B,MAAM,CAAC,UAAU/D,IAAI,EAAE;UAC3D,OAAO,CAACwD,WAAW,CAACQ,QAAQ,CAAChE,IAAI,CAACxF,KAAK,CAAC;QAC1C,CAAC,CAAC;QACFqJ,eAAe,GAAG,EAAE,CAACI,MAAM,CAAC1L,kBAAkB,CAACsL,eAAe,CAAC,EAAEtL,kBAAkB,CAACuL,UAAU,CAAC,CAAC;MAClG;MAEA,IAAII,mBAAmB,GAAGxD,mBAAmB,CAACmD,eAAe,CAAC;MAC9D,IAAIM,cAAc,GAAG;QACnB;QACAC,QAAQ,EAAErC,gBAAgB;QAC1B4B,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC;MACH;MACA;;MAEA,IAAIU,YAAY,GAAG,IAAI;MAEvB,IAAIpI,iBAAiB,IAAIsH,MAAM,KAAK,WAAW,IAAI,CAACK,QAAQ,EAAE;QAC5DS,YAAY,GAAG,KAAK;MACtB;MAEApK,kBAAkB,CAACkK,cAAc,EAAER,YAAY,EAAEpE,YAAY,EAAEN,cAAc,EAAEoF,YAAY,EAAEjG,gBAAgB,CAAC;MAE9G,IAAIN,eAAe,EAAE;QACnBqG,cAAc,CAACG,OAAO,GAAGV,QAAQ;MACnC,CAAC,MAAM;QACLO,cAAc,CAACP,QAAQ,GAAGA,QAAQ;MACpC;MAEA,IAAIW,YAAY,GAAGxG,kBAAkB,GAAGmG,mBAAmB,GAAGA,mBAAmB,CAACzD,GAAG,CAAC,UAAUT,IAAI,EAAE;QACpG,OAAOA,IAAI,CAACxF,KAAK;MACnB,CAAC,CAAC;MACFS,QAAQ,CAAC+C,cAAc,GAAGuG,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,EAAExG,kBAAkB,GAAG,IAAI,GAAGmG,mBAAmB,CAACzD,GAAG,CAAC,UAAUT,IAAI,EAAE;QAC5H,OAAOA,IAAI,CAACY,KAAK;MACnB,CAAC,CAAC,EAAEuD,cAAc,CAAC;IACrB;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ;;EAEA,IAAIK,cAAc,GAAG3L,KAAK,CAACyG,WAAW,CAAC,UAAUmF,WAAW,EAAEC,KAAK,EAAE;IACnE,IAAIC,qBAAqB;IAEzB,IAAIf,QAAQ,GAAGc,KAAK,CAACd,QAAQ;MACzBL,MAAM,GAAGmB,KAAK,CAACnB,MAAM;IACzB,IAAItC,MAAM,GAAG9B,WAAW,CAACsF,WAAW,CAAC;IACrC,IAAIrD,IAAI,GAAGH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,IAAI;IACtE,IAAIwD,aAAa,GAAG,CAACD,qBAAqB,GAAGvD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAChD,gBAAgB,CAAC5D,KAAK,CAAC,MAAM,IAAI,IAAImK,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGF,WAAW,CAAC,CAAC;;IAE3M,IAAI,CAACzG,cAAc,EAAE;MACnB;MACAqF,aAAa,CAAC,CAACuB,aAAa,CAAC,EAAE;QAC7BhB,QAAQ,EAAE,IAAI;QACdD,YAAY,EAAEiB;MAChB,CAAC,EAAE,QAAQ,CAAC;IACd,CAAC,MAAM;MACL,IAAIrF,YAAY,GAAGqE,QAAQ,GAAG,EAAE,CAACK,MAAM,CAAC1L,kBAAkB,CAAC0J,SAAS,CAAC,EAAE,CAAC2C,aAAa,CAAC,CAAC,GAAGxC,gBAAgB,CAAC2B,MAAM,CAAC,UAAUc,CAAC,EAAE;QAC7H,OAAOA,CAAC,KAAKD,aAAa;MAC5B,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAI/G,cAAc,EAAE;QAClB;QACA,IAAIiH,eAAe,GAAGzF,cAAc,CAACE,YAAY,CAAC;UAC9CC,gBAAgB,GAAGsF,eAAe,CAACtF,gBAAgB;UACnDC,cAAc,GAAGqF,eAAe,CAACrF,cAAc;QAEnD,IAAIsF,OAAO,GAAGtF,cAAc,CAACgB,GAAG,CAAC,UAAUd,GAAG,EAAE;UAC9C,OAAOP,aAAa,CAAC8B,GAAG,CAACvB,GAAG,CAAC,CAAC6C,GAAG;QACnC,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIwC,WAAW;QAEf,IAAIpB,QAAQ,EAAE;UACZ,IAAIqB,aAAa,GAAGlM,YAAY,CAACgM,OAAO,EAAE,IAAI,EAAE5F,WAAW,CAAC;UAE5D6F,WAAW,GAAGC,aAAa,CAACD,WAAW;QACzC,CAAC,MAAM;UACL,IAAIE,cAAc,GAAGnM,YAAY,CAACgM,OAAO,EAAE;YACzCT,OAAO,EAAE,KAAK;YACda,eAAe,EAAE9C;UACnB,CAAC,EAAElD,WAAW,CAAC;UAEf6F,WAAW,GAAGE,cAAc,CAACF,WAAW;QAC1C,CAAC,CAAC;;QAGFzF,YAAY,GAAG,EAAE,CAAC0E,MAAM,CAAC1L,kBAAkB,CAACiH,gBAAgB,CAAC,EAAEjH,kBAAkB,CAACyM,WAAW,CAACvE,GAAG,CAAC,UAAU+B,GAAG,EAAE;UAC/G,OAAOrD,WAAW,CAACqD,GAAG,CAAC,CAACpB,IAAI,CAAChD,gBAAgB,CAAC5D,KAAK,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC;MACN;MAEA6I,aAAa,CAAC9D,YAAY,EAAE;QAC1BqE,QAAQ,EAAEA,QAAQ;QAClBD,YAAY,EAAEiB;MAChB,CAAC,EAAErB,MAAM,IAAI,QAAQ,CAAC;IACxB,CAAC,CAAC;;IAGF,IAAIK,QAAQ,IAAI,CAAC5F,cAAc,EAAE;MAC/B9C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC0J,aAAa,EAAE1K,eAAe,CAACkH,IAAI,CAAC,CAAC;IACpG,CAAC,MAAM;MACLjG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACyJ,aAAa,EAAE1K,eAAe,CAACkH,IAAI,CAAC,CAAC;IAC1G;EACF,CAAC,EAAE,CAAC/B,cAAc,EAAED,aAAa,EAAED,WAAW,EAAEf,gBAAgB,EAAEJ,cAAc,EAAEiE,SAAS,EAAEoB,aAAa,EAAExF,cAAc,EAAE3C,QAAQ,EAAEC,UAAU,EAAEiH,gBAAgB,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAE5L,IAAI+C,+BAA+B,GAAGvM,KAAK,CAACyG,WAAW,CAAC,UAAU+F,IAAI,EAAE;IACtE,IAAIlI,uBAAuB,EAAE;MAC3B,IAAImI,WAAW,GAAG,CAAC,CAAC;MACpBC,MAAM,CAACC,cAAc,CAACF,WAAW,EAAE,oBAAoB,EAAE;QACvDpE,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;UAClB5G,OAAO,CAAC,KAAK,EAAE,6DAA6D,CAAC;UAC7E,OAAO,KAAK;QACd;MACF,CAAC,CAAC;MACF6C,uBAAuB,CAACkI,IAAI,EAAEC,WAAW,CAAC;IAC5C;EACF,CAAC,EAAE,CAACnI,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIsI,qBAAqB,GAAG1L,UAAU,CAAC,UAAU2L,SAAS,EAAEC,IAAI,EAAE;IAChE,IAAIpG,YAAY,GAAGmG,SAAS,CAACjF,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAACxF,KAAK;IACnB,CAAC,CAAC;IAEF,IAAImL,IAAI,CAACC,IAAI,KAAK,OAAO,EAAE;MACzBvC,aAAa,CAAC9D,YAAY,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC;MAC5C;IACF,CAAC,CAAC;;IAGF,IAAIoG,IAAI,CAACnF,MAAM,CAACJ,MAAM,EAAE;MACtBoE,cAAc,CAACmB,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC,CAAChG,KAAK,EAAE;QACnCoJ,QAAQ,EAAE,KAAK;QACfL,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIsC,iBAAiB,GAAGhN,KAAK,CAACwF,OAAO,CAAC,YAAY;IAChD,OAAO;MACLvB,OAAO,EAAEA,OAAO;MAChBO,wBAAwB,EAAEA,wBAAwB;MAClDL,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9Bb,QAAQ,EAAEyD,gBAAgB;MAC1B3D,UAAU,EAAEiC,gBAAgB;MAC5BlD,QAAQ,EAAEsJ;IACZ,CAAC;EACH,CAAC,EAAE,CAAC1H,OAAO,EAAEO,wBAAwB,EAAEL,UAAU,EAAEE,cAAc,EAAE4C,gBAAgB,EAAE1B,gBAAgB,EAAEoG,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEzH,IAAIsB,aAAa,GAAGjN,KAAK,CAACwF,OAAO,CAAC,YAAY;IAC5C,OAAO;MACL0H,SAAS,EAAEjI,eAAe;MAC1BvB,QAAQ,EAAEA,QAAQ;MAClBC,cAAc,EAAEA,cAAc;MAC9BC,UAAU,EAAEA,UAAU;MACtBuI,WAAW,EAAE5C,gBAAgB;MAC7B+C,eAAe,EAAE9C,oBAAoB;MACrC3F,oBAAoB,EAAEA,oBAAoB;MAC1CC,gBAAgB,EAAEA,gBAAgB;MAClCC,uBAAuB,EAAEA,uBAAuB;MAChDC,YAAY,EAAEA,YAAY;MAC1BU,QAAQ,EAAEA,QAAQ;MAClBG,UAAU,EAAEA,UAAU;MACtBF,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BH,QAAQ,EAAEA,QAAQ;MAClB3B,kBAAkB,EAAEA,kBAAkB;MACtCwD,WAAW,EAAEA;IACf,CAAC;EACH,CAAC,EAAE,CAACrB,eAAe,EAAEvB,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAE2F,gBAAgB,EAAEC,oBAAoB,EAAE3F,oBAAoB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEU,QAAQ,EAAEG,UAAU,EAAEF,YAAY,EAAEC,YAAY,EAAEH,QAAQ,EAAE3B,kBAAkB,EAAEwD,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEjR,OAAO,aAAatG,KAAK,CAACmN,aAAa,CAACxM,iBAAiB,CAACyM,QAAQ,EAAE;IAClEzL,KAAK,EAAEqL;EACT,CAAC,EAAE,aAAahN,KAAK,CAACmN,aAAa,CAACvM,aAAa,CAACwM,QAAQ,EAAE;IAC1DzL,KAAK,EAAEsL;EACT,CAAC,EAAE,aAAajN,KAAK,CAACmN,aAAa,CAAClN,UAAU,EAAER,QAAQ,CAAC;IACvDsC,GAAG,EAAEA;EACP,CAAC,EAAE+C,SAAS,EAAE;IACZ;IACA9C,EAAE,EAAE+C,QAAQ;IACZ7C,SAAS,EAAEA,SAAS;IACpBmL,IAAI,EAAElI,cAAc,GAAG,UAAU,GAAGS,SAAS,CAAC;IAAA;;IAE9C6D,aAAa,EAAEc,mBAAmB;IAClCqC,qBAAqB,EAAEA,qBAAqB,CAAC;IAAA;;IAE7CrK,WAAW,EAAEyD,iBAAiB;IAC9BvD,QAAQ,EAAEyD,gBAAgB,CAAC;IAAA;;IAE3B7F,UAAU,EAAEA,UAAU;IACtBiN,YAAY,EAAE,CAAClH,cAAc,CAACmB,MAAM;IACpCjD,uBAAuB,EAAEiI,+BAA+B;IACxD/H,wBAAwB,EAAEA;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC,CAAC;;AAEJ,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC1D,UAAU,CAAC2L,WAAW,GAAG,YAAY;AACvC;AAEA,IAAIC,iBAAiB,GAAG5L,UAAU;AAClC4L,iBAAiB,CAAClN,QAAQ,GAAGA,QAAQ;AACrCkN,iBAAiB,CAAChN,QAAQ,GAAGA,QAAQ;AACrCgN,iBAAiB,CAAC/M,WAAW,GAAGA,WAAW;AAC3C+M,iBAAiB,CAAC9M,UAAU,GAAGA,UAAU;AACzC,eAAe8M,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}