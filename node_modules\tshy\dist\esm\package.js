// get the package.json data for the cwd
import { readFileSync } from 'fs';
import { parse, stringify } from 'polite-json';
import fail from './fail.js';
const isPackage = (pkg) => !!pkg && typeof pkg === 'object' && !Array.isArray(pkg);
const readPkg = () => {
    try {
        const res = parse(readFileSync('package.json', 'utf8'));
        if (isPackage(res))
            return res;
        throw new Error('Invalid package.json contents: ' + stringify(res));
    }
    catch (er) {
        fail('failed to read package.json', er);
        process.exit(1);
    }
};
export default readPkg();
//# sourceMappingURL=package.js.map