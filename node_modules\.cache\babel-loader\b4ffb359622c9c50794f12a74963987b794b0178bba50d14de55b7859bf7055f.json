{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar deep_diff_1 = require(\"deep-diff\");\n// @ts-ignore\nvar nested_property_1 = require(\"nested-property\");\nfunction isPlainObj() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return args.every(function (d) {\n    if (!d) {\n      return false;\n    }\n    var prototype = Object.getPrototypeOf(d);\n    return Object.prototype.toString.call(d).slice(8, -1) === 'Object' && (prototype === null || prototype === Object.getPrototypeOf({}));\n  });\n}\nfunction isArray() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return args.every(Array.isArray);\n}\nfunction isNumber() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return args.every(function (d) {\n    return typeof d === 'number';\n  });\n}\nfunction treeChanges(data, nextData) {\n  if (!data || !nextData) {\n    throw new Error('Missing required parameters');\n  }\n  return {\n    changed: function (key) {\n      var left = nested_property_1.get(data, key);\n      var right = nested_property_1.get(nextData, key);\n      if (isArray(left, right) || isPlainObj(left, right)) {\n        return !!deep_diff_1.diff(left, right);\n      }\n      return left !== right;\n    },\n    changedFrom: function (key, previous, actual) {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n      var useActual = typeof previous !== 'undefined' && typeof actual !== 'undefined';\n      var left = nested_property_1.get(data, key);\n      var right = nested_property_1.get(nextData, key);\n      var leftComparator = Array.isArray(previous) ? previous.indexOf(left) >= 0 : left === previous;\n      var rightComparator = Array.isArray(actual) ? actual.indexOf(right) >= 0 : right === actual;\n      return leftComparator && (useActual ? rightComparator : !useActual);\n    },\n    changedTo: function (key, actual) {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n      var left = nested_property_1.get(data, key);\n      var right = nested_property_1.get(nextData, key);\n      var leftComparator = Array.isArray(actual) ? actual.indexOf(left) < 0 : left !== actual;\n      var rightComparator = Array.isArray(actual) ? actual.indexOf(right) >= 0 : right === actual;\n      return leftComparator && rightComparator;\n    },\n    increased: function (key) {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n      return isNumber(nested_property_1.get(data, key), nested_property_1.get(nextData, key)) && nested_property_1.get(data, key) < nested_property_1.get(nextData, key);\n    },\n    decreased: function (key) {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n      return isNumber(nested_property_1.get(data, key), nested_property_1.get(nextData, key)) && nested_property_1.get(data, key) > nested_property_1.get(nextData, key);\n    }\n  };\n}\nexports.default = treeChanges;", "map": {"version": 3, "names": ["deep_diff_1", "require", "nested_property_1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args", "_i", "arguments", "length", "every", "d", "prototype", "Object", "getPrototypeOf", "toString", "call", "slice", "isArray", "Array", "isNumber", "treeChanges", "data", "nextData", "Error", "changed", "key", "left", "get", "right", "diff", "changedFrom", "previous", "actual", "useActual", "leftComparator", "indexOf", "rightComparator", "changedTo", "increased", "decreased", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\react-floater\\node_modules\\tree-changes\\src\\index.ts"], "sourcesContent": ["import { diff } from 'deep-diff';\n// @ts-ignore\nimport { get as nested } from 'nested-property';\n\ninterface IPlainObject {\n  [key: string]: any;\n}\n\nexport type TypeInput =\n  | string\n  | boolean\n  | number\n  | IPlainObject\n  | Array<string | boolean | number | IPlainObject>;\n\nexport type IData = IPlainObject | IPlainObject[];\n\nexport interface ITreeChanges {\n  changed: (key?: string | number) => boolean;\n  changedFrom: (key: string | number, previous: TypeInput, actual?: TypeInput) => boolean;\n  changedTo: (key: string | number, actual: TypeInput) => boolean;\n  increased: (key: string | number) => boolean;\n  decreased: (key: string | number) => boolean;\n}\n\nfunction isPlainObj(...args: any): boolean {\n  return args.every((d: any) => {\n    if (!d) {\n      return false;\n    }\n    const prototype = Object.getPrototypeOf(d);\n\n    return (\n      Object.prototype.toString.call(d).slice(8, -1) === 'Object' &&\n      (prototype === null || prototype === Object.getPrototypeOf({}))\n    );\n  });\n}\n\nfunction isArray(...args: any): boolean {\n  return args.every(Array.isArray);\n}\n\nfunction isNumber(...args: any): boolean {\n  return args.every((d: any) => typeof d === 'number');\n}\n\nexport default function treeChanges(data: IData, nextData: IData): ITreeChanges {\n  if (!data || !nextData) {\n    throw new Error('Missing required parameters');\n  }\n\n  return {\n    changed(key?: string | number): boolean {\n      const left = nested(data, key);\n      const right = nested(nextData, key);\n\n      if (isArray(left, right) || isPlainObj(left, right)) {\n        return !!diff(left, right);\n      }\n\n      return left !== right;\n    },\n    changedFrom(key: string | number, previous: TypeInput, actual?: TypeInput): boolean {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n\n      const useActual = typeof previous !== 'undefined' && typeof actual !== 'undefined';\n      const left = nested(data, key);\n      const right = nested(nextData, key);\n      const leftComparator = Array.isArray(previous)\n        ? previous.indexOf(left) >= 0\n        : left === previous;\n      const rightComparator = Array.isArray(actual) ? actual.indexOf(right) >= 0 : right === actual;\n\n      return leftComparator && (useActual ? rightComparator : !useActual);\n    },\n    changedTo(key: string | number, actual: TypeInput): boolean {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n\n      const left = nested(data, key);\n      const right = nested(nextData, key);\n\n      const leftComparator = Array.isArray(actual) ? actual.indexOf(left) < 0 : left !== actual;\n      const rightComparator = Array.isArray(actual) ? actual.indexOf(right) >= 0 : right === actual;\n\n      return leftComparator && rightComparator;\n    },\n    increased(key: string | number): boolean {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n\n      return (\n        isNumber(nested(data, key), nested(nextData, key)) &&\n        nested(data, key) < nested(nextData, key)\n      );\n    },\n    decreased(key: string | number): boolean {\n      if (typeof key === 'undefined') {\n        throw new Error('Key parameter is required');\n      }\n\n      return (\n        isNumber(nested(data, key), nested(nextData, key)) &&\n        nested(data, key) > nested(nextData, key)\n      );\n    },\n  };\n}\n"], "mappings": ";;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AACA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AAuBA,SAASE,UAAUA,CAAA;EAAC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAY,EAAZA,EAAA,GAAAC,SAAA,CAAAC,MAAY,EAAZF,EAAA,EAAY;IAAZD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAClB,OAAOD,IAAI,CAACI,KAAK,CAAC,UAACC,CAAM;IACvB,IAAI,CAACA,CAAC,EAAE;MACN,OAAO,KAAK;;IAEd,IAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACH,CAAC,CAAC;IAE1C,OACEE,MAAM,CAACD,SAAS,CAACG,QAAQ,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,KAC1DL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,MAAM,CAACC,cAAc,CAAC,EAAE,CAAC,CAAC;EAEnE,CAAC,CAAC;AACJ;AAEA,SAASI,OAAOA,CAAA;EAAC,IAAAZ,IAAA;OAAA,IAAAC,EAAA,IAAY,EAAZA,EAAA,GAAAC,SAAA,CAAAC,MAAY,EAAZF,EAAA,EAAY;IAAZD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACf,OAAOD,IAAI,CAACI,KAAK,CAACS,KAAK,CAACD,OAAO,CAAC;AAClC;AAEA,SAASE,QAAQA,CAAA;EAAC,IAAAd,IAAA;OAAA,IAAAC,EAAA,IAAY,EAAZA,EAAA,GAAAC,SAAA,CAAAC,MAAY,EAAZF,EAAA,EAAY;IAAZD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAChB,OAAOD,IAAI,CAACI,KAAK,CAAC,UAACC,CAAM;IAAK,cAAOA,CAAC,KAAK,QAAQ;EAArB,CAAqB,CAAC;AACtD;AAEA,SAAwBU,WAAWA,CAACC,IAAW,EAAEC,QAAe;EAC9D,IAAI,CAACD,IAAI,IAAI,CAACC,QAAQ,EAAE;IACtB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;;EAGhD,OAAO;IACLC,OAAO,EAAP,SAAAA,CAAQC,GAAqB;MAC3B,IAAMC,IAAI,GAAGvB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC;MAC9B,IAAMG,KAAK,GAAGzB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC;MAEnC,IAAIR,OAAO,CAACS,IAAI,EAAEE,KAAK,CAAC,IAAIxB,UAAU,CAACsB,IAAI,EAAEE,KAAK,CAAC,EAAE;QACnD,OAAO,CAAC,CAAC3B,WAAA,CAAA4B,IAAI,CAACH,IAAI,EAAEE,KAAK,CAAC;;MAG5B,OAAOF,IAAI,KAAKE,KAAK;IACvB,CAAC;IACDE,WAAW,EAAX,SAAAA,CAAYL,GAAoB,EAAEM,QAAmB,EAAEC,MAAkB;MACvE,IAAI,OAAOP,GAAG,KAAK,WAAW,EAAE;QAC9B,MAAM,IAAIF,KAAK,CAAC,2BAA2B,CAAC;;MAG9C,IAAMU,SAAS,GAAG,OAAOF,QAAQ,KAAK,WAAW,IAAI,OAAOC,MAAM,KAAK,WAAW;MAClF,IAAMN,IAAI,GAAGvB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC;MAC9B,IAAMG,KAAK,GAAGzB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC;MACnC,IAAMS,cAAc,GAAGhB,KAAK,CAACD,OAAO,CAACc,QAAQ,CAAC,GAC1CA,QAAQ,CAACI,OAAO,CAACT,IAAI,CAAC,IAAI,CAAC,GAC3BA,IAAI,KAAKK,QAAQ;MACrB,IAAMK,eAAe,GAAGlB,KAAK,CAACD,OAAO,CAACe,MAAM,CAAC,GAAGA,MAAM,CAACG,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,GAAGA,KAAK,KAAKI,MAAM;MAE7F,OAAOE,cAAc,KAAKD,SAAS,GAAGG,eAAe,GAAG,CAACH,SAAS,CAAC;IACrE,CAAC;IACDI,SAAS,EAAT,SAAAA,CAAUZ,GAAoB,EAAEO,MAAiB;MAC/C,IAAI,OAAOP,GAAG,KAAK,WAAW,EAAE;QAC9B,MAAM,IAAIF,KAAK,CAAC,2BAA2B,CAAC;;MAG9C,IAAMG,IAAI,GAAGvB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC;MAC9B,IAAMG,KAAK,GAAGzB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC;MAEnC,IAAMS,cAAc,GAAGhB,KAAK,CAACD,OAAO,CAACe,MAAM,CAAC,GAAGA,MAAM,CAACG,OAAO,CAACT,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI,KAAKM,MAAM;MACzF,IAAMI,eAAe,GAAGlB,KAAK,CAACD,OAAO,CAACe,MAAM,CAAC,GAAGA,MAAM,CAACG,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,GAAGA,KAAK,KAAKI,MAAM;MAE7F,OAAOE,cAAc,IAAIE,eAAe;IAC1C,CAAC;IACDE,SAAS,EAAT,SAAAA,CAAUb,GAAoB;MAC5B,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;QAC9B,MAAM,IAAIF,KAAK,CAAC,2BAA2B,CAAC;;MAG9C,OACEJ,QAAQ,CAAChB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC,EAAEtB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC,CAAC,IAClDtB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC,GAAGtB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC;IAE7C,CAAC;IACDc,SAAS,EAAT,SAAAA,CAAUd,GAAoB;MAC5B,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;QAC9B,MAAM,IAAIF,KAAK,CAAC,2BAA2B,CAAC;;MAG9C,OACEJ,QAAQ,CAAChB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC,EAAEtB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC,CAAC,IAClDtB,iBAAA,CAAAwB,GAAM,CAACN,IAAI,EAAEI,GAAG,CAAC,GAAGtB,iBAAA,CAAAwB,GAAM,CAACL,QAAQ,EAAEG,GAAG,CAAC;IAE7C;GACD;AACH;AAjEAe,OAAA,CAAAC,OAAA,GAAArB,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}