{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.changeConfirmLocale = changeConfirmLocale;\nexports.getConfirmLocale = getConfirmLocale;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _default = _interopRequireDefault(require(\"../locale/default\"));\nvar runtimeLocale = (0, _extends2[\"default\"])({}, _default[\"default\"].Modal);\nfunction changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    runtimeLocale = (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, runtimeLocale), newLocale);\n  } else {\n    runtimeLocale = (0, _extends2[\"default\"])({}, _default[\"default\"].Modal);\n  }\n}\nfunction getConfirmLocale() {\n  return runtimeLocale;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "changeConfirmLocale", "getConfirmLocale", "_extends2", "_default", "runtimeLocale", "Modal", "newLocale"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/modal/locale.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.changeConfirmLocale = changeConfirmLocale;\nexports.getConfirmLocale = getConfirmLocale;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _default = _interopRequireDefault(require(\"../locale/default\"));\n\nvar runtimeLocale = (0, _extends2[\"default\"])({}, _default[\"default\"].Modal);\n\nfunction changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    runtimeLocale = (0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, runtimeLocale), newLocale);\n  } else {\n    runtimeLocale = (0, _extends2[\"default\"])({}, _default[\"default\"].Modal);\n  }\n}\n\nfunction getConfirmLocale() {\n  return runtimeLocale;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB;AACjDF,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAE3C,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIQ,QAAQ,GAAGT,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAEnE,IAAIS,aAAa,GAAG,CAAC,CAAC,EAAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEC,QAAQ,CAAC,SAAS,CAAC,CAACE,KAAK,CAAC;AAE5E,SAASL,mBAAmBA,CAACM,SAAS,EAAE;EACtC,IAAIA,SAAS,EAAE;IACbF,aAAa,GAAG,CAAC,CAAC,EAAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEE,aAAa,CAAC,EAAEE,SAAS,CAAC;EACpG,CAAC,MAAM;IACLF,aAAa,GAAG,CAAC,CAAC,EAAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEC,QAAQ,CAAC,SAAS,CAAC,CAACE,KAAK,CAAC;EAC1E;AACF;AAEA,SAASJ,gBAAgBA,CAAA,EAAG;EAC1B,OAAOG,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}