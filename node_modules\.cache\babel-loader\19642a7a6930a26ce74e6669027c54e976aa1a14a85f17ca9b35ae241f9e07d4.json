{"ast": null, "code": "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\nvar TypeError = global.TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a function');\n};", "map": {"version": 3, "names": ["global", "require", "isCallable", "tryToString", "TypeError", "module", "exports", "argument"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/a-callable.js"], "sourcesContent": ["var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar TypeError = global.TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a function');\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,WAAW,GAAGF,OAAO,CAAC,4BAA4B,CAAC;AAEvD,IAAIG,SAAS,GAAGJ,MAAM,CAACI,SAAS;;AAEhC;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIL,UAAU,CAACK,QAAQ,CAAC,EAAE,OAAOA,QAAQ;EACzC,MAAMH,SAAS,CAACD,WAAW,CAACI,QAAQ,CAAC,GAAG,oBAAoB,CAAC;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}