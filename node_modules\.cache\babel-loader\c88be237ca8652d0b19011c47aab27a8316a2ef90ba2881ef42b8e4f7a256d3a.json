{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useRaf(callback) {\n  var rafRef = useRef();\n  var removedRef = useRef(false);\n  function trigger() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (!removedRef.current) {\n      raf.cancel(rafRef.current);\n      rafRef.current = raf(function () {\n        callback.apply(void 0, args);\n      });\n    }\n  }\n  useEffect(function () {\n    return function () {\n      removedRef.current = true;\n      raf.cancel(rafRef.current);\n    };\n  }, []);\n  return trigger;\n}\nexport function useRafState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    forceUpdate = _useState2[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useRaf(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useRef", "useState", "useEffect", "raf", "useRaf", "callback", "rafRef", "removedRef", "trigger", "_len", "arguments", "length", "args", "Array", "_key", "current", "cancel", "apply", "useRafState", "defaultState", "batchRef", "_useState", "_useState2", "forceUpdate", "state", "flushUpdate", "for<PERSON>ach", "updater", "push"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/hooks/useRaf.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useRef, useState, useEffect } from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useRaf(callback) {\n  var rafRef = useRef();\n  var removedRef = useRef(false);\n\n  function trigger() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    if (!removedRef.current) {\n      raf.cancel(rafRef.current);\n      rafRef.current = raf(function () {\n        callback.apply(void 0, args);\n      });\n    }\n  }\n\n  useEffect(function () {\n    return function () {\n      removedRef.current = true;\n      raf.cancel(rafRef.current);\n    };\n  }, []);\n  return trigger;\n}\nexport function useRafState(defaultState) {\n  var batchRef = useRef([]);\n\n  var _useState = useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      forceUpdate = _useState2[1];\n\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useRaf(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n\n  return [state.current, updater];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,MAAMA,CAACC,QAAQ,EAAE;EACvC,IAAIC,MAAM,GAAGN,MAAM,CAAC,CAAC;EACrB,IAAIO,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;EAE9B,SAASQ,OAAOA,CAAA,EAAG;IACjB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEA,IAAI,CAACP,UAAU,CAACQ,OAAO,EAAE;MACvBZ,GAAG,CAACa,MAAM,CAACV,MAAM,CAACS,OAAO,CAAC;MAC1BT,MAAM,CAACS,OAAO,GAAGZ,GAAG,CAAC,YAAY;QAC/BE,QAAQ,CAACY,KAAK,CAAC,KAAK,CAAC,EAAEL,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ;EACF;EAEAV,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBK,UAAU,CAACQ,OAAO,GAAG,IAAI;MACzBZ,GAAG,CAACa,MAAM,CAACV,MAAM,CAACS,OAAO,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOP,OAAO;AAChB;AACA,OAAO,SAASU,WAAWA,CAACC,YAAY,EAAE;EACxC,IAAIC,QAAQ,GAAGpB,MAAM,CAAC,EAAE,CAAC;EAEzB,IAAIqB,SAAS,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxBqB,UAAU,GAAGvB,cAAc,CAACsB,SAAS,EAAE,CAAC,CAAC;IACzCE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE/B,IAAIE,KAAK,GAAGxB,MAAM,CAAC,OAAOmB,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY,CAAC;EACtF,IAAIM,WAAW,GAAGrB,MAAM,CAAC,YAAY;IACnC,IAAIW,OAAO,GAAGS,KAAK,CAACT,OAAO;IAC3BK,QAAQ,CAACL,OAAO,CAACW,OAAO,CAAC,UAAUrB,QAAQ,EAAE;MAC3CU,OAAO,GAAGV,QAAQ,CAACU,OAAO,CAAC;IAC7B,CAAC,CAAC;IACFK,QAAQ,CAACL,OAAO,GAAG,EAAE;IACrBS,KAAK,CAACT,OAAO,GAAGA,OAAO;IACvBQ,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC;EAEF,SAASI,OAAOA,CAACtB,QAAQ,EAAE;IACzBe,QAAQ,CAACL,OAAO,CAACa,IAAI,CAACvB,QAAQ,CAAC;IAC/BoB,WAAW,CAAC,CAAC;EACf;EAEA,OAAO,CAACD,KAAK,CAACT,OAAO,EAAEY,OAAO,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}