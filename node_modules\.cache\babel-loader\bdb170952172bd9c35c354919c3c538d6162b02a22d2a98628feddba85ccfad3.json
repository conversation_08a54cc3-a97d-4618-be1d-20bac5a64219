{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\avviaConversazione.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AvviaConversazione - operazioni sull'aggiunta premi fine anno e sconti pagamento\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { InputText } from 'primereact/inputtext';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport { affiliatoGestionePuntiVendita } from '../components/route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AvviaConversazione = props => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [messages, setMessages] = useState([]);\n  const [currentMessages, setCurrentMessages] = useState([]);\n  const [message, setMessage] = useState([]);\n  const [uniqueUsers, setUniqueUsers] = useState([]);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    let role = localStorage.getItem(\"role\");\n\n    //Fare una get per vedere se ci sono fià message presenti legati al fornitore\n\n    async function trovaRisultato() {\n      await APIRequest('GET', \"notifyuser/?id=\".concat(props.results.id)).then(res => {\n        let uniqueUser = getUniqueUsers(res.data);\n        uniqueUser = uniqueUser.filter(el => el.username !== JSON.parse(localStorage.getItem(\"user\")).username);\n        setUniqueUsers(role !== 'DISTRIBUTORE' ? window.location.pathname !== affiliatoGestionePuntiVendita ? uniqueUser : null : null);\n        let currentMessage = res.data.filter(el => el.idUserReciver.id === uniqueUser[0].id || el.idUserSender.id === uniqueUser[0].id);\n        setCurrentMessages(currentMessage);\n        setMessages(res.data);\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non \\xE8 stato possibile reperire i messaggi per il cliente selezionato. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n      stopLoading();\n    }\n    trovaRisultato();\n  }, [props.results.id]);\n  const getUniqueUsers = data => {\n    const uniqueIds = new Set();\n    const uniqueUser = [];\n    data.forEach(item => {\n      ['idUserReciver', 'idUserSender'].forEach(key => {\n        if (item[key] && !uniqueIds.has(item[key].id)) {\n          uniqueIds.add(item[key].id);\n          uniqueUser.push({\n            id: item[key].id,\n            username: item[key].username,\n            name: \"\".concat(item[key].idRegistry.firstName, \" \").concat(item[key].idRegistry.lastName)\n          });\n        }\n      });\n    });\n    return uniqueUser;\n  };\n  const renderMessages = () => {\n    const groupedMessages = groupConsecutiveMessages(currentMessages);\n    return groupedMessages.map((group, groupIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex flex-column\",\n      children: group.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-box \".concat(message.idUserSender.id === JSON.parse(localStorage.getItem('user')).id ? 'sent-message' : 'received-message'),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-content\",\n          children: message.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-timestamp text-right\",\n          children: formatTimestamp(message.createAt)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 25\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 21\n      }, this))\n    }, groupIndex, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this));\n  };\n  const formatTimestamp = timestamp => {\n    const date = new Date(timestamp);\n    const hours = date.getHours();\n    const minutes = date.getMinutes();\n    return \"\".concat(hours, \":\").concat(minutes);\n  };\n  const groupConsecutiveMessages = messages => {\n    const grouped = [];\n    let currentGroup = [];\n    messages.forEach((message, index) => {\n      if (index === 0 || message.idUserSender.username !== messages[index - 1].idUserSender.username) {\n        currentGroup = [message];\n        grouped.push(currentGroup);\n      } else {\n        currentGroup.push(message);\n      }\n    });\n    return grouped;\n  };\n  const changeSelection = (e, el) => {\n    let currentMessage = messages.filter(obj => obj.idUserReciver.id === el.id || obj.idUserSender.id === el.id);\n    setCurrentMessages(currentMessage);\n  };\n  const Invia = async e => {\n    let body = {\n      idUserSender: JSON.parse(localStorage.getItem('user')).id,\n      idUserReciver: JSON.parse(localStorage.getItem('user')).id !== props.results.id ? props.results.id : currentMessages[0].idUserReciver.id !== props.results.id ? currentMessages[0].idUserReciver.id : currentMessages[0].idUserSender.id,\n      message: message\n    };\n    await APIRequest('POST', 'notifyuser', body).then(res => {\n      console.log(res.data);\n      setMessage(null);\n      toast.current.show({\n        severity: 'success',\n        summary: 'Ottimo',\n        detail: \"Messaggio inviato con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile inviare il messaggio. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: uniqueUsers ? \"modalBody messageboxheight\" : \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row h-100\",\n      children: [uniqueUsers && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center col-12 col-md-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"list-group w-100\",\n          children: uniqueUsers.map((el, key) => {\n            return /*#__PURE__*/_jsxDEV(React.Fragment, {\n              children: /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"list-group-item chatNameBox w-100\",\n                style: {\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"m-0 text-center\",\n                  onClick: e => changeSelection(e, el),\n                  children: [\" \", el.name, \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 45\n                }, this)\n              }, el.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 41\n              }, this)\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 37\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: uniqueUsers ? 'col-12 col-md-10 d-flex flex-column justify-content-between border-left' : 'col-12 d-flex flex-column justify-content-between',\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: renderMessages()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-row mt-3 border-top py-3\",\n          children: [/*#__PURE__*/_jsxDEV(InputText, {\n            className: \"rounded mr-2 w-100\",\n            type: \"text\",\n            value: message,\n            onChange: e => setMessage(e.target.value),\n            placeholder: \"Inserisci il messaggio e premi invio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: !uniqueUsers ? 'p-button-rounded p-3 w-auto' : 'p-button-rounded p-3',\n            onClick: Invia,\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-send\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 9\n  }, this);\n};\n_s(AvviaConversazione, \"WsdDavcdpRDtT4F4R/08NpMHfRM=\");\n_c = AvviaConversazione;\nexport default AvviaConversazione;\nvar _c;\n$RefreshReg$(_c, \"AvviaConversazione\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "APIRequest", "InputText", "stopLoading", "<PERSON><PERSON>", "Toast", "affiliatoGestionePuntiVendita", "jsxDEV", "_jsxDEV", "AvviaConversazione", "props", "_s", "messages", "setMessages", "currentMessages", "setCurrentMessages", "message", "setMessage", "uniqueUsers", "setUniqueUsers", "toast", "role", "localStorage", "getItem", "trovaRisultato", "concat", "results", "id", "then", "res", "uniqueUser", "getUniqueUsers", "data", "filter", "el", "username", "JSON", "parse", "window", "location", "pathname", "currentMessage", "idUserReciver", "idUserSender", "catch", "e", "_e$response", "_e$response2", "console", "log", "current", "show", "severity", "summary", "detail", "response", "undefined", "life", "uniqueIds", "Set", "for<PERSON>ach", "item", "key", "has", "add", "push", "name", "idRegistry", "firstName", "lastName", "renderMessages", "groupedMessages", "groupConsecutiveMessages", "map", "group", "groupIndex", "className", "children", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatTimestamp", "createAt", "timestamp", "date", "Date", "hours", "getHours", "minutes", "getMinutes", "grouped", "currentGroup", "changeSelection", "obj", "Invia", "body", "setTimeout", "reload", "_e$response3", "_e$response4", "ref", "Fragment", "style", "cursor", "onClick", "type", "value", "onChange", "target", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/avviaConversazione.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AvviaConversazione - operazioni sull'aggiunta premi fine anno e sconti pagamento\n*\n*/\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { InputText } from 'primereact/inputtext';\nimport { stopLoading } from '../components/generalizzazioni/stopLoading';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport '../css/modale.css';\nimport { affiliatoGestionePuntiVendita } from '../components/route';\n\nconst AvviaConversazione = (props) => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [messages, setMessages] = useState([]);\n    const [currentMessages, setCurrentMessages] = useState([]);\n    const [message, setMessage] = useState([]);\n    const [uniqueUsers, setUniqueUsers] = useState([]);\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n\n        let role = localStorage.getItem(\"role\")\n\n        //Fare una get per vedere se ci sono fià message presenti legati al fornitore\n\n        async function trovaRisultato() {\n            await APIRequest('GET', `notifyuser/?id=${props.results.id}`)\n                .then(res => {\n                    let uniqueUser = getUniqueUsers(res.data);\n                    uniqueUser = uniqueUser.filter(el => el.username !== JSON.parse(localStorage.getItem(\"user\")).username)\n                    setUniqueUsers(role !== 'DISTRIBUTORE' ? (window.location.pathname !== affiliatoGestionePuntiVendita ? uniqueUser : null) : null)\n                    let currentMessage = res.data.filter(el => el.idUserReciver.id === uniqueUser[0].id || el.idUserSender.id === uniqueUser[0].id)\n                    setCurrentMessages(currentMessage)\n                    setMessages(res.data)\n                }).catch((e) => {\n                    console.log(e)\n                    toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile reperire i messaggi per il cliente selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                })\n            stopLoading()\n        }\n        trovaRisultato();\n    }, [props.results.id]);\n\n    const getUniqueUsers = (data) => {\n        const uniqueIds = new Set();\n        const uniqueUser = [];\n\n        data.forEach(item => {\n            ['idUserReciver', 'idUserSender'].forEach(key => {\n                if (item[key] && !uniqueIds.has(item[key].id)) {\n                    uniqueIds.add(item[key].id);\n                    uniqueUser.push({\n                        id: item[key].id,\n                        username: item[key].username,\n                        name: `${item[key].idRegistry.firstName} ${item[key].idRegistry.lastName}`\n                    });\n                }\n            });\n        });\n\n        return uniqueUser;\n    };\n\n    const renderMessages = () => {\n        const groupedMessages = groupConsecutiveMessages(currentMessages);\n    \n        return groupedMessages.map((group, groupIndex) => (\n            <div key={groupIndex} className=\"d-flex flex-column\">\n                {group.map((message, index) => (\n                    <div\n                        key={index}\n                        className={`message-box ${message.idUserSender.id === JSON.parse(localStorage.getItem('user')).id\n                            ? 'sent-message'\n                            : 'received-message'\n                        }`}\n                    >\n                        {/* {index === 0 && (\n                            <div className=\"username\">{message.idUserSender.username}</div>\n                        )} */}\n                        <div className=\"message-content\">{message.message}</div>\n                        <div className=\"message-timestamp text-right\">{formatTimestamp(message.createAt)}</div>\n                    </div>\n                ))}\n            </div>\n        ));\n    };\n    \n    const formatTimestamp = (timestamp) => {\n        const date = new Date(timestamp);\n        const hours = date.getHours();\n        const minutes = date.getMinutes();\n        return `${hours}:${minutes}`;\n    };\n\n    const groupConsecutiveMessages = (messages) => {\n        const grouped = [];\n        let currentGroup = [];\n\n        messages.forEach((message, index) => {\n            if (index === 0 || message.idUserSender.username !== messages[index - 1].idUserSender.username) {\n                currentGroup = [message];\n                grouped.push(currentGroup);\n            } else {\n                currentGroup.push(message);\n            }\n        });\n\n        return grouped;\n    };\n\n    const changeSelection = (e, el) => {\n        let currentMessage = messages.filter(obj => obj.idUserReciver.id === el.id || obj.idUserSender.id === el.id)\n        setCurrentMessages(currentMessage)\n    }\n\n    const Invia = async (e) => {\n        let body = {\n            idUserSender: JSON.parse(localStorage.getItem('user')).id,\n            idUserReciver: JSON.parse(localStorage.getItem('user')).id !== props.results.id ? props.results.id : (currentMessages[0].idUserReciver.id !== props.results.id ? currentMessages[0].idUserReciver.id : currentMessages[0].idUserSender.id),\n            message: message\n        }\n        await APIRequest('POST', 'notifyuser', body)\n            .then(res => {\n                console.log(res.data);\n                setMessage(null)\n                toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"Messaggio inviato con successo\", life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            }).catch((e) => {\n                console.log(e)\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile inviare il messaggio. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    };\n\n    return (\n        <div className={uniqueUsers ? \"modalBody messageboxheight\" : \"modalBody\"}>\n            <Toast ref={toast} />\n            <div className='row h-100'>\n                {uniqueUsers &&\n                    <div className='d-flex justify-content-center col-12 col-md-2'>\n                        <ul className=\"list-group w-100\">\n                            {uniqueUsers.map((el, key) => {\n                                return (\n                                    <React.Fragment key={key}>\n                                        <li className=\"list-group-item chatNameBox w-100\" style={{cursor:'pointer'}} key={el.id}>\n                                            <p className='m-0 text-center' onClick={(e) => changeSelection(e, el)}> {el.name} </p>\n                                        </li>\n                                    </React.Fragment>\n                                )\n                            })}\n                        </ul>\n                    </div>\n                }\n                <div className={uniqueUsers ? 'col-12 col-md-10 d-flex flex-column justify-content-between border-left' : 'col-12 d-flex flex-column justify-content-between'}>\n                    <div>\n                        {renderMessages()}\n                    </div>\n                    <div className='d-flex flex-row mt-3 border-top py-3'>\n                        <InputText\n                            className='rounded mr-2 w-100'\n                            type='text'\n                            value={message}\n                            onChange={(e) => setMessage(e.target.value)}\n                            placeholder='Inserisci il messaggio e premi invio'\n                        />\n                        <Button\n                            className={!uniqueUsers ? 'p-button-rounded p-3 w-auto' : 'p-button-rounded p-3'}\n                            onClick={Invia}\n                        >\n                            <i className='pi pi-send'></i>\n                        </Button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default AvviaConversazione;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,4CAA4C;AACxE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,mBAAmB;AAC1B,SAASC,6BAA6B,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMsB,KAAK,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IAEZ,IAAIsB,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;;IAEvC;;IAEA,eAAeC,cAAcA,CAAA,EAAG;MAC5B,MAAMvB,UAAU,CAAC,KAAK,oBAAAwB,MAAA,CAAoBf,KAAK,CAACgB,OAAO,CAACC,EAAE,CAAE,CAAC,CACxDC,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,UAAU,GAAGC,cAAc,CAACF,GAAG,CAACG,IAAI,CAAC;QACzCF,UAAU,GAAGA,UAAU,CAACG,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAKC,IAAI,CAACC,KAAK,CAACf,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC;QACvGhB,cAAc,CAACE,IAAI,KAAK,cAAc,GAAIiB,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKlC,6BAA6B,GAAGwB,UAAU,GAAG,IAAI,GAAI,IAAI,CAAC;QACjI,IAAIW,cAAc,GAAGZ,GAAG,CAACG,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACQ,aAAa,CAACf,EAAE,KAAKG,UAAU,CAAC,CAAC,CAAC,CAACH,EAAE,IAAIO,EAAE,CAACS,YAAY,CAAChB,EAAE,KAAKG,UAAU,CAAC,CAAC,CAAC,CAACH,EAAE,CAAC;QAC/HZ,kBAAkB,CAAC0B,cAAc,CAAC;QAClC5B,WAAW,CAACgB,GAAG,CAACG,IAAI,CAAC;MACzB,CAAC,CAAC,CAACY,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAC,WAAA,EAAAC,YAAA;QACZC,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;QACdzB,KAAK,CAAC8B,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,gGAAA7B,MAAA,CAA6F,EAAAqB,WAAA,GAAAD,CAAC,CAACU,QAAQ,cAAAT,WAAA,uBAAVA,WAAA,CAAYd,IAAI,MAAKwB,SAAS,IAAAT,YAAA,GAAGF,CAAC,CAACU,QAAQ,cAAAR,YAAA,uBAAVA,YAAA,CAAYf,IAAI,GAAGa,CAAC,CAAC7B,OAAO,CAAE;UAAEyC,IAAI,EAAE;QAAK,CAAC,CAAC;MACzP,CAAC,CAAC;MACNtD,WAAW,CAAC,CAAC;IACjB;IACAqB,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACd,KAAK,CAACgB,OAAO,CAACC,EAAE,CAAC,CAAC;EAEtB,MAAMI,cAAc,GAAIC,IAAI,IAAK;IAC7B,MAAM0B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,MAAM7B,UAAU,GAAG,EAAE;IAErBE,IAAI,CAAC4B,OAAO,CAACC,IAAI,IAAI;MACjB,CAAC,eAAe,EAAE,cAAc,CAAC,CAACD,OAAO,CAACE,GAAG,IAAI;QAC7C,IAAID,IAAI,CAACC,GAAG,CAAC,IAAI,CAACJ,SAAS,CAACK,GAAG,CAACF,IAAI,CAACC,GAAG,CAAC,CAACnC,EAAE,CAAC,EAAE;UAC3C+B,SAAS,CAACM,GAAG,CAACH,IAAI,CAACC,GAAG,CAAC,CAACnC,EAAE,CAAC;UAC3BG,UAAU,CAACmC,IAAI,CAAC;YACZtC,EAAE,EAAEkC,IAAI,CAACC,GAAG,CAAC,CAACnC,EAAE;YAChBQ,QAAQ,EAAE0B,IAAI,CAACC,GAAG,CAAC,CAAC3B,QAAQ;YAC5B+B,IAAI,KAAAzC,MAAA,CAAKoC,IAAI,CAACC,GAAG,CAAC,CAACK,UAAU,CAACC,SAAS,OAAA3C,MAAA,CAAIoC,IAAI,CAACC,GAAG,CAAC,CAACK,UAAU,CAACE,QAAQ;UAC5E,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,OAAOvC,UAAU;EACrB,CAAC;EAED,MAAMwC,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,eAAe,GAAGC,wBAAwB,CAAC1D,eAAe,CAAC;IAEjE,OAAOyD,eAAe,CAACE,GAAG,CAAC,CAACC,KAAK,EAAEC,UAAU,kBACzCnE,OAAA;MAAsBoE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAC/CH,KAAK,CAACD,GAAG,CAAC,CAACzD,OAAO,EAAE8D,KAAK,kBACtBtE,OAAA;QAEIoE,SAAS,iBAAAnD,MAAA,CAAiBT,OAAO,CAAC2B,YAAY,CAAChB,EAAE,KAAKS,IAAI,CAACC,KAAK,CAACf,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACI,EAAE,GAC3F,cAAc,GACd,kBAAkB,CACrB;QAAAkD,QAAA,gBAKHrE,OAAA;UAAKoE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAE7D,OAAO,CAACA;QAAO;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxD1E,OAAA;UAAKoE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAEM,eAAe,CAACnE,OAAO,CAACoE,QAAQ;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAVlFJ,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWT,CACR;IAAC,GAfIP,UAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAgBf,CACR,CAAC;EACN,CAAC;EAED,MAAMC,eAAe,GAAIE,SAAS,IAAK;IACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMG,KAAK,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC7B,MAAMC,OAAO,GAAGJ,IAAI,CAACK,UAAU,CAAC,CAAC;IACjC,UAAAlE,MAAA,CAAU+D,KAAK,OAAA/D,MAAA,CAAIiE,OAAO;EAC9B,CAAC;EAED,MAAMlB,wBAAwB,GAAI5D,QAAQ,IAAK;IAC3C,MAAMgF,OAAO,GAAG,EAAE;IAClB,IAAIC,YAAY,GAAG,EAAE;IAErBjF,QAAQ,CAACgD,OAAO,CAAC,CAAC5C,OAAO,EAAE8D,KAAK,KAAK;MACjC,IAAIA,KAAK,KAAK,CAAC,IAAI9D,OAAO,CAAC2B,YAAY,CAACR,QAAQ,KAAKvB,QAAQ,CAACkE,KAAK,GAAG,CAAC,CAAC,CAACnC,YAAY,CAACR,QAAQ,EAAE;QAC5F0D,YAAY,GAAG,CAAC7E,OAAO,CAAC;QACxB4E,OAAO,CAAC3B,IAAI,CAAC4B,YAAY,CAAC;MAC9B,CAAC,MAAM;QACHA,YAAY,CAAC5B,IAAI,CAACjD,OAAO,CAAC;MAC9B;IACJ,CAAC,CAAC;IAEF,OAAO4E,OAAO;EAClB,CAAC;EAED,MAAME,eAAe,GAAGA,CAACjD,CAAC,EAAEX,EAAE,KAAK;IAC/B,IAAIO,cAAc,GAAG7B,QAAQ,CAACqB,MAAM,CAAC8D,GAAG,IAAIA,GAAG,CAACrD,aAAa,CAACf,EAAE,KAAKO,EAAE,CAACP,EAAE,IAAIoE,GAAG,CAACpD,YAAY,CAAChB,EAAE,KAAKO,EAAE,CAACP,EAAE,CAAC;IAC5GZ,kBAAkB,CAAC0B,cAAc,CAAC;EACtC,CAAC;EAED,MAAMuD,KAAK,GAAG,MAAOnD,CAAC,IAAK;IACvB,IAAIoD,IAAI,GAAG;MACPtD,YAAY,EAAEP,IAAI,CAACC,KAAK,CAACf,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACI,EAAE;MACzDe,aAAa,EAAEN,IAAI,CAACC,KAAK,CAACf,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACI,EAAE,KAAKjB,KAAK,CAACgB,OAAO,CAACC,EAAE,GAAGjB,KAAK,CAACgB,OAAO,CAACC,EAAE,GAAIb,eAAe,CAAC,CAAC,CAAC,CAAC4B,aAAa,CAACf,EAAE,KAAKjB,KAAK,CAACgB,OAAO,CAACC,EAAE,GAAGb,eAAe,CAAC,CAAC,CAAC,CAAC4B,aAAa,CAACf,EAAE,GAAGb,eAAe,CAAC,CAAC,CAAC,CAAC6B,YAAY,CAAChB,EAAG;MAC1OX,OAAO,EAAEA;IACb,CAAC;IACD,MAAMf,UAAU,CAAC,MAAM,EAAE,YAAY,EAAEgG,IAAI,CAAC,CACvCrE,IAAI,CAACC,GAAG,IAAI;MACTmB,OAAO,CAACC,GAAG,CAACpB,GAAG,CAACG,IAAI,CAAC;MACrBf,UAAU,CAAC,IAAI,CAAC;MAChBG,KAAK,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,gCAAgC;QAAEG,IAAI,EAAE;MAAK,CAAC,CAAC;MACpHyC,UAAU,CAAC,MAAM;QACb5D,MAAM,CAACC,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAACvD,KAAK,CAAEC,CAAC,IAAK;MAAA,IAAAuD,YAAA,EAAAC,YAAA;MACZrD,OAAO,CAACC,GAAG,CAACJ,CAAC,CAAC;MACdzB,KAAK,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,sEAAA7B,MAAA,CAAmE,EAAA2E,YAAA,GAAAvD,CAAC,CAACU,QAAQ,cAAA6C,YAAA,uBAAVA,YAAA,CAAYpE,IAAI,MAAKwB,SAAS,IAAA6C,YAAA,GAAGxD,CAAC,CAACU,QAAQ,cAAA8C,YAAA,uBAAVA,YAAA,CAAYrE,IAAI,GAAGa,CAAC,CAAC7B,OAAO,CAAE;QAAEyC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC/N,CAAC,CAAC;EACV,CAAC;EAED,oBACIjD,OAAA;IAAKoE,SAAS,EAAE1D,WAAW,GAAG,4BAA4B,GAAG,WAAY;IAAA2D,QAAA,gBACrErE,OAAA,CAACH,KAAK;MAACiG,GAAG,EAAElF;IAAM;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB1E,OAAA;MAAKoE,SAAS,EAAC,WAAW;MAAAC,QAAA,GACrB3D,WAAW,iBACRV,OAAA;QAAKoE,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC1DrE,OAAA;UAAIoE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC3B3D,WAAW,CAACuD,GAAG,CAAC,CAACvC,EAAE,EAAE4B,GAAG,KAAK;YAC1B,oBACItD,OAAA,CAACX,KAAK,CAAC0G,QAAQ;cAAA1B,QAAA,eACXrE,OAAA;gBAAIoE,SAAS,EAAC,mCAAmC;gBAAC4B,KAAK,EAAE;kBAACC,MAAM,EAAC;gBAAS,CAAE;gBAAA5B,QAAA,eACxErE,OAAA;kBAAGoE,SAAS,EAAC,iBAAiB;kBAAC8B,OAAO,EAAG7D,CAAC,IAAKiD,eAAe,CAACjD,CAAC,EAAEX,EAAE,CAAE;kBAAA2C,QAAA,GAAC,GAAC,EAAC3C,EAAE,CAACgC,IAAI,EAAC,GAAC;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC,GADRhD,EAAE,CAACP,EAAE;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnF;YAAC,GAHYpB,GAAG;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIR,CAAC;UAEzB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV1E,OAAA;QAAKoE,SAAS,EAAE1D,WAAW,GAAG,yEAAyE,GAAG,mDAAoD;QAAA2D,QAAA,gBAC1JrE,OAAA;UAAAqE,QAAA,EACKP,cAAc,CAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACN1E,OAAA;UAAKoE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACjDrE,OAAA,CAACN,SAAS;YACN0E,SAAS,EAAC,oBAAoB;YAC9B+B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE5F,OAAQ;YACf6F,QAAQ,EAAGhE,CAAC,IAAK5B,UAAU,CAAC4B,CAAC,CAACiE,MAAM,CAACF,KAAK,CAAE;YAC5CG,WAAW,EAAC;UAAsC;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACF1E,OAAA,CAACJ,MAAM;YACHwE,SAAS,EAAE,CAAC1D,WAAW,GAAG,6BAA6B,GAAG,sBAAuB;YACjFwF,OAAO,EAAEV,KAAM;YAAAnB,QAAA,eAEfrE,OAAA;cAAGoE,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAvE,EAAA,CAtKKF,kBAAkB;AAAAuG,EAAA,GAAlBvG,kBAAkB;AAwKxB,eAAeA,kBAAkB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}