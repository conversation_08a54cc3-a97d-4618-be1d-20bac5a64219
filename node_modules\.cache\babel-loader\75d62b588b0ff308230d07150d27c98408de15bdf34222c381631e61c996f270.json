{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Radio from './radio';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\nimport { RadioGroupContextProvider } from './context';\nimport getDataOrAriaProps from '../_util/getDataOrAriaProps';\nvar RadioGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var size = React.useContext(SizeContext);\n  var _useMergedState = useMergedState(props.defaultValue, {\n      value: props.value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var onRadioChange = function onRadioChange(ev) {\n    var lastValue = value;\n    var val = ev.target.value;\n    if (!('value' in props)) {\n      setValue(val);\n    }\n    var onChange = props.onChange;\n    if (onChange && val !== lastValue) {\n      onChange(ev);\n    }\n  };\n  var renderGroup = function renderGroup() {\n    var _classNames;\n    var customizePrefixCls = props.prefixCls,\n      _props$className = props.className,\n      className = _props$className === void 0 ? '' : _props$className,\n      options = props.options,\n      _props$buttonStyle = props.buttonStyle,\n      buttonStyle = _props$buttonStyle === void 0 ? 'outline' : _props$buttonStyle,\n      disabled = props.disabled,\n      children = props.children,\n      customizeSize = props.size,\n      style = props.style,\n      id = props.id,\n      onMouseEnter = props.onMouseEnter,\n      onMouseLeave = props.onMouseLeave;\n    var prefixCls = getPrefixCls('radio', customizePrefixCls);\n    var groupPrefixCls = \"\".concat(prefixCls, \"-group\");\n    var childrenToRender = children; // 如果存在 options, 优先使用\n\n    if (options && options.length > 0) {\n      childrenToRender = options.map(function (option) {\n        if (typeof option === 'string' || typeof option === 'number') {\n          // 此处类型自动推导为 string\n          return /*#__PURE__*/React.createElement(Radio, {\n            key: option.toString(),\n            prefixCls: prefixCls,\n            disabled: disabled,\n            value: option,\n            checked: value === option\n          }, option);\n        } // 此处类型自动推导为 { label: string value: string }\n\n        return /*#__PURE__*/React.createElement(Radio, {\n          key: \"radio-group-value-options-\".concat(option.value),\n          prefixCls: prefixCls,\n          disabled: option.disabled || disabled,\n          value: option.value,\n          checked: value === option.value,\n          style: option.style\n        }, option.label);\n      });\n    }\n    var mergedSize = customizeSize || size;\n    var classString = classNames(groupPrefixCls, \"\".concat(groupPrefixCls, \"-\").concat(buttonStyle), (_classNames = {}, _defineProperty(_classNames, \"\".concat(groupPrefixCls, \"-\").concat(mergedSize), mergedSize), _defineProperty(_classNames, \"\".concat(groupPrefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, getDataOrAriaProps(props), {\n      className: classString,\n      style: style,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      id: id,\n      ref: ref\n    }), childrenToRender);\n  };\n  return /*#__PURE__*/React.createElement(RadioGroupContextProvider, {\n    value: {\n      onChange: onRadioChange,\n      value: value,\n      disabled: props.disabled,\n      name: props.name,\n      optionType: props.optionType\n    }\n  }, renderGroup());\n});\nexport default /*#__PURE__*/React.memo(RadioGroup);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "classNames", "useMergedState", "Radio", "ConfigContext", "SizeContext", "RadioGroupContextProvider", "getDataOrAriaProps", "RadioGroup", "forwardRef", "props", "ref", "_React$useContext", "useContext", "getPrefixCls", "direction", "size", "_useMergedState", "defaultValue", "value", "_useMergedState2", "setValue", "onRadioChange", "ev", "lastValue", "val", "target", "onChange", "renderGroup", "_classNames", "customizePrefixCls", "prefixCls", "_props$className", "className", "options", "_props$buttonStyle", "buttonStyle", "disabled", "children", "customizeSize", "style", "id", "onMouseEnter", "onMouseLeave", "groupPrefixCls", "concat", "children<PERSON><PERSON><PERSON><PERSON>", "length", "map", "option", "createElement", "key", "toString", "checked", "label", "mergedSize", "classString", "name", "optionType", "memo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/radio/group.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Radio from './radio';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\nimport { RadioGroupContextProvider } from './context';\nimport getDataOrAriaProps from '../_util/getDataOrAriaProps';\nvar RadioGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var size = React.useContext(SizeContext);\n\n  var _useMergedState = useMergedState(props.defaultValue, {\n    value: props.value\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      value = _useMergedState2[0],\n      setValue = _useMergedState2[1];\n\n  var onRadioChange = function onRadioChange(ev) {\n    var lastValue = value;\n    var val = ev.target.value;\n\n    if (!('value' in props)) {\n      setValue(val);\n    }\n\n    var onChange = props.onChange;\n\n    if (onChange && val !== lastValue) {\n      onChange(ev);\n    }\n  };\n\n  var renderGroup = function renderGroup() {\n    var _classNames;\n\n    var customizePrefixCls = props.prefixCls,\n        _props$className = props.className,\n        className = _props$className === void 0 ? '' : _props$className,\n        options = props.options,\n        _props$buttonStyle = props.buttonStyle,\n        buttonStyle = _props$buttonStyle === void 0 ? 'outline' : _props$buttonStyle,\n        disabled = props.disabled,\n        children = props.children,\n        customizeSize = props.size,\n        style = props.style,\n        id = props.id,\n        onMouseEnter = props.onMouseEnter,\n        onMouseLeave = props.onMouseLeave;\n    var prefixCls = getPrefixCls('radio', customizePrefixCls);\n    var groupPrefixCls = \"\".concat(prefixCls, \"-group\");\n    var childrenToRender = children; // 如果存在 options, 优先使用\n\n    if (options && options.length > 0) {\n      childrenToRender = options.map(function (option) {\n        if (typeof option === 'string' || typeof option === 'number') {\n          // 此处类型自动推导为 string\n          return /*#__PURE__*/React.createElement(Radio, {\n            key: option.toString(),\n            prefixCls: prefixCls,\n            disabled: disabled,\n            value: option,\n            checked: value === option\n          }, option);\n        } // 此处类型自动推导为 { label: string value: string }\n\n\n        return /*#__PURE__*/React.createElement(Radio, {\n          key: \"radio-group-value-options-\".concat(option.value),\n          prefixCls: prefixCls,\n          disabled: option.disabled || disabled,\n          value: option.value,\n          checked: value === option.value,\n          style: option.style\n        }, option.label);\n      });\n    }\n\n    var mergedSize = customizeSize || size;\n    var classString = classNames(groupPrefixCls, \"\".concat(groupPrefixCls, \"-\").concat(buttonStyle), (_classNames = {}, _defineProperty(_classNames, \"\".concat(groupPrefixCls, \"-\").concat(mergedSize), mergedSize), _defineProperty(_classNames, \"\".concat(groupPrefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, getDataOrAriaProps(props), {\n      className: classString,\n      style: style,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      id: id,\n      ref: ref\n    }), childrenToRender);\n  };\n\n  return /*#__PURE__*/React.createElement(RadioGroupContextProvider, {\n    value: {\n      onChange: onRadioChange,\n      value: value,\n      disabled: props.disabled,\n      name: props.name,\n      optionType: props.optionType\n    }\n  }, renderGroup());\n});\nexport default /*#__PURE__*/React.memo(RadioGroup);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,SAASC,yBAAyB,QAAQ,WAAW;AACrD,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,IAAIC,UAAU,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACT,aAAa,CAAC;IACnDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,IAAI,GAAGhB,KAAK,CAACa,UAAU,CAACR,WAAW,CAAC;EAExC,IAAIY,eAAe,GAAGf,cAAc,CAACQ,KAAK,CAACQ,YAAY,EAAE;MACvDC,KAAK,EAAET,KAAK,CAACS;IACf,CAAC,CAAC;IACEC,gBAAgB,GAAGrB,cAAc,CAACkB,eAAe,EAAE,CAAC,CAAC;IACrDE,KAAK,GAAGC,gBAAgB,CAAC,CAAC,CAAC;IAC3BC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,EAAE,EAAE;IAC7C,IAAIC,SAAS,GAAGL,KAAK;IACrB,IAAIM,GAAG,GAAGF,EAAE,CAACG,MAAM,CAACP,KAAK;IAEzB,IAAI,EAAE,OAAO,IAAIT,KAAK,CAAC,EAAE;MACvBW,QAAQ,CAACI,GAAG,CAAC;IACf;IAEA,IAAIE,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IAE7B,IAAIA,QAAQ,IAAIF,GAAG,KAAKD,SAAS,EAAE;MACjCG,QAAQ,CAACJ,EAAE,CAAC;IACd;EACF,CAAC;EAED,IAAIK,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIC,WAAW;IAEf,IAAIC,kBAAkB,GAAGpB,KAAK,CAACqB,SAAS;MACpCC,gBAAgB,GAAGtB,KAAK,CAACuB,SAAS;MAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;MAC/DE,OAAO,GAAGxB,KAAK,CAACwB,OAAO;MACvBC,kBAAkB,GAAGzB,KAAK,CAAC0B,WAAW;MACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,kBAAkB;MAC5EE,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;MACzBC,QAAQ,GAAG5B,KAAK,CAAC4B,QAAQ;MACzBC,aAAa,GAAG7B,KAAK,CAACM,IAAI;MAC1BwB,KAAK,GAAG9B,KAAK,CAAC8B,KAAK;MACnBC,EAAE,GAAG/B,KAAK,CAAC+B,EAAE;MACbC,YAAY,GAAGhC,KAAK,CAACgC,YAAY;MACjCC,YAAY,GAAGjC,KAAK,CAACiC,YAAY;IACrC,IAAIZ,SAAS,GAAGjB,YAAY,CAAC,OAAO,EAAEgB,kBAAkB,CAAC;IACzD,IAAIc,cAAc,GAAG,EAAE,CAACC,MAAM,CAACd,SAAS,EAAE,QAAQ,CAAC;IACnD,IAAIe,gBAAgB,GAAGR,QAAQ,CAAC,CAAC;;IAEjC,IAAIJ,OAAO,IAAIA,OAAO,CAACa,MAAM,GAAG,CAAC,EAAE;MACjCD,gBAAgB,GAAGZ,OAAO,CAACc,GAAG,CAAC,UAAUC,MAAM,EAAE;QAC/C,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC5D;UACA,OAAO,aAAajD,KAAK,CAACkD,aAAa,CAAC/C,KAAK,EAAE;YAC7CgD,GAAG,EAAEF,MAAM,CAACG,QAAQ,CAAC,CAAC;YACtBrB,SAAS,EAAEA,SAAS;YACpBM,QAAQ,EAAEA,QAAQ;YAClBlB,KAAK,EAAE8B,MAAM;YACbI,OAAO,EAAElC,KAAK,KAAK8B;UACrB,CAAC,EAAEA,MAAM,CAAC;QACZ,CAAC,CAAC;;QAGF,OAAO,aAAajD,KAAK,CAACkD,aAAa,CAAC/C,KAAK,EAAE;UAC7CgD,GAAG,EAAE,4BAA4B,CAACN,MAAM,CAACI,MAAM,CAAC9B,KAAK,CAAC;UACtDY,SAAS,EAAEA,SAAS;UACpBM,QAAQ,EAAEY,MAAM,CAACZ,QAAQ,IAAIA,QAAQ;UACrClB,KAAK,EAAE8B,MAAM,CAAC9B,KAAK;UACnBkC,OAAO,EAAElC,KAAK,KAAK8B,MAAM,CAAC9B,KAAK;UAC/BqB,KAAK,EAAES,MAAM,CAACT;QAChB,CAAC,EAAES,MAAM,CAACK,KAAK,CAAC;MAClB,CAAC,CAAC;IACJ;IAEA,IAAIC,UAAU,GAAGhB,aAAa,IAAIvB,IAAI;IACtC,IAAIwC,WAAW,GAAGvD,UAAU,CAAC2C,cAAc,EAAE,EAAE,CAACC,MAAM,CAACD,cAAc,EAAE,GAAG,CAAC,CAACC,MAAM,CAACT,WAAW,CAAC,GAAGP,WAAW,GAAG,CAAC,CAAC,EAAE/B,eAAe,CAAC+B,WAAW,EAAE,EAAE,CAACgB,MAAM,CAACD,cAAc,EAAE,GAAG,CAAC,CAACC,MAAM,CAACU,UAAU,CAAC,EAAEA,UAAU,CAAC,EAAEzD,eAAe,CAAC+B,WAAW,EAAE,EAAE,CAACgB,MAAM,CAACD,cAAc,EAAE,MAAM,CAAC,EAAE7B,SAAS,KAAK,KAAK,CAAC,EAAEc,WAAW,GAAGI,SAAS,CAAC;IAC/T,OAAO,aAAajC,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAEU,kBAAkB,CAACG,KAAK,CAAC,EAAE;MACrFuB,SAAS,EAAEuB,WAAW;MACtBhB,KAAK,EAAEA,KAAK;MACZE,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BF,EAAE,EAAEA,EAAE;MACN9B,GAAG,EAAEA;IACP,CAAC,CAAC,EAAEmC,gBAAgB,CAAC;EACvB,CAAC;EAED,OAAO,aAAa9C,KAAK,CAACkD,aAAa,CAAC5C,yBAAyB,EAAE;IACjEa,KAAK,EAAE;MACLQ,QAAQ,EAAEL,aAAa;MACvBH,KAAK,EAAEA,KAAK;MACZkB,QAAQ,EAAE3B,KAAK,CAAC2B,QAAQ;MACxBoB,IAAI,EAAE/C,KAAK,CAAC+C,IAAI;MAChBC,UAAU,EAAEhD,KAAK,CAACgD;IACpB;EACF,CAAC,EAAE9B,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC;AACF,eAAe,aAAa5B,KAAK,CAAC2D,IAAI,CAACnD,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}