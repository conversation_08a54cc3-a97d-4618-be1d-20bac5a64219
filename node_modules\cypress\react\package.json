{"name": "@cypress/react", "version": "0.0.0-development", "description": "Test React components using Cypress", "main": "dist/cypress-react.cjs.js", "scripts": {"build": "rimraf dist && rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "build-prod": "yarn build", "cy:open": "node ../../scripts/cypress.js open --component", "cy:open:debug": "node --inspect-brk ../../scripts/start.js --component-testing --run-project ${PWD}", "cy:run": "node ../../scripts/cypress.js run --component", "cy:run:debug": "node --inspect-brk ../../scripts/start.js --component-testing --run-project ${PWD}", "test": "yarn cy:run", "watch": "yarn build --watch --watch.exclude ./dist/**/*"}, "devDependencies": {"@cypress/mount-utils": "0.0.0-development", "@types/semver": "7.3.9", "@vitejs/plugin-react": "1.3.1", "axios": "0.21.2", "cypress": "0.0.0-development", "prop-types": "15.7.2", "react": "16.8.6", "react-dom": "16.8.6", "react-router": "6.0.0-alpha.1", "react-router-dom": "6.0.0-alpha.1", "semver": "^7.3.2", "typescript": "^4.7.4", "vite": "3.1.0", "vite-plugin-require-transform": "1.0.3"}, "peerDependencies": {"@types/react": "^16.9.16 || ^17.0.0", "cypress": "*", "react": "^=16.x || ^=17.x", "react-dom": "^=16.x || ^=17.x"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/react/#readme", "author": "<PERSON><PERSON><PERSON> <gleb.bah<PERSON><PERSON>@gmail.com>", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Freact&template=1-bug-report.md&title=", "keywords": ["react", "cypress", "cypress-io", "test", "testing"], "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "social": "@dmtrKovalenko"}, {"name": "<PERSON>", "social": "@brian-mann"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "social": "@elevatebart"}, {"name": "<PERSON><PERSON><PERSON>", "social": "@lmiller1990"}, {"name": "<PERSON>", "social": "@_<PERSON><PERSON><PERSON><PERSON>"}], "unpkg": "dist/cypress-react.browser.js", "module": "dist/cypress-react.esm-bundler.js", "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public"}, "standard": {"globals": ["Cypress", "cy", "expect"]}}