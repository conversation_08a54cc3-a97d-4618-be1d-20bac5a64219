{"ast": null, "code": "import i18n from 'i18next';\nimport Backend from 'i18next-http-backend';\nimport LanguageDetector from 'i18next-browser-languagedetector';\nimport { initReactI18next } from 'react-i18next';\n// don't want to use this?\n// have a look at the Quick start guide \n// for passing in lng and translations on init\nconst Lingue = ['it', 'en'];\ni18n\n// load translation using http -> see /public/locales (i.e. https://github.com/i18next/react-i18next/tree/master/example/react/public/locales)\n// learn more: https://github.com/i18next/i18next-http-backend\n// want your translations to be loaded from a professional CDN? => https://github.com/locize/react-tutorial#step-2---use-the-locize-cdn\n.use(Backend)\n// detect user language\n// learn more: https://github.com/i18next/i18next-browser-languageDetector\n.use(LanguageDetector)\n// pass the i18n instance to react-i18next.\n.use(initReactI18next)\n// init i18next\n// for all options read: https://www.i18next.com/overview/configuration-options\n.init({\n  supportedLngs: ['it', 'en'],\n  fallbackLng: 'it',\n  debug: false,\n  detection: {\n    order: ['path', 'cookie', 'htmlTag'],\n    caches: ['cookie']\n  },\n  whitelist: Lingue\n  /* interpolation: {\n    escapeValue: false, // not needed for react as it escapes by default\n  } */\n});\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "Backend", "LanguageDetector", "initReactI18next", "Lingue", "use", "init", "supportedLngs", "fallbackLng", "debug", "detection", "order", "caches", "whitelist"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/traduttore/i18n.jsx"], "sourcesContent": ["import i18n from 'i18next';\nimport Backend from 'i18next-http-backend';\nimport LanguageDetector from 'i18next-browser-languagedetector';\nimport { initReactI18next } from 'react-i18next';\n// don't want to use this?\n// have a look at the Quick start guide \n// for passing in lng and translations on init\nconst Lingue = ['it', 'en'];\ni18n\n  // load translation using http -> see /public/locales (i.e. https://github.com/i18next/react-i18next/tree/master/example/react/public/locales)\n  // learn more: https://github.com/i18next/i18next-http-backend\n  // want your translations to be loaded from a professional CDN? => https://github.com/locize/react-tutorial#step-2---use-the-locize-cdn\n  .use(Backend)\n  // detect user language\n  // learn more: https://github.com/i18next/i18next-browser-languageDetector\n  .use(LanguageDetector)\n  // pass the i18n instance to react-i18next.\n  .use(initReactI18next)\n  // init i18next\n  // for all options read: https://www.i18next.com/overview/configuration-options\n  .init({\n    supportedLngs: ['it', 'en'],\n    fallbackLng: 'it',\n    debug: false,\n    detection: {\n      order: ['path', 'cookie', 'htmlTag'],\n      caches: ['cookie'],\n    },\n    whitelist: Lingue,\n    /* interpolation: {\n      escapeValue: false, // not needed for react as it escapes by default\n    } */\n  });\n\nexport default i18n;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,gBAAgB,QAAQ,eAAe;AAChD;AACA;AACA;AACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3BJ;AACE;AACA;AACA;AAAA,CACCK,GAAG,CAACJ,OAAO;AACZ;AACA;AAAA,CACCI,GAAG,CAACH,gBAAgB;AACrB;AAAA,CACCG,GAAG,CAACF,gBAAgB;AACrB;AACA;AAAA,CACCG,IAAI,CAAC;EACJC,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3BC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,KAAK;EACZC,SAAS,EAAE;IACTC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;IACpCC,MAAM,EAAE,CAAC,QAAQ;EACnB,CAAC;EACDC,SAAS,EAAET;EACX;AACJ;AACA;AACE,CAAC,CAAC;AAEJ,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}