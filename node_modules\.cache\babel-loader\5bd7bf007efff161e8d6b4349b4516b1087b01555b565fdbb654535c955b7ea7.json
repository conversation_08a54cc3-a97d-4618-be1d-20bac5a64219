{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { responsiveArray } from '../_util/responsiveObserve';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport SizeContext from './SizeContext';\nvar InternalAvatar = function InternalAvatar(props, ref) {\n  var _classNames, _classNames2;\n  var groupSize = React.useContext(SizeContext);\n  var _React$useState = React.useState(1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    scale = _React$useState2[0],\n    setScale = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  var _React$useState5 = React.useState(true),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isImgExist = _React$useState6[0],\n    setIsImgExist = _React$useState6[1];\n  var avatarNodeRef = React.useRef();\n  var avatarChildrenRef = React.useRef();\n  var avatarNodeMergeRef = composeRef(ref, avatarNodeRef);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var setScaleParam = function setScaleParam() {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    var childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n\n    var nodeWidth = avatarNodeRef.current.offsetWidth; // denominator is 0 is no meaning\n\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      var _props$gap = props.gap,\n        gap = _props$gap === void 0 ? 4 : _props$gap;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n  React.useEffect(function () {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(function () {\n    setScaleParam();\n  }, [props.gap]);\n  var handleImgLoadError = function handleImgLoadError() {\n    var onError = props.onError;\n    var errorFlag = onError ? onError() : undefined;\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  var customizePrefixCls = props.prefixCls,\n    shape = props.shape,\n    customSize = props.size,\n    src = props.src,\n    srcSet = props.srcSet,\n    icon = props.icon,\n    className = props.className,\n    alt = props.alt,\n    draggable = props.draggable,\n    children = props.children,\n    crossOrigin = props.crossOrigin,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  var size = customSize === 'default' ? groupSize : customSize;\n  var needResponsive = Object.keys(_typeof(size) === 'object' ? size || {} : {}).some(function (key) {\n    return ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key);\n  });\n  var screens = useBreakpoint(needResponsive);\n  var responsiveSizeStyle = React.useMemo(function () {\n    if (_typeof(size) !== 'object') {\n      return {};\n    }\n    var currentBreakpoint = responsiveArray.find(function (screen) {\n      return screens[screen];\n    });\n    var currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      lineHeight: \"\".concat(currentSize, \"px\"),\n      fontSize: icon ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  devWarning(!(typeof icon === 'string' && icon.length > 2), 'Avatar', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\"));\n  var prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  var classString = classNames(prefixCls, sizeCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-\").concat(shape), !!shape), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-image\"), hasImageElement || src && isImgExist), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-icon\"), !!icon), _classNames2), className);\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\"),\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  var childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    var transformString = \"scale(\".concat(scale, \") translateX(-50%)\");\n    var childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    var sizeChildrenStyle = typeof size === 'number' ? {\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      ref: function ref(node) {\n        avatarChildrenRef.current = node;\n      },\n      style: _extends(_extends({}, sizeChildrenStyle), childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      style: {\n        opacity: 0\n      },\n      ref: function ref(node) {\n        avatarChildrenRef.current = node;\n      }\n    }, children);\n  } // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n\n  delete others.onError;\n  delete others.gap;\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, others, {\n    style: _extends(_extends(_extends({}, sizeStyle), responsiveSizeStyle), others.style),\n    className: classString,\n    ref: avatarNodeMergeRef\n  }), childrenToRender);\n};\nvar Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nAvatar.displayName = 'Avatar';\nAvatar.defaultProps = {\n  shape: 'circle',\n  size: 'default'\n};\nexport default Avatar;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ResizeObserver", "composeRef", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "responsiveArray", "useBreakpoint", "SizeContext", "InternalAvatar", "props", "ref", "_classNames", "_classNames2", "groupSize", "useContext", "_React$useState", "useState", "_React$useState2", "scale", "setScale", "_React$useState3", "_React$useState4", "mounted", "setMounted", "_React$useState5", "_React$useState6", "isImgExist", "setIsImgExist", "avatarNodeRef", "useRef", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarNodeMergeRef", "_React$useContext", "getPrefixCls", "setScaleParam", "current", "children<PERSON><PERSON>th", "offsetWidth", "nodeWidth", "_props$gap", "gap", "useEffect", "src", "handleImgLoadError", "onError", "errorFlag", "undefined", "customizePrefixCls", "prefixCls", "shape", "customSize", "size", "srcSet", "icon", "className", "alt", "draggable", "children", "crossOrigin", "others", "needResponsive", "keys", "some", "key", "includes", "screens", "responsiveSizeStyle", "useMemo", "currentBreakpoint", "find", "screen", "currentSize", "width", "height", "lineHeight", "concat", "fontSize", "sizeCls", "hasImageElement", "isValidElement", "classString", "sizeStyle", "children<PERSON><PERSON><PERSON><PERSON>", "createElement", "transformString", "childrenStyle", "msTransform", "WebkitTransform", "transform", "sizeChildrenStyle", "onResize", "node", "style", "opacity", "Avatar", "forwardRef", "displayName", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/avatar/avatar.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nimport { responsiveArray } from '../_util/responsiveObserve';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport SizeContext from './SizeContext';\n\nvar InternalAvatar = function InternalAvatar(props, ref) {\n  var _classNames, _classNames2;\n\n  var groupSize = React.useContext(SizeContext);\n\n  var _React$useState = React.useState(1),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      scale = _React$useState2[0],\n      setScale = _React$useState2[1];\n\n  var _React$useState3 = React.useState(false),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      mounted = _React$useState4[0],\n      setMounted = _React$useState4[1];\n\n  var _React$useState5 = React.useState(true),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      isImgExist = _React$useState6[0],\n      setIsImgExist = _React$useState6[1];\n\n  var avatarNodeRef = React.useRef();\n  var avatarChildrenRef = React.useRef();\n  var avatarNodeMergeRef = composeRef(ref, avatarNodeRef);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var setScaleParam = function setScaleParam() {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n\n    var childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n\n    var nodeWidth = avatarNodeRef.current.offsetWidth; // denominator is 0 is no meaning\n\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      var _props$gap = props.gap,\n          gap = _props$gap === void 0 ? 4 : _props$gap;\n\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n  React.useEffect(function () {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(function () {\n    setScaleParam();\n  }, [props.gap]);\n\n  var handleImgLoadError = function handleImgLoadError() {\n    var onError = props.onError;\n    var errorFlag = onError ? onError() : undefined;\n\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n\n  var customizePrefixCls = props.prefixCls,\n      shape = props.shape,\n      customSize = props.size,\n      src = props.src,\n      srcSet = props.srcSet,\n      icon = props.icon,\n      className = props.className,\n      alt = props.alt,\n      draggable = props.draggable,\n      children = props.children,\n      crossOrigin = props.crossOrigin,\n      others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n\n  var size = customSize === 'default' ? groupSize : customSize;\n  var needResponsive = Object.keys(_typeof(size) === 'object' ? size || {} : {}).some(function (key) {\n    return ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key);\n  });\n  var screens = useBreakpoint(needResponsive);\n  var responsiveSizeStyle = React.useMemo(function () {\n    if (_typeof(size) !== 'object') {\n      return {};\n    }\n\n    var currentBreakpoint = responsiveArray.find(function (screen) {\n      return screens[screen];\n    });\n    var currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      lineHeight: \"\".concat(currentSize, \"px\"),\n      fontSize: icon ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  devWarning(!(typeof icon === 'string' && icon.length > 2), 'Avatar', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\"));\n  var prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  var classString = classNames(prefixCls, sizeCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-\").concat(shape), !!shape), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-image\"), hasImageElement || src && isImgExist), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-icon\"), !!icon), _classNames2), className);\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\"),\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  var childrenToRender;\n\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    var transformString = \"scale(\".concat(scale, \") translateX(-50%)\");\n    var childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    var sizeChildrenStyle = typeof size === 'number' ? {\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      ref: function ref(node) {\n        avatarChildrenRef.current = node;\n      },\n      style: _extends(_extends({}, sizeChildrenStyle), childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      style: {\n        opacity: 0\n      },\n      ref: function ref(node) {\n        avatarChildrenRef.current = node;\n      }\n    }, children);\n  } // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n\n\n  delete others.onError;\n  delete others.gap;\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, others, {\n    style: _extends(_extends(_extends({}, sizeStyle), responsiveSizeStyle), others.style),\n    className: classString,\n    ref: avatarNodeMergeRef\n  }), childrenToRender);\n};\n\nvar Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nAvatar.displayName = 'Avatar';\nAvatar.defaultProps = {\n  shape: 'circle',\n  size: 'default'\n};\nexport default Avatar;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,WAAW,MAAM,eAAe;AAEvC,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,IAAIC,WAAW,EAAEC,YAAY;EAE7B,IAAIC,SAAS,GAAGd,KAAK,CAACe,UAAU,CAACP,WAAW,CAAC;EAE7C,IAAIQ,eAAe,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC;IACnCC,gBAAgB,GAAGjC,cAAc,CAAC+B,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIG,gBAAgB,GAAGrB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;IACxCK,gBAAgB,GAAGrC,cAAc,CAACoC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIG,gBAAgB,GAAGzB,KAAK,CAACiB,QAAQ,CAAC,IAAI,CAAC;IACvCS,gBAAgB,GAAGzC,cAAc,CAACwC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEvC,IAAIG,aAAa,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,CAAC;EAClC,IAAIC,iBAAiB,GAAG/B,KAAK,CAAC8B,MAAM,CAAC,CAAC;EACtC,IAAIE,kBAAkB,GAAG7B,UAAU,CAACQ,GAAG,EAAEkB,aAAa,CAAC;EAEvD,IAAII,iBAAiB,GAAGjC,KAAK,CAACe,UAAU,CAACX,aAAa,CAAC;IACnD8B,YAAY,GAAGD,iBAAiB,CAACC,YAAY;EAEjD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI,CAACJ,iBAAiB,CAACK,OAAO,IAAI,CAACP,aAAa,CAACO,OAAO,EAAE;MACxD;IACF;IAEA,IAAIC,aAAa,GAAGN,iBAAiB,CAACK,OAAO,CAACE,WAAW,CAAC,CAAC;;IAE3D,IAAIC,SAAS,GAAGV,aAAa,CAACO,OAAO,CAACE,WAAW,CAAC,CAAC;;IAEnD,IAAID,aAAa,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;MAC1C,IAAIC,UAAU,GAAG9B,KAAK,CAAC+B,GAAG;QACtBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;MAEhD,IAAIC,GAAG,GAAG,CAAC,GAAGF,SAAS,EAAE;QACvBnB,QAAQ,CAACmB,SAAS,GAAGE,GAAG,GAAG,CAAC,GAAGJ,aAAa,GAAG,CAACE,SAAS,GAAGE,GAAG,GAAG,CAAC,IAAIJ,aAAa,GAAG,CAAC,CAAC;MAC3F;IACF;EACF,CAAC;EAEDrC,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1BlB,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNxB,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1Bd,aAAa,CAAC,IAAI,CAAC;IACnBR,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAACV,KAAK,CAACiC,GAAG,CAAC,CAAC;EACf3C,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1BP,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACzB,KAAK,CAAC+B,GAAG,CAAC,CAAC;EAEf,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIC,OAAO,GAAGnC,KAAK,CAACmC,OAAO;IAC3B,IAAIC,SAAS,GAAGD,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAGE,SAAS;IAE/C,IAAID,SAAS,KAAK,KAAK,EAAE;MACvBlB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAIoB,kBAAkB,GAAGtC,KAAK,CAACuC,SAAS;IACpCC,KAAK,GAAGxC,KAAK,CAACwC,KAAK;IACnBC,UAAU,GAAGzC,KAAK,CAAC0C,IAAI;IACvBT,GAAG,GAAGjC,KAAK,CAACiC,GAAG;IACfU,MAAM,GAAG3C,KAAK,CAAC2C,MAAM;IACrBC,IAAI,GAAG5C,KAAK,CAAC4C,IAAI;IACjBC,SAAS,GAAG7C,KAAK,CAAC6C,SAAS;IAC3BC,GAAG,GAAG9C,KAAK,CAAC8C,GAAG;IACfC,SAAS,GAAG/C,KAAK,CAAC+C,SAAS;IAC3BC,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ;IACzBC,WAAW,GAAGjD,KAAK,CAACiD,WAAW;IAC/BC,MAAM,GAAG1E,MAAM,CAACwB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;EAE/I,IAAI0C,IAAI,GAAGD,UAAU,KAAK,SAAS,GAAGrC,SAAS,GAAGqC,UAAU;EAC5D,IAAIU,cAAc,GAAGtE,MAAM,CAACuE,IAAI,CAAC9E,OAAO,CAACoE,IAAI,CAAC,KAAK,QAAQ,GAAGA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACW,IAAI,CAAC,UAAUC,GAAG,EAAE;IACjG,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC;EAC5D,CAAC,CAAC;EACF,IAAIE,OAAO,GAAG3D,aAAa,CAACsD,cAAc,CAAC;EAC3C,IAAIM,mBAAmB,GAAGnE,KAAK,CAACoE,OAAO,CAAC,YAAY;IAClD,IAAIpF,OAAO,CAACoE,IAAI,CAAC,KAAK,QAAQ,EAAE;MAC9B,OAAO,CAAC,CAAC;IACX;IAEA,IAAIiB,iBAAiB,GAAG/D,eAAe,CAACgE,IAAI,CAAC,UAAUC,MAAM,EAAE;MAC7D,OAAOL,OAAO,CAACK,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,IAAIC,WAAW,GAAGpB,IAAI,CAACiB,iBAAiB,CAAC;IACzC,OAAOG,WAAW,GAAG;MACnBC,KAAK,EAAED,WAAW;MAClBE,MAAM,EAAEF,WAAW;MACnBG,UAAU,EAAE,EAAE,CAACC,MAAM,CAACJ,WAAW,EAAE,IAAI,CAAC;MACxCK,QAAQ,EAAEvB,IAAI,GAAGkB,WAAW,GAAG,CAAC,GAAG;IACrC,CAAC,GAAG,CAAC,CAAC;EACR,CAAC,EAAE,CAACN,OAAO,EAAEd,IAAI,CAAC,CAAC;EACnB/C,UAAU,CAAC,EAAE,OAAOiD,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACxD,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,0EAA0E,CAAC8E,MAAM,CAACtB,IAAI,EAAE,yCAAyC,CAAC,CAAC;EACxM,IAAIL,SAAS,GAAGf,YAAY,CAAC,QAAQ,EAAEc,kBAAkB,CAAC;EAC1D,IAAI8B,OAAO,GAAG7E,UAAU,EAAEW,WAAW,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAACgE,MAAM,CAAC3B,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAErE,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAACgE,MAAM,CAAC3B,SAAS,EAAE,KAAK,CAAC,EAAEG,IAAI,KAAK,OAAO,CAAC,EAAExC,WAAW,CAAC,CAAC;EACnN,IAAImE,eAAe,GAAG,aAAa/E,KAAK,CAACgF,cAAc,CAACrC,GAAG,CAAC;EAC5D,IAAIsC,WAAW,GAAGhF,UAAU,CAACgD,SAAS,EAAE6B,OAAO,GAAGjE,YAAY,GAAG,CAAC,CAAC,EAAE9B,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC+D,MAAM,CAAC3B,SAAS,EAAE,GAAG,CAAC,CAAC2B,MAAM,CAAC1B,KAAK,CAAC,EAAE,CAAC,CAACA,KAAK,CAAC,EAAEnE,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC+D,MAAM,CAAC3B,SAAS,EAAE,QAAQ,CAAC,EAAE8B,eAAe,IAAIpC,GAAG,IAAIhB,UAAU,CAAC,EAAE5C,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC+D,MAAM,CAAC3B,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAACK,IAAI,CAAC,EAAEzC,YAAY,GAAG0C,SAAS,CAAC;EAC1V,IAAI2B,SAAS,GAAG,OAAO9B,IAAI,KAAK,QAAQ,GAAG;IACzCqB,KAAK,EAAErB,IAAI;IACXsB,MAAM,EAAEtB,IAAI;IACZuB,UAAU,EAAE,EAAE,CAACC,MAAM,CAACxB,IAAI,EAAE,IAAI,CAAC;IACjCyB,QAAQ,EAAEvB,IAAI,GAAGF,IAAI,GAAG,CAAC,GAAG;EAC9B,CAAC,GAAG,CAAC,CAAC;EACN,IAAI+B,gBAAgB;EAEpB,IAAI,OAAOxC,GAAG,KAAK,QAAQ,IAAIhB,UAAU,EAAE;IACzCwD,gBAAgB,GAAG,aAAanF,KAAK,CAACoF,aAAa,CAAC,KAAK,EAAE;MACzDzC,GAAG,EAAEA,GAAG;MACRc,SAAS,EAAEA,SAAS;MACpBJ,MAAM,EAAEA,MAAM;MACdR,OAAO,EAAED,kBAAkB;MAC3BY,GAAG,EAAEA,GAAG;MACRG,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIoB,eAAe,EAAE;IAC1BI,gBAAgB,GAAGxC,GAAG;EACxB,CAAC,MAAM,IAAIW,IAAI,EAAE;IACf6B,gBAAgB,GAAG7B,IAAI;EACzB,CAAC,MAAM,IAAI/B,OAAO,IAAIJ,KAAK,KAAK,CAAC,EAAE;IACjC,IAAIkE,eAAe,GAAG,QAAQ,CAACT,MAAM,CAACzD,KAAK,EAAE,oBAAoB,CAAC;IAClE,IAAImE,aAAa,GAAG;MAClBC,WAAW,EAAEF,eAAe;MAC5BG,eAAe,EAAEH,eAAe;MAChCI,SAAS,EAAEJ;IACb,CAAC;IACD,IAAIK,iBAAiB,GAAG,OAAOtC,IAAI,KAAK,QAAQ,GAAG;MACjDuB,UAAU,EAAE,EAAE,CAACC,MAAM,CAACxB,IAAI,EAAE,IAAI;IAClC,CAAC,GAAG,CAAC,CAAC;IACN+B,gBAAgB,GAAG,aAAanF,KAAK,CAACoF,aAAa,CAAClF,cAAc,EAAE;MAClEyF,QAAQ,EAAExD;IACZ,CAAC,EAAE,aAAanC,KAAK,CAACoF,aAAa,CAAC,MAAM,EAAE;MAC1C7B,SAAS,EAAE,EAAE,CAACqB,MAAM,CAAC3B,SAAS,EAAE,SAAS,CAAC;MAC1CtC,GAAG,EAAE,SAASA,GAAGA,CAACiF,IAAI,EAAE;QACtB7D,iBAAiB,CAACK,OAAO,GAAGwD,IAAI;MAClC,CAAC;MACDC,KAAK,EAAE/G,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4G,iBAAiB,CAAC,EAAEJ,aAAa;IAChE,CAAC,EAAE5B,QAAQ,CAAC,CAAC;EACf,CAAC,MAAM;IACLyB,gBAAgB,GAAG,aAAanF,KAAK,CAACoF,aAAa,CAAC,MAAM,EAAE;MAC1D7B,SAAS,EAAE,EAAE,CAACqB,MAAM,CAAC3B,SAAS,EAAE,SAAS,CAAC;MAC1C4C,KAAK,EAAE;QACLC,OAAO,EAAE;MACX,CAAC;MACDnF,GAAG,EAAE,SAASA,GAAGA,CAACiF,IAAI,EAAE;QACtB7D,iBAAiB,CAACK,OAAO,GAAGwD,IAAI;MAClC;IACF,CAAC,EAAElC,QAAQ,CAAC;EACd,CAAC,CAAC;EACF;;EAGA,OAAOE,MAAM,CAACf,OAAO;EACrB,OAAOe,MAAM,CAACnB,GAAG;EACjB,OAAO,aAAazC,KAAK,CAACoF,aAAa,CAAC,MAAM,EAAEtG,QAAQ,CAAC,CAAC,CAAC,EAAE8E,MAAM,EAAE;IACnEiC,KAAK,EAAE/G,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,SAAS,CAAC,EAAEf,mBAAmB,CAAC,EAAEP,MAAM,CAACiC,KAAK,CAAC;IACrFtC,SAAS,EAAE0B,WAAW;IACtBtE,GAAG,EAAEqB;EACP,CAAC,CAAC,EAAEmD,gBAAgB,CAAC;AACvB,CAAC;AAED,IAAIY,MAAM,GAAG,aAAa/F,KAAK,CAACgG,UAAU,CAACvF,cAAc,CAAC;AAC1DsF,MAAM,CAACE,WAAW,GAAG,QAAQ;AAC7BF,MAAM,CAACG,YAAY,GAAG;EACpBhD,KAAK,EAAE,QAAQ;EACfE,IAAI,EAAE;AACR,CAAC;AACD,eAAe2C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}