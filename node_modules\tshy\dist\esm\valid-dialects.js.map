{"version": 3, "file": "valid-dialects.js", "sourceRoot": "", "sources": ["../../src/valid-dialects.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,WAAW,CAAA;AAG5B,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,CAAM,EAAgB,EAAE,CAChD,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,KAAK,CAAA;AAEjC,eAAe,CACb,CAAM,EAC2C,EAAE;IACnD,IACE,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,MAAM;QACR,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAC3B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CACF,oEAAoE;QAClE,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC9B,CAAA;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAA", "sourcesContent": ["import fail from './fail.js'\nimport { Dialect, TshyConfig } from './types.js'\n\nexport const isDialect = (d: any): d is Dialect =>\n  d === 'commonjs' || d === 'esm'\n\nexport default (\n  d: any,\n): d is Exclude<TshyConfig['dialects'], undefined> => {\n  if (\n    !!d &&\n    Array.isArray(d) &&\n    d.length &&\n    !d.some(d => !isDialect(d))\n  ) {\n    return true\n  }\n\n  fail(\n    `tshy.dialects must be an array including \"esm\" and/or \"commonjs\", ` +\n      `got: ${JSON.stringify(d)}`,\n  )\n  return process.exit(1)\n}\n"]}