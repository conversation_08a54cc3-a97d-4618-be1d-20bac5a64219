{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MutatingDots = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar MutatingDots = function MutatingDots(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    id: \"goo-loader\",\n    width: props.width,\n    height: props.height,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"filter\", {\n    id: \"fancy-goo\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"feGaussianBlur\", {\n    \"in\": \"SourceGraphic\",\n    stdDeviation: \"6\",\n    result: \"blur\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"feColorMatrix\", {\n    \"in\": \"blur\",\n    mode: \"matrix\",\n    values: \"1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9\",\n    result: \"goo\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"feComposite\", {\n    \"in\": \"SourceGraphic\",\n    in2: \"goo\",\n    operator: \"atop\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    filter: \"url(#fancy-goo)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    id: \"mainAnim\",\n    attributeName: \"transform\",\n    attributeType: \"XML\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"359 50 50\",\n    dur: \"1.2s\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"50%\",\n    cy: \"40\",\n    r: props.radius,\n    fill: props.color\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    id: \"cAnim1\",\n    attributeType: \"XML\",\n    attributeName: \"cy\",\n    dur: \"0.6s\",\n    begin: \"0;cAnim1.end+0.2s\",\n    calcMode: \"spline\",\n    values: \"40;20;40\",\n    keyTimes: \"0;0.3;1\",\n    keySplines: \"0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"50%\",\n    cy: \"60\",\n    r: props.radius,\n    fill: props.secondaryColor\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    id: \"cAnim2\",\n    attributeType: \"XML\",\n    attributeName: \"cy\",\n    dur: \"0.6s\",\n    begin: \"0.4s;cAnim2.end+0.2s\",\n    calcMode: \"spline\",\n    values: \"60;80;60\",\n    keyTimes: \"0;0.3;1\",\n    keySplines: \"0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1\"\n  }))));\n};\nexports.MutatingDots = MutatingDots;\nMutatingDots.propTypes = {\n  width: _propTypes[\"default\"].number,\n  secondaryColor: _propTypes[\"default\"].string,\n  height: _propTypes[\"default\"].number,\n  color: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number,\n  label: _propTypes[\"default\"].string\n};\nMutatingDots.defaultProps = {\n  width: 80,\n  height: 90,\n  color: \"green\",\n  radius: 11,\n  secondaryColor: \"green\",\n  label: \"audio-loading\"\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "MutatingDots", "_react", "_interopRequireDefault", "require", "_propTypes", "obj", "__esModule", "props", "createElement", "id", "width", "height", "label", "stdDeviation", "result", "mode", "values", "in2", "operator", "filter", "attributeName", "attributeType", "type", "from", "to", "dur", "repeatCount", "cx", "cy", "r", "radius", "fill", "color", "begin", "calcMode", "keyTimes", "keySplines", "secondaryColor", "propTypes", "number", "string", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-loader-spinner/dist/loader/MutatingDots.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MutatingDots = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar MutatingDots = function MutatingDots(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"svg\", {\n    id: \"goo-loader\",\n    width: props.width,\n    height: props.height,\n    \"aria-label\": props.label\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"filter\", {\n    id: \"fancy-goo\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"feGaussianBlur\", {\n    \"in\": \"SourceGraphic\",\n    stdDeviation: \"6\",\n    result: \"blur\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"feColorMatrix\", {\n    \"in\": \"blur\",\n    mode: \"matrix\",\n    values: \"1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9\",\n    result: \"goo\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"feComposite\", {\n    \"in\": \"SourceGraphic\",\n    in2: \"goo\",\n    operator: \"atop\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"g\", {\n    filter: \"url(#fancy-goo)\"\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animateTransform\", {\n    id: \"mainAnim\",\n    attributeName: \"transform\",\n    attributeType: \"XML\",\n    type: \"rotate\",\n    from: \"0 50 50\",\n    to: \"359 50 50\",\n    dur: \"1.2s\",\n    repeatCount: \"indefinite\"\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"50%\",\n    cy: \"40\",\n    r: props.radius,\n    fill: props.color\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    id: \"cAnim1\",\n    attributeType: \"XML\",\n    attributeName: \"cy\",\n    dur: \"0.6s\",\n    begin: \"0;cAnim1.end+0.2s\",\n    calcMode: \"spline\",\n    values: \"40;20;40\",\n    keyTimes: \"0;0.3;1\",\n    keySplines: \"0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1\"\n  })), /*#__PURE__*/_react[\"default\"].createElement(\"circle\", {\n    cx: \"50%\",\n    cy: \"60\",\n    r: props.radius,\n    fill: props.secondaryColor\n  }, /*#__PURE__*/_react[\"default\"].createElement(\"animate\", {\n    id: \"cAnim2\",\n    attributeType: \"XML\",\n    attributeName: \"cy\",\n    dur: \"0.6s\",\n    begin: \"0.4s;cAnim2.end+0.2s\",\n    calcMode: \"spline\",\n    values: \"60;80;60\",\n    keyTimes: \"0;0.3;1\",\n    keySplines: \"0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1\"\n  }))));\n};\n\nexports.MutatingDots = MutatingDots;\nMutatingDots.propTypes = {\n  width: _propTypes[\"default\"].number,\n  secondaryColor: _propTypes[\"default\"].string,\n  height: _propTypes[\"default\"].number,\n  color: _propTypes[\"default\"].string,\n  radius: _propTypes[\"default\"].number,\n  label: _propTypes[\"default\"].string\n};\nMutatingDots.defaultProps = {\n  width: 80,\n  height: 90,\n  color: \"green\",\n  radius: 11,\n  secondaryColor: \"green\",\n  label: \"audio-loading\"\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAEhG,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,KAAK,EAAE;EAC9C,OAAO,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,KAAK,EAAE;IACzDC,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAEH,KAAK,CAACG,KAAK;IAClBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;IACpB,YAAY,EAAEJ,KAAK,CAACK;EACtB,CAAC,EAAE,aAAaX,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACxDC,EAAE,EAAE;EACN,CAAC,EAAE,aAAaR,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,gBAAgB,EAAE;IAChE,IAAI,EAAE,eAAe;IACrBK,YAAY,EAAE,GAAG;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAab,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,eAAe,EAAE;IAChE,IAAI,EAAE,MAAM;IACZO,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,8CAA8C;IACtDF,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAab,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,aAAa,EAAE;IAC9D,IAAI,EAAE,eAAe;IACrBS,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,EAAE,aAAajB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,GAAG,EAAE;IACrDW,MAAM,EAAE;EACV,CAAC,EAAE,aAAalB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,kBAAkB,EAAE;IAClEC,EAAE,EAAE,UAAU;IACdW,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE,WAAW;IACfC,GAAG,EAAE,MAAM;IACXC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,aAAazB,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IACzDmB,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEtB,KAAK,CAACuB,MAAM;IACfC,IAAI,EAAExB,KAAK,CAACyB;EACd,CAAC,EAAE,aAAa/B,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDC,EAAE,EAAE,QAAQ;IACZY,aAAa,EAAE,KAAK;IACpBD,aAAa,EAAE,IAAI;IACnBK,GAAG,EAAE,MAAM;IACXQ,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,QAAQ;IAClBlB,MAAM,EAAE,UAAU;IAClBmB,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,EAAE,aAAanC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,QAAQ,EAAE;IAC1DmB,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,CAAC,EAAEtB,KAAK,CAACuB,MAAM;IACfC,IAAI,EAAExB,KAAK,CAAC8B;EACd,CAAC,EAAE,aAAapC,MAAM,CAAC,SAAS,CAAC,CAACO,aAAa,CAAC,SAAS,EAAE;IACzDC,EAAE,EAAE,QAAQ;IACZY,aAAa,EAAE,KAAK;IACpBD,aAAa,EAAE,IAAI;IACnBK,GAAG,EAAE,MAAM;IACXQ,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE,QAAQ;IAClBlB,MAAM,EAAE,UAAU;IAClBmB,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAEDtC,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCA,YAAY,CAACsC,SAAS,GAAG;EACvB5B,KAAK,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACmC,MAAM;EACnCF,cAAc,EAAEjC,UAAU,CAAC,SAAS,CAAC,CAACoC,MAAM;EAC5C7B,MAAM,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACmC,MAAM;EACpCP,KAAK,EAAE5B,UAAU,CAAC,SAAS,CAAC,CAACoC,MAAM;EACnCV,MAAM,EAAE1B,UAAU,CAAC,SAAS,CAAC,CAACmC,MAAM;EACpC3B,KAAK,EAAER,UAAU,CAAC,SAAS,CAAC,CAACoC;AAC/B,CAAC;AACDxC,YAAY,CAACyC,YAAY,GAAG;EAC1B/B,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVqB,KAAK,EAAE,OAAO;EACdF,MAAM,EAAE,EAAE;EACVO,cAAc,EAAE,OAAO;EACvBzB,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}