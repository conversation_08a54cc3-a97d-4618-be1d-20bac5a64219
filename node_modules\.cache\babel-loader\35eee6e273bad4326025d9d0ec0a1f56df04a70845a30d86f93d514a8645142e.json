{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\dettaglioProdImg.jsx\";\nimport React from 'react';\nimport { distributore } from '../route';\nimport { Costanti } from \"../traduttore/const\";\nimport { baseProxy } from './apireq';\nimport DettProdUffAcquisti from './dettaglioProdUffAcquisti';\nimport Immagine from '../../img/mktplaceholder.jpg';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const DettProdImg = product => {\n  var _product$region, _product$brand, _product$nationality, _product$region2, _product$format, _product$family, _product$subfamily, _product$subgroup, _product$deposit;\n  const role = window.localStorage.getItem('role');\n  // Costanti che mi permettono di inibire un campo se non è valorizzato\n  const nationality = product.nationality !== '' && product.nationality !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const region = product.region !== '' && ((_product$region = product.region) === null || _product$region === void 0 ? void 0 : _product$region.replace(/\\s+/g, '')) !== 'Kg' && product.region !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const format = product.format !== '' && product.format !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const family = product.family !== '' && product.family !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const subfamily = product.subfamily !== '' && product.subfamily !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const group = product.group !== '' && product.group !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const subgroup = product.subgroup !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const brand = product.brand !== '' && product.brand !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const deposit = product.deposit !== '' && product.deposit !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  const externalCode = product.externalCode !== '' && product.externalCode !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: role === distributore && product.externalCode !== undefined ? /*#__PURE__*/_jsxDEV(DettProdUffAcquisti, {\n      product: product\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-grid-item col-12 col-sm-4 text-center detailImage\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"w-100\",\n          src: baseProxy + 'asset/prodotti/' + product.id + '.jpg',\n          onError: e => e.target.src = Immagine,\n          alt: \"Immagine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-sm-8 border-left\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 mt-4 mt-sm-0 border-bottom\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [Costanti.SchedProd, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dettProdMod col-12 col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-group list-group-flush border-bottom\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: externalCode,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-externalCode\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.exCode, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 39,\n                      columnNumber: 134\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 104\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data ext-code\",\n                    children: [\" \", product.externalCode]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 166\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: brand,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-brand\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: \"Brand:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 40,\n                      columnNumber: 120\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 90\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: [\" \", product === null || product === void 0 ? void 0 : (_product$brand = product.brand) === null || _product$brand === void 0 ? void 0 : _product$brand.toLowerCase()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 140\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 59\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: nationality,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-nationality\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.Nazionalità, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 41,\n                      columnNumber: 132\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 102\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : (_product$nationality = product.nationality) === null || _product$nationality === void 0 ? void 0 : _product$nationality.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 169\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: region,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-region\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.Regione, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 42,\n                      columnNumber: 122\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 92\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : (_product$region2 = product.region) === null || _product$region2 === void 0 ? void 0 : _product$region2.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 155\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 60\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: format,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-format\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.Formato, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 43,\n                      columnNumber: 122\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 92\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data formato-prod\",\n                    children: product === null || product === void 0 ? void 0 : (_product$format = product.format) === null || _product$format === void 0 ? void 0 : _product$format.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 155\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 60\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dettProdMod col-12 col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-group list-group-flush border-bottom\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: family,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-family mr-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.family, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 48,\n                      columnNumber: 127\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 97\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : (_product$family = product.family) === null || _product$family === void 0 ? void 0 : _product$family.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 159\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 60\n                }, this), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: subfamily,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-subfamily\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.SottoFamiglia, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 49,\n                      columnNumber: 128\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 49,\n                    columnNumber: 98\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : (_product$subfamily = product.subfamily) === null || _product$subfamily === void 0 ? void 0 : _product$subfamily.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 49,\n                    columnNumber: 167\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 63\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: group,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-group mr-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.Group, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 50,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 95\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : product.group\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 156\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 59\n                }, this), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: subgroup,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-subgroup\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.SottoGruppo, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 51,\n                      columnNumber: 126\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 96\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: [\" \", product === null || product === void 0 ? void 0 : (_product$subgroup = product.subgroup) === null || _product$subgroup === void 0 ? void 0 : _product$subgroup.toLowerCase()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 163\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 62\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: deposit,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-deposit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-type\",\n                    children: /*#__PURE__*/_jsxDEV(\"b\", {\n                      children: [Costanti.Materiale, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 52,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 94\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"detail-data\",\n                    children: product === null || product === void 0 ? void 0 : (_product$deposit = product.deposit) === null || _product$deposit === void 0 ? void 0 : _product$deposit.toLowerCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 159\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 61\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 9\n  }, this);\n};\n_c = DettProdImg;\nvar _c;\n$RefreshReg$(_c, \"DettProdImg\");", "map": {"version": 3, "names": ["React", "distributore", "<PERSON><PERSON>", "baseProxy", "DettProdUffAcquisti", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DettProdImg", "product", "_product$region", "_product$brand", "_product$nationality", "_product$region2", "_product$format", "_product$family", "_product$subfamily", "_product$subgroup", "_product$deposit", "role", "window", "localStorage", "getItem", "nationality", "region", "replace", "format", "family", "subfamily", "group", "subgroup", "brand", "deposit", "externalCode", "className", "children", "undefined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "id", "onError", "e", "target", "alt", "<PERSON><PERSON><PERSON><PERSON>", "exCode", "toLowerCase", "Nazionalità", "Regione", "Formato", "SottoFamiglia", "Group", "SottoGruppo", "Materiale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/dettaglioProdImg.jsx"], "sourcesContent": ["import React from 'react';\nimport { distributore } from '../route';\nimport { <PERSON>nti } from \"../traduttore/const\";\nimport { baseProxy } from './apireq';\nimport DettProdUffAcquisti from './dettaglioProdUffAcquisti'\nimport Immagine from '../../img/mktplaceholder.jpg';\n\nexport const DettProdImg = (product) => {\n    const role = window.localStorage.getItem('role')\n    // Costanti che mi permettono di inibire un campo se non è valorizzato\n    const nationality = product.nationality !== '' && product.nationality !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const region = product.region !== '' && product.region?.replace(/\\s+/g, '') !== 'Kg' && product.region !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const format = product.format !== '' && product.format !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const family = product.family !== '' && product.family !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const subfamily = product.subfamily !== '' && product.subfamily !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const group = product.group !== '' && product.group !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const subgroup = product.subgroup !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const brand = product.brand !== '' && product.brand !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const deposit = product.deposit !== '' && product.deposit !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n    const externalCode = product.externalCode !== '' && product.externalCode !== null ? 'list-group-item px-2 py-2' : \"list-group-item px-2 py-2 d-none\";\n\n    return (\n        <div className=\"row\">\n            {/* <div class=\"vr\"></div> */}\n            {role === distributore && product.externalCode !== undefined ? (\n                <DettProdUffAcquisti product={product} />\n            ) : (\n                <>\n                    <div className=\"product-grid-item col-12 col-sm-4 text-center detailImage\">\n                        <img className=\"w-100\" src={baseProxy + 'asset/prodotti/' + product.id + '.jpg'} onError={(e) => e.target.src = Immagine} alt=\"Immagine\" />\n                    </div>\n                    <div className=\"col-12 col-sm-8 border-left\">\n                        <div className=\"row\">\n                            <div className=\"col-12 mt-4 mt-sm-0 border-bottom\">\n                                <h5 className=\"mb-3\"><strong>{Costanti.SchedProd}:</strong></h5>\n                            </div>\n                            <div className=\"dettProdMod col-12 col-sm-6\">\n                                <ul className=\"list-group list-group-flush border-bottom\">\n                                    <li className={externalCode}><div className=\"product-externalCode\"><span className=\"detail-type\"><b>{Costanti.exCode}:</b></span><span className=\"detail-data ext-code\"> {product.externalCode}</span></div></li>\n                                    <li className={brand}><div className=\"product-brand\"><span className=\"detail-type\"><b>Brand:</b></span><span className=\"detail-data\"> {product?.brand?.toLowerCase()}</span></div></li>\n                                    <li className={nationality}><div className=\"product-nationality\"><span className=\"detail-type\"><b>{Costanti.Nazionalità}:</b></span><span className=\"detail-data\">{product?.nationality?.toLowerCase()}</span></div></li>\n                                    <li className={region}><div className=\"product-region\"><span className=\"detail-type\"><b>{Costanti.Regione}:</b></span><span className=\"detail-data\">{product?.region?.toLowerCase()}</span></div></li>\n                                    <li className={format}><div className=\"product-format\"><span className=\"detail-type\"><b>{Costanti.Formato}:</b></span><span className=\"detail-data formato-prod\">{product?.format?.toLowerCase()}</span></div></li>\n                                </ul>\n                            </div>\n                            <div className=\"dettProdMod col-12 col-sm-6\">\n                                <ul className=\"list-group list-group-flush border-bottom\">\n                                    <li className={family}><div className=\"product-family mr-2\"><span className=\"detail-type\"><b>{Costanti.family}:</b></span><span className=\"detail-data\">{product?.family?.toLowerCase()}</span></div> </li>\n                                    <li className={subfamily}><div className=\"product-subfamily\"><span className=\"detail-type\"><b>{Costanti.SottoFamiglia}:</b></span><span className=\"detail-data\">{product?.subfamily?.toLowerCase()}</span></div></li>\n                                    <li className={group}><div className=\"product-group mr-2\"><span className=\"detail-type\"><b>{Costanti.Group}:</b></span><span className=\"detail-data\">{product?.group}</span></div> </li>\n                                    <li className={subgroup}><div className=\"product-subgroup\"><span className=\"detail-type\"><b>{Costanti.SottoGruppo}:</b></span><span className=\"detail-data\"> {product?.subgroup?.toLowerCase()}</span></div></li>\n                                    <li className={deposit}><div className=\"product-deposit\"><span className=\"detail-type\"><b>{Costanti.Materiale}:</b></span><span className=\"detail-data\">{product?.deposit?.toLowerCase()}</span></div></li>\n                                </ul>\n                            </div>\n                        </div>\n                    </div>\n                </>\n            )\n            }\n\n        </div>\n    )\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,UAAU;AACpC,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,OAAO,MAAMC,WAAW,GAAIC,OAAO,IAAK;EAAA,IAAAC,eAAA,EAAAC,cAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,gBAAA;EACpC,MAAMC,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAChD;EACA,MAAMC,WAAW,GAAGd,OAAO,CAACc,WAAW,KAAK,EAAE,IAAId,OAAO,CAACc,WAAW,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EACjJ,MAAMC,MAAM,GAAGf,OAAO,CAACe,MAAM,KAAK,EAAE,IAAI,EAAAd,eAAA,GAAAD,OAAO,CAACe,MAAM,cAAAd,eAAA,uBAAdA,eAAA,CAAgBe,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAK,IAAI,IAAIhB,OAAO,CAACe,MAAM,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAClL,MAAME,MAAM,GAAGjB,OAAO,CAACiB,MAAM,KAAK,EAAE,IAAIjB,OAAO,CAACiB,MAAM,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAClI,MAAMC,MAAM,GAAGlB,OAAO,CAACkB,MAAM,KAAK,EAAE,IAAIlB,OAAO,CAACkB,MAAM,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAClI,MAAMC,SAAS,GAAGnB,OAAO,CAACmB,SAAS,KAAK,EAAE,IAAInB,OAAO,CAACmB,SAAS,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAC3I,MAAMC,KAAK,GAAGpB,OAAO,CAACoB,KAAK,KAAK,EAAE,IAAIpB,OAAO,CAACoB,KAAK,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAC/H,MAAMC,QAAQ,GAAGrB,OAAO,CAACqB,QAAQ,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAC7G,MAAMC,KAAK,GAAGtB,OAAO,CAACsB,KAAK,KAAK,EAAE,IAAItB,OAAO,CAACsB,KAAK,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAC/H,MAAMC,OAAO,GAAGvB,OAAO,CAACuB,OAAO,KAAK,EAAE,IAAIvB,OAAO,CAACuB,OAAO,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EACrI,MAAMC,YAAY,GAAGxB,OAAO,CAACwB,YAAY,KAAK,EAAE,IAAIxB,OAAO,CAACwB,YAAY,KAAK,IAAI,GAAG,2BAA2B,GAAG,kCAAkC;EAEpJ,oBACI5B,OAAA;IAAK6B,SAAS,EAAC,KAAK;IAAAC,QAAA,EAEfhB,IAAI,KAAKpB,YAAY,IAAIU,OAAO,CAACwB,YAAY,KAAKG,SAAS,gBACxD/B,OAAA,CAACH,mBAAmB;MAACO,OAAO,EAAEA;IAAQ;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEzCnC,OAAA,CAAAE,SAAA;MAAA4B,QAAA,gBACI9B,OAAA;QAAK6B,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACtE9B,OAAA;UAAK6B,SAAS,EAAC,OAAO;UAACO,GAAG,EAAExC,SAAS,GAAG,iBAAiB,GAAGQ,OAAO,CAACiC,EAAE,GAAG,MAAO;UAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAGtC,QAAS;UAAC2C,GAAG,EAAC;QAAU;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1I,CAAC,eACNnC,OAAA;QAAK6B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxC9B,OAAA;UAAK6B,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAChB9B,OAAA;YAAK6B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAC9C9B,OAAA;cAAI6B,SAAS,EAAC,MAAM;cAAAC,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,GAASnC,QAAQ,CAAC+C,SAAS,EAAC,GAAC;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNnC,OAAA;YAAK6B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eACxC9B,OAAA;cAAI6B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACrD9B,OAAA;gBAAI6B,SAAS,EAAED,YAAa;gBAAAE,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACgD,MAAM,EAAC,GAAC;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,GAAC,EAAC1B,OAAO,CAACwB,YAAY;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjNnC,OAAA;gBAAI6B,SAAS,EAAEH,KAAM;gBAAAI,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,EAAG;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,GAAC,EAAC1B,OAAO,aAAPA,OAAO,wBAAAE,cAAA,GAAPF,OAAO,CAAEsB,KAAK,cAAApB,cAAA,uBAAdA,cAAA,CAAgBsC,WAAW,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvLnC,OAAA;gBAAI6B,SAAS,EAAEX,WAAY;gBAAAY,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACkD,WAAW,EAAC,GAAC;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAG,oBAAA,GAAPH,OAAO,CAAEc,WAAW,cAAAX,oBAAA,uBAApBA,oBAAA,CAAsBqC,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzNnC,OAAA;gBAAI6B,SAAS,EAAEV,MAAO;gBAAAW,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACmD,OAAO,EAAC,GAAC;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAI,gBAAA,GAAPJ,OAAO,CAAEe,MAAM,cAAAX,gBAAA,uBAAfA,gBAAA,CAAiBoC,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtMnC,OAAA;gBAAI6B,SAAS,EAAER,MAAO;gBAAAS,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACoD,OAAO,EAAC,GAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAK,eAAA,GAAPL,OAAO,CAAEiB,MAAM,cAAAZ,eAAA,uBAAfA,eAAA,CAAiBmC,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnC,OAAA;YAAK6B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eACxC9B,OAAA;cAAI6B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACrD9B,OAAA;gBAAI6B,SAAS,EAAEP,MAAO;gBAAAQ,QAAA,gBAAC9B,OAAA;kBAAK6B,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAAC2B,MAAM,EAAC,GAAC;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAM,eAAA,GAAPN,OAAO,CAAEkB,MAAM,cAAAZ,eAAA,uBAAfA,eAAA,CAAiBkC,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,KAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3MnC,OAAA;gBAAI6B,SAAS,EAAEN,SAAU;gBAAAO,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACqD,aAAa,EAAC,GAAC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAO,kBAAA,GAAPP,OAAO,CAAEmB,SAAS,cAAAZ,kBAAA,uBAAlBA,kBAAA,CAAoBiC,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrNnC,OAAA;gBAAI6B,SAAS,EAAEL,KAAM;gBAAAM,QAAA,gBAAC9B,OAAA;kBAAK6B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACsD,KAAK,EAAC,GAAC;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,KAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxLnC,OAAA;gBAAI6B,SAAS,EAAEJ,QAAS;gBAAAK,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACuD,WAAW,EAAC,GAAC;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,GAAC,EAAC1B,OAAO,aAAPA,OAAO,wBAAAQ,iBAAA,GAAPR,OAAO,CAAEqB,QAAQ,cAAAb,iBAAA,uBAAjBA,iBAAA,CAAmBgC,WAAW,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjNnC,OAAA;gBAAI6B,SAAS,EAAEF,OAAQ;gBAAAG,QAAA,eAAC9B,OAAA;kBAAK6B,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAAC9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAAC9B,OAAA;sBAAA8B,QAAA,GAAInC,QAAQ,CAACwD,SAAS,EAAC,GAAC;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAAnC,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1B,OAAO,aAAPA,OAAO,wBAAAS,gBAAA,GAAPT,OAAO,CAAEuB,OAAO,cAAAd,gBAAA,uBAAhBA,gBAAA,CAAkB+B,WAAW,CAAC;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACR;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGA,CAAC;AAEd,CAAC;AAAAiB,EAAA,GAvDYjD,WAAW;AAAA,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}