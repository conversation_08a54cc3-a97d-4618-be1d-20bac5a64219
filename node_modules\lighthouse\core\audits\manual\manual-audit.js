/**
 * @license Copyright 2017 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

/**
 * @fileoverview Base class for audits that the user should verify manually on
 * their site.
 */

import {Audit} from '../audit.js';

class ManualAudit extends Audit {
  /**
   * @return {Pick<LH.Audit.Meta, 'scoreDisplayMode'|'requiredArtifacts'>}
   */
  static get partialMeta() {
    return {
      scoreDisplayMode: Audit.SCORING_MODES.MANUAL,
      requiredArtifacts: [],
    };
  }

  /**
   * @return {LH.Audit.Product}
   */
  static audit() {
    return {
      score: 0,
      // displayValue: '(needs manual verification)'
    };
  }
}

export default ManualAudit;
