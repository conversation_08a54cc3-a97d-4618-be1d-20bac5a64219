/**
 * Utility per la gestione degli errori API
 * Fornisce messaggi user-friendly per diversi tipi di errore
 */

export const ErrorHandler = {
    /**
     * Analizza un errore API e restituisce un messaggio user-friendly
     * @param {Object} error - L'oggetto errore dalla chiamata API
     * @param {string} context - Contesto dell'operazione (es: 'creazione anagrafica')
     * @returns {Object} - Oggetto con summary e detail per il toast
     */
    parseError: (error, context = 'operazione') => {
        const errorStatus = error.response?.status;
        const errorData = error.response?.data;
        const errorMessage = error.response?.data?.message || error.message || '';
        
        let summary = 'Errore';
        let detail = '';
        let severity = 'error';
        
        // Errori di validazione unique constraint
        if (errorStatus === 409 || 
            errorMessage.toLowerCase().includes('unique') || 
            errorMessage.toLowerCase().includes('duplicate') || 
            errorMessage.toLowerCase().includes('già esiste') ||
            errorMessage.toLowerCase().includes('already exists')) {
            
            if (errorMessage.toLowerCase().includes('partita iva') || 
                errorMessage.toLowerCase().includes('piva') ||
                errorMessage.toLowerCase().includes('vat')) {
                summary = '⚠️ Partita IVA già presente';
                detail = 'La Partita IVA inserita è già registrata nel sistema. Verificare i dati o utilizzare una P.IVA diversa.';
            } else if (errorMessage.toLowerCase().includes('email')) {
                summary = '⚠️ Email già presente';
                detail = 'L\'indirizzo email inserito è già registrato nel sistema.';
            } else {
                summary = '⚠️ Dati duplicati';
                detail = 'Alcuni dati inseriti sono già presenti nel sistema. Verificare i dati inseriti.';
            }
            severity = 'warn';
        }
        // Errori di validazione (400)
        else if (errorStatus === 400) {
            summary = '📝 Dati non validi';
            if (errorMessage.toLowerCase().includes('email')) {
                detail = 'L\'indirizzo email inserito non è valido. Verificare il formato.';
            } else if (errorMessage.toLowerCase().includes('partita iva') || 
                       errorMessage.toLowerCase().includes('piva')) {
                detail = 'La Partita IVA inserita non è valida. Deve contenere 11 cifre.';
            } else if (errorMessage.toLowerCase().includes('telefono') || 
                       errorMessage.toLowerCase().includes('tel')) {
                detail = 'Il numero di telefono inserito non è valido.';
            } else if (errorMessage.toLowerCase().includes('required') || 
                       errorMessage.toLowerCase().includes('obbligatorio')) {
                detail = 'Alcuni campi obbligatori non sono stati compilati.';
            } else {
                detail = `Dati inseriti non validi: ${errorMessage}`;
            }
            severity = 'warn';
        }
        // Errori di autorizzazione (401, 403)
        else if (errorStatus === 401) {
            summary = '🔐 Accesso negato';
            detail = 'Sessione scaduta. Effettuare nuovamente il login.';
        }
        else if (errorStatus === 403) {
            summary = '🚫 Operazione non autorizzata';
            detail = 'Non si dispone dei permessi necessari per questa operazione.';
        }
        // Risorsa non trovata (404)
        else if (errorStatus === 404) {
            summary = '🔍 Risorsa non trovata';
            detail = 'La risorsa richiesta non è stata trovata.';
        }
        // Errori di business logic (422)
        else if (errorStatus === 422) {
            summary = '🚫 Operazione non consentita';
            detail = `Impossibile completare l'${context}: ${errorMessage}`;
        }
        // Errori del server (500, 501, 502)
        else if (errorStatus >= 500 && errorStatus < 600) {
            summary = '🔧 Errore del server';
            if (errorStatus === 501) {
                detail = 'Funzionalità non implementata. Contattare l\'assistenza tecnica.';
            } else if (errorStatus === 503) {
                detail = 'Servizio temporaneamente non disponibile. Riprovare tra qualche minuto.';
                summary = '⏱️ Servizio non disponibile';
            } else {
                detail = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto.';
            }
        }
        // Errori di rete
        else if (!error.response) {
            summary = '🌐 Errore di connessione';
            detail = 'Impossibile connettersi al server. Verificare la connessione internet.';
        }
        // Altri errori
        else {
            summary = '❌ Errore imprevisto';
            detail = `Si è verificato un errore imprevisto durante l'${context}: ${errorMessage}`;
        }
        
        return {
            severity,
            summary,
            detail,
            life: severity === 'error' ? 6000 : 5000
        };
    },

    /**
     * Mostra un toast di errore utilizzando il parser
     * @param {Object} toast - Riferimento al componente Toast
     * @param {Object} error - L'oggetto errore
     * @param {string} context - Contesto dell'operazione
     */
    showError: (toast, error, context = 'operazione') => {
        const errorInfo = ErrorHandler.parseError(error, context);
        toast.current?.show(errorInfo);
        
        // Log dettagliato per debugging
        console.error(`Errore ${context}:`, {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message,
            fullError: error
        });
    },

    /**
     * Validatori comuni per i form
     */
    validators: {
        required: (value, fieldName) => {
            if (!value || value.trim() === '') {
                return `${fieldName} è obbligatorio`;
            }
            return null;
        },

        email: (value) => {
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                return 'Formato email non valido';
            }
            return null;
        },

        partitaIva: (value) => {
            if (value && !/^\d{11}$/.test(value.replace(/\s/g, ''))) {
                return 'La Partita IVA deve contenere esattamente 11 cifre';
            }
            return null;
        },

        telefono: (value) => {
            if (value && !/^\+?[\d\s\-\(\)]{8,}$/.test(value)) {
                return 'Numero di telefono non valido';
            }
            return null;
        }
    }
};

export default ErrorHandler;
