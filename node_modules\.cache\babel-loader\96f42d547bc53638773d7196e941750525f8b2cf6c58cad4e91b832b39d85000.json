{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\giacenzeProdotti.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneProdotti - operazioni sui prodotti\n*\n*/\nimport React, { Component } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Costanti } from '../../components/traduttore/const';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Tooltip } from 'primereact/tooltip';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Dialog } from 'primereact/dialog';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass GiacenzeProdotti extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    /* Seleziono il magazzino per le giacenze */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value,\n        loading: true\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value));\n      var url = 'statistic/productpositionstock/?warehouse=' + e.value;\n      await APIRequest(\"GET\", url).then(res => {\n        res.data = res.data.filter(el => el.giacenza_effettiva > 0);\n        res.data.forEach(element => {\n          element.unitMeasure = \"\".concat(element.id_product_packaging.unitMeasure, \" x \").concat(element.id_product_packaging.pcsXPackage);\n        });\n        this.setState({\n          results: res.data,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: [],\n      resultDialog: false,\n      globalFilter: null,\n      loading: true,\n      selectedWarehouse: null,\n      displayed: false\n    };\n    this.warehouse = [];\n    //Dichiarazione funzioni e metodi\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount(results) {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      this.setState({\n        selectedWarehouse: idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse\n      });\n      var url = 'statistic/productpositionstock/?warehouse=' + (idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse);\n      await APIRequest(\"GET\", url).then(res => {\n        res.data = res.data.filter(el => el.giacenza_effettiva > 0);\n        res.data.forEach(element => {\n          element.unitMeasure = \"\".concat(element.id_product_packaging.unitMeasure, \" x \").concat(element.id_product_packaging.pcsXPackage);\n        });\n        this.setState({\n          results: res.data,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la composizione del magazzino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  render() {\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      field: 'id_product_packaging.idProduct.externalCode',\n      header: Costanti.exCode,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'id_product_packaging.idProduct.description',\n      header: Costanti.Nome,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'unitMeasure',\n      header: Costanti.UnitMis,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'giacenza_effettiva',\n      header: Costanti.Giacenza,\n      sortable: true,\n      showHeader: true\n    }, {\n      field: 'ordinato_fornitore',\n      header: Costanti.OrdinatoAlFornitore,\n      sortable: true,\n      showHeader: true\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.giacenzeProdotti\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          fields: fields,\n          loading: this.state.loading,\n          dataKey: \"id\",\n          paginator: true,\n          rows: 20,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          selectionMode: \"single\",\n          cellSelection: true,\n          fileNames: \"Prodotti\",\n          showExportCsvButton: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-lg-6\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-home mr-2\",\n                style: {\n                  'fontSize': '.8em'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 50\n              }, this), Costanti.Magazzino]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-lg-6 d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              target: \".p-button-rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-rounded p-1 w-auto\",\n              \"data-pr-tooltip\": \"Seleziona un magazzino per vedere le giacenze a lui relative\",\n              \"data-pr-position\": \"top\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                style: {\n                  fontSize: '20px'\n                },\n                className: \"pi pi-question-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 179\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default GiacenzeProdotti;", "map": {"version": 3, "names": ["React", "Component", "Toast", "<PERSON><PERSON>", "APIRequest", "<PERSON><PERSON>", "Dropdown", "<PERSON><PERSON><PERSON>", "JoyrideGen", "Dialog", "Nav", "CustomDataTable", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "props", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "loading", "window", "sessionStorage", "setItem", "JSON", "stringify", "url", "then", "res", "data", "filter", "el", "giacenza_effettiva", "for<PERSON>ach", "element", "unitMeasure", "concat", "id_product_packaging", "pcsXPackage", "results", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "response", "undefined", "message", "life", "state", "resultDialog", "globalFilter", "displayed", "warehouse", "bind", "closeSelectBefore", "componentDidMount", "idWarehouse", "parse", "getItem", "code", "_e$response3", "_e$response4", "entry", "push", "name", "warehouseName", "id", "_e$response5", "_e$response6", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fields", "field", "header", "exCode", "sortable", "showHeader", "Nome", "UnitMis", "Giacenza", "OrdinatoAlFornitore", "ref", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "paginator", "rows", "rowsPerPageOptions", "autoLayout", "selectionMode", "cellSelection", "fileNames", "showExportCsvButton", "visible", "Primadiproseguire", "modal", "onHide", "footer", "title", "content", "target", "fontSize"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/giacenzeProdotti.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* GestioneProdotti - operazioni sui prodotti\n*\n*/\nimport React, { Component } from 'react';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Costanti } from '../../components/traduttore/const';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Tooltip } from 'primereact/tooltip';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Dialog } from 'primereact/dialog';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\n\nclass GiacenzeProdotti extends Component {\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            results: [],\n            resultDialog: false,\n            globalFilter: null,\n            loading: true,\n            selectedWarehouse: null,\n            displayed: false\n        };\n        this.warehouse = [];\n        //Dichiarazione funzioni e metodi\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount(results) {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            this.setState({ selectedWarehouse: idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse });\n            var url = 'statistic/productpositionstock/?warehouse=' + (idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse)\n            await APIRequest(\"GET\", url)\n                .then(res => {\n                    res.data = res.data.filter(el => el.giacenza_effettiva > 0)\n                    res.data.forEach(element => {\n                        element.unitMeasure = `${element.id_product_packaging.unitMeasure} x ${element.id_product_packaging.pcsXPackage}`\n                    })\n                    this.setState({\n                        results: res.data,\n                        loading: false\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                })\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Seleziono il magazzino per le giacenze */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value, loading: true });\n        window.sessionStorage.setItem(\"idWarehouse\", JSON.stringify(e.value))\n        var url = 'statistic/productpositionstock/?warehouse=' + e.value\n        await APIRequest(\"GET\", url)\n            .then(res => {\n                res.data = res.data.filter(el => el.giacenza_effettiva > 0)\n                res.data.forEach(element => {\n                    element.unitMeasure = `${element.id_product_packaging.unitMeasure} x ${element.id_product_packaging.pcsXPackage}`\n                })\n                this.setState({\n                    results: res.data,\n                    loading: false\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la composizione del magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        const fields = [\n            { field: 'id_product_packaging.idProduct.externalCode', header: Costanti.exCode, sortable: true, showHeader: true },\n            { field: 'id_product_packaging.idProduct.description', header: Costanti.Nome, sortable: true, showHeader: true },\n            { field: 'unitMeasure', header: Costanti.UnitMis, sortable: true, showHeader: true },\n            { field: 'giacenza_effettiva', header: Costanti.Giacenza, sortable: true, showHeader: true },\n            { field: 'ordinato_fornitore', header: Costanti.OrdinatoAlFornitore, sortable: true, showHeader: true },\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.giacenzeProdotti}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        fields={fields}\n                        loading={this.state.loading}\n                        dataKey=\"id\"\n                        paginator\n                        rows={20}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        selectionMode='single'\n                        cellSelection={true}\n                        fileNames=\"Prodotti\"\n                        showExportCsvButton={true}\n                    />\n                </div>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='row d-flex align-items-center'>\n                        <div className='col-12 col-lg-6'>\n                            <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        </div>\n                        <div className='col-12 col-lg-6 d-flex justify-content-end'>\n                            <Tooltip target=\".p-button-rounded\" />\n                            <Button className=\"p-button-rounded p-1 w-auto\" data-pr-tooltip=\"Seleziona un magazzino per vedere le giacenze a lui relative\" data-pr-position=\"top\"><i style={{ fontSize: '20px' }} className='pi pi-question-circle'></i></Button>\n                        </div>\n                    </div>\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                    </div>\n                </Dialog>\n            </div>\n        );\n    }\n}\n\nexport default GiacenzeProdotti;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,gBAAgB,SAASb,SAAS,CAAC;EACrCc,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IA6DJ;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG,KAAK;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5DC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACT,CAAC,CAACG,KAAK,CAAC,CAAC;MACrE,IAAIO,GAAG,GAAG,4CAA4C,GAAGV,CAAC,CAACG,KAAK;MAChE,MAAMjB,UAAU,CAAC,KAAK,EAAEwB,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,kBAAkB,GAAG,CAAC,CAAC;QAC3DJ,GAAG,CAACC,IAAI,CAACI,OAAO,CAACC,OAAO,IAAI;UACxBA,OAAO,CAACC,WAAW,MAAAC,MAAA,CAAMF,OAAO,CAACG,oBAAoB,CAACF,WAAW,SAAAC,MAAA,CAAMF,OAAO,CAACG,oBAAoB,CAACC,WAAW,CAAE;QACrH,CAAC,CAAC;QACF,IAAI,CAACrB,QAAQ,CAAC;UACVsB,OAAO,EAAEX,GAAG,CAACC,IAAI;UACjBT,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACoB,KAAK,CAAExB,CAAC,IAAK;QAAA,IAAAyB,WAAA,EAAAC,YAAA;QACZC,OAAO,CAACC,GAAG,CAAC5B,CAAC,CAAC;QACd,IAAI,CAAC6B,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAb,MAAA,CAAyF,EAAAK,WAAA,GAAAzB,CAAC,CAACkC,QAAQ,cAAAT,WAAA,uBAAVA,WAAA,CAAYZ,IAAI,MAAKsB,SAAS,IAAAT,YAAA,GAAG1B,CAAC,CAACkC,QAAQ,cAAAR,YAAA,uBAAVA,YAAA,CAAYb,IAAI,GAAGb,CAAC,CAACoC,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IApFG,IAAI,CAACC,KAAK,GAAG;MACTf,OAAO,EAAE,EAAE;MACXgB,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,IAAI;MAClBpC,OAAO,EAAE,IAAI;MACbF,iBAAiB,EAAE,IAAI;MACvBuC,SAAS,EAAE;IACf,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAAC3C,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC4C,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAAC,IAAI,CAAC;EAC9D;EACA;EACA,MAAME,iBAAiBA,CAACtB,OAAO,EAAE;IAC7B,IAAIuB,WAAW,GAAGtC,IAAI,CAACuC,KAAK,CAAC1C,MAAM,CAACC,cAAc,CAAC0C,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI,CAAC7C,QAAQ,CAAC;QAAEC,iBAAiB,EAAE4C,WAAW,CAACG,IAAI,KAAKd,SAAS,GAAGW,WAAW,CAACG,IAAI,GAAGH;MAAY,CAAC,CAAC;MACrG,IAAIpC,GAAG,GAAG,4CAA4C,IAAIoC,WAAW,CAACG,IAAI,KAAKd,SAAS,GAAGW,WAAW,CAACG,IAAI,GAAGH,WAAW,CAAC;MAC1H,MAAM5D,UAAU,CAAC,KAAK,EAAEwB,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,kBAAkB,GAAG,CAAC,CAAC;QAC3DJ,GAAG,CAACC,IAAI,CAACI,OAAO,CAACC,OAAO,IAAI;UACxBA,OAAO,CAACC,WAAW,MAAAC,MAAA,CAAMF,OAAO,CAACG,oBAAoB,CAACF,WAAW,SAAAC,MAAA,CAAMF,OAAO,CAACG,oBAAoB,CAACC,WAAW,CAAE;QACrH,CAAC,CAAC;QACF,IAAI,CAACrB,QAAQ,CAAC;UACVsB,OAAO,EAAEX,GAAG,CAACC,IAAI;UACjBT,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CAACoB,KAAK,CAAExB,CAAC,IAAK;QAAA,IAAAkD,YAAA,EAAAC,YAAA;QACZxB,OAAO,CAACC,GAAG,CAAC5B,CAAC,CAAC;QACd,IAAI,CAAC6B,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,4FAAAb,MAAA,CAAyF,EAAA8B,YAAA,GAAAlD,CAAC,CAACkC,QAAQ,cAAAgB,YAAA,uBAAVA,YAAA,CAAYrC,IAAI,MAAKsB,SAAS,IAAAgB,YAAA,GAAGnD,CAAC,CAACkC,QAAQ,cAAAiB,YAAA,uBAAVA,YAAA,CAAYtC,IAAI,GAAGb,CAAC,CAACoC,OAAO,CAAE;UAC9JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACpC,QAAQ,CAAC;QAAEsC,YAAY,EAAE,IAAI;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;IACA,MAAMvD,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjCyB,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIwC,KAAK,IAAIxC,GAAG,CAACC,IAAI,EAAE;QACxB,IAAI,CAAC6B,SAAS,CAACW,IAAI,CAAC;UAChBC,IAAI,EAAEF,KAAK,CAACG,aAAa;UACzBpD,KAAK,EAAEiD,KAAK,CAACI;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDhC,KAAK,CAAExB,CAAC,IAAK;MAAA,IAAAyD,YAAA,EAAAC,YAAA;MACV/B,OAAO,CAACC,GAAG,CAAC5B,CAAC,CAAC;MACd,IAAI,CAAC6B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAb,MAAA,CAAuE,EAAAqC,YAAA,GAAAzD,CAAC,CAACkC,QAAQ,cAAAuB,YAAA,uBAAVA,YAAA,CAAY5C,IAAI,MAAKsB,SAAS,IAAAuB,YAAA,GAAG1D,CAAC,CAACkC,QAAQ,cAAAwB,YAAA,uBAAVA,YAAA,CAAY7C,IAAI,GAAGb,CAAC,CAACoC,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EA0BAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACN,KAAK,CAACpC,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVsC,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACV,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEI,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAsB,MAAMA,CAAA,EAAG;IACL,MAAMC,kBAAkB,gBACpBjE,OAAA,CAACb,KAAK,CAAC+E,QAAQ;MAAAC,QAAA,eACXnE,OAAA;QAAKoE,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DnE,OAAA,CAACV,MAAM;UAAC8E,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACpB,iBAAkB;UAAAkB,QAAA,GAAE,GAAC,EAAC3E,QAAQ,CAAC8E,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAMC,MAAM,GAAG,CACX;MAAEC,KAAK,EAAE,6CAA6C;MAAEC,MAAM,EAAErF,QAAQ,CAACsF,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACnH;MAAEJ,KAAK,EAAE,4CAA4C;MAAEC,MAAM,EAAErF,QAAQ,CAACyF,IAAI;MAAEF,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAChH;MAAEJ,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAErF,QAAQ,CAAC0F,OAAO;MAAEH,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EACpF;MAAEJ,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAErF,QAAQ,CAAC2F,QAAQ;MAAEJ,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC5F;MAAEJ,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAErF,QAAQ,CAAC4F,mBAAmB;MAAEL,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CAC1G;IACD,oBACIhF,OAAA;MAAKoE,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CnE,OAAA,CAACX,KAAK;QAACgG,GAAG,EAAGjE,EAAE,IAAK,IAAI,CAACc,KAAK,GAAGd;MAAG;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC1E,OAAA,CAACH,GAAG;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP1E,OAAA;QAAKoE,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCnE,OAAA;UAAAmE,QAAA,EAAK3E,QAAQ,CAAC8F;QAAgB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,EACL,IAAI,CAAC/B,KAAK,CAACpC,iBAAiB,KAAK,IAAI,iBAClCP,OAAA;QAAKoE,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCnE,OAAA;UAAIoE,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEnE,OAAA;YAAIoE,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEnE,OAAA;cAAKoE,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DnE,OAAA;gBAAIoE,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACnE,OAAA;kBAAGoE,SAAS,EAAC,iBAAiB;kBAACmB,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAClF,QAAQ,CAACgG,SAAS,EAAC,GAAC;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H1E,OAAA,CAACP,QAAQ;gBAAC2E,SAAS,EAAC,QAAQ;gBAAC5D,KAAK,EAAE,IAAI,CAACmC,KAAK,CAACpC,iBAAkB;gBAACkF,OAAO,EAAE,IAAI,CAAC1C,SAAU;gBAAC2C,QAAQ,EAAE,IAAI,CAACtF,iBAAkB;gBAACuF,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAACzE,MAAM;gBAAC0E,QAAQ,EAAC;cAAM;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV1E,OAAA;QAAKoE,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBnE,OAAA,CAACF,eAAe;UACZuF,GAAG,EAAGjE,EAAE,IAAK,IAAI,CAAC0E,EAAE,GAAG1E,EAAG;UAC1BZ,KAAK,EAAE,IAAI,CAACmC,KAAK,CAACf,OAAQ;UAC1B+C,MAAM,EAAEA,MAAO;UACflE,OAAO,EAAE,IAAI,CAACkC,KAAK,CAAClC,OAAQ;UAC5BsF,OAAO,EAAC,IAAI;UACZC,SAAS;UACTC,IAAI,EAAE,EAAG;UACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,aAAa,EAAC,QAAQ;UACtBC,aAAa,EAAE,IAAK;UACpBC,SAAS,EAAC,UAAU;UACpBC,mBAAmB,EAAE;QAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1E,OAAA,CAACJ,MAAM;QAAC4G,OAAO,EAAE,IAAI,CAAC7D,KAAK,CAACC,YAAa;QAACiC,MAAM,EAAErF,QAAQ,CAACiH,iBAAkB;QAACC,KAAK;QAACtC,SAAS,EAAC,kBAAkB;QAACuC,MAAM,EAAE,IAAI,CAAC1D,iBAAkB;QAAC2D,MAAM,EAAE3C,kBAAmB;QAAAE,QAAA,GACvK,IAAI,CAACxB,KAAK,CAACG,SAAS,iBACjB9C,OAAA,CAACL,UAAU;UAACkH,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhG1E,OAAA;UAAKoE,SAAS,EAAC,+BAA+B;UAAAD,QAAA,gBAC1CnE,OAAA;YAAKoE,SAAS,EAAC,iBAAiB;YAAAD,QAAA,eAC5BnE,OAAA;cAAIoE,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAACnE,OAAA;gBAAGoE,SAAS,EAAC,iBAAiB;gBAACmB,KAAK,EAAE;kBAAE,UAAU,EAAE;gBAAO;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAAClF,QAAQ,CAACgG,SAAS;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACN1E,OAAA;YAAKoE,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBACvDnE,OAAA,CAACN,OAAO;cAACqH,MAAM,EAAC;YAAmB;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtC1E,OAAA,CAACV,MAAM;cAAC8E,SAAS,EAAC,6BAA6B;cAAC,mBAAgB,8DAA8D;cAAC,oBAAiB,KAAK;cAAAD,QAAA,eAACnE,OAAA;gBAAGuF,KAAK,EAAE;kBAAEyB,QAAQ,EAAE;gBAAO,CAAE;gBAAC5C,SAAS,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1E,OAAA;UAAKoE,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DnE,OAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1E,OAAA,CAACP,QAAQ;YAAC2E,SAAS,EAAC,QAAQ;YAAC5D,KAAK,EAAE,IAAI,CAACmC,KAAK,CAACpC,iBAAkB;YAACkF,OAAO,EAAE,IAAI,CAAC1C,SAAU;YAAC2C,QAAQ,EAAE,IAAI,CAACtF,iBAAkB;YAACuF,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAACzE,MAAM;YAAC0E,QAAQ,EAAC;UAAM;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAezE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}