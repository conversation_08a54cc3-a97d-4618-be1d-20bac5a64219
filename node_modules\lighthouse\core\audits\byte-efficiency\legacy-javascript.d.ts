export default LegacyJavascript;
export type Pattern = {
    name: string;
    expression: string;
    estimateBytes?: ((result: PatternMatchResult) => number) | undefined;
};
export type PatternMatchResult = {
    name: string;
    line: number;
    column: number;
    count: number;
};
export type ByteEfficiencyProduct = import('./byte-efficiency-audit.js').ByteEfficiencyProduct;
export type Item = LH.Audit.ByteEfficiencyItem & {
    subItems: {
        type: 'subitems';
        items: SubItem[];
    };
};
export type SubItem = {
    signal: string;
    location: LH.Audit.Details.SourceLocationValue;
};
declare class LegacyJavascript extends ByteEfficiencyAudit {
    /**
     * @param {string?} object
     * @param {string} property
     */
    static buildPolyfillExpression(object: string | null, property: string): string;
    static getPolyfillData(): {
        name: string;
        modules: string[];
        corejs?: boolean | undefined;
    }[];
    static getCoreJsPolyfillData(): {
        name: string;
        coreJs2Module: string;
        coreJs3Module: string;
    }[];
    /**
     * @return {Pattern[]}
     */
    static getPolyfillPatterns(): Pattern[];
    /**
     * @return {Pattern[]}
     */
    static getTransformPatterns(): Pattern[];
    /**
     * Returns a collection of match results grouped by script url.
     *
     * @param {CodePatternMatcher} matcher
     * @param {LH.Artifacts['Scripts']} scripts
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @param {LH.Artifacts.Bundle[]} bundles
     * @return {Map<LH.Artifacts.Script, PatternMatchResult[]>}
     */
    static detectAcrossScripts(matcher: CodePatternMatcher, scripts: LH.Artifacts['Scripts'], networkRecords: LH.Artifacts.NetworkRequest[], bundles: LH.Artifacts.Bundle[]): Map<LH.Artifacts.Script, PatternMatchResult[]>;
    /**
     * @param {PatternMatchResult[]} matches
     * @return {number}
     */
    static estimateWastedBytes(matches: PatternMatchResult[]): number;
    /**
     * Utility function to estimate transfer size and cache calculation.
     *
     * Note: duplicated-javascript does this exact thing. In the future, consider
     * making a generic estimator on ByteEfficienyAudit.
     * @param {Map<string, number>} transferRatioByUrl
     * @param {string} url
     * @param {LH.Artifacts} artifacts
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     */
    static estimateTransferRatioForScript(transferRatioByUrl: Map<string, number>, url: string, artifacts: LH.Artifacts, networkRecords: Array<LH.Artifacts.NetworkRequest>): Promise<number>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {LH.Audit.Context} context
     * @return {Promise<ByteEfficiencyProduct>}
     */
    static audit_(artifacts: LH.Artifacts, networkRecords: Array<LH.Artifacts.NetworkRequest>, context: LH.Audit.Context): Promise<ByteEfficiencyProduct>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { Audit } from "../audit.js";
import { ByteEfficiencyAudit } from "./byte-efficiency-audit.js";
/**
 * Takes a list of patterns (consisting of a name identifier and a RegExp expression string)
 * and returns match results with line / column information for a given code input.
 */
declare class CodePatternMatcher {
    /**
     * @param {Pattern[]} patterns
     */
    constructor(patterns: Pattern[]);
    re: RegExp;
    patterns: Pattern[];
    /**
     * @param {string} code
     * @return {PatternMatchResult[]}
     */
    match(code: string): PatternMatchResult[];
}
//# sourceMappingURL=legacy-javascript.d.ts.map