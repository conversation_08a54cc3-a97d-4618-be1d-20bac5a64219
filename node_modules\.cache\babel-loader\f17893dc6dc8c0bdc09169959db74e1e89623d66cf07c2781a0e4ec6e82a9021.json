{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport ConfirmDialog from '../ConfirmDialog';\nimport defaultLocale from '../../locale/default';\nimport LocaleReceiver from '../../locale-provider/LocaleReceiver';\nimport { ConfigContext } from '../../config-provider';\nvar HookModal = function HookModal(_ref, ref) {\n  var afterClose = _ref.afterClose,\n    config = _ref.config;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useState3 = React.useState(config),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    innerConfig = _React$useState4[0],\n    setInnerConfig = _React$useState4[1];\n  var _React$useContext = React.useContext(ConfigContext),\n    direction = _React$useContext.direction,\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('modal');\n  var rootPrefixCls = getPrefixCls();\n  var close = function close() {\n    setVisible(false);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var triggerCancel = args.some(function (param) {\n      return param && param.triggerCancel;\n    });\n    if (innerConfig.onCancel && triggerCancel) {\n      innerConfig.onCancel();\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      destroy: close,\n      update: function update(newConfig) {\n        setInnerConfig(function (originConfig) {\n          return _extends(_extends({}, originConfig), newConfig);\n        });\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Modal\",\n    defaultLocale: defaultLocale.Modal\n  }, function (modalLocale) {\n    return /*#__PURE__*/React.createElement(ConfirmDialog, _extends({\n      prefixCls: prefixCls,\n      rootPrefixCls: rootPrefixCls\n    }, innerConfig, {\n      close: close,\n      visible: visible,\n      afterClose: afterClose,\n      okText: innerConfig.okText || (innerConfig.okCancel ? modalLocale.okText : modalLocale.justOkText),\n      direction: direction,\n      cancelText: innerConfig.cancelText || modalLocale.cancelText\n    }));\n  });\n};\nexport default /*#__PURE__*/React.forwardRef(HookModal);", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "ConfirmDialog", "defaultLocale", "LocaleReceiver", "ConfigContext", "HookModal", "_ref", "ref", "afterClose", "config", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "_React$useState3", "_React$useState4", "innerConfig", "setInnerConfig", "_React$useContext", "useContext", "direction", "getPrefixCls", "prefixCls", "rootPrefixCls", "close", "_len", "arguments", "length", "args", "Array", "_key", "triggerCancel", "some", "param", "onCancel", "useImperativeHandle", "destroy", "update", "newConfig", "originConfig", "createElement", "componentName", "Modal", "modalLocale", "okText", "okCancel", "justOkText", "cancelText", "forwardRef"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/modal/useModal/HookModal.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport ConfirmDialog from '../ConfirmDialog';\nimport defaultLocale from '../../locale/default';\nimport LocaleReceiver from '../../locale-provider/LocaleReceiver';\nimport { ConfigContext } from '../../config-provider';\n\nvar HookModal = function HookModal(_ref, ref) {\n  var afterClose = _ref.afterClose,\n      config = _ref.config;\n\n  var _React$useState = React.useState(true),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      visible = _React$useState2[0],\n      setVisible = _React$useState2[1];\n\n  var _React$useState3 = React.useState(config),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      innerConfig = _React$useState4[0],\n      setInnerConfig = _React$useState4[1];\n\n  var _React$useContext = React.useContext(ConfigContext),\n      direction = _React$useContext.direction,\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('modal');\n  var rootPrefixCls = getPrefixCls();\n\n  var close = function close() {\n    setVisible(false);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    var triggerCancel = args.some(function (param) {\n      return param && param.triggerCancel;\n    });\n\n    if (innerConfig.onCancel && triggerCancel) {\n      innerConfig.onCancel();\n    }\n  };\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      destroy: close,\n      update: function update(newConfig) {\n        setInnerConfig(function (originConfig) {\n          return _extends(_extends({}, originConfig), newConfig);\n        });\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Modal\",\n    defaultLocale: defaultLocale.Modal\n  }, function (modalLocale) {\n    return /*#__PURE__*/React.createElement(ConfirmDialog, _extends({\n      prefixCls: prefixCls,\n      rootPrefixCls: rootPrefixCls\n    }, innerConfig, {\n      close: close,\n      visible: visible,\n      afterClose: afterClose,\n      okText: innerConfig.okText || (innerConfig.okCancel ? modalLocale.okText : modalLocale.justOkText),\n      direction: direction,\n      cancelText: innerConfig.cancelText || modalLocale.cancelText\n    }));\n  });\n};\n\nexport default /*#__PURE__*/React.forwardRef(HookModal);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,SAASC,aAAa,QAAQ,uBAAuB;AAErD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC5C,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,MAAM,GAAGH,IAAI,CAACG,MAAM;EAExB,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,IAAI,CAAC;IACtCC,gBAAgB,GAAGb,cAAc,CAACW,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIG,gBAAgB,GAAGf,KAAK,CAACW,QAAQ,CAACF,MAAM,CAAC;IACzCO,gBAAgB,GAAGjB,cAAc,CAACgB,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,iBAAiB,GAAGnB,KAAK,CAACoB,UAAU,CAAChB,aAAa,CAAC;IACnDiB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;EAEjD,IAAIC,SAAS,GAAGD,YAAY,CAAC,OAAO,CAAC;EACrC,IAAIE,aAAa,GAAGF,YAAY,CAAC,CAAC;EAElC,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3BX,UAAU,CAAC,KAAK,CAAC;IAEjB,KAAK,IAAIY,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEA,IAAIC,aAAa,GAAGH,IAAI,CAACI,IAAI,CAAC,UAAUC,KAAK,EAAE;MAC7C,OAAOA,KAAK,IAAIA,KAAK,CAACF,aAAa;IACrC,CAAC,CAAC;IAEF,IAAIf,WAAW,CAACkB,QAAQ,IAAIH,aAAa,EAAE;MACzCf,WAAW,CAACkB,QAAQ,CAAC,CAAC;IACxB;EACF,CAAC;EAEDnC,KAAK,CAACoC,mBAAmB,CAAC7B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL8B,OAAO,EAAEZ,KAAK;MACda,MAAM,EAAE,SAASA,MAAMA,CAACC,SAAS,EAAE;QACjCrB,cAAc,CAAC,UAAUsB,YAAY,EAAE;UACrC,OAAO1C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,YAAY,CAAC,EAAED,SAAS,CAAC;QACxD,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAavC,KAAK,CAACyC,aAAa,CAACtC,cAAc,EAAE;IACtDuC,aAAa,EAAE,OAAO;IACtBxC,aAAa,EAAEA,aAAa,CAACyC;EAC/B,CAAC,EAAE,UAAUC,WAAW,EAAE;IACxB,OAAO,aAAa5C,KAAK,CAACyC,aAAa,CAACxC,aAAa,EAAEH,QAAQ,CAAC;MAC9DyB,SAAS,EAAEA,SAAS;MACpBC,aAAa,EAAEA;IACjB,CAAC,EAAEP,WAAW,EAAE;MACdQ,KAAK,EAAEA,KAAK;MACZZ,OAAO,EAAEA,OAAO;MAChBL,UAAU,EAAEA,UAAU;MACtBqC,MAAM,EAAE5B,WAAW,CAAC4B,MAAM,KAAK5B,WAAW,CAAC6B,QAAQ,GAAGF,WAAW,CAACC,MAAM,GAAGD,WAAW,CAACG,UAAU,CAAC;MAClG1B,SAAS,EAAEA,SAAS;MACpB2B,UAAU,EAAE/B,WAAW,CAAC+B,UAAU,IAAIJ,WAAW,CAACI;IACpD,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAED,eAAe,aAAahD,KAAK,CAACiD,UAAU,CAAC5C,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}