export default MetaElements;
declare class MetaElements extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['MetaElements']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['MetaElements']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=meta-elements.d.ts.map