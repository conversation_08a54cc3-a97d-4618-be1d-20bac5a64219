{"ast": null, "code": "import * as React from 'react';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport ExpandedRowContext from '../context/ExpandedRowContext';\nfunction ExpandedRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    children = _ref.children,\n    Component = _ref.component,\n    cellComponent = _ref.cellComponent,\n    className = _ref.className,\n    expanded = _ref.expanded,\n    colSpan = _ref.colSpan,\n    isEmpty = _ref.isEmpty;\n  var _React$useContext = React.useContext(TableContext),\n    scrollbarSize = _React$useContext.scrollbarSize;\n  var _React$useContext2 = React.useContext(ExpandedRowContext),\n    fixHeader = _React$useContext2.fixHeader,\n    fixColumn = _React$useContext2.fixColumn,\n    componentWidth = _React$useContext2.componentWidth,\n    horizonScroll = _React$useContext2.horizonScroll; // Cache render node\n\n  return React.useMemo(function () {\n    var contentNode = children;\n    if (isEmpty ? horizonScroll : fixColumn) {\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        style: {\n          width: componentWidth - (fixHeader ? scrollbarSize : 0),\n          position: 'sticky',\n          left: 0,\n          overflow: 'hidden'\n        },\n        className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n      }, contentNode);\n    }\n    return /*#__PURE__*/React.createElement(Component, {\n      className: className,\n      style: {\n        display: expanded ? null : 'none'\n      }\n    }, /*#__PURE__*/React.createElement(Cell, {\n      component: cellComponent,\n      prefixCls: prefixCls,\n      colSpan: colSpan\n    }, contentNode));\n  }, [children, Component, className, expanded, colSpan, isEmpty, scrollbarSize, componentWidth, fixColumn, fixHeader, horizonScroll]);\n}\nexport default ExpandedRow;", "map": {"version": 3, "names": ["React", "Cell", "TableContext", "ExpandedRowContext", "ExpandedRow", "_ref", "prefixCls", "children", "Component", "component", "cellComponent", "className", "expanded", "colSpan", "isEmpty", "_React$useContext", "useContext", "scrollbarSize", "_React$useContext2", "fixHeader", "fixColumn", "componentWidth", "horizonScroll", "useMemo", "contentNode", "createElement", "style", "width", "position", "left", "overflow", "concat", "display"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Body/ExpandedRow.js"], "sourcesContent": ["import * as React from 'react';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport ExpandedRowContext from '../context/ExpandedRowContext';\n\nfunction ExpandedRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n      children = _ref.children,\n      Component = _ref.component,\n      cellComponent = _ref.cellComponent,\n      className = _ref.className,\n      expanded = _ref.expanded,\n      colSpan = _ref.colSpan,\n      isEmpty = _ref.isEmpty;\n\n  var _React$useContext = React.useContext(TableContext),\n      scrollbarSize = _React$useContext.scrollbarSize;\n\n  var _React$useContext2 = React.useContext(ExpandedRowContext),\n      fixHeader = _React$useContext2.fixHeader,\n      fixColumn = _React$useContext2.fixColumn,\n      componentWidth = _React$useContext2.componentWidth,\n      horizonScroll = _React$useContext2.horizonScroll; // Cache render node\n\n\n  return React.useMemo(function () {\n    var contentNode = children;\n\n    if (isEmpty ? horizonScroll : fixColumn) {\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        style: {\n          width: componentWidth - (fixHeader ? scrollbarSize : 0),\n          position: 'sticky',\n          left: 0,\n          overflow: 'hidden'\n        },\n        className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n      }, contentNode);\n    }\n\n    return /*#__PURE__*/React.createElement(Component, {\n      className: className,\n      style: {\n        display: expanded ? null : 'none'\n      }\n    }, /*#__PURE__*/React.createElement(Cell, {\n      component: cellComponent,\n      prefixCls: prefixCls,\n      colSpan: colSpan\n    }, contentNode));\n  }, [children, Component, className, expanded, colSpan, isEmpty, scrollbarSize, componentWidth, fixColumn, fixHeader, horizonScroll]);\n}\n\nexport default ExpandedRow;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,kBAAkB,MAAM,+BAA+B;AAE9D,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,SAAS,GAAGH,IAAI,CAACI,SAAS;IAC1BC,aAAa,GAAGL,IAAI,CAACK,aAAa;IAClCC,SAAS,GAAGN,IAAI,CAACM,SAAS;IAC1BC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,OAAO,GAAGR,IAAI,CAACQ,OAAO;IACtBC,OAAO,GAAGT,IAAI,CAACS,OAAO;EAE1B,IAAIC,iBAAiB,GAAGf,KAAK,CAACgB,UAAU,CAACd,YAAY,CAAC;IAClDe,aAAa,GAAGF,iBAAiB,CAACE,aAAa;EAEnD,IAAIC,kBAAkB,GAAGlB,KAAK,CAACgB,UAAU,CAACb,kBAAkB,CAAC;IACzDgB,SAAS,GAAGD,kBAAkB,CAACC,SAAS;IACxCC,SAAS,GAAGF,kBAAkB,CAACE,SAAS;IACxCC,cAAc,GAAGH,kBAAkB,CAACG,cAAc;IAClDC,aAAa,GAAGJ,kBAAkB,CAACI,aAAa,CAAC,CAAC;;EAGtD,OAAOtB,KAAK,CAACuB,OAAO,CAAC,YAAY;IAC/B,IAAIC,WAAW,GAAGjB,QAAQ;IAE1B,IAAIO,OAAO,GAAGQ,aAAa,GAAGF,SAAS,EAAE;MACvCI,WAAW,GAAG,aAAaxB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;QACpDC,KAAK,EAAE;UACLC,KAAK,EAAEN,cAAc,IAAIF,SAAS,GAAGF,aAAa,GAAG,CAAC,CAAC;UACvDW,QAAQ,EAAE,QAAQ;UAClBC,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE;QACZ,CAAC;QACDnB,SAAS,EAAE,EAAE,CAACoB,MAAM,CAACzB,SAAS,EAAE,qBAAqB;MACvD,CAAC,EAAEkB,WAAW,CAAC;IACjB;IAEA,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACjB,SAAS,EAAE;MACjDG,SAAS,EAAEA,SAAS;MACpBe,KAAK,EAAE;QACLM,OAAO,EAAEpB,QAAQ,GAAG,IAAI,GAAG;MAC7B;IACF,CAAC,EAAE,aAAaZ,KAAK,CAACyB,aAAa,CAACxB,IAAI,EAAE;MACxCQ,SAAS,EAAEC,aAAa;MACxBJ,SAAS,EAAEA,SAAS;MACpBO,OAAO,EAAEA;IACX,CAAC,EAAEW,WAAW,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjB,QAAQ,EAAEC,SAAS,EAAEG,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEG,aAAa,EAAEI,cAAc,EAAED,SAAS,EAAED,SAAS,EAAEG,aAAa,CAAC,CAAC;AACtI;AAEA,eAAelB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}