{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = createUseNotification;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useNotification = _interopRequireDefault(require(\"rc-notification/lib/useNotification\"));\nvar _configProvider = require(\"../../config-provider\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction createUseNotification(getNotificationInstance, getRCNoticeProps) {\n  var useNotification = function useNotification() {\n    // We can only get content by render\n    var getPrefixCls; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = (0, _useNotification[\"default\"])(proxy),\n      _useRCNotification2 = (0, _slicedToArray2[\"default\"])(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('notification', customizePrefixCls);\n      getNotificationInstance((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n        prefixCls: mergedPrefixCls\n      }), function (_ref) {\n        var prefixCls = _ref.prefixCls,\n          instance = _ref.instance;\n        innerInstance = instance;\n        hookNotify(getRCNoticeProps(args, prefixCls));\n      });\n    } // Fill functions\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    ['success', 'info', 'warning', 'error'].forEach(function (type) {\n      hookApiRef.current[type] = function (args) {\n        return hookApiRef.current.open((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n          type: type\n        }));\n      };\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(_configProvider.ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      return holder;\n    })];\n  };\n  return useNotification;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "createUseNotification", "_extends2", "_slicedToArray2", "React", "_interopRequireWildcard", "_useNotification", "_config<PERSON><PERSON><PERSON>", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "getNotificationInstance", "getRCNoticeProps", "useNotification", "getPrefixCls", "innerInstance", "proxy", "add", "noticeProps", "<PERSON><PERSON><PERSON><PERSON>", "component", "_useRCNotification", "_useRCNotification2", "hookNotify", "holder", "notify", "args", "customizePrefixCls", "prefixCls", "mergedPrefixCls", "_ref", "instance", "hookApiRef", "useRef", "current", "open", "for<PERSON>ach", "type", "createElement", "ConfigConsumer", "context"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/notification/hooks/useNotification.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = createUseNotification;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _useNotification = _interopRequireDefault(require(\"rc-notification/lib/useNotification\"));\n\nvar _configProvider = require(\"../../config-provider\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction createUseNotification(getNotificationInstance, getRCNoticeProps) {\n  var useNotification = function useNotification() {\n    // We can only get content by render\n    var getPrefixCls; // We create a proxy to handle delay created instance\n\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n\n    var _useRCNotification = (0, _useNotification[\"default\"])(proxy),\n        _useRCNotification2 = (0, _slicedToArray2[\"default\"])(_useRCNotification, 2),\n        hookNotify = _useRCNotification2[0],\n        holder = _useRCNotification2[1];\n\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('notification', customizePrefixCls);\n      getNotificationInstance((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n        prefixCls: mergedPrefixCls\n      }), function (_ref) {\n        var prefixCls = _ref.prefixCls,\n            instance = _ref.instance;\n        innerInstance = instance;\n        hookNotify(getRCNoticeProps(args, prefixCls));\n      });\n    } // Fill functions\n\n\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    ['success', 'info', 'warning', 'error'].forEach(function (type) {\n      hookApiRef.current[type] = function (args) {\n        return hookApiRef.current.open((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n          type: type\n        }));\n      };\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(_configProvider.ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      return holder;\n    })];\n  };\n\n  return useNotification;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,qBAAqB;AAE1C,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIQ,eAAe,GAAGT,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAE7F,IAAIS,KAAK,GAAGC,uBAAuB,CAACV,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIW,gBAAgB,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE7F,IAAIY,eAAe,GAAGZ,OAAO,CAAC,uBAAuB,CAAC;AAEtD,SAASa,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASJ,uBAAuBA,CAACQ,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIjB,OAAO,CAACiB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGtB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACuB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIxB,MAAM,CAACyB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGtB,MAAM,CAACuB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE7B,MAAM,CAACC,cAAc,CAACoB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,SAASjB,qBAAqBA,CAAC0B,uBAAuB,EAAEC,gBAAgB,EAAE;EACxE,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C;IACA,IAAIC,YAAY,CAAC,CAAC;;IAElB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,KAAK,GAAG;MACVC,GAAG,EAAE,SAASA,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAE;QAC7CJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,SAAS,CAACH,GAAG,CAACC,WAAW,EAAEC,cAAc,CAAC;MACxH;IACF,CAAC;IAED,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAE/B,gBAAgB,CAAC,SAAS,CAAC,EAAE0B,KAAK,CAAC;MAC5DM,mBAAmB,GAAG,CAAC,CAAC,EAAEnC,eAAe,CAAC,SAAS,CAAC,EAAEkC,kBAAkB,EAAE,CAAC,CAAC;MAC5EE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;MACnCE,MAAM,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASG,MAAMA,CAACC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAGD,IAAI,CAACE,SAAS;MACvC,IAAIC,eAAe,GAAGf,YAAY,CAAC,cAAc,EAAEa,kBAAkB,CAAC;MACtEhB,uBAAuB,CAAC,CAAC,CAAC,EAAEzB,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEwC,IAAI,CAAC,EAAE;QACrFE,SAAS,EAAEC;MACb,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;QAClB,IAAIF,SAAS,GAAGE,IAAI,CAACF,SAAS;UAC1BG,QAAQ,GAAGD,IAAI,CAACC,QAAQ;QAC5BhB,aAAa,GAAGgB,QAAQ;QACxBR,UAAU,CAACX,gBAAgB,CAACc,IAAI,EAAEE,SAAS,CAAC,CAAC;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAII,UAAU,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,CAAC,CAAC,CAAC;IACjCD,UAAU,CAACE,OAAO,CAACC,IAAI,GAAGV,MAAM;IAChC,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAACW,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC9DL,UAAU,CAACE,OAAO,CAACG,IAAI,CAAC,GAAG,UAAUX,IAAI,EAAE;QACzC,OAAOM,UAAU,CAACE,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEwC,IAAI,CAAC,EAAE;UAC5FW,IAAI,EAAEA;QACR,CAAC,CAAC,CAAC;MACL,CAAC;IACH,CAAC,CAAC;IACF,OAAO,CAACL,UAAU,CAACE,OAAO,EAAE,aAAa9C,KAAK,CAACkD,aAAa,CAAC/C,eAAe,CAACgD,cAAc,EAAE;MAC3FlC,GAAG,EAAE;IACP,CAAC,EAAE,UAAUmC,OAAO,EAAE;MACpB1B,YAAY,GAAG0B,OAAO,CAAC1B,YAAY;MACnC,OAAOU,MAAM;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOX,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}