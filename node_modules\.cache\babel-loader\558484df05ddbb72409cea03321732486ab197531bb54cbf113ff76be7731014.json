{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiAnagrafica.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiAnagrafica = props => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState('');\n  const [results2, setResults2] = useState('');\n  const [results3, setResults3] = useState('');\n  const [results4, setResults4] = useState('');\n  const [results5, setResults5] = useState('');\n  const [results6, setResults6] = useState('');\n  const [results7, setResults7] = useState('');\n  const [results8, setResults8] = useState('');\n  const [results9, setResults9] = useState('');\n  const [results10, setResults10] = useState(null);\n  const [modPag, setModPag] = useState(null);\n  const toast = useRef(null);\n  //Setto le variabili di stato con il valore di props mediante useEffect\n  useEffect(() => {\n    async function renderString() {\n      if (props.result !== undefined) {\n        setResults(props.result.firstName);\n        setResults2(props.result.idRetailer.idRegistry.email);\n        setResults3(props.result.idRetailer.idRegistry.tel);\n        setResults4(props.result.idRetailer.idRegistry.pIva);\n        setResults5(props.result.idRetailer.idRegistry.address);\n        setResults6(props.result.idRetailer.idRegistry.city);\n        setResults7(props.result.idRetailer.idRegistry.cap);\n        setResults8(props.result.idRetailer.idRegistry.lastName);\n        setResults9(props.result.idRetailer.idRegistry.tel);\n        setResults10(props.result.idRetailer.idRegistry.paymentMetod);\n      }\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'paymentmethods/').then(res => {\n        var pm = [];\n        res.data.forEach(element => {\n          var x = {\n            name: element.description,\n            code: element.description\n          };\n          pm.push(x);\n        });\n        setModPag(pm);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    renderString();\n  }, [props.result]);\n  // Funzione di validazione\n  const validateForm = () => {\n    const errors = [];\n\n    // Validazione campi obbligatori\n    if (!results || results.trim() === '') {\n      errors.push('📝 Nome è obbligatorio');\n    }\n    if (!results8 || results8.trim() === '') {\n      errors.push('📝 Cognome è obbligatorio');\n    }\n    if (!results4 || results4.trim() === '') {\n      errors.push('📝 Partita IVA è obbligatoria');\n    }\n\n    // Validazione formato P.IVA (11 cifre)\n    if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n    }\n\n    // Validazione email obbligatoria\n    if (!results2 || results2.trim() === '') {\n      errors.push('📧 Email è obbligatoria');\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n      errors.push('📧 Indirizzo email non valido');\n    }\n\n    // Validazione contatti: almeno uno tra telefono e cellulare\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (!hasTelefono && !hasCellulare) {\n      errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n    }\n\n    // Validazione formato telefono (se presente)\n    if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n      errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n    }\n\n    // Validazione formato cellulare (se presente)\n    if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n      errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n    }\n    return errors;\n  };\n  const Invia = async () => {\n    // Validazione frontend\n    const validationErrors = validateForm();\n    if (validationErrors.length > 0) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ Dati mancanti o non validi',\n        detail: validationErrors.join('. '),\n        life: 5000\n      });\n      return;\n    }\n\n    // Verifica che tutti i campi obbligatori siano compilati\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n      var corpo = {\n        firstName: results,\n        lastName: results8,\n        email: results2,\n        telnum: results3,\n        cellnum: results9,\n        pIva: results4,\n        address: results5,\n        city: results6,\n        cap: results7,\n        paymentMetod: (results10 === null || results10 === void 0 ? void 0 : results10.name) || ''\n      };\n      //Chiamata axios per la creazione del registry\n      await APIRequest('POST', 'registry/', corpo).then(res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Successo',\n          detail: \"L'anagrafica è stata inserita con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      }).catch(e => {\n        var _e$response, _e$response2, _e$response3, _e$response3$data;\n        console.error('❌ Errore creazione anagrafica:', e);\n\n        // Gestione specifica degli errori\n        const errorStatus = (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status;\n        const errorData = (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data;\n        const errorMessage = ((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : (_e$response3$data = _e$response3.data) === null || _e$response3$data === void 0 ? void 0 : _e$response3$data.message) || e.message;\n        let userMessage = '';\n        let summary = 'Errore';\n        if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') || errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n          // Violazione unique constraint (P.IVA duplicata)\n          summary = '⚠️ Partita IVA già presente';\n          userMessage = \"La Partita IVA \\\"\".concat(results4, \"\\\" \\xE8 gi\\xE0 registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.\");\n        } else if (errorStatus === 400) {\n          // Errori di validazione\n          summary = '📝 Dati non validi';\n          if (errorMessage.toLowerCase().includes('email')) {\n            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n          } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n          } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n            userMessage = 'Il numero di telefono inserito non è valido.';\n          } else {\n            userMessage = \"Dati inseriti non validi: \".concat(errorMessage);\n          }\n        } else if (errorStatus === 422) {\n          // Errori di business logic\n          summary = '🚫 Operazione non consentita';\n          userMessage = \"Impossibile completare l'operazione: \".concat(errorMessage);\n        } else if (errorStatus === 500 || errorStatus === 501) {\n          // Errori del server\n          summary = '🔧 Errore del server';\n          userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n        } else if (errorStatus === 503) {\n          // Servizio non disponibile\n          summary = '⏱️ Servizio temporaneamente non disponibile';\n          userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n        } else if (!e.response) {\n          // Errori di rete\n          summary = '🌐 Errore di connessione';\n          userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n        } else {\n          // Altri errori\n          summary = '❌ Errore imprevisto';\n          userMessage = \"Si \\xE8 verificato un errore imprevisto: \".concat(errorMessage);\n        }\n        toast.current.show({\n          severity: 'error',\n          summary: summary,\n          detail: userMessage,\n          life: 6000\n        });\n\n        // Log dettagliato per debugging\n        console.error('Dettagli errore:', {\n          status: errorStatus,\n          data: errorData,\n          message: errorMessage,\n          fullError: e\n        });\n      });\n    } else {\n      toast.current.show({\n        severity: 'warn',\n        summary: '📝 Campi obbligatori mancanti',\n        detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n        life: 6000\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-grid p-fluid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Nome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results,\n            onChange: e => setResults(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci nome (obbligatorio)\",\n            editable: \"true\",\n            className: !results || results.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cognome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results8,\n            onChange: e => setResults8(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci cognome (obbligatorio)\",\n            editable: \"true\",\n            className: !results8 || results8.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Email, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-envelope\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"email\",\n            value: results2,\n            onChange: e => setResults2(e.target.value),\n            placeholder: \"Inserisci email (obbligatoria)\",\n            editable: \"true\",\n            className: !results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 21\n        }, this), (!results2 || results2.trim() === '') && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Email \\xE8 obbligatoria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 25\n        }, this), results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato email non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Tel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-mobile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results3,\n            onChange: e => setResults3(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci telefono\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Cell\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-mobile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results9,\n            onChange: e => setResults9(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci cellulare\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.pIva, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-credit-card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results4,\n            onChange: e => setResults4(e.target.value),\n            keyfilter: /^[\\d\\s]+$/,\n            placeholder: \"Inserisci partita IVA (11 cifre)\",\n            editable: \"true\",\n            maxLength: 11,\n            className: !results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this), results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"La Partita IVA deve contenere esattamente 11 cifre\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-directions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results5,\n            onChange: e => setResults5(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci indirizzo\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results6,\n            onChange: e => setResults6(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci citt\\xE0\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results7,\n            onChange: e => setResults7(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci C.A.P.\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Pagamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"w-100\",\n            value: results10,\n            options: modPag,\n            onChange: e => setResults10(e.target.value),\n            optionLabel: \"name\",\n            placeholder: \"Seleziona metodo di pagamento\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-2 mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        id: \"invia\",\n        className: \"p-button saveList justify-content-center float-right ionicon mx-0 w-50\",\n        onClick: Invia,\n        children: Costanti.salva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiAnagrafica, \"ybjpF41zJzhi8oiwXqejqyVKR2Y=\");\n_c = AggiungiAnagrafica;\nexport default AggiungiAnagrafica;\nvar _c;\n$RefreshReg$(_c, \"AggiungiAnagrafica\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "InputText", "<PERSON><PERSON>", "APIRequest", "Toast", "<PERSON><PERSON>", "Dropdown", "jsxDEV", "_jsxDEV", "AggiungiAnagrafica", "props", "_s", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "results4", "setResults4", "results5", "setResults5", "results6", "setResults6", "results7", "setResults7", "results8", "setResults8", "results9", "setResults9", "results10", "setResults10", "modPag", "setModPag", "toast", "renderString", "result", "undefined", "firstName", "idRetailer", "idRegistry", "email", "tel", "pIva", "address", "city", "cap", "lastName", "paymentMetod", "then", "res", "pm", "data", "for<PERSON>ach", "element", "x", "name", "description", "code", "push", "catch", "e", "console", "log", "validateForm", "errors", "trim", "test", "replace", "hasTelefono", "has<PERSON><PERSON><PERSON><PERSON>", "Invia", "validationErrors", "length", "current", "show", "severity", "summary", "detail", "join", "life", "corpo", "telnum", "cellnum", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "_e$response3", "_e$response3$data", "error", "errorStatus", "response", "status", "errorData", "errorMessage", "message", "userMessage", "toLowerCase", "includes", "concat", "fullError", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Nome", "style", "color", "type", "value", "onChange", "target", "keyfilter", "placeholder", "editable", "Cognome", "Email", "Tel", "Cell", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Pagamento", "options", "optionLabel", "filter", "filterBy", "id", "onClick", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiAnagrafica.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { <PERSON>nti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\n\nconst AggiungiAnagrafica = (props) => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState('');\n    const [results2, setResults2] = useState('');\n    const [results3, setResults3] = useState('');\n    const [results4, setResults4] = useState('');\n    const [results5, setResults5] = useState('');\n    const [results6, setResults6] = useState('');\n    const [results7, setResults7] = useState('');\n    const [results8, setResults8] = useState('');\n    const [results9, setResults9] = useState('');\n    const [results10, setResults10] = useState(null);\n    const [modPag, setModPag] = useState(null);\n    const toast = useRef(null);\n    //Setto le variabili di stato con il valore di props mediante useEffect\n    useEffect(() => {\n        async function renderString() {\n            if (props.result !== undefined) {\n                setResults(props.result.firstName)\n                setResults2(props.result.idRetailer.idRegistry.email)\n                setResults3(props.result.idRetailer.idRegistry.tel)\n                setResults4(props.result.idRetailer.idRegistry.pIva)\n                setResults5(props.result.idRetailer.idRegistry.address)\n                setResults6(props.result.idRetailer.idRegistry.city)\n                setResults7(props.result.idRetailer.idRegistry.cap)\n                setResults8(props.result.idRetailer.idRegistry.lastName)\n                setResults9(props.result.idRetailer.idRegistry.tel)\n                setResults10(props.result.idRetailer.idRegistry.paymentMetod)\n            }\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'paymentmethods/')\n                .then(res => {\n                    var pm = []\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.description,\n                            code: element.description\n                        }\n                        pm.push(x)\n                    });\n                    setModPag(pm)\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        renderString();\n    }, [props.result]);\n    // Funzione di validazione\n    const validateForm = () => {\n        const errors = [];\n\n        // Validazione campi obbligatori\n        if (!results || results.trim() === '') {\n            errors.push('📝 Nome è obbligatorio');\n        }\n        if (!results8 || results8.trim() === '') {\n            errors.push('📝 Cognome è obbligatorio');\n        }\n        if (!results4 || results4.trim() === '') {\n            errors.push('📝 Partita IVA è obbligatoria');\n        }\n\n        // Validazione formato P.IVA (11 cifre)\n        if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n        }\n\n        // Validazione email obbligatoria\n        if (!results2 || results2.trim() === '') {\n            errors.push('📧 Email è obbligatoria');\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n            errors.push('📧 Indirizzo email non valido');\n        }\n\n        // Validazione contatti: almeno uno tra telefono e cellulare\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (!hasTelefono && !hasCellulare) {\n            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n        }\n\n        // Validazione formato telefono (se presente)\n        if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n        }\n\n        // Validazione formato cellulare (se presente)\n        if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n        }\n\n        return errors;\n    };\n\n    const Invia = async () => {\n        // Validazione frontend\n        const validationErrors = validateForm();\n        if (validationErrors.length > 0) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ Dati mancanti o non validi',\n                detail: validationErrors.join('. '),\n                life: 5000\n            });\n            return;\n        }\n\n        // Verifica che tutti i campi obbligatori siano compilati\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (results && results8 && results4 && results2 && (hasTelefono || hasCellulare)) {\n            var corpo = {\n                firstName: results,\n                lastName: results8,\n                email: results2,\n                telnum: results3,\n                cellnum: results9,\n                pIva: results4,\n                address: results5,\n                city: results6,\n                cap: results7,\n                paymentMetod: results10?.name || ''\n            }\n            //Chiamata axios per la creazione del registry\n            await APIRequest('POST', 'registry/', corpo)\n                .then(res => {\n                    console.log(res.data);\n                    toast.current.show({\n                        severity: 'success',\n                        summary: '✅ Successo',\n                        detail: \"L'anagrafica è stata inserita con successo\",\n                        life: 3000\n                    });\n                    setTimeout(() => {\n                        window.location.reload()\n                    }, 3000)\n                }).catch((e) => {\n                    console.error('❌ Errore creazione anagrafica:', e);\n\n                    // Gestione specifica degli errori\n                    const errorStatus = e.response?.status;\n                    const errorData = e.response?.data;\n                    const errorMessage = e.response?.data?.message || e.message;\n\n                    let userMessage = '';\n                    let summary = 'Errore';\n\n                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||\n                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n                        // Violazione unique constraint (P.IVA duplicata)\n                        summary = '⚠️ Partita IVA già presente';\n                        userMessage = `La Partita IVA \"${results4}\" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;\n                    } else if (errorStatus === 400) {\n                        // Errori di validazione\n                        summary = '📝 Dati non validi';\n                        if (errorMessage.toLowerCase().includes('email')) {\n                            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n                            userMessage = 'Il numero di telefono inserito non è valido.';\n                        } else {\n                            userMessage = `Dati inseriti non validi: ${errorMessage}`;\n                        }\n                    } else if (errorStatus === 422) {\n                        // Errori di business logic\n                        summary = '🚫 Operazione non consentita';\n                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;\n                    } else if (errorStatus === 500 || errorStatus === 501) {\n                        // Errori del server\n                        summary = '🔧 Errore del server';\n                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n                    } else if (errorStatus === 503) {\n                        // Servizio non disponibile\n                        summary = '⏱️ Servizio temporaneamente non disponibile';\n                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n                    } else if (!e.response) {\n                        // Errori di rete\n                        summary = '🌐 Errore di connessione';\n                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n                    } else {\n                        // Altri errori\n                        summary = '❌ Errore imprevisto';\n                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;\n                    }\n\n                    toast.current.show({\n                        severity: 'error',\n                        summary: summary,\n                        detail: userMessage,\n                        life: 6000\n                    });\n\n                    // Log dettagliato per debugging\n                    console.error('Dettagli errore:', {\n                        status: errorStatus,\n                        data: errorData,\n                        message: errorMessage,\n                        fullError: e\n                    });\n                })\n        } else {\n            toast.current.show({\n                severity: 'warn',\n                summary: '📝 Campi obbligatori mancanti',\n                detail: \"Compilare tutti i campi obbligatori: Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\",\n                life: 6000\n            });\n        }\n    };\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"p-grid p-fluid\">\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results}\n                            onChange={(e) => setResults(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci nome (obbligatorio)\"\n                            editable='true'\n                            className={!results || results.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cognome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results8}\n                            onChange={(e) => setResults8(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci cognome (obbligatorio)\"\n                            editable='true'\n                            className={!results8 || results8.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-envelope\"></i>\n                        </span>\n                        <InputText\n                            type=\"email\"\n                            value={results2}\n                            onChange={(e) => setResults2(e.target.value)}\n                            placeholder=\"Inserisci email (obbligatoria)\"\n                            editable='true'\n                            className={!results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {(!results2 || results2.trim() === '') && (\n                        <small className=\"p-error\">Email è obbligatoria</small>\n                    )}\n                    {results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && (\n                        <small className=\"p-error\">Formato email non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Tel}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-mobile\"></i>\n                        </span>\n                        <InputText type=\"tel\" value={results3} onChange={(e) => setResults3(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci telefono\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cell}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-mobile\"></i>\n                        </span>\n                        <InputText type=\"tel\" value={results9} onChange={(e) => setResults9(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci cellulare\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-credit-card\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results4}\n                            onChange={(e) => setResults4(e.target.value)}\n                            keyfilter={/^[\\d\\s]+$/}\n                            placeholder=\"Inserisci partita IVA (11 cifre)\"\n                            editable='true'\n                            maxLength={11}\n                            className={!results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && (\n                        <small className=\"p-error\">La Partita IVA deve contenere esattamente 11 cifre</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Indirizzo}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-directions\"></i>\n                        </span>\n                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci indirizzo\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Città}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci città\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.CodPost}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci C.A.P.\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Pagamento}</h6>\n                    <div className=\"p-inputgroup\">\n                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel=\"name\" placeholder=\"Seleziona metodo di pagamento\" filter filterBy='name' />\n                    </div>\n                </div>\n            </div>\n            <div className=\"d-flex justify-content-center mb-2 mt-4\">\n                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                <Button id=\"invia\" className=\"p-button saveList justify-content-center float-right ionicon mx-0 w-50\" onClick={Invia}>{Costanti.salva}</Button>\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiAnagrafica;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAMoC,KAAK,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,SAAS,CAAC,MAAM;IACZ,eAAemC,YAAYA,CAAA,EAAG;MAC1B,IAAIzB,KAAK,CAAC0B,MAAM,KAAKC,SAAS,EAAE;QAC5BxB,UAAU,CAACH,KAAK,CAAC0B,MAAM,CAACE,SAAS,CAAC;QAClCvB,WAAW,CAACL,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACC,KAAK,CAAC;QACrDxB,WAAW,CAACP,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDvB,WAAW,CAACT,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACG,IAAI,CAAC;QACpDtB,WAAW,CAACX,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACI,OAAO,CAAC;QACvDrB,WAAW,CAACb,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACK,IAAI,CAAC;QACpDpB,WAAW,CAACf,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACM,GAAG,CAAC;QACnDnB,WAAW,CAACjB,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACO,QAAQ,CAAC;QACxDlB,WAAW,CAACnB,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDX,YAAY,CAACrB,KAAK,CAAC0B,MAAM,CAACG,UAAU,CAACC,UAAU,CAACQ,YAAY,CAAC;MACjE;MACA;MACA,MAAM7C,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC,CACrC8C,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,EAAE,GAAG,EAAE;QACXD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACG,WAAW;YACzBC,IAAI,EAAEJ,OAAO,CAACG;UAClB,CAAC;UACDN,EAAE,CAACQ,IAAI,CAACJ,CAAC,CAAC;QACd,CAAC,CAAC;QACFtB,SAAS,CAACkB,EAAE,CAAC;MACjB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA1B,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAACzB,KAAK,CAAC0B,MAAM,CAAC,CAAC;EAClB;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI,CAACrD,OAAO,IAAIA,OAAO,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnCD,MAAM,CAACN,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,IAAI,CAACjC,QAAQ,IAAIA,QAAQ,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,2BAA2B,CAAC;IAC5C;IACA,IAAI,CAACzC,QAAQ,IAAIA,QAAQ,CAACgD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,IAAIzC,QAAQ,IAAI,CAAC,UAAU,CAACiD,IAAI,CAACjD,QAAQ,CAACkD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC3DH,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;;IAEA;IACA,IAAI,CAAC7C,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAACQ,IAAI,CAACrD,QAAQ,CAAC,EAAE;MACrDmD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,MAAMU,WAAW,GAAGrD,QAAQ,IAAIA,QAAQ,CAACkD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG1C,QAAQ,IAAIA,QAAQ,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAI,CAACG,WAAW,IAAI,CAACC,YAAY,EAAE;MAC/BL,MAAM,CAACN,IAAI,CAAC,8DAA8D,CAAC;IAC/E;;IAEA;IACA,IAAI3C,QAAQ,IAAI,CAAC,uBAAuB,CAACmD,IAAI,CAACnD,QAAQ,CAAC,EAAE;MACrDiD,MAAM,CAACN,IAAI,CAAC,yDAAyD,CAAC;IAC1E;;IAEA;IACA,IAAI/B,QAAQ,IAAI,CAAC,uBAAuB,CAACuC,IAAI,CAACvC,QAAQ,CAAC,EAAE;MACrDqC,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;IAEA,OAAOM,MAAM;EACjB,CAAC;EAED,MAAMM,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB;IACA,MAAMC,gBAAgB,GAAGR,YAAY,CAAC,CAAC;IACvC,IAAIQ,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7BvC,KAAK,CAACwC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAEN,gBAAgB,CAACO,IAAI,CAAC,IAAI,CAAC;QACnCC,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;;IAEA;IACA,MAAMX,WAAW,GAAGrD,QAAQ,IAAIA,QAAQ,CAACkD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAG1C,QAAQ,IAAIA,QAAQ,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAItD,OAAO,IAAIc,QAAQ,IAAIR,QAAQ,IAAIJ,QAAQ,KAAKuD,WAAW,IAAIC,YAAY,CAAC,EAAE;MAC9E,IAAIW,KAAK,GAAG;QACR3C,SAAS,EAAE1B,OAAO;QAClBmC,QAAQ,EAAErB,QAAQ;QAClBe,KAAK,EAAE3B,QAAQ;QACfoE,MAAM,EAAElE,QAAQ;QAChBmE,OAAO,EAAEvD,QAAQ;QACjBe,IAAI,EAAEzB,QAAQ;QACd0B,OAAO,EAAExB,QAAQ;QACjByB,IAAI,EAAEvB,QAAQ;QACdwB,GAAG,EAAEtB,QAAQ;QACbwB,YAAY,EAAE,CAAAlB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0B,IAAI,KAAI;MACrC,CAAC;MACD;MACA,MAAMrD,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE8E,KAAK,CAAC,CACvChC,IAAI,CAACC,GAAG,IAAI;QACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACE,IAAI,CAAC;QACrBlB,KAAK,CAACwC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,4CAA4C;UACpDE,IAAI,EAAE;QACV,CAAC,CAAC;QACFI,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAAC3B,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAA2B,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA;QACZ7B,OAAO,CAAC8B,KAAK,CAAC,gCAAgC,EAAE/B,CAAC,CAAC;;QAElD;QACA,MAAMgC,WAAW,IAAAL,WAAA,GAAG3B,CAAC,CAACiC,QAAQ,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,MAAM;QACtC,MAAMC,SAAS,IAAAP,YAAA,GAAG5B,CAAC,CAACiC,QAAQ,cAAAL,YAAA,uBAAVA,YAAA,CAAYrC,IAAI;QAClC,MAAM6C,YAAY,GAAG,EAAAP,YAAA,GAAA7B,CAAC,CAACiC,QAAQ,cAAAJ,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYtC,IAAI,cAAAuC,iBAAA,uBAAhBA,iBAAA,CAAkBO,OAAO,KAAIrC,CAAC,CAACqC,OAAO;QAE3D,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAItB,OAAO,GAAG,QAAQ;QAEtB,IAAIgB,WAAW,KAAK,GAAG,IAAII,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpEJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UACvG;UACAxB,OAAO,GAAG,6BAA6B;UACvCsB,WAAW,uBAAAG,MAAA,CAAsBpF,QAAQ,sGAA4F;QACzI,CAAC,MAAM,IAAI2E,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAhB,OAAO,GAAG,oBAAoB;UAC9B,IAAIoB,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9CF,WAAW,GAAG,kEAAkE;UACpF,CAAC,MAAM,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC1GF,WAAW,GAAG,yEAAyE;UAC3F,CAAC,MAAM,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACtGF,WAAW,GAAG,8CAA8C;UAChE,CAAC,MAAM;YACHA,WAAW,gCAAAG,MAAA,CAAgCL,YAAY,CAAE;UAC7D;QACJ,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAhB,OAAO,GAAG,8BAA8B;UACxCsB,WAAW,2CAAAG,MAAA,CAA2CL,YAAY,CAAE;QACxE,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;UACnD;UACAhB,OAAO,GAAG,sBAAsB;UAChCsB,WAAW,GAAG,gHAAgH;QAClI,CAAC,MAAM,IAAIN,WAAW,KAAK,GAAG,EAAE;UAC5B;UACAhB,OAAO,GAAG,6CAA6C;UACvDsB,WAAW,GAAG,8EAA8E;QAChG,CAAC,MAAM,IAAI,CAACtC,CAAC,CAACiC,QAAQ,EAAE;UACpB;UACAjB,OAAO,GAAG,0BAA0B;UACpCsB,WAAW,GAAG,oFAAoF;QACtG,CAAC,MAAM;UACH;UACAtB,OAAO,GAAG,qBAAqB;UAC/BsB,WAAW,+CAAAG,MAAA,CAA4CL,YAAY,CAAE;QACzE;QAEA/D,KAAK,CAACwC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAEqB,WAAW;UACnBnB,IAAI,EAAE;QACV,CAAC,CAAC;;QAEF;QACAlB,OAAO,CAAC8B,KAAK,CAAC,kBAAkB,EAAE;UAC9BG,MAAM,EAAEF,WAAW;UACnBzC,IAAI,EAAE4C,SAAS;UACfE,OAAO,EAAED,YAAY;UACrBM,SAAS,EAAE1C;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH3B,KAAK,CAACwC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAE,uGAAuG;QAC/GE,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ,CAAC;EACD,oBACIxE,OAAA;IAAKgG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBjG,OAAA,CAACJ,KAAK;MAACsG,GAAG,EAAExE;IAAM;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBtG,OAAA;MAAKgG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BjG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,GAAKvG,QAAQ,CAAC6G,IAAI,EAAC,GAAC,eAAAvG,OAAA;YAAMwG,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPtG,OAAA,CAACP,SAAS;YACNiH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEvG,OAAQ;YACfwG,QAAQ,EAAGvD,CAAC,IAAKhD,UAAU,CAACgD,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAC5CG,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,+BAA+B;YAC3CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAAC5F,OAAO,IAAIA,OAAO,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,GAAKvG,QAAQ,CAACuH,OAAO,EAAC,GAAC,eAAAjH,OAAA;YAAMwG,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPtG,OAAA,CAACP,SAAS;YACNiH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEzF,QAAS;YAChB0F,QAAQ,EAAGvD,CAAC,IAAKlC,WAAW,CAACkC,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,kCAAkC;YAC9CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAAC9E,QAAQ,IAAIA,QAAQ,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,GAAKvG,QAAQ,CAACwH,KAAK,EAAC,GAAC,eAAAlH,OAAA;YAAMwG,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACPtG,OAAA,CAACP,SAAS;YACNiH,IAAI,EAAC,OAAO;YACZC,KAAK,EAAErG,QAAS;YAChBsG,QAAQ,EAAGvD,CAAC,IAAK9C,WAAW,CAAC8C,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAC7CI,WAAW,EAAC,gCAAgC;YAC5CC,QAAQ,EAAC,MAAM;YACfhB,SAAS,EAAE,CAAC1F,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACrD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL,CAAC,CAAChG,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,kBACjC1D,OAAA;UAAOgG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzD,EACAhG,QAAQ,IAAIA,QAAQ,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAACrD,QAAQ,CAAC,iBAC/EN,OAAA;UAAOgG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAACyH;QAAG;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvBtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACPtG,OAAA,CAACP,SAAS;YAACiH,IAAI,EAAC,KAAK;YAACC,KAAK,EAAEnG,QAAS;YAACoG,QAAQ,EAAGvD,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,oBAAoB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAAC0H;QAAI;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACPtG,OAAA,CAACP,SAAS;YAACiH,IAAI,EAAC,KAAK;YAACC,KAAK,EAAEvF,QAAS;YAACwF,QAAQ,EAAGvD,CAAC,IAAKhC,WAAW,CAACgC,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,qBAAqB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,GAAKvG,QAAQ,CAACyC,IAAI,EAAC,GAAC,eAAAnC,OAAA;YAAMwG,KAAK,EAAE;cAACC,KAAK,EAAE;YAAK,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACPtG,OAAA,CAACP,SAAS;YACNiH,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEjG,QAAS;YAChBkG,QAAQ,EAAGvD,CAAC,IAAK1C,WAAW,CAAC0C,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE,WAAY;YACvBC,WAAW,EAAC,kCAAkC;YAC9CC,QAAQ,EAAC,MAAM;YACfK,SAAS,EAAE,EAAG;YACdrB,SAAS,EAAE,CAACtF,QAAQ,IAAIA,QAAQ,CAACgD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAACC,IAAI,CAACjD,QAAQ,CAACkD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,WAAW,GAAG;UAAG;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL5F,QAAQ,IAAI,CAAC,UAAU,CAACiD,IAAI,CAACjD,QAAQ,CAACkD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,iBACtD5D,OAAA;UAAOgG,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACvF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAAC4H;QAAS;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjG,OAAA;YAAMgG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChCjG,OAAA;cAAGgG,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACPtG,OAAA,CAACP,SAAS;YAACiH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAE/F,QAAS;YAACgG,QAAQ,EAAGvD,CAAC,IAAKxC,WAAW,CAACwC,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,qBAAqB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAAC6H;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzBtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjG,OAAA,CAACP,SAAS;YAACiH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAE7F,QAAS;YAAC8F,QAAQ,EAAGvD,CAAC,IAAKtC,WAAW,CAACsC,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,oBAAiB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAAC8H;QAAO;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3BtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjG,OAAA,CAACP,SAAS;YAACiH,IAAI,EAAC,MAAM;YAACC,KAAK,EAAE3F,QAAS;YAAC4F,QAAQ,EAAGvD,CAAC,IAAKpC,WAAW,CAACoC,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACG,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,kBAAkB;YAACC,QAAQ,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtG,OAAA;QAAKgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjG,OAAA;UAAAiG,QAAA,EAAKvG,QAAQ,CAAC+H;QAAS;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BtG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBjG,OAAA,CAACF,QAAQ;YAACkG,SAAS,EAAC,OAAO;YAACW,KAAK,EAAErF,SAAU;YAACoG,OAAO,EAAElG,MAAO;YAACoF,QAAQ,EAAGvD,CAAC,IAAK9B,YAAY,CAAC8B,CAAC,CAACwD,MAAM,CAACF,KAAK,CAAE;YAACgB,WAAW,EAAC,MAAM;YAACZ,WAAW,EAAC,+BAA+B;YAACa,MAAM;YAACC,QAAQ,EAAC;UAAM;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNtG,OAAA;MAAKgG,SAAS,EAAC,yCAAyC;MAAAC,QAAA,eAEpDjG,OAAA,CAACH,MAAM;QAACiI,EAAE,EAAC,OAAO;QAAC9B,SAAS,EAAC,wEAAwE;QAAC+B,OAAO,EAAEhE,KAAM;QAAAkC,QAAA,EAAEvG,QAAQ,CAACsI;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9I,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAnG,EAAA,CAzVKF,kBAAkB;AAAAgI,EAAA,GAAlBhI,kBAAkB;AA2VxB,eAAeA,kBAAkB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}