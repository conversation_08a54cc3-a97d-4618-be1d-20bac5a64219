export type TaskGroupIds = 'parseHTML' | 'styleLayout' | 'paintCompositeRender' | 'scriptParseCompile' | 'scriptEvaluation' | 'garbageCollection' | 'other';
export type TaskGroup = {
    id: TaskGroupIds;
    label: string;
    traceEventNames: string[];
};
/**
 * @license Copyright 2017 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/** @typedef {'parseHTML'|'styleLayout'|'paintCompositeRender'|'scriptParseCompile'|'scriptEvaluation'|'garbageCollection'|'other'} TaskGroupIds */
/**
 * @typedef TaskGroup
 * @property {TaskGroupIds} id
 * @property {string} label
 * @property {string[]} traceEventNames
 */
/**
 * Make sure the traceEventNames keep up with the ones in DevTools
 * @see https://cs.chromium.org/chromium/src/third_party/blink/renderer/devtools/front_end/timeline_model/TimelineModel.js?type=cs&q=TimelineModel.TimelineModel.RecordType+%3D&g=0&l=1156
 * @see https://cs.chromium.org/chromium/src/third_party/blink/renderer/devtools/front_end/timeline/TimelineUIUtils.js?type=cs&q=_initEventStyles+-f:out+f:devtools&sq=package:chromium&g=0&l=39
 * @type {{[P in TaskGroupIds]: {id: P, label: string, traceEventNames: Array<string>}}}
 */
export const taskGroups: {
    other: {
        id: "other";
        label: string;
        traceEventNames: Array<string>;
    };
    parseHTML: {
        id: "parseHTML";
        label: string;
        traceEventNames: Array<string>;
    };
    styleLayout: {
        id: "styleLayout";
        label: string;
        traceEventNames: Array<string>;
    };
    paintCompositeRender: {
        id: "paintCompositeRender";
        label: string;
        traceEventNames: Array<string>;
    };
    scriptParseCompile: {
        id: "scriptParseCompile";
        label: string;
        traceEventNames: Array<string>;
    };
    scriptEvaluation: {
        id: "scriptEvaluation";
        label: string;
        traceEventNames: Array<string>;
    };
    garbageCollection: {
        id: "garbageCollection";
        label: string;
        traceEventNames: Array<string>;
    };
};
/** @type {Object<string, TaskGroup>} */
export const taskNameToGroup: {
    [x: string]: TaskGroup;
};
//# sourceMappingURL=task-groups.d.ts.map