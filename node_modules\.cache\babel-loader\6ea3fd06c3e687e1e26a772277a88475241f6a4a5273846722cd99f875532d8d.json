{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\ring\\\\scaricoMagRing.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { ringConfrontoDispQta } from '../../components/route';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ScaricoMagRing extends Component {\n  constructor(props) {\n    var _JSON$parse$warehouse, _JSON$parse$warehouse2;\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    this.onRowSelect = result => {\n      if (result.tasks !== null && result.status !== 'prepared') {\n        var idFOROrd = [];\n        this.state.results.forEach(element => {\n          if (element.id === result.id) {\n            idFOROrd = element;\n          }\n        });\n        localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n        window.location.pathname = ringConfrontoDispQta;\n      } else {\n        this.toast.show({\n          severity: \"warn\",\n          summary: \"Attenzione\",\n          detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato prepared prima di procedere\",\n          life: 3000\n        });\n      }\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      selectedWarehouse: ((_JSON$parse$warehouse = JSON.parse(localStorage.getItem('user') || '{}').warehousesCross) === null || _JSON$parse$warehouse === void 0 ? void 0 : (_JSON$parse$warehouse2 = _JSON$parse$warehouse[0]) === null || _JSON$parse$warehouse2 === void 0 ? void 0 : _JSON$parse$warehouse2.idWarehouse) || null,\n      selectedDocuments: null,\n      loading: true,\n      displayed: false,\n      resultDialog3: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      totalRecords: 0,\n      search: '',\n      value: null,\n      value2: null,\n      clienti: null,\n      param: '?idWarehouses=',\n      param2: '&idRetailer=',\n      selectedRetailer: null,\n      deleteResultDialog: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idRetailer, _element$idRetailer$i, _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: ((_element$idRetailer = element.idRetailer) === null || _element$idRetailer === void 0 ? void 0 : (_element$idRetailer$i = _element$idRetailer.idRegistry) === null || _element$idRetailer$i === void 0 ? void 0 : _element$idRetailer$i.firstName) || 'N/A',\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    /* this.retailers = [] */\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.creaDDT = this.creaDDT.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.onRowSelect = this.onRowSelect.bind(this);\n  }\n  async componentDidMount() {\n    var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n    await APIRequest(\"GET\", url).then(res => {\n      var documento = [];\n      res.data.documents.forEach(element => {\n        var _element$idRetailer2, _element$idRetailer2$, _element$tasks2;\n        var x = {\n          id: element.id,\n          number: element.number,\n          type: element.type,\n          retailer: ((_element$idRetailer2 = element.idRetailer) === null || _element$idRetailer2 === void 0 ? void 0 : (_element$idRetailer2$ = _element$idRetailer2.idRegistry) === null || _element$idRetailer2$ === void 0 ? void 0 : _element$idRetailer2$.firstName) || 'N/A',\n          documentDate: element.documentDate,\n          tasks: element.tasks,\n          erpSync: element.erpSync,\n          status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n          idDocumentHeadOrig: element.idDocumentHeadOrig\n        };\n        documento.push(x);\n      });\n      this.setState({\n        results: documento,\n        results5: documento,\n        totalRecords: res.data.totalCount,\n        lazyParams: {\n          first: this.state.lazyParams.first,\n          rows: this.state.lazyParams.rows,\n          page: this.state.lazyParams.page,\n          pageCount: res.data.totalCount / this.state.lazyParams.rows\n        },\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var task = [];\n    var documentBody = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      respMag: respMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idRetailer3, _element$idRetailer3$, _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: ((_element$idRetailer3 = element.idRetailer) === null || _element$idRetailer3 === void 0 ? void 0 : (_element$idRetailer3$ = _element$idRetailer3.idRegistry) === null || _element$idRetailer3$ === void 0 ? void 0 : _element$idRetailer3$.firstName) || 'N/A',\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idRetailer4, _element$idRetailer4$, _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: ((_element$idRetailer4 = element.idRetailer) === null || _element$idRetailer4 === void 0 ? void 0 : (_element$idRetailer4$ = _element$idRetailer4.idRegistry) === null || _element$idRetailer4$ === void 0 ? void 0 : _element$idRetailer4$.firstName) || 'N/A',\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  async creaDDT(result) {\n    if (result.tasks !== null) {\n      let tipi = [];\n      let url = '';\n      if (result.idDocumentHeadOrig.length > 0) {\n        for (var x = 0; x < ((_result$idDocumentHea = result.idDocumentHeadOrig) === null || _result$idDocumentHea === void 0 ? void 0 : _result$idDocumentHea.length); x++) {\n          var _result$idDocumentHea;\n          url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest;\n          await APIRequest(\"GET\", url).then(res => {\n            console.log(res.data);\n            tipi.push(res.data.type);\n          }).catch(e => {\n            console.log(e);\n          });\n        }\n        var find = tipi.find(el => el === 'CLI-DDT');\n        if (find !== undefined) {\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Il documento è già associato ad una task di tipo CLI-DDT\",\n            life: 3000\n          });\n        } else {\n          url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n          //Chiamata axios per la creazione del documento\n          await APIRequest('POST', url).then(res => {\n            console.log(res.data);\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: \"Il documento è stato inserito con successo\",\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            var _e$response17, _e$response18;\n            console.log(e);\n            this.toast.show({\n              severity: 'error',\n              summary: 'Siamo spiacenti',\n              detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n              life: 3000\n            });\n          });\n        }\n      } else {\n        url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response19, _e$response20;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\",\n        life: 3000\n      });\n    }\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null);\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response21, _e$response22;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      body: \"manager\",\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.ModificaTask,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-pencil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 50\n      }, this),\n      handler: this.onRowSelect\n    }, {\n      name: Costanti.CreaDDT,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-car\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 45\n      }, this),\n      handler: this.creaDDT,\n      status: 'prepared'\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 17\n      }, this), this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n        title: \"Prima di procedere\",\n        content: \"Seleziona un magazzino \",\n        target: \".selWar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiScarico\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          actionsColumn: actionFields,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton: true,\n          actionExtraButton: this.assegnaLavorazioni,\n          labelExtraButton: Costanti.assegnaLavorazioni,\n          disabledExtraButton: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Vendite\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          distributore: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default ScaricoMagRing;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "Dialog", "JoyrideGen", "Toast", "<PERSON><PERSON>", "Print", "ringConfrontoDispQta", "VisualizzaDocumenti", "Nav", "CustomDataTable", "SelezionaOperatore", "jsxDEV", "_jsxDEV", "ScaricoMagRing", "constructor", "props", "_JSON$parse$warehouse", "_JSON$parse$warehouse2", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onRowSelect", "result", "tasks", "idFOROrd", "state", "results", "for<PERSON>ach", "element", "localStorage", "setItem", "JSON", "stringify", "window", "location", "pathname", "toast", "show", "severity", "summary", "detail", "life", "results2", "results3", "results4", "results5", "resultDialog", "resultDialog2", "selectedWarehouse", "parse", "getItem", "warehousesCross", "idWarehouse", "selectedDocuments", "loading", "displayed", "resultDialog3", "opMag", "respMag", "mex", "totalRecords", "search", "value", "value2", "clienti", "param", "param2", "<PERSON><PERSON><PERSON><PERSON>", "deleteResultDialog", "lazyParams", "first", "rows", "page", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "e", "setState", "name", "url", "code", "then", "res", "documento", "data", "documents", "_element$idRetailer", "_element$idRetailer$i", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "documentDate", "erpSync", "idDocumentHeadOrig", "push", "totalCount", "pageCount", "catch", "_e$response", "_e$response2", "console", "log", "concat", "response", "undefined", "message", "loadLazyTimeout", "warehouse", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "onPage", "onSort", "onFilter", "creaDDT", "assegnaLavorazioni", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "componentDidMount", "_element$idRetailer2", "_element$idRetailer2$", "_element$tasks2", "_e$response3", "_e$response4", "entry", "warehouseName", "_e$response5", "_e$response6", "_e$response7", "_e$response8", "task", "documentBody", "documentBodies", "_e$response9", "_e$response0", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "role", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "sessionStorage", "_element$idRetailer3", "_element$idRetailer3$", "_element$tasks3", "_e$response1", "_e$response10", "_element$idRetailer4", "_element$idRetailer4$", "_element$tasks4", "_e$response11", "_e$response12", "event", "clearTimeout", "setTimeout", "_element$tasks5", "_e$response13", "_e$response14", "Math", "random", "field", "_element$tasks6", "_e$response15", "_e$response16", "loadLazyData", "tipi", "length", "_result$idDocumentHea", "idDocDest", "find", "el", "idDocument", "reload", "_e$response17", "_e$response18", "_e$response19", "_e$response20", "FilterOp", "operator", "_e$response21", "_e$response22", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "resultDialogFooter3", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "Responsabile", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "assegnaLavorazione", "status2", "ModificaTask", "CreaDDT", "Elimina", "ref", "title", "content", "target", "DocumentiScarico", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "autoLayout", "actionsColumn", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton", "actionExtraButton", "labelExtraButton", "disabledExtraButton", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "distributore", "Conferma", "style", "fontSize", "ResDeleteDoc"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/ring/scaricoMagRing.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport { ringConfrontoDispQta } from '../../components/route';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\n\nclass ScaricoMagRing extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            selectedWarehouse: JSON.parse(localStorage.getItem('user') || '{}').warehousesCross?.[0]?.idWarehouse || null,\n            selectedDocuments: null,\n            loading: true,\n            displayed: false,\n            resultDialog3: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            totalRecords: 0,\n            search: '',\n            value: null,\n            value2: null,\n            clienti: null,\n            param: '?idWarehouses=',\n            param2: '&idRetailer=',\n            selectedRetailer: null,\n            deleteResultDialog: null,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedRetailer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer?.idRegistry?.firstName || 'N/A',\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        /* this.retailers = [] */\n        this.loadLazyTimeout = null;\n        this.warehouse = [];\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.creaDDT = this.creaDDT.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.onRowSelect = this.onRowSelect.bind(this);\n    }\n    async componentDidMount() {\n        var url = 'documents?idWarehouses=' + this.state.selectedWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer?.idRegistry?.firstName || 'N/A',\n                        documentDate: element.documentDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idDocumentHeadOrig: element.idDocumentHeadOrig\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest(\"GET\", \"employees?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var task = []\n        var documentBody = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer?.idRegistry?.firstName || 'N/A',\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false,\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer?.idRegistry?.firstName || 'N/A',\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    async creaDDT(result) {\n        if (result.tasks !== null) {\n            let tipi = []\n            let url = ''\n            if (result.idDocumentHeadOrig.length > 0) {\n                for (var x = 0; x < result.idDocumentHeadOrig?.length; x++) {\n                    url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest\n                    await APIRequest(\"GET\", url)\n                        .then((res) => {\n                            console.log(res.data)\n                            tipi.push(res.data.type)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                        });\n                }\n                var find = tipi.find(el => el === 'CLI-DDT')\n                if (find !== undefined) {\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Il documento è già associato ad una task di tipo CLI-DDT\", life: 3000 });\n                } else {\n                    url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                    //Chiamata axios per la creazione del documento\n                    await APIRequest('POST', url)\n                        .then(res => {\n                            console.log(res.data);\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.log(e)\n                            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        })\n                }\n            } else {\n                url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\", life: 3000 });\n        }\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    onRowSelect = (result) => {\n        if (result.tasks !== null && result.status !== 'prepared') {\n            var idFOROrd = []\n            this.state.results.forEach(element => {\n                if (element.id === result.id) {\n                    idFOROrd = element\n                }\n            })\n            localStorage.setItem(\"datiComodo\", JSON.stringify(idFOROrd));\n            window.location.pathname = ringConfrontoDispQta;\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione\",\n                detail: \"Verificare se è presente una task per questo documento e che la stessa non sia in stato prepared prima di procedere\",\n                life: 3000,\n            });\n        }\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                body: \"manager\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.operator.idUser.username\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.ModificaTask, icon: <i className=\"pi pi-pencil\" />, handler: this.onRowSelect },\n            { name: Costanti.CreaDDT, icon: <i className=\"pi pi-car\" />, handler: this.creaDDT, status: 'prepared' },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n        ];\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n                {this.state.displayed &&\n                    <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                }\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiScarico}</h1>\n                </div>\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        actionsColumn={actionFields}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButton={true}\n                        actionExtraButton={this.assegnaLavorazioni}\n                        labelExtraButton={Costanti.assegnaLavorazioni}\n                        disabledExtraButton={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Vendite\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} distributore={true} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n            </div>\n        );\n    }\n}\n\nexport default ScaricoMagRing;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,cAAc,SAASf,SAAS,CAAC;EAanCgB,WAAWA,CAACC,KAAK,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACf,KAAK,CAACF,KAAK,CAAC;IAbhB;IAAA,KACAG,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAAA,KAknBDC,WAAW,GAAIC,MAAM,IAAK;MACtB,IAAIA,MAAM,CAACC,KAAK,KAAK,IAAI,IAAID,MAAM,CAACF,MAAM,KAAK,UAAU,EAAE;QACvD,IAAII,QAAQ,GAAG,EAAE;QACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI;UAClC,IAAIA,OAAO,CAAChB,EAAE,KAAKU,MAAM,CAACV,EAAE,EAAE;YAC1BY,QAAQ,GAAGI,OAAO;UACtB;QACJ,CAAC,CAAC;QACFC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;QAC5DS,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGpC,oBAAoB;MACnD,CAAC,MAAM;QACH,IAAI,CAACqC,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,qHAAqH;UAC7HC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC;IAjoBG,IAAI,CAAChB,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbgB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE,EAAAvC,qBAAA,GAAAsB,IAAI,CAACkB,KAAK,CAACpB,YAAY,CAACqB,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAACC,eAAe,cAAA1C,qBAAA,wBAAAC,sBAAA,GAAhED,qBAAA,CAAmE,CAAC,CAAC,cAAAC,sBAAA,uBAArEA,sBAAA,CAAuE0C,WAAW,KAAI,IAAI;MAC7GC,iBAAiB,EAAE,IAAI;MACvBC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPrC,MAAM,EAAE,IAAI,CAACX,WAAW;MACxBiD,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,gBAAgB;MACvBC,MAAM,EAAE,cAAc;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,UAAU,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAEb,KAAK,EAAE,EAAE;YAAEc,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAEd,KAAK,EAAE,EAAE;YAAEc,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAEd,KAAK,EAAE,EAAE;YAAEc,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMC,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVzB,OAAO,EAAE,IAAI;QACbO,MAAM,EAAEiB,CAAC,CAAChB,KAAK,CAACkB,IAAI;QACpBb,gBAAgB,EAAEW,CAAC,CAAChB;MACxB,CAAC,CAAC;MAEF,IAAImB,GAAG,GAAG,WAAW,GAAG,IAAI,CAACxD,KAAK,CAACwC,KAAK,GAAG,IAAI,CAACxC,KAAK,CAACuB,iBAAiB,GAAG,IAAI,CAACvB,KAAK,CAACyC,MAAM,GAAGY,CAAC,CAAChB,KAAK,CAACoB,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACzD,KAAK,CAAC4C,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACG,IAAI;MAClN,MAAM/E,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4D,mBAAA,EAAAC,qBAAA,EAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;YACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;YACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;YAClBC,QAAQ,EAAE,EAAAN,mBAAA,GAAA5D,OAAO,CAACmE,UAAU,cAAAP,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBQ,UAAU,cAAAP,qBAAA,uBAA9BA,qBAAA,CAAgC5E,SAAS,KAAI,KAAK;YAC5DoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;YAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;YACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;YACxB9E,MAAM,GAAAsE,cAAA,GAAE9D,OAAO,CAACL,KAAK,cAAAmE,cAAA,uBAAbA,cAAA,CAAetE,MAAM;YAC7B+E,kBAAkB,EAAEvE,OAAO,CAACuE;UAChC,CAAC;UACDd,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACZ,QAAQ,CAAC;UACVrD,OAAO,EAAE2D,SAAS;UAClBxC,QAAQ,EAAEwC,SAAS;UACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;UACjChC,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI,CAAC7C,KAAK,CAAC4C,UAAU,CAACC,KAAK;YAAEC,IAAI,EAAE,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACE,IAAI;YAAEC,IAAI,EAAE,IAAI,CAAC/C,KAAK,CAAC4C,UAAU,CAACG,IAAI;YAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACe,UAAU,GAAG,IAAI,CAAC5E,KAAK,CAAC4C,UAAU,CAACE;UAAM,CAAC;UACpLjB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAA0B,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAAJ,WAAA,GAAA1B,CAAC,CAAC+B,QAAQ,cAAAL,WAAA,uBAAVA,WAAA,CAAYlB,IAAI,MAAKwB,SAAS,IAAAL,YAAA,GAAG3B,CAAC,CAAC+B,QAAQ,cAAAJ,YAAA,uBAAVA,YAAA,CAAYnB,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;UACxJtE,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD;IACA,IAAI,CAACuE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,MAAM,GAAG,IAAI,CAACA,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACS,OAAO,GAAG,IAAI,CAACA,OAAO,CAACT,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACU,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACX,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACY,YAAY,GAAG,IAAI,CAACA,YAAY,CAACZ,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACa,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACb,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACd,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAAC9F,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC8F,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMe,iBAAiBA,CAAA,EAAG;IACtB,IAAIjD,GAAG,GAAG,yBAAyB,GAAG,IAAI,CAACxD,KAAK,CAACuB,iBAAiB,GAAG,gCAAgC,GAAG,IAAI,CAACvB,KAAK,CAAC4C,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACG,IAAI;IAC1K,MAAM/E,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX,IAAIC,SAAS,GAAG,EAAE;MAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;QAAA,IAAAuG,oBAAA,EAAAC,qBAAA,EAAAC,eAAA;QAClC,IAAI1C,CAAC,GAAG;UACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;UACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;UACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;UAClBC,QAAQ,EAAE,EAAAqC,oBAAA,GAAAvG,OAAO,CAACmE,UAAU,cAAAoC,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBnC,UAAU,cAAAoC,qBAAA,uBAA9BA,qBAAA,CAAgCvH,SAAS,KAAI,KAAK;UAC5DoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;UAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;UACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;UACxB9E,MAAM,GAAAiH,eAAA,GAAEzG,OAAO,CAACL,KAAK,cAAA8G,eAAA,uBAAbA,eAAA,CAAejH,MAAM;UAC7B+E,kBAAkB,EAAEvE,OAAO,CAACuE;QAChC,CAAC;QACDd,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;MACrB,CAAC,CAAC;MACF,IAAI,CAACZ,QAAQ,CAAC;QACVrD,OAAO,EAAE2D,SAAS;QAClBxC,QAAQ,EAAEwC,SAAS;QACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;QACjChC,UAAU,EAAE;UAAEC,KAAK,EAAE,IAAI,CAAC7C,KAAK,CAAC4C,UAAU,CAACC,KAAK;UAAEC,IAAI,EAAE,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACE,IAAI;UAAEC,IAAI,EAAE,IAAI,CAAC/C,KAAK,CAAC4C,UAAU,CAACG,IAAI;UAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACe,UAAU,GAAG,IAAI,CAAC5E,KAAK,CAAC4C,UAAU,CAACE;QAAM,CAAC;QACpLjB,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAAwD,YAAA,EAAAC,YAAA;MACV7B,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAA0B,YAAA,GAAAxD,CAAC,CAAC+B,QAAQ,cAAAyB,YAAA,uBAAVA,YAAA,CAAYhD,IAAI,MAAKwB,SAAS,IAAAyB,YAAA,GAAGzD,CAAC,CAAC+B,QAAQ,cAAA0B,YAAA,uBAAVA,YAAA,CAAYjD,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;QACxJtE,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMhD,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC0F,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIoD,KAAK,IAAIpD,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAAC2B,SAAS,CAACb,IAAI,CAAC;UAChBpB,IAAI,EAAEwD,KAAK,CAACC,aAAa;UACzB3E,KAAK,EAAE0E,KAAK,CAAC5H;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACD2F,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA4D,YAAA,EAAAC,YAAA;MACVjC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAoE,MAAA,CAAuE,EAAA8B,YAAA,GAAA5D,CAAC,CAAC+B,QAAQ,cAAA6B,YAAA,uBAAVA,YAAA,CAAYpD,IAAI,MAAKwB,SAAS,IAAA6B,YAAA,GAAG7D,CAAC,CAAC+B,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAYrD,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;QAC5ItE,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMhD,UAAU,CAAC,KAAK,EAAE,gCAAgC,CAAC,CACpD0F,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACL,QAAQ,CAAC;QACVnC,QAAQ,EAAEwC,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDiB,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA8D,YAAA,EAAAC,YAAA;MACVnC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAoE,MAAA,CAAsF,EAAAgC,YAAA,GAAA9D,CAAC,CAAC+B,QAAQ,cAAA+B,YAAA,uBAAVA,YAAA,CAAYtD,IAAI,MAAKwB,SAAS,IAAA+B,YAAA,GAAG/D,CAAC,CAAC+B,QAAQ,cAAAgC,YAAA,uBAAVA,YAAA,CAAYvD,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;QAC3JtE,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA;EACA,MAAMyE,cAAcA,CAAC5F,MAAM,EAAE;IACzB,IAAI2D,GAAG,GAAG,2BAA2B,GAAG3D,MAAM,CAACV,EAAE;IACjD,IAAIkI,IAAI,GAAG,EAAE;IACb,IAAIC,YAAY,GAAG,EAAE;IACrB,MAAMtJ,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACX2D,YAAY,GAAG3D,GAAG,CAACE,IAAI,CAAC0D,cAAc;MACtC1H,MAAM,CAAC0H,cAAc,GAAG5D,GAAG,CAACE,IAAI,CAAC0D,cAAc;MAC/CF,IAAI,GAAG1D,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDiB,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAAmE,YAAA,EAAAC,YAAA;MACVxC,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAAqC,YAAA,GAAAnE,CAAC,CAAC+B,QAAQ,cAAAoC,YAAA,uBAAVA,YAAA,CAAY3D,IAAI,MAAKwB,SAAS,IAAAoC,YAAA,GAAGpE,CAAC,CAAC+B,QAAQ,cAAAqC,YAAA,uBAAVA,YAAA,CAAY5D,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;QACxJtE,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAIsE,OAAO,GACP,oBAAoB,GACpBzF,MAAM,CAACsE,MAAM,GACb,OAAO,GACP,IAAIuD,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnI,MAAM,CAAC2E,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAClB,QAAQ,CAAC;MACVhC,aAAa,EAAE,IAAI;MACnBzB,MAAM,EAAEwH,IAAI;MACZpG,QAAQ,EAAE,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACgI,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC/I,EAAE,KAAKU,MAAM,CAACV,EAAE,CAAC;MAClE+B,QAAQ,EAAEoG,YAAY;MACtBpF,GAAG,EAAEoD;IACT,CAAC,CAAC;EACN;EACA;EACAK,kBAAkBA,CAAC9F,MAAM,EAAE;IACvB,IAAI,CAACyD,QAAQ,CAAC;MACVzD,MAAM,EAAEA,MAAM;MACdyB,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAsE,UAAUA,CAAC/F,MAAM,EAAE;IACf,IAAIyF,OAAO,GACP,oBAAoB,GACpBzF,MAAM,CAACsE,MAAM,GACb,OAAO,GACP,IAAIuD,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACnI,MAAM,CAAC2E,YAAY,CAAC,CAAC;IAC5C,IAAIxC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACjC,KAAK,CAACmB,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAACnB,KAAK,CAACmB,QAAQ,CAACjB,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACgI,IAAI,KAAK,QAAQ,IAAItI,MAAM,CAACC,KAAK,KAAK,IAAI,EAAE;UACpD,IAAIsI,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;UAC1GxG,KAAK,CAAC2C,IAAI,CAAC;YACP8D,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1F/F,KAAK,EAAElC,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIzI,OAAO,CAACgI,IAAI,KAAK,UAAU,IAAItI,MAAM,CAACC,KAAK,KAAK,IAAI,EAAE;UAC7DmC,OAAO,CAAC0C,IAAI,CAAC;YACT8D,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS;YACnDtG,KAAK,EAAElC,OAAO,CAACyI;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC5G,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsB,QAAQ,CAAC;MACVzD,MAAM,EAAAgJ,aAAA,KAAOhJ,MAAM,CAAE;MACrBkC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEA,OAAO;MAChBC,GAAG,EAAEoD;IACT,CAAC,CAAC;EACN;EACA;EACAO,UAAUA,CAAA,EAAG;IACT,IAAI,CAACvC,QAAQ,CAAC;MACVvB,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAM+D,KAAKA,CAAA,EAAG;IACV,IAAInE,WAAW,GAAGrB,IAAI,CAACkB,KAAK,CAAChB,MAAM,CAACsI,cAAc,CAACrH,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIE,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI6B,GAAG,GAAG,yBAAyB,GAAG7B,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC3B,KAAK,CAAC4C,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACG,IAAI;MACzJ,IAAI,CAACO,QAAQ,CAAC;QAAEZ,gBAAgB,EAAE,IAAI;QAAEnB,iBAAiB,EAAEI,WAAW;QAAEE,OAAO,EAAE,IAAI;QAAEO,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMpE,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4I,oBAAA,EAAAC,qBAAA,EAAAC,eAAA;UAClC,IAAI/E,CAAC,GAAG;YACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;YACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;YACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;YAClBC,QAAQ,EAAE,EAAA0E,oBAAA,GAAA5I,OAAO,CAACmE,UAAU,cAAAyE,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBxE,UAAU,cAAAyE,qBAAA,uBAA9BA,qBAAA,CAAgC5J,SAAS,KAAI,KAAK;YAC5DoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;YAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;YACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;YACxB9E,MAAM,GAAAsJ,eAAA,GAAE9I,OAAO,CAACL,KAAK,cAAAmJ,eAAA,uBAAbA,eAAA,CAAetJ,MAAM;YAC7B+E,kBAAkB,EAAEvE,OAAO,CAACuE;UAChC,CAAC;UACDd,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACZ,QAAQ,CAAC;UACVrD,OAAO,EAAE2D,SAAS;UAClBxC,QAAQ,EAAEwC,SAAS;UACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;UACjChC,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI,CAAC7C,KAAK,CAAC4C,UAAU,CAACC,KAAK;YAAEC,IAAI,EAAE,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACE,IAAI;YAAEC,IAAI,EAAE,IAAI,CAAC/C,KAAK,CAAC4C,UAAU,CAACG,IAAI;YAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACe,UAAU,GAAG,IAAI,CAAC5E,KAAK,CAAC4C,UAAU,CAACE;UAAM,CAAC;UACpLjB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAA6F,YAAA,EAAAC,aAAA;QACVlE,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAA+D,YAAA,GAAA7F,CAAC,CAAC+B,QAAQ,cAAA8D,YAAA,uBAAVA,YAAA,CAAYrF,IAAI,MAAKwB,SAAS,IAAA8D,aAAA,GAAG9F,CAAC,CAAC+B,QAAQ,cAAA+D,aAAA,uBAAVA,aAAA,CAAYtF,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;UACxJtE,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAM+E,SAASA,CAAA,EAAG;IACd,IAAIpE,WAAW,GAAGrB,IAAI,CAACkB,KAAK,CAAChB,MAAM,CAACsI,cAAc,CAACrH,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIE,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAI6B,GAAG,GAAG,yBAAyB,GAAG7B,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC3B,KAAK,CAAC4C,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACG,IAAI;MACzJ,IAAI,CAACO,QAAQ,CAAC;QAAEZ,gBAAgB,EAAE,IAAI;QAAEnB,iBAAiB,EAAEI,WAAW;QAAEE,OAAO,EAAE,IAAI;QAAEO,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMpE,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAiJ,oBAAA,EAAAC,qBAAA,EAAAC,eAAA;UAClC,IAAIpF,CAAC,GAAG;YACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;YACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;YACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;YAClBC,QAAQ,EAAE,EAAA+E,oBAAA,GAAAjJ,OAAO,CAACmE,UAAU,cAAA8E,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB7E,UAAU,cAAA8E,qBAAA,uBAA9BA,qBAAA,CAAgCjK,SAAS,KAAI,KAAK;YAC5DoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;YAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;YACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;YACxB9E,MAAM,GAAA2J,eAAA,GAAEnJ,OAAO,CAACL,KAAK,cAAAwJ,eAAA,uBAAbA,eAAA,CAAe3J,MAAM;YAC7B+E,kBAAkB,EAAEvE,OAAO,CAACuE;UAChC,CAAC;UACDd,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACZ,QAAQ,CAAC;UACVrD,OAAO,EAAE2D,SAAS;UAClBxC,QAAQ,EAAEwC,SAAS;UACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;UACjChC,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI,CAAC7C,KAAK,CAAC4C,UAAU,CAACC,KAAK;YAAEC,IAAI,EAAE,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACE,IAAI;YAAEC,IAAI,EAAE,IAAI,CAAC/C,KAAK,CAAC4C,UAAU,CAACG,IAAI;YAAE8B,SAAS,EAAElB,GAAG,CAACE,IAAI,CAACe,UAAU,GAAG,IAAI,CAAC5E,KAAK,CAAC4C,UAAU,CAACE;UAAM,CAAC;UACpLjB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAAkG,aAAA,EAAAC,aAAA;QACVvE,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAAoE,aAAA,GAAAlG,CAAC,CAAC+B,QAAQ,cAAAmE,aAAA,uBAAVA,aAAA,CAAY1F,IAAI,MAAKwB,SAAS,IAAAmE,aAAA,GAAGnG,CAAC,CAAC+B,QAAQ,cAAAoE,aAAA,uBAAVA,aAAA,CAAY3F,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;UACxJtE,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACAgF,MAAMA,CAACyD,KAAK,EAAE;IACV,IAAI,CAACnG,QAAQ,CAAC;MAAEzB,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAAC0D,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAInG,GAAG,GAAG,WAAW,GAAG,IAAI,CAACxD,KAAK,CAACwC,KAAK,GAAG,IAAI,CAACxC,KAAK,CAACuB,iBAAiB,IAAI,IAAI,CAACvB,KAAK,CAAC0C,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC1C,KAAK,CAACyC,MAAM,GAAG,IAAI,CAACzC,KAAK,CAAC0C,gBAAgB,CAACe,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAGgG,KAAK,CAAC3G,IAAI,GAAG,QAAQ,GAAG2G,KAAK,CAAC1G,IAAI;MACpP,MAAM/E,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyJ,eAAA;UAClC,IAAI1F,CAAC,GAAG;YACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;YACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;YACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;YAClBC,QAAQ,EAAElE,OAAO,CAACmE,UAAU,CAACC,UAAU,CAACnF,SAAS;YACjDoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;YAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;YACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;YACxB9E,MAAM,GAAAiK,eAAA,GAAEzJ,OAAO,CAACL,KAAK,cAAA8J,eAAA,uBAAbA,eAAA,CAAejK;UAC3B,CAAC;UACDiE,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACZ,QAAQ,CAAC;UACVrD,OAAO,EAAE2D,SAAS;UAClBxC,QAAQ,EAAEwC,SAAS;UACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;UACjChC,UAAU,EAAE6G,KAAK;UACjB5H,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAAwG,aAAA,EAAAC,aAAA;QACV7E,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAA0E,aAAA,GAAAxG,CAAC,CAAC+B,QAAQ,cAAAyE,aAAA,uBAAVA,aAAA,CAAYhG,IAAI,MAAKwB,SAAS,IAAAyE,aAAA,GAAGzG,CAAC,CAAC+B,QAAQ,cAAA0E,aAAA,uBAAVA,aAAA,CAAYjG,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;UACxJtE,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE+I,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA/D,MAAMA,CAACwD,KAAK,EAAE;IACV,IAAI,CAACnG,QAAQ,CAAC;MAAEzB,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIoI,KAAK,GAAGR,KAAK,CAACzG,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGyG,KAAK,CAACzG,SAAS;IAChG,IAAI,IAAI,CAACuC,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAInG,GAAG,GAAG,WAAW,GAAG,IAAI,CAACxD,KAAK,CAACwC,KAAK,GAAG,IAAI,CAACxC,KAAK,CAACuB,iBAAiB,IAAI,IAAI,CAACvB,KAAK,CAAC0C,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC1C,KAAK,CAACyC,MAAM,GAAG,IAAI,CAACzC,KAAK,CAAC0C,gBAAgB,CAACe,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACzD,KAAK,CAAC4C,UAAU,CAACE,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACG,IAAI,GAAG,SAAS,GAAGkH,KAAK,GAAG,WAAW,IAAIR,KAAK,CAACxG,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAMjF,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAAC5D,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA+J,eAAA;UAClC,IAAIhG,CAAC,GAAG;YACJ/E,EAAE,EAAEgB,OAAO,CAAChB,EAAE;YACdgF,MAAM,EAAEhE,OAAO,CAACgE,MAAM;YACtBC,IAAI,EAAEjE,OAAO,CAACiE,IAAI;YAClBC,QAAQ,EAAElE,OAAO,CAACmE,UAAU,CAACC,UAAU,CAACnF,SAAS;YACjDoF,YAAY,EAAErE,OAAO,CAACqE,YAAY;YAClC1E,KAAK,EAAEK,OAAO,CAACL,KAAK;YACpB2E,OAAO,EAAEtE,OAAO,CAACsE,OAAO;YACxB9E,MAAM,GAAAuK,eAAA,GAAE/J,OAAO,CAACL,KAAK,cAAAoK,eAAA,uBAAbA,eAAA,CAAevK;UAC3B,CAAC;UACDiE,SAAS,CAACe,IAAI,CAACT,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACZ,QAAQ,CAAC;UACVrD,OAAO,EAAE2D,SAAS;UAClBxC,QAAQ,EAAEwC,SAAS;UACnBzB,YAAY,EAAEwB,GAAG,CAACE,IAAI,CAACe,UAAU;UACjChC,UAAU,EAAAiG,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC7I,KAAK,CAAC4C,UAAU;YAAEI,SAAS,EAAEyG,KAAK,CAACzG,SAAS;YAAEC,SAAS,EAAEwG,KAAK,CAACxG;UAAS,EAAE;UAChGpB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDiD,KAAK,CAAEzB,CAAC,IAAK;QAAA,IAAA8G,aAAA,EAAAC,aAAA;QACVnF,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;QACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAoE,MAAA,CAAmF,EAAAgF,aAAA,GAAA9G,CAAC,CAAC+B,QAAQ,cAAA+E,aAAA,uBAAVA,aAAA,CAAYtG,IAAI,MAAKwB,SAAS,IAAA+E,aAAA,GAAG/G,CAAC,CAAC+B,QAAQ,cAAAgF,aAAA,uBAAVA,aAAA,CAAYvG,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;UACxJtE,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAE+I,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA9D,QAAQA,CAACuD,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACnG,QAAQ,CAAC;MAAEV,UAAU,EAAE6G;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACA,MAAMlE,OAAOA,CAACtG,MAAM,EAAE;IAClB,IAAIA,MAAM,CAACC,KAAK,KAAK,IAAI,EAAE;MACvB,IAAIwK,IAAI,GAAG,EAAE;MACb,IAAI9G,GAAG,GAAG,EAAE;MACZ,IAAI3D,MAAM,CAAC6E,kBAAkB,CAAC6F,MAAM,GAAG,CAAC,EAAE;QACtC,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAAsG,qBAAA,GAAG3K,MAAM,CAAC6E,kBAAkB,cAAA8F,qBAAA,uBAAzBA,qBAAA,CAA2BD,MAAM,GAAErG,CAAC,EAAE,EAAE;UAAA,IAAAsG,qBAAA;UACxDhH,GAAG,GAAG,2BAA2B,GAAG3D,MAAM,CAAC6E,kBAAkB,CAACR,CAAC,CAAC,CAACuG,SAAS;UAC1E,MAAMzM,UAAU,CAAC,KAAK,EAAEwF,GAAG,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;YACXsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAACE,IAAI,CAAC;YACrByG,IAAI,CAAC3F,IAAI,CAAChB,GAAG,CAACE,IAAI,CAACO,IAAI,CAAC;UAC5B,CAAC,CAAC,CACDU,KAAK,CAAEzB,CAAC,IAAK;YACV4B,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;UAClB,CAAC,CAAC;QACV;QACA,IAAIqH,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAK,SAAS,CAAC;QAC5C,IAAID,IAAI,KAAKrF,SAAS,EAAE;UACpB,IAAI,CAAC1E,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,EAAE,0DAA0D;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QACtJ,CAAC,MAAM;UACHwC,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACxD,KAAK,CAACuB,iBAAiB,GAAG,cAAc,GAAG1B,MAAM,CAACV,EAAE,GAAG,cAAc,GAAGU,MAAM,CAACC,KAAK,CAAC8K,UAAU,CAACtG,UAAU,CAACnF,EAAE,GAAG,+BAA+B;UACvL;UACA,MAAMnB,UAAU,CAAC,MAAM,EAAEwF,GAAG,CAAC,CACxBE,IAAI,CAACC,GAAG,IAAI;YACTsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAACE,IAAI,CAAC;YACrB,IAAI,CAAClD,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,4CAA4C;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;YAC7H2I,UAAU,CAAC,MAAM;cACbnJ,MAAM,CAACC,QAAQ,CAACoK,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAC/F,KAAK,CAAEzB,CAAC,IAAK;YAAA,IAAAyH,aAAA,EAAAC,aAAA;YACZ9F,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;YACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,iBAAiB;cAAEC,MAAM,yEAAAoE,MAAA,CAAsE,EAAA2F,aAAA,GAAAzH,CAAC,CAAC+B,QAAQ,cAAA0F,aAAA,uBAAVA,aAAA,CAAYjH,IAAI,MAAKwB,SAAS,IAAA0F,aAAA,GAAG1H,CAAC,CAAC+B,QAAQ,cAAA2F,aAAA,uBAAVA,aAAA,CAAYlH,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;cAAEtE,IAAI,EAAE;YAAK,CAAC,CAAC;UAC/N,CAAC,CAAC;QACV;MACJ,CAAC,MAAM;QACHwC,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACxD,KAAK,CAACuB,iBAAiB,GAAG,cAAc,GAAG1B,MAAM,CAACV,EAAE,GAAG,cAAc,GAAGU,MAAM,CAACC,KAAK,CAAC8K,UAAU,CAACtG,UAAU,CAACnF,EAAE,GAAG,+BAA+B;QACvL;QACA,MAAMnB,UAAU,CAAC,MAAM,EAAEwF,GAAG,CAAC,CACxBE,IAAI,CAACC,GAAG,IAAI;UACTsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAAClD,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,4CAA4C;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7H2I,UAAU,CAAC,MAAM;YACbnJ,MAAM,CAACC,QAAQ,CAACoK,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAC/F,KAAK,CAAEzB,CAAC,IAAK;UAAA,IAAA2H,aAAA,EAAAC,aAAA;UACZhG,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;UACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAoE,MAAA,CAAsE,EAAA6F,aAAA,GAAA3H,CAAC,CAAC+B,QAAQ,cAAA4F,aAAA,uBAAVA,aAAA,CAAYnH,IAAI,MAAKwB,SAAS,IAAA4F,aAAA,GAAG5H,CAAC,CAAC+B,QAAQ,cAAA6F,aAAA,uBAAVA,aAAA,CAAYpH,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;YAAEtE,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/N,CAAC,CAAC;MACV;IAEJ,CAAC,MAAM;MACH,IAAI,CAACL,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,6FAA6F;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACzL;EACJ;EACAoF,kBAAkBA,CAAA,EAAG;IACjB,IAAId,OAAO,GACP,iCAAiC;IACrC,IAAItD,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIgG,MAAM,GAAG,IAAI,CAACjI,KAAK,CAAC4B,iBAAiB,CAACqG,MAAM,CAAC0C,EAAE,IAAIA,EAAE,CAAC7K,KAAK,KAAK,IAAI,CAAC;IACzE,IAAImI,MAAM,CAACsC,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAACvK,KAAK,CAACmB,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAACnB,KAAK,CAACmB,QAAQ,CAACjB,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAACgI,IAAI,KAAK,UAAU,EAAE;YAC7BlG,OAAO,CAAC0C,IAAI,CAAC;cACT8D,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS;cACnDtG,KAAK,EAAElC,OAAO,CAACyI;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAC5G,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACsB,QAAQ,CAAC;QACVzD,MAAM,EAAE,IAAI,CAACG,KAAK,CAAC4B,iBAAiB;QACpCG,aAAa,EAAE,IAAI;QACnBC,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAEoD;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAI2C,MAAM,CAACsC,MAAM,KAAK,IAAI,CAACvK,KAAK,CAAC4B,iBAAiB,CAAC2I,MAAM,EAAE;MAC9D,IAAIW,QAAQ,GAAG,IAAI,CAAClL,KAAK,CAAC4B,iBAAiB,CAACqG,MAAM,CAAC9H,OAAO,IAAIA,OAAO,CAACL,KAAK,CAACqL,QAAQ,KAAK,IAAI,CAAC;MAC9F,IAAID,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIW,QAAQ,CAACX,MAAM,KAAK,IAAI,CAACvK,KAAK,CAAC4B,iBAAiB,CAAC2I,MAAM,EAAE;UACzD,IAAI,IAAI,CAACvK,KAAK,CAACmB,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAACnB,KAAK,CAACmB,QAAQ,CAACjB,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACgI,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAIC,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;gBAC1GxG,KAAK,CAAC2C,IAAI,CAAC;kBACP8D,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1F/F,KAAK,EAAElC,OAAO,CAACyI;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAAC5G,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACsB,QAAQ,CAAC;YACVzD,MAAM,EAAE,IAAI,CAACG,KAAK,CAAC4B,iBAAiB;YACpCG,aAAa,EAAE,IAAI;YACnBC,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAEoD;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAAC3E,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JC,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAChB,KAAK,CAACmB,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAACnB,KAAK,CAACmB,QAAQ,CAACjB,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACgI,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAIC,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAAClI,OAAO,CAACmI,WAAW,CAAC,GAAGD,QAAQ,CAAClI,OAAO,CAACoI,YAAY,CAAC,GAAGF,QAAQ,CAAClI,OAAO,CAACqI,aAAa,CAAC;cAC1GxG,KAAK,CAAC2C,IAAI,CAAC;gBACP8D,KAAK,EAAEtI,OAAO,CAACuI,UAAU,GAAG,GAAG,GAAGvI,OAAO,CAACwI,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1F/F,KAAK,EAAElC,OAAO,CAACyI;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAC5G,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACsB,QAAQ,CAAC;UACVzD,MAAM,EAAE,IAAI,CAACG,KAAK,CAAC4B,iBAAiB;UACpCG,aAAa,EAAE,IAAI;UACnBC,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAEoD;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAAC3E,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAqF,gBAAgBA,CAAChD,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAE1B,iBAAiB,EAAEyB,CAAC,CAAChB;IAAM,CAAC,CAAC;EACjD;EACA;EACAmE,mBAAmBA,CAAC3G,MAAM,EAAE;IACxB,IAAI,CAACyD,QAAQ,CAAC;MACVzD,MAAM;MACN8C,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAM2D,YAAYA,CAAA,EAAG;IACjB,IAAIrG,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO,CAACgI,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAC/I,EAAE,KAAK,IAAI,CAACa,KAAK,CAACH,MAAM,CAACV,EAC1C,CAAC;IACD,IAAI,CAACmE,QAAQ,CAAC;MACVrD,OAAO;MACP0C,kBAAkB,EAAE,KAAK;MACzB9C,MAAM,EAAE,IAAI,CAACX;IACjB,CAAC,CAAC;IACF,IAAIsE,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACxD,KAAK,CAACH,MAAM,CAACV,EAAE;IAC7D,MAAMnB,UAAU,CAAC,QAAQ,EAAEwF,GAAG,CAAC,CAC1BE,IAAI,CAACC,GAAG,IAAI;MACTsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAAClD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CC,IAAI,EAAE;MACV,CAAC,CAAC;MACFR,MAAM,CAACC,QAAQ,CAACoK,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC/F,KAAK,CAAEzB,CAAC,IAAK;MAAA,IAAA+H,aAAA,EAAAC,aAAA;MACZpG,OAAO,CAACC,GAAG,CAAC7B,CAAC,CAAC;MACd,IAAI,CAAC1C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAoE,MAAA,CAAqE,EAAAiG,aAAA,GAAA/H,CAAC,CAAC+B,QAAQ,cAAAgG,aAAA,uBAAVA,aAAA,CAAYvH,IAAI,MAAKwB,SAAS,IAAAgG,aAAA,GAAGhI,CAAC,CAAC+B,QAAQ,cAAAiG,aAAA,uBAAVA,aAAA,CAAYxH,IAAI,GAAGR,CAAC,CAACiC,OAAO,CAAE;QAC1ItE,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAuF,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACjD,QAAQ,CAAC;MACVX,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EAoBA2I,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpB3M,OAAA,CAACf,KAAK,CAAC2N,QAAQ;MAAAC,QAAA,eACX7M,OAAA;QAAK8M,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrB7M,OAAA;UAAK8M,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnB7M,OAAA;YAAK8M,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvC7M,OAAA,CAACR,MAAM;cACHsN,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAChG,kBAAmB;cAAA8F,QAAA,GAEhC,GAAG,EACH1N,QAAQ,CAAC6N,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTpN,OAAA,CAACP,KAAK;cACFuF,SAAS,EAAE,IAAI,CAAC5D,KAAK,CAACH,MAAO;cAC7BqB,QAAQ,EAAE,IAAI,CAAClB,KAAK,CAACkB,QAAS;cAC9BgB,GAAG,EAAE,IAAI,CAAClC,KAAK,CAACkC,GAAI;cACpB+J,GAAG,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAME,mBAAmB,gBACrBtN,OAAA,CAACf,KAAK,CAAC2N,QAAQ;MAAAC,QAAA,eACX7M,OAAA,CAACR,MAAM;QAACsN,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC9F,UAAW;QAAA4F,QAAA,GACjE,GAAG,EACH1N,QAAQ,CAAC6N,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMG,wBAAwB,gBAC1BvN,OAAA,CAACf,KAAK,CAAC2N,QAAQ;MAAAC,QAAA,gBACX7M,OAAA,CAACR,MAAM;QACHqK,KAAK,EAAC,IAAI;QACV2D,IAAI,EAAC,aAAa;QAClBV,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACpF;MAAuB;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFpN,OAAA,CAACR,MAAM;QAACsN,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACrF,YAAa;QAAAmF,QAAA,GACxD,GAAG,EACH1N,QAAQ,CAACsO,EAAE,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMM,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACIxC,KAAK,EAAE,QAAQ;MACfyC,MAAM,EAAE3O,QAAQ,CAAC4O,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,MAAM;MACbyC,MAAM,EAAE3O,QAAQ,CAACqG,IAAI;MACrBwI,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,UAAU;MACjByC,MAAM,EAAE3O,QAAQ,CAACgP,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,cAAc;MACrByC,MAAM,EAAE3O,QAAQ,CAACiP,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,+BAA+B;MACtCyC,MAAM,EAAE3O,QAAQ,CAACkP,YAAY;MAC7BL,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,gCAAgC;MACvCyC,MAAM,EAAE3O,QAAQ,CAACmP,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,cAAc;MACrByC,MAAM,EAAE3O,QAAQ,CAACoP,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACI7C,KAAK,EAAE,SAAS;MAChByC,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMM,YAAY,GAAG,CACjB;MAAE7J,IAAI,EAAExF,QAAQ,CAACsP,OAAO;MAAEjB,IAAI,eAAExN,OAAA;QAAG8M,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEsB,OAAO,EAAE,IAAI,CAAC7H;IAAe,CAAC,EAC3F;MAAElC,IAAI,EAAExF,QAAQ,CAACwP,kBAAkB;MAAEnB,IAAI,eAAExN,OAAA;QAAG8M,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEsB,OAAO,EAAE,IAAI,CAAC1H,UAAU;MAAEjG,MAAM,EAAE,QAAQ;MAAE6N,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAEjK,IAAI,EAAExF,QAAQ,CAAC0P,YAAY;MAAErB,IAAI,eAAExN,OAAA;QAAG8M,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEsB,OAAO,EAAE,IAAI,CAAC1N;IAAY,CAAC,EAChG;MAAE2D,IAAI,EAAExF,QAAQ,CAAC2P,OAAO;MAAEtB,IAAI,eAAExN,OAAA;QAAG8M,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEsB,OAAO,EAAE,IAAI,CAACnH,OAAO;MAAExG,MAAM,EAAE;IAAW,CAAC,EACxG;MAAE4D,IAAI,EAAExF,QAAQ,CAAC4P,OAAO;MAAEvB,IAAI,eAAExN,OAAA;QAAG8M,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEsB,OAAO,EAAE,IAAI,CAAC9G;IAAoB,CAAC,CACrG;IACD,oBACI5H,OAAA;MAAK8M,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9C7M,OAAA,CAACT,KAAK;QAACyP,GAAG,EAAGjD,EAAE,IAAK,IAAI,CAAChK,KAAK,GAAGgK;MAAG;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvCpN,OAAA,CAACJ,GAAG;QAAAqN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACN,IAAI,CAAChM,KAAK,CAAC8B,SAAS,iBACjBlD,OAAA,CAACV,UAAU;QAAC2P,KAAK,EAAC,oBAAoB;QAACC,OAAO,EAAC,yBAAyB;QAACC,MAAM,EAAC;MAAS;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEhGpN,OAAA;QAAK8M,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnC7M,OAAA;UAAA6M,QAAA,EAAK1N,QAAQ,CAACiQ;QAAgB;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNpN,OAAA;QAAK8M,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjB7M,OAAA,CAACH,eAAe;UACZmP,GAAG,EAAGjD,EAAE,IAAK,IAAI,CAACsD,EAAE,GAAGtD,EAAG;UAC1BtI,KAAK,EAAE,IAAI,CAACrC,KAAK,CAACC,OAAQ;UAC1B4B,OAAO,EAAE,IAAI,CAAC7B,KAAK,CAAC6B,OAAQ;UAC5ByK,MAAM,EAAEA,MAAO;UACf4B,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTrI,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBnD,KAAK,EAAE,IAAI,CAAC7C,KAAK,CAAC4C,UAAU,CAACC,KAAM;UACnCV,YAAY,EAAE,IAAI,CAACnC,KAAK,CAACmC,YAAa;UACtCW,IAAI,EAAE,IAAI,CAAC9C,KAAK,CAAC4C,UAAU,CAACE,IAAK;UACjCwL,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,aAAa,EAAEpB,YAAa;UAC5BqB,mBAAmB,EAAE,IAAK;UAC1BlC,aAAa,EAAC,UAAU;UACxBmC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAClJ,cAAe;UAClCmJ,SAAS,EAAE,IAAI,CAAC5O,KAAK,CAAC4B,iBAAkB;UACxCiN,iBAAiB,EAAGxL,CAAC,IAAK,IAAI,CAACgD,gBAAgB,CAAChD,CAAC,CAAE;UACnDyL,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAAC3I,kBAAmB;UAC3C4I,gBAAgB,EAAEjR,QAAQ,CAACqI,kBAAmB;UAC9C6I,mBAAmB,EAAE,CAAC,IAAI,CAACjP,KAAK,CAAC4B,iBAAiB,IAAI,CAAC,IAAI,CAAC5B,KAAK,CAAC4B,iBAAiB,CAAC2I,MAAO;UAC3FtE,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBjD,SAAS,EAAE,IAAI,CAAChD,KAAK,CAAC4C,UAAU,CAACI,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACjD,KAAK,CAAC4C,UAAU,CAACK,SAAU;UAC3CiD,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBhD,OAAO,EAAE,IAAI,CAAClD,KAAK,CAAC4C,UAAU,CAACM,OAAQ;UACvCgM,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAS;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpN,OAAA,CAACX,MAAM;QACHmR,OAAO,EAAE,IAAI,CAACpP,KAAK,CAACsB,aAAc;QAClCoL,MAAM,EAAE3O,QAAQ,CAACsR,MAAO;QACxBC,KAAK;QACL5D,SAAS,EAAC,kBAAkB;QAC5B6D,MAAM,EAAEhE,kBAAmB;QAC3BiE,MAAM,EAAE,IAAI,CAAC7J,kBAAmB;QAChC8J,SAAS,EAAE,KAAM;QAAAhE,QAAA,eAEjB7M,OAAA,CAACL,mBAAmB;UAChBsB,MAAM,EAAE,IAAI,CAACG,KAAK,CAACkB,QAAS;UAC5BjB,OAAO,EAAE,IAAI,CAACD,KAAK,CAACH,MAAO;UAC3B+D,SAAS,EAAE,IAAI,CAAC5D,KAAK,CAACH,MAAO;UAC7B6P,MAAM,EAAE;QAAK;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAETpN,OAAA,CAACX,MAAM;QACHmR,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC+B,aAAc;QAClC2K,MAAM,EAAE,IAAI,CAAC1M,KAAK,CAACkC,GAAI;QACvBoN,KAAK;QACL5D,SAAS,EAAC,kBAAkB;QAC5B6D,MAAM,EAAErD,mBAAoB;QAC5BsD,MAAM,EAAE,IAAI,CAAC3J,UAAW;QAAA4F,QAAA,eAExB7M,OAAA,CAACF,kBAAkB;UAACmB,MAAM,EAAE,IAAI,CAACG,KAAK,CAACH,MAAO;UAACmC,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAACjC,KAAK,CAACiC,OAAQ;UAAC0N,YAAY,EAAE;QAAK;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eAETpN,OAAA,CAACX,MAAM;QACHmR,OAAO,EAAE,IAAI,CAACpP,KAAK,CAAC2C,kBAAmB;QACvC+J,MAAM,EAAE3O,QAAQ,CAAC6R,QAAS;QAC1BN,KAAK;QACLC,MAAM,EAAEpD,wBAAyB;QACjCqD,MAAM,EAAE,IAAI,CAACjJ,sBAAuB;QAAAkF,QAAA,eAEpC7M,OAAA;UAAK8M,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC7M,OAAA;YACI8M,SAAS,EAAC,mCAAmC;YAC7CmE,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAAChM,KAAK,CAACH,MAAM,iBACdjB,OAAA;YAAA6M,QAAA,GACK1N,QAAQ,CAACgS,YAAY,EAAC,GAAC,eAAAnR,OAAA;cAAA6M,QAAA,GAAI,IAAI,CAACzL,KAAK,CAACH,MAAM,CAACsE,MAAM,EAAC,GAAC;YAAA;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd;AACJ;AAEA,eAAenN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}