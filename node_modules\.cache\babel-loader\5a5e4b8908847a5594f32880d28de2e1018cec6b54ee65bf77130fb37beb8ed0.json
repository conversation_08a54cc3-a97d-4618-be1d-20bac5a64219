{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\print\\\\templateOrderPrint.jsx\";\nimport React, { Component } from 'react';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport CustomDataTable from '../customDataTable';\nimport { Costanti } from '../traduttore/const';\nimport { agenteDettaglioCliente, distributore } from '../route';\nimport ReactToPrint, { PrintContextConsumer } from 'react-to-print';\nimport logo from '../../img/tm_logo-01.svg';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass TemplateOrderPrint extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      firstName: '',\n      address: '',\n      indFatt: '',\n      classDisabled: '',\n      nameAddressDis: 'row mb-2',\n      classDocument: 'd-none',\n      note: '',\n      iva: 0,\n      mex: '',\n      role: ''\n    };\n    this.calcTot = this.calcTot.bind(this);\n    this.calcTotIva = this.calcTotIva.bind(this);\n    this.calcIva = this.calcIva.bind(this);\n    this.calcFee = this.calcFee.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  componentDidMount() {\n    var role = localStorage.getItem('role');\n    this.setState({\n      role: role\n    });\n    if (this.props.disabled === true) {\n      this.setState({\n        results: this.props.results3,\n        firstName: this.props.firstName,\n        address: this.props.address,\n        indFatt: this.props.indFatt,\n        classDisabled: 'd-none'\n      });\n    } else if (this.props.disPersonalData) {\n      this.setState({\n        results: this.props.results3,\n        note: this.props.result.note,\n        nameAddressDis: 'd-none',\n        mex: this.props.mex\n      });\n    } else if (this.props.doc === true) {\n      var _this$props$documento;\n      this.setState({\n        mex: this.props.mex,\n        results: this.props.results3,\n        note: (_this$props$documento = this.props.documento) === null || _this$props$documento === void 0 ? void 0 : _this$props$documento.note,\n        //classDisabled: 'd-none',\n        nameAddressDis: 'd-none',\n        classDocument: ''\n      });\n    } else {\n      var _this$props$result;\n      this.setState({\n        note: (_this$props$result = this.props.result) === null || _this$props$result === void 0 ? void 0 : _this$props$result.note,\n        results: this.props.results3,\n        firstName: this.props.firstName,\n        address: this.props.address,\n        indFatt: this.props.indFatt,\n        mex: this.props.mex\n      });\n    }\n  }\n  // Calcolo il totale dei prodotti\n  calcTot() {\n    var tot = 0;\n    if (this.props.results3) {\n      this.props.results3.forEach(element => {\n        tot += parseFloat(element.total);\n      });\n      if (this.state.role === distributore) {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR',\n          maximumFractionDigits: 6\n        }).format(tot);\n      } else {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(tot);\n      }\n    }\n  }\n  // Calcolo l'ammontare dell'iva dei prodotti sottraento al totale tassato il totale\n  calcIva() {\n    var iva = 0;\n    if (this.props.results3) {\n      this.props.results3.forEach(element => {\n        iva += parseFloat(element.totalTaxed) - parseFloat(element.total);\n      });\n      if (this.state.role === distributore) {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR',\n          maximumFractionDigits: 6\n        }).format(iva);\n      } else {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(iva);\n      }\n    }\n  }\n  // Calcolo il totale tassato dei prodotti\n  calcTotIva() {\n    var totIva = 0;\n    if (this.props.results3) {\n      this.props.results3.forEach(element => {\n        totIva += parseFloat(element.totalTaxed);\n      });\n      if (this.state.role === distributore) {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR',\n          maximumFractionDigits: 6\n        }).format(totIva);\n      } else {\n        return new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(totIva);\n      }\n    }\n  }\n  // Calcolo il totale tassato dei prodotti\n  calcFee() {\n    var fee = 0;\n    var role = localStorage.getItem(\"role\");\n    if (this.props.results3 && role !== 'PDV') {\n      this.props.results3.forEach(element => {\n        if (element.fee) {\n          fee += parseFloat(element.fee);\n        }\n      });\n      if (!isNaN(fee) && fee !== 0) {\n        if (this.state.role === distributore) {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"Fee:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 29\n            }, this), \" \", new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(fee)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"Fee:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this), \" \", new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(fee)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    }\n  }\n  render() {\n    var _this$props$documento2, _this$props$documento3, _this$props$documento4, _this$props$documento5, _this$props$documento6, _this$props$documento7, _this$props$documento8, _this$props$documento9, _this$props$documento0, _this$props$documento1;\n    var fee = 0;\n    var fields = [];\n    var role = localStorage.getItem(\"role\");\n    if (this.state.results && role !== 'PDV') {\n      this.state.results.forEach(element => {\n        if (element.fee && element.fee !== '0') {\n          fee += element.fee;\n        }\n      });\n      if (fee !== 0) {\n        fee = {\n          field: 'fee',\n          header: 'Fee',\n          body: 'fee',\n          sortable: true,\n          showHeader: true,\n          className: this.state.classDisabled\n        };\n      } else {\n        fee = {};\n      }\n    }\n    if (this.props.doc === true) {\n      fields = [{\n        field: 'externalCode',\n        header: Costanti.exCode,\n        body: 'docExCode',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'description',\n        header: Costanti.Prodotto,\n        body: 'docProdName',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'eanCode',\n        header: Costanti.eanCode,\n        body: 'eanCode',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'um',\n        header: Costanti.UnitMis,\n        body: 'um',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'colliPreventivo',\n        header: Costanti.colliPreventivo,\n        body: 'colliPreventivo',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'colliConsuntivo',\n        header: Costanti.colliConsuntivo,\n        body: 'colliConsuntivo',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'lotto',\n        header: Costanti.lotto,\n        body: 'lotto',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'scadenza',\n        header: Costanti.scadenza,\n        body: 'scadenzaDoc',\n        sortable: true,\n        showHeader: true\n      }];\n    } else {\n      fields = [{\n        field: 'product.externalCode',\n        header: Costanti.exCode,\n        body: 'prodId',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'product.description',\n        header: Costanti.Prodotto,\n        body: 'prodName',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'unitMeasure',\n        header: Costanti.UnitMis,\n        body: 'unitMeasure',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'pcspkgs',\n        header: 'Package',\n        body: 'pcsXpackage',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'colli',\n        header: Costanti.Colli,\n        body: 'colli',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'quantity',\n        header: Costanti.Quantità,\n        body: 'quantity',\n        sortable: true,\n        showHeader: true\n      }, {\n        field: 'unitPrice',\n        header: Costanti.Prezzo,\n        body: 'unitPrice',\n        sortable: true,\n        showHeader: true,\n        className: this.state.classDisabled\n      }, {\n        field: 'total',\n        header: Costanti.Tot,\n        body: 'total',\n        sortable: true,\n        showHeader: true,\n        className: this.state.classDisabled\n      }, {\n        field: 'tax',\n        header: Costanti.Iva,\n        body: 'iva',\n        sortable: true,\n        showHeader: true,\n        className: this.state.classDisabled\n      }, {\n        field: 'totalTaxed',\n        header: Costanti.TotTax,\n        body: 'totalTaxed',\n        sortable: true,\n        showHeader: true,\n        className: this.state.classDisabled\n      }, fee];\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo mb-5 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          width: 230,\n          src: logo,\n          onError: e => e.target.src = logo,\n          alt: \"Winet e-procurement logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: this.state.classDocument,\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DettDoc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row border mx-1 my-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center border-right border-bottom d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.type, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 106\n            }, this), (_this$props$documento2 = this.props.documento) === null || _this$props$documento2 === void 0 ? void 0 : _this$props$documento2.type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center border-bottom d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.Stato, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 93\n            }, this), Costanti[((_this$props$documento3 = this.props.documento) === null || _this$props$documento3 === void 0 ? void 0 : (_this$props$documento4 = _this$props$documento3.tasks) === null || _this$props$documento4 === void 0 ? void 0 : _this$props$documento4.status) !== 'not delivered' ? (_this$props$documento5 = this.props.documento) === null || _this$props$documento5 === void 0 ? void 0 : (_this$props$documento6 = _this$props$documento5.tasks) === null || _this$props$documento6 === void 0 ? void 0 : _this$props$documento6.status : 'notdelivered']]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center border-right border-bottom d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.NDoc, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 106\n            }, this), (_this$props$documento7 = this.props.documento) === null || _this$props$documento7 === void 0 ? void 0 : _this$props$documento7.number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center border-bottom d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.Destinazione, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 93\n            }, this), (_this$props$documento8 = this.props.documento) === null || _this$props$documento8 === void 0 ? void 0 : _this$props$documento8.deliveryDestination]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center border-right d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.DimensioniPedana, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 92\n            }, this), (_this$props$documento9 = this.props.documento) === null || _this$props$documento9 === void 0 ? void 0 : _this$props$documento9.dimPedana]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-6 text-center d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              className: \"mr-2\",\n              children: [Costanti.PesoPedana, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 79\n            }, this), (_this$props$documento0 = this.props.documento) === null || _this$props$documento0 === void 0 ? void 0 : _this$props$documento0.pesoPedana]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mb-4 w-100 text-center\",\n        children: this.state.mex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: this.state.nameAddressDis,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-1\",\n              children: [Costanti.Nome, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 28\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\" \", this.state.firstName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 78\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-1\",\n              children: [Costanti.IndirizzoFatt, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 28\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\" \", this.state.indFatt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 87\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-1\",\n              children: [Costanti.IndCons, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 28\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\" \", this.state.address]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 81\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"datatable-responsive-demo wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-center\",\n          children: Costanti.ListProd\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n            ref: el => this.dt = el,\n            value: this.props.results3,\n            fields: fields,\n            autoLayout: true,\n            hideHeader: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"page-break\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: this.state.classDisabled,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end flex-row align-items-end mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                children: [Costanti.Tot, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 52\n              }, this), \" \", this.calcTot()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                children: [Costanti.Iva, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 52\n              }, this), \" \", this.calcIva()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                children: [Costanti.TotTax, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 52\n              }, this), \" \", this.calcTotIva()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 29\n            }, this), this.calcFee()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 mr-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: Costanti.Note\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputTextarea, {\n              className: \"w-100\",\n              rows: 5,\n              cols: 30,\n              readOnly: true,\n              value: this.state.note !== undefined ? this.state.note : (_this$props$documento1 = this.props.documento) === null || _this$props$documento1 === void 0 ? void 0 : _this$props$documento1.note\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this);\n  }\n}\nclass TemplatePartiteApertePrint extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      role: ''\n    };\n  }\n  componentDidMount() {\n    var role = localStorage.getItem('role');\n    this.setState({\n      role: role\n    });\n  }\n  render() {\n    var _this$props$results, _this$props$results2, _this$props$results3, _this$props$results4, _this$props$results5, _this$props$results6, _this$props$results7, _this$props$result2;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo mb-5 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          width: 230,\n          src: logo,\n          onError: e => e.target.src = logo,\n          alt: \"Winet e-procurement logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"solid-head d-flex flex-direction-row justify-content-center align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: (_this$props$results = this.props.results) === null || _this$props$results === void 0 ? void 0 : _this$props$results.firstName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row w-100 mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-4 mb-md-0\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-credit-card mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.pIva\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 103\n              }, this), \": \", (_this$props$results2 = this.props.results) === null || _this$props$results2 === void 0 ? void 0 : _this$props$results2.pIva]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-mobile mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.Tel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 98\n              }, this), \": \", (_this$props$results3 = this.props.results) === null || _this$props$results3 === void 0 ? void 0 : _this$props$results3.tel]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-envelope mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.Email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 100\n              }, this), \": \", (_this$props$results4 = this.props.results) === null || _this$props$results4 === void 0 ? void 0 : _this$props$results4.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-4 mb-md-0\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-directions mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.Indirizzo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 102\n              }, this), \": \", (_this$props$results5 = this.props.results) === null || _this$props$results5 === void 0 ? void 0 : _this$props$results5.address]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-map-marker mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.Città\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 102\n              }, this), \": \", (_this$props$results6 = this.props.results) === null || _this$props$results6 === void 0 ? void 0 : _this$props$results6.city]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"list-group-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-compass mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 61\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: Costanti.CodPost\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 99\n              }, this), \": \", (_this$props$results7 = this.props.results) === null || _this$props$results7 === void 0 ? void 0 : _this$props$results7.cap]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 spacerCol\",\n          children: /*#__PURE__*/_jsxDEV(\"hr\", {\n            className: \"mt-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row w-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-3\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: Costanti.Data\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-3\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: Costanti.NumeroPartita\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-3\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: Costanti.Residuo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-3\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: Costanti.Tot\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 17\n      }, this), (_this$props$result2 = this.props.result) === null || _this$props$result2 === void 0 ? void 0 : _this$props$result2.map((el, key) => {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row border\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-3 border-right p-3\",\n              children: new Date(el.dataDoc).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-3 border-right p-3\",\n              children: el.numeroPartita\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-3 border-right p-3\",\n              children: this.state.role === distributore ? new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(el.residuo) : new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(el.residuo)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-3 p-3\",\n              children: this.state.role === distributore ? new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(el.totale) : new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(el.totale)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 29\n          }, this)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 25\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 pr-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"border border-top-0 p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"mr-2\",\n                children: [Costanti.Tot, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 33\n              }, this), this.state.role === distributore ? new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(this.props.total) : new Intl.NumberFormat('it-IT', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(this.props.total)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport class Print extends Component {\n  render() {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(ReactToPrint, {\n        content: () => this.componentRef,\n        children: /*#__PURE__*/_jsxDEV(PrintContextConsumer, {\n          children: _ref => {\n            let {\n              handlePrint\n            } = _ref;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              id: this.props.id,\n              className: \"printBtn p-button p-component p-button-text\",\n              onClick: handlePrint,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-print mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 134\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: Costanti.Stampa\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 170\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 29\n            }, this);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-none\",\n        children: window.location.pathname !== agenteDettaglioCliente ? /*#__PURE__*/_jsxDEV(TemplateOrderPrint, {\n          ref: el => this.componentRef = el,\n          result: this.props.result,\n          results3: this.props.results3,\n          firstName: this.props.firstName,\n          address: this.props.address,\n          indFatt: this.props.indFatt,\n          disPersonalData: this.props.disPersonalData,\n          mex: this.props.mex,\n          doc: this.props.doc,\n          documento: this.props.documento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(TemplatePartiteApertePrint, {\n          ref: el => this.componentRef = el,\n          result: this.props.result,\n          results: this.props.results,\n          total: this.props.total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true);\n  }\n}\nexport default TemplateOrderPrint;", "map": {"version": 3, "names": ["React", "Component", "InputTextarea", "CustomDataTable", "<PERSON><PERSON>", "agenteDettaglioCliente", "distributore", "ReactToPrint", "PrintContextConsumer", "logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TemplateOrderPrint", "constructor", "props", "state", "results", "firstName", "address", "indFatt", "classDisabled", "nameAddressDis", "classDocument", "note", "iva", "mex", "role", "calcTot", "bind", "calcTotIva", "calcIva", "calcFee", "componentDidMount", "localStorage", "getItem", "setState", "disabled", "results3", "disPersonalData", "result", "doc", "_this$props$documento", "documento", "_this$props$result", "tot", "for<PERSON>ach", "element", "parseFloat", "total", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "totalTaxed", "totIva", "fee", "isNaN", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "render", "_this$props$documento2", "_this$props$documento3", "_this$props$documento4", "_this$props$documento5", "_this$props$documento6", "_this$props$documento7", "_this$props$documento8", "_this$props$documento9", "_this$props$documento0", "_this$props$documento1", "fields", "field", "header", "body", "sortable", "showHeader", "exCode", "<PERSON><PERSON><PERSON>", "eanCode", "UnitMis", "colliPreventivo", "colliConsuntivo", "lotto", "scadenza", "<PERSON><PERSON>", "Quantità", "Prezzo", "<PERSON><PERSON>", "<PERSON><PERSON>", "TotTax", "width", "src", "onError", "e", "target", "alt", "DettDoc", "type", "Stato", "tasks", "status", "NDoc", "number", "Destinazione", "deliveryDestination", "DimensioniPedana", "<PERSON><PERSON><PERSON><PERSON>", "PesoPedana", "pesoPedana", "Nome", "IndirizzoFatt", "IndCons", "ListProd", "ref", "el", "dt", "value", "autoLayout", "<PERSON><PERSON>ead<PERSON>", "Note", "rows", "cols", "readOnly", "undefined", "TemplatePartiteApertePrint", "_this$props$results", "_this$props$results2", "_this$props$results3", "_this$props$results4", "_this$props$results5", "_this$props$results6", "_this$props$results7", "_this$props$result2", "pIva", "Tel", "tel", "Email", "email", "<PERSON><PERSON><PERSON><PERSON>", "Città", "city", "CodPost", "cap", "Data", "NumeroPartita", "Residuo", "map", "key", "Date", "dataDoc", "toLocaleDateString", "numeroPartita", "residuo", "totale", "Print", "content", "componentRef", "_ref", "handlePrint", "id", "onClick", "Stampa", "window", "location", "pathname"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/print/templateOrderPrint.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport CustomDataTable from '../customDataTable';\nimport { Costanti } from '../traduttore/const';\nimport { agenteDettaglioCliente, distributore } from '../route';\nimport ReactToPrint, { PrintContextConsumer } from 'react-to-print';\nimport logo from '../../img/tm_logo-01.svg'\n\nclass TemplateOrderPrint extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            firstName: '',\n            address: '',\n            indFatt: '',\n            classDisabled: '',\n            nameAddressDis: 'row mb-2',\n            classDocument: 'd-none',\n            note: '',\n            iva: 0,\n            mex: '',\n            role: ''\n        }\n        this.calcTot = this.calcTot.bind(this);\n        this.calcTotIva = this.calcTotIva.bind(this);\n        this.calcIva = this.calcIva.bind(this);\n        this.calcFee = this.calcFee.bind(this);\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    componentDidMount() {\n        var role = localStorage.getItem('role')\n        this.setState({ role: role })\n        if (this.props.disabled === true) {\n            this.setState({\n                results: this.props.results3,\n                firstName: this.props.firstName,\n                address: this.props.address,\n                indFatt: this.props.indFatt,\n                classDisabled: 'd-none'\n            })\n        } else if (this.props.disPersonalData) {\n            this.setState({\n                results: this.props.results3,\n                note: this.props.result.note,\n                nameAddressDis: 'd-none',\n                mex: this.props.mex,\n            })\n        } else if (this.props.doc === true) {\n            this.setState({\n                mex: this.props.mex,\n                results: this.props.results3,\n                note: this.props.documento?.note,\n                //classDisabled: 'd-none',\n                nameAddressDis: 'd-none',\n                classDocument: ''\n            })\n        } else {\n            this.setState({\n                note: this.props.result?.note,\n                results: this.props.results3,\n                firstName: this.props.firstName,\n                address: this.props.address,\n                indFatt: this.props.indFatt,\n                mex: this.props.mex\n            })\n        }\n    }\n    // Calcolo il totale dei prodotti\n    calcTot() {\n        var tot = 0;\n        if (this.props.results3) {\n            this.props.results3.forEach(element => {\n                tot += parseFloat(element.total)\n            })\n            if (this.state.role === distributore) {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(tot)\n            } else {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(tot)\n            }\n        }\n    }\n    // Calcolo l'ammontare dell'iva dei prodotti sottraento al totale tassato il totale\n    calcIva() {\n        var iva = 0\n        if (this.props.results3) {\n            this.props.results3.forEach(element => {\n                iva += parseFloat(element.totalTaxed) - parseFloat(element.total)\n            })\n            if (this.state.role === distributore) {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(iva)\n            } else {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(iva)\n            }\n        }\n    }\n    // Calcolo il totale tassato dei prodotti\n    calcTotIva() {\n        var totIva = 0;\n        if (this.props.results3) {\n            this.props.results3.forEach(element => {\n                totIva += parseFloat(element.totalTaxed)\n            })\n            if (this.state.role === distributore) {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(totIva)\n            } else {\n                return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totIva)\n            }\n        }\n    }\n    // Calcolo il totale tassato dei prodotti\n    calcFee() {\n        var fee = 0;\n        var role = localStorage.getItem(\"role\")\n        if (this.props.results3 && role !== 'PDV') {\n            this.props.results3.forEach(element => {\n                if (element.fee) {\n                    fee += parseFloat(element.fee)\n                }\n            })\n            if (!isNaN(fee) && fee !== 0) {\n                if (this.state.role === distributore) {\n                    return (\n                        <span className=\"mr-4\">\n                            <b>Fee:</b> {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(fee)}\n                        </span>\n                    )\n                } else {\n                    return (\n                        <span className=\"mr-4\">\n                            <b>Fee:</b> {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(fee)}\n                        </span>\n                    )\n                }\n            }\n        }\n    }\n    render() {\n        var fee = 0\n        var fields = []\n        var role = localStorage.getItem(\"role\")\n        if (this.state.results && role !== 'PDV') {\n            this.state.results.forEach(element => {\n                if (element.fee && element.fee !== '0') {\n                    fee += element.fee\n                }\n            })\n            if (fee !== 0) {\n                fee = { field: 'fee', header: 'Fee', body: 'fee', sortable: true, showHeader: true, className: this.state.classDisabled }\n            } else {\n                fee = {}\n            }\n        }\n        if (this.props.doc === true) {\n            fields = [\n                { field: 'externalCode', header: Costanti.exCode, body: 'docExCode', sortable: true, showHeader: true },\n                { field: 'description', header: Costanti.Prodotto, body: 'docProdName', sortable: true, showHeader: true },\n                { field: 'eanCode', header: Costanti.eanCode, body: 'eanCode', sortable: true, showHeader: true },\n                { field: 'um', header: Costanti.UnitMis, body: 'um', sortable: true, showHeader: true },\n                { field: 'colliPreventivo', header: Costanti.colliPreventivo, body: 'colliPreventivo', sortable: true, showHeader: true },\n                { field: 'colliConsuntivo', header: Costanti.colliConsuntivo, body: 'colliConsuntivo', sortable: true, showHeader: true },\n                { field: 'lotto', header: Costanti.lotto, body: 'lotto', sortable: true, showHeader: true },\n                { field: 'scadenza', header: Costanti.scadenza, body: 'scadenzaDoc', sortable: true, showHeader: true },\n            ];\n        } else {\n            fields = [\n                { field: 'product.externalCode', header: Costanti.exCode, body: 'prodId', sortable: true, showHeader: true },\n                { field: 'product.description', header: Costanti.Prodotto, body: 'prodName', sortable: true, showHeader: true },\n                { field: 'unitMeasure', header: Costanti.UnitMis, body: 'unitMeasure', sortable: true, showHeader: true },\n                { field: 'pcspkgs', header: 'Package', body: 'pcsXpackage', sortable: true, showHeader: true },\n                { field: 'colli', header: Costanti.Colli, body: 'colli', sortable: true, showHeader: true },\n                { field: 'quantity', header: Costanti.Quantità, body: 'quantity', sortable: true, showHeader: true },\n                { field: 'unitPrice', header: Costanti.Prezzo, body: 'unitPrice', sortable: true, showHeader: true, className: this.state.classDisabled },\n                { field: 'total', header: Costanti.Tot, body: 'total', sortable: true, showHeader: true, className: this.state.classDisabled },\n                { field: 'tax', header: Costanti.Iva, body: 'iva', sortable: true, showHeader: true, className: this.state.classDisabled },\n                { field: 'totalTaxed', header: Costanti.TotTax, body: 'totalTaxed', sortable: true, showHeader: true, className: this.state.classDisabled },\n                fee\n            ];\n        }\n\n        return (\n            <div>\n                <div className='logo mb-5 d-flex justify-content-center'>\n                    <img width={230} src={logo} onError={(e) => e.target.src = logo} alt=\"Winet e-procurement logo\" />\n                </div>\n                <div className={this.state.classDocument}>\n                    <h1>{Costanti.DettDoc}</h1>\n                    <div className='row border mx-1 my-2'>\n                        <div className='col-6 text-center border-right border-bottom d-flex flex-column'><strong className='mr-2'>{Costanti.type}:</strong>{this.props.documento?.type}</div>\n                        <div className='col-6 text-center border-bottom d-flex flex-column'><strong className='mr-2'>{Costanti.Stato}:</strong>{Costanti[this.props.documento?.tasks?.status !== 'not delivered' ? this.props.documento?.tasks?.status : 'notdelivered']}</div>\n                        <div className='col-6 text-center border-right border-bottom d-flex flex-column'><strong className='mr-2'>{Costanti.NDoc}:</strong>{this.props.documento?.number}</div>\n                        <div className='col-6 text-center border-bottom d-flex flex-column'><strong className='mr-2'>{Costanti.Destinazione}:</strong>{this.props.documento?.deliveryDestination}</div>\n                        <div className='col-6 text-center border-right d-flex flex-column'><strong className='mr-2'>{Costanti.DimensioniPedana}:</strong>{this.props.documento?.dimPedana}</div>\n                        <div className='col-6 text-center d-flex flex-column'><strong className='mr-2'>{Costanti.PesoPedana}:</strong>{this.props.documento?.pesoPedana}</div>\n                    </div>\n                </div>\n                <h3 className='mb-4 w-100 text-center'>{this.state.mex}</h3>\n                <div className={this.state.nameAddressDis}>\n                    {/* Tabella anagrafica cliente */}\n                    <div className='col-4'>\n                        <b><span className=\"mr-1\">{Costanti.Nome}:</span></b><span> {this.state.firstName}</span>\n                    </div>\n                    <div className='col-4'>\n                        <b><span className=\"mr-1\">{Costanti.IndirizzoFatt}:</span></b><span> {this.state.indFatt}</span>\n                    </div>\n                    <div className='col-4'>\n                        <b><span className=\"mr-1\">{Costanti.IndCons}:</span></b><span> {this.state.address}</span>\n                    </div>\n                </div>\n                <div className=\"datatable-responsive-demo wrapper\">\n                    {/* Tabella prodotti ordine */}\n                    <h2 className='text-center'>{Costanti.ListProd}</h2>\n                    <div className=\"card\">\n                        {/* Componente primereact per la creazione della tabella */}\n                        <CustomDataTable\n                            ref={(el) => this.dt = el}\n                            value={this.props.results3}\n                            fields={fields}\n                            autoLayout={true}\n                            hideHeader\n                        />\n                    </div>\n                    <div className=\"page-break\" />\n                    <div className={this.state.classDisabled}>\n                        <div className=\"d-flex justify-content-end flex-row align-items-end mt-3\">\n                            <span className=\"mr-3\"><b>{Costanti.Tot}:</b> {this.calcTot()}</span>\n                            <span className=\"mr-3\"><b>{Costanti.Iva}:</b> {this.calcIva()}</span>\n                            <span className=\"mr-4\"><b>{Costanti.TotTax}:</b> {this.calcTotIva()}</span>\n                            {this.calcFee()}\n                        </div>\n                        <div className=\"mt-3 mr-5\">\n                            <h3>{Costanti.Note}</h3>\n                            <InputTextarea className='w-100' rows={5} cols={30} readOnly value={this.state.note !== undefined ? this.state.note : this.props.documento?.note} />\n                        </div>\n                    </div>\n                </div>\n            </div>\n        )\n    }\n}\n\nclass TemplatePartiteApertePrint extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            role: ''\n        }\n    }\n\n    componentDidMount() {\n        var role = localStorage.getItem('role')\n        this.setState({ role: role })\n    }\n\n    render() {\n        return (\n            <div className='w-100'>\n                <div className='logo mb-5 d-flex justify-content-center'>\n                    <img width={230} src={logo} onError={(e) => e.target.src = logo} alt=\"Winet e-procurement logo\" />\n                </div>\n                <div className=\"solid-head d-flex flex-direction-row justify-content-center align-items-center\">\n                    <h1>{this.props.results?.firstName}</h1>\n                </div>\n                <div className=\"row w-100 mt-2\">\n                    <div className=\"col-12 col-md-6 mb-4 mb-md-0\">\n                        <ul className=\"list-group\">\n                            <li className=\"list-group-item\"><i className=\"pi pi-credit-card mr-3\"></i><strong>{Costanti.pIva}</strong>: {this.props.results?.pIva}</li>\n                            <li className=\"list-group-item\"><i className=\"pi pi-mobile mr-3\"></i><strong>{Costanti.Tel}</strong>: {this.props.results?.tel}</li>\n                            <li className=\"list-group-item\"><i className=\"pi pi-envelope mr-3\"></i><strong>{Costanti.Email}</strong>: {this.props.results?.email}</li>\n                        </ul>\n                    </div>\n                    <div className=\"col-12 col-md-6 mb-4 mb-md-0\">\n                        <ul className=\"list-group\">\n                            <li className=\"list-group-item\"><i className=\"pi pi-directions mr-3\"></i><strong>{Costanti.Indirizzo}</strong>: {this.props.results?.address}</li>\n                            <li className=\"list-group-item\"><i className=\"pi pi-map-marker mr-3\"></i><strong>{Costanti.Città}</strong>: {this.props.results?.city}</li>\n                            <li className=\"list-group-item\"><i className=\"pi pi-compass mr-3\"></i><strong>{Costanti.CodPost}</strong>: {this.props.results?.cap}</li>\n                        </ul>\n                    </div>\n                    <div className=\"col-12 spacerCol\">\n                        <hr className=\"mt-5\"></hr>\n                    </div>\n                </div>\n                <div className=\"row w-100\">\n                    <div className=\"col-3\">\n                        <strong>{Costanti.Data}</strong>\n                    </div>\n                    <div className=\"col-3\">\n                        <strong>{Costanti.NumeroPartita}</strong>\n                    </div>\n                    <div className=\"col-3\">\n                        <strong>{Costanti.Residuo}</strong>\n                    </div>\n                    <div className=\"col-3\">\n                        <strong>{Costanti.Tot}</strong>\n                    </div>\n                </div>\n                {this.props.result?.map((el, key) => {\n                    return (\n                        <React.Fragment key={key}>\n                            <div className=\"row border\">\n                                <div className=\"col-3 border-right p-3\">\n                                    {new Date(el.dataDoc).toLocaleDateString()}\n                                </div>\n                                <div className=\"col-3 border-right p-3\">\n                                    {el.numeroPartita}\n                                </div>\n                                <div className=\"col-3 border-right p-3\">\n                                    {this.state.role === distributore ?\n                                        (\n                                            new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(el.residuo)\n                                        ) : (\n                                            new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(el.residuo)\n                                        )\n                                    }\n\n                                </div>\n                                <div className=\"col-3 p-3\">\n                                    {this.state.role === distributore ?\n                                        (\n                                            new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(el.totale)\n                                        ) : (\n                                            new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(el.totale)\n                                        )\n                                    }\n\n                                </div>\n                            </div>\n                        </React.Fragment>\n                    )\n                })\n                }\n                <div className=\"row\">\n                    <div className=\"col-12 pr-0\">\n                        <div className=\"d-flex justify-content-end\">\n                            <span className=\"border border-top-0 p-4\">\n                                <strong className=\"mr-2\">{Costanti.Tot}:</strong>\n                                {this.state.role === distributore ?\n                                    (\n                                        new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(this.props.total)\n                                    ) : (\n                                        new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(this.props.total)\n                                    )\n                                }\n                            </span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        )\n    }\n}\n\nexport class Print extends Component {\n    render() {\n        return (\n            <>\n                <ReactToPrint\n                    content={() => this.componentRef}\n                >\n                    <PrintContextConsumer>\n                        {({ handlePrint }) => (\n                            <button id={this.props.id} className=\"printBtn p-button p-component p-button-text\" onClick={handlePrint}><i className=\"pi pi-print mr-3\"></i><span>{Costanti.Stampa}</span></button>\n                        )}\n                    </PrintContextConsumer>\n                </ReactToPrint>\n                <div className='d-none'>\n                    {window.location.pathname !== agenteDettaglioCliente ?\n                        <TemplateOrderPrint\n                            ref={el => (this.componentRef = el)}\n                            result={this.props.result}\n                            results3={this.props.results3}\n                            firstName={this.props.firstName}\n                            address={this.props.address}\n                            indFatt={this.props.indFatt}\n                            disPersonalData={this.props.disPersonalData}\n                            mex={this.props.mex}\n                            doc={this.props.doc}\n                            documento={this.props.documento}\n                        />\n                        :\n                        <TemplatePartiteApertePrint\n                            ref={el => (this.componentRef = el)}\n                            result={this.props.result}\n                            results={this.props.results}\n                            total={this.props.total}\n                        />\n                    }\n                </div>\n            </>\n        )\n    }\n}\n\nexport default TemplateOrderPrint;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,sBAAsB,EAAEC,YAAY,QAAQ,UAAU;AAC/D,OAAOC,YAAY,IAAIC,oBAAoB,QAAQ,gBAAgB;AACnE,OAAOC,IAAI,MAAM,0BAA0B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,kBAAkB,SAASb,SAAS,CAAC;EACvCc,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,UAAU;MAC1BC,aAAa,EAAE,QAAQ;MACvBC,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACH,IAAI,CAAC,IAAI,CAAC;EAC1C;EACA;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAIN,IAAI,GAAGO,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,CAACC,QAAQ,CAAC;MAAET,IAAI,EAAEA;IAAK,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACZ,KAAK,CAACsB,QAAQ,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACD,QAAQ,CAAC;QACVnB,OAAO,EAAE,IAAI,CAACF,KAAK,CAACuB,QAAQ;QAC5BpB,SAAS,EAAE,IAAI,CAACH,KAAK,CAACG,SAAS;QAC/BC,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI,OAAO;QAC3BC,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK,OAAO;QAC3BC,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACN,KAAK,CAACwB,eAAe,EAAE;MACnC,IAAI,CAACH,QAAQ,CAAC;QACVnB,OAAO,EAAE,IAAI,CAACF,KAAK,CAACuB,QAAQ;QAC5Bd,IAAI,EAAE,IAAI,CAACT,KAAK,CAACyB,MAAM,CAAChB,IAAI;QAC5BF,cAAc,EAAE,QAAQ;QACxBI,GAAG,EAAE,IAAI,CAACX,KAAK,CAACW;MACpB,CAAC,CAAC;IACN,CAAC,MAAM,IAAI,IAAI,CAACX,KAAK,CAAC0B,GAAG,KAAK,IAAI,EAAE;MAAA,IAAAC,qBAAA;MAChC,IAAI,CAACN,QAAQ,CAAC;QACVV,GAAG,EAAE,IAAI,CAACX,KAAK,CAACW,GAAG;QACnBT,OAAO,EAAE,IAAI,CAACF,KAAK,CAACuB,QAAQ;QAC5Bd,IAAI,GAAAkB,qBAAA,GAAE,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBlB,IAAI;QAChC;QACAF,cAAc,EAAE,QAAQ;QACxBC,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MAAA,IAAAqB,kBAAA;MACH,IAAI,CAACR,QAAQ,CAAC;QACVZ,IAAI,GAAAoB,kBAAA,GAAE,IAAI,CAAC7B,KAAK,CAACyB,MAAM,cAAAI,kBAAA,uBAAjBA,kBAAA,CAAmBpB,IAAI;QAC7BP,OAAO,EAAE,IAAI,CAACF,KAAK,CAACuB,QAAQ;QAC5BpB,SAAS,EAAE,IAAI,CAACH,KAAK,CAACG,SAAS;QAC/BC,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI,OAAO;QAC3BC,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK,OAAO;QAC3BM,GAAG,EAAE,IAAI,CAACX,KAAK,CAACW;MACpB,CAAC,CAAC;IACN;EACJ;EACA;EACAE,OAAOA,CAAA,EAAG;IACN,IAAIiB,GAAG,GAAG,CAAC;IACX,IAAI,IAAI,CAAC9B,KAAK,CAACuB,QAAQ,EAAE;MACrB,IAAI,CAACvB,KAAK,CAACuB,QAAQ,CAACQ,OAAO,CAACC,OAAO,IAAI;QACnCF,GAAG,IAAIG,UAAU,CAACD,OAAO,CAACE,KAAK,CAAC;MACpC,CAAC,CAAC;MACF,IAAI,IAAI,CAACjC,KAAK,CAACW,IAAI,KAAKtB,YAAY,EAAE;QAClC,OAAO,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE,KAAK;UAAEC,qBAAqB,EAAE;QAAE,CAAC,CAAC,CAACC,MAAM,CAACV,GAAG,CAAC;MACvH,CAAC,MAAM;QACH,OAAO,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACE,MAAM,CAACV,GAAG,CAAC;MAC7F;IACJ;EACJ;EACA;EACAd,OAAOA,CAAA,EAAG;IACN,IAAIN,GAAG,GAAG,CAAC;IACX,IAAI,IAAI,CAACV,KAAK,CAACuB,QAAQ,EAAE;MACrB,IAAI,CAACvB,KAAK,CAACuB,QAAQ,CAACQ,OAAO,CAACC,OAAO,IAAI;QACnCtB,GAAG,IAAIuB,UAAU,CAACD,OAAO,CAACS,UAAU,CAAC,GAAGR,UAAU,CAACD,OAAO,CAACE,KAAK,CAAC;MACrE,CAAC,CAAC;MACF,IAAI,IAAI,CAACjC,KAAK,CAACW,IAAI,KAAKtB,YAAY,EAAE;QAClC,OAAO,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE,KAAK;UAAEC,qBAAqB,EAAE;QAAE,CAAC,CAAC,CAACC,MAAM,CAAC9B,GAAG,CAAC;MACvH,CAAC,MAAM;QACH,OAAO,IAAIyB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACE,MAAM,CAAC9B,GAAG,CAAC;MAC7F;IACJ;EACJ;EACA;EACAK,UAAUA,CAAA,EAAG;IACT,IAAI2B,MAAM,GAAG,CAAC;IACd,IAAI,IAAI,CAAC1C,KAAK,CAACuB,QAAQ,EAAE;MACrB,IAAI,CAACvB,KAAK,CAACuB,QAAQ,CAACQ,OAAO,CAACC,OAAO,IAAI;QACnCU,MAAM,IAAIT,UAAU,CAACD,OAAO,CAACS,UAAU,CAAC;MAC5C,CAAC,CAAC;MACF,IAAI,IAAI,CAACxC,KAAK,CAACW,IAAI,KAAKtB,YAAY,EAAE;QAClC,OAAO,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE,KAAK;UAAEC,qBAAqB,EAAE;QAAE,CAAC,CAAC,CAACC,MAAM,CAACE,MAAM,CAAC;MAC1H,CAAC,MAAM;QACH,OAAO,IAAIP,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACE,MAAM,CAACE,MAAM,CAAC;MAChG;IACJ;EACJ;EACA;EACAzB,OAAOA,CAAA,EAAG;IACN,IAAI0B,GAAG,GAAG,CAAC;IACX,IAAI/B,IAAI,GAAGO,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,IAAI,CAACpB,KAAK,CAACuB,QAAQ,IAAIX,IAAI,KAAK,KAAK,EAAE;MACvC,IAAI,CAACZ,KAAK,CAACuB,QAAQ,CAACQ,OAAO,CAACC,OAAO,IAAI;QACnC,IAAIA,OAAO,CAACW,GAAG,EAAE;UACbA,GAAG,IAAIV,UAAU,CAACD,OAAO,CAACW,GAAG,CAAC;QAClC;MACJ,CAAC,CAAC;MACF,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;QAC1B,IAAI,IAAI,CAAC1C,KAAK,CAACW,IAAI,KAAKtB,YAAY,EAAE;UAClC,oBACIK,OAAA;YAAMkD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBnD,OAAA;cAAAmD,QAAA,EAAG;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAAC,IAAIf,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACG,GAAG,CAAC;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvH,CAAC;QAEf,CAAC,MAAM;UACH,oBACIvD,OAAA;YAAMkD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBnD,OAAA;cAAAmD,QAAA,EAAG;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAAC,EAAC,IAAIf,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACG,GAAG,CAAC;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC;QAEf;MACJ;IACJ;EACJ;EACAC,MAAMA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACL,IAAIlB,GAAG,GAAG,CAAC;IACX,IAAImB,MAAM,GAAG,EAAE;IACf,IAAIlD,IAAI,GAAGO,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,IAAI,CAACnB,KAAK,CAACC,OAAO,IAAIU,IAAI,KAAK,KAAK,EAAE;MACtC,IAAI,CAACX,KAAK,CAACC,OAAO,CAAC6B,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIA,OAAO,CAACW,GAAG,IAAIX,OAAO,CAACW,GAAG,KAAK,GAAG,EAAE;UACpCA,GAAG,IAAIX,OAAO,CAACW,GAAG;QACtB;MACJ,CAAC,CAAC;MACF,IAAIA,GAAG,KAAK,CAAC,EAAE;QACXA,GAAG,GAAG;UAAEoB,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,IAAI,EAAE,KAAK;UAAEC,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,IAAI;UAAEtB,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK;QAAc,CAAC;MAC7H,CAAC,MAAM;QACHqC,GAAG,GAAG,CAAC,CAAC;MACZ;IACJ;IACA,IAAI,IAAI,CAAC3C,KAAK,CAAC0B,GAAG,KAAK,IAAI,EAAE;MACzBoC,MAAM,GAAG,CACL;QAAEC,KAAK,EAAE,cAAc;QAAEC,MAAM,EAAE5E,QAAQ,CAACgF,MAAM;QAAEH,IAAI,EAAE,WAAW;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACvG;QAAEJ,KAAK,EAAE,aAAa;QAAEC,MAAM,EAAE5E,QAAQ,CAACiF,QAAQ;QAAEJ,IAAI,EAAE,aAAa;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC1G;QAAEJ,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE5E,QAAQ,CAACkF,OAAO;QAAEL,IAAI,EAAE,SAAS;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACjG;QAAEJ,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE5E,QAAQ,CAACmF,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACvF;QAAEJ,KAAK,EAAE,iBAAiB;QAAEC,MAAM,EAAE5E,QAAQ,CAACoF,eAAe;QAAEP,IAAI,EAAE,iBAAiB;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACzH;QAAEJ,KAAK,EAAE,iBAAiB;QAAEC,MAAM,EAAE5E,QAAQ,CAACqF,eAAe;QAAER,IAAI,EAAE,iBAAiB;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACzH;QAAEJ,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE5E,QAAQ,CAACsF,KAAK;QAAET,IAAI,EAAE,OAAO;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC3F;QAAEJ,KAAK,EAAE,UAAU;QAAEC,MAAM,EAAE5E,QAAQ,CAACuF,QAAQ;QAAEV,IAAI,EAAE,aAAa;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,CAC1G;IACL,CAAC,MAAM;MACHL,MAAM,GAAG,CACL;QAAEC,KAAK,EAAE,sBAAsB;QAAEC,MAAM,EAAE5E,QAAQ,CAACgF,MAAM;QAAEH,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC5G;QAAEJ,KAAK,EAAE,qBAAqB;QAAEC,MAAM,EAAE5E,QAAQ,CAACiF,QAAQ;QAAEJ,IAAI,EAAE,UAAU;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC/G;QAAEJ,KAAK,EAAE,aAAa;QAAEC,MAAM,EAAE5E,QAAQ,CAACmF,OAAO;QAAEN,IAAI,EAAE,aAAa;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACzG;QAAEJ,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE,SAAS;QAAEC,IAAI,EAAE,aAAa;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC9F;QAAEJ,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE5E,QAAQ,CAACwF,KAAK;QAAEX,IAAI,EAAE,OAAO;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EAC3F;QAAEJ,KAAK,EAAE,UAAU;QAAEC,MAAM,EAAE5E,QAAQ,CAACyF,QAAQ;QAAEZ,IAAI,EAAE,UAAU;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAC,EACpG;QAAEJ,KAAK,EAAE,WAAW;QAAEC,MAAM,EAAE5E,QAAQ,CAAC0F,MAAM;QAAEb,IAAI,EAAE,WAAW;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAI;QAAEtB,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK;MAAc,CAAC,EACzI;QAAEyD,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE5E,QAAQ,CAAC2F,GAAG;QAAEd,IAAI,EAAE,OAAO;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAI;QAAEtB,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK;MAAc,CAAC,EAC9H;QAAEyD,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE5E,QAAQ,CAAC4F,GAAG;QAAEf,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAI;QAAEtB,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK;MAAc,CAAC,EAC1H;QAAEyD,KAAK,EAAE,YAAY;QAAEC,MAAM,EAAE5E,QAAQ,CAAC6F,MAAM;QAAEhB,IAAI,EAAE,YAAY;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAI;QAAEtB,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK;MAAc,CAAC,EAC3IqC,GAAG,CACN;IACL;IAEA,oBACIhD,OAAA;MAAAmD,QAAA,gBACInD,OAAA;QAAKkD,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACpDnD,OAAA;UAAKuF,KAAK,EAAE,GAAI;UAACC,GAAG,EAAE1F,IAAK;UAAC2F,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG1F,IAAK;UAAC8F,GAAG,EAAC;QAA0B;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACO,aAAc;QAAAsC,QAAA,gBACrCnD,OAAA;UAAAmD,QAAA,EAAK1D,QAAQ,CAACoG;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3BvD,OAAA;UAAKkD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCnD,OAAA;YAAKkD,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACqG,IAAI,EAAC,GAAC;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAE,sBAAA,GAAC,IAAI,CAACpD,KAAK,CAAC4B,SAAS,cAAAwB,sBAAA,uBAApBA,sBAAA,CAAsBqC,IAAI;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrKvD,OAAA;YAAKkD,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACsG,KAAK,EAAC,GAAC;YAAA;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC9D,QAAQ,CAAC,EAAAiE,sBAAA,OAAI,CAACrD,KAAK,CAAC4B,SAAS,cAAAyB,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBsC,KAAK,cAAArC,sBAAA,uBAA3BA,sBAAA,CAA6BsC,MAAM,MAAK,eAAe,IAAArC,sBAAA,GAAG,IAAI,CAACvD,KAAK,CAAC4B,SAAS,cAAA2B,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBoC,KAAK,cAAAnC,sBAAA,uBAA3BA,sBAAA,CAA6BoC,MAAM,GAAG,cAAc,CAAC;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvPvD,OAAA;YAAKkD,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACyG,IAAI,EAAC,GAAC;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAO,sBAAA,GAAC,IAAI,CAACzD,KAAK,CAAC4B,SAAS,cAAA6B,sBAAA,uBAApBA,sBAAA,CAAsBqC,MAAM;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvKvD,OAAA;YAAKkD,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAAC2G,YAAY,EAAC,GAAC;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAQ,sBAAA,GAAC,IAAI,CAAC1D,KAAK,CAAC4B,SAAS,cAAA8B,sBAAA,uBAApBA,sBAAA,CAAsBsC,mBAAmB;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/KvD,OAAA;YAAKkD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAAC6G,gBAAgB,EAAC,GAAC;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAS,sBAAA,GAAC,IAAI,CAAC3D,KAAK,CAAC4B,SAAS,cAAA+B,sBAAA,uBAApBA,sBAAA,CAAsBuC,SAAS;UAAA;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxKvD,OAAA;YAAKkD,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAACnD,OAAA;cAAQkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAAC+G,UAAU,EAAC,GAAC;YAAA;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAU,sBAAA,GAAC,IAAI,CAAC5D,KAAK,CAAC4B,SAAS,cAAAgC,sBAAA,uBAApBA,sBAAA,CAAsBwC,UAAU;UAAA;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvD,OAAA;QAAIkD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAE,IAAI,CAAC7C,KAAK,CAACU;MAAG;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DvD,OAAA;QAAKkD,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACM,cAAe;QAAAuC,QAAA,gBAEtCnD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAClBnD,OAAA;YAAAmD,QAAA,eAAGnD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACiH,IAAI,EAAC,GAAC;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAAAvD,OAAA;YAAAmD,QAAA,GAAM,GAAC,EAAC,IAAI,CAAC7C,KAAK,CAACE,SAAS;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAClBnD,OAAA;YAAAmD,QAAA,eAAGnD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACkH,aAAa,EAAC,GAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAAAvD,OAAA;YAAAmD,QAAA,GAAM,GAAC,EAAC,IAAI,CAAC7C,KAAK,CAACI,OAAO;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAClBnD,OAAA;YAAAmD,QAAA,eAAGnD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAE1D,QAAQ,CAACmH,OAAO,EAAC,GAAC;YAAA;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAAAvD,OAAA;YAAAmD,QAAA,GAAM,GAAC,EAAC,IAAI,CAAC7C,KAAK,CAACG,OAAO;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAE9CnD,OAAA;UAAIkD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE1D,QAAQ,CAACoH;QAAQ;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDvD,OAAA;UAAKkD,SAAS,EAAC,MAAM;UAAAC,QAAA,eAEjBnD,OAAA,CAACR,eAAe;YACZsH,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACC,EAAE,GAAGD,EAAG;YAC1BE,KAAK,EAAE,IAAI,CAAC5G,KAAK,CAACuB,QAAS;YAC3BuC,MAAM,EAAEA,MAAO;YACf+C,UAAU,EAAE,IAAK;YACjBC,UAAU;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BvD,OAAA;UAAKkD,SAAS,EAAE,IAAI,CAAC5C,KAAK,CAACK,aAAc;UAAAwC,QAAA,gBACrCnD,OAAA;YAAKkD,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACrEnD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAACnD,OAAA;gBAAAmD,QAAA,GAAI1D,QAAQ,CAAC2F,GAAG,EAAC,GAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,KAAC,EAAC,IAAI,CAACrC,OAAO,CAAC,CAAC;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrEvD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAACnD,OAAA;gBAAAmD,QAAA,GAAI1D,QAAQ,CAAC4F,GAAG,EAAC,GAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,KAAC,EAAC,IAAI,CAAClC,OAAO,CAAC,CAAC;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrEvD,OAAA;cAAMkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAACnD,OAAA;gBAAAmD,QAAA,GAAI1D,QAAQ,CAAC6F,MAAM,EAAC,GAAC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,KAAC,EAAC,IAAI,CAACnC,UAAU,CAAC,CAAC;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1E,IAAI,CAACjC,OAAO,CAAC,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACNvD,OAAA;YAAKkD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBnD,OAAA;cAAAmD,QAAA,EAAK1D,QAAQ,CAAC2H;YAAI;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBvD,OAAA,CAACT,aAAa;cAAC2D,SAAS,EAAC,OAAO;cAACmE,IAAI,EAAE,CAAE;cAACC,IAAI,EAAE,EAAG;cAACC,QAAQ;cAACN,KAAK,EAAE,IAAI,CAAC3G,KAAK,CAACQ,IAAI,KAAK0G,SAAS,GAAG,IAAI,CAAClH,KAAK,CAACQ,IAAI,IAAAoD,sBAAA,GAAG,IAAI,CAAC7D,KAAK,CAAC4B,SAAS,cAAAiC,sBAAA,uBAApBA,sBAAA,CAAsBpD;YAAK;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,MAAMkE,0BAA0B,SAASnI,SAAS,CAAC;EAC/Cc,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTW,IAAI,EAAE;IACV,CAAC;EACL;EAEAM,iBAAiBA,CAAA,EAAG;IAChB,IAAIN,IAAI,GAAGO,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,CAACC,QAAQ,CAAC;MAAET,IAAI,EAAEA;IAAK,CAAC,CAAC;EACjC;EAEAuC,MAAMA,CAAA,EAAG;IAAA,IAAAkE,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,mBAAA;IACL,oBACIjI,OAAA;MAAKkD,SAAS,EAAC,OAAO;MAAAC,QAAA,gBAClBnD,OAAA;QAAKkD,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACpDnD,OAAA;UAAKuF,KAAK,EAAE,GAAI;UAACC,GAAG,EAAE1F,IAAK;UAAC2F,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG1F,IAAK;UAAC8F,GAAG,EAAC;QAA0B;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC3FnD,OAAA;UAAAmD,QAAA,GAAAuE,mBAAA,GAAK,IAAI,CAACrH,KAAK,CAACE,OAAO,cAAAmH,mBAAA,uBAAlBA,mBAAA,CAAoBlH;QAAS;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BnD,OAAA;UAAKkD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eACzCnD,OAAA;YAAIkD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACtBnD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAACyI;cAAI;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAoE,oBAAA,GAAC,IAAI,CAACtH,KAAK,CAACE,OAAO,cAAAoH,oBAAA,uBAAlBA,oBAAA,CAAoBO,IAAI;YAAA;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3IvD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAAC0I;cAAG;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAqE,oBAAA,GAAC,IAAI,CAACvH,KAAK,CAACE,OAAO,cAAAqH,oBAAA,uBAAlBA,oBAAA,CAAoBQ,GAAG;YAAA;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpIvD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAAC4I;cAAK;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAsE,oBAAA,GAAC,IAAI,CAACxH,KAAK,CAACE,OAAO,cAAAsH,oBAAA,uBAAlBA,oBAAA,CAAoBS,KAAK;YAAA;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1I;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eACzCnD,OAAA;YAAIkD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACtBnD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAAC8I;cAAS;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAuE,oBAAA,GAAC,IAAI,CAACzH,KAAK,CAACE,OAAO,cAAAuH,oBAAA,uBAAlBA,oBAAA,CAAoBrH,OAAO;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClJvD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAAC+I;cAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAwE,oBAAA,GAAC,IAAI,CAAC1H,KAAK,CAACE,OAAO,cAAAwH,oBAAA,uBAAlBA,oBAAA,CAAoBU,IAAI;YAAA;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3IvD,OAAA;cAAIkD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAS1D,QAAQ,CAACiJ;cAAO;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,GAAAyE,oBAAA,GAAC,IAAI,CAAC3H,KAAK,CAACE,OAAO,cAAAyH,oBAAA,uBAAlBA,oBAAA,CAAoBW,GAAG;YAAA;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BnD,OAAA;YAAIkD,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvD,OAAA;QAAKkD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBnD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBnD,OAAA;YAAAmD,QAAA,EAAS1D,QAAQ,CAACmJ;UAAI;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBnD,OAAA;YAAAmD,QAAA,EAAS1D,QAAQ,CAACoJ;UAAa;YAAAzF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBnD,OAAA;YAAAmD,QAAA,EAAS1D,QAAQ,CAACqJ;UAAO;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNvD,OAAA;UAAKkD,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBnD,OAAA;YAAAmD,QAAA,EAAS1D,QAAQ,CAAC2F;UAAG;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,GAAA0E,mBAAA,GACL,IAAI,CAAC5H,KAAK,CAACyB,MAAM,cAAAmG,mBAAA,uBAAjBA,mBAAA,CAAmBc,GAAG,CAAC,CAAChC,EAAE,EAAEiC,GAAG,KAAK;QACjC,oBACIhJ,OAAA,CAACX,KAAK,CAACY,QAAQ;UAAAkD,QAAA,eACXnD,OAAA;YAAKkD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBnD,OAAA;cAAKkD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC,IAAI8F,IAAI,CAAClC,EAAE,CAACmC,OAAO,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC4D,EAAE,CAACqC;YAAa;cAAAhG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClC,IAAI,CAAC7C,KAAK,CAACW,IAAI,KAAKtB,YAAY,GAEzB,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACkE,EAAE,CAACsC,OAAO,CAAC,GAEnH,IAAI7G,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACkE,EAAE,CAACsC,OAAO;YAC3F;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB,IAAI,CAAC7C,KAAK,CAACW,IAAI,KAAKtB,YAAY,GAEzB,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACkE,EAAE,CAACuC,MAAM,CAAC,GAElH,IAAI9G,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACkE,EAAE,CAACuC,MAAM;YAC1F;cAAAlG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GA5BWyF,GAAG;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BR,CAAC;MAEzB,CAAC,CAAC,eAEFvD,OAAA;QAAKkD,SAAS,EAAC,KAAK;QAAAC,QAAA,eAChBnD,OAAA;UAAKkD,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBnD,OAAA;YAAKkD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACvCnD,OAAA;cAAMkD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCnD,OAAA;gBAAQkD,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAE1D,QAAQ,CAAC2F,GAAG,EAAC,GAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAChD,IAAI,CAACjD,KAAK,CAACW,IAAI,KAAKtB,YAAY,GAEzB,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAACxC,KAAK,CAACkC,KAAK,CAAC,GAEzH,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAAC,IAAI,CAACxC,KAAK,CAACkC,KAAK,CACjG;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,OAAO,MAAMgG,KAAK,SAASjK,SAAS,CAAC;EACjCkE,MAAMA,CAAA,EAAG;IACL,oBACIxD,OAAA,CAAAE,SAAA;MAAAiD,QAAA,gBACInD,OAAA,CAACJ,YAAY;QACT4J,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAa;QAAAtG,QAAA,eAEjCnD,OAAA,CAACH,oBAAoB;UAAAsD,QAAA,EAChBuG,IAAA;YAAA,IAAC;cAAEC;YAAY,CAAC,GAAAD,IAAA;YAAA,oBACb1J,OAAA;cAAQ4J,EAAE,EAAE,IAAI,CAACvJ,KAAK,CAACuJ,EAAG;cAAC1G,SAAS,EAAC,6CAA6C;cAAC2G,OAAO,EAAEF,WAAY;cAAAxG,QAAA,gBAACnD,OAAA;gBAAGkD,SAAS,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAAAvD,OAAA;gBAAAmD,QAAA,EAAO1D,QAAQ,CAACqK;cAAM;gBAAA1G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;QACvL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACfvD,OAAA;QAAKkD,SAAS,EAAC,QAAQ;QAAAC,QAAA,EAClB4G,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKvK,sBAAsB,gBAChDM,OAAA,CAACG,kBAAkB;UACf2G,GAAG,EAAEC,EAAE,IAAK,IAAI,CAAC0C,YAAY,GAAG1C,EAAI;UACpCjF,MAAM,EAAE,IAAI,CAACzB,KAAK,CAACyB,MAAO;UAC1BF,QAAQ,EAAE,IAAI,CAACvB,KAAK,CAACuB,QAAS;UAC9BpB,SAAS,EAAE,IAAI,CAACH,KAAK,CAACG,SAAU;UAChCC,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI,OAAQ;UAC5BC,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK,OAAQ;UAC5BmB,eAAe,EAAE,IAAI,CAACxB,KAAK,CAACwB,eAAgB;UAC5Cb,GAAG,EAAE,IAAI,CAACX,KAAK,CAACW,GAAI;UACpBe,GAAG,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,GAAI;UACpBE,SAAS,EAAE,IAAI,CAAC5B,KAAK,CAAC4B;QAAU;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,gBAEFvD,OAAA,CAACyH,0BAA0B;UACvBX,GAAG,EAAEC,EAAE,IAAK,IAAI,CAAC0C,YAAY,GAAG1C,EAAI;UACpCjF,MAAM,EAAE,IAAI,CAACzB,KAAK,CAACyB,MAAO;UAC1BvB,OAAO,EAAE,IAAI,CAACF,KAAK,CAACE,OAAQ;UAC5BgC,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACkC;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC;IAAA,eACR,CAAC;EAEX;AACJ;AAEA,eAAepD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}