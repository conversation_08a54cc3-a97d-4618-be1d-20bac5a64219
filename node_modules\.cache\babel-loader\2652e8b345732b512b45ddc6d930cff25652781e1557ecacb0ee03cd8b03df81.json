{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useState, useRef } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL); // ================================= Hook =================================\n\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = useState(),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = useRef(); // ========================= Events =========================\n  // >>> Touch events\n\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n    e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null); // Swipe if needed\n\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY); // Skip swipe if low distance\n\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  } // >>> Wheel event\n\n  var lastWheelDirectionRef = useRef();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY; // Convert both to x & y since wheel only happened on PC\n\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  } // ========================= Effect =========================\n\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: false\n    }); // No need to clean up since element removed\n\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: false\n    });\n    ref.current.addEventListener('wheel', onProxyWheel);\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useState", "useRef", "MIN_SWIPE_DISTANCE", "STOP_SWIPE_DISTANCE", "REFRESH_INTERVAL", "SPEED_OFF_MULTIPLE", "Math", "pow", "useTouchMove", "ref", "onOffset", "_useState", "_useState2", "touchPosition", "setTouchPosition", "_useState3", "_useState4", "lastTimestamp", "setLastTimestamp", "_useState5", "_useState6", "lastTimeDiff", "setLastTimeDiff", "_useState7", "_useState8", "lastOffset", "setLastOffset", "motionRef", "onTouchStart", "e", "_e$touches$", "touches", "screenX", "screenY", "x", "y", "window", "clearInterval", "current", "onTouchMove", "preventDefault", "_e$touches$2", "offsetX", "offsetY", "now", "Date", "onTouchEnd", "distanceX", "distanceY", "absX", "abs", "absY", "max", "currentX", "currentY", "setInterval", "lastWheelDirectionRef", "onWheel", "deltaX", "deltaY", "mixed", "touchEventsRef", "useEffect", "onProxyTouchStart", "onProxyTouchMove", "onProxyTouchEnd", "onProxyWheel", "document", "addEventListener", "passive", "removeEventListener"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/hooks/useTouchMove.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useState, useRef } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL); // ================================= Hook =================================\n\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n      _useState2 = _slicedToArray(_useState, 2),\n      touchPosition = _useState2[0],\n      setTouchPosition = _useState2[1];\n\n  var _useState3 = useState(0),\n      _useState4 = _slicedToArray(_useState3, 2),\n      lastTimestamp = _useState4[0],\n      setLastTimestamp = _useState4[1];\n\n  var _useState5 = useState(0),\n      _useState6 = _slicedToArray(_useState5, 2),\n      lastTimeDiff = _useState6[0],\n      setLastTimeDiff = _useState6[1];\n\n  var _useState7 = useState(),\n      _useState8 = _slicedToArray(_useState7, 2),\n      lastOffset = _useState8[0],\n      setLastOffset = _useState8[1];\n\n  var motionRef = useRef(); // ========================= Events =========================\n  // >>> Touch events\n\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n        screenX = _e$touches$.screenX,\n        screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n    e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n        screenX = _e$touches$2.screenX,\n        screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null); // Swipe if needed\n\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY); // Skip swipe if low distance\n\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  } // >>> Wheel event\n\n\n  var lastWheelDirectionRef = useRef();\n\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n        deltaY = e.deltaY; // Convert both to x & y since wheel only happened on PC\n\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  } // ========================= Effect =========================\n\n\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: false\n    }); // No need to clean up since element removed\n\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: false\n    });\n    ref.current.addEventListener('wheel', onProxyWheel);\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,IAAIC,kBAAkB,GAAG,GAAG;AAC5B,IAAIC,mBAAmB,GAAG,IAAI;AAC9B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,KAAK,EAAEH,gBAAgB,CAAC,CAAC,CAAC;;AAE5D,eAAe,SAASI,YAAYA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAClD,IAAIC,SAAS,GAAGX,QAAQ,CAAC,CAAC;IACtBY,UAAU,GAAGd,cAAc,CAACa,SAAS,EAAE,CAAC,CAAC;IACzCE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIG,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC;IACxBgB,UAAU,GAAGlB,cAAc,CAACiB,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIG,UAAU,GAAGnB,QAAQ,CAAC,CAAC,CAAC;IACxBoB,UAAU,GAAGtB,cAAc,CAACqB,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAGvB,QAAQ,CAAC,CAAC;IACvBwB,UAAU,GAAG1B,cAAc,CAACyB,UAAU,EAAE,CAAC,CAAC;IAC1CE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEjC,IAAIG,SAAS,GAAG1B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1B;;EAEA,SAAS2B,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIC,WAAW,GAAGD,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;MAC1BC,OAAO,GAAGF,WAAW,CAACE,OAAO;MAC7BC,OAAO,GAAGH,WAAW,CAACG,OAAO;IACjCnB,gBAAgB,CAAC;MACfoB,CAAC,EAAEF,OAAO;MACVG,CAAC,EAAEF;IACL,CAAC,CAAC;IACFG,MAAM,CAACC,aAAa,CAACV,SAAS,CAACW,OAAO,CAAC;EACzC;EAEA,SAASC,WAAWA,CAACV,CAAC,EAAE;IACtB,IAAI,CAAChB,aAAa,EAAE;IACpBgB,CAAC,CAACW,cAAc,CAAC,CAAC;IAClB,IAAIC,YAAY,GAAGZ,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;MAC3BC,OAAO,GAAGS,YAAY,CAACT,OAAO;MAC9BC,OAAO,GAAGQ,YAAY,CAACR,OAAO;IAClCnB,gBAAgB,CAAC;MACfoB,CAAC,EAAEF,OAAO;MACVG,CAAC,EAAEF;IACL,CAAC,CAAC;IACF,IAAIS,OAAO,GAAGV,OAAO,GAAGnB,aAAa,CAACqB,CAAC;IACvC,IAAIS,OAAO,GAAGV,OAAO,GAAGpB,aAAa,CAACsB,CAAC;IACvCzB,QAAQ,CAACgC,OAAO,EAAEC,OAAO,CAAC;IAC1B,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACpB1B,gBAAgB,CAAC0B,GAAG,CAAC;IACrBtB,eAAe,CAACsB,GAAG,GAAG3B,aAAa,CAAC;IACpCS,aAAa,CAAC;MACZQ,CAAC,EAAEQ,OAAO;MACVP,CAAC,EAAEQ;IACL,CAAC,CAAC;EACJ;EAEA,SAASG,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACjC,aAAa,EAAE;IACpBC,gBAAgB,CAAC,IAAI,CAAC;IACtBY,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;;IAErB,IAAID,UAAU,EAAE;MACd,IAAIsB,SAAS,GAAGtB,UAAU,CAACS,CAAC,GAAGb,YAAY;MAC3C,IAAI2B,SAAS,GAAGvB,UAAU,CAACU,CAAC,GAAGd,YAAY;MAC3C,IAAI4B,IAAI,GAAG3C,IAAI,CAAC4C,GAAG,CAACH,SAAS,CAAC;MAC9B,IAAII,IAAI,GAAG7C,IAAI,CAAC4C,GAAG,CAACF,SAAS,CAAC,CAAC,CAAC;;MAEhC,IAAI1C,IAAI,CAAC8C,GAAG,CAACH,IAAI,EAAEE,IAAI,CAAC,GAAGjD,kBAAkB,EAAE;MAC/C,IAAImD,QAAQ,GAAGN,SAAS;MACxB,IAAIO,QAAQ,GAAGN,SAAS;MACxBrB,SAAS,CAACW,OAAO,GAAGF,MAAM,CAACmB,WAAW,CAAC,YAAY;QACjD,IAAIjD,IAAI,CAAC4C,GAAG,CAACG,QAAQ,CAAC,GAAGlD,mBAAmB,IAAIG,IAAI,CAAC4C,GAAG,CAACI,QAAQ,CAAC,GAAGnD,mBAAmB,EAAE;UACxFiC,MAAM,CAACC,aAAa,CAACV,SAAS,CAACW,OAAO,CAAC;UACvC;QACF;QAEAe,QAAQ,IAAIhD,kBAAkB;QAC9BiD,QAAQ,IAAIjD,kBAAkB;QAC9BK,QAAQ,CAAC2C,QAAQ,GAAGjD,gBAAgB,EAAEkD,QAAQ,GAAGlD,gBAAgB,CAAC;MACpE,CAAC,EAAEA,gBAAgB,CAAC;IACtB;EACF,CAAC,CAAC;;EAGF,IAAIoD,qBAAqB,GAAGvD,MAAM,CAAC,CAAC;EAEpC,SAASwD,OAAOA,CAAC5B,CAAC,EAAE;IAClB,IAAI6B,MAAM,GAAG7B,CAAC,CAAC6B,MAAM;MACjBC,MAAM,GAAG9B,CAAC,CAAC8B,MAAM,CAAC,CAAC;;IAEvB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIX,IAAI,GAAG3C,IAAI,CAAC4C,GAAG,CAACQ,MAAM,CAAC;IAC3B,IAAIP,IAAI,GAAG7C,IAAI,CAAC4C,GAAG,CAACS,MAAM,CAAC;IAE3B,IAAIV,IAAI,KAAKE,IAAI,EAAE;MACjBS,KAAK,GAAGJ,qBAAqB,CAAClB,OAAO,KAAK,GAAG,GAAGoB,MAAM,GAAGC,MAAM;IACjE,CAAC,MAAM,IAAIV,IAAI,GAAGE,IAAI,EAAE;MACtBS,KAAK,GAAGF,MAAM;MACdF,qBAAqB,CAAClB,OAAO,GAAG,GAAG;IACrC,CAAC,MAAM;MACLsB,KAAK,GAAGD,MAAM;MACdH,qBAAqB,CAAClB,OAAO,GAAG,GAAG;IACrC;IAEA,IAAI5B,QAAQ,CAAC,CAACkD,KAAK,EAAE,CAACA,KAAK,CAAC,EAAE;MAC5B/B,CAAC,CAACW,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,CAAC;;EAGF,IAAIqB,cAAc,GAAG5D,MAAM,CAAC,IAAI,CAAC;EACjC4D,cAAc,CAACvB,OAAO,GAAG;IACvBV,YAAY,EAAEA,YAAY;IAC1BW,WAAW,EAAEA,WAAW;IACxBO,UAAU,EAAEA,UAAU;IACtBW,OAAO,EAAEA;EACX,CAAC;EACD1D,KAAK,CAAC+D,SAAS,CAAC,YAAY;IAC1B,SAASC,iBAAiBA,CAAClC,CAAC,EAAE;MAC5BgC,cAAc,CAACvB,OAAO,CAACV,YAAY,CAACC,CAAC,CAAC;IACxC;IAEA,SAASmC,gBAAgBA,CAACnC,CAAC,EAAE;MAC3BgC,cAAc,CAACvB,OAAO,CAACC,WAAW,CAACV,CAAC,CAAC;IACvC;IAEA,SAASoC,eAAeA,CAACpC,CAAC,EAAE;MAC1BgC,cAAc,CAACvB,OAAO,CAACQ,UAAU,CAACjB,CAAC,CAAC;IACtC;IAEA,SAASqC,YAAYA,CAACrC,CAAC,EAAE;MACvBgC,cAAc,CAACvB,OAAO,CAACmB,OAAO,CAAC5B,CAAC,CAAC;IACnC;IAEAsC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,gBAAgB,EAAE;MACvDK,OAAO,EAAE;IACX,CAAC,CAAC;IACFF,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEH,eAAe,EAAE;MACrDI,OAAO,EAAE;IACX,CAAC,CAAC,CAAC,CAAC;;IAEJ5D,GAAG,CAAC6B,OAAO,CAAC8B,gBAAgB,CAAC,YAAY,EAAEL,iBAAiB,EAAE;MAC5DM,OAAO,EAAE;IACX,CAAC,CAAC;IACF5D,GAAG,CAAC6B,OAAO,CAAC8B,gBAAgB,CAAC,OAAO,EAAEF,YAAY,CAAC;IACnD,OAAO,YAAY;MACjBC,QAAQ,CAACG,mBAAmB,CAAC,WAAW,EAAEN,gBAAgB,CAAC;MAC3DG,QAAQ,CAACG,mBAAmB,CAAC,UAAU,EAAEL,eAAe,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}