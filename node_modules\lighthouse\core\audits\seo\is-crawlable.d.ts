export default IsCrawlable;
declare class IsCrawlable extends Audit {
    /**
     * @param {LH.Artifacts.MetaElement} metaElement
     */
    static handleMetaElement(metaElement: LH.Artifacts.MetaElement): {
        source: {
            snippet: string;
            type: "node";
            lhId?: string | undefined;
            path?: string | undefined;
            selector?: string | undefined;
            boundingRect?: import("../../../types/lhr/audit-details.js").default.Rect | undefined;
            nodeLabel?: string | undefined;
        };
    } | undefined;
    /**
     * @param {string|undefined} userAgent
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {LH.Artifacts.MetaElement[]} metaElements
     * @param {import('robots-parser').Robot|undefined} parsedRobotsTxt
     * @param {URL} robotsTxtUrl
     */
    static determineIfCrawlableForUserAgent(userAgent: string | undefined, mainResource: LH.Artifacts.NetworkRequest, metaElements: LH.Artifacts.MetaElement[], parsedRobotsTxt: import('robots-parser').Robot | undefined, robotsTxtUrl: URL): import("../../../types/lhr/audit-details.js").default.TableItem[];
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=is-crawlable.d.ts.map