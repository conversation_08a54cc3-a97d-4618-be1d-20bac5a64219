{"ast": null, "code": "import compute from 'compute-scroll-into-view';\nfunction isOptionsObject(options) {\n  return options === Object(options) && Object.keys(options).length !== 0;\n}\nfunction defaultBehavior(actions, behavior) {\n  if (behavior === void 0) {\n    behavior = 'auto';\n  }\n  var canSmoothScroll = 'scrollBehavior' in document.body.style;\n  actions.forEach(function (_ref) {\n    var el = _ref.el,\n      top = _ref.top,\n      left = _ref.left;\n    if (el.scroll && canSmoothScroll) {\n      el.scroll({\n        top: top,\n        left: left,\n        behavior: behavior\n      });\n    } else {\n      el.scrollTop = top;\n      el.scrollLeft = left;\n    }\n  });\n}\nfunction getOptions(options) {\n  if (options === false) {\n    return {\n      block: 'end',\n      inline: 'nearest'\n    };\n  }\n  if (isOptionsObject(options)) {\n    return options;\n  }\n  return {\n    block: 'start',\n    inline: 'nearest'\n  };\n}\nfunction scrollIntoView(target, options) {\n  var isTargetAttached = target.isConnected || target.ownerDocument.documentElement.contains(target);\n  if (isOptionsObject(options) && typeof options.behavior === 'function') {\n    return options.behavior(isTargetAttached ? compute(target, options) : []);\n  }\n  if (!isTargetAttached) {\n    return;\n  }\n  var computeOptions = getOptions(options);\n  return defaultBehavior(compute(target, computeOptions), computeOptions.behavior);\n}\nexport default scrollIntoView;", "map": {"version": 3, "names": ["compute", "isOptionsObject", "options", "Object", "keys", "length", "defaultBehavior", "actions", "behavior", "canSmoothScroll", "document", "body", "style", "for<PERSON>ach", "_ref", "el", "top", "left", "scroll", "scrollTop", "scrollLeft", "getOptions", "block", "inline", "scrollIntoView", "target", "isTargetAttached", "isConnected", "ownerDocument", "documentElement", "contains", "computeOptions"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/scroll-into-view-if-needed/es/index.js"], "sourcesContent": ["import compute from 'compute-scroll-into-view';\n\nfunction isOptionsObject(options) {\n  return options === Object(options) && Object.keys(options).length !== 0;\n}\n\nfunction defaultBehavior(actions, behavior) {\n  if (behavior === void 0) {\n    behavior = 'auto';\n  }\n\n  var canSmoothScroll = ('scrollBehavior' in document.body.style);\n  actions.forEach(function (_ref) {\n    var el = _ref.el,\n        top = _ref.top,\n        left = _ref.left;\n\n    if (el.scroll && canSmoothScroll) {\n      el.scroll({\n        top: top,\n        left: left,\n        behavior: behavior\n      });\n    } else {\n      el.scrollTop = top;\n      el.scrollLeft = left;\n    }\n  });\n}\n\nfunction getOptions(options) {\n  if (options === false) {\n    return {\n      block: 'end',\n      inline: 'nearest'\n    };\n  }\n\n  if (isOptionsObject(options)) {\n    return options;\n  }\n\n  return {\n    block: 'start',\n    inline: 'nearest'\n  };\n}\n\nfunction scrollIntoView(target, options) {\n  var isTargetAttached = target.isConnected || target.ownerDocument.documentElement.contains(target);\n\n  if (isOptionsObject(options) && typeof options.behavior === 'function') {\n    return options.behavior(isTargetAttached ? compute(target, options) : []);\n  }\n\n  if (!isTargetAttached) {\n    return;\n  }\n\n  var computeOptions = getOptions(options);\n  return defaultBehavior(compute(target, computeOptions), computeOptions.behavior);\n}\n\nexport default scrollIntoView;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,0BAA0B;AAE9C,SAASC,eAAeA,CAACC,OAAO,EAAE;EAChC,OAAOA,OAAO,KAAKC,MAAM,CAACD,OAAO,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC;AACzE;AAEA,SAASC,eAAeA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC1C,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IACvBA,QAAQ,GAAG,MAAM;EACnB;EAEA,IAAIC,eAAe,GAAI,gBAAgB,IAAIC,QAAQ,CAACC,IAAI,CAACC,KAAM;EAC/DL,OAAO,CAACM,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC9B,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;MACZC,GAAG,GAAGF,IAAI,CAACE,GAAG;MACdC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAEpB,IAAIF,EAAE,CAACG,MAAM,IAAIT,eAAe,EAAE;MAChCM,EAAE,CAACG,MAAM,CAAC;QACRF,GAAG,EAAEA,GAAG;QACRC,IAAI,EAAEA,IAAI;QACVT,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLO,EAAE,CAACI,SAAS,GAAGH,GAAG;MAClBD,EAAE,CAACK,UAAU,GAAGH,IAAI;IACtB;EACF,CAAC,CAAC;AACJ;AAEA,SAASI,UAAUA,CAACnB,OAAO,EAAE;EAC3B,IAAIA,OAAO,KAAK,KAAK,EAAE;IACrB,OAAO;MACLoB,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;IACV,CAAC;EACH;EAEA,IAAItB,eAAe,CAACC,OAAO,CAAC,EAAE;IAC5B,OAAOA,OAAO;EAChB;EAEA,OAAO;IACLoB,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE;EACV,CAAC;AACH;AAEA,SAASC,cAAcA,CAACC,MAAM,EAAEvB,OAAO,EAAE;EACvC,IAAIwB,gBAAgB,GAAGD,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG,aAAa,CAACC,eAAe,CAACC,QAAQ,CAACL,MAAM,CAAC;EAElG,IAAIxB,eAAe,CAACC,OAAO,CAAC,IAAI,OAAOA,OAAO,CAACM,QAAQ,KAAK,UAAU,EAAE;IACtE,OAAON,OAAO,CAACM,QAAQ,CAACkB,gBAAgB,GAAG1B,OAAO,CAACyB,MAAM,EAAEvB,OAAO,CAAC,GAAG,EAAE,CAAC;EAC3E;EAEA,IAAI,CAACwB,gBAAgB,EAAE;IACrB;EACF;EAEA,IAAIK,cAAc,GAAGV,UAAU,CAACnB,OAAO,CAAC;EACxC,OAAOI,eAAe,CAACN,OAAO,CAACyB,MAAM,EAAEM,cAAc,CAAC,EAAEA,cAAc,CAACvB,QAAQ,CAAC;AAClF;AAEA,eAAegB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}