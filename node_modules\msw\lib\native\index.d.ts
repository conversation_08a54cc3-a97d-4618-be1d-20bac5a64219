import { b as <PERSON>quest<PERSON><PERSON><PERSON>, i as RequestHandlerDefaultInfo, M as MockedRequest, c as DefaultBodyType, I as SetupServerApi } from '../glossary-dc3fd077.js';
import 'type-fest';
import '@mswjs/interceptors';
import 'headers-polyfill';
import 'strict-event-emitter';

declare const setupServer: (...requestHandlers: RequestHandler<RequestHandlerDefaultInfo, MockedRequest<DefaultBodyType>, any, MockedRequest<DefaultBodyType>>[]) => SetupServerApi;

export { setupServer };
