{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getInstance = exports[\"default\"] = void 0;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _rcNotification = _interopRequireDefault(require(\"rc-notification\"));\nvar _CloseOutlined = _interopRequireDefault(require(\"@ant-design/icons/CloseOutlined\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _CheckCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/CheckCircleOutlined\"));\nvar _CloseCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/CloseCircleOutlined\"));\nvar _ExclamationCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/ExclamationCircleOutlined\"));\nvar _InfoCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/InfoCircleOutlined\"));\nvar _useNotification = _interopRequireDefault(require(\"./hooks/useNotification\"));\nvar _configProvider = _interopRequireWildcard(require(\"../config-provider\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar __awaiter = void 0 && (void 0).__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar notificationInstance = {};\nvar defaultDuration = 4.5;\nvar defaultTop = 24;\nvar defaultBottom = 24;\nvar defaultPrefixCls = '';\nvar defaultPlacement = 'topRight';\nvar defaultGetContainer;\nvar defaultCloseIcon;\nvar rtl = false;\nvar maxCount;\nfunction setNotificationConfig(options) {\n  var duration = options.duration,\n    placement = options.placement,\n    bottom = options.bottom,\n    top = options.top,\n    getContainer = options.getContainer,\n    closeIcon = options.closeIcon,\n    prefixCls = options.prefixCls;\n  if (prefixCls !== undefined) {\n    defaultPrefixCls = prefixCls;\n  }\n  if (duration !== undefined) {\n    defaultDuration = duration;\n  }\n  if (placement !== undefined) {\n    defaultPlacement = placement;\n  } else if (options.rtl) {\n    defaultPlacement = 'topLeft';\n  }\n  if (bottom !== undefined) {\n    defaultBottom = bottom;\n  }\n  if (top !== undefined) {\n    defaultTop = top;\n  }\n  if (getContainer !== undefined) {\n    defaultGetContainer = getContainer;\n  }\n  if (closeIcon !== undefined) {\n    defaultCloseIcon = closeIcon;\n  }\n  if (options.rtl !== undefined) {\n    rtl = options.rtl;\n  }\n  if (options.maxCount !== undefined) {\n    maxCount = options.maxCount;\n  }\n}\nfunction getPlacementStyle(placement) {\n  var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultTop;\n  var bottom = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultBottom;\n  var style;\n  switch (placement) {\n    case 'top':\n      style = {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        right: 'auto',\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n    case 'topLeft':\n      style = {\n        left: 0,\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n    case 'topRight':\n      style = {\n        right: 0,\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n    case 'bottom':\n      style = {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        right: 'auto',\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n    case 'bottomLeft':\n      style = {\n        left: 0,\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n    default:\n      style = {\n        right: 0,\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n  }\n  return style;\n}\nfunction getNotificationInstance(args, callback) {\n  var _args$placement = args.placement,\n    placement = _args$placement === void 0 ? defaultPlacement : _args$placement,\n    top = args.top,\n    bottom = args.bottom,\n    _args$getContainer = args.getContainer,\n    getContainer = _args$getContainer === void 0 ? defaultGetContainer : _args$getContainer,\n    customizePrefixCls = args.prefixCls;\n  var _globalConfig = (0, _configProvider.globalConfig)(),\n    getPrefixCls = _globalConfig.getPrefixCls,\n    getIconPrefixCls = _globalConfig.getIconPrefixCls;\n  var prefixCls = getPrefixCls('notification', customizePrefixCls || defaultPrefixCls);\n  var iconPrefixCls = getIconPrefixCls();\n  var cacheKey = \"\".concat(prefixCls, \"-\").concat(placement);\n  var cacheInstance = notificationInstance[cacheKey];\n  if (cacheInstance) {\n    Promise.resolve(cacheInstance).then(function (instance) {\n      callback({\n        prefixCls: \"\".concat(prefixCls, \"-notice\"),\n        iconPrefixCls: iconPrefixCls,\n        instance: instance\n      });\n    });\n    return;\n  }\n  var notificationClass = (0, _classnames[\"default\"])(\"\".concat(prefixCls, \"-\").concat(placement), (0, _defineProperty2[\"default\"])({}, \"\".concat(prefixCls, \"-rtl\"), rtl === true));\n  notificationInstance[cacheKey] = new Promise(function (resolve) {\n    _rcNotification[\"default\"].newInstance({\n      prefixCls: prefixCls,\n      className: notificationClass,\n      style: getPlacementStyle(placement, top, bottom),\n      getContainer: getContainer,\n      maxCount: maxCount\n    }, function (notification) {\n      resolve(notification);\n      callback({\n        prefixCls: \"\".concat(prefixCls, \"-notice\"),\n        iconPrefixCls: iconPrefixCls,\n        instance: notification\n      });\n    });\n  });\n}\nvar typeToIcon = {\n  success: _CheckCircleOutlined[\"default\"],\n  info: _InfoCircleOutlined[\"default\"],\n  error: _CloseCircleOutlined[\"default\"],\n  warning: _ExclamationCircleOutlined[\"default\"]\n};\nfunction getRCNoticeProps(args, prefixCls, iconPrefixCls) {\n  var durationArg = args.duration,\n    icon = args.icon,\n    type = args.type,\n    description = args.description,\n    message = args.message,\n    btn = args.btn,\n    onClose = args.onClose,\n    onClick = args.onClick,\n    key = args.key,\n    style = args.style,\n    className = args.className,\n    _args$closeIcon = args.closeIcon,\n    closeIcon = _args$closeIcon === void 0 ? defaultCloseIcon : _args$closeIcon;\n  var duration = durationArg === undefined ? defaultDuration : durationArg;\n  var iconNode = null;\n  if (icon) {\n    iconNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-icon\")\n    }, args.icon);\n  } else if (type) {\n    iconNode = /*#__PURE__*/React.createElement(typeToIcon[type] || null, {\n      className: \"\".concat(prefixCls, \"-icon \").concat(prefixCls, \"-icon-\").concat(type)\n    });\n  }\n  var closeIconToRender = /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, closeIcon || /*#__PURE__*/React.createElement(_CloseOutlined[\"default\"], {\n    className: \"\".concat(prefixCls, \"-close-icon\")\n  }));\n  var autoMarginTag = !description && iconNode ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-message-single-line-auto-margin\")\n  }) : null;\n  return {\n    content: /*#__PURE__*/React.createElement(_configProvider[\"default\"], {\n      iconPrefixCls: iconPrefixCls\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: iconNode ? \"\".concat(prefixCls, \"-with-icon\") : '',\n      role: \"alert\"\n    }, iconNode, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message\")\n    }, autoMarginTag, message), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-description\")\n    }, description), btn ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-btn\")\n    }, btn) : null)),\n    duration: duration,\n    closable: true,\n    closeIcon: closeIconToRender,\n    onClose: onClose,\n    onClick: onClick,\n    key: key,\n    style: style || {},\n    className: (0, _classnames[\"default\"])(className, (0, _defineProperty2[\"default\"])({}, \"\".concat(prefixCls, \"-\").concat(type), !!type))\n  };\n}\nfunction notice(args) {\n  getNotificationInstance(args, function (_ref) {\n    var prefixCls = _ref.prefixCls,\n      iconPrefixCls = _ref.iconPrefixCls,\n      instance = _ref.instance;\n    instance.notice(getRCNoticeProps(args, prefixCls, iconPrefixCls));\n  });\n}\nvar api = {\n  open: notice,\n  close: function close(key) {\n    Object.keys(notificationInstance).forEach(function (cacheKey) {\n      return Promise.resolve(notificationInstance[cacheKey]).then(function (instance) {\n        instance.removeNotice(key);\n      });\n    });\n  },\n  config: setNotificationConfig,\n  destroy: function destroy() {\n    Object.keys(notificationInstance).forEach(function (cacheKey) {\n      Promise.resolve(notificationInstance[cacheKey]).then(function (instance) {\n        instance.destroy();\n      });\n      delete notificationInstance[cacheKey]; // lgtm[js/missing-await]\n    });\n  }\n};\n['success', 'info', 'warning', 'error'].forEach(function (type) {\n  api[type] = function (args) {\n    return api.open((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n      type: type\n    }));\n  };\n});\napi.warn = api.warning;\napi.useNotification = (0, _useNotification[\"default\"])(getNotificationInstance, getRCNoticeProps);\n/** @private test Only function. Not work on production */\n\nvar getInstance = function getInstance(cacheKey) {\n  return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regenerator[\"default\"].mark(function _callee() {\n    return _regenerator[\"default\"].wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            return _context.abrupt(\"return\", process.env.NODE_ENV === 'test' ? notificationInstance[cacheKey] : null);\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n};\nexports.getInstance = getInstance;\nvar _default = api;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "_typeof", "Object", "defineProperty", "exports", "value", "getInstance", "_regenerator", "_extends2", "_defineProperty2", "React", "_interopRequireWildcard", "_rcNotification", "_CloseOutlined", "_classnames", "_CheckCircleOutlined", "_CloseCircleOutlined", "_ExclamationCircleOutlined", "_InfoCircleOutlined", "_useNotification", "_config<PERSON><PERSON><PERSON>", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "notificationInstance", "defaultDuration", "defaultTop", "defaultBottom", "defaultPrefixCls", "defaultPlacement", "defaultGetContainer", "defaultCloseIcon", "rtl", "maxCount", "setNotificationConfig", "options", "duration", "placement", "bottom", "top", "getContainer", "closeIcon", "prefixCls", "undefined", "getPlacementStyle", "arguments", "length", "style", "left", "transform", "right", "getNotificationInstance", "args", "callback", "_args$placement", "_args$getContainer", "customizePrefixCls", "_globalConfig", "globalConfig", "getPrefixCls", "getIconPrefixCls", "iconPrefixCls", "cache<PERSON>ey", "concat", "cacheInstance", "instance", "notificationClass", "newInstance", "className", "notification", "typeToIcon", "success", "info", "error", "warning", "getRCNoticeProps", "durationArg", "icon", "type", "description", "message", "btn", "onClose", "onClick", "_args$closeIcon", "iconNode", "createElement", "closeIconToRender", "autoMarginTag", "content", "role", "closable", "notice", "_ref", "api", "open", "close", "keys", "for<PERSON>ach", "removeNotice", "config", "destroy", "warn", "useNotification", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "abrupt", "process", "env", "NODE_ENV", "stop", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/lib/notification/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getInstance = exports[\"default\"] = void 0;\n\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar React = _interopRequireWildcard(require(\"react\"));\n\nvar _rcNotification = _interopRequireDefault(require(\"rc-notification\"));\n\nvar _CloseOutlined = _interopRequireDefault(require(\"@ant-design/icons/CloseOutlined\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _CheckCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/CheckCircleOutlined\"));\n\nvar _CloseCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/CloseCircleOutlined\"));\n\nvar _ExclamationCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/ExclamationCircleOutlined\"));\n\nvar _InfoCircleOutlined = _interopRequireDefault(require(\"@ant-design/icons/InfoCircleOutlined\"));\n\nvar _useNotification = _interopRequireDefault(require(\"./hooks/useNotification\"));\n\nvar _configProvider = _interopRequireWildcard(require(\"../config-provider\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar __awaiter = void 0 && (void 0).__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nvar notificationInstance = {};\nvar defaultDuration = 4.5;\nvar defaultTop = 24;\nvar defaultBottom = 24;\nvar defaultPrefixCls = '';\nvar defaultPlacement = 'topRight';\nvar defaultGetContainer;\nvar defaultCloseIcon;\nvar rtl = false;\nvar maxCount;\n\nfunction setNotificationConfig(options) {\n  var duration = options.duration,\n      placement = options.placement,\n      bottom = options.bottom,\n      top = options.top,\n      getContainer = options.getContainer,\n      closeIcon = options.closeIcon,\n      prefixCls = options.prefixCls;\n\n  if (prefixCls !== undefined) {\n    defaultPrefixCls = prefixCls;\n  }\n\n  if (duration !== undefined) {\n    defaultDuration = duration;\n  }\n\n  if (placement !== undefined) {\n    defaultPlacement = placement;\n  } else if (options.rtl) {\n    defaultPlacement = 'topLeft';\n  }\n\n  if (bottom !== undefined) {\n    defaultBottom = bottom;\n  }\n\n  if (top !== undefined) {\n    defaultTop = top;\n  }\n\n  if (getContainer !== undefined) {\n    defaultGetContainer = getContainer;\n  }\n\n  if (closeIcon !== undefined) {\n    defaultCloseIcon = closeIcon;\n  }\n\n  if (options.rtl !== undefined) {\n    rtl = options.rtl;\n  }\n\n  if (options.maxCount !== undefined) {\n    maxCount = options.maxCount;\n  }\n}\n\nfunction getPlacementStyle(placement) {\n  var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultTop;\n  var bottom = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultBottom;\n  var style;\n\n  switch (placement) {\n    case 'top':\n      style = {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        right: 'auto',\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n\n    case 'topLeft':\n      style = {\n        left: 0,\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n\n    case 'topRight':\n      style = {\n        right: 0,\n        top: top,\n        bottom: 'auto'\n      };\n      break;\n\n    case 'bottom':\n      style = {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        right: 'auto',\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n\n    case 'bottomLeft':\n      style = {\n        left: 0,\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n\n    default:\n      style = {\n        right: 0,\n        top: 'auto',\n        bottom: bottom\n      };\n      break;\n  }\n\n  return style;\n}\n\nfunction getNotificationInstance(args, callback) {\n  var _args$placement = args.placement,\n      placement = _args$placement === void 0 ? defaultPlacement : _args$placement,\n      top = args.top,\n      bottom = args.bottom,\n      _args$getContainer = args.getContainer,\n      getContainer = _args$getContainer === void 0 ? defaultGetContainer : _args$getContainer,\n      customizePrefixCls = args.prefixCls;\n\n  var _globalConfig = (0, _configProvider.globalConfig)(),\n      getPrefixCls = _globalConfig.getPrefixCls,\n      getIconPrefixCls = _globalConfig.getIconPrefixCls;\n\n  var prefixCls = getPrefixCls('notification', customizePrefixCls || defaultPrefixCls);\n  var iconPrefixCls = getIconPrefixCls();\n  var cacheKey = \"\".concat(prefixCls, \"-\").concat(placement);\n  var cacheInstance = notificationInstance[cacheKey];\n\n  if (cacheInstance) {\n    Promise.resolve(cacheInstance).then(function (instance) {\n      callback({\n        prefixCls: \"\".concat(prefixCls, \"-notice\"),\n        iconPrefixCls: iconPrefixCls,\n        instance: instance\n      });\n    });\n    return;\n  }\n\n  var notificationClass = (0, _classnames[\"default\"])(\"\".concat(prefixCls, \"-\").concat(placement), (0, _defineProperty2[\"default\"])({}, \"\".concat(prefixCls, \"-rtl\"), rtl === true));\n  notificationInstance[cacheKey] = new Promise(function (resolve) {\n    _rcNotification[\"default\"].newInstance({\n      prefixCls: prefixCls,\n      className: notificationClass,\n      style: getPlacementStyle(placement, top, bottom),\n      getContainer: getContainer,\n      maxCount: maxCount\n    }, function (notification) {\n      resolve(notification);\n      callback({\n        prefixCls: \"\".concat(prefixCls, \"-notice\"),\n        iconPrefixCls: iconPrefixCls,\n        instance: notification\n      });\n    });\n  });\n}\n\nvar typeToIcon = {\n  success: _CheckCircleOutlined[\"default\"],\n  info: _InfoCircleOutlined[\"default\"],\n  error: _CloseCircleOutlined[\"default\"],\n  warning: _ExclamationCircleOutlined[\"default\"]\n};\n\nfunction getRCNoticeProps(args, prefixCls, iconPrefixCls) {\n  var durationArg = args.duration,\n      icon = args.icon,\n      type = args.type,\n      description = args.description,\n      message = args.message,\n      btn = args.btn,\n      onClose = args.onClose,\n      onClick = args.onClick,\n      key = args.key,\n      style = args.style,\n      className = args.className,\n      _args$closeIcon = args.closeIcon,\n      closeIcon = _args$closeIcon === void 0 ? defaultCloseIcon : _args$closeIcon;\n  var duration = durationArg === undefined ? defaultDuration : durationArg;\n  var iconNode = null;\n\n  if (icon) {\n    iconNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-icon\")\n    }, args.icon);\n  } else if (type) {\n    iconNode = /*#__PURE__*/React.createElement(typeToIcon[type] || null, {\n      className: \"\".concat(prefixCls, \"-icon \").concat(prefixCls, \"-icon-\").concat(type)\n    });\n  }\n\n  var closeIconToRender = /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, closeIcon || /*#__PURE__*/React.createElement(_CloseOutlined[\"default\"], {\n    className: \"\".concat(prefixCls, \"-close-icon\")\n  }));\n  var autoMarginTag = !description && iconNode ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-message-single-line-auto-margin\")\n  }) : null;\n  return {\n    content: /*#__PURE__*/React.createElement(_configProvider[\"default\"], {\n      iconPrefixCls: iconPrefixCls\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: iconNode ? \"\".concat(prefixCls, \"-with-icon\") : '',\n      role: \"alert\"\n    }, iconNode, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-message\")\n    }, autoMarginTag, message), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-description\")\n    }, description), btn ? /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-btn\")\n    }, btn) : null)),\n    duration: duration,\n    closable: true,\n    closeIcon: closeIconToRender,\n    onClose: onClose,\n    onClick: onClick,\n    key: key,\n    style: style || {},\n    className: (0, _classnames[\"default\"])(className, (0, _defineProperty2[\"default\"])({}, \"\".concat(prefixCls, \"-\").concat(type), !!type))\n  };\n}\n\nfunction notice(args) {\n  getNotificationInstance(args, function (_ref) {\n    var prefixCls = _ref.prefixCls,\n        iconPrefixCls = _ref.iconPrefixCls,\n        instance = _ref.instance;\n    instance.notice(getRCNoticeProps(args, prefixCls, iconPrefixCls));\n  });\n}\n\nvar api = {\n  open: notice,\n  close: function close(key) {\n    Object.keys(notificationInstance).forEach(function (cacheKey) {\n      return Promise.resolve(notificationInstance[cacheKey]).then(function (instance) {\n        instance.removeNotice(key);\n      });\n    });\n  },\n  config: setNotificationConfig,\n  destroy: function destroy() {\n    Object.keys(notificationInstance).forEach(function (cacheKey) {\n      Promise.resolve(notificationInstance[cacheKey]).then(function (instance) {\n        instance.destroy();\n      });\n      delete notificationInstance[cacheKey]; // lgtm[js/missing-await]\n    });\n  }\n};\n['success', 'info', 'warning', 'error'].forEach(function (type) {\n  api[type] = function (args) {\n    return api.open((0, _extends2[\"default\"])((0, _extends2[\"default\"])({}, args), {\n      type: type\n    }));\n  };\n});\napi.warn = api.warning;\napi.useNotification = (0, _useNotification[\"default\"])(getNotificationInstance, getRCNoticeProps);\n/** @private test Only function. Not work on production */\n\nvar getInstance = function getInstance(cacheKey) {\n  return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regenerator[\"default\"].mark(function _callee() {\n    return _regenerator[\"default\"].wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            return _context.abrupt(\"return\", process.env.NODE_ENV === 'test' ? notificationInstance[cacheKey] : null);\n\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n};\n\nexports.getInstance = getInstance;\nvar _default = api;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpF,IAAIC,OAAO,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAEtDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAEjD,IAAIG,YAAY,GAAGR,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAEhF,IAAIQ,SAAS,GAAGT,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIS,gBAAgB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,KAAK,GAAGC,uBAAuB,CAACX,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIY,eAAe,GAAGb,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAExE,IAAIa,cAAc,GAAGd,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEvF,IAAIc,WAAW,GAAGf,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,oBAAoB,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAEnG,IAAIgB,oBAAoB,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAEnG,IAAIiB,0BAA0B,GAAGlB,sBAAsB,CAACC,OAAO,CAAC,6CAA6C,CAAC,CAAC;AAE/G,IAAIkB,mBAAmB,GAAGnB,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAEjG,IAAImB,gBAAgB,GAAGpB,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAEjF,IAAIoB,eAAe,GAAGT,uBAAuB,CAACX,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE5E,SAASqB,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAE9U,SAASX,uBAAuBA,CAACe,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAIzB,OAAO,CAACyB,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAE,SAAS,EAAEA;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAG9B,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC+B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIhC,MAAM,CAACiC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAG9B,MAAM,CAAC+B,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAErC,MAAM,CAACC,cAAc,CAAC4B,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAC,SAAS,CAAC,GAAGL,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAE1yB,IAAIS,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAEA,SAAS,IAAI,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EAC3F,SAASC,KAAKA,CAACxC,KAAK,EAAE;IACpB,OAAOA,KAAK,YAAYsC,CAAC,GAAGtC,KAAK,GAAG,IAAIsC,CAAC,CAAC,UAAUG,OAAO,EAAE;MAC3DA,OAAO,CAACzC,KAAK,CAAC;IAChB,CAAC,CAAC;EACJ;EAEA,OAAO,KAAKsC,CAAC,KAAKA,CAAC,GAAGI,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACzD,SAASC,SAASA,CAAC5C,KAAK,EAAE;MACxB,IAAI;QACF6C,IAAI,CAACN,SAAS,CAACO,IAAI,CAAC9C,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAO+C,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IAEA,SAASC,QAAQA,CAAChD,KAAK,EAAE;MACvB,IAAI;QACF6C,IAAI,CAACN,SAAS,CAAC,OAAO,CAAC,CAACvC,KAAK,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO+C,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IAEA,SAASF,IAAIA,CAACI,MAAM,EAAE;MACpBA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACjD,KAAK,CAAC,GAAGwC,KAAK,CAACS,MAAM,CAACjD,KAAK,CAAC,CAACmD,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IACrF;IAEAH,IAAI,CAAC,CAACN,SAAS,GAAGA,SAAS,CAACa,KAAK,CAAChB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAES,IAAI,CAAC,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ,CAAC;AAED,IAAIO,oBAAoB,GAAG,CAAC,CAAC;AAC7B,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,gBAAgB,GAAG,UAAU;AACjC,IAAIC,mBAAmB;AACvB,IAAIC,gBAAgB;AACpB,IAAIC,GAAG,GAAG,KAAK;AACf,IAAIC,QAAQ;AAEZ,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACtC,IAAIC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;IAC3BC,SAAS,GAAGF,OAAO,CAACE,SAAS;IAC7BC,MAAM,GAAGH,OAAO,CAACG,MAAM;IACvBC,GAAG,GAAGJ,OAAO,CAACI,GAAG;IACjBC,YAAY,GAAGL,OAAO,CAACK,YAAY;IACnCC,SAAS,GAAGN,OAAO,CAACM,SAAS;IAC7BC,SAAS,GAAGP,OAAO,CAACO,SAAS;EAEjC,IAAIA,SAAS,KAAKC,SAAS,EAAE;IAC3Bf,gBAAgB,GAAGc,SAAS;EAC9B;EAEA,IAAIN,QAAQ,KAAKO,SAAS,EAAE;IAC1BlB,eAAe,GAAGW,QAAQ;EAC5B;EAEA,IAAIC,SAAS,KAAKM,SAAS,EAAE;IAC3Bd,gBAAgB,GAAGQ,SAAS;EAC9B,CAAC,MAAM,IAAIF,OAAO,CAACH,GAAG,EAAE;IACtBH,gBAAgB,GAAG,SAAS;EAC9B;EAEA,IAAIS,MAAM,KAAKK,SAAS,EAAE;IACxBhB,aAAa,GAAGW,MAAM;EACxB;EAEA,IAAIC,GAAG,KAAKI,SAAS,EAAE;IACrBjB,UAAU,GAAGa,GAAG;EAClB;EAEA,IAAIC,YAAY,KAAKG,SAAS,EAAE;IAC9Bb,mBAAmB,GAAGU,YAAY;EACpC;EAEA,IAAIC,SAAS,KAAKE,SAAS,EAAE;IAC3BZ,gBAAgB,GAAGU,SAAS;EAC9B;EAEA,IAAIN,OAAO,CAACH,GAAG,KAAKW,SAAS,EAAE;IAC7BX,GAAG,GAAGG,OAAO,CAACH,GAAG;EACnB;EAEA,IAAIG,OAAO,CAACF,QAAQ,KAAKU,SAAS,EAAE;IAClCV,QAAQ,GAAGE,OAAO,CAACF,QAAQ;EAC7B;AACF;AAEA,SAASW,iBAAiBA,CAACP,SAAS,EAAE;EACpC,IAAIE,GAAG,GAAGM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAGnB,UAAU;EACxF,IAAIY,MAAM,GAAGO,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAGlB,aAAa;EAC9F,IAAIoB,KAAK;EAET,QAAQV,SAAS;IACf,KAAK,KAAK;MACRU,KAAK,GAAG;QACNC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,MAAM;QACbX,GAAG,EAAEA,GAAG;QACRD,MAAM,EAAE;MACV,CAAC;MACD;IAEF,KAAK,SAAS;MACZS,KAAK,GAAG;QACNC,IAAI,EAAE,CAAC;QACPT,GAAG,EAAEA,GAAG;QACRD,MAAM,EAAE;MACV,CAAC;MACD;IAEF,KAAK,UAAU;MACbS,KAAK,GAAG;QACNG,KAAK,EAAE,CAAC;QACRX,GAAG,EAAEA,GAAG;QACRD,MAAM,EAAE;MACV,CAAC;MACD;IAEF,KAAK,QAAQ;MACXS,KAAK,GAAG;QACNC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,MAAM;QACbX,GAAG,EAAE,MAAM;QACXD,MAAM,EAAEA;MACV,CAAC;MACD;IAEF,KAAK,YAAY;MACfS,KAAK,GAAG;QACNC,IAAI,EAAE,CAAC;QACPT,GAAG,EAAE,MAAM;QACXD,MAAM,EAAEA;MACV,CAAC;MACD;IAEF;MACES,KAAK,GAAG;QACNG,KAAK,EAAE,CAAC;QACRX,GAAG,EAAE,MAAM;QACXD,MAAM,EAAEA;MACV,CAAC;MACD;EACJ;EAEA,OAAOS,KAAK;AACd;AAEA,SAASI,uBAAuBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC/C,IAAIC,eAAe,GAAGF,IAAI,CAACf,SAAS;IAChCA,SAAS,GAAGiB,eAAe,KAAK,KAAK,CAAC,GAAGzB,gBAAgB,GAAGyB,eAAe;IAC3Ef,GAAG,GAAGa,IAAI,CAACb,GAAG;IACdD,MAAM,GAAGc,IAAI,CAACd,MAAM;IACpBiB,kBAAkB,GAAGH,IAAI,CAACZ,YAAY;IACtCA,YAAY,GAAGe,kBAAkB,KAAK,KAAK,CAAC,GAAGzB,mBAAmB,GAAGyB,kBAAkB;IACvFC,kBAAkB,GAAGJ,IAAI,CAACV,SAAS;EAEvC,IAAIe,aAAa,GAAG,CAAC,CAAC,EAAEvE,eAAe,CAACwE,YAAY,EAAE,CAAC;IACnDC,YAAY,GAAGF,aAAa,CAACE,YAAY;IACzCC,gBAAgB,GAAGH,aAAa,CAACG,gBAAgB;EAErD,IAAIlB,SAAS,GAAGiB,YAAY,CAAC,cAAc,EAAEH,kBAAkB,IAAI5B,gBAAgB,CAAC;EACpF,IAAIiC,aAAa,GAAGD,gBAAgB,CAAC,CAAC;EACtC,IAAIE,QAAQ,GAAG,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,GAAG,CAAC,CAACqB,MAAM,CAAC1B,SAAS,CAAC;EAC1D,IAAI2B,aAAa,GAAGxC,oBAAoB,CAACsC,QAAQ,CAAC;EAElD,IAAIE,aAAa,EAAE;IACjBnD,OAAO,CAACD,OAAO,CAACoD,aAAa,CAAC,CAAC1C,IAAI,CAAC,UAAU2C,QAAQ,EAAE;MACtDZ,QAAQ,CAAC;QACPX,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACrB,SAAS,EAAE,SAAS,CAAC;QAC1CmB,aAAa,EAAEA,aAAa;QAC5BI,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;EACF;EAEA,IAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAEtF,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,CAACmF,MAAM,CAACrB,SAAS,EAAE,GAAG,CAAC,CAACqB,MAAM,CAAC1B,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE9D,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAACwF,MAAM,CAACrB,SAAS,EAAE,MAAM,CAAC,EAAEV,GAAG,KAAK,IAAI,CAAC,CAAC;EAClLR,oBAAoB,CAACsC,QAAQ,CAAC,GAAG,IAAIjD,OAAO,CAAC,UAAUD,OAAO,EAAE;IAC9DlC,eAAe,CAAC,SAAS,CAAC,CAACyF,WAAW,CAAC;MACrCzB,SAAS,EAAEA,SAAS;MACpB0B,SAAS,EAAEF,iBAAiB;MAC5BnB,KAAK,EAAEH,iBAAiB,CAACP,SAAS,EAAEE,GAAG,EAAED,MAAM,CAAC;MAChDE,YAAY,EAAEA,YAAY;MAC1BP,QAAQ,EAAEA;IACZ,CAAC,EAAE,UAAUoC,YAAY,EAAE;MACzBzD,OAAO,CAACyD,YAAY,CAAC;MACrBhB,QAAQ,CAAC;QACPX,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACrB,SAAS,EAAE,SAAS,CAAC;QAC1CmB,aAAa,EAAEA,aAAa;QAC5BI,QAAQ,EAAEI;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,IAAIC,UAAU,GAAG;EACfC,OAAO,EAAE1F,oBAAoB,CAAC,SAAS,CAAC;EACxC2F,IAAI,EAAExF,mBAAmB,CAAC,SAAS,CAAC;EACpCyF,KAAK,EAAE3F,oBAAoB,CAAC,SAAS,CAAC;EACtC4F,OAAO,EAAE3F,0BAA0B,CAAC,SAAS;AAC/C,CAAC;AAED,SAAS4F,gBAAgBA,CAACvB,IAAI,EAAEV,SAAS,EAAEmB,aAAa,EAAE;EACxD,IAAIe,WAAW,GAAGxB,IAAI,CAAChB,QAAQ;IAC3ByC,IAAI,GAAGzB,IAAI,CAACyB,IAAI;IAChBC,IAAI,GAAG1B,IAAI,CAAC0B,IAAI;IAChBC,WAAW,GAAG3B,IAAI,CAAC2B,WAAW;IAC9BC,OAAO,GAAG5B,IAAI,CAAC4B,OAAO;IACtBC,GAAG,GAAG7B,IAAI,CAAC6B,GAAG;IACdC,OAAO,GAAG9B,IAAI,CAAC8B,OAAO;IACtBC,OAAO,GAAG/B,IAAI,CAAC+B,OAAO;IACtBnF,GAAG,GAAGoD,IAAI,CAACpD,GAAG;IACd+C,KAAK,GAAGK,IAAI,CAACL,KAAK;IAClBqB,SAAS,GAAGhB,IAAI,CAACgB,SAAS;IAC1BgB,eAAe,GAAGhC,IAAI,CAACX,SAAS;IAChCA,SAAS,GAAG2C,eAAe,KAAK,KAAK,CAAC,GAAGrD,gBAAgB,GAAGqD,eAAe;EAC/E,IAAIhD,QAAQ,GAAGwC,WAAW,KAAKjC,SAAS,GAAGlB,eAAe,GAAGmD,WAAW;EACxE,IAAIS,QAAQ,GAAG,IAAI;EAEnB,IAAIR,IAAI,EAAE;IACRQ,QAAQ,GAAG,aAAa7G,KAAK,CAAC8G,aAAa,CAAC,MAAM,EAAE;MAClDlB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,OAAO;IACzC,CAAC,EAAEU,IAAI,CAACyB,IAAI,CAAC;EACf,CAAC,MAAM,IAAIC,IAAI,EAAE;IACfO,QAAQ,GAAG,aAAa7G,KAAK,CAAC8G,aAAa,CAAChB,UAAU,CAACQ,IAAI,CAAC,IAAI,IAAI,EAAE;MACpEV,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,QAAQ,CAAC,CAACqB,MAAM,CAACrB,SAAS,EAAE,QAAQ,CAAC,CAACqB,MAAM,CAACe,IAAI;IACnF,CAAC,CAAC;EACJ;EAEA,IAAIS,iBAAiB,GAAG,aAAa/G,KAAK,CAAC8G,aAAa,CAAC,MAAM,EAAE;IAC/DlB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAED,SAAS,IAAI,aAAajE,KAAK,CAAC8G,aAAa,CAAC3G,cAAc,CAAC,SAAS,CAAC,EAAE;IAC1EyF,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,aAAa;EAC/C,CAAC,CAAC,CAAC;EACH,IAAI8C,aAAa,GAAG,CAACT,WAAW,IAAIM,QAAQ,GAAG,aAAa7G,KAAK,CAAC8G,aAAa,CAAC,MAAM,EAAE;IACtFlB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,kCAAkC;EACpE,CAAC,CAAC,GAAG,IAAI;EACT,OAAO;IACL+C,OAAO,EAAE,aAAajH,KAAK,CAAC8G,aAAa,CAACpG,eAAe,CAAC,SAAS,CAAC,EAAE;MACpE2E,aAAa,EAAEA;IACjB,CAAC,EAAE,aAAarF,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;MACzClB,SAAS,EAAEiB,QAAQ,GAAG,EAAE,CAACtB,MAAM,CAACrB,SAAS,EAAE,YAAY,CAAC,GAAG,EAAE;MAC7DgD,IAAI,EAAE;IACR,CAAC,EAAEL,QAAQ,EAAE,aAAa7G,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;MACnDlB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAE8C,aAAa,EAAER,OAAO,CAAC,EAAE,aAAaxG,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;MAClElB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,cAAc;IAChD,CAAC,EAAEqC,WAAW,CAAC,EAAEE,GAAG,GAAG,aAAazG,KAAK,CAAC8G,aAAa,CAAC,MAAM,EAAE;MAC9DlB,SAAS,EAAE,EAAE,CAACL,MAAM,CAACrB,SAAS,EAAE,MAAM;IACxC,CAAC,EAAEuC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB7C,QAAQ,EAAEA,QAAQ;IAClBuD,QAAQ,EAAE,IAAI;IACdlD,SAAS,EAAE8C,iBAAiB;IAC5BL,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBnF,GAAG,EAAEA,GAAG;IACR+C,KAAK,EAAEA,KAAK,IAAI,CAAC,CAAC;IAClBqB,SAAS,EAAE,CAAC,CAAC,EAAExF,WAAW,CAAC,SAAS,CAAC,EAAEwF,SAAS,EAAE,CAAC,CAAC,EAAE7F,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAACwF,MAAM,CAACrB,SAAS,EAAE,GAAG,CAAC,CAACqB,MAAM,CAACe,IAAI,CAAC,EAAE,CAAC,CAACA,IAAI,CAAC;EACxI,CAAC;AACH;AAEA,SAASc,MAAMA,CAACxC,IAAI,EAAE;EACpBD,uBAAuB,CAACC,IAAI,EAAE,UAAUyC,IAAI,EAAE;IAC5C,IAAInD,SAAS,GAAGmD,IAAI,CAACnD,SAAS;MAC1BmB,aAAa,GAAGgC,IAAI,CAAChC,aAAa;MAClCI,QAAQ,GAAG4B,IAAI,CAAC5B,QAAQ;IAC5BA,QAAQ,CAAC2B,MAAM,CAACjB,gBAAgB,CAACvB,IAAI,EAAEV,SAAS,EAAEmB,aAAa,CAAC,CAAC;EACnE,CAAC,CAAC;AACJ;AAEA,IAAIiC,GAAG,GAAG;EACRC,IAAI,EAAEH,MAAM;EACZI,KAAK,EAAE,SAASA,KAAKA,CAAChG,GAAG,EAAE;IACzBhC,MAAM,CAACiI,IAAI,CAACzE,oBAAoB,CAAC,CAAC0E,OAAO,CAAC,UAAUpC,QAAQ,EAAE;MAC5D,OAAOjD,OAAO,CAACD,OAAO,CAACY,oBAAoB,CAACsC,QAAQ,CAAC,CAAC,CAACxC,IAAI,CAAC,UAAU2C,QAAQ,EAAE;QAC9EA,QAAQ,CAACkC,YAAY,CAACnG,GAAG,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDoG,MAAM,EAAElE,qBAAqB;EAC7BmE,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1BrI,MAAM,CAACiI,IAAI,CAACzE,oBAAoB,CAAC,CAAC0E,OAAO,CAAC,UAAUpC,QAAQ,EAAE;MAC5DjD,OAAO,CAACD,OAAO,CAACY,oBAAoB,CAACsC,QAAQ,CAAC,CAAC,CAACxC,IAAI,CAAC,UAAU2C,QAAQ,EAAE;QACvEA,QAAQ,CAACoC,OAAO,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,OAAO7E,oBAAoB,CAACsC,QAAQ,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ;AACF,CAAC;AACD,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAACoC,OAAO,CAAC,UAAUpB,IAAI,EAAE;EAC9DgB,GAAG,CAAChB,IAAI,CAAC,GAAG,UAAU1B,IAAI,EAAE;IAC1B,OAAO0C,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzH,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;MAC7E0B,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AACFgB,GAAG,CAACQ,IAAI,GAAGR,GAAG,CAACpB,OAAO;AACtBoB,GAAG,CAACS,eAAe,GAAG,CAAC,CAAC,EAAEtH,gBAAgB,CAAC,SAAS,CAAC,EAAEkE,uBAAuB,EAAEwB,gBAAgB,CAAC;AACjG;;AAEA,IAAIvG,WAAW,GAAG,SAASA,WAAWA,CAAC0F,QAAQ,EAAE;EAC/C,OAAOxD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAajC,YAAY,CAAC,SAAS,CAAC,CAACmI,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;IACpG,OAAOpI,YAAY,CAAC,SAAS,CAAC,CAACqI,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;MAC9D,OAAO,CAAC,EAAE;QACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAAC3F,IAAI;UACnC,KAAK,CAAC;YACJ,OAAO2F,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,GAAGzF,oBAAoB,CAACsC,QAAQ,CAAC,GAAG,IAAI,CAAC;UAE3G,KAAK,CAAC;UACN,KAAK,KAAK;YACR,OAAO8C,QAAQ,CAACM,IAAI,CAAC,CAAC;QAC1B;MACF;IACF,CAAC,EAAET,OAAO,CAAC;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AAEDvI,OAAO,CAACE,WAAW,GAAGA,WAAW;AACjC,IAAI+I,QAAQ,GAAGrB,GAAG;AAClB5H,OAAO,CAAC,SAAS,CAAC,GAAGiJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}