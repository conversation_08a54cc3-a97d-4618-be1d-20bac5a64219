/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var DO_NOT_EXPORT_CODEPAGE=true;var DO_NOT_EXPORT_JSZIP=true;var XLSX={};function make_xlsx_lib(e){e.version="0.17.5";var r=1200,t=1252;if(typeof module!=="undefined"&&typeof require!=="undefined"){if(typeof cptable==="undefined"){if(typeof global!=="undefined")global.cptable=undefined;else if(typeof window!=="undefined")window.cptable=undefined}}var a=[874,932,936,949,950];for(var n=0;n<=8;++n)a.push(1250+n);var i={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969};var s=function(e){if(a.indexOf(e)==-1)return;t=i[0]=e};function f(){s(1252)}var l=function(e){r=e;s(e)};function o(){l(1200);f()}function c(e){var r=[];for(var t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function u(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t)+(e.charCodeAt(2*t+1)<<8));return r.join("")}function h(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var d=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);if(r==255&&t==254)return u(e.slice(2));if(r==254&&t==255)return h(e.slice(2));if(r==65279)return e.slice(1);return e};var v=function Rm(e){return String.fromCharCode(e)};var p=function Fm(e){return String.fromCharCode(e)};if(typeof cptable!=="undefined"){l=function(e){r=e;s(e)};d=function(e){if(e.charCodeAt(0)===255&&e.charCodeAt(1)===254){return cptable.utils.decode(1200,c(e.slice(2)))}return e};v=function Dm(e){if(r===1200)return String.fromCharCode(e);return cptable.utils.decode(r,[e&255,e>>8])[0]};p=function Om(e){return cptable.utils.decode(t,[e])[0]}}var b=null;var m=true;var g=function Pm(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return{encode:function(r){var t="";var a=0,n=0,i=0,s=0,f=0,l=0,o=0;for(var c=0;c<r.length;){a=r.charCodeAt(c++);s=a>>2;n=r.charCodeAt(c++);f=(a&3)<<4|n>>4;i=r.charCodeAt(c++);l=(n&15)<<2|i>>6;o=i&63;if(isNaN(n)){l=o=64}else if(isNaN(i)){o=64}t+=e.charAt(s)+e.charAt(f)+e.charAt(l)+e.charAt(o)}return t},decode:function r(t){var a="";var n=0,i=0,s=0,f=0,l=0,o=0,c=0;t=t.replace(/[^\w\+\/\=]/g,"");for(var u=0;u<t.length;){f=e.indexOf(t.charAt(u++));l=e.indexOf(t.charAt(u++));n=f<<2|l>>4;a+=String.fromCharCode(n);o=e.indexOf(t.charAt(u++));i=(l&15)<<4|o>>2;if(o!==64){a+=String.fromCharCode(i)}c=e.indexOf(t.charAt(u++));s=(o&3)<<6|c;if(c!==64){a+=String.fromCharCode(s)}}return a}}}();var E=typeof Buffer!=="undefined"&&typeof process!=="undefined"&&typeof process.versions!=="undefined"&&!!process.versions.node;var w=function(){};if(typeof Buffer!=="undefined"){var k=!Buffer.from;if(!k)try{Buffer.from("foo","utf8")}catch(S){k=true}w=k?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer);if(!Buffer.alloc)Buffer.alloc=function(e){return new Buffer(e)};if(!Buffer.allocUnsafe)Buffer.allocUnsafe=function(e){return new Buffer(e)}}function B(e){return E?Buffer.alloc(e):new Array(e)}function C(e){return E?Buffer.allocUnsafe(e):new Array(e)}var T=function Nm(e){if(E)return w(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)&255})};function _(e){if(typeof ArrayBuffer==="undefined")return T(e);var r=new ArrayBuffer(e.length),t=new Uint8Array(r);for(var a=0;a!=e.length;++a)t[a]=e.charCodeAt(a)&255;return r}function x(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");var r=[];for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function A(e){if(typeof Uint8Array==="undefined")throw new Error("Unsupported");return new Uint8Array(e)}function y(e){if(typeof ArrayBuffer=="undefined")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return y(new Uint8Array(e));var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=e[t];return r}var I=function(e){return[].concat.apply([],e)};var R=/\u0000/g,F=/[\u0001-\u0006]/g;var D={};var O=function Mm(e){e.version="0.11.2";function r(e){var r="",t=e.length-1;while(t>=0)r+=e.charAt(t--);return r}function t(e,r){var t="";while(t.length<r)t+=e;return t}function a(e,r){var a=""+e;return a.length>=r?a:t("0",r-a.length)+a}function n(e,r){var a=""+e;return a.length>=r?a:t(" ",r-a.length)+a}function i(e,r){var a=""+e;return a.length>=r?a:a+t(" ",r-a.length)}function s(e,r){var a=""+Math.round(e);return a.length>=r?a:t("0",r-a.length)+a}function f(e,r){var a=""+e;return a.length>=r?a:t("0",r-a.length)+a}var l=Math.pow(2,32);function o(e,r){if(e>l||e<-l)return s(e,r);var t=Math.round(e);return f(t,r)}function c(e,r){r=r||0;return e.length>=7+r&&(e.charCodeAt(r)|32)===103&&(e.charCodeAt(r+1)|32)===101&&(e.charCodeAt(r+2)|32)===110&&(e.charCodeAt(r+3)|32)===101&&(e.charCodeAt(r+4)|32)===114&&(e.charCodeAt(r+5)|32)===97&&(e.charCodeAt(r+6)|32)===108}var u=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]];var h=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function d(e){e[0]="General";e[1]="0";e[2]="0.00";e[3]="#,##0";e[4]="#,##0.00";e[9]="0%";e[10]="0.00%";e[11]="0.00E+00";e[12]="# ?/?";e[13]="# ??/??";e[14]="m/d/yy";e[15]="d-mmm-yy";e[16]="d-mmm";e[17]="mmm-yy";e[18]="h:mm AM/PM";e[19]="h:mm:ss AM/PM";e[20]="h:mm";e[21]="h:mm:ss";e[22]="m/d/yy h:mm";e[37]="#,##0 ;(#,##0)";e[38]="#,##0 ;[Red](#,##0)";e[39]="#,##0.00;(#,##0.00)";e[40]="#,##0.00;[Red](#,##0.00)";e[45]="mm:ss";e[46]="[h]:mm:ss";e[47]="mmss.0";e[48]="##0.0E+0";e[49]="@";e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "'}var v={};d(v);var p=[];var b=0;for(b=5;b<=8;++b)p[b]=32+b;for(b=23;b<=26;++b)p[b]=0;for(b=27;b<=31;++b)p[b]=14;for(b=50;b<=58;++b)p[b]=14;for(b=59;b<=62;++b)p[b]=b-58;for(b=67;b<=68;++b)p[b]=b-58;for(b=72;b<=75;++b)p[b]=b-58;for(b=67;b<=68;++b)p[b]=b-57;for(b=76;b<=78;++b)p[b]=b-56;for(b=79;b<=81;++b)p[b]=b-34;var m=[];m[5]=m[63]='"$"#,##0_);\\("$"#,##0\\)';m[6]=m[64]='"$"#,##0_);[Red]\\("$"#,##0\\)';m[7]=m[65]='"$"#,##0.00_);\\("$"#,##0.00\\)';m[8]=m[66]='"$"#,##0.00_);[Red]\\("$"#,##0.00\\)';m[41]='_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)';m[42]='_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)';m[43]='_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)';m[44]='_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)';function g(e,r,t){var a=e<0?-1:1;var n=e*a;var i=0,s=1,f=0;var l=1,o=0,c=0;var u=Math.floor(n);while(o<r){u=Math.floor(n);f=u*s+i;c=u*o+l;if(n-u<5e-8)break;n=1/(n-u);i=s;s=f;l=o;o=c}if(c>r){if(o>r){c=l;f=i}else{c=o;f=s}}if(!t)return[0,a*f,c];var h=Math.floor(a*f/c);return[h,a*f-h*c,c]}function E(e,r,t){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0;var s=[];var f={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6)f.u=0;if(r&&r.date1904)a+=1462;if(f.u>.9999){f.u=0;if(++n==86400){f.T=n=0;++a;++f.D}}if(a===60){s=t?[1317,10,29]:[1900,2,29];i=3}else if(a===0){s=t?[1317,8,29]:[1900,1,0];i=6}else{if(a>60)--a;var l=new Date(1900,0,1);l.setDate(l.getDate()+a-1);s=[l.getFullYear(),l.getMonth()+1,l.getDate()];i=l.getDay();if(a<60)i=(i+6)%7;if(t)i=x(l,s)}f.y=s[0];f.m=s[1];f.d=s[2];f.S=n%60;n=Math.floor(n/60);f.M=n%60;n=Math.floor(n/60);f.H=n;f.q=i;return f}e.parse_date_code=E;var w=new Date(1899,11,31,0,0,0);var k=w.getTime();var S=new Date(1900,2,1,0,0,0);function B(e,r){var t=e.getTime();if(r)t-=1461*24*60*60*1e3;else if(e>=S)t+=24*60*60*1e3;return(t-(k+(e.getTimezoneOffset()-w.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function C(e){return e.toString(10)}e._general_int=C;var T=function W(){var e=/(?:\.0*|(\.\d*[1-9])0+)$/;function r(r){return r.indexOf(".")==-1?r:r.replace(e,"$1")}var t=/(?:\.0*|(\.\d*[1-9])0+)[Ee]/;var a=/(E[+-])(\d)$/;function n(e){if(e.indexOf("E")==-1)return e;return e.replace(t,"$1E").replace(a,"$10$2")}function i(e){var t=e<0?12:11;var a=r(e.toFixed(12));if(a.length<=t)return a;a=e.toPrecision(10);if(a.length<=t)return a;return e.toExponential(5)}function s(e){var t=r(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function f(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),a;if(t>=-4&&t<=-1)a=e.toPrecision(10+t);else if(Math.abs(t)<=9)a=i(e);else if(t===10)a=e.toFixed(10).substr(0,12);else a=s(e);return r(n(a.toUpperCase()))}return f}();e._general_num=T;function _(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):T(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return U(14,B(e,r&&r.date1904),r);}throw new Error("unsupported value in General format: "+e)}e._general=_;function x(e,r){r[0]-=581;var t=e.getDay();if(e<60)t=(t+6)%7;return t}function A(e,r,t,n){var i="",s=0,f=0,l=t.y,o,c=0;switch(e){case 98:l=t.y+543;case 121:switch(r.length){case 1:;case 2:o=l%100;c=2;break;default:o=l%1e4;c=4;break;}break;case 109:switch(r.length){case 1:;case 2:o=t.m;c=r.length;break;case 3:return h[t.m-1][1];case 5:return h[t.m-1][0];default:return h[t.m-1][2];}break;case 100:switch(r.length){case 1:;case 2:o=t.d;c=r.length;break;case 3:return u[t.q][0];default:return u[t.q][1];}break;case 104:switch(r.length){case 1:;case 2:o=1+(t.H+11)%12;c=r.length;break;default:throw"bad hour format: "+r;}break;case 72:switch(r.length){case 1:;case 2:o=t.H;c=r.length;break;default:throw"bad hour format: "+r;}break;case 77:switch(r.length){case 1:;case 2:o=t.M;c=r.length;break;default:throw"bad minute format: "+r;}break;case 115:if(r!="s"&&r!="ss"&&r!=".0"&&r!=".00"&&r!=".000")throw"bad second format: "+r;if(t.u===0&&(r=="s"||r=="ss"))return a(t.S,r.length);if(n>=2)f=n===3?1e3:100;else f=n===1?10:1;s=Math.round(f*(t.S+t.u));if(s>=60*f)s=0;if(r==="s")return s===0?"0":""+s/f;i=a(s,2+n);if(r==="ss")return i.substr(0,2);return"."+i.substr(2,r.length-1);case 90:switch(r){case"[h]":;case"[hh]":o=t.D*24+t.H;break;case"[m]":;case"[mm]":o=(t.D*24+t.H)*60+t.M;break;case"[s]":;case"[ss]":o=((t.D*24+t.H)*60+t.M)*60+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r;}c=r.length===3?1:2;break;case 101:o=l;c=1;break;}var d=c>0?a(o,c):"";return d}function y(e){var r=3;if(e.length<=r)return e;var t=e.length%r,a=e.substr(0,t);for(;t!=e.length;t+=r)a+=(a.length>0?",":"")+e.substr(t,r);return a}var I=function V(){var e=/%/g;function s(r,a,n){var i=a.replace(e,""),s=a.length-i.length;return I(r,i,n*Math.pow(10,2*s))+t("%",s)}function f(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return I(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function l(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+l(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(t.indexOf("e")===-1){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);while(t.substr(0,2)==="0."){t=t.charAt(0)+t.substr(2,n)+"."+t.substr(2+n);t=t.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.")}t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}var c=/# (\?+)( ?)\/( ?)(\d+)/;function u(e,r,i){var s=parseInt(e[4],10),f=Math.round(r*s),l=Math.floor(f/s);var o=f-l*s,c=s;return i+(l===0?"":""+l)+" "+(o===0?t(" ",e[1].length+1+e[4].length):n(o,e[1].length)+e[2]+"/"+e[3]+a(c,e[4].length))}function h(e,r,a){return a+(r===0?"":""+r)+t(" ",e[1].length+2+e[4].length)}var d=/^#*0*\.([0#]+)/;var v=/\).*[0#]/;var p=/\(###\) ###\\?-####/;function b(e){var r="",t;for(var a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t);}return r}function m(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function E(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);if(r<(""+Math.round(t*a)).length)return 0;return Math.round(t*a)}function w(e,r){if(r<(""+Math.round((e-Math.floor(e))*Math.pow(10,r))).length){return 1}return 0}function k(e){if(e<2147483647&&e>-2147483648)return""+(e>=0?e|0:e-1|0);return""+Math.floor(e)}function S(e,h,B){if(e.charCodeAt(0)===40&&!h.match(v)){var C=h.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(B>=0)return S("n",C,B);return"("+S("n",C,-B)+")"}if(h.charCodeAt(h.length-1)===44)return f(e,h,B);if(h.indexOf("%")!==-1)return s(e,h,B);if(h.indexOf("E")!==-1)return l(h,B);if(h.charCodeAt(0)===36)return"$"+S(e,h.substr(h.charAt(1)==" "?2:1),B);var T;var _,x,A,R=Math.abs(B),F=B<0?"-":"";if(h.match(/^00+$/))return F+o(R,h.length);if(h.match(/^[#?]+$/)){T=o(B,0);if(T==="0")T="";return T.length>h.length?T:b(h.substr(0,h.length-T.length))+T}if(_=h.match(c))return u(_,R,F);if(h.match(/^#+0+$/))return F+o(R,h.length-h.indexOf("0"));if(_=h.match(d)){T=m(B,_[1].length).replace(/^([^\.]+)$/,"$1."+b(_[1])).replace(/\.$/,"."+b(_[1])).replace(/\.(\d*)$/,function(e,r){return"."+r+t("0",b(_[1]).length-r.length)});return h.indexOf("0.")!==-1?T:T.replace(/^0\./,".")}h=h.replace(/^#+([0.])/,"$1");if(_=h.match(/^(0*)\.(#*)$/)){return F+m(R,_[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,_[1].length?"0.":".")}if(_=h.match(/^#{1,3},##0(\.?)$/))return F+y(o(R,0));if(_=h.match(/^#,##0\.([#0]*0)$/)){return B<0?"-"+S(e,h,-B):y(""+(Math.floor(B)+w(B,_[1].length)))+"."+a(E(B,_[1].length),_[1].length)}if(_=h.match(/^#,#*,#0/))return S(e,h.replace(/^#,#*,/,""),B);if(_=h.match(/^([0#]+)(\\?-([0#]+))+$/)){T=r(S(e,h.replace(/[\\-]/g,""),B));x=0;return r(r(h.replace(/\\/g,"")).replace(/[0#]/g,function(e){return x<T.length?T.charAt(x++):e==="0"?"0":""}))}if(h.match(p)){T=S(e,"##########",B);return"("+T.substr(0,3)+") "+T.substr(3,3)+"-"+T.substr(6)}var D="";if(_=h.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){x=Math.min(_[4].length,7);A=g(R,Math.pow(10,x)-1,false);T=""+F;D=I("n",_[1],A[1]);if(D.charAt(D.length-1)==" ")D=D.substr(0,D.length-1)+"0";T+=D+_[2]+"/"+_[3];D=i(A[2],x);if(D.length<_[4].length)D=b(_[4].substr(_[4].length-D.length))+D;T+=D;return T}if(_=h.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){x=Math.min(Math.max(_[1].length,_[4].length),7);A=g(R,Math.pow(10,x)-1,true);return F+(A[0]||(A[1]?"":"0"))+" "+(A[1]?n(A[1],x)+_[2]+"/"+_[3]+i(A[2],x):t(" ",2*x+1+_[2].length+_[3].length))}if(_=h.match(/^[#0?]+$/)){T=o(B,0);if(h.length<=T.length)return T;return b(h.substr(0,h.length-T.length))+T}if(_=h.match(/^([#0?]+)\.([#0]+)$/)){T=""+B.toFixed(Math.min(_[2].length,10)).replace(/([^0])0+$/,"$1");x=T.indexOf(".");var O=h.indexOf(".")-x,P=h.length-T.length-O;return b(h.substr(0,O)+T+h.substr(h.length-P))}if(_=h.match(/^00,000\.([#0]*0)$/)){x=E(B,_[1].length);return B<0?"-"+S(e,h,-B):y(k(B)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?a(0,3-e.length):"")+e})+"."+a(x,_[1].length)}switch(h){case"###,##0.00":return S(e,"#,##0.00",B);case"###,###":;case"##,###":;case"#,###":var N=y(o(R,0));return N!=="0"?F+N:"";case"###,###.00":return S(e,"###,##0.00",B).replace(/^0\./,".");case"#,###.00":return S(e,"#,##0.00",B).replace(/^0\./,".");default:;}throw new Error("unsupported format |"+h+"|")}function B(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return I(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function C(r,a,n){var i=a.replace(e,""),s=a.length-i.length;return I(r,i,n*Math.pow(10,2*s))+t("%",s)}function T(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+T(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(!t.match(/[Ee]/)){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}function _(e,s,f){if(e.charCodeAt(0)===40&&!s.match(v)){var l=s.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(f>=0)return _("n",l,f);return"("+_("n",l,-f)+")"}if(s.charCodeAt(s.length-1)===44)return B(e,s,f);if(s.indexOf("%")!==-1)return C(e,s,f);if(s.indexOf("E")!==-1)return T(s,f);if(s.charCodeAt(0)===36)return"$"+_(e,s.substr(s.charAt(1)==" "?2:1),f);var o;var u,m,E,w=Math.abs(f),k=f<0?"-":"";if(s.match(/^00+$/))return k+a(w,s.length);if(s.match(/^[#?]+$/)){o=""+f;if(f===0)o="";return o.length>s.length?o:b(s.substr(0,s.length-o.length))+o}if(u=s.match(c))return h(u,w,k);if(s.match(/^#+0+$/))return k+a(w,s.length-s.indexOf("0"));if(u=s.match(d)){o=(""+f).replace(/^([^\.]+)$/,"$1."+b(u[1])).replace(/\.$/,"."+b(u[1]));o=o.replace(/\.(\d*)$/,function(e,r){return"."+r+t("0",b(u[1]).length-r.length)});return s.indexOf("0.")!==-1?o:o.replace(/^0\./,".")}s=s.replace(/^#+([0.])/,"$1");if(u=s.match(/^(0*)\.(#*)$/)){return k+(""+w).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".")}if(u=s.match(/^#{1,3},##0(\.?)$/))return k+y(""+w);if(u=s.match(/^#,##0\.([#0]*0)$/)){return f<0?"-"+_(e,s,-f):y(""+f)+"."+t("0",u[1].length)}if(u=s.match(/^#,#*,#0/))return _(e,s.replace(/^#,#*,/,""),f);if(u=s.match(/^([0#]+)(\\?-([0#]+))+$/)){o=r(_(e,s.replace(/[\\-]/g,""),f));m=0;return r(r(s.replace(/\\/g,"")).replace(/[0#]/g,function(e){return m<o.length?o.charAt(m++):e==="0"?"0":""}))}if(s.match(p)){o=_(e,"##########",f);return"("+o.substr(0,3)+") "+o.substr(3,3)+"-"+o.substr(6)}var S="";if(u=s.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){m=Math.min(u[4].length,7);E=g(w,Math.pow(10,m)-1,false);o=""+k;S=I("n",u[1],E[1]);if(S.charAt(S.length-1)==" ")S=S.substr(0,S.length-1)+"0";o+=S+u[2]+"/"+u[3];S=i(E[2],m);if(S.length<u[4].length)S=b(u[4].substr(u[4].length-S.length))+S;o+=S;return o}if(u=s.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){m=Math.min(Math.max(u[1].length,u[4].length),7);E=g(w,Math.pow(10,m)-1,true);return k+(E[0]||(E[1]?"":"0"))+" "+(E[1]?n(E[1],m)+u[2]+"/"+u[3]+i(E[2],m):t(" ",2*m+1+u[2].length+u[3].length))}if(u=s.match(/^[#0?]+$/)){o=""+f;if(s.length<=o.length)return o;return b(s.substr(0,s.length-o.length))+o}if(u=s.match(/^([#0]+)\.([#0]+)$/)){o=""+f.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1");m=o.indexOf(".");var x=s.indexOf(".")-m,A=s.length-o.length-x;return b(s.substr(0,x)+o+s.substr(s.length-A))}if(u=s.match(/^00,000\.([#0]*0)$/)){return f<0?"-"+_(e,s,-f):y(""+f).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?a(0,3-e.length):"")+e})+"."+a(0,u[1].length)}switch(s){case"###,###":;case"##,###":;case"#,###":var R=y(""+w);return R!=="0"?k+R:"";default:if(s.match(/\.[0#?]*$/))return _(e,s.slice(0,s.lastIndexOf(".")),f)+b(s.slice(s.lastIndexOf(".")));}throw new Error("unsupported format |"+s+"|")}return function x(e,r,t){return(t|0)===t?_(e,r,t):S(e,r,t)}}();function R(e){var r=[];var t=false;for(var a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:;case 42:;case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n);n=a+1;}r[r.length]=e.substr(n);if(t===true)throw new Error("Format |"+e+"| unterminated string ");return r}e._split=R;var F=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function D(e){var r=0,t="",a="";while(r<e.length){switch(t=e.charAt(r)){case"G":if(c(e,r))r+=6;r++;break;case'"':for(;e.charCodeAt(++r)!==34&&r<e.length;){}++r;break;case"\\":r+=2;break;case"_":r+=2;break;case"@":++r;break;case"B":;case"b":if(e.charAt(r+1)==="1"||e.charAt(r+1)==="2")return true;case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":;case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":return true;case"A":;case"a":;case"上":if(e.substr(r,3).toUpperCase()==="A/P")return true;if(e.substr(r,5).toUpperCase()==="AM/PM")return true;if(e.substr(r,5).toUpperCase()==="上午/下午")return true;++r;break;case"[":a=t;while(e.charAt(r++)!=="]"&&r<e.length)a+=e.charAt(r);if(a.match(F))return true;break;case".":;case"0":;case"#":while(r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||t=="\\"&&e.charAt(r+1)=="-"&&"0#".indexOf(e.charAt(r+2))>-1)){}break;case"?":while(e.charAt(++r)===t){}break;case"*":++r;if(e.charAt(r)==" "||e.charAt(r)=="*")++r;break;case"(":;case")":++r;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":while(r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1){}break;case" ":++r;break;default:++r;break;}}return false}e.is_date=D;function O(e,r,t,a){var n=[],i="",s=0,f="",l="t",o,u,h;var d="H";while(s<e.length){switch(f=e.charAt(s)){case"G":if(!c(e,s))throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"G",v:"General"};s+=7;break;case'"':for(i="";(h=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(h);n[n.length]={t:"t",v:i};++s;break;case"\\":var v=e.charAt(++s),p=v==="("||v===")"?v:"t";n[n.length]={t:p,v:v};++s;break;case"_":n[n.length]={t:"t",v:" "};s+=2;break;case"@":n[n.length]={t:"T",v:r};++s;break;case"B":;case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(o==null){o=E(r,t,e.charAt(s+1)==="2");if(o==null)return""}n[n.length]={t:"X",v:e.substr(s,2)};l=f;s+=2;break};case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":f=f.toLowerCase();case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":if(r<0)return"";if(o==null){o=E(r,t);if(o==null)return""}i=f;while(++s<e.length&&e.charAt(s).toLowerCase()===f)i+=f;if(f==="m"&&l.toLowerCase()==="h")f="M";if(f==="h")f=d;n[n.length]={t:f,v:i};l=f;break;case"A":;case"a":;case"上":var b={t:f,v:f};if(o==null)o=E(r,t);if(e.substr(s,3).toUpperCase()==="A/P"){if(o!=null)b.v=o.H>=12?"P":"A";b.t="T";d="h";s+=3}else if(e.substr(s,5).toUpperCase()==="AM/PM"){if(o!=null)b.v=o.H>=12?"PM":"AM";b.t="T";s+=5;d="h"}else if(e.substr(s,5).toUpperCase()==="上午/下午"){if(o!=null)b.v=o.H>=12?"下午":"上午";b.t="T";s+=5;d="h"}else{b.t="t";++s}if(o==null&&b.t==="T")return"";n[n.length]=b;l=f;break;case"[":i=f;while(e.charAt(s++)!=="]"&&s<e.length)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(F)){if(o==null){o=E(r,t);if(o==null)return""}n[n.length]={t:"Z",v:i.toLowerCase()};l=i.charAt(1)}else if(i.indexOf("$")>-1){i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$";if(!D(e))n[n.length]={t:"t",v:i}}break;case".":if(o!=null){i=f;while(++s<e.length&&(f=e.charAt(s))==="0")i+=f;n[n.length]={t:"s",v:i};break};case"0":;case"#":i=f;while(++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1)i+=f;n[n.length]={t:"n",v:i};break;case"?":i=f;while(e.charAt(++s)===f)i+=f;n[n.length]={t:f,v:i};l=f;break;case"*":++s;if(e.charAt(s)==" "||e.charAt(s)=="*")++s;break;case"(":;case")":n[n.length]={t:a===1?"t":f,v:f};++s;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":i=f;while(s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:f,v:f};++s;break;case"$":n[n.length]={t:"t",v:"$"};++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"t",v:f};++s;break;}}var m=0,g=0,w;for(s=n.length-1,l="t";s>=0;--s){switch(n[s].t){case"h":;case"H":n[s].t=d;l="h";if(m<1)m=1;break;case"s":if(w=n[s].v.match(/\.0+$/))g=Math.max(g,w[0].length-1);if(m<3)m=3;case"d":;case"y":;case"M":;case"e":l=n[s].t;break;case"m":if(l==="s"){n[s].t="M";if(m<2)m=2}break;case"X":break;case"Z":if(m<1&&n[s].v.match(/[Hh]/))m=1;if(m<2&&n[s].v.match(/[Mm]/))m=2;if(m<3&&n[s].v.match(/[Ss]/))m=3;}}switch(m){case 0:break;case 1:if(o.u>=.5){o.u=0;++o.S}if(o.S>=60){o.S=0;++o.M}if(o.M>=60){o.M=0;++o.H}break;case 2:if(o.u>=.5){o.u=0;++o.S}if(o.S>=60){o.S=0;++o.M}break;}var k="",S;for(s=0;s<n.length;++s){switch(n[s].t){case"t":;case"T":;case" ":;case"D":break;case"X":n[s].v="";n[s].t=";";break;case"d":;case"m":;case"y":;case"h":;case"H":;case"M":;case"s":;case"e":;case"b":;case"Z":n[s].v=A(n[s].t.charCodeAt(0),n[s].v,o,g);n[s].t="t";break;case"n":;case"?":S=s+1;while(n[S]!=null&&((f=n[S].t)==="?"||f==="D"||(f===" "||f==="t")&&n[S+1]!=null&&(n[S+1].t==="?"||n[S+1].t==="t"&&n[S+1].v==="/")||n[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(n[S].v==="/"||n[S].v===" "&&n[S+1]!=null&&n[S+1].t=="?"))){n[s].v+=n[S].v;n[S]={v:"",t:";"};++S}k+=n[s].v;s=S-1;break;case"G":n[s].t="t";n[s].v=_(r,t);break;}}var B="",C,T;if(k.length>0){if(k.charCodeAt(0)==40){C=r<0&&k.charCodeAt(0)===45?-r:r;T=I("n",k,C)}else{C=r<0&&a>1?-r:r;T=I("n",k,C);if(C<0&&n[0]&&n[0].t=="t"){T=T.substr(1);n[0].v="-"+n[0].v}}S=T.length-1;var x=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){x=s;break}var y=n.length;if(x===n.length&&T.indexOf("E")===-1){for(s=n.length-1;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;if(S>=n[s].v.length-1){S-=n[s].v.length;n[s].v=T.substr(S+1,n[s].v.length)}else if(S<0)n[s].v="";else{n[s].v=T.substr(0,S+1);S=-1}n[s].t="t";y=s}if(S>=0&&y<n.length)n[y].v=T.substr(0,S+1)+n[y].v}else if(x!==n.length&&T.indexOf("E")===-1){S=T.indexOf(".")-1;for(s=x;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;u=n[s].v.indexOf(".")>-1&&s===x?n[s].v.indexOf(".")-1:n[s].v.length-1;B=n[s].v.substr(u+1);for(;u>=0;--u){if(S>=0&&(n[s].v.charAt(u)==="0"||n[s].v.charAt(u)==="#"))B=T.charAt(S--)+B}n[s].v=B;n[s].t="t";y=s}if(S>=0&&y<n.length)n[y].v=T.substr(0,S+1)+n[y].v;S=T.indexOf(".")+1;for(s=x;s<n.length;++s){if(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==x)continue;u=n[s].v.indexOf(".")>-1&&s===x?n[s].v.indexOf(".")+1:0;B=n[s].v.substr(0,u);for(;u<n[s].v.length;++u){if(S<T.length)B+=T.charAt(S++)}n[s].v=B;n[s].t="t";y=s}}}for(s=0;s<n.length;++s)if(n[s]!=null&&"n?".indexOf(n[s].t)>-1){C=a>1&&r<0&&s>0&&n[s-1].v==="-"?-r:r;n[s].v=I(n[s].t,n[s].v,C);n[s].t="t"}var R="";for(s=0;s!==n.length;++s)if(n[s]!=null)R+=n[s].v;return R}e._eval=O;var P=/\[[=<>]/;var N=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function M(e,r){if(r==null)return false;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return true;break;case">":if(e>t)return true;break;case"<":if(e<t)return true;break;case"<>":if(e!=t)return true;break;case">=":if(e>=t)return true;break;case"<=":if(e<=t)return true;break;}return false}function L(e,r){var t=R(e);var a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1)--a;if(t.length>4)throw new Error("cannot find right format for |"+t.join("|")+"|");if(typeof r!=="number")return[4,t.length===4||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"];break;case 4:break;}var i=r>0?t[0]:r<0?t[1]:t[2];if(t[0].indexOf("[")===-1&&t[1].indexOf("[")===-1)return[a,i];if(t[0].match(P)!=null||t[1].match(P)!=null){var s=t[0].match(N);var f=t[1].match(N);return M(r,s)?[a,t[0]]:M(r,f)?[a,t[1]]:[a,t[s!=null&&f!=null?2:1]]}return[a,i]}function U(e,r,t){if(t==null)t={};var a="";switch(typeof e){case"string":if(e=="m/d/yy"&&t.dateNF)a=t.dateNF;else a=e;break;case"number":if(e==14&&t.dateNF)a=t.dateNF;else a=(t.table!=null?t.table:v)[e];if(a==null)a=t.table&&t.table[p[e]]||v[p[e]];if(a==null)a=m[e]||"General";break;}if(c(a,0))return _(r,t);if(r instanceof Date)r=B(r,t.date1904);var n=L(a,r);if(c(n[1]))return _(r,t);if(r===true)r="TRUE";else if(r===false)r="FALSE";else if(r===""||r==null)return"";return O(n[1],r,t,n[0])}function H(e,r){if(typeof r!="number"){r=+r||-1;for(var t=0;t<392;++t){if(v[t]==undefined){if(r<0)r=t;continue}if(v[t]==e){r=t;break}}if(r<0)r=391}v[r]=e;return r}e.load=H;e._table=v;e.get_table=function X(){return v};e.load_table=function G(e){for(var r=0;r!=392;++r)if(e[r]!==undefined)H(e[r],r)};e.init_table=d;e.format=U};O(D);var P={"General Number":"General","General Date":D._table[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":D._table[15],"Short Date":D._table[14],"Long Time":D._table[19],"Medium Time":D._table[18],"Short Time":D._table[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:D._table[2],Standard:D._table[4],Percent:D._table[10],Scientific:D._table[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var N={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"};var M=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function L(e){var r=typeof e=="number"?D._table[e]:e;r=r.replace(M,"(\\d+)");return new RegExp("^"+r+"$")}function U(e,r,t){var a=-1,n=-1,i=-1,s=-1,f=-1,l=-1;(r.match(M)||[]).forEach(function(e,r){var o=parseInt(t[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=o;break;case"d":i=o;break;case"h":s=o;break;case"s":l=o;break;case"m":if(s>=0)f=o;else n=o;break;}});if(l>=0&&f==-1&&n>=0){f=n;n=-1}var o=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);if(o.length==7)o="0"+o;if(o.length==8)o="20"+o;var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);if(s==-1&&f==-1&&l==-1)return o;if(a==-1&&n==-1&&i==-1)return c;return o+"T"+c}var H=true;var W;(function(e){e(W={})})(function(e){e.version="1.2.0";function r(){var e=0,r=new Array(256);for(var t=0;t!=256;++t){e=t;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;r[t]=e}return typeof Int32Array!=="undefined"?new Int32Array(r):r}var t=r();function a(e,r){var a=r^-1,n=e.length-1;for(var i=0;i<n;){a=a>>>8^t[(a^e.charCodeAt(i++))&255];a=a>>>8^t[(a^e.charCodeAt(i++))&255]}if(i===n)a=a>>>8^t[(a^e.charCodeAt(i))&255];return a^-1}function n(e,r){if(e.length>1e4)return i(e,r);var a=r^-1,n=e.length-3;for(var s=0;s<n;){a=a>>>8^t[(a^e[s++])&255];a=a>>>8^t[(a^e[s++])&255];a=a>>>8^t[(a^e[s++])&255];a=a>>>8^t[(a^e[s++])&255]}while(s<n+3)a=a>>>8^t[(a^e[s++])&255];return a^-1}function i(e,r){var a=r^-1,n=e.length-7;for(var i=0;i<n;){a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255];a=a>>>8^t[(a^e[i++])&255]}while(i<n+7)a=a>>>8^t[(a^e[i++])&255];return a^-1}function s(e,r){var a=r^-1;for(var n=0,i=e.length,s,f;n<i;){s=e.charCodeAt(n++);if(s<128){a=a>>>8^t[(a^s)&255]}else if(s<2048){a=a>>>8^t[(a^(192|s>>6&31))&255];a=a>>>8^t[(a^(128|s&63))&255]}else if(s>=55296&&s<57344){s=(s&1023)+64;f=e.charCodeAt(n++)&1023;a=a>>>8^t[(a^(240|s>>8&7))&255];a=a>>>8^t[(a^(128|s>>2&63))&255];a=a>>>8^t[(a^(128|f>>6&15|(s&3)<<4))&255];
a=a>>>8^t[(a^(128|f&63))&255]}else{a=a>>>8^t[(a^(224|s>>12&15))&255];a=a>>>8^t[(a^(128|s>>6&63))&255];a=a>>>8^t[(a^(128|s&63))&255]}}return a^-1}e.table=t;e.bstr=a;e.buf=n;e.str=s});var V=function Lm(){var e={};e.version="1.1.4";function r(e,r){var t=e.split("/"),a=r.split("/");for(var n=0,i=0,s=Math.min(t.length,a.length);n<s;++n){if(i=t[n].length-a[n].length)return i;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}function t(e){if(e.charAt(e.length-1)=="/")return e.slice(0,-1).indexOf("/")===-1?e:t(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(0,r+1)}function a(e){if(e.charAt(e.length-1)=="/")return a(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(r+1)}function n(e,r){if(typeof r==="string")r=new Date(r);var t=r.getHours();t=t<<6|r.getMinutes();t=t<<5|r.getSeconds()>>>1;e._W(2,t);var a=r.getFullYear()-1980;a=a<<4|r.getMonth()+1;a=a<<5|r.getDate();e._W(2,a)}function i(e){var r=e._R(2)&65535;var t=e._R(2)&65535;var a=new Date;var n=t&31;t>>>=5;var i=t&15;t>>>=4;a.setMilliseconds(0);a.setFullYear(t+1980);a.setMonth(i-1);a.setDate(n);var s=r&31;r>>>=5;var f=r&63;r>>>=6;a.setHours(r);a.setMinutes(f);a.setSeconds(s<<1);return a}function s(e){Yr(e,0);var r={};var t=0;while(e.l<=e.length-4){var a=e._R(2);var n=e._R(2),i=e.l+n;var s={};switch(a){case 21589:{t=e._R(1);if(t&1)s.mtime=e._R(4);if(n>5){if(t&2)s.atime=e._R(4);if(t&4)s.ctime=e._R(4)}if(s.mtime)s.mt=new Date(s.mtime*1e3)}break;}e.l=i;r[a]=s}return r}var f;function l(){return f||(f=require("fs"))}function o(e,r){if(e[0]==80&&e[1]==75)return _e(e,r);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t=3;var a=512;var n=0;var i=0;var s=0;var f=0;var l=0;var o=[];var v=e.slice(0,512);Yr(v,0);var b=c(v);t=b[0];switch(t){case 3:a=512;break;case 4:a=4096;break;case 0:if(b[1]==0)return _e(e,r);default:throw new Error("Major Version: Expected 3 or 4 saw "+t);}if(a!==512){v=e.slice(0,a);Yr(v,28)}var g=e.slice(0,a);u(v,t);var E=v._R(4,"i");if(t===3&&E!==0)throw new Error("# Directory Sectors: Expected 0 saw "+E);v.l+=4;s=v._R(4,"i");v.l+=4;v.chk("00100000","Mini Stream Cutoff Size: ");f=v._R(4,"i");n=v._R(4,"i");l=v._R(4,"i");i=v._R(4,"i");for(var k=-1,S=0;S<109;++S){k=v._R(4,"i");if(k<0)break;o[S]=k}var B=h(e,a);p(l,i,B,a,o);var C=m(B,s,o,a);C[s].name="!Directory";if(n>0&&f!==N)C[f].name="!MiniFAT";C[o[0]].name="!FAT";C.fat_addrs=o;C.ssz=a;var T={},_=[],x=[],A=[];w(s,C,B,_,n,T,x,f);d(x,A,_);_.shift();var y={FileIndex:x,FullPaths:A};if(r&&r.raw)y.raw={header:g,sectors:B};return y}function c(e){if(e[e.l]==80&&e[e.l+1]==75)return[0,0];e.chk(M,"Header Signature: ");e.l+=16;var r=e._R(2,"u");return[e._R(2,"u"),r]}function u(e,r){var t=9;e.l+=2;switch(t=e._R(2)){case 9:if(r!=3)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(r!=4)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t);}e.chk("0600","Mini Sector Shift: ");e.chk("000000000000","Reserved: ")}function h(e,r){var t=Math.ceil(e.length/r)-1;var a=[];for(var n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);a[t-1]=e.slice(t*r);return a}function d(e,r,t){var a=0,n=0,i=0,s=0,f=0,l=t.length;var o=[],c=[];for(;a<l;++a){o[a]=c[a]=a;r[a]=t[a]}for(;f<c.length;++f){a=c[f];n=e[a].L;i=e[a].R;s=e[a].C;if(o[a]===a){if(n!==-1&&o[n]!==n)o[a]=o[n];if(i!==-1&&o[i]!==i)o[a]=o[i]}if(s!==-1)o[s]=a;if(n!==-1&&a!=o[a]){o[n]=o[a];if(c.lastIndexOf(n)<f)c.push(n)}if(i!==-1&&a!=o[a]){o[i]=o[a];if(c.lastIndexOf(i)<f)c.push(i)}}for(a=1;a<l;++a)if(o[a]===a){if(i!==-1&&o[i]!==i)o[a]=o[i];else if(n!==-1&&o[n]!==n)o[a]=o[n]}for(a=1;a<l;++a){if(e[a].type===0)continue;f=a;if(f!=o[f])do{f=o[f];r[a]=r[f]+"/"+r[a]}while(f!==0&&-1!==o[f]&&f!=o[f]);o[a]=-1}r[0]+="/";for(a=1;a<l;++a){if(e[a].type!==2)r[a]+="/"}}function v(e,r,t){var a=e.start,n=e.size;var i=[];var s=a;while(t&&n>0&&s>=0){i.push(r.slice(s*P,s*P+P));n-=P;s=Wr(t,s*4)}if(i.length===0)return Jr(0);return I(i).slice(0,e.size)}function p(e,r,t,a,n){var i=N;if(e===N){if(r!==0)throw new Error("DIFAT chain shorter than expected")}else if(e!==-1){var s=t[e],f=(a>>>2)-1;if(!s)return;for(var l=0;l<f;++l){if((i=Wr(s,l*4))===N)break;n.push(i)}if(r>=1)p(Wr(s,a-4),r-1,t,a,n)}}function b(e,r,t,a,n){var i=[],s=[];if(!n)n=[];var f=a-1,l=0,o=0;for(l=r;l>=0;){n[l]=true;i[i.length]=l;s.push(e[l]);var c=t[Math.floor(l*4/a)];o=l*4&f;if(a<4+o)throw new Error("FAT boundary crossed: "+l+" 4 "+a);if(!e[c])break;l=Wr(e[c],o)}return{nodes:i,data:br([s])}}function m(e,r,t,a){var n=e.length,i=[];var s=[],f=[],l=[];var o=a-1,c=0,u=0,h=0,d=0;for(c=0;c<n;++c){f=[];h=c+r;if(h>=n)h-=n;if(s[h])continue;l=[];var v=[];for(u=h;u>=0;){v[u]=true;s[u]=true;f[f.length]=u;l.push(e[u]);var p=t[Math.floor(u*4/a)];d=u*4&o;if(a<4+d)throw new Error("FAT boundary crossed: "+u+" 4 "+a);if(!e[p])break;u=Wr(e[p],d);if(v[u])break}i[h]={nodes:f,data:br([l])}}return i}function w(e,r,t,a,n,i,s,f){var l=0,o=a.length?2:0;var c=r[e].data;var u=0,h=0,d;for(;u<c.length;u+=128){var p=c.slice(u,u+128);Yr(p,64);h=p._R(2);d=gr(p,0,h-o);a.push(d);var m={name:d,type:p._R(1),color:p._R(1),L:p._R(4,"i"),R:p._R(4,"i"),C:p._R(4,"i"),clsid:p._R(16),state:p._R(4,"i"),start:0,size:0};var g=p._R(2)+p._R(2)+p._R(2)+p._R(2);if(g!==0)m.ct=k(p,p.l-8);var E=p._R(2)+p._R(2)+p._R(2)+p._R(2);if(E!==0)m.mt=k(p,p.l-8);m.start=p._R(4,"i");m.size=p._R(4,"i");if(m.size<0&&m.start<0){m.size=m.type=0;m.start=N;m.name=""}if(m.type===5){l=m.start;if(n>0&&l!==N)r[l].name="!StreamData"}else if(m.size>=4096){m.storage="fat";if(r[m.start]===undefined)r[m.start]=b(t,m.start,r.fat_addrs,r.ssz);r[m.start].name=m.name;m.content=r[m.start].data.slice(0,m.size)}else{m.storage="minifat";if(m.size<0)m.size=0;else if(l!==N&&m.start!==N&&r[l]){m.content=v(m,r[l].data,(r[f]||{}).data)}}if(m.content)Yr(m.content,0);i[d]=m;s.push(m)}}function k(e,r){return new Date((Hr(e,r+4)/1e7*Math.pow(2,32)+Hr(e,r)/1e7-11644473600)*1e3)}function S(e,r){l();return o(f.readFileSync(e),r)}function _(e,r){switch(r&&r.type||"base64"){case"file":return S(e,r);case"base64":return o(T(g.decode(e)),r);case"binary":return o(T(e),r);}return o(e,r)}function x(e,r){var t=r||{},a=t.root||"Root Entry";if(!e.FullPaths)e.FullPaths=[];if(!e.FileIndex)e.FileIndex=[];if(e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");if(e.FullPaths.length===0){e.FullPaths[0]=a+"/";e.FileIndex[0]={name:a,type:5}}if(t.CLSID)e.FileIndex[0].clsid=t.CLSID;A(e)}function A(e){var r="Sh33tJ5";if(V.find(e,"/"+r))return;var t=Jr(4);t[0]=55;t[1]=t[3]=50;t[2]=54;e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69});e.FullPaths.push(e.FullPaths[0]+r);y(e)}function y(e,n){x(e);var i=false,s=false;for(var f=e.FullPaths.length-1;f>=0;--f){var l=e.FileIndex[f];switch(l.type){case 0:if(s)i=true;else{e.FileIndex.pop();e.FullPaths.pop()}break;case 1:;case 2:;case 5:s=true;if(isNaN(l.R*l.L*l.C))i=true;if(l.R>-1&&l.L>-1&&l.R==l.L)i=true;break;default:i=true;break;}}if(!i&&!n)return;var o=new Date(1987,1,19),c=0;var u=[];for(f=0;f<e.FullPaths.length;++f){if(e.FileIndex[f].type===0)continue;u.push([e.FullPaths[f],e.FileIndex[f]])}for(f=0;f<u.length;++f){var h=t(u[f][0]);s=false;for(c=0;c<u.length;++c)if(u[c][0]===h)s=true;if(!s)u.push([h,{name:a(h).replace("/",""),type:1,clsid:U,ct:o,mt:o,content:null}])}u.sort(function(e,t){return r(e[0],t[0])});e.FullPaths=[];e.FileIndex=[];for(f=0;f<u.length;++f){e.FullPaths[f]=u[f][0];e.FileIndex[f]=u[f][1]}for(f=0;f<u.length;++f){var d=e.FileIndex[f];var v=e.FullPaths[f];d.name=a(v).replace("/","");d.L=d.R=d.C=-(d.color=1);d.size=d.content?d.content.length:0;d.start=0;d.clsid=d.clsid||U;if(f===0){d.C=u.length>1?1:-1;d.size=0;d.type=5}else if(v.slice(-1)=="/"){for(c=f+1;c<u.length;++c)if(t(e.FullPaths[c])==v)break;d.C=c>=u.length?-1:c;for(c=f+1;c<u.length;++c)if(t(e.FullPaths[c])==t(v))break;d.R=c>=u.length?-1:c;d.type=1}else{if(t(e.FullPaths[f+1]||"")==t(v))d.R=f+1;d.type=2}}}function D(e,r){var t=r||{};y(e);if(t.fileType=="zip")return Ae(e,t);var a=function(e){var r=0,t=0;for(var a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(!n.content)continue;var i=n.content.length;if(i>0){if(i<4096)r+=i+63>>6;else t+=i+511>>9}}var s=e.FullPaths.length+3>>2;var f=r+7>>3;var l=r+127>>7;var o=f+t+s+l;var c=o+127>>7;var u=c<=109?0:Math.ceil((c-109)/127);while(o+c+u+127>>7>c)u=++c<=109?0:Math.ceil((c-109)/127);var h=[1,u,c,l,s,t,r,0];e.FileIndex[0].size=r<<6;h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3);return h}(e);var n=Jr(a[7]<<9);var i=0,s=0;{for(i=0;i<8;++i)n._W(1,L[i]);for(i=0;i<8;++i)n._W(2,0);n._W(2,62);n._W(2,3);n._W(2,65534);n._W(2,9);n._W(2,6);for(i=0;i<3;++i)n._W(2,0);n._W(4,0);n._W(4,a[2]);n._W(4,a[0]+a[1]+a[2]+a[3]-1);n._W(4,0);n._W(4,1<<12);n._W(4,a[3]?a[0]+a[1]+a[2]-1:N);n._W(4,a[3]);n._W(-4,a[1]?a[0]-1:N);n._W(4,a[1]);for(i=0;i<109;++i)n._W(-4,i<a[2]?a[1]+i:-1)}if(a[1]){for(s=0;s<a[1];++s){for(;i<236+s*127;++i)n._W(-4,i<a[2]?a[1]+i:-1);n._W(-4,s===a[1]-1?N:s+1)}}var f=function(e){for(s+=e;i<s-1;++i)n._W(-4,i+1);if(e){++i;n._W(-4,N)}};s=i=0;for(s+=a[1];i<s;++i)n._W(-4,H.DIFSECT);for(s+=a[2];i<s;++i)n._W(-4,H.FATSECT);f(a[3]);f(a[4]);var l=0,o=0;var c=e.FileIndex[0];for(;l<e.FileIndex.length;++l){c=e.FileIndex[l];if(!c.content)continue;o=c.content.length;if(o<4096)continue;c.start=s;f(o+511>>9)}f(a[6]+7>>3);while(n.l&511)n._W(-4,H.ENDOFCHAIN);s=i=0;for(l=0;l<e.FileIndex.length;++l){c=e.FileIndex[l];if(!c.content)continue;o=c.content.length;if(!o||o>=4096)continue;c.start=s;f(o+63>>6)}while(n.l&511)n._W(-4,H.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(!u||u.length===0){for(l=0;l<17;++l)n._W(4,0);for(l=0;l<3;++l)n._W(4,-1);for(l=0;l<12;++l)n._W(4,0);continue}c=e.FileIndex[i];if(i===0)c.start=c.size?c.start-1:N;var h=i===0&&t.root||c.name;o=2*(h.length+1);n._W(64,h,"utf16le");n._W(2,o);n._W(1,c.type);n._W(1,c.color);n._W(-4,c.L);n._W(-4,c.R);n._W(-4,c.C);if(!c.clsid)for(l=0;l<4;++l)n._W(4,0);else n._W(16,c.clsid,"hex");n._W(4,c.state||0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,c.start);n._W(4,c.size);n._W(4,0)}for(i=1;i<e.FileIndex.length;++i){c=e.FileIndex[i];if(c.size>=4096){n.l=c.start+1<<9;for(l=0;l<c.size;++l)n._W(1,c.content[l]);for(;l&511;++l)n._W(1,0)}}for(i=1;i<e.FileIndex.length;++i){c=e.FileIndex[i];if(c.size>0&&c.size<4096){for(l=0;l<c.size;++l)n._W(1,c.content[l]);for(;l&63;++l)n._W(1,0)}}while(n.l<n.length)n._W(1,0);return n}function O(e,r){var t=e.FullPaths.map(function(e){return e.toUpperCase()});var a=t.map(function(e){var r=e.split("/");return r[r.length-(e.slice(-1)=="/"?2:1)]});var n=false;if(r.charCodeAt(0)===47){n=true;r=t[0].slice(0,-1)+r}else n=r.indexOf("/")!==-1;var i=r.toUpperCase();var s=n===true?t.indexOf(i):a.indexOf(i);if(s!==-1)return e.FileIndex[s];var f=!i.match(F);i=i.replace(R,"");if(f)i=i.replace(F,"!");for(s=0;s<t.length;++s){if((f?t[s].replace(F,"!"):t[s]).replace(R,"")==i)return e.FileIndex[s];if((f?a[s].replace(F,"!"):a[s]).replace(R,"")==i)return e.FileIndex[s]}return null}var P=64;var N=-2;var M="d0cf11e0a1b11ae1";var L=[208,207,17,224,161,177,26,225];var U="00000000000000000000000000000000";var H={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:N,FREESECT:-1,HEADER_SIGNATURE:M,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:U,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function X(e,r,t){l();var a=D(e,t);f.writeFileSync(r,a)}function G(e){var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function z(e,r){var t=D(e,r);switch(r&&r.type){case"file":l();f.writeFileSync(r.filename,t);return t;case"binary":return G(t);case"base64":return g.encode(G(t));}return t}var j;function $(e){try{var r=e.InflateRaw;var t=new r;t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag);if(t.bytesRead)j=e;else throw new Error("zlib does not expose bytesRead")}catch(a){console.error("cannot use native zlib: "+(a.message||a))}}function K(e,r){if(!j)return Ce(e,r);var t=j.InflateRaw;var a=new t;var n=a._processChunk(e.slice(e.l),a._finishFlushFlag);e.l+=a.bytesRead;return n}function Y(e){return j?j.deflateRawSync(e):he(e)}var Q=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];var J=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258];var q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Z(e){var r=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(r>>16|r>>8|r)&255}var ee=typeof Uint8Array!=="undefined";var re=ee?new Uint8Array(1<<8):[];for(var te=0;te<1<<8;++te)re[te]=Z(te);function ae(e,r){var t=re[e&255];if(r<=8)return t>>>8-r;t=t<<8|re[e>>8&255];if(r<=16)return t>>>16-r;t=t<<8|re[e>>16&255];return t>>>24-r}function ne(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}function ie(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function se(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=4?0:e[a+1]<<8))>>>t&15}function fe(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function le(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function oe(e,r,t){var a=r&7,n=r>>>3,i=(1<<t)-1;var s=e[n]>>>a;if(t<8-a)return s&i;s|=e[n+1]<<8-a;if(t<16-a)return s&i;s|=e[n+2]<<16-a;if(t<24-a)return s&i;s|=e[n+3]<<24-a;return s&i}function ce(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(E){var i=C(a);if(e.copy)e.copy(i);else for(;n<e.length;++n)i[n]=e[n];return i}else if(ee){var s=new Uint8Array(a);if(s.set)s.set(e);else for(;n<e.length;++n)s[n]=e[n];return s}e.length=a;return e}function ue(e){var r=new Array(e);for(var t=0;t<e;++t)r[t]=0;return r}var he=function(){var e=function(){return function e(r,t){var a=0;while(a<r.length){var n=Math.min(65535,r.length-a);var i=a+n==r.length;t._W(1,+i);t._W(2,n);t._W(2,~n&65535);while(n-- >0)t[t.l++]=r[a++]}return t.l}}();return function(r){var t=Jr(50+Math.floor(r.length*1.1));var a=e(r,t);return t.slice(0,a)}}();function de(e,r,t){var a=1,n=0,i=0,s=0,f=0,l=e.length;var o=ee?new Uint16Array(32):ue(32);for(i=0;i<32;++i)o[i]=0;for(i=l;i<t;++i)e[i]=0;l=e.length;var c=ee?new Uint16Array(l):ue(l);for(i=0;i<l;++i){o[n=e[i]]++;if(a<n)a=n;c[i]=0}o[0]=0;for(i=1;i<=a;++i)o[i+16]=f=f+o[i-1]<<1;for(i=0;i<l;++i){f=e[i];if(f!=0)c[i]=o[f+16]++}var u=0;for(i=0;i<l;++i){u=e[i];if(u!=0){f=ae(c[i],a)>>a-u;for(s=(1<<a+4-u)-1;s>=0;--s)r[f|s<<u]=u&15|i<<4}}return a}var ve=ee?new Uint16Array(512):ue(512);var pe=ee?new Uint16Array(32):ue(32);if(!ee){for(var be=0;be<512;++be)ve[be]=0;for(be=0;be<32;++be)pe[be]=0}(function(){var e=[];var r=0;for(;r<32;r++)e.push(5);de(e,pe,32);var t=[];r=0;for(;r<=143;r++)t.push(8);for(;r<=255;r++)t.push(9);for(;r<=279;r++)t.push(7);for(;r<=287;r++)t.push(8);de(t,ve,288)})();var me=ee?new Uint16Array(32768):ue(32768);var ge=ee?new Uint16Array(32768):ue(32768);var Ee=ee?new Uint16Array(128):ue(128);var we=1,ke=1;function Se(e,r){var t=fe(e,r)+257;r+=5;var a=fe(e,r)+1;r+=5;var n=se(e,r)+4;r+=4;var i=0;var s=ee?new Uint8Array(19):ue(19);var f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var l=1;var o=ee?new Uint8Array(8):ue(8);var c=ee?new Uint8Array(8):ue(8);var u=s.length;for(var h=0;h<n;++h){s[Q[h]]=i=ie(e,r);if(l<i)l=i;o[i]++;r+=3}var d=0;o[0]=0;for(h=1;h<=l;++h)c[h]=d=d+o[h-1]<<1;for(h=0;h<u;++h)if((d=s[h])!=0)f[h]=c[d]++;var v=0;for(h=0;h<u;++h){v=s[h];if(v!=0){d=re[f[h]]>>8-v;for(var p=(1<<7-v)-1;p>=0;--p)Ee[d|p<<v]=v&7|h<<3}}var b=[];l=1;for(;b.length<t+a;){d=Ee[le(e,r)];r+=d&7;switch(d>>>=3){case 16:i=3+ne(e,r);r+=2;d=b[b.length-1];while(i-- >0)b.push(d);break;case 17:i=3+ie(e,r);r+=3;while(i-- >0)b.push(0);break;case 18:i=11+le(e,r);r+=7;while(i-- >0)b.push(0);break;default:b.push(d);if(l<d)l=d;break;}}var m=b.slice(0,t),g=b.slice(t);for(h=t;h<286;++h)m[h]=0;for(h=a;h<30;++h)g[h]=0;we=de(m,me,286);ke=de(g,ge,30);return r}function Be(e,r){if(e[0]==3&&!(e[1]&3)){return[B(r),2]}var t=0;var a=0;var n=C(r?r:1<<18);var i=0;var s=n.length>>>0;var f=0,l=0;while((a&1)==0){a=ie(e,t);t+=3;if(a>>>1==0){if(t&7)t+=8-(t&7);var o=e[t>>>3]|e[(t>>>3)+1]<<8;t+=32;if(!r&&s<i+o){n=ce(n,i+o);s=n.length}if(typeof e.copy==="function"){e.copy(n,i,t>>>3,(t>>>3)+o);i+=o;t+=8*o}else while(o-- >0){n[i++]=e[t>>>3];t+=8}continue}else if(a>>>1==1){f=9;l=5}else{t=Se(e,t);f=we;l=ke}if(!r&&s<i+32767){n=ce(n,i+32767);s=n.length}for(;;){var c=oe(e,t,f);var u=a>>>1==1?ve[c]:me[c];t+=u&15;u>>>=4;if((u>>>8&255)===0)n[i++]=u;else if(u==256)break;else{u-=257;var h=u<8?0:u-4>>2;if(h>5)h=0;var d=i+J[u];if(h>0){d+=oe(e,t,h);t+=h}c=oe(e,t,l);u=a>>>1==1?pe[c]:ge[c];t+=u&15;u>>>=4;var v=u<4?0:u-2>>1;var p=q[u];if(v>0){p+=oe(e,t,v);t+=v}if(!r&&s<d){n=ce(n,d);s=n.length}while(i<d){n[i]=n[i-p];++i}}}}return[r?n:n.slice(0,i),t+7>>>3]}function Ce(e,r){var t=e.slice(e.l||0);var a=Be(t,r);e.l+=a[1];return a[0]}function Te(e,r){if(e){if(typeof console!=="undefined")console.error(r)}else throw new Error(r)}function _e(e,r){var t=e;Yr(t,0);var a=[],n=[];var i={FileIndex:a,FullPaths:n};x(i,{root:r.root});var f=t.length-4;while((t[f]!=80||t[f+1]!=75||t[f+2]!=5||t[f+3]!=6)&&f>=0)--f;t.l=f+4;t.l+=4;var l=t._R(2);t.l+=6;var o=t._R(4);t.l=o;for(f=0;f<l;++f){t.l+=20;var c=t._R(4);var u=t._R(4);var h=t._R(2);var d=t._R(2);var v=t._R(2);t.l+=8;var p=t._R(4);var b=s(t.slice(t.l+h,t.l+h+d));t.l+=h+d+v;var m=t.l;t.l=p+4;xe(t,c,u,i,b);t.l=m}return i}function xe(e,r,t,a,n){e.l+=2;var f=e._R(2);var l=e._R(2);var o=i(e);if(f&8257)throw new Error("Unsupported ZIP encryption");var c=e._R(4);var u=e._R(4);var h=e._R(4);var d=e._R(2);var v=e._R(2);var p="";for(var b=0;b<d;++b)p+=String.fromCharCode(e[e.l++]);if(v){var m=s(e.slice(e.l,e.l+v));if((m[21589]||{}).mt)o=m[21589].mt;if(((n||{})[21589]||{}).mt)o=n[21589].mt}e.l+=v;var g=e.slice(e.l,e.l+u);switch(l){case 8:g=K(e,h);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+l);}var E=false;if(f&8){c=e._R(4);if(c==134695760){c=e._R(4);E=true}u=e._R(4);h=e._R(4)}if(u!=r)Te(E,"Bad compressed size: "+r+" != "+u);if(h!=t)Te(E,"Bad uncompressed size: "+t+" != "+h);var w=W.buf(g,0);if(c>>0!=w>>0)Te(E,"Bad CRC32 checksum: "+c+" != "+w);Ie(a,p,g,{unsafe:true,mt:o})}function Ae(e,r){var t=r||{};var a=[],i=[];var s=Jr(1);var f=t.compression?8:0,l=0;var o=false;if(o)l|=8;var c=0,u=0;var h=0,d=0;var v=e.FullPaths[0],p=v,b=e.FileIndex[0];var m=[];var g=0;for(c=1;c<e.FullPaths.length;++c){p=e.FullPaths[c].slice(v.length);b=e.FileIndex[c];if(!b.size||!b.content||p=="Sh33tJ5")continue;var E=h;var w=Jr(p.length);for(u=0;u<p.length;++u)w._W(1,p.charCodeAt(u)&127);w=w.slice(0,w.l);m[d]=W.buf(b.content,0);var k=b.content;if(f==8)k=Y(k);s=Jr(30);s._W(4,67324752);s._W(2,20);s._W(2,l);s._W(2,f);if(b.mt)n(s,b.mt);else s._W(4,0);s._W(-4,l&8?0:m[d]);s._W(4,l&8?0:k.length);s._W(4,l&8?0:b.content.length);s._W(2,w.length);s._W(2,0);h+=s.length;a.push(s);h+=w.length;a.push(w);h+=k.length;a.push(k);if(l&8){s=Jr(12);s._W(-4,m[d]);s._W(4,k.length);s._W(4,b.content.length);h+=s.l;a.push(s)}s=Jr(46);s._W(4,33639248);s._W(2,0);s._W(2,20);s._W(2,l);s._W(2,f);s._W(4,0);s._W(-4,m[d]);s._W(4,k.length);s._W(4,b.content.length);s._W(2,w.length);s._W(2,0);s._W(2,0);s._W(2,0);s._W(2,0);s._W(4,0);s._W(4,E);g+=s.l;i.push(s);g+=w.length;i.push(w);++d}s=Jr(22);s._W(4,101010256);s._W(2,0);s._W(2,0);s._W(2,d);s._W(2,d);s._W(4,g);s._W(4,h);s._W(2,0);return I([I(a),I(i),s])}function ye(e){var r={};x(r,e);return r}function Ie(e,r,t,n){var i=n&&n.unsafe;if(!i)x(e);var s=!i&&V.find(e,r);if(!s){var f=e.FullPaths[0];if(r.slice(0,f.length)==f)f=r;else{if(f.slice(-1)!="/")f+="/";f=(f+r).replace("//","/")}s={name:a(r),type:2};e.FileIndex.push(s);e.FullPaths.push(f);if(!i)V.utils.cfb_gc(e)}s.content=t;s.size=t?t.length:0;if(n){if(n.CLSID)s.clsid=n.CLSID;if(n.mt)s.mt=n.mt;if(n.ct)s.ct=n.ct}return s}function Re(e,r){x(e);var t=V.find(e,r);if(t)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t){e.FileIndex.splice(a,1);e.FullPaths.splice(a,1);return true}return false}function Fe(e,r,t){x(e);var n=V.find(e,r);if(n)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n){e.FileIndex[i].name=a(t);e.FullPaths[i]=t;return true}return false}function De(e){y(e,true)}e.find=O;e.read=_;e.parse=o;e.write=z;e.writeFile=X;e.utils={cfb_new:ye,cfb_add:Ie,cfb_del:Re,cfb_mov:Fe,cfb_gc:De,ReadShift:Xr,CheckField:Kr,prep_blob:Yr,bconcat:I,use_zlib:$,_deflateRaw:he,_inflateRaw:Ce,consts:H};return e}();if(typeof require!=="undefined"&&typeof module!=="undefined"&&typeof H==="undefined"){module.exports=V}var X;if(typeof require!=="undefined")try{X=require("fs")}catch(S){}function G(e){if(typeof e==="string")return _(e);if(Array.isArray(e))return A(e);return e}function z(e,r,t){if(typeof X!=="undefined"&&X.writeFileSync)return t?X.writeFileSync(e,r,t):X.writeFileSync(e,r);var a=t=="utf8"?Qe(r):r;if(typeof IE_SaveFile!=="undefined")return IE_SaveFile(a,e);if(typeof Blob!=="undefined"){var n=new Blob([G(a)],{type:"application/octet-stream"});if(typeof navigator!=="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs!=="undefined")return saveAs(n,e);if(typeof URL!=="undefined"&&typeof document!=="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome==="object"&&typeof(chrome.downloads||{}).download=="function"){if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return chrome.downloads.download({url:i,filename:e,saveAs:true})}var s=document.createElement("a");if(s.download!=null){s.download=e;s.href=i;document.body.appendChild(s);s.click();document.body.removeChild(s);if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return i}}}if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var f=File(e);f.open("w");f.encoding="binary";if(Array.isArray(r))r=x(r);f.write(r);f.close();return r}catch(l){if(!l.message||!l.message.match(/onstruct/))throw l}throw new Error("cannot save file "+e)}function j(e){if(typeof X!=="undefined")return X.readFileSync(e);if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var r=File(e);r.open("r");r.encoding="binary";var t=r.read();r.close();return t}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function K(e){var r=Object.keys(e),t=[];for(var a=0;a<r.length;++a)if(Object.prototype.hasOwnProperty.call(e,r[a]))t.push(r[a]);return t}function Y(e,r){var t=[],a=K(e);for(var n=0;n!==a.length;++n)if(t[e[a[n]][r]]==null)t[e[a[n]][r]]=a[n];return t}function Q(e){var r=[],t=K(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}function J(e){var r=[],t=K(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=parseInt(t[a],10);return r}function q(e){var r=[],t=K(e);for(var a=0;a!==t.length;++a){if(r[e[t[a]]]==null)r[e[t[a]]]=[];r[e[t[a]]].push(t[a])}return r}var Z=new Date(1899,11,30,0,0,0);function ee(e,r){var t=e.getTime();if(r)t-=1462*24*60*60*1e3;var a=Z.getTime()+(e.getTimezoneOffset()-Z.getTimezoneOffset())*6e4;return(t-a)/(24*60*60*1e3)}var re=new Date;var te=Z.getTime()+(re.getTimezoneOffset()-Z.getTimezoneOffset())*6e4;var ae=re.getTimezoneOffset();function ne(e){var r=new Date;r.setTime(e*24*60*60*1e3+te);if(r.getTimezoneOffset()!==ae){r.setTime(r.getTime()+(r.getTimezoneOffset()-ae)*6e4)}return r}function ie(e){var r=0,t=0,a=false;var n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i){if(!n[i])continue;t=1;if(i>3)a=true;switch(n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":t*=24;case"H":t*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");else t*=60;case"S":break;}r+=t*parseInt(n[i],10)}return r}var se=new Date("2017-02-19T19:06:09.000Z");if(isNaN(se.getFullYear()))se=new Date("2/19/17");var fe=se.getFullYear()==2017;function le(e,r){var t=new Date(e);if(fe){if(r>0)t.setTime(t.getTime()+t.getTimezoneOffset()*60*1e3);else if(r<0)t.setTime(t.getTime()-t.getTimezoneOffset()*60*1e3);return t}if(e instanceof Date)return e;if(se.getFullYear()==1917&&!isNaN(t.getFullYear())){var a=t.getFullYear();if(e.indexOf(""+a)>-1)return t;t.setFullYear(t.getFullYear()+100);return t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"];var i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);if(e.indexOf("Z")>-1)i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3);return i}function oe(e){var r="";for(var t=0;t!=e.length;++t)r+=String.fromCharCode(e[t]);return r}function ce(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))r[t]=ce(e[t]);return r}function ue(e,r){var t="";while(t.length<r)t+=e;return t}function he(e){var r=Number(e);if(isFinite(r))return r;if(!isNaN(r))return NaN;if(!/\d/.test(e))return r;var t=1;var a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){t*=100;return""});if(!isNaN(r=Number(a)))return r/t;a=a.replace(/[(](.*)[)]/,function(e,r){t=-t;return r});if(!isNaN(r=Number(a)))return r/t;return r}function de(e){var r=new Date(e),t=new Date(NaN);var a=r.getYear(),n=r.getMonth(),i=r.getDate();if(isNaN(i))return t;if(a<0||a>8099)return t;if((n>0||i>1)&&a!=101)return r;if(e.toLowerCase().match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/))return r;if(e.match(/[^-0-9:,\/\\]/))return t;return r}var ve="abacaba".split(/(:?b)/i).length==5;function pe(e,r,t){if(ve||typeof r=="string")return e.split(r);var a=e.split(r),n=[a[0]];for(var i=1;i<a.length;++i){n.push(t);n.push(a[i])}return n}function be(e){if(!e)return null;if(e.data)return d(e.data);if(e.asNodeBuffer&&E)return d(e.asNodeBuffer().toString("binary"));if(e.asBinary)return d(e.asBinary());if(e._data&&e._data.getContent)return d(oe(Array.prototype.slice.call(e._data.getContent(),0)));if(e.content&&e.type)return d(oe(e.content));return null}function me(e){if(!e)return null;if(e.data)return c(e.data);if(e.asNodeBuffer&&E)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();if(typeof r=="string")return c(r);return Array.prototype.slice.call(r)}if(e.content&&e.type)return e.content;return null}function ge(e){return e&&e.name.slice(-4)===".bin"?me(e):be(e)}function Ee(e,r){var t=e.FullPaths||K(e.files);var a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/");for(var i=0;i<t.length;++i){var s=t[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[t[i]]:e.FileIndex[i]}return null}function we(e,r){var t=Ee(e,r);if(t==null)throw new Error("Cannot find file "+r+" in zip");return t}function ke(e,r,t){if(!t)return ge(we(e,r));if(!r)return null;try{return ke(e,r)}catch(a){return null}}function Se(e,r,t){if(!t)return be(we(e,r));if(!r)return null;try{return Se(e,r)}catch(a){return null}}function Be(e){var r=e.FullPaths||K(e.files),t=[];for(var a=0;a<r.length;++a)if(r[a].slice(-1)!="/")t.push(r[a]);return t.sort()}function Ce(e,r,t){if(e.FullPaths)V.utils.cfb_add(e,r,t);else e.file(r,t)}var Te;if(typeof JSZipSync!=="undefined")Te=JSZipSync;if(typeof exports!=="undefined"){if(typeof module!=="undefined"&&module.exports){if(typeof Te==="undefined")Te=undefined}}function _e(){if(!Te)return V.utils.cfb_new();return new Te}function xe(e,r){var t;if(Te)switch(r.type){case"base64":t=new Te(e,{base64:true});break;case"binary":;case"array":t=new Te(e,{base64:false});break;case"buffer":t=new Te(e);break;default:throw new Error("Unrecognized type "+r.type);}else switch(r.type){case"base64":t=V.read(e,{type:"base64"});break;case"binary":t=V.read(e,{type:"binary"});break;case"buffer":;case"array":t=V.read(e,{type:"buffer"});break;default:throw new Error("Unrecognized type "+r.type);}return t}function Ae(e,r){if(e.charAt(0)=="/")return e.slice(1);var t=r.split("/");if(r.slice(-1)!="/")t.pop();var a=e.split("/");while(a.length!==0){var n=a.shift();if(n==="..")t.pop();else if(n!==".")t.push(n)}return t.join("/")}var ye='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var Ie=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g;var Re=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm;if(!ye.match(Re))Re=/<[^>]*>/g;var Fe=/<\w*:/,De=/<(\/?)\w+:/;function Oe(e,r,t){var a={};var n=0,i=0;for(;n!==e.length;++n)if((i=e.charCodeAt(n))===32||i===10||i===13)break;if(!r)a[0]=e.slice(0,n);if(n===e.length)return a;var s=e.match(Ie),f=0,l="",o=0,c="",u="",h=1;if(s)for(o=0;o!=s.length;++o){u=s[o];for(i=0;i!=u.length;++i)if(u.charCodeAt(i)===61)break;c=u.slice(0,i).trim();while(u.charCodeAt(i+1)==32)++i;h=(n=u.charCodeAt(i+1))==34||n==39?1:0;l=u.slice(i+1+h,u.length-h);for(f=0;f!=c.length;++f)if(c.charCodeAt(f)===58)break;if(f===c.length){if(c.indexOf("_")>0)c=c.slice(0,c.indexOf("_"));a[c]=l;if(!t)a[c.toLowerCase()]=l}else{var d=(f===5&&c.slice(0,5)==="xmlns"?"xmlns":"")+c.slice(f+1);if(a[d]&&c.slice(f-3,f)=="ext")continue;a[d]=l;if(!t)a[d.toLowerCase()]=l}}return a}function Pe(e){return e.replace(De,"<$1")}var Ne={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"};var Me=Q(Ne);var Le=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,r=/_x([\da-fA-F]{4})_/gi;return function t(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(e,r){return Ne[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e}).replace(r,function(e,r){return String.fromCharCode(parseInt(r,16))});var s=n.indexOf("]]>");return t(n.slice(0,i))+n.slice(i+9,s)+t(n.slice(s+3))}}();var Ue=/[&<>'"]/g,He=/[\u0000-\u0008\u000b-\u001f]/g;function We(e){var r=e+"";return r.replace(Ue,function(e){return Me[e]}).replace(He,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function Ve(e){return We(e).replace(/ /g,"_x0020_")}var Xe=/[\u0000-\u001f]/g;function Ge(e){var r=e+"";return r.replace(Ue,function(e){return Me[e]}).replace(/\n/g,"<br/>").replace(Xe,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function ze(e){var r=e+"";return r.replace(Ue,function(e){return Me[e]}).replace(Xe,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var je=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function t(a){return a.replace(e,r)}}();var $e=function(){return function e(r){return r.replace(/(\r\n|[\r\n])/g,"&#10;")}}();function Ke(e){switch(e){case 1:;case true:;case"1":;case"true":;case"TRUE":return true;default:return false;}}var Ye=function Um(e){var r="",t=0,a=0,n=0,i=0,s=0,f=0;while(t<e.length){a=e.charCodeAt(t++);if(a<128){r+=String.fromCharCode(a);
continue}n=e.charCodeAt(t++);if(a>191&&a<224){s=(a&31)<<6;s|=n&63;r+=String.fromCharCode(s);continue}i=e.charCodeAt(t++);if(a<240){r+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(t++);f=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536;r+=String.fromCharCode(55296+(f>>>10&1023));r+=String.fromCharCode(56320+(f&1023))}return r};var Qe=function(e){var r=[],t=0,a=0,n=0;while(t<e.length){a=e.charCodeAt(t++);switch(true){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6)));r.push(String.fromCharCode(128+(a&63)));break;case a>=55296&&a<57344:a-=55296;n=e.charCodeAt(t++)-56320+(a<<10);r.push(String.fromCharCode(240+(n>>18&7)));r.push(String.fromCharCode(144+(n>>12&63)));r.push(String.fromCharCode(128+(n>>6&63)));r.push(String.fromCharCode(128+(n&63)));break;default:r.push(String.fromCharCode(224+(a>>12)));r.push(String.fromCharCode(128+(a>>6&63)));r.push(String.fromCharCode(128+(a&63)));}}return r.join("")};if(E){var Je=function Hm(e){var r=Buffer.alloc(2*e.length),t,a,n=1,i=0,s=0,f;for(a=0;a<e.length;a+=n){n=1;if((f=e.charCodeAt(a))<128)t=f;else if(f<224){t=(f&31)*64+(e.charCodeAt(a+1)&63);n=2}else if(f<240){t=(f&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63);n=3}else{n=4;t=(f&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63);t-=65536;s=55296+(t>>>10&1023);t=56320+(t&1023)}if(s!==0){r[i++]=s&255;r[i++]=s>>>8;s=0}r[i++]=t%256;r[i++]=t>>>8}return r.slice(0,i).toString("ucs2")};var qe="foo bar bazâð£";if(Ye(qe)==Je(qe))Ye=Je;var Ze=function Wm(e){return w(e,"binary").toString("utf8")};if(Ye(qe)==Ze(qe))Ye=Ze;Qe=function(e){return w(e,"utf8").toString("binary")}}var er=function(){var e={};return function r(t,a){var n=t+"|"+(a||"");if(e[n])return e[n];return e[n]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",a||"")}}();var rr=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]});return function r(t){var a=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,"");for(var n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}();var tr=function(){var e={};return function r(t){if(e[t]!==undefined)return e[t];return e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}();var ar=/<\/?(?:vt:)?variant>/g,nr=/<(?:vt:)([^>]*)>([\s\S]*)</;function ir(e,r){var t=Oe(e);var a=e.match(tr(t.baseType))||[];var n=[];if(a.length!=t.size){if(r.WTF)throw new Error("unexpected vector length "+a.length+" != "+t.size);return n}a.forEach(function(e){var r=e.replace(ar,"").match(nr);if(r)n.push({v:Ye(r[2]),t:r[1]})});return n}var sr=/(^\s|\s$|\n)/;function fr(e,r){return"<"+e+(r.match(sr)?' xml:space="preserve"':"")+">"+r+"</"+e+">"}function lr(e){return K(e).map(function(r){return" "+r+'="'+e[r]+'"'}).join("")}function or(e,r,t){return"<"+e+(t!=null?lr(t):"")+(r!=null?(r.match(sr)?' xml:space="preserve"':"")+">"+r+"</"+e:"/")+">"}function cr(e,r){try{return e.toISOString().replace(/\.\d*/,"")}catch(t){if(r)throw t}return""}function ur(e,r){switch(typeof e){case"string":var t=or("vt:lpwstr",We(e));if(r)t=t.replace(/&quot;/g,"_x0022_");return t;case"number":return or((e|0)==e?"vt:i4":"vt:r8",We(String(e)));case"boolean":return or("vt:bool",e?"true":"false");}if(e instanceof Date)return or("vt:filetime",cr(e));throw new Error("Unable to serialize "+e)}var hr={dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"};hr.main=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];var dr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function vr(e,r){var t=1-2*(e[r+7]>>>7);var a=((e[r+7]&127)<<4)+(e[r+6]>>>4&15);var n=e[r+6]&15;for(var i=5;i>=0;--i)n=n*256+e[r+i];if(a==2047)return n==0?t*Infinity:NaN;if(a==0)a=-1022;else{a-=1023;n+=Math.pow(2,52)}return t*Math.pow(2,a-52)*n}function pr(e,r,t){var a=(r<0||1/r==-Infinity?1:0)<<7,n=0,i=0;var s=a?-r:r;if(!isFinite(s)){n=2047;i=isNaN(r)?26985:0}else if(s==0)n=i=0;else{n=Math.floor(Math.log(s)/Math.LN2);i=s*Math.pow(2,52-n);if(n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))){n=-1022}else{i-=Math.pow(2,52);n+=1023}}for(var f=0;f<=5;++f,i/=256)e[t+f]=i&255;e[t+6]=(n&15)<<4|i&15;e[t+7]=n>>4|a}var br=function(e){var r=[],t=10240;for(var a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=t)r.push.apply(r,e[0][a].slice(n,n+t));return r};var mr=br;var gr=function(e,r,t){var a=[];for(var n=r;n<t;n+=2)a.push(String.fromCharCode(Lr(e,n)));return a.join("").replace(R,"")};var Er=gr;var wr=function(e,r,t){var a=[];for(var n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")};var kr=wr;var Sr=function(e,r,t){var a=[];for(var n=r;n<t;n++)a.push(String.fromCharCode(Mr(e,n)));return a.join("")};var Br=Sr;var Cr=function(e,r){var t=Hr(e,r);return t>0?Sr(e,r+4,r+4+t-1):""};var Tr=Cr;var _r=function(e,r){var t=Hr(e,r);return t>0?Sr(e,r+4,r+4+t-1):""};var xr=_r;var Ar=function(e,r){var t=2*Hr(e,r);return t>0?Sr(e,r+4,r+4+t-1):""};var yr=Ar;var Ir,Rr;Ir=Rr=function Vm(e,r){var t=Hr(e,r);return t>0?gr(e,r+4,r+4+t):""};var Fr=function(e,r){var t=Hr(e,r);return t>0?Sr(e,r+4,r+4+t):""};var Dr=Fr;var Or,Pr;Or=Pr=function(e,r){return vr(e,r)};var Nr=function Xm(e){return Array.isArray(e)};if(E){gr=function(e,r,t){if(!Buffer.isBuffer(e))return Er(e,r,t);return e.toString("utf16le",r,t).replace(R,"")};wr=function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):kr(e,r,t)};Cr=function Gm(e,r){if(!Buffer.isBuffer(e))return Tr(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};_r=function zm(e,r){if(!Buffer.isBuffer(e))return xr(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};Ar=function jm(e,r){if(!Buffer.isBuffer(e))return yr(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)};Ir=function $m(e,r){if(!Buffer.isBuffer(e))return Rr(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)};Fr=function Km(e,r){if(!Buffer.isBuffer(e))return Dr(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)};Sr=function Ym(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):Br(e,r,t)};br=function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0]):mr(e)};I=function(e){return Buffer.isBuffer(e[0])?Buffer.concat(e):[].concat.apply([],e)};Or=function Qm(e,r){if(Buffer.isBuffer(e))return e.readDoubleLE(r);return Pr(e,r)};Nr=function Jm(e){return Buffer.isBuffer(e)||Array.isArray(e)}}if(typeof cptable!=="undefined"){gr=function(e,r,t){return cptable.utils.decode(1200,e.slice(r,t)).replace(R,"")};Sr=function(e,r,t){return cptable.utils.decode(65001,e.slice(r,t))};Cr=function(e,r){var a=Hr(e,r);return a>0?cptable.utils.decode(t,e.slice(r+4,r+4+a-1)):""};_r=function(e,t){var a=Hr(e,t);return a>0?cptable.utils.decode(r,e.slice(t+4,t+4+a-1)):""};Ar=function(e,r){var t=2*Hr(e,r);return t>0?cptable.utils.decode(1200,e.slice(r+4,r+4+t-1)):""};Ir=function(e,r){var t=Hr(e,r);return t>0?cptable.utils.decode(1200,e.slice(r+4,r+4+t)):""};Fr=function(e,r){var t=Hr(e,r);return t>0?cptable.utils.decode(65001,e.slice(r+4,r+4+t)):""}}var Mr=function(e,r){return e[r]};var Lr=function(e,r){return e[r+1]*(1<<8)+e[r]};var Ur=function(e,r){var t=e[r+1]*(1<<8)+e[r];return t<32768?t:(65535-t+1)*-1};var Hr=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]};var Wr=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]};var Vr=function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]};function Xr(e,t){var a="",n,i,s=[],f,l,o,c;switch(t){case"dbcs":c=this.l;if(E&&Buffer.isBuffer(this))a=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o){a+=String.fromCharCode(Lr(this,c));c+=2}e*=2;break;case"utf8":a=Sr(this,this.l,this.l+e);break;case"utf16le":e*=2;a=gr(this,this.l,this.l+e);break;case"wstr":if(typeof cptable!=="undefined")a=cptable.utils.decode(r,this.slice(this.l,this.l+2*e));else return Xr.call(this,e,"dbcs");e=2*e;break;case"lpstr-ansi":a=Cr(this,this.l);e=4+Hr(this,this.l);break;case"lpstr-cp":a=_r(this,this.l);e=4+Hr(this,this.l);break;case"lpwstr":a=Ar(this,this.l);e=4+2*Hr(this,this.l);break;case"lpp4":e=4+Hr(this,this.l);a=Ir(this,this.l);if(e&2)e+=2;break;case"8lpp4":e=4+Hr(this,this.l);a=Fr(this,this.l);if(e&3)e+=4-(e&3);break;case"cstr":e=0;a="";while((f=Mr(this,this.l+e++))!==0)s.push(v(f));a=s.join("");break;case"_wstr":e=0;a="";while((f=Lr(this,this.l+e))!==0){s.push(v(f));e+=2}e+=2;a=s.join("");break;case"dbcs-cont":a="";c=this.l;for(o=0;o<e;++o){if(this.lens&&this.lens.indexOf(c)!==-1){f=Mr(this,c);this.l=c+1;l=Xr.call(this,e-o,f?"dbcs-cont":"sbcs-cont");return s.join("")+l}s.push(v(Lr(this,c)));c+=2}a=s.join("");e*=2;break;case"cpstr":if(typeof cptable!=="undefined"){a=cptable.utils.decode(r,this.slice(this.l,this.l+e));break};case"sbcs-cont":a="";c=this.l;for(o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(c)!==-1){f=Mr(this,c);this.l=c+1;l=Xr.call(this,e-o,f?"dbcs-cont":"sbcs-cont");return s.join("")+l}s.push(v(Mr(this,c)));c+=1}a=s.join("");break;default:switch(e){case 1:n=Mr(this,this.l);this.l++;return n;case 2:n=(t==="i"?Ur:Lr)(this,this.l);this.l+=2;return n;case 4:;case-4:if(t==="i"||(this[this.l+3]&128)===0){n=(e>0?Wr:Vr)(this,this.l);this.l+=4;return n}else{i=Hr(this,this.l);this.l+=4}return i;case 8:;case-8:if(t==="f"){if(e==8)i=Or(this,this.l);else i=Or([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0);this.l+=8;return i}else e=8;case 16:a=wr(this,this.l,e);break;};}this.l+=e;return a}var Gr=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255;e[t+2]=r>>>16&255;e[t+3]=r>>>24&255};var zr=function(e,r,t){e[t]=r&255;e[t+1]=r>>8&255;e[t+2]=r>>16&255;e[t+3]=r>>24&255};var jr=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255};function $r(e,r,a){var n=0,i=0;if(a==="dbcs"){for(i=0;i!=r.length;++i)jr(this,r.charCodeAt(i),this.l+2*i);n=2*r.length}else if(a==="sbcs"){if(typeof cptable!=="undefined"&&t==874){for(i=0;i!=r.length;++i){var s=cptable.utils.encode(t,r.charAt(i));this[this.l+i]=s[0]}}else{r=r.replace(/[^\x00-\x7F]/g,"_");for(i=0;i!=r.length;++i)this[this.l+i]=r.charCodeAt(i)&255}n=r.length}else if(a==="hex"){for(;i<e;++i){this[this.l++]=parseInt(r.slice(2*i,2*i+2),16)||0}return this}else if(a==="utf16le"){var f=Math.min(this.l+e,this.length);for(i=0;i<Math.min(r.length,e);++i){var l=r.charCodeAt(i);this[this.l++]=l&255;this[this.l++]=l>>8}while(this.l<f)this[this.l++]=0;return this}else switch(e){case 1:n=1;this[this.l]=r&255;break;case 2:n=2;this[this.l]=r&255;r>>>=8;this[this.l+1]=r&255;break;case 3:n=3;this[this.l]=r&255;r>>>=8;this[this.l+1]=r&255;r>>>=8;this[this.l+2]=r&255;break;case 4:n=4;Gr(this,r,this.l);break;case 8:n=8;if(a==="f"){pr(this,r,this.l);break};case 16:break;case-4:n=4;zr(this,r,this.l);break;}this.l+=n;return this}function Kr(e,r){var t=wr(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function Yr(e,r){e.l=r;e._R=Xr;e.chk=Kr;e._W=$r}function Qr(e,r){e.l+=r}function Jr(e){var r=B(e);Yr(r,0);return r}function qr(e,r,t){if(!e)return;var a,n,i;Yr(e,e.l||0);var s=e.length,f=0,l=0;while(e.l<s){f=e._R(1);if(f&128)f=(f&127)+((e._R(1)&127)<<7);var o=qp[f]||qp[65535];a=e._R(1);i=a&127;for(n=1;n<4&&a&128;++n)i+=((a=e._R(1))&127)<<7*n;l=e.l+i;var c=o.f&&o.f(e,i,t);e.l=l;if(r(c,o.n,f))return}}function Zr(){var e=[],r=E?256:2048;var t=function l(e){var r=Jr(e);Yr(r,0);return r};var a=t(r);var n=function o(){if(!a)return;if(a.length>a.l){a=a.slice(0,a.l);a.l=a.length}if(a.length>0)e.push(a);a=null};var i=function c(e){if(a&&e<a.length-a.l)return a;n();return a=t(Math.max(e+1,r))};var s=function u(){n();return br([e])};var f=function h(e){n();a=e;if(a.l==null)a.l=a.length;i(r)};return{next:i,push:f,end:s,_bufs:e}}function et(e,r,t,a){var n=+Zp[r],i;if(isNaN(n))return;if(!a)a=qp[n].p||(t||[]).length||0;i=1+(n>=128?1:0)+1;if(a>=128)++i;if(a>=16384)++i;if(a>=2097152)++i;var s=e.next(i);if(n<=127)s._W(1,n);else{s._W(1,(n&127)+128);s._W(1,n>>7)}for(var f=0;f!=4;++f){if(a>=128){s._W(1,(a&127)+128);a>>=7}else{s._W(1,a);break}}if(a>0&&Nr(t))e.push(t)}function rt(e,r,t){var a=ce(e);if(r.s){if(a.cRel)a.c+=r.s.c;if(a.rRel)a.r+=r.s.r}else{if(a.cRel)a.c+=r.c;if(a.rRel)a.r+=r.r}if(!t||t.biff<12){while(a.c>=256)a.c-=256;while(a.r>=65536)a.r-=65536}return a}function tt(e,r,t){var a=ce(e);a.s=rt(a.s,r.s,t);a.e=rt(a.e,r.s,t);return a}function at(e,r){if(e.cRel&&e.c<0){e=ce(e);while(e.c<0)e.c+=r>8?16384:256}if(e.rRel&&e.r<0){e=ce(e);while(e.r<0)e.r+=r>8?1048576:r>5?65536:16384}var t=mt(e);if(!e.cRel&&e.cRel!=null)t=dt(t);if(!e.rRel&&e.rRel!=null)t=ot(t);return t}function nt(e,r){if(e.s.r==0&&!e.s.rRel){if(e.e.r==(r.biff>=12?1048575:r.biff>=8?65536:16384)&&!e.e.rRel){return(e.s.cRel?"":"$")+ht(e.s.c)+":"+(e.e.cRel?"":"$")+ht(e.e.c)}}if(e.s.c==0&&!e.s.cRel){if(e.e.c==(r.biff>=12?16383:255)&&!e.e.cRel){return(e.s.rRel?"":"$")+lt(e.s.r)+":"+(e.e.rRel?"":"$")+lt(e.e.r)}}return at(e.s,r.biff)+":"+at(e.e,r.biff)}var it={};var st=function(e,r){var t;if(typeof r!=="undefined")t=r;else if(typeof require!=="undefined"){try{t=undefined}catch(a){t=null}}e.rc4=function(e,r){var t=new Array(256);var a=0,n=0,i=0,s=0;for(n=0;n!=256;++n)t[n]=n;for(n=0;n!=256;++n){i=i+t[n]+e[n%e.length].charCodeAt(0)&255;s=t[n];t[n]=t[i];t[i]=s}n=i=0;var f=B(r.length);for(a=0;a!=r.length;++a){n=n+1&255;i=(i+t[n])%256;s=t[n];t[n]=t[i];t[i]=s;f[a]=r[a]^t[t[n]+t[i]&255]}return f};e.md5=function(e){if(!t)throw new Error("Unsupported crypto");return t.createHash("md5").update(e).digest("hex")}};st(it,typeof crypto!=="undefined"?crypto:undefined);function ft(e){return parseInt(ct(e),10)-1}function lt(e){return""+(e+1)}function ot(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function ct(e){return e.replace(/\$(\d+)$/,"$1")}function ut(e){var r=vt(e),t=0,a=0;for(;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function ht(e){if(e<0)throw new Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function dt(e){return e.replace(/^([A-Z])/,"$$$1")}function vt(e){return e.replace(/^\$([A-Z])/,"$1")}function pt(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function bt(e){var r=0,t=0;for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);if(n>=48&&n<=57)r=10*r+(n-48);else if(n>=65&&n<=90)t=26*t+(n-64)}return{c:t-1,r:r-1}}function mt(e){var r=e.c+1;var t="";for(;r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function gt(e){var r=e.indexOf(":");if(r==-1)return{s:bt(e),e:bt(e)};return{s:bt(e.slice(0,r)),e:bt(e.slice(r+1))}}function Et(e,r){if(typeof r==="undefined"||typeof r==="number"){return Et(e.s,e.e)}if(typeof e!=="string")e=mt(e);if(typeof r!=="string")r=mt(r);return e==r?e:e+":"+r}function wt(e){var r={s:{c:0,r:0},e:{c:0,r:0}};var t=0,a=0,n=0;var i=e.length;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.s.c=--t;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.s.r=--t;if(a===i||n!=10){r.e.c=r.s.c;r.e.r=r.s.r;return r}++a;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.e.c=--t;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.e.r=--t;return r}function kt(e,r){var t=e.t=="d"&&r instanceof Date;if(e.z!=null)try{return e.w=D.format(e.z,t?ee(r):r)}catch(a){}try{return e.w=D.format((e.XF||{}).numFmtId||(t?14:0),t?ee(r):r)}catch(a){return""+r}}function St(e,r,t){if(e==null||e.t==null||e.t=="z")return"";if(e.w!==undefined)return e.w;if(e.t=="d"&&!e.z&&t&&t.dateNF)e.z=t.dateNF;if(e.t=="e")return ya[e.v]||e.v;if(r==undefined)return kt(e,e.v);return kt(e,r)}function Bt(e,r){var t=r&&r.sheet?r.sheet:"Sheet1";var a={};a[t]=e;return{SheetNames:[t],Sheets:a}}function Ct(e,r,t){var a=t||{};var n=e?Array.isArray(e):a.dense;if(b!=null&&n==null)n=b;var i=e||(n?[]:{});var s=0,f=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var l=typeof a.origin=="string"?bt(a.origin):a.origin;s=l.r;f=l.c}if(!i["!ref"])i["!ref"]="A1:A1"}var o={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=wt(i["!ref"]);o.s.c=c.s.c;o.s.r=c.s.r;o.e.c=Math.max(o.e.c,c.e.c);o.e.r=Math.max(o.e.r,c.e.r);if(s==-1)o.e.r=s=c.e.r+1}for(var u=0;u!=r.length;++u){if(!r[u])continue;if(!Array.isArray(r[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=r[u].length;++h){if(typeof r[u][h]==="undefined")continue;var d={v:r[u][h]};var v=s+u,p=f+h;if(o.s.r>v)o.s.r=v;if(o.s.c>p)o.s.c=p;if(o.e.r<v)o.e.r=v;if(o.e.c<p)o.e.c=p;if(r[u][h]&&typeof r[u][h]==="object"&&!Array.isArray(r[u][h])&&!(r[u][h]instanceof Date))d=r[u][h];else{if(Array.isArray(d.v)){d.f=r[u][h][1];d.v=d.v[0]}if(d.v===null){if(d.f)d.t="n";else if(a.nullError){d.t="e";d.v=0}else if(!a.sheetStubs)continue;else d.t="z"}else if(typeof d.v==="number")d.t="n";else if(typeof d.v==="boolean")d.t="b";else if(d.v instanceof Date){d.z=a.dateNF||D._table[14];if(a.cellDates){d.t="d";d.w=D.format(d.z,ee(d.v))}else{d.t="n";d.v=ee(d.v);d.w=D.format(d.z,d.v)}}else d.t="s"}if(n){if(!i[v])i[v]=[];if(i[v][p]&&i[v][p].z)d.z=i[v][p].z;i[v][p]=d}else{var m=mt({c:p,r:v});if(i[m]&&i[m].z)d.z=i[m].z;i[m]=d}}}if(o.s.c<1e7)i["!ref"]=Et(o);return i}function Tt(e,r){return Ct(null,e,r)}function _t(e,r){if(!r)r=Jr(4);r._W(4,e);return r}function xt(e){var r=e._R(4);return r===0?"":e._R(r,"dbcs")}function At(e,r){var t=false;if(r==null){t=true;r=Jr(4+2*e.length)}r._W(4,e.length);if(e.length>0)r._W(0,e,"dbcs");return t?r.slice(0,r.l):r}function yt(e){return{ich:e._R(2),ifnt:e._R(2)}}function It(e,r){if(!r)r=Jr(4);r._W(2,e.ich||0);r._W(2,e.ifnt||0);return r}function Rt(e,r){var t=e.l;var a=e._R(1);var n=xt(e);var i=[];var s={t:n,h:n};if((a&1)!==0){var f=e._R(4);for(var l=0;l!=f;++l)i.push(yt(e));s.r=i}else s.r=[{ich:0,ifnt:0}];e.l=t+r;return s}function Ft(e,r){var t=false;if(r==null){t=true;r=Jr(15+4*e.t.length)}r._W(1,0);At(e.t,r);return t?r.slice(0,r.l):r}var Dt=Rt;function Ot(e,r){var t=false;if(r==null){t=true;r=Jr(23+4*e.t.length)}r._W(1,1);At(e.t,r);r._W(4,1);It({ich:0,ifnt:0},r);return t?r.slice(0,r.l):r}function Pt(e){var r=e._R(4);var t=e._R(2);t+=e._R(1)<<16;e.l++;return{c:r,iStyleRef:t}}function Nt(e,r){if(r==null)r=Jr(8);r._W(-4,e.c);r._W(3,e.iStyleRef||e.s);r._W(1,0);return r}function Mt(e){var r=e._R(2);r+=e._R(1)<<16;e.l++;return{c:-1,iStyleRef:r}}function Lt(e,r){if(r==null)r=Jr(4);r._W(3,e.iStyleRef||e.s);r._W(1,0);return r}var Ut=xt;var Ht=At;function Wt(e){var r=e._R(4);return r===0||r===4294967295?"":e._R(r,"dbcs")}function Vt(e,r){var t=false;if(r==null){t=true;r=Jr(127)}r._W(4,e.length>0?e.length:4294967295);if(e.length>0)r._W(0,e,"dbcs");return t?r.slice(0,r.l):r}var Xt=xt;var Gt=Wt;var zt=Vt;function jt(e){var r=e.slice(e.l,e.l+4);var t=r[0]&1,a=r[0]&2;e.l+=4;r[0]&=252;var n=a===0?Or([0,0,0,0,r[0],r[1],r[2],r[3]],0):Wr(r,0)>>2;return t?n/100:n}function $t(e,r){if(r==null)r=Jr(4);var t=0,a=0,n=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29){a=1}else if(n==(n|0)&&n>=-(1<<29)&&n<1<<29){a=1;t=1}if(a)r._W(-4,((t?n:e)<<2)+(t+2));else throw new Error("unsupported RkNumber "+e)}function Kt(e){var r={s:{},e:{}};r.s.r=e._R(4);r.e.r=e._R(4);r.s.c=e._R(4);r.e.c=e._R(4);return r}function Yt(e,r){if(!r)r=Jr(16);r._W(4,e.s.r);r._W(4,e.e.r);r._W(4,e.s.c);r._W(4,e.e.c);return r}var Qt=Kt;var Jt=Yt;function qt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e._R(8,"f")}function Zt(e,r){return(r||Jr(8))._W(8,e,"f")}function ea(e){var r={};var t=e._R(1);var a=t>>>1;var n=e._R(1);var i=e._R(2,"i");var s=e._R(1);var f=e._R(1);var l=e._R(1);e.l++;switch(a){case 0:r.auto=1;break;case 1:r.index=n;var o=Aa[n];if(o)r.rgb=cl(o);break;case 2:r.rgb=cl([s,f,l]);break;case 3:r.theme=n;break;}if(i!=0)r.tint=i>0?i/32767:i/32768;return r}function ra(e,r){if(!r)r=Jr(8);if(!e||e.auto){r._W(4,0);r._W(4,0);return r}if(e.index!=null){r._W(1,2);r._W(1,e.index)}else if(e.theme!=null){r._W(1,6);r._W(1,e.theme)}else{r._W(1,5);r._W(1,0)}var t=e.tint||0;if(t>0)t*=32767;else if(t<0)t*=32768;r._W(2,t);if(!e.rgb||e.theme!=null){r._W(2,0);r._W(1,0);r._W(1,0)}else{var a=e.rgb||"FFFFFF";if(typeof a=="number")a=("000000"+a.toString(16)).slice(-6);r._W(1,parseInt(a.slice(0,2),16));r._W(1,parseInt(a.slice(2,4),16));r._W(1,parseInt(a.slice(4,6),16));r._W(1,255)}return r}function ta(e){var r=e._R(1);e.l++;var t={fBold:r&1,fItalic:r&2,fUnderline:r&4,fStrikeout:r&8,fOutline:r&16,fShadow:r&32,fCondense:r&64,fExtend:r&128};return t}function aa(e,r){if(!r)r=Jr(2);var t=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);r._W(1,t);r._W(1,0);return r}function na(e,r){var t={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"};var a=e._R(4);switch(a){case 0:return"";case 4294967295:;case 4294967294:return t[e._R(4)]||"";}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));e.l-=4;return e._R(0,r==1?"lpstr":"lpwstr")}function ia(e){return na(e,1)}function sa(e){return na(e,2)}var fa=2;var la=3;var oa=11;var ca=12;var ua=19;var ha=30;var da=64;var va=65;var pa=71;var ba=4096;var ma=80;var ga=81;var Ea=[ma,ga];var wa={1:{n:"CodePage",t:fa},2:{n:"Category",t:ma},3:{n:"PresentationFormat",t:ma},4:{n:"ByteCount",t:la},5:{n:"LineCount",t:la},6:{n:"ParagraphCount",t:la},7:{n:"SlideCount",t:la},8:{n:"NoteCount",t:la},9:{n:"HiddenCount",t:la},10:{n:"MultimediaClipCount",t:la},11:{n:"ScaleCrop",t:oa},12:{n:"HeadingPairs",t:ba|ca},13:{n:"TitlesOfParts",t:ba|ha},14:{n:"Manager",t:ma},15:{n:"Company",t:ma},16:{n:"LinksUpToDate",t:oa},17:{n:"CharacterCount",t:la},19:{n:"SharedDoc",t:oa},22:{n:"HyperlinksChanged",t:oa},23:{n:"AppVersion",t:la,p:"version"},24:{n:"DigSig",t:va},26:{n:"ContentType",t:ma},27:{n:"ContentStatus",t:ma},28:{n:"Language",t:ma},29:{n:"Version",t:ma},255:{},2147483648:{n:"Locale",t:ua},2147483651:{n:"Behavior",t:ua},1919054434:{}};var ka={1:{n:"CodePage",t:fa},2:{n:"Title",t:ma},3:{n:"Subject",t:ma},4:{n:"Author",t:ma},5:{n:"Keywords",t:ma},6:{n:"Comments",t:ma},7:{n:"Template",t:ma},8:{n:"LastAuthor",t:ma},9:{n:"RevNumber",t:ma},10:{n:"EditTime",t:da},11:{n:"LastPrinted",t:da},12:{n:"CreatedDate",t:da},13:{n:"ModifiedDate",t:da},14:{n:"PageCount",t:la},15:{n:"WordCount",t:la},16:{n:"CharCount",t:la},17:{n:"Thumbnail",t:pa},18:{n:"Application",t:ma},19:{n:"DocSecurity",t:la},255:{},2147483648:{n:"Locale",t:ua},2147483651:{n:"Behavior",t:ua},1919054434:{}};var Sa=Y(wa,"n");var Ba=Y(ka,"n");var Ca={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"};var Ta=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function _a(e){return e.map(function(e){return[e>>16&255,e>>8&255,e&255]})}var xa=_a([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);var Aa=ce(xa);var ya={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"};var Ia=J(ya);var Ra={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.sheetMetadata":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"TODO","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"vba","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};var Fa=function(){var e={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};K(e).forEach(function(r){["xlsm","xlam"].forEach(function(t){if(!e[r][t])e[r][t]=e[r].xlsx})});K(e).forEach(function(r){K(e[r]).forEach(function(t){Ra[e[r][t]]=r})});return e}();var Da=q(Ra);hr.CT="http://schemas.openxmlformats.org/package/2006/content-types";function Oa(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],TODO:[],xmlns:""}}function Pa(e){var r=Oa();if(!e||!e.match)return r;var t={};(e.match(Re)||[]).forEach(function(e){var a=Oe(e);switch(a[0].replace(Fe,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension]=a.ContentType;break;case"<Override":if(r[Ra[a.ContentType]]!==undefined)r[Ra[a.ContentType]].push(a.PartName);break;}});if(r.xmlns!==hr.CT)throw new Error("Unknown Namespace: "+r.xmlns);
r.calcchain=r.calcchains.length>0?r.calcchains[0]:"";r.sst=r.strs.length>0?r.strs[0]:"";r.style=r.styles.length>0?r.styles[0]:"";r.defaults=t;delete r.calcchains;return r}var Na=or("Types",null,{xmlns:hr.CT,"xmlns:xsd":hr.xsd,"xmlns:xsi":hr.xsi});var Ma=[["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels",Da.rels[0]]].map(function(e){return or("Default",null,{Extension:e[0],ContentType:e[1]})});function La(e,r){var t=[],a;t[t.length]=ye;t[t.length]=Na;t=t.concat(Ma);var n=function(n){if(e[n]&&e[n].length>0){a=e[n][0];t[t.length]=or("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:Fa[n][r.bookType||"xlsx"]})}};var i=function(a){(e[a]||[]).forEach(function(e){t[t.length]=or("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:Fa[a][r.bookType||"xlsx"]})})};var s=function(r){(e[r]||[]).forEach(function(e){t[t.length]=or("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:Da[r][0]})})};n("workbooks");i("sheets");i("charts");s("themes");["strs","styles"].forEach(n);["coreprops","extprops","custprops"].forEach(s);s("vba");s("comments");s("drawings");if(t.length>2){t[t.length]="</Types>";t[1]=t[1].replace("/>",">")}return t.join("")}var Ua={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Ha(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function Wa(e,r){var t={"!id":{}};if(!e)return t;if(r.charAt(0)!=="/"){r="/"+r}var a={};(e.match(Re)||[]).forEach(function(e){var n=Oe(e);if(n[0]==="<Relationship"){var i={};i.Type=n.Type;i.Target=n.Target;i.Id=n.Id;if(n.TargetMode)i.TargetMode=n.TargetMode;var s=n.TargetMode==="External"?n.Target:Ae(n.Target,r);t[s]=i;a[n.Id]=i}});t["!id"]=a;return t}hr.RELS="http://schemas.openxmlformats.org/package/2006/relationships";var Va=or("Relationships",null,{xmlns:hr.RELS});function Xa(e){var r=[ye,Va];K(e["!id"]).forEach(function(t){r[r.length]=or("Relationship",null,e["!id"][t])});if(r.length>2){r[r.length]="</Relationships>";r[1]=r[1].replace("/>",">")}return r.join("")}var Ga=[Ua.HLINK,Ua.XPATH,Ua.XMISS];function za(e,r,t,a,n,i){if(!n)n={};if(!e["!id"])e["!id"]={};if(r<0)for(r=1;e["!id"]["rId"+r];++r){}n.Id="rId"+r;n.Type=a;n.Target=t;if(i)n.TargetMode=i;else if(Ga.indexOf(n.Type)>-1)n.TargetMode="External";if(e["!id"][n.Id])throw new Error("Cannot rewrite rId "+r);e["!id"][n.Id]=n;e[("/"+n.Target).replace("//","/")]=n;return r}var ja="application/vnd.oasis.opendocument.spreadsheet";function $a(e,r){var t=Tp(e);var a;var n;while(a=_p.exec(t))switch(a[3]){case"manifest":break;case"file-entry":n=Oe(a[0],false);if(n.path=="/"&&n.type!==ja)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":;case"algorithm":;case"start-key-generation":;case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw a;}}function Ka(e){var r=[ye];r.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n');r.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var t=0;t<e.length;++t)r.push('  <manifest:file-entry manifest:full-path="'+e[t][0]+'" manifest:media-type="'+e[t][1]+'"/>\n');r.push("</manifest:manifest>");return r.join("")}function Ya(e,r,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(t||"odf")+"#"+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Qa(e,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Ja(e){var r=[ye];r.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var t=0;t!=e.length;++t){r.push(Ya(e[t][0],e[t][1]));r.push(Qa("",e[t][0]))}r.push(Ya("","Document","pkg"));r.push("</rdf:RDF>");return r.join("")}var qa=function(){var r='<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>Sheet'+"JS "+e.version+"</meta:generator></office:meta></office:document-meta>";return function t(){return r}}();var Za=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];hr.CORE_PROPS="http://schemas.openxmlformats.org/package/2006/metadata/core-properties";Ua.CORE_PROPS="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties";var en=function(){var e=new Array(Za.length);for(var r=0;r<Za.length;++r){var t=Za[r];var a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function rn(e){var r={};e=Ye(e);for(var t=0;t<Za.length;++t){var a=Za[t],n=e.match(en[t]);if(n!=null&&n.length>0)r[a[1]]=Le(n[1]);if(a[2]==="date"&&r[a[1]])r[a[1]]=le(r[a[1]])}return r}var tn=or("cp:coreProperties",null,{"xmlns:cp":hr.CORE_PROPS,"xmlns:dc":hr.dc,"xmlns:dcterms":hr.dcterms,"xmlns:dcmitype":hr.dcmitype,"xmlns:xsi":hr.xsi});function an(e,r,t,a,n){if(n[e]!=null||r==null||r==="")return;n[e]=r;r=We(r);a[a.length]=t?or(e,r,t):fr(e,r)}function nn(e,r){var t=r||{};var a=[ye,tn],n={};if(!e&&!t.Props)return a.join("");if(e){if(e.CreatedDate!=null)an("dcterms:created",typeof e.CreatedDate==="string"?e.CreatedDate:cr(e.CreatedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n);if(e.ModifiedDate!=null)an("dcterms:modified",typeof e.ModifiedDate==="string"?e.ModifiedDate:cr(e.ModifiedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n)}for(var i=0;i!=Za.length;++i){var s=Za[i];var f=t.Props&&t.Props[s[1]]!=null?t.Props[s[1]]:e?e[s[1]]:null;if(f===true)f="1";else if(f===false)f="0";else if(typeof f=="number")f=String(f);if(f!=null)an(s[0],f,null,a,n)}if(a.length>2){a[a.length]="</cp:coreProperties>";a[1]=a[1].replace("/>",">")}return a.join("")}var sn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];hr.EXT_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties";Ua.EXT_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties";var fn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ln(e,r,t,a){var n=[];if(typeof e=="string")n=ir(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(e){return{v:e}}));var s=typeof r=="string"?ir(r,a).map(function(e){return e.v}):r;var f=0,l=0;if(s.length>0)for(var o=0;o!==n.length;o+=2){l=+n[o+1].v;switch(n[o].v){case"Worksheets":;case"工作表":;case"Листы":;case"أوراق العمل":;case"ワークシート":;case"גליונות עבודה":;case"Arbeitsblätter":;case"Çalışma Sayfaları":;case"Feuilles de calcul":;case"Fogli di lavoro":;case"Folhas de cálculo":;case"Planilhas":;case"Regneark":;case"Hojas de cálculo":;case"Werkbladen":t.Worksheets=l;t.SheetNames=s.slice(f,f+l);break;case"Named Ranges":;case"Rangos con nombre":;case"名前付き一覧":;case"Benannte Bereiche":;case"Navngivne områder":t.NamedRanges=l;t.DefinedNames=s.slice(f,f+l);break;case"Charts":;case"Diagramme":t.Chartsheets=l;t.ChartNames=s.slice(f,f+l);break;}f+=l}}function on(e,r,t){var a={};if(!r)r={};e=Ye(e);sn.forEach(function(t){var n=(e.match(er(t[0]))||[])[1];switch(t[2]){case"string":if(n)r[t[1]]=Le(n);break;case"bool":r[t[1]]=n==="true";break;case"raw":var i=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));if(i&&i.length>0)a[t[1]]=i[1];break;}});if(a.HeadingPairs&&a.TitlesOfParts)ln(a.HeadingPairs,a.TitlesOfParts,r,t);return r}var cn=or("Properties",null,{xmlns:hr.EXT_PROPS,"xmlns:vt":hr.vt});function un(e){var r=[],t=or;if(!e)e={};e.Application="SheetJS";r[r.length]=ye;r[r.length]=cn;sn.forEach(function(a){if(e[a[1]]===undefined)return;var n;switch(a[2]){case"string":n=We(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break;}if(n!==undefined)r[r.length]=t(a[0],n)});r[r.length]=t("HeadingPairs",t("vt:vector",t("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+t("vt:variant",t("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"}));r[r.length]=t("TitlesOfParts",t("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+We(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"}));if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}hr.CUST_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties";Ua.CUST_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties";var hn=/<[^>]+>[^<]*/g;function dn(e,r){var t={},a="";var n=e.match(hn);if(n)for(var i=0;i!=n.length;++i){var s=n[i],f=Oe(s);switch(f[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Le(f.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var l=s.split(">");var o=l[0].slice(4),c=l[1];switch(o){case"lpstr":;case"bstr":;case"lpwstr":t[a]=Le(c);break;case"bool":t[a]=Ke(c);break;case"i1":;case"i2":;case"i4":;case"i8":;case"int":;case"uint":t[a]=parseInt(c,10);break;case"r4":;case"r8":;case"decimal":t[a]=parseFloat(c);break;case"filetime":;case"date":t[a]=le(c);break;case"cy":;case"error":t[a]=Le(c);break;default:if(o.slice(-1)=="/")break;if(r.WTF&&typeof console!=="undefined")console.warn("Unexpected",s,o,l);}}else if(s.slice(0,2)==="</"){}else if(r.WTF)throw new Error(s);}}return t}var vn=or("Properties",null,{xmlns:hr.CUST_PROPS,"xmlns:vt":hr.vt});function pn(e){var r=[ye,vn];if(!e)return r.join("");var t=1;K(e).forEach(function a(n){++t;r[r.length]=or("property",ur(e[n],true),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:t,name:We(n)})});if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}var bn={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};var mn=Q(bn);function gn(e,r,t){r=mn[r]||r;e[r]=t}function En(e,r){var t=[];K(bn).map(function(e){for(var r=0;r<Za.length;++r)if(Za[r][1]==e)return Za[r];for(r=0;r<sn.length;++r)if(sn[r][1]==e)return sn[r];throw e}).forEach(function(a){if(e[a[1]]==null)return;var n=r&&r.Props&&r.Props[a[1]]!=null?r.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break;}if(typeof n=="number")n=String(n);else if(n===true||n===false){n=n?"1":"0"}else if(n instanceof Date)n=new Date(n).toISOString().replace(/\.\d*Z/,"");t.push(fr(bn[a[1]]||a[1],n))});return or("DocumentProperties",t.join(""),{xmlns:dr.o})}function wn(e,r){var t=["Worksheets","SheetNames"];var a="CustomDocumentProperties";var n=[];if(e)K(e).forEach(function(r){if(!Object.prototype.hasOwnProperty.call(e,r))return;for(var a=0;a<Za.length;++a)if(r==Za[a][1])return;for(a=0;a<sn.length;++a)if(r==sn[a][1])return;for(a=0;a<t.length;++a)if(r==t[a])return;var i=e[r];var s="string";if(typeof i=="number"){s="float";i=String(i)}else if(i===true||i===false){s="boolean";i=i?"1":"0"}else i=String(i);n.push(or(Ve(r),i,{"dt:dt":s}))});if(r)K(r).forEach(function(t){if(!Object.prototype.hasOwnProperty.call(r,t))return;if(e&&Object.prototype.hasOwnProperty.call(e,t))return;var a=r[t];var i="string";if(typeof a=="number"){i="float";a=String(a)}else if(a===true||a===false){i="boolean";a=a?"1":"0"}else if(a instanceof Date){i="dateTime.tz";a=a.toISOString()}else a=String(a);n.push(or(Ve(t),a,{"dt:dt":i}))});return"<"+a+' xmlns="'+dr.o+'">'+n.join("")+"</"+a+">"}function kn(e){var r=e._R(4),t=e._R(4);return new Date((t/1e7*Math.pow(2,32)+r/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function Sn(e){var r=typeof e=="string"?new Date(Date.parse(e)):e;var t=r.getTime()/1e3+11644473600;var a=t%Math.pow(2,32),n=(t-a)/Math.pow(2,32);a*=1e7;n*=1e7;var i=a/Math.pow(2,32)|0;if(i>0){a=a%Math.pow(2,32);n+=i}var s=Jr(8);s._W(4,a);s._W(4,n);return s}function Bn(e,r,t){var a=e.l;var n=e._R(0,"lpstr-cp");if(t)while(e.l-a&3)++e.l;return n}function Cn(e,r,t){var a=e._R(0,"lpwstr");if(t)e.l+=4-(a.length+1&3)&3;return a}function Tn(e,r,t){if(r===31)return Cn(e);return Bn(e,r,t)}function _n(e,r,t){return Tn(e,r,t===false?0:4)}function xn(e,r){if(!r)throw new Error("VtUnalignedString must have positive length");return Tn(e,r,0)}function An(e){var r=e._R(4);var t=[];for(var a=0;a!=r;++a){var n=e.l;t[a]=e._R(0,"lpwstr").replace(R,"");if(e.l-n&2)e.l+=2}return t}function yn(e){var r=e._R(4);var t=[];for(var a=0;a!=r;++a)t[a]=e._R(0,"lpstr-cp").replace(R,"");return t}function In(e){var r=e.l;var t=Pn(e,ga);if(e[e.l]==0&&e[e.l+1]==0&&e.l-r&2)e.l+=2;var a=Pn(e,la);return[t,a]}function Rn(e){var r=e._R(4);var t=[];for(var a=0;a<r/2;++a)t.push(In(e));return t}function Fn(e,r){var t=e._R(4);var a={};for(var n=0;n!=t;++n){var i=e._R(4);var s=e._R(4);a[i]=e._R(s,r===1200?"utf16le":"utf8").replace(R,"").replace(F,"!");if(r===1200&&s%2)e.l+=2}if(e.l&3)e.l=e.l>>2+1<<2;return a}function Dn(e){var r=e._R(4);var t=e.slice(e.l,e.l+r);e.l+=r;if((r&3)>0)e.l+=4-(r&3)&3;return t}function On(e){var r={};r.Size=e._R(4);e.l+=r.Size+3-(r.Size-1)%4;return r}function Pn(e,r,t){var a=e._R(2),n,i=t||{};e.l+=2;if(r!==ca)if(a!==r&&Ea.indexOf(r)===-1&&!((r&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+r+" saw "+a);switch(r===ca?a:r){case 2:n=e._R(2,"i");if(!i.raw)e.l+=2;return n;case 3:n=e._R(4,"i");return n;case 11:return e._R(4)!==0;case 19:n=e._R(4);return n;case 30:return Bn(e,a,4).replace(R,"");case 31:return Cn(e);case 64:return kn(e);case 65:return Dn(e);case 71:return On(e);case 80:return _n(e,a,!i.raw).replace(R,"");case 81:return xn(e,a).replace(R,"");case 4108:return Rn(e);case 4126:;case 4127:return a==4127?An(e):yn(e);default:throw new Error("TypedPropertyValue unrecognized type "+r+" "+a);}}function Nn(e,r){var t=Jr(4),a=Jr(4);t._W(4,e==80?31:e);switch(e){case 3:a._W(-4,r);break;case 5:a=Jr(8);a._W(8,r,"f");break;case 11:a._W(4,r?1:0);break;case 64:a=Sn(r);break;case 31:;case 80:a=Jr(4+2*(r.length+1)+(r.length%2?0:2));a._W(4,r.length+1);a._W(0,r,"dbcs");while(a.l!=a.length)a._W(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+r);}return I([t,a])}function Mn(e,r){var t=e.l;var a=e._R(4);var n=e._R(4);var i=[],s=0;var f=0;var o=-1,c={};for(s=0;s!=n;++s){var u=e._R(4);var h=e._R(4);i[s]=[u,h+t]}i.sort(function(e,r){return e[1]-r[1]});var d={};for(s=0;s!=n;++s){if(e.l!==i[s][1]){var v=true;if(s>0&&r)switch(r[i[s-1][0]].t){case 2:if(e.l+2===i[s][1]){e.l+=2;v=false}break;case 80:if(e.l<=i[s][1]){e.l=i[s][1];v=false}break;case 4108:if(e.l<=i[s][1]){e.l=i[s][1];v=false}break;}if((!r||s==0)&&e.l<=i[s][1]){v=false;e.l=i[s][1]}if(v)throw new Error("Read Error: Expected address "+i[s][1]+" at "+e.l+" :"+s)}if(r){var p=r[i[s][0]];d[p.n]=Pn(e,p.t,{raw:true});if(p.p==="version")d[p.n]=String(d[p.n]>>16)+"."+("0000"+String(d[p.n]&65535)).slice(-4);if(p.n=="CodePage")switch(d[p.n]){case 0:d[p.n]=1252;case 874:;case 932:;case 936:;case 949:;case 950:;case 1250:;case 1251:;case 1253:;case 1254:;case 1255:;case 1256:;case 1257:;case 1258:;case 1e4:;case 1200:;case 1201:;case 1252:;case 65e3:;case-536:;case 65001:;case-535:l(f=d[p.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[p.n]);}}else{if(i[s][0]===1){f=d.CodePage=Pn(e,fa);l(f);if(o!==-1){var b=e.l;e.l=i[o][1];c=Fn(e,f);e.l=b}}else if(i[s][0]===0){if(f===0){o=s;e.l=i[s+1][1];continue}c=Fn(e,f)}else{var m=c[i[s][0]];var g;switch(e[e.l]){case 65:e.l+=4;g=Dn(e);break;case 30:e.l+=4;g=_n(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4;g=_n(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4;g=e._R(4,"i");break;case 19:e.l+=4;g=e._R(4);break;case 5:e.l+=4;g=e._R(8,"f");break;case 11:e.l+=4;g=jn(e,4);break;case 64:e.l+=4;g=le(kn(e));break;default:throw new Error("unparsed value: "+e[e.l]);}d[m]=g}}}e.l=t+a;return d}var Ln=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"].concat(fn);function Un(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break;}return-1}function Hn(e,r,t){var a=Jr(8),n=[],i=[];var s=8,f=0;var l=Jr(8),o=Jr(8);l._W(4,2);l._W(4,1200);o._W(4,1);i.push(l);n.push(o);s+=8+l.length;if(!r){o=Jr(8);o._W(4,0);n.unshift(o);var c=[Jr(4)];c[0]._W(4,e.length);for(f=0;f<e.length;++f){var u=e[f][0];l=Jr(4+4+2*(u.length+1)+(u.length%2?0:2));l._W(4,f+2);l._W(4,u.length+1);l._W(0,u,"dbcs");while(l.l!=l.length)l._W(1,0);c.push(l)}l=I(c);i.unshift(l);s+=8+l.length}for(f=0;f<e.length;++f){if(r&&!r[e[f][0]])continue;if(Ln.indexOf(e[f][0])>-1)continue;if(e[f][1]==null)continue;var h=e[f][1],d=0;if(r){d=+r[e[f][0]];var v=t[d];if(v.p=="version"&&typeof h=="string"){var p=h.split(".");h=(+p[0]<<16)+(+p[1]||0)}l=Nn(v.t,h)}else{var b=Un(h);if(b==-1){b=31;h=String(h)}l=Nn(b,h)}i.push(l);o=Jr(8);o._W(4,!r?2+f:d);n.push(o);s+=8+l.length}var m=8*(i.length+1);for(f=0;f<i.length;++f){n[f]._W(4,m);m+=i[f].length}a._W(4,s);a._W(4,i.length);return I([a].concat(n).concat(i))}function Wn(e,r,t){var a=e.content;if(!a)return{};Yr(a,0);var n,i,s,f,l=0;a.chk("feff","Byte Order: ");a._R(2);var o=a._R(4);var c=a._R(16);if(c!==V.utils.consts.HEADER_CLSID&&c!==t)throw new Error("Bad PropertySet CLSID "+c);n=a._R(4);if(n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);i=a._R(16);f=a._R(4);if(n===1&&f!==a.l)throw new Error("Length mismatch: "+f+" !== "+a.l);else if(n===2){s=a._R(16);l=a._R(4)}var u=Mn(a,r);var h={SystemIdentifier:o};for(var d in u)h[d]=u[d];h.FMTID=i;if(n===1)return h;if(l-a.l==2)a.l+=2;if(a.l!==l)throw new Error("Length mismatch 2: "+a.l+" !== "+l);var v;try{v=Mn(a,null)}catch(p){}for(d in v)h[d]=v[d];h.FMTID=[i,s];return h}function Vn(e,r,t,a,n,i){var s=Jr(n?68:48);var f=[s];s._W(2,65534);s._W(2,0);s._W(4,842412599);s._W(16,V.utils.consts.HEADER_CLSID,"hex");s._W(4,n?2:1);s._W(16,r,"hex");s._W(4,n?68:48);var l=Hn(e,t,a);f.push(l);if(n){var o=Hn(n,null,null);s._W(16,i,"hex");s._W(4,68+l.length);f.push(o)}return I(f)}function Xn(e,r){e._R(r);return null}function Gn(e,r){if(!r)r=Jr(e);for(var t=0;t<e;++t)r._W(1,0);return r}function zn(e,r,t){var a=[],n=e.l+r;while(e.l<n)a.push(t(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function jn(e,r){return e._R(r)===1}function $n(e,r){if(!r)r=Jr(2);r._W(2,+!!e);return r}function Kn(e){return e._R(2,"u")}function Yn(e,r){if(!r)r=Jr(2);r._W(2,e);return r}function Qn(e,r){return zn(e,r,Kn)}function Jn(e){var r=e._R(1),t=e._R(1);return t===1?r:r===1}function qn(e,r,t){if(!t)t=Jr(2);t._W(1,r=="e"?+e:+!!e);t._W(1,r=="e"?1:0);return t}function Zn(e,t,a){var n=e._R(a&&a.biff>=12?2:1);var i="sbcs-cont";var s=r;if(a&&a.biff>=8)r=1200;if(!a||a.biff==8){var f=e._R(1);if(f){i="dbcs-cont"}}else if(a.biff==12){i="wstr"}if(a.biff>=2&&a.biff<=5)i="cpstr";var l=n?e._R(n,i):"";r=s;return l}function ei(e){var t=r;r=1200;var a=e._R(2),n=e._R(1);var i=n&4,s=n&8;var f=1+(n&1);var l=0,o;var c={};if(s)l=e._R(2);if(i)o=e._R(4);var u=f==2?"dbcs-cont":"sbcs-cont";var h=a===0?"":e._R(a,u);if(s)e.l+=4*l;if(i)e.l+=o;c.t=h;if(!s){c.raw="<t>"+c.t+"</t>";c.r=c.t}r=t;return c}function ri(e){var r=e.t||"",t=1;var a=Jr(3+(t>1?2:0));a._W(2,r.length);a._W(1,(t>1?8:0)|1);if(t>1)a._W(2,t);var n=Jr(2*r.length);n._W(2*r.length,r,"utf16le");var i=[a,n];return I(i)}function ti(e,r,t){var a;if(t){if(t.biff>=2&&t.biff<=5)return e._R(r,"cpstr");if(t.biff>=12)return e._R(r,"dbcs-cont")}var n=e._R(1);if(n===0){a=e._R(r,"sbcs-cont")}else{a=e._R(r,"dbcs-cont")}return a}function ai(e,r,t){var a=e._R(t&&t.biff==2?1:2);if(a===0){e.l++;return""}return ti(e,a,t)}function ni(e,r,t){if(t.biff>5)return ai(e,r,t);var a=e._R(1);if(a===0){e.l++;return""}return e._R(a,t.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function ii(e,r,t){if(!t)t=Jr(3+2*e.length);t._W(2,e.length);t._W(1,1);t._W(31,e,"utf16le");return t}function si(e){var r=e._R(1);e.l++;var t=e._R(2);e.l+=2;return[r,t]}function fi(e){var r=e._R(4),t=e.l;var a=false;if(r>24){e.l+=r-24;if(e._R(16)==="795881f43b1d7f48af2c825dc4852763")a=true;e.l=t}var n=e._R((a?r-24:r)>>1,"utf16le").replace(R,"");if(a)e.l+=24;return n}function li(e){var r=e._R(2);var t="";while(r-- >0)t+="../";var a=e._R(0,"lpstr-ansi");e.l+=2;if(e._R(2)!=57005)throw new Error("Bad FileMoniker");var n=e._R(4);if(n===0)return t+a.replace(/\\/g,"/");var i=e._R(4);if(e._R(2)!=3)throw new Error("Bad FileMoniker");var s=e._R(i>>1,"utf16le").replace(R,"");return t+s}function oi(e,r){var t=e._R(16);r-=16;switch(t){case"e0c9ea79f9bace118c8200aa004ba90b":return fi(e,r);case"0303000000000000c000000000000046":return li(e,r);default:throw new Error("Unsupported Moniker "+t);}}function ci(e){var r=e._R(4);var t=r>0?e._R(r,"utf16le").replace(R,""):"";return t}function ui(e,r){if(!r)r=Jr(6+e.length*2);r._W(4,1+e.length);for(var t=0;t<e.length;++t)r._W(2,e.charCodeAt(t));r._W(2,0);return r}function hi(e,r){var t=e.l+r;var a=e._R(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e._R(2);e.l+=2;var i,s,f,l,o="",c,u;if(n&16)i=ci(e,t-e.l);if(n&128)s=ci(e,t-e.l);if((n&257)===257)f=ci(e,t-e.l);if((n&257)===1)l=oi(e,t-e.l);if(n&8)o=ci(e,t-e.l);if(n&32)c=e._R(16);if(n&64)u=kn(e);e.l=t;var h=s||f||l||"";if(h&&o)h+="#"+o;if(!h)h="#"+o;if(n&2&&h.charAt(0)=="/"&&h.charAt(1)!="/")h="file://"+h;var d={Target:h};if(c)d.guid=c;if(u)d.time=u;if(i)d.Tooltip=i;return d}function di(e){var r=Jr(512),t=0;var a=e.Target;if(a.slice(0,7)=="file://")a=a.slice(7);var n=a.indexOf("#");var i=n>-1?31:23;switch(a.charAt(0)){case"#":i=28;break;case".":i&=~2;break;}r._W(4,2);r._W(4,i);var s=[8,6815827,6619237,4849780,83];for(t=0;t<s.length;++t)r._W(4,s[t]);if(i==28){a=a.slice(1);ui(a,r)}else if(i&2){s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");for(t=0;t<s.length;++t)r._W(1,parseInt(s[t],16));var f=n>-1?a.slice(0,n):a;r._W(4,2*(f.length+1));for(t=0;t<f.length;++t)r._W(2,f.charCodeAt(t));r._W(2,0);if(i&8)ui(n>-1?a.slice(n+1):"",r)}else{s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");for(t=0;t<s.length;++t)r._W(1,parseInt(s[t],16));var l=0;while(a.slice(l*3,l*3+3)=="../"||a.slice(l*3,l*3+3)=="..\\")++l;r._W(2,l);r._W(4,a.length-3*l+1);for(t=0;t<a.length-3*l;++t)r._W(1,a.charCodeAt(t+3*l)&255);r._W(1,0);r._W(2,65535);r._W(2,57005);for(t=0;t<6;++t)r._W(4,0)}return r.slice(0,r.l)}function vi(e){var r=e._R(1),t=e._R(1),a=e._R(1),n=e._R(1);return[r,t,a,n]}function pi(e,r){var t=vi(e,r);t[3]=0;return t}function bi(e){var r=e._R(2);var t=e._R(2);var a=e._R(2);return{r:r,c:t,ixfe:a}}function mi(e,r,t,a){if(!a)a=Jr(6);a._W(2,e);a._W(2,r);a._W(2,t||0);return a}function gi(e){var r=e._R(2);var t=e._R(2);e.l+=8;return{type:r,flags:t}}function Ei(e,r,t){return r===0?"":ni(e,r,t)}function wi(e,r,t){var a=t.biff>8?4:2;var n=e._R(a),i=e._R(a,"i"),s=e._R(a,"i");return[n,i,s]}function ki(e){var r=e._R(2);var t=jt(e);return[r,t]}function Si(e,r,t){e.l+=4;r-=4;var a=e.l+r;var n=Zn(e,r,t);var i=e._R(2);a-=e.l;if(i!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+i);e.l+=i;return n}function Bi(e){var r=e._R(2);var t=e._R(2);var a=e._R(2);var n=e._R(2);return{s:{c:a,r:r},e:{c:n,r:t}}}function Ci(e,r){if(!r)r=Jr(8);r._W(2,e.s.r);r._W(2,e.e.r);r._W(2,e.s.c);r._W(2,e.e.c);return r}function Ti(e){var r=e._R(2);var t=e._R(2);var a=e._R(1);var n=e._R(1);return{s:{c:a,r:r},e:{c:n,r:t}}}var _i=Ti;function xi(e){e.l+=4;var r=e._R(2);var t=e._R(2);var a=e._R(2);e.l+=12;return[t,r,a]}function Ai(e){var r={};e.l+=4;e.l+=16;r.fSharedNote=e._R(2);e.l+=4;return r}function yi(e){var r={};e.l+=4;e.cf=e._R(2);return r}function Ii(e){e.l+=2;e.l+=e._R(2)}var Ri={0:Ii,4:Ii,5:Ii,6:Ii,7:yi,8:Ii,9:Ii,10:Ii,11:Ii,12:Ii,13:Ai,14:Ii,15:Ii,16:Ii,17:Ii,18:Ii,19:Ii,20:Ii,21:xi};function Fi(e,r){var t=e.l+r;var a=[];while(e.l<t){var n=e._R(2);e.l-=2;try{a.push(Ri[n](e,t-e.l))}catch(i){e.l=t;return a}}if(e.l!=t)e.l=t;return a}function Di(e,r){var t={BIFFVer:0,dt:0};t.BIFFVer=e._R(2);r-=2;if(r>=2){t.dt=e._R(2);e.l-=2}switch(t.BIFFVer){case 1536:;case 1280:;case 1024:;case 768:;case 512:;case 2:;case 7:break;default:if(r>6)throw new Error("Unexpected BIFF Ver "+t.BIFFVer);}e._R(r);return t}function Oi(e,r,t){var a=1536,n=16;switch(t.bookType){case"biff8":break;case"biff5":a=1280;n=8;break;case"biff4":a=4;n=6;break;case"biff3":a=3;n=6;break;case"biff2":a=2;n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version");}var i=Jr(n);i._W(2,a);i._W(2,r);if(n>4)i._W(2,29282);if(n>6)i._W(2,1997);if(n>8){i._W(2,49161);i._W(2,1);i._W(2,1798);i._W(2,0)}return i}function Pi(e,r){if(r===0)return 1200;if(e._R(2)!==1200){}return 1200}function Ni(e,r,t){if(t.enc){e.l+=r;return""}var a=e.l;var n=ni(e,0,t);e._R(r+a-e.l);return n}function Mi(e,r){var t=!r||r.biff==8;var a=Jr(t?112:54);a._W(r.biff==8?2:1,7);if(t)a._W(1,0);a._W(4,859007059);a._W(4,5458548|(t?0:536870912));while(a.l<a.length)a._W(1,t?0:32);return a}function Li(e,r,t){var a=t&&t.biff==8||r==2?e._R(2):(e.l+=r,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function Ui(e,r,t){var a=e._R(4);var n=e._R(1)&3;var i=e._R(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule";break;}var s=Zn(e,0,t);if(s.length===0)s="Sheet1";return{pos:a,hs:n,dt:i,name:s}}function Hi(e,r){var t=!r||r.biff>=8?2:1;var a=Jr(8+t*e.name.length);a._W(4,e.pos);a._W(1,e.hs||0);a._W(1,e.dt);a._W(1,e.name.length);if(r.biff>=8)a._W(1,1);a._W(t*e.name.length,e.name,r.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);n.l=a.l;return n}function Wi(e,r){var t=e.l+r;var a=e._R(4);var n=e._R(4);var i=[];for(var s=0;s!=n&&e.l<t;++s){i.push(ei(e))}i.Count=a;i.Unique=n;return i}function Vi(e,r){var t=Jr(8);t._W(4,e.Count);t._W(4,e.Unique);var a=[];for(var n=0;n<e.length;++n)a[n]=ri(e[n],r);var i=I([t].concat(a));i.parts=[t.length].concat(a.map(function(e){return e.length}));return i}function Xi(e,r){var t={};t.dsst=e._R(2);e.l+=r-2;return t}function Gi(e){var r={};r.r=e._R(2);r.c=e._R(2);r.cnt=e._R(2)-r.c;var t=e._R(2);e.l+=4;var a=e._R(1);e.l+=3;if(a&7)r.level=a&7;if(a&32)r.hidden=true;if(a&64)r.hpt=t/20;return r}function zi(e){var r=gi(e);if(r.type!=2211)throw new Error("Invalid Future Record "+r.type);var t=e._R(4);return t!==0}function ji(e){e._R(2);return e._R(4)}function $i(e,r,t){var a=0;if(!(t&&t.biff==2)){a=e._R(2)}var n=e._R(2);if(t&&t.biff==2){a=1-(n>>15);n&=32767}var i={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[i,n]}function Ki(e){var r=e._R(2),t=e._R(2),a=e._R(2),n=e._R(2);var i=e._R(2),s=e._R(2),f=e._R(2);var l=e._R(2),o=e._R(2);return{Pos:[r,t],Dim:[a,n],Flags:i,CurTab:s,FirstTab:f,Selected:l,TabRatio:o}}function Yi(){var e=Jr(18);e._W(2,0);e._W(2,0);e._W(2,29280);e._W(2,17600);e._W(2,56);e._W(2,0);e._W(2,0);e._W(2,1);e._W(2,500);return e}function Qi(e,r,t){if(t&&t.biff>=2&&t.biff<5)return{};var a=e._R(2);return{RTL:a&64}}function Ji(e){var r=Jr(18),t=1718;if(e&&e.RTL)t|=64;r._W(2,t);r._W(4,0);r._W(4,64);r._W(4,0);r._W(4,0);return r}function qi(){}function Zi(e,r,t){var a={dyHeight:e._R(2),fl:e._R(2)};switch(t&&t.biff||8){case 2:break;case 3:;case 4:e.l+=2;break;default:e.l+=10;break;}a.name=Zn(e,0,t);return a}function es(e,r){var t=e.name||"Arial";var a=r&&r.biff==5,n=a?15+t.length:16+2*t.length;var i=Jr(n);i._W(2,(e.sz||12)*20);i._W(4,0);i._W(2,400);i._W(4,0);i._W(2,0);i._W(1,t.length);if(!a)i._W(1,1);i._W((a?1:2)*t.length,t,a?"sbcs":"utf16le");return i}function rs(e){var r=bi(e);r.isst=e._R(4);return r}function ts(e,r,t,a){var n=Jr(10);mi(e,r,a,n);n._W(4,t);return n}function as(e,r,t){if(t.biffguess&&t.biff==2)t.biff=5;var a=e.l+r;var n=bi(e,6);if(t.biff==2)e.l++;var i=ai(e,a-e.l,t);n.val=i;return n}function ns(e,r,t,a,n){var i=!n||n.biff==8;var s=Jr(6+2+ +i+(1+i)*t.length);mi(e,r,a,s);s._W(2,t.length);if(i)s._W(1,1);s._W((1+i)*t.length,t,i?"utf16le":"sbcs");
return s}function is(e,r,t){var a=e._R(2);var n=ni(e,0,t);return[a,n]}function ss(e,r,t,a){var n=t&&t.biff==5;if(!a)a=Jr(n?3+r.length:5+2*r.length);a._W(2,e);a._W(n?1:2,r.length);if(!n)a._W(1,1);a._W((n?1:2)*r.length,r,n?"sbcs":"utf16le");var i=a.length>a.l?a.slice(0,a.l):a;if(i.l==null)i.l=i.length;return i}var fs=ni;function ls(e,r,t){var a=e.l+r;var n=t.biff==8||!t.biff?4:2;var i=e._R(n),s=e._R(n);var f=e._R(2),l=e._R(2);e.l=a;return{s:{r:i,c:f},e:{r:s,c:l}}}function os(e,r){var t=r.biff==8||!r.biff?4:2;var a=Jr(2*t+6);a._W(t,e.s.r);a._W(t,e.e.r+1);a._W(2,e.s.c);a._W(2,e.e.c+1);a._W(2,0);return a}function cs(e){var r=e._R(2),t=e._R(2);var a=ki(e);return{r:r,c:t,ixfe:a[0],rknum:a[1]}}function us(e,r){var t=e.l+r-2;var a=e._R(2),n=e._R(2);var i=[];while(e.l<t)i.push(ki(e));if(e.l!==t)throw new Error("MulRK read error");var s=e._R(2);if(i.length!=s-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:s,rkrec:i}}function hs(e,r){var t=e.l+r-2;var a=e._R(2),n=e._R(2);var i=[];while(e.l<t)i.push(e._R(2));if(e.l!==t)throw new Error("MulBlank read error");var s=e._R(2);if(i.length!=s-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:s,ixfe:i}}function ds(e,r,t,a){var n={};var i=e._R(4),s=e._R(4);var f=e._R(4),l=e._R(2);n.patternType=Ta[f>>26];if(!a.cellStyles)return n;n.alc=i&7;n.fWrap=i>>3&1;n.alcV=i>>4&7;n.fJustLast=i>>7&1;n.trot=i>>8&255;n.cIndent=i>>16&15;n.fShrinkToFit=i>>20&1;n.iReadOrder=i>>22&2;n.fAtrNum=i>>26&1;n.fAtrFnt=i>>27&1;n.fAtrAlc=i>>28&1;n.fAtrBdr=i>>29&1;n.fAtrPat=i>>30&1;n.fAtrProt=i>>31&1;n.dgLeft=s&15;n.dgRight=s>>4&15;n.dgTop=s>>8&15;n.dgBottom=s>>12&15;n.icvLeft=s>>16&127;n.icvRight=s>>23&127;n.grbitDiag=s>>30&3;n.icvTop=f&127;n.icvBottom=f>>7&127;n.icvDiag=f>>14&127;n.dgDiag=f>>21&15;n.icvFore=l&127;n.icvBack=l>>7&127;n.fsxButton=l>>14&1;return n}function vs(e,r,t){var a={};a.ifnt=e._R(2);a.numFmtId=e._R(2);a.flags=e._R(2);a.fStyle=a.flags>>2&1;r-=6;a.data=ds(e,r,a.fStyle,t);return a}function ps(e,r,t,a){var n=t&&t.biff==5;if(!a)a=Jr(n?16:20);a._W(2,0);if(e.style){a._W(2,e.numFmtId||0);a._W(2,65524)}else{a._W(2,e.numFmtId||0);a._W(2,r<<4)}var i=0;if(e.numFmtId>0&&n)i|=1024;a._W(4,i);a._W(4,0);if(!n)a._W(4,0);a._W(2,0);return a}function bs(e){e.l+=4;var r=[e._R(2),e._R(2)];if(r[0]!==0)r[0]--;if(r[1]!==0)r[1]--;if(r[0]>7||r[1]>7)throw new Error("Bad Gutters: "+r.join("|"));return r}function ms(e){var r=Jr(8);r._W(4,0);r._W(2,e[0]?e[0]+1:0);r._W(2,e[1]?e[1]+1:0);return r}function gs(e,r,t){var a=bi(e,6);if(t.biff==2||r==9)++e.l;var n=Jn(e,2);a.val=n;a.t=n===true||n===false?"b":"e";return a}function Es(e,r,t,a,n,i){var s=Jr(8);mi(e,r,a,s);qn(t,i,s);return s}function ws(e,r,t){if(t.biffguess&&t.biff==2)t.biff=5;var a=bi(e,6);var n=qt(e,8);a.val=n;return a}function ks(e,r,t,a){var n=Jr(14);mi(e,r,a,n);Zt(t,n);return n}var Ss=Ei;function Bs(e,r,t){var a=e.l+r;var n=e._R(2);var i=e._R(2);t.sbcch=i;if(i==1025||i==14849)return[i,n];if(i<1||i>255)throw new Error("Unexpected SupBook type: "+i);var s=ti(e,i);var f=[];while(a>e.l)f.push(ai(e));return[i,n,s,f]}function Cs(e,r,t){var a=e._R(2);var n;var i={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};if(t.sbcch===14849)n=Si(e,r-2,t);i.body=n||e._R(r-2);if(typeof n==="string")i.Name=n;return i}var Ts=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function _s(e,r,t){var a=e.l+r;var n=e._R(2);var i=e._R(1);var s=e._R(1);var f=e._R(t&&t.biff==2?1:2);var l=0;if(!t||t.biff>=5){if(t.biff!=5)e.l+=2;l=e._R(2);if(t.biff==5)e.l+=2;e.l+=4}var o=ti(e,s,t);if(n&32)o=Ts[o.charCodeAt(0)];var c=a-e.l;if(t&&t.biff==2)--c;var u=a==e.l||f===0||!(c>0)?[]:zu(e,c,t,f);return{chKey:i,Name:o,itab:l,rgce:u}}function xs(e,r,t){if(t.biff<8)return As(e,r,t);var a=[],n=e.l+r,i=e._R(t.biff>8?4:2);while(i--!==0)a.push(wi(e,t.biff>8?12:6,t));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function As(e,r,t){if(e[e.l+1]==3)e[e.l]++;var a=Zn(e,r,t);return a.charCodeAt(0)==3?a.slice(1):a}function ys(e,r,t){if(t.biff<8){e.l+=r;return}var a=e._R(2);var n=e._R(2);var i=ti(e,a,t);var s=ti(e,n,t);return[i,s]}function Is(e,r,t){var a=Ti(e,6);e.l++;var n=e._R(1);r-=8;return[ju(e,r,t),n,a]}function Rs(e,r,t){var a=_i(e,6);switch(t.biff){case 2:e.l++;r-=7;break;case 3:;case 4:e.l+=2;r-=8;break;default:e.l+=6;r-=12;}return[a,Xu(e,r,t,a)]}function Fs(e){var r=e._R(4)!==0;var t=e._R(4)!==0;var a=e._R(4);return[r,t,a]}function Ds(e,r,t){if(t.biff<8)return;var a=e._R(2),n=e._R(2);var i=e._R(2),s=e._R(2);var f=ni(e,0,t);if(t.biff<8)e._R(1);return[{r:a,c:n},f,s,i]}function Os(e,r,t){return Ds(e,r,t)}function Ps(e,r){var t=[];var a=e._R(2);while(a--)t.push(Bi(e,r));return t}function Ns(e){var r=Jr(2+e.length*8);r._W(2,e.length);for(var t=0;t<e.length;++t)Ci(e[t],r);return r}function Ms(e,r,t){if(t&&t.biff<8)return Us(e,r,t);var a=xi(e,22);var n=Fi(e,r-22,a[1]);return{cmo:a,ft:n}}var Ls=[];Ls[8]=function(e,r){var t=e.l+r;e.l+=10;var a=e._R(2);e.l+=4;e.l+=2;e.l+=2;e.l+=2;e.l+=4;var n=e._R(1);e.l+=n;e.l=t;return{fmt:a}};function Us(e,r,t){e.l+=4;var a=e._R(2);var n=e._R(2);var i=e._R(2);e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=2;e.l+=6;r-=36;var s=[];s.push((Ls[a]||Qr)(e,r,t));return{cmo:[n,a,i],ft:s}}function Hs(e,r,t){var a=e.l;var n="";try{e.l+=4;var i=(t.lastobj||{cmo:[0,0]}).cmo[1];var s;if([0,5,7,11,12,14].indexOf(i)==-1)e.l+=6;else s=si(e,6,t);var f=e._R(2);e._R(2);Kn(e,2);var l=e._R(2);e.l+=l;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var c=e[e.l];var u=ti(e,e.lens[o+1]-e.lens[o]-1);n+=u;if(n.length>=(c?f:2*f))break}if(n.length!==f&&n.length!==f*2){throw new Error("cchText: "+f+" != "+n.length)}e.l=a+r;return{t:n}}catch(h){e.l=a+r;return{t:n}}}function Ws(e,r){var t=Bi(e,8);e.l+=16;var a=hi(e,r-24);return[t,a]}function Vs(e){var r=Jr(24);var t=bt(e[0]);r._W(2,t.r);r._W(2,t.r);r._W(2,t.c);r._W(2,t.c);var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");for(var n=0;n<16;++n)r._W(1,parseInt(a[n],16));return I([r,di(e[1])])}function Xs(e,r){e._R(2);var t=Bi(e,8);var a=e._R((r-10)/2,"dbcs-cont");a=a.replace(R,"");return[t,a]}function Gs(e){var r=e[1].Tooltip;var t=Jr(10+2*(r.length+1));t._W(2,2048);var a=bt(e[0]);t._W(2,a.r);t._W(2,a.r);t._W(2,a.c);t._W(2,a.c);for(var n=0;n<r.length;++n)t._W(2,r.charCodeAt(n));t._W(2,0);return t}function zs(e){var r=[0,0],t;t=e._R(2);r[0]=Ca[t]||t;t=e._R(2);r[1]=Ca[t]||t;return r}function js(e){if(!e)e=Jr(4);e._W(2,1);e._W(2,1);return e}function $s(e){var r=e._R(2);var t=[];while(r-- >0)t.push(pi(e,8));return t}function Ks(e){var r=e._R(2);var t=[];while(r-- >0)t.push(pi(e,8));return t}function Ys(e){e.l+=2;var r={cxfs:0,crc:0};r.cxfs=e._R(2);r.crc=e._R(4);return r}function Qs(e,r,t){if(!t.cellStyles)return Qr(e,r);var a=t&&t.biff>=12?4:2;var n=e._R(a);var i=e._R(a);var s=e._R(a);var f=e._R(a);var l=e._R(2);if(a==2)e.l+=2;var o={s:n,e:i,w:s,ixfe:f,flags:l};if(t.biff>=5||!t.biff)o.level=l>>8&7;return o}function Js(e,r){var t=Jr(12);t._W(2,r);t._W(2,r);t._W(2,e.width*256);t._W(2,0);var a=0;if(e.hidden)a|=1;t._W(1,a);a=e.level||0;t._W(1,a);t._W(2,0);return t}function qs(e,r){var t={};if(r<32)return t;e.l+=16;t.header=qt(e,8);t.footer=qt(e,8);e.l+=2;return t}function Zs(e,r,t){var a={area:false};if(t.biff!=5){e.l+=r;return a}var n=e._R(1);e.l+=3;if(n&16)a.area=true;return a}function ef(e){var r=Jr(2*e);for(var t=0;t<e;++t)r._W(2,t+1);return r}var rf=bi;var tf=Qn;var af=ai;function nf(e){var r=e._R(2);var t=e._R(2);var a=e._R(4);var n={fmt:r,env:t,len:a,data:e.slice(e.l,e.l+a)};e.l+=a;return n}function sf(e,r,t){if(t.biffguess&&t.biff==5)t.biff=2;var a=bi(e,6);++e.l;var n=ni(e,r-7,t);a.t="str";a.val=n;return a}function ff(e){var r=bi(e,6);++e.l;var t=qt(e,8);r.t="n";r.val=t;return r}function lf(e,r,t){var a=Jr(15);nb(a,e,r);a._W(8,t,"f");return a}function of(e){var r=bi(e,6);++e.l;var t=e._R(2);r.t="n";r.val=t;return r}function cf(e,r,t){var a=Jr(9);nb(a,e,r);a._W(2,t);return a}function uf(e){var r=e._R(1);if(r===0){e.l++;return""}return e._R(r,"sbcs-cont")}function hf(e,r){e.l+=6;e.l+=2;e.l+=1;e.l+=3;e.l+=1;e.l+=r-13}function df(e,r,t){var a=e.l+r;var n=bi(e,6);var i=e._R(2);var s=ti(e,i,t);e.l=a;n.t="str";n.val=s;return n}var vf=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969};var r=Q({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});var a=[2,3,48,49,131,139,140,245];function n(r,t){var a=[];var n=B(1);switch(t.type){case"base64":n=T(g.decode(r));break;case"binary":n=T(r);break;case"buffer":;case"array":n=r;break;}Yr(n,0);var i=n._R(1);var s=!!(i&136);var f=false,l=false;switch(i){case 2:break;case 3:break;case 48:f=true;s=true;break;case 49:f=true;s=true;break;case 131:break;case 139:break;case 140:l=true;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+i.toString(16));}var o=0,c=521;if(i==2)o=n._R(2);n.l+=3;if(i!=2)o=n._R(4);if(o>1048576)o=1e6;if(i!=2)c=n._R(2);var u=n._R(2);var h=t.codepage||1252;if(i!=2){n.l+=16;n._R(1);if(n[n.l]!==0)h=e[n[n.l]];n.l+=1;n.l+=2}if(l)n.l+=36;var d=[],v={};var p=Math.min(n.length,i==2?521:c-10-(f?264:0));var b=l?32:11;while(n.l<p&&n[n.l]!=13){v={};v.name=cptable.utils.decode(h,n.slice(n.l,n.l+b)).replace(/[\u0000\r\n].*$/g,"");n.l+=b;v.type=String.fromCharCode(n._R(1));if(i!=2&&!l)v.offset=n._R(4);v.len=n._R(1);if(i==2)v.offset=n._R(2);v.dec=n._R(1);if(v.name.length)d.push(v);if(i!=2)n.l+=l?13:14;switch(v.type){case"B":if((!f||v.len!=8)&&t.WTF)console.log("Skipping "+v.name+":"+v.type);break;case"G":;case"P":if(t.WTF)console.log("Skipping "+v.name+":"+v.type);break;case"+":;case"0":;case"@":;case"C":;case"D":;case"F":;case"I":;case"L":;case"M":;case"N":;case"O":;case"T":;case"Y":break;default:throw new Error("Unknown Field Type: "+v.type);}}if(n[n.l]!==13)n.l=c-1;if(n._R(1)!==13)throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=c;var m=0,E=0;a[0]=[];for(E=0;E!=d.length;++E)a[0][E]=d[E].name;while(o-- >0){if(n[n.l]===42){n.l+=u;continue}++n.l;a[++m]=[];E=0;for(E=0;E!=d.length;++E){var w=n.slice(n.l,n.l+d[E].len);n.l+=d[E].len;Yr(w,0);var k=cptable.utils.decode(h,w);switch(d[E].type){case"C":if(k.trim().length)a[m][E]=k.replace(/\s+$/,"");break;case"D":if(k.length===8)a[m][E]=new Date(+k.slice(0,4),+k.slice(4,6)-1,+k.slice(6,8));else a[m][E]=k;break;case"F":a[m][E]=parseFloat(k.trim());break;case"+":;case"I":a[m][E]=l?w._R(-4,"i")^2147483648:w._R(4,"i");break;case"L":switch(k.trim().toUpperCase()){case"Y":;case"T":a[m][E]=true;break;case"N":;case"F":a[m][E]=false;break;case"":;case"?":break;default:throw new Error("DBF Unrecognized L:|"+k+"|");}break;case"M":if(!s)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));a[m][E]="##MEMO##"+(l?parseInt(k.trim(),10):w._R(4));break;case"N":k=k.replace(/\u0000/g,"").trim();if(k&&k!=".")a[m][E]=+k||0;break;case"@":a[m][E]=new Date(w._R(-8,"f")-621356832e5);break;case"T":a[m][E]=new Date((w._R(4)-2440588)*864e5+w._R(4));break;case"Y":a[m][E]=w._R(4,"i")/1e4;break;case"O":a[m][E]=-w._R(-8,"f");break;case"B":if(f&&d[E].len==8){a[m][E]=w._R(8,"f");break};case"G":;case"P":w.l+=d[E].len;break;case"0":if(d[E].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+d[E].type);}}}if(i!=2)if(n.l<n.length&&n[n.l++]!=26)throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));if(t&&t.sheetRows)a=a.slice(0,t.sheetRows);return a}function i(e,r){var t=r||{};if(!t.dateNF)t.dateNF="yyyymmdd";return Tt(n(e,t),t)}function s(e,r){try{return Bt(i(e,r),r)}catch(t){if(r&&r.WTF)throw t}return{SheetNames:[],Sheets:{}}}var f={B:8,C:250,L:1,D:8,"?":0,"":0};function o(e,a){var n=a||{};if(+n.codepage>=0)l(+n.codepage);if(n.type=="string")throw new Error("Cannot write DBF to JS string");var i=Zr();var s=Sm(e,{header:1,raw:true,cellDates:true});var o=s[0],c=s.slice(1);var u=0,h=0,d=0,v=1;for(u=0;u<o.length;++u){if(u==null)continue;++d;if(typeof o[u]==="number")o[u]=o[u].toString(10);if(typeof o[u]!=="string")throw new Error("DBF Invalid column name "+o[u]+" |"+typeof o[u]+"|");if(o.indexOf(o[u])!==u)for(h=0;h<1024;++h)if(o.indexOf(o[u]+"_"+h)==-1){o[u]+="_"+h;break}}var p=wt(e["!ref"]);var b=[];for(u=0;u<=p.e.c-p.s.c;++u){var m=[];for(h=0;h<c.length;++h){if(c[h][u]!=null)m.push(c[h][u])}if(m.length==0||o[u]==null){b[u]="?";continue}var g="",E="";for(h=0;h<m.length;++h){switch(typeof m[h]){case"number":E="B";break;case"string":E="C";break;case"boolean":E="L";break;case"object":E=m[h]instanceof Date?"D":"C";break;default:E="C";}g=g&&g!=E?"C":E;if(g=="C")break}v+=f[g]||0;b[u]=g}var w=i.next(32);w._W(4,318902576);w._W(4,c.length);w._W(2,296+32*d);w._W(2,v);for(u=0;u<4;++u)w._W(4,0);w._W(4,0|(+r[t]||3)<<8);for(u=0,h=0;u<o.length;++u){if(o[u]==null)continue;var k=i.next(32);var S=(o[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);k._W(1,S,"sbcs");k._W(1,b[u]=="?"?"C":b[u],"sbcs");k._W(4,h);k._W(1,f[b[u]]||0);k._W(1,0);k._W(1,2);k._W(4,0);k._W(1,0);k._W(4,0);k._W(4,0);h+=f[b[u]]||0}var B=i.next(264);B._W(4,13);for(u=0;u<65;++u)B._W(4,0);for(u=0;u<c.length;++u){var C=i.next(v);C._W(1,0);for(h=0;h<o.length;++h){if(o[h]==null)continue;switch(b[h]){case"L":C._W(1,c[u][h]==null?63:c[u][h]?84:70);break;case"B":C._W(8,c[u][h]||0,"f");break;case"D":if(!c[u][h])C._W(8,"00000000","sbcs");else{C._W(4,("0000"+c[u][h].getFullYear()).slice(-4),"sbcs");C._W(2,("00"+(c[u][h].getMonth()+1)).slice(-2),"sbcs");C._W(2,("00"+c[u][h].getDate()).slice(-2),"sbcs")}break;case"C":var T=String(c[u][h]||"");C._W(1,T,"sbcs");for(d=0;d<250-T.length;++d)C._W(1,32);break;}}}i.next(1)._W(1,26);return i.end()}return{versions:a,to_workbook:s,to_sheet:i,from_sheet:o}}();var pf=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223};var r=new RegExp("N("+K(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm");var t=function(r,t){var a=e[t];return typeof a=="number"?p(a):a};var a=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return a==59?e:p(a)};e["|"]=254;function n(e,r){switch(r.type){case"base64":return i(g.decode(e),r);case"binary":return i(e,r);case"buffer":return i(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),r);case"array":return i(oe(e),r);}throw new Error("Unrecognized type "+r.type)}function i(e,n){var i=e.split(/[\n\r]+/),s=-1,f=-1,o=0,c=0,u=[];var h=[];var d=null;var v={},p=[],b=[],m=[];var g=0,E;if(+n.codepage>=0)l(+n.codepage);for(;o!==i.length;++o){g=0;var w=i[o].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(r,t);var k=w.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")});var S=k[0],B;if(w.length>0)switch(S){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":if(k[1].charAt(0)=="P")h.push(w.slice(3).replace(/;;/g,";"));break;case"C":var C=false,T=false,_=false,x=false,A=-1,y=-1;for(c=1;c<k.length;++c)switch(k[c].charAt(0)){case"A":break;case"X":f=parseInt(k[c].slice(1))-1;T=true;break;case"Y":s=parseInt(k[c].slice(1))-1;if(!T)f=0;for(E=u.length;E<=s;++E)u[E]=[];break;case"K":B=k[c].slice(1);if(B.charAt(0)==='"')B=B.slice(1,B.length-1);else if(B==="TRUE")B=true;else if(B==="FALSE")B=false;else if(!isNaN(he(B))){B=he(B);if(d!==null&&D.is_date(d))B=ne(B)}else if(!isNaN(de(B).getDate())){B=le(B)}if(typeof cptable!=="undefined"&&typeof B=="string"&&(n||{}).type!="string"&&(n||{}).codepage)B=cptable.utils.decode(n.codepage,B);C=true;break;case"E":x=true;var I=sc(k[c].slice(1),{r:s,c:f});u[s][f]=[u[s][f],I];break;case"S":_=true;u[s][f]=[u[s][f],"S5S"];break;case"G":break;case"R":A=parseInt(k[c].slice(1))-1;break;case"C":y=parseInt(k[c].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w);}if(C){if(u[s][f]&&u[s][f].length==2)u[s][f][0]=B;else u[s][f]=B;d=null}if(_){if(x)throw new Error("SYLK shared formula cannot have own formula");var R=A>-1&&u[A][y];if(!R||!R[1])throw new Error("SYLK shared formula cannot find base");u[s][f][1]=oc(R[1],{r:s-A,c:f-y})}break;case"F":var F=0;for(c=1;c<k.length;++c)switch(k[c].charAt(0)){case"X":f=parseInt(k[c].slice(1))-1;++F;break;case"Y":s=parseInt(k[c].slice(1))-1;for(E=u.length;E<=s;++E)u[E]=[];break;case"M":g=parseInt(k[c].slice(1))/20;break;case"F":break;case"G":break;case"P":d=h[parseInt(k[c].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":m=k[c].slice(1).split(" ");for(E=parseInt(m[0],10);E<=parseInt(m[1],10);++E){g=parseInt(m[2],10);b[E-1]=g===0?{hidden:true}:{wch:g};Bl(b[E-1])}break;case"C":f=parseInt(k[c].slice(1))-1;if(!b[f])b[f]={};break;case"R":s=parseInt(k[c].slice(1))-1;if(!p[s])p[s]={};if(g>0){p[s].hpt=g;p[s].hpx=xl(g)}else if(g===0)p[s].hidden=true;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w);}if(F<1)d=null;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w);}}if(p.length>0)v["!rows"]=p;if(b.length>0)v["!cols"]=b;if(n&&n.sheetRows)u=u.slice(0,n.sheetRows);return[u,v]}function s(e,r){var t=n(e,r);var a=t[0],i=t[1];var s=Tt(a,r);K(i).forEach(function(e){s[e]=i[e]});return s}function f(e,r){return Bt(s(e,r),r)}function o(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0;if(e.f&&!e.F)n+=";E"+lc(e.f,{r:t,c:a});break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"")+'"';break;}return n}function c(e,r){r.forEach(function(r,t){var a="F;W"+(t+1)+" "+(t+1)+" ";if(r.hidden)a+="0";else{if(typeof r.width=="number"&&!r.wpx)r.wpx=gl(r.width);if(typeof r.wpx=="number"&&!r.wch)r.wch=El(r.wpx);if(typeof r.wch=="number")a+=Math.round(r.wch)}if(a.charAt(a.length-1)!=" ")e.push(a)})}function u(e,r){r.forEach(function(r,t){var a="F;";if(r.hidden)a+="M0;";else if(r.hpt)a+="M"+20*r.hpt+";";else if(r.hpx)a+="M"+20*_l(r.hpx)+";";if(a.length>2)e.push(a+"R"+(t+1))})}function h(e,r){var t=["ID;PWXL;N;E"],a=[];var n=wt(e["!ref"]),i;var s=Array.isArray(e);var f="\r\n";t.push("P;PGeneral");t.push("F;P0;DG0G8;M255");if(e["!cols"])c(t,e["!cols"]);if(e["!rows"])u(t,e["!rows"]);t.push("B;Y"+(n.e.r-n.s.r+1)+";X"+(n.e.c-n.s.c+1)+";D"+[n.s.c,n.s.r,n.e.c,n.e.r].join(" "));for(var l=n.s.r;l<=n.e.r;++l){for(var h=n.s.c;h<=n.e.c;++h){var d=mt({r:l,c:h});i=s?(e[l]||[])[h]:e[d];if(!i||i.v==null&&(!i.f||i.F))continue;a.push(o(i,e,l,h,r))}}return t.join(f)+f+a.join(f)+f+"E"+f}return{to_workbook:f,to_sheet:s,from_sheet:h}}();var bf=function(){function e(e,t){switch(t.type){case"base64":return r(g.decode(e),t);case"binary":return r(e,t);case"buffer":return r(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),t);case"array":return r(oe(e),t);}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=e.split("\n"),a=-1,n=-1,i=0,s=[];for(;i!==t.length;++i){if(t[i].trim()==="BOT"){s[++a]=[];n=0;continue}if(a<0)continue;var f=t[i].trim().split(",");var l=f[0],o=f[1];++i;var c=t[i]||"";while((c.match(/["]/g)||[]).length&1&&i<t.length-1)c+="\n"+t[++i];c=c.trim();switch(+l){case-1:if(c==="BOT"){s[++a]=[];n=0;continue}else if(c!=="EOD")throw new Error("Unrecognized DIF special command "+c);break;case 0:if(c==="TRUE")s[a][n]=true;else if(c==="FALSE")s[a][n]=false;else if(!isNaN(he(o)))s[a][n]=he(o);else if(!isNaN(de(o).getDate()))s[a][n]=le(o);else s[a][n]=o;++n;break;case 1:c=c.slice(1,c.length-1);c=c.replace(/""/g,'"');if(m&&c&&c.match(/^=".*"$/))c=c.slice(2,-1);s[a][n++]=c!==""?c:null;break;}if(c==="EOD")break}if(r&&r.sheetRows)s=s.slice(0,r.sheetRows);return s}function t(r,t){return Tt(e(r,t),t)}function a(e,r){return Bt(t(e,r),r)}var n=function(){var e=function t(e,r,a,n,i){e.push(r);e.push(a+","+n);e.push('"'+i.replace(/"/g,'""')+'"')};var r=function a(e,r,t,n){e.push(r+","+t);e.push(r==1?'"'+n.replace(/"/g,'""')+'"':n)};return function n(t){var a=[];var n=wt(t["!ref"]),i;var s=Array.isArray(t);e(a,"TABLE",0,1,"sheetjs");e(a,"VECTORS",0,n.e.r-n.s.r+1,"");e(a,"TUPLES",0,n.e.c-n.s.c+1,"");e(a,"DATA",0,0,"");for(var f=n.s.r;f<=n.e.r;++f){r(a,-1,0,"BOT");for(var l=n.s.c;l<=n.e.c;++l){var o=mt({r:f,c:l});i=s?(t[f]||[])[l]:t[o];if(!i){r(a,1,0,"");continue}switch(i.t){case"n":var c=m?i.w:i.v;if(!c&&i.v!=null)c=i.v;if(c==null){if(m&&i.f&&!i.F)r(a,1,0,"="+i.f);else r(a,1,0,"")}else r(a,0,c,"V");break;case"b":r(a,0,i.v?1:0,i.v?"TRUE":"FALSE");break;case"s":r(a,1,0,!m||isNaN(i.v)?i.v:'="'+i.v+'"');break;case"d":if(!i.w)i.w=D.format(i.z||D._table[14],ee(le(i.v)));if(m)r(a,0,i.w,"V");else r(a,1,0,i.w);break;default:r(a,1,0,"");}}}r(a,-1,0,"EOD");var u="\r\n";var h=a.join(u);return h}}();return{to_workbook:a,to_sheet:t,from_sheet:n}}();var mf=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function r(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(r,t){var a=r.split("\n"),n=-1,i=-1,s=0,f=[];for(;s!==a.length;++s){var l=a[s].trim().split(":");if(l[0]!=="cell")continue;var o=bt(l[1]);if(f.length<=o.r)for(n=f.length;n<=o.r;++n)if(!f[n])f[n]=[];n=o.r;i=o.c;switch(l[2]){case"t":f[n][i]=e(l[3]);break;case"v":f[n][i]=+l[3];break;case"vtf":var c=l[l.length-1];case"vtc":switch(l[3]){case"nl":f[n][i]=+l[4]?true:false;break;default:f[n][i]=+l[4];break;}if(l[2]=="vtf")f[n][i]=[f[n][i],c];}}if(t&&t.sheetRows)f=f.slice(0,t.sheetRows);return f}function a(e,r){return Tt(t(e,r),r)}function n(e,r){return Bt(a(e,r),r)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n");var s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n";var f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n");var l="--SocialCalcSpreadsheetControlSave--";function o(e){if(!e||!e["!ref"])return"";var t=[],a=[],n,i="";var s=gt(e["!ref"]);var f=Array.isArray(e);for(var l=s.s.r;l<=s.e.r;++l){for(var o=s.s.c;o<=s.e.c;++o){i=mt({r:l,c:o});n=f?(e[l]||[])[o]:e[i];if(!n||n.v==null||n.t==="z")continue;a=["cell",i,"t"];switch(n.t){case"s":;case"str":a.push(r(n.v));break;case"n":if(!n.f){a[2]="v";a[3]=n.v}else{a[2]="vtf";a[3]="n";a[4]=n.v;a[5]=r(n.f)}break;case"b":a[2]="vt"+(n.f?"f":"c");a[3]="nl";a[4]=n.v?"1":"0";a[5]=r(n.f||(n.v?"TRUE":"FALSE"));break;case"d":var c=ee(le(n.v));a[2]="vtc";a[3]="nd";a[4]=""+c;a[5]=n.w||D.format(n.z||D._table[14],c);break;case"e":continue;}t.push(a.join(":"))}}t.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1");t.push("valueformat:1:text-wiki");return t.join("\n")}function c(e){return[i,s,f,s,o(e),l].join("\n")}return{to_workbook:n,to_sheet:a,from_sheet:c}}();var gf=function(){function e(e,r,t,a,n){if(n.raw)r[t][a]=e;else if(e===""){}else if(e==="TRUE")r[t][a]=true;else if(e==="FALSE")r[t][a]=false;else if(!isNaN(he(e)))r[t][a]=he(e);else if(!isNaN(de(e).getDate()))r[t][a]=le(e);else r[t][a]=e}function r(r,t){var a=t||{};var n=[];if(!r||r.length===0)return n;var i=r.split(/[\r\n]/);var s=i.length-1;while(s>=0&&i[s].length===0)--s;var f=10,l=0;var o=0;for(;o<=s;++o){l=i[o].indexOf(" ");if(l==-1)l=i[o].length;else l++;f=Math.max(f,l)}for(o=0;o<=s;++o){n[o]=[];var c=0;e(i[o].slice(0,f).trim(),n,o,c,a);for(c=1;c<=(i[o].length-f)/10+1;++c)e(i[o].slice(f+(c-1)*10,f+c*10).trim(),n,o,c,a)}if(a.sheetRows)n=n.slice(0,a.sheetRows);return n}var t={44:",",9:"\t",59:";",124:"|"};var a={44:3,9:2,59:1,124:0};function n(e){var r={},n=false,i=0,s=0;for(;i<e.length;++i){if((s=e.charCodeAt(i))==34)n=!n;else if(!n&&s in t)r[s]=(r[s]||0)+1}s=[];for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}if(!s.length){r=a;for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}}s.sort(function(e,r){return e[0]-r[0]||a[e[1]]-a[r[1]]});return t[s.pop()[1]]||44}function i(e,r){var t=r||{};var a="";if(b!=null&&t.dense==null)t.dense=b;var i=t.dense?[]:{};var s={s:{c:0,r:0},e:{c:0,r:0}};if(e.slice(0,4)=="sep="){if(e.charCodeAt(5)==13&&e.charCodeAt(6)==10){a=e.charAt(4);e=e.slice(7)}else if(e.charCodeAt(5)==13||e.charCodeAt(5)==10){a=e.charAt(4);e=e.slice(6)}else a=n(e.slice(0,1024))}else if(t&&t.FS)a=t.FS;else a=n(e.slice(0,1024));var f=0,l=0,o=0;var c=0,u=0,h=a.charCodeAt(0),d=false,v=0,p=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var m=t.dateNF!=null?L(t.dateNF):null;function g(){var r=e.slice(c,u);var a={};if(r.charAt(0)=='"'&&r.charAt(r.length-1)=='"')r=r.slice(1,-1).replace(/""/g,'"');if(r.length===0)a.t="z";else if(t.raw){a.t="s";a.v=r}else if(r.trim().length===0){a.t="s";a.v=r}else if(r.charCodeAt(0)==61){if(r.charCodeAt(1)==34&&r.charCodeAt(r.length-1)==34){a.t="s";a.v=r.slice(2,-1).replace(/""/g,'"')}else if(uc(r)){a.t="n";a.f=r.slice(1)}else{a.t="s";a.v=r}}else if(r=="TRUE"){a.t="b";a.v=true}else if(r=="FALSE"){a.t="b";a.v=false}else if(!isNaN(o=he(r))){a.t="n";if(t.cellText!==false)a.w=r;a.v=o}else if(!isNaN(de(r).getDate())||m&&r.match(m)){a.z=t.dateNF||D._table[14];var n=0;if(m&&r.match(m)){r=U(r,t.dateNF,r.match(m)||[]);n=1}if(t.cellDates){a.t="d";a.v=le(r,n)}else{a.t="n";a.v=ee(le(r,n))}if(t.cellText!==false)a.w=D.format(a.z,a.v instanceof Date?ee(a.v):a.v);if(!t.cellNF)delete a.z}else{a.t="s";a.v=r}if(a.t=="z"){}else if(t.dense){if(!i[f])i[f]=[];i[f][l]=a}else i[mt({c:l,r:f})]=a;c=u+1;p=e.charCodeAt(c);if(s.e.c<l)s.e.c=l;if(s.e.r<f)s.e.r=f;if(v==h)++l;else{l=0;++f;if(t.sheetRows&&t.sheetRows<=f)return true}}e:for(;u<e.length;++u)switch(v=e.charCodeAt(u)){case 34:if(p===34)d=!d;break;case h:;case 10:;case 13:if(!d&&g())break e;break;default:break;}if(u-c>0)g();i["!ref"]=Et(s);return i}function s(e,t){if(!(t&&t.PRN))return i(e,t);if(t.FS)return i(e,t);if(e.slice(0,4)=="sep=")return i(e,t);if(e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0)return i(e,t);return Tt(r(e,t),t)}function f(e,r){var t="",a=r.type=="string"?[0,0,0,0]:rm(e,r);switch(r.type){case"base64":t=g.decode(e);break;case"binary":t=e;break;case"buffer":if(r.codepage==65001)t=e.toString("utf8");else if(r.codepage&&typeof cptable!=="undefined")t=cptable.utils.decode(r.codepage,e);else t=E&&Buffer.isBuffer(e)?e.toString("binary"):x(e);break;case"array":t=oe(e);break;case"string":t=e;break;default:throw new Error("Unrecognized type "+r.type);}if(a[0]==239&&a[1]==187&&a[2]==191)t=Ye(t.slice(3));else if(r.type!="string"&&r.codepage==65001)t=Ye(t);else if(r.type=="binary"&&typeof cptable!=="undefined"&&r.codepage)t=cptable.utils.decode(r.codepage,cptable.utils.encode(28591,t));if(t.slice(0,19)=="socialcalc:version:")return mf.to_sheet(r.type=="string"?t:Ye(t),r);return s(t,r)}function l(e,r){return Bt(f(e,r),r)}function o(e){var r=[];var t=wt(e["!ref"]),a;var n=Array.isArray(e);for(var i=t.s.r;i<=t.e.r;++i){var s=[];for(var f=t.s.c;f<=t.e.c;++f){var l=mt({r:i,c:f});a=n?(e[i]||[])[f]:e[l];if(!a||a.v==null){s.push("          ");continue}var o=(a.w||(St(a),a.w)||"").slice(0,10);while(o.length<10)o+=" ";s.push(o+(f===0?" ":""))}r.push(s.join(""))}return r.join("\n")}return{to_workbook:l,to_sheet:f,from_sheet:o}}();function Ef(e,r){var t=r||{},a=!!t.WTF;t.WTF=true;try{var n=pf.to_workbook(e,t);t.WTF=a;return n}catch(i){t.WTF=a;if(!i.message.match(/SYLK bad record ID/)&&a)throw i;return gf.to_workbook(e,r)}}var wf=function(){function e(e,r,t){if(!e)return;Yr(e,e.l||0);var a=t.Enum||V;while(e.l<e.length){var n=e._R(2);var i=a[n]||a[65535];var s=e._R(2);var f=e.l+s;var l=i.f&&i.f(e,s,t);e.l=f;if(r(l,i,n))return}}function r(e,r){switch(r.type){case"base64":return t(T(g.decode(e)),r);case"binary":return t(T(e),r);case"buffer":;case"array":return t(e,r);}throw"Unsupported type "+r.type}function t(r,t){if(!r)return r;var a=t||{};if(b!=null&&a.dense==null)a.dense=b;var n=a.dense?[]:{},i="Sheet1",s="",f=0;var l={},o=[],c=[];var u={s:{r:0,c:0},e:{r:0,c:0}};var h=a.sheetRows||0;if(r[2]==0){if(r[3]==8||r[3]==9){if(r.length>=16&&r[14]==5&&r[15]===108)throw new Error("Unsupported Works 3 for Mac file")}}if(r[2]==2){a.Enum=V;e(r,function(e,r,t){switch(t){case 0:a.vers=e;if(e>=4096)a.qpro=true;break;case 6:u=e;break;case 204:if(e)s=e;break;case 222:s=e;break;case 15:;case 51:if(!a.qpro)e[1].v=e[1].v.slice(1);case 13:;case 14:;case 16:if(t==14&&(e[2]&112)==112&&(e[2]&15)>1&&(e[2]&15)<15){e[1].z=a.dateNF||D._table[14];if(a.cellDates){e[1].t="d";e[1].v=ne(e[1].v)}}if(a.qpro){if(e[3]>f){n["!ref"]=Et(u);l[i]=n;o.push(i);n=a.dense?[]:{};u={s:{r:0,c:0},e:{r:0,c:0}};f=e[3];i=s||"Sheet"+(f+1);s=""}}var c=a.dense?(n[e[0].r]||[])[e[0].c]:n[mt(e[0])];if(c){c.t=e[1].t;c.v=e[1].v;if(e[1].z!=null)c.z=e[1].z;if(e[1].f!=null)c.f=e[1].f;break}if(a.dense){if(!n[e[0].r])n[e[0].r]=[];n[e[0].r][e[0].c]=e[1]}else n[mt(e[0])]=e[1];break;default:;}},a)}else if(r[2]==26||r[2]==14){a.Enum=X;if(r[2]==14){a.qpro=true;r.l=0}e(r,function(e,r,t){switch(t){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:;case 24:;case 25:;case 37:;case 39:;case 40:if(e[3]>f){n["!ref"]=Et(u);l[i]=n;o.push(i);n=a.dense?[]:{};u={s:{r:0,c:0},e:{r:0,c:0}};f=e[3];i="Sheet"+(f+1)}if(h>0&&e[0].r>=h)break;if(a.dense){if(!n[e[0].r])n[e[0].r]=[];n[e[0].r][e[0].c]=e[1]}else n[mt(e[0])]=e[1];if(u.e.c<e[0].c)u.e.c=e[0].c;if(u.e.r<e[0].r)u.e.r=e[0].r;break;case 27:if(e[14e3])c[e[14e3][0]]=e[14e3][1];break;case 1537:c[e[0]]=e[1];if(e[0]==f)i=e[1];break;default:break;}},a)}else throw new Error("Unrecognized LOTUS BOF "+r[2]);n["!ref"]=Et(u);l[s||i]=n;o.push(s||i);if(!c.length)return{SheetNames:o,Sheets:l};var d={},v=[];for(var p=0;p<c.length;++p)if(l[o[p]]){v.push(c[p]||o[p]);d[c[p]]=l[c[p]]||l[o[p]];
}else{v.push(c[p]);d[c[p]]={"!ref":"A1"}}return{SheetNames:v,Sheets:d}}function a(e,r){var t=r||{};if(+t.codepage>=0)l(+t.codepage);if(t.type=="string")throw new Error("Cannot write WK1 to JS string");var a=Zr();var n=wt(e["!ref"]);var s=Array.isArray(e);var f=[];tb(a,0,i(1030));tb(a,6,o(n));var c=Math.min(n.e.r,8191);for(var u=n.s.r;u<=c;++u){var d=lt(u);for(var p=n.s.c;p<=n.e.c;++p){if(u===n.s.r)f[p]=ht(p);var b=f[p]+d;var g=s?(e[u]||[])[p]:e[b];if(!g||g.t=="z")continue;if(g.t=="n"){if((g.v|0)==g.v&&g.v>=-32768&&g.v<=32767)tb(a,13,v(u,p,g.v));else tb(a,14,m(u,p,g.v))}else{var E=St(g);tb(a,15,h(u,p,E.slice(0,239)))}}}tb(a,1);return a.end()}function n(e,r){var t=r||{};if(+t.codepage>=0)l(+t.codepage);if(t.type=="string")throw new Error("Cannot write WK3 to JS string");var a=Zr();tb(a,0,s(e));for(var n=0,i=0;n<e.SheetNames.length;++n)if((e.Sheets[e.SheetNames[n]]||{})["!ref"])tb(a,27,W(e.SheetNames[n],i++));var f=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(!o||!o["!ref"])continue;var c=wt(o["!ref"]);var u=Array.isArray(o);var h=[];var d=Math.min(c.e.r,8191);for(var v=c.s.r;v<=d;++v){var p=lt(v);for(var b=c.s.c;b<=c.e.c;++b){if(v===c.s.r)h[b]=ht(b);var m=h[b]+p;var g=u?(o[v]||[])[b]:o[m];if(!g||g.t=="z")continue;if(g.t=="n"){tb(a,23,R(v,b,f,g.v))}else{var E=St(g);tb(a,22,A(v,b,f,E.slice(0,239)))}}}++f}tb(a,1);return a.end()}function i(e){var r=Jr(2);r._W(2,e);return r}function s(e){var r=Jr(26);r._W(2,4096);r._W(2,4);r._W(4,0);var t=0,a=0,n=0;for(var i=0;i<e.SheetNames.length;++i){var s=e.SheetNames[i];var f=e.Sheets[s];if(!f||!f["!ref"])continue;++n;var l=gt(f["!ref"]);if(t<l.e.r)t=l.e.r;if(a<l.e.c)a=l.e.c}if(t>8191)t=8191;r._W(2,t);r._W(1,n);r._W(1,a);r._W(2,0);r._W(2,0);r._W(1,1);r._W(1,2);r._W(4,0);r._W(4,0);return r}function f(e,r,t){var a={s:{c:0,r:0},e:{c:0,r:0}};if(r==8&&t.qpro){a.s.c=e._R(1);e.l++;a.s.r=e._R(2);a.e.c=e._R(1);e.l++;a.e.r=e._R(2);return a}a.s.c=e._R(2);a.s.r=e._R(2);if(r==12&&t.qpro)e.l+=2;a.e.c=e._R(2);a.e.r=e._R(2);if(r==12&&t.qpro)e.l+=2;if(a.s.c==65535)a.s.c=a.e.c=a.s.r=a.e.r=0;return a}function o(e){var r=Jr(8);r._W(2,e.s.c);r._W(2,e.s.r);r._W(2,e.e.c);r._W(2,e.e.r);return r}function c(e,r,t){var a=[{c:0,r:0},{t:"n",v:0},0,0];if(t.qpro&&t.vers!=20768){a[0].c=e._R(1);a[3]=e._R(1);a[0].r=e._R(2);e.l+=2}else{a[2]=e._R(1);a[0].c=e._R(2);a[0].r=e._R(2)}return a}function u(e,r,t){var a=e.l+r;var n=c(e,r,t);n[1].t="s";if(t.vers==20768){e.l++;var i=e._R(1);n[1].v=e._R(i,"utf8");return n}if(t.qpro)e.l++;n[1].v=e._R(a-e.l,"cstr");return n}function h(e,r,t){var a=Jr(7+t.length);a._W(1,255);a._W(2,r);a._W(2,e);a._W(1,39);for(var n=0;n<a.length;++n){var i=t.charCodeAt(n);a._W(1,i>=128?95:i)}a._W(1,0);return a}function d(e,r,t){var a=c(e,r,t);a[1].v=e._R(2,"i");return a}function v(e,r,t){var a=Jr(7);a._W(1,255);a._W(2,r);a._W(2,e);a._W(2,t,"i");return a}function p(e,r,t){var a=c(e,r,t);a[1].v=e._R(8,"f");return a}function m(e,r,t){var a=Jr(13);a._W(1,255);a._W(2,r);a._W(2,e);a._W(8,t,"f");return a}function E(e,r,t){var a=e.l+r;var n=c(e,r,t);n[1].v=e._R(8,"f");if(t.qpro)e.l=a;else{var i=e._R(2);C(e.slice(e.l,e.l+i),n);e.l+=i}return n}function w(e,r,t){var a=r&32768;r&=~32768;r=(a?e:0)+(r>=8192?r-16384:r);return(a?"":"$")+(t?ht(r):lt(r))}var k=[8,8,8,8,8,8,8,8,6,4,4,5,5,7,3,3,3,3,3,3,1,1,2,6,8,8,8,8,8,8,8,8];var S={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]};var B=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function C(e,r){Yr(e,0);var t=[],a=0,n="",i="",s="",f="";while(e.l<e.length){var l=e[e.l++];switch(l){case 0:t.push(e._R(8,"f"));break;case 1:{i=w(r[0].c,e._R(2),true);n=w(r[0].r,e._R(2),false);t.push(i+n)}break;case 2:{var o=w(r[0].c,e._R(2),true);var c=w(r[0].r,e._R(2),false);i=w(r[0].c,e._R(2),true);n=w(r[0].r,e._R(2),false);t.push(o+c+":"+i+n)}break;case 3:if(e.l<e.length){console.error("WK1 premature formula end");return}break;case 4:t.push("("+t.pop()+")");break;case 5:t.push(e._R(2));break;case 6:{var u="";while(l=e[e.l++])u+=String.fromCharCode(l);t.push('"'+u.replace(/"/g,'""')+'"');break}break;case 8:t.push("-"+t.pop());break;case 23:t.push("+"+t.pop());break;case 22:t.push("NOT("+t.pop()+")");break;case 20:;case 21:{f=t.pop();s=t.pop();t.push(["AND","OR"][l-20]+"("+s+","+f+")")}break;default:if(l<32&&B[l]){f=t.pop();s=t.pop();t.push(s+B[l]+f)}else if(S[l]){a=S[l][1];if(a==69)a=e[e.l++];if(a>t.length){console.error("WK1 bad formula parse 0x"+l.toString(16)+":|"+t.join("|")+"|");return}var h=t.slice(-a);t.length-=a;t.push(S[l][0]+"("+h.join(",")+")")}else if(l<=7)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=24)return console.error("WK1 unsupported op "+l.toString(16));else if(l<=30)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=115)return console.error("WK1 unsupported function opcode "+l.toString(16));else return console.error("WK1 unrecognized opcode "+l.toString(16));}}if(t.length==1)r[1].f=""+t[0];else console.error("WK1 bad formula parse |"+t.join("|")+"|")}function _(e){var r=[{c:0,r:0},{t:"n",v:0},0];r[0].r=e._R(2);r[3]=e[e.l++];r[0].c=e[e.l++];return r}function x(e,r){var t=_(e,r);t[1].t="s";t[1].v=e._R(r-4,"cstr");return t}function A(e,r,t,a){var n=Jr(6+a.length);n._W(2,e);n._W(1,t);n._W(1,r);n._W(1,39);for(var i=0;i<a.length;++i){var s=a.charCodeAt(i);n._W(1,s>=128?95:s)}n._W(1,0);return n}function y(e,r){var t=_(e,r);t[1].v=e._R(2);var a=t[1].v>>1;if(t[1].v&1){switch(a&7){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64;break;}}t[1].v=a;return t}function I(e,r){var t=_(e,r);var a=e._R(4);var n=e._R(4);var i=e._R(2);if(i==65535){if(a===0&&n===3221225472){t[1].t="e";t[1].v=15}else if(a===0&&n===3489660928){t[1].t="e";t[1].v=42}else t[1].v=0;return t}var s=i&32768;i=(i&32767)-16446;t[1].v=(1-s*2)*(n*Math.pow(2,i+32)+a*Math.pow(2,i));return t}function R(e,r,t,a){var n=Jr(14);n._W(2,e);n._W(1,t);n._W(1,r);if(a==0){n._W(4,0);n._W(4,0);n._W(2,65535);return n}var i=0,s=0,f=0,l=0;if(a<0){i=1;a=-a}s=Math.log2(a)|0;a/=Math.pow(2,s-31);l=a>>>0;if((l&2147483648)==0){a/=2;++s;l=a>>>0}a-=l;l|=2147483648;l>>>=0;a*=Math.pow(2,32);f=a>>>0;n._W(4,f);n._W(4,l);s+=16383+(i?32768:0);n._W(2,s);return n}function F(e,r){var t=I(e,14);e.l+=r-14;return t}function O(e,r){var t=_(e,r);var a=e._R(4);t[1].v=a>>6;return t}function P(e,r){var t=_(e,r);var a=e._R(8,"f");t[1].v=a;return t}function N(e,r){var t=P(e,14);e.l+=r-10;return t}function M(e,r){return e[e.l+r-1]==0?e._R(r,"cstr"):""}function L(e,r){var t=e[e.l++];if(t>r-1)t=r-1;var a="";while(a.length<t)a+=String.fromCharCode(e[e.l++]);return a}function U(e,r,t){if(!t.qpro||r<21)return;var a=e._R(1);e.l+=17;var n=e._R(1);e.l+=2;var i=e._R(r-21,"cstr");return[a,i]}function H(e,r){var t={},a=e.l+r;while(e.l<a){var n=e._R(2);if(n==14e3){t[n]=[0,""];t[n][0]=e._R(2);while(e[e.l]){t[n][1]+=String.fromCharCode(e[e.l]);e.l++}e.l++}}return t}function W(e,r){var t=Jr(5+e.length);t._W(2,14e3);t._W(2,r);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);t[t.l++]=n>127?95:n}t[t.l++]=0;return t}var V={0:{n:"BOF",f:Kn},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:p},15:{n:"LABEL",f:u},16:{n:"FORMULA",f:E},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:u},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:M},222:{n:"SHEETNAMELP",f:L},65535:{n:""}};var X={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:x},23:{n:"NUMBER17",f:I},24:{n:"NUMBER18",f:y},25:{n:"FORMULA19",f:F},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:H},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:O},38:{n:"??"},39:{n:"NUMBER27",f:P},40:{n:"FORMULA28",f:N},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:M},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:U},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:r}}();function kf(e){var r={},t=e.match(Re),a=0;var n=false;if(t)for(;a!=t.length;++a){var s=Oe(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":;case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;r.cp=i[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":;case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":;case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting";break;};case"<u>":;case"<u/>":r.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":;case"<b/>":r.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":;case"<i/>":r.i=1;break;case"</i>":break;case"<color":if(s.rgb)r.color=s.rgb.slice(2,8);break;case"<family":r.family=s.val;break;case"<vertAlign":r.valign=s.val;break;case"<scheme":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":n=true;break;case"</ext>":n=false;break;default:if(s[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+s[0]);}}return r}var Sf=function(){var e=er("t"),r=er("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:Le(a[1])};var i=t.match(r);if(i)n.s=kf(i[1]);return n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function i(e){return e.replace(a,"").split(n).map(t).filter(function(e){return e.v})}}();var Bf=function qm(){var e=/(\r\n|\n)/g;function r(e,r,t){var a=[];if(e.u)a.push("text-decoration: underline;");if(e.uval)a.push("text-underline-style:"+e.uval+";");if(e.sz)a.push("font-size:"+e.sz+"pt;");if(e.outline)a.push("text-effect: outline;");if(e.shadow)a.push("text-shadow: auto;");r.push('<span style="'+a.join("")+'">');if(e.b){r.push("<b>");t.push("</b>")}if(e.i){r.push("<i>");t.push("</i>")}if(e.strike){r.push("<s>");t.push("</s>")}var n=e.valign||"";if(n=="superscript"||n=="super")n="sup";else if(n=="subscript")n="sub";if(n!=""){r.push("<"+n+">");t.push("</"+n+">")}t.push("</span>");return e}function t(t){var a=[[],t.v,[]];if(!t.v)return"";if(t.s)r(t.s,a[0],a[2]);return a[0].join("")+a[1].replace(e,"<br/>")+a[2].join("")}return function a(e){return e.map(t).join("")}}();var Cf=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Tf=/<(?:\w+:)?r>/;var _f=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function xf(e,r){var t=r?r.cellHTML:true;var a={};if(!e)return{t:""};if(e.match(/^\s*<(?:\w+:)?t[^>]*>/)){a.t=Le(Ye(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||""));a.r=Ye(e);if(t)a.h=Ge(a.t)}else if(e.match(Tf)){a.r=Ye(e);a.t=Le(Ye((e.replace(_f,"").match(Cf)||[]).join("").replace(Re,"")));if(t)a.h=Bf(Sf(a.r))}return a}var Af=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/;var yf=/<(?:\w+:)?(?:si|sstItem)>/g;var If=/<\/(?:\w+:)?(?:si|sstItem)>/;function Rf(e,r){var t=[],a="";if(!e)return t;var n=e.match(Af);if(n){a=n[2].replace(yf,"").split(If);for(var i=0;i!=a.length;++i){var s=xf(a[i].trim(),r);if(s!=null)t[t.length]=s}n=Oe(n[1]);t.Count=n.count;t.Unique=n.uniqueCount}return t}Ua.SST="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings";var Ff=/^\s|\s$|[\t\n\r]/;function Df(e,r){if(!r.bookSST)return"";var t=[ye];t[t.length]=or("sst",null,{xmlns:hr.main[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a){if(e[a]==null)continue;var n=e[a];var i="<si>";if(n.r)i+=n.r;else{i+="<t";if(!n.t)n.t="";if(n.t.match(Ff))i+=' xml:space="preserve"';i+=">"+We(n.t)+"</t>"}i+="</si>";t[t.length]=i}if(t.length>2){t[t.length]="</sst>";t[1]=t[1].replace("/>",">")}return t.join("")}function Of(e){return[e._R(4),e._R(4)]}function Pf(e,r){var t=[];var a=false;qr(e,function n(e,i,s){switch(s){case 159:t.Count=e[0];t.Unique=e[1];break;case 19:t.push(e);break;case 160:return true;case 35:a=true;break;case 36:a=false;break;default:if(i.indexOf("Begin")>0){}else if(i.indexOf("End")>0){}if(!a||r.WTF)throw new Error("Unexpected record "+s+" "+i);}});return t}function Nf(e,r){if(!r)r=Jr(8);r._W(4,e.Count);r._W(4,e.Unique);return r}var Mf=Ft;function Lf(e){var r=Zr();et(r,"BrtBeginSst",Nf(e));for(var t=0;t<e.length;++t)et(r,"BrtSSTItem",Mf(e[t]));et(r,"BrtEndSst");return r.end()}function Uf(e){if(typeof cptable!=="undefined")return cptable.utils.encode(t,e);var r=[],a=e.split("");for(var n=0;n<a.length;++n)r[n]=a[n].charCodeAt(0);return r}function Hf(e,r){var t={};t.Major=e._R(2);t.Minor=e._R(2);if(r>=4)e.l+=r-4;return t}function Wf(e){var r={};r.id=e._R(0,"lpp4");r.R=Hf(e,4);r.U=Hf(e,4);r.W=Hf(e,4);return r}function Vf(e){var r=e._R(4);var t=e.l+r-4;var a={};var n=e._R(4);var i=[];while(n-- >0)i.push({t:e._R(4),v:e._R(0,"lpp4")});a.name=e._R(0,"lpp4");a.comps=i;if(e.l!=t)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+t);return a}function Xf(e){var r=[];e.l+=4;var t=e._R(4);while(t-- >0)r.push(Vf(e));return r}function Gf(e){var r=[];e.l+=4;var t=e._R(4);while(t-- >0)r.push(e._R(0,"lpp4"));return r}function zf(e){var r={};e._R(4);e.l+=4;r.id=e._R(0,"lpp4");r.name=e._R(0,"lpp4");r.R=Hf(e,4);r.U=Hf(e,4);r.W=Hf(e,4);return r}function jf(e){var r=zf(e);r.ename=e._R(0,"8lpp4");r.blksz=e._R(4);r.cmode=e._R(4);if(e._R(4)!=4)throw new Error("Bad !Primary record");return r}function $f(e,r){var t=e.l+r;var a={};a.Flags=e._R(4)&63;e.l+=4;a.AlgID=e._R(4);var n=false;switch(a.AlgID){case 26126:;case 26127:;case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID;}if(!n)throw new Error("Encryption Flags/AlgID mismatch");a.AlgIDHash=e._R(4);a.KeySize=e._R(4);a.ProviderType=e._R(4);e.l+=8;a.CSPName=e._R(t-e.l>>1,"utf16le");e.l=t;return a}function Kf(e,r){var t={},a=e.l+r;e.l+=4;t.Salt=e.slice(e.l,e.l+16);e.l+=16;t.Verifier=e.slice(e.l,e.l+16);e.l+=16;e._R(4);t.VerifierHash=e.slice(e.l,a);e.l=a;return t}function Yf(e){var r=Hf(e);switch(r.Minor){case 2:return[r.Minor,Qf(e,r)];case 3:return[r.Minor,Jf(e,r)];case 4:return[r.Minor,qf(e,r)];}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+r.Minor)}function Qf(e){var r=e._R(4);if((r&63)!=36)throw new Error("EncryptionInfo mismatch");var t=e._R(4);var a=$f(e,t);var n=Kf(e,e.length-e.l);return{t:"Std",h:a,v:n}}function Jf(){throw new Error("File is password-protected: ECMA-376 Extensible")}function qf(e){var r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var t=e._R(e.length-e.l,"utf8");var a={};t.replace(Re,function n(e){var t=Oe(e);switch(Pe(t[0])){case"<?xml":break;case"<encryption":;case"</encryption>":break;case"<keyData":r.forEach(function(e){a[e]=t[e]});break;case"<dataIntegrity":a.encryptedHmacKey=t.encryptedHmacKey;a.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":;case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=t.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(t);break;default:throw t[0];}});return a}function Zf(e,r){var t={};var a=t.EncryptionVersionInfo=Hf(e,4);r-=4;if(a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);t.Flags=e._R(4);r-=4;var n=e._R(4);r-=4;t.EncryptionHeader=$f(e,n);r-=n;t.EncryptionVerifier=Kf(e,r);return t}function el(e){var r={};var t=r.EncryptionVersionInfo=Hf(e,4);if(t.Major!=1||t.Minor!=1)throw"unrecognized version code "+t.Major+" : "+t.Minor;r.Salt=e._R(16);r.EncryptedVerifier=e._R(16);r.EncryptedVerifierHash=e._R(16);return r}function rl(e){var r=0,t;var a=Uf(e);var n=a.length+1,i,s;var f,l,o;t=B(n);t[0]=a.length;for(i=1;i!=n;++i)t[i]=a[i-1];for(i=n-1;i>=0;--i){s=t[i];f=(r&16384)===0?0:1;l=r<<1&32767;o=f|l;r=o^s}return r^52811}var tl=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0];var r=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163];var t=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628];var a=function(e){return(e/2|e*128)&255};var n=function(e,r){return a(e^r)};var i=function(e){var a=r[e.length-1];var n=104;for(var i=e.length-1;i>=0;--i){var s=e[i];for(var f=0;f!=7;++f){if(s&64)a^=t[n];s*=2;--n}}return a};return function(r){var t=Uf(r);var a=i(t);var s=t.length;var f=B(16);for(var l=0;l!=16;++l)f[l]=0;var o,c,u;if((s&1)===1){o=a>>8;f[s]=n(e[0],o);--s;o=a&255;c=t[t.length-1];f[s]=n(c,o)}while(s>0){--s;o=a>>8;f[s]=n(t[s],o);--s;o=a&255;f[s]=n(t[s],o)}s=15;u=15-t.length;while(u>0){o=a>>8;f[s]=n(e[u],o);--s;--u;o=a&255;f[s]=n(t[s],o);--s;--u}return f}}();var al=function(e,r,t,a,n){if(!n)n=r;if(!a)a=tl(e);var i,s;for(i=0;i!=r.length;++i){s=r[i];s^=a[t];s=(s>>5|s<<3)&255;n[i]=s;++t}return[n,t,a]};var nl=function(e){var r=0,t=tl(e);return function(e){var a=al("",e,r,t);r=a[1];return a[0]}};function il(e,r,t,a){var n={key:Kn(e),verificationBytes:Kn(e)};if(t.password)n.verifier=rl(t.password);a.valid=n.verificationBytes===n.verifier;if(a.valid)a.insitu=nl(t.password);return n}function sl(e,r,t){var a=t||{};a.Info=e._R(2);e.l-=2;if(a.Info===1)a.Data=el(e,r);else a.Data=Zf(e,r);return a}function fl(e,r,t){var a={Type:t.biff>=8?e._R(2):0};if(a.Type)sl(e,r-2,a);else il(e,t.biff>=8?r:r-2,t,a);return a}var ll=function(){function e(e,t){switch(t.type){case"base64":return r(g.decode(e),t);case"binary":return r(e,t);case"buffer":return r(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),t);case"array":return r(oe(e),t);}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=r||{};var a=t.dense?[]:{};var n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw new Error("RTF missing table");var i={s:{c:0,r:0},e:{c:0,r:n.length-1}};n.forEach(function(e,r){if(Array.isArray(a))a[r]=[];var t=/\\\w+\b/g;var n=0;var s;var f=-1;while(s=t.exec(e)){switch(s[0]){case"\\cell":var l=e.slice(n,t.lastIndex-s[0].length);if(l[0]==" ")l=l.slice(1);++f;if(l.length){var o={v:l,t:"s"};if(Array.isArray(a))a[r][f]=o;else a[mt({r:r,c:f})]=o}break;}n=t.lastIndex}if(f>i.e.c)i.e.c=f});a["!ref"]=Et(i);return a}function t(r,t){return Bt(e(r,t),t)}function a(e){var r=["{\\rtf1\\ansi"];var t=wt(e["!ref"]),a;var n=Array.isArray(e);for(var i=t.s.r;i<=t.e.r;++i){r.push("\\trowd\\trautofit1");for(var s=t.s.c;s<=t.e.c;++s)r.push("\\cellx"+(s+1));r.push("\\pard\\intbl");for(s=t.s.c;s<=t.e.c;++s){var f=mt({r:i,c:s});a=n?(e[i]||[])[s]:e[f];if(!a||a.v==null&&(!a.f||a.F))continue;r.push(" "+(a.w||(St(a),a.w)));r.push("\\cell")}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}return{to_workbook:t,to_sheet:e,from_sheet:a}}();function ol(e){var r=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(r.slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]}function cl(e){for(var r=0,t=1;r!=3;++r)t=t*256+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function ul(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255;var n=Math.max(r,t,a),i=Math.min(r,t,a),s=n-i;if(s===0)return[0,0,r];var f=0,l=0,o=n+i;l=s/(o>1?2-o:o);switch(n){case r:f=((t-a)/s+6)%6;break;case t:f=(a-r)/s+2;break;case a:f=(r-t)/s+4;break;}return[f/6,l,o/2]}function hl(e){var r=e[0],t=e[1],a=e[2];var n=t*2*(a<.5?a:1-a),i=a-n/2;var s=[i,i,i],f=6*r;var l;if(t!==0)switch(f|0){case 0:;case 6:l=n*f;s[0]+=n;s[1]+=l;break;case 1:l=n*(2-f);s[0]+=l;s[1]+=n;break;case 2:l=n*(f-2);s[1]+=n;s[2]+=l;break;case 3:l=n*(4-f);s[1]+=l;s[2]+=n;break;case 4:l=n*(f-4);s[2]+=n;s[0]+=l;break;case 5:l=n*(6-f);s[2]+=l;s[0]+=n;break;}for(var o=0;o!=3;++o)s[o]=Math.round(s[o]*255);return s}function dl(e,r){if(r===0)return e;var t=ul(ol(e));if(r<0)t[2]=t[2]*(1+r);else t[2]=1-(1-t[2])*(1-r);return cl(hl(t))}var vl=6,pl=15,bl=1,ml=vl;function gl(e){return Math.floor((e+Math.round(128/ml)/256)*ml)}function El(e){return Math.floor((e-5)/ml*100+.5)/100}function wl(e){return Math.round((e*ml+5)/ml*256)/256}function kl(e){return wl(El(gl(e)))}function Sl(e){var r=Math.abs(e-kl(e)),t=ml;if(r>.005)for(ml=bl;ml<pl;++ml)if(Math.abs(e-kl(e))<=r){r=Math.abs(e-kl(e));t=ml}ml=t}function Bl(e){if(e.width){e.wpx=gl(e.width);e.wch=El(e.wpx);e.MDW=ml}else if(e.wpx){e.wch=El(e.wpx);e.width=wl(e.wch);e.MDW=ml}else if(typeof e.wch=="number"){e.width=wl(e.wch);e.wpx=gl(e.width);e.MDW=ml}if(e.customWidth)delete e.customWidth}var Cl=96,Tl=Cl;function _l(e){return e*96/Tl}function xl(e){return e*Tl/96}var Al={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function yl(e,r,t,a){r.Borders=[];var n={};var i=false;(e[0].match(Re)||[]).forEach(function(e){var t=Oe(e);switch(Pe(t[0])){case"<borders":;case"<borders>":;case"</borders>":break;case"<border":;case"<border>":;case"<border/>":n={};if(t.diagonalUp)n.diagonalUp=Ke(t.diagonalUp);if(t.diagonalDown)n.diagonalDown=Ke(t.diagonalDown);r.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":;case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":;case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":;case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":;case"<bottom>":break;case"</bottom>":break;case"<diagonal":;case"<diagonal>":;case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":;case"<horizontal>":;case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":;case"<vertical>":;case"<vertical/>":break;case"</vertical>":break;case"<start":;case"<start>":;case"<start/>":break;case"</start>":break;case"<end":;case"<end>":;case"<end/>":break;case"</end>":break;case"<color":;case"<color>":break;case"<color/>":;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in borders")};}})}function Il(e,r,t,a){r.Fills=[];var n={};var i=false;(e[0].match(Re)||[]).forEach(function(e){var t=Oe(e);switch(Pe(t[0])){case"<fills":;case"<fills>":;case"</fills>":break;case"<fill>":;case"<fill":;case"<fill/>":n={};r.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":;case"</gradientFill>":r.Fills.push(n);n={};break;case"<patternFill":;case"<patternFill>":if(t.patternType)n.patternType=t.patternType;break;case"<patternFill/>":;case"</patternFill>":break;case"<bgColor":if(!n.bgColor)n.bgColor={};if(t.indexed)n.bgColor.indexed=parseInt(t.indexed,10);if(t.theme)n.bgColor.theme=parseInt(t.theme,10);if(t.tint)n.bgColor.tint=parseFloat(t.tint);if(t.rgb)n.bgColor.rgb=t.rgb.slice(-6);break;case"<bgColor/>":;case"</bgColor>":break;case"<fgColor":if(!n.fgColor)n.fgColor={};if(t.theme)n.fgColor.theme=parseInt(t.theme,10);if(t.tint)n.fgColor.tint=parseFloat(t.tint);if(t.rgb!=null)n.fgColor.rgb=t.rgb.slice(-6);break;case"<fgColor/>":;case"</fgColor>":break;case"<stop":;case"<stop/>":break;case"</stop>":break;case"<color":;case"<color/>":break;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in fills")};}})}function Rl(e,r,t,a){r.Fonts=[];var n={};var s=false;(e[0].match(Re)||[]).forEach(function(e){var f=Oe(e);switch(Pe(f[0])){case"<fonts":;case"<fonts>":;case"</fonts>":break;case"<font":;case"<font>":break;case"</font>":;case"<font/>":r.Fonts.push(n);n={};break;case"<name":if(f.val)n.name=Ye(f.val);break;case"<name/>":;case"</name>":break;case"<b":n.bold=f.val?Ke(f.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=f.val?Ke(f.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(f.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break;}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=f.val?Ke(f.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=f.val?Ke(f.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=f.val?Ke(f.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=f.val?Ke(f.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=f.val?Ke(f.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":if(f.val)n.sz=+f.val;break;case"<sz/>":;case"</sz>":break;case"<vertAlign":if(f.val)n.vertAlign=f.val;break;case"<vertAlign/>":;case"</vertAlign>":break;case"<family":if(f.val)n.family=parseInt(f.val,10);break;case"<family/>":;case"</family>":break;case"<scheme":if(f.val)n.scheme=f.val;break;case"<scheme/>":;case"</scheme>":break;case"<charset":if(f.val=="1")break;f.codepage=i[parseInt(f.val,10)];break;case"<color":if(!n.color)n.color={};if(f.auto)n.color.auto=Ke(f.auto);if(f.rgb)n.color.rgb=f.rgb.slice(-6);else if(f.indexed){n.color.index=parseInt(f.indexed,10);var l=Aa[n.color.index];if(n.color.index==81)l=Aa[1];if(!l)l=Aa[1];n.color.rgb=l[0].toString(16)+l[1].toString(16)+l[2].toString(16)}else if(f.theme){n.color.theme=parseInt(f.theme,10);if(f.tint)n.color.tint=parseFloat(f.tint);if(f.theme&&t.themeElements&&t.themeElements.clrScheme){n.color.rgb=dl(t.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)}}break;case"<color/>":;case"</color>":break;case"<AlternateContent":s=true;break;case"</AlternateContent>":s=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":s=true;break;case"</ext>":s=false;break;default:if(a&&a.WTF){if(!s)throw new Error("unrecognized "+f[0]+" in fonts")};}})}function Fl(e,r,t){r.NumberFmt=[];var a=K(D._table);for(var n=0;n<a.length;++n)r.NumberFmt[a[n]]=D._table[a[n]];var i=e[0].match(Re);if(!i)return;for(n=0;n<i.length;++n){var s=Oe(i[n]);switch(Pe(s[0])){case"<numFmts":;case"</numFmts>":;case"<numFmts/>":;case"<numFmts>":break;case"<numFmt":{var f=Le(Ye(s.formatCode)),l=parseInt(s.numFmtId,10);r.NumberFmt[l]=f;if(l>0){if(l>392){for(l=392;l>60;--l)if(r.NumberFmt[l]==null)break;r.NumberFmt[l]=f}D.load(f,l)}}break;case"</numFmt>":break;default:if(t.WTF)throw new Error("unrecognized "+s[0]+" in numFmts");}}}function Dl(e){var r=["<numFmts>"];[[5,8],[23,26],[41,44],[50,392]].forEach(function(t){for(var a=t[0];a<=t[1];++a)if(e[a]!=null)r[r.length]=or("numFmt",null,{numFmtId:a,formatCode:We(e[a])})});if(r.length===1)return"";r[r.length]="</numFmts>";r[0]=or("numFmts",null,{count:r.length-2}).replace("/>",">");return r.join("")}var Ol=["numFmtId","fillId","fontId","borderId","xfId"];var Pl=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Nl(e,r,t){r.CellXf=[];var a;var n=false;(e[0].match(Re)||[]).forEach(function(e){var i=Oe(e),s=0;switch(Pe(i[0])){case"<cellXfs":;case"<cellXfs>":;case"<cellXfs/>":;case"</cellXfs>":break;case"<xf":;case"<xf/>":a=i;delete a[0];for(s=0;s<Ol.length;++s)if(a[Ol[s]])a[Ol[s]]=parseInt(a[Ol[s]],10);for(s=0;s<Pl.length;++s)if(a[Pl[s]])a[Pl[s]]=Ke(a[Pl[s]]);if(r.NumberFmt&&a.numFmtId>392){for(s=392;s>60;--s)if(r.NumberFmt[a.numFmtId]==r.NumberFmt[s]){a.numFmtId=s;break}}r.CellXf.push(a);break;case"</xf>":break;case"<alignment":;case"<alignment/>":var f={};if(i.vertical)f.vertical=i.vertical;if(i.horizontal)f.horizontal=i.horizontal;if(i.textRotation!=null)f.textRotation=i.textRotation;if(i.indent)f.indent=i.indent;if(i.wrapText)f.wrapText=Ke(i.wrapText);a.alignment=f;break;case"</alignment>":break;case"<protection":break;case"</protection>":;case"<protection/>":break;case"<AlternateContent":n=true;break;case"</AlternateContent>":n=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":n=true;break;case"</ext>":n=false;break;default:if(t&&t.WTF){if(!n)throw new Error("unrecognized "+i[0]+" in cellXfs")};}})}function Ml(e){
var r=[];r[r.length]=or("cellXfs",null);e.forEach(function(e){r[r.length]=or("xf",null,e)});r[r.length]="</cellXfs>";if(r.length===2)return"";r[0]=or("cellXfs",null,{count:r.length-2}).replace("/>",">");return r.join("")}var Ll=function Zm(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/;var r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/;var t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/;var a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/;var n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function i(s,f,l){var o={};if(!s)return o;s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var c;if(c=s.match(e))Fl(c,o,l);if(c=s.match(a))Rl(c,o,f,l);if(c=s.match(t))Il(c,o,f,l);if(c=s.match(n))yl(c,o,f,l);if(c=s.match(r))Nl(c,o,l);return o}}();var Ul=or("styleSheet",null,{xmlns:hr.main[0],"xmlns:vt":hr.vt});Ua.STY="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles";function Hl(e,r){var t=[ye,Ul],a;if(e.SSF&&(a=Dl(e.SSF))!=null)t[t.length]=a;t[t.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>';t[t.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>';t[t.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>';t[t.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>';if(a=Ml(r.cellXfs))t[t.length]=a;t[t.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>';t[t.length]='<dxfs count="0"/>';t[t.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>';if(t.length>2){t[t.length]="</styleSheet>";t[1]=t[1].replace("/>",">")}return t.join("")}function Wl(e,r){var t=e._R(2);var a=xt(e,r-2);return[t,a]}function Vl(e,r,t){if(!t)t=Jr(6+4*r.length);t._W(2,e);At(r,t);var a=t.length>t.l?t.slice(0,t.l):t;if(t.l==null)t.l=t.length;return a}function Xl(e,r,t){var a={};a.sz=e._R(2)/20;var n=ta(e,2,t);if(n.fItalic)a.italic=1;if(n.fCondense)a.condense=1;if(n.fExtend)a.extend=1;if(n.fShadow)a.shadow=1;if(n.fOutline)a.outline=1;if(n.fStrikeout)a.strike=1;var i=e._R(2);if(i===700)a.bold=1;switch(e._R(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break;}var s=e._R(1);if(s!=0)a.underline=s;var f=e._R(1);if(f>0)a.family=f;var l=e._R(1);if(l>0)a.charset=l;e.l++;a.color=ea(e,8);switch(e._R(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break;}a.name=xt(e,r-21);return a}function Gl(e,r){if(!r)r=Jr(25+4*32);r._W(2,e.sz*20);aa(e,r);r._W(2,e.bold?700:400);var t=0;if(e.vertAlign=="superscript")t=1;else if(e.vertAlign=="subscript")t=2;r._W(2,t);r._W(1,e.underline||0);r._W(1,e.family||0);r._W(1,e.charset||0);r._W(1,0);ra(e.color,r);var a=0;if(e.scheme=="major")a=1;if(e.scheme=="minor")a=2;r._W(1,a);At(e.name,r);return r.length>r.l?r.slice(0,r.l):r}var zl=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];var jl=Q(zl);var $l=Qr;function Kl(e,r){if(!r)r=Jr(4*3+8*7+16*1);var t=jl[e.patternType];if(t==null)t=40;r._W(4,t);var a=0;if(t!=40){ra({auto:1},r);ra({auto:1},r);for(;a<12;++a)r._W(4,0)}else{for(;a<4;++a)r._W(4,0);for(;a<12;++a)r._W(4,0)}return r.length>r.l?r.slice(0,r.l):r}function Yl(e,r){var t=e.l+r;var a=e._R(2);var n=e._R(2);e.l=t;return{ixfe:a,numFmtId:n}}function Ql(e,r,t){if(!t)t=Jr(16);t._W(2,r||0);t._W(2,e.numFmtId||0);t._W(2,0);t._W(2,0);t._W(2,0);t._W(1,0);t._W(1,0);var a=0;t._W(1,a);t._W(1,0);t._W(1,0);t._W(1,0);return t}function Jl(e,r){if(!r)r=Jr(10);r._W(1,0);r._W(1,0);r._W(4,0);r._W(4,0);return r}var ql=Qr;function Zl(e,r){if(!r)r=Jr(51);r._W(1,0);Jl(null,r);Jl(null,r);Jl(null,r);Jl(null,r);Jl(null,r);return r.length>r.l?r.slice(0,r.l):r}function eo(e,r){if(!r)r=Jr(12+4*10);r._W(4,e.xfId);r._W(2,1);r._W(1,+e.builtinId);r._W(1,0);Vt(e.name||"",r);return r.length>r.l?r.slice(0,r.l):r}function ro(e,r,t){var a=Jr(4+256*2*4);a._W(4,e);Vt(r,a);Vt(t,a);return a.length>a.l?a.slice(0,a.l):a}function to(e,r,t){var a={};a.NumberFmt=[];for(var n in D._table)a.NumberFmt[n]=D._table[n];a.CellXf=[];a.Fonts=[];var i=[];var s=false;qr(e,function f(e,n,l){switch(l){case 44:a.NumberFmt[e[0]]=e[1];D.load(e[1],e[0]);break;case 43:a.Fonts.push(e);if(e.color.theme!=null&&r&&r.themeElements&&r.themeElements.clrScheme){e.color.rgb=dl(r.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0)}break;case 1025:break;case 45:break;case 46:break;case 47:if(i[i.length-1]=="BrtBeginCellXFs"){a.CellXf.push(e)}break;case 48:;case 507:;case 572:;case 475:break;case 1171:;case 2102:;case 1130:;case 512:;case 2095:;case 3072:break;case 35:s=true;break;case 36:s=false;break;case 37:i.push(n);s=true;break;case 38:i.pop();s=false;break;default:if((n||"").indexOf("Begin")>0)i.push(n);else if((n||"").indexOf("End")>0)i.pop();else if(!s||t.WTF&&i[i.length-1]!="BrtACBegin")throw new Error("Unexpected record "+l+" "+n);}});return a}function ao(e,r){if(!r)return;var t=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var a=e[0];a<=e[1];++a)if(r[a]!=null)++t});if(t==0)return;et(e,"BrtBeginFmts",_t(t));[[5,8],[23,26],[41,44],[50,392]].forEach(function(t){for(var a=t[0];a<=t[1];++a)if(r[a]!=null)et(e,"BrtFmt",Vl(a,r[a]))});et(e,"BrtEndFmts")}function no(e){var r=1;if(r==0)return;et(e,"BrtBeginFonts",_t(r));et(e,"BrtFont",Gl({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"}));et(e,"BrtEndFonts")}function io(e){var r=2;if(r==0)return;et(e,"BrtBeginFills",_t(r));et(e,"BrtFill",Kl({patternType:"none"}));et(e,"BrtFill",Kl({patternType:"gray125"}));et(e,"BrtEndFills")}function so(e){var r=1;if(r==0)return;et(e,"BrtBeginBorders",_t(r));et(e,"BrtBorder",Zl({}));et(e,"BrtEndBorders")}function fo(e){var r=1;et(e,"BrtBeginCellStyleXFs",_t(r));et(e,"BrtXF",Ql({numFmtId:0,fontId:0,fillId:0,borderId:0},65535));et(e,"BrtEndCellStyleXFs")}function lo(e,r){et(e,"BrtBeginCellXFs",_t(r.length));r.forEach(function(r){et(e,"BrtXF",Ql(r,0))});et(e,"BrtEndCellXFs")}function oo(e){var r=1;et(e,"BrtBeginStyles",_t(r));et(e,"BrtStyle",eo({xfId:0,builtinId:0,name:"Normal"}));et(e,"BrtEndStyles")}function co(e){var r=0;et(e,"BrtBeginDXFs",_t(r));et(e,"BrtEndDXFs")}function uo(e){var r=0;et(e,"BrtBeginTableStyles",ro(r,"TableStyleMedium9","PivotStyleMedium4"));et(e,"BrtEndTableStyles")}function ho(){return}function vo(e,r){var t=Zr();et(t,"BrtBeginStyleSheet");ao(t,e.SSF);no(t,e);io(t,e);so(t,e);fo(t,e);lo(t,r.cellXfs);oo(t,e);co(t,e);uo(t,e);ho(t,e);et(t,"BrtEndStyleSheet");return t.end()}Ua.THEME="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme";var po=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function bo(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(Re)||[]).forEach(function(e){var n=Oe(e);switch(n[0]){case"<a:clrScheme":;case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":;case"</a:dk1>":;case"<a:lt1>":;case"</a:lt1>":;case"<a:dk2>":;case"</a:dk2>":;case"<a:lt2>":;case"</a:lt2>":;case"<a:accent1>":;case"</a:accent1>":;case"<a:accent2>":;case"</a:accent2>":;case"<a:accent3>":;case"</a:accent3>":;case"<a:accent4>":;case"</a:accent4>":;case"<a:accent5>":;case"</a:accent5>":;case"<a:accent6>":;case"</a:accent6>":;case"<a:hlink>":;case"</a:hlink>":;case"<a:folHlink>":;case"</a:folHlink>":if(n[0].charAt(1)==="/"){r.themeElements.clrScheme[po.indexOf(n[0])]=a;a={}}else{a.name=n[0].slice(3,n[0].length-1)}break;default:if(t&&t.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme");}})}function mo(){}function go(){}var Eo=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/;var wo=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/;var ko=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function So(e,r,t){r.themeElements={};var a;[["clrScheme",Eo,bo],["fontScheme",wo,mo],["fmtScheme",ko,go]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,r,t)})}var Bo=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Co(e,r){if(!e||e.length===0)return Co(To());var t;var a={};if(!(t=e.match(Bo)))throw new Error("themeElements not found in theme");So(t[0],a,r);a.raw=e;return a}function To(e,r){if(r&&r.themeXLSX)return r.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var t=[ye];t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">';t[t.length]="<a:themeElements>";t[t.length]='<a:clrScheme name="Office">';t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>';t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>';t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>';t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>';t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>';t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>';t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>';t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>';t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>';t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>';t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>';t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>';t[t.length]="</a:clrScheme>";t[t.length]='<a:fontScheme name="Office">';t[t.length]="<a:majorFont>";t[t.length]='<a:latin typeface="Cambria"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>';t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:majorFont>";t[t.length]="<a:minorFont>";t[t.length]='<a:latin typeface="Calibri"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Arial"/>';t[t.length]='<a:font script="Hebr" typeface="Arial"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Arial"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:minorFont>";t[t.length]="</a:fontScheme>";t[t.length]='<a:fmtScheme name="Office">';t[t.length]="<a:fillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="1"/>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="0"/>';t[t.length]="</a:gradFill>";t[t.length]="</a:fillStyleLst>";t[t.length]="<a:lnStyleLst>";t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]="</a:lnStyleLst>";t[t.length]="<a:effectStyleLst>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>';t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>';t[t.length]="</a:effectStyle>";t[t.length]="</a:effectStyleLst>";t[t.length]="<a:bgFillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]="</a:bgFillStyleLst>";t[t.length]="</a:fmtScheme>";t[t.length]="</a:themeElements>";t[t.length]="<a:objectDefaults>";t[t.length]="<a:spDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>';t[t.length]="</a:spDef>";t[t.length]="<a:lnDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>';t[t.length]="</a:lnDef>";t[t.length]="</a:objectDefaults>";t[t.length]="<a:extraClrSchemeLst/>";t[t.length]="</a:theme>";return t.join("")}function _o(e,r,t){var a=e.l+r;var n=e._R(4);if(n===124226)return;if(!t.cellStyles){e.l=a;return}var i=e.slice(e.l);e.l=a;var s;try{s=xe(i,{type:"array"})}catch(f){return}var l=Se(s,"theme/theme/theme1.xml",true);if(!l)return;return Co(l,t)}function xo(e){return e._R(4)}function Ao(e){var r={};r.xclrType=e._R(2);r.nTintShade=e._R(2);switch(r.xclrType){case 0:e.l+=4;break;case 1:r.xclrValue=yo(e,4);break;case 2:r.xclrValue=vi(e,4);break;case 3:r.xclrValue=xo(e,4);break;case 4:e.l+=4;break;}e.l+=8;return r}function yo(e,r){return Qr(e,r)}function Io(e,r){return Qr(e,r)}function Ro(e){var r=e._R(2);var t=e._R(2)-4;var a=[r];switch(r){case 4:;case 5:;case 7:;case 8:;case 9:;case 10:;case 11:;case 13:a[1]=Ao(e,t);break;case 6:a[1]=Io(e,t);break;case 14:;case 15:a[1]=e._R(t===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+r+" "+t);}return a}function Fo(e,r){var t=e.l+r;e.l+=2;var a=e._R(2);e.l+=2;var n=e._R(2);var i=[];while(n-- >0)i.push(Ro(e,t-e.l));return{ixfe:a,ext:i}}function Do(e,r){r.forEach(function(e){switch(e[0]){case 4:break;case 5:break;case 6:break;case 7:break;case 8:break;case 9:break;case 10:break;case 11:break;case 13:break;case 14:break;case 15:break;}})}function Oo(e){var r=[];if(!e)return r;var t=1;(e.match(Re)||[]).forEach(function(e){var a=Oe(e);switch(a[0]){case"<?xml":break;case"<calcChain":;case"<calcChain>":;case"</calcChain>":break;case"<c":delete a[0];if(a.i)t=a.i;else a.i=t;r.push(a);break;}});return r}function Po(e){var r={};r.i=e._R(4);var t={};t.r=e._R(4);t.c=e._R(4);r.r=mt(t);var a=e._R(1);if(a&2)r.l="1";if(a&8)r.a="1";return r}function No(e,r,t){var a=[];var n=false;qr(e,function i(e,r,s){switch(s){case 63:a.push(e);break;default:if((r||"").indexOf("Begin")>0){}else if((r||"").indexOf("End")>0){}else if(!n||t.WTF)throw new Error("Unexpected record "+s+" "+r);}});return a}function Mo(){}function Lo(e,r,t,a){if(!e)return e;var n=a||{};var i=false,s=false;qr(e,function f(e,r,t){if(s)return;switch(t){case 359:;case 363:;case 364:;case 366:;case 367:;case 368:;case 369:;case 370:;case 371:;case 472:;case 577:;case 578:;case 579:;case 580:;case 581:;case 582:;case 583:;case 584:;case 585:;case 586:;case 587:break;case 35:i=true;break;case 36:i=false;break;default:if((r||"").indexOf("Begin")>0){}else if((r||"").indexOf("End")>0){}else if(!i||n.WTF)throw new Error("Unexpected record "+t.toString(16)+" "+r);}},n)}Ua.IMG="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image";Ua.DRAW="http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing";function Uo(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}var Ho=1024;function Wo(e,r){var t=[21600,21600];var a=["m0,0l0",t[1],t[0],t[1],t[0],"0xe"].join(",");var n=[or("xml",null,{"xmlns:v":dr.v,"xmlns:o":dr.o,"xmlns:x":dr.x,"xmlns:mv":dr.mv}).replace(/\/>/,">"),or("o:shapelayout",or("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),or("v:shapetype",[or("v:stroke",null,{joinstyle:"miter"}),or("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:t.join(","),path:a})];while(Ho<e*1e3)Ho+=1e3;r.forEach(function(e){var r=bt(e[0]);var t={color2:"#BEFF82",type:"gradient"};if(t.type=="gradient")t.angle="-180";var a=t.type=="gradient"?or("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null;var i=or("v:fill",a,t);var s={on:"t",obscured:"t"};++Ho;n=n.concat(["<v:shape"+lr({id:"_x0000_s"+Ho,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,or("v:shadow",null,s),or("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",fr("x:Anchor",[r.c+1,0,r.r+1,0,r.c+3,20,r.r+5,20].join(",")),fr("x:AutoFill","False"),fr("x:Row",String(r.r)),fr("x:Column",String(r.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])});n.push("</xml>");return n.join("")}Ua.CMNT="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments";function Vo(e,r){var t=Array.isArray(e);var a;r.forEach(function(r){var n=bt(r.ref);if(t){if(!e[n.r])e[n.r]=[];a=e[n.r][n.c]}else a=e[r.ref];if(!a){a={t:"z"};if(t)e[n.r][n.c]=a;else e[r.ref]=a;var i=wt(e["!ref"]||"BDWGO1000001:A1");if(i.s.r>n.r)i.s.r=n.r;if(i.e.r<n.r)i.e.r=n.r;if(i.s.c>n.c)i.s.c=n.c;if(i.e.c<n.c)i.e.c=n.c;var s=Et(i);if(s!==e["!ref"])e["!ref"]=s}if(!a.c)a.c=[];var f={a:r.author,t:r.t,r:r.r};if(r.h)f.h=r.h;a.c.push(f)})}function Xo(e,r){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var t=[];var a=[];var n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);if(n&&n[1])n[1].split(/<\/\w*:?author>/).forEach(function(e){if(e===""||e.trim()==="")return;var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);if(r)t.push(r[1])});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);if(i&&i[1])i[1].split(/<\/\w*:?comment>/).forEach(function(e){if(e===""||e.trim()==="")return;var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(!n)return;var i=Oe(n[0]);var s={author:i.authorId&&t[i.authorId]||"sheetjsghost",ref:i.ref,guid:i.guid};var f=bt(i.ref);if(r.sheetRows&&r.sheetRows<=f.r)return;var l=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/);var o=!!l&&!!l[1]&&xf(l[1])||{r:"",t:"",h:""};s.r=o.r;if(o.r=="<t></t>")o.t=o.h="";s.t=(o.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n");if(r.cellHTML)s.h=o.h;a.push(s)});return a}var Go=or("comments",null,{xmlns:hr.main[0]});function zo(e){var r=[ye,Go];var t=[];r.push("<authors>");e.forEach(function(e){e[1].forEach(function(e){var a=We(e.a);if(t.indexOf(a)>-1)return;t.push(a);r.push("<author>"+a+"</author>")})});r.push("</authors>");r.push("<commentList>");e.forEach(function(e){e[1].forEach(function(a){r.push('<comment ref="'+e[0]+'" authorId="'+t.indexOf(We(a.a))+'"><text>');r.push(fr("t",a.t==null?"":We(a.t)));r.push("</text></comment>")})});r.push("</commentList>");if(r.length>2){r[r.length]="</comments>";r[1]=r[1].replace("/>",">")}return r.join("")}function jo(e){var r={};r.iauthor=e._R(4);var t=Qt(e,16);r.rfx=t.s;r.ref=mt(t.s);e.l+=16;return r}function $o(e,r){if(r==null)r=Jr(36);r._W(4,e[1].iauthor);Jt(e[0],r);r._W(4,0);r._W(4,0);r._W(4,0);r._W(4,0);return r}var Ko=xt;function Yo(e){return At(e.slice(0,54))}function Qo(e,r){var t=[];var a=[];var n={};var i=false;qr(e,function s(e,f,l){switch(l){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t;n.h=e.h;n.r=e.r;break;case 636:n.author=a[n.iauthor];delete n.iauthor;if(r.sheetRows&&n.rfx&&r.sheetRows<=n.rfx.r)break;if(!n.t)n.t="";delete n.rfx;t.push(n);break;case 3072:break;case 35:i=true;break;case 36:i=false;break;case 37:break;case 38:break;default:if((f||"").indexOf("Begin")>0){}else if((f||"").indexOf("End")>0){}else if(!i||r.WTF)throw new Error("Unexpected record "+l+" "+f);}});return t}function Jo(e){var r=Zr();var t=[];et(r,"BrtBeginComments");et(r,"BrtBeginCommentAuthors");e.forEach(function(e){e[1].forEach(function(e){if(t.indexOf(e.a)>-1)return;t.push(e.a.slice(0,54));et(r,"BrtCommentAuthor",Yo(e.a))})});et(r,"BrtEndCommentAuthors");et(r,"BrtBeginCommentList");e.forEach(function(e){e[1].forEach(function(a){a.iauthor=t.indexOf(a.a);var n={s:bt(e[0]),e:bt(e[0])};et(r,"BrtBeginComment",$o([n,a]));if(a.t&&a.t.length>0)et(r,"BrtCommentText",Ot(a));et(r,"BrtEndComment");delete a.iauthor})});et(r,"BrtEndCommentList");et(r,"BrtEndComments");return r.end()}var qo="application/vnd.ms-office.vbaProject";function Zo(e){var r=V.utils.cfb_new({root:"R"});e.FullPaths.forEach(function(t,a){if(t.slice(-1)==="/"||!t.match(/_VBA_PROJECT_CUR/))return;var n=t.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");V.utils.cfb_add(r,n,e.FileIndex[a].content)});return V.write(r)}function ec(e,r){r.FullPaths.forEach(function(t,a){if(a==0)return;var n=t.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");if(n.slice(-1)!=="/")V.utils.cfb_add(e,n,r.FileIndex[a].content)})}var rc=["xlsb","xlsm","xlam","biff8","xla"];Ua.DS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet";Ua.MS="http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet";function tc(){return{"!type":"dialog"}}function ac(){return{"!type":"dialog"}}function nc(){return{"!type":"macro"}}function ic(){return{"!type":"macro"}}var sc=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g;var r={r:0,c:0};function t(e,t,a,n){var i=false,s=false;if(a.length==0)s=true;else if(a.charAt(0)=="["){s=true;a=a.slice(1,-1)}if(n.length==0)i=true;else if(n.charAt(0)=="["){i=true;n=n.slice(1,-1)}var f=a.length>0?parseInt(a,10)|0:0,l=n.length>0?parseInt(n,10)|0:0;if(i)l+=r.c;else--l;if(s)f+=r.r;else--f;return t+(i?"":"$")+ht(l)+(s?"":"$")+lt(f)}return function a(n,i){r=i;return n.replace(e,t)}}();var fc=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g;var lc=function(){return function e(r,t){return r.replace(fc,function(e,r,a,n,i,s){var f=ut(n)-(a?0:t.c);var l=ft(s)-(i?0:t.r);var o=l==0?"":!i?"["+l+"]":l+1;var c=f==0?"":!a?"["+f+"]":f+1;return r+"R"+o+"C"+c})}}();function oc(e,r){return e.replace(fc,function(e,t,a,n,i,s){return t+(a=="$"?a+n:ht(ut(n)+r.c))+(i=="$"?i+s:lt(ft(s)+r.r))})}function cc(e,r,t){var a=gt(r),n=a.s,i=bt(t);var s={r:i.r-n.r,c:i.c-n.c};return oc(e,s)}function uc(e){if(e.length==1)return false;return true}function hc(e){return e.replace(/_xlfn\./g,"")}function dc(e){e.l+=1;return}function vc(e,r){var t=e._R(r==1?1:2);return[t&16383,t>>14&1,t>>15&1]}function pc(e,r,t){var a=2;if(t){if(t.biff>=2&&t.biff<=5)return bc(e,r,t);else if(t.biff==12)a=4}var n=e._R(a),i=e._R(a);var s=vc(e,2);var f=vc(e,2);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function bc(e){var r=vc(e,2),t=vc(e,2);var a=e._R(1);var n=e._R(1);return{s:{r:r[0],c:a,cRel:r[1],rRel:r[2]},e:{r:t[0],c:n,cRel:t[1],rRel:t[2]}}}function mc(e,r,t){if(t.biff<8)return bc(e,r,t);var a=e._R(t.biff==12?4:2),n=e._R(t.biff==12?4:2);var i=vc(e,2);var s=vc(e,2);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:n,c:s[0],cRel:s[1],rRel:s[2]}}}function gc(e,r,t){if(t&&t.biff>=2&&t.biff<=5)return Ec(e,r,t);var a=e._R(t&&t.biff==12?4:2);var n=vc(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Ec(e){var r=vc(e,2);var t=e._R(1);return{r:r[0],c:t,cRel:r[1],rRel:r[2]}}function wc(e){var r=e._R(2);var t=e._R(2);return{r:r,c:t&255,fQuoted:!!(t&16384),cRel:t>>15,rRel:t>>15}}function kc(e,r,t){var a=t&&t.biff?t.biff:8;if(a>=2&&a<=5)return Sc(e,r,t);var n=e._R(a>=12?4:2);var i=e._R(2);var s=(i&16384)>>14,f=(i&32768)>>15;i&=16383;if(f==1)while(n>524287)n-=1048576;if(s==1)while(i>8191)i=i-16384;return{r:n,c:i,cRel:s,rRel:f}}function Sc(e){var r=e._R(2);var t=e._R(1);var a=(r&32768)>>15,n=(r&16384)>>14;r&=16383;if(a==1&&r>=8192)r=r-16384;if(n==1&&t>=128)t=t-256;return{r:r,c:t,cRel:n,rRel:a}}function Bc(e,r,t){var a=(e[e.l++]&96)>>5;var n=pc(e,t.biff>=2&&t.biff<=5?6:8,t);return[a,n]}function Cc(e,r,t){var a=(e[e.l++]&96)>>5;var n=e._R(2,"i");var i=8;if(t)switch(t.biff){case 5:e.l+=12;i=6;break;case 12:i=12;break;}var s=pc(e,i,t);return[a,n,s]}function Tc(e,r,t){var a=(e[e.l++]&96)>>5;e.l+=t&&t.biff>8?12:t.biff<8?6:8;return[a]}function _c(e,r,t){var a=(e[e.l++]&96)>>5;var n=e._R(2);var i=8;if(t)switch(t.biff){case 5:e.l+=12;i=6;break;case 12:i=12;break;}e.l+=i;return[a,n]}function xc(e,r,t){var a=(e[e.l++]&96)>>5;var n=mc(e,r-1,t);return[a,n]}function Ac(e,r,t){var a=(e[e.l++]&96)>>5;e.l+=t.biff==2?6:t.biff==12?14:7;return[a]}function yc(e){var r=e[e.l+1]&1;var t=1;e.l+=4;return[r,t]}function Ic(e,r,t){e.l+=2;var a=e._R(t&&t.biff==2?1:2);var n=[];for(var i=0;i<=a;++i)n.push(e._R(t&&t.biff==2?1:2));return n}function Rc(e,r,t){var a=e[e.l+1]&255?1:0;e.l+=2;return[a,e._R(t&&t.biff==2?1:2)]}function Fc(e,r,t){var a=e[e.l+1]&255?1:0;e.l+=2;return[a,e._R(t&&t.biff==2?1:2)]}function Dc(e){var r=e[e.l+1]&255?1:0;e.l+=2;return[r,e._R(2)]}function Oc(e,r,t){var a=e[e.l+1]&255?1:0;e.l+=t&&t.biff==2?3:4;return[a]}function Pc(e){var r=e._R(1),t=e._R(1);return[r,t]}function Nc(e){e._R(2);return Pc(e,2)}function Mc(e){e._R(2);return Pc(e,2)}function Lc(e,r,t){var a=(e[e.l]&96)>>5;e.l+=1;var n=gc(e,0,t);return[a,n]}function Uc(e,r,t){var a=(e[e.l]&96)>>5;e.l+=1;var n=kc(e,0,t);return[a,n]}function Hc(e,r,t){var a=(e[e.l]&96)>>5;e.l+=1;var n=e._R(2);if(t&&t.biff==5)e.l+=12;var i=gc(e,0,t);return[a,n,i]}function Wc(e,r,t){
var a=(e[e.l]&96)>>5;e.l+=1;var n=e._R(t&&t.biff<=3?1:2);return[nh[n],ah[n],a]}function Vc(e,r,t){var a=e[e.l++];var n=e._R(1),i=t&&t.biff<=3?[a==88?-1:0,e._R(1)]:Xc(e);return[n,(i[0]===0?ah:th)[i[1]]]}function Xc(e){return[e[e.l+1]>>7,e._R(2)&32767]}function Gc(e,r,t){e.l+=t&&t.biff==2?3:4;return}function zc(e,r,t){e.l++;if(t&&t.biff==12)return[e._R(4,"i"),0];var a=e._R(2);var n=e._R(t&&t.biff==2?1:2);return[a,n]}function jc(e){e.l++;return ya[e._R(1)]}function $c(e){e.l++;return e._R(2)}function Kc(e){e.l++;return e._R(1)!==0}function Yc(e){e.l++;return qt(e,8)}function Qc(e,r,t){e.l++;return Zn(e,r-1,t)}function Jc(e,r){var t=[e._R(1)];if(r==12)switch(t[0]){case 2:t[0]=4;break;case 4:t[0]=16;break;case 0:t[0]=1;break;case 1:t[0]=2;break;}switch(t[0]){case 4:t[1]=jn(e,1)?"TRUE":"FALSE";if(r!=12)e.l+=7;break;case 37:;case 16:t[1]=ya[e[e.l]];e.l+=r==12?4:8;break;case 0:e.l+=8;break;case 1:t[1]=qt(e,8);break;case 2:t[1]=ni(e,0,{biff:r>0&&r<8?2:r});break;default:throw new Error("Bad SerAr: "+t[0]);}return t}function qc(e,r,t){var a=e._R(t.biff==12?4:2);var n=[];for(var i=0;i!=a;++i)n.push((t.biff==12?Qt:Bi)(e,8));return n}function Zc(e,r,t){var a=0,n=0;if(t.biff==12){a=e._R(4);n=e._R(4)}else{n=1+e._R(1);a=1+e._R(2)}if(t.biff>=2&&t.biff<8){--a;if(--n==0)n=256}for(var i=0,s=[];i!=a&&(s[i]=[]);++i)for(var f=0;f!=n;++f)s[i][f]=Jc(e,t.biff);return s}function eu(e,r,t){var a=e._R(1)>>>5&3;var n=!t||t.biff>=8?4:2;var i=e._R(n);switch(t.biff){case 2:e.l+=5;break;case 3:;case 4:e.l+=8;break;case 5:e.l+=12;break;}return[a,0,i]}function ru(e,r,t){if(t.biff==5)return tu(e,r,t);var a=e._R(1)>>>5&3;var n=e._R(2);var i=e._R(4);return[a,n,i]}function tu(e){var r=e._R(1)>>>5&3;var t=e._R(2,"i");e.l+=8;var a=e._R(2);e.l+=12;return[r,t,a]}function au(e,r,t){var a=e._R(1)>>>5&3;e.l+=t&&t.biff==2?3:4;var n=e._R(t&&t.biff==2?1:2);return[a,n]}function nu(e,r,t){var a=e._R(1)>>>5&3;var n=e._R(t&&t.biff==2?1:2);return[a,n]}function iu(e,r,t){var a=e._R(1)>>>5&3;e.l+=4;if(t.biff<8)e.l--;if(t.biff==12)e.l+=2;return[a]}function su(e,r,t){var a=(e[e.l++]&96)>>5;var n=e._R(2);var i=4;if(t)switch(t.biff){case 5:i=15;break;case 12:i=6;break;}e.l+=i;return[a,n]}var fu=Qr;var lu=Qr;var ou=Qr;function cu(e,r,t){e.l+=2;return[wc(e,4,t)]}function uu(e){e.l+=6;return[]}var hu=cu;var du=uu;var vu=uu;var pu=cu;function bu(e){e.l+=2;return[Kn(e),e._R(2)&1]}var mu=cu;var gu=bu;var Eu=uu;var wu=cu;var ku=cu;var Su=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Bu(e){e.l+=2;var r=e._R(2);var t=e._R(2);var a=e._R(4);var n=e._R(2);var i=e._R(2);var s=Su[t>>2&31];return{ixti:r,coltype:t&3,rt:s,idx:a,c:n,C:i}}function Cu(e){e.l+=2;return[e._R(4)]}function Tu(e,r,t){e.l+=5;e.l+=2;e.l+=t.biff==2?1:4;return["PTGSHEET"]}function _u(e,r,t){e.l+=t.biff==2?4:5;return["PTGENDSHEET"]}function xu(e){var r=e._R(1)>>>5&3;var t=e._R(2);return[r,t]}function Au(e){var r=e._R(1)>>>5&3;var t=e._R(2);return[r,t]}function yu(e){e.l+=4;return[0,0]}var Iu={1:{n:"PtgExp",f:zc},2:{n:"PtgTbl",f:ou},3:{n:"PtgAdd",f:dc},4:{n:"PtgSub",f:dc},5:{n:"PtgMul",f:dc},6:{n:"PtgDiv",f:dc},7:{n:"PtgPower",f:dc},8:{n:"PtgConcat",f:dc},9:{n:"PtgLt",f:dc},10:{n:"PtgLe",f:dc},11:{n:"PtgEq",f:dc},12:{n:"PtgGe",f:dc},13:{n:"PtgGt",f:dc},14:{n:"PtgNe",f:dc},15:{n:"PtgIsect",f:dc},16:{n:"PtgUnion",f:dc},17:{n:"PtgRange",f:dc},18:{n:"PtgUplus",f:dc},19:{n:"PtgUminus",f:dc},20:{n:"PtgPercent",f:dc},21:{n:"PtgParen",f:dc},22:{n:"PtgMissArg",f:dc},23:{n:"PtgStr",f:Qc},26:{n:"PtgSheet",f:Tu},27:{n:"PtgEndSheet",f:_u},28:{n:"PtgErr",f:jc},29:{n:"PtgBool",f:Kc},30:{n:"PtgInt",f:$c},31:{n:"PtgNum",f:Yc},32:{n:"PtgArray",f:Ac},33:{n:"PtgFunc",f:Wc},34:{n:"PtgFuncVar",f:Vc},35:{n:"PtgName",f:eu},36:{n:"PtgRef",f:Lc},37:{n:"PtgArea",f:Bc},38:{n:"PtgMemArea",f:au},39:{n:"PtgMemErr",f:fu},40:{n:"PtgMemNoMem",f:lu},41:{n:"PtgMemFunc",f:nu},42:{n:"PtgRefErr",f:iu},43:{n:"PtgAreaErr",f:Tc},44:{n:"PtgRefN",f:Uc},45:{n:"PtgAreaN",f:xc},46:{n:"PtgMemAreaN",f:xu},47:{n:"PtgMemNoMemN",f:Au},57:{n:"PtgNameX",f:ru},58:{n:"PtgRef3d",f:Hc},59:{n:"PtgArea3d",f:Cc},60:{n:"PtgRefErr3d",f:su},61:{n:"PtgAreaErr3d",f:_c},255:{}};var Ru={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61};(function(){for(var e in Ru)Iu[e]=Iu[Ru[e]]})();var Fu={1:{n:"PtgElfLel",f:bu},2:{n:"PtgElfRw",f:wu},3:{n:"PtgElfCol",f:hu},6:{n:"PtgElfRwV",f:ku},7:{n:"PtgElfColV",f:pu},10:{n:"PtgElfRadical",f:mu},11:{n:"PtgElfRadicalS",f:Eu},13:{n:"PtgElfColS",f:du},15:{n:"PtgElfColSV",f:vu},16:{n:"PtgElfRadicalLel",f:gu},25:{n:"PtgList",f:Bu},29:{n:"PtgSxName",f:Cu},255:{}};var Du={0:{n:"PtgAttrNoop",f:yu},1:{n:"PtgAttrSemi",f:Oc},2:{n:"PtgAttrIf",f:Fc},4:{n:"PtgAttrChoose",f:Ic},8:{n:"PtgAttrGoto",f:Rc},16:{n:"PtgAttrSum",f:Gc},32:{n:"PtgAttrBaxcel",f:yc},64:{n:"PtgAttrSpace",f:Nc},65:{n:"PtgAttrSpaceSemi",f:Mc},128:{n:"PtgAttrIfError",f:Dc},255:{}};Du[33]=Du[32];function Ou(e,r,t,a){if(a.biff<8)return Qr(e,r);var n=e.l+r;var i=[];for(var s=0;s!==t.length;++s){switch(t[s][0]){case"PtgArray":t[s][1]=Zc(e,0,a);i.push(t[s][1]);break;case"PtgMemArea":t[s][2]=qc(e,t[s][1],a);i.push(t[s][2]);break;case"PtgExp":if(a&&a.biff==12){t[s][1][1]=e._R(4);i.push(t[s][1])}break;case"PtgList":;case"PtgElfRadicalS":;case"PtgElfColS":;case"PtgElfColSV":throw"Unsupported "+t[s][0];default:break;}}r=n-e.l;if(r!==0)i.push(Qr(e,r));return i}function Pu(e,r,t){var a=e.l+r;var n,i,s=[];while(a!=e.l){r=a-e.l;i=e[e.l];n=Iu[i];if(i===24||i===25)n=(i===24?Fu:Du)[e[e.l+1]];if(!n||!n.f){Qr(e,r)}else{s.push([n.n,n.f(e,r,t)])}}return s}function Nu(e){var r=[];for(var t=0;t<e.length;++t){var a=e[t],n=[];for(var i=0;i<a.length;++i){var s=a[i];if(s)switch(s[0]){case 2:n.push('"'+s[1].replace(/"/g,'""')+'"');break;default:n.push(s[1]);}else n.push("")}r.push(n.join(","))}return r.join(";")}var Mu={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};var Lu=new RegExp(/[^\w\u4E00-\u9FFF\u3040-\u30FF]/);function Uu(e,r){if(!e&&!(r&&r.biff<=5&&r.biff>=2))throw new Error("empty sheet name");if(Lu.test(e))return"'"+e+"'";return e}function Hu(e,r,t){if(!e)return"SH33TJSERR0";if(t.biff>8&&(!e.XTI||!e.XTI[r]))return e.SheetNames[r];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[r];if(t.biff<8){if(r>1e4)r-=65536;if(r<0)r=-r;return r==0?"":e.XTI[r-1]}if(!a)return"SH33TJSERR1";var n="";if(t.biff>8)switch(e[a[0]][0]){case 357:n=a[1]==-1?"#REF":e.SheetNames[a[1]];return a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(t.SID!=null)return e.SheetNames[t.SID];return"SH33TJSSAME"+e[a[0]][0];case 355:;default:return"SH33TJSSRC"+e[a[0]][0];}switch(e[a[0]][0][0]){case 1025:n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3";return a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4";return a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]];}}function Wu(e,r,t){var a=Hu(e,r,t);return a=="#REF"?a:Uu(a,t)}function Vu(e,r,t,a,n){var i=n&&n.biff||8;var s={s:{c:0,r:0},e:{c:0,r:0}};var f=[],l,o,c,u=0,h=0,d,v="";if(!e[0]||!e[0][0])return"";var p=-1,b="";for(var m=0,g=e[0].length;m<g;++m){var E=e[0][m];switch(E[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":;case"PtgConcat":;case"PtgDiv":;case"PtgEq":;case"PtgGe":;case"PtgGt":;case"PtgLe":;case"PtgLt":;case"PtgMul":;case"PtgNe":;case"PtgPower":;case"PtgSub":l=f.pop();o=f.pop();if(p>=0){switch(e[0][p][1][0]){case 0:b=ue(" ",e[0][p][1][1]);break;case 1:b=ue("\r",e[0][p][1][1]);break;default:b="";if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][p][1][0]);}o=o+b;p=-1}f.push(o+Mu[E[0]]+l);break;case"PtgIsect":l=f.pop();o=f.pop();f.push(o+" "+l);break;case"PtgUnion":l=f.pop();o=f.pop();f.push(o+","+l);break;case"PtgRange":l=f.pop();o=f.pop();f.push(o+":"+l);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=rt(E[1][1],s,n);f.push(at(c,i));break;case"PtgRefN":c=t?rt(E[1][1],t,n):E[1][1];f.push(at(c,i));break;case"PtgRef3d":u=E[1][1];c=rt(E[1][2],s,n);v=Wu(a,u,n);var w=v;f.push(v+"!"+at(c,i));break;case"PtgFunc":;case"PtgFuncVar":var k=E[1][0],S=E[1][1];if(!k)k=0;k&=127;var B=k==0?[]:f.slice(-k);f.length-=k;if(S==="User")S=B.shift();f.push(S+"("+B.join(",")+")");break;case"PtgBool":f.push(E[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(E[1]);break;case"PtgNum":f.push(String(E[1]));break;case"PtgStr":f.push('"'+E[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(E[1]);break;case"PtgAreaN":d=tt(E[1][1],t?{s:t}:s,n);f.push(nt(d,n));break;case"PtgArea":d=tt(E[1][1],s,n);f.push(nt(d,n));break;case"PtgArea3d":u=E[1][1];d=E[1][2];v=Wu(a,u,n);f.push(v+"!"+nt(d,n));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":;case"PtgAttrSemi":break;case"PtgName":h=E[1][2];var C=(a.names||[])[h-1]||(a[0]||[])[h];var T=C?C.Name:"SH33TJSNAME"+String(h);if(T in ih)T=ih[T];f.push(T);break;case"PtgNameX":var _=E[1][1];h=E[1][2];var x;if(n.biff<=5){if(_<0)_=-_;if(a[_])x=a[_][h]}else{var A="";if(((a[_]||[])[0]||[])[0]==14849){}else if(((a[_]||[])[0]||[])[0]==1025){if(a[_][h]&&a[_][h].itab>0){A=a.SheetNames[a[_][h].itab-1]+"!"}}else A=a.SheetNames[h-1]+"!";if(a[_]&&a[_][h])A+=a[_][h].Name;else if(a[0]&&a[0][h])A+=a[0][h].Name;else{var y=(Hu(a,_,n)||"").split(";;");if(y[h-1])A=y[h-1];else A+="SH33TJSERRX"}f.push(A);break}if(!x)x={Name:"SH33TJSERRY"};f.push(x.Name);break;case"PtgParen":var I="(",R=")";if(p>=0){b="";switch(e[0][p][1][0]){case 2:I=ue(" ",e[0][p][1][1])+I;break;case 3:I=ue("\r",e[0][p][1][1])+I;break;case 4:R=ue(" ",e[0][p][1][1])+R;break;case 5:R=ue("\r",e[0][p][1][1])+R;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][p][1][0]);}p=-1}f.push(I+f.pop()+R);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":c={c:E[1][1],r:E[1][0]};var F={c:t.c,r:t.r};if(a.sharedf[mt(c)]){var D=a.sharedf[mt(c)];f.push(Vu(D,s,F,a,n))}else{var O=false;for(l=0;l!=a.arrayf.length;++l){o=a.arrayf[l];if(c.c<o[0].s.c||c.c>o[0].e.c)continue;if(c.r<o[0].s.r||c.r>o[0].e.r)continue;f.push(Vu(o[1],s,F,a,n));O=true;break}if(!O)f.push(E[1])}break;case"PtgArray":f.push("{"+Nu(E[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":;case"PtgAttrSpaceSemi":p=m;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+E[1].idx+"[#"+E[1].rt+"]");break;case"PtgMemAreaN":;case"PtgMemNoMemN":;case"PtgAttrNoop":;case"PtgSheet":;case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":;case"PtgElfColS":;case"PtgElfColSV":;case"PtgElfColV":;case"PtgElfLel":;case"PtgElfRadical":;case"PtgElfRadicalLel":;case"PtgElfRadicalS":;case"PtgElfRw":;case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(E));default:throw new Error("Unrecognized Formula Token: "+String(E));}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3)if(p>=0&&P.indexOf(e[0][m][0])==-1){E=e[0][p];var N=true;switch(E[1][0]){case 4:N=false;case 0:b=ue(" ",E[1][1]);break;case 5:N=false;case 1:b=ue("\r",E[1][1]);break;default:b="";if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+E[1][0]);}f.push((N?b:"")+f.pop()+(N?"":b));p=-1}}if(f.length>1&&n.WTF)throw new Error("bad formula stack");return f[0]}function Xu(e,r,t){var a=e.l+r,n=t.biff==2?1:2;var i,s=e._R(n);if(s==65535)return[[],Qr(e,r-2)];var f=Pu(e,s,t);if(r!==s+n)i=Ou(e,r-s-n,f,t);e.l=a;return[f,i]}function Gu(e,r,t){var a=e.l+r,n=t.biff==2?1:2;var i,s=e._R(n);if(s==65535)return[[],Qr(e,r-2)];var f=Pu(e,s,t);if(r!==s+n)i=Ou(e,r-s-n,f,t);e.l=a;return[f,i]}function zu(e,r,t,a){var n=e.l+r;var i=Pu(e,a,t);var s;if(n!==e.l)s=Ou(e,n-e.l,i,t);return[i,s]}function ju(e,r,t){var a=e.l+r;var n,i=e._R(2);var s=Pu(e,i,t);if(i==65535)return[[],Qr(e,r-2)];if(r!==i+2)n=Ou(e,a-i-2,s,t);return[s,n]}function $u(e){var r;if(Lr(e,e.l+6)!==65535)return[qt(e),"n"];switch(e[e.l]){case 0:e.l+=8;return["String","s"];case 1:r=e[e.l+2]===1;e.l+=8;return[r,"b"];case 2:r=e[e.l+2];e.l+=8;return[r,"e"];case 3:e.l+=8;return["","s"];}return[]}function Ku(e){if(e==null){var r=Jr(8);r._W(1,3);r._W(1,0);r._W(2,0);r._W(2,0);r._W(2,65535);return r}else if(typeof e=="number")return Zt(e);return Zt(0)}function Yu(e,r,t){var a=e.l+r;var n=bi(e,6);if(t.biff==2)++e.l;var i=$u(e,8);var s=e._R(1);if(t.biff!=2){e._R(1);if(t.biff>=5){e._R(4)}}var f=Gu(e,a-e.l,t);return{cell:n,val:i[0],formula:f,shared:s>>3&1,tt:i[1]}}function Qu(e,r,t,a,n){var i=mi(r,t,n);var s=Ku(e.v);var f=Jr(6);var l=1|32;f._W(2,l);f._W(4,0);var o=Jr(e.bf.length);for(var c=0;c<e.bf.length;++c)o[c]=e.bf[c];var u=I([i,s,f,o]);return u}function Ju(e,r,t){var a=e._R(4);var n=Pu(e,a,t);var i=e._R(4);var s=i>0?Ou(e,i,n,t):null;return[n,s]}var qu=Ju;var Zu=Ju;var eh=Ju;var rh=Ju;var th={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"};var ah={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"};var nh={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};var ih={"_xlfn.ACOT":"ACOT","_xlfn.ACOTH":"ACOTH","_xlfn.AGGREGATE":"AGGREGATE","_xlfn.ARABIC":"ARABIC","_xlfn.AVERAGEIF":"AVERAGEIF","_xlfn.AVERAGEIFS":"AVERAGEIFS","_xlfn.BASE":"BASE","_xlfn.BETA.DIST":"BETA.DIST","_xlfn.BETA.INV":"BETA.INV","_xlfn.BINOM.DIST":"BINOM.DIST","_xlfn.BINOM.DIST.RANGE":"BINOM.DIST.RANGE","_xlfn.BINOM.INV":"BINOM.INV","_xlfn.BITAND":"BITAND","_xlfn.BITLSHIFT":"BITLSHIFT","_xlfn.BITOR":"BITOR","_xlfn.BITRSHIFT":"BITRSHIFT","_xlfn.BITXOR":"BITXOR","_xlfn.CEILING.MATH":"CEILING.MATH","_xlfn.CEILING.PRECISE":"CEILING.PRECISE","_xlfn.CHISQ.DIST":"CHISQ.DIST","_xlfn.CHISQ.DIST.RT":"CHISQ.DIST.RT","_xlfn.CHISQ.INV":"CHISQ.INV","_xlfn.CHISQ.INV.RT":"CHISQ.INV.RT","_xlfn.CHISQ.TEST":"CHISQ.TEST","_xlfn.COMBINA":"COMBINA","_xlfn.CONCAT":"CONCAT","_xlfn.CONFIDENCE.NORM":"CONFIDENCE.NORM","_xlfn.CONFIDENCE.T":"CONFIDENCE.T","_xlfn.COT":"COT","_xlfn.COTH":"COTH","_xlfn.COUNTIFS":"COUNTIFS","_xlfn.COVARIANCE.P":"COVARIANCE.P","_xlfn.COVARIANCE.S":"COVARIANCE.S","_xlfn.CSC":"CSC","_xlfn.CSCH":"CSCH","_xlfn.DAYS":"DAYS","_xlfn.DECIMAL":"DECIMAL","_xlfn.ECMA.CEILING":"ECMA.CEILING","_xlfn.ERF.PRECISE":"ERF.PRECISE","_xlfn.ERFC.PRECISE":"ERFC.PRECISE","_xlfn.EXPON.DIST":"EXPON.DIST","_xlfn.F.DIST":"F.DIST","_xlfn.F.DIST.RT":"F.DIST.RT","_xlfn.F.INV":"F.INV","_xlfn.F.INV.RT":"F.INV.RT","_xlfn.F.TEST":"F.TEST","_xlfn.FILTERXML":"FILTERXML","_xlfn.FLOOR.MATH":"FLOOR.MATH","_xlfn.FLOOR.PRECISE":"FLOOR.PRECISE","_xlfn.FORECAST.ETS":"FORECAST.ETS","_xlfn.FORECAST.ETS.CONFINT":"FORECAST.ETS.CONFINT","_xlfn.FORECAST.ETS.SEASONALITY":"FORECAST.ETS.SEASONALITY","_xlfn.FORECAST.ETS.STAT":"FORECAST.ETS.STAT","_xlfn.FORECAST.LINEAR":"FORECAST.LINEAR","_xlfn.FORMULATEXT":"FORMULATEXT","_xlfn.GAMMA":"GAMMA","_xlfn.GAMMA.DIST":"GAMMA.DIST","_xlfn.GAMMA.INV":"GAMMA.INV","_xlfn.GAMMALN.PRECISE":"GAMMALN.PRECISE","_xlfn.GAUSS":"GAUSS","_xlfn.HYPGEOM.DIST":"HYPGEOM.DIST","_xlfn.IFERROR":"IFERROR","_xlfn.IFNA":"IFNA","_xlfn.IFS":"IFS","_xlfn.IMCOSH":"IMCOSH","_xlfn.IMCOT":"IMCOT","_xlfn.IMCSC":"IMCSC","_xlfn.IMCSCH":"IMCSCH","_xlfn.IMSEC":"IMSEC","_xlfn.IMSECH":"IMSECH","_xlfn.IMSINH":"IMSINH","_xlfn.IMTAN":"IMTAN","_xlfn.ISFORMULA":"ISFORMULA","_xlfn.ISO.CEILING":"ISO.CEILING","_xlfn.ISOWEEKNUM":"ISOWEEKNUM","_xlfn.LOGNORM.DIST":"LOGNORM.DIST","_xlfn.LOGNORM.INV":"LOGNORM.INV","_xlfn.MAXIFS":"MAXIFS","_xlfn.MINIFS":"MINIFS","_xlfn.MODE.MULT":"MODE.MULT","_xlfn.MODE.SNGL":"MODE.SNGL","_xlfn.MUNIT":"MUNIT","_xlfn.NEGBINOM.DIST":"NEGBINOM.DIST","_xlfn.NETWORKDAYS.INTL":"NETWORKDAYS.INTL","_xlfn.NIGBINOM":"NIGBINOM","_xlfn.NORM.DIST":"NORM.DIST","_xlfn.NORM.INV":"NORM.INV","_xlfn.NORM.S.DIST":"NORM.S.DIST","_xlfn.NORM.S.INV":"NORM.S.INV","_xlfn.NUMBERVALUE":"NUMBERVALUE","_xlfn.PDURATION":"PDURATION","_xlfn.PERCENTILE.EXC":"PERCENTILE.EXC","_xlfn.PERCENTILE.INC":"PERCENTILE.INC","_xlfn.PERCENTRANK.EXC":"PERCENTRANK.EXC","_xlfn.PERCENTRANK.INC":"PERCENTRANK.INC",
"_xlfn.PERMUTATIONA":"PERMUTATIONA","_xlfn.PHI":"PHI","_xlfn.POISSON.DIST":"POISSON.DIST","_xlfn.QUARTILE.EXC":"QUARTILE.EXC","_xlfn.QUARTILE.INC":"QUARTILE.INC","_xlfn.QUERYSTRING":"QUERYSTRING","_xlfn.RANK.AVG":"RANK.AVG","_xlfn.RANK.EQ":"RANK.EQ","_xlfn.RRI":"RRI","_xlfn.SEC":"SEC","_xlfn.SECH":"SECH","_xlfn.SHEET":"SHEET","_xlfn.SHEETS":"SHEETS","_xlfn.SKEW.P":"SKEW.P","_xlfn.STDEV.P":"STDEV.P","_xlfn.STDEV.S":"STDEV.S","_xlfn.SUMIFS":"SUMIFS","_xlfn.SWITCH":"SWITCH","_xlfn.T.DIST":"T.DIST","_xlfn.T.DIST.2T":"T.DIST.2T","_xlfn.T.DIST.RT":"T.DIST.RT","_xlfn.T.INV":"T.INV","_xlfn.T.INV.2T":"T.INV.2T","_xlfn.T.TEST":"T.TEST","_xlfn.TEXTJOIN":"TEXTJOIN","_xlfn.UNICHAR":"UNICHAR","_xlfn.UNICODE":"UNICODE","_xlfn.VAR.P":"VAR.P","_xlfn.VAR.S":"VAR.S","_xlfn.WEBSERVICE":"WEBSERVICE","_xlfn.WEIBULL.DIST":"WEIBULL.DIST","_xlfn.WORKDAY.INTL":"WORKDAY.INTL","_xlfn.XOR":"XOR","_xlfn.Z.TEST":"Z.TEST"};function sh(e){if(e.slice(0,3)=="of:")e=e.slice(3);if(e.charCodeAt(0)==61){e=e.slice(1);if(e.charCodeAt(0)==61)e=e.slice(1)}e=e.replace(/COM\.MICROSOFT\./g,"");e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,r){return r.replace(/\./g,"")});e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1");return e.replace(/[;~]/g,",").replace(/\|/g,";")}function fh(e){var r="of:="+e.replace(fc,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return r.replace(/;/g,"|").replace(/,/g,";")}function lh(e){var r=e.split(":");var t=r[0].split(".")[0];return[t,r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}function oh(e){return e.replace(/\./,"!")}var ch={};var uh={};Ua.WS=["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"];var hh=typeof Map!=="undefined";function dh(e,r,t){var a=0,n=e.length;if(t){if(hh?t.has(r):Object.prototype.hasOwnProperty.call(t,r)){var i=hh?t.get(r):t[r];for(;a<i.length;++a){if(e[i[a]].t===r){e.Count++;return i[a]}}}}else for(;a<n;++a){if(e[a].t===r){e.Count++;return a}}e[n]={t:r};e.Count++;e.Unique++;if(t){if(hh){if(!t.has(r))t.set(r,[]);t.get(r).push(n)}else{if(!Object.prototype.hasOwnProperty.call(t,r))t[r]=[];t[r].push(n)}}return n}function vh(e,r){var t={min:e+1,max:e+1};var a=-1;if(r.MDW)ml=r.MDW;if(r.width!=null)t.customWidth=1;else if(r.wpx!=null)a=El(r.wpx);else if(r.wch!=null)a=r.wch;if(a>-1){t.width=wl(a);t.customWidth=1}else if(r.width!=null)t.width=r.width;if(r.hidden)t.hidden=true;if(r.level!=null){t.outlineLevel=t.level=r.level}return t}function ph(e,r){if(!e)return;var t=[.7,.7,.75,.75,.3,.3];if(r=="xlml")t=[1,1,1,1,.5,.5];if(e.left==null)e.left=t[0];if(e.right==null)e.right=t[1];if(e.top==null)e.top=t[2];if(e.bottom==null)e.bottom=t[3];if(e.header==null)e.header=t[4];if(e.footer==null)e.footer=t[5]}function bh(e,r,t){var a=t.revssf[r.z!=null?r.z:"General"];var n=60,i=e.length;if(a==null&&t.ssf){for(;n<392;++n)if(t.ssf[n]==null){D.load(r.z,n);t.ssf[n]=r.z;t.revssf[r.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1};return i}function mh(e,r,t,a,n,i){try{if(a.cellNF)e.z=D._table[r]}catch(s){if(a.WTF)throw s}if(e.t==="z"&&!a.cellStyles)return;if(e.t==="d"&&typeof e.v==="string")e.v=le(e.v);if((!a||a.cellText!==false)&&e.t!=="z")try{if(D._table[r]==null)D.load(N[r]||"General",r);if(e.t==="e")e.w=e.w||ya[e.v];else if(r===0){if(e.t==="n"){if((e.v|0)===e.v)e.w=D._general_int(e.v);else e.w=D._general_num(e.v)}else if(e.t==="d"){var f=ee(e.v);if((f|0)===f)e.w=D._general_int(f);else e.w=D._general_num(f)}else if(e.v===undefined)return"";else e.w=D._general(e.v,uh)}else if(e.t==="d")e.w=D.format(r,ee(e.v),uh);else e.w=D.format(r,e.v,uh)}catch(s){if(a.WTF)throw s}if(!a.cellStyles)return;if(t!=null)try{e.s=i.Fills[t];if(e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb){e.s.fgColor.rgb=dl(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0);if(a.WTF)e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb}if(e.s.bgColor&&e.s.bgColor.theme){e.s.bgColor.rgb=dl(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0);if(a.WTF)e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb}}catch(s){if(a.WTF&&i.Fills)throw s}}function gh(e,r,t){if(e&&e["!ref"]){var a=wt(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+t+"): "+e["!ref"])}}function Eh(e,r){var t=wt(r);if(t.s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0)e["!ref"]=Et(t)}var wh=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g;var kh=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/;var Sh=/<(?:\w:)?hyperlink [^>]*>/gm;var Bh=/"(\w*:\w*)"/;var Ch=/<(?:\w:)?col\b[^>]*[\/]?>/g;var Th=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g;var _h=/<(?:\w:)?pageMargins[^>]*\/>/g;var xh=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/;var Ah=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/;var yh=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Ih(e,r,t,a,n,i,s){if(!e)return e;if(!a)a={"!id":{}};if(b!=null&&r.dense==null)r.dense=b;var f=r.dense?[]:{};var l={s:{r:2e6,c:2e6},e:{r:0,c:0}};var o="",c="";var u=e.match(kh);if(u){o=e.slice(0,u.index);c=e.slice(u.index+u[0].length)}else o=c=e;var h=o.match(xh);if(h)Fh(h[0],f,n,t);else if(h=o.match(Ah))Dh(h[0],h[1]||"",f,n,t,s,i);var d=(o.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var v=o.slice(d,d+50).match(Bh);if(v)Eh(f,v[1])}var p=o.match(yh);if(p&&p[1])jh(p[1],n);var m=[];if(r.cellStyles){var g=o.match(Ch);if(g)Wh(m,g)}if(u)Yh(u[1],f,r,l,i,s);var E=c.match(Th);if(E)f["!autofilter"]=Xh(E[0]);var w=[];var k=c.match(wh);if(k)for(d=0;d!=k.length;++d)w[d]=wt(k[d].slice(k[d].indexOf('"')+1));var S=c.match(Sh);if(S)Lh(f,S,a);var B=c.match(_h);if(B)f["!margins"]=Uh(Oe(B[0]));if(!f["!ref"]&&l.e.c>=l.s.c&&l.e.r>=l.s.r)f["!ref"]=Et(l);if(r.sheetRows>0&&f["!ref"]){var C=wt(f["!ref"]);if(r.sheetRows<=+C.e.r){C.e.r=r.sheetRows-1;if(C.e.r>l.e.r)C.e.r=l.e.r;if(C.e.r<C.s.r)C.s.r=C.e.r;if(C.e.c>l.e.c)C.e.c=l.e.c;if(C.e.c<C.s.c)C.s.c=C.e.c;f["!fullref"]=f["!ref"];f["!ref"]=Et(C)}}if(m.length>0)f["!cols"]=m;if(w.length>0)f["!merges"]=w;return f}function Rh(e){if(e.length===0)return"";var r='<mergeCells count="'+e.length+'">';for(var t=0;t!=e.length;++t)r+='<mergeCell ref="'+Et(e[t])+'"/>';return r+"</mergeCells>"}function Fh(e,r,t,a){var n=Oe(e);if(!t.Sheets[a])t.Sheets[a]={};if(n.codeName)t.Sheets[a].CodeName=Le(Ye(n.codeName))}function Dh(e,r,t,a,n,i,s){Fh(e.slice(0,e.indexOf(">")),t,a,n)}function Oh(e,r,t,a,n){var i=false;var s={},f=null;if(a.bookType!=="xlsx"&&r.vbaraw){var l=r.SheetNames[t];try{if(r.Workbook)l=r.Workbook.Sheets[t].CodeName||l}catch(o){}i=true;s.codeName=Qe(We(l))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};if(e["!outline"].above)c.summaryBelow=0;if(e["!outline"].left)c.summaryRight=0;f=(f||"")+or("outlinePr",null,c)}if(!i&&!f)return;n[n.length]=or("sheetPr",f,s)}var Ph=["objects","scenarios","selectLockedCells","selectUnlockedCells"];var Nh=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Mh(e){var r={sheet:1};Ph.forEach(function(t){if(e[t]!=null&&e[t])r[t]="1"});Nh.forEach(function(t){if(e[t]!=null&&!e[t])r[t]="0"});if(e.password)r.password=rl(e.password).toString(16).toUpperCase();return or("sheetProtection",null,r)}function Lh(e,r,t){var a=Array.isArray(e);for(var n=0;n!=r.length;++n){var i=Oe(Ye(r[n]),true);if(!i.ref)return;var s=((t||{})["!id"]||[])[i.id];if(s){i.Target=s.Target;if(i.location)i.Target+="#"+Le(i.location)}else{i.Target="#"+Le(i.location);s={Target:i.Target,TargetMode:"Internal"}}i.Rel=s;if(i.tooltip){i.Tooltip=i.tooltip;delete i.tooltip}var f=wt(i.ref);for(var l=f.s.r;l<=f.e.r;++l)for(var o=f.s.c;o<=f.e.c;++o){var c=mt({c:o,r:l});if(a){if(!e[l])e[l]=[];if(!e[l][o])e[l][o]={t:"z",v:undefined};e[l][o].l=i}else{if(!e[c])e[c]={t:"z",v:undefined};e[c].l=i}}}}function Uh(e){var r={};["left","right","top","bottom","header","footer"].forEach(function(t){if(e[t])r[t]=parseFloat(e[t])});return r}function Hh(e){ph(e);return or("pageMargins",null,e)}function Wh(e,r){var t=false;for(var a=0;a!=r.length;++a){var n=Oe(r[a],true);if(n.hidden)n.hidden=Ke(n.hidden);var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;if(n.outlineLevel)n.level=+n.outlineLevel||0;delete n.min;delete n.max;n.width=+n.width;if(!t&&n.width){t=true;Sl(n.width)}Bl(n);while(i<=s)e[i++]=ce(n)}}function Vh(e,r){var t=["<cols>"],a;for(var n=0;n!=r.length;++n){if(!(a=r[n]))continue;t[t.length]=or("col",null,vh(n,a))}t[t.length]="</cols>";return t.join("")}function Xh(e){var r={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return r}function Gh(e,r,t,a){var n=typeof e.ref=="string"?e.ref:Et(e.ref);if(!t.Workbook)t.Workbook={Sheets:[]};if(!t.Workbook.Names)t.Workbook.Names=[];var i=t.Workbook.Names;var s=gt(n);if(s.s.r==s.e.r){s.e.r=gt(r["!ref"]).e.r;n=Et(s)}for(var f=0;f<i.length;++f){var l=i[f];if(l.Name!="_xlnm._FilterDatabase")continue;if(l.Sheet!=a)continue;l.Ref="'"+t.SheetNames[a]+"'!"+n;break}if(f==i.length)i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+n});return or("autoFilter",null,{ref:n})}var zh=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function jh(e,r){if(!r.Views)r.Views=[{}];(e.match(zh)||[]).forEach(function(e,t){var a=Oe(e);if(!r.Views[t])r.Views[t]={};if(+a.zoomScale)r.Views[t].zoom=+a.zoomScale;if(Ke(a.rightToLeft))r.Views[t].RTL=true})}function $h(e,r,t,a){var n={workbookViewId:"0"};if((((a||{}).Workbook||{}).Views||[])[0])n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0";return or("sheetViews",or("sheetView",null,n),{})}function Kh(e,r,t,a){if(e.v===undefined&&typeof e.f!=="string"||e.t==="z")return"";var n="";var i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=ya[e.v];break;case"d":if(a&&a.cellDates)n=le(e.v,-1).toISOString();else{e=ce(e);e.t="n";n=""+(e.v=ee(le(e.v)))}if(typeof e.z==="undefined")e.z=D._table[14];break;default:n=e.v;break;}var f=fr("v",We(n)),l={r:r};var o=bh(a.cellXfs,e,a);if(o!==0)l.s=o;switch(e.t){case"n":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){f=fr("v",""+dh(a.Strings,e.v,a.revStrings));l.t="s";break}l.t="str";break;}if(e.t!=i){e.t=i;e.v=s}if(typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,r.length)==r?{t:"array",ref:e.F}:null;f=or("f",We(e.f),c)+(e.v!=null?f:"")}if(e.l)t["!links"].push([r,e.l]);if(e.c)t["!comments"].push([r,e.c]);return or("c",f,l)}var Yh=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/;var t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/;var n=/ref=["']([^"']*)["']/;var i=er("v"),s=er("f");return function f(l,o,c,u,h,d){var v=0,p="",b=[],m=[],g=0,E=0,w=0,k="",S;var B,C=0,T=0;var _,x;var A=0,y=0;var I=Array.isArray(d.CellXf),R;var F=[];var O=[];var P=Array.isArray(o);var N=[],M={},L=false;var U=!!c.sheetStubs;for(var H=l.split(r),W=0,V=H.length;W!=V;++W){p=H[W].trim();var X=p.length;if(X===0)continue;var G=0;e:for(v=0;v<X;++v)switch(p[v]){case">":if(p[v-1]!="/"){++v;break e}if(c&&c.cellStyles){B=Oe(p.slice(G,v),true);C=B.r!=null?parseInt(B.r,10):C+1;T=-1;if(c.sheetRows&&c.sheetRows<C)continue;M={};L=false;if(B.ht){L=true;M.hpt=parseFloat(B.ht);M.hpx=xl(M.hpt)}if(B.hidden=="1"){L=true;M.hidden=true}if(B.outlineLevel!=null){L=true;M.level=+B.outlineLevel}if(L)N[C-1]=M}break;case"<":G=v;break;}if(G>=v)break;B=Oe(p.slice(G,v),true);C=B.r!=null?parseInt(B.r,10):C+1;T=-1;if(c.sheetRows&&c.sheetRows<C)continue;if(u.s.r>C-1)u.s.r=C-1;if(u.e.r<C-1)u.e.r=C-1;if(c&&c.cellStyles){M={};L=false;if(B.ht){L=true;M.hpt=parseFloat(B.ht);M.hpx=xl(M.hpt)}if(B.hidden=="1"){L=true;M.hidden=true}if(B.outlineLevel!=null){L=true;M.level=+B.outlineLevel}if(L)N[C-1]=M}b=p.slice(v).split(e);for(var z=0;z!=b.length;++z)if(b[z].trim().charAt(0)!="<")break;b=b.slice(z);for(v=0;v!=b.length;++v){p=b[v].trim();if(p.length===0)continue;m=p.match(t);g=v;E=0;w=0;p="<c "+(p.slice(0,1)=="<"?">":"")+p;if(m!=null&&m.length===2){g=0;k=m[1];for(E=0;E!=k.length;++E){if((w=k.charCodeAt(E)-64)<1||w>26)break;g=26*g+w}--g;T=g}else++T;for(E=0;E!=p.length;++E)if(p.charCodeAt(E)===62)break;++E;B=Oe(p.slice(0,E),true);if(!B.r)B.r=mt({r:C-1,c:T});k=p.slice(E);S={t:""};if((m=k.match(i))!=null&&m[1]!=="")S.v=Le(m[1]);if(c.cellFormula){if((m=k.match(s))!=null&&m[1]!==""){S.f=Le(Ye(m[1])).replace(/\r\n/g,"\n");if(!c.xlfn)S.f=hc(S.f);if(m[0].indexOf('t="array"')>-1){S.F=(k.match(n)||[])[1];if(S.F.indexOf(":")>-1)F.push([wt(S.F),S.F])}else if(m[0].indexOf('t="shared"')>-1){x=Oe(m[0]);var j=Le(Ye(m[1]));if(!c.xlfn)j=hc(j);O[parseInt(x.si,10)]=[x,j,B.r]}}else if(m=k.match(/<f[^>]*\/>/)){x=Oe(m[0]);if(O[x.si])S.f=cc(O[x.si][1],O[x.si][2],B.r)}var $=bt(B.r);for(E=0;E<F.length;++E)if($.r>=F[E][0].s.r&&$.r<=F[E][0].e.r)if($.c>=F[E][0].s.c&&$.c<=F[E][0].e.c)S.F=F[E][1]}if(B.t==null&&S.v===undefined){if(S.f||S.F){S.v=0;S.t="n"}else if(!U)continue;else S.t="z"}else S.t=B.t||"n";if(u.s.c>T)u.s.c=T;if(u.e.c<T)u.e.c=T;switch(S.t){case"n":if(S.v==""||S.v==null){if(!U)continue;S.t="z"}else S.v=parseFloat(S.v);break;case"s":if(typeof S.v=="undefined"){if(!U)continue;S.t="z"}else{_=ch[parseInt(S.v,10)];S.v=_.t;S.r=_.r;if(c.cellHTML)S.h=_.h}break;case"str":S.t="s";S.v=S.v!=null?Ye(S.v):"";if(c.cellHTML)S.h=Ge(S.v);break;case"inlineStr":m=k.match(a);S.t="s";if(m!=null&&(_=xf(m[1]))){S.v=_.t;if(c.cellHTML)S.h=_.h}else S.v="";break;case"b":S.v=Ke(S.v);break;case"d":if(c.cellDates)S.v=le(S.v,1);else{S.v=ee(le(S.v,1));S.t="n"}break;case"e":if(!c||c.cellText!==false)S.w=S.v;S.v=Ia[S.v];break;}A=y=0;R=null;if(I&&B.s!==undefined){R=d.CellXf[B.s];if(R!=null){if(R.numFmtId!=null)A=R.numFmtId;if(c.cellStyles){if(R.fillId!=null)y=R.fillId}}}mh(S,A,y,c,h,d);if(c.cellDates&&I&&S.t=="n"&&D.is_date(D._table[A])){S.t="d";S.v=ne(S.v)}if(P){var K=bt(B.r);if(!o[K.r])o[K.r]=[];o[K.r][K.c]=S}else o[B.r]=S}}if(N.length>0)o["!rows"]=N}}();function Qh(e,r,t,a){var n=[],i=[],s=wt(e["!ref"]),f="",l,o="",c=[],u=0,h=0,d=e["!rows"];var v=Array.isArray(e);var p={r:o},b,m=-1;for(h=s.s.c;h<=s.e.c;++h)c[h]=ht(h);for(u=s.s.r;u<=s.e.r;++u){i=[];o=lt(u);for(h=s.s.c;h<=s.e.c;++h){l=c[h]+o;var g=v?(e[u]||[])[h]:e[l];if(g===undefined)continue;if((f=Kh(g,l,e,r,t,a))!=null)i.push(f)}if(i.length>0||d&&d[u]){p={r:o};if(d&&d[u]){b=d[u];if(b.hidden)p.hidden=1;m=-1;if(b.hpx)m=_l(b.hpx);else if(b.hpt)m=b.hpt;if(m>-1){p.ht=m;p.customHeight=1}if(b.level){p.outlineLevel=b.level}}n[n.length]=or("row",i.join(""),p)}}if(d)for(;u<d.length;++u){if(d&&d[u]){p={r:u+1};b=d[u];if(b.hidden)p.hidden=1;m=-1;if(b.hpx)m=_l(b.hpx);else if(b.hpt)m=b.hpt;if(m>-1){p.ht=m;p.customHeight=1}if(b.level){p.outlineLevel=b.level}n[n.length]=or("row","",p)}}return n.join("")}var Jh=or("worksheet",null,{xmlns:hr.main[0],"xmlns:r":hr.r});function qh(e,r,t,a){var n=[ye,Jh];var i=t.SheetNames[e],s=0,f="";var l=t.Sheets[i];if(l==null)l={};var o=l["!ref"]||"A1";var c=wt(o);if(c.e.c>16383||c.e.r>1048575){if(r.WTF)throw new Error("Range "+o+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383);c.e.r=Math.min(c.e.c,1048575);o=Et(c)}if(!a)a={};l["!comments"]=[];var u=[];Oh(l,t,e,r,n);n[n.length]=or("dimension",null,{ref:o});n[n.length]=$h(l,r,e,t);if(r.sheetFormat)n[n.length]=or("sheetFormatPr",null,{defaultRowHeight:r.sheetFormat.defaultRowHeight||"16",baseColWidth:r.sheetFormat.baseColWidth||"10",outlineLevelRow:r.sheetFormat.outlineLevelRow||"7"});if(l["!cols"]!=null&&l["!cols"].length>0)n[n.length]=Vh(l,l["!cols"]);n[s=n.length]="<sheetData/>";l["!links"]=[];if(l["!ref"]!=null){f=Qh(l,r,e,t,a);if(f.length>0)n[n.length]=f}if(n.length>s+1){n[n.length]="</sheetData>";n[s]=n[s].replace("/>",">")}if(l["!protect"])n[n.length]=Mh(l["!protect"]);if(l["!autofilter"]!=null)n[n.length]=Gh(l["!autofilter"],l,t,e);if(l["!merges"]!=null&&l["!merges"].length>0)n[n.length]=Rh(l["!merges"]);var h=-1,d,v=-1;if(l["!links"].length>0){n[n.length]="<hyperlinks>";l["!links"].forEach(function(e){if(!e[1].Target)return;d={ref:e[0]};if(e[1].Target.charAt(0)!="#"){v=za(a,-1,We(e[1].Target).replace(/#.*$/,""),Ua.HLINK);d["r:id"]="rId"+v}if((h=e[1].Target.indexOf("#"))>-1)d.location=We(e[1].Target.slice(h+1));if(e[1].Tooltip)d.tooltip=We(e[1].Tooltip);n[n.length]=or("hyperlink",null,d)});n[n.length]="</hyperlinks>"}delete l["!links"];if(l["!margins"]!=null)n[n.length]=Hh(l["!margins"]);if(!r||r.ignoreEC||r.ignoreEC==void 0)n[n.length]=fr("ignoredErrors",or("ignoredError",null,{numberStoredAsText:1,sqref:o}));if(u.length>0){v=za(a,-1,"../drawings/drawing"+(e+1)+".xml",Ua.DRAW);n[n.length]=or("drawing",null,{"r:id":"rId"+v});l["!drawing"]=u}if(l["!comments"].length>0){v=za(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Ua.VML);n[n.length]=or("legacyDrawing",null,{"r:id":"rId"+v});l["!legacy"]=v}if(n.length>1){n[n.length]="</worksheet>";n[1]=n[1].replace("/>",">")}return n.join("")}function Zh(e,r){var t={};var a=e.l+r;t.r=e._R(4);e.l+=4;var n=e._R(2);e.l+=1;var i=e._R(1);e.l=a;if(i&7)t.level=i&7;if(i&16)t.hidden=true;if(i&32)t.hpt=n/20;return t}function ed(e,r,t){var a=Jr(17+8*16);var n=(t["!rows"]||[])[e]||{};a._W(4,e);a._W(4,0);var i=320;if(n.hpx)i=_l(n.hpx)*20;else if(n.hpt)i=n.hpt*20;a._W(2,i);a._W(1,0);var s=0;if(n.level)s|=n.level;if(n.hidden)s|=16;if(n.hpx||n.hpt)s|=32;a._W(1,s);a._W(1,0);var f=0,l=a.l;a.l+=4;var o={r:e,c:0};for(var c=0;c<16;++c){if(r.s.c>c+1<<10||r.e.c<c<<10)continue;var u=-1,h=-1;for(var d=c<<10;d<c+1<<10;++d){o.c=d;var v=Array.isArray(t)?(t[o.r]||[])[o.c]:t[mt(o)];if(v){if(u<0)u=d;h=d}}if(u<0)continue;++f;a._W(4,u);a._W(4,h)}var p=a.l;a.l=l;a._W(4,f);a.l=p;return a.length>a.l?a.slice(0,a.l):a}function rd(e,r,t,a){var n=ed(a,t,r);if(n.length>17||(r["!rows"]||[])[a])et(e,"BrtRowHdr",n)}var td=Qt;var ad=Jt;function nd(){}function id(e,r){var t={};var a=e[e.l];++e.l;t.above=!(a&64);t.left=!(a&128);e.l+=18;t.name=Ut(e,r-19);return t}function sd(e,r,t){if(t==null)t=Jr(84+4*e.length);var a=192;if(r){if(r.above)a&=~64;if(r.left)a&=~128}t._W(1,a);for(var n=1;n<3;++n)t._W(1,0);ra({auto:1},t);t._W(-4,-1);t._W(-4,-1);Ht(e,t);return t.slice(0,t.l)}function fd(e){var r=Pt(e);return[r]}function ld(e,r,t){if(t==null)t=Jr(8);return Nt(r,t)}function od(e){var r=Mt(e);return[r]}function cd(e,r,t){if(t==null)t=Jr(4);return Lt(r,t)}function ud(e){var r=Pt(e);var t=e._R(1);return[r,t,"b"]}function hd(e,r,t){if(t==null)t=Jr(9);Nt(r,t);t._W(1,e.v?1:0);return t}function dd(e){var r=Mt(e);var t=e._R(1);return[r,t,"b"]}function vd(e,r,t){if(t==null)t=Jr(5);Lt(r,t);t._W(1,e.v?1:0);return t}function pd(e){var r=Pt(e);var t=e._R(1);return[r,t,"e"]}function bd(e,r,t){if(t==null)t=Jr(9);Nt(r,t);t._W(1,e.v);return t}function md(e){var r=Mt(e);var t=e._R(1);return[r,t,"e"]}function gd(e,r,t){if(t==null)t=Jr(8);Lt(r,t);t._W(1,e.v);t._W(2,0);t._W(1,0);return t}function Ed(e){var r=Pt(e);var t=e._R(4);return[r,t,"s"]}function wd(e,r,t){if(t==null)t=Jr(12);Nt(r,t);t._W(4,r.v);return t}function kd(e){var r=Mt(e);var t=e._R(4);return[r,t,"s"]}function Sd(e,r,t){if(t==null)t=Jr(8);Lt(r,t);t._W(4,r.v);return t}function Bd(e){var r=Pt(e);var t=qt(e);return[r,t,"n"]}function Cd(e,r,t){if(t==null)t=Jr(16);Nt(r,t);Zt(e.v,t);return t}function Td(e){var r=Mt(e);var t=qt(e);return[r,t,"n"]}function _d(e,r,t){if(t==null)t=Jr(12);Lt(r,t);Zt(e.v,t);return t}function xd(e){var r=Pt(e);var t=jt(e);return[r,t,"n"]}function Ad(e,r,t){if(t==null)t=Jr(12);Nt(r,t);$t(e.v,t);return t}function yd(e){var r=Mt(e);var t=jt(e);return[r,t,"n"]}function Id(e,r,t){if(t==null)t=Jr(8);Lt(r,t);$t(e.v,t);return t}function Rd(e){var r=Pt(e);var t=xt(e);return[r,t,"str"]}function Fd(e,r,t){if(t==null)t=Jr(12+4*e.v.length);Nt(r,t);At(e.v,t);return t.length>t.l?t.slice(0,t.l):t}function Dd(e){var r=Mt(e);var t=xt(e);return[r,t,"str"]}function Od(e,r,t){if(t==null)t=Jr(8+4*e.v.length);Lt(r,t);At(e.v,t);return t.length>t.l?t.slice(0,t.l):t}function Pd(e,r,t){var a=e.l+r;var n=Pt(e);n.r=t["!row"];var i=e._R(1);var s=[n,i,"b"];if(t.cellFormula){e.l+=2;var f=Zu(e,a-e.l,t);s[3]=Vu(f,null,n,t.supbooks,t)}else e.l=a;return s}function Nd(e,r,t){var a=e.l+r;var n=Pt(e);n.r=t["!row"];var i=e._R(1);var s=[n,i,"e"];if(t.cellFormula){e.l+=2;var f=Zu(e,a-e.l,t);s[3]=Vu(f,null,n,t.supbooks,t)}else e.l=a;return s}function Md(e,r,t){var a=e.l+r;var n=Pt(e);n.r=t["!row"];var i=qt(e);var s=[n,i,"n"];if(t.cellFormula){e.l+=2;var f=Zu(e,a-e.l,t);s[3]=Vu(f,null,n,t.supbooks,t)}else e.l=a;return s}function Ld(e,r,t){var a=e.l+r;var n=Pt(e);n.r=t["!row"];var i=xt(e);var s=[n,i,"str"];if(t.cellFormula){e.l+=2;var f=Zu(e,a-e.l,t);s[3]=Vu(f,null,n,t.supbooks,t)}else e.l=a;return s}var Ud=Qt;var Hd=Jt;function Wd(e,r){if(r==null)r=Jr(4);r._W(4,e);return r}function Vd(e,r){var t=e.l+r;var a=Qt(e,16);var n=Wt(e);var i=xt(e);var s=xt(e);var f=xt(e);e.l=t;var l={rfx:a,relId:n,loc:i,display:f};if(s)l.Tooltip=s;return l}function Xd(e,r){var t=Jr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Jt({s:bt(e[0]),e:bt(e[0])},t);zt("rId"+r,t);var a=e[1].Target.indexOf("#");var n=a==-1?"":e[1].Target.slice(a+1);At(n||"",t);At(e[1].Tooltip||"",t);At("",t);return t.slice(0,t.l)}function Gd(){}function zd(e,r,t){var a=e.l+r;var n=Kt(e,16);var i=e._R(1);var s=[n];s[2]=i;if(t.cellFormula){var f=qu(e,a-e.l,t);s[1]=f}else e.l=a;return s}function jd(e,r,t){var a=e.l+r;var n=Qt(e,16);var i=[n];if(t.cellFormula){var s=rh(e,a-e.l,t);i[1]=s;e.l=a}else e.l=a;return i}function $d(e,r,t){if(t==null)t=Jr(18);var a=vh(e,r);t._W(-4,e);t._W(-4,e);t._W(4,(a.width||10)*256);t._W(4,0);var n=0;if(r.hidden)n|=1;if(typeof a.width=="number")n|=2;if(r.level)n|=r.level<<8;t._W(2,n);return t}var Kd=["left","right","top","bottom","header","footer"];function Yd(e){var r={};Kd.forEach(function(t){r[t]=qt(e,8)});return r}function Qd(e,r){if(r==null)r=Jr(6*8);ph(e);Kd.forEach(function(t){Zt(e[t],r)});return r}function Jd(e){var r=e._R(2);e.l+=28;return{RTL:r&32}}function qd(e,r,t){if(t==null)t=Jr(30);var a=924;if((((r||{}).Views||[])[0]||{}).RTL)a|=32;t._W(2,a);t._W(4,0);t._W(4,0);t._W(4,0);t._W(1,0);t._W(1,0);t._W(2,0);t._W(2,100);t._W(2,0);t._W(2,0);t._W(2,0);t._W(4,0);return t}function Zd(e){var r=Jr(24);r._W(4,4);r._W(4,1);Jt(e,r);return r}function ev(e,r){if(r==null)r=Jr(16*4+2);r._W(2,e.password?rl(e.password):0);r._W(4,1);[["objects",false],["scenarios",false],["formatCells",true],["formatColumns",true],["formatRows",true],["insertColumns",true],["insertRows",true],["insertHyperlinks",true],["deleteColumns",true],["deleteRows",true],["selectLockedCells",false],["sort",true],["autoFilter",true],["pivotTables",true],["selectUnlockedCells",false]].forEach(function(t){if(t[1])r._W(4,e[t[0]]!=null&&!e[t[0]]?1:0);else r._W(4,e[t[0]]!=null&&e[t[0]]?0:1)});return r}function rv(){}function tv(){}function av(e,r,t,a,n,i,s){if(!e)return e;var f=r||{};if(!a)a={"!id":{}};if(b!=null&&f.dense==null)f.dense=b;var l=f.dense?[]:{};var o;var c={s:{r:2e6,c:2e6},e:{r:0,c:0}};var u=[];var h=false,d=false;var v,p,m,g,E,w,k,S,B;var C=[];f.biff=12;f["!row"]=0;var T=0,_=false;var x=[];var A={};var y=f.supbooks||n.supbooks||[[]];y.sharedf=A;y.arrayf=x;y.SheetNames=n.SheetNames||n.Sheets.map(function(e){return e.name});if(!f.supbooks){f.supbooks=y;if(n.Names)for(var I=0;I<n.Names.length;++I)y[0][I+1]=n.Names[I]}var R=[],F=[];var O=false;qp[16]={n:"BrtShortReal",f:Td};qr(e,function N(e,r,b){if(d)return;switch(b){case 148:o=e;break;case 0:v=e;if(f.sheetRows&&f.sheetRows<=v.r)d=true;S=lt(g=v.r);f["!row"]=v.r;if(e.hidden||e.hpt||e.level!=null){if(e.hpt)e.hpx=xl(e.hpt);F[e.r]=e}break;case 2:;case 3:;case 4:;case 5:;case 6:;case 7:;case 8:;case 9:;case 10:;case 11:;case 13:;case 14:;case 15:;case 16:;case 17:;case 18:p={t:e[2]};switch(e[2]){case"n":p.v=e[1];break;case"s":k=ch[e[1]];p.v=k.t;p.r=k.r;break;case"b":p.v=e[1]?true:false;break;case"e":p.v=e[1];if(f.cellText!==false)p.w=ya[p.v];break;case"str":p.t="s";p.v=e[1];break;}if(m=s.CellXf[e[0].iStyleRef])mh(p,m.numFmtId,null,f,i,s);E=e[0].c==-1?E+1:e[0].c;if(f.dense){if(!l[g])l[g]=[];l[g][E]=p}else l[ht(E)+S]=p;if(f.cellFormula){_=false;for(T=0;T<x.length;++T){var I=x[T];if(v.r>=I[0].s.r&&v.r<=I[0].e.r)if(E>=I[0].s.c&&E<=I[0].e.c){p.F=Et(I[0]);_=true}}if(!_&&e.length>3)p.f=e[3]}if(c.s.r>v.r)c.s.r=v.r;if(c.s.c>E)c.s.c=E;if(c.e.r<v.r)c.e.r=v.r;if(c.e.c<E)c.e.c=E;if(f.cellDates&&m&&p.t=="n"&&D.is_date(D._table[m.numFmtId])){var P=D.parse_date_code(p.v);if(P){p.t="d";p.v=new Date(P.y,P.m-1,P.d,P.H,P.M,P.S,P.u)}}break;case 1:;case 12:if(!f.sheetStubs||h)break;p={t:"z",v:undefined};E=e[0].c==-1?E+1:e[0].c;if(f.dense){if(!l[g])l[g]=[];l[g][E]=p}else l[ht(E)+S]=p;if(c.s.r>v.r)c.s.r=v.r;if(c.s.c>E)c.s.c=E;if(c.e.r<v.r)c.e.r=v.r;if(c.e.c<E)c.e.c=E;break;case 176:C.push(e);break;case 494:var N=a["!id"][e.relId];if(N){e.Target=N.Target;if(e.loc)e.Target+="#"+e.loc;e.Rel=N}else if(e.relId==""){e.Target="#"+e.loc}for(g=e.rfx.s.r;g<=e.rfx.e.r;++g)for(E=e.rfx.s.c;E<=e.rfx.e.c;++E){if(f.dense){if(!l[g])l[g]=[];if(!l[g][E])l[g][E]={t:"z",v:undefined};l[g][E].l=e}else{w=mt({c:E,r:g});if(!l[w])l[w]={t:"z",v:undefined};l[w].l=e}}break;case 426:if(!f.cellFormula)break;x.push(e);B=f.dense?l[g][E]:l[ht(E)+S];B.f=Vu(e[1],c,{r:v.r,c:E},y,f);B.F=Et(e[0]);break;case 427:if(!f.cellFormula)break;A[mt(e[0].s)]=e[1];B=f.dense?l[g][E]:l[ht(E)+S];B.f=Vu(e[1],c,{r:v.r,c:E},y,f);break;case 60:if(!f.cellStyles)break;while(e.e>=e.s){R[e.e--]={width:e.w/256,hidden:!!(e.flags&1),level:e.level};if(!O){O=true;Sl(e.w/256)}Bl(R[e.e+1])}break;case 161:l["!autofilter"]={ref:Et(e)};break;case 476:l["!margins"]=e;break;case 147:if(!n.Sheets[t])n.Sheets[t]={};if(e.name)n.Sheets[t].CodeName=e.name;if(e.above||e.left)l["!outline"]={above:e.above,left:e.left};break;case 137:if(!n.Views)n.Views=[{}];if(!n.Views[0])n.Views[0]={};if(e.RTL)n.Views[0].RTL=true;break;case 485:break;case 64:;case 1053:break;case 151:break;case 152:;case 175:;case 644:;case 625:;case 562:;case 396:;case 1112:;case 1146:;case 471:;case 1050:;case 649:;case 1105:;case 49:;case 589:;case 607:;case 564:;case 1055:;case 168:;case 174:;case 1180:;case 499:;case 507:;case 550:;case 171:;case 167:;case 1177:;case 169:;case 1181:;case 551:;case 552:;case 661:;case 639:;case 478:;case 537:;case 477:;case 536:;case 1103:;case 680:;case 1104:;case 1024:;case 663:;case 535:;case 678:;case 504:;case 1043:;case 428:;case 170:;case 3072:;case 50:;case 2070:;case 1045:break;case 35:h=true;break;case 36:h=false;break;case 37:u.push(r);h=true;break;case 38:u.pop();h=false;break;default:if((r||"").indexOf("Begin")>0){}else if((r||"").indexOf("End")>0){}else if(!h||f.WTF)throw new Error("Unexpected record "+b+" "+r);}},f);delete f.supbooks;delete f["!row"];if(!l["!ref"]&&(c.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0)))l["!ref"]=Et(o||c);if(f.sheetRows&&l["!ref"]){var P=wt(l["!ref"]);if(f.sheetRows<=+P.e.r){P.e.r=f.sheetRows-1;if(P.e.r>c.e.r)P.e.r=c.e.r;if(P.e.r<P.s.r)P.s.r=P.e.r;if(P.e.c>c.e.c)P.e.c=c.e.c;if(P.e.c<P.s.c)P.s.c=P.e.c;l["!fullref"]=l["!ref"];l["!ref"]=Et(P)}}if(C.length>0)l["!merges"]=C;if(R.length>0)l["!cols"]=R;if(F.length>0)l["!rows"]=F;return l}function nv(e,r,t,a,n,i,s){if(r.v===undefined)return false;var f="";switch(r.t){case"b":f=r.v?"1":"0";break;case"d":r=ce(r);r.z=r.z||D._table[14];r.v=ee(le(r.v));r.t="n";break;case"n":;case"e":f=""+r.v;break;default:f=r.v;break;}var l={r:t,c:a};l.s=bh(n.cellXfs,r,n);if(r.l)i["!links"].push([mt(l),r.l]);if(r.c)i["!comments"].push([mt(l),r.c]);switch(r.t){case"s":;case"str":if(n.bookSST){f=dh(n.Strings,r.v,n.revStrings);l.t="s";l.v=f;if(s)et(e,"BrtShortIsst",Sd(r,l));else et(e,"BrtCellIsst",wd(r,l))}else{l.t="str";if(s)et(e,"BrtShortSt",Od(r,l));else et(e,"BrtCellSt",Fd(r,l))}return true;case"n":if(r.v==(r.v|0)&&r.v>-1e3&&r.v<1e3){if(s)et(e,"BrtShortRk",Id(r,l));else et(e,"BrtCellRk",Ad(r,l))}else{if(s)et(e,"BrtShortReal",_d(r,l));else et(e,"BrtCellReal",Cd(r,l))}return true;case"b":l.t="b";if(s)et(e,"BrtShortBool",vd(r,l));else et(e,"BrtCellBool",hd(r,l));return true;case"e":l.t="e";if(s)et(e,"BrtShortError",gd(r,l));else et(e,"BrtCellError",bd(r,l));return true;}if(s)et(e,"BrtShortBlank",cd(r,l));else et(e,"BrtCellBlank",ld(r,l));return true}function iv(e,r,t,a){var n=wt(r["!ref"]||"A1"),i,s="",f=[];et(e,"BrtBeginSheetData");var l=Array.isArray(r);var o=n.e.r;if(r["!rows"])o=Math.max(n.e.r,r["!rows"].length-1);for(var c=n.s.r;c<=o;++c){s=lt(c);rd(e,r,n,c);var u=false;if(c<=n.e.r)for(var h=n.s.c;h<=n.e.c;++h){if(c===n.s.r)f[h]=ht(h);i=f[h]+s;var d=l?(r[c]||[])[h]:r[i];if(!d){u=false;continue}u=nv(e,d,c,h,a,r,u)}}et(e,"BrtEndSheetData")}function sv(e,r){if(!r||!r["!merges"])return;et(e,"BrtBeginMergeCells",Wd(r["!merges"].length));r["!merges"].forEach(function(r){et(e,"BrtMergeCell",Hd(r))});et(e,"BrtEndMergeCells")}function fv(e,r){if(!r||!r["!cols"])return;et(e,"BrtBeginColInfos");r["!cols"].forEach(function(r,t){if(r)et(e,"BrtColInfo",$d(t,r))});et(e,"BrtEndColInfos")}function lv(e,r){if(!r||!r["!ref"])return;et(e,"BrtBeginCellIgnoreECs");et(e,"BrtCellIgnoreEC",Zd(wt(r["!ref"])));et(e,"BrtEndCellIgnoreECs")}function ov(e,r,t){r["!links"].forEach(function(r){if(!r[1].Target)return;var a=za(t,-1,r[1].Target.replace(/#.*$/,""),Ua.HLINK);et(e,"BrtHLink",Xd(r,a))});delete r["!links"]}function cv(e,r,t,a){if(r["!comments"].length>0){var n=za(a,-1,"../drawings/vmlDrawing"+(t+1)+".vml",Ua.VML);et(e,"BrtLegacyDrawing",zt("rId"+n));r["!legacy"]=n}}function uv(e,r,t,a){if(!r["!autofilter"])return;var n=r["!autofilter"];var i=typeof n.ref==="string"?n.ref:Et(n.ref);if(!t.Workbook)t.Workbook={Sheets:[]};if(!t.Workbook.Names)t.Workbook.Names=[];var s=t.Workbook.Names;var f=gt(i);if(f.s.r==f.e.r){f.e.r=gt(r["!ref"]).e.r;i=Et(f)}for(var l=0;l<s.length;++l){var o=s[l];if(o.Name!="_xlnm._FilterDatabase")continue;if(o.Sheet!=a)continue;o.Ref="'"+t.SheetNames[a]+"'!"+i;break}if(l==s.length)s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+i});et(e,"BrtBeginAFilter",Jt(wt(i)));et(e,"BrtEndAFilter")}function hv(e,r,t){et(e,"BrtBeginWsViews");{et(e,"BrtBeginWsView",qd(r,t));et(e,"BrtEndWsView")}et(e,"BrtEndWsViews")}function dv(){}function vv(e,r){if(!r["!protect"])return;et(e,"BrtSheetProtection",ev(r["!protect"]))}function pv(e,r,t,a){var n=Zr();var i=t.SheetNames[e],s=t.Sheets[i]||{};var f=i;try{if(t&&t.Workbook)f=t.Workbook.Sheets[e].CodeName||f}catch(l){}var o=wt(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(r.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383);o.e.r=Math.min(o.e.c,1048575)}s["!links"]=[];s["!comments"]=[];et(n,"BrtBeginSheet");if(t.vbaraw||s["!outline"])et(n,"BrtWsProp",sd(f,s["!outline"]));et(n,"BrtWsDim",ad(o));hv(n,s,t.Workbook);dv(n,s);fv(n,s,e,r,t);iv(n,s,e,r,t);vv(n,s);uv(n,s,t,e);sv(n,s);ov(n,s,a);if(s["!margins"])et(n,"BrtMargins",Qd(s["!margins"]));if(!r||r.ignoreEC||r.ignoreEC==void 0)lv(n,s);cv(n,s,e,a);et(n,"BrtEndSheet");return n.end()}Ua.CHART="http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart";Ua.CHARTEX="http://schemas.microsoft.com/office/2014/relationships/chartEx";function bv(e){var r=[];var t=e.match(/^<c:numCache>/);var a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach(function(e){var a=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);if(!a)return;r[+a[1]]=t?+a[2]:a[2]});var n=Le((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach(function(e){a=e.replace(/<.*?>/g,"")});return[r,n,a]}function mv(e,r,t,a,n,i){
var s=i||{"!type":"chart"};if(!e)return i;var f=0,l=0,o="A";var c={s:{r:2e6,c:2e6},e:{r:0,c:0}};(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var r=bv(e);c.s.r=c.s.c=0;c.e.c=f;o=ht(f);r[0].forEach(function(e,t){s[o+lt(t)]={t:"n",v:e,z:r[1]};l=t});if(c.e.r<l)c.e.r=l;++f});if(f>0)s["!ref"]=Et(c);return s}Ua.CS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet";var gv=or("chartsheet",null,{xmlns:hr.main[0],"xmlns:r":hr.r});function Ev(e,r,t,a,n){if(!e)return e;if(!a)a={"!id":{}};var i={"!type":"chart","!drawel":null,"!rel":""};var s;var f=e.match(xh);if(f)Fh(f[0],i,n,t);if(s=e.match(/drawing r:id="(.*?)"/))i["!rel"]=s[1];if(a["!id"][i["!rel"]])i["!drawel"]=a["!id"][i["!rel"]];return i}function wv(e,r,t,a){var n=[ye,gv];n[n.length]=or("drawing",null,{"r:id":"rId1"});za(a,-1,"../drawings/drawing"+(e+1)+".xml",Ua.DRAW);if(n.length>2){n[n.length]="</chartsheet>";n[1]=n[1].replace("/>",">")}return n.join("")}function kv(e,r){e.l+=10;var t=xt(e,r-10);return{name:t}}function Sv(e,r,t,a,n){if(!e)return e;if(!a)a={"!id":{}};var i={"!type":"chart","!drawel":null,"!rel":""};var s=[];var f=false;qr(e,function l(e,a,o){switch(o){case 550:i["!rel"]=e;break;case 651:if(!n.Sheets[t])n.Sheets[t]={};if(e.name)n.Sheets[t].CodeName=e.name;break;case 562:;case 652:;case 669:;case 679:;case 551:;case 552:;case 476:;case 3072:break;case 35:f=true;break;case 36:f=false;break;case 37:s.push(a);break;case 38:s.pop();break;default:if((a||"").indexOf("Begin")>0)s.push(a);else if((a||"").indexOf("End")>0)s.pop();else if(!f||r.WTF)throw new Error("Unexpected record "+o+" "+a);}},r);if(a["!id"][i["!rel"]])i["!drawel"]=a["!id"][i["!rel"]];return i}function Bv(){var e=Zr();et(e,"BrtBeginSheet");et(e,"BrtEndSheet");return e.end()}var Cv=[["allowRefreshQuery",false,"bool"],["autoCompressPictures",true,"bool"],["backupFile",false,"bool"],["checkCompatibility",false,"bool"],["CodeName",""],["date1904",false,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",false,"bool"],["hidePivotFieldList",false,"bool"],["promptedSolutions",false,"bool"],["publishItems",false,"bool"],["refreshAllConnections",false,"bool"],["saveExternalLinkValues",true,"bool"],["showBorderUnselectedTables",true,"bool"],["showInkAnnotation",true,"bool"],["showObjects","all"],["showPivotChartFilter",false,"bool"],["updateLinks","userSet"]];var Tv=[["activeTab",0,"int"],["autoFilterDateGrouping",true,"bool"],["firstSheet",0,"int"],["minimized",false,"bool"],["showHorizontalScroll",true,"bool"],["showSheetTabs",true,"bool"],["showVerticalScroll",true,"bool"],["tabRatio",600,"int"],["visibility","visible"]];var _v=[];var xv=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Av(e,r){for(var t=0;t!=e.length;++t){var a=e[t];for(var n=0;n!=r.length;++n){var i=r[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":if(typeof a[i[0]]=="string")a[i[0]]=Ke(a[i[0]]);break;case"int":if(typeof a[i[0]]=="string")a[i[0]]=parseInt(a[i[0]],10);break;}}}}function yv(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":if(typeof e[a[0]]=="string")e[a[0]]=Ke(e[a[0]]);break;case"int":if(typeof e[a[0]]=="string")e[a[0]]=parseInt(e[a[0]],10);break;}}}function Iv(e){yv(e.WBProps,Cv);yv(e.CalcPr,xv);Av(e.WBView,Tv);Av(e.Sheets,_v);uh.date1904=Ke(e.WBProps.date1904)}function Rv(e){if(!e.Workbook)return"false";if(!e.Workbook.WBProps)return"false";return Ke(e.Workbook.WBProps.date1904)?"true":"false"}var Fv="][*?/\\".split("");function Dv(e,r){if(e.length>31){if(r)return false;throw new Error("Sheet names cannot exceed 31 chars")}var t=true;Fv.forEach(function(a){if(e.indexOf(a)==-1)return;if(!r)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");t=false});return t}function Ov(e,r,t){e.forEach(function(a,n){Dv(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(t){var s=r&&r[n]&&r[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function Pv(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var r=e.Workbook&&e.Workbook.Sheets||[];Ov(e.SheetNames,r,!!e.vbaraw);for(var t=0;t<e.SheetNames.length;++t)gh(e.Sheets[e.SheetNames[t]],e.SheetNames[t],t)}var Nv=/<\w+:workbook/;function Mv(e,r){if(!e)throw new Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""};var a=false,n="xmlns";var i={},s=0;e.replace(Re,function f(l,o){var c=Oe(l);switch(Pe(c[0])){case"<?xml":break;case"<workbook":if(l.match(Nv))n="xmlns"+l.match(/<(\w+):/)[1];t.xmlns=c[n];break;case"</workbook>":break;case"<fileVersion":delete c[0];t.AppVersion=c;break;case"<fileVersion/>":;case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":;case"<workbookPr/>":Cv.forEach(function(e){if(c[e[0]]==null)return;switch(e[2]){case"bool":t.WBProps[e[0]]=Ke(c[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(c[e[0]],10);break;default:t.WBProps[e[0]]=c[e[0]];}});if(c.codeName)t.WBProps.CodeName=Ye(c.codeName);break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":;case"<bookViews>":;case"</bookViews>":break;case"<workbookView":;case"<workbookView/>":delete c[0];t.WBView.push(c);break;case"</workbookView>":break;case"<sheets":;case"<sheets>":;case"</sheets>":break;case"<sheet":switch(c.state){case"hidden":c.Hidden=1;break;case"veryHidden":c.Hidden=2;break;default:c.Hidden=0;}delete c.state;c.name=Le(Ye(c.name));delete c[0];t.Sheets.push(c);break;case"</sheet>":break;case"<functionGroups":;case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":;case"</externalReferences>":;case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":;case"<definedNames":a=true;break;case"</definedNames>":a=false;break;case"<definedName":{i={};i.Name=Ye(c.name);if(c.comment)i.Comment=c.comment;if(c.localSheetId)i.Sheet=+c.localSheetId;if(Ke(c.hidden||"0"))i.Hidden=true;s=o+l.length}break;case"</definedName>":{i.Ref=Le(Ye(e.slice(s,o)));t.Names.push(i)}break;case"<definedName/>":break;case"<calcPr":delete c[0];t.CalcPr=c;break;case"<calcPr/>":delete c[0];t.CalcPr=c;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":;case"</customWorkbookViews>":;case"<customWorkbookViews":break;case"<customWorkbookView":;case"</customWorkbookView>":break;case"<pivotCaches>":;case"</pivotCaches>":;case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":;case"<smartTagPr/>":break;case"<smartTagTypes":;case"<smartTagTypes>":;case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":;case"<webPublishing/>":break;case"<fileRecoveryPr":;case"<fileRecoveryPr/>":break;case"<webPublishObjects>":;case"<webPublishObjects":;case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;case"<ArchID":break;case"<AlternateContent":;case"<AlternateContent>":a=true;break;case"</AlternateContent>":a=false;break;case"<revisionPtr":break;default:if(!a&&r.WTF)throw new Error("unrecognized "+c[0]+" in workbook");}return l});if(hr.main.indexOf(t.xmlns)===-1)throw new Error("Unknown Namespace: "+t.xmlns);Iv(t);return t}var Lv=or("workbook",null,{xmlns:hr.main[0],"xmlns:r":hr.r});function Uv(e){var r=[ye];r[r.length]=Lv;var t=e.Workbook&&(e.Workbook.Names||[]).length>0;var a={codeName:"ThisWorkbook"};if(e.Workbook&&e.Workbook.WBProps){Cv.forEach(function(r){if(e.Workbook.WBProps[r[0]]==null)return;if(e.Workbook.WBProps[r[0]]==r[1])return;a[r[0]]=e.Workbook.WBProps[r[0]]});if(e.Workbook.WBProps.CodeName){a.codeName=e.Workbook.WBProps.CodeName;delete a.CodeName}}r[r.length]=or("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[];var i=0;if(n&&n[0]&&!!n[0].Hidden){r[r.length]="<bookViews>";for(i=0;i!=e.SheetNames.length;++i){if(!n[i])break;if(!n[i].Hidden)break}if(i==e.SheetNames.length)i=0;r[r.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>';r[r.length]="</bookViews>"}r[r.length]="<sheets>";for(i=0;i!=e.SheetNames.length;++i){var s={name:We(e.SheetNames[i].slice(0,31))};s.sheetId=""+(i+1);s["r:id"]="rId"+(i+1);if(n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break;}r[r.length]=or("sheet",null,s)}r[r.length]="</sheets>";if(t){r[r.length]="<definedNames>";if(e.Workbook&&e.Workbook.Names)e.Workbook.Names.forEach(function(e){var t={name:e.Name};if(e.Comment)t.comment=e.Comment;if(e.Sheet!=null)t.localSheetId=""+e.Sheet;if(e.Hidden)t.hidden="1";if(!e.Ref)return;r[r.length]=or("definedName",We(e.Ref),t)});r[r.length]="</definedNames>"}if(r.length>2){r[r.length]="</workbook>";r[1]=r[1].replace("/>",">")}return r.join("")}function Hv(e,r){var t={};t.Hidden=e._R(4);t.iTabID=e._R(4);t.strRelID=Gt(e,r-8);t.name=xt(e);return t}function Wv(e,r){if(!r)r=Jr(127);r._W(4,e.Hidden);r._W(4,e.iTabID);zt(e.strRelID,r);At(e.name.slice(0,31),r);return r.length>r.l?r.slice(0,r.l):r}function Vv(e,r){var t={};var a=e._R(4);t.defaultThemeVersion=e._R(4);var n=r>8?xt(e):"";if(n.length>0)t.CodeName=n;t.autoCompressPictures=!!(a&65536);t.backupFile=!!(a&64);t.checkCompatibility=!!(a&4096);t.date1904=!!(a&1);t.filterPrivacy=!!(a&8);t.hidePivotFieldList=!!(a&1024);t.promptedSolutions=!!(a&16);t.publishItems=!!(a&2048);t.refreshAllConnections=!!(a&262144);t.saveExternalLinkValues=!!(a&128);t.showBorderUnselectedTables=!!(a&4);t.showInkAnnotation=!!(a&32);t.showObjects=["all","placeholders","none"][a>>13&3];t.showPivotChartFilter=!!(a&32768);t.updateLinks=["userSet","never","always"][a>>8&3];return t}function Xv(e,r){if(!r)r=Jr(72);var t=0;if(e){if(e.filterPrivacy)t|=8}r._W(4,t);r._W(4,0);Ht(e&&e.CodeName||"ThisWorkbook",r);return r.slice(0,r.l)}function Gv(e,r){var t={};e._R(4);t.ArchID=e._R(4);e.l+=r-8;return t}function zv(e,r,t){var a=e.l+r;e.l+=4;e.l+=1;var n=e._R(4);var i=Xt(e);var s=eh(e,0,t);var f=Wt(e);e.l=a;var l={Name:i,Ptg:s};if(n<268435455)l.Sheet=n;if(f)l.Comment=f;return l}function jv(e,r){var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""};var a=[];var n=false;if(!r)r={};r.biff=12;var i=[];var s=[[]];s.SheetNames=[];s.XTI=[];qp[16]={n:"BrtFRTArchID$",f:Gv};qr(e,function f(e,l,o){switch(o){case 156:s.SheetNames.push(e.name);t.Sheets.push(e);break;case 153:t.WBProps=e;break;case 39:if(e.Sheet!=null)r.SID=e.Sheet;e.Ref=Vu(e.Ptg,null,null,s,r);delete r.SID;delete e.Ptg;i.push(e);break;case 1036:break;case 357:;case 358:;case 355:;case 667:if(!s[0].length)s[0]=[o,e];else s.push([o,e]);s[s.length-1].XTI=[];break;case 362:if(s.length===0){s[0]=[];s[0].XTI=[]}s[s.length-1].XTI=s[s.length-1].XTI.concat(e);s.XTI=s.XTI.concat(e);break;case 361:break;case 2071:;case 158:;case 143:;case 664:;case 353:break;case 3072:;case 3073:;case 534:;case 677:;case 157:;case 610:;case 2050:;case 155:;case 548:;case 676:;case 128:;case 665:;case 2128:;case 2125:;case 549:;case 2053:;case 596:;case 2076:;case 2075:;case 2082:;case 397:;case 154:;case 1117:;case 553:;case 2091:break;case 35:a.push(l);n=true;break;case 36:a.pop();n=false;break;case 37:a.push(l);n=true;break;case 38:a.pop();n=false;break;case 16:break;default:if((l||"").indexOf("Begin")>0){}else if((l||"").indexOf("End")>0){}else if(!n||r.WTF&&a[a.length-1]!="BrtACBegin"&&a[a.length-1]!="BrtFRTBegin")throw new Error("Unexpected record "+o+" "+l);}},r);Iv(t);t.Names=i;t.supbooks=s;return t}function $v(e,r){et(e,"BrtBeginBundleShs");for(var t=0;t!=r.SheetNames.length;++t){var a=r.Workbook&&r.Workbook.Sheets&&r.Workbook.Sheets[t]&&r.Workbook.Sheets[t].Hidden||0;var n={Hidden:a,iTabID:t+1,strRelID:"rId"+(t+1),name:r.SheetNames[t]};et(e,"BrtBundleSh",Wv(n))}et(e,"BrtEndBundleShs")}function Kv(r,t){if(!t)t=Jr(127);for(var a=0;a!=4;++a)t._W(4,0);At("SheetJS",t);At(e.version,t);At(e.version,t);At("7262",t);return t.length>t.l?t.slice(0,t.l):t}function Yv(e,r){if(!r)r=Jr(29);r._W(-4,0);r._W(-4,460);r._W(4,28800);r._W(4,17600);r._W(4,500);r._W(4,e);r._W(4,e);var t=120;r._W(1,t);return r.length>r.l?r.slice(0,r.l):r}function Qv(e,r){if(!r.Workbook||!r.Workbook.Sheets)return;var t=r.Workbook.Sheets;var a=0,n=-1,i=-1;for(;a<t.length;++a){if(!t[a]||!t[a].Hidden&&n==-1)n=a;else if(t[a].Hidden==1&&i==-1)i=a}if(i>n)return;et(e,"BrtBeginBookViews");et(e,"BrtBookView",Yv(n));et(e,"BrtEndBookViews")}function Jv(e,r){var t=Zr();et(t,"BrtBeginBook");et(t,"BrtFileVersion",Kv());et(t,"BrtWbProp",Xv(e.Workbook&&e.Workbook.WBProps||null));Qv(t,e,r);$v(t,e,r);et(t,"BrtEndBook");return t.end()}function qv(e,r,t){if(r.slice(-4)===".bin")return jv(e,t);return Mv(e,t)}function Zv(e,r,t,a,n,i,s,f){if(r.slice(-4)===".bin")return av(e,a,t,n,i,s,f);return Ih(e,a,t,n,i,s,f)}function ep(e,r,t,a,n,i,s,f){if(r.slice(-4)===".bin")return Sv(e,a,t,n,i,s,f);return Ev(e,a,t,n,i,s,f)}function rp(e,r,t,a,n,i,s,f){if(r.slice(-4)===".bin")return nc(e,a,t,n,i,s,f);return ic(e,a,t,n,i,s,f)}function tp(e,r,t,a,n,i,s,f){if(r.slice(-4)===".bin")return tc(e,a,t,n,i,s,f);return ac(e,a,t,n,i,s,f)}function ap(e,r,t,a){if(r.slice(-4)===".bin")return to(e,t,a);return Ll(e,t,a)}function np(e,r,t){return Co(e,t)}function ip(e,r,t){if(r.slice(-4)===".bin")return Pf(e,t);return Rf(e,t)}function sp(e,r,t){if(r.slice(-4)===".bin")return Qo(e,t);return Xo(e,t)}function fp(e,r,t){if(r.slice(-4)===".bin")return No(e,r,t);return Oo(e,r,t)}function lp(e,r,t,a){if(t.slice(-4)===".bin")return Lo(e,r,t,a);return Mo(e,r,t,a)}function op(e,r,t){return(r.slice(-4)===".bin"?Jv:Uv)(e,t)}function cp(e,r,t,a,n){return(r.slice(-4)===".bin"?pv:qh)(e,t,a,n)}function up(e,r,t,a,n){return(r.slice(-4)===".bin"?Bv:wv)(e,t,a,n)}function hp(e,r,t){return(r.slice(-4)===".bin"?vo:Hl)(e,t)}function dp(e,r,t){return(r.slice(-4)===".bin"?Lf:Df)(e,t)}function vp(e,r,t){return(r.slice(-4)===".bin"?Jo:zo)(e,t)}var pp=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g;var bp=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function mp(e,r){var t=e.split(/\s+/);var a=[];if(!r)a[0]=t[0];if(t.length===1)return a;var n=e.match(pp),i,s,f,l;if(n)for(l=0;l!=n.length;++l){i=n[l].match(bp);if((s=i[1].indexOf(":"))===-1)a[i[1]]=i[2].slice(1,i[2].length-1);else{if(i[1].slice(0,6)==="xmlns:")f="xmlns"+i[1].slice(6);else f=i[1].slice(s+1);a[f]=i[2].slice(1,i[2].length-1)}}return a}function gp(e){var r=e.split(/\s+/);var t={};if(r.length===1)return t;var a=e.match(pp),n,i,s,f;if(a)for(f=0;f!=a.length;++f){n=a[f].match(bp);if((i=n[1].indexOf(":"))===-1)t[n[1]]=n[2].slice(1,n[2].length-1);else{if(n[1].slice(0,6)==="xmlns:")s="xmlns"+n[1].slice(6);else s=n[1].slice(i+1);t[s]=n[2].slice(1,n[2].length-1)}}return t}function Ep(e,r){var t=P[e]||Le(e);if(t==="General")return D._general(r);return D.format(t,r)}function wp(e,r,t,a){var n=a;switch((t[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=Ke(a);break;case"i2":;case"int":n=parseInt(a,10);break;case"r4":;case"float":n=parseFloat(a);break;case"date":;case"dateTime.tz":n=le(a);break;case"i8":;case"string":;case"fixed":;case"uuid":;case"bin.base64":break;default:throw new Error("bad custprop:"+t[0]);}e[Le(r)]=n}function kp(e,r,t){if(e.t==="z")return;if(!t||t.cellText!==false)try{if(e.t==="e"){e.w=e.w||ya[e.v]}else if(r==="General"){if(e.t==="n"){if((e.v|0)===e.v)e.w=D._general_int(e.v);else e.w=D._general_num(e.v)}else e.w=D._general(e.v)}else e.w=Ep(r||"General",e.v)}catch(a){if(t.WTF)throw a}try{var n=P[r]||r||"General";if(t.cellNF)e.z=n;if(t.cellDates&&e.t=="n"&&D.is_date(n)){var i=D.parse_date_code(e.v);if(i){e.t="d";e.v=new Date(i.y,i.m-1,i.d,i.H,i.M,i.S,i.u)}}}catch(a){if(t.WTF)throw a}}function Sp(e,r,t){if(t.cellStyles){if(r.Interior){var a=r.Interior;if(a.Pattern)a.patternType=Al[a.Pattern]||a.Pattern}}e[r.ID]=r}function Bp(e,r,t,a,n,i,s,f,l,o){var c="General",u=a.StyleID,h={};o=o||{};var d=[];var v=0;if(u===undefined&&f)u=f.StyleID;if(u===undefined&&s)u=s.StyleID;while(i[u]!==undefined){if(i[u].nf)c=i[u].nf;if(i[u].Interior)d.push(i[u].Interior);if(!i[u].Parent)break;u=i[u].Parent}switch(t.Type){case"Boolean":a.t="b";a.v=Ke(e);break;case"String":a.t="s";a.r=je(Le(e));a.v=e.indexOf("<")>-1?Le(r||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":if(e.slice(-1)!="Z")e+="Z";a.v=(le(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3);if(a.v!==a.v)a.v=Le(e);else if(a.v<60)a.v=a.v-1;if(!c||c=="General")c="yyyy-mm-dd";case"Number":if(a.v===undefined)a.v=+e;if(!a.t)a.t="n";break;case"Error":a.t="e";a.v=Ia[e];if(o.cellText!==false)a.w=e;break;default:if(e==""&&r==""){a.t="z"}else{a.t="s";a.v=je(r||e)}break;}kp(a,c,o);if(o.cellFormula!==false){if(a.Formula){var p=Le(a.Formula);if(p.charCodeAt(0)==61)p=p.slice(1);a.f=sc(p,n);delete a.Formula;if(a.ArrayRange=="RC")a.F=sc("RC:RC",n);else if(a.ArrayRange){a.F=sc(a.ArrayRange,n);l.push([wt(a.F),a.F])}}else{for(v=0;v<l.length;++v)if(n.r>=l[v][0].s.r&&n.r<=l[v][0].e.r)if(n.c>=l[v][0].s.c&&n.c<=l[v][0].e.c)a.F=l[v][1]}}if(o.cellStyles){d.forEach(function(e){if(!h.patternType&&e.patternType)h.patternType=e.patternType});a.s=h}if(a.StyleID!==undefined)a.ixfe=a.StyleID}function Cp(e){e.t=e.v||"";e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n");e.v=e.w=e.ixfe=undefined}function Tp(e){if(E&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e==="string")return e;if(typeof Uint8Array!=="undefined"&&e instanceof Uint8Array)return Ye(x(y(e)));throw new Error("Bad input format: expected Buffer or string")}var _p=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm;function xp(e,r){var t=r||{};O(D);var a=d(Tp(e));if(t.type=="binary"||t.type=="array"||t.type=="base64"){if(typeof cptable!=="undefined")a=cptable.utils.decode(65001,c(a));else a=Ye(a)}var n=a.slice(0,1024).toLowerCase(),i=false;n=n.replace(/".*?"/g,"");if((n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var s=ce(t);s.type="string";return gf.to_workbook(a,s)}if(n.indexOf("<?xml")==-1)["html","table","head","meta","script","style","div"].forEach(function(e){if(n.indexOf("<"+e)>=0)i=true});if(i)return kb.to_workbook(a,t);var f;var l=[],o;if(b!=null&&t.dense==null)t.dense=b;var u={},h=[],v=t.dense?[]:{},p="";var m={},g={},E={};var w=mp('<Data ss:Type="String">'),k=0;var S=0,B=0;var C={s:{r:2e6,c:2e6},e:{r:0,c:0}};var T={},_={};var x="",A=0;var y=[];var I={},R={},F=0,N=[];var M=[],L={};var U=[],H,W=false;var V=[];var X=[],G={},z=0,j=0;var $={Sheets:[],WBProps:{date1904:false}},K={};_p.lastIndex=0;a=a.replace(/<!--([\s\S]*?)-->/gm,"");var Y="";while(f=_p.exec(a))switch(f[3]=(Y=f[3]).toLowerCase()){case"data":if(Y=="data"){if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/")l.push([f[3],true]);break}if(l[l.length-1][1])break;if(f[1]==="/")Bp(a.slice(k,f.index),x,w,l[l.length-1][0]=="comment"?L:g,{c:S,r:B},T,U[S],E,V,t);else{x="";w=mp(f[0]);k=f.index+f[0].length}break;case"cell":if(f[1]==="/"){if(M.length>0)g.c=M;if((!t.sheetRows||t.sheetRows>B)&&g.v!==undefined){if(t.dense){if(!v[B])v[B]=[];v[B][S]=g}else v[ht(S)+lt(B)]=g}if(g.HRef){g.l={Target:Le(g.HRef)};if(g.HRefScreenTip)g.l.Tooltip=g.HRefScreenTip;delete g.HRef;delete g.HRefScreenTip}if(g.MergeAcross||g.MergeDown){z=S+(parseInt(g.MergeAcross,10)|0);j=B+(parseInt(g.MergeDown,10)|0);y.push({s:{c:S,r:B},e:{c:z,r:j}})}if(!t.sheetStubs){if(g.MergeAcross)S=z+1;else++S}else if(g.MergeAcross||g.MergeDown){for(var Q=S;Q<=z;++Q){for(var J=B;J<=j;++J){if(Q>S||J>B){if(t.dense){if(!v[J])v[J]=[];v[J][Q]={t:"z"}}else v[ht(Q)+lt(J)]={t:"z"}}}}S=z+1}else++S}else{g=gp(f[0]);if(g.Index)S=+g.Index-1;if(S<C.s.c)C.s.c=S;if(S>C.e.c)C.e.c=S;if(f[0].slice(-2)==="/>")++S;M=[]}break;case"row":if(f[1]==="/"||f[0].slice(-2)==="/>"){if(B<C.s.r)C.s.r=B;if(B>C.e.r)C.e.r=B;if(f[0].slice(-2)==="/>"){E=mp(f[0]);if(E.Index)B=+E.Index-1}S=0;++B}else{E=mp(f[0]);if(E.Index)B=+E.Index-1;G={};if(E.AutoFitHeight=="0"||E.Height){G.hpx=parseInt(E.Height,10);G.hpt=_l(G.hpx);X[B]=G}if(E.Hidden=="1"){G.hidden=true;X[B]=G}}break;case"worksheet":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));h.push(p);if(C.s.r<=C.e.r&&C.s.c<=C.e.c){v["!ref"]=Et(C);if(t.sheetRows&&t.sheetRows<=C.e.r){v["!fullref"]=v["!ref"];C.e.r=t.sheetRows-1;v["!ref"]=Et(C)}}if(y.length)v["!merges"]=y;if(U.length>0)v["!cols"]=U;if(X.length>0)v["!rows"]=X;u[p]=v}else{C={s:{r:2e6,c:2e6},e:{r:0,c:0}};B=S=0;l.push([f[3],false]);o=mp(f[0]);p=Le(o.Name);v=t.dense?[]:{};y=[];V=[];X=[];K={name:p,Hidden:0};$.Sheets.push(K)}break;case"table":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].slice(-2)=="/>")break;else{m=mp(f[0]);l.push([f[3],false]);U=[];W=false}break;case"style":if(f[1]==="/")Sp(T,_,t);else _=mp(f[0]);break;case"numberformat":_.nf=Le(mp(f[0]).Format||"General");if(P[_.nf])_.nf=P[_.nf];for(var q=0;q!=392;++q)if(D._table[q]==_.nf)break;if(q==392)for(q=57;q!=392;++q)if(D._table[q]==null){D.load(_.nf,q);break}break;case"column":if(l[l.length-1][0]!=="table")break;H=mp(f[0]);if(H.Hidden){H.hidden=true;delete H.Hidden}if(H.Width)H.wpx=parseInt(H.Width,10);if(!W&&H.wpx>10){W=true;ml=vl;for(var Z=0;Z<U.length;++Z)if(U[Z])Bl(U[Z])}if(W)Bl(H);U[H.Index-1||U.length]=H;for(var ee=0;ee<+H.Span;++ee)U[U.length]=ce(H);break;case"namedrange":if(f[1]==="/")break;if(!$.Names)$.Names=[];var re=Oe(f[0]);var te={Name:re.Name,Ref:sc(re.RefersTo.slice(1),{r:0,c:0})};if($.Sheets.length>0)te.Sheet=$.Sheets.length-1;$.Names.push(te);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(f[0].slice(-2)==="/>")break;else if(f[1]==="/")x+=a.slice(A,f.index);else A=f.index+f[0].length;break;case"interior":if(!t.cellStyles)break;_.Interior=mp(f[0]);break;case"protection":break;case"author":;case"title":;case"description":;case"created":;case"keywords":;case"subject":;case"category":;case"company":;case"lastauthor":;case"lastsaved":;case"lastprinted":;case"version":;case"revision":;case"totaltime":;case"hyperlinkbase":;case"manager":;case"contentstatus":;case"identifier":;case"language":;case"appname":if(f[0].slice(-2)==="/>")break;else if(f[1]==="/")gn(I,Y,a.slice(F,f.index));else F=f.index+f[0].length;break;case"paragraphs":break;case"styles":;case"workbook":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else l.push([f[3],false]);break;case"comment":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));Cp(L);M.push(L)}else{l.push([f[3],false]);o=mp(f[0]);L={a:o.Author}}break;case"autofilter":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/"){var ae=mp(f[0]);v["!autofilter"]={ref:sc(ae.Range).replace(/\$/g,"")};l.push([f[3],true])}break;case"name":break;case"datavalidation":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else{if(f[0].charAt(f[0].length-2)!=="/")l.push([f[3],true])}break;case"pixelsperinch":break;case"componentoptions":;case"documentproperties":;case"customdocumentproperties":;case"officedocumentsettings":;case"pivottable":;case"pivotcache":;case"names":;case"mapinfo":;case"pagebreaks":;case"querytable":;case"sorting":;case"schema":;case"conditionalformatting":;case"smarttagtype":;case"smarttags":;case"excelworkbook":;case"workbookoptions":;case"worksheetoptions":if(f[1]==="/"){if((o=l.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/")l.push([f[3],true]);break;case"null":break;default:if(l.length==0&&f[3]=="document")return yb(a,t);if(l.length==0&&f[3]=="uof")return yb(a,t);var ne=true;switch(l[l.length-1][0]){case"officedocumentsettings":switch(f[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:ne=false;}break;case"componentoptions":switch(f[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:ne=false;}break;case"excelworkbook":switch(f[3]){case"date1904":$.WBProps.date1904=true;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:ne=false;}break;case"workbookoptions":switch(f[3]){case"owcversion":break;case"height":break;case"width":break;default:ne=false;}break;case"worksheetoptions":switch(f[3]){case"visible":if(f[0].slice(-2)==="/>"){}else if(f[1]==="/")switch(a.slice(F,f.index)){case"SheetHidden":K.Hidden=1;break;case"SheetVeryHidden":K.Hidden=2;break;}else F=f.index+f[0].length;break;case"header":if(!v["!margins"])ph(v["!margins"]={},"xlml");v["!margins"].header=Oe(f[0]).Margin;break;case"footer":if(!v["!margins"])ph(v["!margins"]={},"xlml");v["!margins"].footer=Oe(f[0]).Margin;break;case"pagemargins":var ie=Oe(f[0]);if(!v["!margins"])ph(v["!margins"]={},"xlml");if(ie.Top)v["!margins"].top=ie.Top;if(ie.Left)v["!margins"].left=ie.Left;if(ie.Right)v["!margins"].right=ie.Right;if(ie.Bottom)v["!margins"].bottom=ie.Bottom;break;case"displayrighttoleft":if(!$.Views)$.Views=[];if(!$.Views[0])$.Views[0]={};$.Views[0].RTL=true;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":;case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":if(!v["!outline"])v["!outline"]={};v["!outline"].above=true;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":if(!v["!outline"])v["!outline"]={};v["!outline"].left=true;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:ne=false;}break;case"pivottable":;case"pivotcache":switch(f[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:ne=false;}break;case"pagebreaks":switch(f[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:ne=false;}break;case"autofilter":switch(f[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:ne=false;}break;case"querytable":switch(f[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:ne=false;}break;case"datavalidation":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:ne=false;}break;case"sorting":;case"conditionalformatting":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":
break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:ne=false;}break;case"mapinfo":;case"schema":;case"data":switch(f[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":;case"element":;case"complextype":;case"datatype":;case"all":;case"attribute":;case"extends":break;case"row":break;default:ne=false;}break;case"smarttags":break;default:ne=false;break;}if(ne)break;if(f[3].match(/!\[CDATA/))break;if(!l[l.length-1][1])throw"Unrecognized tag: "+f[3]+"|"+l.join("|");if(l[l.length-1][0]==="customdocumentproperties"){if(f[0].slice(-2)==="/>")break;else if(f[1]==="/")wp(R,Y,N,a.slice(F,f.index));else{N=f;F=f.index+f[0].length}break}if(t.WTF)throw"Unrecognized tag: "+f[3]+"|"+l.join("|");}var se={};if(!t.bookSheets&&!t.bookProps)se.Sheets=u;se.SheetNames=h;se.Workbook=$;se.SSF=D.get_table();se.Props=I;se.Custprops=R;return se}function Ap(e,r){jb(r=r||{});switch(r.type||"base64"){case"base64":return xp(g.decode(e),r);case"binary":;case"buffer":;case"file":return xp(e,r);case"array":return xp(x(e),r);}}function yp(e,r){var t=[];if(e.Props)t.push(En(e.Props,r));if(e.Custprops)t.push(wn(e.Props,e.Custprops,r));return t.join("")}function Ip(){return""}function Rp(e,r){var t=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];r.cellXfs.forEach(function(e,r){var a=[];a.push(or("NumberFormat",null,{"ss:Format":We(D._table[e.numFmtId])}));var n={"ss:ID":"s"+(21+r)};t.push(or("Style",a.join(""),n))});return or("Styles",t.join(""))}function Fp(e){return or("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+lc(e.Ref,{r:0,c:0})})}function Dp(e){if(!((e||{}).Workbook||{}).Names)return"";var r=e.Workbook.Names;var t=[];for(var a=0;a<r.length;++a){var n=r[a];if(n.Sheet!=null)continue;if(n.Name.match(/^_xlfn\./))continue;t.push(Fp(n))}return or("Names",t.join(""))}function Op(e,r,t,a){if(!e)return"";if(!((a||{}).Workbook||{}).Names)return"";var n=a.Workbook.Names;var i=[];for(var s=0;s<n.length;++s){var f=n[s];if(f.Sheet!=t)continue;if(f.Name.match(/^_xlfn\./))continue;i.push(Fp(f))}return i.join("")}function Pp(e,r,t,a){if(!e)return"";var n=[];if(e["!margins"]){n.push("<PageSetup>");if(e["!margins"].header)n.push(or("Header",null,{"x:Margin":e["!margins"].header}));if(e["!margins"].footer)n.push(or("Footer",null,{"x:Margin":e["!margins"].footer}));n.push(or("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"}));n.push("</PageSetup>")}if(a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[t]){if(a.Workbook.Sheets[t].Hidden)n.push(or("Visible",a.Workbook.Sheets[t].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<t;++i)if(a.Workbook.Sheets[i]&&!a.Workbook.Sheets[i].Hidden)break;if(i==t)n.push("<Selected/>")}}if(((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL)n.push("<DisplayRightToLeft/>");if(e["!protect"]){n.push(fr("ProtectContents","True"));if(e["!protect"].objects)n.push(fr("ProtectObjects","True"));if(e["!protect"].scenarios)n.push(fr("ProtectScenarios","True"));if(e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells)n.push(fr("EnableSelection","NoSelection"));else if(e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells)n.push(fr("EnableSelection","UnlockedCells"));[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(r){if(e["!protect"][r[0]])n.push("<"+r[1]+"/>")})}if(n.length==0)return"";return or("WorksheetOptions",n.join(""),{xmlns:dr.x})}function Np(e){return e.map(function(e){var r=$e(e.t||"");var t=or("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return or("Comment",t,{"ss:Author":e.a})}).join("")}function Mp(e,r,t,a,n,i,s){if(!e||e.v==undefined&&e.f==undefined)return"";var f={};if(e.f)f["ss:Formula"]="="+We(lc(e.f,s));if(e.F&&e.F.slice(0,r.length)==r){var l=bt(e.F.slice(r.length+1));f["ss:ArrayRange"]="RC:R"+(l.r==s.r?"":"["+(l.r-s.r)+"]")+"C"+(l.c==s.c?"":"["+(l.c-s.c)+"]")}if(e.l&&e.l.Target){f["ss:HRef"]=We(e.l.Target);if(e.l.Tooltip)f["x:HRefScreenTip"]=We(e.l.Tooltip)}if(t["!merges"]){var o=t["!merges"];for(var c=0;c!=o.length;++c){if(o[c].s.c!=s.c||o[c].s.r!=s.r)continue;if(o[c].e.c>o[c].s.c)f["ss:MergeAcross"]=o[c].e.c-o[c].s.c;if(o[c].e.r>o[c].s.r)f["ss:MergeDown"]=o[c].e.r-o[c].s.r}}var u="",h="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":u="Number";h=String(e.v);break;case"b":u="Boolean";h=e.v?"1":"0";break;case"e":u="Error";h=ya[e.v];break;case"d":u="DateTime";h=new Date(e.v).toISOString();if(e.z==null)e.z=e.z||D._table[14];break;case"s":u="String";h=ze(e.v||"");break;}var d=bh(a.cellXfs,e,a);f["ss:StyleID"]="s"+(21+d);f["ss:Index"]=s.c+1;var v=e.v!=null?h:"";var p=e.t=="z"?"":'<Data ss:Type="'+u+'">'+v+"</Data>";if((e.c||[]).length>0)p+=Np(e.c);return or("Cell",p,f)}function Lp(e,r){var t='<Row ss:Index="'+(e+1)+'"';if(r){if(r.hpt&&!r.hpx)r.hpx=xl(r.hpt);if(r.hpx)t+=' ss:AutoFitHeight="0" ss:Height="'+r.hpx+'"';if(r.hidden)t+=' ss:Hidden="1"'}return t+">"}function Up(e,r,t,a){if(!e["!ref"])return"";var n=wt(e["!ref"]);var i=e["!merges"]||[],s=0;var f=[];if(e["!cols"])e["!cols"].forEach(function(e,r){Bl(e);var t=!!e.width;var a=vh(r,e);var n={"ss:Index":r+1};if(t)n["ss:Width"]=gl(a.width);if(e.hidden)n["ss:Hidden"]="1";f.push(or("Column",null,n))});var l=Array.isArray(e);for(var o=n.s.r;o<=n.e.r;++o){var c=[Lp(o,(e["!rows"]||[])[o])];for(var u=n.s.c;u<=n.e.c;++u){var h=false;for(s=0;s!=i.length;++s){if(i[s].s.c>u)continue;if(i[s].s.r>o)continue;if(i[s].e.c<u)continue;if(i[s].e.r<o)continue;if(i[s].s.c!=u||i[s].s.r!=o)h=true;break}if(h)continue;var d={r:o,c:u};var v=mt(d),p=l?(e[o]||[])[u]:e[v];c.push(Mp(p,v,e,r,t,a,d))}c.push("</Row>");if(c.length>2)f.push(c.join(""))}return f.join("")}function Hp(e,r,t){var a=[];var n=t.SheetNames[e];var i=t.Sheets[n];var s=i?Op(i,r,e,t):"";if(s.length>0)a.push("<Names>"+s+"</Names>");s=i?Up(i,r,e,t):"";if(s.length>0)a.push("<Table>"+s+"</Table>");a.push(Pp(i,r,e,t));return a.join("")}function Wp(e,r){if(!r)r={};if(!e.SSF)e.SSF=D.get_table();if(e.SSF){O(D);D.load_table(e.SSF);r.revssf=J(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF;r.cellXfs=[];bh(r.cellXfs,{},{revssf:{General:0}})}var t=[];t.push(yp(e,r));t.push(Ip(e,r));t.push("");t.push("");for(var a=0;a<e.SheetNames.length;++a)t.push(or("Worksheet",Hp(a,r,e),{"ss:Name":We(e.SheetNames[a])}));t[2]=Rp(e,r);t[3]=Dp(e,r);return ye+or("Workbook",t.join(""),{xmlns:dr.ss,"xmlns:o":dr.o,"xmlns:x":dr.x,"xmlns:ss":dr.ss,"xmlns:dt":dr.dt,"xmlns:html":dr.html})}function Vp(e){var r={};var t=e.content;t.l=28;r.AnsiUserType=t._R(0,"lpstr-ansi");r.AnsiClipboardFormat=ia(t);if(t.length-t.l<=4)return r;var a=t._R(4);if(a==0||a>40)return r;t.l-=4;r.Reserved1=t._R(0,"lpstr-ansi");if(t.length-t.l<=4)return r;a=t._R(4);if(a!==1907505652)return r;r.UnicodeClipboardFormat=sa(t);a=t._R(4);if(a==0||a>40)return r;t.l-=4;r.Reserved2=t._R(0,"lpwstr")}function Xp(e,r,t,a){var n=t;var i=[];var s=r.slice(r.l,r.l+n);if(a&&a.enc&&a.enc.insitu&&s.length>0)switch(e.n){case"BOF":;case"FilePass":;case"FileLock":;case"InterfaceHdr":;case"RRDInfo":;case"RRDHead":;case"UsrExcl":;case"EOF":break;case"BoundSheet8":break;default:a.enc.insitu(s);}i.push(s);r.l+=n;var f=eb[Lr(r,r.l)];var l=0;while(f!=null&&f.n.slice(0,8)==="Continue"){n=Lr(r,r.l+2);l=r.l+4;if(f.n=="ContinueFrt")l+=4;else if(f.n.slice(0,11)=="ContinueFrt"){l+=12}s=r.slice(l,r.l+4+n);i.push(s);r.l+=4+n;f=eb[Lr(r,r.l)]}var o=I(i);Yr(o,0);var c=0;o.lens=[];for(var u=0;u<i.length;++u){o.lens.push(c);c+=i[u].length}if(o.length<t)throw"XLS Record "+(e&&e.n||"??")+" Truncated: "+o.length+" < "+t;return e.f(o,o.length,a)}function Gp(e,r,t){if(e.t==="z")return;if(!e.XF)return;var a=0;try{a=e.z||e.XF.numFmtId||0;if(r.cellNF)e.z=D._table[a]}catch(n){if(r.WTF)throw n}if(!r||r.cellText!==false)try{if(e.t==="e"){e.w=e.w||ya[e.v]}else if(a===0||a=="General"){if(e.t==="n"){if((e.v|0)===e.v)e.w=D._general_int(e.v);else e.w=D._general_num(e.v)}else e.w=D._general(e.v)}else e.w=D.format(a,e.v,{date1904:!!t,dateNF:r&&r.dateNF})}catch(n){if(r.WTF)throw n}if(r.cellDates&&a&&e.t=="n"&&D.is_date(D._table[a]||String(a))){var i=D.parse_date_code(e.v);if(i){e.t="d";e.v=new Date(i.y,i.m-1,i.d,i.H,i.M,i.S,i.u)}}}function zp(e,r,t){return{v:e,ixfe:r,t:t}}function jp(e,r){var t={opts:{}};var a={};if(b!=null&&r.dense==null)r.dense=b;var n=r.dense?[]:{};var i={};var s={};var f=null;var o=[];var c="";var u={};var h,d="",v,p,m,g;var E={};var w=[];var k;var S;var B=[];var C=[];var T={Sheets:[],WBProps:{date1904:false},Views:[{}]},_={};var x=function ge(e){if(e<8)return Aa[e];if(e<64)return C[e-8]||Aa[e];return Aa[e]};var A=function Ee(e,r,t){var a=r.XF.data;if(!a||!a.patternType||!t||!t.cellStyles)return;r.s={};r.s.patternType=a.patternType;var n;if(n=cl(x(a.icvFore))){r.s.fgColor={rgb:n}}if(n=cl(x(a.icvBack))){r.s.bgColor={rgb:n}}};var y=function we(e,r,t){if(V>1)return;if(t.sheetRows&&e.r>=t.sheetRows)return;if(t.cellStyles&&r.XF&&r.XF.data)A(e,r,t);delete r.ixfe;delete r.XF;h=e;d=mt(e);if(!s||!s.s||!s.e)s={s:{r:0,c:0},e:{r:0,c:0}};if(e.r<s.s.r)s.s.r=e.r;if(e.c<s.s.c)s.s.c=e.c;if(e.r+1>s.e.r)s.e.r=e.r+1;if(e.c+1>s.e.c)s.e.c=e.c+1;if(t.cellFormula&&r.f){for(var a=0;a<w.length;++a){if(w[a][0].s.c>e.c||w[a][0].s.r>e.r)continue;if(w[a][0].e.c<e.c||w[a][0].e.r<e.r)continue;r.F=Et(w[a][0]);if(w[a][0].s.c!=e.c||w[a][0].s.r!=e.r)delete r.f;if(r.f)r.f=""+Vu(w[a][1],s,e,H,I);break}}{if(t.dense){if(!n[e.r])n[e.r]=[];n[e.r][e.c]=r}else n[d]=r}};var I={enc:false,sbcch:0,snames:[],sharedf:E,arrayf:w,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!r&&!!r.cellStyles,WTF:!!r&&!!r.wtf};if(r.password)I.password=r.password;var R;var F=[];var O=[];var P=[],N=[];var M=0,L=0;var U=false;var H=[];H.SheetNames=I.snames;H.sharedf=I.sharedf;H.arrayf=I.arrayf;H.names=[];H.XTI=[];var W="";var V=0;var X=0,G=[];var z=[];var j;I.codepage=1200;l(1200);var $=false;while(e.l<e.length-1){var Y=e.l;var Q=e._R(2);if(Q===0&&W==="EOF")break;var J=e.l===e.length?0:e._R(2);var q=eb[Q];if(q&&q.f){if(r.bookSheets){if(W==="BoundSheet8"&&q.n!=="BoundSheet8")break}W=q.n;if(q.r===2||q.r==12){var Z=e._R(2);J-=2;if(!I.enc&&Z!==Q&&((Z&255)<<8|Z>>8)!==Q)throw new Error("rt mismatch: "+Z+"!="+Q);if(q.r==12){e.l+=10;J-=10}}var ee={};if(q.n==="EOF")ee=q.f(e,J,I);else ee=Xp(q,e,J,I);var re=q.n;if(V==0&&re!="BOF")continue;switch(re){case"Date1904":t.opts.Date1904=T.WBProps.date1904=ee;break;case"WriteProtect":t.opts.WriteProtect=true;break;case"FilePass":if(!I.enc)e.l=0;I.enc=ee;if(!r.password)throw new Error("File is password-protected");if(ee.valid==null)throw new Error("Encryption scheme unsupported");if(!ee.valid)throw new Error("Password is incorrect");break;case"WriteAccess":I.lastuser=ee;break;case"FileSharing":break;case"CodePage":var te=Number(ee);switch(te){case 21010:te=1200;break;case 32768:te=1e4;break;case 32769:te=1252;break;}l(I.codepage=te);$=true;break;case"RRTabId":I.rrtabid=ee;break;case"WinProtect":I.winlocked=ee;break;case"Template":break;case"BookBool":break;case"UsesELFs":break;case"MTRSettings":break;case"RefreshAll":;case"CalcCount":;case"CalcDelta":;case"CalcIter":;case"CalcMode":;case"CalcPrecision":;case"CalcSaveRecalc":t.opts[re]=ee;break;case"CalcRefMode":I.CalcRefMode=ee;break;case"Uncalced":break;case"ForceFullCalculation":t.opts.FullCalc=ee;break;case"WsBool":if(ee.fDialog)n["!type"]="dialog";if(!ee.fBelow)(n["!outline"]||(n["!outline"]={})).above=true;if(!ee.fRight)(n["!outline"]||(n["!outline"]={})).left=true;break;case"XF":B.push(ee);break;case"ExtSST":break;case"BookExt":break;case"RichTextStream":break;case"BkHim":break;case"SupBook":H.push([ee]);H[H.length-1].XTI=[];break;case"ExternName":H[H.length-1].push(ee);break;case"Index":break;case"Lbl":j={Name:ee.Name,Ref:Vu(ee.rgce,s,null,H,I)};if(ee.itab>0)j.Sheet=ee.itab-1;H.names.push(j);if(!H[0]){H[0]=[];H[0].XTI=[]}H[H.length-1].push(ee);if(ee.Name=="_xlnm._FilterDatabase"&&ee.itab>0)if(ee.rgce&&ee.rgce[0]&&ee.rgce[0][0]&&ee.rgce[0][0][0]=="PtgArea3d")z[ee.itab-1]={ref:Et(ee.rgce[0][0][1][2])};break;case"ExternCount":I.ExternCount=ee;break;case"ExternSheet":if(H.length==0){H[0]=[];H[0].XTI=[]}H[H.length-1].XTI=H[H.length-1].XTI.concat(ee);H.XTI=H.XTI.concat(ee);break;case"NameCmt":if(I.biff<8)break;if(j!=null)j.Comment=ee[1];break;case"Protect":n["!protect"]=ee;break;case"Password":if(ee!==0&&I.WTF)console.error("Password verifier: "+ee);break;case"Prot4Rev":;case"Prot4RevPass":break;case"BoundSheet8":{i[ee.pos]=ee;I.snames.push(ee.name)}break;case"EOF":{if(--V)break;if(s.e){if(s.e.r>0&&s.e.c>0){s.e.r--;s.e.c--;n["!ref"]=Et(s);if(r.sheetRows&&r.sheetRows<=s.e.r){var ae=s.e.r;s.e.r=r.sheetRows-1;n["!fullref"]=n["!ref"];n["!ref"]=Et(s);s.e.r=ae}s.e.r++;s.e.c++}if(F.length>0)n["!merges"]=F;if(O.length>0)n["!objects"]=O;if(P.length>0)n["!cols"]=P;if(N.length>0)n["!rows"]=N;T.Sheets.push(_)}if(c==="")u=n;else a[c]=n;n=r.dense?[]:{}}break;case"BOF":{if(I.biff===8)I.biff={9:2,521:3,1033:4}[Q]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[ee.BIFFVer]||8;I.biffguess=ee.BIFFVer==0;if(ee.BIFFVer==0&&ee.dt==4096){I.biff=5;$=true;l(I.codepage=28591)}if(I.biff==8&&ee.BIFFVer==0&&ee.dt==16)I.biff=2;if(V++)break;n=r.dense?[]:{};if(I.biff<8&&!$){$=true;l(I.codepage=r.codepage||1252)}if(I.biff<5||ee.BIFFVer==0&&ee.dt==4096){if(c==="")c="Sheet1";s={s:{r:0,c:0},e:{r:0,c:0}};var ne={pos:e.l-J,name:c};i[ne.pos]=ne;I.snames.push(c)}else c=(i[Y]||{name:""}).name;if(ee.dt==32)n["!type"]="chart";if(ee.dt==64)n["!type"]="macro";F=[];O=[];I.arrayf=w=[];P=[];N=[];M=L=0;U=false;_={Hidden:(i[Y]||{hs:0}).hs,name:c}}break;case"Number":;case"BIFF2NUM":;case"BIFF2INT":{if(n["!type"]=="chart")if(r.dense?(n[ee.r]||[])[ee.c]:n[mt({c:ee.c,r:ee.r})])++ee.c;k={ixfe:ee.ixfe,XF:B[ee.ixfe]||{},v:ee.val,t:"n"};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r)}break;case"BoolErr":{k={ixfe:ee.ixfe,XF:B[ee.ixfe],v:ee.val,t:ee.t};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r)}break;case"RK":{k={ixfe:ee.ixfe,XF:B[ee.ixfe],v:ee.rknum,t:"n"};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r)}break;case"MulRk":{for(var ie=ee.c;ie<=ee.C;++ie){var se=ee.rkrec[ie-ee.c][0];k={ixfe:se,XF:B[se],v:ee.rkrec[ie-ee.c][1],t:"n"};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ie,r:ee.r},k,r)}}break;case"Formula":{if(ee.val=="String"){f=ee;break}k=zp(ee.val,ee.cell.ixfe,ee.tt);k.XF=B[k.ixfe];if(r.cellFormula){var fe=ee.formula;if(fe&&fe[0]&&fe[0][0]&&fe[0][0][0]=="PtgExp"){var le=fe[0][0][1][0],oe=fe[0][0][1][1];var ce=mt({r:le,c:oe});if(E[ce])k.f=""+Vu(ee.formula,s,ee.cell,H,I);else k.F=((r.dense?(n[le]||[])[oe]:n[ce])||{}).F}else k.f=""+Vu(ee.formula,s,ee.cell,H,I)}if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y(ee.cell,k,r);f=ee}break;case"String":{if(f){f.val=ee;k=zp(ee,f.cell.ixfe,"s");k.XF=B[k.ixfe];if(r.cellFormula){k.f=""+Vu(f.formula,s,f.cell,H,I)}if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y(f.cell,k,r);f=null}else throw new Error("String record expects Formula")}break;case"Array":{w.push(ee);var ue=mt(ee[0].s);v=r.dense?(n[ee[0].s.r]||[])[ee[0].s.c]:n[ue];if(r.cellFormula&&v){if(!f)break;if(!ue||!v)break;v.f=""+Vu(ee[1],s,ee[0],H,I);v.F=Et(ee[0])}}break;case"ShrFmla":{if(!r.cellFormula)break;if(d){if(!f)break;E[mt(f.cell)]=ee[0];v=r.dense?(n[f.cell.r]||[])[f.cell.c]:n[mt(f.cell)];(v||{}).f=""+Vu(ee[0],s,h,H,I)}}break;case"LabelSst":k=zp(o[ee.isst].t,ee.ixfe,"s");if(o[ee.isst].h)k.h=o[ee.isst].h;k.XF=B[k.ixfe];if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r);break;case"Blank":if(r.sheetStubs){k={ixfe:ee.ixfe,XF:B[ee.ixfe],t:"z"};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r)}break;case"MulBlank":if(r.sheetStubs){for(var he=ee.c;he<=ee.C;++he){var de=ee.ixfe[he-ee.c];k={ixfe:de,XF:B[de],t:"z"};if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:he,r:ee.r},k,r)}}break;case"RString":;case"Label":;case"BIFF2STR":k=zp(ee.val,ee.ixfe,"s");k.XF=B[k.ixfe];if(X>0)k.z=G[k.ixfe>>8&63];Gp(k,r,t.opts.Date1904);y({c:ee.c,r:ee.r},k,r);break;case"Dimensions":{if(V===1)s=ee}break;case"SST":{o=ee}break;case"Format":{if(I.biff==4){G[X++]=ee[1];for(var ve=0;ve<X+163;++ve)if(D._table[ve]==ee[1])break;if(ve>=163)D.load(ee[1],X+163)}else D.load(ee[1],ee[0])}break;case"BIFF2FORMAT":{G[X++]=ee;for(var pe=0;pe<X+163;++pe)if(D._table[pe]==ee)break;if(pe>=163)D.load(ee,X+163)}break;case"MergeCells":F=F.concat(ee);break;case"Obj":O[ee.cmo[0]]=I.lastobj=ee;break;case"TxO":I.lastobj.TxO=ee;break;case"ImData":I.lastobj.ImData=ee;break;case"HLink":{for(g=ee[0].s.r;g<=ee[0].e.r;++g)for(m=ee[0].s.c;m<=ee[0].e.c;++m){v=r.dense?(n[g]||[])[m]:n[mt({c:m,r:g})];if(v)v.l=ee[1]}}break;case"HLinkTooltip":{for(g=ee[0].s.r;g<=ee[0].e.r;++g)for(m=ee[0].s.c;m<=ee[0].e.c;++m){v=r.dense?(n[g]||[])[m]:n[mt({c:m,r:g})];if(v&&v.l)v.l.Tooltip=ee[1]}}break;case"Note":{if(I.biff<=5&&I.biff>=2)break;v=r.dense?(n[ee[0].r]||[])[ee[0].c]:n[mt(ee[0])];var be=O[ee[2]];if(!v){if(r.dense){if(!n[ee[0].r])n[ee[0].r]=[];v=n[ee[0].r][ee[0].c]={t:"z"}}else{v=n[mt(ee[0])]={t:"z"}}s.e.r=Math.max(s.e.r,ee[0].r);s.s.r=Math.min(s.s.r,ee[0].r);s.e.c=Math.max(s.e.c,ee[0].c);s.s.c=Math.min(s.s.c,ee[0].c)}if(!v.c)v.c=[];p={a:ee[1],t:be.TxO.t};v.c.push(p)}break;default:switch(q.n){case"ClrtClient":break;case"XFExt":Do(B[ee.ixfe],ee.ext);break;case"DefColWidth":M=ee;break;case"DefaultRowHeight":L=ee[1];break;case"ColInfo":{if(!I.cellStyles)break;while(ee.e>=ee.s){P[ee.e--]={width:ee.w/256,level:ee.level||0,hidden:!!(ee.flags&1)};if(!U){U=true;Sl(ee.w/256)}Bl(P[ee.e+1])}}break;case"Row":{var me={};if(ee.level!=null){N[ee.r]=me;me.level=ee.level}if(ee.hidden){N[ee.r]=me;me.hidden=true}if(ee.hpt){N[ee.r]=me;me.hpt=ee.hpt;me.hpx=xl(ee.hpt)}}break;case"LeftMargin":;case"RightMargin":;case"TopMargin":;case"BottomMargin":if(!n["!margins"])ph(n["!margins"]={});n["!margins"][re.slice(0,-6).toLowerCase()]=ee;break;case"Selection":break;case"Setup":if(!n["!margins"])ph(n["!margins"]={});n["!margins"].header=ee.header;n["!margins"].footer=ee.footer;break;case"Window2":if(ee.RTL)T.Views[0].RTL=true;break;case"Header":break;case"Footer":break;case"HCenter":break;case"VCenter":break;case"Pls":break;case"GCW":break;case"LHRecord":break;case"DBCell":break;case"EntExU2":break;case"SxView":break;case"Sxvd":break;case"SXVI":break;case"SXVDEx":break;case"SxIvd":break;case"SXString":break;case"Sync":break;case"Addin":break;case"SXDI":break;case"SXLI":break;case"SXEx":break;case"QsiSXTag":break;case"Feat":break;case"FeatHdr":;case"FeatHdr11":break;case"Feature11":;case"Feature12":;case"List12":break;case"Country":S=ee;break;case"RecalcId":break;case"DxGCol":break;case"Fbi":;case"Fbi2":;case"GelFrame":break;case"Font":break;case"XFCRC":break;case"Style":break;case"StyleExt":break;case"Palette":C=ee;break;case"Theme":R=ee;break;case"ScenarioProtect":break;case"ObjProtect":break;case"CondFmt12":break;case"Table":break;case"TableStyles":break;case"TableStyle":break;case"TableStyleElement":break;case"SXStreamID":break;case"SXVS":break;case"DConRef":break;case"SXAddl":break;case"DConBin":break;case"DConName":break;case"SXPI":break;case"SxFormat":break;case"SxSelect":break;case"SxRule":break;case"SxFilt":break;case"SxItm":break;case"SxDXF":break;case"ScenMan":break;case"DCon":break;case"CellWatch":break;case"PrintRowCol":break;case"PrintGrid":break;case"PrintSize":break;case"XCT":break;case"CRN":break;case"Scl":{}break;case"SheetExt":{}break;case"SheetExtOptional":{}break;case"ObNoMacros":{}break;case"ObProj":{}break;case"CodeName":{if(!c)T.WBProps.CodeName=ee||"ThisWorkbook";else _.CodeName=ee||_.name}break;case"GUIDTypeLib":{}break;case"WOpt":break;case"PhoneticInfo":break;case"OleObjectSize":break;case"DXF":;case"DXFN":;case"DXFN12":;case"DXFN12List":;case"DXFN12NoCB":break;case"Dv":;case"DVal":break;case"BRAI":;case"Series":;case"SeriesText":break;case"DConn":break;case"DbOrParamQry":break;case"DBQueryExt":break;case"OleDbConn":break;case"ExtString":break;case"IFmtRecord":break;case"CondFmt":;case"CF":;case"CF12":;case"CFEx":break;case"Excel9File":break;case"Units":break;case"InterfaceHdr":;case"Mms":;case"InterfaceEnd":;case"DSF":break;case"BuiltInFnGroupCount":break;case"Window1":;case"HideObj":;case"GridSet":;case"Guts":;case"UserBView":;case"UserSViewBegin":;case"UserSViewEnd":break;case"Pane":break;default:switch(q.n){case"Dat":;case"Begin":;case"End":;case"StartBlock":;case"EndBlock":;case"Frame":;case"Area":;case"Axis":;case"AxisLine":;case"Tick":break;case"AxesUsed":;case"CrtLayout12":;case"CrtLayout12A":;case"CrtLink":;case"CrtLine":;case"CrtMlFrt":;case"CrtMlFrtContinue":break;case"LineFormat":;case"AreaFormat":;case"Chart":;case"Chart3d":;case"Chart3DBarShape":;case"ChartFormat":;case"ChartFrtInfo":break;case"PlotArea":;case"PlotGrowth":break;case"SeriesList":;case"SerParent":;case"SerAuxTrend":break;case"DataFormat":;case"SerToCrt":;case"FontX":break;case"CatSerRange":;case"AxcExt":;case"SerFmt":break;case"ShtProps":break;case"DefaultText":;case"Text":;case"CatLab":break;case"DataLabExtContents":break;case"Legend":;case"LegendException":break;case"Pie":;case"Scatter":break;case"PieFormat":;case"MarkerFormat":break;case"StartObject":;case"EndObject":break;case"AlRuns":;case"ObjectLink":break;case"SIIndex":break;case"AttachedLabel":;case"YMult":break;case"Line":;case"Bar":break;case"Surf":break;case"AxisParent":break;case"Pos":break;case"ValueRange":break;case"SXViewEx9":break;case"SXViewLink":break;case"PivotChartBits":break;case"SBaseRef":break;case"TextPropsStream":break;case"LnExt":break;case"MkrExt":break;case"CrtCoopt":break;case"Qsi":;case"Qsif":;case"Qsir":;case"QsiSXTag":break;case"TxtQry":break;case"FilterMode":break;case"AutoFilter":;case"AutoFilterInfo":break;case"AutoFilter12":break;case"DropDownObjIds":break;case"Sort":break;case"SortData":break;case"ShapePropsStream":break;case"MsoDrawing":;case"MsoDrawingGroup":;case"MsoDrawingSelection":break;case"WebPub":;case"AutoWebPub":break;case"HeaderFooter":;case"HFPicture":;case"PLV":;case"HorizontalPageBreaks":;case"VerticalPageBreaks":break;case"Backup":;case"CompressPictures":;case"Compat12":break;case"Continue":;case"ContinueFrt12":break;case"FrtFontList":;case"FrtWrapper":break;default:switch(q.n){case"TabIdConf":;case"Radar":;case"RadarArea":;case"DropBar":;case"Intl":;case"CoordList":;case"SerAuxErrBar":break;case"BIFF2FONTCLR":;case"BIFF2FMTCNT":;case"BIFF2FONTXTRA":break;case"BIFF2XF":;case"BIFF3XF":;case"BIFF4XF":break;case"BIFF2XFINDEX":break;case"BIFF4FMTCNT":;case"BIFF2ROW":;case"BIFF2WINDOW2":break;case"SCENARIO":;case"DConBin":;case"PicF":;case"DataLabExt":;case"Lel":;case"BopPop":;case"BopPopCustom":;case"RealTimeData":;case"Name":break;case"LHNGraph":;case"FnGroupName":;case"AddMenu":;case"LPr":break;case"ListObj":;case"ListField":break;case"RRSort":break;case"BigName":break;case"ToolbarHdr":;case"ToolbarEnd":break;case"DDEObjName":break;case"FRTArchId$":break;default:if(r.WTF)throw"Unrecognized Record "+q.n;};};};}}else e.l+=J}t.SheetNames=K(i).sort(function(e,r){return Number(e)-Number(r)}).map(function(e){return i[e].name});if(!r.bookSheets)t.Sheets=a;if(!t.SheetNames.length&&u["!ref"]){t.SheetNames.push("Sheet1");if(t.Sheets)t.Sheets["Sheet1"]=u}else t.Preamble=u;if(t.Sheets)z.forEach(function(e,r){t.Sheets[t.SheetNames[r]]["!autofilter"]=e});t.Strings=o;t.SSF=D.get_table();if(I.enc)t.Encryption=I.enc;if(R)t.Themes=R;t.Metadata={};if(S!==undefined)t.Metadata.Country=S;if(H.names.length>0)T.Names=H.names;t.Workbook=T;return t}var $p={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function Kp(e,r,t){var a=V.find(e,"!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Wn(a,wa,$p.DSI);for(var i in n)r[i]=n[i]}catch(s){if(t.WTF)throw s}var f=V.find(e,"!SummaryInformation");if(f&&f.size>0)try{var l=Wn(f,ka,$p.SI);for(var o in l)if(r[o]==null)r[o]=l[o]}catch(s){if(t.WTF)throw s}if(r.HeadingPairs&&r.TitlesOfParts){ln(r.HeadingPairs,r.TitlesOfParts,r,t);delete r.HeadingPairs;delete r.TitlesOfParts}}function Yp(e,r){var t=[],a=[],n=[];var i=0,s;if(e.Props){s=K(e.Props);for(i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(Sa,s[i])?t:Object.prototype.hasOwnProperty.call(Ba,s[i])?a:n).push([s[i],e.Props[s[i]]])}if(e.Custprops){s=K(e.Custprops);for(i=0;i<s.length;++i)if(!Object.prototype.hasOwnProperty.call(e.Props||{},s[i]))(Object.prototype.hasOwnProperty.call(Sa,s[i])?t:Object.prototype.hasOwnProperty.call(Ba,s[i])?a:n).push([s[i],e.Custprops[s[i]]])}var f=[];for(i=0;i<n.length;++i){if(Ln.indexOf(n[i][0])>-1)continue;if(n[i][1]==null)continue;f.push(n[i])}if(a.length)V.utils.cfb_add(r,"/SummaryInformation",Vn(a,$p.SI,Ba,ka));if(t.length||f.length)V.utils.cfb_add(r,"/DocumentSummaryInformation",Vn(t,$p.DSI,Sa,wa,f.length?f:null,$p.UDI))}function Qp(e,r){if(!r)r={};jb(r);o();if(r.codepage)s(r.codepage);var t,a;if(e.FullPaths){if(V.find(e,"/encryption"))throw new Error("File is password-protected");t=V.find(e,"!CompObj");a=V.find(e,"/Workbook")||V.find(e,"/Book")}else{switch(r.type){case"base64":e=T(g.decode(e));break;case"binary":e=T(e);break;case"buffer":break;case"array":if(!Array.isArray(e))e=Array.prototype.slice.call(e);break;}Yr(e,0);a={content:e}}var n;var i;if(t)Vp(t);if(r.bookProps&&!r.bookSheets)n={};else{var f=E?"buffer":"array";if(a&&a.content)n=jp(a.content,r);else if((i=V.find(e,"PerfectOffice_MAIN"))&&i.content)n=wf.to_workbook(i.content,(r.type=f,r));else if((i=V.find(e,"NativeContent_MAIN"))&&i.content)n=wf.to_workbook(i.content,(r.type=f,r));else if((i=V.find(e,"MN0"))&&i.content)throw new Error("Unsupported Works 4 for Mac file");else throw new Error("Cannot find Workbook stream");if(r.bookVBA&&e.FullPaths&&V.find(e,"/_VBA_PROJECT_CUR/VBA/dir"))n.vbaraw=Zo(e)}var l={};if(e.FullPaths)Kp(e,l,r);n.Props=n.Custprops=l;if(r.bookFiles)n.cfb=e;return n}function Jp(e,r){var t=r||{};var a=V.utils.cfb_new({root:"R"});var n="/Workbook";switch(t.bookType||"xls"){case"xls":t.bookType="biff8";case"xla":if(!t.bookType)t.bookType="xla";case"biff8":n="/Workbook";t.biff=8;break;case"biff5":n="/Book";t.biff=5;break;default:throw new Error("invalid type "+t.bookType+" for XLS CFB");}V.utils.cfb_add(a,n,wb(e,t));if(t.biff==8&&(e.Props||e.Custprops))Yp(e,a);if(t.biff==8&&e.vbaraw)ec(a,V.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"}));return a}var qp={0:{n:"BrtRowHdr",f:Zh},1:{n:"BrtCellBlank",f:fd},2:{n:"BrtCellRk",f:xd},3:{n:"BrtCellError",f:pd},4:{n:"BrtCellBool",f:ud},5:{n:"BrtCellReal",f:Bd},6:{n:"BrtCellSt",f:Rd},7:{n:"BrtCellIsst",f:Ed},8:{n:"BrtFmlaString",f:Ld},9:{n:"BrtFmlaNum",f:Md},10:{n:"BrtFmlaBool",f:Pd},11:{n:"BrtFmlaError",f:Nd},12:{n:"BrtShortBlank",f:od},13:{n:"BrtShortRk",f:yd},14:{n:"BrtShortError",f:md},15:{n:"BrtShortBool",f:dd},16:{n:"BrtShortReal",f:Td},17:{n:"BrtShortSt",f:Dd},18:{n:"BrtShortIsst",f:kd},19:{n:"BrtSSTItem",f:Rt},20:{n:"BrtPCDIMissing"},21:{n:"BrtPCDINumber"},22:{n:"BrtPCDIBoolean"},23:{n:"BrtPCDIError"},24:{n:"BrtPCDIString"},25:{n:"BrtPCDIDatetime"},26:{n:"BrtPCDIIndex"},27:{n:"BrtPCDIAMissing"},28:{n:"BrtPCDIANumber"},29:{n:"BrtPCDIABoolean"},30:{n:"BrtPCDIAError"},31:{n:"BrtPCDIAString"},32:{n:"BrtPCDIADatetime"},33:{n:"BrtPCRRecord"},34:{n:"BrtPCRRecordDt"},35:{n:"BrtFRTBegin"},36:{n:"BrtFRTEnd"},37:{n:"BrtACBegin"},38:{n:"BrtACEnd"},39:{n:"BrtName",f:zv},40:{n:"BrtIndexRowBlock"},42:{n:"BrtIndexBlock"},43:{n:"BrtFont",f:Xl},44:{n:"BrtFmt",f:Wl},45:{n:"BrtFill",f:$l},46:{n:"BrtBorder",f:ql},47:{n:"BrtXF",f:Yl},48:{n:"BrtStyle"},49:{n:"BrtCellMeta"},50:{n:"BrtValueMeta"},51:{n:"BrtMdb"},52:{n:"BrtBeginFmd"},53:{n:"BrtEndFmd"},54:{n:"BrtBeginMdx"},55:{n:"BrtEndMdx"},56:{n:"BrtBeginMdxTuple"},57:{n:"BrtEndMdxTuple"},58:{n:"BrtMdxMbrIstr"},59:{n:"BrtStr"},60:{n:"BrtColInfo",f:Qs},62:{n:"BrtCellRString"},63:{n:"BrtCalcChainItem$",f:Po},64:{n:"BrtDVal",f:rv},65:{n:"BrtSxvcellNum"},66:{n:"BrtSxvcellStr"},67:{n:"BrtSxvcellBool"},68:{n:"BrtSxvcellErr"},69:{n:"BrtSxvcellDate"},70:{n:"BrtSxvcellNil"},128:{n:"BrtFileVersion"},129:{n:"BrtBeginSheet"},130:{n:"BrtEndSheet"},131:{n:"BrtBeginBook",f:Qr,p:0},132:{n:"BrtEndBook"},133:{n:"BrtBeginWsViews"},134:{n:"BrtEndWsViews"},135:{n:"BrtBeginBookViews"},136:{n:"BrtEndBookViews"},137:{n:"BrtBeginWsView",f:Jd},138:{n:"BrtEndWsView"},139:{n:"BrtBeginCsViews"},140:{n:"BrtEndCsViews"},141:{n:"BrtBeginCsView"},142:{n:"BrtEndCsView"},143:{n:"BrtBeginBundleShs"},144:{n:"BrtEndBundleShs"},145:{n:"BrtBeginSheetData"},146:{n:"BrtEndSheetData"},147:{n:"BrtWsProp",f:id},148:{n:"BrtWsDim",f:td,p:16},151:{n:"BrtPane",f:Gd},152:{n:"BrtSel"},153:{n:"BrtWbProp",f:Vv},154:{n:"BrtWbFactoid"},155:{n:"BrtFileRecover"},156:{n:"BrtBundleSh",f:Hv},157:{n:"BrtCalcProp"},158:{n:"BrtBookView"},159:{n:"BrtBeginSst",f:Of},160:{n:"BrtEndSst"},161:{n:"BrtBeginAFilter",f:Qt},162:{n:"BrtEndAFilter"},163:{n:"BrtBeginFilterColumn"},164:{n:"BrtEndFilterColumn"},165:{n:"BrtBeginFilters"},166:{n:"BrtEndFilters"},167:{n:"BrtFilter"},168:{n:"BrtColorFilter"},169:{n:"BrtIconFilter"},170:{n:"BrtTop10Filter"},171:{n:"BrtDynamicFilter"},172:{n:"BrtBeginCustomFilters"},173:{n:"BrtEndCustomFilters"},174:{n:"BrtCustomFilter"},175:{n:"BrtAFilterDateGroupItem"},176:{n:"BrtMergeCell",f:Ud},177:{n:"BrtBeginMergeCells"},178:{n:"BrtEndMergeCells"},179:{n:"BrtBeginPivotCacheDef"},180:{n:"BrtEndPivotCacheDef"},181:{n:"BrtBeginPCDFields"},182:{n:"BrtEndPCDFields"},183:{n:"BrtBeginPCDField"},184:{n:"BrtEndPCDField"},185:{n:"BrtBeginPCDSource"},186:{n:"BrtEndPCDSource"},187:{n:"BrtBeginPCDSRange"},188:{n:"BrtEndPCDSRange"},189:{n:"BrtBeginPCDFAtbl"},190:{n:"BrtEndPCDFAtbl"},191:{n:"BrtBeginPCDIRun"},192:{n:"BrtEndPCDIRun"},193:{n:"BrtBeginPivotCacheRecords"},194:{n:"BrtEndPivotCacheRecords"},195:{n:"BrtBeginPCDHierarchies"},196:{n:"BrtEndPCDHierarchies"},197:{n:"BrtBeginPCDHierarchy"},198:{n:"BrtEndPCDHierarchy"},199:{n:"BrtBeginPCDHFieldsUsage"},200:{n:"BrtEndPCDHFieldsUsage"},201:{n:"BrtBeginExtConnection"},202:{n:"BrtEndExtConnection"},203:{n:"BrtBeginECDbProps"},204:{n:"BrtEndECDbProps"},205:{n:"BrtBeginECOlapProps"},206:{n:"BrtEndECOlapProps"},207:{n:"BrtBeginPCDSConsol"},208:{n:"BrtEndPCDSConsol"},209:{n:"BrtBeginPCDSCPages"},210:{n:"BrtEndPCDSCPages"},211:{n:"BrtBeginPCDSCPage"},212:{n:"BrtEndPCDSCPage"},213:{n:"BrtBeginPCDSCPItem"},214:{n:"BrtEndPCDSCPItem"},215:{n:"BrtBeginPCDSCSets"},216:{n:"BrtEndPCDSCSets"},217:{n:"BrtBeginPCDSCSet"},218:{n:"BrtEndPCDSCSet"},219:{n:"BrtBeginPCDFGroup"},220:{n:"BrtEndPCDFGroup"},221:{n:"BrtBeginPCDFGItems"},222:{n:"BrtEndPCDFGItems"},223:{n:"BrtBeginPCDFGRange"},224:{n:"BrtEndPCDFGRange"},225:{n:"BrtBeginPCDFGDiscrete"},226:{n:"BrtEndPCDFGDiscrete"},227:{n:"BrtBeginPCDSDTupleCache"},228:{n:"BrtEndPCDSDTupleCache"},229:{n:"BrtBeginPCDSDTCEntries"},230:{n:"BrtEndPCDSDTCEntries"},231:{n:"BrtBeginPCDSDTCEMembers"},232:{n:"BrtEndPCDSDTCEMembers"},233:{n:"BrtBeginPCDSDTCEMember"},234:{n:"BrtEndPCDSDTCEMember"},235:{n:"BrtBeginPCDSDTCQueries"},236:{n:"BrtEndPCDSDTCQueries"},237:{n:"BrtBeginPCDSDTCQuery"},238:{n:"BrtEndPCDSDTCQuery"},239:{n:"BrtBeginPCDSDTCSets"},240:{n:"BrtEndPCDSDTCSets"},241:{n:"BrtBeginPCDSDTCSet"},242:{n:"BrtEndPCDSDTCSet"},243:{n:"BrtBeginPCDCalcItems"},244:{n:"BrtEndPCDCalcItems"},245:{n:"BrtBeginPCDCalcItem"},246:{n:"BrtEndPCDCalcItem"},247:{n:"BrtBeginPRule"},248:{n:"BrtEndPRule"},249:{n:"BrtBeginPRFilters"},250:{n:"BrtEndPRFilters"},251:{n:"BrtBeginPRFilter"},252:{n:"BrtEndPRFilter"},253:{n:"BrtBeginPNames"},254:{
n:"BrtEndPNames"},255:{n:"BrtBeginPName"},256:{n:"BrtEndPName"},257:{n:"BrtBeginPNPairs"},258:{n:"BrtEndPNPairs"},259:{n:"BrtBeginPNPair"},260:{n:"BrtEndPNPair"},261:{n:"BrtBeginECWebProps"},262:{n:"BrtEndECWebProps"},263:{n:"BrtBeginEcWpTables"},264:{n:"BrtEndECWPTables"},265:{n:"BrtBeginECParams"},266:{n:"BrtEndECParams"},267:{n:"BrtBeginECParam"},268:{n:"BrtEndECParam"},269:{n:"BrtBeginPCDKPIs"},270:{n:"BrtEndPCDKPIs"},271:{n:"BrtBeginPCDKPI"},272:{n:"BrtEndPCDKPI"},273:{n:"BrtBeginDims"},274:{n:"BrtEndDims"},275:{n:"BrtBeginDim"},276:{n:"BrtEndDim"},277:{n:"BrtIndexPartEnd"},278:{n:"BrtBeginStyleSheet"},279:{n:"BrtEndStyleSheet"},280:{n:"BrtBeginSXView"},281:{n:"BrtEndSXVI"},282:{n:"BrtBeginSXVI"},283:{n:"BrtBeginSXVIs"},284:{n:"BrtEndSXVIs"},285:{n:"BrtBeginSXVD"},286:{n:"BrtEndSXVD"},287:{n:"BrtBeginSXVDs"},288:{n:"BrtEndSXVDs"},289:{n:"BrtBeginSXPI"},290:{n:"BrtEndSXPI"},291:{n:"BrtBeginSXPIs"},292:{n:"BrtEndSXPIs"},293:{n:"BrtBeginSXDI"},294:{n:"BrtEndSXDI"},295:{n:"BrtBeginSXDIs"},296:{n:"BrtEndSXDIs"},297:{n:"BrtBeginSXLI"},298:{n:"BrtEndSXLI"},299:{n:"BrtBeginSXLIRws"},300:{n:"BrtEndSXLIRws"},301:{n:"BrtBeginSXLICols"},302:{n:"BrtEndSXLICols"},303:{n:"BrtBeginSXFormat"},304:{n:"BrtEndSXFormat"},305:{n:"BrtBeginSXFormats"},306:{n:"BrtEndSxFormats"},307:{n:"BrtBeginSxSelect"},308:{n:"BrtEndSxSelect"},309:{n:"BrtBeginISXVDRws"},310:{n:"BrtEndISXVDRws"},311:{n:"BrtBeginISXVDCols"},312:{n:"BrtEndISXVDCols"},313:{n:"BrtEndSXLocation"},314:{n:"BrtBeginSXLocation"},315:{n:"BrtEndSXView"},316:{n:"BrtBeginSXTHs"},317:{n:"BrtEndSXTHs"},318:{n:"BrtBeginSXTH"},319:{n:"BrtEndSXTH"},320:{n:"BrtBeginISXTHRws"},321:{n:"BrtEndISXTHRws"},322:{n:"BrtBeginISXTHCols"},323:{n:"BrtEndISXTHCols"},324:{n:"BrtBeginSXTDMPS"},325:{n:"BrtEndSXTDMPs"},326:{n:"BrtBeginSXTDMP"},327:{n:"BrtEndSXTDMP"},328:{n:"BrtBeginSXTHItems"},329:{n:"BrtEndSXTHItems"},330:{n:"BrtBeginSXTHItem"},331:{n:"BrtEndSXTHItem"},332:{n:"BrtBeginMetadata"},333:{n:"BrtEndMetadata"},334:{n:"BrtBeginEsmdtinfo"},335:{n:"BrtMdtinfo"},336:{n:"BrtEndEsmdtinfo"},337:{n:"BrtBeginEsmdb"},338:{n:"BrtEndEsmdb"},339:{n:"BrtBeginEsfmd"},340:{n:"BrtEndEsfmd"},341:{n:"BrtBeginSingleCells"},342:{n:"BrtEndSingleCells"},343:{n:"BrtBeginList"},344:{n:"BrtEndList"},345:{n:"BrtBeginListCols"},346:{n:"BrtEndListCols"},347:{n:"BrtBeginListCol"},348:{n:"BrtEndListCol"},349:{n:"BrtBeginListXmlCPr"},350:{n:"BrtEndListXmlCPr"},351:{n:"BrtListCCFmla"},352:{n:"BrtListTrFmla"},353:{n:"BrtBeginExternals"},354:{n:"BrtEndExternals"},355:{n:"BrtSupBookSrc",f:Gt},357:{n:"BrtSupSelf"},358:{n:"BrtSupSame"},359:{n:"BrtSupTabs"},360:{n:"BrtBeginSupBook"},361:{n:"BrtPlaceholderName"},362:{n:"BrtExternSheet",f:xs},363:{n:"BrtExternTableStart"},364:{n:"BrtExternTableEnd"},366:{n:"BrtExternRowHdr"},367:{n:"BrtExternCellBlank"},368:{n:"BrtExternCellReal"},369:{n:"BrtExternCellBool"},370:{n:"BrtExternCellError"},371:{n:"BrtExternCellString"},372:{n:"BrtBeginEsmdx"},373:{n:"BrtEndEsmdx"},374:{n:"BrtBeginMdxSet"},375:{n:"BrtEndMdxSet"},376:{n:"BrtBeginMdxMbrProp"},377:{n:"BrtEndMdxMbrProp"},378:{n:"BrtBeginMdxKPI"},379:{n:"BrtEndMdxKPI"},380:{n:"BrtBeginEsstr"},381:{n:"BrtEndEsstr"},382:{n:"BrtBeginPRFItem"},383:{n:"BrtEndPRFItem"},384:{n:"BrtBeginPivotCacheIDs"},385:{n:"BrtEndPivotCacheIDs"},386:{n:"BrtBeginPivotCacheID"},387:{n:"BrtEndPivotCacheID"},388:{n:"BrtBeginISXVIs"},389:{n:"BrtEndISXVIs"},390:{n:"BrtBeginColInfos"},391:{n:"BrtEndColInfos"},392:{n:"BrtBeginRwBrk"},393:{n:"BrtEndRwBrk"},394:{n:"BrtBeginColBrk"},395:{n:"BrtEndColBrk"},396:{n:"BrtBrk"},397:{n:"BrtUserBookView"},398:{n:"BrtInfo"},399:{n:"BrtCUsr"},400:{n:"BrtUsr"},401:{n:"BrtBeginUsers"},403:{n:"BrtEOF"},404:{n:"BrtUCR"},405:{n:"BrtRRInsDel"},406:{n:"BrtRREndInsDel"},407:{n:"BrtRRMove"},408:{n:"BrtRREndMove"},409:{n:"BrtRRChgCell"},410:{n:"BrtRREndChgCell"},411:{n:"BrtRRHeader"},412:{n:"BrtRRUserView"},413:{n:"BrtRRRenSheet"},414:{n:"BrtRRInsertSh"},415:{n:"BrtRRDefName"},416:{n:"BrtRRNote"},417:{n:"BrtRRConflict"},418:{n:"BrtRRTQSIF"},419:{n:"BrtRRFormat"},420:{n:"BrtRREndFormat"},421:{n:"BrtRRAutoFmt"},422:{n:"BrtBeginUserShViews"},423:{n:"BrtBeginUserShView"},424:{n:"BrtEndUserShView"},425:{n:"BrtEndUserShViews"},426:{n:"BrtArrFmla",f:zd},427:{n:"BrtShrFmla",f:jd},428:{n:"BrtTable"},429:{n:"BrtBeginExtConnections"},430:{n:"BrtEndExtConnections"},431:{n:"BrtBeginPCDCalcMems"},432:{n:"BrtEndPCDCalcMems"},433:{n:"BrtBeginPCDCalcMem"},434:{n:"BrtEndPCDCalcMem"},435:{n:"BrtBeginPCDHGLevels"},436:{n:"BrtEndPCDHGLevels"},437:{n:"BrtBeginPCDHGLevel"},438:{n:"BrtEndPCDHGLevel"},439:{n:"BrtBeginPCDHGLGroups"},440:{n:"BrtEndPCDHGLGroups"},441:{n:"BrtBeginPCDHGLGroup"},442:{n:"BrtEndPCDHGLGroup"},443:{n:"BrtBeginPCDHGLGMembers"},444:{n:"BrtEndPCDHGLGMembers"},445:{n:"BrtBeginPCDHGLGMember"},446:{n:"BrtEndPCDHGLGMember"},447:{n:"BrtBeginQSI"},448:{n:"BrtEndQSI"},449:{n:"BrtBeginQSIR"},450:{n:"BrtEndQSIR"},451:{n:"BrtBeginDeletedNames"},452:{n:"BrtEndDeletedNames"},453:{n:"BrtBeginDeletedName"},454:{n:"BrtEndDeletedName"},455:{n:"BrtBeginQSIFs"},456:{n:"BrtEndQSIFs"},457:{n:"BrtBeginQSIF"},458:{n:"BrtEndQSIF"},459:{n:"BrtBeginAutoSortScope"},460:{n:"BrtEndAutoSortScope"},461:{n:"BrtBeginConditionalFormatting"},462:{n:"BrtEndConditionalFormatting"},463:{n:"BrtBeginCFRule"},464:{n:"BrtEndCFRule"},465:{n:"BrtBeginIconSet"},466:{n:"BrtEndIconSet"},467:{n:"BrtBeginDatabar"},468:{n:"BrtEndDatabar"},469:{n:"BrtBeginColorScale"},470:{n:"BrtEndColorScale"},471:{n:"BrtCFVO"},472:{n:"BrtExternValueMeta"},473:{n:"BrtBeginColorPalette"},474:{n:"BrtEndColorPalette"},475:{n:"BrtIndexedColor"},476:{n:"BrtMargins",f:Yd},477:{n:"BrtPrintOptions"},478:{n:"BrtPageSetup"},479:{n:"BrtBeginHeaderFooter"},480:{n:"BrtEndHeaderFooter"},481:{n:"BrtBeginSXCrtFormat"},482:{n:"BrtEndSXCrtFormat"},483:{n:"BrtBeginSXCrtFormats"},484:{n:"BrtEndSXCrtFormats"},485:{n:"BrtWsFmtInfo",f:nd},486:{n:"BrtBeginMgs"},487:{n:"BrtEndMGs"},488:{n:"BrtBeginMGMaps"},489:{n:"BrtEndMGMaps"},490:{n:"BrtBeginMG"},491:{n:"BrtEndMG"},492:{n:"BrtBeginMap"},493:{n:"BrtEndMap"},494:{n:"BrtHLink",f:Vd},495:{n:"BrtBeginDCon"},496:{n:"BrtEndDCon"},497:{n:"BrtBeginDRefs"},498:{n:"BrtEndDRefs"},499:{n:"BrtDRef"},500:{n:"BrtBeginScenMan"},501:{n:"BrtEndScenMan"},502:{n:"BrtBeginSct"},503:{n:"BrtEndSct"},504:{n:"BrtSlc"},505:{n:"BrtBeginDXFs"},506:{n:"BrtEndDXFs"},507:{n:"BrtDXF"},508:{n:"BrtBeginTableStyles"},509:{n:"BrtEndTableStyles"},510:{n:"BrtBeginTableStyle"},511:{n:"BrtEndTableStyle"},512:{n:"BrtTableStyleElement"},513:{n:"BrtTableStyleClient"},514:{n:"BrtBeginVolDeps"},515:{n:"BrtEndVolDeps"},516:{n:"BrtBeginVolType"},517:{n:"BrtEndVolType"},518:{n:"BrtBeginVolMain"},519:{n:"BrtEndVolMain"},520:{n:"BrtBeginVolTopic"},521:{n:"BrtEndVolTopic"},522:{n:"BrtVolSubtopic"},523:{n:"BrtVolRef"},524:{n:"BrtVolNum"},525:{n:"BrtVolErr"},526:{n:"BrtVolStr"},527:{n:"BrtVolBool"},528:{n:"BrtBeginCalcChain$"},529:{n:"BrtEndCalcChain$"},530:{n:"BrtBeginSortState"},531:{n:"BrtEndSortState"},532:{n:"BrtBeginSortCond"},533:{n:"BrtEndSortCond"},534:{n:"BrtBookProtection"},535:{n:"BrtSheetProtection"},536:{n:"BrtRangeProtection"},537:{n:"BrtPhoneticInfo"},538:{n:"BrtBeginECTxtWiz"},539:{n:"BrtEndECTxtWiz"},540:{n:"BrtBeginECTWFldInfoLst"},541:{n:"BrtEndECTWFldInfoLst"},542:{n:"BrtBeginECTwFldInfo"},548:{n:"BrtFileSharing"},549:{n:"BrtOleSize"},550:{n:"BrtDrawing",f:Gt},551:{n:"BrtLegacyDrawing"},552:{n:"BrtLegacyDrawingHF"},553:{n:"BrtWebOpt"},554:{n:"BrtBeginWebPubItems"},555:{n:"BrtEndWebPubItems"},556:{n:"BrtBeginWebPubItem"},557:{n:"BrtEndWebPubItem"},558:{n:"BrtBeginSXCondFmt"},559:{n:"BrtEndSXCondFmt"},560:{n:"BrtBeginSXCondFmts"},561:{n:"BrtEndSXCondFmts"},562:{n:"BrtBkHim"},564:{n:"BrtColor"},565:{n:"BrtBeginIndexedColors"},566:{n:"BrtEndIndexedColors"},569:{n:"BrtBeginMRUColors"},570:{n:"BrtEndMRUColors"},572:{n:"BrtMRUColor"},573:{n:"BrtBeginDVals"},574:{n:"BrtEndDVals"},577:{n:"BrtSupNameStart"},578:{n:"BrtSupNameValueStart"},579:{n:"BrtSupNameValueEnd"},580:{n:"BrtSupNameNum"},581:{n:"BrtSupNameErr"},582:{n:"BrtSupNameSt"},583:{n:"BrtSupNameNil"},584:{n:"BrtSupNameBool"},585:{n:"BrtSupNameFmla"},586:{n:"BrtSupNameBits"},587:{n:"BrtSupNameEnd"},588:{n:"BrtEndSupBook"},589:{n:"BrtCellSmartTagProperty"},590:{n:"BrtBeginCellSmartTag"},591:{n:"BrtEndCellSmartTag"},592:{n:"BrtBeginCellSmartTags"},593:{n:"BrtEndCellSmartTags"},594:{n:"BrtBeginSmartTags"},595:{n:"BrtEndSmartTags"},596:{n:"BrtSmartTagType"},597:{n:"BrtBeginSmartTagTypes"},598:{n:"BrtEndSmartTagTypes"},599:{n:"BrtBeginSXFilters"},600:{n:"BrtEndSXFilters"},601:{n:"BrtBeginSXFILTER"},602:{n:"BrtEndSXFilter"},603:{n:"BrtBeginFills"},604:{n:"BrtEndFills"},605:{n:"BrtBeginCellWatches"},606:{n:"BrtEndCellWatches"},607:{n:"BrtCellWatch"},608:{n:"BrtBeginCRErrs"},609:{n:"BrtEndCRErrs"},610:{n:"BrtCrashRecErr"},611:{n:"BrtBeginFonts"},612:{n:"BrtEndFonts"},613:{n:"BrtBeginBorders"},614:{n:"BrtEndBorders"},615:{n:"BrtBeginFmts"},616:{n:"BrtEndFmts"},617:{n:"BrtBeginCellXFs"},618:{n:"BrtEndCellXFs"},619:{n:"BrtBeginStyles"},620:{n:"BrtEndStyles"},625:{n:"BrtBigName"},626:{n:"BrtBeginCellStyleXFs"},627:{n:"BrtEndCellStyleXFs"},628:{n:"BrtBeginComments"},629:{n:"BrtEndComments"},630:{n:"BrtBeginCommentAuthors"},631:{n:"BrtEndCommentAuthors"},632:{n:"BrtCommentAuthor",f:Ko},633:{n:"BrtBeginCommentList"},634:{n:"BrtEndCommentList"},635:{n:"BrtBeginComment",f:jo},636:{n:"BrtEndComment"},637:{n:"BrtCommentText",f:Dt},638:{n:"BrtBeginOleObjects"},639:{n:"BrtOleObject"},640:{n:"BrtEndOleObjects"},641:{n:"BrtBeginSxrules"},642:{n:"BrtEndSxRules"},643:{n:"BrtBeginActiveXControls"},644:{n:"BrtActiveX"},645:{n:"BrtEndActiveXControls"},646:{n:"BrtBeginPCDSDTCEMembersSortBy"},648:{n:"BrtBeginCellIgnoreECs"},649:{n:"BrtCellIgnoreEC"},650:{n:"BrtEndCellIgnoreECs"},651:{n:"BrtCsProp",f:kv},652:{n:"BrtCsPageSetup"},653:{n:"BrtBeginUserCsViews"},654:{n:"BrtEndUserCsViews"},655:{n:"BrtBeginUserCsView"},656:{n:"BrtEndUserCsView"},657:{n:"BrtBeginPcdSFCIEntries"},658:{n:"BrtEndPCDSFCIEntries"},659:{n:"BrtPCDSFCIEntry"},660:{n:"BrtBeginListParts"},661:{n:"BrtListPart"},662:{n:"BrtEndListParts"},663:{n:"BrtSheetCalcProp"},664:{n:"BrtBeginFnGroup"},665:{n:"BrtFnGroup"},666:{n:"BrtEndFnGroup"},667:{n:"BrtSupAddin"},668:{n:"BrtSXTDMPOrder"},669:{n:"BrtCsProtection"},671:{n:"BrtBeginWsSortMap"},672:{n:"BrtEndWsSortMap"},673:{n:"BrtBeginRRSort"},674:{n:"BrtEndRRSort"},675:{n:"BrtRRSortItem"},676:{n:"BrtFileSharingIso"},677:{n:"BrtBookProtectionIso"},678:{n:"BrtSheetProtectionIso"},679:{n:"BrtCsProtectionIso"},680:{n:"BrtRangeProtectionIso"},681:{n:"BrtDValList"},1024:{n:"BrtRwDescent"},1025:{n:"BrtKnownFonts"},1026:{n:"BrtBeginSXTupleSet"},1027:{n:"BrtEndSXTupleSet"},1028:{n:"BrtBeginSXTupleSetHeader"},1029:{n:"BrtEndSXTupleSetHeader"},1030:{n:"BrtSXTupleSetHeaderItem"},1031:{n:"BrtBeginSXTupleSetData"},1032:{n:"BrtEndSXTupleSetData"},1033:{n:"BrtBeginSXTupleSetRow"},1034:{n:"BrtEndSXTupleSetRow"},1035:{n:"BrtSXTupleSetRowItem"},1036:{n:"BrtNameExt"},1037:{n:"BrtPCDH14"},1038:{n:"BrtBeginPCDCalcMem14"},1039:{n:"BrtEndPCDCalcMem14"},1040:{n:"BrtSXTH14"},1041:{n:"BrtBeginSparklineGroup"},1042:{n:"BrtEndSparklineGroup"},1043:{n:"BrtSparkline"},1044:{n:"BrtSXDI14"},1045:{n:"BrtWsFmtInfoEx14"},1046:{n:"BrtBeginConditionalFormatting14"},1047:{n:"BrtEndConditionalFormatting14"},1048:{n:"BrtBeginCFRule14"},1049:{n:"BrtEndCFRule14"},1050:{n:"BrtCFVO14"},1051:{n:"BrtBeginDatabar14"},1052:{n:"BrtBeginIconSet14"},1053:{n:"BrtDVal14",f:tv},1054:{n:"BrtBeginDVals14"},1055:{n:"BrtColor14"},1056:{n:"BrtBeginSparklines"},1057:{n:"BrtEndSparklines"},1058:{n:"BrtBeginSparklineGroups"},1059:{n:"BrtEndSparklineGroups"},1061:{n:"BrtSXVD14"},1062:{n:"BrtBeginSXView14"},1063:{n:"BrtEndSXView14"},1064:{n:"BrtBeginSXView16"},1065:{n:"BrtEndSXView16"},1066:{n:"BrtBeginPCD14"},1067:{n:"BrtEndPCD14"},1068:{n:"BrtBeginExtConn14"},1069:{n:"BrtEndExtConn14"},1070:{n:"BrtBeginSlicerCacheIDs"},1071:{n:"BrtEndSlicerCacheIDs"},1072:{n:"BrtBeginSlicerCacheID"},1073:{n:"BrtEndSlicerCacheID"},1075:{n:"BrtBeginSlicerCache"},1076:{n:"BrtEndSlicerCache"},1077:{n:"BrtBeginSlicerCacheDef"},1078:{n:"BrtEndSlicerCacheDef"},1079:{n:"BrtBeginSlicersEx"},1080:{n:"BrtEndSlicersEx"},1081:{n:"BrtBeginSlicerEx"},1082:{n:"BrtEndSlicerEx"},1083:{n:"BrtBeginSlicer"},1084:{n:"BrtEndSlicer"},1085:{n:"BrtSlicerCachePivotTables"},1086:{n:"BrtBeginSlicerCacheOlapImpl"},1087:{n:"BrtEndSlicerCacheOlapImpl"},1088:{n:"BrtBeginSlicerCacheLevelsData"},1089:{n:"BrtEndSlicerCacheLevelsData"},1090:{n:"BrtBeginSlicerCacheLevelData"},1091:{n:"BrtEndSlicerCacheLevelData"},1092:{n:"BrtBeginSlicerCacheSiRanges"},1093:{n:"BrtEndSlicerCacheSiRanges"},1094:{n:"BrtBeginSlicerCacheSiRange"},1095:{n:"BrtEndSlicerCacheSiRange"},1096:{n:"BrtSlicerCacheOlapItem"},1097:{n:"BrtBeginSlicerCacheSelections"},1098:{n:"BrtSlicerCacheSelection"},1099:{n:"BrtEndSlicerCacheSelections"},1100:{n:"BrtBeginSlicerCacheNative"},1101:{n:"BrtEndSlicerCacheNative"},1102:{n:"BrtSlicerCacheNativeItem"},1103:{n:"BrtRangeProtection14"},1104:{n:"BrtRangeProtectionIso14"},1105:{n:"BrtCellIgnoreEC14"},1111:{n:"BrtList14"},1112:{n:"BrtCFIcon"},1113:{n:"BrtBeginSlicerCachesPivotCacheIDs"},1114:{n:"BrtEndSlicerCachesPivotCacheIDs"},1115:{n:"BrtBeginSlicers"},1116:{n:"BrtEndSlicers"},1117:{n:"BrtWbProp14"},1118:{n:"BrtBeginSXEdit"},1119:{n:"BrtEndSXEdit"},1120:{n:"BrtBeginSXEdits"},1121:{n:"BrtEndSXEdits"},1122:{n:"BrtBeginSXChange"},1123:{n:"BrtEndSXChange"},1124:{n:"BrtBeginSXChanges"},1125:{n:"BrtEndSXChanges"},1126:{n:"BrtSXTupleItems"},1128:{n:"BrtBeginSlicerStyle"},1129:{n:"BrtEndSlicerStyle"},1130:{n:"BrtSlicerStyleElement"},1131:{n:"BrtBeginStyleSheetExt14"},1132:{n:"BrtEndStyleSheetExt14"},1133:{n:"BrtBeginSlicerCachesPivotCacheID"},1134:{n:"BrtEndSlicerCachesPivotCacheID"},1135:{n:"BrtBeginConditionalFormattings"},1136:{n:"BrtEndConditionalFormattings"},1137:{n:"BrtBeginPCDCalcMemExt"},1138:{n:"BrtEndPCDCalcMemExt"},1139:{n:"BrtBeginPCDCalcMemsExt"},1140:{n:"BrtEndPCDCalcMemsExt"},1141:{n:"BrtPCDField14"},1142:{n:"BrtBeginSlicerStyles"},1143:{n:"BrtEndSlicerStyles"},1144:{n:"BrtBeginSlicerStyleElements"},1145:{n:"BrtEndSlicerStyleElements"},1146:{n:"BrtCFRuleExt"},1147:{n:"BrtBeginSXCondFmt14"},1148:{n:"BrtEndSXCondFmt14"},1149:{n:"BrtBeginSXCondFmts14"},1150:{n:"BrtEndSXCondFmts14"},1152:{n:"BrtBeginSortCond14"},1153:{n:"BrtEndSortCond14"},1154:{n:"BrtEndDVals14"},1155:{n:"BrtEndIconSet14"},1156:{n:"BrtEndDatabar14"},1157:{n:"BrtBeginColorScale14"},1158:{n:"BrtEndColorScale14"},1159:{n:"BrtBeginSxrules14"},1160:{n:"BrtEndSxrules14"},1161:{n:"BrtBeginPRule14"},1162:{n:"BrtEndPRule14"},1163:{n:"BrtBeginPRFilters14"},1164:{n:"BrtEndPRFilters14"},1165:{n:"BrtBeginPRFilter14"},1166:{n:"BrtEndPRFilter14"},1167:{n:"BrtBeginPRFItem14"},1168:{n:"BrtEndPRFItem14"},1169:{n:"BrtBeginCellIgnoreECs14"},1170:{n:"BrtEndCellIgnoreECs14"},1171:{n:"BrtDxf14"},1172:{n:"BrtBeginDxF14s"},1173:{n:"BrtEndDxf14s"},1177:{n:"BrtFilter14"},1178:{n:"BrtBeginCustomFilters14"},1180:{n:"BrtCustomFilter14"},1181:{n:"BrtIconFilter14"},1182:{n:"BrtPivotCacheConnectionName"},2048:{n:"BrtBeginDecoupledPivotCacheIDs"},2049:{n:"BrtEndDecoupledPivotCacheIDs"},2050:{n:"BrtDecoupledPivotCacheID"},2051:{n:"BrtBeginPivotTableRefs"},2052:{n:"BrtEndPivotTableRefs"},2053:{n:"BrtPivotTableRef"},2054:{n:"BrtSlicerCacheBookPivotTables"},2055:{n:"BrtBeginSxvcells"},2056:{n:"BrtEndSxvcells"},2057:{n:"BrtBeginSxRow"},2058:{n:"BrtEndSxRow"},2060:{n:"BrtPcdCalcMem15"},2067:{n:"BrtQsi15"},2068:{n:"BrtBeginWebExtensions"},2069:{n:"BrtEndWebExtensions"},2070:{n:"BrtWebExtension"},2071:{n:"BrtAbsPath15"},2072:{n:"BrtBeginPivotTableUISettings"},2073:{n:"BrtEndPivotTableUISettings"},2075:{n:"BrtTableSlicerCacheIDs"},2076:{n:"BrtTableSlicerCacheID"},2077:{n:"BrtBeginTableSlicerCache"},2078:{n:"BrtEndTableSlicerCache"},2079:{n:"BrtSxFilter15"},2080:{n:"BrtBeginTimelineCachePivotCacheIDs"},2081:{n:"BrtEndTimelineCachePivotCacheIDs"},2082:{n:"BrtTimelineCachePivotCacheID"},2083:{n:"BrtBeginTimelineCacheIDs"},2084:{n:"BrtEndTimelineCacheIDs"},2085:{n:"BrtBeginTimelineCacheID"},2086:{n:"BrtEndTimelineCacheID"},2087:{n:"BrtBeginTimelinesEx"},2088:{n:"BrtEndTimelinesEx"},2089:{n:"BrtBeginTimelineEx"},2090:{n:"BrtEndTimelineEx"},2091:{n:"BrtWorkBookPr15"},2092:{n:"BrtPCDH15"},2093:{n:"BrtBeginTimelineStyle"},2094:{n:"BrtEndTimelineStyle"},2095:{n:"BrtTimelineStyleElement"},2096:{n:"BrtBeginTimelineStylesheetExt15"},2097:{n:"BrtEndTimelineStylesheetExt15"},2098:{n:"BrtBeginTimelineStyles"},2099:{n:"BrtEndTimelineStyles"},2100:{n:"BrtBeginTimelineStyleElements"},2101:{n:"BrtEndTimelineStyleElements"},2102:{n:"BrtDxf15"},2103:{n:"BrtBeginDxfs15"},2104:{n:"brtEndDxfs15"},2105:{n:"BrtSlicerCacheHideItemsWithNoData"},2106:{n:"BrtBeginItemUniqueNames"},2107:{n:"BrtEndItemUniqueNames"},2108:{n:"BrtItemUniqueName"},2109:{n:"BrtBeginExtConn15"},2110:{n:"BrtEndExtConn15"},2111:{n:"BrtBeginOledbPr15"},2112:{n:"BrtEndOledbPr15"},2113:{n:"BrtBeginDataFeedPr15"},2114:{n:"BrtEndDataFeedPr15"},2115:{n:"BrtTextPr15"},2116:{n:"BrtRangePr15"},2117:{n:"BrtDbCommand15"},2118:{n:"BrtBeginDbTables15"},2119:{n:"BrtEndDbTables15"},2120:{n:"BrtDbTable15"},2121:{n:"BrtBeginDataModel"},2122:{n:"BrtEndDataModel"},2123:{n:"BrtBeginModelTables"},2124:{n:"BrtEndModelTables"},2125:{n:"BrtModelTable"},2126:{n:"BrtBeginModelRelationships"},2127:{n:"BrtEndModelRelationships"},2128:{n:"BrtModelRelationship"},2129:{n:"BrtBeginECTxtWiz15"},2130:{n:"BrtEndECTxtWiz15"},2131:{n:"BrtBeginECTWFldInfoLst15"},2132:{n:"BrtEndECTWFldInfoLst15"},2133:{n:"BrtBeginECTWFldInfo15"},2134:{n:"BrtFieldListActiveItem"},2135:{n:"BrtPivotCacheIdVersion"},2136:{n:"BrtSXDI15"},2137:{n:"BrtBeginModelTimeGroupings"},2138:{n:"BrtEndModelTimeGroupings"},2139:{n:"BrtBeginModelTimeGrouping"},2140:{n:"BrtEndModelTimeGrouping"},2141:{n:"BrtModelTimeGroupingCalcCol"},3072:{n:"BrtUid"},3073:{n:"BrtRevisionPtr"},5095:{n:"BrtBeginCalcFeatures"},5096:{n:"BrtEndCalcFeatures"},5097:{n:"BrtCalcFeature"},65535:{n:""}};var Zp=Y(qp,"n");Zp["BrtFRTArchID$"]=16;var eb={6:{n:"Formula",f:Yu},10:{n:"EOF",f:Xn},12:{n:"CalcCount",f:Kn},13:{n:"CalcMode",f:Kn},14:{n:"CalcPrecision",f:jn},15:{n:"CalcRefMode",f:jn},16:{n:"CalcDelta",f:qt},17:{n:"CalcIter",f:jn},18:{n:"Protect",f:jn},19:{n:"Password",f:Kn},20:{n:"Header",f:Ss},21:{n:"Footer",f:Ss},23:{n:"ExternSheet",f:xs},24:{n:"Lbl",f:_s},25:{n:"WinProtect",f:jn},26:{n:"VerticalPageBreaks"},27:{n:"HorizontalPageBreaks"},28:{n:"Note",f:Os},29:{n:"Selection"},34:{n:"Date1904",f:jn},35:{n:"ExternName",f:Cs},38:{n:"LeftMargin",f:qt},39:{n:"RightMargin",f:qt},40:{n:"TopMargin",f:qt},41:{n:"BottomMargin",f:qt},42:{n:"PrintRowCol",f:jn},43:{n:"PrintGrid",f:jn},47:{n:"FilePass",f:fl},49:{n:"Font",f:Zi},51:{n:"PrintSize",f:Kn},60:{n:"Continue"},61:{n:"Window1",f:Ki},64:{n:"Backup",f:jn},65:{n:"Pane",f:qi},66:{n:"CodePage",f:Kn},77:{n:"Pls"},80:{n:"DCon"},81:{n:"DConRef"},82:{n:"DConName"},85:{n:"DefColWidth",f:Kn},89:{n:"XCT"},90:{n:"CRN"},91:{n:"FileSharing"},92:{n:"WriteAccess",f:Ni},93:{n:"Obj",f:Ms},94:{n:"Uncalced"},95:{n:"CalcSaveRecalc",f:jn},96:{n:"Template"},97:{n:"Intl"},99:{n:"ObjProtect",f:jn},125:{n:"ColInfo",f:Qs},128:{n:"Guts",f:bs},129:{n:"WsBool",f:Li},130:{n:"GridSet",f:Kn},131:{n:"HCenter",f:jn},132:{n:"VCenter",f:jn},133:{n:"BoundSheet8",f:Ui},134:{n:"WriteProtect"},140:{n:"Country",f:zs},141:{n:"HideObj",f:Kn},144:{n:"Sort"},146:{n:"Palette",f:Ks},151:{n:"Sync"},152:{n:"LPr"},153:{n:"DxGCol"},154:{n:"FnGroupName"},155:{n:"FilterMode"},156:{n:"BuiltInFnGroupCount",f:Kn},157:{n:"AutoFilterInfo"},158:{n:"AutoFilter"},160:{n:"Scl",f:tf},161:{n:"Setup",f:qs},174:{n:"ScenMan"},175:{n:"SCENARIO"},176:{n:"SxView"},177:{n:"Sxvd"},178:{n:"SXVI"},180:{n:"SxIvd"},181:{n:"SXLI"},182:{n:"SXPI"},184:{n:"DocRoute"},185:{n:"RecipName"},189:{n:"MulRk",f:us},190:{n:"MulBlank",f:hs},193:{n:"Mms",f:Xn},197:{n:"SXDI"},198:{n:"SXDB"},199:{n:"SXFDB"},200:{n:"SXDBB"},201:{n:"SXNum"},202:{n:"SxBool",f:jn},203:{n:"SxErr"},204:{n:"SXInt"},205:{n:"SXString"},206:{n:"SXDtr"},207:{n:"SxNil"},208:{n:"SXTbl"},209:{n:"SXTBRGIITM"},210:{n:"SxTbpg"},211:{n:"ObProj"},213:{n:"SXStreamID"},215:{n:"DBCell"},216:{n:"SXRng"},217:{n:"SxIsxoper"},218:{n:"BookBool",f:Kn},220:{n:"DbOrParamQry"},221:{n:"ScenarioProtect",f:jn},222:{n:"OleObjectSize"},224:{n:"XF",f:vs},225:{n:"InterfaceHdr",f:Pi},226:{n:"InterfaceEnd",f:Xn},227:{n:"SXVS"},229:{n:"MergeCells",f:Ps},233:{n:"BkHim"},235:{n:"MsoDrawingGroup"},236:{n:"MsoDrawing"},237:{n:"MsoDrawingSelection"},239:{n:"PhoneticInfo"},240:{n:"SxRule"},241:{n:"SXEx"},242:{n:"SxFilt"},244:{n:"SxDXF"},245:{n:"SxItm"},246:{n:"SxName"},247:{n:"SxSelect"},248:{n:"SXPair"},249:{n:"SxFmla"},251:{n:"SxFormat"},252:{n:"SST",f:Wi},253:{n:"LabelSst",f:rs},255:{n:"ExtSST",f:Xi},256:{n:"SXVDEx"},259:{n:"SXFormula"},290:{n:"SXDBEx"},311:{n:"RRDInsDel"},312:{n:"RRDHead"},315:{n:"RRDChgCell"},317:{n:"RRTabId",f:Qn},318:{n:"RRDRenSheet"},319:{n:"RRSort"},320:{n:"RRDMove"},330:{n:"RRFormat"},331:{n:"RRAutoFmt"},333:{n:"RRInsertSh"},334:{n:"RRDMoveBegin"},335:{n:"RRDMoveEnd"},336:{n:"RRDInsDelBegin"},337:{n:"RRDInsDelEnd"},338:{n:"RRDConflict"},339:{n:"RRDDefName"},340:{n:"RRDRstEtxp"},351:{n:"LRng"},352:{n:"UsesELFs",f:jn},353:{n:"DSF",f:Xn},401:{n:"CUsr"},402:{n:"CbUsr"},403:{n:"UsrInfo"},404:{n:"UsrExcl"},405:{n:"FileLock"},406:{n:"RRDInfo"},407:{n:"BCUsrs"},408:{n:"UsrChk"},425:{n:"UserBView"},426:{n:"UserSViewBegin"},427:{n:"UserSViewEnd"},428:{n:"RRDUserView"},429:{n:"Qsi"},430:{n:"SupBook",f:Bs},431:{n:"Prot4Rev",f:jn},432:{n:"CondFmt"},433:{n:"CF"},434:{n:"DVal"},437:{n:"DConBin"},438:{n:"TxO",f:Hs},439:{n:"RefreshAll",f:jn},440:{n:"HLink",f:Ws},441:{n:"Lel"},442:{n:"CodeName",f:ai},443:{n:"SXFDBType"},444:{n:"Prot4RevPass",f:Kn},445:{n:"ObNoMacros"},446:{n:"Dv"},448:{n:"Excel9File",f:Xn},449:{n:"RecalcId",f:ji,r:2},450:{n:"EntExU2",f:Xn},512:{n:"Dimensions",f:ls},513:{n:"Blank",f:rf},515:{n:"Number",f:ws},516:{n:"Label",f:as},517:{n:"BoolErr",f:gs},519:{n:"String",f:af},520:{n:"Row",f:Gi},523:{n:"Index"},545:{n:"Array",f:Rs},549:{n:"DefaultRowHeight",f:$i},566:{n:"Table"},574:{n:"Window2",f:Qi},638:{n:"RK",f:cs},659:{n:"Style"},1048:{n:"BigName"},1054:{n:"Format",f:is},1084:{n:"ContinueBigName"},1212:{n:"ShrFmla",f:Is},2048:{n:"HLinkTooltip",f:Xs},2049:{n:"WebPub"},2050:{n:"QsiSXTag"},2051:{n:"DBQueryExt"},2052:{n:"ExtString"},2053:{n:"TxtQry"},2054:{n:"Qsir"},2055:{n:"Qsif"},2056:{n:"RRDTQSIF"},2057:{n:"BOF",f:Di},2058:{n:"OleDbConn"},2059:{n:"WOpt"},2060:{n:"SXViewEx"},2061:{n:"SXTH"},2062:{n:"SXPIEx"},2063:{n:"SXVDTEx"},2064:{n:"SXViewEx9"},2066:{n:"ContinueFrt"},2067:{n:"RealTimeData"},2128:{n:"ChartFrtInfo"},2129:{n:"FrtWrapper"},2130:{n:"StartBlock"},2131:{n:"EndBlock"},2132:{n:"StartObject"},2133:{n:"EndObject"},2134:{n:"CatLab"},2135:{n:"YMult"},2136:{n:"SXViewLink"},2137:{n:"PivotChartBits"},2138:{n:"FrtFontList"},2146:{n:"SheetExt"},2147:{n:"BookExt",r:12},2148:{n:"SXAddl"},2149:{n:"CrErr"},2150:{n:"HFPicture"},2151:{n:"FeatHdr",f:Xn},2152:{n:"Feat"},2154:{n:"DataLabExt"},2155:{n:"DataLabExtContents"},2156:{n:"CellWatch"},2161:{n:"FeatHdr11"},2162:{n:"Feature11"},2164:{n:"DropDownObjIds"},2165:{n:"ContinueFrt11"},2166:{n:"DConn"},2167:{n:"List12"},2168:{n:"Feature12"},2169:{n:"CondFmt12"},2170:{n:"CF12"},2171:{n:"CFEx"},2172:{n:"XFCRC",f:Ys,r:12},2173:{n:"XFExt",f:Fo,r:12},2174:{n:"AutoFilter12"},2175:{n:"ContinueFrt12"},2180:{n:"MDTInfo"},2181:{n:"MDXStr"},2182:{n:"MDXTuple"},2183:{n:"MDXSet"},2184:{n:"MDXProp"},2185:{n:"MDXKPI"},2186:{n:"MDB"},2187:{n:"PLV"},2188:{n:"Compat12",f:jn,r:12},2189:{n:"DXF"},2190:{n:"TableStyles",r:12},2191:{n:"TableStyle"},2192:{n:"TableStyleElement"},2194:{n:"StyleExt"},2195:{n:"NamePublish"},2196:{n:"NameCmt",f:ys,r:12},2197:{n:"SortData"},2198:{n:"Theme",f:_o,r:12},2199:{n:"GUIDTypeLib"},2200:{n:"FnGrp12"},2201:{n:"NameFnGrp12"},2202:{n:"MTRSettings",f:Fs,r:12},2203:{n:"CompressPictures",f:Xn},2204:{n:"HeaderFooter"},2205:{n:"CrtLayout12"},2206:{n:"CrtMlFrt"},2207:{n:"CrtMlFrtContinue"},2211:{n:"ForceFullCalculation",f:zi},2212:{n:"ShapePropsStream"},2213:{n:"TextPropsStream"},2214:{n:"RichTextStream"},2215:{n:"CrtLayout12A"},4097:{n:"Units"},4098:{n:"Chart"},4099:{n:"Series"},4102:{n:"DataFormat"},4103:{n:"LineFormat"},4105:{n:"MarkerFormat"},4106:{n:"AreaFormat"},4107:{n:"PieFormat"},4108:{n:"AttachedLabel"},4109:{n:"SeriesText"},4116:{n:"ChartFormat"},4117:{n:"Legend"},4118:{n:"SeriesList"},4119:{n:"Bar"},4120:{n:"Line"},4121:{n:"Pie"},4122:{n:"Area"},4123:{n:"Scatter"},4124:{n:"CrtLine"},4125:{n:"Axis"},4126:{n:"Tick"},4127:{n:"ValueRange"},4128:{n:"CatSerRange"},4129:{n:"AxisLine"},4130:{n:"CrtLink"},4132:{n:"DefaultText"},4133:{n:"Text"},4134:{n:"FontX",f:Kn},4135:{n:"ObjectLink"},4146:{n:"Frame"},4147:{n:"Begin"},4148:{n:"End"},4149:{n:"PlotArea"},4154:{n:"Chart3d"},4156:{n:"PicF"},4157:{n:"DropBar"},4158:{n:"Radar"},4159:{n:"Surf"},4160:{n:"RadarArea"},4161:{n:"AxisParent"},4163:{n:"LegendException"},4164:{n:"ShtProps",f:Zs},4165:{n:"SerToCrt"},4166:{n:"AxesUsed"},4168:{n:"SBaseRef"},4170:{n:"SerParent"},4171:{n:"SerAuxTrend"},4174:{n:"IFmtRecord"},4175:{n:"Pos"},4176:{n:"AlRuns"},4177:{n:"BRAI"},4187:{n:"SerAuxErrBar"},4188:{n:"ClrtClient",f:$s},4189:{n:"SerFmt"},4191:{n:"Chart3DBarShape"},4192:{n:"Fbi"},4193:{n:"BopPop"},4194:{n:"AxcExt"},4195:{n:"Dat"},4196:{n:"PlotGrowth"},4197:{n:"SIIndex"},4198:{n:"GelFrame"},4199:{n:"BopPopCustom"},4200:{n:"Fbi2"},0:{n:"Dimensions",f:ls},1:{n:"BIFF2BLANK"},2:{n:"BIFF2INT",f:of},3:{n:"BIFF2NUM",f:ff},4:{n:"BIFF2STR",f:sf},5:{n:"BoolErr",f:gs},7:{n:"String",f:uf},8:{n:"BIFF2ROW"},9:{n:"BOF",f:Di},11:{n:"Index"},22:{n:"ExternCount",f:Kn},30:{n:"BIFF2FORMAT",f:fs},31:{n:"BIFF2FMTCNT"},32:{n:"BIFF2COLINFO"},33:{n:"Array",f:Rs},36:{n:"COLWIDTH"},37:{n:"DefaultRowHeight",f:$i},50:{n:"BIFF2FONTXTRA",f:hf},62:{n:"BIFF2WINDOW2"},52:{n:"DDEObjName"},67:{n:"BIFF2XF"},68:{n:"BIFF2XFINDEX",f:Kn},69:{n:"BIFF2FONTCLR"},86:{n:"BIFF4FMTCNT"},126:{n:"RK"},127:{n:"ImData",f:nf},135:{n:"Addin"},136:{n:"Edg"},137:{n:"Pub"},145:{n:"Sub"},148:{n:"LHRecord"},149:{n:"LHNGraph"},150:{n:"Sound"},169:{n:"CoordList"},171:{n:"GCW"},188:{n:"ShrFmla"},191:{n:"ToolbarHdr"},192:{n:"ToolbarEnd"},194:{n:"AddMenu"},195:{n:"DelMenu"},214:{n:"RString",f:df},223:{n:"UDDesc"},234:{n:"TabIdConf"},354:{n:"XL5Modify"},421:{n:"FileSharing2"},518:{n:"Formula",f:Yu},521:{n:"BOF",f:Di},536:{n:"Lbl",f:_s},547:{n:"ExternName",f:Cs},561:{n:"Font"},579:{n:"BIFF3XF"},1030:{n:"Formula",f:Yu},1033:{n:"BOF",f:Di},1091:{n:"BIFF4XF"},2157:{n:"FeatInfo"},2163:{n:"FeatInfo11"},2177:{n:"SXAddl12"},2240:{n:"AutoWebPub"},2241:{n:"ListObj"},2242:{n:"ListField"},2243:{n:"ListDV"},2244:{n:"ListCondFmt"},2245:{n:"ListCF"},2246:{n:"FMQry"},2247:{n:"FMSQry"},2248:{n:"PLV"},2249:{n:"LnExt"},2250:{n:"MkrExt"},2251:{n:"CrtCoopt"},2262:{n:"FRTArchId$",r:12},29282:{}};var rb=Y(eb,"n");function tb(e,r,t,a){var n=typeof r=="number"?r:+r||+rb[r];if(isNaN(n))return;var i=a||(t||[]).length||0;var s=e.next(4);s._W(2,n);s._W(2,i);if(i>0&&Nr(t))e.push(t)}function ab(e,r,t,a){var n=a||(t||[]).length||0;if(n<=8224)return tb(e,r,t,n);var i=+r||+rb[r];if(isNaN(i))return;var s=t.parts||[],f=0;var l=0,o=0;while(o+(s[f]||8224)<=8224){o+=s[f]||8224;f++}var c=e.next(4);c._W(2,i);c._W(2,o);e.push(t.slice(l,l+o));l+=o;while(l<n){c=e.next(4);c._W(2,60);o=0;while(o+(s[f]||8224)<=8224){o+=s[f]||8224;f++}c._W(2,o);e.push(t.slice(l,l+o));l+=o}}function nb(e,r,t){if(!e)e=Jr(7);e._W(2,r);e._W(2,t);e._W(2,0);e._W(1,0);return e}function ib(e,r,t,a){var n=Jr(9);nb(n,e,r);qn(t,a||"b",n);return n}function sb(e,r,t){var a=Jr(8+2*t.length);nb(a,e,r);a._W(1,t.length);a._W(t.length,t,"sbcs");return a.l<a.length?a.slice(0,a.l):a}function fb(e,r,t,a){if(r.v!=null)switch(r.t){case"d":;case"n":var n=r.t=="d"?ee(le(r.v)):r.v;if(n==(n|0)&&n>=0&&n<65536)tb(e,2,cf(t,a,n));else tb(e,3,lf(t,a,n));return;case"b":;case"e":tb(e,5,ib(t,a,r.v,r.t));return;case"s":;case"str":tb(e,4,sb(t,a,(r.v||"").slice(0,255)));return;}tb(e,1,nb(null,t,a))}function lb(e,r,t,a){var n=Array.isArray(r);var i=wt(r["!ref"]||"A1"),s,f="",l=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(r["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255);i.e.r=Math.min(i.e.c,16383);s=Et(i)}for(var o=i.s.r;o<=i.e.r;++o){f=lt(o);for(var c=i.s.c;c<=i.e.c;++c){if(o===i.s.r)l[c]=ht(c);s=l[c]+f;var u=n?(r[o]||[])[c]:r[s];if(!u)continue;fb(e,u,o,c,a)}}}function ob(e,r){var t=r||{};if(b!=null&&t.dense==null)t.dense=b;var a=Zr();var n=0;for(var i=0;i<e.SheetNames.length;++i)if(e.SheetNames[i]==t.sheet)n=i;if(n==0&&!!t.sheet&&e.SheetNames[0]!=t.sheet)throw new Error("Sheet not found: "+t.sheet);tb(a,t.biff==4?1033:t.biff==3?521:9,Oi(e,16,t));lb(a,e.Sheets[e.SheetNames[n]],n,t,e);tb(a,10);return a.end()}function cb(e,r,t){tb(e,"Font",es({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},t))}function ub(e,r,t){if(!r)return;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)if(r[n]!=null)tb(e,"Format",ss(n,r[n],t))})}function hb(e,r){var t=Jr(19);t._W(4,2151);t._W(4,0);t._W(4,0);t._W(2,3);t._W(1,1);t._W(4,0);tb(e,"FeatHdr",t);t=Jr(39);t._W(4,2152);t._W(4,0);t._W(4,0);t._W(2,3);t._W(1,0);t._W(4,0);t._W(2,1);t._W(4,4);t._W(2,0);Ci(wt(r["!ref"]||"A1"),t);t._W(4,4);tb(e,"Feat",t)}function db(e,r){for(var t=0;t<16;++t)tb(e,"XF",ps({numFmtId:0,style:true},0,r));r.cellXfs.forEach(function(t){tb(e,"XF",ps(t,0,r))})}function vb(e,r){for(var t=0;t<r["!links"].length;++t){var a=r["!links"][t];tb(e,"HLink",Vs(a));if(a[1].Tooltip)tb(e,"HLinkTooltip",Gs(a))}delete r["!links"]}function pb(e,r,t){if(!r)return;var a=0;r.forEach(function(r,t){if(++a<=256&&r){tb(e,"ColInfo",Js(vh(t,r),t))}})}function bb(e,r,t,a,n){var i=16+bh(n.cellXfs,r,n);if(r.v==null&&!r.bf){tb(e,"Blank",mi(t,a,i));return}if(r.bf)tb(e,"Formula",Qu(r,t,a,n,i));else switch(r.t){case"d":;case"n":var s=r.t=="d"?ee(le(r.v)):r.v;tb(e,"Number",ks(t,a,s,i,n));break;case"b":;case"e":tb(e,517,Es(t,a,r.v,i,n,r.t));break;case"s":;case"str":if(n.bookSST){var f=dh(n.Strings,r.v,n.revStrings);tb(e,"LabelSst",ts(t,a,f,i,n))}else tb(e,"Label",ns(t,a,(r.v||"").slice(0,255),i,n));break;default:tb(e,"Blank",mi(t,a,i));}}function mb(e,r,t){var a=Zr();var n=t.SheetNames[e],i=t.Sheets[n]||{};var s=(t||{}).Workbook||{};var f=(s.Sheets||[])[e]||{};var l=Array.isArray(i);var o=r.biff==8;var c,u="",h=[];var d=wt(i["!ref"]||"A1");var v=o?65536:16384;if(d.e.c>255||d.e.r>=v){if(r.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255);d.e.r=Math.min(d.e.c,v-1)}tb(a,2057,Oi(t,16,r));tb(a,"CalcMode",Yn(1));tb(a,"CalcCount",Yn(100));tb(a,"CalcRefMode",$n(true));tb(a,"CalcIter",$n(false));tb(a,"CalcDelta",Zt(.001));tb(a,"CalcSaveRecalc",$n(true));tb(a,"PrintRowCol",$n(false));tb(a,"PrintGrid",$n(false));tb(a,"GridSet",Yn(1));tb(a,"Guts",ms([0,0]));tb(a,"HCenter",$n(false));tb(a,"VCenter",$n(false));if(o)pb(a,i["!cols"],i);tb(a,512,os(d,r));if(o)i["!links"]=[];for(var p=d.s.r;p<=d.e.r;++p){u=lt(p);for(var b=d.s.c;b<=d.e.c;++b){if(p===d.s.r)h[b]=ht(b);c=h[b]+u;var m=l?(i[p]||[])[b]:i[c];if(!m)continue;bb(a,m,p,b,r);if(o&&m.l)i["!links"].push([c,m.l])}}var g=f.CodeName||f.name||n;if(o)tb(a,"Window2",Ji((s.Views||[])[0]));if(o&&(i["!merges"]||[]).length)tb(a,"MergeCells",Ns(i["!merges"]));if(o)vb(a,i);tb(a,"CodeName",ii(g,r));if(o)hb(a,i);tb(a,"EOF");return a.end()}function gb(e,r,t){var a=Zr();var n=(e||{}).Workbook||{};var i=n.Sheets||[];var s=n.WBProps||{};var f=t.biff==8,l=t.biff==5;tb(a,2057,Oi(e,5,t));if(t.bookType=="xla")tb(a,"Addin");tb(a,"InterfaceHdr",f?Yn(1200):null);tb(a,"Mms",Gn(2));if(l)tb(a,"ToolbarHdr");if(l)tb(a,"ToolbarEnd");tb(a,"InterfaceEnd");tb(a,"WriteAccess",Mi("SheetJS",t));tb(a,"CodePage",Yn(f?1200:1252));if(f)tb(a,"DSF",Yn(0));if(f)tb(a,"Excel9File");tb(a,"RRTabId",ef(e.SheetNames.length));if(f&&e.vbaraw)tb(a,"ObProj");if(f&&e.vbaraw){var o=s.CodeName||"ThisWorkbook";tb(a,"CodeName",ii(o,t))}tb(a,"BuiltInFnGroupCount",Yn(17));tb(a,"WinProtect",$n(false));
tb(a,"Protect",$n(false));tb(a,"Password",Yn(0));if(f)tb(a,"Prot4Rev",$n(false));if(f)tb(a,"Prot4RevPass",Yn(0));tb(a,"Window1",Yi(t));tb(a,"Backup",$n(false));tb(a,"HideObj",Yn(0));tb(a,"Date1904",$n(Rv(e)=="true"));tb(a,"CalcPrecision",$n(true));if(f)tb(a,"RefreshAll",$n(false));tb(a,"BookBool",Yn(0));cb(a,e,t);ub(a,e.SSF,t);db(a,t);if(f)tb(a,"UsesELFs",$n(false));var c=a.end();var u=Zr();if(f)tb(u,"Country",js());if(f&&t.Strings)ab(u,"SST",Vi(t.Strings,t));tb(u,"EOF");var h=u.end();var d=Zr();var v=0,p=0;for(p=0;p<e.SheetNames.length;++p)v+=(f?12:11)+(f?2:1)*e.SheetNames[p].length;var b=c.length+v+h.length;for(p=0;p<e.SheetNames.length;++p){var m=i[p]||{};tb(d,"BoundSheet8",Hi({pos:b,hs:m.Hidden||0,dt:0,name:e.SheetNames[p]},t));b+=r[p].length}var g=d.end();if(v!=g.length)throw new Error("BS8 "+v+" != "+g.length);var E=[];if(c.length)E.push(c);if(g.length)E.push(g);if(h.length)E.push(h);return br([E])}function Eb(e,r){var t=r||{};var a=[];if(e&&!e.SSF){e.SSF=D.get_table()}if(e&&e.SSF){O(D);D.load_table(e.SSF);t.revssf=J(e.SSF);t.revssf[e.SSF[65535]]=0;t.ssf=e.SSF}t.Strings=[];t.Strings.Count=0;t.Strings.Unique=0;$b(t);t.cellXfs=[];bh(t.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};for(var n=0;n<e.SheetNames.length;++n)a[a.length]=mb(n,t,e);a.unshift(gb(e,a,t));return br([a])}function wb(e,r){for(var t=0;t<=e.SheetNames.length;++t){var a=e.Sheets[e.SheetNames[t]];if(!a||!a["!ref"])continue;var n=gt(a["!ref"]);if(n.e.c>255){console.error("Worksheet '"+e.SheetNames[t]+"' extends beyond column IV (255).  Data may be lost.")}}var i=r||{};switch(i.biff||2){case 8:;case 5:return Eb(e,r);case 4:;case 3:;case 2:return ob(e,r);}throw new Error("invalid type "+i.bookType+" for BIFF")}var kb=function(){function e(e,r){var t=r||{};if(b!=null&&t.dense==null)t.dense=b;var a=t.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i);var s=n.index,f=i&&i.index||e.length;var l=pe(e.slice(s,f),/(:?<tr[^>]*>)/i,"<tr>");var o=-1,c=0,u=0,h=0;var d={s:{r:1e7,c:1e7},e:{r:0,c:0}};var v=[];for(s=0;s<l.length;++s){var p=l[s].trim();var m=p.slice(0,3).toLowerCase();if(m=="<tr"){++o;if(t.sheetRows&&t.sheetRows<=o){--o;break}c=0;continue}if(m!="<td"&&m!="<th")continue;var g=p.split(/<\/t[dh]>/i);for(f=0;f<g.length;++f){var E=g[f].trim();if(!E.match(/<t[dh]/i))continue;var w=E,k=0;while(w.charAt(0)=="<"&&(k=w.indexOf(">"))>-1)w=w.slice(k+1);for(var S=0;S<v.length;++S){var B=v[S];if(B.s.c==c&&B.s.r<o&&o<=B.e.r){c=B.e.c+1;S=-1}}var C=Oe(E.slice(0,E.indexOf(">")));h=C.colspan?+C.colspan:1;if((u=+C.rowspan)>1||h>1)v.push({s:{r:o,c:c},e:{r:o+(u||1)-1,c:c+h-1}});var T=C.t||C["data-t"]||"";if(!w.length){c+=h;continue}w=rr(w);if(d.s.r>o)d.s.r=o;if(d.e.r<o)d.e.r=o;if(d.s.c>c)d.s.c=c;if(d.e.c<c)d.e.c=c;if(!w.length)continue;var _={t:"s",v:w};if(t.raw||!w.trim().length||T=="s"){}else if(w==="TRUE")_={t:"b",v:true};else if(w==="FALSE")_={t:"b",v:false};else if(!isNaN(he(w)))_={t:"n",v:he(w)};else if(!isNaN(de(w).getDate())){_={t:"d",v:le(w)};if(!t.cellDates)_={t:"n",v:ee(_.v)};_.z=t.dateNF||D._table[14]}if(t.dense){if(!a[o])a[o]=[];a[o][c]=_}else a[mt({r:o,c:c})]=_;c+=h}}a["!ref"]=Et(d);if(v.length)a["!merges"]=v;return a}function r(r,t){var a=r.match(/<table.*?>[\s\S]*?<\/table>/gi);if(!a||a.length==0)throw new Error("Invalid HTML: could not find <table>");if(a.length==1)return Bt(e(a[0],t),t);var n=Im.book_new();a.forEach(function(r,a){Im.book_append_sheet(n,e(r,t),"Sheet"+(a+1))});return n}function t(e,r,t,a){var n=e["!merges"]||[];var i=[];for(var s=r.s.c;s<=r.e.c;++s){var f=0,l=0;for(var o=0;o<n.length;++o){if(n[o].s.r>t||n[o].s.c>s)continue;if(n[o].e.r<t||n[o].e.c<s)continue;if(n[o].s.r<t||n[o].s.c<s){f=-1;break}f=n[o].e.r-n[o].s.r+1;l=n[o].e.c-n[o].s.c+1;break}if(f<0)continue;var c=mt({r:t,c:s});var u=a.dense?(e[t]||[])[s]:e[c];var h=u&&u.v!=null&&(u.h||Ge(u.w||(St(u),u.w)||""))||"";var d={};if(f>1)d.rowspan=f;if(l>1)d.colspan=l;if(a.editable)h='<span contenteditable="true">'+h+"</span>";else if(u){d["data-t"]=u&&u.t||"z";if(u.v!=null)d["data-v"]=u.v;if(u.z!=null)d["data-z"]=u.z;if(u.l&&(u.l.Target||"#").charAt(0)!="#")h='<a href="'+u.l.Target+'">'+h+"</a>"}d.id=(a.id||"sjs")+"-"+c;i.push(or("td",h,d))}var v="<tr>";return v+i.join("")+"</tr>"}function a(e,r,t){var a=[];return a.join("")+"<table"+(t&&t.id?' id="'+t.id+'"':"")+">"}var n='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>';var i="</body></html>";function s(e,r){var s=r||{};var f=s.header!=null?s.header:n;var l=s.footer!=null?s.footer:i;var o=[f];var c=gt(e["!ref"]);s.dense=Array.isArray(e);o.push(a(e,c,s));for(var u=c.s.r;u<=c.e.r;++u)o.push(t(e,c,u,s));o.push("</table>"+l);return o.join("")}return{to_workbook:r,to_sheet:e,_row:t,BEGIN:n,END:i,_preamble:a,from_sheet:s}}();function Sb(e,r,t){var a=t||{};if(b!=null)a.dense=b;var n=0,i=0;if(a.origin!=null){if(typeof a.origin=="number")n=a.origin;else{var s=typeof a.origin=="string"?bt(a.origin):a.origin;n=s.r;i=s.c}}var f=r.getElementsByTagName("tr");var l=Math.min(a.sheetRows||1e7,f.length);var o={s:{r:0,c:0},e:{r:n,c:i}};if(e["!ref"]){var c=gt(e["!ref"]);o.s.r=Math.min(o.s.r,c.s.r);o.s.c=Math.min(o.s.c,c.s.c);o.e.r=Math.max(o.e.r,c.e.r);o.e.c=Math.max(o.e.c,c.e.c);if(n==-1)o.e.r=n=c.e.r+1}var u=[],h=0;var d=e["!rows"]||(e["!rows"]=[]);var v=0,p=0,m=0,g=0,E=0,w=0;if(!e["!cols"])e["!cols"]=[];for(;v<f.length&&p<l;++v){var k=f[v];if(Tb(k)){if(a.display)continue;d[p]={hidden:true}}var S=k.children;for(m=g=0;m<S.length;++m){var B=S[m];if(a.display&&Tb(B))continue;var C=B.hasAttribute("data-v")?B.getAttribute("data-v"):B.hasAttribute("v")?B.getAttribute("v"):rr(B.innerHTML);var T=B.getAttribute("data-z")||B.getAttribute("z");for(h=0;h<u.length;++h){var _=u[h];if(_.s.c==g+i&&_.s.r<p+n&&p+n<=_.e.r){g=_.e.c+1-i;h=-1}}w=+B.getAttribute("colspan")||1;if((E=+B.getAttribute("rowspan")||1)>1||w>1)u.push({s:{r:p+n,c:g+i},e:{r:p+n+(E||1)-1,c:g+i+(w||1)-1}});var x={t:"s",v:C};var A=B.getAttribute("data-t")||B.getAttribute("t")||"";if(C!=null){if(C.length==0)x.t=A||"z";else if(a.raw||C.trim().length==0||A=="s"){}else if(C==="TRUE")x={t:"b",v:true};else if(C==="FALSE")x={t:"b",v:false};else if(!isNaN(he(C)))x={t:"n",v:he(C)};else if(!isNaN(de(C).getDate())){x={t:"d",v:le(C)};if(!a.cellDates)x={t:"n",v:ee(x.v)};x.z=a.dateNF||D._table[14]}}if(x.z===undefined&&T!=null)x.z=T;var y="",I=B.getElementsByTagName("A");if(I&&I.length)for(var R=0;R<I.length;++R)if(I[R].hasAttribute("href")){y=I[R].getAttribute("href");if(y.charAt(0)!="#")break}if(y&&y.charAt(0)!="#")x.l={Target:y};if(a.dense){if(!e[p+n])e[p+n]=[];e[p+n][g+i]=x}else e[mt({c:g+i,r:p+n})]=x;if(o.e.c<g+i)o.e.c=g+i;g+=w}++p}if(u.length)e["!merges"]=(e["!merges"]||[]).concat(u);o.e.r=Math.max(o.e.r,p-1+n);e["!ref"]=Et(o);if(p>=l)e["!fullref"]=Et((o.e.r=f.length-v+p-1+n,o));return e}function Bb(e,r){var t=r||{};var a=t.dense?[]:{};return Sb(a,e,r)}function Cb(e,r){return Bt(Bb(e,r),r)}function Tb(e){var r="";var t=_b(e);if(t)r=t(e).getPropertyValue("display");if(!r)r=e.style.display;return r==="none"}function _b(e){if(e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle==="function")return e.ownerDocument.defaultView.getComputedStyle;if(typeof getComputedStyle==="function")return getComputedStyle;return null}var xb=function(){var e=function(e){var r=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,r){return Array(parseInt(r,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n");var t=Le(r.replace(/<[^>]*>/g,""));return[t]};var r={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};return function t(a,n){var i=n||{};if(b!=null&&i.dense==null)i.dense=b;var s=Tp(a);var f=[],l;var o;var c={name:""},u="",h=0;var d;var v;var p={},m=[];var g=i.dense?[]:{};var E,w;var k={value:""};var S="",B=0,C;var T=[];var _=-1,x=-1,A={s:{r:1e6,c:1e7},e:{r:0,c:0}};var y=0;var I={};var R=[],F={},D=0,O=0;var P=[],N=1,M=1;var L=[];var U={Names:[]};var H={};var W=["",""];var V=[],X={};var G="",z=0;var j=false,$=false;var K=0;_p.lastIndex=0;s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");while(E=_p.exec(s))switch(E[3]=E[3].replace(/_.*$/,"")){case"table":;case"工作表":if(E[1]==="/"){if(A.e.c>=A.s.c&&A.e.r>=A.s.r)g["!ref"]=Et(A);else g["!ref"]="A1:A1";if(i.sheetRows>0&&i.sheetRows<=A.e.r){g["!fullref"]=g["!ref"];A.e.r=i.sheetRows-1;g["!ref"]=Et(A)}if(R.length)g["!merges"]=R;if(P.length)g["!rows"]=P;d.name=d["名称"]||d.name;if(typeof JSON!=="undefined")JSON.stringify(d);m.push(d.name);p[d.name]=g;$=false}else if(E[0].charAt(E[0].length-2)!=="/"){d=Oe(E[0],false);_=x=-1;A.s.r=A.s.c=1e7;A.e.r=A.e.c=0;g=i.dense?[]:{};R=[];P=[];$=true}break;case"table-row-group":if(E[1]==="/")--y;else++y;break;case"table-row":;case"行":if(E[1]==="/"){_+=N;N=1;break}v=Oe(E[0],false);if(v["行号"])_=v["行号"]-1;else if(_==-1)_=0;N=+v["number-rows-repeated"]||1;if(N<10)for(K=0;K<N;++K)if(y>0)P[_+K]={level:y};x=-1;break;case"covered-table-cell":if(E[1]!=="/")++x;if(i.sheetStubs){if(i.dense){if(!g[_])g[_]=[];g[_][x]={t:"z"}}else g[mt({r:_,c:x})]={t:"z"}}S="";T=[];break;case"table-cell":;case"数据":if(E[0].charAt(E[0].length-2)==="/"){++x;k=Oe(E[0],false);M=parseInt(k["number-columns-repeated"]||"1",10);w={t:"z",v:null};if(k.formula&&i.cellFormula!=false)w.f=sh(Le(k.formula));if((k["数据类型"]||k["value-type"])=="string"){w.t="s";w.v=Le(k["string-value"]||"");if(i.dense){if(!g[_])g[_]=[];g[_][x]=w}else{g[mt({r:_,c:x})]=w}}x+=M-1}else if(E[1]!=="/"){++x;S="";B=0;T=[];M=1;var Y=N?_+N-1:_;if(x>A.e.c)A.e.c=x;if(x<A.s.c)A.s.c=x;if(_<A.s.r)A.s.r=_;if(Y>A.e.r)A.e.r=Y;k=Oe(E[0],false);V=[];X={};w={t:k["数据类型"]||k["value-type"],v:null};if(i.cellFormula){if(k.formula)k.formula=Le(k.formula);if(k["number-matrix-columns-spanned"]&&k["number-matrix-rows-spanned"]){D=parseInt(k["number-matrix-rows-spanned"],10)||0;O=parseInt(k["number-matrix-columns-spanned"],10)||0;F={s:{r:_,c:x},e:{r:_+D-1,c:x+O-1}};w.F=Et(F);L.push([F,w.F])}if(k.formula)w.f=sh(k.formula);else for(K=0;K<L.length;++K)if(_>=L[K][0].s.r&&_<=L[K][0].e.r)if(x>=L[K][0].s.c&&x<=L[K][0].e.c)w.F=L[K][1]}if(k["number-columns-spanned"]||k["number-rows-spanned"]){D=parseInt(k["number-rows-spanned"],10)||0;O=parseInt(k["number-columns-spanned"],10)||0;F={s:{r:_,c:x},e:{r:_+D-1,c:x+O-1}};R.push(F)}if(k["number-columns-repeated"])M=parseInt(k["number-columns-repeated"],10);switch(w.t){case"boolean":w.t="b";w.v=Ke(k["boolean-value"]);break;case"float":w.t="n";w.v=parseFloat(k.value);break;case"percentage":w.t="n";w.v=parseFloat(k.value);break;case"currency":w.t="n";w.v=parseFloat(k.value);break;case"date":w.t="d";w.v=le(k["date-value"]);if(!i.cellDates){w.t="n";w.v=ee(w.v)}w.z="m/d/yy";break;case"time":w.t="n";w.v=ie(k["time-value"])/86400;if(i.cellDates){w.t="d";w.v=ne(w.v)}w.z="HH:MM:SS";break;case"number":w.t="n";w.v=parseFloat(k["数据数值"]);break;default:if(w.t==="string"||w.t==="text"||!w.t){w.t="s";if(k["string-value"]!=null){S=Le(k["string-value"]);T=[]}}else throw new Error("Unsupported value type "+w.t);}}else{j=false;if(w.t==="s"){w.v=S||"";if(T.length)w.R=T;j=B==0}if(H.Target)w.l=H;if(V.length>0){w.c=V;V=[]}if(S&&i.cellText!==false)w.w=S;if(j){w.t="z";delete w.v}if(!j||i.sheetStubs){if(!(i.sheetRows&&i.sheetRows<=_)){for(var Q=0;Q<N;++Q){M=parseInt(k["number-columns-repeated"]||"1",10);if(i.dense){if(!g[_+Q])g[_+Q]=[];g[_+Q][x]=Q==0?w:ce(w);while(--M>0)g[_+Q][x+M]=ce(w)}else{g[mt({r:_+Q,c:x})]=w;while(--M>0)g[mt({r:_+Q,c:x+M})]=ce(w)}if(A.e.c<=x)A.e.c=x}}}M=parseInt(k["number-columns-repeated"]||"1",10);x+=M-1;M=0;w={};S="";T=[]}H={};break;case"document":;case"document-content":;case"电子表格文档":;case"spreadsheet":;case"主体":;case"scripts":;case"styles":;case"font-face-decls":;case"master-styles":if(E[1]==="/"){if((l=f.pop())[0]!==E[3])throw"Bad state: "+l}else if(E[0].charAt(E[0].length-2)!=="/")f.push([E[3],true]);break;case"annotation":if(E[1]==="/"){if((l=f.pop())[0]!==E[3])throw"Bad state: "+l;X.t=S;if(T.length)X.R=T;X.a=G;V.push(X)}else if(E[0].charAt(E[0].length-2)!=="/"){f.push([E[3],false])}G="";z=0;S="";B=0;T=[];break;case"creator":if(E[1]==="/"){G=s.slice(z,E.index)}else z=E.index+E[0].length;break;case"meta":;case"元数据":;case"settings":;case"config-item-set":;case"config-item-map-indexed":;case"config-item-map-entry":;case"config-item-map-named":;case"shapes":;case"frame":;case"text-box":;case"image":;case"data-pilot-tables":;case"list-style":;case"form":;case"dde-links":;case"event-listeners":;case"chart":if(E[1]==="/"){if((l=f.pop())[0]!==E[3])throw"Bad state: "+l}else if(E[0].charAt(E[0].length-2)!=="/")f.push([E[3],false]);S="";B=0;T=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":;case"percentage-style":;case"date-style":;case"time-style":if(E[1]==="/"){I[c.name]=u;if((l=f.pop())[0]!==E[3])throw"Bad state: "+l}else if(E[0].charAt(E[0].length-2)!=="/"){u="";c=Oe(E[0],false);f.push([E[3],true])}break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":;case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(f[f.length-1][0]){case"time-style":;case"date-style":o=Oe(E[0],false);u+=r[E[3]][o.style==="long"?1:0];break;}break;case"fraction":break;case"day":;case"month":;case"year":;case"era":;case"day-of-week":;case"week-of-year":;case"quarter":;case"hours":;case"minutes":;case"seconds":;case"am-pm":switch(f[f.length-1][0]){case"time-style":;case"date-style":o=Oe(E[0],false);u+=r[E[3]][o.style==="long"?1:0];break;}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(E[0].slice(-2)==="/>")break;else if(E[1]==="/")switch(f[f.length-1][0]){case"number-style":;case"date-style":;case"time-style":u+=s.slice(h,E.index);break;}else h=E.index+E[0].length;break;case"named-range":o=Oe(E[0],false);W=lh(o["cell-range-address"]);var J={Name:o.name,Ref:W[0]+"!"+W[1]};if($)J.Sheet=m.length;U.Names.push(J);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":;case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":;case"文本串":if(["master-styles"].indexOf(f[f.length-1][0])>-1)break;if(E[1]==="/"&&(!k||!k["string-value"])){var q=e(s.slice(B,E.index),C);S=(S.length>0?S+"\n":"")+q[0]}else{C=Oe(E[0],false);B=E.index+E[0].length}break;case"s":break;case"database-range":if(E[1]==="/")break;try{W=lh(Oe(E[0])["target-range-address"]);p[W[0]]["!autofilter"]={ref:W[1]}}catch(Z){}break;case"date":break;case"object":break;case"title":;case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":;case"sender-lastname":;case"sender-initials":;case"sender-title":;case"sender-position":;case"sender-email":;case"sender-phone-private":;case"sender-fax":;case"sender-company":;case"sender-phone-work":;case"sender-street":;case"sender-city":;case"sender-postal-code":;case"sender-country":;case"sender-state-or-province":;case"author-name":;case"author-initials":;case"chapter":;case"file-name":;case"template-name":;case"sheet-name":break;case"event-listener":break;case"initial-creator":;case"creation-date":;case"print-date":;case"generator":;case"document-statistic":;case"user-defined":;case"editing-duration":;case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":;case"source-cell-range":;case"source-service":;case"data-pilot-field":;case"data-pilot-level":;case"data-pilot-subtotals":;case"data-pilot-subtotal":;case"data-pilot-members":;case"data-pilot-member":;case"data-pilot-display-info":;case"data-pilot-sort-info":;case"data-pilot-layout-info":;case"data-pilot-field-reference":;case"data-pilot-groups":;case"data-pilot-group":;case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":;case"dde-connection-decl":;case"dde-link":;case"dde-source":break;case"properties":break;case"property":break;case"a":if(E[1]!=="/"){H=Oe(E[0],false);if(!H.href)break;H.Target=Le(H.href);delete H.href;if(H.Target.charAt(0)=="#"&&H.Target.indexOf(".")>-1){W=lh(H.Target.slice(1));H.Target="#"+W[0]+"!"+W[1]}else if(H.Target.match(/^\.\.[\\\/]/))H.Target=H.Target.slice(3)}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(E[2]){case"dc:":;case"calcext:":;case"loext:":;case"ooo:":;case"chartooo:":;case"draw:":;case"style:":;case"chart:":;case"form:":;case"uof:":;case"表:":;case"字:":break;default:if(i.WTF)throw new Error(E);};}var re={Sheets:p,SheetNames:m,Workbook:U};if(i.bookSheets)delete re.Sheets;return re}}();function Ab(e,r){r=r||{};if(Ee(e,"META-INF/manifest.xml"))$a(ke(e,"META-INF/manifest.xml"),r);var t=Se(e,"content.xml");if(!t)throw new Error("Missing content.xml in ODS / UOF file");var a=xb(Ye(t),r);if(Ee(e,"meta.xml"))a.Props=rn(ke(e,"meta.xml"));return a}function yb(e,r){return xb(e,r)}var Ib=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join("");var r="<office:document-styles "+lr({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function t(){return ye+r}}();var Rb=function(){var e=function(e){return We(e).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")};var r="          <table:table-cell />\n";var t="          <table:covered-table-cell/>\n";var a=function(a,n,i){var s=[];s.push('      <table:table table:name="'+We(n.SheetNames[i])+'" table:style-name="ta1">\n');var f=0,l=0,o=gt(a["!ref"]||"A1");var c=a["!merges"]||[],u=0;var h=Array.isArray(a);if(a["!cols"]){for(l=0;l<=o.e.c;++l)s.push("        <table:table-column"+(a["!cols"][l]?' table:style-name="co'+a["!cols"][l].ods+'"':"")+"></table:table-column>\n")}var d="",v=a["!rows"]||[];for(f=0;f<o.s.r;++f){d=v[f]?' table:style-name="ro'+v[f].ods+'"':"";s.push("        <table:table-row"+d+"></table:table-row>\n")}for(;f<=o.e.r;++f){d=v[f]?' table:style-name="ro'+v[f].ods+'"':"";s.push("        <table:table-row"+d+">\n");for(l=0;l<o.s.c;++l)s.push(r);for(;l<=o.e.c;++l){var p=false,b={},m="";for(u=0;u!=c.length;++u){if(c[u].s.c>l)continue;if(c[u].s.r>f)continue;if(c[u].e.c<l)continue;if(c[u].e.r<f)continue;if(c[u].s.c!=l||c[u].s.r!=f)p=true;b["table:number-columns-spanned"]=c[u].e.c-c[u].s.c+1;b["table:number-rows-spanned"]=c[u].e.r-c[u].s.r+1;break}if(p){s.push(t);continue}var g=mt({r:f,c:l}),E=h?(a[f]||[])[l]:a[g];if(E&&E.f){b["table:formula"]=We(fh(E.f));if(E.F){if(E.F.slice(0,g.length)==g){var w=gt(E.F);b["table:number-matrix-columns-spanned"]=w.e.c-w.s.c+1;b["table:number-matrix-rows-spanned"]=w.e.r-w.s.r+1}}}if(!E){s.push(r);continue}switch(E.t){case"b":m=E.v?"TRUE":"FALSE";b["office:value-type"]="boolean";b["office:boolean-value"]=E.v?"true":"false";break;case"n":m=E.w||String(E.v||0);b["office:value-type"]="float";b["office:value"]=E.v||0;break;case"s":;case"str":m=E.v==null?"":E.v;b["office:value-type"]="string";break;case"d":m=E.w||le(E.v).toISOString();b["office:value-type"]="date";b["office:date-value"]=le(E.v).toISOString();b["table:style-name"]="ce1";break;default:s.push(r);continue;}var k=e(m);if(E.l&&E.l.Target){var S=E.l.Target;S=S.charAt(0)=="#"?"#"+oh(S.slice(1)):S;if(S.charAt(0)!="#"&&!S.match(/^\w+:/))S="../"+S;k=or("text:a",k,{"xlink:href":S.replace(/&/g,"&amp;")})}s.push("          "+or("table:table-cell",or("text:p",k,{}),b)+"\n")}s.push("        </table:table-row>\n")}s.push("      </table:table>\n");return s.join("")};var n=function(e,r){e.push(" <office:automatic-styles>\n");e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n');e.push('   <number:month number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push('   <number:day number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push("   <number:year/>\n");e.push("  </number:date-style>\n");var t=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!cols"]){for(var a=0;a<r["!cols"].length;++a)if(r["!cols"][a]){var n=r["!cols"][a];if(n.width==null&&n.wpx==null&&n.wch==null)continue;Bl(n);n.ods=t;var i=r["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+t+'" style:family="table-column">\n');e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n');e.push("  </style:style>\n");++t}}});var a=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!rows"]){for(var t=0;t<r["!rows"].length;++t)if(r["!rows"][t]){r["!rows"][t].ods=a;var n=r["!rows"][t].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n');e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n');e.push("  </style:style>\n");++a}}});e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n');e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n');e.push("  </style:style>\n");e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n');e.push(" </office:automatic-styles>\n")};return function i(e,r){var t=[ye];var i=lr({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"});var s=lr({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});if(r.bookType=="fods"){t.push("<office:document"+i+s+">\n");t.push(qa().replace(/office:document-meta/g,"office:meta"))}else t.push("<office:document-content"+i+">\n");n(t,e);t.push("  <office:body>\n");t.push("    <office:spreadsheet>\n");for(var f=0;f!=e.SheetNames.length;++f)t.push(a(e.Sheets[e.SheetNames[f]],e,f,r));t.push("    </office:spreadsheet>\n");t.push("  </office:body>\n");if(r.bookType=="fods")t.push("</office:document>");else t.push("</office:document-content>");return t.join("")}}();function Fb(e,r){if(r.bookType=="fods")return Rb(e,r);var t=_e();var a="";var n=[];var i=[];a="mimetype";Ce(t,a,"application/vnd.oasis.opendocument.spreadsheet");a="content.xml";Ce(t,a,Rb(e,r));n.push([a,"text/xml"]);i.push([a,"ContentFile"]);a="styles.xml";Ce(t,a,Ib(e,r));n.push([a,"text/xml"]);i.push([a,"StylesFile"]);a="meta.xml";Ce(t,a,ye+qa());n.push([a,"text/xml"]);i.push([a,"MetadataFile"]);a="manifest.rdf";Ce(t,a,Ja(i));n.push([a,"application/rdf+xml"]);a="META-INF/manifest.xml";Ce(t,a,Ka(n));return t}function Db(e,r){if(!r)return 0;var t=e.SheetNames.indexOf(r);if(t==-1)throw new Error("Sheet not found: "+r);return t}function Ob(e){return function r(t,a){var n=Db(t,a.sheet);return e.from_sheet(t.Sheets[t.SheetNames[n]],a,t)}}var Pb=Ob(kb);var Nb=Ob({from_sheet:Tm});var Mb=Ob(typeof pf!=="undefined"?pf:{});var Lb=Ob(typeof bf!=="undefined"?bf:{});var Ub=Ob(typeof gf!=="undefined"?gf:{});var Hb=Ob(typeof ll!=="undefined"?ll:{});var Wb=Ob({from_sheet:_m});var Vb=Ob(typeof vf!=="undefined"?vf:{});var Xb=Ob(typeof mf!=="undefined"?mf:{});var Gb=Ob(typeof wf!=="undefined"?{from_sheet:wf.sheet_to_wk1}:{});function zb(e){return function r(t){for(var a=0;a!=e.length;++a){var n=e[a];if(t[n[0]]===undefined)t[n[0]]=n[1];if(n[2]==="n")t[n[0]]=Number(t[n[0]])}}}var jb=function(e){zb([["cellNF",false],["cellHTML",true],["cellFormula",true],["cellStyles",false],["cellText",true],["cellDates",false],["sheetStubs",false],["sheetRows",0,"n"],["bookDeps",false],["bookSheets",false],["bookProps",false],["bookFiles",false],["bookVBA",false],["password",""],["WTF",false]])(e)};var $b=zb([["cellDates",false],["bookSST",false],["bookType","xlsx"],["compression",false],["WTF",false]]);function Kb(e){if(Ua.WS.indexOf(e)>-1)return"sheet";if(Ua.CS&&e==Ua.CS)return"chart";if(Ua.DS&&e==Ua.DS)return"dialog";if(Ua.MS&&e==Ua.MS)return"macro";return e&&e.length?e:"sheet"}function Yb(e,r){if(!e)return 0;try{e=r.map(function a(r){if(!r.id)r.id=r.strRelID;return[r.name,e["!id"][r.id].Target,Kb(e["!id"][r.id].Type)]})}catch(t){return null}return!e||e.length===0?null:e}function Qb(e,r,t,a,n,i,s,f,l,o,c,u){try{i[a]=Wa(Se(e,t,true),r);var h=ke(e,r);var d;switch(f){case"sheet":d=Zv(h,r,n,l,i[a],o,c,u);break;case"chart":d=ep(h,r,n,l,i[a],o,c,u);if(!d||!d["!drawel"])break;var v=Ae(d["!drawel"].Target,r);var p=Ha(v);var b=Uo(Se(e,v,true),Wa(Se(e,p,true),v));var m=Ae(b,v);var g=Ha(m);d=mv(Se(e,m,true),m,l,Wa(Se(e,g,true),m),o,d);break;case"macro":d=rp(h,r,n,l,i[a],o,c,u);break;case"dialog":d=tp(h,r,n,l,i[a],o,c,u);break;default:throw new Error("Unrecognized sheet type "+f);}s[a]=d;var E=[];if(i&&i[a])K(i[a]).forEach(function(t){if(i[a][t].Type==Ua.CMNT){var n=Ae(i[a][t].Target,r);E=sp(ke(e,n,true),n,l);if(!E||!E.length)return;Vo(d,E)}})}catch(w){if(l.WTF)throw w}}function Jb(e){return e.charAt(0)=="/"?e.slice(1):e}function qb(e,r){O(D);r=r||{};jb(r);if(Ee(e,"META-INF/manifest.xml"))return Ab(e,r);if(Ee(e,"objectdata.xml"))return Ab(e,r);if(Ee(e,"Index/Document.iwa"))throw new Error("Unsupported NUMBERS file");if(!Ee(e,"[Content_Types].xml")){if(Ee(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Ee(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var t=Be(e);var a=Pa(Se(e,"[Content_Types].xml"));var n=false;var i,s;if(a.workbooks.length===0){s="xl/workbook.xml";if(ke(e,s,true))a.workbooks.push(s)}if(a.workbooks.length===0){s="xl/workbook.bin";if(!ke(e,s,true))throw new Error("Could not find workbook");a.workbooks.push(s);n=true}if(a.workbooks[0].slice(-3)=="bin")n=true;var f={};var l={};if(!r.bookSheets&&!r.bookProps){ch=[];if(a.sst)try{ch=ip(ke(e,Jb(a.sst)),a.sst,r)}catch(o){if(r.WTF)throw o}if(r.cellStyles&&a.themes.length)f=np(Se(e,a.themes[0].replace(/^\//,""),true)||"",a.themes[0],r);if(a.style)l=ap(ke(e,Jb(a.style)),a.style,f,r)}a.links.map(function(t){try{var a=Wa(Se(e,Ha(Jb(t))),t);return lp(ke(e,Jb(t)),a,t,r)}catch(n){}});var c=qv(ke(e,Jb(a.workbooks[0])),a.workbooks[0],r);var u={},h="";if(a.coreprops.length){h=ke(e,Jb(a.coreprops[0]),true);if(h)u=rn(h);if(a.extprops.length!==0){h=ke(e,Jb(a.extprops[0]),true);if(h)on(h,u,r)}}var d={};if(!r.bookSheets||r.bookProps){if(a.custprops.length!==0){h=Se(e,Jb(a.custprops[0]),true);if(h)d=dn(h,r)}}var v={};if(r.bookSheets||r.bookProps){if(c.Sheets)i=c.Sheets.map(function I(e){return e.name});else if(u.Worksheets&&u.SheetNames.length>0)i=u.SheetNames;if(r.bookProps){v.Props=u;v.Custprops=d}if(r.bookSheets&&typeof i!=="undefined")v.SheetNames=i;if(r.bookSheets?v.SheetNames:r.bookProps)return v}i={};var p={};if(r.bookDeps&&a.calcchain)p=fp(ke(e,Jb(a.calcchain)),a.calcchain,r);var b=0;var m={};var g,E;{var w=c.Sheets;u.Worksheets=w.length;u.SheetNames=[];for(var k=0;k!=w.length;++k){u.SheetNames[k]=w[k].name}}var S=n?"bin":"xml";var B=a.workbooks[0].lastIndexOf("/");var C=(a.workbooks[0].slice(0,B+1)+"_rels/"+a.workbooks[0].slice(B+1)+".rels").replace(/^\//,"");if(!Ee(e,C))C="xl/_rels/workbook."+S+".rels";var T=Wa(Se(e,C,true),C);if(T)T=Yb(T,c.Sheets);var _=ke(e,"xl/worksheets/sheet.xml",true)?1:0;e:for(b=0;b!=u.Worksheets;++b){var x="sheet";if(T&&T[b]){g="xl/"+T[b][1].replace(/[\/]?xl\//,"");if(!Ee(e,g))g=T[b][1];if(!Ee(e,g))g=C.replace(/_rels\/.*$/,"")+T[b][1];x=T[b][2]}else{g="xl/worksheets/sheet"+(b+1-_)+"."+S;g=g.replace(/sheet0\./,"sheet.")}E=g.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels");if(r&&r.sheets!=null)switch(typeof r.sheets){case"number":if(b!=r.sheets)continue e;break;case"string":if(u.SheetNames[b].toLowerCase()!=r.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(r.sheets)){var A=false;for(var y=0;y!=r.sheets.length;++y){
if(typeof r.sheets[y]=="number"&&r.sheets[y]==b)A=1;if(typeof r.sheets[y]=="string"&&r.sheets[y].toLowerCase()==u.SheetNames[b].toLowerCase())A=1}if(!A)continue e};}Qb(e,g,E,u.SheetNames[b],b,m,i,x,r,c,f,l)}v={Directory:a,Workbook:c,Props:u,Custprops:d,Deps:p,Sheets:i,SheetNames:u.SheetNames,Strings:ch,Styles:l,Themes:f,SSF:D.get_table()};if(r&&r.bookFiles){if(e.files){v.keys=t;v.files=e.files}else{v.keys=[];v.files={};e.FullPaths.forEach(function(r,t){r=r.replace(/^Root Entry[\/]/,"");v.keys.push(r);v.files[r]=e.FileIndex[t]})}}if(r&&r.bookVBA){if(a.vba.length>0)v.vbaraw=ke(e,Jb(a.vba[0]),true);else if(a.defaults&&a.defaults.bin===qo)v.vbaraw=ke(e,"xl/vbaProject.bin",true)}return v}function Zb(e,r){var t=r||{};var a="Workbook",n=V.find(e,a);try{a="/!DataSpaces/Version";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);Wf(n.content);a="/!DataSpaces/DataSpaceMap";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=Xf(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=Gf(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);jf(n.content)}catch(f){}a="/EncryptionInfo";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var l=Yf(n.content);a="/EncryptedPackage";n=V.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(l[0]==4&&typeof decrypt_agile!=="undefined")return decrypt_agile(l[1],n.content,t.password||"",t);if(l[0]==2&&typeof decrypt_std76!=="undefined")return decrypt_std76(l[1],n.content,t.password||"",t);throw new Error("File is password-protected")}function em(e,r){Ho=1024;if(r.bookType=="ods")return Fb(e,r);if(e&&!e.SSF){e.SSF=D.get_table()}if(e&&e.SSF){O(D);D.load_table(e.SSF);r.revssf=J(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF}r.rels={};r.wbrels={};r.Strings=[];r.Strings.Count=0;r.Strings.Unique=0;if(hh)r.revStrings=new Map;else{r.revStrings={};r.revStrings.foo=[];delete r.revStrings.foo}var t=r.bookType=="xlsb"?"bin":"xml";var a=rc.indexOf(r.bookType)>-1;var n=Oa();$b(r=r||{});var i=_e();var s="",f=0;r.cellXfs=[];bh(r.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};s="docProps/core.xml";Ce(i,s,nn(e.Props,r));n.coreprops.push(s);za(r.rels,2,s,Ua.CORE_PROPS);s="docProps/app.xml";if(e.Props&&e.Props.SheetNames){}else if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{var l=[];for(var o=0;o<e.SheetNames.length;++o)if((e.Workbook.Sheets[o]||{}).Hidden!=2)l.push(e.SheetNames[o]);e.Props.SheetNames=l}e.Props.Worksheets=e.Props.SheetNames.length;Ce(i,s,un(e.Props,r));n.extprops.push(s);za(r.rels,3,s,Ua.EXT_PROPS);if(e.Custprops!==e.Props&&K(e.Custprops||{}).length>0){s="docProps/custom.xml";Ce(i,s,pn(e.Custprops,r));n.custprops.push(s);za(r.rels,4,s,Ua.CUST_PROPS)}for(f=1;f<=e.SheetNames.length;++f){var c={"!id":{}};var u=e.Sheets[e.SheetNames[f-1]];var h=(u||{})["!type"]||"sheet";switch(h){case"chart":;default:s="xl/worksheets/sheet"+f+"."+t;Ce(i,s,cp(f-1,s,r,e,c));n.sheets.push(s);za(r.wbrels,-1,"worksheets/sheet"+f+"."+t,Ua.WS[0]);}if(u){var d=u["!comments"];var v=false;if(d&&d.length>0){var p="xl/comments"+f+"."+t;Ce(i,p,vp(d,p,r));n.comments.push(p);za(c,-1,"../comments"+f+"."+t,Ua.CMNT);v=true}if(u["!legacy"]){if(v)Ce(i,"xl/drawings/vmlDrawing"+f+".vml",Wo(f,u["!comments"]))}delete u["!comments"];delete u["!legacy"]}if(c["!id"].rId1)Ce(i,Ha(s),Xa(c))}if(r.Strings!=null&&r.Strings.length>0){s="xl/sharedStrings."+t;Ce(i,s,dp(r.Strings,s,r));n.strs.push(s);za(r.wbrels,-1,"sharedStrings."+t,Ua.SST)}s="xl/workbook."+t;Ce(i,s,op(e,s,r));n.workbooks.push(s);za(r.rels,1,s,Ua.WB);s="xl/theme/theme1.xml";Ce(i,s,To(e.Themes,r));n.themes.push(s);za(r.wbrels,-1,"theme/theme1.xml",Ua.THEME);s="xl/styles."+t;Ce(i,s,hp(e,s,r));n.styles.push(s);za(r.wbrels,-1,"styles."+t,Ua.STY);if(e.vbaraw&&a){s="xl/vbaProject.bin";Ce(i,s,e.vbaraw);n.vba.push(s);za(r.wbrels,-1,"vbaProject.bin",Ua.VBA)}Ce(i,"[Content_Types].xml",La(n,r));Ce(i,"_rels/.rels",Xa(r.rels));Ce(i,"xl/_rels/workbook."+t+".rels",Xa(r.wbrels));delete r.revssf;delete r.ssf;return i}function rm(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=g.decode(e.slice(0,12));break;case"binary":t=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(r&&r.type||"undefined"));}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function tm(e,r){if(V.find(e,"EncryptedPackage"))return Zb(e,r);return Qp(e,r)}function am(e,r){var t,a=e;var n=r||{};if(!n.type)n.type=E&&Buffer.isBuffer(e)?"buffer":"base64";t=xe(a,n);return qb(t,n)}function nm(e,r){var t=0;e:while(t<e.length)switch(e.charCodeAt(t)){case 10:;case 13:;case 32:++t;break;case 60:return Ap(e.slice(t),r);default:break e;}return gf.to_workbook(e,r)}function im(e,r){var t="",a=rm(e,r);switch(r.type){case"base64":t=g.decode(e);break;case"binary":t=e;break;case"buffer":t=e.toString("binary");break;case"array":t=oe(e);break;default:throw new Error("Unrecognized type "+r.type);}if(a[0]==239&&a[1]==187&&a[2]==191)t=Ye(t);return nm(t,r)}function sm(e,r){var t=e;if(r.type=="base64")t=g.decode(t);t=cptable.utils.decode(1200,t.slice(2),"str");r.type="binary";return nm(t,r)}function fm(e){return!e.match(/[^\x00-\x7F]/)?e:Qe(e)}function lm(e,r,t,a){if(a){t.type="string";return gf.to_workbook(e,t)}return gf.to_workbook(r,t)}function om(e,r){o();var t=r||{};if(typeof ArrayBuffer!=="undefined"&&e instanceof ArrayBuffer)return om(new Uint8Array(e),(t=ce(t),t.type="array",t));var a=e,n=[0,0,0,0],i=false;if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}uh={};if(t.dateNF)uh.dateNF=t.dateNF;if(!t.type)t.type=E&&Buffer.isBuffer(e)?"buffer":"base64";if(t.type=="file"){t.type=E?"buffer":"binary";a=j(e)}if(t.type=="string"){i=true;t.type="binary";t.codepage=65001;a=fm(e)}if(t.type=="array"&&typeof Uint8Array!=="undefined"&&e instanceof Uint8Array&&typeof ArrayBuffer!=="undefined"){var s=new ArrayBuffer(3),f=new Uint8Array(s);f.foo="bar";if(!f.foo){t=ce(t);t.type="array";return om(y(a),t)}}switch((n=rm(a,t))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return tm(V.read(a,t),t);break;case 9:if(n[1]<=8)return Qp(a,t);break;case 60:return Ap(a,t);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return Ef(a,t);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return bf.to_workbook(a,t);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?am(a,t):lm(e,a,t,i);case 239:return n[3]===60?Ap(a,t):lm(e,a,t,i);case 255:if(n[1]===254){return sm(a,t)}else if(n[1]===0&&n[2]===2&&n[3]===0)return wf.to_workbook(a,t);break;case 0:if(n[1]===0){if(n[2]>=2&&n[3]===0)return wf.to_workbook(a,t);if(n[2]===0&&(n[3]===8||n[3]===9))return wf.to_workbook(a,t)}break;case 3:;case 131:;case 139:;case 140:return vf.to_workbook(a,t);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return ll.to_workbook(a,t);break;case 10:;case 13:;case 32:return im(a,t);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break;}if(vf.versions.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31)return vf.to_workbook(a,t);return lm(e,a,t,i)}function cm(e,r){var t=r||{};t.type="file";return om(e,t)}function um(e,r){switch(r.type){case"base64":;case"binary":break;case"buffer":;case"array":r.type="";break;case"file":return z(r.file,V.write(e,{type:E?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");default:throw new Error("Unrecognized type "+r.type);}return V.write(e,r)}function hm(e,r){var t=ce(r||{});var a=em(e,t);var n={};if(t.compression)n.compression="DEFLATE";if(t.password)n.type=E?"nodebuffer":"string";else switch(t.type){case"base64":n.type="base64";break;case"binary":n.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":;case"file":n.type=E?"nodebuffer":"string";break;default:throw new Error("Unrecognized type "+t.type);}var i=a.FullPaths?V.write(a,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[n.type]||n.type}):a.generate(n);if(t.password&&typeof encrypt_agile!=="undefined")return um(encrypt_agile(i,t.password),t);if(t.type==="file")return z(t.file,i);return t.type=="string"?Ye(i):i}function dm(e,r){var t=r||{};var a=Jp(e,t);return um(a,t)}function vm(e,r,t){if(!t)t="";var a=t+e;switch(r.type){case"base64":return g.encode(Qe(a));case"binary":return Qe(a);case"string":return e;case"file":return z(r.file,a,"utf8");case"buffer":{if(E)return w(a,"utf8");else return vm(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function pm(e,r){switch(r.type){case"base64":return g.encode(e);case"binary":return e;case"string":return e;case"file":return z(r.file,e,"binary");case"buffer":{if(E)return w(e,"binary");else return e.split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function bm(e,r){switch(r.type){case"string":;case"base64":;case"binary":var t="";for(var a=0;a<e.length;++a)t+=String.fromCharCode(e[a]);return r.type=="base64"?g.encode(t):r.type=="string"?Ye(t):t;case"file":return z(r.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+r.type);}}function mm(e,r){o();Pv(e);var t=ce(r||{});if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}if(t.type=="array"){t.type="binary";var a=mm(e,t);t.type="array";return _(a)}switch(t.bookType||"xlsb"){case"xml":;case"xlml":return vm(Wp(e,t),t);case"slk":;case"sylk":return vm(Mb(e,t),t);case"htm":;case"html":return vm(Pb(e,t),t);case"txt":return pm(Wb(e,t),t);case"csv":return vm(Nb(e,t),t,"\ufeff");case"dif":return vm(Lb(e,t),t);case"dbf":return bm(Vb(e,t),t);case"prn":return vm(Ub(e,t),t);case"rtf":return vm(Hb(e,t),t);case"eth":return vm(Xb(e,t),t);case"fods":return vm(Fb(e,t),t);case"wk1":return bm(Gb(e,t),t);case"wk3":return bm(wf.book_to_wk3(e,t),t);case"biff2":if(!t.biff)t.biff=2;case"biff3":if(!t.biff)t.biff=3;case"biff4":if(!t.biff)t.biff=4;return bm(wb(e,t),t);case"biff5":if(!t.biff)t.biff=5;case"biff8":;case"xla":;case"xls":if(!t.biff)t.biff=8;return dm(e,t);case"xlsx":;case"xlsm":;case"xlam":;case"xlsb":;case"ods":return hm(e,t);default:throw new Error("Unrecognized bookType |"+t.bookType+"|");}}function gm(e){if(e.bookType)return;var r={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"};var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();if(t.match(/^\.[a-z]+$/))e.bookType=t.slice(1);e.bookType=r[e.bookType]||e.bookType}function Em(e,r,t){var a=t||{};a.type="file";a.file=r;gm(a);return mm(e,a)}function wm(e,r,t,a){var n=t||{};n.type="file";n.file=e;gm(n);n.type="buffer";var i=a;if(!(i instanceof Function))i=t;return X.writeFile(e,mm(r,n),i)}function km(e,r,t,a,n,i,s,f){var l=lt(t);var o=f.defval,c=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw");var u=true;var h=n===1?[]:{};if(n!==1){if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:t,enumerable:false})}catch(d){h.__rowNum__=t}else h.__rowNum__=t}if(!s||e[t])for(var v=r.s.c;v<=r.e.c;++v){var p=s?e[t][v]:e[a[v]+l];if(p===undefined||p.t===undefined){if(o===undefined)continue;if(i[v]!=null){h[i[v]]=o}continue}var b=p.v;switch(p.t){case"z":if(b==null)break;continue;case"e":b=b==0?null:void 0;break;case"s":;case"d":;case"b":;case"n":break;default:throw new Error("unrecognized type "+p.t);}if(i[v]!=null){if(b==null){if(p.t=="e"&&b===null)h[i[v]]=null;else if(o!==undefined)h[i[v]]=o;else if(c&&b===null)h[i[v]]=null;else continue}else{h[i[v]]=c||f.rawNumbers&&p.t=="n"?b:St(p,b,f)}if(b!=null)u=false}}return{row:h,isempty:u}}function Sm(e,r){if(e==null||e["!ref"]==null)return[];var t={t:"n",v:0},a=0,n=1,i=[],s=0,f="";var l={s:{r:0,c:0},e:{r:0,c:0}};var o=r||{};var c=o.range!=null?o.range:e["!ref"];if(o.header===1)a=1;else if(o.header==="A")a=2;else if(Array.isArray(o.header))a=3;else if(o.header==null)a=0;switch(typeof c){case"string":l=wt(c);break;case"number":l=wt(e["!ref"]);l.s.r=c;break;default:l=c;}if(a>0)n=0;var u=lt(l.s.r);var h=[];var d=[];var v=0,p=0;var b=Array.isArray(e);var m=l.s.r,g=0,E=0;if(b&&!e[m])e[m]=[];for(g=l.s.c;g<=l.e.c;++g){h[g]=ht(g);t=b?e[m][g]:e[h[g]+u];switch(a){case 1:i[g]=g-l.s.c;break;case 2:i[g]=h[g];break;case 3:i[g]=o.header[g-l.s.c];break;default:if(t==null)t={w:"__EMPTY",t:"s"};f=s=St(t,null,o);p=0;for(E=0;E<i.length;++E)if(i[E]==f)f=s+"_"+ ++p;i[g]=f;}}for(m=l.s.r+n;m<=l.e.r;++m){var w=km(e,l,m,h,a,i,b,o);if(w.isempty===false||(a===1?o.blankrows!==false:!!o.blankrows))d[v++]=w.row}d.length=v;return d}var Bm=/"/g;function Cm(e,r,t,a,n,i,s,f){var l=true;var o=[],c="",u=lt(t);for(var h=r.s.c;h<=r.e.c;++h){if(!a[h])continue;var d=f.dense?(e[t]||[])[h]:e[a[h]+u];if(d==null)c="";else if(d.v!=null){l=false;c=""+(f.rawNumbers&&d.t=="n"?d.v:St(d,null,f));for(var v=0,p=0;v!==c.length;++v)if((p=c.charCodeAt(v))===n||p===i||p===34||f.forceQuotes){c='"'+c.replace(Bm,'""')+'"';break}if(c=="ID")c='"ID"'}else if(d.f!=null&&!d.F){l=false;c="="+d.f;if(c.indexOf(",")>=0)c='"'+c.replace(Bm,'""')+'"'}else c="";o.push(c)}if(f.blankrows===false&&l)return null;return o.join(s)}function Tm(e,r){var t=[];var a=r==null?{}:r;if(e==null||e["!ref"]==null)return"";var n=wt(e["!ref"]);var i=a.FS!==undefined?a.FS:",",s=i.charCodeAt(0);var f=a.RS!==undefined?a.RS:"\n",l=f.charCodeAt(0);var o=new RegExp((i=="|"?"\\|":i)+"+$");var c="",u=[];a.dense=Array.isArray(e);var h=a.skipHidden&&e["!cols"]||[];var d=a.skipHidden&&e["!rows"]||[];for(var v=n.s.c;v<=n.e.c;++v)if(!(h[v]||{}).hidden)u[v]=ht(v);for(var p=n.s.r;p<=n.e.r;++p){if((d[p]||{}).hidden)continue;c=Cm(e,n,p,u,s,l,i,a);if(c==null){continue}if(a.strip)c=c.replace(o,"");t.push(c+f)}delete a.dense;return t.join("")}function _m(e,r){if(!r)r={};r.FS="\t";r.RS="\n";var t=Tm(e,r);if(typeof cptable=="undefined"||r.type=="string")return t;var a=cptable.utils.encode(1200,t,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function xm(e){var r="",t,a="";if(e==null||e["!ref"]==null)return[];var n=wt(e["!ref"]),i="",s=[],f;var l=[];var o=Array.isArray(e);for(f=n.s.c;f<=n.e.c;++f)s[f]=ht(f);for(var c=n.s.r;c<=n.e.r;++c){i=lt(c);for(f=n.s.c;f<=n.e.c;++f){r=s[f]+i;t=o?(e[c]||[])[f]:e[r];a="";if(t===undefined)continue;else if(t.F!=null){r=t.F;if(!t.f)continue;a=t.f;if(r.indexOf(":")==-1)r=r+":"+r}if(t.f!=null)a=t.f;else if(t.t=="z")continue;else if(t.t=="n"&&t.v!=null)a=""+t.v;else if(t.t=="b")a=t.v?"TRUE":"FALSE";else if(t.w!==undefined)a="'"+t.w;else if(t.v===undefined)continue;else if(t.t=="s")a="'"+t.v;else a=""+t.v;l[l.length]=r+"="+a}}return l}function Am(e,r,t){var a=t||{};var n=+!a.skipHeader;var i=e||{};var s=0,f=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var l=typeof a.origin=="string"?bt(a.origin):a.origin;s=l.r;f=l.c}}var o;var c={s:{c:0,r:0},e:{c:f,r:s+r.length-1+n}};if(i["!ref"]){var u=wt(i["!ref"]);c.e.c=Math.max(c.e.c,u.e.c);c.e.r=Math.max(c.e.r,u.e.r);if(s==-1){s=u.e.r+1;c.e.r=s+r.length-1+n}}else{if(s==-1){s=0;c.e.r=r.length-1+n}}var h=a.header||[],d=0;r.forEach(function(e,r){K(e).forEach(function(t){if((d=h.indexOf(t))==-1)h[d=h.length]=t;var l=e[t];var c="z";var u="";var v=mt({c:f+d,r:s+r+n});o=Im.sheet_get_cell(i,v);if(l&&typeof l==="object"&&!(l instanceof Date)){i[v]=l}else{if(typeof l=="number")c="n";else if(typeof l=="boolean")c="b";else if(typeof l=="string")c="s";else if(l instanceof Date){c="d";if(!a.cellDates){c="n";l=ee(l)}u=a.dateNF||D._table[14]}else if(l===null&&a.nullError){c="e";l=0}if(!o)i[v]=o={t:c,v:l};else{o.t=c;o.v=l;delete o.w;delete o.R;if(u)o.z=u}if(u)o.z=u}})});c.e.c=Math.max(c.e.c,f+h.length-1);var v=lt(s);if(n)for(d=0;d<h.length;++d)i[ht(d+f)+v]={t:"s",v:h[d]};i["!ref"]=Et(c);return i}function ym(e,r){return Am(null,e,r)}var Im={encode_col:ht,encode_row:lt,encode_cell:mt,encode_range:Et,decode_col:ut,decode_row:ft,split_cell:pt,decode_cell:bt,decode_range:gt,format_cell:St,get_formulae:xm,make_csv:Tm,make_json:Sm,make_formulae:xm,sheet_add_aoa:Ct,sheet_add_json:Am,sheet_add_dom:Sb,aoa_to_sheet:Tt,json_to_sheet:ym,table_to_sheet:Bb,table_to_book:Cb,sheet_to_csv:Tm,sheet_to_txt:_m,sheet_to_json:Sm,sheet_to_html:kb.from_sheet,sheet_to_formulae:xm,sheet_to_row_object_array:Sm};(function(e){e.consts=e.consts||{};function r(r){r.forEach(function(r){e.consts[r[0]]=r[1]})}function t(e,r,t){return e[r]!=null?e[r]:e[r]=t}function a(e,r,t){if(typeof r=="string"){if(Array.isArray(e)){var n=bt(r);if(!e[n.r])e[n.r]=[];return e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[r]||(e[r]={t:"z"})}if(typeof r!="number")return a(e,mt(r));return a(e,mt({r:r,c:t||0}))}e.sheet_get_cell=a;function n(e,r){if(typeof r=="number"){if(r>=0&&e.SheetNames.length>r)return r;throw new Error("Cannot find sheet # "+r)}else if(typeof r=="string"){var t=e.SheetNames.indexOf(r);if(t>-1)return t;throw new Error("Cannot find sheet name |"+r+"|")}else throw new Error("Cannot find sheet |"+r+"|")}e.book_new=function(){return{SheetNames:[],Sheets:{}}};e.book_append_sheet=function(e,r,t){if(!t)for(var a=1;a<=65535;++a,t=undefined)if(e.SheetNames.indexOf(t="Sheet"+a)==-1)break;if(!t||e.SheetNames.length>=65535)throw new Error("Too many worksheets");Dv(t);if(e.SheetNames.indexOf(t)>=0)throw new Error("Worksheet with name |"+t+"| already exists!");e.SheetNames.push(t);e.Sheets[t]=r};e.book_set_sheet_visibility=function(e,r,a){t(e,"Workbook",{});t(e.Workbook,"Sheets",[]);var i=n(e,r);t(e.Workbook.Sheets,i,{});switch(a){case 0:;case 1:;case 2:break;default:throw new Error("Bad sheet visibility setting "+a);}e.Workbook.Sheets[i].Hidden=a};r([["SHEET_VISIBLE",0],["SHEET_HIDDEN",1],["SHEET_VERY_HIDDEN",2]]);e.cell_set_number_format=function(e,r){e.z=r;return e};e.cell_set_hyperlink=function(e,r,t){if(!r){delete e.l}else{e.l={Target:r};if(t)e.l.Tooltip=t}return e};e.cell_set_internal_link=function(r,t,a){return e.cell_set_hyperlink(r,"#"+t,a)};e.cell_add_comment=function(e,r,t){if(!e.c)e.c=[];e.c.push({t:r,a:t||"SheetJS"})};e.sheet_set_array_formula=function(e,r,t){var n=typeof r!="string"?r:wt(r);var i=typeof r=="string"?r:Et(r);for(var s=n.s.r;s<=n.e.r;++s)for(var f=n.s.c;f<=n.e.c;++f){var l=a(e,s,f);l.t="n";l.F=i;delete l.v;if(s==n.s.r&&f==n.s.c)l.f=t}return e};return e})(Im);if(E&&typeof require!="undefined")(function(){var r=undefined;if(!r)return;var t=r.Readable;if(!t)return;var a=function(e,r){var a=t();var n=r==null?{}:r;if(e==null||e["!ref"]==null){a.push(null);return a}var i=wt(e["!ref"]);var s=n.FS!==undefined?n.FS:",",f=s.charCodeAt(0);var l=n.RS!==undefined?n.RS:"\n",o=l.charCodeAt(0);var c=new RegExp((s=="|"?"\\|":s)+"+$");var u="",h=[];n.dense=Array.isArray(e);var d=n.skipHidden&&e["!cols"]||[];var v=n.skipHidden&&e["!rows"]||[];for(var p=i.s.c;p<=i.e.c;++p)if(!(d[p]||{}).hidden)h[p]=ht(p);var b=i.s.r;var m=false;a._read=function(){if(!m){m=true;return a.push("\ufeff")}while(b<=i.e.r){++b;if((v[b-1]||{}).hidden)continue;u=Cm(e,i,b-1,h,f,o,s,n);if(u!=null){if(n.strip)u=u.replace(c,"");a.push(u+l);break}}if(b>i.e.r)return a.push(null)};return a};var n=function(e,r){var a=t();var n=r||{};var i=n.header!=null?n.header:kb.BEGIN;var s=n.footer!=null?n.footer:kb.END;a.push(i);var f=gt(e["!ref"]);n.dense=Array.isArray(e);a.push(kb._preamble(e,f,n));var l=f.s.r;var o=false;a._read=function(){if(l>f.e.r){if(!o){o=true;a.push("</table>"+s)}return a.push(null)}while(l<=f.e.r){a.push(kb._row(e,f,l,n));++l;break}};return a};var i=function(e,r){var a=t({objectMode:true});if(e==null||e["!ref"]==null){a.push(null);return a}var n={t:"n",v:0},i=0,s=1,f=[],l=0,o="";var c={s:{r:0,c:0},e:{r:0,c:0}};var u=r||{};var h=u.range!=null?u.range:e["!ref"];if(u.header===1)i=1;else if(u.header==="A")i=2;else if(Array.isArray(u.header))i=3;switch(typeof h){case"string":c=wt(h);break;case"number":c=wt(e["!ref"]);c.s.r=h;break;default:c=h;}if(i>0)s=0;var d=lt(c.s.r);var v=[];var p=0;var b=Array.isArray(e);var m=c.s.r,g=0,E=0;if(b&&!e[m])e[m]=[];for(g=c.s.c;g<=c.e.c;++g){v[g]=ht(g);n=b?e[m][g]:e[v[g]+d];switch(i){case 1:f[g]=g-c.s.c;break;case 2:f[g]=v[g];break;case 3:f[g]=u.header[g-c.s.c];break;default:if(n==null)n={w:"__EMPTY",t:"s"};o=l=St(n,null,u);p=0;for(E=0;E<f.length;++E)if(f[E]==o)o=l+"_"+ ++p;f[g]=o;}}m=c.s.r+s;a._read=function(){if(m>c.e.r)return a.push(null);while(m<=c.e.r){var r=km(e,c,m,v,i,f,b,u);++m;if(r.isempty===false||(i===1?u.blankrows!==false:!!u.blankrows)){a.push(r.row);break}}};return a};e.stream={to_json:i,to_html:n,to_csv:a}})();if(typeof Qp!=="undefined")e.parse_xlscfb=Qp;e.parse_zip=qb;e.read=om;e.readFile=cm;e.readFileSync=cm;e.write=mm;e.writeFile=Em;e.writeFileSync=Em;e.writeFileAsync=wm;e.utils=Im;e.SSF=D;if(typeof V!=="undefined")e.CFB=V}if(typeof exports!=="undefined")make_xlsx_lib(exports);else if(typeof module!=="undefined"&&module.exports)make_xlsx_lib(module.exports);else if(typeof define==="function"&&define.amd)define("xlsx",function(){if(!XLSX.version)make_xlsx_lib(XLSX);return XLSX});else make_xlsx_lib(XLSX);if(typeof window!=="undefined"&&!window.XLSX)try{window.XLSX=XLSX}catch(e){}var XLS=XLSX,ODS=XLSX;
