{"ast": null, "code": "import InternalLayout, { Content, Footer, Header } from './layout';\nimport Sider from './Sider';\nvar Layout = InternalLayout;\nLayout.Header = Header;\nLayout.Footer = Footer;\nLayout.Content = Content;\nLayout.Sider = Sider;\nexport default Layout;", "map": {"version": 3, "names": ["InternalLayout", "Content", "Footer", "Header", "<PERSON><PERSON>", "Layout"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/layout/index.js"], "sourcesContent": ["import InternalLayout, { Content, Footer, Header } from './layout';\nimport Sider from './Sider';\nvar Layout = InternalLayout;\nLayout.Header = Header;\nLayout.Footer = Footer;\nLayout.Content = Content;\nLayout.Sider = Sider;\nexport default Layout;"], "mappings": "AAAA,OAAOA,cAAc,IAAIC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,UAAU;AAClE,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,MAAM,GAAGL,cAAc;AAC3BK,MAAM,CAACF,MAAM,GAAGA,MAAM;AACtBE,MAAM,CAACH,MAAM,GAAGA,MAAM;AACtBG,MAAM,CAACJ,OAAO,GAAGA,OAAO;AACxBI,MAAM,CAACD,KAAK,GAAGA,KAAK;AACpB,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}