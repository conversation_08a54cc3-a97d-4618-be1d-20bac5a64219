{"ast": null, "code": "import * as React from 'react';\nimport HeaderRow from './HeaderRow';\nimport TableContext from '../context/TableContext';\nfunction parseHeaderRows(rootColumns) {\n  var rows = [];\n  function fillRowCells(columns, colIndex) {\n    var rowIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    // Init rows\n    rows[rowIndex] = rows[rowIndex] || [];\n    var currentColIndex = colIndex;\n    var colSpans = columns.filter(Boolean).map(function (column) {\n      var cell = {\n        key: column.key,\n        className: column.className || '',\n        children: column.title,\n        column: column,\n        colStart: currentColIndex\n      };\n      var colSpan = 1;\n      var subColumns = column.children;\n      if (subColumns && subColumns.length > 0) {\n        colSpan = fillRowCells(subColumns, currentColIndex, rowIndex + 1).reduce(function (total, count) {\n          return total + count;\n        }, 0);\n        cell.hasSubColumns = true;\n      }\n      if ('colSpan' in column) {\n        colSpan = column.colSpan;\n      }\n      if ('rowSpan' in column) {\n        cell.rowSpan = column.rowSpan;\n      }\n      cell.colSpan = colSpan;\n      cell.colEnd = cell.colStart + colSpan - 1;\n      rows[rowIndex].push(cell);\n      currentColIndex += colSpan;\n      return colSpan;\n    });\n    return colSpans;\n  } // Generate `rows` cell data\n\n  fillRowCells(rootColumns, 0); // Handle `rowSpan`\n\n  var rowCount = rows.length;\n  var _loop = function _loop(rowIndex) {\n    rows[rowIndex].forEach(function (cell) {\n      if (!('rowSpan' in cell) && !cell.hasSubColumns) {\n        // eslint-disable-next-line no-param-reassign\n        cell.rowSpan = rowCount - rowIndex;\n      }\n    });\n  };\n  for (var rowIndex = 0; rowIndex < rowCount; rowIndex += 1) {\n    _loop(rowIndex);\n  }\n  return rows;\n}\nfunction Header(_ref) {\n  var stickyOffsets = _ref.stickyOffsets,\n    columns = _ref.columns,\n    flattenColumns = _ref.flattenColumns,\n    onHeaderRow = _ref.onHeaderRow;\n  var _React$useContext = React.useContext(TableContext),\n    prefixCls = _React$useContext.prefixCls,\n    getComponent = _React$useContext.getComponent;\n  var rows = React.useMemo(function () {\n    return parseHeaderRows(columns);\n  }, [columns]);\n  var WrapperComponent = getComponent(['header', 'wrapper'], 'thead');\n  var trComponent = getComponent(['header', 'row'], 'tr');\n  var thComponent = getComponent(['header', 'cell'], 'th');\n  return /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-thead\")\n  }, rows.map(function (row, rowIndex) {\n    var rowNode = /*#__PURE__*/React.createElement(HeaderRow, {\n      key: rowIndex,\n      flattenColumns: flattenColumns,\n      cells: row,\n      stickyOffsets: stickyOffsets,\n      rowComponent: trComponent,\n      cellComponent: thComponent,\n      onHeaderRow: onHeaderRow,\n      index: rowIndex\n    });\n    return rowNode;\n  }));\n}\nexport default Header;", "map": {"version": 3, "names": ["React", "HeaderRow", "TableContext", "parseHeaderRows", "rootColumns", "rows", "fill<PERSON><PERSON><PERSON><PERSON>s", "columns", "colIndex", "rowIndex", "arguments", "length", "undefined", "currentColIndex", "colSpans", "filter", "Boolean", "map", "column", "cell", "key", "className", "children", "title", "colStart", "colSpan", "subColumns", "reduce", "total", "count", "hasSubColumns", "rowSpan", "colEnd", "push", "rowCount", "_loop", "for<PERSON>ach", "Header", "_ref", "stickyOffsets", "flattenColumns", "onHeaderRow", "_React$useContext", "useContext", "prefixCls", "getComponent", "useMemo", "WrapperComponent", "trComponent", "thComponent", "createElement", "concat", "row", "rowNode", "cells", "rowComponent", "cellComponent", "index"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Header/Header.js"], "sourcesContent": ["import * as React from 'react';\nimport HeaderRow from './HeaderRow';\nimport TableContext from '../context/TableContext';\n\nfunction parseHeaderRows(rootColumns) {\n  var rows = [];\n\n  function fillRowCells(columns, colIndex) {\n    var rowIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    // Init rows\n    rows[rowIndex] = rows[rowIndex] || [];\n    var currentColIndex = colIndex;\n    var colSpans = columns.filter(Boolean).map(function (column) {\n      var cell = {\n        key: column.key,\n        className: column.className || '',\n        children: column.title,\n        column: column,\n        colStart: currentColIndex\n      };\n      var colSpan = 1;\n      var subColumns = column.children;\n\n      if (subColumns && subColumns.length > 0) {\n        colSpan = fillRowCells(subColumns, currentColIndex, rowIndex + 1).reduce(function (total, count) {\n          return total + count;\n        }, 0);\n        cell.hasSubColumns = true;\n      }\n\n      if ('colSpan' in column) {\n        colSpan = column.colSpan;\n      }\n\n      if ('rowSpan' in column) {\n        cell.rowSpan = column.rowSpan;\n      }\n\n      cell.colSpan = colSpan;\n      cell.colEnd = cell.colStart + colSpan - 1;\n      rows[rowIndex].push(cell);\n      currentColIndex += colSpan;\n      return colSpan;\n    });\n    return colSpans;\n  } // Generate `rows` cell data\n\n\n  fillRowCells(rootColumns, 0); // Handle `rowSpan`\n\n  var rowCount = rows.length;\n\n  var _loop = function _loop(rowIndex) {\n    rows[rowIndex].forEach(function (cell) {\n      if (!('rowSpan' in cell) && !cell.hasSubColumns) {\n        // eslint-disable-next-line no-param-reassign\n        cell.rowSpan = rowCount - rowIndex;\n      }\n    });\n  };\n\n  for (var rowIndex = 0; rowIndex < rowCount; rowIndex += 1) {\n    _loop(rowIndex);\n  }\n\n  return rows;\n}\n\nfunction Header(_ref) {\n  var stickyOffsets = _ref.stickyOffsets,\n      columns = _ref.columns,\n      flattenColumns = _ref.flattenColumns,\n      onHeaderRow = _ref.onHeaderRow;\n\n  var _React$useContext = React.useContext(TableContext),\n      prefixCls = _React$useContext.prefixCls,\n      getComponent = _React$useContext.getComponent;\n\n  var rows = React.useMemo(function () {\n    return parseHeaderRows(columns);\n  }, [columns]);\n  var WrapperComponent = getComponent(['header', 'wrapper'], 'thead');\n  var trComponent = getComponent(['header', 'row'], 'tr');\n  var thComponent = getComponent(['header', 'cell'], 'th');\n  return /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-thead\")\n  }, rows.map(function (row, rowIndex) {\n    var rowNode = /*#__PURE__*/React.createElement(HeaderRow, {\n      key: rowIndex,\n      flattenColumns: flattenColumns,\n      cells: row,\n      stickyOffsets: stickyOffsets,\n      rowComponent: trComponent,\n      cellComponent: thComponent,\n      onHeaderRow: onHeaderRow,\n      index: rowIndex\n    });\n    return rowNode;\n  }));\n}\n\nexport default Header;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,yBAAyB;AAElD,SAASC,eAAeA,CAACC,WAAW,EAAE;EACpC,IAAIC,IAAI,GAAG,EAAE;EAEb,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACvC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACpF;IACAL,IAAI,CAACI,QAAQ,CAAC,GAAGJ,IAAI,CAACI,QAAQ,CAAC,IAAI,EAAE;IACrC,IAAII,eAAe,GAAGL,QAAQ;IAC9B,IAAIM,QAAQ,GAAGP,OAAO,CAACQ,MAAM,CAACC,OAAO,CAAC,CAACC,GAAG,CAAC,UAAUC,MAAM,EAAE;MAC3D,IAAIC,IAAI,GAAG;QACTC,GAAG,EAAEF,MAAM,CAACE,GAAG;QACfC,SAAS,EAAEH,MAAM,CAACG,SAAS,IAAI,EAAE;QACjCC,QAAQ,EAAEJ,MAAM,CAACK,KAAK;QACtBL,MAAM,EAAEA,MAAM;QACdM,QAAQ,EAAEX;MACZ,CAAC;MACD,IAAIY,OAAO,GAAG,CAAC;MACf,IAAIC,UAAU,GAAGR,MAAM,CAACI,QAAQ;MAEhC,IAAII,UAAU,IAAIA,UAAU,CAACf,MAAM,GAAG,CAAC,EAAE;QACvCc,OAAO,GAAGnB,YAAY,CAACoB,UAAU,EAAEb,eAAe,EAAEJ,QAAQ,GAAG,CAAC,CAAC,CAACkB,MAAM,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;UAC/F,OAAOD,KAAK,GAAGC,KAAK;QACtB,CAAC,EAAE,CAAC,CAAC;QACLV,IAAI,CAACW,aAAa,GAAG,IAAI;MAC3B;MAEA,IAAI,SAAS,IAAIZ,MAAM,EAAE;QACvBO,OAAO,GAAGP,MAAM,CAACO,OAAO;MAC1B;MAEA,IAAI,SAAS,IAAIP,MAAM,EAAE;QACvBC,IAAI,CAACY,OAAO,GAAGb,MAAM,CAACa,OAAO;MAC/B;MAEAZ,IAAI,CAACM,OAAO,GAAGA,OAAO;MACtBN,IAAI,CAACa,MAAM,GAAGb,IAAI,CAACK,QAAQ,GAAGC,OAAO,GAAG,CAAC;MACzCpB,IAAI,CAACI,QAAQ,CAAC,CAACwB,IAAI,CAACd,IAAI,CAAC;MACzBN,eAAe,IAAIY,OAAO;MAC1B,OAAOA,OAAO;IAChB,CAAC,CAAC;IACF,OAAOX,QAAQ;EACjB,CAAC,CAAC;;EAGFR,YAAY,CAACF,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE9B,IAAI8B,QAAQ,GAAG7B,IAAI,CAACM,MAAM;EAE1B,IAAIwB,KAAK,GAAG,SAASA,KAAKA,CAAC1B,QAAQ,EAAE;IACnCJ,IAAI,CAACI,QAAQ,CAAC,CAAC2B,OAAO,CAAC,UAAUjB,IAAI,EAAE;MACrC,IAAI,EAAE,SAAS,IAAIA,IAAI,CAAC,IAAI,CAACA,IAAI,CAACW,aAAa,EAAE;QAC/C;QACAX,IAAI,CAACY,OAAO,GAAGG,QAAQ,GAAGzB,QAAQ;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,KAAK,IAAIA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGyB,QAAQ,EAAEzB,QAAQ,IAAI,CAAC,EAAE;IACzD0B,KAAK,CAAC1B,QAAQ,CAAC;EACjB;EAEA,OAAOJ,IAAI;AACb;AAEA,SAASgC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;IAClChC,OAAO,GAAG+B,IAAI,CAAC/B,OAAO;IACtBiC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,WAAW,GAAGH,IAAI,CAACG,WAAW;EAElC,IAAIC,iBAAiB,GAAG1C,KAAK,CAAC2C,UAAU,CAACzC,YAAY,CAAC;IAClD0C,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;EAEjD,IAAIxC,IAAI,GAAGL,KAAK,CAAC8C,OAAO,CAAC,YAAY;IACnC,OAAO3C,eAAe,CAACI,OAAO,CAAC;EACjC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,IAAIwC,gBAAgB,GAAGF,YAAY,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;EACnE,IAAIG,WAAW,GAAGH,YAAY,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EACvD,IAAII,WAAW,GAAGJ,YAAY,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACxD,OAAO,aAAa7C,KAAK,CAACkD,aAAa,CAACH,gBAAgB,EAAE;IACxD1B,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAACP,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEvC,IAAI,CAACY,GAAG,CAAC,UAAUmC,GAAG,EAAE3C,QAAQ,EAAE;IACnC,IAAI4C,OAAO,GAAG,aAAarD,KAAK,CAACkD,aAAa,CAACjD,SAAS,EAAE;MACxDmB,GAAG,EAAEX,QAAQ;MACb+B,cAAc,EAAEA,cAAc;MAC9Bc,KAAK,EAAEF,GAAG;MACVb,aAAa,EAAEA,aAAa;MAC5BgB,YAAY,EAAEP,WAAW;MACzBQ,aAAa,EAAEP,WAAW;MAC1BR,WAAW,EAAEA,WAAW;MACxBgB,KAAK,EAAEhD;IACT,CAAC,CAAC;IACF,OAAO4C,OAAO;EAChB,CAAC,CAAC,CAAC;AACL;AAEA,eAAehB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}