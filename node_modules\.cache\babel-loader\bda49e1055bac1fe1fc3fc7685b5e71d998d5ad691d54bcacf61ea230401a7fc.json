{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\agenti\\\\messaggi.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Messaggi - messaggi dell'agente \n*\n*/\nimport React, { Component } from 'react';\nimport AvviaConversazione from '../../aggiunta_dati/avviaConversazione';\nimport Caricamento from '../../utils/caricamento';\nimport { Costanti } from '../../components/traduttore/const';\nimport Nav from '../../components/navigation/Nav';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Messaggi extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      role: localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : ''\n    };\n  }\n  componentDidMount() {\n    console.log(this.state.role);\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card form-complete border-0 creaOrdine\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Messaggi\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(AvviaConversazione, {\n        results: this.state.role\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Messaggi;", "map": {"version": 3, "names": ["React", "Component", "AvviaConversazione", "Caricamento", "<PERSON><PERSON>", "Nav", "jsxDEV", "_jsxDEV", "Messaggi", "constructor", "props", "state", "role", "localStorage", "getItem", "JSON", "parse", "componentDidMount", "console", "log", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "results"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/agenti/messaggi.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Messaggi - messaggi dell'agente \n*\n*/\nimport React, { Component } from 'react';\nimport AvviaConversazione from '../../aggiunta_dati/avviaConversazione';\nimport Caricamento from '../../utils/caricamento';\nimport { Costanti } from '../../components/traduttore/const';\nimport Nav from '../../components/navigation/Nav';\n\nclass Messaggi extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            role: localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : ''\n        }\n    }\n\n    componentDidMount() {\n        console.log(this.state.role)\n    }\n\n    render() {\n        return (\n            <div className=\"card form-complete border-0 creaOrdine\">\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.Messaggi}</h1>\n                </div>\n                <Caricamento />\n                <AvviaConversazione results={this.state.role} />\n            </div>\n        );\n    }\n}\n\nexport default Messaggi;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,kBAAkB,MAAM,wCAAwC;AACvE,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,OAAOC,GAAG,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,SAASP,SAAS,CAAC;EAC7BQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;IACpF,CAAC;EACL;EAEAG,iBAAiBA,CAAA,EAAG;IAChBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;EAChC;EAEAQ,MAAMA,CAAA,EAAG;IACL,oBACIb,OAAA;MAAKc,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACnDf,OAAA,CAACF,GAAG;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPnB,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACnCf,OAAA;UAAAe,QAAA,EAAKlB,QAAQ,CAACI;QAAQ;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACNnB,OAAA,CAACJ,WAAW;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfnB,OAAA,CAACL,kBAAkB;QAACyB,OAAO,EAAE,IAAI,CAAChB,KAAK,CAACC;MAAK;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEd;AACJ;AAEA,eAAelB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}