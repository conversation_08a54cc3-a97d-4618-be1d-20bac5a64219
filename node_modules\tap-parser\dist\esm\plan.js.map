{"version": 3, "file": "plan.js", "sourceRoot": "", "sources": ["../../src/plan.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,IAAI;IACR,KAAK,CAAQ;IACb,GAAG,CAAQ;IACX,OAAO,CAAQ;IACtB,YAAY,KAAa,EAAE,GAAW,EAAE,UAAkB,EAAE;QAC1D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IACxB,CAAC;CACF", "sourcesContent": ["/**\n * A class representing the TAP plan line\n */\nexport class Plan {\n  public start: number\n  public end: number\n  public comment: string\n  constructor(start: number, end: number, comment: string = '') {\n    this.start = start\n    this.end = end\n    this.comment = comment\n  }\n}\n"]}