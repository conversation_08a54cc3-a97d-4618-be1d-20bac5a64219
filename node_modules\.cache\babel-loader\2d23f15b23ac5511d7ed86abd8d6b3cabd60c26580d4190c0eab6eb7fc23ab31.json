{"ast": null, "code": "/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Types - dichiarazione azioni del carrello\n*\n*/\nexport const GET_NUMBERS_PRODUCT = 'GET_NUMBERS_PRODUCT';", "map": {"version": 3, "names": ["GET_NUMBERS_PRODUCT"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/carrello/actions/types.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Types - dichiarazione azioni del carrello\n*\n*/\nexport const GET_NUMBERS_PRODUCT = 'GET_NUMBERS_PRODUCT';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,mBAAmB,GAAG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}