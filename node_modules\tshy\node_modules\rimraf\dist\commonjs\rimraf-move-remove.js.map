{"version": 3, "file": "rimraf-move-remove.js", "sourceRoot": "", "sources": ["../../src/rimraf-move-remove.ts"], "names": [], "mappings": ";AAAA,qCAAqC;AACrC,EAAE;AACF,aAAa;AACb,oBAAoB;AACpB,yCAAyC;AACzC,uEAAuE;AACvE,0BAA0B;AAC1B,EAAE;AACF,uEAAuE;AACvE,kDAAkD;AAClD,EAAE;AACF,0EAA0E;;;AAE1E,+BAA+C;AAC/C,qDAA6D;AAE7D,yDAAmE;AAEnE,mCAOgB;AAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,gBAAU,CAAA;AAI1D,+DAA0E;AAE1E,sEAAsE;AACtE,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAA;AAE9E,MAAM,cAAc,GAAG,KAAK,EAAE,IAAY,EAAE,EAAE,CAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAA6B,EAAE,EAAE;IACnD,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAC5B,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAClB,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC1B,OAAM;YACR,CAAC;YACD,MAAM,EAAE,CAAA;QACV,CAAC,CACF,CAAA;IACH,CAAC;SAAM,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAM;IACR,CAAC;IACD,MAAM,EAAE,CAAA;AACV,CAAC,CAAC,CAAA;AAEJ,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,IAAI,CAAC;QACH,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAA;IAClB,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;YACpD,IAAI,CAAC;gBACH,OAAO,IAAA,iBAAS,EAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YAC/B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAK,GAA6B,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACtD,OAAM;gBACR,CAAC;gBACD,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC;aAAM,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5D,OAAM;QACR,CAAC;QACD,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAEM,MAAM,gBAAgB,GAAG,KAAK,EACnC,IAAY,EACZ,GAAuB,EACvB,EAAE;IACF,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC;QACH,OAAO,MAAM,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAChE,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAA;QACjE,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAbY,QAAA,gBAAgB,oBAa5B;AAED,MAAM,mBAAmB,GAAG,KAAK,EAC/B,IAAY,EACZ,GAAuB,EACvB,GAAmB,EACD,EAAE;IACpB,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QACb,OAAO,mBAAmB,CACxB,IAAI,EACJ,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,IAAA,2BAAU,EAAC,IAAI,CAAC,EAAE,EACvC,GAAG,CACJ,CAAA;IACH,CAAC;IACD,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAA;IACnE,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,IAAA,oCAAc,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACrE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,gEAAgE;QAChE,oDAAoD;QACpD,qBAAqB;QACrB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,OAAO,CAAA;YACf,CAAC;QACH,CAAC;QACD,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,MAAM,IAAA,+BAAY,EAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,UAAU,GAAG,CACjB,MAAM,OAAO,CAAC,GAAG,CACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAChB,mBAAmB,CAAC,IAAA,cAAO,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CACvD,CACF,CACF,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;IAChC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,sEAAsE;IACtE,+DAA+D;IAC/D,iDAAiD;IACjD,IAAI,GAAG,CAAC,YAAY,KAAK,KAAK,IAAI,IAAI,KAAK,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QACjD,OAAO,KAAK,CAAA;IACd,CAAC;IACD,MAAM,IAAA,+BAAY,EAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAA;IACnD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,MAAM,SAAS,GAAG,KAAK,EACrB,IAAY,EACZ,GAAW,EACX,EAA+B,EAC/B,EAAE;IACF,MAAM,OAAO,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;IAClD,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC3B,OAAO,MAAM,EAAE,CAAC,OAAO,CAAC,CAAA;AAC1B,CAAC,CAAA;AAEM,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,GAAsB,EAAE,EAAE;IAC3E,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC;QACH,OAAO,uBAAuB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,CAAA;IAC5D,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAA;QACjE,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAVY,QAAA,oBAAoB,wBAUhC;AAED,MAAM,uBAAuB,GAAG,CAC9B,IAAY,EACZ,GAAsB,EACtB,GAAmB,EACV,EAAE;IACX,IAAI,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACzB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAA;IACzB,CAAC;IACD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QACb,OAAO,uBAAuB,CAC5B,IAAI,EACJ,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,IAAA,+BAAc,EAAC,IAAI,CAAC,EAAE,EACrC,GAAG,CACJ,CAAA;IACH,CAAC;IACD,MAAM,GAAG,GAAW,GAAG,CAAC,GAAG,CAAA;IAE3B,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAA;IACnE,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAA,wCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,gEAAgE;QAChE,oDAAoD;QACpD,qBAAqB;QACrB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,OAAO,CAAA;YACf,CAAC;QACH,CAAC;QACD,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;QACD,IAAA,mCAAgB,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAA;QACpE,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,UAAU,GAAG,IAAI,CAAA;IACrB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,MAAM,CAAC,GAAG,IAAA,cAAO,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;QACjC,UAAU,GAAG,uBAAuB,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,UAAU,CAAA;IACjE,CAAC;IACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,GAAG,CAAC,YAAY,KAAK,KAAK,IAAI,IAAI,KAAK,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QACzC,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAA,mCAAgB,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAS,CAAC,CAAC,CAAA;IAC3D,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CACpB,IAAY,EACZ,GAAW,EACX,MAA2B,EAC3B,EAAE;IACF,MAAM,OAAO,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;IAClD,IAAA,kBAAU,EAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACzB,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;AACxB,CAAC,CAAA", "sourcesContent": ["// https://youtu.be/uhRWMGBjlO8?t=537\n//\n// 1. readdir\n// 2. for each entry\n//   a. if a non-empty directory, recurse\n//   b. if an empty directory, move to random hidden file name in $TEMP\n//   c. unlink/rmdir $TEMP\n//\n// This works around the fact that unlink/rmdir is non-atomic and takes\n// a non-deterministic amount of time to complete.\n//\n// However, it is HELLA SLOW, like 2-10x slower than a naive recursive rm.\n\nimport { basename, parse, resolve } from 'path'\nimport { defaultTmp, defaultTmpSync } from './default-tmp.js'\n\nimport { ignoreENOENT, ignoreENOENTSync } from './ignore-enoent.js'\n\nimport {\n  chmodSync,\n  lstatSync,\n  promises as fsPromises,\n  renameSync,\n  rmdirSync,\n  unlinkSync,\n} from './fs.js'\nconst { lstat, rename, unlink, rmdir, chmod } = fsPromises\n\nimport { Dirent, Stats } from 'fs'\nimport { RimrafAsyncOptions, RimrafSyncOptions } from './index.js'\nimport { readdirOrError, readdirOrErrorSync } from './readdir-or-error.js'\n\n// crypto.randomBytes is much slower, and Math.random() is enough here\nconst uniqueFilename = (path: string) => `.${basename(path)}.${Math.random()}`\n\nconst unlinkFixEPERM = async (path: string) =>\n  unlink(path).catch((er: Error & { code?: string }) => {\n    if (er.code === 'EPERM') {\n      return chmod(path, 0o666).then(\n        () => unlink(path),\n        er2 => {\n          if (er2.code === 'ENOENT') {\n            return\n          }\n          throw er\n        },\n      )\n    } else if (er.code === 'ENOENT') {\n      return\n    }\n    throw er\n  })\n\nconst unlinkFixEPERMSync = (path: string) => {\n  try {\n    unlinkSync(path)\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code === 'EPERM') {\n      try {\n        return chmodSync(path, 0o666)\n      } catch (er2) {\n        if ((er2 as NodeJS.ErrnoException)?.code === 'ENOENT') {\n          return\n        }\n        throw er\n      }\n    } else if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') {\n      return\n    }\n    throw er\n  }\n}\n\nexport const rimrafMoveRemove = async (\n  path: string,\n  opt: RimrafAsyncOptions,\n) => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  try {\n    return await rimrafMoveRemoveDir(path, opt, await lstat(path))\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') return true\n    throw er\n  }\n}\n\nconst rimrafMoveRemoveDir = async (\n  path: string,\n  opt: RimrafAsyncOptions,\n  ent: Dirent | Stats,\n): Promise<boolean> => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  if (!opt.tmp) {\n    return rimrafMoveRemoveDir(\n      path,\n      { ...opt, tmp: await defaultTmp(path) },\n      ent,\n    )\n  }\n  if (path === opt.tmp && parse(path).root !== path) {\n    throw new Error('cannot delete temp directory used for deletion')\n  }\n\n  const entries = ent.isDirectory() ? await readdirOrError(path) : null\n  if (!Array.isArray(entries)) {\n    // this can only happen if lstat/readdir lied, or if the dir was\n    // swapped out with a file at just the right moment.\n    /* c8 ignore start */\n    if (entries) {\n      if (entries.code === 'ENOENT') {\n        return true\n      }\n      if (entries.code !== 'ENOTDIR') {\n        throw entries\n      }\n    }\n    /* c8 ignore stop */\n    if (opt.filter && !(await opt.filter(path, ent))) {\n      return false\n    }\n    await ignoreENOENT(tmpUnlink(path, opt.tmp, unlinkFixEPERM))\n    return true\n  }\n\n  const removedAll = (\n    await Promise.all(\n      entries.map(ent =>\n        rimrafMoveRemoveDir(resolve(path, ent.name), opt, ent),\n      ),\n    )\n  ).reduce((a, b) => a && b, true)\n  if (!removedAll) {\n    return false\n  }\n\n  // we don't ever ACTUALLY try to unlink /, because that can never work\n  // but when preserveRoot is false, we could be operating on it.\n  // No need to check if preserveRoot is not false.\n  if (opt.preserveRoot === false && path === parse(path).root) {\n    return false\n  }\n  if (opt.filter && !(await opt.filter(path, ent))) {\n    return false\n  }\n  await ignoreENOENT(tmpUnlink(path, opt.tmp, rmdir))\n  return true\n}\n\nconst tmpUnlink = async (\n  path: string,\n  tmp: string,\n  rm: (p: string) => Promise<any>,\n) => {\n  const tmpFile = resolve(tmp, uniqueFilename(path))\n  await rename(path, tmpFile)\n  return await rm(tmpFile)\n}\n\nexport const rimrafMoveRemoveSync = (path: string, opt: RimrafSyncOptions) => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  try {\n    return rimrafMoveRemoveDirSync(path, opt, lstatSync(path))\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') return true\n    throw er\n  }\n}\n\nconst rimrafMoveRemoveDirSync = (\n  path: string,\n  opt: RimrafSyncOptions,\n  ent: Dirent | Stats,\n): boolean => {\n  if (opt?.signal?.aborted) {\n    throw opt.signal.reason\n  }\n  if (!opt.tmp) {\n    return rimrafMoveRemoveDirSync(\n      path,\n      { ...opt, tmp: defaultTmpSync(path) },\n      ent,\n    )\n  }\n  const tmp: string = opt.tmp\n\n  if (path === opt.tmp && parse(path).root !== path) {\n    throw new Error('cannot delete temp directory used for deletion')\n  }\n\n  const entries = ent.isDirectory() ? readdirOrErrorSync(path) : null\n  if (!Array.isArray(entries)) {\n    // this can only happen if lstat/readdir lied, or if the dir was\n    // swapped out with a file at just the right moment.\n    /* c8 ignore start */\n    if (entries) {\n      if (entries.code === 'ENOENT') {\n        return true\n      }\n      if (entries.code !== 'ENOTDIR') {\n        throw entries\n      }\n    }\n    /* c8 ignore stop */\n    if (opt.filter && !opt.filter(path, ent)) {\n      return false\n    }\n    ignoreENOENTSync(() => tmpUnlinkSync(path, tmp, unlinkFixEPERMSync))\n    return true\n  }\n\n  let removedAll = true\n  for (const ent of entries) {\n    const p = resolve(path, ent.name)\n    removedAll = rimrafMoveRemoveDirSync(p, opt, ent) && removedAll\n  }\n  if (!removedAll) {\n    return false\n  }\n  if (opt.preserveRoot === false && path === parse(path).root) {\n    return false\n  }\n  if (opt.filter && !opt.filter(path, ent)) {\n    return false\n  }\n  ignoreENOENTSync(() => tmpUnlinkSync(path, tmp, rmdirSync))\n  return true\n}\n\nconst tmpUnlinkSync = (\n  path: string,\n  tmp: string,\n  rmSync: (p: string) => void,\n) => {\n  const tmpFile = resolve(tmp, uniqueFilename(path))\n  renameSync(path, tmpFile)\n  return rmSync(tmpFile)\n}\n"]}