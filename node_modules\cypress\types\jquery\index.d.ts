// Type definitions for jquery 3.3
// Project: https://jquery.com
// Definitions by: <PERSON> <https://github.com/leonard-thieu>
//                 <PERSON> <https://github.com/b<PERSON><PERSON><PERSON>>
//                 <PERSON> <https://github.com/choffmeister>
//                 <PERSON> <https://github.com/<PERSON>>
//                 <PERSON><PERSON><PERSON> <https://github.com/Di<PERSON>ei>
//                 Ta<PERSON> <https://github.com/tasoili>
//                 <PERSON> <https://github.com/jasons-novaleaf>
//                 <PERSON> <https://github.com/seanski>
//                 Gu<PERSON> <https://github.com/Guuz>
//                 <PERSON> <https://github.com/ksummerlin>
//                 Basarat <PERSON> <https://github.com/basarat>
//                 <PERSON> <https://github.com/nwolverson>
//                 <PERSON> <https://github.com/derekcicerone>
//                 <PERSON> <https://github.com/AndrewGaspar>
//                 Seiki<PERSON> <https://github.com/seikichi>
//                 <PERSON> <https://github.com/benjaminjackman>
//                 Poul Sorensen <https://github.com/s093294>
//                 Josh Strobl <https://github.com/JoshStrobl>
//                 John Reilly <https://github.com/johnnyreilly>
//                 Dick van den Brink <https://github.com/DickvdBrink>
//                 Thomas Schulz <https://github.com/King2500>
//                 Terry Mun <https://github.com/terrymun>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// TypeScript Version: 2.3

/// <reference types="sizzle" />
/// <reference path="JQueryStatic.d.ts" />
/// <reference path="JQuery.d.ts" />
/// <reference path="misc.d.ts" />
/// <reference path="legacy.d.ts" />

export = jQuery;
