{"core/audits/accessibility/accesskeys.js | description": {"message": "பக்கத்தின் ஏதேனும் ஒரு பகுதிக்கு விரைவாகச் செல்ல அணுகல் விசைகளைப் பயனர்கள் பயன்படுத்தலாம். சரியாகச் செல்வதற்கு, ஒவ்வொரு அணுகல் விசையும் தனித்துவமானதாக இருக்க வேண்டும். [அணுகல் விசைகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` மதிப்புகள் பிரத்தியேகமானவையாக இல்லை"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` மதிப்புகள் தனித்துவமாக உள்ளன"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "ஒவ்வொரு ARIA `role` நியமனமும் `aria-*` பண்புக்கூறுகளின் குறிப்பிட்ட துணைத்தொகுப்பை ஆதரிக்கும். பொருந்தாதபட்சத்தில் `aria-*` பண்புக்கூறுகள் செல்லுபடியாகாதவையாகும். [ARIA பண்புக்கூறுகளை அவற்றின் பங்களிப்புகளுக்கு எப்படிப் பொருத்தலாம் என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` பண்புக்கூறுகள் அவற்றின் பங்களிப்புகளுடன் பொருந்தவில்லை"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` பண்புக்கூறுகள் அவற்றின் பங்களிப்புகளுடன் பொருந்துகின்றன"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "ஓர் உறுப்புக்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் பொதுவான பெயரைப் பயன்படுத்தி அதை அறிவிக்கும். ஸ்கிரீன் ரீடர்களைப் பயன்படுத்துபவர்களுக்கு இது உதவியாக இருக்காது. [கட்டளை உறுப்புகளை அதிகம் அணுகத்தக்கதாக மாற்றுவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link`, `menuitem` ஆகிய உறுப்புகளுக்குத் தெளிவான பெயர்கள் இல்லை."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link`, `menuitem` ஆகிய உறுப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "`<body>` ஆவணத்தில் `aria-hidden=\"true\"` அமைக்கப்பட்டிருக்கும்போது ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்கள் சீராக இயங்காது. [`aria-hidden` ஆவண அங்கத்தை எப்படிப் பாதிக்கிறது என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`<body>` ஆவணத்தில் `[aria-hidden=\"true\"]` உள்ளது"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`<body>` ஆவணத்தில் `[aria-hidden=\"true\"]` இல்லை"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்களின் பயனர்களுக்கு அந்த பங்கேற்கத்தக்க உறுப்புகள் கிடைப்பதை `[aria-hidden=\"true\"]` உறுப்பு ஒன்றுக்குள் இருக்கும் மையப்படுத்தக்கூடிய வழித்தோன்றல்கள் தடுக்கும். [ மையப்படுத்தல் உறுப்புகளை `aria-hidden` எப்படிப் பாதிக்கிறது என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` உறுப்புகளில் மையப்படுத்தக்கூடிய வழித்தோன்றல்கள் உள்ளன"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` உறுப்புகளில் மையப்படுத்தக்கூடிய வழித்தோன்றல்கள் இல்லை"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "ஓர் உள்ளீட்டுப் புலத்திற்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் அதைப் பொதுவான பெயரைப் பயன்படுத்தி அறிவிக்கும். இது ஸ்கிரீன் ரீடர்களைப் பயன்படுத்தும் பயனர்களுக்கு உதவியாக இருக்காது. [உள்ளீட்டுப் புலங்களின் லேபிள்கள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA உள்ளீட்டுப் புலங்களுக்குத் தெளிவான பெயர்கள் இல்லை"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA உள்ளீட்டுப் புலங்கள் தெளிவான பெயர்களைக் கொண்டுள்ளன"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "அளவிடல் உறுப்புக்குத் தெளிவான பெயர் இல்லையெனில் ஸ்கிரீன் ரீடர்கள் அதைப் பொதுவான பெயரைப் பயன்படுத்தி அறிவிக்கும். இது ஸ்கிரீன் ரீடர்களைப் பயன்படுத்தும் பயனர்களுக்கு உதவியாக இருக்காது. [`meter` உறுப்புகளுக்கு எப்படிப் பெயரிடுவது என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` உறுப்புகளுக்குத் தெளிவான பெயர்கள் இல்லை."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` உறுப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "`progressbar` உறுப்பிற்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் ஒரு பொதுவான பெயரில் அதைக் குறிப்பிடும். ஸ்கிரீன் ரீடர்களைப் பயன்படுத்திக் கேட்பவர்களுக்கு இது பயனுள்ளதாக இருக்காது. [`progressbar` உறுப்புகளை லேபிளிடுவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` உறுப்புகளுக்குத் தெளிவான பெயர்கள் இல்லை."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` உறுப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "சில ARIA பங்களிப்புகளில் ஸ்கிரீன் ரீடர்களுக்கு உறுப்பின் நிலையை விவரிக்கத் தேவையான பண்புக்கூறுகள் உள்ளன. [பங்களிப்புகளையும் தேவையான பண்புக்கூறுகளையும் பற்றி மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`கள் தேவையான அனைத்து `[aria-*]` பண்புக்கூறுகளையும் கொண்டிருக்கவில்லை"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "தேவையான அனைத்து `[aria-*]` பண்புக்கூறுகளும் `[role]`களில் உள்ளன"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "சில ARIA முதல்நிலைப் பங்களிப்புகள் அவற்றுக்கான அணுகல்தன்மைச் செயல்பாடுகளைச் செய்ய, குறிப்பிட்ட உபநிலைப் பங்களிப்புகளைக் கொண்டிருக்க வேண்டும். [பங்களிப்புகள் மற்றும் தேவையான உபநிலை உறுப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "குறிப்பிட்ட `[role]` இருந்தாக வேண்டிய ARIA `[role]` ஐக் கொண்டுள்ள உறுப்புகளில், தேவையான சில உபநிலைகளோ அனைத்துமோ காணப்படவில்லை."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "குறிப்பிட்ட `[role]` ஐக் கொண்டிருப்பதற்கு உபநிலைகள் தேவைப்படுகின்ற ARIA `[role]` ஐ உடைய உறுப்புகளில் தேவையான உபநிலைகள் உள்ளன."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "சில ARIA உபநிலைப் பங்களிப்புகள் அவற்றுக்கான அணுகல்தன்மைச் செயல்பாடுகளைச் சரியாகச் செய்ய, குறிப்பிட்ட முதல்நிலைப் பங்களிப்புகளில் அவை இருக்க வேண்டும். [ARIA பங்களிப்புகள், தேவையான முதல்நிலை உறுப்பு போன்றவை குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`கள் அவற்றுக்குத் தேவையான முதல்நிலை உறுப்புக்குள் இல்லை"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`கள் அவற்றுக்குத் தேவையான முதல்நிலை உறுப்புகளுக்குள் உள்ளன"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA பங்களிப்புகள் அவற்றின் அணுகல்தன்மைச் செயல்பாடுகளைச் செய்ய அவற்றில் செல்லுபடியாகும் மதிப்புகள் இருக்க வேண்டும். [செல்லுபடியாகும் ARIA பங்களிப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` செல்லுபடியாகாத மதிப்புகளைக் கொண்டுள்ளன"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` செல்லுபடியாகும் மதிப்புகளைக் கொண்டுள்ளன"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "நிலைமாற்றுப் புலம் ஒன்றுக்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் அதைப் பொதுவான பெயரைப் பயன்படுத்தி அறிவிக்கும். இது ஸ்கிரீன் ரீடர்களைப் பயன்படுத்தும் பயனர்களுக்கு உதவியாக இருக்காது. [நிலைமாற்றுப் புலங்கள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA நிலைமாற்றுப் புலங்களுக்குத் தெளிவான பெயர்கள் இல்லை"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA நிலைமாற்றுப் புலங்கள் தெளிவான பெயர்களைக் கொண்டுள்ளன"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "உதவிக்குறிப்பு உறுப்புக்குத் தெளிவான பெயர் இல்லையெனில் ஸ்கிரீன் ரீடர்கள் அதைப் பொதுவான பெயரைப் பயன்படுத்தி அறிவிக்கும். இது ஸ்கிரீன் ரீடர்களைப் பயன்படுத்தும் பயனர்களுக்கு உதவியாக இருக்காது. [`tooltip` உறுப்புகளுக்கு எப்படிப் பெயரிடுவது என அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` உறுப்புகளுக்குத் தெளிவான பெயர்கள் இல்லை."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` உறுப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "`treeitem` உறுப்பிற்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் ஒரு பொதுவான பெயரில் அதைக் குறிப்பிடும். ஸ்கிரீன் ரீடர்களைப் பயன்படுத்திக் கேட்பவர்களுக்கு இது பயனுள்ளதாக இருக்காது. [`treeitem` உறுப்புகளை லேபிளிடுவது குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` உறுப்புகளுக்குத் தெளிவான பெயர்கள் இல்லை."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` உறுப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "செல்லுபடியாகாத மதிப்புகள் உள்ள ARIA பண்புக்கூறுகளை ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்களால் புரிந்துகொள்ள முடியாது. [ARIA பண்புக்கூறுகளுக்கான செல்லுபடியாகும் மதிப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` பண்புக்கூறுகள் செல்லுபடியாகும் மதிப்புகளைக் கொண்டிருக்கவில்லை"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` பண்புக்கூறுகள் செல்லுபடியாகும் மதிப்புகளைக் கொண்டுள்ளன"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "தவறான பெயர்கள் உள்ள ARIA பண்புக்கூறுகளை ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்களால் புரிந்துகொள்ள இயலாது. [சரியான ARIA பண்புக்கூறுகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` பண்புக்கூறுகள் செல்லுபடியாகாத மதிப்புகளைக் கொண்டுள்ளன அல்லது தவறாக உள்ளிடப்பட்டுள்ளன"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` பண்புக்கூறுகள் செல்லுபடியாகும் மதிப்புகளைக் கொண்டுள்ளன, மேலும் அவை சரியாக உள்ளிடப்பட்டுள்ளன"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "தோல்வியுற்ற உறுப்புகள்"}, "core/audits/accessibility/button-name.js | description": {"message": "ஒரு பட்டனுக்குத் தெளிவான பெயர் இல்லை எனில் ஸ்கிரீன் ரீடர்கள் அதை வெறும் \"பட்டன்\" என அறிவிக்கும். இது ஸ்கிரீன் ரீடர்களைப் பயன்படுத்தும் பயனர்களுக்கு உதவியாக இருக்காது. [பட்டன்களை மேலும் அணுகத்தக்கதாக அமைப்பது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "பட்டன்களுக்குத் தெளிவான பெயர் இல்லை"}, "core/audits/accessibility/button-name.js | title": {"message": "பட்டன்களுக்கு ஸ்க்ரீன் ரீடர்களால் படிக்கக்கூடிய பெயர்கள் உள்ளன"}, "core/audits/accessibility/bypass.js | description": {"message": "திரும்பத்திரும்ப வரும் உள்ளடக்கத்தை மீறிச் செல்லும் வழிகளைச் சேர்த்தால், கீபோர்டுப் பயனர்கள் பக்கத்தை மேலும் வசதியாகக் கையாள முடியும். [பைபாஸ் தடுப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "பக்கத்தில் தலைப்பு, தவிர்ப்பு இணைப்பு அல்லது இட அடையாள மண்டலம் இல்லை"}, "core/audits/accessibility/bypass.js | title": {"message": "பக்கத்தில் தலைப்பு, தவிர்ப்பு இணைப்பு அல்லது இட அடையாள மண்டலம் உள்ளது"}, "core/audits/accessibility/color-contrast.js | description": {"message": "பல பயனர்களால் குறைவான ஒளி மாறுபாடுள்ள வார்த்தைகளைப் படிக்க இயலாது அல்லது அவை படிக்கக் கடினமாக இருக்கும். [போதுமான வண்ண மாறுபாட்டை வழங்குவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "பின்னணி, முன்னணி நிறங்களுக்குப் போதுமான ஒளி மாறுபாடு விகிதம் இல்லை."}, "core/audits/accessibility/color-contrast.js | title": {"message": "பின்னணி, முன்னணி நிறங்கள் போதுமான ஒளி மாறுபாட்டு விகிதத்தைக் கொண்டுள்ளன"}, "core/audits/accessibility/definition-list.js | description": {"message": "விளக்கப்பட்டியல்கள் சரியாகக் குறிக்கப்படவில்லை எனில் ஸ்கிரீன் ரீடர்கள் குழப்பமான அல்லது துல்லியமற்ற அறிவிப்பை வழங்கக்கூடும். [விளக்கப்பட்டியல்களைச் சரியாகக் கட்டமைப்பது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "சரியாக வரிசைப்படுத்தப்பட்ட `<dt>`, `<dd>` ஆகிய குழுக்களும் `<script>`, `<template>`, `<div>` போன்ற உறுப்புகளும் மட்டுமல்லாது வேறு வகைகளையும் `<dl>` கொண்டுள்ளது."}, "core/audits/accessibility/definition-list.js | title": {"message": "சரியாக வரிசைப்படுத்தப்பட்ட `<dt>`, `<dd>` ஆகிய குழுக்களையும் `<script>`, `<template>`, `<div>` போன்ற உறுப்புகளையும் மட்டுமே `<dl>` கொண்டுள்ளது."}, "core/audits/accessibility/dlitem.js | description": {"message": "விளக்கப்பட்டியலில் உள்ளவற்றை (`<dt>` மற்றும் `<dd>`) ஸ்கிரீன் ரீடர்கள் சரியாகப் படிக்க அவை ஒரு முதல்நிலை `<dl>` உறுப்பில் சேர்க்கப்பட்டிருக்க வேண்டும். [விளக்கப்பட்டியல்களைச் சரியாகக் கட்டமைப்பது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "விளக்கப்பட்டியலில் உள்ளவை`<dl>` உறுப்புகளில் சேர்க்கப்படவில்லை"}, "core/audits/accessibility/dlitem.js | title": {"message": "விளக்கப் பட்டியலில் உள்ளவை`<dl>` உறுப்புகளில் சேர்க்கப்பட்டுள்ளன"}, "core/audits/accessibility/document-title.js | description": {"message": "தலைப்பு என்பது பக்கம் குறித்த மேலோட்ட விவரங்களை ஸ்கிரீன் ரீடர் பயனர்களுக்கு வழங்குகிறது. தேடல் இன்ஜின் பயனர்கள் தங்களுடைய தேடலுடன் பக்கம் பொருந்துகின்றதா என்பதைத் தீர்மானிக்க அதைப் பெரிதும் சார்ந்துள்ளனர். [ஆவணத் தலைப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "ஆவணத்தில் `<title>` உறுப்பு இல்லை"}, "core/audits/accessibility/document-title.js | title": {"message": "ஆவணத்தில் `<title>` உறுப்பு உள்ளது"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "உதவிகரமான தொழில்நுட்பங்களுக்குத் தெரியும்வகையில் இருப்பதை உறுதிசெய்வதற்காக அனைத்து மையப்படுத்தக்கூடிய உறுப்புகளிடமும் தனித்துவ `id` இருக்க வேண்டும். [நகல் `id`களைச் சரிசெய்வது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "செயலிலுள்ள, மையப்படுத்தக்கூடிய உறுப்புகளில் `[id]` பண்புக்கூறுகள் பிரத்தியேகமானவையாக இல்லை"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "செயலிலுள்ள, மையப்படுத்தக்கூடிய உறுப்புகளில் `[id]` பண்புக்கூறுகள் பிரத்தியேகமானவையாக உள்ளன"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "உதவிகரமான தொழில்நுட்பங்களால் மற்ற நேர்வுகள் புறக்கணிக்கப்படுவதைத் தடுக்க, ARIA ஐடியின் மதிப்பு தனித்துவமானதாக இருக்க வேண்டும். [நகல் ARIA ஐடிகளைச் சரிசெய்வது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ஐடிகள் பிரத்தியேகமானவையாக இல்லை"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ஐடிகள் பிரத்தியேகமானவையாக உள்ளன"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்கள் முதல் லேபிள், இறுதி லேபிள் அல்லது அனைத்து லேபிள்களையும் பயன்படுத்தும் என்பதால் படிவத்தில் பல லேபிள்கள் இருப்பது தெளிவற்ற அறிவிப்புகளுக்கு வழிவகுக்கலாம். [படிவ லேபிள்களைப் பயன்படுத்துவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "படிவத்தின் புலங்கள் பல லேபிள்களைக் கொண்டுள்ளன"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "படிவத்தின் புலங்கள் எதுவும் பல லேபிள்களைக் கொண்டதாக இல்லை"}, "core/audits/accessibility/frame-title.js | description": {"message": "ஃபிரேம்களில் உள்ளவற்றை விவரிக்க அவற்றின் தலைப்பை ஸ்கிரீன் ரீடர் பயனர்கள் சார்ந்துள்ளனர். [ஃபிரேம் தலைப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` அல்லது `<iframe>` உறுப்புகளுக்குத் தலைப்பு இல்லை"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` அல்லது `<iframe>` உறுப்புகளுக்குத் தலைப்பு உள்ளது"}, "core/audits/accessibility/heading-order.js | description": {"message": "நிலைகளைத் தவிர்க்காமல் சரியாக வரிசைப்படுத்தப்பட்ட தலைப்புகள் பக்கத்தின் புரியக்கூடிய வடிவமைப்பிலான தோற்ற அமைப்பைத் தெரிவிக்கும், உதவிகரமான தொழில்நுட்பங்களைப் பயன்படுத்தும்போது எளிதாக வழிசெலுத்துவதற்கும் புரிந்துகொள்வதற்கும் இது உதவும். [தலைப்பு வரிசை குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "தலைப்பு உறுப்புகள் தொடர்-இறங்குவரிசையில் இல்லை"}, "core/audits/accessibility/heading-order.js | title": {"message": "தலைப்பு உறுப்புகள் தொடர்-இறங்குவரிசையில் உள்ளன"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "ஒரு பக்கம் `lang` பண்புக்கூறைக் குறிப்பிடவில்லை எனில் ஸ்கிரீன் ரீடரை அமைக்கும்போது பயனர் தேர்வுசெய்த மொழியையே பக்கத்தின் இயல்பு மொழியாக ஸ்கிரீன் ரீடர் கருதும். இயல்பு மொழியில் பக்கம் இல்லை எனில் பக்கத்தின் வார்த்தையை ஸ்கிரீன் ரீடர் சரியாக வாசிக்க முடியாமல் போகக்கூடும். [`lang` பண்புக்கூறு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` உறுப்பில்`[lang]` பண்புக்கூறு இல்லை"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` உறுப்பானது`[lang]` பண்புக்கூறைக் கொண்டுள்ளது"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "செல்லுபடியாகும் [BCP 47 மொழியைக்](https://www.w3.org/International/questions/qa-choosing-language-tags#question) குறிப்பிட்டால், வார்த்தையை ஸ்கிரீன் ரீடர்கள் சரியாகப் படிக்க அது உதவியாக இருக்கும். [`lang` பண்புக்கூறைப் பயன்படுத்துவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` உறுப்பில் அதன்`[lang]` பண்புக்கூறுக்கான செல்லுபடியாகும் மதிப்பு இல்லை."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` உறுப்பில் அதன்`[lang]` பண்புக்கூறுக்கான செல்லுபடியாகும் மதிப்பு உள்ளது"}, "core/audits/accessibility/image-alt.js | description": {"message": "தகவல் உறுப்புகளில் சுருக்கமான, விளக்கமான மாற்று வார்த்தைகள் இருக்க வேண்டும். அலங்கார உறுப்புகளில் காலியான மாற்றுப் பண்புக்கூறு இருக்கலாம். [`alt` பண்புக்கூறு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "பட உறுப்புகளில் `[alt]` பண்புக்கூறுகள் இல்லை"}, "core/audits/accessibility/image-alt.js | title": {"message": "பட உறுப்புகள் `[alt]` பண்புக்கூறுகளைக் கொண்டுள்ளன"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "`<input>` பட்டனாக ஒரு படத்தைப் பயன்படுத்தும்போது அந்த பட்டனுக்கான மாற்று வார்த்தைகளை வழங்குவது அதன் செயல்பாட்டை ஸ்கிரீன் ரீடர் பயனர்கள் புரிந்துகொள்ள உதவியாக இருக்கும். [உள்ளீட்டுப் படத்திற்கான மாற்று வார்த்தைகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` உறுப்புகளில் `[alt]` வார்த்தைகள் இல்லை"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` உறுப்புகளில் `[alt]` வார்த்தைகள் உள்ளன"}, "core/audits/accessibility/label.js | description": {"message": "ஸ்கிரீன் ரீடர்கள் போன்ற உதவிகரமான தொழில்நுட்பங்களால் சரியாக அறிவிக்கப்படும் வகையில் படிவக் கட்டுப்பாடுகள் இருப்பதை லேபிள்கள் உறுதிசெய்கின்றன. [படிவ உறுப்பு லேபிள்கள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "படிவ உறுப்புகளுக்கு, தொடர்புடைய லேபிள்கள் இல்லை"}, "core/audits/accessibility/label.js | title": {"message": "படிவ உறுப்புகளுக்கு, தொடர்புடைய லேபிள்கள் உள்ளன"}, "core/audits/accessibility/link-name.js | description": {"message": "தெளிவான, தனித்துவமான, மையப்படுத்தக்கூடிய இணைப்பு வார்த்தைகள் (மற்றும் இணைப்புகளாகப் பயன்படுத்தப்படும் படங்களுக்கான மாற்று வார்த்தைகள்) ஸ்கிரீன் ரீடர் பயனர்களுக்கான வழிகாட்டும் அனுபவத்தை மேம்படுத்தும். [இணைப்புகளின் தெளிவுத்தன்மையை அதிகப்படுத்துவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "இணைப்புகளுக்குத் தெளிவான பெயர் இல்லை"}, "core/audits/accessibility/link-name.js | title": {"message": "இணைப்புகளுக்குத் தெளிவான பெயர்கள் உள்ளன"}, "core/audits/accessibility/list.js | description": {"message": "பட்டியல்களை அறிவிப்பதற்கு ஸ்கிரீன் ரீடர்களுக்கு என ஒரு குறிப்பிட்ட வழிமுறை உள்ளது. சரியான பட்டியல் வடிவமைப்பை உறுதிசெய்தால் அது ஸ்கிரீன் ரீடர் செயல்பாட்டிற்கு உதவிகரமாக இருக்கும். [சரியான பட்டியல் கட்டமைப்பு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "பட்டியல்களில் `<li>` உறுப்புகள், ஸ்கிரிப்ட்டை ஆதரிக்கும் உறுப்புகள்(`<script>`,`<template>`) மட்டுமல்லாமல் வேறு உறுப்புகளும் உள்ளன."}, "core/audits/accessibility/list.js | title": {"message": "`<li>` உறுப்புகள், ஸ்கிரிப்ட்டை ஆதரிக்கும் உறுப்புகள் (`<script>`, `<template>`) ஆகியவை மட்டும் பட்டியல்களில் உள்ளன."}, "core/audits/accessibility/listitem.js | description": {"message": "பட்டியலில் உள்ளவற்றை (`<li>`) ஸ்கிரீன் ரீடர்கள் சரியாகப் படிப்பதற்கு அவை ஒரு முதல்நிலை `<ul>`, `<ol>` அல்லது `<menu>`க்குள் இருக்க வேண்டும். [சரியான பட்டியல் கட்டமைப்பு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "பட்டியலில் உள்ளவை (`<li>`) `<ul>`, `<ol>` அல்லது `<menu>` முதல்நிலை உறுப்புகளுக்குள் இல்லை."}, "core/audits/accessibility/listitem.js | title": {"message": "பட்டியலில் உள்ளவை (`<li>`) `<ul>`, `<ol>` அல்லது `<menu>` முதல்நிலை உறுப்புகளில் உள்ளன"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "பக்கம் தானாகப் புதுப்பிக்கப்படும் எனப் பயனர்கள் எதிர்பார்க்க மாட்டார்கள். அவ்வாறு ஏற்பட்டால் பக்கம் மீண்டும் முதலில் இருந்தே காட்டப்படும். இது அவர்களுக்குக் குழப்பத்தை விளைவிக்கலாம். [மீத்தரவுக் குறிச்சொல்லைப் புதுப்பிப்பது குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "ஆவணம் `<meta http-equiv=\"refresh\">`ஐப் பயன்படுத்துகிறது"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "`<meta http-equiv=\"refresh\">`ஐ ஆவணம் பயன்படுத்தவில்லை"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "அளவை மாற்றும் வசதியை முடக்கினால் இணையப் பக்கத்தில் உள்ளவற்றைத் தெளிவாகப் பார்க்க 'திரையைப் பெரிதாக்கும் செயல்பாட்டைப்' பயன்படுத்தும் பார்வைக் குறைபாடுள்ள பயனர்களுக்குச் சிக்கல் ஏற்படும். [காட்சிப்பகுதிக்கான மீத்தரவுக் குறிச்சொல் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`<meta name=\"viewport\">` உறுப்பில் `[user-scalable=\"no\"]` பயன்படுத்தப்பட்டுள்ளது அல்லது `[maximum-scale]` பண்புக்கூறின் மதிப்பு 5க்குக் கீழ் உள்ளது."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`<meta name=\"viewport\">` உறுப்பில் `[user-scalable=\"no\"]` பயன்படுத்தப்படவில்லை, `[maximum-scale]` பண்புக்கூறு 5க்குக் குறைவாக இல்லை."}, "core/audits/accessibility/object-alt.js | description": {"message": "வார்த்தைகளாக இல்லாதவற்றை ஸ்கிரீன் ரீடர்களால் மொழிபெயர்க்க முடியாது. `<object>` உறுப்புகளில் மாற்று வார்த்தைகளைச் சேர்ப்பது ஸ்கிரீன் ரீடர்கள் சரியான அர்த்தத்தைப் பயனர்களுக்குத் தெரிவிக்க உதவும். [`object` உறுப்புகளுக்கான மாற்று வார்த்தைகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` உறுப்புகளில் மாற்று வார்த்தைகள் இல்லை"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` உறுப்புகளில் மாற்று வார்த்தைகள் உள்ளன"}, "core/audits/accessibility/tabindex.js | description": {"message": "0க்கு அதிகமான மதிப்பு வெளிப்படையான ஒரு வழிசெலுத்தல் வரிசை முறையைக் குறிப்பிடுகிறது. தொழில்நுட்பரீதியாகச் செல்லுபடியாகும் என்றாலும், உதவிகரமான தொழில்நுட்பங்களைச் சார்ந்திருக்கும் பயனர்களுக்கு இது பெரும்பாலும் குழப்பமான அனுபவத்தையே உருவாக்கும். [`tabindex` பண்புக்கூறு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "சில உறுப்புகளின் `[tabindex]` மதிப்பு 0க்கு அதிகமாக உள்ளது"}, "core/audits/accessibility/tabindex.js | title": {"message": "எந்த உறுப்புக்கும் `[tabindex]` மதிப்பு 0க்கு அதிகமாக இல்லை"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "அட்டவணைகளில் எளிதாகச் செல்வதற்கு ஸ்கிரீன் ரீடர்களில் அம்சங்கள் உள்ளன. `[headers]` பண்புக்கூறைப் பயன்படுத்தும் `<td>` கலங்கள் அதே அட்டவணையில் உள்ள பிற கலங்களை மட்டும் குறிப்பிடுவதை உறுதிசெய்தால் அது ஸ்கிரீன் ரீடர் பயனர்களின் அனுபவத்தை மேம்படுத்தக்கூடும். [`headers` பண்புக்கூறு குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`[headers]` பண்புக்கூறைப் பயன்படுத்தும் `<table>` உறுப்பிலுள்ள கலங்கள் அதே அட்டவணையில் கண்டறியப்படாத `id` உறுப்பைக் குறிப்பிடுகின்றன."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`[headers]` பண்புக்கூறைப் பயன்படுத்தும் `<table>` உறுப்பிலுள்ள கலங்கள் அதே அட்டவணையிலுள்ள கலங்களைக் குறிப்பிடுகின்றன."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "அட்டவணைகளில் கலங்களுக்கு இடையே எளிதாகச் செல்வதற்கான அம்சங்கள் ஸ்கிரீன் ரீடர்களில் உள்ளன. அட்டவணைத் தலைப்புகள் எப்போதும் சில கலங்களின் தொகுப்பைக் குறிப்பிடுமாறு அமைப்பது ஸ்கிரீன் ரீடர் பயனர்களின் அனுபவத்தை மேம்படுத்தக்கூடும். [அட்டவணைத் தலைப்புகள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` உறுப்புகளும் `[role=\"columnheader\"/\"rowheader\"]`ஐக் கொண்டுள்ள உறுப்புகளும் விவரிக்கும் தரவுக் கலங்கள் அவற்றுக்கு இல்லை."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` உறுப்புகளும் `[role=\"columnheader\"/\"rowheader\"]`ஐக் கொண்டுள்ள உறுப்புகளும் விவரிக்கும் தரவுக் கலங்கள் அவற்றுக்கு உள்ளன."}, "core/audits/accessibility/valid-lang.js | description": {"message": "உறுப்புகளில் செல்லுபடியாகும் [BCP 47 மொழியைக்](https://www.w3.org/International/questions/qa-choosing-language-tags#question) குறிப்பிட்டால், உரையை ஸ்கிரீன் ரீடர் சரியாக உச்சரிக்கிறதா என்பதை உறுதிப்படுத்திக் கொள்ள அது உதவும். [`lang` பண்புக்கூறைப் பயன்படுத்துவது எப்படி என அறிக](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` பண்புக்கூறுகள் செல்லுபடியாகும் மதிப்பைக் கொண்டிருக்கவில்லை"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` பண்புக்கூறுகள் செல்லுபடியாகும் மதிப்பைக் கொண்டுள்ளன"}, "core/audits/accessibility/video-caption.js | description": {"message": "காது கேளாதோர் & செவித்திறன் குறைபாடுள்ளோர் வீடியோ குறித்த தகவல்களை அறிந்துகொள்ள வீடியோ வசனங்கள் உதவும். [வீடியோ வசனங்கள் குறித்து மேலும் அறிக](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` உறுப்புகளில் `[kind=\"captions\"]` உள்ள `<track>` உறுப்பு இல்லை."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` உறுப்புகளில் `[kind=\"captions\"]` உள்ள`<track>` உறுப்பு உள்ளது"}, "core/audits/autocomplete.js | columnCurrent": {"message": "தற்போதைய மதிப்பு"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "பரிந்துரைக்கப்படும் டோக்கன்"}, "core/audits/autocomplete.js | description": {"message": "படிவங்களைப் பயனர்கள் விரைவாகச் சமர்ப்பிக்க `autocomplete` பண்புக்கூறு உதவுகிறது. பயனரின் வேலையை எளிதாக்கும் வகையில், சரியான மதிப்பில் `autocomplete` பண்புக்கூறை அமைத்து அதை இயக்கவும். [படிவங்களில் `autocomplete` பயன்பாடு குறித்து மேலும் அறிக](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` கூறுகளில் சரியான `autocomplete` பண்புக்கூறுகள் இல்லை"}, "core/audits/autocomplete.js | manualReview": {"message": "நீங்களே உள்ளிட வேண்டும்"}, "core/audits/autocomplete.js | reviewOrder": {"message": "டோக்கன்களின் வரிசையைச் சரிபார்க்கவும்"}, "core/audits/autocomplete.js | title": {"message": "`<input>` கூறுகள் `autocomplete` பண்புக்கூறைச் சரியாகப் பயன்படுத்துகின்றன"}, "core/audits/autocomplete.js | warningInvalid": {"message": "{snippet} இல் \"{token}\" என்ற `autocomplete` டோக்கன் (டோக்கன்கள்) தவறாக உள்ளன"}, "core/audits/autocomplete.js | warningOrder": {"message": "{snippet} இல் \"{tokens}\" டோக்கன்களின் வரிசையைச் சரிபார்க்கவும்"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "நடவடிக்கை எடுக்கக் கூடியவை"}, "core/audits/bf-cache.js | description": {"message": "முந்தைய பக்கத்திற்குச் சென்றதன் மூலமாகவோ மீண்டும் முன்னோக்கிச் சென்றதன் மூலமாகவோ பல வழிசெலுத்துதல்கள் மேற்கொள்ளப்பட்டுள்ளன. ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ (bfcache) அம்சத்தின் மூலம் இந்த வழிசெலுத்துதல்களை விரைவுபடுத்தலாம். [bfcache குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{தோல்விக்கான 1 காரணம்}other{தோல்விக்கான # காரணங்கள்}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "தோல்விக்கான காரணம்"}, "core/audits/bf-cache.js | failureTitle": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து மீட்டெடுப்பதைப் பக்கம் தடுத்துள்ளது"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "தோல்வி வகை"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "நடவடிக்கை எடுக்க முடியாதவை"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "உலாவி தொடர்பாக நிலுவையில் உள்ள உதவி"}, "core/audits/bf-cache.js | title": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து மீட்டெடுப்பதைப் பக்கம் தடுக்கவில்லை"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome நீட்டிப்புகள் இந்தப் பக்கத்தின் ஏற்றுதல் செயல்திறனை எதிர்மறையாகப் பாதிக்கின்றன. மறைநிலையிலோ, நீட்டிப்புகள் இல்லாத ஒரு Chrome கணக்கிலிருந்தோ பக்கத்தைத் தணிக்கை செய்ய முயலவும்."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "ஸ்கிரிப்ட் மதிப்பாய்வு"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "ஸ்கிரிப்ட் பாகுபடுத்துதல்"}, "core/audits/bootup-time.js | columnTotal": {"message": "மொத்த CPU நேரம்"}, "core/audits/bootup-time.js | description": {"message": "JSஸைப் பாகுபடுத்துதல், தொகுத்தல், இயக்குதல் ஆகியவற்றுக்குச் செலவழிக்கும் நேரத்தைக் குறைக்கவும். இதற்கு சிறிய அளவிலான JS பேலோடுகளை வழங்குவது உதவலாம். [Javascriptடின் செயல்படுத்தல் நேரத்தைக் குறைப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "JavaScript செயல்பாட்டு நேரத்தைக் குறைக்கவும்"}, "core/audits/bootup-time.js | title": {"message": "JavaScript செயல்பாட்டு நேரம்"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "நெட்வொர்க் செயல்பாடு தேவையற்ற பைட்களை உபயோகப்படுத்துவதைக் குறைக்க வேண்டுமென்றால் தொகுப்புகளிலிருந்து பெரிய, நகலெடுக்கப்பட்ட JavaScript மாடியூல்களை அகற்றவும். "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "JavaScript தொகுப்புகளில் உள்ள நகல் மாடியூல்களை அகற்றுதல்"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "அனிமேஷன் செய்த உள்ளடக்கத்தைக் காட்டுவதற்குப் பெரிய அளவிலான GIFகள் பொருத்தமானவை அல்ல. நெட்வொர்க் பைட்டுகளைச் சேமிக்க GIFக்குப் பதிலாக அனிமேஷன்களுக்கு MPEG4/WebM வீடியோக்களையும் நிலையான படங்களுக்கு PNG/WebP வடிவமைப்புகளையும் பயன்படுத்தவும். [செயல்திறன் மிக்க வீடியோ வடிவங்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "அனிமேஷன் செய்யப்பட்ட உள்ளடக்கங்களுக்கு வீடியோ வடிவமைப்புகளைப் பயன்படுத்தவும்"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "புதிய JavaScript அம்சங்களைப் பழைய உலாவிகள் பயன்படுத்த பாலிஃபில்களும் ட்ரான்ஸ்ஃபார்ம்களும் உதவுகின்றன. எனினும் நவீன உலாவிகளுக்கு இவற்றுள் பல தேவைப்படுவதில்லை. தொகுக்கப்பட்ட JavaScriptடிற்கு பழைய உலாவிகளுக்கான ஆதரவைத் தக்க வைக்கும் அதே நேரத்தில், நவீன உலாவிகளுக்கு மாற்றப்படும் குறியீடுகளைக் குறைக்க மாடியூல்/நோமாடியூல் அம்சக் கண்டறிதலைப் பயன்படுத்தி நவீன ஸ்கிரிப்ட் பயன்படுத்துதல் உத்தியைப் பயன்படுத்துங்கள். [நவீன JavaScriptடைப் பயன்படுத்துவது எப்படி என அறிக](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "நவீன உலாவிகளில் லெகஸி JavaScript சேவை வழங்குவதைத் தவிர்த்தல்"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "PNG, JPEG போன்றவற்றைக் காட்டிலும் படத்தின் அளவைக் குறைப்பதில் WebP, AVIF போன்ற பட வடிவமைப்புகள் சிறப்பாகச் செயல்படலாம். அதாவது அவற்றை விரைவாகப் பதிவிறக்க முடியும், டேட்டா உபயோகமும் குறைவாக இருக்கும். [நவீன பட வடிவமைப்புகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "படங்களை நவீன வடிவமைப்புகளில் வழங்கவும்"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "எதிர்வினை நேரத்தைக் குறைக்க முக்கியமான அனைத்து ஆதாரங்களும் ஏற்றப்பட்ட பின்னர், திரைக்கு வெளியிலுள்ள மற்றும் மறைக்கப்பட்ட படங்களைத் தேவையுள்ளபோது காட்டுமாறு அமைக்கவும். [திரைக்கு வெளியிலுள்ள படங்களைத் தாமதமாக ஏற்றுவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "திரைக்கு வெளியிலுள்ள படங்களைத் தவிர்க்கவும்"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "ஆதாரங்கள் உங்கள் பக்கத்தின் முதல் தோற்றத்தைத் தடுக்கின்றன. முக்கிய JS/CSSஸை இன்லைனில் வழங்கவும். முக்கியமல்லாத அனைத்து JS/ஸ்டைல்களையும் தவிர்க்கவும். [ரெண்டர்-ப்ளாக்கிங் ஆதாரங்களை நீக்குவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "ரென்டரிங்கைத் தடுக்கும் ஆதாரங்களை நீக்கவும்"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "அதிகளவிலான நெட்வொர்க் பேலோடுகள், பயனர்களுக்குப் பண இழப்பை ஏற்படுத்துவதோடு பக்கங்கள் ஏற்றப்பட நீண்ட நேரம் ஆவதற்கும் காரணமாகின்றன. [பேலோடுகளின் அளவைக் குறைப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "மொத்த அளவு: {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "அபரிமிதமான நெட்வொர்க் ஆதாரங்களைத் தவிர்க்கவும்"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "அபரிமிதமான நெட்வொர்க் ஆதாரங்களைத் தவிர்க்கிறது"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS ஃபைல்களைச் சிறிதாக்கினால் நெட்வொர்க் பேலோடு அளவுகள் குறையலாம். [CSSஸைக் குறைப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSSஸைச் சிறிதாக்கவும்"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript ஃபைல்களைச் சிறிதாக்கி பேலோடு அளவுகளையும் ஸ்கிரிப்ட் பாகுபடுத்தப்படும் நேரத்தையும் குறைக்கலாம். [JavaScriptடைச் சிறியதாக்குவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScriptடைச் சிறிதாக்கவும்"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "நெட்வொர்க் செயல்பாட்டின்போது உபயோகிக்கப்படும் பைட்டுகளைக் குறைக்க, ஸ்டைல்ஷீட்டுகளில் உள்ள பயன்படுத்தப்படாத விதிகளைக் குறைப்பதுடன் பக்கத்தின் மேல் பகுதியில் உள்ள உள்ளடக்கத்திற்குப் பயன்படுத்தப்படாத CSSஸையும் தாமதப்படுத்தவும். [பயன்படுத்தப்படாத CSSஸைக் குறைப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "பயன்படுத்தப்படாத CSSஸைக் குறையுங்கள்"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "நெட்வொர்க் செயல்பாட்டின்போது உபயோகிக்கப்படும் பைட்டுகளைக் குறைக்க, பயன்படுத்தப்படாத JavaScriptடைக் குறைப்பதுடன் தேவைப்படாத வரை ஸ்கிரிப்ட்டுகள் ஏற்றப்படுவதையும் தாமதப்படுத்தவும். [பயன்படுத்தப்படாத JavaScriptடைக் குறைப்பது குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "பயன்படுத்தப்படாத JavaScriptடைக் குறையுங்கள்"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "தற்காலிக நினைவகத்தின் ஆயுட்காலம் நீண்டதாக இருந்தால், மீண்டும் மீண்டும் திறக்கப்படும் உங்கள் இணையப் பக்கங்கள் விரைவாகக் காட்டப்படலாம். [திறன்வாய்ந்த தற்காலிக நினைவகச் சேமிப்புக் கொள்கைகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 ஆதாரம் கண்டறியப்பட்டது}other{# ஆதாரங்கள் கண்டறியப்பட்டன}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "திறனுள்ள தற்காலிக நினைவகக் கொள்கையுடன் நிலையான உள்ளடக்கத்தை வழங்கவும்"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "நிலையான உள்ளடக்கத்தில் திறனுள்ள தற்காலிக நினைவகக் கொள்கையைப் பயன்படுத்துகிறது"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "மேம்படுத்தப்பட்ட படங்கள் விரைவாகக் காட்டப்படுவதுடன் குறைவான மொபைல் டேட்டாவைப் பயன்படுத்தும். [படங்களைத் திறம்பட என்கோடிங் செய்வது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "படங்களைத் திறம்பட என்கோடிங் செய்யவும்"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "அசல் பரிமாணங்கள்"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "காட்சிப் பரிமாணங்கள்"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "படங்கள் அவை காட்டப்படுவதற்கான அளவைவிடப் பெரிதாக உள்ளன"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "படங்கள் அவை காட்டப்படுவதற்கான அளவுடன் சரியாகப் பொருந்தியுள்ளன"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "மொபைல் டேட்டாவைச் சேமிக்கவும் பக்கத்தை விரைவாக ஏற்றவும் படங்களைச் சரியான அளவுகளில் வழங்கவும். [படத்தின் அளவுகளை மாற்றுவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "படங்களைச் சரியான அளவுக்கு மாற்றவும்"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "மொத்த நெட்வொர்க் பைட்டுகளைக் குறைப்பதற்கு, வார்த்தைகள் அடிப்படையிலான ஆதாரங்கள் சுருக்கப்பட்டு (gzip, deflate, brotli போன்றவை) வழங்கப்பட வேண்டும். [வார்த்தைகளைச் சுருக்குதல் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "உரைச் சுருக்கத்தை இயக்கவும்"}, "core/audits/content-width.js | description": {"message": "ஆப்ஸின் உள்ளடக்க அகலம் காட்சிப் பகுதியின் அகலத்துடன் பொருந்தவில்லை எனில் மொபைல் திரைகளுக்கு ஏற்ற வகையில் உங்கள் ஆப்ஸ் மேம்படுத்தப்படாமல் போகலாம். [காட்சிப் பகுதிக்கான உள்ளடக்கத்தை மாற்றுவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "{innerWidth} என்ற காட்சிப் பகுதி அளவு பிக்சலுடன் {outerWidth} என்ற சாளரத்தின் அளவு பிக்சல் பொருந்தவில்லை."}, "core/audits/content-width.js | failureTitle": {"message": "காட்சிப் பகுதிக்கு ஏற்ற வகையில் உள்ளடக்கம் சரியாகப் பொருந்தவில்லை"}, "core/audits/content-width.js | title": {"message": "காட்சிப் பகுதிக்கு ஏற்ற வகையில் உள்ளடக்கம் சரியாகப் பொருந்துகிறது"}, "core/audits/critical-request-chains.js | description": {"message": "கீழே இருக்கும் 'முக்கியக் கோரிக்கை வரிசைகள்' எந்தெந்த ஆதாரங்கள் அதிக முன்னுரிமையுடன் ஏற்றப்படுகின்றன என்பதைக் காட்டுகின்றன. பக்கம் ஏற்றப்படுவதன் வேகத்தை அதிகரிக்க, வரிசைகளின் நீளத்தைக் குறைத்தல், ஆதாரங்களின் பதிவிறக்க அளவைக் குறைத்தல், தேவையற்ற ஆதாரங்களைப் பதிவிறக்குவதைத் தவிர்த்தல் போன்றவற்றை முயற்சி செய்யவும். [முக்கியக் கோரிக்கைகள் தேங்குவதைத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 வரிசை கண்டறியப்பட்டது}other{# வரிசைகள் கண்டறியப்பட்டன}}"}, "core/audits/critical-request-chains.js | title": {"message": "முக்கியக் கோரிக்கைகள் தொடர்ந்து கோர்வையாக உருவாவதைத் தவிர்க்கவும்"}, "core/audits/csp-xss.js | columnDirective": {"message": "டைரெக்டிவ்"}, "core/audits/csp-xss.js | columnSeverity": {"message": "தீவிரத்தன்மை"}, "core/audits/csp-xss.js | description": {"message": "மாற்ற முடியாதபடி உள்ளடக்கப் பாதுகாப்புக் கொள்கையை (CSP - Content Security Policy) அமைப்பது வேற்று தள ஸ்கிரிப்டிங் (XSS - cross-site scripting) தாக்குதல்களின் ஆபத்தைக் குறைக்கும். [CSP பயன்படுத்தி XSSஸைத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "தொடரியல்"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "<meta> குறியீட்டில் வரையறுக்கப்பட்டுள்ள CSP இந்தப் பக்கத்தில் உள்ளது. CSPயை HTTP தலைப்பிற்கு நகர்த்தவும் அல்லது ஒரு HTTP தலைப்பில் மற்றொரு strict CSPயை வரையறுக்கவும்."}, "core/audits/csp-xss.js | noCsp": {"message": "அமலாக்கப் பயன்முறையில் CSP எதுவும் கண்டறியப்படவில்லை"}, "core/audits/csp-xss.js | title": {"message": "XSS தாக்குதல்களுக்கு எதிராக CSP சிறப்பாகச் செயல்படுவதை உறுதிசெய்து கொள்ளவும்"}, "core/audits/deprecations.js | columnDeprecate": {"message": "நிறுத்தம் / எச்சரித்தல்"}, "core/audits/deprecations.js | columnLine": {"message": "வரி"}, "core/audits/deprecations.js | description": {"message": "நிறுத்தப்பட்ட APIகள் இறுதியில் உலாவியில் இருந்து அகற்றப்படும். [நிறுத்தப்பட்ட APIகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 எச்சரிக்கை கண்டறியப்பட்டது}other{# எச்சரிக்கைகள் கண்டறியப்பட்டுள்ளன}}"}, "core/audits/deprecations.js | failureTitle": {"message": "நிறுத்தப்பட்ட APIகளைப் பயன்படுத்துகிறது"}, "core/audits/deprecations.js | title": {"message": "நிறுத்தப்பட்டுள்ள APIகளைத் தவிர்க்கும்"}, "core/audits/dobetterweb/charset.js | description": {"message": "எழுத்துக்குறி என்கோடிங்கை வரையறுக்க வேண்டும். HTMLலின் முதல் 1024 பைட்டுகளிலோ Content-Type HTTP பதில் தலைப்பிலோ `<meta>` குறிச்சொல்லைக் குறிப்பிட்டு இதைச் செய்யலாம். [எழுத்துக்குறி என்கோடிங்கை வரையறுப்பது குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "எழுத்துக்குறியாக்க அறிவிப்பு இல்லை அல்லது HTMLலில் மிகவும் தாமதமாக நிகழ்கிறது"}, "core/audits/dobetterweb/charset.js | title": {"message": "எழுத்துக்குறியாக்கம் முறையாக இருக்கிறது"}, "core/audits/dobetterweb/doctype.js | description": {"message": "ஆவண வகையைக் குறிப்பிடுவது குவர்க்ஸ் பயன்முறைக்கு உலாவியை மாற்றுவதைத் தடுக்கும். [ஆவண வகை அறிவிப்பு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "ஆவண வகையின் பெயர் `html` வார்த்தைகளில் இருக்க வேண்டும்"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "ஆவணத்தில், `limited-quirks-mode` ஐத் தூண்டும் `doctype` ஆவண வகை உள்ளது"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "ஆவணமானது ஏதேனுமொரு ஆவண வகையைக் கொண்டிருக்க வேண்டும்"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "பப்ளிக்ஐடி காலியாக இருந்திருக்க வேண்டும்"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "சிஸ்டம்ஐடி காலியாக இருந்திருக்க வேண்டும்"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "ஆவணத்தில், `quirks-mode` ஐத் தூண்டும் `doctype` ஆவண வகை உள்ளது"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "பக்கத்தில் HTML ஆவண வகை இல்லாததால் குவர்க்ஸ் பயன்முறையை இயக்குகிறது"}, "core/audits/dobetterweb/doctype.js | title": {"message": "இந்தப் பக்கமானது HTML ஆவண வகையில் உள்ளது"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "புள்ளிவிவரம்"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "மதிப்பு"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "ஒரு பெரிய DOMமால் நினைவக உபயோகத்தை அதிகரிக்கவும், [ஸ்டைல் கணக்கீடுகளை](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) நீட்டிக்கவும், அதிக செலவு பிடிக்கும் [தளவமைப்பு மறுசீராக்கங்களை](https://developers.google.com/speed/articles/reflow) ஏற்படுத்தவும் முடியும். [அதிகப்படியாக DOM அளவைத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 உறுப்பு}other{# உறுப்புகள்}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "அபரிமிதமான DOM அளவைத் தவிர்க்கவும்"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "அதிகபட்ச DOM கிளை அடுக்கு"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "மொத்த DOM உறுப்புகள்"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "அதிகபட்சத் துணை உறுப்புகள்"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "அபரிமிதமான DOM அளவைத் தவிர்க்கிறது"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "சரியான காரணங்களைத் தெரிவிக்காமல் இருப்பிடத்தை அறிய அனுமதி கேட்கும் தளங்களால் பயனர்கள் குழப்பமடையலாம் அவற்றைச் சந்தேகிக்கலாம். அதற்குப் பதிலாக பயனர் செயல்பாட்டை முயலவும். [புவி இருப்பிட அனுமதிகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "பக்கம் ஏற்றப்படும்போது உங்கள் புவி இருப்பிடத்தைத் தெரிந்துகொள்வதற்கான அனுமதியைக் கோரும்"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "பக்கம் ஏற்றப்படும்போது உங்கள் புவி இருப்பிடத்தைத் தெரிந்துகொள்வதற்கான அனுமதியைக் கோராது"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "சிக்கல் வகை"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome டெவெலப்பர் கருவிகளில் `Issues` பேனலில் பதிவுசெய்யப்படும் சிக்கல்கள் குறித்த தகவல்கள் தீர்க்கப்படாத சிக்கல்களைக் குறிக்கின்றன. நெட்வொர்க் கோரிக்கைப் பிழைகள், குறைவான பாதுகாப்புக் கட்டுப்பாடுகள், மேலும் பிற உலாவி சிக்கல்களினாலும் அவை ஏற்பட்டிருக்கும். ஒவ்வொரு சிக்கல் குறித்தும் கூடுதல் விவரங்களைப் பெற, Chrome டெவெலப்பர் கருவிகளில் ’சிக்கல்கள்’ பேனலைத் திறக்கவும்."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Chrome டெவெலப்பர் கருவிகளில் `Issues` பேனலில் சிக்கல்கள் குறித்த தகவல்கள் பதிவுசெய்யப்பட்டன"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "கிராஸ் ஆரிஜின் கொள்கை காரணமாகத் தடுக்கப்பட்டுள்ளது"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "விளம்பரங்கள் அதிக மூலங்களை உபயோகித்தல்"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome டெவெலப்பர் கருவிகளில் `Issues` பேனலில் சிக்கல்கள் குறித்த தகவல்கள் எதுவுமில்லை"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "பதிப்பு"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "பக்கத்திலுள்ள அனைத்து ஃபிரண்ட் எண்ட் JavaScript லைப்ரரிகளும் கண்டறியப்பட்டன. [இந்த JavaScript லைப்ரரி டிடெக்ஷன் கண்டறிதல் சோதனை குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript லைப்ரரிகள் கண்டறியப்பட்டுள்ளன"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "வெளி ஸ்கிரிப்ட்டுகளை மாறிக்கொண்டே இருக்கும் வகையில் `document.write()` மூலம் சேர்ப்பது வேகம் குறைந்த இணையச் சேவையைக் கொண்டிருக்கும் பயனர்களுக்குப் பக்கம் காட்டப்படுவதைப் பல வினாடிகள் தாமதிக்கலாம். [document.write() ஐத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` ஐத் தவிர்க்கவும்"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()`ஐத் தவிர்க்கும்"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "சரியான காரணங்களைத் தெரிவிக்காமல் அறிவிப்புகளை அனுப்ப அனுமதி கேட்கும் தளங்களால் பயனர்கள் குழப்பமடையலாம் அவற்றைச் சந்தேகிக்கலாம். அதற்குப் பதிலாக பயனர் சைகைகளை முயலவும். [அறிவிப்புகளை அனுப்ப பொறுப்புடன் அனுமதி பெறுதல் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "பக்கம் ஏற்றப்படும்போது அதுகுறித்த அறிவிப்பைத் தெரிந்துகொள்வதற்கான அனுமதியைக் கோரும்"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "பக்கம் ஏற்றப்படும்போது அதுகுறித்த அறிவிப்பைத் தெரிந்துகொள்வதற்கான அனுமதியைக் கோராது"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "நெறிமுறை"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/1.1ஐ விட HTTP/2 அதிகப் பலன்களை வழங்குகிறது. அவற்றில் பைனரி தலைப்புகள், மல்டிபிளெக்ஸிங் ஆகியவையும் அடங்கும். [HTTP/2 குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{HTTP/2 வழியாக 1 கோரிக்கை பதிலளிக்கப்படவில்லை}other{HTTP/2 வழியாக # கோரிக்கைகள் பதிலளிக்கப்படவில்லை}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2ஐப் பயன்படுத்து"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "உங்கள் பக்கத்தின் நகர்த்துதல் செயல்திறனை மேம்படுத்த டச் மற்றும் வீல் ஈவண்ட் லிசனர்களை `passive` என அமைக்கவும். [செயலற்ற ஈவண்ட் லிசனர்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "நகர்த்துதல் செயல்திறனை மேம்படுத்துவதற்காக பேசிவ் லிசனர்களைப் பயன்படுத்தவில்லை"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "நகர்த்துதல் செயல்திறனை மேம்படுத்துவதற்காக பேசிவ் லிசனர்களைப் பயன்படுத்துகின்றன"}, "core/audits/errors-in-console.js | description": {"message": "கன்சோலில் பிழைகள் பதிவு செய்யப்படுவது சிக்கல்கள் தீர்க்கப்படவில்லை என்பதைக் குறிக்கிறது. அவை நெட்வொர்க் கோரிக்கைப் பிழைகளினாலும் வேறு உலாவி சிக்கல்களினாலும் ஏற்பட்டிருக்கலாம். [கன்சோல் கண்டறிதல் சோதனையில் இந்தப் பிழைகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "கன்சோலில் உலாவி தொடர்பான பிழைகள் பதிவு செய்யப்பட்டுள்ளன"}, "core/audits/errors-in-console.js | title": {"message": "கன்சோலில் உலாவியின் பிழைகள் எதுவும் பதிவு செய்யப்படவில்லை"}, "core/audits/font-display.js | description": {"message": "இணைய எழுத்துவடிவங்கள் ஏற்றப்படும்போது எழுத்துகள் பயனருக்குத் தெரிவதை உறுதிசெய்ய `font-display` CSS அம்சத்தைச் சேர்க்கவும். [`font-display` குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "இணைய எழுத்துரு ஏற்றப்படும்போது உரை எழுத்துகள் தெரிவதை உறுதிசெய்யவும்"}, "core/audits/font-display.js | title": {"message": "இணைய எழுத்துருக்கள் ஏற்றப்படும்போது உரை எழுத்துகள் அனைத்தும் தெரிகின்றன"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{{fontOrigin} என்ற மூல இணைப்புக்கான `font-display` மதிப்பை Lighthouseஸால் தானாகச் சரிபார்க்க முடியவில்லை.}other{{fontOrigin} என்ற மூல இணைப்புக்கான `font-display` மதிப்புகளை Lighthouseஸால் தானாகவே சரிபார்க்க முடியவில்லை.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "தோற்ற விகிதம் (அசல்)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "தோற்ற விகிதம் (காட்சிப்படுத்தப்பட்டது)"}, "core/audits/image-aspect-ratio.js | description": {"message": "படத்தின் காட்சிப் பரிமாணங்கள் இயல்பான தோற்ற விகிதத்துடன் பொருந்த வேண்டும். [படத்தின் தோற்ற விகிதம் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "தவறான தோற்ற விகிதமுள்ள படங்களைக் காட்டுகிறது"}, "core/audits/image-aspect-ratio.js | title": {"message": "சரியான தோற்ற விகிதத்துடன் படங்களைக் காட்டுகிறது"}, "core/audits/image-size-responsive.js | columnActual": {"message": "உண்மையான அளவு"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "காட்டப்படும் அளவு"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "எதிர்பார்க்கப்படும் அளவு"}, "core/audits/image-size-responsive.js | description": {"message": "படத்தின் தெளிவுத்தன்மையை அதிகரிக்க, படத்தின் இயல்புப் பரிமாண அளவுகள் காட்சி அளவிற்கும் பிக்சல் விகிதத்திற்கும் சரிவிகிதத்தில் இருக்க வேண்டும். [தானாகப் பொருந்தும் தன்மை கொண்ட படங்களை வழங்குவது எப்படி என அறிக](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "குறைந்த தெளிவுத்திறனைக் கொண்ட படங்களை வழங்குதல்"}, "core/audits/image-size-responsive.js | title": {"message": "பொருத்தமான தெளிவுத்திறனைக் கொண்ட படங்களை வழங்குதல்"}, "core/audits/installable-manifest.js | already-installed": {"message": "இந்த ஆப்ஸ் ஏற்கெனவே நிறுவப்பட்டுள்ளது"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "மெனிஃபெஸ்ட்டில் இருந்து அவசியமான ஐகானைப் பதிவிறக்க முடியவில்லை"}, "core/audits/installable-manifest.js | columnValue": {"message": "தோல்விக்கான காரணம்"}, "core/audits/installable-manifest.js | description": {"message": "Service worker என்பது ஆஃப்லைனில் பணிபுரிவது, முகப்புத் திரையில் சேர்ப்பது, புஷ் அறிவிப்புகள் போன்ற இணைய ஆப்ஸின் பல்வேறு மேம்பட்ட அம்சங்களை உங்கள் ஆப்ஸ் பயன்படுத்த அனுமதிக்கும் தொழில்நுட்பமாகும். சரியான service worker, மெனிஃபெஸ்ட் செயலாக்கங்கள் ஆகியவற்றின் மூலம் உலாவிகள் உங்கள் ஆப்ஸை முகப்புத்திரையில் சேர்க்குமாறு பயனர்களுக்கு முன்கூட்டியே அறிவிக்கும். இப்படிச் செய்வது அதிகப் பயனர்களைப் பெற வழிவகுக்கும். [மெனிஃபெஸ்ட் நிறுவல்தன்மைக்கான தேவைகள் பற்றி மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 காரணம்}other{# காரணங்கள்}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "இணைய ஆப்ஸ் மெனிஃபெஸ்ட், service worker போன்றவை நிறுவுதல் தேவைகளைப் பூர்த்தி செய்யவில்லை"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play Store ஆப்ஸின் URLலும் Play Storeரின் ஐடியும் பொருந்தவில்லை"}, "core/audits/installable-manifest.js | in-incognito": {"message": "மறைநிலை சாளரத்தில் பக்கம் ஏற்றப்பட்டுள்ளது"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "மெனிஃபெஸ்ட் 'display' அம்சமானது 'standalone', 'fullscreen', 'minimal-ui' போன்றவற்றில் ஏதேனும் ஒன்றாக இருக்க வேண்டும்"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "மெனிஃபெஸ்ட் 'display_override' புலத்தைக் கொண்டிருக்கும், மேலும் முதலில் ஆதரிக்கப்படும் காட்சிப் பயன்முறையானது 'standalone', 'fullscreen', 'minimal-ui' போன்றவற்றில் ஒன்றாக இருக்க வேண்டும்"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "மெனிஃபெஸ்ட்டைப் பெறவோ பாகுபடுத்தவோ முடியவில்லை அல்லது அது காலியாக இருக்கக்கூடும்"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "மெனிஃபெஸ்ட் பெறப்படும்போது மெனிஃபெஸ்ட் URL மாறியது."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "'name' அல்லது 'short_name' புலத்தை மெனிஃபெஸ்ட் கொண்டிருக்கவில்லை"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "பொருத்தமான ஐகானை மெனிஃபெஸ்ட் கொண்டிருக்கவில்லை. PNG, SVG, WebP போன்ற வடிவமைப்பில் குறைந்தபட்சம் {value0} பிக்சல்களுடன் இருக்க வேண்டும், sizes பண்புக்கூறு அமைக்கப்பட்டிருக்க வேண்டும், purpose பண்புக்கூறு அமைக்கப்பட்டிருந்தால் \"any\" என்பதைக் கொண்டிருக்க வேண்டும்."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "வழங்கப்பட்ட எந்தவொரு ஐகானும் PNG, SVG, WebP போன்ற வடிவமைப்பில் குறைந்தபட்சம் {value0} சதுர பிக்சல்களுடன் இல்லை. purpose பண்புக்கூறு அமைக்கப்படவில்லை அல்லது “any” என அமைக்கப்பட்டு உள்ளது"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "பதிவிறக்கிய ஐகான் காலியாக உள்ளது அல்லது சிதைந்துள்ளது"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Play Store ஐடி வழங்கப்படவில்லை"}, "core/audits/installable-manifest.js | no-manifest": {"message": "பக்கம் மெனிஃபெஸ்ட் <link> URLலைக் கொண்டிருக்கவில்லை"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "பொருந்தும் service worker எதுவும் கண்டறியப்படவில்லை. பக்கத்தை ரெஃப்ரெஷ் செய்ய வேண்டியிருக்கலாம் அல்லது தற்போதைய பக்கத்திற்கான service workerரின் நோக்கம் மெனிஃபெஸ்ட்டின் ஸ்கோப் மற்றும் ஸ்டார்ட் URLலைக் கொண்டுள்ளதா எனச் சரிபார்க்கவும்."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "மெனிஃபெஸ்ட்டில், 'start_url' இல்லாத service workerரைச் சரிபார்க்க முடியவில்லை"}, "core/audits/installable-manifest.js | noErrorId": {"message": "'{errorId}' என்ற நிறுவுதல் பிழை ஐடியை அடையாளம் காண முடியவில்லை"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "பாதுகாப்பான மூலத்திலிருந்து பக்கம் காட்டப்படவில்லை"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "முதன்மை ஃபிரேமில் பக்கம் ஏற்றப்படவில்லை"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "பக்கம் ஆஃப்லைனில் காட்டப்படாது"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA நிறுவல் நீக்கப்பட்டது. நிறுவலுக்கான சரிபார்ப்புகள் மீட்டமைக்கப்படுகின்றன."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "குறிப்பிடப்பட்ட ஆப்ஸ் பிளாட்ஃபார்ம், Androidல் ஆதரிக்கப்படவில்லை"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "prefer_related_applicationsஸை மெனிஃபெஸ்ட் ’true' எனக் குறிக்கிறது"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Chrome பீட்டாவிலும் Androidல் நிலையான சேனல்களிலும் மட்டுமே prefer_related_applications ஆதரிக்கப்படும்."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse கருவியால் service worker இருந்ததா என்பதைத் தீர்மானிக்க முடியவில்லை. Chromeமின் புதிய பதிப்பில் முயலவும்."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Androidல் மெனிஃபெஸ்ட் URL ஸ்கீம் ({scheme}) ஆதரிக்கப்படவில்லை."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "மெனிஃபெஸ்ட் ஸ்டார்ட் URL தவறானது"}, "core/audits/installable-manifest.js | title": {"message": "இணைய ஆப்ஸ் மெனிஃபெஸ்ட், service worker ஆகியவை நிறுவுதல் தேவைகளைப் பூர்த்தி செய்கின்றன"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "பயனர்பெயர், கடவுச்சொல், போர்ட் போன்றவற்றை மெனிஃபெஸ்ட்டில் உள்ள URL கொண்டுள்ளது"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "பக்கம் ஆஃப்லைனில் காட்டப்படாது. ஆகஸ்ட் 2021ல் Chrome 93 இன் நிலையான பதிப்பு வெளியிடப்பட்ட பிறகு, பக்கம் நிறுவக்கூடியதாகக் கருதப்படாது."}, "core/audits/is-on-https.js | allowed": {"message": "அனுமதிக்கப்பட்டது"}, "core/audits/is-on-https.js | blocked": {"message": "தடுக்கப்பட்டது"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "பாதுகாப்பில்லாத URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "கோரிக்கைக்கான தீர்வு"}, "core/audits/is-on-https.js | description": {"message": "அனைத்துத் தளங்களும் HTTPS மூலம் பாதுகாக்கப்பட வேண்டும். பாதுகாக்கப்பட வேண்டிய தனிப்பட்ட தரவைக் கையாளாதத் தளங்களுக்கும் இது பொருந்தும். தொடக்கக் கோரிக்கை HTTPS மூலம் வழங்கப்பட்டாலும் HTTP வழியாகவே சில தளங்கள் ஏற்றப்படும். [கலவையான உள்ளடக்கத்தைத்](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) தவிர்ப்பதும் இதில் உள்ளடங்கும். உங்கள் ஆப்ஸிற்கும் பயனர்களுக்கும் இடையில் நடக்கும் தகவல் பரிமாற்றத்தில் குறுக்கிட்டு சேதப்படுத்துபவர்களையும் மறைந்திருந்து கவனிப்பவர்களையும் HTTPS தடுக்கிறது. இது HTTP/2 மற்றும் பல புதிய பிளாட்ஃபார்ம் APIகளில் முக்கியமாக இருக்க வேண்டியதாகும். [HTTPS குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{பாதுகாப்பு இல்லாத 1 கோரிக்கை கண்டறியப்பட்டுள்ளது}other{பாதுகாப்பு இல்லாத # கோரிக்கைகள் கண்டறியப்பட்டுள்ளன}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "HTTPSஸைப் பயன்படுத்தவில்லை"}, "core/audits/is-on-https.js | title": {"message": "HTTPSஸைப் பயன்படுத்துகிறது"}, "core/audits/is-on-https.js | upgraded": {"message": "தானாகவே HTTPSஸிற்கு மாற்றப்பட்டது"}, "core/audits/is-on-https.js | warning": {"message": "எச்சரிக்கையுடன் அனுமதிக்கப்பட்டது"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "இது காட்சிப் பகுதிக்குள் தோன்றக்கூடிய உள்ளடக்கம் உள்ள மிகப்பெரிய உறுப்பாகும். ['பெரிய பகுதியைக் காட்டும் நேரம்' உறுப்பு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "பெரிய பகுதியைக் காண்பிக்கும் நேரத்திற்கான உறுப்பு"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS பங்களிப்பு"}, "core/audits/layout-shift-elements.js | description": {"message": "பக்கத்தின் பெரும்பான்மையான CLSஸில் இந்த DOM உறுப்புகள் பங்களிக்கின்றன. [CLSஸை எப்படி மேம்படுத்துவது என அறிக](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "பெரிய தளவமைப்பு ஷிஃப்ட்களைத் தவிர்த்தல்"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "தேவையுள்ளபோது காட்டப்படும் வகையில் ஏற்றப்படும் ‘பக்கத்தின் மேல் பகுதியில்’ உள்ள படங்கள் பக்க லைஃப்சைக்கிள் செயல்முறையின் பிற்பகுதியில் ரென்டரிங் ஆகும். இதனால் பெரிய பகுதியைக் காட்டும் நேரத்தில் தாமதம் ஏற்படலாம். [பொருத்தமான 'தேவையுள்ளபோது காண்பித்தல்' முறை குறித்து மேலும் அறிக](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "பெரிய பகுதியைக் காட்டும் நேரப் படம் தேவையுள்ளபோது காட்டப்படும் வகையில் ஏற்றப்பட்டது"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "பெரிய பகுதியைக் காட்டும் நேரப் படம் தேவையுள்ளபோது காட்டப்படும் வகையில் ஏற்றப்பட்டவில்லை"}, "core/audits/long-tasks.js | description": {"message": "முதன்மைத் தொடரிழையில் உள்ள மிக நீளமான பணிகளைப் பட்டியலிடுகிறது. உள்ளீட்டுத் தாமதத்திற்கு அதிகமாகப் பங்களிப்பவற்றை அடையாளம் காண இது உதவுகிறது. [நீண்ட முதன்மைத் தொடர் பணிகளைத் தவிர்ப்பது எப்படி என அறிக](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# நீண்ட பணி உள்ளது}other{# நீண்ட பணிகள் உள்ளன}}"}, "core/audits/long-tasks.js | title": {"message": "நீளமான முக்கியத் தொடரிழைப் பணிகளைத் தவிர்த்தல்"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "வகை"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "JSஸைப் பாகுபடுத்துதல், தொகுத்தல், இயக்குதல் ஆகியவற்றுக்குச் செலவழிக்கும் நேரத்தைக் குறைக்கவும். இதற்கு சிறிய அளவிலான JS பேலோடுகளை வழங்குவது உதவலாம். [முதன்மைத் தொடரிழைப் பணியைக் குறைப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "முக்கியத் தொடரிழையின் பணியைக் குறைக்கவும்"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "முக்கியத் தொடரிழையின் பணியைக் குறைக்கிறது"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "அதிக அளவிலான பயனர்களைப் பெற அனைத்துப் பிரபலமான உலாவிகளிலும் தளங்கள் செயல்பட வேண்டும். [உலாவிகளுக்கு இடையேயான இணக்கத்தன்மை குறித்து அறிக](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "வெவ்வேறு உலாவியிலும் தளம் செயல்படும்"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "URL மூலம் தனிப்பட்ட பக்கங்களை லிங்க் செய்யலாம் என்பதையும் சமூக வலைதளங்களில் பகிர்வதற்கு அந்த URLகள் தனித்துவமானவை என்பதையும் உறுதிப்படுத்தவும். [தனிப்பட்ட இணைப்புகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "ஒவ்வொரு பக்கத்திற்கும் URL உள்ளது"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "வேகம் குறைந்த நெட்வொர்க்கிலும், பக்கத்தை நீங்கள் தட்டும்போது மாற்றங்கள் விரைவாக இருக்க வேண்டும். இது சிறந்த செயல்திறன் எனப் பயனர் கருதும் முக்கிய அம்சமாகும். [பக்க மாற்றங்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "பக்கத்தின் மாற்றங்கள் நெட்வொர்க்கில் தடுக்கப்பட்டவை போன்று தோன்றக்கூடாது"}, "core/audits/maskable-icon.js | description": {"message": "சாதனத்தில் ஆப்ஸை நிறுவும்போது ஐகானின் படம் லெட்டர்பாக்ஸ் ஆகாமல் அந்த வடிவத்தினுள் முழுமையாகத் தோன்றுவதை மாஸ்க் செய்யப்பட்ட ஐகான் உறுதிப்படுத்துகின்றது. [மாஸ்க் செய்யப்பட்ட மெனிஃபெஸ்ட் ஐகான்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "மாஸ்க் செய்யக்கூடிய ஐகான் மெனிஃபெஸ்டில் இல்லை"}, "core/audits/maskable-icon.js | title": {"message": "மாஸ்க் செய்யப்பட்ட ஐகான் மெனிஃபெஸ்டில் உள்ளது"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "காட்சிப் பகுதிக்குள் தெரியக்கூடிய கூறுகளின் நகர்வை கியூமுலேட்டிவ் லேஅவுட் ஷிஃப்ட் அளவிடுகிறது. [கியூமுலேட்டிவ் லேஅவுட் ஷிஃப்ட் அளவீடு குறித்து மேலும் அறிக](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "செயல்-காட்சி நேரம் என்பது பக்கத்தின் பதிலளிக்கும் தன்மையை அளவிடுகிறது, அதாவது பயனர் உள்ளீட்டிற்குப் பக்கத்தின் தெரிவுநிலை எந்த அளவிற்கு உள்ளது என்பதை அளவிடுவது. [செயல்-காட்சி நேர அளவீடு குறித்து மேலும் அறிக](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "'உள்ளடக்கத்துடன் முதல் தோற்றம்' என்பது வார்த்தைகளோ படமோ முதலில் தோன்றும் நேரத்தைக் குறிக்கிறது. ['உள்ளடக்கத்துடன் முதல் தோற்றம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "'பயனுள்ள முதல் தோற்றம்' என்பது பக்கத்தின் முதன்மை உள்ளடக்கம் எப்போது தெரிகிறது என்பதை அளவிடுகிறது. ['பயனுள்ள முதல் தோற்றம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "பக்கம் முழுமையாகப் பங்கேற்கத்தக்க வகையில் ஏற்றப்பட எடுத்துக்கொள்ளும் நேரம் எதிர்வினை நேரம் எனப்படும். ['எதிர்வினை நேரம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "'பெரிய பகுதியைக் காட்டும் நேரம்' மிகப்பெரிய வார்த்தைகளோ படமோ தோன்றும் நேரத்தைக் குறிக்கிறது. ['பெரிய பகுதியைக் காட்டும் நேரம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "உங்கள் பயனர்கள் எதிர்கொள்ளக்கூடிய அதிகச் சாத்தியமான 'முதல் உள்ளீட்டிற்குப் பதில் அளிக்கும் நேரம்' என்பது மிக நீண்ட பணியின் கால அளவாகும். [அதிகபட்ச சாத்தியமான 'முதல் உள்ளீட்டிற்குப் பதில் அளிக்கும் நேரம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "பக்கத்திலுள்ள உள்ளடக்கங்கள் எவ்வளவு விரைவாகத் தெரிகின்றன என்பதை 'வேக அட்டவணை' காட்டுகிறது. ['வேக அட்டவணை' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "பணியின் நீளம் 50ms என்பதைத் தாண்டும்போது FCP, எதிர்வினை நேரத்திற்கு இடையிலான அனைத்துக் கால அளவுகளின் கூட்டுத்தொகை மில்லி வினாடிகளில் குறிப்பிடப்படும். [மொத்தத் தடுப்பு நேர அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "நெட்வொர்க் ரவுண்ட் டிரிப் டைம்ஸ் (Round Trip Times - RTT) செயல்திறனில் பெரிய மாற்றத்தை ஏற்படுத்தும். அசல் சேவையகத்துக்கான RTT அதிகமாக இருந்தால் பயனரின் அருகில் இருக்கும் சேவையகங்கள் இணையதளத்தின் செயல்திறனை மேம்படுத்தலாம் என்பதைக் குறிக்கும். [ரவுண்ட் டிரிப் டைம் குறித்து மேலும் அறிக](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "நெட்வொர்க் ரவுண்ட் டிரிப் நேரங்கள்"}, "core/audits/network-server-latency.js | description": {"message": "சேவையகத் தாமதங்கள் இணையச் செயல்திறனைப் பாதிக்கக்கூடும். மூல சேவையகத்தில் தாமதம் அதிகமாக இருந்தால் சேவையகத்தின் வேலைப் பளு அதிகமாக உள்ளது அல்லது மோசமான பின்னணிச் செயல்திறன் உள்ளது என்று அர்த்தமாகும். [சேவையகப் பதிலளிப்பு நேரம் குறித்து மேலும் அறிக](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "சேவையக பேக்எண்ட் தாமதங்கள்"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload` நிகழ்வு நம்பத்தகுந்த வகையில் தொடங்கவில்லை, பக்கங்கள் மேற்கொண்டு இதைக் கையாண்டால் உலாவியின் மேம்பட்ட திறன்கள் தடுக்கப்படலாம் (‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் போன்றவை). இதற்குப் பதிலாக `pagehide` அல்லது `visibilitychange` நிகழ்வுகளைப் பயன்படுத்தவும். [அன்லோடு ஈவண்ட் லிசனர்கள் குறித்து மேலும் அறிக](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "`unload` நிகழ்வு லிசனரைப் பதிவுசெய்கிறது"}, "core/audits/no-unload-listeners.js | title": {"message": "`unload` நிகழ்வு லிசனர்களைத் தவிர்க்கிறது"}, "core/audits/non-composited-animations.js | description": {"message": "தொகுக்கப்படாத அனிமேஷன்கள் மோசமாக இருக்கும், CLSஸை அதிகரிக்கும். [தொகுக்கப்படாத அனிமேஷன்களைத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# அனிமேஷன் உறுப்பு உள்ளது}other{# அனிமேஷன் உறுப்புகள் உள்ளன}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "வடிப்பான் தொடர்பான பண்பு பிக்சல்களைச் சீராக வழங்காமல் இருக்கக்கூடும்"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "பொருந்தாத மற்றொரு அனிமேஷன் டார்கெட்டில் உள்ளது"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "\"replace\" அல்லாமல் வேறொரு தொகுப்புப் பயன்முறையை எஃபெக்ட் கொண்டுள்ளது"}, "core/audits/non-composited-animations.js | title": {"message": "தொகுக்கப்படாத அனிமேஷன்களைத் தவிர்த்திடுங்கள்"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "மாற்றம் தொடர்பான பண்பு, அனிமேஷன் பரப்பைப் பொறுத்தது"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{ஆதரிக்கப்படாத CSS பண்பு: {properties}}other{ஆதரிக்கப்படாத CSS பண்புகள்: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "ஆதரிக்கப்படாத நேர அளவுருக்களை எஃபெக்ட் கொண்டுள்ளது"}, "core/audits/performance-budget.js | description": {"message": "நெட்வொர்க் கோரிக்கைகளின் அளவையும் எண்ணிக்கையையும் செயல்திறன் பட்ஜெட் இலக்கீடுகளுக்குள் வைத்திருக்கவும். [செயல்திறன் பட்ஜெட்கள் குறித்து மேலும் அறிக](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 கோரிக்கை}other{# கோரிக்கைகள்}}"}, "core/audits/performance-budget.js | title": {"message": "செயல்திறன் பட்ஜெட்"}, "core/audits/preload-fonts.js | description": {"message": "`optional` என அமைக்கப்பட்டுள்ள எழுத்து வடிவங்களை முன்கூட்டியே ஏற்றுவதால் முதல் முறையாக வரும் பயனர்கள் அவற்றைப் பயன்படுத்தக்கூடும். [எழுத்து வடிவங்களை முன்கூட்டியே ஏற்றுவது குறித்து மேலும் அறிக](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "`font-display: optional` என அமைக்கப்பட்டுள்ள எழுத்துருக்கள் முன்கூட்டியே ஏற்றப்படவில்லை"}, "core/audits/preload-fonts.js | title": {"message": "`font-display: optional` என அமைக்கப்பட்டுள்ள எழுத்துருக்கள் முன்கூட்டியே ஏற்றப்பட்டன"}, "core/audits/prioritize-lcp-image.js | description": {"message": "பக்கத்தில் LCP உறுப்பு மாறிக்கொண்டே இருக்கும் வகையில் சேர்க்கப்பட்டிருந்தால் LCPயை மேம்படுத்த படத்தை முன்கூட்டியே ஏற்ற வேண்டும். [LCP உறுப்புகளை முன்கூட்டியே ஏற்றுதல் குறித்து மேலும் அறிக](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "பெரிய பகுதியைக் காண்பிக்கும் படத்தை முன்கூட்டியே ஏற்றுதல்"}, "core/audits/redirects.js | description": {"message": "'திசைதிருப்புதல்கள்' பக்கம் ஏற்றப்படுவதற்கு முன்பு கூடுதல் தாமதங்களை ஏற்படுத்தும். [பக்கம் திசைதிரும்புவதைத் தவிர்ப்பது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "பல பக்கங்களுக்குத் திசைதிருப்புவதைத் தவிர்க்கவும்"}, "core/audits/resource-summary.js | description": {"message": "பக்க ஆதாரங்களின் அளவிற்கும் எண்ணிக்கைக்கும் பட்ஜெட்களை அமைக்க budget.json ஃபைலைச் சேர்க்கவும். [செயல்திறன் பட்ஜெட்கள் குறித்து மேலும் அறிக](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 கோரிக்கை • {byteCount, number, bytes} KiB}other{# கோரிக்கைகள் • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "கோரிக்கை எண்ணிக்கைகளைக் குறைவாகவும் பரிமாற்ற அளவுகளை சிறியதாகவும் வைத்திருங்கள்"}, "core/audits/seo/canonical.js | description": {"message": "தேடல் முடிவுகளில் எந்த URLலைக் காட்ட வேண்டும் என்பதை 'முன்னுரிமை இணைப்புகள்' பரிந்துரைக்கும். [முன்னுரிமை இணைப்புகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "பல முரண்படும் URLகள் ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "தவறான URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "வேறொரு `hreflang` இருப்பிடத்தைச் சுட்டுகிறது ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "இது துல்லியமான URL அல்ல ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "உள்ளடக்கத்திற்குப் பொருத்தமான பக்கத்திற்குப் பதிலாக டொமைனின் மூல URLலை (முகப்புப்பக்கம்) குறிப்பிடுகிறது"}, "core/audits/seo/canonical.js | failureTitle": {"message": "ஆவணத்தில் செல்லுபடியாகும் `rel=canonical` இல்லை"}, "core/audits/seo/canonical.js | title": {"message": "ஆவணத்தில் செல்லுபடியாகும்`rel=canonical` உள்ளது"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "கிரால் செய்ய முடியாத இணைப்பு"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "இணையதளங்களைக் கிரால் செய்வதற்காக `href` பண்புக்கூறுகளைத் தேடல் இன்ஜின்கள் இணைப்புகளில் பயன்படுத்தலாம். தளத்தின் அதிகப் பக்கங்களைக் கண்டறியும் வகையில் ஆங்க்கர் உறுப்புகளின் `href` பண்புக்கூறு பொருத்தமான இலக்குடன் இணைக்கப்பட்டுள்ளதை உறுதிசெய்யவும். [இணைப்புகளைக் கிரால் செய்யக்கூடியதாக மாற்றுவது எப்படி என அறிக](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "இணைப்புகள் கிரால் செய்ய முடியாதவை"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "இணைப்புகள் கிரால் செய்யக்கூடியவை"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "தெளிவற்ற கூடுதல் உரை"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "எழுத்து வடிவ அளவு"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "பக்க உரையின் சதவீதம் (%)"}, "core/audits/seo/font-size.js | columnSelector": {"message": "தேர்வி"}, "core/audits/seo/font-size.js | description": {"message": "12 பிக்சலுக்குக் குறைவான எழுத்து வடிவ அளவுகள் படிப்பதற்கு மிகச் சிறியவை. மொபைலில் இந்தத் தளத்தைப் பார்ப்பவர்கள் வார்த்தைகளைப் படிக்க “அளவை மாற்ற பின்ச் செய்தல்” அம்சத்தைப் பயன்படுத்த வேண்டியிருக்கும். எனவே பக்கத்தின் 60%க்கும் அதிகமான எழுத்துகளுக்கு 12px அல்லது அதற்கு அதிகமான எழுத்துவடிவ அளவை அமைக்க முயலவும். [தெளிவான எழுத்து வடிவ அளவுகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} தெளிவான உரை"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "மொபைல் திரைகளுக்கேற்ப காட்சிப் பகுதி மேலதிகத் தகவல் மேம்படுத்தப்படாததால் உரையைச் சரியாகப் படிக்க முடியவில்லை."}, "core/audits/seo/font-size.js | failureTitle": {"message": "ஆவணத்தில் தெளிவான எழுத்துரு அளவுகள் பயன்படுத்தப்படவில்லை"}, "core/audits/seo/font-size.js | legibleText": {"message": "தெளிவான உரை"}, "core/audits/seo/font-size.js | title": {"message": "ஆவணத்தில் தெளிவான எழுத்துரு அளவுகள் பயன்படுத்தப்பட்டுள்ளன"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang இணைப்புகள் என்பவை குறிப்பிட்ட ஒரு மொழியிலான அல்லது பகுதிக்கான தேடல் முடிவுகளில், ஒரு பக்கத்தின் எந்தப் பதிப்பைப் பட்டியலிட வேண்டும் என்பதைத் தேடல் இன்ஜின்களுக்குச் சொல்கின்றன. [`hreflang` குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "ஆவணத்தில் செல்லுபடியாகும் `hreflang` இல்லை"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "தொடர்புடைய href மதிப்பு"}, "core/audits/seo/hreflang.js | title": {"message": "ஆவணத்தில் செல்லுபடியாகும்`hreflang` உள்ளது"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "எதிர்பாராத மொழிக் குறியீடு"}, "core/audits/seo/http-status-code.js | description": {"message": "தவறான HTTP நிலைக் குறியீடுகள் உள்ள பக்கங்கள் சரியாக இன்டெக்ஸ் செய்யப்படாமல் போகலாம். [HTTP நிலைக் குறியீடுகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "பக்கத்தில் வெற்றியடையாத HTTP நிலைக் குறியீடு உள்ளது"}, "core/audits/seo/http-status-code.js | title": {"message": "பக்கத்தில் வெற்றிகரமான HTTP நிலைக் குறியீடு உள்ளது"}, "core/audits/seo/is-crawlable.js | description": {"message": "தேடல் இன்ஜின்களுக்கு உங்கள் பக்கங்களைக் கிரால் செய்வதற்கான அனுமதி இல்லை என்றால் அவற்றால் உங்கள் பக்கங்களைத் தேடல் முடிவுகளில் சேர்க்க முடியாது. [கிராலர் டைரெக்டிவ்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "பக்கம் அட்டவணைப்படுத்தப்படுவதிலிருந்து தடுக்கப்பட்டுள்ளது"}, "core/audits/seo/is-crawlable.js | title": {"message": "பக்கம் அட்டவணைப்படுத்தப்படுவதிலிருந்து தடுக்கப்படவில்லை"}, "core/audits/seo/link-text.js | description": {"message": "தேடல் இன்ஜின்கள் உங்கள் உள்ளடக்கத்தைப் புரிந்துகொள்ள இணைப்புக்கான விளக்க வரிகள் உதவும். [இணைப்புகளின் அணுகல்தன்மையை மேம்படுத்துவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 இணைப்பு உள்ளது}other{# இணைப்புகள் உள்ளன}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "இணைப்புகளுக்கு விளக்க உரை இல்லை"}, "core/audits/seo/link-text.js | title": {"message": "இணைப்புகளுக்கு விளக்க உரை உள்ளது"}, "core/audits/seo/manual/structured-data.js | description": {"message": "கட்டமைந்த தரவைச் சரிபார்க்க [கட்டமைந்த தரவுச் சோதனைக் கருவியையும்](https://search.google.com/structured-data/testing-tool/) [கட்டமைந்த தரவு லிண்டரையும்](http://linter.structured-data.org/) இயக்கவும். [கட்டமைந்த தரவு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "கட்டமைந்த தரவு செல்லுபடியாகிறது"}, "core/audits/seo/meta-description.js | description": {"message": "பக்கத்தின் உள்ளடக்கத்தைத் தெளிவாகச் சுருக்கி விளக்குவதற்கு, மீவிளக்கங்களைத் தேடல் முடிவுகளில் சேர்க்கலாம். [மீவிளக்கம் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "விளக்க உரை காலியாக உள்ளது."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "ஆவணத்தில் மீவிளக்கம் இல்லை"}, "core/audits/seo/meta-description.js | title": {"message": "ஆவணத்தில் மீவிளக்கம் உள்ளது"}, "core/audits/seo/plugins.js | description": {"message": "தேடல் இன்ஜின்களால் செருகுநிரல் உள்ளடக்கத்தை இன்டெக்ஸ் செய்ய முடியவில்லை. மேலும் பல சாதனங்கள் அவற்றைக் கட்டுப்படுத்தும் அல்லது ஆதரிக்காது. [செருகுநிரல்களைத் தவிர்ப்பது குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "ஆவணத்தில் செருகுநிரல்கள் உள்ளன"}, "core/audits/seo/plugins.js | title": {"message": "ஆவணத்தில் செருகுநிரல்கள் தவிர்க்கப்பட்டுள்ளன"}, "core/audits/seo/robots-txt.js | description": {"message": "உங்கள் robots.txt ஃபைல் தவறான வடிவமைப்பில் இருந்தால் உங்கள் இணையதளத்தை எப்படி உலாவ அல்லது இன்டெக்ஸ் செய்ய விரும்புகிறீர்கள் என்பதை கிராலர் மென்பொருட்களால் புரிந்துகொள்ள முடியாமல் போகக்கூடும். [robots.txt குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txtக்கான கோரிக்கை இந்த HTTP நிலையைப் பதிலாக அனுப்பியுள்ளது: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 பிழை உள்ளது}other{# பிழைகள் உள்ளன}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouseஸால் robots.txt ஃபைலைப் பதிவிறக்க முடியவில்லை"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt செல்லுபடியாகவில்லை"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt செல்லுபடியாகிறது"}, "core/audits/seo/tap-targets.js | description": {"message": "பட்டன்கள், இணைப்புகள் போன்ற பங்கேற்கத்தக்க உறுப்புகள் போதுமான அளவு பெரிதாகவும் (48x48px) அவற்றைச் சுற்றி போதுமான இடத்துடனும் இருக்க வேண்டும். இதனால் அவற்றைப் பிற உறுப்புகளின் குறுக்கீடு இல்லாமல் எளிதாகத் தட்டலாம். [தட்டல் இலக்குகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} தட்டுவதற்கான இலக்குகள் பொருத்தமான அளவில் இருக்கின்றன"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "மொபைல் திரைகளுக்கேற்ப காட்சிப் பகுதி மேலதிகத் தகவல் மேம்படுத்தப்படாததால் தட்டுவதற்கான இலக்குகள் மிகச் சிறிதாக உள்ளன"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "தட்டுவதற்கான இலக்குகள் சரியாக அளவிடப்படவில்லை"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "குறுக்கிடும் இலக்கு"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "தட்டுவதற்கான இலக்கு"}, "core/audits/seo/tap-targets.js | title": {"message": "தட்டுவதற்கான இலக்குகள் சரியாக அளவிடப்பட்டுள்ளன"}, "core/audits/server-response-time.js | description": {"message": "முதன்மை ஆவணத்திற்கான சேவையகப் பதிலளிப்பு நேரத்தைக் குறைவாக வைக்கவும். ஏனெனில் பிற அனைத்துக் கோரிக்கைகளும் அதைச் சார்ந்தே உள்ளன. ['முதல் பைட்டிற்கான நேரம்' அளவீடு குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "மூல ஆவணம் எடுத்துக் கொண்ட நேரம்: {timeInMs, number, milliseconds} மி.வி."}, "core/audits/server-response-time.js | failureTitle": {"message": "சேவையகப் பதிலளிப்புக்கான தொடக்க நேரத்தைக் குறை"}, "core/audits/server-response-time.js | title": {"message": "சேவையகப் பதிலளிப்புக்கான தொடக்க நேரம் குறைவானது"}, "core/audits/service-worker.js | description": {"message": "Service worker என்பது ஆஃப்லைனில் இயங்குதல், முகப்புத்திரையில் சேர்த்தல், புஷ் அறிவிப்புகள் போன்ற நவீன இணைய ஆப்ஸின் பல்வேறு அம்சங்களை உங்கள் ஆப்ஸ் பயன்படுத்த அனுமதிக்கும் தொழில்நுட்பமாகும். [Service Workerகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "சேவைச் செயலாக்கி மூலம் இந்தப் பக்கம் கட்டுப்படுத்தப்படுகிறது, இருப்பினும் சரியான JSON ஆக மெனிஃபெஸ்ட்டைப் பாகுபடுத்த முடியாத காரணத்தால் `start_url` கண்டறியப்படவில்லை"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "இந்தப் பக்கம் சேவைச் செயலாக்கி மூலம் கட்டுப்படுத்தப்படுகிறது, இருப்பினும் `start_url` ({startUrl}) சேவைச் செயாலாக்கியின் நோக்கத்தில் ({scopeUrl}) இல்லை"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "இந்தப் பக்கம் சேவைச் செயலாக்கி மூலம் கட்டுப்படுத்தப்படுகிறது, இருப்பினும் மெனிஃபெஸ்ட் எதுவும் பெறப்படவில்லை என்பதால் `start_url` கண்டறியப்படவில்லை."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "இந்த இணையதளத்தில் ஒன்றோ அதற்கு மேற்பட்ட சேவைச் செயலாக்கிகளோ உள்ளன, இருப்பினும் இந்தப் பக்கம் ({pageUrl}) நோக்கத்தில் இல்லை."}, "core/audits/service-worker.js | failureTitle": {"message": "பக்கம், `start_url` போன்றவற்றைக் கட்டுப்படுத்துவதற்கான சேவைச் செயலாக்கி பதிவு செய்யப்படவில்லை"}, "core/audits/service-worker.js | title": {"message": "பக்கம், `start_url` போன்றவற்றைக் கட்டுப்படுத்தும் சேவைச் செயலாக்கியைப் பதிவுசெய்யும்."}, "core/audits/splash-screen.js | description": {"message": "பயனர்கள் அவர்களது முகப்புத் திரைகளில் இருந்து உங்கள் ஆப்ஸைத் தொடங்கும்போது உயர்தரமான அனுபவத்தைப் பெறுவதை தீம் அமைக்கப்பட்ட ஸ்பிளாஷ் திரை உறுதிப்படுத்தும். [ஸ்பிளாஷ் திரைகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "பிரத்யேக ஸ்பிளாஷ் திரைக்கு உள்ளமைக்கப்படவில்லை"}, "core/audits/splash-screen.js | title": {"message": "பிரத்யேக ஸ்பிளாஷ் திரைக்கு உள்ளமைக்கப்பட்டது"}, "core/audits/themed-omnibox.js | description": {"message": "உங்களின் தளத்திற்குப் பொருந்தும் வகையில் உலாவியின் முகவரிப் பட்டிக்கான தீமினை அமைக்க முடியும். [முகவரிப் பட்டியில் தீமினை அமைப்பது குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "முகவரிப் பட்டிக்கான தீம் வண்ணத்தை அமைக்க முடியவில்லை."}, "core/audits/themed-omnibox.js | title": {"message": "முகவரிப் பட்டிக்கான தீம் வண்ணத்தை அமைக்கும்."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (வாடிக்கையாளர் சேவை)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (மார்க்கெட்டிங்)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (சமூகம்)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (வீடியோ)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "தயாரிப்பு"}, "core/audits/third-party-facades.js | description": {"message": "உட்பொதிக்கப்பட்ட சில மூன்றாம் தரப்புக் குறியீடுகள் தேவையுள்ளபோது காட்டப்படும். தேவைப்படாத வரை அவற்றுக்குப் பதிலாக ஃபஸாடைப் பயன்படுத்தவும். [ஃபஸாடைப் பயன்படுத்தும் மூன்றாம் தரப்புகளைத் தாமதப்படுத்துவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# மாற்று ஃபஸாடு உள்ளது}other{# மாற்று ஃபஸாடுகள் உள்ளன}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "சில மூன்றாம் தரப்பு மூலங்கள் ஃபஸாடு மூலம் மெதுவாக ஏற்றப்படும்"}, "core/audits/third-party-facades.js | title": {"message": "ஃபஸாடுகளுடன் மூன்றாம் தரப்பு மூலங்களைத் தேவையுள்ளபோது ஏற்றுதல்"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "மூன்றாம் தரப்பு"}, "core/audits/third-party-summary.js | description": {"message": "மூன்றாம் தரப்புக் குறியீடானது ஏற்றுதல் செயல்திறனைக் குறிப்பிடத்தக்க வகையில் விளைவை ஏற்படுத்தக்கூடும். தேவையற்ற மூன்றாம் தரப்புச் சேவை வழங்குநர்களின் எண்ணிக்கையைக் குறைத்துக் கொள்ளவும். மேலும் உங்கள் பக்கத்தின் முதன்மை விவரங்களை ஏற்றியபிறகு மூன்றாம் தரப்புக் குறியீட்டை ஏற்ற முயலவும். [மூன்றாம் தரப்புத் தாக்கத்தைக் குறைப்பது எப்படி என அறிக](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "{timeInMs, number, milliseconds} msக்கான முக்கியத் தொடரிழையை மூன்றாம் தரப்புக் குறியீடு தடுத்துள்ளது"}, "core/audits/third-party-summary.js | failureTitle": {"message": "மூன்றாம் தரப்புக் குறியீட்டின் பாதிப்பைக் குறைக்கவும்"}, "core/audits/third-party-summary.js | title": {"message": "மூன்றாம் தரப்பு உபயோகத்தைக் குறைத்தல்"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "அளவீடு"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "மெட்ரிக்"}, "core/audits/timing-budget.js | description": {"message": "உங்கள் தளத்தின் செயல்திறனைக் கண்காணிக்க டைமிங் பட்ஜெட் ஒன்றை அமையுங்கள். செயல்திறன் மிக்க தளங்கள் வேகமாக ஏற்றப்படுவதோடு பயனர் உள்ளீடுகளுக்கு விரைவாகப் பதிலளிக்கும். [செயல்திறன் பட்ஜெட்கள் குறித்து மேலும் அறிக](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "டைமிங் பட்ஜெட்"}, "core/audits/unsized-images.js | description": {"message": "தளவமைப்பு மாற்றங்களைக் குறைப்பதற்கும் CLSஸை மேம்படுத்துவதற்கும் படத்தின் துல்லியமான அகலத்தையும் உயரத்தையும் அமைக்கவும். [படத்தின் பரிமாணங்களை அமைப்பது எப்படி என அறிக](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "பட உறுப்புகள் துல்லியமான `width`, `height` ஆகியவற்றைக் கொண்டிருக்கவில்லை"}, "core/audits/unsized-images.js | title": {"message": "பட உறுப்புகள் துல்லியமான `width`, `height` ஆகியவற்றைக் கொண்டுள்ளன"}, "core/audits/user-timings.js | columnType": {"message": "வகை"}, "core/audits/user-timings.js | description": {"message": "முக்கியமான பயனர் அனுபவங்களின்போது உங்கள் ஆப்ஸின் நிகழ்நேர செயல்திறனை அளவிட ஆப்ஸில் User Timing APIயைப் பயன்படுத்தவும். [பயனர் நேரக் குறியீடுகள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 பயனர் நேரம்}other{# பயனர் நேரங்கள்}}"}, "core/audits/user-timings.js | title": {"message": "பயனர் நேரக் குறிப்புகளும் அளவீடுகளும்"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "\"{security<PERSON><PERSON>in}\" தளத்திற்கான முன்னிணைப்பு (`<link rel=preconnect>`) கண்டறியப்பட்டது, ஆனால் உலாவி அதைப் பயன்படுத்தவில்லை. `crossorigin` பண்புக்கூறைச் சரியாகப் பயன்படுத்துகிறீர்களா என்று பார்க்கவும்."}, "core/audits/uses-rel-preconnect.js | description": {"message": "முக்கிய மூன்றாம் தரப்பு ஆரிஜின்களுடன் விரைவான இணைப்புகளை ஏற்படுத்த `preconnect` அல்லது `dns-prefetch` ஆதாரக் குறிப்புகளைச் சேர்க்கலாம். [தேவையான ஆரிஜின்களுடன் முன்கூட்டியே இணைவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "தேவைப்படும் டொமைன் பெயர்களுக்கு முன்கூட்டியே இணைப்பு வழங்கவும்"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "இரண்டிற்கும் மேற்பட்ட `<link rel=preconnect>` இணைப்புகள் உள்ளன. இவற்றை அரிதாகவும் மிகவும் முக்கியமான மூலங்களை இணைப்பதற்கும் மட்டுமே பயன்படுத்த வேண்டும்."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "\"{security<PERSON><PERSON>in}\" தளத்திற்கான முன்னிணைப்பு (`<link rel=preconnect>`) கண்டறியப்பட்டது, ஆனால் உலாவி அதைப் பயன்படுத்தவில்லை. பக்கம் நிச்சயமாகக் கோரும் முக்கியமான தளங்களுக்கு மட்டுமே `preconnect` ஐப் பயன்படுத்தவும்."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "\"{preloadURL}\" தளத்திற்கான முன்கூட்டியே ஏற்றப்பட்ட இணைப்பு (`<link>`) கண்டறியப்பட்டது, ஆனால் உலாவி அதைப் பயன்படுத்தவில்லை. `crossorigin` பண்புக்கூறைச் சரியாகப் பயன்படுத்துகிறீர்களா என்று பார்க்கவும்."}, "core/audits/uses-rel-preload.js | description": {"message": "பக்கம் ஏற்றப்படும்போது தற்சமயம் பின்னர் கோரிக்கையளிக்கப்படும் ஆதாரங்களுக்கு முன்னுரிமை அளிக்க `<link rel=preload>` ஐப் பயன்படுத்தவும். [முக்கியக் கோரிக்கைகளை முன்கூட்டியே ஏற்றுவது எப்படி என அறிக](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "முக்கியக் கோரிக்கைகளை முன்கூட்டியே ஏற்றவும்"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "சோர்ஸ் மேப்பிற்கான URL"}, "core/audits/valid-source-maps.js | description": {"message": "சோர்ஸ் மேப்கள் சிறிதாக்கப்பட்ட குறியீட்டை அசல் மூலக் குறியீடாக மாற்றுகின்றன. இதனால் டெவெலப்பர்கள் தயாரிப்பின்போது பிழைதிருத்தம் செய்ய முடியும். இத்துடன் Lighthouseஸாலும் கூடுதல் புள்ளிவிவரங்களை வழங்க முடியும். இந்தப் பலன்களைப் பெற சோர்ஸ் மேப்களைப் பயன்படுத்திப் பார்க்கவும். [சோர்ஸ் மேப்கள் குறித்து மேலும் அறிக](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "பெரிய அளவிலான முதல் தரப்பு JavaScriptடுக்கான சோர்ஸ் மேப்கள் விடுபட்டுள்ளன"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "பெரிய அளவிலான JavaScript ஃபைலில் சோர்ஸ் மேப் விடுபட்டுள்ளது"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{எச்சரிக்கை: `.sourcesContent` இல் 1 உறுப்பு விடுபட்டுள்ளது}other{எச்சரிக்கை: `.sourcesContent` இல் # உறுப்புகள் விடுபட்டுள்ளன}}"}, "core/audits/valid-source-maps.js | title": {"message": "பக்கத்திற்குச் சரியான சோர்ஸ் மேப்கள் உள்ளன"}, "core/audits/viewport.js | description": {"message": "மொபைல் திரை அளவுகளுக்கு ஏற்றபடி உங்கள் ஆப்ஸை `<meta name=\"viewport\">` மேம்படுத்துவதோடு [பயனர் உள்ளீட்டில் ஏற்படும் 300 மில்லிவினாடி தாமதத்தையும்](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) தடுக்கிறது. [காட்சிப்பகுதிக்கான மீத்தரவுக் குறிச்சொல்லைப் பயன்படுத்துவது குறித்து மேலும் அறிக](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` குறிச்சொல் எதுவும் இல்லை"}, "core/audits/viewport.js | failureTitle": {"message": "`width` அல்லது `initial-scale` உடன் கூடிய `<meta name=\"viewport\">` குறிச்சொல் அமைக்கப்படவில்லை"}, "core/audits/viewport.js | title": {"message": "`width` அல்லது `initial-scale` உடன் `<meta name=\"viewport\">` குறிச்சொல் அமைக்கப்பட்டுள்ளது"}, "core/audits/work-during-interaction.js | description": {"message": "இது ஒரு வரிசைத் தடுப்புப் பணியாகும். இது செயல்-காட்சி நேர அளவீட்டின்போது நடைபெறுகிறது. [செயல்-காட்சி நேர அளவீடு குறித்து மேலும் அறிக](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "'{interactionType}' நிகழ்விற்கு {timeInMs, number, milliseconds} ms நேரம் ஆனது"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "நிகழ்வு இலக்கு"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "முக்கியப் பங்கேற்பின்போது பணியைக் குறைத்தல்"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "உள்ளீட்டுத் தாமதம்"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "காட்சிப்படுத்தல் தாமதம்"}, "core/audits/work-during-interaction.js | processingTime": {"message": "செயலாக்க நேரம்"}, "core/audits/work-during-interaction.js | title": {"message": "முக்கியப் பங்கேற்பின்போது வேலையைக் குறைக்கிறது"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "இந்த வாய்ப்புகள் உங்கள் ஆப்ஸில் உள்ள ARIAயின் ஆப்ஸை மேம்படுத்தும், இது ஸ்க்ரீன் ரீடர் போன்ற உதவிகரமான தொழில்நுட்பத்தைப் பயன்படுத்தும் பயனர்களின் அனுபவத்தை மேம்படுத்தக்கூடும்."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "இவை ஆடியோவுக்கும் வீடியோவுக்கும் மாற்று உள்ளடக்கத்தை வழங்குவதற்கான வாய்ப்புகளாகும். இது செவித்திறன் அல்லது பார்வைக் குறைபாடுள்ள பயனர்களுக்கான அனுபவத்தை மேம்படுத்தக்கூடும்."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "ஆடியோ & வீடியோ"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "இவை பொதுவான அணுகல்தன்மைக்கான சிறந்த நடைமுறைகளைத் தனிப்படுத்துகின்றன."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "சிறந்த நடைமுறைகள்"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "இந்த சரிபார்ப்புகள் [உங்கள் இணைய ஆப்ஸின் அணுகல்தன்மையை மேம்படுத்துவதற்கான](https://developer.chrome.com/docs/lighthouse/accessibility/) வாய்ப்புகளைத் தனிப்படுத்திக் காட்டுகின்றன. அணுகல்தன்மை சிக்கல்களில் சிலவற்றை மட்டுமே தானாகக் கண்டறிய முடியும் என்பதால் நேரடி பரிசோதனையையும் செய்ய ஊக்குவிக்கிறோம்."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "தானியங்கி சோதனைக் கருவியால் சோதிக்க முடியாத பகுதிகளை இவை சோதிக்கும். எங்கள் வழிகாட்டியில் [அணுகல்தன்மை மதிப்பாய்வை நடத்துவதைப்](https://web.dev/how-to-review/) பற்றி மேலும் தெரிந்துகொள்ளலாம்."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "அணுகல்தன்மை"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "இவை உங்கள் உள்ளடக்கத்தின் நம்பகத்தன்மையை மேம்படுத்துவதற்கான வாய்ப்புகளாகும்."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "ஒளி மாறுபாடு"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "இந்த வாய்ப்புகள், பல்வேறு மொழிப் பயனர்கள் உங்கள் உள்ளடக்கத்தைப் புரிந்துகொள்வதற்கான வாய்ப்பினை மேம்படுத்தும்."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "சர்வதேசமயமாக்குதல் & உள்ளூர்மயமாக்குதல்"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "இந்த வாய்ப்புகள் உங்கள் ஆப்ஸின் கட்டுப்பாடுகளின் பொருள்விளக்கத்தை மேம்படுத்தும். இது ஸ்க்ரீன் ரீடர் போன்ற உதவிகரமான தொழில்நுட்பத்தைப் பயன்படுத்தும் பயனர்களின் அனுபவத்தை மேம்படுத்தக்கூடும்."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "பெயர்கள் & லேபிள்கள்"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "இவை உங்கள் ஆப்ஸில் கீபோர்டு நேவிகேஷனை மேம்படுத்துவதற்கான வாய்ப்புகளாகும்."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "நேவிகேஷன்"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "இவை ஸ்க்ரீன் ரீடர் போன்ற உதவிகரமான தொழில்நுட்பத்தைப் பயன்படுத்தி அட்டவணையையோ பட்டியல் தரவையோ படிக்கும் அனுபவத்தை மேம்படுத்துவதற்கான வாய்ப்புகளாகும்."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "டேபிள்கள் & பட்டியல்கள்"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "உலாவியின் இணக்கத்தன்மை"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "சிறந்த நடைமுறைகள்"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "பொது"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "நம்பிக்கையும் பாதுகாப்பும்"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "பயனர் அனுபவம்"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "செயல்திறன் பட்ஜெட்கள் உங்கள் தளத்தின் செயல்திறனுக்கான தர நிலைகளை அமைக்கும்."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "பட்ஜெட்கள்"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "உங்கள் ஆப்ஸின் செயல்திறன் பற்றிய மேலும் சில தகவல்கள். இந்த மதிப்புகளானது செயல்திறனின் ஸ்கோரை [நேரடியாகப் பாதிக்காது](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "பகுப்பாய்வு"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "திரையில் பிக்சல்கள் எவ்வளவு விரைவாக ரென்டரிங் செய்யப்படுகின்றன என்பது செயல்திறனின் மிக முக்கிய அம்சமாகும். முக்கிய அளவீடுகள்: 'உள்ளடக்கமுள்ள முதல் தோற்றம்', 'அர்த்தமுள்ள முதல் தோற்றம்'"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "முதல் தோற்ற மேம்பாடுகள்"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "இந்தப் பரிந்துரைகள் உங்கள் பக்கத்தை வேகமாக ஏற்ற உதவும். அவை செயல்திறன் ஸ்கோரை [நேரடியாகப் பாதிக்காது](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "பரிந்துரைகள்"}, "core/config/default-config.js | metricGroupTitle": {"message": "அளவீடுகள்"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "ஒட்டுமொத்தமாகப் பக்கம் ஏற்றப்படும் அனுபவத்தை மேம்படுத்தவும், இதனால் பக்கம் விரைவாக எதிர்வினையாற்றும், அத்துடன் முடிந்தளவு விரைவாகப் பயன்படுத்தத் தயாராக இருக்கும். முக்கிய அளவீடுகள்: 'எதிர்வினை நேரம்', 'வேக அட்டவணை'"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "ஒட்டுமொத்த மேம்பாடுகள்"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "இணையச் செயல்திறன்"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "இந்தச் சரிபார்ப்புகள் மேம்பட்ட இணைய ஆப்ஸின் அம்சங்களை மதிப்பிடும்.[ஒரு நல்ல மேம்பட்ட இணைய ஆப்ஸின் தன்மைகள் குறித்து அறிக](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "பேஸ்லைன் [PWA சரிபார்ப்புப் பட்டியலுக்கு ](https://web.dev/pwa-checklist/) இந்தச் சரிபார்ப்புகள் தேவை, இருப்பினும் இவற்றை Lighthouse தானாகச் சரிபார்ப்பதில்லை. அவை உங்களின் ஸ்கோரில் பாதிப்பை ஏற்படுத்தாது, இருப்பினும் நீங்களாகவே அவற்றைச் சரிபார்ப்பது முக்கியமாகும்."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "நிறுவக்கூடியவை"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA மேம்படுத்தப்பட்டுள்ளது"}, "core/config/default-config.js | seoCategoryDescription": {"message": "தேடலில் மேம்படுத்துதல் செயல்முறையின் அடிப்படை வழிமுறையை உங்கள் பக்கம் பின்பற்றுகிறது என்பதை இந்தச் சரிபார்ப்புகள் உறுதிசெய்கின்றன. Lighthouse கணக்கில் எடுத்துக்கொள்ளாத பல கூடுதல் காரணிகள் உங்கள் தேடல் தரவரிசையைப் பாதிக்கக்கூடும். அவற்றில் [வலைதளத்தின் முக்கிய அடிப்படை விவரங்கள்](https://web.dev/learn-core-web-vitals/) மீதான செயல்திறனும் அடங்கும். [Google Search Essentials குறித்து மேலும் அறிக](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "மேலும் சில சிறந்த SEO நடைமுறைகளைச் சோதனை செய்ய, உங்கள் தளத்தில் இந்தக் கூடுதல் வேலிடேட்டர்களை இயக்கவும்."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "ஆப்ஸின் உள்ளடக்கத்தை கிராலர்கள் நன்கு புரிந்துகொள்ள வசதியாக HTMLலைப் பொருத்தமாக வடிவமைக்கவும்."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "உள்ளடக்கம் தொடர்பான சிறந்த நடைமுறைகள்"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "தேடல் முடிவுகளில் தோன்ற உங்கள் ஆப்ஸிற்கான அணுகல் கிராலர்களுக்குத் தேவை."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "கிராலிங் & அட்டவணைப்படுத்துதல்"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "பக்கங்களின் உள்ளடக்கத்தைப் பயனர்கள் பின்ச் செய்தோ பெரிதாக்கியோ படிக்க வேண்டிய அவசியம் ஏற்படாத வகையில் உங்கள் பக்கங்கள் மொபைலுக்கு ஏற்றவையாக இருப்பதை உறுதிசெய்துகொள்ளவும். [மொபைலுக்கு ஏற்ற வகையில் பக்கங்களை எப்படி உருவாக்குவது என அறிக](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "மொபைலுக்கேற்றது"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "சோதனை செய்யப்பட்ட சாதனம் Lighthouse எதிர்பார்ப்பதை விட வேகம் குறைவான CPUவைக் கொண்டிருப்பதுபோல் தெரிகிறது. இது உங்கள் செயல்திறனைப் பாதிக்கும். [பொருத்தமான CPU ஸ்லோடவுன் மல்டிபிளையரை அளவிடுதல்](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling) குறித்து மேலும் அறிக."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "உங்கள் சோதனை URL ({requested}) {final}என்பதற்குத் திசைதிருப்பப்பட்டதால் எதிர்பரர்த்த அளவிற்குப் பக்கம் ஏற்றப்படாமல் போகக்கூடும். நேரடியாக இரண்டாவது URLலை சோதித்துப் பாருங்கள்."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "நேர வரம்பிற்குள் பக்கத்தை முழுமையாக ஏற்ற முடியவில்லை. இதனால் முடிவுகள் காட்டப்படாமல் போகலாம்."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "உலாவியின் தற்காலிகச் சேமிப்பை அழிப்பதற்கான நேரம் முடிந்துவிட்டது. இந்தப் பக்கத்தைத் தணிக்கை செய்த பின்னரும் சிக்கல் தொடர்ந்தால் பிழையைப் புகாரளிக்கவும்."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{செயல்திறன் அளவீட்டைப் பாதிக்கக்கூடிய, சேமித்தத் தரவு இந்த இடத்தில் இருக்கலாம்: {locations}. இவை உங்கள் ஸ்கோர்களைப் பாதிப்பதைத் தவிர்க்கும் வகையில், மறைநிலைச் சாளரத்தில் இந்தப் பக்கத்தின் செயல்பாட்டை ஆய்வு செய்யவும்.}other{செயல்திறன் அளவீட்டைப் பாதிக்கக்கூடிய, சேமித்தத் தரவு இந்த இடத்தில் இருக்கலாம்: {locations}. இவை உங்கள் ஸ்கோர்களைப் பாதிப்பதைத் தவிர்க்கும் வகையில், மறைநிலைச் சாளரத்தில் இந்தப் பக்கத்தின் செயல்பாட்டை ஆய்வு செய்யவும்.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "ஆரிஜின் தரவை அழிப்பதற்கான நேரம் முடிந்துவிட்டது. இந்தப் பக்கத்தைத் தணிக்கை செய்த பின்னரும் சிக்கல் தொடர்ந்தால் பிழையைப் புகாரளிக்கவும்."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "GET கோரிக்கை மூலம் ஏற்றப்பட்ட பக்கங்களை மட்டுமே ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியும்."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "2XX நிலைக் குறியீட்டுடன் இருக்கும் பக்கங்களை மட்டுமே தற்காலிகமாகச் சேமிக்க முடியும்."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "பக்கம் தற்காலிகச் சேமிப்பில் சேமிக்கப்பட்டிருக்கும்போது JavaScriptடைச் செயல்படுத்துவதற்கு முயற்சி செய்யப்பட்டதாக Chrome கண்டறிந்துள்ளது."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "AppBannerரைக் கோரிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் கொடிகள் மூலம் முடக்கப்பட்டுள்ளது. சாதனத்தில் இதை இயக்க, chrome://flags/#back-forward-cache என்ற பக்கத்திற்குச் செல்லவும்."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "கட்டளை மூலம் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "போதுமான நினைவகம் இல்லாததால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தை உட்பொதிவில் பயன்படுத்த முடியாது."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "ப்ரீரெண்டரிங் செய்யப்படுவதற்காக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "பதிவுசெய்யப்பட்ட லிசனர்களுடன் கூடிய BroadcastChannel நேர்வைப் பக்கம் கொண்டுள்ளதால் அதைத் தற்காலிகமாகச் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "‘cache-control:no-store’ என்ற தலைப்புடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "தற்காலிகச் சேமிப்பு வேண்டுமென்றே அழிக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "மற்றொரு பக்கத்தைத் தற்காலிகமாகச் சேமிப்பதற்காக இந்தப் பக்கம் தற்காலிகச் சேமிப்பில் இருந்து அகற்றப்பட்டது."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "செருகுநிரல்களைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcherரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "வெளியேறும்போது மீடியா பிளேயர் பிளேயாகிக் கொண்டிருந்தது."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession APIயைப் பயன்படுத்தி பிளேபேக் நிலையை அமைக்கும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession APIயைப் பயன்படுத்தி செயல்பாட்டு ஹேண்ட்லர்களை அமைக்கும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "ஸ்கிரீன் ரீடர் காரணமாக 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "SecurityHandlerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthentication APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Dedicated worker/workletடைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "வெளியேறுவதற்கு முன் ஆவணம் ஏற்றப்படவில்லை."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "வெளியேறும்போது ஆப்ஸ் பேனர் காட்டப்பட்டது."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "வெளியேறும்போது Chrome Password Manager செயலில் இருந்தது."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "வெளியேறும்போது DOM டிஸ்டிலேஷன் நடைபெற்றுக் கொண்டிருந்தது."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "வெளியேறும்போது DOM டிஸ்டில்லர் வியூவர் செயலில் இருந்தது."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "மெசேஜிங் APIயை நீட்டிப்புகள் பயன்படுத்தியதால் 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சத்தைப் பயன்படுத்துவதற்கு முன்பு, நீண்ட கால இணைப்பைக் கொண்டிருக்கும் நீட்டிப்புகள் அவற்றின் இணைப்பைத் துண்டிக்க வேண்டும்."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "நீண்ட கால இணைப்பைக் கொண்டிருக்கும் நீட்டிப்புகள், 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சத்தில் உள்ள ஃபிரேம்களுக்கு மெசேஜ் அனுப்ப முயன்றன."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "நீட்டிப்புகள் காரணமாக, 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "வெளியேறும்போது பக்கத்திற்கான படிவ மறுசமர்ப்பிப்பு, HTTP கடவுச்சொல் உரையாடல் போன்ற மோடல் உரையாடல் காட்டப்பட்டது."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "வெளியேறும்போது ஆஃப்லைன் பக்கம் காட்டப்பட்டது."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "வெளியேறும்போது நினைவகப் பற்றாக்குறை இடையீட்டுப் பட்டி செயலில் இருந்தது."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "வெளியேறும்போது அனுமதிக் கோரிக்கைகள் இருந்தன."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "வெளியேறும்போது பாப்-அப் தடுப்பான் இயங்கிக் கொண்டிருந்தது."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "வெளியேறும்போது பாதுகாப்பு உலாவல் விவரங்கள் காட்டப்பட்டன."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "இந்தப் பக்கம் தவறான பயன்பாட்டில் ஈடுபடக்கூடியது என்பதைப் ‘பாதுகாப்பு உலாவல்’ அறிந்து, பாப்-அப்பைத் தடுத்துள்ளது."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் சேமிக்கப்பட்டிருந்தபோது service worker இயக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "ஆவணப் பிழையின் காரணமாக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "FencedFramesஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "மற்றொரு பக்கத்தைத் தற்காலிகமாகச் சேமிப்பதற்காக இந்தப் பக்கம் தற்காலிகச் சேமிப்பில் இருந்து அகற்றப்பட்டது."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "மீடியா ஸ்ட்ரீம் அணுகலை வழங்கியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "போர்ட்டல்களைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "IdleManagerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "பொது IndexedDB இணைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "பொருத்தமற்ற APIகள் பயன்படுத்தப்பட்டுள்ளன."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "நீட்டிப்புகள் மூலம் JavaScript உள்ளிடப்பட்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "நீட்டிப்புகள் மூலம் StyleSheet உள்ளிடப்பட்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | internalError": {"message": "அகப் பிழை."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "கீப்-அலைவ் கோரிக்கையின் காரணமாக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "கீபோர்டு லாக்கைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | loading": {"message": "வெளியேறுவதற்கு முன் பக்கம் ஏற்றப்படவில்லை."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "முதன்மை ஆதாரத்தில் ‘cache-control:no-cache’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "முதன்மை ஆதாரத்தில் ‘cache-control:no-store’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தின் மூலம் பக்கம் மீட்டெடுக்கப்படுவதற்கு முன் வழிசெலுத்துதல் ரத்துசெய்யப்பட்டது."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "செயலில் உள்ள நெட்வொர்க் இணைப்பு அதிகளவு தரவைப் பெற்றதால் தற்காலிகச் சேமிப்பில் இருந்து பக்கம் அகற்றப்பட்டது. பக்கம் தற்காலிகமாகச் சேமிக்கப்படும்போது அது எவ்வளவு தரவைப் பெறலாம் என்பதை Chrome கட்டுப்படுத்தும்."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() அல்லது XHR நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "செயலில் உள்ள நெட்வொர்க் கோரிக்கை திசைதிருப்பப்பட்டதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து பக்கம் அகற்றப்பட்டது."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "நெட்வொர்க் இணைப்பு நீண்ட நேரம் திறந்திருந்ததால் தற்காலிகச் சேமிப்பில் இருந்து பக்கம் அகற்றப்பட்டது. பக்கம் தற்காலிகமாகச் சேமிக்கப்படும்போது அது எவ்வளவு நேரம் தரவைப் பெறலாம் என்பதை Chrome கட்டுப்படுத்தும்."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "சரியான ‘பதிலளிப்புத் தலைப்பு’ இல்லாத பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "முதன்மை ஃபிரேமில் அல்லாமல் வேறு ஃபிரேமில் வழிசெலுத்துதல் மேற்கொள்ளப்பட்டது."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "செயலில் உள்ள indexed DB பரிமாற்றங்களைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "fetch() நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "XHR நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "PaymentManagerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "பிக்ச்சர்-இன்-பிக்ச்சர் அம்சத்தைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | portal": {"message": "போர்ட்டல்களைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | printing": {"message": "Printing UIயைக் காட்டும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "‘`window.open()`’ செயல்பாட்டைப் பயன்படுத்தி பக்கம் திறக்கப்பட்டதுடன் அதற்கான குறிப்பை மற்றொரு உலாவிப் பக்கம் கொண்டுள்ளது அல்லது சாளரத்தைப் பக்கம் திறந்துள்ளது."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் உள்ள பக்கத்திற்கான ரென்டரிங் செயல்முறை சிதைந்துவிட்டது."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் உள்ள பக்கத்திற்கான ரென்டரிங் செயல்முறை நிறுத்தப்பட்டது."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "ஆடியோவை ரெக்கார்டு செய்வதற்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "சென்சார் அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "பின்னணி ஒத்திசைவு அல்லது பெறுதல் அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "MIDI அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "அறிவிப்புகளுக்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "சேமிப்பக அணுகலைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "வீடியோவை ரெக்கார்டு செய்வதற்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPS என்ற URL ஸ்கீமைக் கொண்ட பக்கங்களை மட்டுமே தற்காலிகமாகச் சேமிக்க முடியும்."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் இருக்கும்போது service worker மூலம் அது கோரப்பட்டது."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்கப்பட்டுள்ள பக்கத்திற்கு `MessageEvent` ஐ அனுப்ப service worker முயற்சி செய்துள்ளது."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் இருந்தபோது ServiceWorker பதிவுநீக்கம் செய்யப்பட்டது."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "service worker இயக்கப்பட்டதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து பக்கம் அகற்றப்பட்டது."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome மீண்டும் தொடங்கப்பட்டு ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தின் உள்ளீடுகள் அழிக்கப்பட்டன."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "SharedWorkerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecognizerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesisஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "பக்கத்தில் உள்ள ஒரு iframe தொடங்கிய வழிசெலுத்துதல் நிறைவடையவில்லை."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "துணை ஆதாரத்தில் ‘cache-control:no-cache’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "துணை ஆதாரத்தில் ‘cache-control:no-store’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | timeout": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமித்து வைப்பதற்கான அதிகபட்ச நேரத்தை இந்தப் பக்கம் தாண்டிவிட்டதால் அது காலாவதியாகிவிட்டது."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கத்தைச் சேர்ப்பதற்கான நேரம் முடிந்துவிட்டது (நீண்ட நேரம் செயல்படும் pagehide ஹேண்ட்லர்கள் காரணமாக இவ்வாறு நடந்திருக்கலாம்)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "பக்கத்தின் முதன்மை ஃபிரேமில் அகற்றுதல் ஹேண்ட்லர் உள்ளது."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "பக்கத்தின் துணை ஃபிரேமில் அகற்றுதல் ஹேண்ட்லர் உள்ளது."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "யூசர் ஏஜெண்ட் ஓவர்-ரைடு தலைப்பை உலாவி மாற்றியுள்ளது."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "வீடியோ/ஆடியோவை ரெக்கார்டு செய்வதற்கான அணுகலை வழங்கியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabaseஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHIDயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLocksஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfcயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPServiceஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "WebRTCயுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebShareரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "WebSocketடுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "WebTransportடுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXRரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "பழைய உலாவிகளுடன் இணக்கமாக இருக்கும் வகையில் https: மற்றும் http: URL திட்டங்களைச் ('strict-dynamic' என்பதை ஆதரிக்கும் உலாவிகளால் புறக்கணிக்கப்பட்டது) சேர்க்க வேண்டும்."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "CSP3 முதல் disown-opener நிறுத்தப்பட்டது. இதற்குப் பதிலாக Cross-Origin-Opener-Policy தலைப்பைப் பயன்படுத்தவும்."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "CSP2 முதல் referrer நிறுத்தப்பட்டது. இதற்குப் பதிலாக Referrer-Policy தலைப்பைப் பயன்படுத்தவும்."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "CSP2 முதல் reflected-xss நிறுத்தப்பட்டது. இதற்குப் பதிலாக X-XSS-Protection தலைப்பைப் பயன்படுத்தவும்."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "தீங்கிழைப்பவர் கட்டுப்படுத்தும் டொமைனிற்கான தொடர்புடைய URLகள் (எ.கா. ஸ்கிரிப்ட்டுகள்) அனைத்திற்கும் அடிப்படை URLலை அமைக்க base-uri டைரெக்டிவ் இல்லாதது <base> குறியீடுகள் திணிப்பை அனுமதிக்கிறது. base-uri என்பதை 'none' அல்லது 'self' என அமைக்கவும்."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "object-src இல்லாததால் பாதுகாப்பற்ற ஸ்கிரிப்ட்டுகளை இயக்கும் செருகுநிரல்கள் சேர்க்கப்படலாம். முடிந்தால் object-src என்பதை 'none' என அமைத்துப் பார்க்கவும்."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src டைரெக்டிவ் இல்லை. பாதுகாப்பற்ற ஸ்கிரிப்ட்டுகளின் இயக்கத்தை இது அனுமதிக்கலாம்."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "அரைப்புள்ளியை மறந்துவிட்டீர்களா? {keyword} டைரெக்டிவ் போல் தெரிகிறது, தேடல் குறிப்பாக இல்லை."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "base64 எழுத்துக்குறியாக்கத்தை Nonces பயன்படுத்த வேண்டும்."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces குறைந்தபட்சம் 8 எழுத்துகள் வரை இருக்க வேண்டும்."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "இந்த டைரெக்டிவில் பிளைன் URL ஸ்கீம்களை ({keyword}) பயன்படுத்துவதைத் தவிர்க்கவும். பாதுகாப்பற்ற டொமைன்களில் இருந்து ஸ்கிரிப்ட்டுகளைப் பெற பிளைன் URL ஸ்கீம்கள் அனுமதிக்கின்றன."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "இந்த டைரெக்டிவில் பிளைன் வைல்டுகார்டுகளை ({keyword}) பயன்படுத்துவதைத் தவிர்க்கவும். பாதுகாப்பற்ற டொமைன்களில் இருந்து ஸ்கிரிப்ட்டுகளைப் பெற பிளைன் வைல்டுகார்டுகள் அனுமதிக்கின்றன."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "புகாரளிக்கும் இடத்தை report-to டைரெக்டிவ் வழியாக மட்டுமே உள்ளமைக்க வேண்டும். இந்த டைரெக்டிவ் Chromium சார்ந்த உலாவிகளில் மட்டுமே ஆதரிக்கப்படும் என்பதால் report-uri டைரெக்டிவ்வையும் பயன்படுத்துமாறு பரிந்துரைக்கப்படுகிறது."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "புகாரளிக்கும் இடத்தை எந்த CSPயும் உள்ளமைக்கவில்லை. CSPயைக் காலப்போக்கில் நிர்வகிப்பதையும் சிதைவுகளுக்காகக் கண்காணிப்பதையும் இது கடினமாக்குகிறது."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "ஹோஸ்ட் ஏற்புப் பட்டியல்கள் அடிக்கடி புறக்கணிக்கப்படலாம். இதற்குப் பதிலாக 'strict-dynamic' உடன் CSP nonces/hashes ஐப் பயன்படுத்திப் பார்க்கவும் (தேவையெனில்)."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "அறியப்படாத CSP டைரெக்டிவ்."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} தவறான தேடல் குறிப்பு போல் தெரிகிறது."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "பக்கத்தில் உள்ள பாதுகாப்பற்ற ஸ்கிரிப்ட்டுகள் & ஈவண்ட் ஹேண்ட்லர்கள் இயக்கத்தை 'unsafe-inline' அனுமதிக்கிறது. ஸ்கிரிப்ட்டுகளைத் தனியாக அனுமதிக்க, CSP nonces/hashes என்பதைப் பயன்படுத்தவும்."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "பழைய உலாவிகளுடன் இணக்கமாக இருக்கும் வகையில் 'unsafe-inline' (nonces/hashes என்பதை ஆதரிக்கும் உலாவிகளால் புறக்கணிக்கப்பட்டது) என்பதைச் சேர்ப்பதைக் கருத்தில் கொள்ளவும்."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "CORS `Access-Control-Allow-Headers`ஸைக் கையாளும்போது வைல்டுகார்டு குறியீட்டில் (*) அங்கீகரிப்பு சேர்க்கப்படாது."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "அகற்றப்பட்ட இடைவெளி `(n|r|t)` எழுத்துகள், ‘குறைவைக்’ குறிக்கும் எழுத்துகள் (`<`) ஆகிய இரண்டும் இருக்கும் URLகளின் ஆதாரக் கோரிக்கைகள் தடுக்கப்பட்டுள்ளன. இந்த ஆதாரங்களை ஏற்ற, நியூலைன்களை அகற்றி ‘குறைவைக்’ குறிக்கும் எழுத்துகளை எலிமெண்ட் ஆட்ரிபியூட் மதிப்புகள் போன்ற இடங்களில் இருந்து என்கோடிங் செய்யவும்."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` செயல்பாடு நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Navigation Timing 2ல் `nextHopProtocol`."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "`(0|r|n)` எழுத்து இருக்கும் குக்கீகள் துண்டிக்கப்படுவதற்குப் பதிலாக நிராகரிக்கப்படும்."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "`document.domain` ஐ அமைத்து அதே ஆரிஜின் கொள்கையைத் தளர்த்துவது நிறுத்தப்பட்டது. இயல்பாகவே இது முடக்கப்படும். இந்த நிறுத்த எச்சரிக்கை கிராஸ் ஆரிஜின் அணுகலுக்கானது. இது `document.domain` அமைப்பின் மூலம் இயக்கப்பட்டது."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "கிராஸ் ஆரிஜின் iframesஸில் இருந்து {PH1} ஐ டிரிகர் செய்வது நிறுத்தப்பட்டது, எதிர்காலத்தில் அது அகற்றப்படும்."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "இயல்பு அலைபரப்பு ஒருங்கிணைப்பை முடக்க `-internal-media-controls-overlay-cast-button` தேர்விக்குப் பதிலாக `disableRemotePlayback` பண்புக்கூறைப் பயன்படுத்த வேண்டும்."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} நிறுத்தப்பட்டது. இதற்குப் பதிலாக {PH2} ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "இது, மொழிபெயர்க்கப்பட்ட நிறுத்தச் சிக்கல் மெசேஜிற்கான ஓர் எடுத்துக்காட்டாகும்."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "`document.domain` ஐ அமைத்து அதே ஆரிஜின் கொள்கையைத் தளர்த்துவது நிறுத்தப்பட்டது. இயல்பாகவே இது முடக்கப்படும். இந்த அம்சத்தைத் தொடர்ந்து பயன்படுத்த ஆவணம் மற்றும் ஃபிரேம்களுக்கான HTTP பதிலுடன் `Origin-Agent-Cluster: ?0` தலைப்பை அனுப்பி ஆரிஜின்-கீய்டு ஏஜெண்ட் கிளஸ்டர்களின் ஒப்புதலை நீக்கவும். கூடுதல் தகவல்களுக்கு https://developer.chrome.com/blog/immutable-document-domain/ தளத்திற்குச் செல்லவும்."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும். இதற்குப் பதிலாக `Event.composedPath()` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` தலைப்பு நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும். ஏப்ரல் 30, 2018க்குப் பிறகு வெளியிடப்பட்ட பொதுவில் நம்பப்படும் சான்றிதழ்கள் அனைத்திற்குமான சான்றிதழ் வெளிப்படைத்தன்மை Chromeமிற்குத் தேவை."}, "core/lib/deprecations-strings.js | feature": {"message": "மேலும் விவரங்களுக்கு அம்சத்தின் நிலைப் பக்கத்தைப் பாருங்கள்."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் `getCurrentPosition()` மற்றும் `watchPosition()` இனி செயல்படாது. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் `getCurrentPosition()`, `watchPosition()` ஆகியவை நிறுத்தப்பட்டன. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் `getUserMedia()` இனி செயல்படாது. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` நிறுத்தப்பட்டது. இதற்குப் பதிலாக `RTCPeerConnectionIceErrorEvent.address` அல்லது `RTCPeerConnectionIceErrorEvent.port` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "`canmakepayment` service worker நிகழ்வில் இருந்து பெறப்பட்ட வணிகரின் ஆரிஜின் மற்றும் தன்னிச்சையான தரவு நிறுத்தப்பட்டு அகற்றப்படும்: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "பயனர்களின் சிறப்பு நெட்வொர்க் நிலை காரணமாக மட்டுமே இந்த இணையதளம் அணுகக்கூடிய நெட்வொர்க்கில் இருந்து துணை ஆதாரத்தைக் கோருகிறது. இந்தக் கோரிக்கைகள் பொதுவில் இல்லாத சாதனங்களையும் சேவையகங்களையும் இணையத்தில் வெளிப்படுத்தும். இதன் மூலம் வேற்று தளக் கோரிக்கை மோசடி (CSRF) தாக்குதல் மற்றும்/அல்லது தகவல் கசிவு போன்ற அபாயத்தை அதிகரிக்கும். இதுபோன்ற அபாயங்களைக் குறைக்க பொதுவில் இல்லாத துணை ஆதாரங்களுக்குப் பாதுகாப்பற்ற சூழல்களில் இருந்து வரக்கூடிய கோரிக்கைகளை Chrome நிறுத்துவதோடு அவற்றைத் தடுக்கவும் செய்கிறது."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "`.css` ஃபைல் நீட்டிப்பில் முடிவடைந்தால் மட்டுமே `file:` URLகளில் இருந்து CSSஸை ஏற்ற முடியும்."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "`remove()` இன் ஒத்திசையாத வரம்பு அகற்றுதலை ரத்துசெய்ய `SourceBuffer.abort()` ஐப் பயன்படுத்துவது விவரக்குறிப்பு மாற்றம் காரணமாக நிறுத்தப்பட்டது. இதற்கான ஆதரவும் எதிர்காலத்தில் அகற்றப்படும். அதற்குப் பதிலாக `updateend` நிகழ்வைக் கவனிக்க வேண்டும். ஒத்திசையாத மீடியா இணைப்பை ரத்துசெய்ய அல்லது பாகுபடுத்தும் நிலையை மீட்டமைக்க மட்டும் `abort()` பயன்படுத்தப்படும்."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "இடையகக் குறியீட்டு ஃபிரேம்களின் அதிகபட்சக் காட்சி நேரமுத்திரைக்குக் குறைவாக `MediaSource.duration` ஐ அமைப்பது விவரக்குறிப்பு மாற்றம் காரணமாக நிறுத்தப்பட்டது. துண்டிக்கப்பட்ட இடையக மீடியாவை மறைமுகமாக அகற்றுவதற்கான ஆதரவு எதிர்காலத்தில் அகற்றப்படும். `newDuration < oldDuration` எனும் நிலை உள்ள எல்லா இடங்களிலும் `sourceBuffers` அனைத்திலும் வெளிப்படையான `remove(newDuration, oldDuration)` ஐ மேற்கொள்ள வேண்டும்."}, "core/lib/deprecations-strings.js | milestone": {"message": "{milestone}வது பதிப்பில் இந்த மாற்றம் நடைமுறைக்கு வரும்."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "`MIDIOptions` இல் sysex குறிப்பிடப்படாமல் இருந்தாலும் பயன்படுத்துவதற்கான அனுமதியை இணைய MIDI கோரும்."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் இருந்து Notification APIயை இனி பயன்படுத்த முடியாது. உங்கள் ஆப்ஸைப் பாதுகாப்பான ஆரிஜினுக்கு (எ.கா. HTTPS) மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Notification APIக்கான அனுமதியைக் கிராஸ் ஆரிஜின் iframeமில் இருந்து இனி கோர முடியாது. உயர்நிலை ஃபிரேமிடம் அனுமதி கோரவும் அல்லது புதிய சாளரத்தைத் திறக்கவும்."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "காலாவதியான (D)TLS பதிப்பை உங்கள் கூட்டாளர் பரிமாற்றம் செய்கிறார். இதைச் சரிசெய்ய உங்கள் கூட்டாளரைத் தொடர்புகொள்ளவும்."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "பாதுகாப்பற்ற சூழல்களில் உள்ள WebSQL நிறுத்தப்பட்டது, அத்துடன் அது விரைவில் அகற்றப்படும். இணையச் சேமிப்பகம் அல்லது இன்டெக்ஸ் செய்யப்பட்ட தரவுத்தளத்தைப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "படம், வீடியோ மற்றும் கேன்வாஸ் குறிச்சொற்களில் `overflow: visible` ஐக் குறிப்பிடுவது, உறுப்பு வரம்புகளைத் தாண்டிய காட்சி உள்ளடக்கத்திற்கு வழிவகுக்கலாம். https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md எனும் இணைப்பைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` நிறுத்தப்பட்டது. அதற்குப் பதிலாக, பேமெண்ட் ஹேண்ட்லர்களுக்கான தக்க நேர நிறுவலைப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "உங்கள் `PaymentRequest` அழைப்பு உள்ளடக்கப் பாதுகாப்புக் கொள்கை (CSP) `connect-src` டைரெக்டிவைப் புறக்கணித்துவிட்டது. இந்தப் புறக்கணிப்பு நிறுத்தப்பட்டது. `PaymentRequest` APIயில் (`supportedMethods` புலத்தில்) உள்ள பேமெண்ட் முறை அடையாளங்காட்டியை உங்கள் CSP `connect-src` டைரெக்டிவில் சேர்க்கவும்."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` நிறுத்தப்பட்டது. இதற்குப் பதிலாக நிலையான `navigator.storage` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "முதல்நிலை `<picture>` உடன் கூடிய `<source src>` தவறானது, எனவே தவிர்க்கப்பட்டது. இதற்குப் பதிலாக `<source srcset>` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` நிறுத்தப்பட்டது. இதற்குப் பதிலாக நிலையான `navigator.storage` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "உட்பொதிந்த அனுமதிச் சான்றுகள் இருக்கும் URLகளின் துணை ஆதாரக் கோரிக்கைகள் (எ.கா. `**********************/`) தடுக்கப்பட்டன."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "`DtlsSrtpKeyAgreement` கட்டுப்பாடு அகற்றப்பட்டது. இந்தக் கட்டுப்பாட்டுக்கு `false` மதிப்பைக் குறிப்பிட்டுள்ளீர்கள். இதன் மூலம் அகற்றப்பட்ட `SDES key negotiation` முறையைப் பயன்படுத்த முயன்றதாகப் புரிந்துகொள்ளப்படுகிறது. இந்தச் செயல்பாடு அகற்றப்பட்டது, இதற்குப் பதிலாக `DTLS key negotiation` ஐ ஆதரிக்கும் சேவையைப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "`DtlsSrtpKeyAgreement` கட்டுப்பாடு அகற்றப்பட்டது. இந்தக் கட்டுப்பாட்டுக்கு `true` மதிப்பைக் குறிப்பிட்டுள்ளீர்கள். இது விளைவுகள் எதையும் ஏற்படுத்தாது, ஆனால் தோற்றத்திற்காக இந்தக் கட்டுப்பாட்டை நீங்கள் அகற்றலாம்."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` கண்டறியப்பட்டது. `Session Description Protocol` இன் இந்தக் குறிப்பிட்ட பதிப்பு இனி ஆதரிக்கப்படாது. இதற்குப் பதிலாக `Unified Plan SDP` ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`{sdpSemantics:plan-b}` உடன் `RTCPeerConnection` ஐ உருவாக்கும்போது `Plan B SDP semantics` பயன்படுத்தப்படும். இது இணைய பிளாட்ஃபார்மில் இருந்து நிரந்தரமாக நீக்கப்பட்ட `Session Description Protocol` இன் நிலையற்ற பழைய பதிப்பு. `IS_FUCHSIA` மூலம் உருவாக்கும்போது இதைத் தொடர்ந்து பயன்படுத்தலாம். ஆனால் கூடிய விரைவில் இதை நீக்கிவிடுவோம். இதைச் சார்ந்திருப்பதைத் தவிர்க்க வேண்டும். நிலையை அறிய https://crbug.com/1302249 தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` விருப்பம் நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும்."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer`க்குக் கிராஸ் ஆரிஜின் ஐசொலேஷன் தேவை. கூடுதல் தகவல்களுக்கு https://developer.chrome.com/blog/enabling-shared-array-buffer/ தளத்திற்குச் செல்லவும்."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "பயனர் செயல்படுத்தல் இல்லாத `speechSynthesis.speak()` நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும்."}, "core/lib/deprecations-strings.js | title": {"message": "நிறுத்தப்பட்ட அம்சம் பயன்படுத்தப்பட்டுள்ளது"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "`SharedArrayBuffer` ஐத் தொடர்ந்து பயன்படுத்த நீட்டிப்புகள் கிராஸ் ஆரிஜின் ஐசொலேஷனைப் பயன்படுத்த வேண்டும். https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ தளத்தைப் பார்வையிடவும்."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} விற்பனையாளர் சார்ந்தது. இதற்குப் பதிலாக நிலையான {PH2} ஐப் பயன்படுத்தவும்."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "`XMLHttpRequest` இல் உள்ள json பதிலில் UTF-16 ஆதரிக்கப்படுவதில்லை"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "இறுதிப் பயனர் அனுபவத்தில் பாதிப்பை ஏற்படுத்தும் என்பதால் முதன்மைத் தொடரிழையில் ஒத்திசையும் `XMLHttpRequest` நிறுத்தப்பட்டது. கூடுதல் உதவிக்கு https://xhr.spec.whatwg.org/ தளத்தைப் பார்க்கவும்."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` நிறுத்தப்பட்டது. இதற்குப் பதிலாக `isSessionSupported()` ஐப் பயன்படுத்தித் தீர்க்கப்பட்ட பூலியன் மதிப்பைப் பார்க்கவும்."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "முக்கியத் தொடரிழையில் தடுப்பதற்குச் செலவிட்ட நேரம்"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "தற்காலிக நினைவக TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "விளக்கம்"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "கால அளவு"}, "core/lib/i18n/i18n.js | columnElement": {"message": "உறுப்பு"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "தோல்வியுறும் உறுப்புகள்"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "இடம்"}, "core/lib/i18n/i18n.js | columnName": {"message": "பெயர்"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "பட்ஜெட்டைத் தாண்டிவிட்டது"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "கோரிக்கைகள்"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "ஆதாரங்களின் அளவு"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "ஆதார வகை"}, "core/lib/i18n/i18n.js | columnSize": {"message": "அளவு"}, "core/lib/i18n/i18n.js | columnSource": {"message": "மூலம்"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "தொடக்க நேரம்"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "செலவிட்ட நேரம்"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "பரிமாற்ற அளவு"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "சேமிக்கப்படக்கூடியது"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "சேமிக்கப்படக்கூடியது"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "சேமிக்கக்கூடிய அளவு: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 உறுப்பு உள்ளது}other{# உறுப்புகள் உள்ளன}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "சேமிக்கக்கூடிய நேரம்: {wastedMs, number, milliseconds} மி.வி."}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "ஆவணம்"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "அர்த்தமுள்ள முதல் தோற்றம்"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "எழுத்துரு"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "படம்"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "செயல்-காட்சி நேரம்"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "அதிகம்"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "குறைவானது"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "நடுத்தரமானது"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "முதல் உள்ளீட்டிற்கு பதிலளிக்கக்கூடிய அதிகபட்ச நேரம்"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "மீடியா"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} மி.வி."}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "மற்றவை"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "இதர மூலங்கள்"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "ஸ்கிரிப்ட்"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} வி."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "ஸ்டைல்ஷீட்"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "மூன்றாம் தரப்பு"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "மொத்தம்"}, "core/lib/lh-error.js | badTraceRecording": {"message": "உங்கள் பக்கம் ஏற்றப்படுகையில் டிரேஸைப் பதிவுசெய்யும்போது ஏதோ தவறாகிவிட்டது. Lighthouseஸை மீண்டும் இயக்கவும். ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "முதல் பிழைதிருத்தும் நெறிமுறை இணைப்பிற்கான நேரமுடிவு காத்திருப்பு."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "பக்கம் ஏற்றப்பட்டபோது ஸ்க்ரீன்ஷாட்கள் எதையும் Chrome சேகரிக்கவில்லை. பக்கத்தில் ஏதேனும் உள்ளடக்கம் காண்பிக்கப்படுவதை உறுதிப்படுத்திக் கொண்டு Lighthouseஸை மீண்டும் இயக்கவும். ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "வழங்கப்பட்டுள்ள டொமைனை DNS சேவையகங்களால் அடையாளம் காண முடியவில்லை."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "தேவைப்படும் {artifactName} சேகரிப்பானில் பிழையொன்று ஏற்பட்டது: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Chrome அகப் பிழை நேர்ந்தது. Chromeமை மீண்டும் தொடங்கி, Lighthouseஸை மீண்டும் இயக்கவும்."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "தேவைப்படும் {artifactName} சேகரிப்பான் இயங்கவில்லை."}, "core/lib/lh-error.js | noFcp": {"message": "பக்கம் எந்தவொரு உள்ளடக்கத்தையும் காட்சிப்படுத்தவில்லை. பக்கம் ஏற்றப்படும்போது உலாவிச் சாளரத்தை முன்புலத்திலேயே வைத்துக்கொண்டு மீண்டும் முயலவும். ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "பெரிய பகுதியைக் காட்டும் நேர (LCP) அளவீட்டிற்குத் தகுதிபெறும் உள்ளடக்கம் பக்கத்தில் காட்டப்படவில்லை. பக்கத்தில் சரியான LCP உறுப்பு இருப்பதை உறுதிசெய்துவிட்டு மீண்டும் முயலவும். ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "வழங்கிய பக்கம் HTML அல்ல ({mimeType} என்ற MIME வகையாக வழங்கப்பட்டுள்ளது)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Chromeமின் இந்தப் பதிப்பு மிகவும் பழையது என்பதால் '{featureName}' ஐ ஆதரிக்கவில்லை. அனைத்து முடிவுகளையும் பார்ப்பதற்கு புதிய பதிப்பைப் பயன்படுத்தவும்."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "நீங்கள் கோரிய பக்கத்தை Lighthouseஸால் முழுமையாக ஏற்ற முடியவில்லை. நீங்கள் சரியான URLலைச் சோதிப்பதையும் அனைத்துக் கோரிக்கைகளுக்கும் சேவையகம் சரியாகப் பதிலளிப்பதையும் உறுதிசெய்யவும்."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "பக்கம் பதிலளிப்பதை நிறுத்தியதால் நீங்கள் கோரிய URLலை Lighthouseஸால் முழுமையாக ஏற்ற முடியவில்லை."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "நீங்கள் வழங்கியுள்ள URLலில் செல்லுபடியாகும் பாதுகாப்பு சான்றிதழ்கள் இல்லை. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "பக்கத்தை ஏற்றுவதை இடைச்செருகல் திரையொன்றின் மூலம் Chrome தடுத்துவிட்டது. நீங்கள் சரியான URLலைப் பரிசோதிப்பதையும் அனைத்துக் கோரிக்கைகளுக்கும் சேவையகம் முறையாக பதிலளிப்பதையும் உறுதிசெய்து கொள்ளவும்."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "நீங்கள் கோரிய பக்கத்தை Lighthouseஸால் முழுமையாக ஏற்ற முடியவில்லை. நீங்கள் சரியான URLலைப் பரிசோதிப்பதையும் அனைத்துக் கோரிக்கைகளுக்கும் சேவையகம் முறையாக பதிலளிப்பதையும் உறுதிசெய்து கொள்ளவும். (விவரங்கள்: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "நீங்கள் கோரிய பக்கத்தை Lighthouseஸால் முழுமையாக ஏற்ற முடியவில்லை. நீங்கள் சரியான URLலைப் பரிசோதிப்பதையும் அனைத்துக் கோரிக்கைகளுக்கும் சேவையகம் முறையாக பதிலளிப்பதையும் உறுதிசெய்து கொள்ளவும். (நிலைக் குறியீடு: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "உங்கள் பக்கம் ஏற்றப்பட நீண்ட நேரம் எடுத்துக்கொண்டது. பக்கம் ஏற்றப்படும் நேரத்தைக் குறைக்க அறிக்கையிலுள்ள வாய்ப்புகளைப் பின்பற்றி Lighthouseஸை மீண்டும் இயக்கவும். ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools நெறிமுறைக்காகக் காத்திருக்கும் ஒதுக்கப்பட்ட நேரத்தை மீறிவிட்டது. (முறை:{protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "ஒதுக்கப்பட்ட நேரத்தை ஆதார உள்ளடக்கத்தைப் பெறுவதற்கான நேரம் மீறிவிட்டது"}, "core/lib/lh-error.js | urlInvalid": {"message": "நீங்கள் அளித்துள்ள URL செல்லாததெனத் தோன்றுகிறது."}, "core/lib/navigation-error.js | warningXhtml": {"message": "பக்கத்தின் MIME வகை XHTMLலைச் சார்ந்தது: Lighthouse இந்த ஆவண வகையை வெளிப்படையாக ஆதரிக்காது"}, "core/user-flow.js | defaultFlowName": {"message": "பயனர் செல்லும் பக்கங்களின் வரிசை ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "வழிசெலுத்துதல் அறிக்கை ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "ஸ்னாப்ஷாட் அறிக்கை ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "கால அளவு அறிக்கை ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "அனைத்து அறிக்கைகளும்"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "வகைகள்"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "அணுகல்தன்மை"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "சிறந்த நடைமுறைகள்"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "இணையச் செயல்திறன்"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "நவீன இணைய ஆப்ஸ்"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "டெஸ்க்டாப்"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Lighthouse செயல்முறை அறிக்கையை அறிந்துகொள்ளுதல்"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "செயல்முறைகளை அறிந்துகொள்க"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "வழிசெலுத்துதல் குறித்த அறிக்கைகளை இவற்றுக்குப் பயன்படுத்து..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "ஸ்னாப்ஷாட் குறித்த அறிக்கைகளை இவற்றுக்குப் பயன்படுத்து..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "கால அளவு குறித்த அறிக்கைகளை இவற்றுக்குப் பயன்படுத்து..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Lighthouseஸின் செயல்திறன் ஸ்கோரைப் பெறுதல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "பெரிய பகுதியைக் காண்பிக்கும் நேரம், ஸ்பீடு இண்டெக்ஸ் போன்ற ‘பக்க ஏற்றச் செயல்திறன் அளவீடுகளை’ அளவிடுதல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "மேம்பட்ட இணைய ஆப்ஸின் திறன்களை மதிப்பாய்வு செய்தல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "ஒற்றைப் பக்க ஆப்ஸ், சிக்கலான படிவங்கள் போன்றவற்றில் உள்ள அணுகல்தன்மைச் சிக்கல்களைக் கண்டறிதல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "செயல்பாட்டிற்குப் பின்னால் மறைக்கப்பட்டுள்ள மெனுக்கள், UI உறுப்புகள் ஆகியவற்றின் சிறந்த நடைமுறைகளை மதிப்பாய்வு செய்தல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "தொடர் செயல்பாடுகளில் தளவமைப்பு மாற்றங்கள், JavaScript செயல்பாட்டு நேரம் ஆகியவற்றை அளவிடுதல்."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "நீண்ட நேரம் திறந்திருக்கும் பக்கங்கள், ஒற்றைப் பக்க ஆப்ஸ் ஆகியவை தரும் பயனர் அனுபவத்தை மேம்படுத்தும் வகையில் செயல்திறன் வாய்ப்புகளைக் கண்டறிதல்."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "அதிகளவு தாக்கத்தை ஏற்படுத்தியவை"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{தகவல்பூர்வமான தணிக்கை: {numInformative}}other{தகவல்பூர்வமான தணிக்கைகள்: {numInformative}}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "மொபைல்"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "பக்க ஏற்றம்"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "அசல் Lighthouse அறிக்கைகளைப் போலவே வழிசெலுத்துதல் அறிக்கைகளும் ஒற்றைப் பக்க ஏற்றத்தைப் பகுப்பாய்வு செய்யும்."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "வழிசெலுத்துதல் அறிக்கை"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{வழிசெலுத்துதல் குறித்த அறிக்கை: {numNavigation}}other{வழிசெலுத்துதல் குறித்த அறிக்கைகள்: {numNavigation}}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{தேர்ச்சி பெறக்கூடிய தணிக்கை: {numPassableAudits}}other{தேர்ச்சி பெறக்கூடிய தணிக்கைகள்: {numPassableAudits}}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} தணிக்கை தேர்ச்சி பெற்றது}other{{numPassed} தணிக்கைகள் தேர்ச்சி பெற்றன}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "சராசரி"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "பிழை"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "மோசம்"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "நன்று"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "சேமி"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "படமெடுக்கப்பட்ட பக்கத்தின் நிலை"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "ஸ்னாப்ஷாட் அறிக்கைகள் ஒரு குறிப்பிட்ட நிலையில் பக்கத்தைப் பகுப்பாய்வு செய்யும். பெரும்பாலும் பயனரின் செயல்பாடுகளுக்குப் பிறகே பகுப்பாய்வு செய்யும்."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "ஸ்னாப்ஷாட் அறிக்கை"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{ஸ்னாப்ஷாட் குறித்த அறிக்கை: {numSnapshot}}other{ஸ்னாப்ஷாட் குறித்த அறிக்கைகள்: {numSnapshot}}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "சுருக்க விவரம்"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "பயனர் செயல்பாடுகள்"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "கால அளவு குறித்த அறிக்கைகள் தன்னிச்சையான கால அளவைப் பகுப்பாய்வு செய்யும். பெரும்பாலும், இந்தக் கால அளவில் பயனரின் செயல்பாடுகள் இடம்பெற்றிருக்கும்."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "கால அளவு குறித்த அறிக்கை"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{கால அளவு குறித்த அறிக்கை: {numTimespan}}other{கால அளவு குறித்த அறிக்கைகள்: {numTimespan}}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouseஸில் பயனர் செல்லும் பக்கங்களின் வரிசை குறித்த அறிக்கை"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "அனிமேஷன் செய்யப்பட்ட உள்ளடக்கமாக இருந்தால், உள்ளடக்கம் திரைக்கு வெளியில் இருக்கும்போது CPU உபயோகத்தைக் குறைக்கும் வகையில் [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) காம்பனென்ட்டைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "மற்ற உலாவிகளுக்குத் தகுந்த ஒரு ஃபால்பேக்கைக் குறிப்பிடும்போது [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) காம்பனென்ட்கள் அனைத்தையும் WebP வடிவங்களில் காட்சிப்படுத்தவும். [மேலும் அறிக](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "தேவையுள்ளபோது படங்களைத் தானாகவே ஏற்றுவதற்கு [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) காம்பனென்ட்டைப் பயன்படுத்துவதை உறுதிசெய்துகொள்ளவும். [மேலும் அறிக](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[AMP மேம்படுத்துதல் அம்சம்](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) முதல் [சேவையகத் தரப்பு ரெண்டரிங் AMP தளவமைப்புகள்](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) போன்ற அம்சங்களைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "அனைத்து ஸ்டைல்களும் ஆதரிக்கப்படுவதை உறுதிசெய்துகொள்ள, [AMP ஆவணமாக்கலைப்](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) பார்க்கவும்."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "திரை அளவின் அடிப்படையில் எந்தப் பட அசெட்டுகளைப் பயன்படுத்த வேண்டும் என்பதைக் குறிப்பிடும் வகையில் [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) காம்பனென்ட் [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) பண்புக்கூறை ஆதரிக்கும். [மேலும் அறிக](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "பெரிய பட்டியல்கள் ரெண்டரிங் செய்யப்பட்டால் Component Dev Kitஐ (CDK) பயன்படுத்தி விர்ச்சுவல் ஸ்கிராலிங்க் செய்வதைப் பரிசீலிக்கவும். [மேலும் அறிக](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "உங்கள் JavaScript தொகுப்புகளின் அளவைக் குறைக்க, [ரூட் நிலையில் குறியீட்டைப் பிரிப்பதைப்](https://web.dev/route-level-code-splitting-in-angular/) பயன்படுத்தவும். இத்துடன் [Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/)ரைப் பயன்படுத்தி அசெட்டுகளை முன்கூட்டியே தற்காலிகமாகவும் சேமிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "நீங்கள் Angular CLIயை பயன்படுத்தினால் பதிப்புகள் தயாரிப்பு பயன்முறையில் உருவாக்கப்படுவதை உறுதிப்படுத்திக்கொள்ளவும். [மேலும் அறிக](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Angular CLIயைப் பயன்படுத்தினால் உங்கள் தொகுப்புகளை ஆய்வு செய்யும் வகையில் தயாரிப்புப் பதிப்பில் மூல வரைபடங்களைச் சேர்க்கவும். [மேலும் அறிக](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "வழிசெலுத்தலை வேகப்படுத்த வழிகளை முன்கூட்டியே ஏற்றவும். [மேலும் அறிக](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "பட பிரேக்பாயிண்ட்டுகளை நிர்வகிக்க Component Dev Kitடில் (CDK) `BreakpointObserver` உபகரணத்தைப் பயன்படுத்துவதைப் பரிசீலிக்கவும். [மேலும் அறிக](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "GIFஐ HTML5 வீடியோவாக உட்பொதிக்க உதவும் சேவையில் அதைப் பதிவேற்றவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "உங்கள் தீமில் பிரத்தியேக எழுத்துருக்களை வரையறுக்கும்போது `@font-display` ஐக் குறிப்பிடவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "உங்கள் தளத்தில் [‘பட வடிவமைப்பை மாற்று’ என்பதைப் பயன்படுத்தி WebP பட வடிவமைப்புகளை](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) உள்ளமைக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "படங்களைத் தேவையுள்ளபோது ஏற்றும் [Drupal மாடியூலை](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) நிறுவவும். செயல்திறனை மேம்படுத்துவதற்காக இந்த மாடியூல்கள் திரைக்கு வெளியில் உள்ள படங்களை மெதுவாக ஏற்றும் வசதியை வழங்குகின்றன."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "முக்கியமான CSS & JavaScriptடை முன்னிலைப்படுத்த, மாடியூலைப் பயன்படுத்தவும் அல்லது [மேம்பட்ட CSS/JS ஒருங்கிணைப்பு](https://www.drupal.org/project/advagg) மாடியூல் போன்ற JavaScript மூலம் ஒத்திசைவின்றி அசெட்டுகளை ஏற்றவும். இந்த மாடியூல் வழங்கும் மேம்படுத்துதல்கள் உங்கள் இணையதளத்தைப் பாதிக்கலாம் என்பதால் நீங்கள் குறியீட்டில் மாற்றங்களைச் செய்ய வேண்டியிருக்கும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "தீம்கள், மாடியூல்கள், சேவையக விவரக்குறிப்புகள் ஆகிய அனைத்தும் சேவையகத்தின் வேகத்தை நிர்ணயிக்கின்றன. மேலும் மேம்படுத்தப்பட்ட தீமினைக் கண்டறிந்து, மேம்படுத்தல் மாடியூலைக் கவனமாகத் தேர்ந்தெடுக்கவும் மற்றும்/அல்லது சேவையகத்தை மேம்படுத்தவும். PHP ஆப்கோட் தற்காலிகச் சேமிப்பு, Redis/Memcached போன்ற தரவுத்தள வினவல் நேரங்களைக் குறைப்பதற்கான தற்காலிக நினைவகச் சேமிப்பு, பக்கங்கள் விரைவாக ஏற்றப்படுவதற்கான மேம்பட்ட பயன்பாட்டு லாஜிக் ஆகியவற்றை உங்கள் ஹோஸ்டிங் சேவையகங்கள் பயன்படுத்த அனுமதிக்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "உங்கள் பக்கத்தில் ஏற்றப்படும் படங்களின் அளவைக் குறைக்க [திரைக்கு ஏற்ப அளவு மாறும் பட ஸ்டைல்களைப்](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) பயன்படுத்தவும். பக்கத்தில் பல உள்ளடக்கத்தைக் காட்டுவதற்கு ’காட்சிகளைப்’ பயன்படுத்தினால் அந்தப் பக்கத்தில் காட்டப்படும் உள்ளடக்கத்தின் எண்ணிக்கையை வரம்பிட அதைப் பக்கங்களாகப் பிரிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "\"நிர்வாகம் » உள்ளமைவு » மேம்பாடு\" என்ற பக்கத்தில் “ஒருங்கிணைப்பு CSS கோப்புகளை” இயக்கியுள்ளதை உறுதிசெய்துகொள்ளவும். CSS ஸ்டைல்களை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் உங்கள் தளத்தின் செயல்பாட்டை வேகப்படுத்த, [கூடுதல் மாடியூல்கள்](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) மூலமாகவும் மேலும் மேம்பட்ட ஒருங்கிணைப்பு விருப்பங்களை உள்ளமைக்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "\"நிர்வாகம் » உள்ளமைவு » மேம்பாடு\" என்ற பக்கத்தில் “JavaScript கோப்புகளை ஒருங்கிணை” என்பதை இயக்கியுள்ளதை உறுதிசெய்துகொள்ளவும். JavaScript அசெட்டுகளை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் உங்கள் தளத்தின் செயல்பாட்டை வேகப்படுத்த, [கூடுதல் மாடியூல்கள்](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) மூலமாகவும் மேலும் மேம்பட்ட ஒருங்கிணைப்பு விருப்பங்களை உள்ளமைக்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "பயன்படுத்தப்படாத CSS விதிகளை அகற்றிவிட்டு தொடர்புடைய பக்கம்/பக்கத்தில் உள்ள காம்பனென்ட்டில் தேவையான Drupal லைப்ரரிகளை மட்டும் இணைக்கவும். விவரங்களுக்கு [Drupal ஆவணமாக்கல் இணைப்பைப்](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) பார்க்கவும். பொருத்தமற்ற CSSஸைச் சேர்க்கும் இணைக்கப்பட்ட லைப்ரரிகளை அடையாளம் காண, Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கிப் பார்க்கவும். Drupal தளத்தில் CSS ஒருங்கிணைப்பு முடக்கப்பட்டிருக்கும்போது ஸ்டைல்ஷீட்டின் URLலில் இதற்குக் காரணமான தீம்/மாடியூலைக் கண்டறியலாம். அதிகளவு சிவப்பில் உள்ள குறியீட்டுக் கவரேஜைக் கொண்ட பட்டியலில் பல ஸ்டைல்ஷீட்களைக் கொண்ட தீம்கள்/மாடியூல்களைக் கண்டறியவும். பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு தீம்/மாடியூல் ஒரு ஸ்டைல்ஷீட்டை மட்டுமே வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "பயன்படுத்தப்படாத JavaScript அசெட்டுகளை அகற்றிவிட்டு தொடர்புடைய பக்கம்/பக்கத்தில் உள்ள காம்பனென்ட்டில் தேவையான Drupal லைப்ரரிகளை மட்டும் இணைக்கவும். விவரங்களுக்கு [Drupal ஆவணமாக்கல் இணைப்பைப்](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) பார்க்கவும். பொருத்தமற்ற JavaScriptடைச் சேர்க்கும் இணைக்கப்பட்ட லைப்ரரிகளை அடையாளம் காண, Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கிப் பார்க்கவும். Drupal தளத்தில் JavaScript ஒருங்கிணைப்பு முடக்கப்பட்டிருக்கும்போது ஸ்கிரிப்ட்டின் URLலில் இதற்குக் காரணமான தீம்/மாடியூலைக் கண்டறியலாம். குறியீட்டுக் கவரேஜில் அதிக சிவப்பைக் கொண்ட பட்டியலிலுள்ள பல ஸ்கிரிப்ட்டுகளைக் கொண்ட தீம்கள்/மாடியூல்களைக் கண்டறியவும். பக்கத்தில் பயன்படுத்தும் பட்சத்தில் மட்டுமே ஒரு தீம்/மாடியூல் ஒரு ஸ்கிரிப்ட்டை வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "\"நிர்வாகம் » உள்ளமைவு » மேம்பாடு\" என்ற பக்கத்தில் \"உலாவி & ப்ராக்ஸி தகவல்களை நீண்ட காலத்திற்குத் தற்காலிகமாகச் சேமி\" என அமைக்கவும். [செயல்திறனுக்கான Drupal தற்காலிகச் சேமிப்பு & மேம்படுத்துதல்](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources) குறித்து தெரிந்துகொள்ளவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "தளத்தில் பதிவேற்றப்பட்ட படங்களின் தரம் குறையாமல் தானாகவே அவற்றை மேம்படுத்தும் & அவற்றின் அளவைக் குறைக்கும் [மாடியூலைப்](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) பயன்படுத்தவும். இணையதளத்தில் ரெண்டர் செய்யப்பட்ட அனைத்துப் படங்களுக்கும் Drupalலில் வழங்கப்பட்டுள்ள இயல்பான [திரைக்கு ஏற்ப அளவு மாறும் பட ஸ்டைல்களை](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (Drupal 8 & அதற்குப் பிந்தைய பதிப்பில் கிடைக்கும்) பயன்படுத்துவதையும் உறுதிசெய்துகொள்ளவும்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "பயனர் ஏஜெண்ட் மூலக் குறிப்புகளுக்கான வசதிகளை வழங்கும் [ஒரு மாடியூலை](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) நிறுவி உள்ளமைப்பதன் மூலம் முன்னிணைப்பு அல்லது dns-ப்ரீஃபெட்ச் மூலக் குறிப்புகளைச் சேர்க்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Drupalலில் வழங்கப்பட்டுள்ள இயல்பான [திரைக்கு ஏற்ப அளவு மாறும் பட ஸ்டைல்களை](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (Drupal 8 & அதற்குப் பிந்தைய பதிப்பில் கிடைக்கும்) பயன்படுத்துவதை உறுதிசெய்துகொள்ளவும். காட்சிப் பயன்முறைகள், காட்சிகள் ஆகியவற்றின் மூலமாகவோ WYSIWYG எடிட்டரைப் பயன்படுத்திப் பதிவேற்றப்பட்ட படங்களின் மூலமாகவோ படப் புலங்களை ரெண்டர் செய்யும்போது திரைக்கு ஏற்ப அளவு மாறும் பட ஸ்டைல்களைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "இணைய எழுத்துருக்கள் ஏற்றப்படும்போது பயனருக்குத் தெரியும் வகையில் வார்த்தை இருப்பதை உறுதிசெய்வதற்காக `font-display` CSS அம்சத்தைத் தானாகப் பயன்படுத்த, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Optimize Fonts` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "படங்களை WebPக்கு மாற்ற, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Next-Gen Formats` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "தற்போது திரையில் காட்டப்படாத படங்கள் ஏற்றப்படுவதைத் தாமதப்படுத்த, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Lazy Load Images` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "முக்கியமில்லாத JS/CSSஸைத் தாமதப்படுத்த, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Critical CSS`, `Script Delay` ஆகியவற்றையும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "ஏற்றப்படும் நேரத்தைக் குறைத்து, எங்களின் உலகளாவிய நெட்வொர்க் முழுவதும் உங்கள் உள்ளடக்கத்தைத் தற்காலிகமாகச் சேமிக்க, [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) ஆப்ஸைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "நெட்வொர்க் பேலோடு அளவுகளைக் குறைக்கும் வகையில் CSSஸைத் தானாகச் சிறிதாக்க, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Minify CSS` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "நெட்வொர்க் பேலோடு அளவுகளைக் குறைக்கும் வகையில் JSஸைத் தானாகச் சிறிதாக்க, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Minify Javascript` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "இந்தச் சிக்கலுக்கு உதவ, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Remove Unused CSS` ஐயும் இயக்கவும். இது உங்கள் தளத்தின் ஒவ்வொரு பக்கத்திலும் பயன்படுத்தப்படும் CSS கிளாஸ்களைக் கண்டறிவதுடன் ஃபைலின் அளவைச் சிறியதாக வைத்திருப்பதற்காக பிறவற்றையும் அகற்றும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "நிலையான அசெட்டுகளுக்கான தற்காலிகச் சேமிப்புத் தலைப்பில், பரிந்துரைக்கப்படும் மதிப்புகளை அமைக்க, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Efficient Static Cache Policy` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "படங்களை WebPக்கு மாற்ற, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Next-Gen Formats` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "முக்கியமான மூன்றாம் தரப்பு ஆரிஜின்களுக்கு விரைவான இணைப்புகளை ஏற்படுத்துவதற்காக `preconnect` ஆதாரக் குறிப்புகளைத் தானாகச் சேர்க்க, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Pre-Connect Origins` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "பக்கம் ஏற்றப்படும்போது தற்சமயம் பின்னர் கோரப்படும் ஆதாரங்களைப் பெற முன்னுரிமை அளிப்பதற்காக `preload` இணைப்புகளைச் சேர்க்க, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Preload Fonts`, `Preload Background Images` ஆகியவற்றையும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "நெட்வொர்க் பேலோடு அளவுகளைக் குறைத்து, சாதனத்திற்குப் பொருத்தமாக இருக்கும் வகையில் படங்களை அளவு மாற்றம் செய்ய, [Ezoic Leap](https://pubdash.ezoic.com/speed) கருவியைப் பயன்படுத்துவதுடன் `Resize Images` ஐயும் இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "GIFஐ HTML5 வீடியோவாக உட்பொதிக்க உதவும் சேவையில் அதைப் பதிவேற்றவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "நீங்கள் பதிவேற்றிய படங்களை மேம்படுத்தப்பட்ட வடிவமைப்புகளுக்குத் தானாக மாற்றும் ஒரு [செருகுநிரலையோ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) சேவையையோ பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "திரைக்கு வெளியில் உள்ள படங்களை மெதுவாக ஏற்றும் வசதியை வழங்குகிற [தேவையுள்ளபோது ஏற்றும் Joomla செருகுநிரலை](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) நிறுவவும் அல்லது அந்த வசதியை வழங்கும் டெம்ப்ளேட்டிற்கு மாறவும். Joomla 4.0 ஐப் பயன்படுத்தித் தொடங்குவதன் மூலம் புதிய படங்கள் அனைத்தும் `loading` பண்புக்கூறு முழுவதையும் [தானாகவே](https://github.com/joomla/joomla-cms/pull/30748) பெறும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "[முக்கியமான அசெட்டுகளை முன்னிலைப்படுத்த](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)/[முக்கியத்துவம் குறைவான ஆதாரங்களைத் தவிர்க்க](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) பல Joomla செருகுநிரல்கள் உதவுகின்றன. இந்தச் செருகுநிரல்கள் வழங்கும் மேம்படுத்துதல்கள் உங்கள் டெம்ப்ளேட்டுகள்/செருகுநிரல்களில் உள்ள அம்சங்களைப் பாதிக்கலாம் என்பதால் இவற்றை நீங்கள் முற்றிலும் சோதனை செய்ய வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "டெம்ப்ளேட்டுகள், நீட்டிப்புகள், சேவையக விவரக்குறிப்புகள் ஆகிய அனைத்தும் சேவையகத்தின் வேகத்தை நிர்ணயிக்கின்றன. மேலும் மேம்படுத்தப்பட்ட டெம்ப்ளேட்டைக் கண்டறிந்து, மேம்படுத்தல் நீட்டிப்பைக் கவனமாகத் தேர்ந்தெடுக்கவும் மற்றும்/அல்லது சேவையகத்தை மேம்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "கட்டுரை வகைகளில் முக்கியமான பகுதியை மட்டும் காட்டவும் (உதாரணமாக மேலும் காட்டு என்ற இணைப்பின் மூலம்), பக்கத்தில் கட்டுரைகளின் எண்ணிக்கையைக் குறைக்கவும், பெரிய இடுகைகளை ஒன்றுக்கும் மேற்பட்ட பக்கங்களாகப் பிரிக்கவும் அல்லது தேவையுள்ளபோது மட்டும் கருத்துகளைச் செருகுநிரல்கள் மூலம் ஏற்றவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "பல [Joomla நீட்டிப்புகளால்](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) உங்கள் இணையதளத்தில் உள்ள css ஸ்டைல்களை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் தளத்தின் செயல்பாட்டை வேகப்படுத்த முடியும். இந்த வசதியை வழங்கும் டெம்ப்ளேட்டுகளும் உள்ளன."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "பல [Joomla நீட்டிப்புகளால்](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) உங்கள் இணையதளத்தில் உள்ள ஸ்கிரிப்ட்டுகளை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் தளத்தின் செயல்பாட்டை வேகப்படுத்த முடியும். இந்த வசதியை வழங்கும் டெம்ப்ளேட்டுகளும் உள்ளன."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "உங்கள் பக்கத்தில் ‘பயன்படுத்தப்படாத CSSஸை’ ஏற்றும் [Joomla நீட்டிப்புகளின்](https://extensions.joomla.org/) எண்ணிக்கையைக் குறைக்கவும்/மாற்றவும். பொருத்தமற்ற CSSஸைச் சேர்க்கும் நீட்டிப்புகளை அடையாளம் காண, Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கிப் பார்க்கவும். ஸ்டைல்ஷீட்டின் URLலில் இதற்குக் காரணமான தீம்/செருகுநிரலைக் கண்டறியலாம். அதிகளவு சிவப்பில் உள்ள குறியீட்டுக் கவரேஜைக் கொண்ட பட்டியலில் பல ஸ்டைல்ஷீட்களைக் கொண்ட செருகுநிரல்களைக் கண்டறியவும். பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு செருகுநிரல் ஒரு ஸ்டைல்ஷீட்டை மட்டுமே வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "உங்கள் பக்கத்தில் ‘பயன்படுத்தப்படாத JavaScriptடை’ ஏற்றும் [Joomla நீட்டிப்புகளின்](https://extensions.joomla.org/) எண்ணிக்கையைக் குறைக்கவும்/மாற்றவும். பொருத்தமற்ற JSஸைச் சேர்க்கும் செருகுநிரல்களை அடையாளம் காண, Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கிப் பார்க்கவும். ஸ்கிரிப்ட்டின் URLலில் இதற்குக் காரணமான நீட்டிப்பைக் கண்டறியலாம். அதிகளவு சிவப்பில் உள்ள குறியீட்டுக் கவரேஜைக் கொண்ட பட்டியலில் பல ஸ்கிரிப்ட்டுகளைக் கொண்ட நீட்டிப்புகளைக் கண்டறியவும். பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு நீட்டிப்பு ஒரு ஸ்கிரிப்ட்டை மட்டுமே வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Joomlaவில் உலாவியின் தற்காலிகச் சேமிப்பு](https://docs.joomla.org/Cache) குறித்து தெரிந்துகொள்ளவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "படங்களின் தரம் குறையாமல் அவற்றைச் சுருக்க, [படத்தை மேம்படுத்தும் செருகுநிரலைப்](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "உங்கள் உள்ளடக்கத்தில் திரைக்கு ஏற்ப அளவு மாறும் படங்களைப் பயன்படுத்த, [திரைக்கு ஏற்ப அளவு மாறும் படங்களுக்கான செருகுநிரலைப்](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Joomlaவில் Gzip பக்கச் சுருக்குதலை இயக்கி உரைச் சுருக்கத்தை இயக்கலாம் (சிஸ்டம் > பொது உள்ளமைவு > சேவையகம்)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "JavaScript உரிமையுடையவற்றை நீங்கள் தொகுக்கப்போவதில்லை எனில் [பேலரைப்](https://github.com/magento/baler) பயன்படுத்துவதைப் பரிசீலிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Magentoவின் உள்ளமைக்கப்பட்ட பதிப்பான [JavaScript தொகுத்தலையும் சிறிதாக்குதலையும் முடக்கி](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) [பேலரைப்](https://github.com/magento/baler/) பயன்படுத்துவதைப் பரிசீலிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[பிரத்தியேகமான எழுத்துருவை வரையறுக்கும்போது](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) `@font-display`ஐக் குறிப்பிடவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "பல்வேறு மூன்றாம் தரப்பு நீட்டிப்புகள் புதிய பட ஃபார்மேட்டுகளை அளவிட [Magento சந்தையைத்](https://marketplace.magento.com/catalogsearch/result/?q=webp) தேடுவதைப் பரிசீலிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "இணைய பிளாட்ஃபார்மின் [படங்களை மெதுவாக ஏற்றும்](https://web.dev/native-lazy-loading) அம்சத்தைப் பயன்படுத்த உங்கள் தயாரிப்பையும் கேட்டலாக் டெம்ப்ளேட்டுகளையும் மாற்றுவதைப் பரிசீலிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Magentoவின் [வார்னிஷ் ஒருங்கிணைப்பு](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) அம்சத்தைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "\"CSS ஃபைல்களைச் சிறிதாக்குதல்\" என்ற விருப்பத்தேர்வை உங்கள் ஸ்டோரின் டெவெலப்பர் அமைப்புகளில் இயக்கவும். [மேலும் அறிக](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "நிலையான உள்ளடக்க வழங்கலில் உள்ள JavaScript அசெட்டுகள் அனைத்தையும் சிறிதாக்க [Terser](https://www.npmjs.com/package/terser)ரைப் பயன்படுத்தி, உள்ளமைந்த சிறிதாக்குதல் அம்சத்தை முடக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Magentoவின் உள்ளமைக்கப்பட்ட பதிப்பான [JavaScript தொகுத்தலை](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) முடக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "பல்வேறு மூன்றாம் தரப்பு நீட்டிப்புகள் படங்களை மேம்படுத்த [Magento சந்தையைத்](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) தேடுவதைப் பரிசீலிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[தீம்களின் தளவமைப்பை மாற்றுவதன்](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) மூலம் முன்னிணைப்பு அல்லது dns ப்ரீஃபெட்ச் மூலக் குறிப்புகளைச் சேர்க்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "[தீம்களின் தளவமைப்பை மாற்றுவதன்](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) மூலம் `<link rel=preload>` குறியிடல்களைச் சேர்க்க முடியும்."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "பட வடிவமைப்பைத் தானாக மேம்படுத்த, `next/image` காம்பனென்ட்டிற்குப் பதிலாக `<img>` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "தேவையுள்ளபோது படங்கள் தானாகக் காட்டப்பட, `next/image` காம்பனென்ட்டிற்குப் பதிலாக `<img>` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "LCP படத்தை முன்கூட்டியே ஏற்ற, `next/image` காம்பனென்ட்டைப் பயன்படுத்துவதுடன் \"முன்னுரிமை\" என்பதை ‘சரி’ எனவும் அமைக்கவும். [மேலும் அறிக](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "முக்கியமில்லாத மூன்றாம் தரப்பு ஸ்கிரிப்ட்டுகள் ஏற்றப்படுவதைத் தாமதப்படுத்த, `next/script` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "படங்கள் எப்போதும் பொருத்தமான அளவில் காட்டப்படுவதை உறுதிப்படுத்திக்கொள்ள, `next/image` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "பயன்படுத்தப்படாத விதிகளை ஸ்டைல்ஷீட்டுகளில் இருந்து அகற்ற, `Next.js` உள்ளமைவில் `PurgeCSS` செருகுநிரலை அமைக்கவும். [மேலும் அறிக](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "பயன்படுத்தப்படாத JavaScript குறியீட்டைக் கண்டறிய, `Webpack Bundle Analyzer` ஐப் பயன்படுத்தவும். [மேலும் அறிக](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "உங்கள் ஆப்ஸின் நிகழ்நேரச் செயல்திறனை அளவிட, `Next.js Analytics` ஐப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "இம்மியூட்டபிள் அசெட்டுகள், `Server-side Rendered` (SSR) பக்கங்கள் ஆகியவற்றுக்கான தற்காலிகச் சேமிப்பை உள்ளமைக்கவும். [மேலும் அறிக](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "படத் தரத்தைச் சரிசெய்ய, `next/image` காம்பனென்ட்டிற்குப் பதிலாக `<img>` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "பொருத்தமான `sizes` ஐ அமைக்க, `next/image` காம்பனென்ட்டைப் பயன்படுத்தவும். [மேலும் அறிக](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "உங்கள் Next.js சேவையகத்தில் சுருக்குதலை இயக்கவும். [மேலும் அறிக](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "`nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி, `format=\"webp\"` வடிவமைப்பை அமைக்கவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "திரையில் காட்டப்படாத படங்களைத் தேவையுள்ளபோது ஏற்ற, `nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி `loading=\"lazy\"` என அமைக்கவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "LCP படத்தை ஏற்ற, `nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி `preload` எனக் குறிப்பிடவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "`nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி `width`, `height` ஆகியவற்றைத் துல்லியமாகக் குறிப்பிடவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "`nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி, பொருத்தமான `quality` ஐ அமைக்கவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "`nuxt/image` காம்பனென்ட்டைப் பயன்படுத்தி, பொருத்தமான `sizes` ஐ அமைக்கவும். [மேலும் அறிக](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "இணையப் பக்கம் விரைவாக ஏற்றப்படுவதற்கு, [வீடியோவிற்குப் பதிலாக அனிமேஷன் செய்யப்பட்ட GIFகளைப் பயன்படுத்தவும்](https://web.dev/replace-gifs-with-videos/). மேலும் சுருக்குதல் செயல்திறனைத் தற்போதைய அதிநவீன VP9 வீடியோ கோடெக்கை விட 30% கூடுதலாக மேம்படுத்த [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos), [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) போன்ற நவீன ஃபைல் வடிவமைப்புகளைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "பதிவேற்றிய படங்களை மேம்படுத்தப்பட்ட வடிவமைப்புகளுக்குத் தானாக மாற்றும் [செருகுநிரல்](https://octobercms.com/plugins?search=image)/சேவையைப் பயன்படுத்தவும். SSIM தரக் குறியீட்டின்படி [தரம் குறையாத WebP படங்கள்](https://developers.google.com/speed/webp) PNGகளுடன் ஒப்பிடும்போது 26% சிறியதாகவும் ஒப்பிடக்கூடிய JPEG படங்களைக் காட்டிலும் 25-34% சிறியதாகவும் இருக்கும். [AVIF](https://jakearchibald.com/2020/avif-has-landed/) என்ற மற்றொரு புதிய பட வடிவமைப்பைப் பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "திரைக்கு வெளியில் உள்ள படங்களை மெதுவாக ஏற்றும் வசதியை வழங்கும் [படத்தைத் தேவையுள்ளபோது ஏற்றும் செருகுநிரலை](https://octobercms.com/plugins?search=lazy) நிறுவவும் அல்லது அந்த வசதியை வழங்கும் தீமிற்கு மாறவும். [AMP செருகுநிரலையும்](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "[முக்கியமான இன்லைன் அசெட்டுகளுக்கு](https://octobercms.com/plugins?search=css) உதவும் பல செருகுநிரல்கள் உள்ளன. இந்தச் செருகுநிரல்கள் பிற செருகுநிரல்களின் செயல்பாட்டைப் பாதிக்கக்கூடும் என்பதால் அவற்றை முழுமையாகச் சோதனை செய்ய வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "தீம்கள், செருகுநிரல்கள், சேவையக விவரக்குறிப்புகள் ஆகிய அனைத்தும் சேவையகத்தின் வேகத்தை நிர்ணயிக்கும். மேலும் மேம்படுத்தப்பட்ட தீமினைக் கண்டறிந்து, மேம்படுத்தல் நீட்டிப்பைக் கவனமாகத் தேர்ந்தெடுக்கவும் மற்றும்/அல்லது சேவையகத்தை மேம்படுத்தவும். மின்னஞ்சல் அனுப்புவது போன்ற அதிக நேரம் எடுத்துக்கொள்ளும் பணியின் செயலாக்கத்தைத் தாமதப்படுத்த [`Queues`](https://octobercms.com/docs/services/queues) ஐ டெவெலப்பர்கள் பயன்படுத்துவதற்கு October CMS அனுமதிக்கிறது. இது இணைய கோரிக்கைகளை நன்கு வேகப்படுத்துகிறது."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "இடுகைப் பட்டியல்களில் முக்கியமான பகுதியை மட்டும் காட்டவும் (உதாரணமாக `show more` பட்டனைப் பயன்படுத்துவதன் மூலம்), இணையப் பக்கத்தில் இடுகைகளின் எண்ணிக்கையைக் குறைக்கவும், பெரிய இடுகைகளை ஒன்றுக்கும் மேற்பட்ட இணையப் பக்கங்களாகப் பிரிக்கவும் அல்லது செருகுநிரல் மூலம் தேவையுள்ளபோது மட்டும் கருத்துகளை ஏற்றவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "ஸ்டைல்களை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் தளத்தின் செயல்பாட்டை வேகப்படுத்தும் பல [செருகுநிரல்கள்](https://octobercms.com/plugins?search=css) உள்ளன. இந்தச் சிறிதாக்குதலை முன்னதாகவே மேற்கொள்ள பதிப்பு முறைமையைப் பயன்படுத்துவது டெவெலப்மெண்ட்டை வேகப்படுத்தும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "ஸ்கிரிப்ட்டுகளை ஒன்றிணைத்தும் சிறிதாக்கியும் சுருக்கியும் தளத்தின் செயல்பாட்டை வேகப்படுத்தும் பல [செருகுநிரல்கள்](https://octobercms.com/plugins?search=javascript) உள்ளன. இந்தச் சிறிதாக்குதலை முன்னதாகவே மேற்கொள்ள பதிப்பு முறைமையைப் பயன்படுத்துவது டெவெலப்மெண்ட்டை வேகப்படுத்தும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "இணையதளத்தில் பயன்படுத்தப்படாத CSSஸை ஏற்றும் [செருகுநிரல்களைச்](https://octobercms.com/plugins) சரிபார்க்கவும். தேவையற்ற CSSஸைச் சேர்க்கும் செருகுநிரல்களைக் கண்டறிய, Chrome டெவெலப்பர் கருவிகளில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கவும். ஸ்டைல்ஷீட்டின் URLலில் இதற்குக் காரணமான தீம்/செருகுநிரலைக் கண்டறியவும். குறியீட்டுக் கவரேஜில் அதிக சிவப்பைக் கொண்ட பல ஸ்கிரிப்ட்டுகளுடன் கூடிய ஸ்டைல்ஷீட்டுகளைக் கண்டறியவும். இணையப் பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு செருகுநிரல் ஒரு ஸ்டைல்ஷீட்டை மட்டுமே சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "இணையப் பக்கத்தில் ‘பயன்படுத்தப்படாத JavaScriptடை’ ஏற்றும் [செருகுநிரல்களைச்](https://octobercms.com/plugins?search=javascript) சரிபார்க்கவும். தேவையற்ற JavaScriptடைச் சேர்க்கும் செருகுநிரல்களைக் கண்டறிய, Chrome டெவெலப்பர் கருவிகளில் [குறியீட்டுக் கவரேஜை](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) இயக்கவும். ஸ்கிரிப்ட்டின் URLலில் இதற்குக் காரணமான தீம்/செருகுநிரலைக் கண்டறியவும். குறியீட்டுக் கவரேஜில் அதிக சிவப்பைக் கொண்ட பல ஸ்கிரிப்ட்டுகளுடன் கூடிய செருகுநிரல்களைக் கண்டறியவும். இணையப் பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு செருகுநிரல் ஒரு ஸ்கிரிப்ட்டை மட்டுமே சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[HTTP தற்காலிகச் சேமிப்பின் மூலம் தேவையற்ற நெட்வொர்க் கோரிக்கைகளைத் தடுப்பது](https://web.dev/http-cache/#caching-checklist) குறித்துத் தெரிந்துகொள்ளவும். தற்காலிகமாகச் சேமிப்பதை வேகப்படுத்துவதற்காகப் பயன்படுத்தப்படக்கூடிய பல [செருகுநிரல்கள்](https://octobercms.com/plugins?search=Caching) உள்ளன."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "படங்களின் தரம் குறையாமல் அவற்றைச் சுருக்க, [படத்தை மேம்படுத்தும் செருகுநிரலைப்](https://octobercms.com/plugins?search=image) பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "மீடியா நிர்வாகியில் படங்களை நேரடியாகப் பதிவேற்றி சரியான அளவில் கிடைக்கிறதா என உறுதிப்படுத்திக் கொள்ளலாம். சரியான பட அளவுகள் பயன்படுத்தப்பட்டிருப்பதை உறுதிசெய்துகொள்ள, [அளவை மாற்றும் வடிப்பானையோ](https://octobercms.com/docs/markup/filter-resize) [படத்தின் அளவை மாற்றும் செருகுநிரலையோ](https://octobercms.com/plugins?search=image) பயன்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "இணையச் சேவையக உள்ளமைவில் உரைச் சுருக்குதலை இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "ஒரு பக்கத்தில் தொடர்ந்து நிகழ்கின்ற பல கூறுகளை ரெண்டரிங் செய்கிறீர்கள் எனில் DOM கணுக்கள் உருவாக்கப்படும் எண்ணிக்கையைக் குறைக்கும் வகையில் `react-window` போன்ற “விண்டோவிங்” லைப்ரரியைப் பயன்படுத்தவும். [மேலும் அறிக](https://web.dev/virtualize-long-lists-react-window/). இயங்கும் நேரச் செயல்திறனை மேம்படுத்த `Effect` ஹுக்கைப் பயன்படுத்துகிறீர்கள் எனில் குறிப்பிட்ட சார்புநிலைகள் மாறும் வரை மட்டும் [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent), [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) போன்றவற்றைப் பயன்படுத்தித் தேவையற்ற மறு ரெண்டரிங்குகளைக் குறைத்துக்கொள்ளவும் [விளைவுகளைத் தவிர்க்கவும்](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "நீங்கள் ரியாக்ட் ரூட்டரைப் பயன்படுத்தினால் [ரூட் வழிசெலுத்தல்களுக்கு](https://reacttraining.com/react-router/web/api/Redirect) `<Redirect>` உறுப்பைப் பயன்படுத்துவதைக் குறைத்துக்கொள்ளவும்."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "நீங்கள் சேவையகம் தரப்பாக இருந்து ரியாக்ட் கூறுகளை ரெண்டரிங் செய்பவராக இருந்தால் அனைத்தையும் ஒரே நேரத்தில் அல்லாமல் மார்க்-அப்பின் வெவ்வேறு பகுதிகளைப் பெறவும் ஹைட்ரேட் செய்யவும் கிளையண்ட்டை அனுமதிப்பதற்கு `renderToPipeableStream()` அல்லது `renderToStaticNodeStream()`ஐப் பயன்படுத்துவதைப் பரிசீலிக்கவும். [மேலும் அறிக](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "உங்கள் பதிப்பு சிஸ்டம் உங்கள் CSS ஃபைல்களைத் தானாகவே சிறிதாக்கினால் உங்கள் ஆப்ஸின் தயாரிப்புப் பதிப்பைப் பயன்படுத்துவதை உறுதிசெய்துகொள்ளவும். React Developer Tools நீட்டிப்பில் இதைப் பார்க்கலாம். [மேலும் அறிக](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "உங்கள் பதிப்பு சிஸ்டம் உங்கள் JS ஃபைல்களைத் தானாகவே சிறிதாக்கினால் உங்கள் ஆப்ஸின் தயாரிப்புப் பதிப்பைப் பயன்படுத்துவதை உறுதிசெய்துகொள்ளவும். React Developer Tools நீட்டிப்பில் இதைப் பார்க்கலாம். [மேலும் அறிக](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "நீங்கள் சேவையகத் தரப்பு ரெண்டரிங் செய்யவில்லை என்றால் `React.lazy()` மூலம் [JavaScript தொகுப்புகளை பிரிக்கவும்](https://web.dev/code-splitting-suspense/). இல்லையென்றால் [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/) போன்ற மூன்றாம் தரப்பு லைப்ரரி மூலம் குறியீட்டைப் பிரிக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "உங்கள் கூறுகளின் ரெண்டரிங் செயல்திறனை அளவிட Profiler APIயைப் பயன்படுத்தும் React DevTools Profilerரைப் பயன்படுத்தவும். [மேலும் அறிக.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "GIFஐ HTML5 வீடியோவாக உட்பொதிக்க உதவும் சேவையில் அதைப் பதிவேற்றவும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "பொருந்தக்கூடிய இடங்களில், நீங்கள் பதிவேற்றிய JPEG படங்களைத் தானாக WebP வடிவத்திற்கு மாற்ற [Performance Lab](https://wordpress.org/plugins/performance-lab/) செருகுநிரலைப் பயன்படுத்திப் பாருங்கள்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "[lazy-load WordPress செருகுநிரலை](https://wordpress.org/plugins/search/lazy+load/) நிறுவி திரைக்கு வெளியிலுள்ள படங்களைத் தேவையான போது ஏற்றும் திறனைப் பெறலாம் அல்லது அந்தத் திறன் கொண்ட தீமிற்கு மாறலாம். [AMP செருகுநிரலையும்](https://wordpress.org/plugins/amp/) பயன்படுத்திப் பார்க்கவும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "[முக்கியமான உடைமைகளை முன்னிலைப்படுத்தவோ](https://wordpress.org/plugins/search/critical+css/) [முக்கியத்துவம் குறைவான ஆதாரங்களைத் தவிர்க்கவோ](https://wordpress.org/plugins/search/defer+css+javascript/) உங்களுக்கு உதவக்கூடிய பல WordPress செருகுநிரல்கள் உள்ளன. இந்த செருகுநிரல்கள் வழங்கும் மேம்படுத்துதல்கள் உங்கள் தீமிலோ செருகுநிரலிலோ உள்ள அம்சத்தைப் பாதிக்கலாம். அதனால் நீங்கள் குறியீட்டில் சில மாற்றங்களை செய்ய வேண்டியிருக்கும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "தீம்கள், செருகுநிரல்கள், சேவையக விவரக்குறிப்புகள் அனைத்தும் சேவையகத்தின் வேகத்தை நிர்ணயிக்கும். மேலும் மேம்படுத்தப்பட்ட தீமைக் கண்டறிந்து, மேம்படுத்தும் செருகுநிரலைக் கவனமாகத் தேர்ந்தெடுத்து மற்றும்/அல்லது சேவையகத்தை மேம்படுத்தவும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "இடுகைப் பட்டியல்களில் முக்கியமான பகுதியை மட்டும் காட்டலாம் (உதாரணமாக மேலும் என்ற குறிச்சொல்லுடன்), பக்கத்தில் இடுகைகளின் எண்ணிக்கையைக் குறைக்கலாம், ஒரு பெரிய இடுகையைப் பல சின்ன பக்கங்களாகப் பிரிக்கலாம் அல்லது தேவையுள்ள போது மட்டும் கருத்துகளைச் செருகுநிரல்கள் மூலம் ஏற்றலாம்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "பல [WordPress செருகுநிரல்கள்](https://wordpress.org/plugins/search/minify+css/) உங்கள் இணையதளத்தில் உள்ள ஸ்டைல்களை சிறிதாக்கியும், சுருக்கியும், ஒன்றிணைத்தும் அதை வேகப்படுத்த முடியும். முடிந்தால், ஸ்கிரிப்ட்களை முன்னதாகவே சிறிதாக்க பதிப்பு முறைமையைப் பயன்படுத்திப் பார்க்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "பல [WordPress செருகுநிரல்கள்](https://wordpress.org/plugins/search/minify+javascript/) உங்கள் இணையதளத்திலுள்ள ஸ்கிரிப்ட்களை சிறிதாக்கியும், சுருக்கியும், ஒன்றிணைத்தும் அதை வேகப்படுத்த முடியும். முடிந்தால், ஸ்கிரிப்ட்களை முன்னதாகவே சிறிதாக்க பதிப்பு முறைமையைப் பயன்படுத்திப் பார்க்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "உங்கள் பக்கத்தில் [WordPress செருகுநிரல்கள்](https://wordpress.org/plugins/) ஏற்றும் பயன்படுத்தப்படாத CSSஸின் எண்ணிக்கையைக் குறைக்கவும் அல்லது மாற்றிப் பார்க்கவும். Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜ் ](https://developer.chrome.com/docs/devtools/coverage/) என்பதை இயக்கி தேவையற்ற CSSஸை சேர்க்கும் செருகுநிரல்களைக் கண்டறியவும். ஸ்டைல்ஷீட்டின் URLலில் இதற்குக் காரணமான தீம்/செருகுநிரலைக் கண்டறியலாம். அதிகளவு சிவப்பில் உள்ள குறியீட்டுக் கவரேஜைக் கொண்ட பட்டியலில் பல ஸ்டைல்ஷீட்களைக் கொண்ட செருகுநிரல்களைக் கண்டறியவும். இணையப் பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு செருகுநிரல் ஒரு ஸ்டைல்ஷீட்டை மட்டுமே வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "உங்கள் பக்கத்தில் [WordPress செருகுநிரல்கள்](https://wordpress.org/plugins/) ஏற்றும் பயன்படுத்தப்படாத JavaScriptகளின் எண்ணிக்கையைக் குறைக்கவும் அல்லது மாற்றிப் பார்க்கவும். Chrome DevToolsஸில் [குறியீட்டுக் கவரேஜ் ](https://developer.chrome.com/docs/devtools/coverage/) என்பதை இயக்கி தேவையற்ற JSகளைச் சேர்க்கும் செருகுநிரல்களைக் கண்டறியவும். ஸ்கிரிப்ட்டின் URLலில் இதற்குக் காரணமான தீம்/செருகுநிரலைக் கண்டறியலாம். அதிகளவு சிவப்பில் உள்ள குறியீட்டுக் கவரேஜைக் கொண்ட பட்டியலில் பல ஸ்கிரிப்ட்களைக் கொண்ட செருகுநிரல்களைக் கண்டறியவும். இணையப் பக்கத்தில் பயன்படுத்தும் பட்சத்தில் ஒரு செருகுநிரல் ஒரு ஸ்கிரிப்ட்டை மட்டுமே வரிசையில் சேர்க்க வேண்டும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[WordPressஸில் உலாவியின் தற்காலிக சேமிப்பு](https://wordpress.org/support/article/optimization/#browser-caching) பற்றித் தெரிந்துகொள்ளவும்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "[படங்களை மேம்படுத்தும் WordPress செருகுநிரலைப்](https://wordpress.org/plugins/search/optimize+images/) பயன்படுத்தி உங்கள் படங்களின் தரத்திற்குப் பாதிப்பு ஏற்படாமல் சுருக்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "[மீடியா லைப்ரரி](https://wordpress.org/support/article/media-library-screen/) மூலம் படத்தை நேரடியாகப் பதிவேற்றி சரியான அளவில் கிடைக்கிறதா என்று உறுதிப்படுத்திக் கொள்ளலாம். பிறகு மேம்படுத்தப்பட்ட அளவுகளில் (சிறப்பாகப் பதிலளிக்கும் புள்ளிகளுக்கும் ஏற்ற அளவுகளில்) படங்கள் பயன்படுத்தப்படுவதை உறுதி செய்ய மீடியா லைப்ரரியிலிருந்தோ பட விட்ஜெட்டைப் பயன்படுத்தியோ படங்களைச் செருகலாம். பயன்படுத்த ஏதுவான அளவுகள் உள்ளபோது மட்டுமே `Full Size` படங்களைப் பயன்படுத்தவும். [மேலும் அறிக](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "இணையச் சேவையக உள்ளமைவில் உரை சுருக்குதலை இயக்கலாம்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "உங்கள் படங்களை WebP வடிவமைப்பிற்கு மாற்ற 'WP Rocketடில்' படத்தை மேம்படுத்துதல் பக்கத்தில் உள்ள ‘Imagify’ செருகுநிரலை இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "இந்தப் பரிந்துரையைச் செயல்படுத்த, [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) அம்சத்தை 'WP Rocketடில்' இயக்கவும். பக்கத்தைப் பார்ப்பவர் கீழே சென்று பார்க்கும் வரை, படங்கள் ஏற்றப்படுவதை இந்த அம்சம் தாமதப்படுத்தும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "இந்தப் பரிந்துரையைச் செயல்படுத்த, [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css), [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) ஆகிய அம்சங்களை 'WP Rocketடில்' இயக்கவும். இந்த அம்சங்கள் முறையே CSS, JavaScript ஃபைல்களை மேம்படுத்துவதால் உங்கள் பக்கம் ரென்டரிங் செய்யப்படுவதை அவை தடுக்காது."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "இந்தச் சிக்கலைச் சரிசெய்ய, [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) அம்சத்தை 'WP Rocketடில்' இயக்கவும். உங்கள் தளத்தின் CSS ஃபைல்களின் அளவைக் குறைத்து அவற்றை வேகமாகப் பதிவிறக்க, அவற்றில் உள்ள வெற்றிடங்களும் கருத்துகளும் அகற்றப்படும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "இந்தச் சிக்கலைச் சரிசெய்ய, [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) அம்சத்தை 'WP Rocketடில்' இயக்கவும். JavaScript ஃபைல்களின் அளவைக் குறைத்து அவற்றை வேகமாகப் பதிவிறக்க, அவற்றில் உள்ள வெற்றிடங்களும் கருத்துகளும் அகற்றப்படும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "இந்தச் சிக்கலைச் சரிசெய்ய, [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) அம்சத்தை 'WP Rocketடில்' இயக்கவும். ஒவ்வொரு பக்கத்திற்கும் பயன்படுத்தப்படும் CSSஸை மட்டும் வைத்துக்கொண்டு, பயன்படுத்தப்படாத அனைத்து CSSகளையும் ஸ்டைல்ஷீட்டுகளையும் அகற்றுவதன் மூலம் பக்கத்தின் அளவை இது குறைக்கும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "இந்தச் சிக்கலைச் சரிசெய்ய, [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) அம்சத்தை 'WP Rocketடில்' இயக்கவும். பயனர் எதுவும் செய்யாத வரை ஸ்கிரிப்ட்டுகள் செயல்படுத்தப்படுவதைத் தாமதப்படுத்துவதன் மூலம் பக்கத்தை விரைவாகக் காட்டும். உங்கள் தளத்தில் iframeகள் இருந்தால் WP Rocketடின் [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos), [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) ஆகிய அம்சங்களைப் பயன்படுத்தலாம்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "உங்கள் படங்களைச் சுருக்குவதற்கு ‘மொத்தமாக மேம்படுத்துதலைச்’ செயல்படுத்த, 'WP Rocketடில்' படத்தை மேம்படுத்துதல் பக்கத்தில் உள்ள ‘Imagify’ செருகுநிரலை இயக்கவும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "\"dns-prefetch\" பண்புக்கூற்றைச் சேர்த்து வெளிப்புற டொமைன்களுடனான இணைப்பை விரைவுபடுத்த 'WP Rocketடில்' [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) அம்சத்தைப் பயன்படுத்தவும். அத்துடன் [Google Fonts டொமைனிலும்](https://docs.wp-rocket.me/article/1312-optimize-google-fonts), [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) அம்சத்தின் மூலம் சேர்க்கப்பட்ட எந்தவொரு CNAME அமைப்பிலும் “preconnect” பண்புக்கூற்றை 'WP Rocket' தானாகவே சேர்க்கும்."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "எழுத்து வடிவங்களில் இந்தச் சிக்கலைச் சரிசெய்ய, [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) அம்சத்தை 'WP Rocketடில்' இயக்கவும். உங்கள் தளத்தின் முக்கியமான எழுத்து வடிவங்களுக்கு முன்னுரிமை அளிக்கப்பட்டு அவை முன்னரே ஏற்றப்படும்."}, "report/renderer/report-utils.js | calculatorLink": {"message": "கால்குலேட்டரைக் காட்டு."}, "report/renderer/report-utils.js | collapseView": {"message": "சுருக்கு"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "துவக்க நெட்வொர்க் கோரிக்கை"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "முக்கியக் கோரிக்கைத் தடத்தின் அதிகபட்சத் தாமதம்:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSONனை நகலெடு"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "டார்க் தீம் நிலைமாற்று"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "விரிக்கப்பட்ட பிரிண்ட்"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "பிரிண்ட் சுருக்கம்"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Gistடாக சேமி"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "HTMLலாக சேமி"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "JSONனாக சேமி"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "வியூவரில் திற"}, "report/renderer/report-utils.js | errorLabel": {"message": "பிழை!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "அறிக்கைப் பிழை: தணிக்கைத் தகவல் இல்லை"}, "report/renderer/report-utils.js | expandView": {"message": "விரிவாக்கு"}, "report/renderer/report-utils.js | footerIssue": {"message": "சிக்கலைப் புகார் செய்க"}, "report/renderer/report-utils.js | hide": {"message": "மறை"}, "report/renderer/report-utils.js | labDataTitle": {"message": "ஆய்வகத் தரவு"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "மாதிரியாக உருவாக்கப்பட்ட மொபைல் நெட்வொர்க்கில் தற்போதைய பக்கத்திற்கான [Lighthouse](https://developers.google.com/web/tools/lighthouse/) பகுப்பாய்வு. மதிப்புகள் தோராயமானவை, மாறுபடக்கூடியவை."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "கைமுறையாகச் சரிபார்க்க வேண்டிய கூடுதல் விஷயங்கள்"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "பொருந்தாதது"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "பரிந்துரை"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "தோராயமான சேமிப்பு"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "தேர்ச்சி பெற்ற தணிக்கைகள்"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "பக்கத்தின் முதற்கட்ட ஏற்றம்"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "பிரத்தியேகமான த்ராட்லிங்"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "மாதிரி இயக்கப்பட்ட டெஸ்க்டாப்"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "மாதிரி இயக்கப்படவில்லை"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe பதிப்பு"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "த்ராட்லிங் செய்யப்படாத CPU/நினைவகத் திறன்"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU த்ராட்லிங்"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "சாதனம்"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "நெட்வொர்க் த்ராட்லிங்"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "திரையின் மாதிரி இயக்கம்"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "பயனர் ஏஜெண்ட் (நெட்வொர்க்)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "ஒற்றைப் பக்க ஏற்றம்"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "ஒன்றுக்கும் மேற்பட்ட அமர்வுகளைப் பற்றிய சுருக்கவிவரத்தை வழங்கும் புலத் தரவுக்குப் பதிலாக ஒற்றைப் பக்கம் ஏற்றப்படும் செயலாக்கத்தின் மூலம் இந்தத் தரவு பெறப்பட்டது."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "வேகம் குறைவான 4G த்ராட்லிங்"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "தெரியவில்லை"}, "report/renderer/report-utils.js | show": {"message": "காட்டு"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "இதற்குத் தொடர்புடைய தணிக்கைகளைக் காட்டு:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "துணுக்கைச் சுருக்கு"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "துணுக்கை விரி"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "மூன்றாம் தரப்பு ஆதாரங்களைக் காட்டு"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "இயக்கச் சூழல் வழங்கியது"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Lighthouseஸின் இந்த இயக்கத்தைச் சில சிக்கல்கள் பாதிக்கின்றன:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "மதிப்புகள் தோராயமானவை, மாறுபடக்கூடியவை. இந்த அளவீடுகளிலிருந்து நேரடியாக [செயல்திறன் ஸ்கோர் கணக்கிடப்படும்](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "அசல் டிரேஸைக் காட்டு"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "டிரேஸைக் காட்டு"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "ட்ரீமேப்பைக் காட்டு"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "தணிக்கைகளில் தேர்ச்சிபெற்றவை, ஆனால் எச்சரிக்கைகள் உள்ளவை"}, "report/renderer/report-utils.js | warningHeader": {"message": "எச்சரிக்கைகள்: "}, "treemap/app/src/util.js | allLabel": {"message": "எல்லாம்"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "எல்லா ஸ்கிரிப்ட்டுகளும்"}, "treemap/app/src/util.js | coverageColumnName": {"message": "கவரேஜ்"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "நகல் மாடியூல்கள்"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "ஃபைலின் அளவு (பைட்டுகள்)"}, "treemap/app/src/util.js | tableColumnName": {"message": "பெயர்"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "டேபிளைக் காட்டு/மறை"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "பயன்படுத்தப்படாத பைட்டுகள்"}}