"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.lineType = exports.lineTypes = void 0;
/**
 * Collection of the various types of lines encountered in a TAP stream
 */
exports.lineTypes = {
    testPoint: /^(not )?ok(?: ([0-9]+))?(?:(?: -)?( .*?))?(\{?)\n$/,
    pragma: /^pragma ([+-])([a-zA-Z0-9_-]+)\n$/,
    bailout: /^bail out!(.*)\n$/i,
    version: /^TAP version ([0-9]+)\n$/i,
    childVersion: /^(    )+TAP version ([0-9]+)\n$/i,
    plan: /^([0-9]+)\.\.([0-9]+)(?:\s+(?:#\s*(.*)))?\n$/,
    subtest: /^# Subtest(?:: (.*))?\n$/,
    subtestIndent: /^    # Subtest(?:: (.*))?\n$/,
    comment: /^\s*#.*\n$/,
};
/**
 * Determine the type of line, and parse it into a {@link ParsedLine}
 */
const lineType = (line) => {
    for (const [t, pattern] of Object.entries(exports.lineTypes)) {
        const match = line.match(pattern);
        if (match)
            return [t, match];
    }
    return null;
};
exports.lineType = lineType;
//# sourceMappingURL=line-type.js.map