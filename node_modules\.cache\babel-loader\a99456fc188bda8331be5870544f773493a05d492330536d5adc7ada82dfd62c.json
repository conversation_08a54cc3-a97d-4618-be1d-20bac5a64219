{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport isRequiredIf from 'react-proptype-conditional-require';\nimport Popper from 'popper.js';\nimport deepmerge from 'deepmerge';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\nimport ReactDOM from 'react-dom';\nimport ExecutionEnvironment from 'exenv';\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nvar DEFAULTS = {\n  flip: {\n    padding: 20\n  },\n  preventOverflow: {\n    padding: 10\n  }\n};\nvar STATUS = {\n  INIT: 'init',\n  IDLE: 'idle',\n  OPENING: 'opening',\n  OPEN: 'open',\n  CLOSING: 'closing',\n  ERROR: 'error'\n};\nvar canUseDOM = ExecutionEnvironment.canUseDOM;\nvar isReact16 = ReactDOM.createPortal !== undefined;\nfunction isMobile() {\n  return 'ontouchstart' in window && /Mobi/.test(navigator.userAgent);\n} /**\n  * Log method calls if debug is enabled\n  *\n  * @private\n  * @param {Object}       arg\n  * @param {string}       arg.title    - The title the logger was called from\n  * @param {Object|Array} [arg.data]   - The data to be logged\n  * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n  * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n  */\nfunction log(_ref) {\n  var title = _ref.title,\n    data = _ref.data,\n    _ref$warn = _ref.warn,\n    warn = _ref$warn === void 0 ? false : _ref$warn,\n    _ref$debug = _ref.debug,\n    debug = _ref$debug === void 0 ? false : _ref$debug; /* eslint-disable no-console */\n  var logFn = warn ? console.warn || console.error : console.log;\n  if (debug && title && data) {\n    console.groupCollapsed(\"%creact-floater: \".concat(title), 'color: #9b00ff; font-weight: bold; font-size: 12px;');\n    if (Array.isArray(data)) {\n      data.forEach(function (d) {\n        if (is.plainObject(d) && d.key) {\n          logFn.apply(console, [d.key, d.value]);\n        } else {\n          logFn.apply(console, [d]);\n        }\n      });\n    } else {\n      logFn.apply(console, [data]);\n    }\n    console.groupEnd();\n  } /* eslint-enable */\n}\nfunction on(element, event, cb) {\n  var capture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  element.addEventListener(event, cb, capture);\n}\nfunction off(element, event, cb) {\n  var capture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  element.removeEventListener(event, cb, capture);\n}\nfunction once(element, event, cb) {\n  var capture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var _nextCB; // eslint-disable-next-line prefer-const\n  _nextCB = function nextCB(e) {\n    cb(e);\n    off(element, event, _nextCB);\n  };\n  on(element, event, _nextCB, capture);\n}\nfunction noop() {}\nvar ReactFloaterPortal = /*#__PURE__*/function (_React$Component) {\n  _inherits(ReactFloaterPortal, _React$Component);\n  var _super = _createSuper(ReactFloaterPortal);\n  function ReactFloaterPortal(props) {\n    var _this;\n    _classCallCheck(this, ReactFloaterPortal);\n    _this = _super.call(this, props);\n    if (!canUseDOM) return _possibleConstructorReturn(_this);\n    _this.node = document.createElement('div');\n    if (props.id) {\n      _this.node.id = props.id;\n    }\n    if (props.zIndex) {\n      _this.node.style.zIndex = props.zIndex;\n    }\n    document.body.appendChild(_this.node);\n    return _this;\n  }\n  _createClass(ReactFloaterPortal, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n      if (!isReact16) {\n        this.renderPortal();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!canUseDOM) return;\n      if (!isReact16) {\n        this.renderPortal();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (!canUseDOM || !this.node) return;\n      if (!isReact16) {\n        ReactDOM.unmountComponentAtNode(this.node);\n      }\n      document.body.removeChild(this.node);\n    }\n  }, {\n    key: \"renderPortal\",\n    value: function renderPortal() {\n      if (!canUseDOM) return null;\n      var _this$props = this.props,\n        children = _this$props.children,\n        setRef = _this$props.setRef; /* istanbul ignore else */\n      if (isReact16) {\n        return /*#__PURE__*/ReactDOM.createPortal(children, this.node);\n      }\n      var portal = ReactDOM.unstable_renderSubtreeIntoContainer(this, children.length > 1 ? /*#__PURE__*/React.createElement(\"div\", null, children) : children[0], this.node);\n      setRef(portal);\n      return null;\n    }\n  }, {\n    key: \"renderReact16\",\n    value: function renderReact16() {\n      var _this$props2 = this.props,\n        hasChildren = _this$props2.hasChildren,\n        placement = _this$props2.placement,\n        target = _this$props2.target;\n      if (!hasChildren) {\n        if (target || placement === 'center') {\n          return this.renderPortal();\n        }\n        return null;\n      }\n      return this.renderPortal();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!isReact16) {\n        return null;\n      }\n      return this.renderReact16();\n    }\n  }]);\n  return ReactFloaterPortal;\n}(React.Component);\n_defineProperty(ReactFloaterPortal, \"propTypes\", {\n  children: PropTypes.oneOfType([PropTypes.element, PropTypes.array]),\n  hasChildren: PropTypes.bool,\n  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  placement: PropTypes.string,\n  setRef: PropTypes.func.isRequired,\n  target: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  zIndex: PropTypes.number\n});\nvar FloaterArrow = /*#__PURE__*/function (_React$Component) {\n  _inherits(FloaterArrow, _React$Component);\n  var _super = _createSuper(FloaterArrow);\n  function FloaterArrow() {\n    _classCallCheck(this, FloaterArrow);\n    return _super.apply(this, arguments);\n  }\n  _createClass(FloaterArrow, [{\n    key: \"parentStyle\",\n    get: function get() {\n      var _this$props = this.props,\n        placement = _this$props.placement,\n        styles = _this$props.styles;\n      var length = styles.arrow.length;\n      var arrow = {\n        pointerEvents: 'none',\n        position: 'absolute',\n        width: '100%'\n      }; /* istanbul ignore else */\n      if (placement.startsWith('top')) {\n        arrow.bottom = 0;\n        arrow.left = 0;\n        arrow.right = 0;\n        arrow.height = length;\n      } else if (placement.startsWith('bottom')) {\n        arrow.left = 0;\n        arrow.right = 0;\n        arrow.top = 0;\n        arrow.height = length;\n      } else if (placement.startsWith('left')) {\n        arrow.right = 0;\n        arrow.top = 0;\n        arrow.bottom = 0;\n      } else if (placement.startsWith('right')) {\n        arrow.left = 0;\n        arrow.top = 0;\n      }\n      return arrow;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        placement = _this$props2.placement,\n        setArrowRef = _this$props2.setArrowRef,\n        styles = _this$props2.styles;\n      var _styles$arrow = styles.arrow,\n        color = _styles$arrow.color,\n        display = _styles$arrow.display,\n        length = _styles$arrow.length,\n        margin = _styles$arrow.margin,\n        position = _styles$arrow.position,\n        spread = _styles$arrow.spread;\n      var arrowStyles = {\n        display: display,\n        position: position\n      };\n      var points;\n      var x = spread;\n      var y = length; /* istanbul ignore else */\n      if (placement.startsWith('top')) {\n        points = \"0,0 \".concat(x / 2, \",\").concat(y, \" \").concat(x, \",0\");\n        arrowStyles.bottom = 0;\n        arrowStyles.marginLeft = margin;\n        arrowStyles.marginRight = margin;\n      } else if (placement.startsWith('bottom')) {\n        points = \"\".concat(x, \",\").concat(y, \" \").concat(x / 2, \",0 0,\").concat(y);\n        arrowStyles.top = 0;\n        arrowStyles.marginLeft = margin;\n        arrowStyles.marginRight = margin;\n      } else if (placement.startsWith('left')) {\n        y = spread;\n        x = length;\n        points = \"0,0 \".concat(x, \",\").concat(y / 2, \" 0,\").concat(y);\n        arrowStyles.right = 0;\n        arrowStyles.marginTop = margin;\n        arrowStyles.marginBottom = margin;\n      } else if (placement.startsWith('right')) {\n        y = spread;\n        x = length;\n        points = \"\".concat(x, \",\").concat(y, \" \").concat(x, \",0 0,\").concat(y / 2);\n        arrowStyles.left = 0;\n        arrowStyles.marginTop = margin;\n        arrowStyles.marginBottom = margin;\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"__floater__arrow\",\n        style: this.parentStyle\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        ref: setArrowRef,\n        style: arrowStyles\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        width: x,\n        height: y,\n        version: \"1.1\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"polygon\", {\n        points: points,\n        fill: color\n      }))));\n    }\n  }]);\n  return FloaterArrow;\n}(React.Component);\n_defineProperty(FloaterArrow, \"propTypes\", {\n  placement: PropTypes.string.isRequired,\n  setArrowRef: PropTypes.func.isRequired,\n  styles: PropTypes.object.isRequired\n});\nvar _excluded = [\"color\", \"height\", \"width\"];\nvar FloaterCloseBtn = function FloaterCloseBtn(_ref) {\n  var handleClick = _ref.handleClick,\n    styles = _ref.styles;\n  var color = styles.color,\n    height = styles.height,\n    width = styles.width,\n    style = _objectWithoutProperties(styles, _excluded);\n  return /*#__PURE__*/React.createElement(\"button\", {\n    \"aria-label\": \"close\",\n    onClick: handleClick,\n    style: style,\n    type: \"button\"\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    width: \"\".concat(width, \"px\"),\n    height: \"\".concat(height, \"px\"),\n    viewBox: \"0 0 18 18\",\n    version: \"1.1\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    preserveAspectRatio: \"xMidYMid\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",\n    fill: color\n  }))));\n};\nFloaterCloseBtn.propTypes = {\n  handleClick: PropTypes.func.isRequired,\n  styles: PropTypes.object.isRequired\n};\nvar FloaterContainer = function FloaterContainer(_ref) {\n  var content = _ref.content,\n    footer = _ref.footer,\n    handleClick = _ref.handleClick,\n    open = _ref.open,\n    positionWrapper = _ref.positionWrapper,\n    showCloseButton = _ref.showCloseButton,\n    title = _ref.title,\n    styles = _ref.styles;\n  var output = {\n    content: /*#__PURE__*/React.isValidElement(content) ? content : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"__floater__content\",\n      style: styles.content\n    }, content)\n  };\n  if (title) {\n    output.title = /*#__PURE__*/React.isValidElement(title) ? title : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"__floater__title\",\n      style: styles.title\n    }, title);\n  }\n  if (footer) {\n    output.footer = /*#__PURE__*/React.isValidElement(footer) ? footer : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"__floater__footer\",\n      style: styles.footer\n    }, footer);\n  }\n  if ((showCloseButton || positionWrapper) && !is[\"boolean\"](open)) {\n    output.close = /*#__PURE__*/React.createElement(FloaterCloseBtn, {\n      styles: styles.close,\n      handleClick: handleClick\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"__floater__container\",\n    style: styles.container\n  }, output.close, output.title, output.content, output.footer);\n};\nFloaterContainer.propTypes = {\n  content: PropTypes.node.isRequired,\n  footer: PropTypes.node,\n  handleClick: PropTypes.func.isRequired,\n  open: PropTypes.bool,\n  positionWrapper: PropTypes.bool.isRequired,\n  showCloseButton: PropTypes.bool.isRequired,\n  styles: PropTypes.object.isRequired,\n  title: PropTypes.node\n};\nvar Floater = /*#__PURE__*/function (_React$Component) {\n  _inherits(Floater, _React$Component);\n  var _super = _createSuper(Floater);\n  function Floater() {\n    _classCallCheck(this, Floater);\n    return _super.apply(this, arguments);\n  }\n  _createClass(Floater, [{\n    key: \"style\",\n    get: function get() {\n      var _this$props = this.props,\n        disableAnimation = _this$props.disableAnimation,\n        component = _this$props.component,\n        placement = _this$props.placement,\n        hideArrow = _this$props.hideArrow,\n        status = _this$props.status,\n        styles = _this$props.styles;\n      var length = styles.arrow.length,\n        floater = styles.floater,\n        floaterCentered = styles.floaterCentered,\n        floaterClosing = styles.floaterClosing,\n        floaterOpening = styles.floaterOpening,\n        floaterWithAnimation = styles.floaterWithAnimation,\n        floaterWithComponent = styles.floaterWithComponent;\n      var element = {};\n      if (!hideArrow) {\n        if (placement.startsWith('top')) {\n          element.padding = \"0 0 \".concat(length, \"px\");\n        } else if (placement.startsWith('bottom')) {\n          element.padding = \"\".concat(length, \"px 0 0\");\n        } else if (placement.startsWith('left')) {\n          element.padding = \"0 \".concat(length, \"px 0 0\");\n        } else if (placement.startsWith('right')) {\n          element.padding = \"0 0 0 \".concat(length, \"px\");\n        }\n      }\n      if ([STATUS.OPENING, STATUS.OPEN].indexOf(status) !== -1) {\n        element = _objectSpread2(_objectSpread2({}, element), floaterOpening);\n      }\n      if (status === STATUS.CLOSING) {\n        element = _objectSpread2(_objectSpread2({}, element), floaterClosing);\n      }\n      if (status === STATUS.OPEN && !disableAnimation) {\n        element = _objectSpread2(_objectSpread2({}, element), floaterWithAnimation);\n      }\n      if (placement === 'center') {\n        element = _objectSpread2(_objectSpread2({}, element), floaterCentered);\n      }\n      if (component) {\n        element = _objectSpread2(_objectSpread2({}, element), floaterWithComponent);\n      }\n      return _objectSpread2(_objectSpread2({}, floater), element);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        component = _this$props2.component,\n        closeFn = _this$props2.handleClick,\n        hideArrow = _this$props2.hideArrow,\n        setFloaterRef = _this$props2.setFloaterRef,\n        status = _this$props2.status;\n      var output = {};\n      var classes = ['__floater'];\n      if (component) {\n        if (/*#__PURE__*/React.isValidElement(component)) {\n          output.content = /*#__PURE__*/React.cloneElement(component, {\n            closeFn: closeFn\n          });\n        } else {\n          output.content = component({\n            closeFn: closeFn\n          });\n        }\n      } else {\n        output.content = /*#__PURE__*/React.createElement(FloaterContainer, this.props);\n      }\n      if (status === STATUS.OPEN) {\n        classes.push('__floater__open');\n      }\n      if (!hideArrow) {\n        output.arrow = /*#__PURE__*/React.createElement(FloaterArrow, this.props);\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: setFloaterRef,\n        className: classes.join(' '),\n        style: this.style\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"__floater__body\"\n      }, output.content, output.arrow));\n    }\n  }]);\n  return Floater;\n}(React.Component);\n_defineProperty(Floater, \"propTypes\", {\n  component: PropTypes.oneOfType([PropTypes.func, PropTypes.element]),\n  content: PropTypes.node,\n  disableAnimation: PropTypes.bool.isRequired,\n  footer: PropTypes.node,\n  handleClick: PropTypes.func.isRequired,\n  hideArrow: PropTypes.bool.isRequired,\n  open: PropTypes.bool,\n  placement: PropTypes.string.isRequired,\n  positionWrapper: PropTypes.bool.isRequired,\n  setArrowRef: PropTypes.func.isRequired,\n  setFloaterRef: PropTypes.func.isRequired,\n  showCloseButton: PropTypes.bool,\n  status: PropTypes.string.isRequired,\n  styles: PropTypes.object.isRequired,\n  title: PropTypes.node\n});\nvar ReactFloaterWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(ReactFloaterWrapper, _React$Component);\n  var _super = _createSuper(ReactFloaterWrapper);\n  function ReactFloaterWrapper() {\n    _classCallCheck(this, ReactFloaterWrapper);\n    return _super.apply(this, arguments);\n  }\n  _createClass(ReactFloaterWrapper, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        children = _this$props.children,\n        handleClick = _this$props.handleClick,\n        handleMouseEnter = _this$props.handleMouseEnter,\n        handleMouseLeave = _this$props.handleMouseLeave,\n        setChildRef = _this$props.setChildRef,\n        setWrapperRef = _this$props.setWrapperRef,\n        style = _this$props.style,\n        styles = _this$props.styles;\n      var element; /* istanbul ignore else */\n      if (children) {\n        if (React.Children.count(children) === 1) {\n          if (! /*#__PURE__*/React.isValidElement(children)) {\n            element = /*#__PURE__*/React.createElement(\"span\", null, children);\n          } else {\n            var refProp = is[\"function\"](children.type) ? 'innerRef' : 'ref';\n            element = /*#__PURE__*/React.cloneElement(React.Children.only(children), _defineProperty({}, refProp, setChildRef));\n          }\n        } else {\n          element = children;\n        }\n      }\n      if (!element) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", {\n        ref: setWrapperRef,\n        style: _objectSpread2(_objectSpread2({}, styles), style),\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave\n      }, element);\n    }\n  }]);\n  return ReactFloaterWrapper;\n}(React.Component);\n_defineProperty(ReactFloaterWrapper, \"propTypes\", {\n  children: PropTypes.node,\n  handleClick: PropTypes.func.isRequired,\n  handleMouseEnter: PropTypes.func.isRequired,\n  handleMouseLeave: PropTypes.func.isRequired,\n  setChildRef: PropTypes.func.isRequired,\n  setWrapperRef: PropTypes.func.isRequired,\n  style: PropTypes.object,\n  styles: PropTypes.object.isRequired\n});\nvar defaultOptions = {\n  zIndex: 100\n};\nfunction getStyles(styles) {\n  var options = deepmerge(defaultOptions, styles.options || {});\n  return {\n    wrapper: {\n      cursor: 'help',\n      display: 'inline-flex',\n      flexDirection: 'column',\n      zIndex: options.zIndex\n    },\n    wrapperPosition: {\n      left: -1000,\n      position: 'absolute',\n      top: -1000,\n      visibility: 'hidden'\n    },\n    floater: {\n      display: 'inline-block',\n      filter: 'drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))',\n      maxWidth: 300,\n      opacity: 0,\n      position: 'relative',\n      transition: 'opacity 0.3s',\n      visibility: 'hidden',\n      zIndex: options.zIndex\n    },\n    floaterOpening: {\n      opacity: 1,\n      visibility: 'visible'\n    },\n    floaterWithAnimation: {\n      opacity: 1,\n      transition: 'opacity 0.3s, transform 0.2s',\n      visibility: 'visible'\n    },\n    floaterWithComponent: {\n      maxWidth: '100%'\n    },\n    floaterClosing: {\n      opacity: 0,\n      visibility: 'visible'\n    },\n    floaterCentered: {\n      left: '50%',\n      position: 'fixed',\n      top: '50%',\n      transform: 'translate(-50%, -50%)'\n    },\n    container: {\n      backgroundColor: '#fff',\n      color: '#666',\n      minHeight: 60,\n      minWidth: 200,\n      padding: 20,\n      position: 'relative',\n      zIndex: 10\n    },\n    title: {\n      borderBottom: '1px solid #555',\n      color: '#555',\n      fontSize: 18,\n      marginBottom: 5,\n      paddingBottom: 6,\n      paddingRight: 18\n    },\n    content: {\n      fontSize: 15\n    },\n    close: {\n      backgroundColor: 'transparent',\n      border: 0,\n      borderRadius: 0,\n      color: '#555',\n      fontSize: 0,\n      height: 15,\n      outline: 'none',\n      padding: 10,\n      position: 'absolute',\n      right: 0,\n      top: 0,\n      width: 15,\n      WebkitAppearance: 'none'\n    },\n    footer: {\n      borderTop: '1px solid #ccc',\n      fontSize: 13,\n      marginTop: 10,\n      paddingTop: 5\n    },\n    arrow: {\n      color: '#fff',\n      display: 'inline-flex',\n      length: 16,\n      margin: 8,\n      position: 'absolute',\n      spread: 32\n    },\n    options: options\n  };\n}\nvar _excluded$1 = [\"arrow\", \"flip\", \"offset\"];\nvar POSITIONING_PROPS = ['position', 'top', 'right', 'bottom', 'left'];\nvar ReactFloater = /*#__PURE__*/function (_React$Component) {\n  _inherits(ReactFloater, _React$Component);\n  var _super = _createSuper(ReactFloater);\n  function ReactFloater(props) {\n    var _this;\n    _classCallCheck(this, ReactFloater);\n    _this = _super.call(this, props); /* istanbul ignore else */\n    _defineProperty(_assertThisInitialized(_this), \"setArrowRef\", function (ref) {\n      _this.arrowRef = ref;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setChildRef\", function (ref) {\n      _this.childRef = ref;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setFloaterRef\", function (ref) {\n      if (!_this.floaterRef) {\n        _this.floaterRef = ref;\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setWrapperRef\", function (ref) {\n      _this.wrapperRef = ref;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleTransitionEnd\", function () {\n      var status = _this.state.status;\n      var callback = _this.props.callback; /* istanbul ignore else */\n      if (_this.wrapperPopper) {\n        _this.wrapperPopper.instance.update();\n      }\n      _this.setState({\n        status: status === STATUS.OPENING ? STATUS.OPEN : STATUS.IDLE\n      }, function () {\n        var newStatus = _this.state.status;\n        callback(newStatus === STATUS.OPEN ? 'open' : 'close', _this.props);\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClick\", function () {\n      var _this$props = _this.props,\n        event = _this$props.event,\n        open = _this$props.open;\n      if (is[\"boolean\"](open)) return;\n      var _this$state = _this.state,\n        positionWrapper = _this$state.positionWrapper,\n        status = _this$state.status; /* istanbul ignore else */\n      if (_this.event === 'click' || _this.event === 'hover' && positionWrapper) {\n        log({\n          title: 'click',\n          data: [{\n            event: event,\n            status: status === STATUS.OPEN ? 'closing' : 'opening'\n          }],\n          debug: _this.debug\n        });\n        _this.toggle();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleMouseEnter\", function () {\n      var _this$props2 = _this.props,\n        event = _this$props2.event,\n        open = _this$props2.open;\n      if (is[\"boolean\"](open) || isMobile()) return;\n      var status = _this.state.status; /* istanbul ignore else */\n      if (_this.event === 'hover' && status === STATUS.IDLE) {\n        log({\n          title: 'mouseEnter',\n          data: [{\n            key: 'originalEvent',\n            value: event\n          }],\n          debug: _this.debug\n        });\n        clearTimeout(_this.eventDelayTimeout);\n        _this.toggle();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleMouseLeave\", function () {\n      var _this$props3 = _this.props,\n        event = _this$props3.event,\n        eventDelay = _this$props3.eventDelay,\n        open = _this$props3.open;\n      if (is[\"boolean\"](open) || isMobile()) return;\n      var _this$state2 = _this.state,\n        status = _this$state2.status,\n        positionWrapper = _this$state2.positionWrapper; /* istanbul ignore else */\n      if (_this.event === 'hover') {\n        log({\n          title: 'mouseLeave',\n          data: [{\n            key: 'originalEvent',\n            value: event\n          }],\n          debug: _this.debug\n        });\n        if (!eventDelay) {\n          _this.toggle(STATUS.IDLE);\n        } else if ([STATUS.OPENING, STATUS.OPEN].indexOf(status) !== -1 && !positionWrapper && !_this.eventDelayTimeout) {\n          _this.eventDelayTimeout = setTimeout(function () {\n            delete _this.eventDelayTimeout;\n            _this.toggle();\n          }, eventDelay * 1000);\n        }\n      }\n    });\n    _this.state = {\n      currentPlacement: props.placement,\n      positionWrapper: props.wrapperOptions.position && !!props.target,\n      status: STATUS.INIT,\n      statusWrapper: STATUS.INIT\n    };\n    _this._isMounted = false;\n    if (canUseDOM) {\n      window.addEventListener('load', function () {\n        if (_this.popper) {\n          _this.popper.instance.update();\n        }\n        if (_this.wrapperPopper) {\n          _this.wrapperPopper.instance.update();\n        }\n      });\n    }\n    return _this;\n  }\n  _createClass(ReactFloater, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n      var positionWrapper = this.state.positionWrapper;\n      var _this$props5 = this.props,\n        children = _this$props5.children,\n        open = _this$props5.open,\n        target = _this$props5.target;\n      this._isMounted = true;\n      log({\n        title: 'init',\n        data: {\n          hasChildren: !!children,\n          hasTarget: !!target,\n          isControlled: is[\"boolean\"](open),\n          positionWrapper: positionWrapper,\n          target: this.target,\n          floater: this.floaterRef\n        },\n        debug: this.debug\n      });\n      this.initPopper();\n      if (!children && target && !is[\"boolean\"](open)) ;\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (!canUseDOM) return;\n      var _this$props6 = this.props,\n        autoOpen = _this$props6.autoOpen,\n        open = _this$props6.open,\n        target = _this$props6.target,\n        wrapperOptions = _this$props6.wrapperOptions;\n      var _treeChanges = treeChanges(prevState, this.state),\n        changedFrom = _treeChanges.changedFrom,\n        changedTo = _treeChanges.changedTo;\n      if (prevProps.open !== open) {\n        var forceStatus; // always follow `open` in controlled mode\n        if (is[\"boolean\"](open)) {\n          forceStatus = open ? STATUS.OPENING : STATUS.CLOSING;\n        }\n        this.toggle(forceStatus);\n      }\n      if (prevProps.wrapperOptions.position !== wrapperOptions.position || prevProps.target !== target) {\n        this.changeWrapperPosition(this.props);\n      }\n      if (changedTo('status', STATUS.IDLE) && open) {\n        this.toggle(STATUS.OPEN);\n      } else if (changedFrom('status', STATUS.INIT, STATUS.IDLE) && autoOpen) {\n        this.toggle(STATUS.OPEN);\n      }\n      if (this.popper && changedTo('status', STATUS.OPENING)) {\n        this.popper.instance.update();\n      }\n      if (this.floaterRef && (changedTo('status', STATUS.OPENING) || changedTo('status', STATUS.CLOSING))) {\n        once(this.floaterRef, 'transitionend', this.handleTransitionEnd);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (!canUseDOM) return;\n      this._isMounted = false;\n      if (this.popper) {\n        this.popper.instance.destroy();\n      }\n      if (this.wrapperPopper) {\n        this.wrapperPopper.instance.destroy();\n      }\n    }\n  }, {\n    key: \"initPopper\",\n    value: function initPopper() {\n      var _this2 = this;\n      var target = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.target;\n      var positionWrapper = this.state.positionWrapper;\n      var _this$props7 = this.props,\n        disableFlip = _this$props7.disableFlip,\n        getPopper = _this$props7.getPopper,\n        hideArrow = _this$props7.hideArrow,\n        offset = _this$props7.offset,\n        placement = _this$props7.placement,\n        wrapperOptions = _this$props7.wrapperOptions;\n      var flipBehavior = placement === 'top' || placement === 'bottom' ? 'flip' : ['right', 'bottom-end', 'top-end', 'left', 'top-start', 'bottom-start']; /* istanbul ignore else */\n      if (placement === 'center') {\n        this.setState({\n          status: STATUS.IDLE\n        });\n      } else if (target && this.floaterRef) {\n        var _this$options = this.options,\n          arrow = _this$options.arrow,\n          flip = _this$options.flip,\n          offsetOptions = _this$options.offset,\n          rest = _objectWithoutProperties(_this$options, _excluded$1);\n        new Popper(target, this.floaterRef, {\n          placement: placement,\n          modifiers: _objectSpread2({\n            arrow: _objectSpread2({\n              enabled: !hideArrow,\n              element: this.arrowRef\n            }, arrow),\n            flip: _objectSpread2({\n              enabled: !disableFlip,\n              behavior: flipBehavior\n            }, flip),\n            offset: _objectSpread2({\n              offset: \"0, \".concat(offset, \"px\")\n            }, offsetOptions)\n          }, rest),\n          onCreate: function onCreate(data) {\n            _this2.popper = data;\n            getPopper(data, 'floater');\n            if (_this2._isMounted) {\n              _this2.setState({\n                currentPlacement: data.placement,\n                status: STATUS.IDLE\n              });\n            }\n            if (placement !== data.placement) {\n              setTimeout(function () {\n                data.instance.update();\n              }, 1);\n            }\n          },\n          onUpdate: function onUpdate(data) {\n            _this2.popper = data;\n            var currentPlacement = _this2.state.currentPlacement;\n            if (_this2._isMounted && data.placement !== currentPlacement) {\n              _this2.setState({\n                currentPlacement: data.placement\n              });\n            }\n          }\n        });\n      }\n      if (positionWrapper) {\n        var wrapperOffset = !is.undefined(wrapperOptions.offset) ? wrapperOptions.offset : 0;\n        new Popper(this.target, this.wrapperRef, {\n          placement: wrapperOptions.placement || placement,\n          modifiers: {\n            arrow: {\n              enabled: false\n            },\n            offset: {\n              offset: \"0, \".concat(wrapperOffset, \"px\")\n            },\n            flip: {\n              enabled: false\n            }\n          },\n          onCreate: function onCreate(data) {\n            _this2.wrapperPopper = data;\n            if (_this2._isMounted) {\n              _this2.setState({\n                statusWrapper: STATUS.IDLE\n              });\n            }\n            getPopper(data, 'wrapper');\n            if (placement !== data.placement) {\n              setTimeout(function () {\n                data.instance.update();\n              }, 1);\n            }\n          }\n        });\n      }\n    }\n  }, {\n    key: \"changeWrapperPosition\",\n    value: function changeWrapperPosition(_ref) {\n      var target = _ref.target,\n        wrapperOptions = _ref.wrapperOptions;\n      this.setState({\n        positionWrapper: wrapperOptions.position && !!target\n      });\n    }\n  }, {\n    key: \"toggle\",\n    value: function toggle(forceStatus) {\n      var status = this.state.status;\n      var nextStatus = status === STATUS.OPEN ? STATUS.CLOSING : STATUS.OPENING;\n      if (!is.undefined(forceStatus)) {\n        nextStatus = forceStatus;\n      }\n      this.setState({\n        status: nextStatus\n      });\n    }\n  }, {\n    key: \"debug\",\n    get: function get() {\n      var debug = this.props.debug;\n      return debug || !!global.ReactFloaterDebug;\n    }\n  }, {\n    key: \"event\",\n    get: function get() {\n      var _this$props8 = this.props,\n        disableHoverToClick = _this$props8.disableHoverToClick,\n        event = _this$props8.event;\n      if (event === 'hover' && isMobile() && !disableHoverToClick) {\n        return 'click';\n      }\n      return event;\n    }\n  }, {\n    key: \"options\",\n    get: function get() {\n      var options = this.props.options;\n      return deepmerge(DEFAULTS, options || {});\n    }\n  }, {\n    key: \"styles\",\n    get: function get() {\n      var _this3 = this;\n      var _this$state3 = this.state,\n        status = _this$state3.status,\n        positionWrapper = _this$state3.positionWrapper,\n        statusWrapper = _this$state3.statusWrapper;\n      var styles = this.props.styles;\n      var nextStyles = deepmerge(getStyles(styles), styles);\n      if (positionWrapper) {\n        var wrapperStyles;\n        if (!([STATUS.IDLE].indexOf(status) !== -1) || !([STATUS.IDLE].indexOf(statusWrapper) !== -1)) {\n          wrapperStyles = nextStyles.wrapperPosition;\n        } else {\n          wrapperStyles = this.wrapperPopper.styles;\n        }\n        nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), wrapperStyles);\n      } /* istanbul ignore else */\n      if (this.target) {\n        var targetStyles = window.getComputedStyle(this.target); /* istanbul ignore else */\n        if (this.wrapperStyles) {\n          nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), this.wrapperStyles);\n        } else if (!(['relative', 'static'].indexOf(targetStyles.position) !== -1)) {\n          this.wrapperStyles = {};\n          if (!positionWrapper) {\n            POSITIONING_PROPS.forEach(function (d) {\n              _this3.wrapperStyles[d] = targetStyles[d];\n            });\n            nextStyles.wrapper = _objectSpread2(_objectSpread2({}, nextStyles.wrapper), this.wrapperStyles);\n            this.target.style.position = 'relative';\n            this.target.style.top = 'auto';\n            this.target.style.right = 'auto';\n            this.target.style.bottom = 'auto';\n            this.target.style.left = 'auto';\n          }\n        }\n      }\n      return nextStyles;\n    }\n  }, {\n    key: \"target\",\n    get: function get() {\n      if (!canUseDOM) return null;\n      var target = this.props.target;\n      if (target) {\n        if (is.domElement(target)) {\n          return target;\n        }\n        return document.querySelector(target);\n      }\n      return this.childRef || this.wrapperRef;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state4 = this.state,\n        currentPlacement = _this$state4.currentPlacement,\n        positionWrapper = _this$state4.positionWrapper,\n        status = _this$state4.status;\n      var _this$props9 = this.props,\n        children = _this$props9.children,\n        component = _this$props9.component,\n        content = _this$props9.content,\n        disableAnimation = _this$props9.disableAnimation,\n        footer = _this$props9.footer,\n        hideArrow = _this$props9.hideArrow,\n        id = _this$props9.id,\n        open = _this$props9.open,\n        showCloseButton = _this$props9.showCloseButton,\n        style = _this$props9.style,\n        target = _this$props9.target,\n        title = _this$props9.title;\n      var wrapper = /*#__PURE__*/React.createElement(ReactFloaterWrapper, {\n        handleClick: this.handleClick,\n        handleMouseEnter: this.handleMouseEnter,\n        handleMouseLeave: this.handleMouseLeave,\n        setChildRef: this.setChildRef,\n        setWrapperRef: this.setWrapperRef,\n        style: style,\n        styles: this.styles.wrapper\n      }, children);\n      var output = {};\n      if (positionWrapper) {\n        output.wrapperInPortal = wrapper;\n      } else {\n        output.wrapperAsChildren = wrapper;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(ReactFloaterPortal, {\n        hasChildren: !!children,\n        id: id,\n        placement: currentPlacement,\n        setRef: this.setFloaterRef,\n        target: target,\n        zIndex: this.styles.options.zIndex\n      }, /*#__PURE__*/React.createElement(Floater, {\n        component: component,\n        content: content,\n        disableAnimation: disableAnimation,\n        footer: footer,\n        handleClick: this.handleClick,\n        hideArrow: hideArrow || currentPlacement === 'center',\n        open: open,\n        placement: currentPlacement,\n        positionWrapper: positionWrapper,\n        setArrowRef: this.setArrowRef,\n        setFloaterRef: this.setFloaterRef,\n        showCloseButton: showCloseButton,\n        status: status,\n        styles: this.styles,\n        title: title\n      }), output.wrapperInPortal), output.wrapperAsChildren);\n    }\n  }]);\n  return ReactFloater;\n}(React.Component);\n_defineProperty(ReactFloater, \"propTypes\", {\n  autoOpen: PropTypes.bool,\n  callback: PropTypes.func,\n  children: PropTypes.node,\n  component: isRequiredIf(PropTypes.oneOfType([PropTypes.func, PropTypes.element]), function (props) {\n    return !props.content;\n  }),\n  content: isRequiredIf(PropTypes.node, function (props) {\n    return !props.component;\n  }),\n  debug: PropTypes.bool,\n  disableAnimation: PropTypes.bool,\n  disableFlip: PropTypes.bool,\n  disableHoverToClick: PropTypes.bool,\n  event: PropTypes.oneOf(['hover', 'click']),\n  eventDelay: PropTypes.number,\n  footer: PropTypes.node,\n  getPopper: PropTypes.func,\n  hideArrow: PropTypes.bool,\n  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  offset: PropTypes.number,\n  open: PropTypes.bool,\n  options: PropTypes.object,\n  placement: PropTypes.oneOf(['top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end', 'right', 'right-start', 'right-end', 'auto', 'center']),\n  showCloseButton: PropTypes.bool,\n  style: PropTypes.object,\n  styles: PropTypes.object,\n  target: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  title: PropTypes.node,\n  wrapperOptions: PropTypes.shape({\n    offset: PropTypes.number,\n    placement: PropTypes.oneOf(['top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end', 'right', 'right-start', 'right-end', 'auto']),\n    position: PropTypes.bool\n  })\n});\n_defineProperty(ReactFloater, \"defaultProps\", {\n  autoOpen: false,\n  callback: noop,\n  debug: false,\n  disableAnimation: false,\n  disableFlip: false,\n  disableHoverToClick: false,\n  event: 'click',\n  eventDelay: 0.4,\n  getPopper: noop,\n  hideArrow: false,\n  offset: 15,\n  placement: 'bottom',\n  showCloseButton: false,\n  styles: {},\n  target: null,\n  wrapperOptions: {\n    position: false\n  }\n});\nexport default ReactFloater;", "map": {"version": 3, "names": ["React", "PropTypes", "isRequiredIf", "<PERSON><PERSON>", "deepmerge", "is", "treeChanges", "ReactDOM", "ExecutionEnvironment", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_createClass", "protoProps", "staticProps", "prototype", "obj", "value", "_inherits", "subClass", "superClass", "create", "constructor", "_setPrototypeOf", "_getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "p", "_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "call", "e", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "_assertThisInitialized", "self", "ReferenceError", "_possibleConstructorReturn", "_createSuper", "Derived", "hasNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULTS", "flip", "padding", "preventOverflow", "STATUS", "INIT", "IDLE", "OPENING", "OPEN", "CLOSING", "ERROR", "canUseDOM", "isReact16", "createPortal", "undefined", "isMobile", "window", "test", "navigator", "userAgent", "log", "_ref", "title", "data", "_ref$warn", "warn", "_ref$debug", "debug", "logFn", "console", "error", "groupCollapsed", "concat", "Array", "isArray", "d", "plainObject", "groupEnd", "on", "element", "event", "cb", "capture", "addEventListener", "off", "removeEventListener", "once", "_nextCB", "nextCB", "noop", "ReactFloaterPortal", "_React$Component", "_super", "_this", "node", "document", "createElement", "id", "zIndex", "style", "body", "append<PERSON><PERSON><PERSON>", "componentDidMount", "renderPortal", "componentDidUpdate", "componentWillUnmount", "unmountComponentAtNode", "<PERSON><PERSON><PERSON><PERSON>", "_this$props", "children", "setRef", "portal", "unstable_renderSubtreeIntoContainer", "renderReact16", "_this$props2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placement", "render", "Component", "oneOfType", "array", "bool", "string", "number", "func", "isRequired", "FloaterArrow", "get", "styles", "arrow", "pointerEvents", "position", "width", "startsWith", "bottom", "left", "right", "height", "top", "setArrowRef", "_styles$arrow", "color", "display", "margin", "spread", "arrowStyles", "points", "x", "y", "marginLeft", "marginRight", "marginTop", "marginBottom", "className", "parentStyle", "ref", "version", "xmlns", "fill", "_excluded", "FloaterCloseBtn", "handleClick", "onClick", "type", "viewBox", "preserveAspectRatio", "propTypes", "FloaterContainer", "content", "footer", "open", "positionWrapper", "showCloseButton", "output", "isValidElement", "close", "container", "Floater", "disableAnimation", "component", "hideArrow", "status", "floater", "floaterCentered", "floaterClosing", "floaterOpening", "floaterWithAnimation", "floaterWithComponent", "closeFn", "setFloaterRef", "classes", "cloneElement", "join", "ReactFloaterWrapper", "handleMouseEnter", "handleMouseLeave", "set<PERSON><PERSON>dRef", "setWrapperRef", "Children", "count", "refProp", "only", "onMouseEnter", "onMouseLeave", "defaultOptions", "getStyles", "options", "wrapper", "cursor", "flexDirection", "wrapperPosition", "visibility", "max<PERSON><PERSON><PERSON>", "opacity", "transition", "transform", "backgroundColor", "minHeight", "min<PERSON><PERSON><PERSON>", "borderBottom", "fontSize", "paddingBottom", "paddingRight", "border", "borderRadius", "outline", "WebkitAppearance", "borderTop", "paddingTop", "_excluded$1", "POSITIONING_PROPS", "ReactFloater", "arrowRef", "childRef", "floaterRef", "wrapperRef", "state", "callback", "wrapperPopper", "update", "setState", "newStatus", "_this$state", "toggle", "clearTimeout", "eventDelayTimeout", "_this$props3", "eventDelay", "_this$state2", "setTimeout", "currentPlacement", "wrapperOptions", "statusWrapper", "_isMounted", "popper", "_this$props5", "<PERSON><PERSON><PERSON><PERSON>", "isControlled", "initPopper", "prevProps", "prevState", "_this$props6", "autoOpen", "_treeChanges", "changedFrom", "changedTo", "forceStatus", "changeWrapperPosition", "handleTransitionEnd", "destroy", "_this2", "_this$props7", "disableFlip", "getPopper", "offset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$options", "offsetOptions", "rest", "modifiers", "enabled", "behavior", "onCreate", "onUpdate", "wrapperOffset", "nextStatus", "global", "ReactFloaterDebug", "_this$props8", "disableHoverToClick", "_this3", "_this$state3", "nextStyles", "wrapperStyles", "targetStyles", "getComputedStyle", "dom<PERSON>lement", "querySelector", "_this$state4", "_this$props9", "wrapperInPortal", "wrapperAsChildren", "oneOf", "shape"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-floater/es/index.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport isRequiredIf from 'react-proptype-conditional-require';\nimport Popper from 'popper.js';\nimport deepmerge from 'deepmerge';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\nimport ReactDOM from 'react-dom';\nimport ExecutionEnvironment from 'exenv';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar DEFAULTS = {flip:{padding:20},preventOverflow:{padding:10}};\n\nvar STATUS = {INIT:'init',IDLE:'idle',OPENING:'opening',OPEN:'open',CLOSING:'closing',ERROR:'error'};\n\nvar canUseDOM=ExecutionEnvironment.canUseDOM;var isReact16=ReactDOM.createPortal!==undefined;function isMobile(){return 'ontouchstart'in window&&/Mobi/.test(navigator.userAgent);}/**\n * Log method calls if debug is enabled\n *\n * @private\n * @param {Object}       arg\n * @param {string}       arg.title    - The title the logger was called from\n * @param {Object|Array} [arg.data]   - The data to be logged\n * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n */function log(_ref){var title=_ref.title,data=_ref.data,_ref$warn=_ref.warn,warn=_ref$warn===void 0?false:_ref$warn,_ref$debug=_ref.debug,debug=_ref$debug===void 0?false:_ref$debug;/* eslint-disable no-console */var logFn=warn?console.warn||console.error:console.log;if(debug&&title&&data){console.groupCollapsed(\"%creact-floater: \".concat(title),'color: #9b00ff; font-weight: bold; font-size: 12px;');if(Array.isArray(data)){data.forEach(function(d){if(is.plainObject(d)&&d.key){logFn.apply(console,[d.key,d.value]);}else {logFn.apply(console,[d]);}});}else {logFn.apply(console,[data]);}console.groupEnd();}/* eslint-enable */}function on(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.addEventListener(event,cb,capture);}function off(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.removeEventListener(event,cb,capture);}function once(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var _nextCB;// eslint-disable-next-line prefer-const\n_nextCB=function nextCB(e){cb(e);off(element,event,_nextCB);};on(element,event,_nextCB,capture);}function noop(){}\n\nvar ReactFloaterPortal=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterPortal,_React$Component);var _super=_createSuper(ReactFloaterPortal);function ReactFloaterPortal(props){var _this;_classCallCheck(this,ReactFloaterPortal);_this=_super.call(this,props);if(!canUseDOM)return _possibleConstructorReturn(_this);_this.node=document.createElement('div');if(props.id){_this.node.id=props.id;}if(props.zIndex){_this.node.style.zIndex=props.zIndex;}document.body.appendChild(_this.node);return _this;}_createClass(ReactFloaterPortal,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM)return;if(!isReact16){this.renderPortal();}}},{key:\"componentDidUpdate\",value:function componentDidUpdate(){if(!canUseDOM)return;if(!isReact16){this.renderPortal();}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM||!this.node)return;if(!isReact16){ReactDOM.unmountComponentAtNode(this.node);}document.body.removeChild(this.node);}},{key:\"renderPortal\",value:function renderPortal(){if(!canUseDOM)return null;var _this$props=this.props,children=_this$props.children,setRef=_this$props.setRef;/* istanbul ignore else */if(isReact16){return/*#__PURE__*/ReactDOM.createPortal(children,this.node);}var portal=ReactDOM.unstable_renderSubtreeIntoContainer(this,children.length>1?/*#__PURE__*/React.createElement(\"div\",null,children):children[0],this.node);setRef(portal);return null;}},{key:\"renderReact16\",value:function renderReact16(){var _this$props2=this.props,hasChildren=_this$props2.hasChildren,placement=_this$props2.placement,target=_this$props2.target;if(!hasChildren){if(target||placement==='center'){return this.renderPortal();}return null;}return this.renderPortal();}},{key:\"render\",value:function render(){if(!isReact16){return null;}return this.renderReact16();}}]);return ReactFloaterPortal;}(React.Component);_defineProperty(ReactFloaterPortal,\"propTypes\",{children:PropTypes.oneOfType([PropTypes.element,PropTypes.array]),hasChildren:PropTypes.bool,id:PropTypes.oneOfType([PropTypes.string,PropTypes.number]),placement:PropTypes.string,setRef:PropTypes.func.isRequired,target:PropTypes.oneOfType([PropTypes.object,PropTypes.string]),zIndex:PropTypes.number});\n\nvar FloaterArrow=/*#__PURE__*/function(_React$Component){_inherits(FloaterArrow,_React$Component);var _super=_createSuper(FloaterArrow);function FloaterArrow(){_classCallCheck(this,FloaterArrow);return _super.apply(this,arguments);}_createClass(FloaterArrow,[{key:\"parentStyle\",get:function get(){var _this$props=this.props,placement=_this$props.placement,styles=_this$props.styles;var length=styles.arrow.length;var arrow={pointerEvents:'none',position:'absolute',width:'100%'};/* istanbul ignore else */if(placement.startsWith('top')){arrow.bottom=0;arrow.left=0;arrow.right=0;arrow.height=length;}else if(placement.startsWith('bottom')){arrow.left=0;arrow.right=0;arrow.top=0;arrow.height=length;}else if(placement.startsWith('left')){arrow.right=0;arrow.top=0;arrow.bottom=0;}else if(placement.startsWith('right')){arrow.left=0;arrow.top=0;}return arrow;}},{key:\"render\",value:function render(){var _this$props2=this.props,placement=_this$props2.placement,setArrowRef=_this$props2.setArrowRef,styles=_this$props2.styles;var _styles$arrow=styles.arrow,color=_styles$arrow.color,display=_styles$arrow.display,length=_styles$arrow.length,margin=_styles$arrow.margin,position=_styles$arrow.position,spread=_styles$arrow.spread;var arrowStyles={display:display,position:position};var points;var x=spread;var y=length;/* istanbul ignore else */if(placement.startsWith('top')){points=\"0,0 \".concat(x/2,\",\").concat(y,\" \").concat(x,\",0\");arrowStyles.bottom=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('bottom')){points=\"\".concat(x,\",\").concat(y,\" \").concat(x/2,\",0 0,\").concat(y);arrowStyles.top=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('left')){y=spread;x=length;points=\"0,0 \".concat(x,\",\").concat(y/2,\" 0,\").concat(y);arrowStyles.right=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}else if(placement.startsWith('right')){y=spread;x=length;points=\"\".concat(x,\",\").concat(y,\" \").concat(x,\",0 0,\").concat(y/2);arrowStyles.left=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}return/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__arrow\",style:this.parentStyle},/*#__PURE__*/React.createElement(\"span\",{ref:setArrowRef,style:arrowStyles},/*#__PURE__*/React.createElement(\"svg\",{width:x,height:y,version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\"},/*#__PURE__*/React.createElement(\"polygon\",{points:points,fill:color}))));}}]);return FloaterArrow;}(React.Component);_defineProperty(FloaterArrow,\"propTypes\",{placement:PropTypes.string.isRequired,setArrowRef:PropTypes.func.isRequired,styles:PropTypes.object.isRequired});\n\nvar _excluded=[\"color\",\"height\",\"width\"];var FloaterCloseBtn=function FloaterCloseBtn(_ref){var handleClick=_ref.handleClick,styles=_ref.styles;var color=styles.color,height=styles.height,width=styles.width,style=_objectWithoutProperties(styles,_excluded);return/*#__PURE__*/React.createElement(\"button\",{\"aria-label\":\"close\",onClick:handleClick,style:style,type:\"button\"},/*#__PURE__*/React.createElement(\"svg\",{width:\"\".concat(width,\"px\"),height:\"\".concat(height,\"px\"),viewBox:\"0 0 18 18\",version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\",preserveAspectRatio:\"xMidYMid\"},/*#__PURE__*/React.createElement(\"g\",null,/*#__PURE__*/React.createElement(\"path\",{d:\"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",fill:color}))));};FloaterCloseBtn.propTypes={handleClick:PropTypes.func.isRequired,styles:PropTypes.object.isRequired};\n\nvar FloaterContainer=function FloaterContainer(_ref){var content=_ref.content,footer=_ref.footer,handleClick=_ref.handleClick,open=_ref.open,positionWrapper=_ref.positionWrapper,showCloseButton=_ref.showCloseButton,title=_ref.title,styles=_ref.styles;var output={content:/*#__PURE__*/React.isValidElement(content)?content:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__content\",style:styles.content},content)};if(title){output.title=/*#__PURE__*/React.isValidElement(title)?title:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__title\",style:styles.title},title);}if(footer){output.footer=/*#__PURE__*/React.isValidElement(footer)?footer:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__footer\",style:styles.footer},footer);}if((showCloseButton||positionWrapper)&&!is[\"boolean\"](open)){output.close=/*#__PURE__*/React.createElement(FloaterCloseBtn,{styles:styles.close,handleClick:handleClick});}return/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__container\",style:styles.container},output.close,output.title,output.content,output.footer);};FloaterContainer.propTypes={content:PropTypes.node.isRequired,footer:PropTypes.node,handleClick:PropTypes.func.isRequired,open:PropTypes.bool,positionWrapper:PropTypes.bool.isRequired,showCloseButton:PropTypes.bool.isRequired,styles:PropTypes.object.isRequired,title:PropTypes.node};\n\nvar Floater=/*#__PURE__*/function(_React$Component){_inherits(Floater,_React$Component);var _super=_createSuper(Floater);function Floater(){_classCallCheck(this,Floater);return _super.apply(this,arguments);}_createClass(Floater,[{key:\"style\",get:function get(){var _this$props=this.props,disableAnimation=_this$props.disableAnimation,component=_this$props.component,placement=_this$props.placement,hideArrow=_this$props.hideArrow,status=_this$props.status,styles=_this$props.styles;var length=styles.arrow.length,floater=styles.floater,floaterCentered=styles.floaterCentered,floaterClosing=styles.floaterClosing,floaterOpening=styles.floaterOpening,floaterWithAnimation=styles.floaterWithAnimation,floaterWithComponent=styles.floaterWithComponent;var element={};if(!hideArrow){if(placement.startsWith('top')){element.padding=\"0 0 \".concat(length,\"px\");}else if(placement.startsWith('bottom')){element.padding=\"\".concat(length,\"px 0 0\");}else if(placement.startsWith('left')){element.padding=\"0 \".concat(length,\"px 0 0\");}else if(placement.startsWith('right')){element.padding=\"0 0 0 \".concat(length,\"px\");}}if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1){element=_objectSpread2(_objectSpread2({},element),floaterOpening);}if(status===STATUS.CLOSING){element=_objectSpread2(_objectSpread2({},element),floaterClosing);}if(status===STATUS.OPEN&&!disableAnimation){element=_objectSpread2(_objectSpread2({},element),floaterWithAnimation);}if(placement==='center'){element=_objectSpread2(_objectSpread2({},element),floaterCentered);}if(component){element=_objectSpread2(_objectSpread2({},element),floaterWithComponent);}return _objectSpread2(_objectSpread2({},floater),element);}},{key:\"render\",value:function render(){var _this$props2=this.props,component=_this$props2.component,closeFn=_this$props2.handleClick,hideArrow=_this$props2.hideArrow,setFloaterRef=_this$props2.setFloaterRef,status=_this$props2.status;var output={};var classes=['__floater'];if(component){if(/*#__PURE__*/React.isValidElement(component)){output.content=/*#__PURE__*/React.cloneElement(component,{closeFn:closeFn});}else {output.content=component({closeFn:closeFn});}}else {output.content=/*#__PURE__*/React.createElement(FloaterContainer,this.props);}if(status===STATUS.OPEN){classes.push('__floater__open');}if(!hideArrow){output.arrow=/*#__PURE__*/React.createElement(FloaterArrow,this.props);}return/*#__PURE__*/React.createElement(\"div\",{ref:setFloaterRef,className:classes.join(' '),style:this.style},/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__body\"},output.content,output.arrow));}}]);return Floater;}(React.Component);_defineProperty(Floater,\"propTypes\",{component:PropTypes.oneOfType([PropTypes.func,PropTypes.element]),content:PropTypes.node,disableAnimation:PropTypes.bool.isRequired,footer:PropTypes.node,handleClick:PropTypes.func.isRequired,hideArrow:PropTypes.bool.isRequired,open:PropTypes.bool,placement:PropTypes.string.isRequired,positionWrapper:PropTypes.bool.isRequired,setArrowRef:PropTypes.func.isRequired,setFloaterRef:PropTypes.func.isRequired,showCloseButton:PropTypes.bool,status:PropTypes.string.isRequired,styles:PropTypes.object.isRequired,title:PropTypes.node});\n\nvar ReactFloaterWrapper=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterWrapper,_React$Component);var _super=_createSuper(ReactFloaterWrapper);function ReactFloaterWrapper(){_classCallCheck(this,ReactFloaterWrapper);return _super.apply(this,arguments);}_createClass(ReactFloaterWrapper,[{key:\"render\",value:function render(){var _this$props=this.props,children=_this$props.children,handleClick=_this$props.handleClick,handleMouseEnter=_this$props.handleMouseEnter,handleMouseLeave=_this$props.handleMouseLeave,setChildRef=_this$props.setChildRef,setWrapperRef=_this$props.setWrapperRef,style=_this$props.style,styles=_this$props.styles;var element;/* istanbul ignore else */if(children){if(React.Children.count(children)===1){if(!/*#__PURE__*/React.isValidElement(children)){element=/*#__PURE__*/React.createElement(\"span\",null,children);}else {var refProp=is[\"function\"](children.type)?'innerRef':'ref';element=/*#__PURE__*/React.cloneElement(React.Children.only(children),_defineProperty({},refProp,setChildRef));}}else {element=children;}}if(!element){return null;}return/*#__PURE__*/React.createElement(\"span\",{ref:setWrapperRef,style:_objectSpread2(_objectSpread2({},styles),style),onClick:handleClick,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave},element);}}]);return ReactFloaterWrapper;}(React.Component);_defineProperty(ReactFloaterWrapper,\"propTypes\",{children:PropTypes.node,handleClick:PropTypes.func.isRequired,handleMouseEnter:PropTypes.func.isRequired,handleMouseLeave:PropTypes.func.isRequired,setChildRef:PropTypes.func.isRequired,setWrapperRef:PropTypes.func.isRequired,style:PropTypes.object,styles:PropTypes.object.isRequired});\n\nvar defaultOptions={zIndex:100};function getStyles(styles){var options=deepmerge(defaultOptions,styles.options||{});return {wrapper:{cursor:'help',display:'inline-flex',flexDirection:'column',zIndex:options.zIndex},wrapperPosition:{left:-1000,position:'absolute',top:-1000,visibility:'hidden'},floater:{display:'inline-block',filter:'drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))',maxWidth:300,opacity:0,position:'relative',transition:'opacity 0.3s',visibility:'hidden',zIndex:options.zIndex},floaterOpening:{opacity:1,visibility:'visible'},floaterWithAnimation:{opacity:1,transition:'opacity 0.3s, transform 0.2s',visibility:'visible'},floaterWithComponent:{maxWidth:'100%'},floaterClosing:{opacity:0,visibility:'visible'},floaterCentered:{left:'50%',position:'fixed',top:'50%',transform:'translate(-50%, -50%)'},container:{backgroundColor:'#fff',color:'#666',minHeight:60,minWidth:200,padding:20,position:'relative',zIndex:10},title:{borderBottom:'1px solid #555',color:'#555',fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:'transparent',border:0,borderRadius:0,color:'#555',fontSize:0,height:15,outline:'none',padding:10,position:'absolute',right:0,top:0,width:15,WebkitAppearance:'none'},footer:{borderTop:'1px solid #ccc',fontSize:13,marginTop:10,paddingTop:5},arrow:{color:'#fff',display:'inline-flex',length:16,margin:8,position:'absolute',spread:32},options:options};}\n\nvar _excluded$1=[\"arrow\",\"flip\",\"offset\"];var POSITIONING_PROPS=['position','top','right','bottom','left'];var ReactFloater=/*#__PURE__*/function(_React$Component){_inherits(ReactFloater,_React$Component);var _super=_createSuper(ReactFloater);function ReactFloater(props){var _this;_classCallCheck(this,ReactFloater);_this=_super.call(this,props);/* istanbul ignore else */_defineProperty(_assertThisInitialized(_this),\"setArrowRef\",function(ref){_this.arrowRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setChildRef\",function(ref){_this.childRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setFloaterRef\",function(ref){if(!_this.floaterRef){_this.floaterRef=ref;}});_defineProperty(_assertThisInitialized(_this),\"setWrapperRef\",function(ref){_this.wrapperRef=ref;});_defineProperty(_assertThisInitialized(_this),\"handleTransitionEnd\",function(){var status=_this.state.status;var callback=_this.props.callback;/* istanbul ignore else */if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}_this.setState({status:status===STATUS.OPENING?STATUS.OPEN:STATUS.IDLE},function(){var newStatus=_this.state.status;callback(newStatus===STATUS.OPEN?'open':'close',_this.props);});});_defineProperty(_assertThisInitialized(_this),\"handleClick\",function(){var _this$props=_this.props,event=_this$props.event,open=_this$props.open;if(is[\"boolean\"](open))return;var _this$state=_this.state,positionWrapper=_this$state.positionWrapper,status=_this$state.status;/* istanbul ignore else */if(_this.event==='click'||_this.event==='hover'&&positionWrapper){log({title:'click',data:[{event:event,status:status===STATUS.OPEN?'closing':'opening'}],debug:_this.debug});_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseEnter\",function(){var _this$props2=_this.props,event=_this$props2.event,open=_this$props2.open;if(is[\"boolean\"](open)||isMobile())return;var status=_this.state.status;/* istanbul ignore else */if(_this.event==='hover'&&status===STATUS.IDLE){log({title:'mouseEnter',data:[{key:'originalEvent',value:event}],debug:_this.debug});clearTimeout(_this.eventDelayTimeout);_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseLeave\",function(){var _this$props3=_this.props,event=_this$props3.event,eventDelay=_this$props3.eventDelay,open=_this$props3.open;if(is[\"boolean\"](open)||isMobile())return;var _this$state2=_this.state,status=_this$state2.status,positionWrapper=_this$state2.positionWrapper;/* istanbul ignore else */if(_this.event==='hover'){log({title:'mouseLeave',data:[{key:'originalEvent',value:event}],debug:_this.debug});if(!eventDelay){_this.toggle(STATUS.IDLE);}else if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1&&!positionWrapper&&!_this.eventDelayTimeout){_this.eventDelayTimeout=setTimeout(function(){delete _this.eventDelayTimeout;_this.toggle();},eventDelay*1000);}}});_this.state={currentPlacement:props.placement,positionWrapper:props.wrapperOptions.position&&!!props.target,status:STATUS.INIT,statusWrapper:STATUS.INIT};_this._isMounted=false;if(canUseDOM){window.addEventListener('load',function(){if(_this.popper){_this.popper.instance.update();}if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}});}return _this;}_createClass(ReactFloater,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM)return;var positionWrapper=this.state.positionWrapper;var _this$props5=this.props,children=_this$props5.children,open=_this$props5.open,target=_this$props5.target;this._isMounted=true;log({title:'init',data:{hasChildren:!!children,hasTarget:!!target,isControlled:is[\"boolean\"](open),positionWrapper:positionWrapper,target:this.target,floater:this.floaterRef},debug:this.debug});this.initPopper();if(!children&&target&&!is[\"boolean\"](open));}},{key:\"componentDidUpdate\",value:function componentDidUpdate(prevProps,prevState){if(!canUseDOM)return;var _this$props6=this.props,autoOpen=_this$props6.autoOpen,open=_this$props6.open,target=_this$props6.target,wrapperOptions=_this$props6.wrapperOptions;var _treeChanges=treeChanges(prevState,this.state),changedFrom=_treeChanges.changedFrom,changedTo=_treeChanges.changedTo;if(prevProps.open!==open){var forceStatus;// always follow `open` in controlled mode\nif(is[\"boolean\"](open)){forceStatus=open?STATUS.OPENING:STATUS.CLOSING;}this.toggle(forceStatus);}if(prevProps.wrapperOptions.position!==wrapperOptions.position||prevProps.target!==target){this.changeWrapperPosition(this.props);}if(changedTo('status',STATUS.IDLE)&&open){this.toggle(STATUS.OPEN);}else if(changedFrom('status',STATUS.INIT,STATUS.IDLE)&&autoOpen){this.toggle(STATUS.OPEN);}if(this.popper&&changedTo('status',STATUS.OPENING)){this.popper.instance.update();}if(this.floaterRef&&(changedTo('status',STATUS.OPENING)||changedTo('status',STATUS.CLOSING))){once(this.floaterRef,'transitionend',this.handleTransitionEnd);}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM)return;this._isMounted=false;if(this.popper){this.popper.instance.destroy();}if(this.wrapperPopper){this.wrapperPopper.instance.destroy();}}},{key:\"initPopper\",value:function initPopper(){var _this2=this;var target=arguments.length>0&&arguments[0]!==undefined?arguments[0]:this.target;var positionWrapper=this.state.positionWrapper;var _this$props7=this.props,disableFlip=_this$props7.disableFlip,getPopper=_this$props7.getPopper,hideArrow=_this$props7.hideArrow,offset=_this$props7.offset,placement=_this$props7.placement,wrapperOptions=_this$props7.wrapperOptions;var flipBehavior=placement==='top'||placement==='bottom'?'flip':['right','bottom-end','top-end','left','top-start','bottom-start'];/* istanbul ignore else */if(placement==='center'){this.setState({status:STATUS.IDLE});}else if(target&&this.floaterRef){var _this$options=this.options,arrow=_this$options.arrow,flip=_this$options.flip,offsetOptions=_this$options.offset,rest=_objectWithoutProperties(_this$options,_excluded$1);new Popper(target,this.floaterRef,{placement:placement,modifiers:_objectSpread2({arrow:_objectSpread2({enabled:!hideArrow,element:this.arrowRef},arrow),flip:_objectSpread2({enabled:!disableFlip,behavior:flipBehavior},flip),offset:_objectSpread2({offset:\"0, \".concat(offset,\"px\")},offsetOptions)},rest),onCreate:function onCreate(data){_this2.popper=data;getPopper(data,'floater');if(_this2._isMounted){_this2.setState({currentPlacement:data.placement,status:STATUS.IDLE});}if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}},onUpdate:function onUpdate(data){_this2.popper=data;var currentPlacement=_this2.state.currentPlacement;if(_this2._isMounted&&data.placement!==currentPlacement){_this2.setState({currentPlacement:data.placement});}}});}if(positionWrapper){var wrapperOffset=!is.undefined(wrapperOptions.offset)?wrapperOptions.offset:0;new Popper(this.target,this.wrapperRef,{placement:wrapperOptions.placement||placement,modifiers:{arrow:{enabled:false},offset:{offset:\"0, \".concat(wrapperOffset,\"px\")},flip:{enabled:false}},onCreate:function onCreate(data){_this2.wrapperPopper=data;if(_this2._isMounted){_this2.setState({statusWrapper:STATUS.IDLE});}getPopper(data,'wrapper');if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}}});}}},{key:\"changeWrapperPosition\",value:function changeWrapperPosition(_ref){var target=_ref.target,wrapperOptions=_ref.wrapperOptions;this.setState({positionWrapper:wrapperOptions.position&&!!target});}},{key:\"toggle\",value:function toggle(forceStatus){var status=this.state.status;var nextStatus=status===STATUS.OPEN?STATUS.CLOSING:STATUS.OPENING;if(!is.undefined(forceStatus)){nextStatus=forceStatus;}this.setState({status:nextStatus});}},{key:\"debug\",get:function get(){var debug=this.props.debug;return debug||!!global.ReactFloaterDebug;}},{key:\"event\",get:function get(){var _this$props8=this.props,disableHoverToClick=_this$props8.disableHoverToClick,event=_this$props8.event;if(event==='hover'&&isMobile()&&!disableHoverToClick){return 'click';}return event;}},{key:\"options\",get:function get(){var options=this.props.options;return deepmerge(DEFAULTS,options||{});}},{key:\"styles\",get:function get(){var _this3=this;var _this$state3=this.state,status=_this$state3.status,positionWrapper=_this$state3.positionWrapper,statusWrapper=_this$state3.statusWrapper;var styles=this.props.styles;var nextStyles=deepmerge(getStyles(styles),styles);if(positionWrapper){var wrapperStyles;if(!([STATUS.IDLE].indexOf(status)!==-1)||!([STATUS.IDLE].indexOf(statusWrapper)!==-1)){wrapperStyles=nextStyles.wrapperPosition;}else {wrapperStyles=this.wrapperPopper.styles;}nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),wrapperStyles);}/* istanbul ignore else */if(this.target){var targetStyles=window.getComputedStyle(this.target);/* istanbul ignore else */if(this.wrapperStyles){nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);}else if(!(['relative','static'].indexOf(targetStyles.position)!==-1)){this.wrapperStyles={};if(!positionWrapper){POSITIONING_PROPS.forEach(function(d){_this3.wrapperStyles[d]=targetStyles[d];});nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);this.target.style.position='relative';this.target.style.top='auto';this.target.style.right='auto';this.target.style.bottom='auto';this.target.style.left='auto';}}}return nextStyles;}},{key:\"target\",get:function get(){if(!canUseDOM)return null;var target=this.props.target;if(target){if(is.domElement(target)){return target;}return document.querySelector(target);}return this.childRef||this.wrapperRef;}},{key:\"render\",value:function render(){var _this$state4=this.state,currentPlacement=_this$state4.currentPlacement,positionWrapper=_this$state4.positionWrapper,status=_this$state4.status;var _this$props9=this.props,children=_this$props9.children,component=_this$props9.component,content=_this$props9.content,disableAnimation=_this$props9.disableAnimation,footer=_this$props9.footer,hideArrow=_this$props9.hideArrow,id=_this$props9.id,open=_this$props9.open,showCloseButton=_this$props9.showCloseButton,style=_this$props9.style,target=_this$props9.target,title=_this$props9.title;var wrapper=/*#__PURE__*/React.createElement(ReactFloaterWrapper,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:style,styles:this.styles.wrapper},children);var output={};if(positionWrapper){output.wrapperInPortal=wrapper;}else {output.wrapperAsChildren=wrapper;}return/*#__PURE__*/React.createElement(\"span\",null,/*#__PURE__*/React.createElement(ReactFloaterPortal,{hasChildren:!!children,id:id,placement:currentPlacement,setRef:this.setFloaterRef,target:target,zIndex:this.styles.options.zIndex},/*#__PURE__*/React.createElement(Floater,{component:component,content:content,disableAnimation:disableAnimation,footer:footer,handleClick:this.handleClick,hideArrow:hideArrow||currentPlacement==='center',open:open,placement:currentPlacement,positionWrapper:positionWrapper,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:showCloseButton,status:status,styles:this.styles,title:title}),output.wrapperInPortal),output.wrapperAsChildren);}}]);return ReactFloater;}(React.Component);_defineProperty(ReactFloater,\"propTypes\",{autoOpen:PropTypes.bool,callback:PropTypes.func,children:PropTypes.node,component:isRequiredIf(PropTypes.oneOfType([PropTypes.func,PropTypes.element]),function(props){return !props.content;}),content:isRequiredIf(PropTypes.node,function(props){return !props.component;}),debug:PropTypes.bool,disableAnimation:PropTypes.bool,disableFlip:PropTypes.bool,disableHoverToClick:PropTypes.bool,event:PropTypes.oneOf(['hover','click']),eventDelay:PropTypes.number,footer:PropTypes.node,getPopper:PropTypes.func,hideArrow:PropTypes.bool,id:PropTypes.oneOfType([PropTypes.string,PropTypes.number]),offset:PropTypes.number,open:PropTypes.bool,options:PropTypes.object,placement:PropTypes.oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto','center']),showCloseButton:PropTypes.bool,style:PropTypes.object,styles:PropTypes.object,target:PropTypes.oneOfType([PropTypes.object,PropTypes.string]),title:PropTypes.node,wrapperOptions:PropTypes.shape({offset:PropTypes.number,placement:PropTypes.oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto']),position:PropTypes.bool})});_defineProperty(ReactFloater,\"defaultProps\",{autoOpen:false,callback:noop,debug:false,disableAnimation:false,disableFlip:false,disableHoverToClick:false,event:'click',eventDelay:0.4,getPopper:noop,hideArrow:false,offset:15,placement:'bottom',showCloseButton:false,styles:{},target:null,wrapperOptions:{position:false}});\n\nexport default ReactFloater;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,EAAE,MAAM,SAAS;AACxB,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,oBAAoB,MAAM,OAAO;AAExC,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAElD,IAAIC,cAAc,EAAE;MAClBI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QACtC,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAChE,CAAC,CAAC;IACJ;IAEAP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAChC;EAEA,OAAOH,IAAI;AACb;AAEA,SAASU,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QACnDC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAC3ClB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC7ChB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASW,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACf,MAAM,EAAEgB,KAAK,EAAE;EACxC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IACzBgB,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IACtDqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IACrD7B,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEiB,UAAU,CAACX,GAAG,EAAEW,UAAU,CAAC;EAC3D;AACF;AAEA,SAASG,YAAYA,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEN,iBAAiB,CAACF,WAAW,CAACU,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEP,iBAAiB,CAACF,WAAW,EAAES,WAAW,CAAC;EAC5D,OAAOT,WAAW;AACpB;AAEA,SAASN,eAAeA,CAACiB,GAAG,EAAElB,GAAG,EAAEmB,KAAK,EAAE;EACxC,IAAInB,GAAG,IAAIkB,GAAG,EAAE;IACdlC,MAAM,CAACoB,cAAc,CAACc,GAAG,EAAElB,GAAG,EAAE;MAC9BmB,KAAK,EAAEA,KAAK;MACZ7B,UAAU,EAAE,IAAI;MAChBsB,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLK,GAAG,CAAClB,GAAG,CAAC,GAAGmB,KAAK;EAClB;EAEA,OAAOD,GAAG;AACZ;AAEA,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAId,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAa,QAAQ,CAACJ,SAAS,GAAGjC,MAAM,CAACuC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACL,SAAS,EAAE;IACrEO,WAAW,EAAE;MACXL,KAAK,EAAEE,QAAQ;MACfR,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIU,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,eAAeA,CAACC,CAAC,EAAE;EAC1BD,eAAe,GAAG1C,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAAC6C,cAAc,GAAG,SAASH,eAAeA,CAACC,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAI9C,MAAM,CAAC6C,cAAc,CAACF,CAAC,CAAC;EAChD,CAAC;EACD,OAAOD,eAAe,CAACC,CAAC,CAAC;AAC3B;AAEA,SAASF,eAAeA,CAACE,CAAC,EAAEI,CAAC,EAAE;EAC7BN,eAAe,GAAGzC,MAAM,CAAC4C,cAAc,IAAI,SAASH,eAAeA,CAACE,CAAC,EAAEI,CAAC,EAAE;IACxEJ,CAAC,CAACG,SAAS,GAAGC,CAAC;IACf,OAAOJ,CAAC;EACV,CAAC;EAED,OAAOF,eAAe,CAACE,CAAC,EAAEI,CAAC,CAAC;AAC9B;AAEA,SAASC,yBAAyBA,CAAA,EAAG;EACnC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EACtE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAE5C,IAAI;IACFC,OAAO,CAACpB,SAAS,CAACqB,OAAO,CAACC,IAAI,CAACN,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASC,6BAA6BA,CAAC3C,MAAM,EAAE4C,QAAQ,EAAE;EACvD,IAAI5C,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIiD,UAAU,GAAG3D,MAAM,CAACD,IAAI,CAACe,MAAM,CAAC;EACpC,IAAIE,GAAG,EAAEL,CAAC;EAEV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,UAAU,CAAC9C,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCK,GAAG,GAAG2C,UAAU,CAAChD,CAAC,CAAC;IACnB,IAAI+C,QAAQ,CAACE,OAAO,CAAC5C,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAC3B;EAEA,OAAON,MAAM;AACf;AAEA,SAASmD,wBAAwBA,CAAC/C,MAAM,EAAE4C,QAAQ,EAAE;EAClD,IAAI5C,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIJ,MAAM,GAAG+C,6BAA6B,CAAC3C,MAAM,EAAE4C,QAAQ,CAAC;EAE5D,IAAI1C,GAAG,EAAEL,CAAC;EAEV,IAAIX,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAI6D,gBAAgB,GAAG9D,MAAM,CAACC,qBAAqB,CAACa,MAAM,CAAC;IAE3D,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,gBAAgB,CAACjD,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CK,GAAG,GAAG8C,gBAAgB,CAACnD,CAAC,CAAC;MACzB,IAAI+C,QAAQ,CAACE,OAAO,CAAC5C,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAAChB,MAAM,CAACiC,SAAS,CAAC8B,oBAAoB,CAACR,IAAI,CAACzC,MAAM,EAAEE,GAAG,CAAC,EAAE;MAC9DN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC3B;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASsD,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,0BAA0BA,CAACF,IAAI,EAAEV,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACpE,OAAOA,IAAI;EACb;EAEA,OAAOS,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASG,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,yBAAyB,GAAGtB,yBAAyB,CAAC,CAAC;EAE3D,OAAO,SAASuB,oBAAoBA,CAAA,EAAG;IACrC,IAAIC,KAAK,GAAG9B,eAAe,CAAC2B,OAAO,CAAC;MAChCI,MAAM;IAEV,IAAIH,yBAAyB,EAAE;MAC7B,IAAII,SAAS,GAAGhC,eAAe,CAAC,IAAI,CAAC,CAACF,WAAW;MAEjDiC,MAAM,GAAGxB,OAAO,CAACC,SAAS,CAACsB,KAAK,EAAE5D,SAAS,EAAE8D,SAAS,CAAC;IACzD,CAAC,MAAM;MACLD,MAAM,GAAGD,KAAK,CAAChE,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;IACvC;IAEA,OAAOuD,0BAA0B,CAAC,IAAI,EAAEM,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,IAAIE,QAAQ,GAAG;EAACC,IAAI,EAAC;IAACC,OAAO,EAAC;EAAE,CAAC;EAACC,eAAe,EAAC;IAACD,OAAO,EAAC;EAAE;AAAC,CAAC;AAE/D,IAAIE,MAAM,GAAG;EAACC,IAAI,EAAC,MAAM;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,SAAS;EAACC,KAAK,EAAC;AAAO,CAAC;AAEpG,IAAIC,SAAS,GAAC3F,oBAAoB,CAAC2F,SAAS;AAAC,IAAIC,SAAS,GAAC7F,QAAQ,CAAC8F,YAAY,KAAGC,SAAS;AAAC,SAASC,QAAQA,CAAA,EAAE;EAAC,OAAO,cAAc,IAAGC,MAAM,IAAE,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;AAAC,CAAC;AACnL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAG,SAASC,GAAGA,CAACC,IAAI,EAAC;EAAC,IAAIC,KAAK,GAACD,IAAI,CAACC,KAAK;IAACC,IAAI,GAACF,IAAI,CAACE,IAAI;IAACC,SAAS,GAACH,IAAI,CAACI,IAAI;IAACA,IAAI,GAACD,SAAS,KAAG,KAAK,CAAC,GAAC,KAAK,GAACA,SAAS;IAACE,UAAU,GAACL,IAAI,CAACM,KAAK;IAACA,KAAK,GAACD,UAAU,KAAG,KAAK,CAAC,GAAC,KAAK,GAACA,UAAU,CAAC;EAA+B,IAAIE,KAAK,GAACH,IAAI,GAACI,OAAO,CAACJ,IAAI,IAAEI,OAAO,CAACC,KAAK,GAACD,OAAO,CAACT,GAAG;EAAC,IAAGO,KAAK,IAAEL,KAAK,IAAEC,IAAI,EAAC;IAACM,OAAO,CAACE,cAAc,CAAC,mBAAmB,CAACC,MAAM,CAACV,KAAK,CAAC,EAAC,qDAAqD,CAAC;IAAC,IAAGW,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,EAAC;MAACA,IAAI,CAACnF,OAAO,CAAC,UAAS+F,CAAC,EAAC;QAAC,IAAGtH,EAAE,CAACuH,WAAW,CAACD,CAAC,CAAC,IAAEA,CAAC,CAAC9F,GAAG,EAAC;UAACuF,KAAK,CAAC/F,KAAK,CAACgG,OAAO,EAAC,CAACM,CAAC,CAAC9F,GAAG,EAAC8F,CAAC,CAAC3E,KAAK,CAAC,CAAC;QAAC,CAAC,MAAK;UAACoE,KAAK,CAAC/F,KAAK,CAACgG,OAAO,EAAC,CAACM,CAAC,CAAC,CAAC;QAAC;MAAC,CAAC,CAAC;IAAC,CAAC,MAAK;MAACP,KAAK,CAAC/F,KAAK,CAACgG,OAAO,EAAC,CAACN,IAAI,CAAC,CAAC;IAAC;IAACM,OAAO,CAACQ,QAAQ,CAAC,CAAC;EAAC,CAAC;AAAmB;AAAC,SAASC,EAAEA,CAACC,OAAO,EAACC,KAAK,EAACC,EAAE,EAAC;EAAC,IAAIC,OAAO,GAACzG,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAG6E,SAAS,GAAC7E,SAAS,CAAC,CAAC,CAAC,GAAC,KAAK;EAACsG,OAAO,CAACI,gBAAgB,CAACH,KAAK,EAACC,EAAE,EAACC,OAAO,CAAC;AAAC;AAAC,SAASE,GAAGA,CAACL,OAAO,EAACC,KAAK,EAACC,EAAE,EAAC;EAAC,IAAIC,OAAO,GAACzG,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAG6E,SAAS,GAAC7E,SAAS,CAAC,CAAC,CAAC,GAAC,KAAK;EAACsG,OAAO,CAACM,mBAAmB,CAACL,KAAK,EAACC,EAAE,EAACC,OAAO,CAAC;AAAC;AAAC,SAASI,IAAIA,CAACP,OAAO,EAACC,KAAK,EAACC,EAAE,EAAC;EAAC,IAAIC,OAAO,GAACzG,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAG6E,SAAS,GAAC7E,SAAS,CAAC,CAAC,CAAC,GAAC,KAAK;EAAC,IAAI8G,OAAO,CAAC;EAC/hCA,OAAO,GAAC,SAASC,MAAMA,CAACnE,CAAC,EAAC;IAAC4D,EAAE,CAAC5D,CAAC,CAAC;IAAC+D,GAAG,CAACL,OAAO,EAACC,KAAK,EAACO,OAAO,CAAC;EAAC,CAAC;EAACT,EAAE,CAACC,OAAO,EAACC,KAAK,EAACO,OAAO,EAACL,OAAO,CAAC;AAAC;AAAC,SAASO,IAAIA,CAAA,EAAE,CAAC;AAEjH,IAAIC,kBAAkB,GAAC,aAAa,UAASC,gBAAgB,EAAC;EAAC1F,SAAS,CAACyF,kBAAkB,EAACC,gBAAgB,CAAC;EAAC,IAAIC,MAAM,GAAC3D,YAAY,CAACyD,kBAAkB,CAAC;EAAC,SAASA,kBAAkBA,CAACnG,KAAK,EAAC;IAAC,IAAIsG,KAAK;IAAC3G,eAAe,CAAC,IAAI,EAACwG,kBAAkB,CAAC;IAACG,KAAK,GAACD,MAAM,CAACxE,IAAI,CAAC,IAAI,EAAC7B,KAAK,CAAC;IAAC,IAAG,CAAC4D,SAAS,EAAC,OAAOnB,0BAA0B,CAAC6D,KAAK,CAAC;IAACA,KAAK,CAACC,IAAI,GAACC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAAC,IAAGzG,KAAK,CAAC0G,EAAE,EAAC;MAACJ,KAAK,CAACC,IAAI,CAACG,EAAE,GAAC1G,KAAK,CAAC0G,EAAE;IAAC;IAAC,IAAG1G,KAAK,CAAC2G,MAAM,EAAC;MAACL,KAAK,CAACC,IAAI,CAACK,KAAK,CAACD,MAAM,GAAC3G,KAAK,CAAC2G,MAAM;IAAC;IAACH,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACR,KAAK,CAACC,IAAI,CAAC;IAAC,OAAOD,KAAK;EAAC;EAAClG,YAAY,CAAC+F,kBAAkB,EAAC,CAAC;IAAC7G,GAAG,EAAC,mBAAmB;IAACmB,KAAK,EAAC,SAASsG,iBAAiBA,CAAA,EAAE;MAAC,IAAG,CAACnD,SAAS,EAAC;MAAO,IAAG,CAACC,SAAS,EAAC;QAAC,IAAI,CAACmD,YAAY,CAAC,CAAC;MAAC;IAAC;EAAC,CAAC,EAAC;IAAC1H,GAAG,EAAC,oBAAoB;IAACmB,KAAK,EAAC,SAASwG,kBAAkBA,CAAA,EAAE;MAAC,IAAG,CAACrD,SAAS,EAAC;MAAO,IAAG,CAACC,SAAS,EAAC;QAAC,IAAI,CAACmD,YAAY,CAAC,CAAC;MAAC;IAAC;EAAC,CAAC,EAAC;IAAC1H,GAAG,EAAC,sBAAsB;IAACmB,KAAK,EAAC,SAASyG,oBAAoBA,CAAA,EAAE;MAAC,IAAG,CAACtD,SAAS,IAAE,CAAC,IAAI,CAAC2C,IAAI,EAAC;MAAO,IAAG,CAAC1C,SAAS,EAAC;QAAC7F,QAAQ,CAACmJ,sBAAsB,CAAC,IAAI,CAACZ,IAAI,CAAC;MAAC;MAACC,QAAQ,CAACK,IAAI,CAACO,WAAW,CAAC,IAAI,CAACb,IAAI,CAAC;IAAC;EAAC,CAAC,EAAC;IAACjH,GAAG,EAAC,cAAc;IAACmB,KAAK,EAAC,SAASuG,YAAYA,CAAA,EAAE;MAAC,IAAG,CAACpD,SAAS,EAAC,OAAO,IAAI;MAAC,IAAIyD,WAAW,GAAC,IAAI,CAACrH,KAAK;QAACsH,QAAQ,GAACD,WAAW,CAACC,QAAQ;QAACC,MAAM,GAACF,WAAW,CAACE,MAAM,CAAC;MAA0B,IAAG1D,SAAS,EAAC;QAAC,OAAM,aAAa7F,QAAQ,CAAC8F,YAAY,CAACwD,QAAQ,EAAC,IAAI,CAACf,IAAI,CAAC;MAAC;MAAC,IAAIiB,MAAM,GAACxJ,QAAQ,CAACyJ,mCAAmC,CAAC,IAAI,EAACH,QAAQ,CAACnI,MAAM,GAAC,CAAC,GAAC,aAAa1B,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC,IAAI,EAACa,QAAQ,CAAC,GAACA,QAAQ,CAAC,CAAC,CAAC,EAAC,IAAI,CAACf,IAAI,CAAC;MAACgB,MAAM,CAACC,MAAM,CAAC;MAAC,OAAO,IAAI;IAAC;EAAC,CAAC,EAAC;IAAClI,GAAG,EAAC,eAAe;IAACmB,KAAK,EAAC,SAASiH,aAAaA,CAAA,EAAE;MAAC,IAAIC,YAAY,GAAC,IAAI,CAAC3H,KAAK;QAAC4H,WAAW,GAACD,YAAY,CAACC,WAAW;QAACC,SAAS,GAACF,YAAY,CAACE,SAAS;QAAC7I,MAAM,GAAC2I,YAAY,CAAC3I,MAAM;MAAC,IAAG,CAAC4I,WAAW,EAAC;QAAC,IAAG5I,MAAM,IAAE6I,SAAS,KAAG,QAAQ,EAAC;UAAC,OAAO,IAAI,CAACb,YAAY,CAAC,CAAC;QAAC;QAAC,OAAO,IAAI;MAAC;MAAC,OAAO,IAAI,CAACA,YAAY,CAAC,CAAC;IAAC;EAAC,CAAC,EAAC;IAAC1H,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASqH,MAAMA,CAAA,EAAE;MAAC,IAAG,CAACjE,SAAS,EAAC;QAAC,OAAO,IAAI;MAAC;MAAC,OAAO,IAAI,CAAC6D,aAAa,CAAC,CAAC;IAAC;EAAC,CAAC,CAAC,CAAC;EAAC,OAAOvB,kBAAkB;AAAC,CAAC,CAAC1I,KAAK,CAACsK,SAAS,CAAC;AAACxI,eAAe,CAAC4G,kBAAkB,EAAC,WAAW,EAAC;EAACmB,QAAQ,EAAC5J,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAAC8H,OAAO,EAAC9H,SAAS,CAACuK,KAAK,CAAC,CAAC;EAACL,WAAW,EAAClK,SAAS,CAACwK,IAAI;EAACxB,EAAE,EAAChJ,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACyK,MAAM,EAACzK,SAAS,CAAC0K,MAAM,CAAC,CAAC;EAACP,SAAS,EAACnK,SAAS,CAACyK,MAAM;EAACZ,MAAM,EAAC7J,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACtJ,MAAM,EAACtB,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACS,MAAM,EAACT,SAAS,CAACyK,MAAM,CAAC,CAAC;EAACxB,MAAM,EAACjJ,SAAS,CAAC0K;AAAM,CAAC,CAAC;AAE9qE,IAAIG,YAAY,GAAC,aAAa,UAASnC,gBAAgB,EAAC;EAAC1F,SAAS,CAAC6H,YAAY,EAACnC,gBAAgB,CAAC;EAAC,IAAIC,MAAM,GAAC3D,YAAY,CAAC6F,YAAY,CAAC;EAAC,SAASA,YAAYA,CAAA,EAAE;IAAC5I,eAAe,CAAC,IAAI,EAAC4I,YAAY,CAAC;IAAC,OAAOlC,MAAM,CAACvH,KAAK,CAAC,IAAI,EAACI,SAAS,CAAC;EAAC;EAACkB,YAAY,CAACmI,YAAY,EAAC,CAAC;IAACjJ,GAAG,EAAC,aAAa;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAInB,WAAW,GAAC,IAAI,CAACrH,KAAK;QAAC6H,SAAS,GAACR,WAAW,CAACQ,SAAS;QAACY,MAAM,GAACpB,WAAW,CAACoB,MAAM;MAAC,IAAItJ,MAAM,GAACsJ,MAAM,CAACC,KAAK,CAACvJ,MAAM;MAAC,IAAIuJ,KAAK,GAAC;QAACC,aAAa,EAAC,MAAM;QAACC,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC;MAAM,CAAC,CAAC;MAA0B,IAAGhB,SAAS,CAACiB,UAAU,CAAC,KAAK,CAAC,EAAC;QAACJ,KAAK,CAACK,MAAM,GAAC,CAAC;QAACL,KAAK,CAACM,IAAI,GAAC,CAAC;QAACN,KAAK,CAACO,KAAK,GAAC,CAAC;QAACP,KAAK,CAACQ,MAAM,GAAC/J,MAAM;MAAC,CAAC,MAAK,IAAG0I,SAAS,CAACiB,UAAU,CAAC,QAAQ,CAAC,EAAC;QAACJ,KAAK,CAACM,IAAI,GAAC,CAAC;QAACN,KAAK,CAACO,KAAK,GAAC,CAAC;QAACP,KAAK,CAACS,GAAG,GAAC,CAAC;QAACT,KAAK,CAACQ,MAAM,GAAC/J,MAAM;MAAC,CAAC,MAAK,IAAG0I,SAAS,CAACiB,UAAU,CAAC,MAAM,CAAC,EAAC;QAACJ,KAAK,CAACO,KAAK,GAAC,CAAC;QAACP,KAAK,CAACS,GAAG,GAAC,CAAC;QAACT,KAAK,CAACK,MAAM,GAAC,CAAC;MAAC,CAAC,MAAK,IAAGlB,SAAS,CAACiB,UAAU,CAAC,OAAO,CAAC,EAAC;QAACJ,KAAK,CAACM,IAAI,GAAC,CAAC;QAACN,KAAK,CAACS,GAAG,GAAC,CAAC;MAAC;MAAC,OAAOT,KAAK;IAAC;EAAC,CAAC,EAAC;IAACpJ,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASqH,MAAMA,CAAA,EAAE;MAAC,IAAIH,YAAY,GAAC,IAAI,CAAC3H,KAAK;QAAC6H,SAAS,GAACF,YAAY,CAACE,SAAS;QAACuB,WAAW,GAACzB,YAAY,CAACyB,WAAW;QAACX,MAAM,GAACd,YAAY,CAACc,MAAM;MAAC,IAAIY,aAAa,GAACZ,MAAM,CAACC,KAAK;QAACY,KAAK,GAACD,aAAa,CAACC,KAAK;QAACC,OAAO,GAACF,aAAa,CAACE,OAAO;QAACpK,MAAM,GAACkK,aAAa,CAAClK,MAAM;QAACqK,MAAM,GAACH,aAAa,CAACG,MAAM;QAACZ,QAAQ,GAACS,aAAa,CAACT,QAAQ;QAACa,MAAM,GAACJ,aAAa,CAACI,MAAM;MAAC,IAAIC,WAAW,GAAC;QAACH,OAAO,EAACA,OAAO;QAACX,QAAQ,EAACA;MAAQ,CAAC;MAAC,IAAIe,MAAM;MAAC,IAAIC,CAAC,GAACH,MAAM;MAAC,IAAII,CAAC,GAAC1K,MAAM,CAAC;MAA0B,IAAG0I,SAAS,CAACiB,UAAU,CAAC,KAAK,CAAC,EAAC;QAACa,MAAM,GAAC,MAAM,CAAC1E,MAAM,CAAC2E,CAAC,GAAC,CAAC,EAAC,GAAG,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,EAAC,GAAG,CAAC,CAAC5E,MAAM,CAAC2E,CAAC,EAAC,IAAI,CAAC;QAACF,WAAW,CAACX,MAAM,GAAC,CAAC;QAACW,WAAW,CAACI,UAAU,GAACN,MAAM;QAACE,WAAW,CAACK,WAAW,GAACP,MAAM;MAAC,CAAC,MAAK,IAAG3B,SAAS,CAACiB,UAAU,CAAC,QAAQ,CAAC,EAAC;QAACa,MAAM,GAAC,EAAE,CAAC1E,MAAM,CAAC2E,CAAC,EAAC,GAAG,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,EAAC,GAAG,CAAC,CAAC5E,MAAM,CAAC2E,CAAC,GAAC,CAAC,EAAC,OAAO,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,CAAC;QAACH,WAAW,CAACP,GAAG,GAAC,CAAC;QAACO,WAAW,CAACI,UAAU,GAACN,MAAM;QAACE,WAAW,CAACK,WAAW,GAACP,MAAM;MAAC,CAAC,MAAK,IAAG3B,SAAS,CAACiB,UAAU,CAAC,MAAM,CAAC,EAAC;QAACe,CAAC,GAACJ,MAAM;QAACG,CAAC,GAACzK,MAAM;QAACwK,MAAM,GAAC,MAAM,CAAC1E,MAAM,CAAC2E,CAAC,EAAC,GAAG,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,GAAC,CAAC,EAAC,KAAK,CAAC,CAAC5E,MAAM,CAAC4E,CAAC,CAAC;QAACH,WAAW,CAACT,KAAK,GAAC,CAAC;QAACS,WAAW,CAACM,SAAS,GAACR,MAAM;QAACE,WAAW,CAACO,YAAY,GAACT,MAAM;MAAC,CAAC,MAAK,IAAG3B,SAAS,CAACiB,UAAU,CAAC,OAAO,CAAC,EAAC;QAACe,CAAC,GAACJ,MAAM;QAACG,CAAC,GAACzK,MAAM;QAACwK,MAAM,GAAC,EAAE,CAAC1E,MAAM,CAAC2E,CAAC,EAAC,GAAG,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,EAAC,GAAG,CAAC,CAAC5E,MAAM,CAAC2E,CAAC,EAAC,OAAO,CAAC,CAAC3E,MAAM,CAAC4E,CAAC,GAAC,CAAC,CAAC;QAACH,WAAW,CAACV,IAAI,GAAC,CAAC;QAACU,WAAW,CAACM,SAAS,GAACR,MAAM;QAACE,WAAW,CAACO,YAAY,GAACT,MAAM;MAAC;MAAC,OAAM,aAAa/L,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;QAACyD,SAAS,EAAC,kBAAkB;QAACtD,KAAK,EAAC,IAAI,CAACuD;MAAW,CAAC,EAAC,aAAa1M,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAC;QAAC2D,GAAG,EAAChB,WAAW;QAACxC,KAAK,EAAC8C;MAAW,CAAC,EAAC,aAAajM,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;QAACoC,KAAK,EAACe,CAAC;QAACV,MAAM,EAACW,CAAC;QAACQ,OAAO,EAAC,KAAK;QAACC,KAAK,EAAC;MAA4B,CAAC,EAAC,aAAa7M,KAAK,CAACgJ,aAAa,CAAC,SAAS,EAAC;QAACkD,MAAM,EAACA,MAAM;QAACY,IAAI,EAACjB;MAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;EAAC,CAAC,CAAC,CAAC;EAAC,OAAOf,YAAY;AAAC,CAAC,CAAC9K,KAAK,CAACsK,SAAS,CAAC;AAACxI,eAAe,CAACgJ,YAAY,EAAC,WAAW,EAAC;EAACV,SAAS,EAACnK,SAAS,CAACyK,MAAM,CAACG,UAAU;EAACc,WAAW,EAAC1L,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACG,MAAM,EAAC/K,SAAS,CAACS,MAAM,CAACmK;AAAU,CAAC,CAAC;AAElmF,IAAIkC,SAAS,GAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC;AAAC,IAAIC,eAAe,GAAC,SAASA,eAAeA,CAACnG,IAAI,EAAC;EAAC,IAAIoG,WAAW,GAACpG,IAAI,CAACoG,WAAW;IAACjC,MAAM,GAACnE,IAAI,CAACmE,MAAM;EAAC,IAAIa,KAAK,GAACb,MAAM,CAACa,KAAK;IAACJ,MAAM,GAACT,MAAM,CAACS,MAAM;IAACL,KAAK,GAACJ,MAAM,CAACI,KAAK;IAACjC,KAAK,GAACzE,wBAAwB,CAACsG,MAAM,EAAC+B,SAAS,CAAC;EAAC,OAAM,aAAa/M,KAAK,CAACgJ,aAAa,CAAC,QAAQ,EAAC;IAAC,YAAY,EAAC,OAAO;IAACkE,OAAO,EAACD,WAAW;IAAC9D,KAAK,EAACA,KAAK;IAACgE,IAAI,EAAC;EAAQ,CAAC,EAAC,aAAanN,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;IAACoC,KAAK,EAAC,EAAE,CAAC5D,MAAM,CAAC4D,KAAK,EAAC,IAAI,CAAC;IAACK,MAAM,EAAC,EAAE,CAACjE,MAAM,CAACiE,MAAM,EAAC,IAAI,CAAC;IAAC2B,OAAO,EAAC,WAAW;IAACR,OAAO,EAAC,KAAK;IAACC,KAAK,EAAC,4BAA4B;IAACQ,mBAAmB,EAAC;EAAU,CAAC,EAAC,aAAarN,KAAK,CAACgJ,aAAa,CAAC,GAAG,EAAC,IAAI,EAAC,aAAahJ,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAC;IAACrB,CAAC,EAAC,86BAA86B;IAACmF,IAAI,EAACjB;EAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAACmB,eAAe,CAACM,SAAS,GAAC;EAACL,WAAW,EAAChN,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACG,MAAM,EAAC/K,SAAS,CAACS,MAAM,CAACmK;AAAU,CAAC;AAEtrD,IAAI0C,gBAAgB,GAAC,SAASA,gBAAgBA,CAAC1G,IAAI,EAAC;EAAC,IAAI2G,OAAO,GAAC3G,IAAI,CAAC2G,OAAO;IAACC,MAAM,GAAC5G,IAAI,CAAC4G,MAAM;IAACR,WAAW,GAACpG,IAAI,CAACoG,WAAW;IAACS,IAAI,GAAC7G,IAAI,CAAC6G,IAAI;IAACC,eAAe,GAAC9G,IAAI,CAAC8G,eAAe;IAACC,eAAe,GAAC/G,IAAI,CAAC+G,eAAe;IAAC9G,KAAK,GAACD,IAAI,CAACC,KAAK;IAACkE,MAAM,GAACnE,IAAI,CAACmE,MAAM;EAAC,IAAI6C,MAAM,GAAC;IAACL,OAAO,EAAC,aAAaxN,KAAK,CAAC8N,cAAc,CAACN,OAAO,CAAC,GAACA,OAAO,GAAC,aAAaxN,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;MAACyD,SAAS,EAAC,oBAAoB;MAACtD,KAAK,EAAC6B,MAAM,CAACwC;IAAO,CAAC,EAACA,OAAO;EAAC,CAAC;EAAC,IAAG1G,KAAK,EAAC;IAAC+G,MAAM,CAAC/G,KAAK,GAAC,aAAa9G,KAAK,CAAC8N,cAAc,CAAChH,KAAK,CAAC,GAACA,KAAK,GAAC,aAAa9G,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;MAACyD,SAAS,EAAC,kBAAkB;MAACtD,KAAK,EAAC6B,MAAM,CAAClE;IAAK,CAAC,EAACA,KAAK,CAAC;EAAC;EAAC,IAAG2G,MAAM,EAAC;IAACI,MAAM,CAACJ,MAAM,GAAC,aAAazN,KAAK,CAAC8N,cAAc,CAACL,MAAM,CAAC,GAACA,MAAM,GAAC,aAAazN,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;MAACyD,SAAS,EAAC,mBAAmB;MAACtD,KAAK,EAAC6B,MAAM,CAACyC;IAAM,CAAC,EAACA,MAAM,CAAC;EAAC;EAAC,IAAG,CAACG,eAAe,IAAED,eAAe,KAAG,CAACtN,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,EAAC;IAACG,MAAM,CAACE,KAAK,GAAC,aAAa/N,KAAK,CAACgJ,aAAa,CAACgE,eAAe,EAAC;MAAChC,MAAM,EAACA,MAAM,CAAC+C,KAAK;MAACd,WAAW,EAACA;IAAW,CAAC,CAAC;EAAC;EAAC,OAAM,aAAajN,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;IAACyD,SAAS,EAAC,sBAAsB;IAACtD,KAAK,EAAC6B,MAAM,CAACgD;EAAS,CAAC,EAACH,MAAM,CAACE,KAAK,EAACF,MAAM,CAAC/G,KAAK,EAAC+G,MAAM,CAACL,OAAO,EAACK,MAAM,CAACJ,MAAM,CAAC;AAAC,CAAC;AAACF,gBAAgB,CAACD,SAAS,GAAC;EAACE,OAAO,EAACvN,SAAS,CAAC6I,IAAI,CAAC+B,UAAU;EAAC4C,MAAM,EAACxN,SAAS,CAAC6I,IAAI;EAACmE,WAAW,EAAChN,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAAC6C,IAAI,EAACzN,SAAS,CAACwK,IAAI;EAACkD,eAAe,EAAC1N,SAAS,CAACwK,IAAI,CAACI,UAAU;EAAC+C,eAAe,EAAC3N,SAAS,CAACwK,IAAI,CAACI,UAAU;EAACG,MAAM,EAAC/K,SAAS,CAACS,MAAM,CAACmK,UAAU;EAAC/D,KAAK,EAAC7G,SAAS,CAAC6I;AAAI,CAAC;AAEp2C,IAAImF,OAAO,GAAC,aAAa,UAAStF,gBAAgB,EAAC;EAAC1F,SAAS,CAACgL,OAAO,EAACtF,gBAAgB,CAAC;EAAC,IAAIC,MAAM,GAAC3D,YAAY,CAACgJ,OAAO,CAAC;EAAC,SAASA,OAAOA,CAAA,EAAE;IAAC/L,eAAe,CAAC,IAAI,EAAC+L,OAAO,CAAC;IAAC,OAAOrF,MAAM,CAACvH,KAAK,CAAC,IAAI,EAACI,SAAS,CAAC;EAAC;EAACkB,YAAY,CAACsL,OAAO,EAAC,CAAC;IAACpM,GAAG,EAAC,OAAO;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAInB,WAAW,GAAC,IAAI,CAACrH,KAAK;QAAC2L,gBAAgB,GAACtE,WAAW,CAACsE,gBAAgB;QAACC,SAAS,GAACvE,WAAW,CAACuE,SAAS;QAAC/D,SAAS,GAACR,WAAW,CAACQ,SAAS;QAACgE,SAAS,GAACxE,WAAW,CAACwE,SAAS;QAACC,MAAM,GAACzE,WAAW,CAACyE,MAAM;QAACrD,MAAM,GAACpB,WAAW,CAACoB,MAAM;MAAC,IAAItJ,MAAM,GAACsJ,MAAM,CAACC,KAAK,CAACvJ,MAAM;QAAC4M,OAAO,GAACtD,MAAM,CAACsD,OAAO;QAACC,eAAe,GAACvD,MAAM,CAACuD,eAAe;QAACC,cAAc,GAACxD,MAAM,CAACwD,cAAc;QAACC,cAAc,GAACzD,MAAM,CAACyD,cAAc;QAACC,oBAAoB,GAAC1D,MAAM,CAAC0D,oBAAoB;QAACC,oBAAoB,GAAC3D,MAAM,CAAC2D,oBAAoB;MAAC,IAAI5G,OAAO,GAAC,CAAC,CAAC;MAAC,IAAG,CAACqG,SAAS,EAAC;QAAC,IAAGhE,SAAS,CAACiB,UAAU,CAAC,KAAK,CAAC,EAAC;UAACtD,OAAO,CAACrC,OAAO,GAAC,MAAM,CAAC8B,MAAM,CAAC9F,MAAM,EAAC,IAAI,CAAC;QAAC,CAAC,MAAK,IAAG0I,SAAS,CAACiB,UAAU,CAAC,QAAQ,CAAC,EAAC;UAACtD,OAAO,CAACrC,OAAO,GAAC,EAAE,CAAC8B,MAAM,CAAC9F,MAAM,EAAC,QAAQ,CAAC;QAAC,CAAC,MAAK,IAAG0I,SAAS,CAACiB,UAAU,CAAC,MAAM,CAAC,EAAC;UAACtD,OAAO,CAACrC,OAAO,GAAC,IAAI,CAAC8B,MAAM,CAAC9F,MAAM,EAAC,QAAQ,CAAC;QAAC,CAAC,MAAK,IAAG0I,SAAS,CAACiB,UAAU,CAAC,OAAO,CAAC,EAAC;UAACtD,OAAO,CAACrC,OAAO,GAAC,QAAQ,CAAC8B,MAAM,CAAC9F,MAAM,EAAC,IAAI,CAAC;QAAC;MAAC;MAAC,IAAG,CAACkE,MAAM,CAACG,OAAO,EAACH,MAAM,CAACI,IAAI,CAAC,CAACvB,OAAO,CAAC4J,MAAM,CAAC,KAAG,CAAC,CAAC,EAAC;QAACtG,OAAO,GAACzG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACyG,OAAO,CAAC,EAAC0G,cAAc,CAAC;MAAC;MAAC,IAAGJ,MAAM,KAAGzI,MAAM,CAACK,OAAO,EAAC;QAAC8B,OAAO,GAACzG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACyG,OAAO,CAAC,EAACyG,cAAc,CAAC;MAAC;MAAC,IAAGH,MAAM,KAAGzI,MAAM,CAACI,IAAI,IAAE,CAACkI,gBAAgB,EAAC;QAACnG,OAAO,GAACzG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACyG,OAAO,CAAC,EAAC2G,oBAAoB,CAAC;MAAC;MAAC,IAAGtE,SAAS,KAAG,QAAQ,EAAC;QAACrC,OAAO,GAACzG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACyG,OAAO,CAAC,EAACwG,eAAe,CAAC;MAAC;MAAC,IAAGJ,SAAS,EAAC;QAACpG,OAAO,GAACzG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACyG,OAAO,CAAC,EAAC4G,oBAAoB,CAAC;MAAC;MAAC,OAAOrN,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAACgN,OAAO,CAAC,EAACvG,OAAO,CAAC;IAAC;EAAC,CAAC,EAAC;IAAClG,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASqH,MAAMA,CAAA,EAAE;MAAC,IAAIH,YAAY,GAAC,IAAI,CAAC3H,KAAK;QAAC4L,SAAS,GAACjE,YAAY,CAACiE,SAAS;QAACS,OAAO,GAAC1E,YAAY,CAAC+C,WAAW;QAACmB,SAAS,GAAClE,YAAY,CAACkE,SAAS;QAACS,aAAa,GAAC3E,YAAY,CAAC2E,aAAa;QAACR,MAAM,GAACnE,YAAY,CAACmE,MAAM;MAAC,IAAIR,MAAM,GAAC,CAAC,CAAC;MAAC,IAAIiB,OAAO,GAAC,CAAC,WAAW,CAAC;MAAC,IAAGX,SAAS,EAAC;QAAC,IAAG,aAAanO,KAAK,CAAC8N,cAAc,CAACK,SAAS,CAAC,EAAC;UAACN,MAAM,CAACL,OAAO,GAAC,aAAaxN,KAAK,CAAC+O,YAAY,CAACZ,SAAS,EAAC;YAACS,OAAO,EAACA;UAAO,CAAC,CAAC;QAAC,CAAC,MAAK;UAACf,MAAM,CAACL,OAAO,GAACW,SAAS,CAAC;YAACS,OAAO,EAACA;UAAO,CAAC,CAAC;QAAC;MAAC,CAAC,MAAK;QAACf,MAAM,CAACL,OAAO,GAAC,aAAaxN,KAAK,CAACgJ,aAAa,CAACuE,gBAAgB,EAAC,IAAI,CAAChL,KAAK,CAAC;MAAC;MAAC,IAAG8L,MAAM,KAAGzI,MAAM,CAACI,IAAI,EAAC;QAAC8I,OAAO,CAAC1N,IAAI,CAAC,iBAAiB,CAAC;MAAC;MAAC,IAAG,CAACgN,SAAS,EAAC;QAACP,MAAM,CAAC5C,KAAK,GAAC,aAAajL,KAAK,CAACgJ,aAAa,CAAC8B,YAAY,EAAC,IAAI,CAACvI,KAAK,CAAC;MAAC;MAAC,OAAM,aAAavC,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;QAAC2D,GAAG,EAACkC,aAAa;QAACpC,SAAS,EAACqC,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;QAAC7F,KAAK,EAAC,IAAI,CAACA;MAAK,CAAC,EAAC,aAAanJ,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAC;QAACyD,SAAS,EAAC;MAAiB,CAAC,EAACoB,MAAM,CAACL,OAAO,EAACK,MAAM,CAAC5C,KAAK,CAAC,CAAC;IAAC;EAAC,CAAC,CAAC,CAAC;EAAC,OAAOgD,OAAO;AAAC,CAAC,CAACjO,KAAK,CAACsK,SAAS,CAAC;AAACxI,eAAe,CAACmM,OAAO,EAAC,WAAW,EAAC;EAACE,SAAS,EAAClO,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAAC2K,IAAI,EAAC3K,SAAS,CAAC8H,OAAO,CAAC,CAAC;EAACyF,OAAO,EAACvN,SAAS,CAAC6I,IAAI;EAACoF,gBAAgB,EAACjO,SAAS,CAACwK,IAAI,CAACI,UAAU;EAAC4C,MAAM,EAACxN,SAAS,CAAC6I,IAAI;EAACmE,WAAW,EAAChN,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACuD,SAAS,EAACnO,SAAS,CAACwK,IAAI,CAACI,UAAU;EAAC6C,IAAI,EAACzN,SAAS,CAACwK,IAAI;EAACL,SAAS,EAACnK,SAAS,CAACyK,MAAM,CAACG,UAAU;EAAC8C,eAAe,EAAC1N,SAAS,CAACwK,IAAI,CAACI,UAAU;EAACc,WAAW,EAAC1L,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACgE,aAAa,EAAC5O,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAAC+C,eAAe,EAAC3N,SAAS,CAACwK,IAAI;EAAC4D,MAAM,EAACpO,SAAS,CAACyK,MAAM,CAACG,UAAU;EAACG,MAAM,EAAC/K,SAAS,CAACS,MAAM,CAACmK,UAAU;EAAC/D,KAAK,EAAC7G,SAAS,CAAC6I;AAAI,CAAC,CAAC;AAErnG,IAAImG,mBAAmB,GAAC,aAAa,UAAStG,gBAAgB,EAAC;EAAC1F,SAAS,CAACgM,mBAAmB,EAACtG,gBAAgB,CAAC;EAAC,IAAIC,MAAM,GAAC3D,YAAY,CAACgK,mBAAmB,CAAC;EAAC,SAASA,mBAAmBA,CAAA,EAAE;IAAC/M,eAAe,CAAC,IAAI,EAAC+M,mBAAmB,CAAC;IAAC,OAAOrG,MAAM,CAACvH,KAAK,CAAC,IAAI,EAACI,SAAS,CAAC;EAAC;EAACkB,YAAY,CAACsM,mBAAmB,EAAC,CAAC;IAACpN,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASqH,MAAMA,CAAA,EAAE;MAAC,IAAIT,WAAW,GAAC,IAAI,CAACrH,KAAK;QAACsH,QAAQ,GAACD,WAAW,CAACC,QAAQ;QAACoD,WAAW,GAACrD,WAAW,CAACqD,WAAW;QAACiC,gBAAgB,GAACtF,WAAW,CAACsF,gBAAgB;QAACC,gBAAgB,GAACvF,WAAW,CAACuF,gBAAgB;QAACC,WAAW,GAACxF,WAAW,CAACwF,WAAW;QAACC,aAAa,GAACzF,WAAW,CAACyF,aAAa;QAAClG,KAAK,GAACS,WAAW,CAACT,KAAK;QAAC6B,MAAM,GAACpB,WAAW,CAACoB,MAAM;MAAC,IAAIjD,OAAO,CAAC;MAA0B,IAAG8B,QAAQ,EAAC;QAAC,IAAG7J,KAAK,CAACsP,QAAQ,CAACC,KAAK,CAAC1F,QAAQ,CAAC,KAAG,CAAC,EAAC;UAAC,IAAG,EAAC,aAAa7J,KAAK,CAAC8N,cAAc,CAACjE,QAAQ,CAAC,EAAC;YAAC9B,OAAO,GAAC,aAAa/H,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAC,IAAI,EAACa,QAAQ,CAAC;UAAC,CAAC,MAAK;YAAC,IAAI2F,OAAO,GAACnP,EAAE,CAAC,UAAU,CAAC,CAACwJ,QAAQ,CAACsD,IAAI,CAAC,GAAC,UAAU,GAAC,KAAK;YAACpF,OAAO,GAAC,aAAa/H,KAAK,CAAC+O,YAAY,CAAC/O,KAAK,CAACsP,QAAQ,CAACG,IAAI,CAAC5F,QAAQ,CAAC,EAAC/H,eAAe,CAAC,CAAC,CAAC,EAAC0N,OAAO,EAACJ,WAAW,CAAC,CAAC;UAAC;QAAC,CAAC,MAAK;UAACrH,OAAO,GAAC8B,QAAQ;QAAC;MAAC;MAAC,IAAG,CAAC9B,OAAO,EAAC;QAAC,OAAO,IAAI;MAAC;MAAC,OAAM,aAAa/H,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAC;QAAC2D,GAAG,EAAC0C,aAAa;QAAClG,KAAK,EAAC7H,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAC0J,MAAM,CAAC,EAAC7B,KAAK,CAAC;QAAC+D,OAAO,EAACD,WAAW;QAACyC,YAAY,EAACR,gBAAgB;QAACS,YAAY,EAACR;MAAgB,CAAC,EAACpH,OAAO,CAAC;IAAC;EAAC,CAAC,CAAC,CAAC;EAAC,OAAOkH,mBAAmB;AAAC,CAAC,CAACjP,KAAK,CAACsK,SAAS,CAAC;AAACxI,eAAe,CAACmN,mBAAmB,EAAC,WAAW,EAAC;EAACpF,QAAQ,EAAC5J,SAAS,CAAC6I,IAAI;EAACmE,WAAW,EAAChN,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACqE,gBAAgB,EAACjP,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACsE,gBAAgB,EAAClP,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACuE,WAAW,EAACnP,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAACwE,aAAa,EAACpP,SAAS,CAAC2K,IAAI,CAACC,UAAU;EAAC1B,KAAK,EAAClJ,SAAS,CAACS,MAAM;EAACsK,MAAM,EAAC/K,SAAS,CAACS,MAAM,CAACmK;AAAU,CAAC,CAAC;AAE5oD,IAAI+E,cAAc,GAAC;EAAC1G,MAAM,EAAC;AAAG,CAAC;AAAC,SAAS2G,SAASA,CAAC7E,MAAM,EAAC;EAAC,IAAI8E,OAAO,GAAC1P,SAAS,CAACwP,cAAc,EAAC5E,MAAM,CAAC8E,OAAO,IAAE,CAAC,CAAC,CAAC;EAAC,OAAO;IAACC,OAAO,EAAC;MAACC,MAAM,EAAC,MAAM;MAAClE,OAAO,EAAC,aAAa;MAACmE,aAAa,EAAC,QAAQ;MAAC/G,MAAM,EAAC4G,OAAO,CAAC5G;IAAM,CAAC;IAACgH,eAAe,EAAC;MAAC3E,IAAI,EAAC,CAAC,IAAI;MAACJ,QAAQ,EAAC,UAAU;MAACO,GAAG,EAAC,CAAC,IAAI;MAACyE,UAAU,EAAC;IAAQ,CAAC;IAAC7B,OAAO,EAAC;MAACxC,OAAO,EAAC,cAAc;MAAC9K,MAAM,EAAC,yCAAyC;MAACoP,QAAQ,EAAC,GAAG;MAACC,OAAO,EAAC,CAAC;MAAClF,QAAQ,EAAC,UAAU;MAACmF,UAAU,EAAC,cAAc;MAACH,UAAU,EAAC,QAAQ;MAACjH,MAAM,EAAC4G,OAAO,CAAC5G;IAAM,CAAC;IAACuF,cAAc,EAAC;MAAC4B,OAAO,EAAC,CAAC;MAACF,UAAU,EAAC;IAAS,CAAC;IAACzB,oBAAoB,EAAC;MAAC2B,OAAO,EAAC,CAAC;MAACC,UAAU,EAAC,8BAA8B;MAACH,UAAU,EAAC;IAAS,CAAC;IAACxB,oBAAoB,EAAC;MAACyB,QAAQ,EAAC;IAAM,CAAC;IAAC5B,cAAc,EAAC;MAAC6B,OAAO,EAAC,CAAC;MAACF,UAAU,EAAC;IAAS,CAAC;IAAC5B,eAAe,EAAC;MAAChD,IAAI,EAAC,KAAK;MAACJ,QAAQ,EAAC,OAAO;MAACO,GAAG,EAAC,KAAK;MAAC6E,SAAS,EAAC;IAAuB,CAAC;IAACvC,SAAS,EAAC;MAACwC,eAAe,EAAC,MAAM;MAAC3E,KAAK,EAAC,MAAM;MAAC4E,SAAS,EAAC,EAAE;MAACC,QAAQ,EAAC,GAAG;MAAChL,OAAO,EAAC,EAAE;MAACyF,QAAQ,EAAC,UAAU;MAACjC,MAAM,EAAC;IAAE,CAAC;IAACpC,KAAK,EAAC;MAAC6J,YAAY,EAAC,gBAAgB;MAAC9E,KAAK,EAAC,MAAM;MAAC+E,QAAQ,EAAC,EAAE;MAACpE,YAAY,EAAC,CAAC;MAACqE,aAAa,EAAC,CAAC;MAACC,YAAY,EAAC;IAAE,CAAC;IAACtD,OAAO,EAAC;MAACoD,QAAQ,EAAC;IAAE,CAAC;IAAC7C,KAAK,EAAC;MAACyC,eAAe,EAAC,aAAa;MAACO,MAAM,EAAC,CAAC;MAACC,YAAY,EAAC,CAAC;MAACnF,KAAK,EAAC,MAAM;MAAC+E,QAAQ,EAAC,CAAC;MAACnF,MAAM,EAAC,EAAE;MAACwF,OAAO,EAAC,MAAM;MAACvL,OAAO,EAAC,EAAE;MAACyF,QAAQ,EAAC,UAAU;MAACK,KAAK,EAAC,CAAC;MAACE,GAAG,EAAC,CAAC;MAACN,KAAK,EAAC,EAAE;MAAC8F,gBAAgB,EAAC;IAAM,CAAC;IAACzD,MAAM,EAAC;MAAC0D,SAAS,EAAC,gBAAgB;MAACP,QAAQ,EAAC,EAAE;MAACrE,SAAS,EAAC,EAAE;MAAC6E,UAAU,EAAC;IAAC,CAAC;IAACnG,KAAK,EAAC;MAACY,KAAK,EAAC,MAAM;MAACC,OAAO,EAAC,aAAa;MAACpK,MAAM,EAAC,EAAE;MAACqK,MAAM,EAAC,CAAC;MAACZ,QAAQ,EAAC,UAAU;MAACa,MAAM,EAAC;IAAE,CAAC;IAAC8D,OAAO,EAACA;EAAO,CAAC;AAAC;AAEn5C,IAAIuB,WAAW,GAAC,CAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,CAAC;AAAC,IAAIC,iBAAiB,GAAC,CAAC,UAAU,EAAC,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,CAAC;AAAC,IAAIC,YAAY,GAAC,aAAa,UAAS5I,gBAAgB,EAAC;EAAC1F,SAAS,CAACsO,YAAY,EAAC5I,gBAAgB,CAAC;EAAC,IAAIC,MAAM,GAAC3D,YAAY,CAACsM,YAAY,CAAC;EAAC,SAASA,YAAYA,CAAChP,KAAK,EAAC;IAAC,IAAIsG,KAAK;IAAC3G,eAAe,CAAC,IAAI,EAACqP,YAAY,CAAC;IAAC1I,KAAK,GAACD,MAAM,CAACxE,IAAI,CAAC,IAAI,EAAC7B,KAAK,CAAC,CAAC;IAA0BT,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,aAAa,EAAC,UAAS8D,GAAG,EAAC;MAAC9D,KAAK,CAAC2I,QAAQ,GAAC7E,GAAG;IAAC,CAAC,CAAC;IAAC7K,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,aAAa,EAAC,UAAS8D,GAAG,EAAC;MAAC9D,KAAK,CAAC4I,QAAQ,GAAC9E,GAAG;IAAC,CAAC,CAAC;IAAC7K,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,eAAe,EAAC,UAAS8D,GAAG,EAAC;MAAC,IAAG,CAAC9D,KAAK,CAAC6I,UAAU,EAAC;QAAC7I,KAAK,CAAC6I,UAAU,GAAC/E,GAAG;MAAC;IAAC,CAAC,CAAC;IAAC7K,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,eAAe,EAAC,UAAS8D,GAAG,EAAC;MAAC9D,KAAK,CAAC8I,UAAU,GAAChF,GAAG;IAAC,CAAC,CAAC;IAAC7K,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,qBAAqB,EAAC,YAAU;MAAC,IAAIwF,MAAM,GAACxF,KAAK,CAAC+I,KAAK,CAACvD,MAAM;MAAC,IAAIwD,QAAQ,GAAChJ,KAAK,CAACtG,KAAK,CAACsP,QAAQ,CAAC;MAA0B,IAAGhJ,KAAK,CAACiJ,aAAa,EAAC;QAACjJ,KAAK,CAACiJ,aAAa,CAAC3P,QAAQ,CAAC4P,MAAM,CAAC,CAAC;MAAC;MAAClJ,KAAK,CAACmJ,QAAQ,CAAC;QAAC3D,MAAM,EAACA,MAAM,KAAGzI,MAAM,CAACG,OAAO,GAACH,MAAM,CAACI,IAAI,GAACJ,MAAM,CAACE;MAAI,CAAC,EAAC,YAAU;QAAC,IAAImM,SAAS,GAACpJ,KAAK,CAAC+I,KAAK,CAACvD,MAAM;QAACwD,QAAQ,CAACI,SAAS,KAAGrM,MAAM,CAACI,IAAI,GAAC,MAAM,GAAC,OAAO,EAAC6C,KAAK,CAACtG,KAAK,CAAC;MAAC,CAAC,CAAC;IAAC,CAAC,CAAC;IAACT,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,aAAa,EAAC,YAAU;MAAC,IAAIe,WAAW,GAACf,KAAK,CAACtG,KAAK;QAACyF,KAAK,GAAC4B,WAAW,CAAC5B,KAAK;QAAC0F,IAAI,GAAC9D,WAAW,CAAC8D,IAAI;MAAC,IAAGrN,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,EAAC;MAAO,IAAIwE,WAAW,GAACrJ,KAAK,CAAC+I,KAAK;QAACjE,eAAe,GAACuE,WAAW,CAACvE,eAAe;QAACU,MAAM,GAAC6D,WAAW,CAAC7D,MAAM,CAAC;MAA0B,IAAGxF,KAAK,CAACb,KAAK,KAAG,OAAO,IAAEa,KAAK,CAACb,KAAK,KAAG,OAAO,IAAE2F,eAAe,EAAC;QAAC/G,GAAG,CAAC;UAACE,KAAK,EAAC,OAAO;UAACC,IAAI,EAAC,CAAC;YAACiB,KAAK,EAACA,KAAK;YAACqG,MAAM,EAACA,MAAM,KAAGzI,MAAM,CAACI,IAAI,GAAC,SAAS,GAAC;UAAS,CAAC,CAAC;UAACmB,KAAK,EAAC0B,KAAK,CAAC1B;QAAK,CAAC,CAAC;QAAC0B,KAAK,CAACsJ,MAAM,CAAC,CAAC;MAAC;IAAC,CAAC,CAAC;IAACrQ,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,kBAAkB,EAAC,YAAU;MAAC,IAAIqB,YAAY,GAACrB,KAAK,CAACtG,KAAK;QAACyF,KAAK,GAACkC,YAAY,CAAClC,KAAK;QAAC0F,IAAI,GAACxD,YAAY,CAACwD,IAAI;MAAC,IAAGrN,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,IAAEnH,QAAQ,CAAC,CAAC,EAAC;MAAO,IAAI8H,MAAM,GAACxF,KAAK,CAAC+I,KAAK,CAACvD,MAAM,CAAC;MAA0B,IAAGxF,KAAK,CAACb,KAAK,KAAG,OAAO,IAAEqG,MAAM,KAAGzI,MAAM,CAACE,IAAI,EAAC;QAACc,GAAG,CAAC;UAACE,KAAK,EAAC,YAAY;UAACC,IAAI,EAAC,CAAC;YAAClF,GAAG,EAAC,eAAe;YAACmB,KAAK,EAACgF;UAAK,CAAC,CAAC;UAACb,KAAK,EAAC0B,KAAK,CAAC1B;QAAK,CAAC,CAAC;QAACiL,YAAY,CAACvJ,KAAK,CAACwJ,iBAAiB,CAAC;QAACxJ,KAAK,CAACsJ,MAAM,CAAC,CAAC;MAAC;IAAC,CAAC,CAAC;IAACrQ,eAAe,CAAC+C,sBAAsB,CAACgE,KAAK,CAAC,EAAC,kBAAkB,EAAC,YAAU;MAAC,IAAIyJ,YAAY,GAACzJ,KAAK,CAACtG,KAAK;QAACyF,KAAK,GAACsK,YAAY,CAACtK,KAAK;QAACuK,UAAU,GAACD,YAAY,CAACC,UAAU;QAAC7E,IAAI,GAAC4E,YAAY,CAAC5E,IAAI;MAAC,IAAGrN,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,IAAEnH,QAAQ,CAAC,CAAC,EAAC;MAAO,IAAIiM,YAAY,GAAC3J,KAAK,CAAC+I,KAAK;QAACvD,MAAM,GAACmE,YAAY,CAACnE,MAAM;QAACV,eAAe,GAAC6E,YAAY,CAAC7E,eAAe,CAAC;MAA0B,IAAG9E,KAAK,CAACb,KAAK,KAAG,OAAO,EAAC;QAACpB,GAAG,CAAC;UAACE,KAAK,EAAC,YAAY;UAACC,IAAI,EAAC,CAAC;YAAClF,GAAG,EAAC,eAAe;YAACmB,KAAK,EAACgF;UAAK,CAAC,CAAC;UAACb,KAAK,EAAC0B,KAAK,CAAC1B;QAAK,CAAC,CAAC;QAAC,IAAG,CAACoL,UAAU,EAAC;UAAC1J,KAAK,CAACsJ,MAAM,CAACvM,MAAM,CAACE,IAAI,CAAC;QAAC,CAAC,MAAK,IAAG,CAACF,MAAM,CAACG,OAAO,EAACH,MAAM,CAACI,IAAI,CAAC,CAACvB,OAAO,CAAC4J,MAAM,CAAC,KAAG,CAAC,CAAC,IAAE,CAACV,eAAe,IAAE,CAAC9E,KAAK,CAACwJ,iBAAiB,EAAC;UAACxJ,KAAK,CAACwJ,iBAAiB,GAACI,UAAU,CAAC,YAAU;YAAC,OAAO5J,KAAK,CAACwJ,iBAAiB;YAACxJ,KAAK,CAACsJ,MAAM,CAAC,CAAC;UAAC,CAAC,EAACI,UAAU,GAAC,IAAI,CAAC;QAAC;MAAC;IAAC,CAAC,CAAC;IAAC1J,KAAK,CAAC+I,KAAK,GAAC;MAACc,gBAAgB,EAACnQ,KAAK,CAAC6H,SAAS;MAACuD,eAAe,EAACpL,KAAK,CAACoQ,cAAc,CAACxH,QAAQ,IAAE,CAAC,CAAC5I,KAAK,CAAChB,MAAM;MAAC8M,MAAM,EAACzI,MAAM,CAACC,IAAI;MAAC+M,aAAa,EAAChN,MAAM,CAACC;IAAI,CAAC;IAACgD,KAAK,CAACgK,UAAU,GAAC,KAAK;IAAC,IAAG1M,SAAS,EAAC;MAACK,MAAM,CAAC2B,gBAAgB,CAAC,MAAM,EAAC,YAAU;QAAC,IAAGU,KAAK,CAACiK,MAAM,EAAC;UAACjK,KAAK,CAACiK,MAAM,CAAC3Q,QAAQ,CAAC4P,MAAM,CAAC,CAAC;QAAC;QAAC,IAAGlJ,KAAK,CAACiJ,aAAa,EAAC;UAACjJ,KAAK,CAACiJ,aAAa,CAAC3P,QAAQ,CAAC4P,MAAM,CAAC,CAAC;QAAC;MAAC,CAAC,CAAC;IAAC;IAAC,OAAOlJ,KAAK;EAAC;EAAClG,YAAY,CAAC4O,YAAY,EAAC,CAAC;IAAC1P,GAAG,EAAC,mBAAmB;IAACmB,KAAK,EAAC,SAASsG,iBAAiBA,CAAA,EAAE;MAAC,IAAG,CAACnD,SAAS,EAAC;MAAO,IAAIwH,eAAe,GAAC,IAAI,CAACiE,KAAK,CAACjE,eAAe;MAAC,IAAIoF,YAAY,GAAC,IAAI,CAACxQ,KAAK;QAACsH,QAAQ,GAACkJ,YAAY,CAAClJ,QAAQ;QAAC6D,IAAI,GAACqF,YAAY,CAACrF,IAAI;QAACnM,MAAM,GAACwR,YAAY,CAACxR,MAAM;MAAC,IAAI,CAACsR,UAAU,GAAC,IAAI;MAACjM,GAAG,CAAC;QAACE,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;UAACoD,WAAW,EAAC,CAAC,CAACN,QAAQ;UAACmJ,SAAS,EAAC,CAAC,CAACzR,MAAM;UAAC0R,YAAY,EAAC5S,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC;UAACC,eAAe,EAACA,eAAe;UAACpM,MAAM,EAAC,IAAI,CAACA,MAAM;UAAC+M,OAAO,EAAC,IAAI,CAACoD;QAAU,CAAC;QAACvK,KAAK,EAAC,IAAI,CAACA;MAAK,CAAC,CAAC;MAAC,IAAI,CAAC+L,UAAU,CAAC,CAAC;MAAC,IAAG,CAACrJ,QAAQ,IAAEtI,MAAM,IAAE,CAAClB,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,EAAC;IAAC;EAAC,CAAC,EAAC;IAAC7L,GAAG,EAAC,oBAAoB;IAACmB,KAAK,EAAC,SAASwG,kBAAkBA,CAAC2J,SAAS,EAACC,SAAS,EAAC;MAAC,IAAG,CAACjN,SAAS,EAAC;MAAO,IAAIkN,YAAY,GAAC,IAAI,CAAC9Q,KAAK;QAAC+Q,QAAQ,GAACD,YAAY,CAACC,QAAQ;QAAC5F,IAAI,GAAC2F,YAAY,CAAC3F,IAAI;QAACnM,MAAM,GAAC8R,YAAY,CAAC9R,MAAM;QAACoR,cAAc,GAACU,YAAY,CAACV,cAAc;MAAC,IAAIY,YAAY,GAACjT,WAAW,CAAC8S,SAAS,EAAC,IAAI,CAACxB,KAAK,CAAC;QAAC4B,WAAW,GAACD,YAAY,CAACC,WAAW;QAACC,SAAS,GAACF,YAAY,CAACE,SAAS;MAAC,IAAGN,SAAS,CAACzF,IAAI,KAAGA,IAAI,EAAC;QAAC,IAAIgG,WAAW,CAAC;QAC9lI,IAAGrT,EAAE,CAAC,SAAS,CAAC,CAACqN,IAAI,CAAC,EAAC;UAACgG,WAAW,GAAChG,IAAI,GAAC9H,MAAM,CAACG,OAAO,GAACH,MAAM,CAACK,OAAO;QAAC;QAAC,IAAI,CAACkM,MAAM,CAACuB,WAAW,CAAC;MAAC;MAAC,IAAGP,SAAS,CAACR,cAAc,CAACxH,QAAQ,KAAGwH,cAAc,CAACxH,QAAQ,IAAEgI,SAAS,CAAC5R,MAAM,KAAGA,MAAM,EAAC;QAAC,IAAI,CAACoS,qBAAqB,CAAC,IAAI,CAACpR,KAAK,CAAC;MAAC;MAAC,IAAGkR,SAAS,CAAC,QAAQ,EAAC7N,MAAM,CAACE,IAAI,CAAC,IAAE4H,IAAI,EAAC;QAAC,IAAI,CAACyE,MAAM,CAACvM,MAAM,CAACI,IAAI,CAAC;MAAC,CAAC,MAAK,IAAGwN,WAAW,CAAC,QAAQ,EAAC5N,MAAM,CAACC,IAAI,EAACD,MAAM,CAACE,IAAI,CAAC,IAAEwN,QAAQ,EAAC;QAAC,IAAI,CAACnB,MAAM,CAACvM,MAAM,CAACI,IAAI,CAAC;MAAC;MAAC,IAAG,IAAI,CAAC8M,MAAM,IAAEW,SAAS,CAAC,QAAQ,EAAC7N,MAAM,CAACG,OAAO,CAAC,EAAC;QAAC,IAAI,CAAC+M,MAAM,CAAC3Q,QAAQ,CAAC4P,MAAM,CAAC,CAAC;MAAC;MAAC,IAAG,IAAI,CAACL,UAAU,KAAG+B,SAAS,CAAC,QAAQ,EAAC7N,MAAM,CAACG,OAAO,CAAC,IAAE0N,SAAS,CAAC,QAAQ,EAAC7N,MAAM,CAACK,OAAO,CAAC,CAAC,EAAC;QAACqC,IAAI,CAAC,IAAI,CAACoJ,UAAU,EAAC,eAAe,EAAC,IAAI,CAACkC,mBAAmB,CAAC;MAAC;IAAC;EAAC,CAAC,EAAC;IAAC/R,GAAG,EAAC,sBAAsB;IAACmB,KAAK,EAAC,SAASyG,oBAAoBA,CAAA,EAAE;MAAC,IAAG,CAACtD,SAAS,EAAC;MAAO,IAAI,CAAC0M,UAAU,GAAC,KAAK;MAAC,IAAG,IAAI,CAACC,MAAM,EAAC;QAAC,IAAI,CAACA,MAAM,CAAC3Q,QAAQ,CAAC0R,OAAO,CAAC,CAAC;MAAC;MAAC,IAAG,IAAI,CAAC/B,aAAa,EAAC;QAAC,IAAI,CAACA,aAAa,CAAC3P,QAAQ,CAAC0R,OAAO,CAAC,CAAC;MAAC;IAAC;EAAC,CAAC,EAAC;IAAChS,GAAG,EAAC,YAAY;IAACmB,KAAK,EAAC,SAASkQ,UAAUA,CAAA,EAAE;MAAC,IAAIY,MAAM,GAAC,IAAI;MAAC,IAAIvS,MAAM,GAACE,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAG6E,SAAS,GAAC7E,SAAS,CAAC,CAAC,CAAC,GAAC,IAAI,CAACF,MAAM;MAAC,IAAIoM,eAAe,GAAC,IAAI,CAACiE,KAAK,CAACjE,eAAe;MAAC,IAAIoG,YAAY,GAAC,IAAI,CAACxR,KAAK;QAACyR,WAAW,GAACD,YAAY,CAACC,WAAW;QAACC,SAAS,GAACF,YAAY,CAACE,SAAS;QAAC7F,SAAS,GAAC2F,YAAY,CAAC3F,SAAS;QAAC8F,MAAM,GAACH,YAAY,CAACG,MAAM;QAAC9J,SAAS,GAAC2J,YAAY,CAAC3J,SAAS;QAACuI,cAAc,GAACoB,YAAY,CAACpB,cAAc;MAAC,IAAIwB,YAAY,GAAC/J,SAAS,KAAG,KAAK,IAAEA,SAAS,KAAG,QAAQ,GAAC,MAAM,GAAC,CAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,MAAM,EAAC,WAAW,EAAC,cAAc,CAAC,CAAC;MAA0B,IAAGA,SAAS,KAAG,QAAQ,EAAC;QAAC,IAAI,CAAC4H,QAAQ,CAAC;UAAC3D,MAAM,EAACzI,MAAM,CAACE;QAAI,CAAC,CAAC;MAAC,CAAC,MAAK,IAAGvE,MAAM,IAAE,IAAI,CAACmQ,UAAU,EAAC;QAAC,IAAI0C,aAAa,GAAC,IAAI,CAACtE,OAAO;UAAC7E,KAAK,GAACmJ,aAAa,CAACnJ,KAAK;UAACxF,IAAI,GAAC2O,aAAa,CAAC3O,IAAI;UAAC4O,aAAa,GAACD,aAAa,CAACF,MAAM;UAACI,IAAI,GAAC5P,wBAAwB,CAAC0P,aAAa,EAAC/C,WAAW,CAAC;QAAC,IAAIlR,MAAM,CAACoB,MAAM,EAAC,IAAI,CAACmQ,UAAU,EAAC;UAACtH,SAAS,EAACA,SAAS;UAACmK,SAAS,EAACjT,cAAc,CAAC;YAAC2J,KAAK,EAAC3J,cAAc,CAAC;cAACkT,OAAO,EAAC,CAACpG,SAAS;cAACrG,OAAO,EAAC,IAAI,CAACyJ;YAAQ,CAAC,EAACvG,KAAK,CAAC;YAACxF,IAAI,EAACnE,cAAc,CAAC;cAACkT,OAAO,EAAC,CAACR,WAAW;cAACS,QAAQ,EAACN;YAAY,CAAC,EAAC1O,IAAI,CAAC;YAACyO,MAAM,EAAC5S,cAAc,CAAC;cAAC4S,MAAM,EAAC,KAAK,CAAC1M,MAAM,CAAC0M,MAAM,EAAC,IAAI;YAAC,CAAC,EAACG,aAAa;UAAC,CAAC,EAACC,IAAI,CAAC;UAACI,QAAQ,EAAC,SAASA,QAAQA,CAAC3N,IAAI,EAAC;YAAC+M,MAAM,CAAChB,MAAM,GAAC/L,IAAI;YAACkN,SAAS,CAAClN,IAAI,EAAC,SAAS,CAAC;YAAC,IAAG+M,MAAM,CAACjB,UAAU,EAAC;cAACiB,MAAM,CAAC9B,QAAQ,CAAC;gBAACU,gBAAgB,EAAC3L,IAAI,CAACqD,SAAS;gBAACiE,MAAM,EAACzI,MAAM,CAACE;cAAI,CAAC,CAAC;YAAC;YAAC,IAAGsE,SAAS,KAAGrD,IAAI,CAACqD,SAAS,EAAC;cAACqI,UAAU,CAAC,YAAU;gBAAC1L,IAAI,CAAC5E,QAAQ,CAAC4P,MAAM,CAAC,CAAC;cAAC,CAAC,EAAC,CAAC,CAAC;YAAC;UAAC,CAAC;UAAC4C,QAAQ,EAAC,SAASA,QAAQA,CAAC5N,IAAI,EAAC;YAAC+M,MAAM,CAAChB,MAAM,GAAC/L,IAAI;YAAC,IAAI2L,gBAAgB,GAACoB,MAAM,CAAClC,KAAK,CAACc,gBAAgB;YAAC,IAAGoB,MAAM,CAACjB,UAAU,IAAE9L,IAAI,CAACqD,SAAS,KAAGsI,gBAAgB,EAAC;cAACoB,MAAM,CAAC9B,QAAQ,CAAC;gBAACU,gBAAgB,EAAC3L,IAAI,CAACqD;cAAS,CAAC,CAAC;YAAC;UAAC;QAAC,CAAC,CAAC;MAAC;MAAC,IAAGuD,eAAe,EAAC;QAAC,IAAIiH,aAAa,GAAC,CAACvU,EAAE,CAACiG,SAAS,CAACqM,cAAc,CAACuB,MAAM,CAAC,GAACvB,cAAc,CAACuB,MAAM,GAAC,CAAC;QAAC,IAAI/T,MAAM,CAAC,IAAI,CAACoB,MAAM,EAAC,IAAI,CAACoQ,UAAU,EAAC;UAACvH,SAAS,EAACuI,cAAc,CAACvI,SAAS,IAAEA,SAAS;UAACmK,SAAS,EAAC;YAACtJ,KAAK,EAAC;cAACuJ,OAAO,EAAC;YAAK,CAAC;YAACN,MAAM,EAAC;cAACA,MAAM,EAAC,KAAK,CAAC1M,MAAM,CAACoN,aAAa,EAAC,IAAI;YAAC,CAAC;YAACnP,IAAI,EAAC;cAAC+O,OAAO,EAAC;YAAK;UAAC,CAAC;UAACE,QAAQ,EAAC,SAASA,QAAQA,CAAC3N,IAAI,EAAC;YAAC+M,MAAM,CAAChC,aAAa,GAAC/K,IAAI;YAAC,IAAG+M,MAAM,CAACjB,UAAU,EAAC;cAACiB,MAAM,CAAC9B,QAAQ,CAAC;gBAACY,aAAa,EAAChN,MAAM,CAACE;cAAI,CAAC,CAAC;YAAC;YAACmO,SAAS,CAAClN,IAAI,EAAC,SAAS,CAAC;YAAC,IAAGqD,SAAS,KAAGrD,IAAI,CAACqD,SAAS,EAAC;cAACqI,UAAU,CAAC,YAAU;gBAAC1L,IAAI,CAAC5E,QAAQ,CAAC4P,MAAM,CAAC,CAAC;cAAC,CAAC,EAAC,CAAC,CAAC;YAAC;UAAC;QAAC,CAAC,CAAC;MAAC;IAAC;EAAC,CAAC,EAAC;IAAClQ,GAAG,EAAC,uBAAuB;IAACmB,KAAK,EAAC,SAAS2Q,qBAAqBA,CAAC9M,IAAI,EAAC;MAAC,IAAItF,MAAM,GAACsF,IAAI,CAACtF,MAAM;QAACoR,cAAc,GAAC9L,IAAI,CAAC8L,cAAc;MAAC,IAAI,CAACX,QAAQ,CAAC;QAACrE,eAAe,EAACgF,cAAc,CAACxH,QAAQ,IAAE,CAAC,CAAC5J;MAAM,CAAC,CAAC;IAAC;EAAC,CAAC,EAAC;IAACM,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASmP,MAAMA,CAACuB,WAAW,EAAC;MAAC,IAAIrF,MAAM,GAAC,IAAI,CAACuD,KAAK,CAACvD,MAAM;MAAC,IAAIwG,UAAU,GAACxG,MAAM,KAAGzI,MAAM,CAACI,IAAI,GAACJ,MAAM,CAACK,OAAO,GAACL,MAAM,CAACG,OAAO;MAAC,IAAG,CAAC1F,EAAE,CAACiG,SAAS,CAACoN,WAAW,CAAC,EAAC;QAACmB,UAAU,GAACnB,WAAW;MAAC;MAAC,IAAI,CAAC1B,QAAQ,CAAC;QAAC3D,MAAM,EAACwG;MAAU,CAAC,CAAC;IAAC;EAAC,CAAC,EAAC;IAAChT,GAAG,EAAC,OAAO;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAI5D,KAAK,GAAC,IAAI,CAAC5E,KAAK,CAAC4E,KAAK;MAAC,OAAOA,KAAK,IAAE,CAAC,CAAC2N,MAAM,CAACC,iBAAiB;IAAC;EAAC,CAAC,EAAC;IAAClT,GAAG,EAAC,OAAO;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAIiK,YAAY,GAAC,IAAI,CAACzS,KAAK;QAAC0S,mBAAmB,GAACD,YAAY,CAACC,mBAAmB;QAACjN,KAAK,GAACgN,YAAY,CAAChN,KAAK;MAAC,IAAGA,KAAK,KAAG,OAAO,IAAEzB,QAAQ,CAAC,CAAC,IAAE,CAAC0O,mBAAmB,EAAC;QAAC,OAAO,OAAO;MAAC;MAAC,OAAOjN,KAAK;IAAC;EAAC,CAAC,EAAC;IAACnG,GAAG,EAAC,SAAS;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAI+E,OAAO,GAAC,IAAI,CAACvN,KAAK,CAACuN,OAAO;MAAC,OAAO1P,SAAS,CAACoF,QAAQ,EAACsK,OAAO,IAAE,CAAC,CAAC,CAAC;IAAC;EAAC,CAAC,EAAC;IAACjO,GAAG,EAAC,QAAQ;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAImK,MAAM,GAAC,IAAI;MAAC,IAAIC,YAAY,GAAC,IAAI,CAACvD,KAAK;QAACvD,MAAM,GAAC8G,YAAY,CAAC9G,MAAM;QAACV,eAAe,GAACwH,YAAY,CAACxH,eAAe;QAACiF,aAAa,GAACuC,YAAY,CAACvC,aAAa;MAAC,IAAI5H,MAAM,GAAC,IAAI,CAACzI,KAAK,CAACyI,MAAM;MAAC,IAAIoK,UAAU,GAAChV,SAAS,CAACyP,SAAS,CAAC7E,MAAM,CAAC,EAACA,MAAM,CAAC;MAAC,IAAG2C,eAAe,EAAC;QAAC,IAAI0H,aAAa;QAAC,IAAG,EAAE,CAACzP,MAAM,CAACE,IAAI,CAAC,CAACrB,OAAO,CAAC4J,MAAM,CAAC,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,CAACzI,MAAM,CAACE,IAAI,CAAC,CAACrB,OAAO,CAACmO,aAAa,CAAC,KAAG,CAAC,CAAC,CAAC,EAAC;UAACyC,aAAa,GAACD,UAAU,CAAClF,eAAe;QAAC,CAAC,MAAK;UAACmF,aAAa,GAAC,IAAI,CAACvD,aAAa,CAAC9G,MAAM;QAAC;QAACoK,UAAU,CAACrF,OAAO,GAACzO,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAC8T,UAAU,CAACrF,OAAO,CAAC,EAACsF,aAAa,CAAC;MAAC,CAAC;MAA0B,IAAG,IAAI,CAAC9T,MAAM,EAAC;QAAC,IAAI+T,YAAY,GAAC9O,MAAM,CAAC+O,gBAAgB,CAAC,IAAI,CAAChU,MAAM,CAAC,CAAC;QAA0B,IAAG,IAAI,CAAC8T,aAAa,EAAC;UAACD,UAAU,CAACrF,OAAO,GAACzO,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAC8T,UAAU,CAACrF,OAAO,CAAC,EAAC,IAAI,CAACsF,aAAa,CAAC;QAAC,CAAC,MAAK,IAAG,EAAE,CAAC,UAAU,EAAC,QAAQ,CAAC,CAAC5Q,OAAO,CAAC6Q,YAAY,CAACnK,QAAQ,CAAC,KAAG,CAAC,CAAC,CAAC,EAAC;UAAC,IAAI,CAACkK,aAAa,GAAC,CAAC,CAAC;UAAC,IAAG,CAAC1H,eAAe,EAAC;YAAC2D,iBAAiB,CAAC1P,OAAO,CAAC,UAAS+F,CAAC,EAAC;cAACuN,MAAM,CAACG,aAAa,CAAC1N,CAAC,CAAC,GAAC2N,YAAY,CAAC3N,CAAC,CAAC;YAAC,CAAC,CAAC;YAACyN,UAAU,CAACrF,OAAO,GAACzO,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAC8T,UAAU,CAACrF,OAAO,CAAC,EAAC,IAAI,CAACsF,aAAa,CAAC;YAAC,IAAI,CAAC9T,MAAM,CAAC4H,KAAK,CAACgC,QAAQ,GAAC,UAAU;YAAC,IAAI,CAAC5J,MAAM,CAAC4H,KAAK,CAACuC,GAAG,GAAC,MAAM;YAAC,IAAI,CAACnK,MAAM,CAAC4H,KAAK,CAACqC,KAAK,GAAC,MAAM;YAAC,IAAI,CAACjK,MAAM,CAAC4H,KAAK,CAACmC,MAAM,GAAC,MAAM;YAAC,IAAI,CAAC/J,MAAM,CAAC4H,KAAK,CAACoC,IAAI,GAAC,MAAM;UAAC;QAAC;MAAC;MAAC,OAAO6J,UAAU;IAAC;EAAC,CAAC,EAAC;IAACvT,GAAG,EAAC,QAAQ;IAACkJ,GAAG,EAAC,SAASA,GAAGA,CAAA,EAAE;MAAC,IAAG,CAAC5E,SAAS,EAAC,OAAO,IAAI;MAAC,IAAI5E,MAAM,GAAC,IAAI,CAACgB,KAAK,CAAChB,MAAM;MAAC,IAAGA,MAAM,EAAC;QAAC,IAAGlB,EAAE,CAACmV,UAAU,CAACjU,MAAM,CAAC,EAAC;UAAC,OAAOA,MAAM;QAAC;QAAC,OAAOwH,QAAQ,CAAC0M,aAAa,CAAClU,MAAM,CAAC;MAAC;MAAC,OAAO,IAAI,CAACkQ,QAAQ,IAAE,IAAI,CAACE,UAAU;IAAC;EAAC,CAAC,EAAC;IAAC9P,GAAG,EAAC,QAAQ;IAACmB,KAAK,EAAC,SAASqH,MAAMA,CAAA,EAAE;MAAC,IAAIqL,YAAY,GAAC,IAAI,CAAC9D,KAAK;QAACc,gBAAgB,GAACgD,YAAY,CAAChD,gBAAgB;QAAC/E,eAAe,GAAC+H,YAAY,CAAC/H,eAAe;QAACU,MAAM,GAACqH,YAAY,CAACrH,MAAM;MAAC,IAAIsH,YAAY,GAAC,IAAI,CAACpT,KAAK;QAACsH,QAAQ,GAAC8L,YAAY,CAAC9L,QAAQ;QAACsE,SAAS,GAACwH,YAAY,CAACxH,SAAS;QAACX,OAAO,GAACmI,YAAY,CAACnI,OAAO;QAACU,gBAAgB,GAACyH,YAAY,CAACzH,gBAAgB;QAACT,MAAM,GAACkI,YAAY,CAAClI,MAAM;QAACW,SAAS,GAACuH,YAAY,CAACvH,SAAS;QAACnF,EAAE,GAAC0M,YAAY,CAAC1M,EAAE;QAACyE,IAAI,GAACiI,YAAY,CAACjI,IAAI;QAACE,eAAe,GAAC+H,YAAY,CAAC/H,eAAe;QAACzE,KAAK,GAACwM,YAAY,CAACxM,KAAK;QAAC5H,MAAM,GAACoU,YAAY,CAACpU,MAAM;QAACuF,KAAK,GAAC6O,YAAY,CAAC7O,KAAK;MAAC,IAAIiJ,OAAO,GAAC,aAAa/P,KAAK,CAACgJ,aAAa,CAACiG,mBAAmB,EAAC;QAAChC,WAAW,EAAC,IAAI,CAACA,WAAW;QAACiC,gBAAgB,EAAC,IAAI,CAACA,gBAAgB;QAACC,gBAAgB,EAAC,IAAI,CAACA,gBAAgB;QAACC,WAAW,EAAC,IAAI,CAACA,WAAW;QAACC,aAAa,EAAC,IAAI,CAACA,aAAa;QAAClG,KAAK,EAACA,KAAK;QAAC6B,MAAM,EAAC,IAAI,CAACA,MAAM,CAAC+E;MAAO,CAAC,EAAClG,QAAQ,CAAC;MAAC,IAAIgE,MAAM,GAAC,CAAC,CAAC;MAAC,IAAGF,eAAe,EAAC;QAACE,MAAM,CAAC+H,eAAe,GAAC7F,OAAO;MAAC,CAAC,MAAK;QAAClC,MAAM,CAACgI,iBAAiB,GAAC9F,OAAO;MAAC;MAAC,OAAM,aAAa/P,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAC,IAAI,EAAC,aAAahJ,KAAK,CAACgJ,aAAa,CAACN,kBAAkB,EAAC;QAACyB,WAAW,EAAC,CAAC,CAACN,QAAQ;QAACZ,EAAE,EAACA,EAAE;QAACmB,SAAS,EAACsI,gBAAgB;QAAC5I,MAAM,EAAC,IAAI,CAAC+E,aAAa;QAACtN,MAAM,EAACA,MAAM;QAAC2H,MAAM,EAAC,IAAI,CAAC8B,MAAM,CAAC8E,OAAO,CAAC5G;MAAM,CAAC,EAAC,aAAalJ,KAAK,CAACgJ,aAAa,CAACiF,OAAO,EAAC;QAACE,SAAS,EAACA,SAAS;QAACX,OAAO,EAACA,OAAO;QAACU,gBAAgB,EAACA,gBAAgB;QAACT,MAAM,EAACA,MAAM;QAACR,WAAW,EAAC,IAAI,CAACA,WAAW;QAACmB,SAAS,EAACA,SAAS,IAAEsE,gBAAgB,KAAG,QAAQ;QAAChF,IAAI,EAACA,IAAI;QAACtD,SAAS,EAACsI,gBAAgB;QAAC/E,eAAe,EAACA,eAAe;QAAChC,WAAW,EAAC,IAAI,CAACA,WAAW;QAACkD,aAAa,EAAC,IAAI,CAACA,aAAa;QAACjB,eAAe,EAACA,eAAe;QAACS,MAAM,EAACA,MAAM;QAACrD,MAAM,EAAC,IAAI,CAACA,MAAM;QAAClE,KAAK,EAACA;MAAK,CAAC,CAAC,EAAC+G,MAAM,CAAC+H,eAAe,CAAC,EAAC/H,MAAM,CAACgI,iBAAiB,CAAC;IAAC;EAAC,CAAC,CAAC,CAAC;EAAC,OAAOtE,YAAY;AAAC,CAAC,CAACvR,KAAK,CAACsK,SAAS,CAAC;AAACxI,eAAe,CAACyP,YAAY,EAAC,WAAW,EAAC;EAAC+B,QAAQ,EAACrT,SAAS,CAACwK,IAAI;EAACoH,QAAQ,EAAC5R,SAAS,CAAC2K,IAAI;EAACf,QAAQ,EAAC5J,SAAS,CAAC6I,IAAI;EAACqF,SAAS,EAACjO,YAAY,CAACD,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAAC2K,IAAI,EAAC3K,SAAS,CAAC8H,OAAO,CAAC,CAAC,EAAC,UAASxF,KAAK,EAAC;IAAC,OAAO,CAACA,KAAK,CAACiL,OAAO;EAAC,CAAC,CAAC;EAACA,OAAO,EAACtN,YAAY,CAACD,SAAS,CAAC6I,IAAI,EAAC,UAASvG,KAAK,EAAC;IAAC,OAAO,CAACA,KAAK,CAAC4L,SAAS;EAAC,CAAC,CAAC;EAAChH,KAAK,EAAClH,SAAS,CAACwK,IAAI;EAACyD,gBAAgB,EAACjO,SAAS,CAACwK,IAAI;EAACuJ,WAAW,EAAC/T,SAAS,CAACwK,IAAI;EAACwK,mBAAmB,EAAChV,SAAS,CAACwK,IAAI;EAACzC,KAAK,EAAC/H,SAAS,CAAC6V,KAAK,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC;EAACvD,UAAU,EAACtS,SAAS,CAAC0K,MAAM;EAAC8C,MAAM,EAACxN,SAAS,CAAC6I,IAAI;EAACmL,SAAS,EAAChU,SAAS,CAAC2K,IAAI;EAACwD,SAAS,EAACnO,SAAS,CAACwK,IAAI;EAACxB,EAAE,EAAChJ,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACyK,MAAM,EAACzK,SAAS,CAAC0K,MAAM,CAAC,CAAC;EAACuJ,MAAM,EAACjU,SAAS,CAAC0K,MAAM;EAAC+C,IAAI,EAACzN,SAAS,CAACwK,IAAI;EAACqF,OAAO,EAAC7P,SAAS,CAACS,MAAM;EAAC0J,SAAS,EAACnK,SAAS,CAAC6V,KAAK,CAAC,CAAC,KAAK,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,EAAC,cAAc,EAAC,YAAY,EAAC,MAAM,EAAC,YAAY,EAAC,UAAU,EAAC,OAAO,EAAC,aAAa,EAAC,WAAW,EAAC,MAAM,EAAC,QAAQ,CAAC,CAAC;EAAClI,eAAe,EAAC3N,SAAS,CAACwK,IAAI;EAACtB,KAAK,EAAClJ,SAAS,CAACS,MAAM;EAACsK,MAAM,EAAC/K,SAAS,CAACS,MAAM;EAACa,MAAM,EAACtB,SAAS,CAACsK,SAAS,CAAC,CAACtK,SAAS,CAACS,MAAM,EAACT,SAAS,CAACyK,MAAM,CAAC,CAAC;EAAC5D,KAAK,EAAC7G,SAAS,CAAC6I,IAAI;EAAC6J,cAAc,EAAC1S,SAAS,CAAC8V,KAAK,CAAC;IAAC7B,MAAM,EAACjU,SAAS,CAAC0K,MAAM;IAACP,SAAS,EAACnK,SAAS,CAAC6V,KAAK,CAAC,CAAC,KAAK,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,EAAC,cAAc,EAAC,YAAY,EAAC,MAAM,EAAC,YAAY,EAAC,UAAU,EAAC,OAAO,EAAC,aAAa,EAAC,WAAW,EAAC,MAAM,CAAC,CAAC;IAAC3K,QAAQ,EAAClL,SAAS,CAACwK;EAAI,CAAC;AAAC,CAAC,CAAC;AAAC3I,eAAe,CAACyP,YAAY,EAAC,cAAc,EAAC;EAAC+B,QAAQ,EAAC,KAAK;EAACzB,QAAQ,EAACpJ,IAAI;EAACtB,KAAK,EAAC,KAAK;EAAC+G,gBAAgB,EAAC,KAAK;EAAC8F,WAAW,EAAC,KAAK;EAACiB,mBAAmB,EAAC,KAAK;EAACjN,KAAK,EAAC,OAAO;EAACuK,UAAU,EAAC,GAAG;EAAC0B,SAAS,EAACxL,IAAI;EAAC2F,SAAS,EAAC,KAAK;EAAC8F,MAAM,EAAC,EAAE;EAAC9J,SAAS,EAAC,QAAQ;EAACwD,eAAe,EAAC,KAAK;EAAC5C,MAAM,EAAC,CAAC,CAAC;EAACzJ,MAAM,EAAC,IAAI;EAACoR,cAAc,EAAC;IAACxH,QAAQ,EAAC;EAAK;AAAC,CAAC,CAAC;AAEz/Q,eAAeoG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}