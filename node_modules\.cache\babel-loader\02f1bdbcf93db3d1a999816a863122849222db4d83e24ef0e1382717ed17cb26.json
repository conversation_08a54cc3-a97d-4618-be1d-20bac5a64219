{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\marketplace\\\\carrello.jsx\";\nimport React, { Component } from 'react';\nimport Immagine from '../../../img/mktplaceholder.jpg';\n//import AggiungiDestinazioni from '../../../aggiunta_dati/aggiungiDestinazioni';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../../../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { APIRequest, baseProxy } from '../../../components/generalizzazioni/apireq';\nimport { getNumbers } from '../../../common/PDV/carrello/actions/getAction';\nimport { connect } from 'react-redux';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport { Toast } from 'primereact/toast';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Dialog } from 'primereact/dialog';\nimport { Checkbox } from 'primereact/checkbox';\nimport { InputText } from 'primereact/inputtext';\nimport { Calendar } from 'primereact/calendar';\nimport { affiliato, affiliatoDocumentiOrdineAcquisto, affiliatoVisualizzaOrdini, agente, agenteGestioneOrdiniAgente, chain, chainDocumentiOrdineAcquisto, distributore, distributoreCarrello, distributoreDocumentiOrdineAcquisto, pdv, pdvGestioneLogisticaOrdini, pdvStoricoDocumenti, ring } from '../../route';\nimport { onlyUniqueCondizioneCorrelatiOmaggio } from '../uniqueElements/onlyUniqueNameStatus';\nimport { OverlayPanelGen } from '../overlayPanelGen';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass CarrelloGen extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: '',\n      address: '',\n      pIva: '',\n      email: '',\n      isValid: '',\n      idProduct2: [],\n      createAt: '',\n      updateAt: ''\n    };\n    /* Funzione di invio ordine effettuata in maniera asincrona con axios */\n    this.Invia = async () => {\n      this.setState({\n        disabled: true\n      });\n      var prodotti = [];\n      var unitMeasure = '';\n      var pcsXPackage = '';\n      var colli = '';\n      if (this.state.selectedAddress.length === 0 && this.state.role !== distributore && this.state.role !== chain && !JSON.parse(sessionStorage.getItem('affForOrd'))) {\n        this.setState({\n          disabled: false\n        });\n        this.toast.show({\n          severity: 'error',\n          summary: 'Attenzione !',\n          detail: \"Selezionare indirizzo di destinazione prima di procedere con l'ordine\",\n          life: 3000\n        });\n      } else {\n        /* Per ogni elemento definiamo il moltiplicatore delle quantità il formato ed i colli */\n        this.state.results2.forEach(element => {\n          if (element.idProduct2.productsPackagings !== undefined) {\n            element.idProduct2.productsPackagings.forEach(el => {\n              if (element.moltiplicatore !== undefined) {\n                if (parseFloat(element.moltiplicatore) === el.pcsXPackage) {\n                  unitMeasure = el.unitMeasure;\n                  pcsXPackage = el.pcsXPackage;\n                  colli = element.quantity / el.pcsXPackage;\n                }\n              } else {\n                if (element.pcsXpackage === el.pcsXPackage) {\n                  unitMeasure = el.unitMeasure;\n                  pcsXPackage = el.pcsXPackage;\n                  colli = element.quantity / el.pcsXPackage;\n                }\n              }\n            });\n          } else {\n            unitMeasure = element.unitMeasure;\n            pcsXPackage = element.pcsXpackage;\n            colli = element.quantity / element.pcsXpackage;\n          }\n          /* Per ogni elemento definiamo il totale */\n          if (element.price !== undefined) {\n            var tot = element.quantity * parseFloat(element.price) + ' €';\n          } else {\n            tot = element.quantity * parseFloat(element.unitPrice !== undefined ? element.unitPrice : element.sell_in) + ' €';\n          }\n          /* Per ogni elemento definiamo il prezzo unitario */\n          var unitPrice = 0;\n          if (element.price !== undefined) {\n            unitPrice = parseFloat(element.price);\n          } else {\n            unitPrice = parseFloat(element.unitPrice !== undefined ? element.unitPrice : element.sell_in);\n          }\n          var FEE = '';\n          if (element.priceDad !== undefined) {\n            var feature = parseFloat(element.price) - parseFloat(element.priceDad);\n            FEE = feature * element.quantity + ' €';\n          }\n          /* Definisco gli elementi per singolo prodotto */\n          if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n            var find = element.idProduct2.productsPackagings.find(el => el.pcsXPackage === pcsXPackage);\n            prodotti.push({\n              id: element.idProduct2.id,\n              quantity: element.quantity,\n              total: parseFloat(tot),\n              totalTaxed: this.state.zeroIva ? parseFloat(tot) : parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100,\n              idProductsPackaging: find.id,\n              unitPrice: unitPrice,\n              colliPreventivo: colli,\n              tax: element.idProduct2.iva + '%'\n            });\n          } else {\n            if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n              var x = _objectSpread(_objectSpread({}, element), {}, {\n                colliPreventivo: element.qtaIns,\n                colliConsuntivo: 0,\n                total: parseFloat(tot),\n                totalTaxed: parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100,\n                idProductsPackaging: element.idProductsPackaging !== undefined ? element.idProductsPackaging : element.idProduct2.productsPackagings.find(el => el.pcsXPackage === parseInt(element.moltiplicatore)).id\n              });\n              delete x.totale;\n              prodotti.push(x);\n            } else {\n              prodotti.push({\n                id: element.idProduct2.id,\n                quantity: element.quantity,\n                total: parseFloat(tot),\n                totalTaxed: parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100,\n                unitMeasure: unitMeasure,\n                pcsXpackage: pcsXPackage,\n                unitPrice: unitPrice,\n                colli: colli,\n                tax: element.idProduct2.iva + '%',\n                fee: parseFloat(FEE)\n              });\n            }\n          }\n        });\n        if (prodotti.length > 0) {\n          var fee = 0;\n          prodotti.forEach(element => {\n            if (element.fee !== undefined) {\n              fee += element.fee;\n            }\n          });\n          if (fee !== 0) {\n            fee = fee + ' €';\n          }\n          let body = [];\n          var numberOfCustomers = 1;\n          /* Definisco il body per la chiamata */\n          /* Affiliato */\n          if (this.state.datiConsegna.stato === undefined) {\n            body = {\n              deliveryDestination: this.state.datiConsegna.indirizzo,\n              status: this.state.datiConsegna.stato,\n              note: this.state.datiConsegna.note,\n              deliveryDate: this.state.datiConsegna.data,\n              paymentStatus: '',\n              total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n              orderDate: new Date(),\n              idRetailer: this.state.results2,\n              termsPayment: this.state.results.idRegistry.paymentMetod,\n              products: prodotti,\n              pdvEmail: this.state.selectedMail,\n              sendMail: this.state.checked\n            };\n          } else {\n            /* Agente */\n            if (this.state.results.paymentMetod === undefined) {\n              if (fee !== 0) {\n                fee = parseFloat(fee.replace('€', '').replace(',', '.'));\n              }\n              console.log(this.state.datiConsegna);\n              numberOfCustomers = parseInt(sessionStorage.getItem('numberOfCustomer')) !== 0 ? parseInt(sessionStorage.getItem('numberOfCustomer')) : 1;\n              body = {\n                deliveryDestination: this.state.datiConsegna.indirizzo,\n                status: this.state.datiConsegna.stato,\n                note: this.state.datiConsegna.note,\n                deliveryDate: this.state.datiConsegna.data !== undefined ? this.state.datiConsegna.data : this.state.data !== null ? new Date(this.state.data) : null,\n                paymentStatus: '',\n                total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                orderDate: new Date(),\n                termsPayment: this.state.selectedPaymentMethod !== null ? this.state.selectedPaymentMethod.code : this.state.results.idRegistry.paymentMetod,\n                idRetailer: this.state.results,\n                products: prodotti,\n                fee: fee,\n                pdvEmail: this.state.selectedMail,\n                sendMail: this.state.checked,\n                numberOfCustomers: numberOfCustomers ? numberOfCustomers : 1\n              };\n            } else {\n              /* PDV */\n              if (fee !== 0) {\n                fee = parseFloat(fee.replace('€', '').replace(',', '.'));\n              }\n              if (window.location.pathname !== distributoreCarrello && this.state.role !== chain && !JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n                  body = {\n                    idRetailer: this.state.results.retailers,\n                    type: 'CLI-ORDINE',\n                    documentDate: new Date(),\n                    deliveryDestination: this.state.datiConsegna.indirizzo,\n                    note: this.state.datiConsegna.note,\n                    mail: this.state.checked ? this.state.selectedMail : '',\n                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                    rowBody: prodotti,\n                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                    totalTaxed: parseFloat(this.state.totTax.replace('€', '').replace('.', '').replace(',', '.'))\n                  };\n                } else {\n                  numberOfCustomers = parseInt(sessionStorage.getItem('numberOfCustomer')) !== 0 ? parseInt(sessionStorage.getItem('numberOfCustomer')) : 1;\n                  body = {\n                    deliveryDestination: this.state.datiConsegna.indirizzo,\n                    status: this.state.datiConsegna.stato,\n                    note: this.state.datiConsegna.note,\n                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                    paymentStatus: '',\n                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                    orderDate: new Date(),\n                    termsPayment: this.state.results.paymentMetod,\n                    idRetailer: this.state.results.retailers,\n                    products: prodotti,\n                    fee: fee,\n                    pdvEmail: this.state.selectedMail,\n                    sendMail: this.state.checked,\n                    numberOfCustomers: numberOfCustomers ? numberOfCustomers : 1\n                  };\n                }\n              } else {\n                if (this.state.selectedPaymentMethod) {\n                  body = {\n                    type: 'FOR-ORDINE',\n                    deliveryDestination: JSON.parse(sessionStorage.getItem(\"idWarehouse\")).name,\n                    note: this.state.zeroIva ? \"Iva azzerata, \".concat(this.state.datiConsegna.note) : this.state.datiConsegna.note,\n                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                    paymentStatus: '',\n                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                    totalTaxed: parseFloat(this.state.totTax.replace('€', '').replace('.', '').replace(',', '.')),\n                    orderDate: new Date(),\n                    idSupplying: JSON.parse(window.sessionStorage.getItem(\"idSupplier\")),\n                    rowBody: prodotti,\n                    pdvEmail: this.state.selectedMail,\n                    sendMail: this.state.checked,\n                    termsPayment: this.state.selectedPaymentMethod.code\n                  };\n                } else {\n                  this.setState({\n                    disabled: false\n                  });\n                  this.toast.show({\n                    severity: 'error',\n                    summary: 'Attenzione !',\n                    detail: \"Inserire termini di pagamento prima di proseguire con l'ordine\",\n                    life: 3000\n                  });\n                }\n              }\n            }\n          }\n          if (window.location.pathname !== distributoreCarrello && this.state.role !== chain && (body.termsPayment === null || body.termsPayment === undefined)) {\n            body.termsPayment = '';\n          }\n          if (body.deliveryDate !== undefined && body.deliveryDate !== null) {\n            if (localStorage.getItem(\"role\") === agente && this.state.datiConsegna.idOrder !== '') {\n              var url = 'orders/?id=' + this.state.datiConsegna.idOrder;\n              await APIRequest('PUT', url, body).then(res => {\n                console.log(res.data);\n                this.toast.show({\n                  severity: 'success',\n                  summary: 'Ottimo',\n                  detail: 'Il suo ordine è stato inserito con successo',\n                  life: 3000\n                });\n                localStorage.setItem(\"Prodotti\", []);\n                localStorage.setItem(\"Cart\", []);\n                localStorage.setItem(\"OrdineRecuperato\", []);\n                localStorage.setItem(\"DatiConsegna\", []);\n                window.sessionStorage.setItem(\"Carrello\", 0);\n                window.sessionStorage.setItem(\"totCart\", 0);\n                localStorage.setItem(\"datiComodo\", 0);\n                setTimeout(() => {\n                  window.location.pathname = agenteGestioneOrdiniAgente;\n                }, 3000);\n              }).catch(e => {\n                console.log(e);\n                this.setState({\n                  disabled: false\n                });\n              });\n            } else if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n              if (JSON.parse(sessionStorage.getItem(\"idDocument\")) !== 0 && JSON.parse(sessionStorage.getItem(\"idDocument\")) !== null) {\n                body = _objectSpread(_objectSpread({}, body), {}, {\n                  documentDate: JSON.parse(sessionStorage.getItem(\"idDocument\")).documentDate,\n                  number: JSON.parse(sessionStorage.getItem(\"idDocument\")).number\n                });\n                await APIRequest('PUT', \"documents/?idDocumentHead=\".concat(JSON.parse(sessionStorage.getItem(\"idDocument\")).id), body).then(async res => {\n                  console.log(res.data);\n                  this.toast.show({\n                    severity: 'success',\n                    summary: 'Ottimo',\n                    detail: 'Il suo ordine è stato modificato con successo',\n                    life: 3000\n                  });\n                  localStorage.setItem(\"Prodotti\", []);\n                  localStorage.setItem(\"Cart\", []);\n                  localStorage.setItem(\"OrdineRecuperato\", []);\n                  localStorage.setItem(\"DatiConsegna\", []);\n                  localStorage.setItem(\"datiComodo\", 0);\n                  window.sessionStorage.setItem(\"Carrello\", 0);\n                  window.sessionStorage.setItem(\"totCart\", 0);\n                  window.sessionStorage.setItem(\"idWarehouse\", 0);\n                  window.sessionStorage.setItem(\"idDocument\", 0);\n                  window.sessionStorage.setItem(\"idSupplier\", 0);\n                  window.sessionStorage.setItem(\"affForOrd\", false);\n                  setTimeout(() => {\n                    window.location.pathname = this.state.role === distributore ? distributoreDocumentiOrdineAcquisto : this.state.role === chain ? chainDocumentiOrdineAcquisto : affiliatoDocumentiOrdineAcquisto;\n                  }, 3000);\n                }).catch(e => {\n                  console.log(e);\n                  this.setState({\n                    disabled: false\n                  });\n                });\n              } else {\n                await APIRequest('POST', \"documents/?idWarehouses=\".concat(JSON.parse(sessionStorage.getItem(\"idWarehouse\")).code !== undefined ? JSON.parse(sessionStorage.getItem(\"idWarehouse\")).code : JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse), body).then(async res => {\n                  console.log(res.data);\n                  this.toast.show({\n                    severity: 'success',\n                    summary: 'Ottimo',\n                    detail: 'Il suo ordine è stato inserito con successo',\n                    life: 3000\n                  });\n                  localStorage.setItem(\"Prodotti\", []);\n                  localStorage.setItem(\"Cart\", []);\n                  localStorage.setItem(\"OrdineRecuperato\", []);\n                  localStorage.setItem(\"DatiConsegna\", []);\n                  localStorage.setItem(\"datiComodo\", 0);\n                  window.sessionStorage.setItem(\"Carrello\", 0);\n                  window.sessionStorage.setItem(\"totCart\", 0);\n                  window.sessionStorage.setItem(\"idSupplier\", 0);\n                  window.sessionStorage.setItem(\"affForOrd\", false);\n                  if (this.state.role === affiliato && this.state.checked) {\n                    var selfMail = this.state.results.retailers.idRegistry.email;\n                    await APIRequest(\"GET\", \"email/sendDocuments?idDocument=\".concat(res.data.result.id, \"&destEmail=\").concat(selfMail, \"&oggetto=Riepilogo fornitura&cc=\")).then(res => {\n                      var _this$toast;\n                      console.log(res.data);\n                      (_this$toast = this.toast) === null || _this$toast === void 0 ? void 0 : _this$toast.show({\n                        severity: \"success\",\n                        summary: \"Ottimo !\",\n                        detail: \"L'email è stata inviata con successo\",\n                        life: 3000\n                      });\n                      setTimeout(() => {\n                        window.location.pathname = affiliatoDocumentiOrdineAcquisto;\n                      }, 3000);\n                    }).catch(e => {\n                      var _this$toast2, _e$response, _e$response2;\n                      console.log(e);\n                      (_this$toast2 = this.toast) === null || _this$toast2 === void 0 ? void 0 : _this$toast2.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: \"Non \\xE8 stato possibile inviare l'email. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n                        life: 3000\n                      });\n                    });\n                  }\n                  setTimeout(() => {\n                    window.location.pathname = this.state.role === distributore ? distributoreDocumentiOrdineAcquisto : this.state.role === chain ? chainDocumentiOrdineAcquisto : affiliatoDocumentiOrdineAcquisto;\n                  }, 3000);\n                }).catch(e => {\n                  console.log(e);\n                  this.setState({\n                    disabled: false\n                  });\n                });\n              }\n            } else {\n              if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n                var cc = \"\".concat(this.state.results.retailers.idAffiliate2.idRegistry2.email, \",\").concat(this.state.ordersMail);\n                console.log(cc);\n                await APIRequest('POST', \"documents/?idWarehouses=\".concat(this.state.role !== ring ? JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse : JSON.parse(localStorage.getItem('user')).idRegistry.retailers.idAffiliate2.idRegistry2.users[0].warehousesCross[0].idWarehouse), body).then(async res => {\n                  var _this$toast3;\n                  console.log(res.data);\n                  (_this$toast3 = this.toast) === null || _this$toast3 === void 0 ? void 0 : _this$toast3.show({\n                    severity: 'success',\n                    summary: 'Ottimo',\n                    detail: \"Il documento è stato inserito con successo\",\n                    life: 3000\n                  });\n                  localStorage.setItem(\"Prodotti\", []);\n                  localStorage.setItem(\"Cart\", []);\n                  localStorage.setItem(\"OrdineRecuperato\", []);\n                  localStorage.setItem(\"DatiConsegna\", []);\n                  window.sessionStorage.setItem(\"Carrello\", 0);\n                  window.sessionStorage.setItem(\"totCart\", 0);\n                  window.sessionStorage.setItem(\"documentType\", '');\n                  localStorage.setItem(\"datiComodo\", 0);\n                  await APIRequest(\"GET\", \"email/sendDocuments?idDocument=\".concat(res.data.result.id, \"&destEmail=\").concat(this.state.selectedMail, \"&oggetto=\", \"\".concat(this.state.oggetto, \" \").concat(this.state.results.retailers.idRegistry.firstName), \"&cc=\").concat(cc)).then(res => {\n                    var _this$toast4;\n                    console.log(res.data);\n                    (_this$toast4 = this.toast) === null || _this$toast4 === void 0 ? void 0 : _this$toast4.show({\n                      severity: \"success\",\n                      summary: \"Ottimo !\",\n                      detail: \"L'email è stata inviata con successo\",\n                      life: 3000\n                    });\n                    setTimeout(() => {\n                      window.location.pathname = pdvStoricoDocumenti;\n                    }, 3000);\n                  }).catch(e => {\n                    var _this$toast5, _e$response3, _e$response4;\n                    console.log(e);\n                    (_this$toast5 = this.toast) === null || _this$toast5 === void 0 ? void 0 : _this$toast5.show({\n                      severity: \"error\",\n                      summary: \"Siamo spiacenti\",\n                      detail: \"Non \\xE8 stato possibile inviare l'email. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n                      life: 3000\n                    });\n                  });\n                }).catch(e => {\n                  var _this$toast6, _e$response5, _e$response6;\n                  console.log(e);\n                  (_this$toast6 = this.toast) === null || _this$toast6 === void 0 ? void 0 : _this$toast6.show({\n                    severity: 'error',\n                    summary: 'Siamo spiacenti',\n                    detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n                    life: 3000\n                  });\n                });\n              } else {\n                await APIRequest('POST', 'orders/', body).then(res => {\n                  console.log(res.data);\n                  this.toast.show({\n                    severity: 'success',\n                    summary: 'Ottimo',\n                    detail: 'Il suo ordine è stato inserito con successo',\n                    life: 3000\n                  });\n                  localStorage.setItem(\"Prodotti\", []);\n                  localStorage.setItem(\"Cart\", []);\n                  localStorage.setItem(\"OrdineRecuperato\", []);\n                  localStorage.setItem(\"DatiConsegna\", []);\n                  window.sessionStorage.setItem(\"Carrello\", 0);\n                  window.sessionStorage.setItem(\"totCart\", 0);\n                  localStorage.setItem(\"datiComodo\", 0);\n                  var role = localStorage.getItem(\"role\");\n                  if (role === agente) {\n                    setTimeout(() => {\n                      window.location.pathname = agenteGestioneOrdiniAgente;\n                    }, 3000);\n                  } else if (role === affiliato) {\n                    setTimeout(() => {\n                      window.location.pathname = affiliatoVisualizzaOrdini;\n                    }, 3000);\n                  } else if (role === pdv) {\n                    setTimeout(() => {\n                      window.location.pathname = pdvGestioneLogisticaOrdini;\n                    }, 3000);\n                  }\n                }).catch(e => {\n                  console.log(e);\n                  this.setState({\n                    disabled: false\n                  });\n                });\n              }\n            }\n          } else {\n            this.setState({\n              disabled: false\n            });\n            this.toast.show({\n              severity: 'error',\n              summary: 'Attenzione !',\n              detail: \"Inserire data di consegna prima di proseguire con l'ordine\",\n              life: 3000\n            });\n          }\n        } else {\n          this.setState({\n            disabled: false\n          });\n          this.toast.show({\n            severity: 'error',\n            summary: 'Attenzione !',\n            detail: \"Aggiungere prodotti prima di proseguire con l'ordine\",\n            life: 3000\n          });\n        }\n      }\n    };\n    this.state = {\n      result: this.emptyResult,\n      results: null,\n      results2: null,\n      results3: null,\n      value: null,\n      value2: null,\n      data: null,\n      datiConsegna: null,\n      paymentMetod: null,\n      globalFilter: null,\n      globalFilter2: null,\n      selectedPaymentMethod: null,\n      selectedProductsToDiscount: null,\n      selectedProducts: null,\n      disabled: false,\n      checked: false,\n      zeroIva: false,\n      loading: true,\n      deleteResultDialog: false,\n      resultDialog: false,\n      resultDialog2: false,\n      addTribute: true,\n      selectedAddress: [],\n      selectedMail: [],\n      ordersMail: [],\n      discountPayment: [],\n      oggetto: 'Logistica interna per punto vendita',\n      hideSelect: 'col-11 col-sm-12 col-md-10 col-lg-10 col-xl-11',\n      discountTotClass: 'd-none',\n      mex: '',\n      omaggi: '',\n      conditioned_discount: '',\n      role: localStorage.getItem('role'),\n      total: 0,\n      totTax: 0,\n      Iva: 0\n    };\n    this.options = [{\n      name: '% Sconto percentuale',\n      value: 'ScontoPercentuale'\n    }, {\n      name: '€ Sconto a valore',\n      value: 'ScontoAValore'\n    }, {\n      name: 'Nessuno sconto',\n      value: 'NessunoSconto'\n    }];\n    this.minDate = new Date();\n    this.cities = [];\n    this.warehouses = [];\n    this.actionBodyTemplate = this.actionBodyTemplate.bind(this);\n    this.nameBodyTemplate = this.nameBodyTemplate.bind(this);\n    this.nameBodyTemplate2 = this.nameBodyTemplate2.bind(this);\n    this.pIvaBodyTemplate = this.pIvaBodyTemplate.bind(this);\n    this.telBodyTemplate = this.telBodyTemplate.bind(this);\n    this.cityBodyTemplate = this.cityBodyTemplate.bind(this);\n    this.capBodyTemplate = this.capBodyTemplate.bind(this);\n    this.addressBodyTemplate = this.addressBodyTemplate.bind(this);\n    this.externalCodeBodyTemplate = this.externalCodeBodyTemplate.bind(this);\n    this.indirizzoConsegnaBodytemplate = this.indirizzoConsegnaBodytemplate.bind(this);\n    this.noteConsegnaBodytemplate = this.noteConsegnaBodytemplate.bind(this);\n    this.ivaBodyTemplate = this.ivaBodyTemplate.bind(this);\n    this.quantitàBodyTemplate = this.quantitàBodyTemplate.bind(this);\n    this.priceBodyTemplate = this.priceBodyTemplate.bind(this);\n    this.totaleBodyTemplate = this.totaleBodyTemplate.bind(this);\n    this.pcxpkgBodyTemplate = this.pcxpkgBodyTemplate.bind(this);\n    this.imageBodyTemplate = this.imageBodyTemplate.bind(this);\n    this.calcTotal = this.calcTotal.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog(this);\n    this.unitMisBodyTemplate = this.unitMisBodyTemplate.bind(this);\n    this.colliBodyTemplate = this.colliBodyTemplate.bind(this);\n    this.totTaxBodyTemplate = this.totTaxBodyTemplate.bind(this);\n    this.cambioIndirizzo = this.cambioIndirizzo.bind(this);\n    this.cambioNote = this.cambioNote.bind(this);\n    //this.addNewAddress = this.addNewAddress.bind(this);\n    //this.hideAggiungiDest = this.hideAggiungiDest.bind(this);\n    this.onPaymentMethodChange = this.onPaymentMethodChange.bind(this);\n    this.discount_active = this.discount_active.bind(this);\n    this.inflation_active = this.inflation_active.bind(this);\n    this.noteBodyTemplate = this.noteBodyTemplate.bind(this);\n    this.paymentBodyTemplate = this.paymentBodyTemplate.bind(this);\n    this.onKeyUpHandler = this.onKeyUpHandler.bind(this);\n    this.ScontoPercentualeBodyTemplate = this.ScontoPercentualeBodyTemplate.bind(this);\n    this.ScontoAValoreBodyTemplate = this.ScontoAValoreBodyTemplate.bind(this);\n    this.scontoPercEditor = this.scontoPercEditor.bind(this);\n    this.scontoValEditor = this.scontoValEditor.bind(this);\n    this.discountTotClass = this.discountTotClass.bind(this);\n    this.onEditDiscountTotal = this.onEditDiscountTotal.bind(this);\n    this.prodOmaggio = this.prodOmaggio.bind(this);\n    this.hideProdOmaggio = this.hideProdOmaggio.bind(this);\n    this.newColliBodyTemplate = this.newColliBodyTemplate.bind(this);\n    this.colliEditor = this.colliEditor.bind(this);\n    this.onRowEditComplete = this.onRowEditComplete.bind(this);\n    this.onRowModalEditComplete = this.onRowModalEditComplete.bind(this);\n    this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n    this.aggiungiOmaggio = this.aggiungiOmaggio.bind(this);\n    this.zeroIvaHandler = this.zeroIvaHandler.bind(this);\n    this.selectionDiscountHandler = this.selectionDiscountHandler.bind(this);\n  }\n  //Chiamata axios effettuata una sola volta grazie a componentDidMount\n  async componentDidMount() {\n    var indirizzo = '';\n    var mail = '';\n    var methPag = [];\n    //var methPagId = []\n    /* Controllo i dati passati in props per definire quale utente utente sta acquistando */\n    if (this.props.datiConsegna !== null && this.props.datiConsegna !== undefined && window.location.pathname !== '/distributore/carrello' && window.location.pathname !== '/chain/carrello' && !JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n      /* Agente */\n      if (this.props.datiConsegna.data !== undefined) {\n        await APIRequest('GET', 'paymentmethods').then(res => {\n          console.log(res.data);\n          res.data.forEach(element => {\n            var x = {\n              name: element.description,\n              code: element.description\n            };\n            methPag.push(x);\n          });\n          this.setState({\n            paymentMetod: methPag\n          });\n        }).catch(e => {\n          console.log(e);\n        });\n        this.props.datiConsegna.data = new Date(this.props.datiConsegna.data);\n        var datiConsegna = {\n          data: this.props.datiConsegna.data,\n          firstName: this.props.datiConsegna.firstName,\n          idOrder: this.props.datiConsegna.idOrder,\n          note: this.props.datiConsegna.note,\n          stato: this.props.datiConsegna.stato,\n          indirizzo: this.props.results.idRegistry.address\n        };\n        mail = this.props.results.idRegistry.email;\n        indirizzo = this.props.results.idRegistry.address;\n        this.setState({\n          results: this.props.results,\n          results2: this.props.results2,\n          datiConsegna: datiConsegna\n        });\n      } else {\n        /* Affiliato */\n        datiConsegna = {\n          indirizzo: this.props.datiConsegna.address,\n          note: '',\n          stato: 'Registrato'\n        };\n        mail = this.props.datiConsegna.email;\n        indirizzo = this.props.datiConsegna.address;\n        this.setState({\n          results: this.props.datiConsegna,\n          results2: this.props.results2,\n          datiConsegna: datiConsegna\n        });\n      }\n    } else if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n      if ((this.state.role === distributore || this.state.role === chain) && typeof JSON.parse(sessionStorage.getItem(\"idWarehouse\")) === 'number') {\n        await APIRequest(\"GET\", \"warehouses/\").then(res => {\n          var idWarehouse = null;\n          var find = res.data.find(el => el.id === JSON.parse(sessionStorage.getItem(\"idWarehouse\")));\n          idWarehouse = find !== undefined ? {\n            name: find.warehouseName,\n            code: find.id\n          } : null;\n          if (idWarehouse !== null) {\n            JSON.parse(sessionStorage.setItem(\"idWarehouse\", JSON.stringify(idWarehouse)));\n          }\n        }).catch(e => {\n          console.log(e);\n        });\n      }\n      await APIRequest('GET', 'paymentmethods').then(res => {\n        res.data.forEach(element => {\n          var x = {\n            name: element.description,\n            code: element.description\n          };\n          methPag.push(x);\n        });\n        this.setState({\n          paymentMetod: methPag\n        });\n        if (this.props.datiConsegna.supplyingAgreements.length > 0) {\n          var _this$props$datiConse;\n          (_this$props$datiConse = this.props.datiConsegna.supplyingAgreements[0].discount_payment) === null || _this$props$datiConse === void 0 ? void 0 : _this$props$datiConse.map(el => el.paymentMethodName = res.data.find(obj => obj.id === parseInt(el.idPaymentMethod)).description);\n          this.setState({\n            discountPayment: this.props.datiConsegna.supplyingAgreements[0].discount_payment //this.props.datiConsegna.supplyingAgreements[0].discount_payment.map(el => el.fixed ? `Pagamento: ${el.paymentMethodName} sconto ${el.fixed}€` : `Pagamento: ${el.idPaymentMethod} sconto ${el.percent}%`)\n          });\n        }\n      }).catch(e => {\n        console.log(e);\n      });\n      /* Distributore */\n      datiConsegna = {\n        data: '',\n        firstName: this.props.datiConsegna.idRegistry.firstName,\n        idOrder: '',\n        indirizzo: this.props.datiConsegna.idRegistry.address,\n        note: this.props.datiConsegna.note ? this.props.datiConsegna.note : '',\n        stato: 'Registrato'\n      };\n      mail = this.props.datiConsegna.idRegistry.email;\n      indirizzo = this.props.datiConsegna.idRegistry.address;\n      var prodotti = [];\n      this.props.results2.forEach(element => {\n        var _element$conditioned_;\n        var discount = (_element$conditioned_ = element.conditioned_discount) === null || _element$conditioned_ === void 0 ? void 0 : _element$conditioned_.find(el => !el.correlati ? parseInt(el.condizione) === element.quantity : undefined);\n        var x = {};\n        if (discount) {\n          x = {\n            date_end: element.date_end,\n            date_start: element.date_start,\n            discount_active: element.discount_active,\n            conditioned_discount: element.conditioned_discount,\n            discount_note: element.discount_note,\n            id: element.id,\n            idProduct2: element.idProduct !== undefined ? element.idProduct : element.idProduct2,\n            idSupplying: element.idSupplying,\n            moltiplicatore: element.moltiplicatore,\n            qtaIns: element.qtaIns,\n            quantity: element.quantity,\n            sell_in: discount.percent ? element.sell_in - element.sell_in * parseFloat(discount.percent) / 100 : element.sell_in - discount.fixed,\n            supplyCode: element.supplyCode,\n            totale: element.totale,\n            initPrice: element.initPrice,\n            ScontoPercentuale: discount.percent ? discount.percent : 0,\n            ScontoAValore: discount.fixed ? discount.fixed : 0\n          };\n          var prodInCart = [];\n          if (localStorage.getItem(\"Cart\") !== '') {\n            prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n            let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === x.idProduct2.id);\n            if (index !== -1) {\n              prodInCart[index] = x;\n              localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n            }\n          }\n        } else {\n          x = {\n            date_end: element.date_end,\n            date_start: element.date_start,\n            discount_active: element.discount_active,\n            conditioned_discount: element.conditioned_discount,\n            discount_note: element.discount_note,\n            id: element.id,\n            idProduct2: element.idProduct !== undefined ? element.idProduct : element.idProduct2,\n            idSupplying: element.idSupplying,\n            moltiplicatore: element.moltiplicatore,\n            qtaIns: element.qtaIns,\n            quantity: element.quantity,\n            sell_in: element.sell_in,\n            supplyCode: element.supplyCode,\n            totale: element.totale,\n            initPrice: element.initPrice,\n            ScontoPercentuale: 0,\n            ScontoAValore: 0\n          };\n        }\n        prodotti.push(x);\n      });\n      this.setState({\n        results: this.props.results,\n        results2: prodotti,\n        datiConsegna: datiConsegna,\n        selectedPaymentMethod: this.props.datiConsegna.termsPayment ? methPag.find(el => el.name === this.props.datiConsegna.termsPayment) : null,\n        data: this.props.datiConsegna.deliveryDate ? new Date(this.props.datiConsegna.deliveryDate) : null\n      });\n      if (this.props.datiConsegna.iva) {\n        this.zeroIvaHandler({\n          checked: this.props.datiConsegna.iva\n        });\n      }\n    } else {\n      /* PDV */\n      datiConsegna = {\n        data: '',\n        idOrder: '',\n        indirizzo: this.props.results.address !== undefined ? this.props.results.address : this.props.results.idRegistry.address,\n        note: '',\n        stato: 'Registrato'\n      };\n      mail = this.props.results.email;\n      indirizzo = this.props.results.address !== undefined ? this.props.results.address : this.props.results.idRegistry.address;\n      this.setState({\n        results: this.props.results,\n        results2: this.props.results2,\n        datiConsegna: datiConsegna\n      });\n    }\n    if (window.location.pathname !== distributoreCarrello && !JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n      var _this$props$datiConse2, _this$props$datiConse3, _this$props$results$i;\n      var url = '';\n      if (((_this$props$datiConse2 = this.props.datiConsegna) === null || _this$props$datiConse2 === void 0 ? void 0 : _this$props$datiConse2.idRegistry) !== undefined && typeof ((_this$props$datiConse3 = this.props.datiConsegna) === null || _this$props$datiConse3 === void 0 ? void 0 : _this$props$datiConse3.idRegistry) !== 'object') {\n        url = 'destination/?idRegistry=' + this.props.datiConsegna.idRegistry;\n      } else if (((_this$props$results$i = this.props.results.idRegistry) === null || _this$props$results$i === void 0 ? void 0 : _this$props$results$i.id) !== undefined) {\n        url = 'destination/?idRegistry=' + this.props.results.idRegistry.id;\n      } else if (this.props.results.id !== undefined) {\n        url = 'destination/?idRegistry=' + this.props.results.id;\n      }\n      /* Chiamata per le destinazioni */\n      await APIRequest('GET', url).then(res => {\n        console.log(res.data);\n        if (indirizzo !== '') {\n          this.cities.push({\n            name: indirizzo,\n            value: indirizzo\n          });\n        }\n        res.data.forEach(element => {\n          var x = [];\n          if (element.destcap !== null) {\n            x = {\n              name: element.destragsoc + ', ' + element.destind + ' ' + element.destcitta + ' (' + element.destprov + '), ' + element.destcap,\n              code: element.id\n            };\n          } else {\n            x = {\n              name: element.destragsoc + ', ' + element.destind + ' ' + element.destcitta + ' (' + element.destprov + '), ',\n              code: element.id\n            };\n          }\n          this.cities.push(x);\n        });\n        this.setState({\n          selectedMail: mail\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n      await APIRequest('GET', 'notify?ufficio=VENDITE').then(res => {\n        console.log(res.data);\n        var email = res.data.map(el => el.email).join(',');\n        this.setState({\n          ordersMail: email\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    this.calcTotal();\n  }\n  /* Reperiamo il nome del cliente */\n  nameBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.firstName !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.firstName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.idRegistry.firstName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo il nome del prodotto */\n  nameBodyTemplate2(results2) {\n    var _results2$idProduct;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 17\n      }, this), (_results2$idProduct = results2.idProduct2) === null || _results2$idProduct === void 0 ? void 0 : _results2$idProduct.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo la quantità dei prodotti */\n  quantitàBodyTemplate(results2) {\n    if (results2.qtaIns !== undefined) {\n      results2.quantity = results2.qtaIns * parseInt(results2.moltiplicatore);\n    } else {\n      results2.quantity = results2.colli * parseInt(results2.pcsXpackage);\n    }\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Quantità\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 17\n      }, this), results2.quantity]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo il prezzo dei prodotti */\n  priceBodyTemplate(results2) {\n    if (results2.price !== undefined) {\n      if (this.state.role === distributore || this.state.role === chain) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results2.price)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(typeof results2.price !== 'string' ? results2.price : parseFloat(results2.price))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 21\n        }, this);\n      }\n    } else if (results2.initPrice !== undefined) {\n      if (this.state.role === distributore || this.state.role === chain) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results2.sell_in)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results2.initPrice)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      if (this.state.role === distributore || this.state.role === chain) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 25\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 21\n        }, this);\n      }\n    }\n  }\n  /* Reperiamo il totale del prodotto */\n  totaleBodyTemplate(results2) {\n    if (results2.qtaIns !== undefined) {\n      if (results2.price !== undefined) {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 25\n          }, this);\n        }\n      } else {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format((results2.colli !== undefined ? results2.colli : results2.qtaIns) * (results2.pcsXpackage !== undefined ? results2.pcsXpackage : results2.moltiplicatore) * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format((results2.colli !== undefined ? results2.colli : results2.qtaIns) * (results2.pcsXpackage !== undefined ? results2.pcsXpackage : results2.moltiplicatore) * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    } else {\n      if (results2.price !== undefined) {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.price))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.price))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 25\n          }, this);\n        }\n      } else {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Tot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    }\n  }\n  /* Reperiamo il formato richiesto per il prodotto */\n  pcxpkgBodyTemplate(results2) {\n    if (results2.moltiplicatore !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Pezzi per package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 21\n        }, this), results2.moltiplicatore]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Pezzi per package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 21\n        }, this), results2.pcsXpackage]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  noteBodyTemplate(results2) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Note\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 17\n      }, this), results2.discount_note]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo il totale con calcolo dell'iva */\n  totTaxBodyTemplate(results2) {\n    if (results2.qtaIns !== undefined) {\n      if (results2.price !== undefined) {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price) * parseInt(results2.idProduct2.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price) * parseInt(results2.idProduct2.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 25\n          }, this);\n        }\n      } else {\n        if (this.state.role === distributore || this.state.role === chain) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in) * parseInt(results2.idProduct2.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 25\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in) * parseInt(results2.idProduct2.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    } else {\n      if (results2.price !== undefined) {\n        if (this.state.role === distributore || this.state.role === chain) {\n          var _results2$idProduct2;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.price)) + results2.colli * results2.pcsXpackage * parseFloat(results2.price) * parseInt((_results2$idProduct2 = results2.idProduct2) === null || _results2$idProduct2 === void 0 ? void 0 : _results2$idProduct2.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 25\n          }, this);\n        } else {\n          var _results2$idProduct3;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.price)) + results2.colli * results2.pcsXpackage * parseFloat(results2.price) * parseInt((_results2$idProduct3 = results2.idProduct2) === null || _results2$idProduct3 === void 0 ? void 0 : _results2$idProduct3.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 25\n          }, this);\n        }\n      } else {\n        if (this.state.role === distributore || this.state.role === chain) {\n          var _results2$idProduct4;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice)) + results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice) * parseInt((_results2$idProduct4 = results2.idProduct2) === null || _results2$idProduct4 === void 0 ? void 0 : _results2$idProduct4.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 25\n          }, this);\n        } else {\n          var _results2$idProduct5;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.TotTax\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 29\n            }, this), new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice)) + results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice) * parseInt((_results2$idProduct5 = results2.idProduct2) === null || _results2$idProduct5 === void 0 ? void 0 : _results2$idProduct5.iva) / 100)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    }\n  }\n  /* Reperiamo l'iva del prodotto */\n  ivaBodyTemplate(results2) {\n    var _results2$idProduct6;\n    if (((_results2$idProduct6 = results2.idProduct2) === null || _results2$idProduct6 === void 0 ? void 0 : _results2$idProduct6.iva) !== null) {\n      var _results2$idProduct7;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 21\n        }, this), (_results2$idProduct7 = results2.idProduct2) === null || _results2$idProduct7 === void 0 ? void 0 : _results2$idProduct7.iva, \" %\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 21\n        }, this), results2 === null || results2 === void 0 ? void 0 : results2.tax]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  /* Reperiamo il codice esterno del prodotto */\n  externalCodeBodyTemplate(results2) {\n    var _results2$idProduct8;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.exCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 17\n      }, this), (_results2$idProduct8 = results2.idProduct2) === null || _results2$idProduct8 === void 0 ? void 0 : _results2$idProduct8.externalCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo l'immagine del prodotto con una chiamata in base all'id del prodotto */\n  imageBodyTemplate(results2) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 17\n      }, this), results2.idProduct !== undefined ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"immaginiProdTab\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: baseProxy + 'asset/prodotti/' + results2.idProduct + '.jpg',\n          onError: e => e.target.src = Immagine,\n          alt: results2.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"immaginiProdTab\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: baseProxy + 'asset/prodotti/' + (results2.productId !== undefined ? results2.productId : results2.idProduct2.id) + '.jpg',\n          onError: e => e.target.src = Immagine,\n          alt: results2.name !== undefined ? results2.name : results2.idProduct2.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo la partita iva del cliente */\n  pIvaBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.pIva !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.pIva\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.idRegistry.pIva\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo il numero di telefono del cliente */\n  telBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.tel !== undefined) {\n        if (this.state.results.tel.includes('null')) {\n          return \"Nessun numero di telefono disponibile\";\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: this.state.results.tel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 25\n          }, this);\n        }\n      } else {\n        if (this.state.results.idRegistry.tel.includes('null')) {\n          return \"Nessun numero di telefono disponibile\";\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: this.state.results.idRegistry.tel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 25\n          }, this);\n        }\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo la città del cliente */\n  cityBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.city !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.city\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.idRegistry.city\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo il codice postale del cliente */\n  capBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.cap !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.cap\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.idRegistry.cap\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo l'indirizzo del cliente */\n  addressBodyTemplate() {\n    if (this.state.results !== null) {\n      if (this.state.results.address !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.address\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 21\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: this.state.results.idRegistry.address\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 21\n        }, this);\n      }\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo l'indirizzo del cliente */\n  paymentBodyTemplate() {\n    if (this.state.results !== null) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: this.state.results.paymentMetod\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  /* Reperiamo il codice formato del prodotto */\n  unitMisBodyTemplate(results) {\n    var UnitMeasure = '';\n    if (results.pcsXpackage !== undefined) {\n      results.idProduct2.productsPackagings.forEach(element => {\n        if (results.moltiplicatore !== undefined) {\n          if (parseInt(results.moltiplicatore) === element.pcsXPackage) {\n            UnitMeasure = element.unitMeasure;\n          }\n        } else {\n          if (results.pcsXpackage === element.pcsXPackage) {\n            UnitMeasure = element.unitMeasure;\n          }\n        }\n      });\n    } else {\n      if (results.moltiplicatore !== undefined) {\n        results.idProduct2.productsPackagings.forEach(element => {\n          if (parseInt(results.moltiplicatore) === element.pcsXPackage) {\n            UnitMeasure = element.unitMeasure;\n          }\n        });\n      } else {\n        UnitMeasure = results.unitMeasure;\n      }\n    }\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UnitMis\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 17\n      }, this), UnitMeasure]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo i colli ordinati per il prodotto */\n  colliBodyTemplate(results) {\n    if (results.qtaIns !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.QtaIns\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 21\n        }, this), results.qtaIns]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Colli\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 21\n        }, this), results.colli]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  /* Reperiamo i colli ordinati per il prodotto */\n  ScontoPercentualeBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UltScontoPercentuale\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 17\n      }, this), results.ScontoPercentuale, \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo i colli ordinati per il prodotto */\n  ScontoAValoreBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UltScontoAValore\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 17\n      }, this), results.ScontoAValore]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 13\n    }, this);\n  }\n  /* Reperiamo l'indirizzo di consegna di default */\n  indirizzoConsegnaBodytemplate() {\n    if (this.state.datiConsegna !== null) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: this.state.datiConsegna.indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 885,\n        columnNumber: 17\n      }, this);\n    } else {\n      return null;\n    }\n  }\n  /* Reperiamo le note per l'ordine */\n  noteConsegnaBodytemplate() {\n    if (this.state.datiConsegna !== null) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: this.state.datiConsegna.note\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 17\n      }, this);\n    } else {\n      return null;\n    }\n  }\n  discount_active(results2) {\n    var _results2$discount_ac, _results2$discount_ac2;\n    var percent = (_results2$discount_ac = results2.discount_active) === null || _results2$discount_ac === void 0 ? void 0 : _results2$discount_ac.percent;\n    var fixed = (_results2$discount_ac2 = results2.discount_active) === null || _results2$discount_ac2 === void 0 ? void 0 : _results2$discount_ac2.fixed;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ScontoAttivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 17\n      }, this), (percent === null || percent === void 0 ? void 0 : percent.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: percent.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + \"% \"\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false), (fixed === null || fixed === void 0 ? void 0 : fixed.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: fixed.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + '€ '\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 910,\n      columnNumber: 13\n    }, this);\n  }\n  conditioned_discount(results2) {\n    var _results2$conditioned;\n    var scontiCondizionati = [];\n    (_results2$conditioned = results2.conditioned_discount) === null || _results2$conditioned === void 0 ? void 0 : _results2$conditioned.map(el => scontiCondizionati.push(el.correlati ? {\n      acquistando: el.condizione,\n      unità: el.correlati.map(item => item).join(', '),\n      ricevi: Object.keys(el.omaggio)[0],\n      di: Object.values(el.omaggio).map(obj => obj.map(item => \"\".concat(Object.keys(item), \": \").concat(Object.values(item))).join(', '))\n    } : el.condizione ? \"Acquistando \".concat(el.condizione, \" unit\\xE0 \").concat(el.percent ? \"ricevi il \".concat(el.percent, \"% di sconto\") : \"ricevi \".concat(el.fixed, \" di sconto\")) : ''));\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ScontoCondizionato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 17\n      }, this), scontiCondizionati.length > 0 && /*#__PURE__*/_jsxDEV(OverlayPanelGen, {\n        values: scontiCondizionati,\n        label: \"Sconto\",\n        badge: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 21\n      }, this)]\n    }, results2.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 941,\n      columnNumber: 13\n    }, this);\n  }\n  inflation_active(results2) {\n    var _results2$inflation_a, _results2$inflation_a2;\n    var percent = (_results2$inflation_a = results2.inflation_active) === null || _results2$inflation_a === void 0 ? void 0 : _results2$inflation_a.percent;\n    var fixed = (_results2$inflation_a2 = results2.inflation_active) === null || _results2$inflation_a2 === void 0 ? void 0 : _results2$inflation_a2.fixed;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Rincaro\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 17\n      }, this), (percent === null || percent === void 0 ? void 0 : percent.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: percent.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + \"% \"\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false), (fixed === null || fixed === void 0 ? void 0 : fixed.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: fixed.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + '€ '\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 953,\n      columnNumber: 13\n    }, this);\n  }\n  /* Calcolo il totale dei prodotti */\n  calcTotal(discount) {\n    let prodInCart = [];\n    if (localStorage.getItem(\"Cart\") === \"\" || localStorage.getItem(\"Cart\") === '[]') {\n      this.setState({\n        total: '0,00 €',\n        totTax: '0,00 €'\n      });\n    } else {\n      var tot = 0;\n      var totTax = 0;\n      prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n      prodInCart.forEach(element => {\n        if (element.unitPrice !== undefined) {\n          var _element$idProduct;\n          var total = parseFloat(element.unitPrice) * element.colli * element.pcsXpackage + parseFloat(tot);\n          tot = total;\n          totTax = tot + tot * (((_element$idProduct = element.idProduct) === null || _element$idProduct === void 0 ? void 0 : _element$idProduct.iva) !== undefined ? parseInt(element.idProduct.iva) : element.idProduct2 !== undefined ? parseInt(element.idProduct2.iva) : 22) / 100;\n          if (this.state.role === distributore || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n            this.setState({\n              total: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(discount ? discount === 'ScontoPercentuale' ? tot - tot * this.state.value2 / 100 : tot - this.state.value2 : tot),\n              totTax: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(discount ? discount === 'ScontoPercentuale' ? totTax - totTax * this.state.value2 / 100 : totTax - this.state.value2 : totTax),\n              Iva: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(discount ? discount === 'ScontoPercentuale' ? totTax - tot - (totTax - tot) * this.state.value2 / 100 : totTax - tot - this.state.value2 : totTax - tot)\n            });\n          } else {\n            this.setState({\n              total: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(tot),\n              totTax: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(totTax),\n              Iva: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(totTax - tot)\n            });\n          }\n        } else {\n          var _element$idProduct2;\n          total = parseFloat(element.price !== undefined ? element.price : element.sell_in) * element.qtaIns * element.moltiplicatore + parseFloat(tot);\n          tot = total;\n          totTax = tot + tot * (((_element$idProduct2 = element.idProduct) === null || _element$idProduct2 === void 0 ? void 0 : _element$idProduct2.iva) !== undefined ? parseInt(element.idProduct.iva) : element.idProduct2 !== undefined ? parseInt(element.idProduct2.iva) : 22) / 100;\n          if (this.state.role === distributore || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n            this.setState({\n              total: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(discount ? discount === 'ScontoPercentuale' ? tot - tot * this.state.value2 / 100 : tot - this.state.value2 : tot),\n              totTax: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(discount ? discount === 'ScontoPercentuale' ? totTax - totTax * this.state.value2 / 100 : totTax - this.state.value2 : totTax),\n              Iva: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR',\n                maximumFractionDigits: 6\n              }).format(discount ? discount === 'ScontoPercentuale' ? totTax - tot - (totTax - tot) * this.state.value2 / 100 : totTax - tot - this.state.value2 : totTax - tot)\n            });\n          } else {\n            this.setState({\n              total: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(tot),\n              totTax: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(totTax),\n              Iva: new Intl.NumberFormat('de-DE', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(totTax - tot)\n            });\n          }\n        }\n      });\n    }\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  deleteResult() {\n    var prodInCart = [];\n    prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n    let index = prodInCart.findIndex(obj => obj.idProduct2.description === this.state.result.idProduct2.description);\n    prodInCart.splice(index, 1);\n    var totale = [];\n    totale = prodInCart;\n    for (var i = 0; i < prodInCart.length; i++) {\n      if (prodInCart[i].price !== undefined) {\n        totale[i].totale = parseFloat(prodInCart[i].price) * prodInCart[i].quantity;\n      } else {\n        totale[i].totale = parseFloat(prodInCart[i].unitPrice) * prodInCart[i].quantity;\n      }\n    }\n    prodInCart = totale;\n    localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n    this.setState({\n      deleteResultDialog: false,\n      result: this.emptyResult,\n      results2: prodInCart\n    });\n    this.calcTotal();\n    return this.props.getNumbers();\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result\n    });\n    confirmDialog({\n      message: Costanti.RimProd,\n      header: 'Attenzione',\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: \"Si\",\n      rejectLabel: \"No\",\n      accept: this.deleteResult,\n      reject: this.hideDeleteResultDialog\n    });\n  }\n  /* Icona per eliminare un prodotto dal carrello */\n  actionBodyTemplate(rowData) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-rounded\",\n        onClick: () => this.confirmDeleteResult(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1476,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1475,\n      columnNumber: 13\n    }, this);\n  }\n  /* Modifica indirizzo tramite inputText */\n  cambioIndirizzo(e) {\n    var _this$state$datiConse, _this$state$datiConse2, _this$state$datiConse3, _this$state$datiConse4;\n    var datiConsegna = {\n      data: (_this$state$datiConse = this.state.datiConsegna) === null || _this$state$datiConse === void 0 ? void 0 : _this$state$datiConse.data,\n      idOrder: (_this$state$datiConse2 = this.state.datiConsegna) === null || _this$state$datiConse2 === void 0 ? void 0 : _this$state$datiConse2.idOrder,\n      indirizzo: e.target.value.name !== undefined ? e.target.value.name : e.target.value,\n      note: (_this$state$datiConse3 = this.state.datiConsegna) === null || _this$state$datiConse3 === void 0 ? void 0 : _this$state$datiConse3.note,\n      stato: (_this$state$datiConse4 = this.state.datiConsegna) === null || _this$state$datiConse4 === void 0 ? void 0 : _this$state$datiConse4.stato\n    };\n    this.setState({\n      datiConsegna: datiConsegna,\n      selectedAddress: e.target.value\n    });\n  }\n  /* Modifica note tramite inputTextArea */\n  cambioNote(e) {\n    var _this$state$datiConse5, _this$state$datiConse6, _this$state$datiConse7, _this$state$datiConse8;\n    var datiConsegna = {\n      data: (_this$state$datiConse5 = this.state.datiConsegna) === null || _this$state$datiConse5 === void 0 ? void 0 : _this$state$datiConse5.data,\n      idOrder: (_this$state$datiConse6 = this.state.datiConsegna) === null || _this$state$datiConse6 === void 0 ? void 0 : _this$state$datiConse6.idOrder,\n      indirizzo: (_this$state$datiConse7 = this.state.datiConsegna) === null || _this$state$datiConse7 === void 0 ? void 0 : _this$state$datiConse7.indirizzo,\n      note: e.target.value,\n      stato: (_this$state$datiConse8 = this.state.datiConsegna) === null || _this$state$datiConse8 === void 0 ? void 0 : _this$state$datiConse8.stato\n    };\n    this.setState({\n      datiConsegna: datiConsegna\n    });\n  }\n  /* Modifico quantità del prodotto */\n  onRowEditComplete(e, options) {\n    let results2 = [...this.state.results2];\n    results2.forEach(element => {\n      if (element.idProduct !== undefined && options.rowData.idProduct !== undefined) {\n        if (element.idProduct === options.rowData.idProduct) {\n          if (element.qtaIns !== undefined) {\n            element.qtaIns = e.value;\n          } else {\n            element.colli = e.value;\n          }\n        }\n      } else {\n        if (element.idProduct2.id === options.rowData.idProduct2.id) {\n          if (element.qtaIns !== undefined) {\n            element.qtaIns = e.value;\n          } else {\n            element.colli = e.value;\n          }\n        }\n      }\n    });\n    var prodInCart = [];\n    if (localStorage.getItem(\"Cart\") !== '') {\n      prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n      let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === options.rowData.idProduct2.id);\n      if (index !== -1) {\n        prodInCart[index] = options.rowData;\n        localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n      }\n    }\n    this.setState({\n      results2\n    });\n    this.calcTotal();\n  }\n  /* Modifico il formato del prodotto e di conseguenza il moltiplicatore delle quantità */\n  onRowEditComplete2(e, options) {\n    let results2 = [...this.state.results2];\n    results2.forEach(element => {\n      if (element.idProduct === undefined) {\n        if (element.idProduct2.id === options.rowData.idProduct2.id) {\n          if (element.moltiplicatore !== undefined) {\n            element.moltiplicatore = e;\n            element.quantity = element.moltiplicatore * element.colli;\n            element.totale = new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(element.quantity * parseFloat(element.price));\n          } else {\n            element.pcsXpackage = e;\n            element.quantity = element.pcsXpackage * element.colli;\n            element.totale = new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(element.quantity * parseFloat(element.price));\n          }\n        }\n      } else {\n        if (element.idProduct === options.rowData.idProduct) {\n          if (element.moltiplicatore !== undefined) {\n            element.moltiplicatore = e;\n            element.quantity = element.moltiplicatore * element.qtaIns;\n            element.totale = new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(element.quantity * parseFloat(element.price));\n          } else {\n            element.pcsXpackage = e;\n            element.quantity = element.pcsXpackage * element.colli;\n            element.totale = new Intl.NumberFormat('de-DE', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(element.quantity * parseFloat(element.price));\n          }\n        }\n      }\n    });\n    localStorage.setItem(\"Cart\", JSON.stringify(results2));\n    this.setState({\n      results2\n    });\n    this.calcTotal();\n    return this.props.getNumbers();\n  }\n  onEditDiscount(e, options, key) {\n    let results2 = [...this.state.results2];\n    options.rowData[key] = e.value !== null ? e.value : 0;\n    if (key === 'ScontoPercentuale') {\n      options.rowData.sell_in = parseFloat(options.rowData.sell_in) - parseFloat(options.rowData.sell_in) * parseFloat(e.value !== null ? e.value : 0) / 100;\n    } else {\n      options.rowData.sell_in = parseFloat(options.rowData.sell_in) - parseFloat(e.value !== null ? e.value : 0);\n    }\n    var prodInCart = [];\n    if (localStorage.getItem(\"Cart\") !== '') {\n      prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n      let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === options.rowData.idProduct2.id);\n      if (index !== -1) {\n        prodInCart[index] = options.rowData;\n        localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n      }\n    }\n    this.setState({\n      results2\n    });\n    this.calcTotal();\n  }\n  /* InputNumber per la modifica dei colli */\n  colliEditor(options) {\n    if (options.rowData.qtaIns !== undefined) {\n      return /*#__PURE__*/_jsxDEV(InputNumber, {\n        value: options.rowData['qtaIns'],\n        onValueChange: e => this.onRowEditComplete(e, options)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1600,\n        columnNumber: 20\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(InputNumber, {\n        value: options.rowData['colli'],\n        onValueChange: e => this.onRowEditComplete(e, options)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1602,\n        columnNumber: 20\n      }, this);\n    }\n  }\n  scontoPercEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.rowData['ScontoPercentuale'],\n      onValueChange: (e, key) => this.onEditDiscount(e, options, key = 'ScontoPercentuale'),\n      suffix: \"%\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1607,\n      columnNumber: 16\n    }, this);\n  }\n  scontoValEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.rowData['ScontoAValore'],\n      onValueChange: (e, key) => this.onEditDiscount(e, options, key = 'ScontoAValore'),\n      suffix: \"\\u20AC\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1611,\n      columnNumber: 16\n    }, this);\n  }\n  /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n  unitMisEditor(options) {\n    var formato = [];\n    if (options.rowData.idProduct2 !== undefined) {\n      for (const object of options.rowData.idProduct2.productsPackagings) {\n        formato.push({\n          unitMeasure: object.unitMeasure,\n          pcsXPackage: object.pcsXPackage\n        });\n      }\n    } else {\n      for (const obj of options.rowData.product.productsPackagings) {\n        formato.push({\n          unitMeasure: obj.unitMeasure,\n          pcsXPackage: obj.pcsXPackage\n        });\n      }\n    }\n    var placeholder = '';\n    if (options.rowData.moltiplicatore !== undefined) {\n      options.rowData.idProduct2.productsPackagings.forEach(element => {\n        if (parseInt(options.rowData.moltiplicatore) === element.pcsXPackage) placeholder = element.unitMeasure;\n      });\n    }\n    return /*#__PURE__*/_jsxDEV(Dropdown, {\n      value: options.rowData['pcsXpackage'],\n      options: formato,\n      optionLabel: \"unitMeasure\",\n      optionValue: \"pcsXPackage\",\n      onChange: e => this.onRowEditComplete2(e.value, options, formato),\n      style: {\n        width: '100%'\n      },\n      placeholder: placeholder,\n      itemTemplate: option => {\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"product-badge status-\".concat(option.unitMeasure.toLowerCase()),\n          children: option.unitMeasure\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1643,\n          columnNumber: 28\n        }, this);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1640,\n      columnNumber: 13\n    }, this);\n  }\n  /* Apertura dialogo aggiunta nuovo indirizzo */\n  /* addNewAddress() {\n      var idRegistry = 0\n      if (this.props.datiConsegna?.idRegistry !== undefined && window.location.pathname !== distributoreCarrello) {\n          idRegistry = this.props.datiConsegna.idRegistry\n      } else if (this.props.results.idRegistry?.id !== undefined) {\n          idRegistry = this.props.results.idRegistry.id\n      } else if (this.props.results.id !== undefined) {\n          idRegistry = this.props.results.id\n      }\n      window.sessionStorage.setItem(\"datiUtili\", idRegistry)\n      this.setState({\n          resultDialog: true\n      })\n  } */\n  /* Chiusura dialogo aggiunta nuovo indirizzo con chiamata asincrona per mappare gli indirizzi nel dropdown */\n  /* async hideAggiungiDest() {\n      this.setState({\n          resultDialog: false\n      })\n      var url = ''\n      if (this.props.datiConsegna?.idRegistry !== undefined && window.location.pathname !== distributoreCarrello) {\n          url = 'destination/?idRegistry=' + this.props.datiConsegna.idRegistry\n      } else if (this.props.results.idRegistry?.id !== undefined) {\n          url = 'destination/?idRegistry=' + this.props.results.idRegistry.id\n      } else if (this.props.results.id !== undefined) {\n          url = 'destination/?idRegistry=' + this.props.results.id\n      }\n      this.cities = []\n      await APIRequest('GET', url)\n          .then(res => {\n              console.log(res.data);\n              res.data.forEach(element => {\n                  var x = []\n                  if (element.destcap !== null) {\n                      x = {\n                          name: element.destind + ' ' + element.destcitta + ' ' + element.destprov + ' ' + element.destcap, code: element.id\n                      }\n                  } else {\n                      x = {\n                          name: element.destind + ' ' + element.destcitta + ' ' + element.destprov, code: element.id\n                      }\n                  }\n                  this.cities.push(x)\n              })\n          }).catch((e) => {\n              console.log(e)\n          })\n  } */\n  onPaymentMethodChange(e) {\n    if (this.state.discountPayment.length > 0) {\n      var totale = parseFloat(this.state.total.split('€')[0].replace('.', '').replace(',', '.'));\n      var totTax = parseFloat(this.state.totTax.split('€')[0].replace('.', '').replace(',', '.'));\n      var find = this.state.discountPayment.find(el => el.paymentMethodName === e.value.code);\n      if (find !== undefined) {\n        totale = find.percent ? totale - totale * find.percent / 100 : totale - find.fixed;\n        totTax = find.percent ? totTax - totTax * find.percent / 100 : totTax - find.fixed;\n        this.setState({\n          total: new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(totale),\n          totTax: new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(totTax),\n          Iva: new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(totTax - totale)\n        });\n      }\n    }\n    this.setState({\n      selectedPaymentMethod: e.value\n    });\n  }\n  onKeyUpHandler(e) {\n    var mex = 'Inseriti: ' + e.target.value.length + ' di ' + e.currentTarget.maxLength + ' caratteri';\n    this.setState({\n      mex: mex\n    });\n  }\n  discountTotClass(e) {\n    this.setState({\n      value: e.value\n    });\n    if (e.value !== 'NessunoSconto') {\n      this.setState({\n        discountTotClass: ''\n      });\n    } else {\n      this.setState({\n        discountTotClass: 'd-none'\n      });\n      this.calcTotal();\n    }\n  }\n  onEditDiscountTotal() {\n    this.calcTotal(this.state.value);\n  }\n  async prodOmaggio() {\n    var conditioned_discount = [];\n    this.state.results2.forEach(element => {\n      var _element$conditioned_2;\n      (_element$conditioned_2 = element.conditioned_discount) === null || _element$conditioned_2 === void 0 ? void 0 : _element$conditioned_2.map(el => conditioned_discount.push(el));\n    });\n    conditioned_discount = onlyUniqueCondizioneCorrelatiOmaggio(conditioned_discount);\n    var find = conditioned_discount.find(item => item.omaggio !== undefined);\n    if (find !== undefined) {\n      var omaggi = [];\n      if (conditioned_discount.length > 0) {\n        var idSupplier = JSON.parse(window.sessionStorage.getItem(\"idSupplier\"));\n        await APIRequest(\"GET\", \"supplyingproduct/?idSupplying=\".concat(idSupplier, \"&active=true\")).then(res => {\n          var prodCorr = [];\n          conditioned_discount.forEach(element => {\n            var verifyCondition = this.state.results2.map(el => el.quantity).reduce((prev, curr) => prev + curr, 0);\n            if (verifyCondition >= parseInt(element.condizione)) {\n              omaggi.push(\"Condizione: \".concat(element.condizione, \" Correlati: \").concat(element.correlati.map(el => el).join(', '), \" Omaggi: (\").concat(Object.keys(element.omaggio)[0], \") \").concat(Object.values(element.omaggio)[0].map(el => Object.keys(el)[0])));\n            }\n            Object.values(element.omaggio)[0].forEach(obj => {\n              res.data.find(el => el.idProduct.externalCode === Object.keys(obj)[0] ? prodCorr.push(_objectSpread(_objectSpread({}, el), {}, {\n                newColli: 0\n              })) : null);\n            });\n          });\n          this.setState({\n            results3: prodCorr,\n            resultDialog2: true,\n            omaggi: omaggi,\n            conditioned_discount: conditioned_discount\n          });\n        }).catch(e => {\n          var _e$response7, _e$response8;\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare il listino per l'aggiunta dei prodotti omaggio. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n            life: 3000\n          });\n        });\n      } else {\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non sono disponibili omaggi per i prodotti selezionati\",\n          life: 3000\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non sono previsti omaggi per i prodotti selezionati\",\n        life: 3000\n      });\n    }\n  }\n  hideProdOmaggio() {\n    this.setState({\n      resultDialog2: false\n    });\n  }\n  /* Reperiamo i colli ordinati per il prodotto */\n  newColliBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Colli\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1791,\n        columnNumber: 17\n      }, this), results.newColli]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1790,\n      columnNumber: 13\n    }, this);\n  }\n  /* InputNumber per la modifica dei colli */\n  newColliEditor(options) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      value: options.rowData['newColli'],\n      onValueChange: e => this.onRowModalEditComplete(e, options)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1798,\n      columnNumber: 16\n    }, this);\n  }\n  onRowModalEditComplete(e, options) {\n    options.rowData.newColli = e.value;\n  }\n  selectionChangeHandler(e) {\n    var filter = e.value.filter(element => element.newColli > 0);\n    if (filter.length < e.value.length) {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I prodotti selezionati devono avere quantità inputata maggiore di 0\",\n        life: 3000\n      });\n    } else {\n      var quantità = 0;\n      var qtaTotProd = 0;\n      var nOmaggi = [];\n      var omaggiTot = [];\n      qtaTotProd = this.state.results2.map(el => el.quantity).reduce((prev, curr) => prev + curr, 0);\n      var find = this.state.conditioned_discount.find(el => parseInt(el.condizione) >= qtaTotProd);\n      if (find !== undefined) {\n        nOmaggi = parseInt(Object.keys(find.omaggio)[0]);\n        omaggiTot = Object.values(find.omaggio)[0];\n        filter.forEach(el => {\n          var maximal = omaggiTot.find(item => Object.keys(item)[0] === el.idProduct.externalCode);\n          if (Object.values(maximal)[0] !== -1 && Object.values(maximal)[0] > el.newColli) {\n            this.toast.show({\n              severity: \"warn\",\n              summary: \"Attenzione!\",\n              detail: \"La quantità inserita supera il numero di omaggi a disposizione per questo prodotto\",\n              life: 3000\n            });\n          } else {\n            var found = el.idProduct.productsPackagings.find(obj => obj.pcsXPackage === 1);\n            el.moltiplicatore = found !== undefined ? found.pcsXPackage : el.idProduct.productsPackagings[0].pcsXPackage;\n            quantità += el.newColli;\n          }\n        });\n        if (quantità > nOmaggi) {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"La quantità inserita supera il numero di omaggi a disposizione\",\n            life: 3000\n          });\n        } else {\n          this.setState({\n            selectedProducts: filter\n          });\n        }\n      } else {\n        this.toast.show({\n          severity: \"warn\",\n          summary: \"Attenzione!\",\n          detail: \"Non è stato raggiunto il target necessario per accedere ai prodotti omaggio rivedi le condizioni di quantità minime prima di procedere\",\n          life: 3000\n        });\n      }\n    }\n  }\n  selectionDiscountHandler(e) {\n    this.setState({\n      selectedProductsToDiscount: e.value\n    });\n  }\n  /* Funzione asincrona con chiamata post sulle destinazioni */\n  async aggiungiOmaggio() {\n    console.log(this.state.selectedProducts, this.state.selectedProductsToDiscount);\n    var tableProd = [...this.state.results2];\n    this.state.selectedProducts.forEach(element => {\n      //Valutare accisa nello sconto se indicato nell'ogetto \n      var found = tableProd.find(el => el.id === element.id);\n      if (found !== undefined) {\n        found.qtaIns += element.newColli;\n        found.quantity = found.qtaIns * parseInt(found.moltiplicatore);\n      } else {\n        element.initPrice = element.sell_in;\n        element.idProduct2 = element.idProduct;\n        element.ScontoPercentuale = 0;\n        element.ScontoAValore = 0;\n        var pack = element.idProduct2.productsPackagings.find(el => el.default === true);\n        if (pack) {\n          element.moltiplicatore = pack.pcsXPackage;\n        } else {\n          element.moltiplicatore = element.idProduct2.productsPackagings[0].pcsXPackage;\n        }\n        element.qtaIns = element.newColli;\n        element.quantity = element.qtaIns * element.moltiplicatore;\n        tableProd = [...tableProd, element];\n      }\n    });\n    this.state.selectedProductsToDiscount.forEach(el => {\n      var find = tableProd.find(obj => obj.id === el.id);\n      if (find !== undefined) {\n        find.sell_in = find.totale / find.quantity;\n      }\n    });\n    console.log(tableProd);\n    this.setState({\n      results2: tableProd,\n      resultDialog2: false\n    });\n    this.calcTotal();\n  }\n  zeroIvaHandler(e) {\n    this.setState({\n      zeroIva: e.checked\n    });\n    var prod = [...this.state.results2];\n    var prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n    if (e.checked) {\n      prod.forEach(el => {\n        el.idProduct2.ivaPrev = el.idProduct2.iva;\n        el.idProduct2.iva = '0';\n      });\n      prodInCart.forEach(element => {\n        if (element.idProduct) {\n          element.idProduct.ivaPrev = element.idProduct.iva;\n          element.idProduct.iva = '0';\n        } else {\n          element.idProduct2.ivaPrev = element.idProduct2.iva;\n          element.idProduct2.iva = '0';\n        }\n      });\n    } else {\n      prod.forEach(element => {\n        element.idProduct2.iva = element.idProduct2.ivaPrev;\n        delete element.idProduct2.ivaPrev;\n      });\n      prodInCart.forEach(element => {\n        console.log(element);\n        if (element.idProduct) {\n          element.idProduct.iva = element.idProduct.ivaPrev;\n          delete element.idProduct.ivaPrev;\n        } else {\n          element.idProduct2.iva = element.idProduct2.ivaPrev;\n          delete element.idProduct2.ivaPrev;\n        }\n      });\n    }\n    this.setState({\n      results2: prod\n    });\n    localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n    this.calcTotal();\n  }\n  render() {\n    var _this$state$datiConse9, _this$state$omaggi;\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta \n    /* const resultDialogFooter = (\n        <React.Fragment>\n            <Button className=\"p-button-text\" onClick={this.hideAggiungiDest} > {Costanti.Chiudi} </Button>\n        </React.Fragment>\n    ); */\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta \n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideProdOmaggio,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1947,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1946,\n      columnNumber: 13\n    }, this);\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1955,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              className: \"w-100\",\n              type: \"search\",\n              onInput: e => this.setState({\n                globalFilter: e.target.value\n              }),\n              placeholder: \"Cerca...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1956,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1954,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1953,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1952,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1951,\n      columnNumber: 13\n    }, this);\n    const header2 = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1967,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(InputText, {\n              className: \"w-100\",\n              type: \"search\",\n              onInput: e => this.setState({\n                globalFilter2: e.target.value\n              }),\n              placeholder: \"Cerca...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1968,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1966,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1965,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1964,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1963,\n      columnNumber: 13\n    }, this);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-order-details px-3 px-lg-0 mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1976,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"justify-content-center mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [window.location.pathname !== distributoreCarrello && this.state.role !== chain ? /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"p-text-center p-text-bold\",\n            children: Costanti.DatCli\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1981,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"p-text-center p-text-bold\",\n            children: Costanti.DatForn\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1982,\n            columnNumber: 31\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-md-6 mb-4 mb-md-0\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-user mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1987,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.Nome\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1987,\n                    columnNumber: 104\n                  }, this), \": \", this.nameBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1987,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi pi-mobile mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1988,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.Tel\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1988,\n                    columnNumber: 109\n                  }, this), \": \", this.telBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1988,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi pi-credit-card mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1989,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.pIva\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1989,\n                    columnNumber: 114\n                  }, this), \": \", this.pIvaBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1989,\n                  columnNumber: 37\n                }, this), (window.location.pathname === distributoreCarrello || this.state.role === chain) && /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-euro mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1991,\n                    columnNumber: 73\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.paymentMetod\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1991,\n                    columnNumber: 108\n                  }, this), \": \", this.paymentBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1991,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1986,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1985,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-directions mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1997,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.Indirizzo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1997,\n                    columnNumber: 110\n                  }, this), \": \", this.addressBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1997,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-map-marker mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1998,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.Città\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1998,\n                    columnNumber: 110\n                  }, this), \": \", this.cityBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1998,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"list-group-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-compass mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1999,\n                    columnNumber: 69\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: Costanti.CodPost\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1999,\n                    columnNumber: 107\n                  }, this), \": \", this.capBodyTemplate()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1999,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1996,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1995,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1984,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row justify-content-center mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2006,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"datatable-responsive-demo datatable-responsive-order wrapper mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"p-text-center p-text-bold\",\n                  children: Costanti.Prodotti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2008,\n                  columnNumber: 37\n                }, this), localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) && /*#__PURE__*/_jsxDEV(DataTable, {\n                  className: \"p-datatable-responsive-demo editable-prices-table mt-4\",\n                  ref: el => this.dt = el,\n                  value: this.state.results2,\n                  dataKey: \"id\",\n                  editMode: \"row\",\n                  onRowEditComplete: this.onRowEditComplete,\n                  responsiveLayout: \"scroll\",\n                  autoLayout: \"true\",\n                  csvSeparator: \";\",\n                  children: [/*#__PURE__*/_jsxDEV(Column, {\n                    field: \"image\",\n                    body: this.imageBodyTemplate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2014,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"description\",\n                    header: Costanti.Prodotto,\n                    body: this.nameBodyTemplate2,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2015,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"externalCode\",\n                    header: Costanti.exCode,\n                    body: this.externalCodeBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2016,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"quantity\",\n                    header: Costanti.UnitMis,\n                    body: this.unitMisBodyTemplate,\n                    editor: options => this.unitMisEditor(options),\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2017,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"pcspkgs\",\n                    header: \"Pezzi per package\",\n                    body: this.pcxpkgBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2018,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"quantity\",\n                    header: Costanti.Colli,\n                    body: this.colliBodyTemplate,\n                    editor: options => this.colliEditor(options),\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2019,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"quantity\",\n                    header: Costanti.Quantità,\n                    body: this.quantitàBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2020,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"price\",\n                    header: Costanti.Prezzo,\n                    body: this.priceBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2021,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"totale\",\n                    header: Costanti.Tot,\n                    body: this.totaleBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2022,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"iva\",\n                    header: Costanti.Iva,\n                    body: this.ivaBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2023,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"totTax\",\n                    header: Costanti.TotTax,\n                    body: this.totTaxBodyTemplate,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2024,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    rowEditor: true,\n                    headerStyle: {\n                      width: '10%',\n                      minWidth: '8rem'\n                    },\n                    bodyStyle: {\n                      textAlign: 'center'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2025,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    body: this.actionBodyTemplate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2026,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2011,\n                  columnNumber: 41\n                }, this), (localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-end mr-4\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      className: \"buttonPlus p-button\",\n                      onClick: () => this.prodOmaggio(),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"pi pi-plus mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2032,\n                        columnNumber: 124\n                      }, this), \"Aggiungi prodotto omaggio\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2032,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2031,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n                    className: \"p-datatable-responsive-demo editable-prices-table mt-4\",\n                    ref: el => this.dt = el,\n                    value: this.state.results2,\n                    dataKey: \"id\",\n                    editMode: \"row\",\n                    onRowEditComplete: this.onRowEditComplete,\n                    responsiveLayout: \"scroll\",\n                    autoLayout: \"true\",\n                    csvSeparator: \";\",\n                    children: [/*#__PURE__*/_jsxDEV(Column, {\n                      field: \"image\",\n                      body: this.imageBodyTemplate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2037,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"description\",\n                      header: Costanti.Prodotto,\n                      body: this.nameBodyTemplate2,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2038,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"externalCode\",\n                      header: Costanti.exCode,\n                      body: this.externalCodeBodyTemplate,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2039,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"quantity\",\n                      header: Costanti.UnitMis,\n                      body: this.unitMisBodyTemplate,\n                      editor: options => this.unitMisEditor(options),\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2040,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"quantity\",\n                      header: Costanti.Colli,\n                      body: this.colliBodyTemplate,\n                      editor: options => this.colliEditor(options),\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2041,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"quantity\",\n                      header: Costanti.Quantità,\n                      body: this.quantitàBodyTemplate,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2042,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"price\",\n                      header: Costanti.Prezzo,\n                      body: this.priceBodyTemplate,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2043,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"discount_active\",\n                      header: Costanti.ScontoAttivo,\n                      body: this.discount_active,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2044,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"inflation_active\",\n                      header: Costanti.Rincaro,\n                      body: this.inflation_active,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2045,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"conditioned_discount\",\n                      header: Costanti.ScontoCondizionato,\n                      body: this.conditioned_discount,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2046,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"totale\",\n                      header: Costanti.Tot,\n                      body: this.totaleBodyTemplate,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2047,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"discount_note\",\n                      header: Costanti.Note,\n                      body: this.noteBodyTemplate,\n                      sortable: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2048,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"ScontoPercentuale\",\n                      header: Costanti.UltScontoPercentuale,\n                      body: this.ScontoPercentualeBodyTemplate,\n                      editor: options => this.scontoPercEditor(options)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2049,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      field: \"ScontoAValore\",\n                      header: Costanti.UltScontoAValore,\n                      body: this.ScontoAValoreBodyTemplate,\n                      editor: options => this.scontoValEditor(options)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2050,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      rowEditor: true,\n                      headerStyle: {\n                        width: '10%',\n                        minWidth: '8rem'\n                      },\n                      bodyStyle: {\n                        textAlign: 'center'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2051,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Column, {\n                      body: this.actionBodyTemplate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2052,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2034,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2056,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2007,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2005,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end flex-column align-items-end w-100\",\n              children: [(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-end flex-column w-100 mr-5 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n                  className: \"px-3\",\n                  value: this.state.value,\n                  options: this.options,\n                  onChange: e => this.discountTotClass(e),\n                  optionLabel: \"name\",\n                  optionValue: \"value\",\n                  placeholder: \"Scontistica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2062,\n                  columnNumber: 41\n                }, this), this.state.value && this.state.discountTotClass !== 'd-none' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: this.state.value2,\n                    onValueChange: e => this.setState({\n                      value2: e.value\n                    }),\n                    suffix: this.state.value === 'ScontoPercentuale' ? '%' : '€'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2065,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    className: \"buttonPlus p-button-rounded ml-2 p-2\",\n                    onClick: () => this.onEditDiscountTotal(),\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-check-circle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2066,\n                      columnNumber: 149\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2066,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2064,\n                  columnNumber: 45\n                }, this), (localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-end flex-row w-100 mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-tag mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2071,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"binary\",\n                    className: \"mr-2 mb-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: Costanti.zeroIVACheck\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2073,\n                      columnNumber: 95\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2073,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                    inputId: \"binary\",\n                    checked: this.state.zeroIva,\n                    onChange: e => this.zeroIvaHandler(e)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2074,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2070,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2061,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-5\",\n                children: [\" \", /*#__PURE__*/_jsxDEV(\"b\", {\n                  children: Costanti.Tot\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2079,\n                  columnNumber: 57\n                }, this), \" \", this.state.total, \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2079,\n                columnNumber: 33\n              }, this), this.state.zeroIva ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-5\",\n                children: /*#__PURE__*/_jsxDEV(\"del\", {\n                  children: [\" \", /*#__PURE__*/_jsxDEV(\"b\", {\n                    className: \"ml-2\",\n                    children: Costanti.Iva\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2082,\n                    columnNumber: 70\n                  }, this), \" \", isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.Iva, \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2082,\n                  columnNumber: 64\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2082,\n                columnNumber: 41\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-5\",\n                children: [\" \", /*#__PURE__*/_jsxDEV(\"b\", {\n                  className: \"ml-2\",\n                  children: Costanti.Iva\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2084,\n                  columnNumber: 65\n                }, this), \" \", isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.Iva, \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2084,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                  className: \"ml-2\",\n                  children: Costanti.TotTax\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2087,\n                  columnNumber: 56\n                }, this), \" \", isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.totTax, \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2087,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2059,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2004,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row justify-content-center mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"p-text-center p-text-bold col-12\",\n              children: Costanti.DatiCons\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2091,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center col-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 mb-4 mb-md-0\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-group-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: [localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-sm-6 mb-3 mb-lg-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"pi pi-user mr-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2103,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: Costanti.Indirizzo\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2105,\n                              columnNumber: 65\n                            }, this), \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2101,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-md-10 col-xl-10\",\n                            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n                              value: this.state.selectedAddress,\n                              options: this.cities,\n                              onChange: e => {\n                                this.cambioIndirizzo(e);\n                              },\n                              optionLabel: \"name\",\n                              placeholder: \"Seleziona indirizzo\",\n                              emptyMessage: \"Non ci sono indirizzi disponibili. Proseguire per utilizzare quello di default del cliente\",\n                              filterBy: \"name\",\n                              filter: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2110,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2109,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2100,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2099,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd')) ? 'col-12' : 'col-12 col-sm-6',\n                        children: [localStorage.getItem('role') !== agente && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"pi pi-calendar mr-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2121,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: Costanti.Data\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2123,\n                              columnNumber: 65\n                            }, this), \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2119,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-md-10 col-xl-10\",\n                            children: /*#__PURE__*/_jsxDEV(Calendar, {\n                              className: \"w-auto\",\n                              dateFormat: \"dd/mm/yy\",\n                              minDate: this.minDate,\n                              placeholder: \"Data di consegna\",\n                              value: this.state.data,\n                              onChange: e => this.setState({\n                                data: e.value\n                              }),\n                              readOnlyInput: true,\n                              showIcon: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2128,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2127,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2118,\n                          columnNumber: 57\n                        }, this), localStorage.getItem('role') === agente && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-3\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"pi pi-euro mr-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2136,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: Costanti.ModalitàPag\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2138,\n                              columnNumber: 65\n                            }, this), \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2134,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-md-10 col-xl-9\",\n                            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n                              value: this.state.selectedPaymentMethod,\n                              options: this.state.paymentMetod,\n                              onChange: this.onPaymentMethodChange,\n                              optionLabel: \"name\",\n                              placeholder: \"Seleziona modalit\\xE0 di pagamento\",\n                              filter: true,\n                              filterBy: \"name\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2143,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2142,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2133,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2116,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2097,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2096,\n                    columnNumber: 41\n                  }, this), (localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) && /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-group-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row w-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2 pr-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"pi pi-euro mr-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2155,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: Costanti.ModalitàPag\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2157,\n                          columnNumber: 57\n                        }, this), \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2153,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-md-10 col-xl-10 pl-0\",\n                        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n                          value: this.state.selectedPaymentMethod,\n                          options: this.state.paymentMetod,\n                          onChange: this.onPaymentMethodChange,\n                          optionLabel: \"name\",\n                          placeholder: \"Seleziona modalit\\xE0 di pagamento\",\n                          filter: true,\n                          filterBy: \"name\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2162,\n                          columnNumber: 57\n                        }, this), this.state.discountPayment.length > 0 && this.state.discountPayment.map(el => {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"p-3\",\n                            children: [Costanti.Pagamento, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: el.paymentMethodName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2166,\n                              columnNumber: 113\n                            }, this), \" \", Costanti.Sconto, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"\".concat(el.fixed ? \"\".concat(el.fixed, \"\\u20AC\") : \"\".concat(el.percent, \"%\"))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2166,\n                              columnNumber: 172\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2166,\n                            columnNumber: 69\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2161,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2152,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2151,\n                    columnNumber: 45\n                  }, this), localStorage.getItem('role') === affiliato && JSON.parse(sessionStorage.getItem('affForOrd')) && /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-group-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row w-100\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center col-12 col-lg-6 mb-1 mt-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-md-0 col-12\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"pi pi-envelope mr-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2180,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                              htmlFor: \"binary\",\n                              className: \"mr-2 mb-0\",\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: Costanti.mailAffCheck\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2182,\n                                columnNumber: 111\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2182,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                              inputId: \"binary\",\n                              checked: this.state.checked,\n                              onChange: e => this.setState({\n                                checked: e.checked\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2183,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2179,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2178,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2177,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2176,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2175,\n                    columnNumber: 45\n                  }, this), localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) && /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-group-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-lg-6 mb-1 mt-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-md-0 col-12 col-md-6 col-lg-2 col-xl-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"pi pi-envelope mr-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2197,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: Costanti.Email\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2199,\n                              columnNumber: 65\n                            }, this), \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2195,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"col-12 col-md-6 col-lg-10 col-xl-10\",\n                            children: /*#__PURE__*/_jsxDEV(InputText, {\n                              className: \"w-50\",\n                              value: this.state.selectedMail,\n                              onChange: e => this.setState({\n                                selectedMail: e.target.value\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2204,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2203,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2194,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2193,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center col-12 col-lg-6 mb-1 mt-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"row\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center mb-3 mb-md-0 col-12\",\n                            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                              inputId: \"binary\",\n                              checked: this.state.checked,\n                              onChange: e => this.setState({\n                                checked: e.checked\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2211,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                              htmlFor: \"binary\",\n                              className: \"ml-2\",\n                              children: Costanti.MailConfOrd\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2212,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2210,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2209,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2208,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2192,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2191,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-group-item d-flex align-items-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"row w-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-3 mb-md-0 col-12 col-sm-2 col-xl-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"pi pi-pencil mr-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2223,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: Costanti.Note\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2224,\n                          columnNumber: 53\n                        }, this), \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2221,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"col-12 col-md-10 col-xl-11\",\n                        children: [/*#__PURE__*/_jsxDEV(InputTextarea, {\n                          maxLength: 240,\n                          onKeyUp: e => this.onKeyUpHandler(e),\n                          className: \"inputNote\",\n                          value: (_this$state$datiConse9 = this.state.datiConsegna) === null || _this$state$datiConse9 === void 0 ? void 0 : _this$state$datiConse9.note,\n                          onChange: e => {\n                            this.cambioNote(e);\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2227,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-end\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: this.state.mex\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2228,\n                            columnNumber: 97\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2228,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2226,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2220,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2219,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2094,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2093,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2092,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2090,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row mt-5 mb-5\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"mr-4 goBackBtn\",\n                    onClick: () => window.history.back(),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-chevron-left mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2241,\n                      columnNumber: 115\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mx-auto\",\n                      children: [\" \", Costanti.Indietro, \" \"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2241,\n                      columnNumber: 158\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2241,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    onClick: this.Invia,\n                    disabled: this.state.disabled,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mx-auto\",\n                      children: [\" \", Costanti.salvaOrd, \" \"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2242,\n                      columnNumber: 102\n                    }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-check ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2242,\n                      columnNumber: 156\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2242,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2240,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2238,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2237,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2236,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1978,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1977,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.AggProdOmaggio,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hideProdOmaggio,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modalBody\",\n          children: this.state.addTribute ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [this.state.omaggi ? (_this$state$omaggi = this.state.omaggi) === null || _this$state$omaggi === void 0 ? void 0 : _this$state$omaggi.map((el, key) => {\n              return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center w-100 border p-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"\",\n                    children: \"\".concat(el)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2262,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2261,\n                  columnNumber: 45\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2260,\n                columnNumber: 41\n              }, this);\n            }) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-grid p-fluid mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"datatable-responsive-demo wrapper w-100\",\n                children: /*#__PURE__*/_jsxDEV(DataTable, {\n                  className: \"p-datatable-responsive-demo editable-prices-table\",\n                  value: this.state.results3,\n                  globalFilter: this.state.globalFilter,\n                  header: header,\n                  dataKey: \"id\",\n                  editMode: \"row\",\n                  onRowEditComplete: this.onRowModalEditComplete,\n                  responsiveLayout: \"scroll\",\n                  autoLayout: \"true\",\n                  paginator: true,\n                  rows: 10,\n                  rowsPerPageOptions: [10, 20, 50],\n                  selection: this.state.selectedProducts,\n                  onSelectionChange: e => this.selectionChangeHandler(e),\n                  emptyMessage: \"Non ci sono elementi da visualizzare\",\n                  selectionMode: \"checkbox\",\n                  csvSeparator: \";\",\n                  children: [/*#__PURE__*/_jsxDEV(Column, {\n                    selectionMode: \"multiple\",\n                    headerStyle: {\n                      width: \"3em\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2291,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"idProduct.externalCode\",\n                    header: Costanti.exCode,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2292,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"idProduct.description\",\n                    header: Costanti.Prodotto,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2293,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"newColli\",\n                    header: Costanti.Quantità,\n                    body: this.newColliBodyTemplate,\n                    editor: options => this.newColliEditor(options)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2294,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    className: \"modActionColumn\",\n                    rowEditor: true,\n                    headerStyle: {\n                      width: '7rem'\n                    },\n                    bodyStyle: {\n                      textAlign: 'center'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2295,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2272,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2271,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2270,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mb-2 mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                id: \"invia\",\n                className: \"p-button saveList justify-content-center float-right ionicon mx-0 w-auto\",\n                onClick: () => this.state.selectedProducts ? this.setState({\n                  addTribute: false\n                }) : this.toast.show({\n                  severity: \"warn\",\n                  summary: \"Attenzione!\",\n                  detail: \"Selezionare prodotti prima di procedere\",\n                  life: 3000\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-angle-double-right mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2306,\n                  columnNumber: 41\n                }, this), Costanti.Procedi]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2301,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2299,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-grid p-fluid\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-center my-3 w-100\",\n                children: Costanti.SelProdScont\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2314,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"datatable-responsive-demo wrapper w-100\",\n                children: /*#__PURE__*/_jsxDEV(DataTable, {\n                  className: \"p-datatable-responsive-demo editable-prices-table\",\n                  value: this.state.results2,\n                  globalFilter: this.state.globalFilter2,\n                  header: header2,\n                  dataKey: \"id\",\n                  responsiveLayout: \"scroll\",\n                  autoLayout: \"true\",\n                  paginator: true,\n                  rows: 10,\n                  rowsPerPageOptions: [10, 20, 50],\n                  selection: this.state.selectedProductsToDiscount,\n                  onSelectionChange: e => this.selectionDiscountHandler(e),\n                  emptyMessage: \"Non ci sono elementi da visualizzare\",\n                  selectionMode: \"checkbox\",\n                  csvSeparator: \";\",\n                  children: [/*#__PURE__*/_jsxDEV(Column, {\n                    selectionMode: \"multiple\",\n                    headerStyle: {\n                      width: \"3em\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2333,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"idProduct2.externalCode\",\n                    header: Costanti.exCode,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2334,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Column, {\n                    field: \"idProduct2.description\",\n                    header: Costanti.Prodotto,\n                    sortable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2335,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2316,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2315,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2313,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 col-md-6 d-flex justify-content-center justify-content-md-end mb-2 mb-md-0\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  className: \"p-button ionicon mx-0 w-auto justify-content-center justify-content-md-start\",\n                  onClick: () => this.setState({\n                    addTribute: true\n                  }),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-angle-double-left mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2345,\n                    columnNumber: 45\n                  }, this), Costanti.Indietro]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2341,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2340,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 col-md-6 d-flex justify-content-center justify-content-md-start\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  className: \"p-button ionicon mx-0 w-auto justify-content-center justify-content-md-start\",\n                  onClick: this.aggiungiOmaggio,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"pi pi-check mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2354,\n                    columnNumber: 45\n                  }, this), Costanti.Conferma]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2350,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2349,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2339,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2255,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2254,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1975,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default connect(null, {\n  getNumbers\n})(CarrelloGen);", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "DataTable", "Column", "APIRequest", "baseProxy", "getNumbers", "connect", "InputTextarea", "Toast", "InputNumber", "Dropdown", "confirmDialog", "Dialog", "Checkbox", "InputText", "Calendar", "affiliato", "affiliatoDocumentiOrdine<PERSON>cquisto", "affiliatoVisualizza<PERSON>", "agente", "agenteGestioneOrdiniAgente", "chain", "chainDocumentiOrdineAcquisto", "distributore", "distributoreCarrello", "distributoreDocumentiOrdineAcquisto", "pdv", "pdvGestioneLogistica<PERSON>rdini", "pdvStoricoDocumenti", "ring", "onlyUniqueCondizioneCorrelatiOmaggio", "OverlayPanelGen", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CarrelloGen", "constructor", "props", "emptyResult", "id", "firstName", "address", "pIva", "email", "<PERSON><PERSON><PERSON><PERSON>", "idProduct2", "createAt", "updateAt", "Invia", "setState", "disabled", "prodotti", "unitMeasure", "pcsXPackage", "colli", "state", "<PERSON><PERSON><PERSON><PERSON>", "length", "role", "JSON", "parse", "sessionStorage", "getItem", "toast", "show", "severity", "summary", "detail", "life", "results2", "for<PERSON>ach", "element", "productsPackagings", "undefined", "el", "moltiplicatore", "parseFloat", "quantity", "pcsXpackage", "price", "tot", "unitPrice", "sell_in", "FEE", "priceDad", "feature", "window", "location", "pathname", "find", "push", "total", "totalTaxed", "zeroIva", "iva", "idProductsPackaging", "colliPreventivo", "tax", "x", "_objectSpread", "qtaIns", "colliConsuntivo", "parseInt", "totale", "fee", "body", "numberOfCustomers", "dati<PERSON>ons<PERSON>na", "stato", "deliveryDestination", "<PERSON><PERSON><PERSON><PERSON>", "status", "note", "deliveryDate", "data", "paymentStatus", "replace", "orderDate", "Date", "idRetailer", "termsPayment", "results", "idRegistry", "paymentMetod", "products", "pdvEmail", "selectedMail", "sendMail", "checked", "console", "log", "selectedPaymentMethod", "code", "retailers", "type", "documentDate", "mail", "rowBody", "totTax", "name", "concat", "idSupplying", "localStorage", "idOrder", "url", "then", "res", "setItem", "setTimeout", "catch", "e", "number", "warehousesCross", "idWarehouse", "selfMail", "result", "_this$toast", "_this$toast2", "_e$response", "_e$response2", "response", "message", "cc", "idAffiliate2", "idRegistry2", "ordersMail", "users", "_this$toast3", "<PERSON><PERSON><PERSON>", "_this$toast4", "_this$toast5", "_e$response3", "_e$response4", "_this$toast6", "_e$response5", "_e$response6", "results3", "value", "value2", "globalFilter", "globalFilter2", "selected<PERSON>roductsToDiscount", "selectedProducts", "loading", "deleteResultDialog", "resultDialog", "resultDialog2", "addTribute", "discountPayment", "hideSelect", "discountTotClass", "mex", "omaggi", "conditioned_discount", "<PERSON><PERSON>", "options", "minDate", "cities", "warehouses", "actionBodyTemplate", "bind", "nameBodyTemplate", "nameBodyTemplate2", "pIvaBodyTemplate", "telBodyTemplate", "cityBodyTemplate", "capBodyTemplate", "addressBodyTemplate", "externalCodeBodyTemplate", "indirizzoConsegnaBodytemplate", "noteConsegnaBodytemplate", "ivaBodyTemplate", "quantitàBodyTemplate", "priceBodyTemplate", "totaleBodyTemplate", "pcxpkgBodyTemplate", "imageBodyTemplate", "calcTotal", "deleteResult", "hideDeleteResultDialog", "unitMisBodyTemplate", "colliBodyTemplate", "totTaxBodyTemplate", "cambioIndirizzo", "cambioNote", "onPaymentMethodChange", "discount_active", "inflation_active", "noteBodyTemplate", "paymentBodyTemplate", "onKeyUpHandler", "ScontoPercentualeBodyTemplate", "ScontoAValoreBodyTemplate", "scontoPercEditor", "scontoValEditor", "onEditDiscountTotal", "prodOmaggio", "hideProdOmaggio", "newColliBodyTemplate", "colliEditor", "onRowEditComplete", "onRowModalEditComplete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aggiungiOmaggio", "zeroIvaHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "componentDidMount", "methPag", "description", "warehouseName", "stringify", "supplyingAgreements", "_this$props$datiConse", "discount_payment", "map", "paymentMethodName", "obj", "idPaymentMethod", "_element$conditioned_", "discount", "correlati", "condizione", "date_end", "date_start", "discount_note", "idProduct", "percent", "fixed", "supplyCode", "initPrice", "ScontoPercentuale", "ScontoAValore", "prodInCart", "index", "findIndex", "_this$props$datiConse2", "_this$props$datiConse3", "_this$props$results$i", "destcap", "destragsoc", "destind", "<PERSON>t<PERSON><PERSON>", "destprov", "join", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_results2$idProduct", "Nome", "Quantità", "Prezzo", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "<PERSON><PERSON>", "Note", "TotTax", "_results2$idProduct2", "_results2$idProduct3", "_results2$idProduct4", "_results2$idProduct5", "_results2$idProduct6", "_results2$idProduct7", "_results2$idProduct8", "exCode", "externalCode", "image", "src", "onError", "target", "alt", "productId", "tel", "includes", "city", "cap", "UnitMeasure", "UnitMis", "QtaIns", "<PERSON><PERSON>", "UltScontoPercentuale", "UltScontoAValore", "_results2$discount_ac", "_results2$discount_ac2", "ScontoAttivo", "key", "_results2$conditioned", "scontiCondizionati", "a<PERSON><PERSON><PERSON><PERSON>", "unità", "item", "rice<PERSON>", "Object", "keys", "omaggio", "di", "values", "ScontoCondizionato", "label", "badge", "_results2$inflation_a", "_results2$inflation_a2", "<PERSON><PERSON><PERSON><PERSON>", "_element$idProduct", "_element$idProduct2", "splice", "i", "confirmDeleteResult", "<PERSON><PERSON><PERSON><PERSON>", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "reject", "rowData", "onClick", "_this$state$datiConse", "_this$state$datiConse2", "_this$state$datiConse3", "_this$state$datiConse4", "_this$state$datiConse5", "_this$state$datiConse6", "_this$state$datiConse7", "_this$state$datiConse8", "onRowEditComplete2", "onEditDiscount", "onValueChange", "suffix", "unitMisEditor", "formato", "object", "product", "placeholder", "optionLabel", "optionValue", "onChange", "width", "itemTemplate", "option", "toLowerCase", "split", "currentTarget", "max<PERSON><PERSON><PERSON>", "_element$conditioned_2", "idSupplier", "prodCorr", "verifyCondition", "reduce", "prev", "curr", "new<PERSON><PERSON><PERSON>", "_e$response7", "_e$response8", "newColliEditor", "filter", "quantità", "qtaTotProd", "nOmaggi", "omaggiTot", "maximal", "found", "tableProd", "pack", "default", "prod", "ivaPrev", "render", "_this$state$datiConse9", "_this$state$omaggi", "resultDialogFooter2", "<PERSON><PERSON>", "onInput", "header2", "ref", "<PERSON><PERSON><PERSON><PERSON>", "DatForn", "Tel", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "<PERSON><PERSON>tti", "dt", "dataKey", "editMode", "responsiveLayout", "autoLayout", "csvSeparator", "field", "<PERSON><PERSON><PERSON>", "sortable", "editor", "rowEditor", "headerStyle", "min<PERSON><PERSON><PERSON>", "bodyStyle", "textAlign", "htmlFor", "zeroIVACheck", "inputId", "isNaN", "DatiCons", "emptyMessage", "filterBy", "Data", "dateFormat", "readOnlyInput", "showIcon", "ModalitàPag", "Pagamento", "Sconto", "mailAffCheck", "Email", "MailConfOrd", "onKeyUp", "history", "back", "Indietro", "salvaOrd", "visible", "AggProdOmaggio", "modal", "footer", "onHide", "paginator", "rows", "rowsPerPageOptions", "selection", "onSelectionChange", "selectionMode", "<PERSON><PERSON><PERSON>", "SelProdScont", "Conferma"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/marketplace/carrello.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport Immagine from '../../../img/mktplaceholder.jpg';\n//import AggiungiDestinazioni from '../../../aggiunta_dati/aggiungiDestinazioni';\nimport { But<PERSON> } from 'primereact/button';\nimport { <PERSON><PERSON> } from '../../../components/traduttore/const';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { APIRequest, baseProxy } from '../../../components/generalizzazioni/apireq';\nimport { getNumbers } from '../../../common/PDV/carrello/actions/getAction';\nimport { connect } from 'react-redux';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport { Toast } from 'primereact/toast';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Dialog } from 'primereact/dialog';\nimport { Checkbox } from 'primereact/checkbox';\nimport { InputText } from 'primereact/inputtext';\nimport { Calendar } from 'primereact/calendar';\nimport {\n    affiliato,\n    affiliatoDocumentiOrdineAcquisto,\n    affiliatoVisualizzaOrdini,\n    agente,\n    agenteGestioneOrdiniAgente,\n    chain,\n    chainDocumentiOrdineAcquisto,\n    distributore,\n    distributoreCarrello,\n    distributoreDocumentiOrdineAcquisto,\n    pdv,\n    pdvGestioneLogisticaOrdini,\n    pdvStoricoDocumenti,\n    ring\n} from '../../route';\nimport { onlyUniqueCondizioneCorrelatiOmaggio } from '../uniqueElements/onlyUniqueNameStatus';\nimport { OverlayPanelGen } from '../overlayPanelGen';\n\nclass CarrelloGen extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: '',\n        address: '',\n        pIva: '',\n        email: '',\n        isValid: '',\n        idProduct2: [],\n        createAt: '',\n        updateAt: ''\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            result: this.emptyResult,\n            results: null,\n            results2: null,\n            results3: null,\n            value: null,\n            value2: null,\n            data: null,\n            datiConsegna: null,\n            paymentMetod: null,\n            globalFilter: null,\n            globalFilter2: null,\n            selectedPaymentMethod: null,\n            selectedProductsToDiscount: null,\n            selectedProducts: null,\n            disabled: false,\n            checked: false,\n            zeroIva: false,\n            loading: true,\n            deleteResultDialog: false,\n            resultDialog: false,\n            resultDialog2: false,\n            addTribute: true,\n            selectedAddress: [],\n            selectedMail: [],\n            ordersMail: [],\n            discountPayment: [],\n            oggetto: 'Logistica interna per punto vendita',\n            hideSelect: 'col-11 col-sm-12 col-md-10 col-lg-10 col-xl-11',\n            discountTotClass: 'd-none',\n            mex: '',\n            omaggi: '',\n            conditioned_discount: '',\n            role: localStorage.getItem('role'),\n            total: 0,\n            totTax: 0,\n            Iva: 0\n        }\n        this.options = [{ name: '% Sconto percentuale', value: 'ScontoPercentuale' }, { name: '€ Sconto a valore', value: 'ScontoAValore' }, { name: 'Nessuno sconto', value: 'NessunoSconto' }];\n        this.minDate = new Date();\n        this.cities = [];\n        this.warehouses = [];\n        this.actionBodyTemplate = this.actionBodyTemplate.bind(this);\n        this.nameBodyTemplate = this.nameBodyTemplate.bind(this);\n        this.nameBodyTemplate2 = this.nameBodyTemplate2.bind(this);\n        this.pIvaBodyTemplate = this.pIvaBodyTemplate.bind(this);\n        this.telBodyTemplate = this.telBodyTemplate.bind(this);\n        this.cityBodyTemplate = this.cityBodyTemplate.bind(this);\n        this.capBodyTemplate = this.capBodyTemplate.bind(this);\n        this.addressBodyTemplate = this.addressBodyTemplate.bind(this);\n        this.externalCodeBodyTemplate = this.externalCodeBodyTemplate.bind(this);\n        this.indirizzoConsegnaBodytemplate = this.indirizzoConsegnaBodytemplate.bind(this);\n        this.noteConsegnaBodytemplate = this.noteConsegnaBodytemplate.bind(this);\n        this.ivaBodyTemplate = this.ivaBodyTemplate.bind(this);\n        this.quantitàBodyTemplate = this.quantitàBodyTemplate.bind(this);\n        this.priceBodyTemplate = this.priceBodyTemplate.bind(this);\n        this.totaleBodyTemplate = this.totaleBodyTemplate.bind(this);\n        this.pcxpkgBodyTemplate = this.pcxpkgBodyTemplate.bind(this);\n        this.imageBodyTemplate = this.imageBodyTemplate.bind(this);\n        this.calcTotal = this.calcTotal.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog(this);\n        this.unitMisBodyTemplate = this.unitMisBodyTemplate.bind(this);\n        this.colliBodyTemplate = this.colliBodyTemplate.bind(this);\n        this.totTaxBodyTemplate = this.totTaxBodyTemplate.bind(this);\n        this.cambioIndirizzo = this.cambioIndirizzo.bind(this);\n        this.cambioNote = this.cambioNote.bind(this);\n        //this.addNewAddress = this.addNewAddress.bind(this);\n        //this.hideAggiungiDest = this.hideAggiungiDest.bind(this);\n        this.onPaymentMethodChange = this.onPaymentMethodChange.bind(this);\n        this.discount_active = this.discount_active.bind(this);\n        this.inflation_active = this.inflation_active.bind(this);\n        this.noteBodyTemplate = this.noteBodyTemplate.bind(this);\n        this.paymentBodyTemplate = this.paymentBodyTemplate.bind(this);\n        this.onKeyUpHandler = this.onKeyUpHandler.bind(this);\n        this.ScontoPercentualeBodyTemplate = this.ScontoPercentualeBodyTemplate.bind(this);\n        this.ScontoAValoreBodyTemplate = this.ScontoAValoreBodyTemplate.bind(this);\n        this.scontoPercEditor = this.scontoPercEditor.bind(this);\n        this.scontoValEditor = this.scontoValEditor.bind(this);\n        this.discountTotClass = this.discountTotClass.bind(this);\n        this.onEditDiscountTotal = this.onEditDiscountTotal.bind(this);\n        this.prodOmaggio = this.prodOmaggio.bind(this);\n        this.hideProdOmaggio = this.hideProdOmaggio.bind(this);\n        this.newColliBodyTemplate = this.newColliBodyTemplate.bind(this);\n        this.colliEditor = this.colliEditor.bind(this);\n        this.onRowEditComplete = this.onRowEditComplete.bind(this);\n        this.onRowModalEditComplete = this.onRowModalEditComplete.bind(this);\n        this.selectionChangeHandler = this.selectionChangeHandler.bind(this);\n        this.aggiungiOmaggio = this.aggiungiOmaggio.bind(this);\n        this.zeroIvaHandler = this.zeroIvaHandler.bind(this);\n        this.selectionDiscountHandler = this.selectionDiscountHandler.bind(this);\n    }\n    //Chiamata axios effettuata una sola volta grazie a componentDidMount\n    async componentDidMount() {\n        var indirizzo = ''\n        var mail = ''\n        var methPag = []\n        //var methPagId = []\n        /* Controllo i dati passati in props per definire quale utente utente sta acquistando */\n        if (this.props.datiConsegna !== null && this.props.datiConsegna !== undefined && window.location.pathname !== '/distributore/carrello' && window.location.pathname !== '/chain/carrello' && !JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n            /* Agente */\n            if (this.props.datiConsegna.data !== undefined) {\n                await APIRequest('GET', 'paymentmethods')\n                    .then(res => {\n                        console.log(res.data);\n                        res.data.forEach(element => {\n                            var x = { name: element.description, code: element.description }\n                            methPag.push(x)\n                        })\n                        this.setState({\n                            paymentMetod: methPag\n                        })\n                    }).catch((e) => {\n                        console.log(e)\n                    })\n                this.props.datiConsegna.data = new Date(this.props.datiConsegna.data)\n                var datiConsegna = {\n                    data: this.props.datiConsegna.data,\n                    firstName: this.props.datiConsegna.firstName,\n                    idOrder: this.props.datiConsegna.idOrder,\n                    note: this.props.datiConsegna.note,\n                    stato: this.props.datiConsegna.stato,\n                    indirizzo: this.props.results.idRegistry.address\n                }\n                mail = this.props.results.idRegistry.email\n                indirizzo = this.props.results.idRegistry.address\n                this.setState({\n                    results: this.props.results,\n                    results2: this.props.results2,\n                    datiConsegna: datiConsegna\n                })\n            } else {\n                /* Affiliato */\n                datiConsegna = {\n                    indirizzo: this.props.datiConsegna.address,\n                    note: '',\n                    stato: 'Registrato'\n                }\n                mail = this.props.datiConsegna.email\n                indirizzo = this.props.datiConsegna.address\n                this.setState({\n                    results: this.props.datiConsegna,\n                    results2: this.props.results2,\n                    datiConsegna: datiConsegna\n                })\n            }\n        } else if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n            if ((this.state.role === distributore || this.state.role === chain) && typeof (JSON.parse(sessionStorage.getItem(\"idWarehouse\"))) === 'number') {\n                await APIRequest(\"GET\", \"warehouses/\")\n                    .then((res) => {\n                        var idWarehouse = null\n                        var find = res.data.find(el => el.id === JSON.parse(sessionStorage.getItem(\"idWarehouse\")))\n                        idWarehouse = find !== undefined ? { name: find.warehouseName, code: find.id } : null\n                        if (idWarehouse !== null) {\n                            JSON.parse(sessionStorage.setItem(\"idWarehouse\", JSON.stringify(idWarehouse)))\n                        }\n                    })\n                    .catch((e) => {\n                        console.log(e);\n                    });\n            }\n            await APIRequest('GET', 'paymentmethods')\n                .then(res => {\n                    res.data.forEach(element => {\n                        var x = { name: element.description, code: element.description }\n                        methPag.push(x)\n                    })\n                    this.setState({\n                        paymentMetod: methPag\n                    })\n                    if (this.props.datiConsegna.supplyingAgreements.length > 0) {\n                        this.props.datiConsegna.supplyingAgreements[0].discount_payment?.map(el => el.paymentMethodName = res.data.find(obj => obj.id === parseInt(el.idPaymentMethod)).description)\n                        this.setState({\n                            discountPayment: this.props.datiConsegna.supplyingAgreements[0].discount_payment //this.props.datiConsegna.supplyingAgreements[0].discount_payment.map(el => el.fixed ? `Pagamento: ${el.paymentMethodName} sconto ${el.fixed}€` : `Pagamento: ${el.idPaymentMethod} sconto ${el.percent}%`)\n                        })\n                    }\n                }).catch((e) => {\n                    console.log(e)\n                })\n            /* Distributore */\n            datiConsegna = {\n                data: '',\n                firstName: this.props.datiConsegna.idRegistry.firstName,\n                idOrder: '',\n                indirizzo: this.props.datiConsegna.idRegistry.address,\n                note: this.props.datiConsegna.note ? this.props.datiConsegna.note : '',\n                stato: 'Registrato'\n            }\n            mail = this.props.datiConsegna.idRegistry.email\n            indirizzo = this.props.datiConsegna.idRegistry.address\n            var prodotti = []\n            this.props.results2.forEach(element => {\n                var discount = element.conditioned_discount?.find(el => !el.correlati ? parseInt(el.condizione) === element.quantity : undefined)\n                var x = {}\n                if (discount) {\n                    x = {\n                        date_end: element.date_end,\n                        date_start: element.date_start,\n                        discount_active: element.discount_active,\n                        conditioned_discount: element.conditioned_discount,\n                        discount_note: element.discount_note,\n                        id: element.id,\n                        idProduct2: element.idProduct !== undefined ? element.idProduct : element.idProduct2,\n                        idSupplying: element.idSupplying,\n                        moltiplicatore: element.moltiplicatore,\n                        qtaIns: element.qtaIns,\n                        quantity: element.quantity,\n                        sell_in: discount.percent ? element.sell_in - element.sell_in * parseFloat(discount.percent) / 100 : element.sell_in - discount.fixed,\n                        supplyCode: element.supplyCode,\n                        totale: element.totale,\n                        initPrice: element.initPrice,\n                        ScontoPercentuale: discount.percent ? discount.percent : 0,\n                        ScontoAValore: discount.fixed ? discount.fixed : 0\n                    }\n                    var prodInCart = [];\n                    if (localStorage.getItem(\"Cart\") !== '') {\n                        prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n                        let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === x.idProduct2.id);\n                        if (index !== -1) {\n                            prodInCart[index] = x;\n                            localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n                        }\n                    }\n                } else {\n                    x = {\n                        date_end: element.date_end,\n                        date_start: element.date_start,\n                        discount_active: element.discount_active,\n                        conditioned_discount: element.conditioned_discount,\n                        discount_note: element.discount_note,\n                        id: element.id,\n                        idProduct2: element.idProduct !== undefined ? element.idProduct : element.idProduct2,\n                        idSupplying: element.idSupplying,\n                        moltiplicatore: element.moltiplicatore,\n                        qtaIns: element.qtaIns,\n                        quantity: element.quantity,\n                        sell_in: element.sell_in,\n                        supplyCode: element.supplyCode,\n                        totale: element.totale,\n                        initPrice: element.initPrice,\n                        ScontoPercentuale: 0,\n                        ScontoAValore: 0\n                    }\n                }\n\n                prodotti.push(x)\n            })\n            this.setState({\n                results: this.props.results,\n                results2: prodotti,\n                datiConsegna: datiConsegna,\n                selectedPaymentMethod: this.props.datiConsegna.termsPayment ? methPag.find(el => el.name === this.props.datiConsegna.termsPayment) : null,\n                data: this.props.datiConsegna.deliveryDate ? new Date(this.props.datiConsegna.deliveryDate) : null,\n            })\n            if (this.props.datiConsegna.iva) {\n                this.zeroIvaHandler({ checked: this.props.datiConsegna.iva })\n            }\n        } else {\n            /* PDV */\n            datiConsegna = {\n                data: '',\n                idOrder: '',\n                indirizzo: this.props.results.address !== undefined ? this.props.results.address : this.props.results.idRegistry.address,\n                note: '',\n                stato: 'Registrato'\n            }\n            mail = this.props.results.email\n            indirizzo = this.props.results.address !== undefined ? this.props.results.address : this.props.results.idRegistry.address\n            this.setState({\n                results: this.props.results,\n                results2: this.props.results2,\n                datiConsegna: datiConsegna\n            })\n        }\n        if (window.location.pathname !== distributoreCarrello && !JSON.parse(window.sessionStorage.getItem(\"affForOrd\"))) {\n            var url = ''\n            if (this.props.datiConsegna?.idRegistry !== undefined && typeof (this.props.datiConsegna?.idRegistry) !== 'object') {\n                url = 'destination/?idRegistry=' + this.props.datiConsegna.idRegistry\n            } else if (this.props.results.idRegistry?.id !== undefined) {\n                url = 'destination/?idRegistry=' + this.props.results.idRegistry.id\n            } else if (this.props.results.id !== undefined) {\n                url = 'destination/?idRegistry=' + this.props.results.id\n            }\n            /* Chiamata per le destinazioni */\n            await APIRequest('GET', url)\n                .then(res => {\n                    console.log(res.data);\n                    if (indirizzo !== '') {\n                        this.cities.push({ name: indirizzo, value: indirizzo })\n                    }\n                    res.data.forEach(element => {\n                        var x = []\n                        if (element.destcap !== null) {\n                            x = {\n                                name: element.destragsoc + ', ' + element.destind + ' ' + element.destcitta + ' (' + element.destprov + '), ' + element.destcap, code: element.id\n                            }\n                        } else {\n                            x = {\n                                name: element.destragsoc + ', ' + element.destind + ' ' + element.destcitta + ' (' + element.destprov + '), ', code: element.id\n                            }\n                        }\n                        this.cities.push(x)\n                    })\n                    this.setState({\n                        selectedMail: mail\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n            await APIRequest('GET', 'notify?ufficio=VENDITE')\n                .then(res => {\n                    console.log(res.data);\n                    var email = res.data.map(el => el.email).join(',')\n                    this.setState({ ordersMail: email })\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n\n        this.calcTotal()\n    }\n    /* Reperiamo il nome del cliente */\n    nameBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.firstName !== undefined) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.firstName}</span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.idRegistry.firstName}</span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo il nome del prodotto */\n    nameBodyTemplate2(results2) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results2.idProduct2?.description}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo la quantità dei prodotti */\n    quantitàBodyTemplate(results2) {\n        if (results2.qtaIns !== undefined) {\n            results2.quantity = results2.qtaIns * parseInt(results2.moltiplicatore)\n        } else {\n            results2.quantity = results2.colli * parseInt(results2.pcsXpackage)\n        }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Quantità}</span>\n                {results2.quantity}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo il prezzo dei prodotti */\n    priceBodyTemplate(results2) {\n        if (results2.price !== undefined) {\n            if (this.state.role === distributore || this.state.role === chain) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.price)}\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(typeof (results2.price) !== 'string' ? results2.price : parseFloat(results2.price))}\n                    </React.Fragment>\n                );\n            }\n\n        } else if (results2.initPrice !== undefined) {\n            if (this.state.role === distributore || this.state.role === chain) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.sell_in)}\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results2.initPrice)}\n                    </React.Fragment>\n                );\n            }\n        }\n        else {\n            if (this.state.role === distributore || this.state.role === chain) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)}\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                        {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)}\n                    </React.Fragment>\n                );\n            }\n        }\n    }\n    /* Reperiamo il totale del prodotto */\n    totaleBodyTemplate(results2) {\n        if (results2.qtaIns !== undefined) {\n            if (results2.price !== undefined) {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price))}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price))}\n                        </React.Fragment>\n                    );\n                }\n            } else {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format((results2.colli !== undefined ? results2.colli : results2.qtaIns) * (results2.pcsXpackage !== undefined ? results2.pcsXpackage : results2.moltiplicatore) * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in))}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format((results2.colli !== undefined ? results2.colli : results2.qtaIns) * (results2.pcsXpackage !== undefined ? results2.pcsXpackage : results2.moltiplicatore) * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in))}\n                        </React.Fragment>\n                    );\n                }\n            }\n        } else {\n            if (results2.price !== undefined) {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.price))}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.price))}\n                        </React.Fragment>\n                    );\n                }\n            } else {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice))}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.Tot}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice))}\n                        </React.Fragment>\n                    );\n                }\n            }\n        }\n\n    }\n    /* Reperiamo il formato richiesto per il prodotto */\n    pcxpkgBodyTemplate(results2) {\n        if (results2.moltiplicatore !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Pezzi per package</span>\n                    {results2.moltiplicatore}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Pezzi per package</span>\n                    {results2.pcsXpackage}\n                </React.Fragment>\n            );\n        }\n    }\n    noteBodyTemplate(results2) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Note}</span>\n                {results2.discount_note}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo il totale con calcolo dell'iva */\n    totTaxBodyTemplate(results2) {\n        if (results2.qtaIns !== undefined) {\n            if (results2.price !== undefined) {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price) * parseInt(results2.idProduct2.iva) / 100)}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.price) * parseInt(results2.idProduct2.iva) / 100)}\n                        </React.Fragment>\n                    );\n                }\n\n            } else {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in) * parseInt(results2.idProduct2.iva) / 100)}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(parseFloat(results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in)) + results2.qtaIns * results2.moltiplicatore * parseFloat(results2.unitPrice !== undefined ? results2.unitPrice : results2.sell_in) * parseInt(results2.idProduct2.iva) / 100)}\n                        </React.Fragment>\n                    );\n                }\n            }\n        } else {\n            if (results2.price !== undefined) {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.price)) + results2.colli * results2.pcsXpackage * parseFloat(results2.price) * parseInt(results2.idProduct2?.iva) / 100)}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.price)) + results2.colli * results2.pcsXpackage * parseFloat(results2.price) * parseInt(results2.idProduct2?.iva) / 100)}\n                        </React.Fragment>\n                    );\n                }\n            } else {\n                if (this.state.role === distributore || this.state.role === chain) {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice)) + results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice) * parseInt(results2.idProduct2?.iva) / 100)}\n                        </React.Fragment>\n                    );\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                            {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(parseFloat(results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice)) + results2.colli * results2.pcsXpackage * parseFloat(results2.unitPrice) * parseInt(results2.idProduct2?.iva) / 100)}\n                        </React.Fragment>\n                    );\n                }\n            }\n        }\n    }\n    /* Reperiamo l'iva del prodotto */\n    ivaBodyTemplate(results2) {\n\n        if (results2.idProduct2?.iva !== null) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Iva}</span>\n                    {results2.idProduct2?.iva} %\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Iva}</span>\n                    {results2?.tax}\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperiamo il codice esterno del prodotto */\n    externalCodeBodyTemplate(results2) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results2.idProduct2?.externalCode}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo l'immagine del prodotto con una chiamata in base all'id del prodotto */\n    imageBodyTemplate(results2) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.image}</span>\n                {results2.idProduct !== undefined ? (\n                    < div className='immaginiProdTab'>\n                        <img src={baseProxy + 'asset/prodotti/' + results2.idProduct + '.jpg'} onError={(e) => e.target.src = Immagine} alt={results2.name} />\n                    </div>\n                ) : (\n                    < div className='immaginiProdTab'>\n                        <img src={baseProxy + 'asset/prodotti/' + (results2.productId !== undefined ? results2.productId : results2.idProduct2.id) + '.jpg'} onError={(e) => e.target.src = Immagine} alt={results2.name !== undefined ? results2.name : results2.idProduct2.description} />\n                    </div>\n                )\n                }\n\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo la partita iva del cliente */\n    pIvaBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.pIva !== undefined) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.pIva}</span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.idRegistry.pIva}</span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo il numero di telefono del cliente */\n    telBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.tel !== undefined) {\n                if (this.state.results.tel.includes('null')) {\n                    return \"Nessun numero di telefono disponibile\";\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{this.state.results.tel}</span>\n                        </React.Fragment>\n                    );\n                }\n            } else {\n                if (this.state.results.idRegistry.tel.includes('null')) {\n                    return \"Nessun numero di telefono disponibile\";\n                } else {\n                    return (\n                        <React.Fragment>\n                            <span className=\"p-column-title\">{this.state.results.idRegistry.tel}</span>\n                        </React.Fragment>\n                    );\n                }\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo la città del cliente */\n    cityBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.city !== undefined) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.city}</span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.idRegistry.city}</span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo il codice postale del cliente */\n    capBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.cap !== undefined) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.cap}</span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.idRegistry.cap}</span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo l'indirizzo del cliente */\n    addressBodyTemplate() {\n        if (this.state.results !== null) {\n            if (this.state.results.address !== undefined) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.address}</span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{this.state.results.idRegistry.address}</span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo l'indirizzo del cliente */\n    paymentBodyTemplate() {\n        if (this.state.results !== null) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{this.state.results.paymentMetod}</span>\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperiamo il codice formato del prodotto */\n    unitMisBodyTemplate(results) {\n        var UnitMeasure = ''\n        if (results.pcsXpackage !== undefined) {\n            results.idProduct2.productsPackagings.forEach(element => {\n                if (results.moltiplicatore !== undefined) {\n                    if (parseInt(results.moltiplicatore) === element.pcsXPackage) {\n                        UnitMeasure = element.unitMeasure\n                    }\n                } else {\n                    if (results.pcsXpackage === element.pcsXPackage) {\n                        UnitMeasure = element.unitMeasure\n                    }\n                }\n            })\n        } else {\n            if (results.moltiplicatore !== undefined) {\n                results.idProduct2.productsPackagings.forEach(element => {\n                    if (parseInt(results.moltiplicatore) === element.pcsXPackage) {\n                        UnitMeasure = element.unitMeasure\n                    }\n                })\n            } else {\n                UnitMeasure = results.unitMeasure\n            }\n        }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UnitMis}</span>\n                {UnitMeasure}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    colliBodyTemplate(results) {\n        if (results.qtaIns !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.QtaIns}</span>\n                    {results.qtaIns}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Colli}</span>\n                    {results.colli}\n                </React.Fragment>\n            );\n        }\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    ScontoPercentualeBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UltScontoPercentuale}</span>\n                {results.ScontoPercentuale}%\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    ScontoAValoreBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UltScontoAValore}</span>\n                {results.ScontoAValore}\n            </React.Fragment>\n        );\n    }\n    /* Reperiamo l'indirizzo di consegna di default */\n    indirizzoConsegnaBodytemplate() {\n        if (this.state.datiConsegna !== null) {\n\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{this.state.datiConsegna.indirizzo}</span>\n                </React.Fragment>\n            );\n\n        } else {\n            return null;\n        }\n    }\n    /* Reperiamo le note per l'ordine */\n    noteConsegnaBodytemplate() {\n        if (this.state.datiConsegna !== null) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{this.state.datiConsegna.note}</span>\n                </React.Fragment>\n            );\n        } else {\n            return null;\n        }\n    }\n    discount_active(results2) {\n        var percent = results2.discount_active?.percent\n        var fixed = results2.discount_active?.fixed\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ScontoAttivo}</span>\n                {percent?.length > 0 &&\n                    <>\n                        {percent.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + \"% \"}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n                {fixed?.length > 0 &&\n                    <>\n                        {fixed.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + '€ '}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n            </React.Fragment>\n        );\n    }\n    conditioned_discount(results2) {\n        var scontiCondizionati = []\n        results2.conditioned_discount?.map(el => scontiCondizionati.push(el.correlati ? { acquistando: el.condizione, unità: el.correlati.map(item => item).join(', '), ricevi: Object.keys(el.omaggio)[0], di: Object.values(el.omaggio).map(obj => obj.map(item => `${Object.keys(item)}: ${Object.values(item)}`).join(', ')) } : (el.condizione ? `Acquistando ${el.condizione} unità ${el.percent ? `ricevi il ${el.percent}% di sconto` : `ricevi ${el.fixed} di sconto`}` : '')))\n        return (\n            <React.Fragment key={results2.id}>\n                <span className=\"p-column-title\">{Costanti.ScontoCondizionato}</span>\n                {scontiCondizionati.length > 0 &&\n                    <OverlayPanelGen values={scontiCondizionati} label='Sconto' badge={true} />\n                }\n            </React.Fragment>\n        );\n    }\n    inflation_active(results2) {\n        var percent = results2.inflation_active?.percent\n        var fixed = results2.inflation_active?.fixed\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Rincaro}</span>\n                {percent?.length > 0 &&\n                    <>\n                        {percent.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + \"% \"}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n                {fixed?.length > 0 &&\n                    <>\n                        {fixed.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + '€ '}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n            </React.Fragment>\n        );\n    }\n    /* Calcolo il totale dei prodotti */\n    calcTotal(discount) {\n        let prodInCart = [];\n        if (localStorage.getItem(\"Cart\") === \"\" || localStorage.getItem(\"Cart\") === '[]') {\n            this.setState({\n                total: '0,00 €',\n                totTax: '0,00 €',\n            })\n        } else {\n            var tot = 0;\n            var totTax = 0;\n            prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n            prodInCart.forEach(element => {\n                if (element.unitPrice !== undefined) {\n                    var total = parseFloat(element.unitPrice) * element.colli * element.pcsXpackage + parseFloat(tot);\n                    tot = total;\n                    totTax = tot + tot * (element.idProduct?.iva !== undefined ? parseInt(element.idProduct.iva) : (element.idProduct2 !== undefined ? parseInt(element.idProduct2.iva) : 22)) / 100\n                    if (this.state.role === distributore || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                        this.setState({\n                            total: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(discount ? (discount === 'ScontoPercentuale' ? tot - tot * this.state.value2 / 100 : tot - this.state.value2) : tot),\n                            totTax: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(discount ? (discount === 'ScontoPercentuale' ? totTax - totTax * this.state.value2 / 100 : totTax - this.state.value2) : totTax),\n                            Iva: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(discount ? (discount === 'ScontoPercentuale' ? (totTax - tot) - (totTax - tot) * this.state.value2 / 100 : (totTax - tot) - this.state.value2) : totTax - tot)\n                        })\n                    } else {\n                        this.setState({\n                            total: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(tot),\n                            totTax: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(totTax),\n                            Iva: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(totTax - tot)\n                        })\n                    }\n                } else {\n                    total = parseFloat(element.price !== undefined ? element.price : element.sell_in) * element.qtaIns * element.moltiplicatore + parseFloat(tot);\n                    tot = total;\n                    totTax = tot + tot * (element.idProduct?.iva !== undefined ? parseInt(element.idProduct.iva) : (element.idProduct2 !== undefined ? parseInt(element.idProduct2.iva) : 22)) / 100\n                    if (this.state.role === distributore || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                        this.setState({\n                            total: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(discount ? (discount === 'ScontoPercentuale' ? tot - tot * this.state.value2 / 100 : tot - this.state.value2) : tot),\n                            totTax: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(discount ? (discount === 'ScontoPercentuale' ? totTax - totTax * this.state.value2 / 100 : totTax - this.state.value2) : totTax),\n                            Iva: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(discount ? (discount === 'ScontoPercentuale' ? (totTax - tot) - (totTax - tot) * this.state.value2 / 100 : (totTax - tot) - this.state.value2) : totTax - tot)\n                        })\n                    } else {\n                        this.setState({\n                            total: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(tot),\n                            totTax: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totTax),\n                            Iva: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totTax - tot)\n                        })\n                    }\n                }\n            })\n        }\n    }\n    /* Funzione di invio ordine effettuata in maniera asincrona con axios */\n    Invia = async () => {\n        this.setState({ disabled: true })\n        var prodotti = [];\n        var unitMeasure = '';\n        var pcsXPackage = '';\n        var colli = '';\n        if (this.state.selectedAddress.length === 0 && this.state.role !== distributore && this.state.role !== chain && !JSON.parse(sessionStorage.getItem('affForOrd'))) {\n            this.setState({ disabled: false })\n            this.toast.show({ severity: 'error', summary: 'Attenzione !', detail: \"Selezionare indirizzo di destinazione prima di procedere con l'ordine\", life: 3000 });\n        } else {\n            /* Per ogni elemento definiamo il moltiplicatore delle quantità il formato ed i colli */\n            this.state.results2.forEach(element => {\n                if (element.idProduct2.productsPackagings !== undefined) {\n                    element.idProduct2.productsPackagings.forEach(el => {\n                        if (element.moltiplicatore !== undefined) {\n                            if (parseFloat(element.moltiplicatore) === el.pcsXPackage) {\n                                unitMeasure = el.unitMeasure;\n                                pcsXPackage = el.pcsXPackage;\n                                colli = element.quantity / el.pcsXPackage;\n                            }\n                        } else {\n                            if (element.pcsXpackage === el.pcsXPackage) {\n                                unitMeasure = el.unitMeasure;\n                                pcsXPackage = el.pcsXPackage;\n                                colli = element.quantity / el.pcsXPackage;\n                            }\n                        }\n                    })\n                } else {\n                    unitMeasure = element.unitMeasure;\n                    pcsXPackage = element.pcsXpackage;\n                    colli = element.quantity / element.pcsXpackage;\n                }\n                /* Per ogni elemento definiamo il totale */\n                if (element.price !== undefined) {\n                    var tot = element.quantity * parseFloat(element.price) + ' €'\n                } else {\n                    tot = element.quantity * parseFloat(element.unitPrice !== undefined ? element.unitPrice : element.sell_in) + ' €'\n                }\n                /* Per ogni elemento definiamo il prezzo unitario */\n                var unitPrice = 0\n                if (element.price !== undefined) {\n                    unitPrice = parseFloat(element.price)\n                } else {\n                    unitPrice = parseFloat(element.unitPrice !== undefined ? element.unitPrice : element.sell_in)\n                }\n                var FEE = ''\n                if (element.priceDad !== undefined) {\n                    var feature = parseFloat(element.price) - parseFloat(element.priceDad)\n                    FEE = feature * element.quantity + ' €'\n                }\n                /* Definisco gli elementi per singolo prodotto */\n                if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                    var find = element.idProduct2.productsPackagings.find(el => el.pcsXPackage === pcsXPackage)\n                    prodotti.push(\n                        {\n                            id: element.idProduct2.id,\n                            quantity: element.quantity,\n                            total: parseFloat(tot),\n                            totalTaxed: this.state.zeroIva ? parseFloat(tot) : parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100,\n                            idProductsPackaging: find.id,\n                            unitPrice: unitPrice,\n                            colliPreventivo: colli,\n                            tax: element.idProduct2.iva + '%',\n                        }\n                    )\n                } else {\n                    if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n                        var x = { ...element, colliPreventivo: element.qtaIns, colliConsuntivo: 0, total: parseFloat(tot), totalTaxed: parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100, idProductsPackaging: element.idProductsPackaging !== undefined ? element.idProductsPackaging : element.idProduct2.productsPackagings.find(el => el.pcsXPackage === parseInt(element.moltiplicatore)).id }\n                        delete x.totale\n                        prodotti.push(x)\n                    } else {\n                        prodotti.push(\n                            {\n                                id: element.idProduct2.id,\n                                quantity: element.quantity,\n                                total: parseFloat(tot),\n                                totalTaxed: parseFloat(tot) + parseFloat(tot) * element.idProduct2.iva / 100,\n                                unitMeasure: unitMeasure,\n                                pcsXpackage: pcsXPackage,\n                                unitPrice: unitPrice,\n                                colli: colli,\n                                tax: element.idProduct2.iva + '%',\n                                fee: parseFloat(FEE)\n                            }\n                        )\n                    }\n                }\n            })\n            if (prodotti.length > 0) {\n                var fee = 0\n                prodotti.forEach(element => {\n                    if (element.fee !== undefined) {\n                        fee += element.fee\n                    }\n                })\n                if (fee !== 0) {\n                    fee = fee + ' €'\n                }\n                let body = []\n                var numberOfCustomers = 1\n                /* Definisco il body per la chiamata */\n                /* Affiliato */\n                if (this.state.datiConsegna.stato === undefined) {\n                    body = {\n                        deliveryDestination: this.state.datiConsegna.indirizzo,\n                        status: this.state.datiConsegna.stato,\n                        note: this.state.datiConsegna.note,\n                        deliveryDate: this.state.datiConsegna.data,\n                        paymentStatus: '',\n                        total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                        orderDate: new Date(),\n                        idRetailer: this.state.results2,\n                        termsPayment: this.state.results.idRegistry.paymentMetod,\n                        products: prodotti,\n                        pdvEmail: this.state.selectedMail,\n                        sendMail: this.state.checked\n                    }\n                } else {\n                    /* Agente */\n                    if (this.state.results.paymentMetod === undefined) {\n                        if (fee !== 0) {\n                            fee = parseFloat(fee.replace('€', '').replace(',', '.'))\n                        }\n                        console.log(this.state.datiConsegna)\n                        numberOfCustomers = parseInt(sessionStorage.getItem('numberOfCustomer')) !== 0 ? parseInt(sessionStorage.getItem('numberOfCustomer')) : 1\n                        body = {\n                            deliveryDestination: this.state.datiConsegna.indirizzo,\n                            status: this.state.datiConsegna.stato,\n                            note: this.state.datiConsegna.note,\n                            deliveryDate: this.state.datiConsegna.data !== undefined ? this.state.datiConsegna.data : (this.state.data !== null ? new Date(this.state.data) : null),\n                            paymentStatus: '',\n                            total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                            orderDate: new Date(),\n                            termsPayment: this.state.selectedPaymentMethod !== null ? this.state.selectedPaymentMethod.code : this.state.results.idRegistry.paymentMetod,\n                            idRetailer: this.state.results,\n                            products: prodotti,\n                            fee: fee,\n                            pdvEmail: this.state.selectedMail,\n                            sendMail: this.state.checked,\n                            numberOfCustomers: numberOfCustomers ? numberOfCustomers : 1\n                        }\n                    } else {\n                        /* PDV */\n                        if (fee !== 0) {\n                            fee = parseFloat(fee.replace('€', '').replace(',', '.'))\n                        }\n                        if (window.location.pathname !== distributoreCarrello && this.state.role !== chain && !JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                            if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n                                body = {\n                                    idRetailer: this.state.results.retailers,\n                                    type: 'CLI-ORDINE',\n                                    documentDate: new Date(),\n                                    deliveryDestination: this.state.datiConsegna.indirizzo,\n                                    note: this.state.datiConsegna.note,\n                                    mail: this.state.checked ? this.state.selectedMail : '',\n                                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                                    rowBody: prodotti,\n                                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                                    totalTaxed: parseFloat(this.state.totTax.replace('€', '').replace('.', '').replace(',', '.')),\n                                }\n                            } else {\n                                numberOfCustomers = parseInt(sessionStorage.getItem('numberOfCustomer')) !== 0 ? parseInt(sessionStorage.getItem('numberOfCustomer')) : 1\n                                body = {\n                                    deliveryDestination: this.state.datiConsegna.indirizzo,\n                                    status: this.state.datiConsegna.stato,\n                                    note: this.state.datiConsegna.note,\n                                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                                    paymentStatus: '',\n                                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                                    orderDate: new Date(),\n                                    termsPayment: this.state.results.paymentMetod,\n                                    idRetailer: this.state.results.retailers,\n                                    products: prodotti,\n                                    fee: fee,\n                                    pdvEmail: this.state.selectedMail,\n                                    sendMail: this.state.checked,\n                                    numberOfCustomers: numberOfCustomers ? numberOfCustomers : 1\n                                }\n                            }\n                        } else {\n                            if (this.state.selectedPaymentMethod) {\n                                body = {\n                                    type: 'FOR-ORDINE',\n                                    deliveryDestination: JSON.parse(sessionStorage.getItem(\"idWarehouse\")).name,\n                                    note: this.state.zeroIva ? `Iva azzerata, ${this.state.datiConsegna.note}` : this.state.datiConsegna.note,\n                                    deliveryDate: this.state.data !== null ? new Date(this.state.data) : null,\n                                    paymentStatus: '',\n                                    total: parseFloat(this.state.total.replace('€', '').replace('.', '').replace(',', '.')),\n                                    totalTaxed: parseFloat(this.state.totTax.replace('€', '').replace('.', '').replace(',', '.')),\n                                    orderDate: new Date(),\n                                    idSupplying: JSON.parse(window.sessionStorage.getItem(\"idSupplier\")),\n                                    rowBody: prodotti,\n                                    pdvEmail: this.state.selectedMail,\n                                    sendMail: this.state.checked,\n                                    termsPayment: this.state.selectedPaymentMethod.code\n                                }\n                            } else {\n                                this.setState({ disabled: false })\n                                this.toast.show({ severity: 'error', summary: 'Attenzione !', detail: \"Inserire termini di pagamento prima di proseguire con l'ordine\", life: 3000 });\n                            }\n                        }\n\n                    }\n                }\n                if (window.location.pathname !== distributoreCarrello && this.state.role !== chain && (body.termsPayment === null || body.termsPayment === undefined)) {\n                    body.termsPayment = ''\n                }\n                if (body.deliveryDate !== undefined && body.deliveryDate !== null) {\n                    if (localStorage.getItem(\"role\") === agente && this.state.datiConsegna.idOrder !== '') {\n                        var url = 'orders/?id=' + this.state.datiConsegna.idOrder\n                        await APIRequest('PUT', url, body)\n                            .then(res => {\n                                console.log(res.data);\n                                this.toast.show({ severity: 'success', summary: 'Ottimo', detail: 'Il suo ordine è stato inserito con successo', life: 3000 });\n                                localStorage.setItem(\"Prodotti\", []);\n                                localStorage.setItem(\"Cart\", []);\n                                localStorage.setItem(\"OrdineRecuperato\", []);\n                                localStorage.setItem(\"DatiConsegna\", []);\n                                window.sessionStorage.setItem(\"Carrello\", 0);\n                                window.sessionStorage.setItem(\"totCart\", 0);\n                                localStorage.setItem(\"datiComodo\", 0);\n                                setTimeout(() => {\n                                    window.location.pathname = agenteGestioneOrdiniAgente;\n                                }, 3000)\n                            }).catch((e) => {\n                                console.log(e)\n                                this.setState({\n                                    disabled: false\n                                })\n                            })\n                    } else if (window.location.pathname === distributoreCarrello || this.state.role === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) {\n                        if (JSON.parse(sessionStorage.getItem(\"idDocument\")) !== 0 && JSON.parse(sessionStorage.getItem(\"idDocument\")) !== null) {\n                            body = { ...body, documentDate: JSON.parse(sessionStorage.getItem(\"idDocument\")).documentDate, number: JSON.parse(sessionStorage.getItem(\"idDocument\")).number }\n                            await APIRequest('PUT', `documents/?idDocumentHead=${JSON.parse(sessionStorage.getItem(\"idDocument\")).id}`, body)\n                                .then(async res => {\n                                    console.log(res.data)\n                                    this.toast.show({ severity: 'success', summary: 'Ottimo', detail: 'Il suo ordine è stato modificato con successo', life: 3000 });\n                                    localStorage.setItem(\"Prodotti\", []);\n                                    localStorage.setItem(\"Cart\", []);\n                                    localStorage.setItem(\"OrdineRecuperato\", []);\n                                    localStorage.setItem(\"DatiConsegna\", []);\n                                    localStorage.setItem(\"datiComodo\", 0);\n                                    window.sessionStorage.setItem(\"Carrello\", 0);\n                                    window.sessionStorage.setItem(\"totCart\", 0);\n                                    window.sessionStorage.setItem(\"idWarehouse\", 0);\n                                    window.sessionStorage.setItem(\"idDocument\", 0);\n                                    window.sessionStorage.setItem(\"idSupplier\", 0);\n                                    window.sessionStorage.setItem(\"affForOrd\", false)\n                                    setTimeout(() => {\n                                        window.location.pathname = this.state.role === distributore ? distributoreDocumentiOrdineAcquisto : (this.state.role === chain ? chainDocumentiOrdineAcquisto : affiliatoDocumentiOrdineAcquisto);\n                                    }, 3000)\n                                }).catch((e) => {\n                                    console.log(e)\n                                    this.setState({\n                                        disabled: false\n                                    })\n                                })\n                        } else {\n                            await APIRequest('POST', `documents/?idWarehouses=${(JSON.parse(sessionStorage.getItem(\"idWarehouse\")).code !== undefined ? JSON.parse(sessionStorage.getItem(\"idWarehouse\")).code : JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse)}`, body)\n                                .then(async res => {\n                                    console.log(res.data)\n                                    this.toast.show({ severity: 'success', summary: 'Ottimo', detail: 'Il suo ordine è stato inserito con successo', life: 3000 });\n                                    localStorage.setItem(\"Prodotti\", []);\n                                    localStorage.setItem(\"Cart\", []);\n                                    localStorage.setItem(\"OrdineRecuperato\", []);\n                                    localStorage.setItem(\"DatiConsegna\", []);\n                                    localStorage.setItem(\"datiComodo\", 0);\n                                    window.sessionStorage.setItem(\"Carrello\", 0);\n                                    window.sessionStorage.setItem(\"totCart\", 0);\n                                    window.sessionStorage.setItem(\"idSupplier\", 0);\n                                    window.sessionStorage.setItem(\"affForOrd\", false)\n                                    if (this.state.role === affiliato && this.state.checked) {\n                                        var selfMail = this.state.results.retailers.idRegistry.email\n                                        await APIRequest(\"GET\", `email/sendDocuments?idDocument=${res.data.result.id}&destEmail=${selfMail}&oggetto=Riepilogo fornitura&cc=`)\n                                            .then((res) => {\n                                                console.log(res.data)\n                                                this.toast?.show({\n                                                    severity: \"success\",\n                                                    summary: \"Ottimo !\",\n                                                    detail: \"L'email è stata inviata con successo\",\n                                                    life: 3000,\n                                                });\n                                                setTimeout(() => {\n                                                    window.location.pathname = affiliatoDocumentiOrdineAcquisto\n                                                }, 3000)\n                                            })\n                                            .catch((e) => {\n                                                console.log(e);\n                                                this.toast?.show({\n                                                    severity: \"error\",\n                                                    summary: \"Siamo spiacenti\",\n                                                    detail: `Non è stato possibile inviare l'email. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                                                    life: 3000,\n                                                });\n                                            });\n                                    }\n                                    setTimeout(() => {\n                                        window.location.pathname = this.state.role === distributore ? distributoreDocumentiOrdineAcquisto : (this.state.role === chain ? chainDocumentiOrdineAcquisto : affiliatoDocumentiOrdineAcquisto);\n                                    }, 3000)\n                                }).catch((e) => {\n                                    console.log(e)\n                                    this.setState({\n                                        disabled: false\n                                    })\n                                })\n                        }\n                    } else {\n                        if (window.sessionStorage.getItem('documentType') === 'Logistica') {\n                            var cc = `${this.state.results.retailers.idAffiliate2.idRegistry2.email},${this.state.ordersMail}`\n                            console.log(cc)\n                            await APIRequest('POST', `documents/?idWarehouses=${this.state.role !== ring ? JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse : JSON.parse(localStorage.getItem('user')).idRegistry.retailers.idAffiliate2.idRegistry2.users[0].warehousesCross[0].idWarehouse}`, body)\n                                .then(async res => {\n                                    console.log(res.data);\n                                    this.toast?.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                                    localStorage.setItem(\"Prodotti\", []);\n                                    localStorage.setItem(\"Cart\", []);\n                                    localStorage.setItem(\"OrdineRecuperato\", []);\n                                    localStorage.setItem(\"DatiConsegna\", []);\n                                    window.sessionStorage.setItem(\"Carrello\", 0);\n                                    window.sessionStorage.setItem(\"totCart\", 0);\n                                    window.sessionStorage.setItem(\"documentType\", '');\n                                    localStorage.setItem(\"datiComodo\", 0);\n                                    await APIRequest(\"GET\", `email/sendDocuments?idDocument=${res.data.result.id}&destEmail=${this.state.selectedMail}&oggetto=${`${this.state.oggetto} ${this.state.results.retailers.idRegistry.firstName}`}&cc=${cc}`)\n                                        .then((res) => {\n                                            console.log(res.data)\n                                            this.toast?.show({\n                                                severity: \"success\",\n                                                summary: \"Ottimo !\",\n                                                detail: \"L'email è stata inviata con successo\",\n                                                life: 3000,\n                                            });\n                                            setTimeout(() => {\n                                                window.location.pathname = pdvStoricoDocumenti\n                                            }, 3000)\n                                        })\n                                        .catch((e) => {\n                                            console.log(e);\n                                            this.toast?.show({\n                                                severity: \"error\",\n                                                summary: \"Siamo spiacenti\",\n                                                detail: `Non è stato possibile inviare l'email. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                                                life: 3000,\n                                            });\n                                        });\n                                }).catch((e) => {\n                                    console.log(e)\n                                    this.toast?.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                                })\n                        } else {\n                            await APIRequest('POST', 'orders/', body)\n                                .then(res => {\n                                    console.log(res.data);\n                                    this.toast.show({ severity: 'success', summary: 'Ottimo', detail: 'Il suo ordine è stato inserito con successo', life: 3000 });\n                                    localStorage.setItem(\"Prodotti\", []);\n                                    localStorage.setItem(\"Cart\", []);\n                                    localStorage.setItem(\"OrdineRecuperato\", []);\n                                    localStorage.setItem(\"DatiConsegna\", []);\n                                    window.sessionStorage.setItem(\"Carrello\", 0);\n                                    window.sessionStorage.setItem(\"totCart\", 0);\n                                    localStorage.setItem(\"datiComodo\", 0);\n                                    var role = localStorage.getItem(\"role\");\n                                    if (role === agente) {\n                                        setTimeout(() => {\n                                            window.location.pathname = agenteGestioneOrdiniAgente;\n                                        }, 3000)\n                                    } else if (role === affiliato) {\n                                        setTimeout(() => {\n                                            window.location.pathname = affiliatoVisualizzaOrdini;\n                                        }, 3000)\n                                    } else if (role === pdv) {\n                                        setTimeout(() => {\n                                            window.location.pathname = pdvGestioneLogisticaOrdini;\n                                        }, 3000)\n                                    }\n                                }).catch((e) => {\n                                    console.log(e)\n                                    this.setState({\n                                        disabled: false\n                                    })\n                                })\n                        }\n                    }\n                } else {\n                    this.setState({ disabled: false })\n                    this.toast.show({ severity: 'error', summary: 'Attenzione !', detail: \"Inserire data di consegna prima di proseguire con l'ordine\", life: 3000 });\n                }\n            } else {\n                this.setState({ disabled: false })\n                this.toast.show({ severity: 'error', summary: 'Attenzione !', detail: \"Aggiungere prodotti prima di proseguire con l'ordine\", life: 3000 });\n            }\n        }\n\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    deleteResult() {\n        var prodInCart = []\n        prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n        let index = prodInCart.findIndex(obj => obj.idProduct2.description === this.state.result.idProduct2.description);\n        prodInCart.splice(index, 1);\n        var totale = [];\n        totale = prodInCart;\n        for (var i = 0; i < prodInCart.length; i++) {\n            if (prodInCart[i].price !== undefined) {\n                totale[i].totale = parseFloat(prodInCart[i].price) * prodInCart[i].quantity;\n            } else {\n                totale[i].totale = parseFloat(prodInCart[i].unitPrice) * prodInCart[i].quantity;\n            }\n        }\n        prodInCart = totale;\n        localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n        this.setState({\n            deleteResultDialog: false,\n            result: this.emptyResult,\n            results2: prodInCart\n        });\n        this.calcTotal()\n        return this.props.getNumbers()\n    }\n    //Chiusura dialogo eliminazione senza azioni\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false\n        });\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result\n        });\n        confirmDialog({\n            message: Costanti.RimProd,\n            header: 'Attenzione',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: this.deleteResult,\n            reject: this.hideDeleteResultDialog\n        });\n    }\n    /* Icona per eliminare un prodotto dal carrello */\n    actionBodyTemplate(rowData) {\n        return (\n            <React.Fragment>\n                <Button icon=\"pi pi-trash\" className=\"p-button-rounded\" onClick={() => this.confirmDeleteResult(rowData)} />\n            </React.Fragment>\n        );\n    }\n    /* Modifica indirizzo tramite inputText */\n    cambioIndirizzo(e) {\n        var datiConsegna = {\n            data: this.state.datiConsegna?.data,\n            idOrder: this.state.datiConsegna?.idOrder,\n            indirizzo: e.target.value.name !== undefined ? e.target.value.name : e.target.value,\n            note: this.state.datiConsegna?.note,\n            stato: this.state.datiConsegna?.stato\n        }\n        this.setState({\n            datiConsegna: datiConsegna,\n            selectedAddress: e.target.value\n        })\n    }\n    /* Modifica note tramite inputTextArea */\n    cambioNote(e) {\n        var datiConsegna = {\n            data: this.state.datiConsegna?.data,\n            idOrder: this.state.datiConsegna?.idOrder,\n            indirizzo: this.state.datiConsegna?.indirizzo,\n            note: e.target.value,\n            stato: this.state.datiConsegna?.stato\n        }\n        this.setState({\n            datiConsegna: datiConsegna\n        })\n    }\n    /* Modifico quantità del prodotto */\n    onRowEditComplete(e, options) {\n        let results2 = [...this.state.results2];\n        results2.forEach(element => {\n            if (element.idProduct !== undefined && options.rowData.idProduct !== undefined) {\n                if (element.idProduct === options.rowData.idProduct) {\n                    if (element.qtaIns !== undefined) {\n                        element.qtaIns = e.value\n                    } else {\n                        element.colli = e.value\n                    }\n                }\n            } else {\n                if (element.idProduct2.id === options.rowData.idProduct2.id) {\n                    if (element.qtaIns !== undefined) {\n                        element.qtaIns = e.value\n                    } else {\n                        element.colli = e.value\n                    }\n                }\n            }\n        })\n        var prodInCart = [];\n        if (localStorage.getItem(\"Cart\") !== '') {\n            prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n            let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === options.rowData.idProduct2.id);\n            if (index !== -1) {\n                prodInCart[index] = options.rowData;\n                localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n            }\n        }\n        this.setState({ results2 });\n        this.calcTotal()\n    }\n    /* Modifico il formato del prodotto e di conseguenza il moltiplicatore delle quantità */\n    onRowEditComplete2(e, options) {\n        let results2 = [...this.state.results2];\n        results2.forEach(element => {\n            if (element.idProduct === undefined) {\n                if (element.idProduct2.id === options.rowData.idProduct2.id) {\n                    if (element.moltiplicatore !== undefined) {\n                        element.moltiplicatore = e\n                        element.quantity = element.moltiplicatore * element.colli\n                        element.totale = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(element.quantity * parseFloat(element.price))\n                    } else {\n                        element.pcsXpackage = e\n                        element.quantity = element.pcsXpackage * element.colli\n                        element.totale = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(element.quantity * parseFloat(element.price))\n                    }\n                }\n            } else {\n                if (element.idProduct === options.rowData.idProduct) {\n                    if (element.moltiplicatore !== undefined) {\n                        element.moltiplicatore = e\n                        element.quantity = element.moltiplicatore * element.qtaIns\n                        element.totale = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(element.quantity * parseFloat(element.price))\n                    } else {\n                        element.pcsXpackage = e\n                        element.quantity = element.pcsXpackage * element.colli\n                        element.totale = new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(element.quantity * parseFloat(element.price))\n                    }\n\n                }\n            }\n        })\n        localStorage.setItem(\"Cart\", JSON.stringify(results2))\n        this.setState({ results2 });\n        this.calcTotal()\n        return this.props.getNumbers()\n    }\n    onEditDiscount(e, options, key) {\n        let results2 = [...this.state.results2];\n        options.rowData[key] = e.value !== null ? e.value : 0\n        if (key === 'ScontoPercentuale') {\n            options.rowData.sell_in = parseFloat(options.rowData.sell_in) - parseFloat(options.rowData.sell_in) * parseFloat(e.value !== null ? e.value : 0) / 100\n        } else {\n            options.rowData.sell_in = parseFloat(options.rowData.sell_in) - parseFloat(e.value !== null ? e.value : 0)\n        }\n        var prodInCart = [];\n        if (localStorage.getItem(\"Cart\") !== '') {\n            prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n            let index = prodInCart.findIndex(obj => (obj.idProduct2 !== undefined ? obj.idProduct2.id : obj.idProduct.id) === options.rowData.idProduct2.id);\n            if (index !== -1) {\n                prodInCart[index] = options.rowData;\n                localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n            }\n        }\n        this.setState({ results2 });\n        this.calcTotal()\n    }\n    /* InputNumber per la modifica dei colli */\n    colliEditor(options) {\n        if (options.rowData.qtaIns !== undefined) {\n            return <InputNumber value={options.rowData['qtaIns']} onValueChange={(e) => this.onRowEditComplete(e, options)} />\n        } else {\n            return <InputNumber value={options.rowData['colli']} onValueChange={(e) => this.onRowEditComplete(e, options)} />\n        }\n\n    }\n    scontoPercEditor(options) {\n        return <InputNumber value={options.rowData['ScontoPercentuale']} onValueChange={(e, key) => this.onEditDiscount(e, options, key = 'ScontoPercentuale')} suffix='%' />\n\n    }\n    scontoValEditor(options) {\n        return <InputNumber value={options.rowData['ScontoAValore']} onValueChange={(e, key) => this.onEditDiscount(e, options, key = 'ScontoAValore')} suffix='€' />\n\n    }\n    /* Dropdown con selettore di formato per la modifica del moltiplicatore e del formato */\n    unitMisEditor(options) {\n        var formato = []\n        if (options.rowData.idProduct2 !== undefined) {\n            for (const object of options.rowData.idProduct2.productsPackagings) {\n                formato.push({\n                    unitMeasure: object.unitMeasure,\n                    pcsXPackage: object.pcsXPackage\n                })\n            }\n        } else {\n            for (const obj of options.rowData.product.productsPackagings) {\n                formato.push({\n                    unitMeasure: obj.unitMeasure,\n                    pcsXPackage: obj.pcsXPackage\n                })\n            }\n        }\n        var placeholder = ''\n        if (options.rowData.moltiplicatore !== undefined) {\n            options.rowData.idProduct2.productsPackagings.forEach(element => {\n                if (parseInt(options.rowData.moltiplicatore) === element.pcsXPackage)\n                    placeholder = element.unitMeasure\n            })\n        }\n        return (\n            <Dropdown value={options.rowData['pcsXpackage']} options={formato} optionLabel=\"unitMeasure\" optionValue=\"pcsXPackage\"\n                onChange={(e) => this.onRowEditComplete2(e.value, options, formato)} style={{ width: '100%' }} placeholder={placeholder}\n                itemTemplate={(option) => {\n                    return <span className={`product-badge status-${option.unitMeasure.toLowerCase()}`}>{option.unitMeasure}</span>\n                }} />\n        );\n    }\n    /* Apertura dialogo aggiunta nuovo indirizzo */\n    /* addNewAddress() {\n        var idRegistry = 0\n        if (this.props.datiConsegna?.idRegistry !== undefined && window.location.pathname !== distributoreCarrello) {\n            idRegistry = this.props.datiConsegna.idRegistry\n        } else if (this.props.results.idRegistry?.id !== undefined) {\n            idRegistry = this.props.results.idRegistry.id\n        } else if (this.props.results.id !== undefined) {\n            idRegistry = this.props.results.id\n        }\n        window.sessionStorage.setItem(\"datiUtili\", idRegistry)\n        this.setState({\n            resultDialog: true\n        })\n    } */\n    /* Chiusura dialogo aggiunta nuovo indirizzo con chiamata asincrona per mappare gli indirizzi nel dropdown */\n    /* async hideAggiungiDest() {\n        this.setState({\n            resultDialog: false\n        })\n        var url = ''\n        if (this.props.datiConsegna?.idRegistry !== undefined && window.location.pathname !== distributoreCarrello) {\n            url = 'destination/?idRegistry=' + this.props.datiConsegna.idRegistry\n        } else if (this.props.results.idRegistry?.id !== undefined) {\n            url = 'destination/?idRegistry=' + this.props.results.idRegistry.id\n        } else if (this.props.results.id !== undefined) {\n            url = 'destination/?idRegistry=' + this.props.results.id\n        }\n        this.cities = []\n        await APIRequest('GET', url)\n            .then(res => {\n                console.log(res.data);\n                res.data.forEach(element => {\n                    var x = []\n                    if (element.destcap !== null) {\n                        x = {\n                            name: element.destind + ' ' + element.destcitta + ' ' + element.destprov + ' ' + element.destcap, code: element.id\n                        }\n                    } else {\n                        x = {\n                            name: element.destind + ' ' + element.destcitta + ' ' + element.destprov, code: element.id\n                        }\n                    }\n                    this.cities.push(x)\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n    } */\n    onPaymentMethodChange(e) {\n        if (this.state.discountPayment.length > 0) {\n            var totale = parseFloat(this.state.total.split('€')[0].replace('.', '').replace(',', '.'))\n            var totTax = parseFloat(this.state.totTax.split('€')[0].replace('.', '').replace(',', '.'))\n            var find = this.state.discountPayment.find(el => el.paymentMethodName === e.value.code)\n            if (find !== undefined) {\n                totale = find.percent ? totale - totale * find.percent / 100 : totale - find.fixed\n                totTax = find.percent ? totTax - totTax * find.percent / 100 : totTax - find.fixed\n                this.setState({\n                    total: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totale),\n                    totTax: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totTax),\n                    Iva: new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(totTax - totale)\n                })\n            }\n        }\n        this.setState({\n            selectedPaymentMethod: e.value\n        })\n    }\n    onKeyUpHandler(e) {\n        var mex = 'Inseriti: ' + e.target.value.length + ' di ' + e.currentTarget.maxLength + ' caratteri'\n        this.setState({\n            mex: mex\n        })\n    }\n    discountTotClass(e) {\n        this.setState({ value: e.value })\n        if (e.value !== 'NessunoSconto') {\n            this.setState({ discountTotClass: '' })\n        } else {\n            this.setState({ discountTotClass: 'd-none' })\n            this.calcTotal()\n        }\n    }\n    onEditDiscountTotal() {\n        this.calcTotal(this.state.value)\n    }\n    async prodOmaggio() {\n        var conditioned_discount = []\n        this.state.results2.forEach(element => {\n            element.conditioned_discount?.map(el => conditioned_discount.push(el))\n        })\n        conditioned_discount = onlyUniqueCondizioneCorrelatiOmaggio(conditioned_discount)\n        var find = conditioned_discount.find(item => item.omaggio !== undefined)\n        if (find !== undefined) {\n            var omaggi = []\n            if (conditioned_discount.length > 0) {\n                var idSupplier = JSON.parse(window.sessionStorage.getItem(\"idSupplier\"))\n                await APIRequest(\"GET\", `supplyingproduct/?idSupplying=${idSupplier}&active=true`)\n                    .then((res) => {\n                        var prodCorr = []\n                        conditioned_discount.forEach(element => {\n                            var verifyCondition = this.state.results2.map(el => el.quantity).reduce((prev, curr) => prev + curr, 0)\n                            if (verifyCondition >= parseInt(element.condizione)) {\n                                omaggi.push(`Condizione: ${element.condizione} Correlati: ${element.correlati.map(el => el).join(', ')} Omaggi: (${Object.keys(element.omaggio)[0]}) ${Object.values(element.omaggio)[0].map(el => Object.keys(el)[0])}`)\n                            }\n                            Object.values(element.omaggio)[0].forEach(obj => {\n                                res.data.find(el => el.idProduct.externalCode === Object.keys(obj)[0] ? prodCorr.push({ ...el, newColli: 0 }) : null)\n                            })\n                        })\n                        this.setState({ results3: prodCorr, resultDialog2: true, omaggi: omaggi, conditioned_discount: conditioned_discount })\n                    })\n                    .catch((e) => {\n                        console.log(e);\n                        this.toast.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare il listino per l'aggiunta dei prodotti omaggio. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    });\n            } else {\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: \"Non sono disponibili omaggi per i prodotti selezionati\",\n                    life: 3000,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: \"Non sono previsti omaggi per i prodotti selezionati\",\n                life: 3000,\n            });\n        }\n    }\n    hideProdOmaggio() {\n        this.setState({ resultDialog2: false })\n    }\n    /* Reperiamo i colli ordinati per il prodotto */\n    newColliBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Colli}</span>\n                {results.newColli}\n            </React.Fragment>\n        );\n    }\n    /* InputNumber per la modifica dei colli */\n    newColliEditor(options) {\n        return <InputNumber value={options.rowData['newColli']} onValueChange={(e) => this.onRowModalEditComplete(e, options)} />\n\n    }\n    onRowModalEditComplete(e, options) {\n        options.rowData.newColli = e.value\n    }\n    selectionChangeHandler(e) {\n        var filter = e.value.filter(element => element.newColli > 0)\n        if (filter.length < e.value.length) {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I prodotti selezionati devono avere quantità inputata maggiore di 0\",\n                life: 3000,\n            });\n        } else {\n            var quantità = 0\n            var qtaTotProd = 0\n            var nOmaggi = []\n            var omaggiTot = []\n            qtaTotProd = this.state.results2.map(el => el.quantity).reduce((prev, curr) => prev + curr, 0)\n            var find = this.state.conditioned_discount.find(el => parseInt(el.condizione) >= qtaTotProd)\n            if (find !== undefined) {\n                nOmaggi = parseInt(Object.keys(find.omaggio)[0])\n                omaggiTot = Object.values(find.omaggio)[0]\n                filter.forEach(el => {\n                    var maximal = omaggiTot.find(item => Object.keys(item)[0] === el.idProduct.externalCode)\n                    if (Object.values(maximal)[0] !== -1 && Object.values(maximal)[0] > el.newColli) {\n                        this.toast.show({\n                            severity: \"warn\",\n                            summary: \"Attenzione!\",\n                            detail: \"La quantità inserita supera il numero di omaggi a disposizione per questo prodotto\",\n                            life: 3000,\n                        });\n                    } else {\n                        var found = el.idProduct.productsPackagings.find(obj => obj.pcsXPackage === 1)\n                        el.moltiplicatore = found !== undefined ? found.pcsXPackage : el.idProduct.productsPackagings[0].pcsXPackage\n                        quantità += el.newColli\n                    }\n                })\n                if (quantità > nOmaggi) {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"La quantità inserita supera il numero di omaggi a disposizione\",\n                        life: 3000,\n                    });\n                } else {\n                    this.setState({ selectedProducts: filter })\n                }\n            } else {\n                this.toast.show({\n                    severity: \"warn\",\n                    summary: \"Attenzione!\",\n                    detail: \"Non è stato raggiunto il target necessario per accedere ai prodotti omaggio rivedi le condizioni di quantità minime prima di procedere\",\n                    life: 3000,\n                });\n            }\n        }\n    }\n    selectionDiscountHandler(e) {\n        this.setState({ selectedProductsToDiscount: e.value })\n    }\n    /* Funzione asincrona con chiamata post sulle destinazioni */\n    async aggiungiOmaggio() {\n        console.log(this.state.selectedProducts, this.state.selectedProductsToDiscount)\n        var tableProd = [...this.state.results2]\n        this.state.selectedProducts.forEach(element => {\n            //Valutare accisa nello sconto se indicato nell'ogetto \n            var found = tableProd.find(el => el.id === element.id)\n            if (found !== undefined) {\n                found.qtaIns += element.newColli\n                found.quantity = found.qtaIns * parseInt(found.moltiplicatore)\n            } else {\n                element.initPrice = element.sell_in\n                element.idProduct2 = element.idProduct\n                element.ScontoPercentuale = 0\n                element.ScontoAValore = 0\n                var pack = element.idProduct2.productsPackagings.find(el => el.default === true)\n                if (pack) {\n                    element.moltiplicatore = pack.pcsXPackage\n                } else {\n                    element.moltiplicatore = element.idProduct2.productsPackagings[0].pcsXPackage\n                }\n                element.qtaIns = element.newColli\n                element.quantity = element.qtaIns * element.moltiplicatore\n                tableProd = [...tableProd, element]\n            }\n        })\n        this.state.selectedProductsToDiscount.forEach(el => {\n            var find = tableProd.find(obj => obj.id === el.id)\n            if (find !== undefined) {\n                find.sell_in = find.totale / find.quantity\n            }\n        })\n        console.log(tableProd)\n        this.setState({ results2: tableProd, resultDialog2: false })\n        this.calcTotal()\n    };\n    zeroIvaHandler(e) {\n        this.setState({ zeroIva: e.checked })\n        var prod = [...this.state.results2]\n        var prodInCart = JSON.parse(localStorage.getItem(\"Cart\"));\n        if (e.checked) {\n            prod.forEach(el => {\n                el.idProduct2.ivaPrev = el.idProduct2.iva\n                el.idProduct2.iva = '0'\n            })\n            prodInCart.forEach(element => {\n                if (element.idProduct) {\n                    element.idProduct.ivaPrev = element.idProduct.iva\n                    element.idProduct.iva = '0'\n                } else {\n                    element.idProduct2.ivaPrev = element.idProduct2.iva\n                    element.idProduct2.iva = '0'\n                }\n            })\n        } else {\n            prod.forEach(element => {\n                element.idProduct2.iva = element.idProduct2.ivaPrev\n                delete element.idProduct2.ivaPrev\n            })\n            prodInCart.forEach(element => {\n                console.log(element)\n                if (element.idProduct) {\n                    element.idProduct.iva = element.idProduct.ivaPrev\n                    delete element.idProduct.ivaPrev\n                } else {\n                    element.idProduct2.iva = element.idProduct2.ivaPrev\n                    delete element.idProduct2.ivaPrev\n                }\n            })\n        }\n        this.setState({\n            results2: prod\n        })\n        localStorage.setItem(\"Cart\", JSON.stringify(prodInCart));\n        this.calcTotal()\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta \n        /* const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideAggiungiDest} > {Costanti.Chiudi} </Button>\n            </React.Fragment>\n        ); */\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta \n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideProdOmaggio} > {Costanti.Chiudi} </Button>\n            </React.Fragment>\n        );\n        const header = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <i className=\"pi pi-search mr-2\" />\n                            <InputText className=\"w-100\" type=\"search\" onInput={(e) => this.setState({ globalFilter: e.target.value })} placeholder=\"Cerca...\" />\n                        </span>\n                    </div>\n                </div>\n            </div>\n        );\n        const header2 = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <i className=\"pi pi-search mr-2\" />\n                            <InputText className=\"w-100\" type=\"search\" onInput={(e) => this.setState({ globalFilter2: e.target.value })} placeholder=\"Cerca...\" />\n                        </span>\n                    </div>\n                </div>\n            </div>\n        );\n        return (\n            <div className=\"container-order-details px-3 px-lg-0 mt-4\">\n                <Toast ref={(el) => this.toast = el} />\n                <div className=\"justify-content-center mt-3\">\n                    <div className=\"col-12\">\n                        {/* Listato con informazioni anagrafiche del cliente */}\n                        {(window.location.pathname !== distributoreCarrello && this.state.role !== chain) ?\n                            <h3 className=\"p-text-center p-text-bold\">{Costanti.DatCli}</h3>\n                            : <h3 className=\"p-text-center p-text-bold\">{Costanti.DatForn}</h3>\n                        }\n                        <div className=\"row mt-4\">\n                            <div className=\"col-12 col-md-6 mb-4 mb-md-0\">\n                                <ul className=\"list-group\">\n                                    <li className=\"list-group-item\"><i className=\"pi pi-user mr-3\"></i><strong>{Costanti.Nome}</strong>: {this.nameBodyTemplate()}</li>\n                                    <li className=\"list-group-item\"><i className=\"pi pi pi-mobile mr-3\"></i><strong>{Costanti.Tel}</strong>: {this.telBodyTemplate()}</li>\n                                    <li className=\"list-group-item\"><i className=\"pi pi pi-credit-card mr-3\"></i><strong>{Costanti.pIva}</strong>: {this.pIvaBodyTemplate()}</li>\n                                    {(window.location.pathname === distributoreCarrello || this.state.role === chain) &&\n                                        <li className=\"list-group-item\"><i className=\"pi pi-euro mr-3\"></i><strong>{Costanti.paymentMetod}</strong>: {this.paymentBodyTemplate()}</li>\n                                    }\n                                </ul>\n                            </div>\n                            <div className=\"col-12 col-md-6\">\n                                <ul className=\"list-group\">\n                                    <li className=\"list-group-item\"><i className=\"pi pi-directions mr-3\"></i><strong>{Costanti.Indirizzo}</strong>: {this.addressBodyTemplate()}</li>\n                                    <li className=\"list-group-item\"><i className=\"pi pi-map-marker mr-3\"></i><strong>{Costanti.Città}</strong>: {this.cityBodyTemplate()}</li>\n                                    <li className=\"list-group-item\"><i className=\"pi pi-compass mr-3\"></i><strong>{Costanti.CodPost}</strong>: {this.capBodyTemplate()}</li>\n                                </ul>\n                            </div>\n\n                        </div>\n                        <div className=\"row justify-content-center mt-3\">\n                            <div className=\"col-12\">\n                                <hr />\n                                <div className=\"datatable-responsive-demo datatable-responsive-order wrapper mt-4\">\n                                    <h3 className=\"p-text-center p-text-bold\">{Costanti.Prodotti}</h3>\n                                    {/* Componente primereact per la creazione della tabella */}\n                                    {localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) &&\n                                        <DataTable className=\"p-datatable-responsive-demo editable-prices-table mt-4\" ref={(el) => this.dt = el} value={this.state.results2}\n                                            dataKey=\"id\" editMode=\"row\" onRowEditComplete={this.onRowEditComplete} responsiveLayout=\"scroll\" autoLayout=\"true\" csvSeparator=\";\"\n                                        >\n                                            <Column field=\"image\" body={this.imageBodyTemplate}></Column>\n                                            <Column field=\"description\" header={Costanti.Prodotto} body={this.nameBodyTemplate2} sortable ></Column>\n                                            <Column field=\"externalCode\" header={Costanti.exCode} body={this.externalCodeBodyTemplate} sortable ></Column>\n                                            <Column field=\"quantity\" header={Costanti.UnitMis} body={this.unitMisBodyTemplate} editor={(options) => this.unitMisEditor(options)} sortable ></Column>\n                                            <Column field=\"pcspkgs\" header=\"Pezzi per package\" body={this.pcxpkgBodyTemplate} sortable ></Column>\n                                            <Column field=\"quantity\" header={Costanti.Colli} body={this.colliBodyTemplate} editor={(options) => this.colliEditor(options)} sortable ></Column>\n                                            <Column field=\"quantity\" header={Costanti.Quantità} body={this.quantitàBodyTemplate} sortable ></Column>\n                                            <Column field=\"price\" header={Costanti.Prezzo} body={this.priceBodyTemplate} sortable ></Column>\n                                            <Column field=\"totale\" header={Costanti.Tot} body={this.totaleBodyTemplate} sortable ></Column>\n                                            <Column field=\"iva\" header={Costanti.Iva} body={this.ivaBodyTemplate} sortable ></Column>\n                                            <Column field=\"totTax\" header={Costanti.TotTax} body={this.totTaxBodyTemplate} sortable ></Column>\n                                            <Column rowEditor headerStyle={{ width: '10%', minWidth: '8rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                                            <Column body={this.actionBodyTemplate} ></Column>\n                                        </DataTable>\n                                    }\n                                    {(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) &&\n                                        <>\n                                            <div className='d-flex justify-content-end mr-4'>\n                                                <Button className=\"buttonPlus p-button\" onClick={() => this.prodOmaggio()}><i className=\"pi pi-plus mr-2\"></i>Aggiungi prodotto omaggio</Button>\n                                            </div>\n                                            <DataTable className=\"p-datatable-responsive-demo editable-prices-table mt-4\" ref={(el) => this.dt = el} value={this.state.results2}\n                                                dataKey=\"id\" editMode=\"row\" onRowEditComplete={this.onRowEditComplete} responsiveLayout=\"scroll\" autoLayout=\"true\" csvSeparator=\";\"\n                                            >\n                                                <Column field=\"image\" body={this.imageBodyTemplate}></Column>\n                                                <Column field=\"description\" header={Costanti.Prodotto} body={this.nameBodyTemplate2} sortable ></Column>\n                                                <Column field=\"externalCode\" header={Costanti.exCode} body={this.externalCodeBodyTemplate} sortable ></Column>\n                                                <Column field=\"quantity\" header={Costanti.UnitMis} body={this.unitMisBodyTemplate} editor={(options) => this.unitMisEditor(options)} sortable ></Column>\n                                                <Column field=\"quantity\" header={Costanti.Colli} body={this.colliBodyTemplate} editor={(options) => this.colliEditor(options)} sortable ></Column>\n                                                <Column field=\"quantity\" header={Costanti.Quantità} body={this.quantitàBodyTemplate} sortable ></Column>\n                                                <Column field=\"price\" header={Costanti.Prezzo} body={this.priceBodyTemplate} sortable ></Column>\n                                                <Column field=\"discount_active\" header={Costanti.ScontoAttivo} body={this.discount_active} sortable ></Column>\n                                                <Column field=\"inflation_active\" header={Costanti.Rincaro} body={this.inflation_active} sortable ></Column>\n                                                <Column field=\"conditioned_discount\" header={Costanti.ScontoCondizionato} body={this.conditioned_discount} sortable ></Column>\n                                                <Column field=\"totale\" header={Costanti.Tot} body={this.totaleBodyTemplate} sortable ></Column>\n                                                <Column field=\"discount_note\" header={Costanti.Note} body={this.noteBodyTemplate} sortable ></Column>\n                                                <Column field='ScontoPercentuale' header={Costanti.UltScontoPercentuale} body={this.ScontoPercentualeBodyTemplate} editor={(options) => this.scontoPercEditor(options)}  ></Column>\n                                                <Column field='ScontoAValore' header={Costanti.UltScontoAValore} body={this.ScontoAValoreBodyTemplate} editor={(options) => this.scontoValEditor(options)} ></Column>\n                                                <Column rowEditor headerStyle={{ width: '10%', minWidth: '8rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                                                <Column body={this.actionBodyTemplate} ></Column>\n                                            </DataTable>\n                                        </>\n                                    }\n                                    <hr />\n                                </div>\n                            </div>\n                            <div className=\"d-flex justify-content-end flex-column align-items-end w-100\">\n                                {(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) &&\n                                    <div className='d-flex align-items-end flex-column w-100 mr-5 mb-3'>\n                                        <Dropdown className='px-3' value={this.state.value} options={this.options} onChange={(e) => this.discountTotClass(e)} optionLabel='name' optionValue=\"value\" placeholder=\"Scontistica\" />\n                                        {this.state.value && this.state.discountTotClass !== 'd-none' &&\n                                            <div className='mt-3'>\n                                                <InputNumber value={this.state.value2} onValueChange={(e) => this.setState({ value2: e.value })} suffix={this.state.value === 'ScontoPercentuale' ? '%' : '€'} />\n                                                <Button className=\"buttonPlus p-button-rounded ml-2 p-2\" onClick={() => this.onEditDiscountTotal()}><i className=\"pi pi-check-circle\"></i></Button>\n                                            </div>\n                                        }\n                                        {(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain) &&\n                                            <div className='d-flex justify-content-end flex-row w-100 mt-3'>\n                                                <i className=\"pi pi-tag mr-2\">\n                                                </i>\n                                                <label htmlFor=\"binary\" className='mr-2 mb-0'><strong>{Costanti.zeroIVACheck}</strong></label>\n                                                <Checkbox inputId=\"binary\" checked={this.state.zeroIva} onChange={e => this.zeroIvaHandler(e)} />\n                                            </div>\n                                        }\n                                    </div>\n                                }\n                                <span className=\"mr-5\"> <b>{Costanti.Tot}</b> {this.state.total} </span>\n                                {this.state.zeroIva ?\n                                    (\n                                        <span className=\"mr-5\"><del> <b className=\"ml-2\">{Costanti.Iva}</b> {isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.Iva} </del></span>\n                                    ) : (\n                                        <span className=\"mr-5\"> <b className=\"ml-2\">{Costanti.Iva}</b> {isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.Iva} </span>\n                                    )\n                                }\n                                <span className=\"mr-5\"><b className=\"ml-2\">{Costanti.TotTax}</b> {isNaN(parseFloat(this.state.Iva)) ? '0,00 €' : this.state.totTax} </span>\n                            </div>\n                        </div>\n                        <div className=\"row justify-content-center mt-4\">\n                            <h3 className=\"p-text-center p-text-bold col-12\" >{Costanti.DatiCons}</h3>\n                            <div className=\"d-flex justify-content-center col-12\">\n                                <div className=\"col-12 mb-4 mb-md-0\">\n                                    <ul className=\"list-group\">\n\n                                        <li className=\"list-group-item\">\n                                            <div className='row'>\n                                                {localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) &&\n                                                    <div className='col-12 col-sm-6 mb-3 mb-lg-0'>\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2'>\n                                                                {/* Selettore per l'indirizzo di consegna */}\n                                                                <i className=\"pi pi-user mr-3\">\n                                                                </i>\n                                                                <strong>\n                                                                    {Costanti.Indirizzo}\n                                                                </strong>:\n                                                            </div>\n                                                            <div className='col-12 col-md-10 col-xl-10'>\n                                                                <Dropdown value={this.state.selectedAddress} options={this.cities} onChange={(e) => { this.cambioIndirizzo(e) }} optionLabel=\"name\" placeholder=\"Seleziona indirizzo\" emptyMessage=\"Non ci sono indirizzi disponibili. Proseguire per utilizzare quello di default del cliente\" filterBy='name' filter />\n                                                                {/* <Button className=\"buttonPlus p-button-rounded ml-0 ml-sm-1 ml-md-2 ml-lg-3\" onClick={() => this.addNewAddress()} /* icon=\"pi pi-search-plus\" *//* > <i className=\"pi pi-plus-circle \"></i> </Button> */}\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                }\n                                                <div className={(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) ? 'col-12' : 'col-12 col-sm-6'}>\n                                                    {localStorage.getItem('role') !== agente &&\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2'>\n                                                                {/* Selettore per l'indirizzo di consegna */}\n                                                                <i className=\"pi pi-calendar mr-3\">\n                                                                </i>\n                                                                <strong>\n                                                                    {Costanti.Data}\n                                                                </strong>:\n                                                            </div>\n                                                            <div className='col-12 col-md-10 col-xl-10'>\n                                                                <Calendar className='w-auto' dateFormat=\"dd/mm/yy\" minDate={this.minDate} placeholder='Data di consegna' value={this.state.data} onChange={(e) => this.setState({ data: e.value })} readOnlyInput showIcon />\n                                                            </div>\n                                                        </div>\n                                                    }\n                                                    {localStorage.getItem('role') === agente &&\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-3'>\n                                                                {/* Selettore per l'indirizzo di consegna */}\n                                                                <i className=\"pi pi-euro mr-3\">\n                                                                </i>\n                                                                <strong>\n                                                                    {Costanti.ModalitàPag}\n                                                                </strong>:\n                                                            </div>\n                                                            <div className='col-12 col-md-10 col-xl-9'>\n                                                                <Dropdown value={this.state.selectedPaymentMethod} options={this.state.paymentMetod} onChange={this.onPaymentMethodChange} optionLabel=\"name\" placeholder=\"Seleziona modalità di pagamento\" filter filterBy=\"name\" />\n                                                            </div>\n                                                        </div>\n                                                    }\n                                                </div>\n                                            </div>\n                                        </li>\n                                        {(localStorage.getItem('role') === distributore || localStorage.getItem('role') === chain || JSON.parse(sessionStorage.getItem('affForOrd'))) &&\n                                            <li className=\"list-group-item\">\n                                                <div className='row w-100'>\n                                                    <div className='d-flex align-items-center mb-3 mb-lg-0 col-12 col-sm-12 col-xl-2 pr-0'>\n                                                        {/* Selettore per l'indirizzo di consegna */}\n                                                        <i className=\"pi pi-euro mr-3\">\n                                                        </i>\n                                                        <strong>\n                                                            {Costanti.ModalitàPag}\n                                                        </strong>:\n                                                    </div>\n                                                    <div className='col-12 col-md-10 col-xl-10 pl-0'>\n                                                        <Dropdown value={this.state.selectedPaymentMethod} options={this.state.paymentMetod} onChange={this.onPaymentMethodChange} optionLabel=\"name\" placeholder=\"Seleziona modalità di pagamento\" filter filterBy=\"name\" />\n                                                        {this.state.discountPayment.length > 0 &&\n                                                            this.state.discountPayment.map(el => {\n                                                                return (\n                                                                    <span className='p-3'>{Costanti.Pagamento}: <strong>{el.paymentMethodName}</strong> {Costanti.Sconto}: <strong>{`${el.fixed ? `${el.fixed}€` : `${el.percent}%`}`}</strong></span>\n                                                                )\n                                                            })\n                                                        }\n                                                    </div>\n                                                </div>\n                                            </li>\n                                        }\n                                        {localStorage.getItem('role') === affiliato && JSON.parse(sessionStorage.getItem('affForOrd')) &&\n                                            <li className=\"list-group-item\">\n                                                <div className='row w-100'>\n                                                    <div className='d-flex align-items-center col-12 col-lg-6 mb-1 mt-1'>\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-md-0 col-12'>\n                                                                <i className=\"pi pi-envelope mr-3\">\n                                                                </i>\n                                                                <label htmlFor=\"binary\" className='mr-2 mb-0'><strong>{Costanti.mailAffCheck}</strong></label>\n                                                                <Checkbox inputId=\"binary\" checked={this.state.checked} onChange={e => this.setState({ checked: e.checked })} />\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </li>\n                                        }\n                                        {localStorage.getItem('role') !== distributore && localStorage.getItem('role') !== chain && !JSON.parse(sessionStorage.getItem('affForOrd')) &&\n                                            <li className=\"list-group-item\">\n                                                <div className='row'>\n                                                    <div className='col-12 col-lg-6 mb-1 mt-1'>\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-md-0 col-12 col-md-6 col-lg-2 col-xl-2'>\n                                                                {/* Selettore per l'indirizzo email */}\n                                                                <i className=\"pi pi-envelope mr-3\">\n                                                                </i>\n                                                                <strong>\n                                                                    {Costanti.Email}\n                                                                </strong>:\n                                                            </div>\n                                                            <div className='col-12 col-md-6 col-lg-10 col-xl-10'>\n                                                                <InputText className='w-50' value={this.state.selectedMail} onChange={(e) => this.setState({ selectedMail: e.target.value })} />\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                    <div className='d-flex align-items-center col-12 col-lg-6 mb-1 mt-1'>\n                                                        <div className='row'>\n                                                            <div className='d-flex align-items-center mb-3 mb-md-0 col-12'>\n                                                                <Checkbox inputId=\"binary\" checked={this.state.checked} onChange={e => this.setState({ checked: e.checked })} />\n                                                                <label htmlFor=\"binary\" className='ml-2'>{Costanti.MailConfOrd}</label>\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </li>\n                                        }\n                                        <li className=\"list-group-item d-flex align-items-center\">\n                                            <div className=\"row w-100\">\n                                                <div className=\"d-flex align-items-center mb-3 mb-md-0 col-12 col-sm-2 col-xl-1\">\n                                                    {/* InputTextArea per le note */}\n                                                    <i className=\"pi pi-pencil mr-3\"></i>\n                                                    <strong>{Costanti.Note}</strong>:\n                                                </div>\n                                                <div className=\"col-12 col-md-10 col-xl-11\">\n                                                    <InputTextarea maxLength={240} onKeyUp={(e) => this.onKeyUpHandler(e)} className=\"inputNote\" value={this.state.datiConsegna?.note} onChange={(e) => { this.cambioNote(e) }} />\n                                                    <div className='d-flex justify-content-end'><span>{this.state.mex}</span></div>\n                                                </div>\n                                            </div>\n                                        </li>\n                                    </ul>\n                                </div>\n                            </div>\n                        </div>\n                        <div className=\"row mt-5 mb-5\">\n                            <div className='col-12'>\n                                <div className=\"d-flex justify-content-center\">\n                                    {/* Bottone salva per l'invio dell'ordine */}\n                                    <div className=\"p-text-center\">\n                                        <Button className='mr-4 goBackBtn' onClick={() => window.history.back()} ><i className=\"pi pi-chevron-left mr-2\"></i><span className=\"mx-auto\"> {Costanti.Indietro} </span></Button>\n                                        <Button onClick={this.Invia} disabled={this.state.disabled} ><span className=\"mx-auto\"> {Costanti.salvaOrd} </span><i className=\"pi pi-check ml-2\"></i></Button>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                {/* Struttura dialogo per l'aggiunta di una nuova destinazione */}\n                {/* <Dialog visible={this.state.resultDialog} header={Costanti.AggDest} modal className=\"p-fluid modalBox\" footer={resultDialogFooter} onHide={this.hideAggiungiDest}>\n                    <AggiungiDestinazioni />\n                </Dialog> */}\n                {/* Struttura dialogo per l'aggiunta di un prodotto omaggio */}\n                <Dialog visible={this.state.resultDialog2} header={Costanti.AggProdOmaggio} modal className=\"p-fluid modalBox\" footer={resultDialogFooter2} onHide={this.hideProdOmaggio}>\n                    <div className=\"modalBody\">\n                        {this.state.addTribute ? (\n                            <>\n                                {this.state.omaggi ? this.state.omaggi?.map((el, key) => {\n                                    return (\n                                        <React.Fragment key={key}>\n                                            <div className='d-flex justify-content-center w-100 border p-2'>\n                                                <span className=''>{`${el}`}</span>\n                                            </div>\n                                        </React.Fragment>\n                                    )\n                                })\n                                    :\n                                    null\n                                }\n                                <div className=\"p-grid p-fluid mt-4\">\n                                    <div className=\"datatable-responsive-demo wrapper w-100\">\n                                        <DataTable\n                                            className=\"p-datatable-responsive-demo editable-prices-table\"\n                                            value={this.state.results3}\n                                            globalFilter={this.state.globalFilter}\n                                            header={header}\n                                            dataKey=\"id\"\n                                            editMode=\"row\"\n                                            onRowEditComplete={this.onRowModalEditComplete}\n                                            responsiveLayout=\"scroll\"\n                                            autoLayout=\"true\"\n                                            paginator\n                                            rows={10}\n                                            rowsPerPageOptions={[10, 20, 50]}\n                                            selection={this.state.selectedProducts}\n                                            onSelectionChange={(e) => this.selectionChangeHandler(e)}\n                                            emptyMessage=\"Non ci sono elementi da visualizzare\"\n                                            selectionMode='checkbox'\n                                            csvSeparator=\";\"\n                                        >\n                                            <Column selectionMode=\"multiple\" headerStyle={{ width: \"3em\" }} ></Column>\n                                            <Column field=\"idProduct.externalCode\" header={Costanti.exCode} sortable ></Column>\n                                            <Column field=\"idProduct.description\" header={Costanti.Prodotto} sortable ></Column>\n                                            <Column field=\"newColli\" header={Costanti.Quantità} body={this.newColliBodyTemplate} editor={(options) => this.newColliEditor(options)}  ></Column>\n                                            <Column className=\"modActionColumn\" rowEditor headerStyle={{ width: '7rem' }} bodyStyle={{ textAlign: 'center' }} ></Column>\n                                        </DataTable>\n                                    </div>\n                                </div>\n                                <div className=\"d-flex justify-content-center mb-2 mt-4\">\n                                    {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                                    <Button\n                                        id=\"invia\"\n                                        className=\"p-button saveList justify-content-center float-right ionicon mx-0 w-auto\"\n                                        onClick={() => this.state.selectedProducts ? this.setState({ addTribute: false }) : this.toast.show({ severity: \"warn\", summary: \"Attenzione!\", detail: \"Selezionare prodotti prima di procedere\", life: 3000, })}\n                                    >\n                                        <i className='pi pi-angle-double-right mr-2'></i>\n                                        {Costanti.Procedi}\n                                    </Button>\n                                </div>\n                            </>\n                        ) : (\n                            <>\n                                <div className=\"p-grid p-fluid\">\n                                    <h4 className='text-center my-3 w-100'>{Costanti.SelProdScont}</h4>\n                                    <div className=\"datatable-responsive-demo wrapper w-100\">\n                                        <DataTable\n                                            className=\"p-datatable-responsive-demo editable-prices-table\"\n                                            value={this.state.results2}\n                                            globalFilter={this.state.globalFilter2}\n                                            header={header2}\n                                            dataKey=\"id\"\n                                            responsiveLayout=\"scroll\"\n                                            autoLayout=\"true\"\n                                            paginator\n                                            rows={10}\n                                            rowsPerPageOptions={[10, 20, 50]}\n                                            selection={this.state.selectedProductsToDiscount}\n                                            onSelectionChange={(e) => this.selectionDiscountHandler(e)}\n                                            emptyMessage=\"Non ci sono elementi da visualizzare\"\n                                            selectionMode='checkbox'\n                                            csvSeparator=\";\"\n                                        >\n                                            <Column selectionMode=\"multiple\" headerStyle={{ width: \"3em\" }} ></Column>\n                                            <Column field=\"idProduct2.externalCode\" header={Costanti.exCode} sortable ></Column>\n                                            <Column field=\"idProduct2.description\" header={Costanti.Prodotto} sortable ></Column>\n                                        </DataTable>\n                                    </div>\n                                </div>\n                                <div className='row mt-3'>\n                                    <div className='col-12 col-md-6 d-flex justify-content-center justify-content-md-end mb-2 mb-md-0'>\n                                        <Button\n                                            className=\"p-button ionicon mx-0 w-auto justify-content-center justify-content-md-start\"\n                                            onClick={() => this.setState({ addTribute: true })}\n                                        >\n                                            <i className='pi pi-angle-double-left mr-2'></i>\n                                            {Costanti.Indietro}\n                                        </Button>\n                                    </div>\n                                    <div className='col-12 col-md-6 d-flex justify-content-center justify-content-md-start'>\n                                        <Button\n                                            className=\"p-button ionicon mx-0 w-auto justify-content-center justify-content-md-start\"\n                                            onClick={this.aggiungiOmaggio}\n                                        >\n                                            <i className='pi pi-check mr-2'></i>\n                                            {Costanti.Conferma}\n                                        </Button>\n                                    </div>\n                                </div>\n                            </>\n                        )\n                        }\n\n                    </div>\n                </Dialog>\n            </div >\n        )\n    }\n}\n\nexport default connect(null, { getNumbers })(CarrelloGen);"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,QAAQ,MAAM,iCAAiC;AACtD;AACA,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,EAAEC,SAAS,QAAQ,6CAA6C;AACnF,SAASC,UAAU,QAAQ,gDAAgD;AAC3E,SAASC,OAAO,QAAQ,aAAa;AACrC,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SACIC,SAAS,EACTC,gCAAgC,EAChCC,yBAAyB,EACzBC,MAAM,EACNC,0BAA0B,EAC1BC,KAAK,EACLC,4BAA4B,EAC5BC,YAAY,EACZC,oBAAoB,EACpBC,mCAAmC,EACnCC,GAAG,EACHC,0BAA0B,EAC1BC,mBAAmB,EACnBC,IAAI,QACD,aAAa;AACpB,SAASC,oCAAoC,QAAQ,wCAAwC;AAC7F,SAASC,eAAe,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,WAAW,SAASvC,SAAS,CAAC;EAahCwC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACd,CAAC;IAo9BD;IAAA,KACAC,KAAK,GAAG,YAAY;MAChB,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjC,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,KAAK,GAAG,EAAE;MACd,IAAI,IAAI,CAACC,KAAK,CAACC,eAAe,CAACC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACF,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAI,CAACuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;QAC9J,IAAI,CAACb,QAAQ,CAAC;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC;QAClC,IAAI,CAACa,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,cAAc;UAAEC,MAAM,EAAE,uEAAuE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAChK,CAAC,MAAM;QACH;QACA,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;UACnC,IAAIA,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,KAAKC,SAAS,EAAE;YACrDF,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,CAACF,OAAO,CAACI,EAAE,IAAI;cAChD,IAAIH,OAAO,CAACI,cAAc,KAAKF,SAAS,EAAE;gBACtC,IAAIG,UAAU,CAACL,OAAO,CAACI,cAAc,CAAC,KAAKD,EAAE,CAACrB,WAAW,EAAE;kBACvDD,WAAW,GAAGsB,EAAE,CAACtB,WAAW;kBAC5BC,WAAW,GAAGqB,EAAE,CAACrB,WAAW;kBAC5BC,KAAK,GAAGiB,OAAO,CAACM,QAAQ,GAAGH,EAAE,CAACrB,WAAW;gBAC7C;cACJ,CAAC,MAAM;gBACH,IAAIkB,OAAO,CAACO,WAAW,KAAKJ,EAAE,CAACrB,WAAW,EAAE;kBACxCD,WAAW,GAAGsB,EAAE,CAACtB,WAAW;kBAC5BC,WAAW,GAAGqB,EAAE,CAACrB,WAAW;kBAC5BC,KAAK,GAAGiB,OAAO,CAACM,QAAQ,GAAGH,EAAE,CAACrB,WAAW;gBAC7C;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,MAAM;YACHD,WAAW,GAAGmB,OAAO,CAACnB,WAAW;YACjCC,WAAW,GAAGkB,OAAO,CAACO,WAAW;YACjCxB,KAAK,GAAGiB,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACO,WAAW;UAClD;UACA;UACA,IAAIP,OAAO,CAACQ,KAAK,KAAKN,SAAS,EAAE;YAC7B,IAAIO,GAAG,GAAGT,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,GAAG,IAAI;UACjE,CAAC,MAAM;YACHC,GAAG,GAAGT,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACU,SAAS,KAAKR,SAAS,GAAGF,OAAO,CAACU,SAAS,GAAGV,OAAO,CAACW,OAAO,CAAC,GAAG,IAAI;UACrH;UACA;UACA,IAAID,SAAS,GAAG,CAAC;UACjB,IAAIV,OAAO,CAACQ,KAAK,KAAKN,SAAS,EAAE;YAC7BQ,SAAS,GAAGL,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC;UACzC,CAAC,MAAM;YACHE,SAAS,GAAGL,UAAU,CAACL,OAAO,CAACU,SAAS,KAAKR,SAAS,GAAGF,OAAO,CAACU,SAAS,GAAGV,OAAO,CAACW,OAAO,CAAC;UACjG;UACA,IAAIC,GAAG,GAAG,EAAE;UACZ,IAAIZ,OAAO,CAACa,QAAQ,KAAKX,SAAS,EAAE;YAChC,IAAIY,OAAO,GAAGT,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,GAAGH,UAAU,CAACL,OAAO,CAACa,QAAQ,CAAC;YACtED,GAAG,GAAGE,OAAO,GAAGd,OAAO,CAACM,QAAQ,GAAG,IAAI;UAC3C;UACA;UACA,IAAIS,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;YACnI,IAAI2B,IAAI,GAAGlB,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,CAACiB,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACrB,WAAW,KAAKA,WAAW,CAAC;YAC3FF,QAAQ,CAACuC,IAAI,CACT;cACInD,EAAE,EAAEgC,OAAO,CAAC1B,UAAU,CAACN,EAAE;cACzBsC,QAAQ,EAAEN,OAAO,CAACM,QAAQ;cAC1Bc,KAAK,EAAEf,UAAU,CAACI,GAAG,CAAC;cACtBY,UAAU,EAAE,IAAI,CAACrC,KAAK,CAACsC,OAAO,GAAGjB,UAAU,CAACI,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC,GAAGT,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG,GAAG;cACnHC,mBAAmB,EAAEN,IAAI,CAAClD,EAAE;cAC5B0C,SAAS,EAAEA,SAAS;cACpBe,eAAe,EAAE1C,KAAK;cACtB2C,GAAG,EAAE1B,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG;YAClC,CACJ,CAAC;UACL,CAAC,MAAM;YACH,IAAIR,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,WAAW,EAAE;cAC/D,IAAIoC,CAAC,GAAAC,aAAA,CAAAA,aAAA,KAAQ5B,OAAO;gBAAEyB,eAAe,EAAEzB,OAAO,CAAC6B,MAAM;gBAAEC,eAAe,EAAE,CAAC;gBAAEV,KAAK,EAAEf,UAAU,CAACI,GAAG,CAAC;gBAAEY,UAAU,EAAEhB,UAAU,CAACI,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC,GAAGT,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG,GAAG;gBAAEC,mBAAmB,EAAExB,OAAO,CAACwB,mBAAmB,KAAKtB,SAAS,GAAGF,OAAO,CAACwB,mBAAmB,GAAGxB,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,CAACiB,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACrB,WAAW,KAAKiD,QAAQ,CAAC/B,OAAO,CAACI,cAAc,CAAC,CAAC,CAACpC;cAAE,EAAE;cAC1X,OAAO2D,CAAC,CAACK,MAAM;cACfpD,QAAQ,CAACuC,IAAI,CAACQ,CAAC,CAAC;YACpB,CAAC,MAAM;cACH/C,QAAQ,CAACuC,IAAI,CACT;gBACInD,EAAE,EAAEgC,OAAO,CAAC1B,UAAU,CAACN,EAAE;gBACzBsC,QAAQ,EAAEN,OAAO,CAACM,QAAQ;gBAC1Bc,KAAK,EAAEf,UAAU,CAACI,GAAG,CAAC;gBACtBY,UAAU,EAAEhB,UAAU,CAACI,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC,GAAGT,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG,GAAG;gBAC5E1C,WAAW,EAAEA,WAAW;gBACxB0B,WAAW,EAAEzB,WAAW;gBACxB4B,SAAS,EAAEA,SAAS;gBACpB3B,KAAK,EAAEA,KAAK;gBACZ2C,GAAG,EAAE1B,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG,GAAG;gBACjCU,GAAG,EAAE5B,UAAU,CAACO,GAAG;cACvB,CACJ,CAAC;YACL;UACJ;QACJ,CAAC,CAAC;QACF,IAAIhC,QAAQ,CAACM,MAAM,GAAG,CAAC,EAAE;UACrB,IAAI+C,GAAG,GAAG,CAAC;UACXrD,QAAQ,CAACmB,OAAO,CAACC,OAAO,IAAI;YACxB,IAAIA,OAAO,CAACiC,GAAG,KAAK/B,SAAS,EAAE;cAC3B+B,GAAG,IAAIjC,OAAO,CAACiC,GAAG;YACtB;UACJ,CAAC,CAAC;UACF,IAAIA,GAAG,KAAK,CAAC,EAAE;YACXA,GAAG,GAAGA,GAAG,GAAG,IAAI;UACpB;UACA,IAAIC,IAAI,GAAG,EAAE;UACb,IAAIC,iBAAiB,GAAG,CAAC;UACzB;UACA;UACA,IAAI,IAAI,CAACnD,KAAK,CAACoD,YAAY,CAACC,KAAK,KAAKnC,SAAS,EAAE;YAC7CgC,IAAI,GAAG;cACHI,mBAAmB,EAAE,IAAI,CAACtD,KAAK,CAACoD,YAAY,CAACG,SAAS;cACtDC,MAAM,EAAE,IAAI,CAACxD,KAAK,CAACoD,YAAY,CAACC,KAAK;cACrCI,IAAI,EAAE,IAAI,CAACzD,KAAK,CAACoD,YAAY,CAACK,IAAI;cAClCC,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAACoD,YAAY,CAACO,IAAI;cAC1CC,aAAa,EAAE,EAAE;cACjBxB,KAAK,EAAEf,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;cACvFC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;cACrBC,UAAU,EAAE,IAAI,CAAChE,KAAK,CAACc,QAAQ;cAC/BmD,YAAY,EAAE,IAAI,CAACjE,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACC,YAAY;cACxDC,QAAQ,EAAEzE,QAAQ;cAClB0E,QAAQ,EAAE,IAAI,CAACtE,KAAK,CAACuE,YAAY;cACjCC,QAAQ,EAAE,IAAI,CAACxE,KAAK,CAACyE;YACzB,CAAC;UACL,CAAC,MAAM;YACH;YACA,IAAI,IAAI,CAACzE,KAAK,CAACkE,OAAO,CAACE,YAAY,KAAKlD,SAAS,EAAE;cAC/C,IAAI+B,GAAG,KAAK,CAAC,EAAE;gBACXA,GAAG,GAAG5B,UAAU,CAAC4B,GAAG,CAACY,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;cAC5D;cACAa,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3E,KAAK,CAACoD,YAAY,CAAC;cACpCD,iBAAiB,GAAGJ,QAAQ,CAACzC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,GAAGwC,QAAQ,CAACzC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC;cACzI2C,IAAI,GAAG;gBACHI,mBAAmB,EAAE,IAAI,CAACtD,KAAK,CAACoD,YAAY,CAACG,SAAS;gBACtDC,MAAM,EAAE,IAAI,CAACxD,KAAK,CAACoD,YAAY,CAACC,KAAK;gBACrCI,IAAI,EAAE,IAAI,CAACzD,KAAK,CAACoD,YAAY,CAACK,IAAI;gBAClCC,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAACoD,YAAY,CAACO,IAAI,KAAKzC,SAAS,GAAG,IAAI,CAAClB,KAAK,CAACoD,YAAY,CAACO,IAAI,GAAI,IAAI,CAAC3D,KAAK,CAAC2D,IAAI,KAAK,IAAI,GAAG,IAAII,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAAC2D,IAAI,CAAC,GAAG,IAAK;gBACvJC,aAAa,EAAE,EAAE;gBACjBxB,KAAK,EAAEf,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvFC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBACrBE,YAAY,EAAE,IAAI,CAACjE,KAAK,CAAC4E,qBAAqB,KAAK,IAAI,GAAG,IAAI,CAAC5E,KAAK,CAAC4E,qBAAqB,CAACC,IAAI,GAAG,IAAI,CAAC7E,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACC,YAAY;gBAC5IJ,UAAU,EAAE,IAAI,CAAChE,KAAK,CAACkE,OAAO;gBAC9BG,QAAQ,EAAEzE,QAAQ;gBAClBqD,GAAG,EAAEA,GAAG;gBACRqB,QAAQ,EAAE,IAAI,CAACtE,KAAK,CAACuE,YAAY;gBACjCC,QAAQ,EAAE,IAAI,CAACxE,KAAK,CAACyE,OAAO;gBAC5BtB,iBAAiB,EAAEA,iBAAiB,GAAGA,iBAAiB,GAAG;cAC/D,CAAC;YACL,CAAC,MAAM;cACH;cACA,IAAIF,GAAG,KAAK,CAAC,EAAE;gBACXA,GAAG,GAAG5B,UAAU,CAAC4B,GAAG,CAACY,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;cAC5D;cACA,IAAI9B,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAI,CAACuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;gBACpI,IAAIwB,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,WAAW,EAAE;kBAC/D2C,IAAI,GAAG;oBACHc,UAAU,EAAE,IAAI,CAAChE,KAAK,CAACkE,OAAO,CAACY,SAAS;oBACxCC,IAAI,EAAE,YAAY;oBAClBC,YAAY,EAAE,IAAIjB,IAAI,CAAC,CAAC;oBACxBT,mBAAmB,EAAE,IAAI,CAACtD,KAAK,CAACoD,YAAY,CAACG,SAAS;oBACtDE,IAAI,EAAE,IAAI,CAACzD,KAAK,CAACoD,YAAY,CAACK,IAAI;oBAClCwB,IAAI,EAAE,IAAI,CAACjF,KAAK,CAACyE,OAAO,GAAG,IAAI,CAACzE,KAAK,CAACuE,YAAY,GAAG,EAAE;oBACvDb,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAAC2D,IAAI,KAAK,IAAI,GAAG,IAAII,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAAC2D,IAAI,CAAC,GAAG,IAAI;oBACzEuB,OAAO,EAAEtF,QAAQ;oBACjBwC,KAAK,EAAEf,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACvFxB,UAAU,EAAEhB,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACmF,MAAM,CAACtB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;kBAChG,CAAC;gBACL,CAAC,MAAM;kBACHV,iBAAiB,GAAGJ,QAAQ,CAACzC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,GAAGwC,QAAQ,CAACzC,cAAc,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC;kBACzI2C,IAAI,GAAG;oBACHI,mBAAmB,EAAE,IAAI,CAACtD,KAAK,CAACoD,YAAY,CAACG,SAAS;oBACtDC,MAAM,EAAE,IAAI,CAACxD,KAAK,CAACoD,YAAY,CAACC,KAAK;oBACrCI,IAAI,EAAE,IAAI,CAACzD,KAAK,CAACoD,YAAY,CAACK,IAAI;oBAClCC,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAAC2D,IAAI,KAAK,IAAI,GAAG,IAAII,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAAC2D,IAAI,CAAC,GAAG,IAAI;oBACzEC,aAAa,EAAE,EAAE;oBACjBxB,KAAK,EAAEf,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACvFC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;oBACrBE,YAAY,EAAE,IAAI,CAACjE,KAAK,CAACkE,OAAO,CAACE,YAAY;oBAC7CJ,UAAU,EAAE,IAAI,CAAChE,KAAK,CAACkE,OAAO,CAACY,SAAS;oBACxCT,QAAQ,EAAEzE,QAAQ;oBAClBqD,GAAG,EAAEA,GAAG;oBACRqB,QAAQ,EAAE,IAAI,CAACtE,KAAK,CAACuE,YAAY;oBACjCC,QAAQ,EAAE,IAAI,CAACxE,KAAK,CAACyE,OAAO;oBAC5BtB,iBAAiB,EAAEA,iBAAiB,GAAGA,iBAAiB,GAAG;kBAC/D,CAAC;gBACL;cACJ,CAAC,MAAM;gBACH,IAAI,IAAI,CAACnD,KAAK,CAAC4E,qBAAqB,EAAE;kBAClC1B,IAAI,GAAG;oBACH6B,IAAI,EAAE,YAAY;oBAClBzB,mBAAmB,EAAElD,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC6E,IAAI;oBAC3E3B,IAAI,EAAE,IAAI,CAACzD,KAAK,CAACsC,OAAO,oBAAA+C,MAAA,CAAoB,IAAI,CAACrF,KAAK,CAACoD,YAAY,CAACK,IAAI,IAAK,IAAI,CAACzD,KAAK,CAACoD,YAAY,CAACK,IAAI;oBACzGC,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAAC2D,IAAI,KAAK,IAAI,GAAG,IAAII,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAAC2D,IAAI,CAAC,GAAG,IAAI;oBACzEC,aAAa,EAAE,EAAE;oBACjBxB,KAAK,EAAEf,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACvFxB,UAAU,EAAEhB,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACmF,MAAM,CAACtB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC7FC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;oBACrBuB,WAAW,EAAElF,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;oBACpE2E,OAAO,EAAEtF,QAAQ;oBACjB0E,QAAQ,EAAE,IAAI,CAACtE,KAAK,CAACuE,YAAY;oBACjCC,QAAQ,EAAE,IAAI,CAACxE,KAAK,CAACyE,OAAO;oBAC5BR,YAAY,EAAE,IAAI,CAACjE,KAAK,CAAC4E,qBAAqB,CAACC;kBACnD,CAAC;gBACL,CAAC,MAAM;kBACH,IAAI,CAACnF,QAAQ,CAAC;oBAAEC,QAAQ,EAAE;kBAAM,CAAC,CAAC;kBAClC,IAAI,CAACa,KAAK,CAACC,IAAI,CAAC;oBAAEC,QAAQ,EAAE,OAAO;oBAAEC,OAAO,EAAE,cAAc;oBAAEC,MAAM,EAAE,gEAAgE;oBAAEC,IAAI,EAAE;kBAAK,CAAC,CAAC;gBACzJ;cACJ;YAEJ;UACJ;UACA,IAAIkB,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,KAAKqF,IAAI,CAACe,YAAY,KAAK,IAAI,IAAIf,IAAI,CAACe,YAAY,KAAK/C,SAAS,CAAC,EAAE;YACnJgC,IAAI,CAACe,YAAY,GAAG,EAAE;UAC1B;UACA,IAAIf,IAAI,CAACQ,YAAY,KAAKxC,SAAS,IAAIgC,IAAI,CAACQ,YAAY,KAAK,IAAI,EAAE;YAC/D,IAAI6B,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK5C,MAAM,IAAI,IAAI,CAACqC,KAAK,CAACoD,YAAY,CAACoC,OAAO,KAAK,EAAE,EAAE;cACnF,IAAIC,GAAG,GAAG,aAAa,GAAG,IAAI,CAACzF,KAAK,CAACoD,YAAY,CAACoC,OAAO;cACzD,MAAM7I,UAAU,CAAC,KAAK,EAAE8I,GAAG,EAAEvC,IAAI,CAAC,CAC7BwC,IAAI,CAACC,GAAG,IAAI;gBACTjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;gBACrB,IAAI,CAACnD,KAAK,CAACC,IAAI,CAAC;kBAAEC,QAAQ,EAAE,SAAS;kBAAEC,OAAO,EAAE,QAAQ;kBAAEC,MAAM,EAAE,6CAA6C;kBAAEC,IAAI,EAAE;gBAAK,CAAC,CAAC;gBAC9H0E,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;gBACpCL,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;gBAChCL,YAAY,CAACK,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC5CL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;gBACxC7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC5C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC3CL,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;gBACrCC,UAAU,CAAC,MAAM;kBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGrE,0BAA0B;gBACzD,CAAC,EAAE,IAAI,CAAC;cACZ,CAAC,CAAC,CAACkI,KAAK,CAAEC,CAAC,IAAK;gBACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;gBACd,IAAI,CAACrG,QAAQ,CAAC;kBACVC,QAAQ,EAAE;gBACd,CAAC,CAAC;cACN,CAAC,CAAC;YACV,CAAC,MAAM,IAAIoC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;cAC1I,IAAIH,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAIH,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;gBACrH2C,IAAI,GAAAN,aAAA,CAAAA,aAAA,KAAQM,IAAI;kBAAE8B,YAAY,EAAE5E,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACyE,YAAY;kBAAEgB,MAAM,EAAE5F,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACyF;gBAAM,EAAE;gBAChK,MAAMrJ,UAAU,CAAC,KAAK,+BAAA0I,MAAA,CAA+BjF,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACvB,EAAE,GAAIkE,IAAI,CAAC,CAC5GwC,IAAI,CAAC,MAAMC,GAAG,IAAI;kBACfjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;kBACrB,IAAI,CAACnD,KAAK,CAACC,IAAI,CAAC;oBAAEC,QAAQ,EAAE,SAAS;oBAAEC,OAAO,EAAE,QAAQ;oBAAEC,MAAM,EAAE,+CAA+C;oBAAEC,IAAI,EAAE;kBAAK,CAAC,CAAC;kBAChI0E,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;kBACpCL,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;kBAChCL,YAAY,CAACK,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;kBAC5CL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;kBACxCL,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBACrC7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;kBAC5C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;kBAC3C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;kBAC/C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBAC9C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBAC9C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;kBACjDC,UAAU,CAAC,MAAM;oBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG,IAAI,CAACjC,KAAK,CAACG,IAAI,KAAKpC,YAAY,GAAGE,mCAAmC,GAAI,IAAI,CAAC+B,KAAK,CAACG,IAAI,KAAKtC,KAAK,GAAGC,4BAA4B,GAAGL,gCAAiC;kBACrM,CAAC,EAAE,IAAI,CAAC;gBACZ,CAAC,CAAC,CAACqI,KAAK,CAAEC,CAAC,IAAK;kBACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;kBACd,IAAI,CAACrG,QAAQ,CAAC;oBACVC,QAAQ,EAAE;kBACd,CAAC,CAAC;gBACN,CAAC,CAAC;cACV,CAAC,MAAM;gBACH,MAAMhD,UAAU,CAAC,MAAM,6BAAA0I,MAAA,CAA8BjF,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,CAACsE,IAAI,KAAK3D,SAAS,GAAGd,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,CAACsE,IAAI,GAAGzE,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC0F,eAAe,CAAC,CAAC,CAAC,CAACC,WAAW,GAAKhD,IAAI,CAAC,CACjQwC,IAAI,CAAC,MAAMC,GAAG,IAAI;kBACfjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;kBACrB,IAAI,CAACnD,KAAK,CAACC,IAAI,CAAC;oBAAEC,QAAQ,EAAE,SAAS;oBAAEC,OAAO,EAAE,QAAQ;oBAAEC,MAAM,EAAE,6CAA6C;oBAAEC,IAAI,EAAE;kBAAK,CAAC,CAAC;kBAC9H0E,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;kBACpCL,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;kBAChCL,YAAY,CAACK,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;kBAC5CL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;kBACxCL,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBACrC7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;kBAC5C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;kBAC3C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBAC9C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;kBACjD,IAAI,IAAI,CAAC5F,KAAK,CAACG,IAAI,KAAK3C,SAAS,IAAI,IAAI,CAACwC,KAAK,CAACyE,OAAO,EAAE;oBACrD,IAAI0B,QAAQ,GAAG,IAAI,CAACnG,KAAK,CAACkE,OAAO,CAACY,SAAS,CAACX,UAAU,CAAC/E,KAAK;oBAC5D,MAAMzC,UAAU,CAAC,KAAK,oCAAA0I,MAAA,CAAoCM,GAAG,CAAChC,IAAI,CAACyC,MAAM,CAACpH,EAAE,iBAAAqG,MAAA,CAAcc,QAAQ,qCAAkC,CAAC,CAChIT,IAAI,CAAEC,GAAG,IAAK;sBAAA,IAAAU,WAAA;sBACX3B,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;sBACrB,CAAA0C,WAAA,OAAI,CAAC7F,KAAK,cAAA6F,WAAA,uBAAVA,WAAA,CAAY5F,IAAI,CAAC;wBACbC,QAAQ,EAAE,SAAS;wBACnBC,OAAO,EAAE,UAAU;wBACnBC,MAAM,EAAE,sCAAsC;wBAC9CC,IAAI,EAAE;sBACV,CAAC,CAAC;sBACFgF,UAAU,CAAC,MAAM;wBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGxE,gCAAgC;sBAC/D,CAAC,EAAE,IAAI,CAAC;oBACZ,CAAC,CAAC,CACDqI,KAAK,CAAEC,CAAC,IAAK;sBAAA,IAAAO,YAAA,EAAAC,WAAA,EAAAC,YAAA;sBACV9B,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;sBACd,CAAAO,YAAA,OAAI,CAAC9F,KAAK,cAAA8F,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,CAAC;wBACbC,QAAQ,EAAE,OAAO;wBACjBC,OAAO,EAAE,iBAAiB;wBAC1BC,MAAM,iEAAAyE,MAAA,CAA8D,EAAAkB,WAAA,GAAAR,CAAC,CAACU,QAAQ,cAAAF,WAAA,uBAAVA,WAAA,CAAY5C,IAAI,MAAKzC,SAAS,IAAAsF,YAAA,GAAGT,CAAC,CAACU,QAAQ,cAAAD,YAAA,uBAAVA,YAAA,CAAY7C,IAAI,GAAGoC,CAAC,CAACW,OAAO,CAAE;wBACnI7F,IAAI,EAAE;sBACV,CAAC,CAAC;oBACN,CAAC,CAAC;kBACV;kBACAgF,UAAU,CAAC,MAAM;oBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG,IAAI,CAACjC,KAAK,CAACG,IAAI,KAAKpC,YAAY,GAAGE,mCAAmC,GAAI,IAAI,CAAC+B,KAAK,CAACG,IAAI,KAAKtC,KAAK,GAAGC,4BAA4B,GAAGL,gCAAiC;kBACrM,CAAC,EAAE,IAAI,CAAC;gBACZ,CAAC,CAAC,CAACqI,KAAK,CAAEC,CAAC,IAAK;kBACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;kBACd,IAAI,CAACrG,QAAQ,CAAC;oBACVC,QAAQ,EAAE;kBACd,CAAC,CAAC;gBACN,CAAC,CAAC;cACV;YACJ,CAAC,MAAM;cACH,IAAIoC,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,WAAW,EAAE;gBAC/D,IAAIoG,EAAE,MAAAtB,MAAA,CAAM,IAAI,CAACrF,KAAK,CAACkE,OAAO,CAACY,SAAS,CAAC8B,YAAY,CAACC,WAAW,CAACzH,KAAK,OAAAiG,MAAA,CAAI,IAAI,CAACrF,KAAK,CAAC8G,UAAU,CAAE;gBAClGpC,OAAO,CAACC,GAAG,CAACgC,EAAE,CAAC;gBACf,MAAMhK,UAAU,CAAC,MAAM,6BAAA0I,MAAA,CAA6B,IAAI,CAACrF,KAAK,CAACG,IAAI,KAAK9B,IAAI,GAAG+B,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC0F,eAAe,CAAC,CAAC,CAAC,CAACC,WAAW,GAAG9F,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC4D,UAAU,CAACW,SAAS,CAAC8B,YAAY,CAACC,WAAW,CAACE,KAAK,CAAC,CAAC,CAAC,CAACd,eAAe,CAAC,CAAC,CAAC,CAACC,WAAW,GAAIhD,IAAI,CAAC,CAC3RwC,IAAI,CAAC,MAAMC,GAAG,IAAI;kBAAA,IAAAqB,YAAA;kBACftC,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;kBACrB,CAAAqD,YAAA,OAAI,CAACxG,KAAK,cAAAwG,YAAA,uBAAVA,YAAA,CAAYvG,IAAI,CAAC;oBAAEC,QAAQ,EAAE,SAAS;oBAAEC,OAAO,EAAE,QAAQ;oBAAEC,MAAM,EAAE,4CAA4C;oBAAEC,IAAI,EAAE;kBAAK,CAAC,CAAC;kBAC9H0E,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;kBACpCL,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;kBAChCL,YAAY,CAACK,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;kBAC5CL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;kBACxC7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;kBAC5C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;kBAC3C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;kBACjDL,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBACrC,MAAMjJ,UAAU,CAAC,KAAK,oCAAA0I,MAAA,CAAoCM,GAAG,CAAChC,IAAI,CAACyC,MAAM,CAACpH,EAAE,iBAAAqG,MAAA,CAAc,IAAI,CAACrF,KAAK,CAACuE,YAAY,kBAAAc,MAAA,CAAe,IAAI,CAACrF,KAAK,CAACiH,OAAO,OAAA5B,MAAA,CAAI,IAAI,CAACrF,KAAK,CAACkE,OAAO,CAACY,SAAS,CAACX,UAAU,CAAClF,SAAS,WAAAoG,MAAA,CAASsB,EAAE,CAAE,CAAC,CAChNjB,IAAI,CAAEC,GAAG,IAAK;oBAAA,IAAAuB,YAAA;oBACXxC,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;oBACrB,CAAAuD,YAAA,OAAI,CAAC1G,KAAK,cAAA0G,YAAA,uBAAVA,YAAA,CAAYzG,IAAI,CAAC;sBACbC,QAAQ,EAAE,SAAS;sBACnBC,OAAO,EAAE,UAAU;sBACnBC,MAAM,EAAE,sCAAsC;sBAC9CC,IAAI,EAAE;oBACV,CAAC,CAAC;oBACFgF,UAAU,CAAC,MAAM;sBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG7D,mBAAmB;oBAClD,CAAC,EAAE,IAAI,CAAC;kBACZ,CAAC,CAAC,CACD0H,KAAK,CAAEC,CAAC,IAAK;oBAAA,IAAAoB,YAAA,EAAAC,YAAA,EAAAC,YAAA;oBACV3C,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;oBACd,CAAAoB,YAAA,OAAI,CAAC3G,KAAK,cAAA2G,YAAA,uBAAVA,YAAA,CAAY1G,IAAI,CAAC;sBACbC,QAAQ,EAAE,OAAO;sBACjBC,OAAO,EAAE,iBAAiB;sBAC1BC,MAAM,iEAAAyE,MAAA,CAA8D,EAAA+B,YAAA,GAAArB,CAAC,CAACU,QAAQ,cAAAW,YAAA,uBAAVA,YAAA,CAAYzD,IAAI,MAAKzC,SAAS,IAAAmG,YAAA,GAAGtB,CAAC,CAACU,QAAQ,cAAAY,YAAA,uBAAVA,YAAA,CAAY1D,IAAI,GAAGoC,CAAC,CAACW,OAAO,CAAE;sBACnI7F,IAAI,EAAE;oBACV,CAAC,CAAC;kBACN,CAAC,CAAC;gBACV,CAAC,CAAC,CAACiF,KAAK,CAAEC,CAAC,IAAK;kBAAA,IAAAuB,YAAA,EAAAC,YAAA,EAAAC,YAAA;kBACZ9C,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;kBACd,CAAAuB,YAAA,OAAI,CAAC9G,KAAK,cAAA8G,YAAA,uBAAVA,YAAA,CAAY7G,IAAI,CAAC;oBAAEC,QAAQ,EAAE,OAAO;oBAAEC,OAAO,EAAE,iBAAiB;oBAAEC,MAAM,yEAAAyE,MAAA,CAAsE,EAAAkC,YAAA,GAAAxB,CAAC,CAACU,QAAQ,cAAAc,YAAA,uBAAVA,YAAA,CAAY5D,IAAI,MAAKzC,SAAS,IAAAsG,YAAA,GAAGzB,CAAC,CAACU,QAAQ,cAAAe,YAAA,uBAAVA,YAAA,CAAY7D,IAAI,GAAGoC,CAAC,CAACW,OAAO,CAAE;oBAAE7F,IAAI,EAAE;kBAAK,CAAC,CAAC;gBAChO,CAAC,CAAC;cACV,CAAC,MAAM;gBACH,MAAMlE,UAAU,CAAC,MAAM,EAAE,SAAS,EAAEuG,IAAI,CAAC,CACpCwC,IAAI,CAACC,GAAG,IAAI;kBACTjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;kBACrB,IAAI,CAACnD,KAAK,CAACC,IAAI,CAAC;oBAAEC,QAAQ,EAAE,SAAS;oBAAEC,OAAO,EAAE,QAAQ;oBAAEC,MAAM,EAAE,6CAA6C;oBAAEC,IAAI,EAAE;kBAAK,CAAC,CAAC;kBAC9H0E,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;kBACpCL,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;kBAChCL,YAAY,CAACK,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;kBAC5CL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;kBACxC7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;kBAC5C7D,MAAM,CAACzB,cAAc,CAACsF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;kBAC3CL,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;kBACrC,IAAIzF,IAAI,GAAGoF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC;kBACvC,IAAIJ,IAAI,KAAKxC,MAAM,EAAE;oBACjBkI,UAAU,CAAC,MAAM;sBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGrE,0BAA0B;oBACzD,CAAC,EAAE,IAAI,CAAC;kBACZ,CAAC,MAAM,IAAIuC,IAAI,KAAK3C,SAAS,EAAE;oBAC3BqI,UAAU,CAAC,MAAM;sBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGvE,yBAAyB;oBACxD,CAAC,EAAE,IAAI,CAAC;kBACZ,CAAC,MAAM,IAAIyC,IAAI,KAAKjC,GAAG,EAAE;oBACrB2H,UAAU,CAAC,MAAM;sBACb9D,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG9D,0BAA0B;oBACzD,CAAC,EAAE,IAAI,CAAC;kBACZ;gBACJ,CAAC,CAAC,CAAC2H,KAAK,CAAEC,CAAC,IAAK;kBACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;kBACd,IAAI,CAACrG,QAAQ,CAAC;oBACVC,QAAQ,EAAE;kBACd,CAAC,CAAC;gBACN,CAAC,CAAC;cACV;YACJ;UACJ,CAAC,MAAM;YACH,IAAI,CAACD,QAAQ,CAAC;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC;YAClC,IAAI,CAACa,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,cAAc;cAAEC,MAAM,EAAE,4DAA4D;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;UACrJ;QACJ,CAAC,MAAM;UACH,IAAI,CAACnB,QAAQ,CAAC;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC;UAClC,IAAI,CAACa,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,cAAc;YAAEC,MAAM,EAAE,sDAAsD;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/I;MACJ;IAEJ,CAAC;IA31CG,IAAI,CAACb,KAAK,GAAG;MACToG,MAAM,EAAE,IAAI,CAACrH,WAAW;MACxBmF,OAAO,EAAE,IAAI;MACbpD,QAAQ,EAAE,IAAI;MACd2G,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZhE,IAAI,EAAE,IAAI;MACVP,YAAY,EAAE,IAAI;MAClBgB,YAAY,EAAE,IAAI;MAClBwD,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBjD,qBAAqB,EAAE,IAAI;MAC3BkD,0BAA0B,EAAE,IAAI;MAChCC,gBAAgB,EAAE,IAAI;MACtBpI,QAAQ,EAAE,KAAK;MACf8E,OAAO,EAAE,KAAK;MACdnC,OAAO,EAAE,KAAK;MACd0F,OAAO,EAAE,IAAI;MACbC,kBAAkB,EAAE,KAAK;MACzBC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,IAAI;MAChBnI,eAAe,EAAE,EAAE;MACnBsE,YAAY,EAAE,EAAE;MAChBuC,UAAU,EAAE,EAAE;MACduB,eAAe,EAAE,EAAE;MACnBpB,OAAO,EAAE,qCAAqC;MAC9CqB,UAAU,EAAE,gDAAgD;MAC5DC,gBAAgB,EAAE,QAAQ;MAC1BC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,oBAAoB,EAAE,EAAE;MACxBvI,IAAI,EAAEoF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC;MAClC6B,KAAK,EAAE,CAAC;MACR+C,MAAM,EAAE,CAAC;MACTwD,GAAG,EAAE;IACT,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAAC;MAAExD,IAAI,EAAE,sBAAsB;MAAEsC,KAAK,EAAE;IAAoB,CAAC,EAAE;MAAEtC,IAAI,EAAE,mBAAmB;MAAEsC,KAAK,EAAE;IAAgB,CAAC,EAAE;MAAEtC,IAAI,EAAE,gBAAgB;MAAEsC,KAAK,EAAE;IAAgB,CAAC,CAAC;IACxL,IAAI,CAACmB,OAAO,GAAG,IAAI9E,IAAI,CAAC,CAAC;IACzB,IAAI,CAAC+E,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACD,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACF,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACH,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACI,eAAe,GAAG,IAAI,CAACA,eAAe,CAACJ,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACL,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACM,eAAe,GAAG,IAAI,CAACA,eAAe,CAACN,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACP,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACQ,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACR,IAAI,CAAC,IAAI,CAAC;IACxE,IAAI,CAACS,6BAA6B,GAAG,IAAI,CAACA,6BAA6B,CAACT,IAAI,CAAC,IAAI,CAAC;IAClF,IAAI,CAACU,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACV,IAAI,CAAC,IAAI,CAAC;IACxE,IAAI,CAACW,eAAe,GAAG,IAAI,CAACA,eAAe,CAACX,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACY,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACZ,IAAI,CAAC,IAAI,CAAC;IAChE,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACb,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACc,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACd,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACe,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACf,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACgB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACiB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACjB,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACA,YAAY,CAAClB,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACmB,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC,IAAI,CAAC;IAC/D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACpB,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACqB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACrB,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACsB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACtB,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACuB,eAAe,GAAG,IAAI,CAACA,eAAe,CAACvB,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACwB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACxB,IAAI,CAAC,IAAI,CAAC;IAC5C;IACA;IACA,IAAI,CAACyB,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACzB,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAAC0B,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC1B,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAAC2B,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC3B,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC5B,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAAC6B,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC7B,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAAC8B,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC9B,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAAC+B,6BAA6B,GAAG,IAAI,CAACA,6BAA6B,CAAC/B,IAAI,CAAC,IAAI,CAAC;IAClF,IAAI,CAACgC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAChC,IAAI,CAAC,IAAI,CAAC;IAC1E,IAAI,CAACiC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACjC,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACkC,eAAe,GAAG,IAAI,CAACA,eAAe,CAAClC,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACU,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACnC,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACoC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACqC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACrC,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACsC,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACtC,IAAI,CAAC,IAAI,CAAC;IAChE,IAAI,CAACuC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACvC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACwC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACxC,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACyC,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACzC,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAAC0C,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC1C,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAAC2C,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC3C,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAAC4C,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC5C,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAAC6C,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC7C,IAAI,CAAC,IAAI,CAAC;EAC5E;EACA;EACA,MAAM8C,iBAAiBA,CAAA,EAAG;IACtB,IAAIxI,SAAS,GAAG,EAAE;IAClB,IAAI0B,IAAI,GAAG,EAAE;IACb,IAAI+G,OAAO,GAAG,EAAE;IAChB;IACA;IACA,IAAI,IAAI,CAAClN,KAAK,CAACsE,YAAY,KAAK,IAAI,IAAI,IAAI,CAACtE,KAAK,CAACsE,YAAY,KAAKlC,SAAS,IAAIa,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,wBAAwB,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,iBAAiB,IAAI,CAAC7B,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;MACjP;MACA,IAAI,IAAI,CAACzB,KAAK,CAACsE,YAAY,CAACO,IAAI,KAAKzC,SAAS,EAAE;QAC5C,MAAMvE,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,CACpC+I,IAAI,CAACC,GAAG,IAAI;UACTjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;UACrBgC,GAAG,CAAChC,IAAI,CAAC5C,OAAO,CAACC,OAAO,IAAI;YACxB,IAAI2B,CAAC,GAAG;cAAEyC,IAAI,EAAEpE,OAAO,CAACiL,WAAW;cAAEpH,IAAI,EAAE7D,OAAO,CAACiL;YAAY,CAAC;YAChED,OAAO,CAAC7J,IAAI,CAACQ,CAAC,CAAC;UACnB,CAAC,CAAC;UACF,IAAI,CAACjD,QAAQ,CAAC;YACV0E,YAAY,EAAE4H;UAClB,CAAC,CAAC;QACN,CAAC,CAAC,CAAClG,KAAK,CAAEC,CAAC,IAAK;UACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;QAClB,CAAC,CAAC;QACN,IAAI,CAACjH,KAAK,CAACsE,YAAY,CAACO,IAAI,GAAG,IAAII,IAAI,CAAC,IAAI,CAACjF,KAAK,CAACsE,YAAY,CAACO,IAAI,CAAC;QACrE,IAAIP,YAAY,GAAG;UACfO,IAAI,EAAE,IAAI,CAAC7E,KAAK,CAACsE,YAAY,CAACO,IAAI;UAClC1E,SAAS,EAAE,IAAI,CAACH,KAAK,CAACsE,YAAY,CAACnE,SAAS;UAC5CuG,OAAO,EAAE,IAAI,CAAC1G,KAAK,CAACsE,YAAY,CAACoC,OAAO;UACxC/B,IAAI,EAAE,IAAI,CAAC3E,KAAK,CAACsE,YAAY,CAACK,IAAI;UAClCJ,KAAK,EAAE,IAAI,CAACvE,KAAK,CAACsE,YAAY,CAACC,KAAK;UACpCE,SAAS,EAAE,IAAI,CAACzE,KAAK,CAACoF,OAAO,CAACC,UAAU,CAACjF;QAC7C,CAAC;QACD+F,IAAI,GAAG,IAAI,CAACnG,KAAK,CAACoF,OAAO,CAACC,UAAU,CAAC/E,KAAK;QAC1CmE,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACoF,OAAO,CAACC,UAAU,CAACjF,OAAO;QACjD,IAAI,CAACQ,QAAQ,CAAC;UACVwE,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACoF,OAAO;UAC3BpD,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACgC,QAAQ;UAC7BsC,YAAY,EAAEA;QAClB,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACAA,YAAY,GAAG;UACXG,SAAS,EAAE,IAAI,CAACzE,KAAK,CAACsE,YAAY,CAAClE,OAAO;UAC1CuE,IAAI,EAAE,EAAE;UACRJ,KAAK,EAAE;QACX,CAAC;QACD4B,IAAI,GAAG,IAAI,CAACnG,KAAK,CAACsE,YAAY,CAAChE,KAAK;QACpCmE,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACsE,YAAY,CAAClE,OAAO;QAC3C,IAAI,CAACQ,QAAQ,CAAC;UACVwE,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACsE,YAAY;UAChCtC,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACgC,QAAQ;UAC7BsC,YAAY,EAAEA;QAClB,CAAC,CAAC;MACN;IACJ,CAAC,MAAM,IAAIrB,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;MACjJ,IAAI,CAAC,IAAI,CAACP,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,KAAK,OAAQuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAE,KAAK,QAAQ,EAAE;QAC5I,MAAM5D,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC+I,IAAI,CAAEC,GAAG,IAAK;UACX,IAAIO,WAAW,GAAG,IAAI;UACtB,IAAIhE,IAAI,GAAGyD,GAAG,CAAChC,IAAI,CAACzB,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACnC,EAAE,KAAKoB,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;UAC3F2F,WAAW,GAAGhE,IAAI,KAAKhB,SAAS,GAAG;YAAEkE,IAAI,EAAElD,IAAI,CAACgK,aAAa;YAAErH,IAAI,EAAE3C,IAAI,CAAClD;UAAG,CAAC,GAAG,IAAI;UACrF,IAAIkH,WAAW,KAAK,IAAI,EAAE;YACtB9F,IAAI,CAACC,KAAK,CAACC,cAAc,CAACsF,OAAO,CAAC,aAAa,EAAExF,IAAI,CAAC+L,SAAS,CAACjG,WAAW,CAAC,CAAC,CAAC;UAClF;QACJ,CAAC,CAAC,CACDJ,KAAK,CAAEC,CAAC,IAAK;UACVrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;QAClB,CAAC,CAAC;MACV;MACA,MAAMpJ,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,CACpC+I,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAAChC,IAAI,CAAC5C,OAAO,CAACC,OAAO,IAAI;UACxB,IAAI2B,CAAC,GAAG;YAAEyC,IAAI,EAAEpE,OAAO,CAACiL,WAAW;YAAEpH,IAAI,EAAE7D,OAAO,CAACiL;UAAY,CAAC;UAChED,OAAO,CAAC7J,IAAI,CAACQ,CAAC,CAAC;QACnB,CAAC,CAAC;QACF,IAAI,CAACjD,QAAQ,CAAC;UACV0E,YAAY,EAAE4H;QAClB,CAAC,CAAC;QACF,IAAI,IAAI,CAAClN,KAAK,CAACsE,YAAY,CAACgJ,mBAAmB,CAAClM,MAAM,GAAG,CAAC,EAAE;UAAA,IAAAmM,qBAAA;UACxD,CAAAA,qBAAA,OAAI,CAACvN,KAAK,CAACsE,YAAY,CAACgJ,mBAAmB,CAAC,CAAC,CAAC,CAACE,gBAAgB,cAAAD,qBAAA,uBAA/DA,qBAAA,CAAiEE,GAAG,CAACpL,EAAE,IAAIA,EAAE,CAACqL,iBAAiB,GAAG7G,GAAG,CAAChC,IAAI,CAACzB,IAAI,CAACuK,GAAG,IAAIA,GAAG,CAACzN,EAAE,KAAK+D,QAAQ,CAAC5B,EAAE,CAACuL,eAAe,CAAC,CAAC,CAACT,WAAW,CAAC;UAC5K,IAAI,CAACvM,QAAQ,CAAC;YACV2I,eAAe,EAAE,IAAI,CAACvJ,KAAK,CAACsE,YAAY,CAACgJ,mBAAmB,CAAC,CAAC,CAAC,CAACE,gBAAgB,CAAC;UACrF,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,CAACxG,KAAK,CAAEC,CAAC,IAAK;QACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;MAClB,CAAC,CAAC;MACN;MACA3C,YAAY,GAAG;QACXO,IAAI,EAAE,EAAE;QACR1E,SAAS,EAAE,IAAI,CAACH,KAAK,CAACsE,YAAY,CAACe,UAAU,CAAClF,SAAS;QACvDuG,OAAO,EAAE,EAAE;QACXjC,SAAS,EAAE,IAAI,CAACzE,KAAK,CAACsE,YAAY,CAACe,UAAU,CAACjF,OAAO;QACrDuE,IAAI,EAAE,IAAI,CAAC3E,KAAK,CAACsE,YAAY,CAACK,IAAI,GAAG,IAAI,CAAC3E,KAAK,CAACsE,YAAY,CAACK,IAAI,GAAG,EAAE;QACtEJ,KAAK,EAAE;MACX,CAAC;MACD4B,IAAI,GAAG,IAAI,CAACnG,KAAK,CAACsE,YAAY,CAACe,UAAU,CAAC/E,KAAK;MAC/CmE,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACsE,YAAY,CAACe,UAAU,CAACjF,OAAO;MACtD,IAAIU,QAAQ,GAAG,EAAE;MACjB,IAAI,CAACd,KAAK,CAACgC,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;QAAA,IAAA2L,qBAAA;QACnC,IAAIC,QAAQ,IAAAD,qBAAA,GAAG3L,OAAO,CAAC0H,oBAAoB,cAAAiE,qBAAA,uBAA5BA,qBAAA,CAA8BzK,IAAI,CAACf,EAAE,IAAI,CAACA,EAAE,CAAC0L,SAAS,GAAG9J,QAAQ,CAAC5B,EAAE,CAAC2L,UAAU,CAAC,KAAK9L,OAAO,CAACM,QAAQ,GAAGJ,SAAS,CAAC;QACjI,IAAIyB,CAAC,GAAG,CAAC,CAAC;QACV,IAAIiK,QAAQ,EAAE;UACVjK,CAAC,GAAG;YACAoK,QAAQ,EAAE/L,OAAO,CAAC+L,QAAQ;YAC1BC,UAAU,EAAEhM,OAAO,CAACgM,UAAU;YAC9BrC,eAAe,EAAE3J,OAAO,CAAC2J,eAAe;YACxCjC,oBAAoB,EAAE1H,OAAO,CAAC0H,oBAAoB;YAClDuE,aAAa,EAAEjM,OAAO,CAACiM,aAAa;YACpCjO,EAAE,EAAEgC,OAAO,CAAChC,EAAE;YACdM,UAAU,EAAE0B,OAAO,CAACkM,SAAS,KAAKhM,SAAS,GAAGF,OAAO,CAACkM,SAAS,GAAGlM,OAAO,CAAC1B,UAAU;YACpFgG,WAAW,EAAEtE,OAAO,CAACsE,WAAW;YAChClE,cAAc,EAAEJ,OAAO,CAACI,cAAc;YACtCyB,MAAM,EAAE7B,OAAO,CAAC6B,MAAM;YACtBvB,QAAQ,EAAEN,OAAO,CAACM,QAAQ;YAC1BK,OAAO,EAAEiL,QAAQ,CAACO,OAAO,GAAGnM,OAAO,CAACW,OAAO,GAAGX,OAAO,CAACW,OAAO,GAAGN,UAAU,CAACuL,QAAQ,CAACO,OAAO,CAAC,GAAG,GAAG,GAAGnM,OAAO,CAACW,OAAO,GAAGiL,QAAQ,CAACQ,KAAK;YACrIC,UAAU,EAAErM,OAAO,CAACqM,UAAU;YAC9BrK,MAAM,EAAEhC,OAAO,CAACgC,MAAM;YACtBsK,SAAS,EAAEtM,OAAO,CAACsM,SAAS;YAC5BC,iBAAiB,EAAEX,QAAQ,CAACO,OAAO,GAAGP,QAAQ,CAACO,OAAO,GAAG,CAAC;YAC1DK,aAAa,EAAEZ,QAAQ,CAACQ,KAAK,GAAGR,QAAQ,CAACQ,KAAK,GAAG;UACrD,CAAC;UACD,IAAIK,UAAU,GAAG,EAAE;UACnB,IAAIlI,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YACrCkN,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;YACrD,IAAImN,KAAK,GAAGD,UAAU,CAACE,SAAS,CAAClB,GAAG,IAAI,CAACA,GAAG,CAACnN,UAAU,KAAK4B,SAAS,GAAGuL,GAAG,CAACnN,UAAU,CAACN,EAAE,GAAGyN,GAAG,CAACS,SAAS,CAAClO,EAAE,MAAM2D,CAAC,CAACrD,UAAU,CAACN,EAAE,CAAC;YAClI,IAAI0O,KAAK,KAAK,CAAC,CAAC,EAAE;cACdD,UAAU,CAACC,KAAK,CAAC,GAAG/K,CAAC;cACrB4C,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACsB,UAAU,CAAC,CAAC;YAC5D;UACJ;QACJ,CAAC,MAAM;UACH9K,CAAC,GAAG;YACAoK,QAAQ,EAAE/L,OAAO,CAAC+L,QAAQ;YAC1BC,UAAU,EAAEhM,OAAO,CAACgM,UAAU;YAC9BrC,eAAe,EAAE3J,OAAO,CAAC2J,eAAe;YACxCjC,oBAAoB,EAAE1H,OAAO,CAAC0H,oBAAoB;YAClDuE,aAAa,EAAEjM,OAAO,CAACiM,aAAa;YACpCjO,EAAE,EAAEgC,OAAO,CAAChC,EAAE;YACdM,UAAU,EAAE0B,OAAO,CAACkM,SAAS,KAAKhM,SAAS,GAAGF,OAAO,CAACkM,SAAS,GAAGlM,OAAO,CAAC1B,UAAU;YACpFgG,WAAW,EAAEtE,OAAO,CAACsE,WAAW;YAChClE,cAAc,EAAEJ,OAAO,CAACI,cAAc;YACtCyB,MAAM,EAAE7B,OAAO,CAAC6B,MAAM;YACtBvB,QAAQ,EAAEN,OAAO,CAACM,QAAQ;YAC1BK,OAAO,EAAEX,OAAO,CAACW,OAAO;YACxB0L,UAAU,EAAErM,OAAO,CAACqM,UAAU;YAC9BrK,MAAM,EAAEhC,OAAO,CAACgC,MAAM;YACtBsK,SAAS,EAAEtM,OAAO,CAACsM,SAAS;YAC5BC,iBAAiB,EAAE,CAAC;YACpBC,aAAa,EAAE;UACnB,CAAC;QACL;QAEA5N,QAAQ,CAACuC,IAAI,CAACQ,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACjD,QAAQ,CAAC;QACVwE,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACoF,OAAO;QAC3BpD,QAAQ,EAAElB,QAAQ;QAClBwD,YAAY,EAAEA,YAAY;QAC1BwB,qBAAqB,EAAE,IAAI,CAAC9F,KAAK,CAACsE,YAAY,CAACa,YAAY,GAAG+H,OAAO,CAAC9J,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACiE,IAAI,KAAK,IAAI,CAACtG,KAAK,CAACsE,YAAY,CAACa,YAAY,CAAC,GAAG,IAAI;QACzIN,IAAI,EAAE,IAAI,CAAC7E,KAAK,CAACsE,YAAY,CAACM,YAAY,GAAG,IAAIK,IAAI,CAAC,IAAI,CAACjF,KAAK,CAACsE,YAAY,CAACM,YAAY,CAAC,GAAG;MAClG,CAAC,CAAC;MACF,IAAI,IAAI,CAAC5E,KAAK,CAACsE,YAAY,CAACb,GAAG,EAAE;QAC7B,IAAI,CAACsJ,cAAc,CAAC;UAAEpH,OAAO,EAAE,IAAI,CAAC3F,KAAK,CAACsE,YAAY,CAACb;QAAI,CAAC,CAAC;MACjE;IACJ,CAAC,MAAM;MACH;MACAa,YAAY,GAAG;QACXO,IAAI,EAAE,EAAE;QACR6B,OAAO,EAAE,EAAE;QACXjC,SAAS,EAAE,IAAI,CAACzE,KAAK,CAACoF,OAAO,CAAChF,OAAO,KAAKgC,SAAS,GAAG,IAAI,CAACpC,KAAK,CAACoF,OAAO,CAAChF,OAAO,GAAG,IAAI,CAACJ,KAAK,CAACoF,OAAO,CAACC,UAAU,CAACjF,OAAO;QACxHuE,IAAI,EAAE,EAAE;QACRJ,KAAK,EAAE;MACX,CAAC;MACD4B,IAAI,GAAG,IAAI,CAACnG,KAAK,CAACoF,OAAO,CAAC9E,KAAK;MAC/BmE,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACoF,OAAO,CAAChF,OAAO,KAAKgC,SAAS,GAAG,IAAI,CAACpC,KAAK,CAACoF,OAAO,CAAChF,OAAO,GAAG,IAAI,CAACJ,KAAK,CAACoF,OAAO,CAACC,UAAU,CAACjF,OAAO;MACzH,IAAI,CAACQ,QAAQ,CAAC;QACVwE,OAAO,EAAE,IAAI,CAACpF,KAAK,CAACoF,OAAO;QAC3BpD,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACgC,QAAQ;QAC7BsC,YAAY,EAAEA;MAClB,CAAC,CAAC;IACN;IACA,IAAIrB,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,CAACoC,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;MAAA,IAAAqN,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;MAC9G,IAAIrI,GAAG,GAAG,EAAE;MACZ,IAAI,EAAAmI,sBAAA,OAAI,CAAC9O,KAAK,CAACsE,YAAY,cAAAwK,sBAAA,uBAAvBA,sBAAA,CAAyBzJ,UAAU,MAAKjD,SAAS,IAAI,SAAA2M,sBAAA,GAAQ,IAAI,CAAC/O,KAAK,CAACsE,YAAY,cAAAyK,sBAAA,uBAAvBA,sBAAA,CAAyB1J,UAAU,CAAC,KAAK,QAAQ,EAAE;QAChHsB,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAAC3G,KAAK,CAACsE,YAAY,CAACe,UAAU;MACzE,CAAC,MAAM,IAAI,EAAA2J,qBAAA,OAAI,CAAChP,KAAK,CAACoF,OAAO,CAACC,UAAU,cAAA2J,qBAAA,uBAA7BA,qBAAA,CAA+B9O,EAAE,MAAKkC,SAAS,EAAE;QACxDuE,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAAC3G,KAAK,CAACoF,OAAO,CAACC,UAAU,CAACnF,EAAE;MACvE,CAAC,MAAM,IAAI,IAAI,CAACF,KAAK,CAACoF,OAAO,CAAClF,EAAE,KAAKkC,SAAS,EAAE;QAC5CuE,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAAC3G,KAAK,CAACoF,OAAO,CAAClF,EAAE;MAC5D;MACA;MACA,MAAMrC,UAAU,CAAC,KAAK,EAAE8I,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;QACTjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;QACrB,IAAIJ,SAAS,KAAK,EAAE,EAAE;UAClB,IAAI,CAACuF,MAAM,CAAC3G,IAAI,CAAC;YAAEiD,IAAI,EAAE7B,SAAS;YAAEmE,KAAK,EAAEnE;UAAU,CAAC,CAAC;QAC3D;QACAoC,GAAG,CAAChC,IAAI,CAAC5C,OAAO,CAACC,OAAO,IAAI;UACxB,IAAI2B,CAAC,GAAG,EAAE;UACV,IAAI3B,OAAO,CAAC+M,OAAO,KAAK,IAAI,EAAE;YAC1BpL,CAAC,GAAG;cACAyC,IAAI,EAAEpE,OAAO,CAACgN,UAAU,GAAG,IAAI,GAAGhN,OAAO,CAACiN,OAAO,GAAG,GAAG,GAAGjN,OAAO,CAACkN,SAAS,GAAG,IAAI,GAAGlN,OAAO,CAACmN,QAAQ,GAAG,KAAK,GAAGnN,OAAO,CAAC+M,OAAO;cAAElJ,IAAI,EAAE7D,OAAO,CAAChC;YACnJ,CAAC;UACL,CAAC,MAAM;YACH2D,CAAC,GAAG;cACAyC,IAAI,EAAEpE,OAAO,CAACgN,UAAU,GAAG,IAAI,GAAGhN,OAAO,CAACiN,OAAO,GAAG,GAAG,GAAGjN,OAAO,CAACkN,SAAS,GAAG,IAAI,GAAGlN,OAAO,CAACmN,QAAQ,GAAG,KAAK;cAAEtJ,IAAI,EAAE7D,OAAO,CAAChC;YACjI,CAAC;UACL;UACA,IAAI,CAAC8J,MAAM,CAAC3G,IAAI,CAACQ,CAAC,CAAC;QACvB,CAAC,CAAC;QACF,IAAI,CAACjD,QAAQ,CAAC;UACV6E,YAAY,EAAEU;QAClB,CAAC,CAAC;MACN,CAAC,CAAC,CAACa,KAAK,CAAEC,CAAC,IAAK;QACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA,IAAIhE,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,WAAW,EAAE;MAC/D,MAAM5D,UAAU,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAC5C+I,IAAI,CAACC,GAAG,IAAI;QACTjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAChC,IAAI,CAAC;QACrB,IAAIvE,KAAK,GAAGuG,GAAG,CAAChC,IAAI,CAAC4I,GAAG,CAACpL,EAAE,IAAIA,EAAE,CAAC/B,KAAK,CAAC,CAACgP,IAAI,CAAC,GAAG,CAAC;QAClD,IAAI,CAAC1O,QAAQ,CAAC;UAAEoH,UAAU,EAAE1H;QAAM,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC0G,KAAK,CAAEC,CAAC,IAAK;QACZrB,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IAEA,IAAI,CAACmE,SAAS,CAAC,CAAC;EACpB;EACA;EACAhB,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAClJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAACjF,SAAS,KAAKiC,SAAS,EAAE;QAC5C,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACjF;UAAS;YAAAsP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAAClF;UAAS;YAAAsP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAEzB;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACAvF,iBAAiBA,CAACrI,QAAQ,EAAE;IAAA,IAAA6N,mBAAA;IACxB,oBACIlQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACoS;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAAC,mBAAA,GACtD7N,QAAQ,CAACxB,UAAU,cAAAqP,mBAAA,uBAAnBA,mBAAA,CAAqB1C,WAAW;IAAA;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEzB;EACA;EACA7E,oBAAoBA,CAAC/I,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,CAAC+B,MAAM,KAAK3B,SAAS,EAAE;MAC/BJ,QAAQ,CAACQ,QAAQ,GAAGR,QAAQ,CAAC+B,MAAM,GAAGE,QAAQ,CAACjC,QAAQ,CAACM,cAAc,CAAC;IAC3E,CAAC,MAAM;MACHN,QAAQ,CAACQ,QAAQ,GAAGR,QAAQ,CAACf,KAAK,GAAGgD,QAAQ,CAACjC,QAAQ,CAACS,WAAW,CAAC;IACvE;IACA,oBACI9C,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACqS;MAAQ;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1D5N,QAAQ,CAACQ,QAAQ;IAAA;MAAAiN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEzB;EACA;EACA5E,iBAAiBA,CAAChJ,QAAQ,EAAE;IACxB,IAAIA,QAAQ,CAACU,KAAK,KAAKN,SAAS,EAAE;MAC9B,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;QAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAACU,KAAK,CAAC;QAAA;UAAA+M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAAC,OAAQtO,QAAQ,CAACU,KAAM,KAAK,QAAQ,GAAGV,QAAQ,CAACU,KAAK,GAAGH,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC;QAAA;UAAA+M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtJ,CAAC;MAEzB;IAEJ,CAAC,MAAM,IAAI5N,QAAQ,CAACwM,SAAS,KAAKpM,SAAS,EAAE;MACzC,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;QAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAACa,OAAO,CAAC;QAAA;UAAA4M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAACtO,QAAQ,CAACwM,SAAS,CAAC;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC;MAEzB;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAAC1O,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;QAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC;QAAA;UAAA4M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtK,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,gBACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE7R,QAAQ,CAACsS;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAACtO,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC;QAAA;UAAA4M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC;MAEzB;IACJ;EACJ;EACA;EACA3E,kBAAkBA,CAACjJ,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAAC+B,MAAM,KAAK3B,SAAS,EAAE;MAC/B,IAAIJ,QAAQ,CAACU,KAAK,KAAKN,SAAS,EAAE;QAC9B,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC;UAAA;YAAA+M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpK,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACtO,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC;UAAA;YAAA+M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1I,CAAC;QAEzB;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1O,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAAC,CAACtO,QAAQ,CAACf,KAAK,KAAKmB,SAAS,GAAGJ,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAAC+B,MAAM,KAAK/B,QAAQ,CAACS,WAAW,KAAKL,SAAS,GAAGJ,QAAQ,CAACS,WAAW,GAAGT,QAAQ,CAACM,cAAc,CAAC,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,CAAC;UAAA;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9U,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAAC,CAACtO,QAAQ,CAACf,KAAK,KAAKmB,SAAS,GAAGJ,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAAC+B,MAAM,KAAK/B,QAAQ,CAACS,WAAW,KAAKL,SAAS,GAAGJ,QAAQ,CAACS,WAAW,GAAGT,QAAQ,CAACM,cAAc,CAAC,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,CAAC;UAAA;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpT,CAAC;QAEzB;MACJ;IACJ,CAAC,MAAM;MACH,IAAI5N,QAAQ,CAACU,KAAK,KAAKN,SAAS,EAAE;QAC9B,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC;UAAA;YAAA+M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACtO,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC;UAAA;YAAA+M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI,CAAC;QAEzB;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1O,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAACtO,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,CAAC;UAAA;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpK,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC6S;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACtO,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,CAAC;UAAA;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1I,CAAC;QAEzB;MACJ;IACJ;EAEJ;EACA;EACA1E,kBAAkBA,CAAClJ,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACM,cAAc,KAAKF,SAAS,EAAE;MACvC,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACxD5N,QAAQ,CAACM,cAAc;MAAA;QAAAmN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAEzB,CAAC,MAAM;MACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACxD5N,QAAQ,CAACS,WAAW;MAAA;QAAAgN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEzB;EACJ;EACA7D,gBAAgBA,CAAC/J,QAAQ,EAAE;IACvB,oBACIrC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAAC8S;MAAI;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtD5N,QAAQ,CAACmM,aAAa;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEzB;EACA;EACAnE,kBAAkBA,CAACzJ,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAAC+B,MAAM,KAAK3B,SAAS,EAAE;MAC/B,IAAIJ,QAAQ,CAACU,KAAK,KAAKN,SAAS,EAAE;QAC9B,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC,GAAGV,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,GAAGuB,QAAQ,CAACjC,QAAQ,CAACxB,UAAU,CAACiD,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnS,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC,GAAGV,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,GAAGuB,QAAQ,CAACjC,QAAQ,CAACxB,UAAU,CAACiD,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzQ,CAAC;QAEzB;MAEJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1O,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAC/D,oBACIY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,CAAC,GAAGb,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,GAAGoB,QAAQ,CAACjC,QAAQ,CAACxB,UAAU,CAACiD,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvZ,CAAC;QAEzB,CAAC,MAAM;UACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,CAAC,GAAGb,QAAQ,CAAC+B,MAAM,GAAG/B,QAAQ,CAACM,cAAc,GAAGC,UAAU,CAACP,QAAQ,CAACY,SAAS,KAAKR,SAAS,GAAGJ,QAAQ,CAACY,SAAS,GAAGZ,QAAQ,CAACa,OAAO,CAAC,GAAGoB,QAAQ,CAACjC,QAAQ,CAACxB,UAAU,CAACiD,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7X,CAAC;QAEzB;MACJ;IACJ,CAAC,MAAM;MACH,IAAI5N,QAAQ,CAACU,KAAK,KAAKN,SAAS,EAAE;QAC9B,IAAI,IAAI,CAAClB,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAAA,IAAA2R,oBAAA;UAC/D,oBACI/Q,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC,GAAGV,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,GAAGuB,QAAQ,EAAAyM,oBAAA,GAAC1O,QAAQ,CAACxB,UAAU,cAAAkQ,oBAAA,uBAAnBA,oBAAA,CAAqBjN,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5R,CAAC;QAEzB,CAAC,MAAM;UAAA,IAAAe,oBAAA;UACH,oBACIhR,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,CAAC,GAAGV,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACU,KAAK,CAAC,GAAGuB,QAAQ,EAAA0M,oBAAA,GAAC3O,QAAQ,CAACxB,UAAU,cAAAmQ,oBAAA,uBAAnBA,oBAAA,CAAqBlN,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClQ,CAAC;QAEzB;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1O,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,EAAE;UAAA,IAAA6R,oBAAA;UAC/D,oBACIjR,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACC,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,CAAC,GAAGZ,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,GAAGqB,QAAQ,EAAA2M,oBAAA,GAAC5O,QAAQ,CAACxB,UAAU,cAAAoQ,oBAAA,uBAAnBA,oBAAA,CAAqBnN,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpS,CAAC;QAEzB,CAAC,MAAM;UAAA,IAAAiB,oBAAA;UACH,oBACIlR,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,gBACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAAC/N,UAAU,CAACP,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,CAAC,GAAGZ,QAAQ,CAACf,KAAK,GAAGe,QAAQ,CAACS,WAAW,GAAGF,UAAU,CAACP,QAAQ,CAACY,SAAS,CAAC,GAAGqB,QAAQ,EAAA4M,oBAAA,GAAC7O,QAAQ,CAACxB,UAAU,cAAAqQ,oBAAA,uBAAnBA,oBAAA,CAAqBpN,GAAG,CAAC,GAAG,GAAG,CAAC;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1Q,CAAC;QAEzB;MACJ;IACJ;EACJ;EACA;EACA9E,eAAeA,CAAC9I,QAAQ,EAAE;IAAA,IAAA8O,oBAAA;IAEtB,IAAI,EAAAA,oBAAA,GAAA9O,QAAQ,CAACxB,UAAU,cAAAsQ,oBAAA,uBAAnBA,oBAAA,CAAqBrN,GAAG,MAAK,IAAI,EAAE;MAAA,IAAAsN,oBAAA;MACnC,oBACIpR,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE7R,QAAQ,CAACmM;QAAG;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,GAAAmB,oBAAA,GACrD/O,QAAQ,CAACxB,UAAU,cAAAuQ,oBAAA,uBAAnBA,oBAAA,CAAqBtN,GAAG,EAAC,IAC9B;MAAA;QAAAgM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAEzB,CAAC,MAAM;MACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE7R,QAAQ,CAACmM;QAAG;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrD5N,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4B,GAAG;MAAA;QAAA6L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEzB;EACJ;EACA;EACAjF,wBAAwBA,CAAC3I,QAAQ,EAAE;IAAA,IAAAgP,oBAAA;IAC/B,oBACIrR,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACuT;MAAM;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAAoB,oBAAA,GACxDhP,QAAQ,CAACxB,UAAU,cAAAwQ,oBAAA,uBAAnBA,oBAAA,CAAqBE,YAAY;IAAA;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAEzB;EACA;EACAzE,iBAAiBA,CAACnJ,QAAQ,EAAE;IACxB,oBACIrC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACyT;MAAK;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvD5N,QAAQ,CAACoM,SAAS,KAAKhM,SAAS,gBAC7BzC,OAAA;QAAM6P,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC7B5P,OAAA;UAAKyR,GAAG,EAAEtT,SAAS,GAAG,iBAAiB,GAAGkE,QAAQ,CAACoM,SAAS,GAAG,MAAO;UAACiD,OAAO,EAAGpK,CAAC,IAAKA,CAAC,CAACqK,MAAM,CAACF,GAAG,GAAG5T,QAAS;UAAC+T,GAAG,EAAEvP,QAAQ,CAACsE;QAAK;UAAAmJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrI,CAAC,gBAENjQ,OAAA;QAAM6P,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC7B5P,OAAA;UAAKyR,GAAG,EAAEtT,SAAS,GAAG,iBAAiB,IAAIkE,QAAQ,CAACwP,SAAS,KAAKpP,SAAS,GAAGJ,QAAQ,CAACwP,SAAS,GAAGxP,QAAQ,CAACxB,UAAU,CAACN,EAAE,CAAC,GAAG,MAAO;UAACmR,OAAO,EAAGpK,CAAC,IAAKA,CAAC,CAACqK,MAAM,CAACF,GAAG,GAAG5T,QAAS;UAAC+T,GAAG,EAAEvP,QAAQ,CAACsE,IAAI,KAAKlE,SAAS,GAAGJ,QAAQ,CAACsE,IAAI,GAAGtE,QAAQ,CAACxB,UAAU,CAAC2M;QAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnQ,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGW,CAAC;EAEzB;EACA;EACAtF,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACpJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAAC/E,IAAI,KAAK+B,SAAS,EAAE;QACvC,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAAC/E;UAAI;YAAAoP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAAChF;UAAI;YAAAoP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAEzB;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACArF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACrJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAACqM,GAAG,KAAKrP,SAAS,EAAE;QACtC,IAAI,IAAI,CAAClB,KAAK,CAACkE,OAAO,CAACqM,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACzC,OAAO,uCAAuC;QAClD,CAAC,MAAM;UACH,oBACI/R,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,eACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACqM;YAAG;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAEzB;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAC1O,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACoM,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACpD,OAAO,uCAAuC;QAClD,CAAC,MAAM;UACH,oBACI/R,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,eACX5P,OAAA;cAAM6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACoM;YAAG;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAEzB;MACJ;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACApF,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACtJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAACuM,IAAI,KAAKvP,SAAS,EAAE;QACvC,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACuM;UAAI;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACsM;UAAI;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAEzB;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACAnF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACvJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAACwM,GAAG,KAAKxP,SAAS,EAAE;QACtC,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACwM;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACuM;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAEzB;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACAlF,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACxJ,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,IAAI,CAAClE,KAAK,CAACkE,OAAO,CAAChF,OAAO,KAAKgC,SAAS,EAAE;QAC1C,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAAChF;UAAO;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAEzB,CAAC,MAAM;QACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;UAAA2P,QAAA,eACX5P,OAAA;YAAM6P,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACC,UAAU,CAACjF;UAAO;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAEzB;IACJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACA5D,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC9K,KAAK,CAACkE,OAAO,KAAK,IAAI,EAAE;MAC7B,oBACIzF,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,eACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACkE,OAAO,CAACE;QAAY;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAEzB;EACJ;EACA;EACArE,mBAAmBA,CAACnG,OAAO,EAAE;IACzB,IAAIyM,WAAW,GAAG,EAAE;IACpB,IAAIzM,OAAO,CAAC3C,WAAW,KAAKL,SAAS,EAAE;MACnCgD,OAAO,CAAC5E,UAAU,CAAC2B,kBAAkB,CAACF,OAAO,CAACC,OAAO,IAAI;QACrD,IAAIkD,OAAO,CAAC9C,cAAc,KAAKF,SAAS,EAAE;UACtC,IAAI6B,QAAQ,CAACmB,OAAO,CAAC9C,cAAc,CAAC,KAAKJ,OAAO,CAAClB,WAAW,EAAE;YAC1D6Q,WAAW,GAAG3P,OAAO,CAACnB,WAAW;UACrC;QACJ,CAAC,MAAM;UACH,IAAIqE,OAAO,CAAC3C,WAAW,KAAKP,OAAO,CAAClB,WAAW,EAAE;YAC7C6Q,WAAW,GAAG3P,OAAO,CAACnB,WAAW;UACrC;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAIqE,OAAO,CAAC9C,cAAc,KAAKF,SAAS,EAAE;QACtCgD,OAAO,CAAC5E,UAAU,CAAC2B,kBAAkB,CAACF,OAAO,CAACC,OAAO,IAAI;UACrD,IAAI+B,QAAQ,CAACmB,OAAO,CAAC9C,cAAc,CAAC,KAAKJ,OAAO,CAAClB,WAAW,EAAE;YAC1D6Q,WAAW,GAAG3P,OAAO,CAACnB,WAAW;UACrC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACH8Q,WAAW,GAAGzM,OAAO,CAACrE,WAAW;MACrC;IACJ;IACA,oBACIpB,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACoU;MAAO;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDiC,WAAW;IAAA;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEzB;EACA;EACApE,iBAAiBA,CAACpG,OAAO,EAAE;IACvB,IAAIA,OAAO,CAACrB,MAAM,KAAK3B,SAAS,EAAE;MAC9B,oBACIzC,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE7R,QAAQ,CAACqU;QAAM;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDxK,OAAO,CAACrB,MAAM;MAAA;QAAA0L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEzB,CAAC,MAAM;MACH,oBACIjQ,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,gBACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE7R,QAAQ,CAACsU;QAAK;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDxK,OAAO,CAACnE,KAAK;MAAA;QAAAwO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEzB;EACJ;EACA;EACA1D,6BAA6BA,CAAC9G,OAAO,EAAE;IACnC,oBACIzF,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACuU;MAAoB;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtExK,OAAO,CAACqJ,iBAAiB,EAAC,GAC/B;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC;EAEzB;EACA;EACAzD,yBAAyBA,CAAC/G,OAAO,EAAE;IAC/B,oBACIzF,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACwU;MAAgB;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAClExK,OAAO,CAACsJ,aAAa;IAAA;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEzB;EACA;EACAhF,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAAC1J,KAAK,CAACoD,YAAY,KAAK,IAAI,EAAE;MAElC,oBACI3E,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,eACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACoD,YAAY,CAACG;QAAS;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAGzB,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACA/E,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC3J,KAAK,CAACoD,YAAY,KAAK,IAAI,EAAE;MAClC,oBACI3E,OAAA,CAACrC,KAAK,CAACsC,QAAQ;QAAA2P,QAAA,eACX5P,OAAA;UAAM6P,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE,IAAI,CAACrO,KAAK,CAACoD,YAAY,CAACK;QAAI;UAAA8K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAEzB,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA/D,eAAeA,CAAC7J,QAAQ,EAAE;IAAA,IAAAmQ,qBAAA,EAAAC,sBAAA;IACtB,IAAI/D,OAAO,IAAA8D,qBAAA,GAAGnQ,QAAQ,CAAC6J,eAAe,cAAAsG,qBAAA,uBAAxBA,qBAAA,CAA0B9D,OAAO;IAC/C,IAAIC,KAAK,IAAA8D,sBAAA,GAAGpQ,QAAQ,CAAC6J,eAAe,cAAAuG,sBAAA,uBAAxBA,sBAAA,CAA0B9D,KAAK;IAC3C,oBACI3O,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAAC2U;MAAY;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC9D,CAAAvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjN,MAAM,IAAG,CAAC,iBAChBzB,OAAA,CAAAE,SAAA;QAAA0P,QAAA,EACKlB,OAAO,CAACZ,GAAG,CAAC,CAACpL,EAAE,EAAEiQ,GAAG,KAAK;UACtB,oBACI3S,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,EACVlN,EAAE,GAAG;UAAI,GADOiQ,GAAG;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC,EAEN,CAAAtB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAElN,MAAM,IAAG,CAAC,iBACdzB,OAAA,CAAAE,SAAA;QAAA0P,QAAA,EACKjB,KAAK,CAACb,GAAG,CAAC,CAACpL,EAAE,EAAEiQ,GAAG,KAAK;UACpB,oBACI3S,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,EACVlN,EAAE,GAAG;UAAI,GADOiQ,GAAG;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEK,CAAC;EAEzB;EACAhG,oBAAoBA,CAAC5H,QAAQ,EAAE;IAAA,IAAAuQ,qBAAA;IAC3B,IAAIC,kBAAkB,GAAG,EAAE;IAC3B,CAAAD,qBAAA,GAAAvQ,QAAQ,CAAC4H,oBAAoB,cAAA2I,qBAAA,uBAA7BA,qBAAA,CAA+B9E,GAAG,CAACpL,EAAE,IAAImQ,kBAAkB,CAACnP,IAAI,CAAChB,EAAE,CAAC0L,SAAS,GAAG;MAAE0E,WAAW,EAAEpQ,EAAE,CAAC2L,UAAU;MAAE0E,KAAK,EAAErQ,EAAE,CAAC0L,SAAS,CAACN,GAAG,CAACkF,IAAI,IAAIA,IAAI,CAAC,CAACrD,IAAI,CAAC,IAAI,CAAC;MAAEsD,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACzQ,EAAE,CAAC0Q,OAAO,CAAC,CAAC,CAAC,CAAC;MAAEC,EAAE,EAAEH,MAAM,CAACI,MAAM,CAAC5Q,EAAE,CAAC0Q,OAAO,CAAC,CAACtF,GAAG,CAACE,GAAG,IAAIA,GAAG,CAACF,GAAG,CAACkF,IAAI,OAAApM,MAAA,CAAOsM,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,QAAApM,MAAA,CAAKsM,MAAM,CAACI,MAAM,CAACN,IAAI,CAAC,CAAE,CAAC,CAACrD,IAAI,CAAC,IAAI,CAAC;IAAE,CAAC,GAAIjN,EAAE,CAAC2L,UAAU,kBAAAzH,MAAA,CAAkBlE,EAAE,CAAC2L,UAAU,gBAAAzH,MAAA,CAAUlE,EAAE,CAACgM,OAAO,gBAAA9H,MAAA,CAAgBlE,EAAE,CAACgM,OAAO,6BAAA9H,MAAA,CAA0BlE,EAAE,CAACiM,KAAK,eAAY,IAAK,EAAG,CAAC,CAAC;IAChd,oBACI3O,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACwV;MAAkB;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACpE4C,kBAAkB,CAACpR,MAAM,GAAG,CAAC,iBAC1BzB,OAAA,CAACF,eAAe;QAACwT,MAAM,EAAET,kBAAmB;QAACW,KAAK,EAAC,QAAQ;QAACC,KAAK,EAAE;MAAK;QAAA3D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,GAH9D5N,QAAQ,CAAC9B,EAAE;MAAAuP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKhB,CAAC;EAEzB;EACA9D,gBAAgBA,CAAC9J,QAAQ,EAAE;IAAA,IAAAqR,qBAAA,EAAAC,sBAAA;IACvB,IAAIjF,OAAO,IAAAgF,qBAAA,GAAGrR,QAAQ,CAAC8J,gBAAgB,cAAAuH,qBAAA,uBAAzBA,qBAAA,CAA2BhF,OAAO;IAChD,IAAIC,KAAK,IAAAgF,sBAAA,GAAGtR,QAAQ,CAAC8J,gBAAgB,cAAAwH,sBAAA,uBAAzBA,sBAAA,CAA2BhF,KAAK;IAC5C,oBACI3O,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAAC6V;MAAO;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzD,CAAAvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjN,MAAM,IAAG,CAAC,iBAChBzB,OAAA,CAAAE,SAAA;QAAA0P,QAAA,EACKlB,OAAO,CAACZ,GAAG,CAAC,CAACpL,EAAE,EAAEiQ,GAAG,KAAK;UACtB,oBACI3S,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,EACVlN,EAAE,GAAG;UAAI,GADOiQ,GAAG;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC,EAEN,CAAAtB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAElN,MAAM,IAAG,CAAC,iBACdzB,OAAA,CAAAE,SAAA;QAAA0P,QAAA,EACKjB,KAAK,CAACb,GAAG,CAAC,CAACpL,EAAE,EAAEiQ,GAAG,KAAK;UACpB,oBACI3S,OAAA,CAACrC,KAAK,CAACsC,QAAQ;YAAA2P,QAAA,EACVlN,EAAE,GAAG;UAAI,GADOiQ,GAAG;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEK,CAAC;EAEzB;EACA;EACAxE,SAASA,CAAC0C,QAAQ,EAAE;IAChB,IAAIa,UAAU,GAAG,EAAE;IACnB,IAAIlI,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAIgF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;MAC9E,IAAI,CAACb,QAAQ,CAAC;QACV0C,KAAK,EAAE,QAAQ;QACf+C,MAAM,EAAE;MACZ,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI1D,GAAG,GAAG,CAAC;MACX,IAAI0D,MAAM,GAAG,CAAC;MACdsI,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;MACrDkN,UAAU,CAAC1M,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAIA,OAAO,CAACU,SAAS,KAAKR,SAAS,EAAE;UAAA,IAAAoR,kBAAA;UACjC,IAAIlQ,KAAK,GAAGf,UAAU,CAACL,OAAO,CAACU,SAAS,CAAC,GAAGV,OAAO,CAACjB,KAAK,GAAGiB,OAAO,CAACO,WAAW,GAAGF,UAAU,CAACI,GAAG,CAAC;UACjGA,GAAG,GAAGW,KAAK;UACX+C,MAAM,GAAG1D,GAAG,GAAGA,GAAG,IAAI,EAAA6Q,kBAAA,GAAAtR,OAAO,CAACkM,SAAS,cAAAoF,kBAAA,uBAAjBA,kBAAA,CAAmB/P,GAAG,MAAKrB,SAAS,GAAG6B,QAAQ,CAAC/B,OAAO,CAACkM,SAAS,CAAC3K,GAAG,CAAC,GAAIvB,OAAO,CAAC1B,UAAU,KAAK4B,SAAS,GAAG6B,QAAQ,CAAC/B,OAAO,CAAC1B,UAAU,CAACiD,GAAG,CAAC,GAAG,EAAG,CAAC,GAAG,GAAG;UAChL,IAAI,IAAI,CAACvC,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;YAClH,IAAI,CAACb,QAAQ,CAAC;cACV0C,KAAK,EAAE,IAAI2M,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAGnL,GAAG,GAAGA,GAAG,GAAG,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAGlG,GAAG,GAAG,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAIlG,GAAG,CAAC;cACzM0D,MAAM,EAAE,IAAI4J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAGzH,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACnF,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAGxC,MAAM,GAAG,IAAI,CAACnF,KAAK,CAAC2H,MAAM,GAAIxC,MAAM,CAAC;cACtNwD,GAAG,EAAE,IAAIoG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAIzH,MAAM,GAAG1D,GAAG,GAAI,CAAC0D,MAAM,GAAG1D,GAAG,IAAI,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAIxC,MAAM,GAAG1D,GAAG,GAAI,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAIxC,MAAM,GAAG1D,GAAG;YACpP,CAAC,CAAC;UACN,CAAC,MAAM;YACH,IAAI,CAAC/B,QAAQ,CAAC;cACV0C,KAAK,EAAE,IAAI2M,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAAC3N,GAAG,CAAC;cACnH0D,MAAM,EAAE,IAAI4J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACjK,MAAM,CAAC;cACvHwD,GAAG,EAAE,IAAIoG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACjK,MAAM,GAAG1D,GAAG;YAC7H,CAAC,CAAC;UACN;QACJ,CAAC,MAAM;UAAA,IAAA8Q,mBAAA;UACHnQ,KAAK,GAAGf,UAAU,CAACL,OAAO,CAACQ,KAAK,KAAKN,SAAS,GAAGF,OAAO,CAACQ,KAAK,GAAGR,OAAO,CAACW,OAAO,CAAC,GAAGX,OAAO,CAAC6B,MAAM,GAAG7B,OAAO,CAACI,cAAc,GAAGC,UAAU,CAACI,GAAG,CAAC;UAC7IA,GAAG,GAAGW,KAAK;UACX+C,MAAM,GAAG1D,GAAG,GAAGA,GAAG,IAAI,EAAA8Q,mBAAA,GAAAvR,OAAO,CAACkM,SAAS,cAAAqF,mBAAA,uBAAjBA,mBAAA,CAAmBhQ,GAAG,MAAKrB,SAAS,GAAG6B,QAAQ,CAAC/B,OAAO,CAACkM,SAAS,CAAC3K,GAAG,CAAC,GAAIvB,OAAO,CAAC1B,UAAU,KAAK4B,SAAS,GAAG6B,QAAQ,CAAC/B,OAAO,CAAC1B,UAAU,CAACiD,GAAG,CAAC,GAAG,EAAG,CAAC,GAAG,GAAG;UAChL,IAAI,IAAI,CAACvC,KAAK,CAACG,IAAI,KAAKpC,YAAY,IAAI,IAAI,CAACiC,KAAK,CAACG,IAAI,KAAKtC,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;YAClH,IAAI,CAACb,QAAQ,CAAC;cACV0C,KAAK,EAAE,IAAI2M,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAGnL,GAAG,GAAGA,GAAG,GAAG,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAGlG,GAAG,GAAG,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAIlG,GAAG,CAAC;cACnO0D,MAAM,EAAE,IAAI4J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAGzH,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACnF,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAGxC,MAAM,GAAG,IAAI,CAACnF,KAAK,CAAC2H,MAAM,GAAIxC,MAAM,CAAC;cAChPwD,GAAG,EAAE,IAAIoG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,CAACC,MAAM,CAACxC,QAAQ,GAAIA,QAAQ,KAAK,mBAAmB,GAAIzH,MAAM,GAAG1D,GAAG,GAAI,CAAC0D,MAAM,GAAG1D,GAAG,IAAI,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAG,GAAG,GAAIxC,MAAM,GAAG1D,GAAG,GAAI,IAAI,CAACzB,KAAK,CAAC2H,MAAM,GAAIxC,MAAM,GAAG1D,GAAG;YAC9Q,CAAC,CAAC;UACN,CAAC,MAAM;YACH,IAAI,CAAC/B,QAAQ,CAAC;cACV0C,KAAK,EAAE,IAAI2M,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAAC3N,GAAG,CAAC;cACzF0D,MAAM,EAAE,IAAI4J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACjK,MAAM,CAAC;cAC7FwD,GAAG,EAAE,IAAIoG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACE,MAAM,CAACjK,MAAM,GAAG1D,GAAG;YACnG,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EA4YA;EACA0I,YAAYA,CAAA,EAAG;IACX,IAAIsD,UAAU,GAAG,EAAE;IACnBA,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;IACrD,IAAImN,KAAK,GAAGD,UAAU,CAACE,SAAS,CAAClB,GAAG,IAAIA,GAAG,CAACnN,UAAU,CAAC2M,WAAW,KAAK,IAAI,CAACjM,KAAK,CAACoG,MAAM,CAAC9G,UAAU,CAAC2M,WAAW,CAAC;IAChHwB,UAAU,CAAC+E,MAAM,CAAC9E,KAAK,EAAE,CAAC,CAAC;IAC3B,IAAI1K,MAAM,GAAG,EAAE;IACfA,MAAM,GAAGyK,UAAU;IACnB,KAAK,IAAIgF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhF,UAAU,CAACvN,MAAM,EAAEuS,CAAC,EAAE,EAAE;MACxC,IAAIhF,UAAU,CAACgF,CAAC,CAAC,CAACjR,KAAK,KAAKN,SAAS,EAAE;QACnC8B,MAAM,CAACyP,CAAC,CAAC,CAACzP,MAAM,GAAG3B,UAAU,CAACoM,UAAU,CAACgF,CAAC,CAAC,CAACjR,KAAK,CAAC,GAAGiM,UAAU,CAACgF,CAAC,CAAC,CAACnR,QAAQ;MAC/E,CAAC,MAAM;QACH0B,MAAM,CAACyP,CAAC,CAAC,CAACzP,MAAM,GAAG3B,UAAU,CAACoM,UAAU,CAACgF,CAAC,CAAC,CAAC/Q,SAAS,CAAC,GAAG+L,UAAU,CAACgF,CAAC,CAAC,CAACnR,QAAQ;MACnF;IACJ;IACAmM,UAAU,GAAGzK,MAAM;IACnBuC,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACsB,UAAU,CAAC,CAAC;IACxD,IAAI,CAAC/N,QAAQ,CAAC;MACVuI,kBAAkB,EAAE,KAAK;MACzB7B,MAAM,EAAE,IAAI,CAACrH,WAAW;MACxB+B,QAAQ,EAAE2M;IACd,CAAC,CAAC;IACF,IAAI,CAACvD,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACpL,KAAK,CAACjC,UAAU,CAAC,CAAC;EAClC;EACA;EACAuN,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC1K,QAAQ,CAAC;MACVuI,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACAyK,mBAAmBA,CAACtM,MAAM,EAAE;IACxB,IAAI,CAAC1G,QAAQ,CAAC;MACV0G;IACJ,CAAC,CAAC;IACFjJ,aAAa,CAAC;MACVuJ,OAAO,EAAElK,QAAQ,CAACmW,OAAO;MACzBC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE,IAAI,CAAC7I,YAAY;MACzB8I,MAAM,EAAE,IAAI,CAAC7I;IACjB,CAAC,CAAC;EACN;EACA;EACApB,kBAAkBA,CAACkK,OAAO,EAAE;IACxB,oBACIzU,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,eACX5P,OAAA,CAAClC,MAAM;QAACsW,IAAI,EAAC,aAAa;QAACvE,SAAS,EAAC,kBAAkB;QAAC6E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACT,mBAAmB,CAACQ,OAAO;MAAE;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEzB;EACA;EACAlE,eAAeA,CAACzE,CAAC,EAAE;IAAA,IAAAqN,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACf,IAAInQ,YAAY,GAAG;MACfO,IAAI,GAAAyP,qBAAA,GAAE,IAAI,CAACpT,KAAK,CAACoD,YAAY,cAAAgQ,qBAAA,uBAAvBA,qBAAA,CAAyBzP,IAAI;MACnC6B,OAAO,GAAA6N,sBAAA,GAAE,IAAI,CAACrT,KAAK,CAACoD,YAAY,cAAAiQ,sBAAA,uBAAvBA,sBAAA,CAAyB7N,OAAO;MACzCjC,SAAS,EAAEwC,CAAC,CAACqK,MAAM,CAAC1I,KAAK,CAACtC,IAAI,KAAKlE,SAAS,GAAG6E,CAAC,CAACqK,MAAM,CAAC1I,KAAK,CAACtC,IAAI,GAAGW,CAAC,CAACqK,MAAM,CAAC1I,KAAK;MACnFjE,IAAI,GAAA6P,sBAAA,GAAE,IAAI,CAACtT,KAAK,CAACoD,YAAY,cAAAkQ,sBAAA,uBAAvBA,sBAAA,CAAyB7P,IAAI;MACnCJ,KAAK,GAAAkQ,sBAAA,GAAE,IAAI,CAACvT,KAAK,CAACoD,YAAY,cAAAmQ,sBAAA,uBAAvBA,sBAAA,CAAyBlQ;IACpC,CAAC;IACD,IAAI,CAAC3D,QAAQ,CAAC;MACV0D,YAAY,EAAEA,YAAY;MAC1BnD,eAAe,EAAE8F,CAAC,CAACqK,MAAM,CAAC1I;IAC9B,CAAC,CAAC;EACN;EACA;EACA+C,UAAUA,CAAC1E,CAAC,EAAE;IAAA,IAAAyN,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACV,IAAIvQ,YAAY,GAAG;MACfO,IAAI,GAAA6P,sBAAA,GAAE,IAAI,CAACxT,KAAK,CAACoD,YAAY,cAAAoQ,sBAAA,uBAAvBA,sBAAA,CAAyB7P,IAAI;MACnC6B,OAAO,GAAAiO,sBAAA,GAAE,IAAI,CAACzT,KAAK,CAACoD,YAAY,cAAAqQ,sBAAA,uBAAvBA,sBAAA,CAAyBjO,OAAO;MACzCjC,SAAS,GAAAmQ,sBAAA,GAAE,IAAI,CAAC1T,KAAK,CAACoD,YAAY,cAAAsQ,sBAAA,uBAAvBA,sBAAA,CAAyBnQ,SAAS;MAC7CE,IAAI,EAAEsC,CAAC,CAACqK,MAAM,CAAC1I,KAAK;MACpBrE,KAAK,GAAAsQ,sBAAA,GAAE,IAAI,CAAC3T,KAAK,CAACoD,YAAY,cAAAuQ,sBAAA,uBAAvBA,sBAAA,CAAyBtQ;IACpC,CAAC;IACD,IAAI,CAAC3D,QAAQ,CAAC;MACV0D,YAAY,EAAEA;IAClB,CAAC,CAAC;EACN;EACA;EACAqI,iBAAiBA,CAAC1F,CAAC,EAAE6C,OAAO,EAAE;IAC1B,IAAI9H,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACd,KAAK,CAACc,QAAQ,CAAC;IACvCA,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;MACxB,IAAIA,OAAO,CAACkM,SAAS,KAAKhM,SAAS,IAAI0H,OAAO,CAACsK,OAAO,CAAChG,SAAS,KAAKhM,SAAS,EAAE;QAC5E,IAAIF,OAAO,CAACkM,SAAS,KAAKtE,OAAO,CAACsK,OAAO,CAAChG,SAAS,EAAE;UACjD,IAAIlM,OAAO,CAAC6B,MAAM,KAAK3B,SAAS,EAAE;YAC9BF,OAAO,CAAC6B,MAAM,GAAGkD,CAAC,CAAC2B,KAAK;UAC5B,CAAC,MAAM;YACH1G,OAAO,CAACjB,KAAK,GAAGgG,CAAC,CAAC2B,KAAK;UAC3B;QACJ;MACJ,CAAC,MAAM;QACH,IAAI1G,OAAO,CAAC1B,UAAU,CAACN,EAAE,KAAK4J,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAACN,EAAE,EAAE;UACzD,IAAIgC,OAAO,CAAC6B,MAAM,KAAK3B,SAAS,EAAE;YAC9BF,OAAO,CAAC6B,MAAM,GAAGkD,CAAC,CAAC2B,KAAK;UAC5B,CAAC,MAAM;YACH1G,OAAO,CAACjB,KAAK,GAAGgG,CAAC,CAAC2B,KAAK;UAC3B;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,IAAI+F,UAAU,GAAG,EAAE;IACnB,IAAIlI,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACrCkN,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;MACrD,IAAImN,KAAK,GAAGD,UAAU,CAACE,SAAS,CAAClB,GAAG,IAAI,CAACA,GAAG,CAACnN,UAAU,KAAK4B,SAAS,GAAGuL,GAAG,CAACnN,UAAU,CAACN,EAAE,GAAGyN,GAAG,CAACS,SAAS,CAAClO,EAAE,MAAM4J,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAACN,EAAE,CAAC;MAChJ,IAAI0O,KAAK,KAAK,CAAC,CAAC,EAAE;QACdD,UAAU,CAACC,KAAK,CAAC,GAAG9E,OAAO,CAACsK,OAAO;QACnC3N,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACsB,UAAU,CAAC,CAAC;MAC5D;IACJ;IACA,IAAI,CAAC/N,QAAQ,CAAC;MAAEoB;IAAS,CAAC,CAAC;IAC3B,IAAI,CAACoJ,SAAS,CAAC,CAAC;EACpB;EACA;EACA0J,kBAAkBA,CAAC7N,CAAC,EAAE6C,OAAO,EAAE;IAC3B,IAAI9H,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACd,KAAK,CAACc,QAAQ,CAAC;IACvCA,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;MACxB,IAAIA,OAAO,CAACkM,SAAS,KAAKhM,SAAS,EAAE;QACjC,IAAIF,OAAO,CAAC1B,UAAU,CAACN,EAAE,KAAK4J,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAACN,EAAE,EAAE;UACzD,IAAIgC,OAAO,CAACI,cAAc,KAAKF,SAAS,EAAE;YACtCF,OAAO,CAACI,cAAc,GAAG2E,CAAC;YAC1B/E,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAACjB,KAAK;YACzDiB,OAAO,CAACgC,MAAM,GAAG,IAAI+L,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACpO,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,CAAC;UAChJ,CAAC,MAAM;YACHR,OAAO,CAACO,WAAW,GAAGwE,CAAC;YACvB/E,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACjB,KAAK;YACtDiB,OAAO,CAACgC,MAAM,GAAG,IAAI+L,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACpO,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,CAAC;UAChJ;QACJ;MACJ,CAAC,MAAM;QACH,IAAIR,OAAO,CAACkM,SAAS,KAAKtE,OAAO,CAACsK,OAAO,CAAChG,SAAS,EAAE;UACjD,IAAIlM,OAAO,CAACI,cAAc,KAAKF,SAAS,EAAE;YACtCF,OAAO,CAACI,cAAc,GAAG2E,CAAC;YAC1B/E,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAAC6B,MAAM;YAC1D7B,OAAO,CAACgC,MAAM,GAAG,IAAI+L,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACpO,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,CAAC;UAChJ,CAAC,MAAM;YACHR,OAAO,CAACO,WAAW,GAAGwE,CAAC;YACvB/E,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACjB,KAAK;YACtDiB,OAAO,CAACgC,MAAM,GAAG,IAAI+L,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACE,MAAM,CAACpO,OAAO,CAACM,QAAQ,GAAGD,UAAU,CAACL,OAAO,CAACQ,KAAK,CAAC,CAAC;UAChJ;QAEJ;MACJ;IACJ,CAAC,CAAC;IACF+D,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACrL,QAAQ,CAAC,CAAC;IACtD,IAAI,CAACpB,QAAQ,CAAC;MAAEoB;IAAS,CAAC,CAAC;IAC3B,IAAI,CAACoJ,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACpL,KAAK,CAACjC,UAAU,CAAC,CAAC;EAClC;EACAgX,cAAcA,CAAC9N,CAAC,EAAE6C,OAAO,EAAEwI,GAAG,EAAE;IAC5B,IAAItQ,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACd,KAAK,CAACc,QAAQ,CAAC;IACvC8H,OAAO,CAACsK,OAAO,CAAC9B,GAAG,CAAC,GAAGrL,CAAC,CAAC2B,KAAK,KAAK,IAAI,GAAG3B,CAAC,CAAC2B,KAAK,GAAG,CAAC;IACrD,IAAI0J,GAAG,KAAK,mBAAmB,EAAE;MAC7BxI,OAAO,CAACsK,OAAO,CAACvR,OAAO,GAAGN,UAAU,CAACuH,OAAO,CAACsK,OAAO,CAACvR,OAAO,CAAC,GAAGN,UAAU,CAACuH,OAAO,CAACsK,OAAO,CAACvR,OAAO,CAAC,GAAGN,UAAU,CAAC0E,CAAC,CAAC2B,KAAK,KAAK,IAAI,GAAG3B,CAAC,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG;IAC1J,CAAC,MAAM;MACHkB,OAAO,CAACsK,OAAO,CAACvR,OAAO,GAAGN,UAAU,CAACuH,OAAO,CAACsK,OAAO,CAACvR,OAAO,CAAC,GAAGN,UAAU,CAAC0E,CAAC,CAAC2B,KAAK,KAAK,IAAI,GAAG3B,CAAC,CAAC2B,KAAK,GAAG,CAAC,CAAC;IAC9G;IACA,IAAI+F,UAAU,GAAG,EAAE;IACnB,IAAIlI,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACrCkN,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;MACrD,IAAImN,KAAK,GAAGD,UAAU,CAACE,SAAS,CAAClB,GAAG,IAAI,CAACA,GAAG,CAACnN,UAAU,KAAK4B,SAAS,GAAGuL,GAAG,CAACnN,UAAU,CAACN,EAAE,GAAGyN,GAAG,CAACS,SAAS,CAAClO,EAAE,MAAM4J,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAACN,EAAE,CAAC;MAChJ,IAAI0O,KAAK,KAAK,CAAC,CAAC,EAAE;QACdD,UAAU,CAACC,KAAK,CAAC,GAAG9E,OAAO,CAACsK,OAAO;QACnC3N,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACsB,UAAU,CAAC,CAAC;MAC5D;IACJ;IACA,IAAI,CAAC/N,QAAQ,CAAC;MAAEoB;IAAS,CAAC,CAAC;IAC3B,IAAI,CAACoJ,SAAS,CAAC,CAAC;EACpB;EACA;EACAsB,WAAWA,CAAC5C,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACsK,OAAO,CAACrQ,MAAM,KAAK3B,SAAS,EAAE;MACtC,oBAAOzC,OAAA,CAACxB,WAAW;QAACyK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,QAAQ,CAAE;QAACY,aAAa,EAAG/N,CAAC,IAAK,IAAI,CAAC0F,iBAAiB,CAAC1F,CAAC,EAAE6C,OAAO;MAAE;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACtH,CAAC,MAAM;MACH,oBAAOjQ,OAAA,CAACxB,WAAW;QAACyK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,OAAO,CAAE;QAACY,aAAa,EAAG/N,CAAC,IAAK,IAAI,CAAC0F,iBAAiB,CAAC1F,CAAC,EAAE6C,OAAO;MAAE;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACrH;EAEJ;EACAxD,gBAAgBA,CAACtC,OAAO,EAAE;IACtB,oBAAOnK,OAAA,CAACxB,WAAW;MAACyK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,mBAAmB,CAAE;MAACY,aAAa,EAAEA,CAAC/N,CAAC,EAAEqL,GAAG,KAAK,IAAI,CAACyC,cAAc,CAAC9N,CAAC,EAAE6C,OAAO,EAAEwI,GAAG,GAAG,mBAAmB,CAAE;MAAC2C,MAAM,EAAC;IAAG;MAAAxF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEzK;EACAvD,eAAeA,CAACvC,OAAO,EAAE;IACrB,oBAAOnK,OAAA,CAACxB,WAAW;MAACyK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,eAAe,CAAE;MAACY,aAAa,EAAEA,CAAC/N,CAAC,EAAEqL,GAAG,KAAK,IAAI,CAACyC,cAAc,CAAC9N,CAAC,EAAE6C,OAAO,EAAEwI,GAAG,GAAG,eAAe,CAAE;MAAC2C,MAAM,EAAC;IAAG;MAAAxF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEjK;EACA;EACAsF,aAAaA,CAACpL,OAAO,EAAE;IACnB,IAAIqL,OAAO,GAAG,EAAE;IAChB,IAAIrL,OAAO,CAACsK,OAAO,CAAC5T,UAAU,KAAK4B,SAAS,EAAE;MAC1C,KAAK,MAAMgT,MAAM,IAAItL,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAAC2B,kBAAkB,EAAE;QAChEgT,OAAO,CAAC9R,IAAI,CAAC;UACTtC,WAAW,EAAEqU,MAAM,CAACrU,WAAW;UAC/BC,WAAW,EAAEoU,MAAM,CAACpU;QACxB,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,KAAK,MAAM2M,GAAG,IAAI7D,OAAO,CAACsK,OAAO,CAACiB,OAAO,CAAClT,kBAAkB,EAAE;QAC1DgT,OAAO,CAAC9R,IAAI,CAAC;UACTtC,WAAW,EAAE4M,GAAG,CAAC5M,WAAW;UAC5BC,WAAW,EAAE2M,GAAG,CAAC3M;QACrB,CAAC,CAAC;MACN;IACJ;IACA,IAAIsU,WAAW,GAAG,EAAE;IACpB,IAAIxL,OAAO,CAACsK,OAAO,CAAC9R,cAAc,KAAKF,SAAS,EAAE;MAC9C0H,OAAO,CAACsK,OAAO,CAAC5T,UAAU,CAAC2B,kBAAkB,CAACF,OAAO,CAACC,OAAO,IAAI;QAC7D,IAAI+B,QAAQ,CAAC6F,OAAO,CAACsK,OAAO,CAAC9R,cAAc,CAAC,KAAKJ,OAAO,CAAClB,WAAW,EAChEsU,WAAW,GAAGpT,OAAO,CAACnB,WAAW;MACzC,CAAC,CAAC;IACN;IACA,oBACIpB,OAAA,CAACvB,QAAQ;MAACwK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,aAAa,CAAE;MAACtK,OAAO,EAAEqL,OAAQ;MAACI,WAAW,EAAC,aAAa;MAACC,WAAW,EAAC,aAAa;MAClHC,QAAQ,EAAGxO,CAAC,IAAK,IAAI,CAAC6N,kBAAkB,CAAC7N,CAAC,CAAC2B,KAAK,EAAEkB,OAAO,EAAEqL,OAAO,CAAE;MAAChF,KAAK,EAAE;QAAEuF,KAAK,EAAE;MAAO,CAAE;MAACJ,WAAW,EAAEA,WAAY;MACxHK,YAAY,EAAGC,MAAM,IAAK;QACtB,oBAAOjW,OAAA;UAAM6P,SAAS,0BAAAjJ,MAAA,CAA0BqP,MAAM,CAAC7U,WAAW,CAAC8U,WAAW,CAAC,CAAC,CAAG;UAAAtG,QAAA,EAAEqG,MAAM,CAAC7U;QAAW;UAAA0O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACnH;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEjB;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhE,qBAAqBA,CAAC3E,CAAC,EAAE;IACrB,IAAI,IAAI,CAAC/F,KAAK,CAACqI,eAAe,CAACnI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI8C,MAAM,GAAG3B,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAACwS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC/Q,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC1F,IAAIsB,MAAM,GAAG9D,UAAU,CAAC,IAAI,CAACrB,KAAK,CAACmF,MAAM,CAACyP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC/Q,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC3F,IAAI3B,IAAI,GAAG,IAAI,CAAClC,KAAK,CAACqI,eAAe,CAACnG,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACqL,iBAAiB,KAAKzG,CAAC,CAAC2B,KAAK,CAAC7C,IAAI,CAAC;MACvF,IAAI3C,IAAI,KAAKhB,SAAS,EAAE;QACpB8B,MAAM,GAAGd,IAAI,CAACiL,OAAO,GAAGnK,MAAM,GAAGA,MAAM,GAAGd,IAAI,CAACiL,OAAO,GAAG,GAAG,GAAGnK,MAAM,GAAGd,IAAI,CAACkL,KAAK;QAClFjI,MAAM,GAAGjD,IAAI,CAACiL,OAAO,GAAGhI,MAAM,GAAGA,MAAM,GAAGjD,IAAI,CAACiL,OAAO,GAAG,GAAG,GAAGhI,MAAM,GAAGjD,IAAI,CAACkL,KAAK;QAClF,IAAI,CAAC1N,QAAQ,CAAC;UACV0C,KAAK,EAAE,IAAI2M,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAACpM,MAAM,CAAC;UAC5FmC,MAAM,EAAE,IAAI4J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAACjK,MAAM,CAAC;UAC7FwD,GAAG,EAAE,IAAIoG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACE,MAAM,CAACjK,MAAM,GAAGnC,MAAM;QACtG,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAACtD,QAAQ,CAAC;MACVkF,qBAAqB,EAAEmB,CAAC,CAAC2B;IAC7B,CAAC,CAAC;EACN;EACAqD,cAAcA,CAAChF,CAAC,EAAE;IACd,IAAIyC,GAAG,GAAG,YAAY,GAAGzC,CAAC,CAACqK,MAAM,CAAC1I,KAAK,CAACxH,MAAM,GAAG,MAAM,GAAG6F,CAAC,CAAC8O,aAAa,CAACC,SAAS,GAAG,YAAY;IAClG,IAAI,CAACpV,QAAQ,CAAC;MACV8I,GAAG,EAAEA;IACT,CAAC,CAAC;EACN;EACAD,gBAAgBA,CAACxC,CAAC,EAAE;IAChB,IAAI,CAACrG,QAAQ,CAAC;MAAEgI,KAAK,EAAE3B,CAAC,CAAC2B;IAAM,CAAC,CAAC;IACjC,IAAI3B,CAAC,CAAC2B,KAAK,KAAK,eAAe,EAAE;MAC7B,IAAI,CAAChI,QAAQ,CAAC;QAAE6I,gBAAgB,EAAE;MAAG,CAAC,CAAC;IAC3C,CAAC,MAAM;MACH,IAAI,CAAC7I,QAAQ,CAAC;QAAE6I,gBAAgB,EAAE;MAAS,CAAC,CAAC;MAC7C,IAAI,CAAC2B,SAAS,CAAC,CAAC;IACpB;EACJ;EACAkB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAClB,SAAS,CAAC,IAAI,CAAClK,KAAK,CAAC0H,KAAK,CAAC;EACpC;EACA,MAAM2D,WAAWA,CAAA,EAAG;IAChB,IAAI3C,oBAAoB,GAAG,EAAE;IAC7B,IAAI,CAAC1I,KAAK,CAACc,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;MAAA,IAAA+T,sBAAA;MACnC,CAAAA,sBAAA,GAAA/T,OAAO,CAAC0H,oBAAoB,cAAAqM,sBAAA,uBAA5BA,sBAAA,CAA8BxI,GAAG,CAACpL,EAAE,IAAIuH,oBAAoB,CAACvG,IAAI,CAAChB,EAAE,CAAC,CAAC;IAC1E,CAAC,CAAC;IACFuH,oBAAoB,GAAGpK,oCAAoC,CAACoK,oBAAoB,CAAC;IACjF,IAAIxG,IAAI,GAAGwG,oBAAoB,CAACxG,IAAI,CAACuP,IAAI,IAAIA,IAAI,CAACI,OAAO,KAAK3Q,SAAS,CAAC;IACxE,IAAIgB,IAAI,KAAKhB,SAAS,EAAE;MACpB,IAAIuH,MAAM,GAAG,EAAE;MACf,IAAIC,oBAAoB,CAACxI,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI8U,UAAU,GAAG5U,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACzB,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxE,MAAM5D,UAAU,CAAC,KAAK,mCAAA0I,MAAA,CAAmC2P,UAAU,iBAAc,CAAC,CAC7EtP,IAAI,CAAEC,GAAG,IAAK;UACX,IAAIsP,QAAQ,GAAG,EAAE;UACjBvM,oBAAoB,CAAC3H,OAAO,CAACC,OAAO,IAAI;YACpC,IAAIkU,eAAe,GAAG,IAAI,CAAClV,KAAK,CAACc,QAAQ,CAACyL,GAAG,CAACpL,EAAE,IAAIA,EAAE,CAACG,QAAQ,CAAC,CAAC6T,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC;YACvG,IAAIH,eAAe,IAAInS,QAAQ,CAAC/B,OAAO,CAAC8L,UAAU,CAAC,EAAE;cACjDrE,MAAM,CAACtG,IAAI,gBAAAkD,MAAA,CAAgBrE,OAAO,CAAC8L,UAAU,kBAAAzH,MAAA,CAAerE,OAAO,CAAC6L,SAAS,CAACN,GAAG,CAACpL,EAAE,IAAIA,EAAE,CAAC,CAACiN,IAAI,CAAC,IAAI,CAAC,gBAAA/I,MAAA,CAAasM,MAAM,CAACC,IAAI,CAAC5Q,OAAO,CAAC6Q,OAAO,CAAC,CAAC,CAAC,CAAC,QAAAxM,MAAA,CAAKsM,MAAM,CAACI,MAAM,CAAC/Q,OAAO,CAAC6Q,OAAO,CAAC,CAAC,CAAC,CAAC,CAACtF,GAAG,CAACpL,EAAE,IAAIwQ,MAAM,CAACC,IAAI,CAACzQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;YAC7N;YACAwQ,MAAM,CAACI,MAAM,CAAC/Q,OAAO,CAAC6Q,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC9Q,OAAO,CAAC0L,GAAG,IAAI;cAC7C9G,GAAG,CAAChC,IAAI,CAACzB,IAAI,CAACf,EAAE,IAAIA,EAAE,CAAC+L,SAAS,CAAC8C,YAAY,KAAK2B,MAAM,CAACC,IAAI,CAACnF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGwI,QAAQ,CAAC9S,IAAI,CAAAS,aAAA,CAAAA,aAAA,KAAMzB,EAAE;gBAAEmU,QAAQ,EAAE;cAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACzH,CAAC,CAAC;UACN,CAAC,CAAC;UACF,IAAI,CAAC5V,QAAQ,CAAC;YAAE+H,QAAQ,EAAEwN,QAAQ;YAAE9M,aAAa,EAAE,IAAI;YAAEM,MAAM,EAAEA,MAAM;YAAEC,oBAAoB,EAAEA;UAAqB,CAAC,CAAC;QAC1H,CAAC,CAAC,CACD5C,KAAK,CAAEC,CAAC,IAAK;UAAA,IAAAwP,YAAA,EAAAC,YAAA;UACV9Q,OAAO,CAACC,GAAG,CAACoB,CAAC,CAAC;UACd,IAAI,CAACvF,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,6GAAAyE,MAAA,CAA0G,EAAAkQ,YAAA,GAAAxP,CAAC,CAACU,QAAQ,cAAA8O,YAAA,uBAAVA,YAAA,CAAY5R,IAAI,MAAKzC,SAAS,IAAAsU,YAAA,GAAGzP,CAAC,CAACU,QAAQ,cAAA+O,YAAA,uBAAVA,YAAA,CAAY7R,IAAI,GAAGoC,CAAC,CAACW,OAAO,CAAE;YAC/K7F,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV,CAAC,MAAM;QACH,IAAI,CAACL,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,EAAE,wDAAwD;UAChEC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACL,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,qDAAqD;QAC7DC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAyK,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC5L,QAAQ,CAAC;MAAEyI,aAAa,EAAE;IAAM,CAAC,CAAC;EAC3C;EACA;EACAoD,oBAAoBA,CAACrH,OAAO,EAAE;IAC1B,oBACIzF,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,gBACX5P,OAAA;QAAM6P,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAE7R,QAAQ,CAACsU;MAAK;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvDxK,OAAO,CAACoR,QAAQ;IAAA;MAAA/G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACA;EACA+G,cAAcA,CAAC7M,OAAO,EAAE;IACpB,oBAAOnK,OAAA,CAACxB,WAAW;MAACyK,KAAK,EAAEkB,OAAO,CAACsK,OAAO,CAAC,UAAU,CAAE;MAACY,aAAa,EAAG/N,CAAC,IAAK,IAAI,CAAC2F,sBAAsB,CAAC3F,CAAC,EAAE6C,OAAO;IAAE;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAE7H;EACAhD,sBAAsBA,CAAC3F,CAAC,EAAE6C,OAAO,EAAE;IAC/BA,OAAO,CAACsK,OAAO,CAACoC,QAAQ,GAAGvP,CAAC,CAAC2B,KAAK;EACtC;EACAiE,sBAAsBA,CAAC5F,CAAC,EAAE;IACtB,IAAI2P,MAAM,GAAG3P,CAAC,CAAC2B,KAAK,CAACgO,MAAM,CAAC1U,OAAO,IAAIA,OAAO,CAACsU,QAAQ,GAAG,CAAC,CAAC;IAC5D,IAAII,MAAM,CAACxV,MAAM,GAAG6F,CAAC,CAAC2B,KAAK,CAACxH,MAAM,EAAE;MAChC,IAAI,CAACM,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,qEAAqE;QAC7EC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI8U,QAAQ,GAAG,CAAC;MAChB,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,SAAS,GAAG,EAAE;MAClBF,UAAU,GAAG,IAAI,CAAC5V,KAAK,CAACc,QAAQ,CAACyL,GAAG,CAACpL,EAAE,IAAIA,EAAE,CAACG,QAAQ,CAAC,CAAC6T,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC;MAC9F,IAAInT,IAAI,GAAG,IAAI,CAAClC,KAAK,CAAC0I,oBAAoB,CAACxG,IAAI,CAACf,EAAE,IAAI4B,QAAQ,CAAC5B,EAAE,CAAC2L,UAAU,CAAC,IAAI8I,UAAU,CAAC;MAC5F,IAAI1T,IAAI,KAAKhB,SAAS,EAAE;QACpB2U,OAAO,GAAG9S,QAAQ,CAAC4O,MAAM,CAACC,IAAI,CAAC1P,IAAI,CAAC2P,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAChDiE,SAAS,GAAGnE,MAAM,CAACI,MAAM,CAAC7P,IAAI,CAAC2P,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C6D,MAAM,CAAC3U,OAAO,CAACI,EAAE,IAAI;UACjB,IAAI4U,OAAO,GAAGD,SAAS,CAAC5T,IAAI,CAACuP,IAAI,IAAIE,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKtQ,EAAE,CAAC+L,SAAS,CAAC8C,YAAY,CAAC;UACxF,IAAI2B,MAAM,CAACI,MAAM,CAACgE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAIpE,MAAM,CAACI,MAAM,CAACgE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG5U,EAAE,CAACmU,QAAQ,EAAE;YAC7E,IAAI,CAAC9U,KAAK,CAACC,IAAI,CAAC;cACZC,QAAQ,EAAE,MAAM;cAChBC,OAAO,EAAE,aAAa;cACtBC,MAAM,EAAE,oFAAoF;cAC5FC,IAAI,EAAE;YACV,CAAC,CAAC;UACN,CAAC,MAAM;YACH,IAAImV,KAAK,GAAG7U,EAAE,CAAC+L,SAAS,CAACjM,kBAAkB,CAACiB,IAAI,CAACuK,GAAG,IAAIA,GAAG,CAAC3M,WAAW,KAAK,CAAC,CAAC;YAC9EqB,EAAE,CAACC,cAAc,GAAG4U,KAAK,KAAK9U,SAAS,GAAG8U,KAAK,CAAClW,WAAW,GAAGqB,EAAE,CAAC+L,SAAS,CAACjM,kBAAkB,CAAC,CAAC,CAAC,CAACnB,WAAW;YAC5G6V,QAAQ,IAAIxU,EAAE,CAACmU,QAAQ;UAC3B;QACJ,CAAC,CAAC;QACF,IAAIK,QAAQ,GAAGE,OAAO,EAAE;UACpB,IAAI,CAACrV,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,gEAAgE;YACxEC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACnB,QAAQ,CAAC;YAAEqI,gBAAgB,EAAE2N;UAAO,CAAC,CAAC;QAC/C;MACJ,CAAC,MAAM;QACH,IAAI,CAAClV,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,aAAa;UACtBC,MAAM,EAAE,wIAAwI;UAChJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;IACJ;EACJ;EACAiL,wBAAwBA,CAAC/F,CAAC,EAAE;IACxB,IAAI,CAACrG,QAAQ,CAAC;MAAEoI,0BAA0B,EAAE/B,CAAC,CAAC2B;IAAM,CAAC,CAAC;EAC1D;EACA;EACA,MAAMkE,eAAeA,CAAA,EAAG;IACpBlH,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3E,KAAK,CAAC+H,gBAAgB,EAAE,IAAI,CAAC/H,KAAK,CAAC8H,0BAA0B,CAAC;IAC/E,IAAImO,SAAS,GAAG,CAAC,GAAG,IAAI,CAACjW,KAAK,CAACc,QAAQ,CAAC;IACxC,IAAI,CAACd,KAAK,CAAC+H,gBAAgB,CAAChH,OAAO,CAACC,OAAO,IAAI;MAC3C;MACA,IAAIgV,KAAK,GAAGC,SAAS,CAAC/T,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACnC,EAAE,KAAKgC,OAAO,CAAChC,EAAE,CAAC;MACtD,IAAIgX,KAAK,KAAK9U,SAAS,EAAE;QACrB8U,KAAK,CAACnT,MAAM,IAAI7B,OAAO,CAACsU,QAAQ;QAChCU,KAAK,CAAC1U,QAAQ,GAAG0U,KAAK,CAACnT,MAAM,GAAGE,QAAQ,CAACiT,KAAK,CAAC5U,cAAc,CAAC;MAClE,CAAC,MAAM;QACHJ,OAAO,CAACsM,SAAS,GAAGtM,OAAO,CAACW,OAAO;QACnCX,OAAO,CAAC1B,UAAU,GAAG0B,OAAO,CAACkM,SAAS;QACtClM,OAAO,CAACuM,iBAAiB,GAAG,CAAC;QAC7BvM,OAAO,CAACwM,aAAa,GAAG,CAAC;QACzB,IAAI0I,IAAI,GAAGlV,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,CAACiB,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACgV,OAAO,KAAK,IAAI,CAAC;QAChF,IAAID,IAAI,EAAE;UACNlV,OAAO,CAACI,cAAc,GAAG8U,IAAI,CAACpW,WAAW;QAC7C,CAAC,MAAM;UACHkB,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAAC1B,UAAU,CAAC2B,kBAAkB,CAAC,CAAC,CAAC,CAACnB,WAAW;QACjF;QACAkB,OAAO,CAAC6B,MAAM,GAAG7B,OAAO,CAACsU,QAAQ;QACjCtU,OAAO,CAACM,QAAQ,GAAGN,OAAO,CAAC6B,MAAM,GAAG7B,OAAO,CAACI,cAAc;QAC1D6U,SAAS,GAAG,CAAC,GAAGA,SAAS,EAAEjV,OAAO,CAAC;MACvC;IACJ,CAAC,CAAC;IACF,IAAI,CAAChB,KAAK,CAAC8H,0BAA0B,CAAC/G,OAAO,CAACI,EAAE,IAAI;MAChD,IAAIe,IAAI,GAAG+T,SAAS,CAAC/T,IAAI,CAACuK,GAAG,IAAIA,GAAG,CAACzN,EAAE,KAAKmC,EAAE,CAACnC,EAAE,CAAC;MAClD,IAAIkD,IAAI,KAAKhB,SAAS,EAAE;QACpBgB,IAAI,CAACP,OAAO,GAAGO,IAAI,CAACc,MAAM,GAAGd,IAAI,CAACZ,QAAQ;MAC9C;IACJ,CAAC,CAAC;IACFoD,OAAO,CAACC,GAAG,CAACsR,SAAS,CAAC;IACtB,IAAI,CAACvW,QAAQ,CAAC;MAAEoB,QAAQ,EAAEmV,SAAS;MAAE9N,aAAa,EAAE;IAAM,CAAC,CAAC;IAC5D,IAAI,CAAC+B,SAAS,CAAC,CAAC;EACpB;EACA2B,cAAcA,CAAC9F,CAAC,EAAE;IACd,IAAI,CAACrG,QAAQ,CAAC;MAAE4C,OAAO,EAAEyD,CAAC,CAACtB;IAAQ,CAAC,CAAC;IACrC,IAAI2R,IAAI,GAAG,CAAC,GAAG,IAAI,CAACpW,KAAK,CAACc,QAAQ,CAAC;IACnC,IAAI2M,UAAU,GAAGrN,IAAI,CAACC,KAAK,CAACkF,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,CAAC;IACzD,IAAIwF,CAAC,CAACtB,OAAO,EAAE;MACX2R,IAAI,CAACrV,OAAO,CAACI,EAAE,IAAI;QACfA,EAAE,CAAC7B,UAAU,CAAC+W,OAAO,GAAGlV,EAAE,CAAC7B,UAAU,CAACiD,GAAG;QACzCpB,EAAE,CAAC7B,UAAU,CAACiD,GAAG,GAAG,GAAG;MAC3B,CAAC,CAAC;MACFkL,UAAU,CAAC1M,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAIA,OAAO,CAACkM,SAAS,EAAE;UACnBlM,OAAO,CAACkM,SAAS,CAACmJ,OAAO,GAAGrV,OAAO,CAACkM,SAAS,CAAC3K,GAAG;UACjDvB,OAAO,CAACkM,SAAS,CAAC3K,GAAG,GAAG,GAAG;QAC/B,CAAC,MAAM;UACHvB,OAAO,CAAC1B,UAAU,CAAC+W,OAAO,GAAGrV,OAAO,CAAC1B,UAAU,CAACiD,GAAG;UACnDvB,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAG,GAAG;QAChC;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH6T,IAAI,CAACrV,OAAO,CAACC,OAAO,IAAI;QACpBA,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAGvB,OAAO,CAAC1B,UAAU,CAAC+W,OAAO;QACnD,OAAOrV,OAAO,CAAC1B,UAAU,CAAC+W,OAAO;MACrC,CAAC,CAAC;MACF5I,UAAU,CAAC1M,OAAO,CAACC,OAAO,IAAI;QAC1B0D,OAAO,CAACC,GAAG,CAAC3D,OAAO,CAAC;QACpB,IAAIA,OAAO,CAACkM,SAAS,EAAE;UACnBlM,OAAO,CAACkM,SAAS,CAAC3K,GAAG,GAAGvB,OAAO,CAACkM,SAAS,CAACmJ,OAAO;UACjD,OAAOrV,OAAO,CAACkM,SAAS,CAACmJ,OAAO;QACpC,CAAC,MAAM;UACHrV,OAAO,CAAC1B,UAAU,CAACiD,GAAG,GAAGvB,OAAO,CAAC1B,UAAU,CAAC+W,OAAO;UACnD,OAAOrV,OAAO,CAAC1B,UAAU,CAAC+W,OAAO;QACrC;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC3W,QAAQ,CAAC;MACVoB,QAAQ,EAAEsV;IACd,CAAC,CAAC;IACF7Q,YAAY,CAACK,OAAO,CAAC,MAAM,EAAExF,IAAI,CAAC+L,SAAS,CAACsB,UAAU,CAAC,CAAC;IACxD,IAAI,CAACvD,SAAS,CAAC,CAAC;EACpB;EACAoM,MAAMA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,kBAAA;IACL;IACA;AACR;AACA;AACA;AACA;IACQ;IACA,MAAMC,mBAAmB,gBACrBhY,OAAA,CAACrC,KAAK,CAACsC,QAAQ;MAAA2P,QAAA,eACX5P,OAAA,CAAClC,MAAM;QAAC+R,SAAS,EAAC,eAAe;QAAC6E,OAAO,EAAE,IAAI,CAAC7H,eAAgB;QAAA+C,QAAA,GAAE,GAAC,EAAC7R,QAAQ,CAACka,MAAM,EAAC,GAAC;MAAA;QAAAnI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CACnB;IACD,MAAMkE,MAAM,gBACRnU,OAAA;MAAK6P,SAAS,EAAC,gBAAgB;MAAAD,QAAA,eAC3B5P,OAAA;QAAK6P,SAAS,EAAC,2DAA2D;QAAAD,QAAA,eACtE5P,OAAA;UAAK6P,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eACzC5P,OAAA;YAAM6P,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAC/C5P,OAAA;cAAG6P,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCjQ,OAAA,CAACnB,SAAS;cAACgR,SAAS,EAAC,OAAO;cAACvJ,IAAI,EAAC,QAAQ;cAAC4R,OAAO,EAAG5Q,CAAC,IAAK,IAAI,CAACrG,QAAQ,CAAC;gBAAEkI,YAAY,EAAE7B,CAAC,CAACqK,MAAM,CAAC1I;cAAM,CAAC,CAAE;cAAC0M,WAAW,EAAC;YAAU;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD,MAAMkI,OAAO,gBACTnY,OAAA;MAAK6P,SAAS,EAAC,gBAAgB;MAAAD,QAAA,eAC3B5P,OAAA;QAAK6P,SAAS,EAAC,2DAA2D;QAAAD,QAAA,eACtE5P,OAAA;UAAK6P,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eACzC5P,OAAA;YAAM6P,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAC/C5P,OAAA;cAAG6P,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCjQ,OAAA,CAACnB,SAAS;cAACgR,SAAS,EAAC,OAAO;cAACvJ,IAAI,EAAC,QAAQ;cAAC4R,OAAO,EAAG5Q,CAAC,IAAK,IAAI,CAACrG,QAAQ,CAAC;gBAAEmI,aAAa,EAAE9B,CAAC,CAACqK,MAAM,CAAC1I;cAAM,CAAC,CAAE;cAAC0M,WAAW,EAAC;YAAU;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD,oBACIjQ,OAAA;MAAK6P,SAAS,EAAC,2CAA2C;MAAAD,QAAA,gBACtD5P,OAAA,CAACzB,KAAK;QAAC6Z,GAAG,EAAG1V,EAAE,IAAK,IAAI,CAACX,KAAK,GAAGW;MAAG;QAAAoN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCjQ,OAAA;QAAK6P,SAAS,EAAC,6BAA6B;QAAAD,QAAA,eACxC5P,OAAA;UAAK6P,SAAS,EAAC,QAAQ;UAAAD,QAAA,GAEjBtM,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,gBAC5EY,OAAA;YAAI6P,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAE7R,QAAQ,CAACsa;UAAM;YAAAvI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAC9DjQ,OAAA;YAAI6P,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAE7R,QAAQ,CAACua;UAAO;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAEvEjQ,OAAA;YAAK6P,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACrB5P,OAAA;cAAK6P,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eACzC5P,OAAA;gBAAI6P,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACtB5P,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAACoS;kBAAI;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAACxF,gBAAgB,CAAC,CAAC;gBAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnIjQ,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAACwa;kBAAG;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAACrF,eAAe,CAAC,CAAC;gBAAA;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtIjQ,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAAC2C;kBAAI;oBAAAoP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAACtF,gBAAgB,CAAC,CAAC;gBAAA;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC5I,CAAC3M,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjE,oBAAoB,IAAI,IAAI,CAACgC,KAAK,CAACG,IAAI,KAAKtC,KAAK,kBAC5EY,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAAC4H;kBAAY;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAAC5D,mBAAmB,CAAC,CAAC;gBAAA;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjQ,OAAA;cAAK6P,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC5B5P,OAAA;gBAAI6P,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACtB5P,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAACya;kBAAS;oBAAA1I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAAClF,mBAAmB,CAAC,CAAC;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJjQ,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAAC0a;kBAAK;oBAAA3I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAACpF,gBAAgB,CAAC,CAAC;gBAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1IjQ,OAAA;kBAAI6P,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAAC5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAAAjQ,OAAA;oBAAA4P,QAAA,EAAS7R,QAAQ,CAAC2a;kBAAO;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,MAAE,EAAC,IAAI,CAACnF,eAAe,CAAC,CAAC;gBAAA;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC,eACNjQ,OAAA;YAAK6P,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAC5C5P,OAAA;cAAK6P,SAAS,EAAC,QAAQ;cAAAD,QAAA,gBACnB5P,OAAA;gBAAA8P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjQ,OAAA;gBAAK6P,SAAS,EAAC,mEAAmE;gBAAAD,QAAA,gBAC9E5P,OAAA;kBAAI6P,SAAS,EAAC,2BAA2B;kBAAAD,QAAA,EAAE7R,QAAQ,CAAC4a;gBAAQ;kBAAA7I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAEjEnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAI,CAACuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,iBACxI9B,OAAA,CAAChC,SAAS;kBAAC6R,SAAS,EAAC,wDAAwD;kBAACuI,GAAG,EAAG1V,EAAE,IAAK,IAAI,CAACkW,EAAE,GAAGlW,EAAG;kBAACuG,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACc,QAAS;kBAChIwW,OAAO,EAAC,IAAI;kBAACC,QAAQ,EAAC,KAAK;kBAAC9L,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;kBAAC+L,gBAAgB,EAAC,QAAQ;kBAACC,UAAU,EAAC,MAAM;kBAACC,YAAY,EAAC,GAAG;kBAAArJ,QAAA,gBAEnI5P,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,OAAO;oBAACzU,IAAI,EAAE,IAAI,CAAC+G;kBAAkB;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC7DjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,aAAa;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACob,QAAS;oBAAC1U,IAAI,EAAE,IAAI,CAACiG,iBAAkB;oBAAC0O,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxGjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,cAAc;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACuT,MAAO;oBAAC7M,IAAI,EAAE,IAAI,CAACuG,wBAAyB;oBAACoO,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9GjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,UAAU;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACoU,OAAQ;oBAAC1N,IAAI,EAAE,IAAI,CAACmH,mBAAoB;oBAACyN,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAACoL,aAAa,CAACpL,OAAO,CAAE;oBAACiP,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxJjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,SAAS;oBAAC/E,MAAM,EAAC,mBAAmB;oBAAC1P,IAAI,EAAE,IAAI,CAAC8G,kBAAmB;oBAAC6N,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACrGjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,UAAU;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACsU,KAAM;oBAAC5N,IAAI,EAAE,IAAI,CAACoH,iBAAkB;oBAACwN,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAAC4C,WAAW,CAAC5C,OAAO,CAAE;oBAACiP,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClJjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,UAAU;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACqS,QAAS;oBAAC3L,IAAI,EAAE,IAAI,CAAC2G,oBAAqB;oBAACgO,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxGjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,OAAO;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACsS,MAAO;oBAAC5L,IAAI,EAAE,IAAI,CAAC4G,iBAAkB;oBAAC+N,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChGjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,QAAQ;oBAAC/E,MAAM,EAAEpW,QAAQ,CAAC6S,GAAI;oBAACnM,IAAI,EAAE,IAAI,CAAC6G,kBAAmB;oBAAC8N,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/FjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,KAAK;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACmM,GAAI;oBAACzF,IAAI,EAAE,IAAI,CAAC0G,eAAgB;oBAACiO,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACzFjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,QAAQ;oBAAC/E,MAAM,EAAEpW,QAAQ,CAAC+S,MAAO;oBAACrM,IAAI,EAAE,IAAI,CAACqH,kBAAmB;oBAACsN,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClGjQ,OAAA,CAAC/B,MAAM;oBAACqb,SAAS;oBAACC,WAAW,EAAE;sBAAExD,KAAK,EAAE,KAAK;sBAAEyD,QAAQ,EAAE;oBAAO,CAAE;oBAACC,SAAS,EAAE;sBAAEC,SAAS,EAAE;oBAAS;kBAAE;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACjHjQ,OAAA,CAAC/B,MAAM;oBAACwG,IAAI,EAAE,IAAI,CAAC8F;kBAAmB;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EAEf,CAACnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,kBACxI9B,OAAA,CAAAE,SAAA;kBAAA0P,QAAA,gBACI5P,OAAA;oBAAK6P,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,eAC5C5P,OAAA,CAAClC,MAAM;sBAAC+R,SAAS,EAAC,qBAAqB;sBAAC6E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9H,WAAW,CAAC,CAAE;sBAAAgD,QAAA,gBAAC5P,OAAA;wBAAG6P,SAAS,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,6BAAyB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/I,CAAC,eACNjQ,OAAA,CAAChC,SAAS;oBAAC6R,SAAS,EAAC,wDAAwD;oBAACuI,GAAG,EAAG1V,EAAE,IAAK,IAAI,CAACkW,EAAE,GAAGlW,EAAG;oBAACuG,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACc,QAAS;oBAChIwW,OAAO,EAAC,IAAI;oBAACC,QAAQ,EAAC,KAAK;oBAAC9L,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;oBAAC+L,gBAAgB,EAAC,QAAQ;oBAACC,UAAU,EAAC,MAAM;oBAACC,YAAY,EAAC,GAAG;oBAAArJ,QAAA,gBAEnI5P,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,OAAO;sBAACzU,IAAI,EAAE,IAAI,CAAC+G;oBAAkB;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC7DjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,aAAa;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACob,QAAS;sBAAC1U,IAAI,EAAE,IAAI,CAACiG,iBAAkB;sBAAC0O,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACxGjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,cAAc;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACuT,MAAO;sBAAC7M,IAAI,EAAE,IAAI,CAACuG,wBAAyB;sBAACoO,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC9GjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,UAAU;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACoU,OAAQ;sBAAC1N,IAAI,EAAE,IAAI,CAACmH,mBAAoB;sBAACyN,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAACoL,aAAa,CAACpL,OAAO,CAAE;sBAACiP,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACxJjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,UAAU;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACsU,KAAM;sBAAC5N,IAAI,EAAE,IAAI,CAACoH,iBAAkB;sBAACwN,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAAC4C,WAAW,CAAC5C,OAAO,CAAE;sBAACiP,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClJjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,UAAU;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACqS,QAAS;sBAAC3L,IAAI,EAAE,IAAI,CAAC2G,oBAAqB;sBAACgO,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACxGjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,OAAO;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACsS,MAAO;sBAAC5L,IAAI,EAAE,IAAI,CAAC4G,iBAAkB;sBAAC+N,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAChGjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,iBAAiB;sBAAC/E,MAAM,EAAEpW,QAAQ,CAAC2U,YAAa;sBAACjO,IAAI,EAAE,IAAI,CAACyH,eAAgB;sBAACkN,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC9GjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,kBAAkB;sBAAC/E,MAAM,EAAEpW,QAAQ,CAAC6V,OAAQ;sBAACnP,IAAI,EAAE,IAAI,CAAC0H,gBAAiB;sBAACiN,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC3GjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,sBAAsB;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACwV,kBAAmB;sBAAC9O,IAAI,EAAE,IAAI,CAACwF,oBAAqB;sBAACmP,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC9HjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,QAAQ;sBAAC/E,MAAM,EAAEpW,QAAQ,CAAC6S,GAAI;sBAACnM,IAAI,EAAE,IAAI,CAAC6G,kBAAmB;sBAAC8N,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC/FjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,eAAe;sBAAC/E,MAAM,EAAEpW,QAAQ,CAAC8S,IAAK;sBAACpM,IAAI,EAAE,IAAI,CAAC2H,gBAAiB;sBAACgN,QAAQ;oBAAA;sBAAAtJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACrGjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,mBAAmB;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACuU,oBAAqB;sBAAC7N,IAAI,EAAE,IAAI,CAAC8H,6BAA8B;sBAAC8M,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAACsC,gBAAgB,CAACtC,OAAO;oBAAE;sBAAA2F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACnLjQ,OAAA,CAAC/B,MAAM;sBAACib,KAAK,EAAC,eAAe;sBAAC/E,MAAM,EAAEpW,QAAQ,CAACwU,gBAAiB;sBAAC9N,IAAI,EAAE,IAAI,CAAC+H,yBAA0B;sBAAC6M,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAACuC,eAAe,CAACvC,OAAO;oBAAE;sBAAA2F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACrKjQ,OAAA,CAAC/B,MAAM;sBAACqb,SAAS;sBAACC,WAAW,EAAE;wBAAExD,KAAK,EAAE,KAAK;wBAAEyD,QAAQ,EAAE;sBAAO,CAAE;sBAACC,SAAS,EAAE;wBAAEC,SAAS,EAAE;sBAAS;oBAAE;sBAAA5J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACjHjQ,OAAA,CAAC/B,MAAM;sBAACwG,IAAI,EAAE,IAAI,CAAC8F;oBAAmB;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA,eACd,CAAC,eAEPjQ,OAAA;kBAAA8P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjQ,OAAA;cAAK6P,SAAS,EAAC,8DAA8D;cAAAD,QAAA,GACxE,CAAC9I,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,kBACxI9B,OAAA;gBAAK6P,SAAS,EAAC,oDAAoD;gBAAAD,QAAA,gBAC/D5P,OAAA,CAACvB,QAAQ;kBAACoR,SAAS,EAAC,MAAM;kBAAC5G,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAAC0H,KAAM;kBAACkB,OAAO,EAAE,IAAI,CAACA,OAAQ;kBAAC2L,QAAQ,EAAGxO,CAAC,IAAK,IAAI,CAACwC,gBAAgB,CAACxC,CAAC,CAAE;kBAACsO,WAAW,EAAC,MAAM;kBAACC,WAAW,EAAC,OAAO;kBAACF,WAAW,EAAC;gBAAa;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxL,IAAI,CAAC1O,KAAK,CAAC0H,KAAK,IAAI,IAAI,CAAC1H,KAAK,CAACuI,gBAAgB,KAAK,QAAQ,iBACzD9J,OAAA;kBAAK6P,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBACjB5P,OAAA,CAACxB,WAAW;oBAACyK,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAAC2H,MAAO;oBAACmM,aAAa,EAAG/N,CAAC,IAAK,IAAI,CAACrG,QAAQ,CAAC;sBAAEiI,MAAM,EAAE5B,CAAC,CAAC2B;oBAAM,CAAC,CAAE;oBAACqM,MAAM,EAAE,IAAI,CAAC/T,KAAK,CAAC0H,KAAK,KAAK,mBAAmB,GAAG,GAAG,GAAG;kBAAI;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjKjQ,OAAA,CAAClC,MAAM;oBAAC+R,SAAS,EAAC,sCAAsC;oBAAC6E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC/H,mBAAmB,CAAC,CAAE;oBAAAiD,QAAA,eAAC5P,OAAA;sBAAG6P,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClJ,CAAC,EAET,CAACnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,kBACrFY,OAAA;kBAAK6P,SAAS,EAAC,gDAAgD;kBAAAD,QAAA,gBAC3D5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACJjQ,OAAA;oBAAO2Z,OAAO,EAAC,QAAQ;oBAAC9J,SAAS,EAAC,WAAW;oBAAAD,QAAA,eAAC5P,OAAA;sBAAA4P,QAAA,EAAS7R,QAAQ,CAAC6b;oBAAY;sBAAA9J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9FjQ,OAAA,CAACpB,QAAQ;oBAACib,OAAO,EAAC,QAAQ;oBAAC7T,OAAO,EAAE,IAAI,CAACzE,KAAK,CAACsC,OAAQ;oBAACiS,QAAQ,EAAExO,CAAC,IAAI,IAAI,CAAC8F,cAAc,CAAC9F,CAAC;kBAAE;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CAAC,eAEVjQ,OAAA;gBAAM6P,SAAS,EAAC,MAAM;gBAAAD,QAAA,GAAC,GAAC,eAAA5P,OAAA;kBAAA4P,QAAA,EAAI7R,QAAQ,CAAC6S;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAAC,IAAI,CAAC1O,KAAK,CAACoC,KAAK,EAAC,GAAC;cAAA;gBAAAmM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvE,IAAI,CAAC1O,KAAK,CAACsC,OAAO,gBAEX7D,OAAA;gBAAM6P,SAAS,EAAC,MAAM;gBAAAD,QAAA,eAAC5P,OAAA;kBAAA4P,QAAA,GAAK,GAAC,eAAA5P,OAAA;oBAAG6P,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE7R,QAAQ,CAACmM;kBAAG;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,KAAC,EAAC6J,KAAK,CAAClX,UAAU,CAAC,IAAI,CAACrB,KAAK,CAAC2I,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC3I,KAAK,CAAC2I,GAAG,EAAC,GAAC;gBAAA;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEjJjQ,OAAA;gBAAM6P,SAAS,EAAC,MAAM;gBAAAD,QAAA,GAAC,GAAC,eAAA5P,OAAA;kBAAG6P,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAE7R,QAAQ,CAACmM;gBAAG;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAAC6J,KAAK,CAAClX,UAAU,CAAC,IAAI,CAACrB,KAAK,CAAC2I,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC3I,KAAK,CAAC2I,GAAG,EAAC,GAAC;cAAA;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACxI,eAELjQ,OAAA;gBAAM6P,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAAC5P,OAAA;kBAAG6P,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAE7R,QAAQ,CAAC+S;gBAAM;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAAC6J,KAAK,CAAClX,UAAU,CAAC,IAAI,CAACrB,KAAK,CAAC2I,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC3I,KAAK,CAACmF,MAAM,EAAC,GAAC;cAAA;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNjQ,OAAA;YAAK6P,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAC5C5P,OAAA;cAAI6P,SAAS,EAAC,kCAAkC;cAAAD,QAAA,EAAG7R,QAAQ,CAACgc;YAAQ;cAAAjK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1EjQ,OAAA;cAAK6P,SAAS,EAAC,sCAAsC;cAAAD,QAAA,eACjD5P,OAAA;gBAAK6P,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,eAChC5P,OAAA;kBAAI6P,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBAEtB5P,OAAA;oBAAI6P,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC3B5P,OAAA;sBAAK6P,SAAS,EAAC,KAAK;sBAAAD,QAAA,GACf9I,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAI,CAACuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,iBACxI9B,OAAA;wBAAK6P,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,eACzC5P,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,gBAChB5P,OAAA;4BAAK6P,SAAS,EAAC,kEAAkE;4BAAAD,QAAA,gBAE7E5P,OAAA;8BAAG6P,SAAS,EAAC;4BAAiB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3B,CAAC,eACJjQ,OAAA;8BAAA4P,QAAA,EACK7R,QAAQ,CAACya;4BAAS;8BAAA1I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACf,CAAC,KACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNjQ,OAAA;4BAAK6P,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACvC5P,OAAA,CAACvB,QAAQ;8BAACwK,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACC,eAAgB;8BAAC2I,OAAO,EAAE,IAAI,CAACE,MAAO;8BAACyL,QAAQ,EAAGxO,CAAC,IAAK;gCAAE,IAAI,CAACyE,eAAe,CAACzE,CAAC,CAAC;8BAAC,CAAE;8BAACsO,WAAW,EAAC,MAAM;8BAACD,WAAW,EAAC,qBAAqB;8BAACqE,YAAY,EAAC,4FAA4F;8BAACC,QAAQ,EAAC,MAAM;8BAAChD,MAAM;4BAAA;8BAAAnH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAExS,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAEVjQ,OAAA;wBAAK6P,SAAS,EAAG/I,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAI,QAAQ,GAAG,iBAAkB;wBAAA8N,QAAA,GACvL9I,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK5C,MAAM,iBACpCc,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,gBAChB5P,OAAA;4BAAK6P,SAAS,EAAC,kEAAkE;4BAAAD,QAAA,gBAE7E5P,OAAA;8BAAG6P,SAAS,EAAC;4BAAqB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/B,CAAC,eACJjQ,OAAA;8BAAA4P,QAAA,EACK7R,QAAQ,CAACmc;4BAAI;8BAAApK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,KACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNjQ,OAAA;4BAAK6P,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,eACvC5P,OAAA,CAAClB,QAAQ;8BAAC+Q,SAAS,EAAC,QAAQ;8BAACsK,UAAU,EAAC,UAAU;8BAAC/P,OAAO,EAAE,IAAI,CAACA,OAAQ;8BAACuL,WAAW,EAAC,kBAAkB;8BAAC1M,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAAC2D,IAAK;8BAAC4Q,QAAQ,EAAGxO,CAAC,IAAK,IAAI,CAACrG,QAAQ,CAAC;gCAAEiE,IAAI,EAAEoC,CAAC,CAAC2B;8BAAM,CAAC,CAAE;8BAACmR,aAAa;8BAACC,QAAQ;4BAAA;8BAAAvK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5M,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,EAETnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK5C,MAAM,iBACpCc,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,gBAChB5P,OAAA;4BAAK6P,SAAS,EAAC,kEAAkE;4BAAAD,QAAA,gBAE7E5P,OAAA;8BAAG6P,SAAS,EAAC;4BAAiB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3B,CAAC,eACJjQ,OAAA;8BAAA4P,QAAA,EACK7R,QAAQ,CAACuc;4BAAW;8BAAAxK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,KACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNjQ,OAAA;4BAAK6P,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,eACtC5P,OAAA,CAACvB,QAAQ;8BAACwK,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAAC4E,qBAAsB;8BAACgE,OAAO,EAAE,IAAI,CAAC5I,KAAK,CAACoE,YAAa;8BAACmQ,QAAQ,EAAE,IAAI,CAAC7J,qBAAsB;8BAAC2J,WAAW,EAAC,MAAM;8BAACD,WAAW,EAAC,oCAAiC;8BAACsB,MAAM;8BAACgD,QAAQ,EAAC;4BAAM;8BAAAnK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACJ,CAACnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAIuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,kBACxI9B,OAAA;oBAAI6P,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC3B5P,OAAA;sBAAK6P,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACtB5P,OAAA;wBAAK6P,SAAS,EAAC,uEAAuE;wBAAAD,QAAA,gBAElF5P,OAAA;0BAAG6P,SAAS,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACJjQ,OAAA;0BAAA4P,QAAA,EACK7R,QAAQ,CAACuc;wBAAW;0BAAAxK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,KACb;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNjQ,OAAA;wBAAK6P,SAAS,EAAC,iCAAiC;wBAAAD,QAAA,gBAC5C5P,OAAA,CAACvB,QAAQ;0BAACwK,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAAC4E,qBAAsB;0BAACgE,OAAO,EAAE,IAAI,CAAC5I,KAAK,CAACoE,YAAa;0BAACmQ,QAAQ,EAAE,IAAI,CAAC7J,qBAAsB;0BAAC2J,WAAW,EAAC,MAAM;0BAACD,WAAW,EAAC,oCAAiC;0BAACsB,MAAM;0BAACgD,QAAQ,EAAC;wBAAM;0BAAAnK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpN,IAAI,CAAC1O,KAAK,CAACqI,eAAe,CAACnI,MAAM,GAAG,CAAC,IAClC,IAAI,CAACF,KAAK,CAACqI,eAAe,CAACkE,GAAG,CAACpL,EAAE,IAAI;0BACjC,oBACI1C,OAAA;4BAAM6P,SAAS,EAAC,KAAK;4BAAAD,QAAA,GAAE7R,QAAQ,CAACwc,SAAS,EAAC,IAAE,eAAAva,OAAA;8BAAA4P,QAAA,EAASlN,EAAE,CAACqL;4BAAiB;8BAAA+B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAS,CAAC,KAAC,EAAClS,QAAQ,CAACyc,MAAM,EAAC,IAAE,eAAAxa,OAAA;8BAAA4P,QAAA,KAAAhJ,MAAA,CAAYlE,EAAE,CAACiM,KAAK,MAAA/H,MAAA,CAAMlE,EAAE,CAACiM,KAAK,iBAAA/H,MAAA,CAASlE,EAAE,CAACgM,OAAO,MAAG;4BAAA;8BAAAoB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAW,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAE1L,CAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAERnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK/C,SAAS,IAAI4C,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,iBAC1F9B,OAAA;oBAAI6P,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC3B5P,OAAA;sBAAK6P,SAAS,EAAC,WAAW;sBAAAD,QAAA,eACtB5P,OAAA;wBAAK6P,SAAS,EAAC,qDAAqD;wBAAAD,QAAA,eAChE5P,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,eAChB5P,OAAA;4BAAK6P,SAAS,EAAC,+CAA+C;4BAAAD,QAAA,gBAC1D5P,OAAA;8BAAG6P,SAAS,EAAC;4BAAqB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/B,CAAC,eACJjQ,OAAA;8BAAO2Z,OAAO,EAAC,QAAQ;8BAAC9J,SAAS,EAAC,WAAW;8BAAAD,QAAA,eAAC5P,OAAA;gCAAA4P,QAAA,EAAS7R,QAAQ,CAAC0c;8BAAY;gCAAA3K,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAS;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC9FjQ,OAAA,CAACpB,QAAQ;8BAACib,OAAO,EAAC,QAAQ;8BAAC7T,OAAO,EAAE,IAAI,CAACzE,KAAK,CAACyE,OAAQ;8BAAC8P,QAAQ,EAAExO,CAAC,IAAI,IAAI,CAACrG,QAAQ,CAAC;gCAAE+E,OAAO,EAAEsB,CAAC,CAACtB;8BAAQ,CAAC;4BAAE;8BAAA8J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/G;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAERnJ,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAKxC,YAAY,IAAIwH,YAAY,CAAChF,OAAO,CAAC,MAAM,CAAC,KAAK1C,KAAK,IAAI,CAACuC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,iBACxI9B,OAAA;oBAAI6P,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,eAC3B5P,OAAA;sBAAK6P,SAAS,EAAC,KAAK;sBAAAD,QAAA,gBAChB5P,OAAA;wBAAK6P,SAAS,EAAC,2BAA2B;wBAAAD,QAAA,eACtC5P,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,gBAChB5P,OAAA;4BAAK6P,SAAS,EAAC,0EAA0E;4BAAAD,QAAA,gBAErF5P,OAAA;8BAAG6P,SAAS,EAAC;4BAAqB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/B,CAAC,eACJjQ,OAAA;8BAAA4P,QAAA,EACK7R,QAAQ,CAAC2c;4BAAK;8BAAA5K,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CAAC,KACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNjQ,OAAA;4BAAK6P,SAAS,EAAC,qCAAqC;4BAAAD,QAAA,eAChD5P,OAAA,CAACnB,SAAS;8BAACgR,SAAS,EAAC,MAAM;8BAAC5G,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACuE,YAAa;8BAACgQ,QAAQ,EAAGxO,CAAC,IAAK,IAAI,CAACrG,QAAQ,CAAC;gCAAE6E,YAAY,EAAEwB,CAAC,CAACqK,MAAM,CAAC1I;8BAAM,CAAC;4BAAE;8BAAA6G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/H,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNjQ,OAAA;wBAAK6P,SAAS,EAAC,qDAAqD;wBAAAD,QAAA,eAChE5P,OAAA;0BAAK6P,SAAS,EAAC,KAAK;0BAAAD,QAAA,eAChB5P,OAAA;4BAAK6P,SAAS,EAAC,+CAA+C;4BAAAD,QAAA,gBAC1D5P,OAAA,CAACpB,QAAQ;8BAACib,OAAO,EAAC,QAAQ;8BAAC7T,OAAO,EAAE,IAAI,CAACzE,KAAK,CAACyE,OAAQ;8BAAC8P,QAAQ,EAAExO,CAAC,IAAI,IAAI,CAACrG,QAAQ,CAAC;gCAAE+E,OAAO,EAAEsB,CAAC,CAACtB;8BAAQ,CAAC;4BAAE;8BAAA8J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAChHjQ,OAAA;8BAAO2Z,OAAO,EAAC,QAAQ;8BAAC9J,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAE7R,QAAQ,CAAC4c;4BAAW;8BAAA7K,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAETjQ,OAAA;oBAAI6P,SAAS,EAAC,2CAA2C;oBAAAD,QAAA,eACrD5P,OAAA;sBAAK6P,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACtB5P,OAAA;wBAAK6P,SAAS,EAAC,iEAAiE;wBAAAD,QAAA,gBAE5E5P,OAAA;0BAAG6P,SAAS,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrCjQ,OAAA;0BAAA4P,QAAA,EAAS7R,QAAQ,CAAC8S;wBAAI;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,KACpC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNjQ,OAAA;wBAAK6P,SAAS,EAAC,4BAA4B;wBAAAD,QAAA,gBACvC5P,OAAA,CAAC1B,aAAa;0BAAC+X,SAAS,EAAE,GAAI;0BAACuE,OAAO,EAAGtT,CAAC,IAAK,IAAI,CAACgF,cAAc,CAAChF,CAAC,CAAE;0BAACuI,SAAS,EAAC,WAAW;0BAAC5G,KAAK,GAAA6O,sBAAA,GAAE,IAAI,CAACvW,KAAK,CAACoD,YAAY,cAAAmT,sBAAA,uBAAvBA,sBAAA,CAAyB9S,IAAK;0BAAC8Q,QAAQ,EAAGxO,CAAC,IAAK;4BAAE,IAAI,CAAC0E,UAAU,CAAC1E,CAAC,CAAC;0BAAC;wBAAE;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9KjQ,OAAA;0BAAK6P,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eAAC5P,OAAA;4BAAA4P,QAAA,EAAO,IAAI,CAACrO,KAAK,CAACwI;0BAAG;4BAAA+F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNjQ,OAAA;YAAK6P,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC1B5P,OAAA;cAAK6P,SAAS,EAAC,QAAQ;cAAAD,QAAA,eACnB5P,OAAA;gBAAK6P,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,eAE1C5P,OAAA;kBAAK6P,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B5P,OAAA,CAAClC,MAAM;oBAAC+R,SAAS,EAAC,gBAAgB;oBAAC6E,OAAO,EAAEA,CAAA,KAAMpR,MAAM,CAACuX,OAAO,CAACC,IAAI,CAAC,CAAE;oBAAAlL,QAAA,gBAAE5P,OAAA;sBAAG6P,SAAS,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAAAjQ,OAAA;sBAAM6P,SAAS,EAAC,SAAS;sBAAAD,QAAA,GAAC,GAAC,EAAC7R,QAAQ,CAACgd,QAAQ,EAAC,GAAC;oBAAA;sBAAAjL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpLjQ,OAAA,CAAClC,MAAM;oBAAC4W,OAAO,EAAE,IAAI,CAAC1T,KAAM;oBAACE,QAAQ,EAAE,IAAI,CAACK,KAAK,CAACL,QAAS;oBAAA0O,QAAA,gBAAE5P,OAAA;sBAAM6P,SAAS,EAAC,SAAS;sBAAAD,QAAA,GAAC,GAAC,EAAC7R,QAAQ,CAACid,QAAQ,EAAC,GAAC;oBAAA;sBAAAlL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAAAjQ,OAAA;sBAAG6P,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/J;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAMNjQ,OAAA,CAACrB,MAAM;QAACsc,OAAO,EAAE,IAAI,CAAC1Z,KAAK,CAACmI,aAAc;QAACyK,MAAM,EAAEpW,QAAQ,CAACmd,cAAe;QAACC,KAAK;QAACtL,SAAS,EAAC,kBAAkB;QAACuL,MAAM,EAAEpD,mBAAoB;QAACqD,MAAM,EAAE,IAAI,CAACxO,eAAgB;QAAA+C,QAAA,eACrK5P,OAAA;UAAK6P,SAAS,EAAC,WAAW;UAAAD,QAAA,EACrB,IAAI,CAACrO,KAAK,CAACoI,UAAU,gBAClB3J,OAAA,CAAAE,SAAA;YAAA0P,QAAA,GACK,IAAI,CAACrO,KAAK,CAACyI,MAAM,IAAA+N,kBAAA,GAAG,IAAI,CAACxW,KAAK,CAACyI,MAAM,cAAA+N,kBAAA,uBAAjBA,kBAAA,CAAmBjK,GAAG,CAAC,CAACpL,EAAE,EAAEiQ,GAAG,KAAK;cACrD,oBACI3S,OAAA,CAACrC,KAAK,CAACsC,QAAQ;gBAAA2P,QAAA,eACX5P,OAAA;kBAAK6P,SAAS,EAAC,gDAAgD;kBAAAD,QAAA,eAC3D5P,OAAA;oBAAM6P,SAAS,EAAC,EAAE;oBAAAD,QAAA,KAAAhJ,MAAA,CAAKlE,EAAE;kBAAA;oBAAAoN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC,GAHW0C,GAAG;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIR,CAAC;YAEzB,CAAC,CAAC,GAEE,IAAI,eAERjQ,OAAA;cAAK6P,SAAS,EAAC,qBAAqB;cAAAD,QAAA,eAChC5P,OAAA;gBAAK6P,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,eACpD5P,OAAA,CAAChC,SAAS;kBACN6R,SAAS,EAAC,mDAAmD;kBAC7D5G,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACyH,QAAS;kBAC3BG,YAAY,EAAE,IAAI,CAAC5H,KAAK,CAAC4H,YAAa;kBACtCgL,MAAM,EAAEA,MAAO;kBACf0E,OAAO,EAAC,IAAI;kBACZC,QAAQ,EAAC,KAAK;kBACd9L,iBAAiB,EAAE,IAAI,CAACC,sBAAuB;kBAC/C8L,gBAAgB,EAAC,QAAQ;kBACzBC,UAAU,EAAC,MAAM;kBACjBsC,SAAS;kBACTC,IAAI,EAAE,EAAG;kBACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;kBACjCC,SAAS,EAAE,IAAI,CAACla,KAAK,CAAC+H,gBAAiB;kBACvCoS,iBAAiB,EAAGpU,CAAC,IAAK,IAAI,CAAC4F,sBAAsB,CAAC5F,CAAC,CAAE;kBACzD0S,YAAY,EAAC,sCAAsC;kBACnD2B,aAAa,EAAC,UAAU;kBACxB1C,YAAY,EAAC,GAAG;kBAAArJ,QAAA,gBAEhB5P,OAAA,CAAC/B,MAAM;oBAAC0d,aAAa,EAAC,UAAU;oBAACpC,WAAW,EAAE;sBAAExD,KAAK,EAAE;oBAAM;kBAAE;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1EjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,wBAAwB;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACuT,MAAO;oBAAC8H,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACnFjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,uBAAuB;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACob,QAAS;oBAACC,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpFjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,UAAU;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACqS,QAAS;oBAAC3L,IAAI,EAAE,IAAI,CAACqI,oBAAqB;oBAACuM,MAAM,EAAGlP,OAAO,IAAK,IAAI,CAAC6M,cAAc,CAAC7M,OAAO;kBAAE;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnJjQ,OAAA,CAAC/B,MAAM;oBAAC4R,SAAS,EAAC,iBAAiB;oBAACyJ,SAAS;oBAACC,WAAW,EAAE;sBAAExD,KAAK,EAAE;oBAAO,CAAE;oBAAC0D,SAAS,EAAE;sBAAEC,SAAS,EAAE;oBAAS;kBAAE;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjQ,OAAA;cAAK6P,SAAS,EAAC,yCAAyC;cAAAD,QAAA,eAEpD5P,OAAA,CAAClC,MAAM;gBACHyC,EAAE,EAAC,OAAO;gBACVsP,SAAS,EAAC,0EAA0E;gBACpF6E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACnT,KAAK,CAAC+H,gBAAgB,GAAG,IAAI,CAACrI,QAAQ,CAAC;kBAAE0I,UAAU,EAAE;gBAAM,CAAC,CAAC,GAAG,IAAI,CAAC5H,KAAK,CAACC,IAAI,CAAC;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,OAAO,EAAE,aAAa;kBAAEC,MAAM,EAAE,yCAAyC;kBAAEC,IAAI,EAAE;gBAAM,CAAC,CAAE;gBAAAwN,QAAA,gBAElN5P,OAAA;kBAAG6P,SAAS,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAChDlS,QAAQ,CAAC6d,OAAO;cAAA;gBAAA9L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,eACR,CAAC,gBAEHjQ,OAAA,CAAAE,SAAA;YAAA0P,QAAA,gBACI5P,OAAA;cAAK6P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC3B5P,OAAA;gBAAI6P,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAAE7R,QAAQ,CAAC8d;cAAY;gBAAA/L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnEjQ,OAAA;gBAAK6P,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,eACpD5P,OAAA,CAAChC,SAAS;kBACN6R,SAAS,EAAC,mDAAmD;kBAC7D5G,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACc,QAAS;kBAC3B8G,YAAY,EAAE,IAAI,CAAC5H,KAAK,CAAC6H,aAAc;kBACvC+K,MAAM,EAAEgE,OAAQ;kBAChBU,OAAO,EAAC,IAAI;kBACZE,gBAAgB,EAAC,QAAQ;kBACzBC,UAAU,EAAC,MAAM;kBACjBsC,SAAS;kBACTC,IAAI,EAAE,EAAG;kBACTC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;kBACjCC,SAAS,EAAE,IAAI,CAACla,KAAK,CAAC8H,0BAA2B;kBACjDqS,iBAAiB,EAAGpU,CAAC,IAAK,IAAI,CAAC+F,wBAAwB,CAAC/F,CAAC,CAAE;kBAC3D0S,YAAY,EAAC,sCAAsC;kBACnD2B,aAAa,EAAC,UAAU;kBACxB1C,YAAY,EAAC,GAAG;kBAAArJ,QAAA,gBAEhB5P,OAAA,CAAC/B,MAAM;oBAAC0d,aAAa,EAAC,UAAU;oBAACpC,WAAW,EAAE;sBAAExD,KAAK,EAAE;oBAAM;kBAAE;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1EjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,yBAAyB;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACuT,MAAO;oBAAC8H,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpFjQ,OAAA,CAAC/B,MAAM;oBAACib,KAAK,EAAC,wBAAwB;oBAAC/E,MAAM,EAAEpW,QAAQ,CAACob,QAAS;oBAACC,QAAQ;kBAAA;oBAAAtJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjQ,OAAA;cAAK6P,SAAS,EAAC,UAAU;cAAAD,QAAA,gBACrB5P,OAAA;gBAAK6P,SAAS,EAAC,mFAAmF;gBAAAD,QAAA,eAC9F5P,OAAA,CAAClC,MAAM;kBACH+R,SAAS,EAAC,8EAA8E;kBACxF6E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACzT,QAAQ,CAAC;oBAAE0I,UAAU,EAAE;kBAAK,CAAC,CAAE;kBAAAiG,QAAA,gBAEnD5P,OAAA;oBAAG6P,SAAS,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC/ClS,QAAQ,CAACgd,QAAQ;gBAAA;kBAAAjL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACNjQ,OAAA;gBAAK6P,SAAS,EAAC,wEAAwE;gBAAAD,QAAA,eACnF5P,OAAA,CAAClC,MAAM;kBACH+R,SAAS,EAAC,8EAA8E;kBACxF6E,OAAO,EAAE,IAAI,CAACvH,eAAgB;kBAAAyC,QAAA,gBAE9B5P,OAAA;oBAAG6P,SAAS,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnClS,QAAQ,CAAC+d,QAAQ;gBAAA;kBAAAhM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,eACR;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEf;AACJ;AAEA,eAAe5R,OAAO,CAAC,IAAI,EAAE;EAAED;AAAW,CAAC,CAAC,CAAC+B,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}