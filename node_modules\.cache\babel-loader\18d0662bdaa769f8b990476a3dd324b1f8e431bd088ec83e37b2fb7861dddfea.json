{"ast": null, "code": "(function (root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  } else {\n    root.Scrollparent = factory();\n  }\n})(this, function () {\n  var regex = /(auto|scroll)/;\n  var parents = function (node, ps) {\n    if (node.parentNode === null) {\n      return ps;\n    }\n    return parents(node.parentNode, ps.concat([node]));\n  };\n  var style = function (node, prop) {\n    return getComputedStyle(node, null).getPropertyValue(prop);\n  };\n  var overflow = function (node) {\n    return style(node, \"overflow\") + style(node, \"overflow-y\") + style(node, \"overflow-x\");\n  };\n  var scroll = function (node) {\n    return regex.test(overflow(node));\n  };\n  var scrollParent = function (node) {\n    if (!(node instanceof HTMLElement || node instanceof SVGElement)) {\n      return;\n    }\n    var ps = parents(node.parentNode, []);\n    for (var i = 0; i < ps.length; i += 1) {\n      if (scroll(ps[i])) {\n        return ps[i];\n      }\n    }\n    return document.scrollingElement || document.documentElement;\n  };\n  return scrollParent;\n});", "map": {"version": 3, "names": ["root", "factory", "define", "amd", "module", "exports", "Scrollparent", "regex", "parents", "node", "ps", "parentNode", "concat", "style", "prop", "getComputedStyle", "getPropertyValue", "overflow", "scroll", "test", "scrollParent", "HTMLElement", "SVGElement", "i", "length", "document", "scrollingElement", "documentElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/scrollparent/scrollparent.js"], "sourcesContent": ["(function (root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  } else {\n    root.Scrollparent = factory();\n  }\n}(this, function () {\n  var regex = /(auto|scroll)/;\n\n  var parents = function (node, ps) {\n    if (node.parentNode === null) { return ps; }\n\n    return parents(node.parentNode, ps.concat([node]));\n  };\n\n  var style = function (node, prop) {\n    return getComputedStyle(node, null).getPropertyValue(prop);\n  };\n\n  var overflow = function (node) {\n    return style(node, \"overflow\") + style(node, \"overflow-y\") + style(node, \"overflow-x\");\n  };\n\n  var scroll = function (node) {\n   return regex.test(overflow(node));\n  };\n\n  var scrollParent = function (node) {\n    if (!(node instanceof HTMLElement || node instanceof SVGElement)) {\n      return ;\n    }\n\n    var ps = parents(node.parentNode, []);\n\n    for (var i = 0; i < ps.length; i += 1) {\n      if (scroll(ps[i])) {\n        return ps[i];\n      }\n    }\n\n    return document.scrollingElement || document.documentElement;\n  };\n\n  return scrollParent;\n}));\n"], "mappings": "AAAC,WAAUA,IAAI,EAAEC,OAAO,EAAE;EACxB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC9CD,MAAM,CAAC,EAAE,EAAED,OAAO,CAAC;EACrB,CAAC,MAAM,IAAI,OAAOG,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAE;IACvDD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC5B,CAAC,MAAM;IACLD,IAAI,CAACM,YAAY,GAAGL,OAAO,CAAC,CAAC;EAC/B;AACF,CAAC,EAAC,IAAI,EAAE,YAAY;EAClB,IAAIM,KAAK,GAAG,eAAe;EAE3B,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEC,EAAE,EAAE;IAChC,IAAID,IAAI,CAACE,UAAU,KAAK,IAAI,EAAE;MAAE,OAAOD,EAAE;IAAE;IAE3C,OAAOF,OAAO,CAACC,IAAI,CAACE,UAAU,EAAED,EAAE,CAACE,MAAM,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,IAAII,KAAK,GAAG,SAAAA,CAAUJ,IAAI,EAAEK,IAAI,EAAE;IAChC,OAAOC,gBAAgB,CAACN,IAAI,EAAE,IAAI,CAAC,CAACO,gBAAgB,CAACF,IAAI,CAAC;EAC5D,CAAC;EAED,IAAIG,QAAQ,GAAG,SAAAA,CAAUR,IAAI,EAAE;IAC7B,OAAOI,KAAK,CAACJ,IAAI,EAAE,UAAU,CAAC,GAAGI,KAAK,CAACJ,IAAI,EAAE,YAAY,CAAC,GAAGI,KAAK,CAACJ,IAAI,EAAE,YAAY,CAAC;EACxF,CAAC;EAED,IAAIS,MAAM,GAAG,SAAAA,CAAUT,IAAI,EAAE;IAC5B,OAAOF,KAAK,CAACY,IAAI,CAACF,QAAQ,CAACR,IAAI,CAAC,CAAC;EAClC,CAAC;EAED,IAAIW,YAAY,GAAG,SAAAA,CAAUX,IAAI,EAAE;IACjC,IAAI,EAAEA,IAAI,YAAYY,WAAW,IAAIZ,IAAI,YAAYa,UAAU,CAAC,EAAE;MAChE;IACF;IAEA,IAAIZ,EAAE,GAAGF,OAAO,CAACC,IAAI,CAACE,UAAU,EAAE,EAAE,CAAC;IAErC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,EAAE,CAACc,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACrC,IAAIL,MAAM,CAACR,EAAE,CAACa,CAAC,CAAC,CAAC,EAAE;QACjB,OAAOb,EAAE,CAACa,CAAC,CAAC;MACd;IACF;IAEA,OAAOE,QAAQ,CAACC,gBAAgB,IAAID,QAAQ,CAACE,eAAe;EAC9D,CAAC;EAED,OAAOP,YAAY;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}