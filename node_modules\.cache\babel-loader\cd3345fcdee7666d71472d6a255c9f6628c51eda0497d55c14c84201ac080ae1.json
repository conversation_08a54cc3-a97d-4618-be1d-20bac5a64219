{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { fillRef, supportRef } from \"rc-util/es/ref\";\nimport classNames from 'classnames';\nimport { getTransitionName, supportTransition } from './util/motion';\nimport { STATUS_NONE, STEP_PREPARE, STEP_START } from './interface';\nimport useStatus from './hooks/useStatus';\nimport DomWrapper from './DomWrapper';\nimport { isActive } from './hooks/useStepQueue';\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\n\nexport function genCSSMotion(config) {\n  var transitionSupport = config;\n  if (_typeof(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props) {\n    return !!(props.motionName && transitionSupport);\n  }\n  var CSSMotion = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var supportMotion = isSupportTransition(props); // Ref to the react node, it may be a HTMLElement\n\n    var nodeRef = useRef(); // Ref to the dom wrapper in case ref can not pass to HTMLElement\n\n    var wrapperNodeRef = useRef();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : findDOMNode(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = useStatus(supportMotion, visible, getDomElement, props),\n      _useStatus2 = _slicedToArray(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3]; // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n\n    var renderedRef = React.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    } // ====================== Refs ======================\n\n    var setNodeRef = React.useCallback(function (node) {\n      nodeRef.current = node;\n      fillRef(ref, node);\n    }, [ref]); // ===================== Render =====================\n\n    var motionChildren;\n    var mergedProps = _objectSpread(_objectSpread({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === STATUS_NONE || !isSupportTransition(props)) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children(_objectSpread({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      var _classNames;\n\n      // In motion\n      var statusSuffix;\n      if (statusStep === STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if (isActive(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === STEP_START) {\n        statusSuffix = 'start';\n      }\n      motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n        className: classNames(getTransitionName(motionName, status), (_classNames = {}, _defineProperty(_classNames, getTransitionName(motionName, \"\".concat(status, \"-\").concat(statusSuffix)), statusSuffix), _defineProperty(_classNames, motionName, typeof motionName === 'string'), _classNames)),\n        style: statusStyle\n      }), setNodeRef);\n    } // Auto inject ref if child node not have `ref` props\n\n    if (/*#__PURE__*/React.isValidElement(motionChildren) && supportRef(motionChildren)) {\n      var _motionChildren = motionChildren,\n        originNodeRef = _motionChildren.ref;\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/React.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/React.createElement(DomWrapper, {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\nexport default genCSSMotion(supportTransition);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "_typeof", "React", "useRef", "findDOMNode", "fillRef", "supportRef", "classNames", "getTransitionName", "supportTransition", "STATUS_NONE", "STEP_PREPARE", "STEP_START", "useStatus", "DomWrapper", "isActive", "genCSSMotion", "config", "transitionSupport", "isSupportTransition", "props", "motionName", "CSSMotion", "forwardRef", "ref", "_props$visible", "visible", "_props$removeOnLeave", "removeOnLeave", "forceRender", "children", "leavedClassName", "eventProps", "supportMotion", "nodeRef", "wrapperNodeRef", "getDomElement", "current", "HTMLElement", "e", "_useStatus", "_useStatus2", "status", "statusStep", "statusStyle", "mergedVisible", "renderedRef", "setNodeRef", "useCallback", "node", "motion<PERSON><PERSON><PERSON><PERSON>", "mergedProps", "className", "style", "display", "_classNames", "statusSuffix", "concat", "isValidElement", "_motion<PERSON><PERSON><PERSON>n", "originNodeRef", "cloneElement", "createElement", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-motion/es/CSSMotion.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { fillRef, supportRef } from \"rc-util/es/ref\";\nimport classNames from 'classnames';\nimport { getTransitionName, supportTransition } from './util/motion';\nimport { STATUS_NONE, STEP_PREPARE, STEP_START } from './interface';\nimport useStatus from './hooks/useStatus';\nimport DomWrapper from './DomWrapper';\nimport { isActive } from './hooks/useStepQueue';\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\n\nexport function genCSSMotion(config) {\n  var transitionSupport = config;\n\n  if (_typeof(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n\n  function isSupportTransition(props) {\n    return !!(props.motionName && transitionSupport);\n  }\n\n  var CSSMotion = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n        visible = _props$visible === void 0 ? true : _props$visible,\n        _props$removeOnLeave = props.removeOnLeave,\n        removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n        forceRender = props.forceRender,\n        children = props.children,\n        motionName = props.motionName,\n        leavedClassName = props.leavedClassName,\n        eventProps = props.eventProps;\n    var supportMotion = isSupportTransition(props); // Ref to the react node, it may be a HTMLElement\n\n    var nodeRef = useRef(); // Ref to the dom wrapper in case ref can not pass to HTMLElement\n\n    var wrapperNodeRef = useRef();\n\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : findDOMNode(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n\n    var _useStatus = useStatus(supportMotion, visible, getDomElement, props),\n        _useStatus2 = _slicedToArray(_useStatus, 4),\n        status = _useStatus2[0],\n        statusStep = _useStatus2[1],\n        statusStyle = _useStatus2[2],\n        mergedVisible = _useStatus2[3]; // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n\n\n    var renderedRef = React.useRef(mergedVisible);\n\n    if (mergedVisible) {\n      renderedRef.current = true;\n    } // ====================== Refs ======================\n\n\n    var setNodeRef = React.useCallback(function (node) {\n      nodeRef.current = node;\n      fillRef(ref, node);\n    }, [ref]); // ===================== Render =====================\n\n    var motionChildren;\n\n    var mergedProps = _objectSpread(_objectSpread({}, eventProps), {}, {\n      visible: visible\n    });\n\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === STATUS_NONE || !isSupportTransition(props)) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children(_objectSpread({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      var _classNames;\n\n      // In motion\n      var statusSuffix;\n\n      if (statusStep === STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if (isActive(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === STEP_START) {\n        statusSuffix = 'start';\n      }\n\n      motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n        className: classNames(getTransitionName(motionName, status), (_classNames = {}, _defineProperty(_classNames, getTransitionName(motionName, \"\".concat(status, \"-\").concat(statusSuffix)), statusSuffix), _defineProperty(_classNames, motionName, typeof motionName === 'string'), _classNames)),\n        style: statusStyle\n      }), setNodeRef);\n    } // Auto inject ref if child node not have `ref` props\n\n\n    if ( /*#__PURE__*/React.isValidElement(motionChildren) && supportRef(motionChildren)) {\n      var _motionChildren = motionChildren,\n          originNodeRef = _motionChildren.ref;\n\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/React.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n\n    return /*#__PURE__*/React.createElement(DomWrapper, {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\nexport default genCSSMotion(supportTransition);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AACpD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,eAAe;AACpE,SAASC,WAAW,EAAEC,YAAY,EAAEC,UAAU,QAAQ,aAAa;AACnE,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,IAAIC,iBAAiB,GAAGD,MAAM;EAE9B,IAAIhB,OAAO,CAACgB,MAAM,CAAC,KAAK,QAAQ,EAAE;IAChCC,iBAAiB,GAAGD,MAAM,CAACC,iBAAiB;EAC9C;EAEA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;IAClC,OAAO,CAAC,EAAEA,KAAK,CAACC,UAAU,IAAIH,iBAAiB,CAAC;EAClD;EAEA,IAAII,SAAS,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,UAAUH,KAAK,EAAEI,GAAG,EAAE;IAClE,IAAIC,cAAc,GAAGL,KAAK,CAACM,OAAO;MAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;MAC3DE,oBAAoB,GAAGP,KAAK,CAACQ,aAAa;MAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;MAC7EE,WAAW,GAAGT,KAAK,CAACS,WAAW;MAC/BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;MACzBT,UAAU,GAAGD,KAAK,CAACC,UAAU;MAC7BU,eAAe,GAAGX,KAAK,CAACW,eAAe;MACvCC,UAAU,GAAGZ,KAAK,CAACY,UAAU;IACjC,IAAIC,aAAa,GAAGd,mBAAmB,CAACC,KAAK,CAAC,CAAC,CAAC;;IAEhD,IAAIc,OAAO,GAAG/B,MAAM,CAAC,CAAC,CAAC,CAAC;;IAExB,IAAIgC,cAAc,GAAGhC,MAAM,CAAC,CAAC;IAE7B,SAASiC,aAAaA,CAAA,EAAG;MACvB,IAAI;QACF;QACA;QACA;QACA;QACA,OAAOF,OAAO,CAACG,OAAO,YAAYC,WAAW,GAAGJ,OAAO,CAACG,OAAO,GAAGjC,WAAW,CAAC+B,cAAc,CAACE,OAAO,CAAC;MACvG,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV;QACA,OAAO,IAAI;MACb;IACF;IAEA,IAAIC,UAAU,GAAG3B,SAAS,CAACoB,aAAa,EAAEP,OAAO,EAAEU,aAAa,EAAEhB,KAAK,CAAC;MACpEqB,WAAW,GAAGzC,cAAc,CAACwC,UAAU,EAAE,CAAC,CAAC;MAC3CE,MAAM,GAAGD,WAAW,CAAC,CAAC,CAAC;MACvBE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC3BG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;MAC5BI,aAAa,GAAGJ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;;IAGA,IAAIK,WAAW,GAAG5C,KAAK,CAACC,MAAM,CAAC0C,aAAa,CAAC;IAE7C,IAAIA,aAAa,EAAE;MACjBC,WAAW,CAACT,OAAO,GAAG,IAAI;IAC5B,CAAC,CAAC;;IAGF,IAAIU,UAAU,GAAG7C,KAAK,CAAC8C,WAAW,CAAC,UAAUC,IAAI,EAAE;MACjDf,OAAO,CAACG,OAAO,GAAGY,IAAI;MACtB5C,OAAO,CAACmB,GAAG,EAAEyB,IAAI,CAAC;IACpB,CAAC,EAAE,CAACzB,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEX,IAAI0B,cAAc;IAElB,IAAIC,WAAW,GAAGpD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MACjEN,OAAO,EAAEA;IACX,CAAC,CAAC;IAEF,IAAI,CAACI,QAAQ,EAAE;MACb;MACAoB,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM,IAAIR,MAAM,KAAKhC,WAAW,IAAI,CAACS,mBAAmB,CAACC,KAAK,CAAC,EAAE;MAChE;MACA,IAAIyB,aAAa,EAAE;QACjBK,cAAc,GAAGpB,QAAQ,CAAC/B,aAAa,CAAC,CAAC,CAAC,EAAEoD,WAAW,CAAC,EAAEJ,UAAU,CAAC;MACvE,CAAC,MAAM,IAAI,CAACnB,aAAa,IAAIkB,WAAW,CAACT,OAAO,EAAE;QAChDa,cAAc,GAAGpB,QAAQ,CAAC/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1EC,SAAS,EAAErB;QACb,CAAC,CAAC,EAAEgB,UAAU,CAAC;MACjB,CAAC,MAAM,IAAIlB,WAAW,EAAE;QACtBqB,cAAc,GAAGpB,QAAQ,CAAC/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1EE,KAAK,EAAE;YACLC,OAAO,EAAE;UACX;QACF,CAAC,CAAC,EAAEP,UAAU,CAAC;MACjB,CAAC,MAAM;QACLG,cAAc,GAAG,IAAI;MACvB;IACF,CAAC,MAAM;MACL,IAAIK,WAAW;;MAEf;MACA,IAAIC,YAAY;MAEhB,IAAIb,UAAU,KAAKhC,YAAY,EAAE;QAC/B6C,YAAY,GAAG,SAAS;MAC1B,CAAC,MAAM,IAAIzC,QAAQ,CAAC4B,UAAU,CAAC,EAAE;QAC/Ba,YAAY,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAIb,UAAU,KAAK/B,UAAU,EAAE;QACpC4C,YAAY,GAAG,OAAO;MACxB;MAEAN,cAAc,GAAGpB,QAAQ,CAAC/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1EC,SAAS,EAAE7C,UAAU,CAACC,iBAAiB,CAACa,UAAU,EAAEqB,MAAM,CAAC,GAAGa,WAAW,GAAG,CAAC,CAAC,EAAEzD,eAAe,CAACyD,WAAW,EAAE/C,iBAAiB,CAACa,UAAU,EAAE,EAAE,CAACoC,MAAM,CAACf,MAAM,EAAE,GAAG,CAAC,CAACe,MAAM,CAACD,YAAY,CAAC,CAAC,EAAEA,YAAY,CAAC,EAAE1D,eAAe,CAACyD,WAAW,EAAElC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,CAAC,EAAEkC,WAAW,CAAC,CAAC;QAC/RF,KAAK,EAAET;MACT,CAAC,CAAC,EAAEG,UAAU,CAAC;IACjB,CAAC,CAAC;;IAGF,IAAK,aAAa7C,KAAK,CAACwD,cAAc,CAACR,cAAc,CAAC,IAAI5C,UAAU,CAAC4C,cAAc,CAAC,EAAE;MACpF,IAAIS,eAAe,GAAGT,cAAc;QAChCU,aAAa,GAAGD,eAAe,CAACnC,GAAG;MAEvC,IAAI,CAACoC,aAAa,EAAE;QAClBV,cAAc,GAAG,aAAahD,KAAK,CAAC2D,YAAY,CAACX,cAAc,EAAE;UAC/D1B,GAAG,EAAEuB;QACP,CAAC,CAAC;MACJ;IACF;IAEA,OAAO,aAAa7C,KAAK,CAAC4D,aAAa,CAAChD,UAAU,EAAE;MAClDU,GAAG,EAAEW;IACP,CAAC,EAAEe,cAAc,CAAC;EACpB,CAAC,CAAC;EACF5B,SAAS,CAACyC,WAAW,GAAG,WAAW;EACnC,OAAOzC,SAAS;AAClB;AACA,eAAeN,YAAY,CAACP,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}