{"version": 3, "file": "self-link.js", "sourceRoot": "", "sources": ["../../src/self-link.ts"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,IAAI,CAAA;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,MAAM,CAAA;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAGrC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAA;AAE1C,oEAAoE;AACpE,oDAAoD;AACpD,wDAAwD;AACxD,kEAAkE;AAClE,8DAA8D;AAC9D,IAAI,IAAI,GAAwB,SAAS,CAAA;AAEzC,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,EAAE;IACrC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IACzB,MAAM,CAAC,GAAG,GAAG,GAAG,eAAe,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;IAC7D,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;IACtB,CAAC;IAED,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;QACjD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAA;YACzD,IAAI,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;YACtB,CAAC;QACH,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;AACvB,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,GAAY,EAAE,KAAa,EAAE,EAAE;IAClD,MAAM,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAA;IACpC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1D,OAAM;IACR,CAAC;IACD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;IACrD,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACzB,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;IACxC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;IAC5B,IAAI,IAAI;QAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAClC,IAAI,CAAC;QACH,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IACxB,CAAC;IAAC,MAAM,CAAC;QACP,UAAU,CAAC,IAAI,CAAC,CAAA;QAChB,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC;YACH,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACtB,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,iEAAiE;YACjE,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS;gBAAE,OAAM;QAC7C,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,GAAY,EAAE,KAAa,EAAE,EAAE;IACpD,IACE,CAAC,GAAG,CAAC,IAAI;QACT,GAAG,EAAE,IAAI,EAAE,QAAQ,KAAK,KAAK;QAC7B,aAAa,CAAC,GAAG,CAAC,EAClB,CAAC;QACD,OAAM;IACR,CAAC;IACD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;IACrD,UAAU,CAAC,IAAI,CAAC,CAAA;IAChB,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC/B,IAAI,IAAI;QAAE,UAAU,CAAC,IAAI,CAAC,CAAA;AAC5B,CAAC,CAAA", "sourcesContent": ["// link the package folder into ./target/node_modules/<pkgname>\nimport { readlinkSync, symlinkSync } from 'fs'\nimport { mkdirpSync } from 'mkdirp'\nimport { dirname, relative, resolve, sep } from 'path'\nimport { rimrafSync } from 'rimraf'\nimport { walkUp } from 'walk-up-path'\nimport { Package } from './types.js'\n\nconst dirsMade = new Map<string, string>()\n\n// if the cwd is in already linked to or living within node_modules,\n// then skip the linking, because it's already done.\n// This is typically the case in a workspaces setup, and\n// creating yet *another* symlink to ourselves in src/node_modules\n// will break nx's change detection logic with an ELOOP error.\nlet inNM: boolean | undefined = undefined\n\nconst linkedAlready = (pkg: Package) => {\n  if (inNM !== undefined) {\n    return inNM\n  }\n\n  const cwd = process.cwd()\n  const p = `${sep}node_modules${sep}${pkg.name}`.toLowerCase()\n  if (cwd.toLowerCase().endsWith(p)) {\n    return (inNM = true)\n  }\n\n  for (const p of walkUp(cwd)) {\n    const link = resolve(p, 'node_modules', pkg.name)\n    try {\n      const target = resolve(dirname(link), readlinkSync(link))\n      if (relative(target, cwd) === '') {\n        return (inNM = true)\n      }\n    } catch {}\n  }\n\n  return (inNM = false)\n}\n\nexport const link = (pkg: Package, where: string) => {\n  const selfLink = pkg?.tshy?.selfLink\n  if (!pkg.name || selfLink === false || linkedAlready(pkg)) {\n    return\n  }\n  const dest = resolve(where, 'node_modules', pkg.name)\n  const dir = dirname(dest)\n  const src = relative(dir, process.cwd())\n  const made = mkdirpSync(dir)\n  if (made) dirsMade.set(dest, made)\n  try {\n    symlinkSync(src, dest)\n  } catch {\n    rimrafSync(dest)\n    let threw = true\n    try {\n      symlinkSync(src, dest)\n      threw = false\n    } finally {\n      // best effort if not set explicitly. suppress error with return.\n      if (threw && selfLink === undefined) return\n    }\n  }\n}\n\nexport const unlink = (pkg: Package, where: string) => {\n  if (\n    !pkg.name ||\n    pkg?.tshy?.selfLink === false ||\n    linkedAlready(pkg)\n  ) {\n    return\n  }\n  const dest = resolve(where, 'node_modules', pkg.name)\n  rimrafSync(dest)\n  const made = dirsMade.get(dest)\n  if (made) rimrafSync(made)\n}\n"]}