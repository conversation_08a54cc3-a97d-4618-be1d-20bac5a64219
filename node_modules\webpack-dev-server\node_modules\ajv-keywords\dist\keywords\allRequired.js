"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const allRequired_1 = __importDefault(require("../definitions/allRequired"));
const allRequired = (ajv) => ajv.addKeyword((0, allRequired_1.default)());
exports.default = allRequired;
module.exports = allRequired;
//# sourceMappingURL=allRequired.js.map