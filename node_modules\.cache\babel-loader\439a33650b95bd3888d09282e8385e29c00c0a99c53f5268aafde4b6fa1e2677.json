{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\customDataTable.jsx\";\nimport React from 'react';\nimport ScaricaCSVProva from '../common/distributore/aggiunta file/scaricaCSVProva';\nimport MenuItem from './menuItem';\nimport * as apiref from './generalizzazioni/apireq';\nimport Immagine from '../img/mktplaceholder.jpg';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { Costanti } from './traduttore/const';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Calendar } from \"primereact/calendar\";\nimport { SplitButton } from 'primereact/splitbutton';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { distributore } from './route';\nimport { OverlayPanelGen } from './generalizzazioni/overlayPanelGen';\nimport '../css/DataTableDemo.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default class CustomDataTable extends React.Component {\n  constructor(props) {\n    super(props);\n    //Converto data e ora in formato IT\n    this.deliveryDateBodyTemplate = results => {\n      if (results.deliveryDate !== null && results.deliveryDate !== undefined) {\n        // results.order_data\n        const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        }).format(new Date(datetime))).join(\" - \");\n        const output = convert(results === null || results === void 0 ? void 0 : results.deliveryDate);\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.DeliveryDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 21\n          }, this), output]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    //Converto data e ora in formato IT\n    this.orderDateBodyTemplate = results => {\n      // results.order_data\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.orderDate);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.dInserimento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 13\n      }, this);\n    };\n    //Converto data e ora in formato IT\n    this.createdAtBodyTemplate = results => {\n      // results.order_data\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.createdAt);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.dInserimento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 13\n      }, this);\n    };\n    this.date_start = results => {\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.date_start);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.ValidFrom\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 13\n      }, this);\n    };\n    this.date_end = results => {\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.date_end);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.ValidTo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 13\n      }, this);\n    };\n    //Converto data e ora in formato IT\n    this.createAtBodyTemplate = results => {\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.createAt);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.dInserimento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 13\n      }, this);\n    };\n    //Converto data e ora in formato IT\n    this.updateAtBodyTemplate = results => {\n      const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      }).format(new Date(datetime))).join(\" - \");\n      const output = convert(results.updateAt);\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.dAggiornamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 17\n        }, this), output]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 13\n      }, this);\n    };\n    //Converto data e ora in formato IT\n    this.validFromBodyTemplate = results => {\n      if (results !== undefined) {\n        // results.order_data\n        const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        }).format(new Date(datetime))).join(\" - \");\n        const output = convert(results.validFrom);\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.ValidFrom\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 21\n          }, this), output]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 17\n        }, this);\n      } else {\n        return null;\n      }\n    };\n    //Converto data e ora in formato IT\n    this.validToBodyTemplate = results => {\n      if (results !== undefined) {\n        // results.order_data\n        const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        }).format(new Date(datetime))).join(\" - \");\n        const output = convert(results.validTo);\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.ValidTo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 21\n          }, this), output]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 17\n        }, this);\n      } else {\n        return null;\n      }\n    };\n    //Metodo per ricavare il nome prodotto\n    this.isValidBodyTemplate = rowData => {\n      if (rowData.isValid !== undefined || rowData.is_valid !== undefined) {\n        if (rowData.isValid === true || rowData.is_valid === true) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Validità\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pi pi-check\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 21\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Validità\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pi pi-ban\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 21\n          }, this);\n        }\n      } else {\n        /* GET CURRENT DATE */\n        var newDate = new Date() /* .toLocaleString() */;\n        /* let year = newDate.getFullYear(); */\n        let month = newDate.getMonth() + 1;\n        let day = newDate.getDate();\n        /* newDate = newDate.slice(0, 8) */\n        if (month < 10) {\n          month = '0' + month;\n        }\n        if (day < 10) {\n          day = '0' + day;\n        }\n        newDate = newDate.getFullYear() + '-' + month + '-' + day;\n        if (rowData.validTo > newDate) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Validità\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pi pi-check\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 21\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.Validità\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pi pi-ban\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 21\n          }, this);\n        }\n      }\n    };\n    this.userBodyTemplate = results => {\n      if (results.users !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Utenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 21\n          }, this), results.users.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Utenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 21\n          }, this), \"0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.usernameBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: results.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.firstNameBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.rSociale\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: [results.firstName, \" \", results.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.addressBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 17\n        }, this), results.address]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.cityBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 17\n        }, this), results.city]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.capBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 17\n        }, this), results.cap]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.pIvaBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.pIva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 17\n        }, this), results.pIva]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.telBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Tel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 17\n        }, this), results.tel]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.emailBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 17\n        }, this), results.email]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati id per la tabella dell'anagrafica cliente\n    this.idBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 17\n        }, this), results.id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.descriptionBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Nome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 17\n        }, this), results.description]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.externalCodeBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.exCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 17\n        }, this), results.externalCode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 13\n      }, this);\n    };\n    this.exCodeBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.exCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 17\n        }, this), results.idProduct2.externalCode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.supplyingCodeBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.CodForn\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 17\n        }, this), results.supplyingCode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.eanCodeBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.eanCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 17\n        }, this), results.eanCode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.statusBodyTemplate = results => {\n      if (results.status === \"In uso\") {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Attivo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pi pi-check\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Attivo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pi pi-ban\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    this.referenteBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Referente\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: results.referente\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 13\n      }, this);\n    };\n    this.externalSystemBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.externalSystem\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 17\n        }, this), results.externalSystem]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 13\n      }, this);\n    };\n    this.externalSystemBodyTemplate2 = results => {\n      if (results.idExternalSystem !== undefined) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.externalSystem\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 21\n          }, this), results.idExternalSystem.externalSistemName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.externalSystem\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 21\n          }, this), results.externalSistemName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    //Definizione dati indirizzo per la tabella dell'anagrafica cliente\n    this.deliveryDestinationBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Destinazione\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 17\n        }, this), results.deliveryDestination]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 13\n      }, this);\n    };\n    this.paymentStatusBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.StatPag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 17\n        }, this), results.paymentStatus]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 13\n      }, this);\n    };\n    this.termsPaymentBodyTemplate = results => {\n      var _results$idRetailer;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Pagamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 17\n        }, this), (_results$idRetailer = results.idRetailer) === null || _results$idRetailer === void 0 ? void 0 : _results$idRetailer.idRegistry.paymentMetod]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 13\n      }, this);\n    };\n    this.statOrdBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.StatOrd\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 17\n        }, this), results.status]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 13\n      }, this);\n    };\n    this.statAlignBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Stato\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 17\n        }, this), results.status]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 13\n      }, this);\n    };\n    this.totBodyTemplate = results => {\n      if (this.state.role === distributore) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Tot\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results.total)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Tot\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results.total)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    this.totIvaBodyTemplate = results => {\n      if (this.state.role === distributore) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.TotTax\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results.totalTaxed)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.TotTax\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results.totalTaxed)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    this.totalPayedBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.TotPag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 17\n        }, this), parseFloat(results.totalPayed).toFixed(2) + ' €']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 13\n      }, this);\n    };\n    this.taxBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 17\n        }, this), new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(parseFloat(results.totalTaxed) - parseFloat(results.total))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 13\n      }, this);\n    };\n    this.ivaBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 17\n        }, this), results.tax]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 13\n      }, this);\n    };\n    this.prodivaBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 17\n        }, this), results.idProduct2.iva, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'ordine\n    this.firstNameOrdBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.rSociale\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: results.idRetailer.idRegistry.firstName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'ordine\n    this.firstNameAlignBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.rSociale\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: results.idRegistry.firstName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'ordine\n    this.usernameAlignBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.rSociale\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"b\", {\n          children: results.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.addressUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 17\n        }, this), results.idRegistry.address]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.cityUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 17\n        }, this), results.idRegistry.city]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 809,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.capUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 17\n        }, this), results.idRegistry.cap]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.pIvaUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.pIva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 17\n        }, this), results.idRegistry.pIva]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.telUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Tel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 17\n        }, this), results.idRegistry.tel]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    this.emailUserBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 17\n        }, this), results.idRegistry.email]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 13\n      }, this);\n    };\n    this.qta = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Quantità\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 17\n        }, this), results.qta]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 13\n      }, this);\n    };\n    this.totale = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Tot\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 17\n        }, this), new Intl.NumberFormat('de-DE', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(results.totale)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 13\n      }, this);\n    };\n    this.externalcode = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.exCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 17\n        }, this), results.externalcode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 13\n      }, this);\n    };\n    //Definizione dati stato per la tabella dell'ordine\n    this.statusAgentBodyTemplate = results => {\n      if (results.status !== undefined) {\n        if (results.status.toLowerCase() === \"bozza\") {\n          this.status = [{\n            name: 'Bozza'\n          }, {\n            name: 'Registrato'\n          }];\n        } else if (results.status.toLowerCase() === \"registrato\") {\n          /* this.status = [\n              { name: 'Registrato' }\n          ]; */\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.StatOrd\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 25\n            }, this), results.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 21\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: Costanti.StatOrd\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 25\n            }, this), results.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 21\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.StatOrd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: this.state.selectedStatus,\n            options: this.status,\n            onChange: e => this.onStatusChange(e, results),\n            optionLabel: \"name\",\n            emptyMessage: \"Nessun elemento selezionabile\",\n            placeholder: results.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    //Definizione dati nome per la tabella dell'ordine\n    this.autistaBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Autista\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 17\n        }, this), results.autista]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 967,\n        columnNumber: 13\n      }, this);\n    };\n    this.availabilityBodyTemplate = rowData => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Giacenza\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 17\n        }, this), rowData.availability]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 13\n      }, this);\n    };\n    this.unitMeasureBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Unità\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 17\n        }, this), results.unitMeasure]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 983,\n        columnNumber: 13\n      }, this);\n    };\n    this.colliBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Quantità\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 17\n        }, this), results.quantity / results.pcsXpackage]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1008,\n        columnNumber: 13\n      }, this);\n    };\n    this.quantityBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.totQta\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 17\n        }, this), results.quantity]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1016,\n        columnNumber: 13\n      }, this);\n    };\n    this.orderNumberBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.N_ord\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 17\n        }, this), results.orderNumber]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 13\n      }, this);\n    };\n    this.deliveryStatusBodyTemplate = results => {\n      var ClassColor = '';\n      if (results.deliveryStatus === 'assigned') {\n        ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary';\n      } else if (results.deliveryStatus === 'create') {\n        ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning';\n      } else if (results.deliveryStatus === 'delivered') {\n        ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success';\n      } else if (results.deliveryStatus === 'not delivered') {\n        ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger';\n      } else {\n        ClassColor = '';\n      }\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.StatCons\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: ClassColor,\n          children: results.deliveryStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1047,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 13\n      }, this);\n    };\n    this.termsPaymentBodyTemplate2 = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.TermPag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 17\n        }, this), results.termsPayment]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1055,\n        columnNumber: 13\n      }, this);\n    };\n    this.noteBodyTemplate = results => {\n      var _results$note;\n      var max = 100;\n      if (((_results$note = results.note) === null || _results$note === void 0 ? void 0 : _results$note.length) > max) {\n        results.note = results.note.substr(0, max);\n      }\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Note\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 17\n        }, this), results.note]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 13\n      }, this);\n    };\n    this.unitPriceBodyTemplate = results => {\n      if (this.state.role === distributore) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1077,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(results.unitPrice)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1084,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('de-DE', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results.unitPrice)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    this.priceBodyTemplate = results => {\n      if (this.state.role === distributore) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('it-IT', {\n            style: 'currency',\n            currency: 'EUR',\n            maximumFractionDigits: 6\n          }).format(parseFloat(results.price !== undefined ? results.price : results.sell_in))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 17\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: Costanti.Prezzo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 21\n          }, this), new Intl.NumberFormat('it-IT', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(parseFloat(results.price !== undefined ? results.price : results.sell_in))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 17\n        }, this);\n      }\n    };\n    this.fee = results => {\n      if (results.fee !== null && results.fee !== '0') {\n        if (this.state.role === distributore) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: \"FEE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1112,\n              columnNumber: 25\n            }, this), new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR',\n              maximumFractionDigits: 6\n            }).format(parseFloat(results.fee))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 21\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-column-title\",\n              children: \"FEE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 25\n            }, this), new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(parseFloat(results.fee))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1118,\n            columnNumber: 21\n          }, this);\n        }\n      }\n    };\n    this.prodNameBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Nome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1130,\n          columnNumber: 17\n        }, this), results.idProduct !== undefined ? results.idProduct.description : results.product.description]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1129,\n        columnNumber: 13\n      }, this);\n    };\n    this.productDescriptionBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Nome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1138,\n          columnNumber: 17\n        }, this), results.idProduct2.description]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1137,\n        columnNumber: 13\n      }, this);\n    };\n    this.prodIdBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.exCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 17\n        }, this), results.idProduct !== undefined ? results.idProduct.externalCode : results.product.externalCode]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 13\n      }, this);\n    };\n    this.statDocBodyTemplate = results => {\n      var ClassColor = '';\n      if (results.status === 'approved') {\n        ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success';\n      } else if (results.status === 'prepared') {\n        ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning';\n      } else if (results.status === 'positioned') {\n        ClassColor = 'border border-secondary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-secondary';\n      } else if (results.status === 'canceled') {\n        ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger';\n      } else if (results.status === 'create') {\n        ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary';\n      } else if (results.status === 'counted') {\n        ClassColor = 'border border-info statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-info';\n      } else if (results.status === 'inventoried') {\n        ClassColor = 'border border-dark statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-dark';\n      } else if (results.status === 'assigned') {\n        ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary';\n      } else if (results.status === 'delivered') {\n        ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success';\n      } else if (results.status === 'not delivered') {\n        ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger';\n      }\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.Stato\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1470,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: ClassColor,\n          children: results.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1471,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1469,\n        columnNumber: 13\n      }, this);\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      deleteResultDialog: false,\n      deleteResultsDialog: false,\n      result: this.emptyResult,\n      submitted: false,\n      globalFilter: null,\n      showModal: false,\n      showModal2: false,\n      loading: true,\n      extraButton: 'd-none p-button ml-0 ml-sm-3',\n      extraButton2: 'd-none p-button ml-0 ml-sm-3',\n      exportCsvButton: 'd-none p-button ml-0 ml-sm-3 mr-0 mr-sm-3',\n      classNameButton: 'd-none csv col-12 col-lg-8 col-xl-6',\n      classInputSearch: 'p-input-icon-left p-input-icon-right d-block mx-auto',\n      classHeader: 'table-header row',\n      splitButtonClass: 'd-none',\n      singleButton: 'd-none',\n      addCSVBTN: false,\n      role: ''\n    };\n    this.handlers = {\n      id: this.idBodyTemplate,\n      description: this.descriptionBodyTemplate,\n      validFrom: this.validFromBodyTemplate,\n      validTo: this.validToBodyTemplate,\n      isValid: this.isValidBodyTemplate,\n      userBodyTemplate: this.userBodyTemplate,\n      username: this.usernameBodyTemplate,\n      firstName: this.firstNameBodyTemplate,\n      address: this.addressBodyTemplate,\n      city: this.cityBodyTemplate,\n      cap: this.capBodyTemplate,\n      pIva: this.pIvaBodyTemplate,\n      tel: this.telBodyTemplate,\n      email: this.emailBodyTemplate,\n      image: this.imageBodyTemplate,\n      externalCode: this.externalCodeBodyTemplate,\n      supplyingCode: this.supplyingCodeBodyTemplate,\n      eanCode: this.eanCodeBodyTemplate,\n      createAt: this.createAtBodyTemplate,\n      createdAt: this.createdAtBodyTemplate,\n      updateAt: this.updateAtBodyTemplate,\n      status: this.statusBodyTemplate,\n      referente: this.referenteBodyTemplate,\n      externalSystem: this.externalSystemBodyTemplate,\n      deliveryDestination: this.deliveryDestinationBodyTemplate,\n      orderDate: this.orderDateBodyTemplate,\n      deliveryDate: this.deliveryDateBodyTemplate,\n      termsPayment: this.termsPaymentBodyTemplate,\n      paymentStatus: this.paymentStatusBodyTemplate,\n      statOrd: this.statOrdBodyTemplate,\n      total: this.totBodyTemplate,\n      totalTaxed: this.totIvaBodyTemplate,\n      totalPayed: this.totalPayedBodyTemplate,\n      tax: this.taxBodyTemplate,\n      firstNameOrd: this.firstNameOrdBodyTemplate,\n      statusAgent: this.statusAgentBodyTemplate,\n      autista: this.autistaBodyTemplate,\n      externalSystem2: this.externalSystemBodyTemplate2,\n      availability: this.availabilityBodyTemplate,\n      unitMeasure: this.unitMeasureBodyTemplate,\n      pcsXpackage: this.pcxpkgBodyTemplate,\n      colli: this.colliBodyTemplate,\n      quantity: this.quantityBodyTemplate,\n      orderNumber: this.orderNumberBodyTemplate,\n      deliveryStatus: this.deliveryStatusBodyTemplate,\n      termsPayment2: this.termsPaymentBodyTemplate2,\n      note: this.noteBodyTemplate,\n      unitPrice: this.unitPriceBodyTemplate,\n      iva: this.ivaBodyTemplate,\n      prodName: this.prodNameBodyTemplate,\n      prodId: this.prodIdBodyTemplate,\n      productDescription: this.productDescriptionBodyTemplate,\n      exCode: this.exCodeBodyTemplate,\n      price: this.priceBodyTemplate,\n      prodiva: this.prodivaBodyTemplate,\n      lotto: this.lottoBodyTemplate,\n      scadenza: this.scadenzaBodyTemplate,\n      giacenza: this.giacenzaBodyTemplate,\n      qtaOrd: this.qtaOrdBodyTemplate,\n      qtaPrep: this.qtaPrepBodyTemplate,\n      qtaAcquist: this.qtaAcquistare,\n      alert: this.alertBodyTemplate,\n      firstNameAlign: this.firstNameAlignBodyTemplate,\n      usernameAlign: this.usernameAlignBodyTemplate,\n      statAlign: this.statAlignBodyTemplate,\n      date: this.dateBodyTemplate,\n      orderProductsExternalCode: this.orderProductsExternalCode,\n      orderProductsDescription: this.orderProductsDescription,\n      orderProductsQuantità: this.orderProductsQuantità,\n      productsAvailabilities: this.productsAvailabilities,\n      typeDoc: this.typeDoc,\n      taskDate: this.taskDate,\n      documentDate: this.documentDate,\n      docExCode: this.docExCode,\n      docProdName: this.docProdName,\n      docEanCode: this.docEanCode,\n      um: this.um,\n      colliPreventivo: this.colliPreventivo,\n      colliConsuntivo: this.colliConsuntivo,\n      scadenzaDoc: this.scadenzaDoc,\n      fee: this.fee,\n      nDoc: this.nDoc,\n      statDoc: this.statDocBodyTemplate,\n      addressUser: this.addressUserBodyTemplate,\n      cityUser: this.cityUserBodyTemplate,\n      capUser: this.capUserBodyTemplate,\n      pIvaUser: this.pIvaUserBodyTemplate,\n      telUser: this.telUserBodyTemplate,\n      emailUser: this.emailUserBodyTemplate,\n      descriptionPrev: this.descriptionPrev,\n      prodExCode: this.prodExCode,\n      prodDesc: this.prodDesc,\n      prodArea: this.prodArea,\n      prodScaffale: this.prodScaffale,\n      prodRipiano: this.prodRipiano,\n      prodPosizione: this.prodPosizione,\n      numberDoc: this.numberDoc,\n      typeDocTask: this.typeDocTask,\n      opDoc: this.opDoc,\n      formato: this.formato,\n      icona: this.icona,\n      Colli: this.colli,\n      taskDate2: this.taskDate2,\n      supplying: this.supplying,\n      retailer: this.retailer,\n      scadenzaDocBodyTemplate: this.scadenzaDocBodyTemplate,\n      assigned: this.assigned,\n      operator: this.operator,\n      name: this.nameBodyTemplate,\n      controller: this.controller,\n      erpSync: this.erpSync,\n      warehouse: this.warehouse,\n      retailerBt: this.retailerBodyTemplate,\n      operatorBt: this.operatorBodyTemplate,\n      manager: this.respDoc,\n      paymentMetod: this.paymentMetod,\n      physicalStock: this.physicalStock,\n      committedCustomer: this.committedCustomer,\n      selectFormato: this.selectFormato,\n      date_start: this.date_start,\n      date_end: this.date_end,\n      discount_active: this.discount_active,\n      inflation_active: this.inflation_active,\n      externalcode: this.externalcode,\n      totale: this.totale,\n      qta: this.qta,\n      idLogEmail: this.idLogEmail,\n      numero: this.numeroBodyTemplate,\n      ragione_sociale: this.ragioneSocialeoBodyTemplate,\n      ordinato_unitario: this.ordinatoUnitarioBodyTemplate,\n      data_documento: this.dataDocumentoBodyTemplate,\n      tipo_documento: this.tipoDocumentoBodyTemplate,\n      affiliate: this.affiliate,\n      Supplying: this.Supplying,\n      logMessage: this.logMessage,\n      affiliates: this.affiliates,\n      retailers: this.retailers,\n      discount_payment: this.discountPayment,\n      pfa: this.pfa,\n      target: this.target,\n      correlati: this.correlati,\n      sconto: this.sconto,\n      conditioned_discount: this.conditioned_discount,\n      pesoNetto: this.pesoNetto,\n      /* Handler filtri */\n      filterDate: this.filterDate\n    };\n    //Dichiarazione funzioni e metodi\n    this.onStatusChange = this.onStatusChange.bind(this);\n    this.saveResult = this.saveResult.bind(this);\n    /* Filtri */\n    this.filterData = this.filterData.bind(this);\n    this.filterDate = this.filterDate.bind(this);\n    this.onDateFilterChange = this.onDateFilterChange.bind(this);\n    this.delFilter = this.delFilter.bind(this);\n    this.displayCondition = this.displayCondition.bind(this);\n  }\n  // definiamo per prima cosa il valore delle classi in base a props\n  componentDidMount() {\n    this.setState({\n      role: localStorage.getItem('role')\n    });\n    if (this.props.showExtraButton) {\n      this.setState({\n        extraButton: 'p-button ml-0 ml-sm-3 mr-2',\n        classNameButton: 'csv col-12 col-lg-8 col-xl-6'\n      });\n    }\n    if (this.props.showExportCsvButton) {\n      this.setState({\n        exportCsvButton: 'p-button ml-0 ml-sm-3 mr-0 mr-sm-3 mt-2',\n        classNameButton: 'csv col-12 col-lg-8 col-xl-6'\n      });\n    }\n    if (this.props.showExtraButton2) {\n      this.setState({\n        extraButton2: 'p-button ml-0 ml-sm-3 mr-2'\n      });\n    }\n    if (this.props.classHeader === true) {\n      document.getElementsByClassName(\"p-datatable-header\")[0].remove();\n      this.setState({\n        classHeader: 'table-header row d-none'\n      });\n    }\n    if (this.props.splitButtonClass === true) {\n      var find = this.props.items.find(el => el.label === Costanti.esportaCSV);\n      if (find === undefined) {\n        this.setState({\n          addCSVBTN: true\n        });\n      }\n      this.setState({\n        splitButtonClass: 'col-12 col-lg-8 col-xl-6',\n        classNameButton: 'd-none',\n        exportCsvButton: 'p-button ml-0 ml-sm-3 mr-0 mr-sm-3 mt-2'\n      });\n    }\n    if (this.props.singleButton !== undefined) {\n      this.setState({\n        classNameButton: 'd-none',\n        singleButton: 'csv col-12 col-lg-8 col-xl-6'\n      });\n    }\n    if (this.props.classExtraButton !== undefined) {\n      this.setState({\n        extraButton: this.props.classExtraButton\n      });\n    }\n    if (this.props.classInputSearch === false) {\n      this.setState({\n        classInputSearch: 'd-none'\n      });\n    }\n  }\n  imageBodyTemplate(rowData) {\n    var _rowData$idProduct;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"immaginiProdTab\",\n      children: /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [rowData.id !== undefined && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: apiref.baseProxy + 'asset/prodotti/' + rowData.id + '.jpg',\n          onError: e => e.target.src = Immagine,\n          alt: rowData.description,\n          className: \"product-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 25\n        }, this), rowData.id === undefined && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: apiref.baseProxy + 'asset/prodotti/' + (rowData.product !== undefined ? rowData.product.id : ((_rowData$idProduct = rowData.idProduct) === null || _rowData$idProduct === void 0 ? void 0 : _rowData$idProduct.id) !== undefined ? rowData.idProduct.id : rowData.idproduct) + '.jpg',\n          onError: e => e.target.src = Immagine,\n          alt: rowData.description,\n          className: \"product-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 13\n    }, this);\n  }\n  onStatusChange(e, results) {\n    confirmDialog({\n      message: \"L'azione di registrazione dell'ordine sarà irreversibile procedere?\",\n      header: 'Attenzione!',\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: \"Si\",\n      rejectLabel: \"No\",\n      accept: () => this.saveResult(e, results),\n      reject: () => null\n    });\n  }\n  //Salvataggio dati modificati mediante chiamata axios\n  async saveResult(e, results) {\n    results.status = e.value.name;\n    this.setState({\n      selectedStatus: e.value.name\n    });\n    let url = 'orders/?id=' + results.id;\n    var prodotti = [];\n    results.orderProducts.forEach(element => {\n      var prod = {\n        colli: element.colli,\n        id: element.productId,\n        orderId: element.orderId,\n        pcsXpackage: element.pcsXpackage,\n        product: element.product,\n        productId: element.productId,\n        quantity: element.quantity,\n        tax: element.tax,\n        total: element.total,\n        totalTaxed: element.totalTaxed,\n        unitMeasure: element.unitMeasure,\n        unitPrice: element.unitPrice\n      };\n      prodotti.push(prod);\n    });\n    let body = {\n      deliveryDate: results.deliveryDate,\n      deliveryDestination: results.deliveryDestination,\n      id: results.id,\n      idRetailer: results.idRetailer,\n      note: results.note,\n      orderDate: results.orderDate,\n      orderNumber: results.orderNumber,\n      products: prodotti,\n      termsPayment: results.termsPayment,\n      total: results.total,\n      totalTaxed: results.totalTaxed,\n      status: results.status,\n      numberOfCustomers: results.numberOfCustomers\n    };\n    var res = await apiref.APIRequest('PUT', url, body);\n    console.log(res.data);\n    window.location.reload();\n  }\n  pcxpkgBodyTemplate(results) {\n    if (results.moltiplicatore !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 21\n        }, this), results.moltiplicatore]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: \"Package\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 21\n        }, this), results.pcsXpackage]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  lottoBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.lotto\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1154,\n        columnNumber: 17\n      }, this), results.lotto]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1153,\n      columnNumber: 13\n    }, this);\n  }\n  scadenzaBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.scadenza\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1162,\n        columnNumber: 17\n      }, this), results.scadenza]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1161,\n      columnNumber: 13\n    }, this);\n  }\n  giacenzaBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Giacenza\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1170,\n        columnNumber: 17\n      }, this), results.giacenza]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1169,\n      columnNumber: 13\n    }, this);\n  }\n  qtaOrdBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.colliPreventivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1178,\n        columnNumber: 17\n      }, this), results.qtaOrd]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1177,\n      columnNumber: 13\n    }, this);\n  }\n  qtaPrepBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.colliConsuntivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1186,\n        columnNumber: 17\n      }, this), results.qtaPrep]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1185,\n      columnNumber: 13\n    }, this);\n  }\n  qtaAcquistare(rowData) {\n    rowData.qtaAcquist = rowData.qtaSuggerita - rowData.giacenza;\n    return rowData.qtaAcquist;\n  }\n  alertBodyTemplate(rowData) {\n    var newDate = new Date();\n    let month = newDate.getMonth() + 1;\n    let day = newDate.getDate();\n    if (month < 10) {\n      month = '0' + month;\n    }\n    if (day < 10) {\n      day = '0' + day;\n    }\n    newDate = newDate.getFullYear() + '-' + month + '-' + day;\n    if (newDate > rowData.giorniRiordino) {\n      var color = \"red\";\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color\n        },\n        children: [\" \", rowData.alert = Costanti.DataOrdAlert, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  dateBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1216,\n        columnNumber: 17\n      }, this), new Intl.DateTimeFormat(\"it-IT\", {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      }).format(new Date(results.date))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1215,\n      columnNumber: 13\n    }, this);\n  }\n  orderProductsExternalCode(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.exCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1224,\n        columnNumber: 17\n      }, this), results.product.externalCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1223,\n      columnNumber: 13\n    }, this);\n  }\n  orderProductsDescription(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1232,\n        columnNumber: 17\n      }, this), results.product.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1231,\n      columnNumber: 13\n    }, this);\n  }\n  orderProductsQuantità(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Quantità\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1240,\n        columnNumber: 17\n      }, this), results.Quantità]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1239,\n      columnNumber: 13\n    }, this);\n  }\n  productsAvailabilities(results) {\n    var availability = results.productsAvailabilities.find(el => {\n      var _el$idWarehouse;\n      return ((_el$idWarehouse = el.idWarehouse) === null || _el$idWarehouse === void 0 ? void 0 : _el$idWarehouse.codDep) === ((results === null || results === void 0 ? void 0 : results.codDep) !== undefined ? results === null || results === void 0 ? void 0 : results.codDep : '00');\n    });\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Giacenza\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1249,\n        columnNumber: 17\n      }, this), availability !== undefined ? availability.availability : 'Non disponibile']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1248,\n      columnNumber: 13\n    }, this);\n  }\n  typeDoc(results) {\n    if (results.type !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 21\n        }, this), results.type]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1257,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1265,\n          columnNumber: 21\n        }, this), results.idDocument.type]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1264,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  taskDate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.DataTask\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1275,\n        columnNumber: 17\n      }, this), new Date(results.createAt).toLocaleDateString()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1274,\n      columnNumber: 13\n    }, this);\n  }\n  documentDate(results) {\n    if (results.documentDate !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.DataDoc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1284,\n          columnNumber: 21\n        }, this), new Date(results.documentDate).toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1283,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.DataDoc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1291,\n          columnNumber: 21\n        }, this), new Date(results.idDocument.documentDate).toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1290,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  docExCode(results) {\n    var _results$idProductsPa;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.exCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1300,\n        columnNumber: 17\n      }, this), (_results$idProductsPa = results.idProductsPackaging) === null || _results$idProductsPa === void 0 ? void 0 : _results$idProductsPa.idProduct.externalCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1299,\n      columnNumber: 13\n    }, this);\n  }\n  docProdName(results) {\n    var _results$idProductsPa2;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Prodotto\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1308,\n        columnNumber: 17\n      }, this), (_results$idProductsPa2 = results.idProductsPackaging) === null || _results$idProductsPa2 === void 0 ? void 0 : _results$idProductsPa2.idProduct.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1307,\n      columnNumber: 13\n    }, this);\n  }\n  descriptionPrev(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Prodotto\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1316,\n        columnNumber: 17\n      }, this), results.idProduct.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1315,\n      columnNumber: 13\n    }, this);\n  }\n  docEanCode(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.eanCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1324,\n        columnNumber: 17\n      }, this), results.idProductsPackaging.idProduct.eanCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1323,\n      columnNumber: 13\n    }, this);\n  }\n  um(results) {\n    var _results$idProductsPa3, _results$idProductsPa4;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.UnitMis\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 17\n      }, this), results.idProductsPackaging !== undefined ? \"\".concat((_results$idProductsPa3 = results.idProductsPackaging) === null || _results$idProductsPa3 === void 0 ? void 0 : _results$idProductsPa3.unitMeasure, \" x \").concat((_results$idProductsPa4 = results.idProductsPackaging) === null || _results$idProductsPa4 === void 0 ? void 0 : _results$idProductsPa4.pcsXPackage) : \"\".concat(results.um, \" x \").concat(results.pcs)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1331,\n      columnNumber: 13\n    }, this);\n  }\n  colliPreventivo(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.colliPreventivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1340,\n        columnNumber: 17\n      }, this), results.colliPreventivo]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1339,\n      columnNumber: 13\n    }, this);\n  }\n  colliConsuntivo(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.colliConsuntivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1348,\n        columnNumber: 17\n      }, this), results.colliConsuntivo]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1347,\n      columnNumber: 13\n    }, this);\n  }\n  colli(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Colli\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1356,\n        columnNumber: 17\n      }, this), results.colli]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1355,\n      columnNumber: 13\n    }, this);\n  }\n  scadenzaDoc(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.scadenza\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1364,\n        columnNumber: 17\n      }, this), results.scadenza]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1363,\n      columnNumber: 13\n    }, this);\n  }\n  scadenzaDocBodyTemplate(results) {\n    if (results.scadenza !== null) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.scadenza\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1373,\n          columnNumber: 21\n        }, this), new Date(results.scadenza).toLocaleDateString().split('T')[0]]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1372,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  nDoc(results) {\n    if (results.number !== undefined) {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.NDoc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1383,\n          columnNumber: 21\n        }, this), results.number]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1382,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: Costanti.NDoc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1390,\n          columnNumber: 21\n        }, this), results.idDocument.number]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1389,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  numeroBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.NDoc\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1399,\n        columnNumber: 17\n      }, this), results.numero]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1398,\n      columnNumber: 13\n    }, this);\n  }\n  ragioneSocialeoBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.rSociale\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1407,\n        columnNumber: 17\n      }, this), results.ragione_sociale]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1406,\n      columnNumber: 13\n    }, this);\n  }\n  ordinatoUnitarioBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Quantità\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1415,\n        columnNumber: 17\n      }, this), results.ordinato_unitario]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1414,\n      columnNumber: 13\n    }, this);\n  }\n  tipoDocumentoBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1423,\n        columnNumber: 17\n      }, this), results.tipo_documento]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1422,\n      columnNumber: 13\n    }, this);\n  }\n  dataDocumentoBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.DataDoc\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1431,\n        columnNumber: 17\n      }, this), new Date(results.data_documento).toLocaleDateString().split('T')[0]]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1430,\n      columnNumber: 13\n    }, this);\n  }\n  prodExCode(results) {\n    var _results$idProductsPa5;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.exCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1480,\n        columnNumber: 17\n      }, this), (_results$idProductsPa5 = results.idProductsPackaging) === null || _results$idProductsPa5 === void 0 ? void 0 : _results$idProductsPa5.idProduct.externalCode]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1479,\n      columnNumber: 13\n    }, this);\n  }\n  prodDesc(results) {\n    var _results$idProductsPa6;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1488,\n        columnNumber: 17\n      }, this), (_results$idProductsPa6 = results.idProductsPackaging) === null || _results$idProductsPa6 === void 0 ? void 0 : _results$idProductsPa6.idProduct.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1487,\n      columnNumber: 13\n    }, this);\n  }\n  prodArea(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Area\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1496,\n        columnNumber: 17\n      }, this), results.idWarehouseComposition.area !== 'UNDEFINED' ? results.idWarehouseComposition.area : 'Non specificata']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1495,\n      columnNumber: 13\n    }, this);\n  }\n  prodScaffale(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Scaffale\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1504,\n        columnNumber: 17\n      }, this), results.idWarehouseComposition.scaffale !== 'UNDEFINED' ? results.idWarehouseComposition.scaffale : 'Non specificato']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1503,\n      columnNumber: 13\n    }, this);\n  }\n  prodRipiano(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Ripiano\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1512,\n        columnNumber: 17\n      }, this), results.idWarehouseComposition.ripiano !== 'UNDEFINED' ? results.idWarehouseComposition.ripiano : 'Non specificato']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1511,\n      columnNumber: 13\n    }, this);\n  }\n  prodPosizione(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Posizione\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1520,\n        columnNumber: 17\n      }, this), results.idWarehouseComposition.posizione !== 'UNDEFINED' ? results.idWarehouseComposition.posizione : 'Non specificata']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1519,\n      columnNumber: 13\n    }, this);\n  }\n  numberDoc(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.NDoc\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1528,\n        columnNumber: 17\n      }, this), results.idDocument.number]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1527,\n      columnNumber: 13\n    }, this);\n  }\n  typeDocTask(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1536,\n        columnNumber: 17\n      }, this), results.idDocument.type]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1535,\n      columnNumber: 13\n    }, this);\n  }\n  opDoc(results) {\n    var _results$operator, _results$operator$idU;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Operatore\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1544,\n        columnNumber: 17\n      }, this), ((_results$operator = results.operator) === null || _results$operator === void 0 ? void 0 : (_results$operator$idU = _results$operator.idUser) === null || _results$operator$idU === void 0 ? void 0 : _results$operator$idU.username) || 'N/A']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1543,\n      columnNumber: 13\n    }, this);\n  }\n  respDoc(results) {\n    var _results$tasks, _results$tasks$manage, _results$tasks$manage2;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Responsabile\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1552,\n        columnNumber: 17\n      }, this), ((_results$tasks = results.tasks) === null || _results$tasks === void 0 ? void 0 : (_results$tasks$manage = _results$tasks.manager) === null || _results$tasks$manage === void 0 ? void 0 : (_results$tasks$manage2 = _results$tasks$manage.idUser) === null || _results$tasks$manage2 === void 0 ? void 0 : _results$tasks$manage2.username) || 'N/A']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1551,\n      columnNumber: 13\n    }, this);\n  }\n  formato(results) {\n    var _results$idProductsPa7, _results$idProductsPa8;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Formato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1560,\n        columnNumber: 17\n      }, this), (_results$idProductsPa7 = results.idProductsPackaging) === null || _results$idProductsPa7 === void 0 ? void 0 : _results$idProductsPa7.unitMeasure, \" x \", (_results$idProductsPa8 = results.idProductsPackaging) === null || _results$idProductsPa8 === void 0 ? void 0 : _results$idProductsPa8.pcsXPackage]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1559,\n      columnNumber: 13\n    }, this);\n  }\n  icona(results) {\n    if (results.type === 'flop') {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1569,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n          name: \"arrow-down-outline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1570,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1568,\n        columnNumber: 17\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1576,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n          name: \"arrow-up-outline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1577,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1575,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  taskDate2(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.DataTask\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1585,\n        columnNumber: 17\n      }, this), new Date(results.taskDate).toLocaleDateString()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1584,\n      columnNumber: 13\n    }, this);\n  }\n  supplying(results) {\n    var _results$idSupplying, _results$idSupplying2;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.DataTask\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1593,\n        columnNumber: 17\n      }, this), ((_results$idSupplying = results.idSupplying) === null || _results$idSupplying === void 0 ? void 0 : _results$idSupplying.idRegistry.firstName) !== undefined ? (_results$idSupplying2 = results.idSupplying) === null || _results$idSupplying2 === void 0 ? void 0 : _results$idSupplying2.idRegistry.firstName : results.supplying]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1592,\n      columnNumber: 13\n    }, this);\n  }\n  retailer(results) {\n    var _results$idRetailer2, _results$idRetailer3;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.DataTask\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1601,\n        columnNumber: 17\n      }, this), ((_results$idRetailer2 = results.idRetailer) === null || _results$idRetailer2 === void 0 ? void 0 : _results$idRetailer2.idRegistry.firstName) !== undefined ? (_results$idRetailer3 = results.idRetailer) === null || _results$idRetailer3 === void 0 ? void 0 : _results$idRetailer3.idRegistry.firstName : results.retailer]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1600,\n      columnNumber: 13\n    }, this);\n  }\n  assigned(results) {\n    var _results$tasks2, _results$tasks3, _results$tasks4, _results$tasks5, _results$tasks6, _results$tasks7, _results$tasks8, _results$tasks9, _results$tasks0, _results$tasks1, _results$tasks10;\n    var ClassColor = '';\n    if (((_results$tasks2 = results.tasks) === null || _results$tasks2 === void 0 ? void 0 : _results$tasks2.status) === 'approved') {\n      ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success';\n    } else if (((_results$tasks3 = results.tasks) === null || _results$tasks3 === void 0 ? void 0 : _results$tasks3.status) === 'prepared') {\n      ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning';\n    } else if (((_results$tasks4 = results.tasks) === null || _results$tasks4 === void 0 ? void 0 : _results$tasks4.status) === 'positioned') {\n      ClassColor = 'border border-secondary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-secondary';\n    } else if (((_results$tasks5 = results.tasks) === null || _results$tasks5 === void 0 ? void 0 : _results$tasks5.status) === 'canceled') {\n      ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger';\n    } else if (((_results$tasks6 = results.tasks) === null || _results$tasks6 === void 0 ? void 0 : _results$tasks6.status) === 'create') {\n      ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary';\n    } else if (((_results$tasks7 = results.tasks) === null || _results$tasks7 === void 0 ? void 0 : _results$tasks7.status) === 'counted') {\n      ClassColor = 'border border-info statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-info';\n    } else if (((_results$tasks8 = results.tasks) === null || _results$tasks8 === void 0 ? void 0 : _results$tasks8.status) === 'inventoried') {\n      ClassColor = 'border border-dark statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-dark';\n    } else if (((_results$tasks9 = results.tasks) === null || _results$tasks9 === void 0 ? void 0 : _results$tasks9.status) === 'assigned') {\n      ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary';\n    } else if (((_results$tasks0 = results.tasks) === null || _results$tasks0 === void 0 ? void 0 : _results$tasks0.status) === 'delivered') {\n      ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success';\n    } else if (((_results$tasks1 = results.tasks) === null || _results$tasks1 === void 0 ? void 0 : _results$tasks1.status) === 'not delivered') {\n      ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger';\n    }\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Stato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1640,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: ClassColor,\n        children: ((_results$tasks10 = results.tasks) === null || _results$tasks10 === void 0 ? void 0 : _results$tasks10.status) || 'N/A'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1641,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1639,\n      columnNumber: 13\n    }, this);\n  }\n  operator(results) {\n    var _results$tasks11, _results$tasks11$oper, _results$tasks11$oper2;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Operatore\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1650,\n        columnNumber: 17\n      }, this), ((_results$tasks11 = results.tasks) === null || _results$tasks11 === void 0 ? void 0 : (_results$tasks11$oper = _results$tasks11.operator) === null || _results$tasks11$oper === void 0 ? void 0 : (_results$tasks11$oper2 = _results$tasks11$oper.idUser) === null || _results$tasks11$oper2 === void 0 ? void 0 : _results$tasks11$oper2.username) || 'N/A']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1649,\n      columnNumber: 13\n    }, this);\n  }\n  nameBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1658,\n        columnNumber: 17\n      }, this), results.name]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1657,\n      columnNumber: 13\n    }, this);\n  }\n  controller(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.externalSystem\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1666,\n        columnNumber: 17\n      }, this), results.controller]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1665,\n      columnNumber: 13\n    }, this);\n  }\n  erpSync(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Documento\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1674,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: results.erpSync === true ? 'border border-success rounded bg-success d-flex justify-content-center text-white' : 'border border-danger rounded bg-danger d-flex justify-content-center text-white',\n        children: results.erpSync === true ? 'Si' : 'No'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1675,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1673,\n      columnNumber: 13\n    }, this);\n  }\n  warehouse(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Magazzino\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1684,\n        columnNumber: 17\n      }, this), results.warehouse]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1683,\n      columnNumber: 13\n    }, this);\n  }\n  retailerBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.cliente\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1692,\n        columnNumber: 17\n      }, this), results.retailer]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1691,\n      columnNumber: 13\n    }, this);\n  }\n  operatorBodyTemplate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Operatore\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1700,\n        columnNumber: 17\n      }, this), results.operator]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1699,\n      columnNumber: 13\n    }, this);\n  }\n  paymentMetod(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.paymentMetod\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1708,\n        columnNumber: 17\n      }, this), results.paymentMetod]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1707,\n      columnNumber: 13\n    }, this);\n  }\n  physicalStock(results) {\n    var availability = results.productsAvailabilities.find(el => {\n      var _el$idWarehouse2;\n      return ((_el$idWarehouse2 = el.idWarehouse) === null || _el$idWarehouse2 === void 0 ? void 0 : _el$idWarehouse2.codDep) === ((results === null || results === void 0 ? void 0 : results.codDep) !== undefined ? results === null || results === void 0 ? void 0 : results.codDep : '00');\n    });\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.GiacenzaFisica\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1717,\n        columnNumber: 17\n      }, this), availability !== undefined ? availability.physicalStock : 'Non disponibile']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1716,\n      columnNumber: 13\n    }, this);\n  }\n  committedCustomer(results) {\n    var availability = results.productsAvailabilities.find(el => {\n      var _el$idWarehouse3;\n      return ((_el$idWarehouse3 = el.idWarehouse) === null || _el$idWarehouse3 === void 0 ? void 0 : _el$idWarehouse3.codDep) === ((results === null || results === void 0 ? void 0 : results.codDep) !== undefined ? results === null || results === void 0 ? void 0 : results.codDep : '00');\n    });\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ImpegnataCliente\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1726,\n        columnNumber: 17\n      }, this), availability !== undefined ? availability.committedCustomer : 'Non disponibile']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1725,\n      columnNumber: 13\n    }, this);\n  }\n  pesoNetto(results) {\n    var _results$idProductsPa9, _results$idProductsPa0;\n    var pesoNetto = \"\".concat(parseFloat((_results$idProductsPa9 = results.idProductsPackaging) === null || _results$idProductsPa9 === void 0 ? void 0 : _results$idProductsPa9.pesoNetto).toFixed(2) * ((_results$idProductsPa0 = results.idProductsPackaging) === null || _results$idProductsPa0 === void 0 ? void 0 : _results$idProductsPa0.pcsXPackage), \"kg\");\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PesoNetto\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1735,\n        columnNumber: 17\n      }, this), pesoNetto]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1734,\n      columnNumber: 13\n    }, this);\n  }\n  idLogEmail(results) {\n    var classColor = results.idLogEmail !== null ? 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center align-items-center text-white bg-success py-1' : 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center align-items-center text-white bg-warning py-1';\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ImpegnataCliente\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1744,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: classColor,\n        children: [results.idLogEmail !== null ? Costanti.Inviata : Costanti.NonInviata, /*#__PURE__*/_jsxDEV(\"i\", {\n          className: results.idLogEmail !== null ? 'pi pi-check ml-2' : 'pi pi-times-circle ml-2'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1746,\n          columnNumber: 91\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1745,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1743,\n      columnNumber: 13\n    }, this);\n  }\n  selectFormato(results) {\n    var formati = [];\n    results.productsPackagings.forEach(element => {\n      formati.push({\n        name: element.unitMeasure + ' X ' + element.pcsXPackage,\n        value: element.id\n      });\n    });\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ImpegnataCliente\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1758,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n        value: results.productsPackagings.length === 1 ? results.productsPackagings[0].id : [],\n        options: formati,\n        onChange: e => results.productsPackagings = results.productsPackagings.filter(el => el.id === e.value),\n        optionLabel: \"name\",\n        placeholder: \"Seleziona formato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1759,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1757,\n      columnNumber: 13\n    }, this);\n  }\n  logMessage(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Mex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1766,\n        columnNumber: 17\n      }, this), results.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: results.message\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1765,\n      columnNumber: 13\n    }, this);\n  }\n  discount_active(results) {\n    var _results$discount_act, _results$discount_act2;\n    var percent = (_results$discount_act = results.discount_active) === null || _results$discount_act === void 0 ? void 0 : _results$discount_act.percent;\n    var fixed = (_results$discount_act2 = results.discount_active) === null || _results$discount_act2 === void 0 ? void 0 : _results$discount_act2.fixed;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ScontoAttivo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1781,\n        columnNumber: 17\n      }, this), (percent === null || percent === void 0 ? void 0 : percent.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: percent.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + \"% \"\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1786,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false), (fixed === null || fixed === void 0 ? void 0 : fixed.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: fixed.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + '€ '\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1797,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1780,\n      columnNumber: 13\n    }, this);\n  }\n  inflation_active(results) {\n    var _results$inflation_ac, _results$inflation_ac2;\n    var percent = (_results$inflation_ac = results.inflation_active) === null || _results$inflation_ac === void 0 ? void 0 : _results$inflation_ac.percent;\n    var fixed = (_results$inflation_ac2 = results.inflation_active) === null || _results$inflation_ac2 === void 0 ? void 0 : _results$inflation_ac2.fixed;\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Rincaro\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1812,\n        columnNumber: 17\n      }, this), (percent === null || percent === void 0 ? void 0 : percent.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: percent.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + \"% \"\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1817,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false), (fixed === null || fixed === void 0 ? void 0 : fixed.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: fixed.map((el, key) => {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: el + '€ '\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1828,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1811,\n      columnNumber: 13\n    }, this);\n  }\n  displayCondition(scontiCondizionati) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: scontiCondizionati && scontiCondizionati.map((el, key) => {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [\"\".concat(el), \" \", key !== scontiCondizionati.length - 1 ? ', ' : '']\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1844,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false);\n  }\n  conditioned_discount(results) {\n    var _results$conditioned_;\n    var scontiCondizionati = [];\n    (_results$conditioned_ = results.conditioned_discount) === null || _results$conditioned_ === void 0 ? void 0 : _results$conditioned_.map(el => scontiCondizionati.push(el.correlati ? {\n      acquistando: el.condizione,\n      unità: el.correlati.map(item => item).join(', '),\n      ricevi: Object.keys(el.omaggio)[0],\n      di: Object.values(el.omaggio).map(obj => obj.map(item => \"\".concat(Object.keys(item), \": \").concat(Object.values(item))).join(', '))\n    } : el.condizione ? \"Acquistando \".concat(el.condizione, \" unit\\xE0 \").concat(el.percent ? \"ricevi il \".concat(el.percent, \"% di sconto\") : \"ricevi \".concat(el.fixed, \" di sconto\")) : ''));\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.ScontoCondizionato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1858,\n        columnNumber: 17\n      }, this), scontiCondizionati.length > 0 && /*#__PURE__*/_jsxDEV(OverlayPanelGen, {\n        values: scontiCondizionati,\n        label: \"Sconto\",\n        badge: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1860,\n        columnNumber: 21\n      }, this)]\n    }, results.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1857,\n      columnNumber: 13\n    }, this);\n  }\n  affiliate(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Affiliato\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1868,\n        columnNumber: 17\n      }, this), results.affiliate]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1867,\n      columnNumber: 13\n    }, this);\n  }\n  Supplying(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Fornitore\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1876,\n        columnNumber: 17\n      }, this), results.supplying]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1875,\n      columnNumber: 13\n    }, this);\n  }\n  affiliates(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Affiliati\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1884,\n        columnNumber: 17\n      }, this), results.affiliate.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1883,\n      columnNumber: 13\n    }, this);\n  }\n  retailers(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.Clienti\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1892,\n        columnNumber: 17\n      }, this), results.retailer.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1891,\n      columnNumber: 13\n    }, this);\n  }\n  pfa(results) {\n    var pfa = results.pfa.map(el => el.correlati !== undefined ? el.percent !== undefined ? \"Target: \".concat(el.condizione, \" correlati: \").concat(el.correlati, \"  premio: \").concat(el.percent, \"%\") : \"Target: \".concat(el.condizione, \" correlati: \").concat(el.correlati, \" premio: \").concat(el.fixed) : el.percent !== undefined ? \"Target: \".concat(el.condizione, \" premio: \").concat(el.percent, \"%\") : \"Target: \".concat(el.condizione, \" premio: \").concat(el.fixed)).join(', ');\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PremioFineAnno\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1901,\n        columnNumber: 17\n      }, this), pfa]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1900,\n      columnNumber: 13\n    }, this);\n  }\n  discountPayment(results) {\n    var discountPayment = results.discount_payment.map(el => el.correlati !== undefined ? el.idPaymentMethod !== undefined ? \"Id pagamento: \".concat(el.idPaymentMethod, \" correlati: \").concat(el.correlati, \" premio: \").concat(el.percent, \"%\") : \"Id pagamento: \".concat(el.idPaymentMethod, \" correlati: \").concat(el.correlati, \" premio: \").concat(el.fixed) : el.idPaymentMethod !== undefined ? \"Id pagamento: \".concat(el.idPaymentMethod, \" premio: \").concat(el.percent, \"%\") : \"Id pagamento: \".concat(el.idPaymentMethod, \" premio: \").concat(el.fixed)).join(', ');\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PremioFineAnno\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1910,\n        columnNumber: 17\n      }, this), discountPayment]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1909,\n      columnNumber: 13\n    }, this);\n  }\n  target(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PremioFineAnno\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1918,\n        columnNumber: 17\n      }, this), parseInt(results.target) !== 0 ? results.target : 'Nessun target']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1917,\n      columnNumber: 13\n    }, this);\n  }\n  correlati(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PremioFineAnno\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1926,\n        columnNumber: 17\n      }, this), results.correlati !== undefined ? results.correlati.map(el => el).join(', ') : '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1925,\n      columnNumber: 13\n    }, this);\n  }\n  sconto(results) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"p-column-title\",\n        children: Costanti.PremioFineAnno\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1934,\n        columnNumber: 17\n      }, this), results.sconto]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1933,\n      columnNumber: 13\n    }, this);\n  }\n  /* filtri custom */\n  delFilter() {\n    this.setState({\n      dateFilter: ''\n    });\n    this.onDateFilterChange();\n    document.getElementById('filterByOrderDate').querySelectorAll('.p-inputtext')[0].classList.remove(\"p-filled\");\n  }\n  filterData() {\n    var classIcon = '';\n    var filtro = this.state.dateFilter;\n    if (filtro !== undefined && filtro !== '') {\n      classIcon = 'pi pi-times ml-1';\n    } else {\n      classIcon = 'pi pi-times ml-1 d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center filterByOrderDate\",\n      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n        id: \"filterByOrderDate\",\n        value: this.state.dateFilter,\n        onChange: this.onDateFilterChange,\n        placeholder: \"Filtra per data ordine\",\n        dateFormat: \"dd/mm/yy\",\n        className: \"p-column-filter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1955,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: classIcon,\n        onClick: () => this.delFilter()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1956,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1954,\n      columnNumber: 13\n    }, this);\n  }\n  onDateFilterChange(event) {\n    if (event !== undefined) {\n      if (event.value !== null) {\n        this.dt.filter(this.formatDate(event.value), 'orderDate', 'contains');\n      } else {\n        this.dt.filter(null, 'orderDate', 'equals');\n      }\n      this.setState({\n        dateFilter: event.value\n      });\n    } else {\n      this.dt.filter(null, 'orderDate', 'equals');\n    }\n  }\n  filterDate(value, filter) {\n    if (filter === undefined || filter === null || typeof filter === 'string' && filter.trim() === '') {\n      return true;\n    }\n    if (value === undefined || value === null) {\n      return false;\n    }\n    return value === this.formatDate(filter);\n  }\n  formatDate(date) {\n    if (date !== null) {\n      let month = date.getMonth() + 1;\n      let day = date.getDate();\n      if (month < 10) {\n        month = '0' + month;\n      }\n      if (day < 10) {\n        day = '0' + day;\n      }\n      return date.getFullYear() + '-' + month + '-' + day;\n    }\n  }\n  exportCSVGhostClick() {\n    document.querySelector('#CSVExport').click();\n  }\n  render() {\n    var _this$props$items, _this$props$fields;\n    /* elemento generico per i filtri custom */\n    const dateFilter = this.filterData();\n    //Dichiarazione header del componente\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: this.state.classHeader,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-lg-4 col-xl-6 mt-3 mb-2 mt-lg-0\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: this.state.classInputSearch,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2005,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            className: \"w-100 clearControl\",\n            type: \"search\",\n            onInput: e => this.setState({\n              globalFilter: e.target.value\n            }),\n            placeholder: \"Cerca...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2006,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"clear-icon pi pi-times invisible\",\n            role: \"button\",\n            onClick: e => {\n              document.querySelector('.clearControl').value = '';\n              this.setState({\n                globalFilter: null\n              });\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2007,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2004,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2003,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: this.state.classNameButton,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"btns-actions d-flex justify-content-center justify-content-lg-end w-auto row\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            className: this.state.extraButton,\n            onClick: this.props.actionExtraButton,\n            disabled: this.props.disabledExtraButton,\n            tooltip: this.props.tooltip,\n            tooltipOptions: {\n              position: 'top'\n            },\n            children: [this.props.labelExtraButton, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2012,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: this.state.extraButton2,\n            onClick: this.props.actionExtraButton2,\n            disabled: this.props.disabledExtraButton2,\n            children: [this.props.labelExtraButton2, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2013,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ScaricaCSVProva, {\n            label: 'esportaCSV',\n            className: this.state.exportCsvButton,\n            results: this.props.value,\n            fileNames: this.props.fileNames\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2015,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2011,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2010,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: this.state.singleButton,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"btns-actions d-flex justify-content-end justify-content-lg-end w-auto row\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: this.state.extraButton,\n            onClick: this.props.actionExtraButton,\n            children: [this.props.labelExtraButton, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2020,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2019,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2018,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: this.state.splitButtonClass,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"btns-actions d-flex justify-content-center justify-content-lg-end w-auto row\",\n          children: [this.props.optionalButton && /*#__PURE__*/_jsxDEV(Button, {\n            className: this.props.classButton,\n            onClick: this.props.actionExtraButton,\n            disabled: this.props.disabledExtraButton,\n            tooltip: this.props.tooltip,\n            tooltipOptions: {\n              position: 'top'\n            },\n            children: [this.props.labelExtraButton, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2026,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"d-none\",\n            children: this.props.value !== null && this.state.addCSVBTN && ((_this$props$items = this.props.items) === null || _this$props$items === void 0 ? void 0 : _this$props$items.unshift({\n              label: Costanti.esportaCSV,\n              command: () => {\n                this.exportCSVGhostClick();\n              }\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2030,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(SplitButton, {\n            label: \"Azioni\",\n            icon: \"pi pi-cog\",\n            model: this.props.items,\n            className: \"splitButtonGen mr-2 mb-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2039,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(ScaricaCSVProva, {\n            hidden: true,\n            className: this.state.exportCsvButton,\n            results: this.props.value,\n            fileNames: this.props.fileNames\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2040,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2024,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2023,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2002,\n      columnNumber: 13\n    }, this);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        className: \"p-datatable-responsive-demo\",\n        ref: el => this.dt = el,\n        value: this.props.value,\n        loading: this.props.loading,\n        dataKey: this.props.dataKey,\n        paginator: this.props.paginator,\n        rows: this.props.rows,\n        rowsPerPageOptions: this.props.rowsPerPageOptions,\n        sortField: this.props.sortField,\n        sortOrder: this.props.sortOrder,\n        autoLayout: this.props.autoLayout,\n        globalFilter: this.state.globalFilter,\n        header: this.props.hideHeader ? null : this.props.header ? this.props.header : header,\n        emptyMessage: Costanti.EmptyMessage,\n        selectionMode: this.props.selectionMode,\n        selection: this.props.selection,\n        onSelectionChange: this.props.onSelectionChange,\n        responsiveLayout: this.props.responsiveLayout,\n        onRowSelect: this.props.onRowSelect,\n        expandedRows: this.props.expandedRows,\n        onRowToggle: this.props.onRowToggle,\n        onRowExpand: this.props.onRowExpand,\n        onRowCollapse: this.props.onRowCollapse,\n        rowExpansionTemplate: this.props.rowExpansionTemplate,\n        onCellSelect: e => e.field !== 'action' ? this.props.actionsColumn[0].handler(e.rowData) : null,\n        cellSelection: this.props.cellSelection,\n        id: this.props.id,\n        editMode: this.props.editMode,\n        onPage: this.props.onPage,\n        first: this.props.first,\n        lazy: this.props.lazy,\n        totalRecords: this.props.totalRecords,\n        filterDisplay: this.props.filterDisplay,\n        onSort: this.props.onSort,\n        onFilter: this.props.onFilter,\n        filters: this.props.filters,\n        csvSeparator: \";\",\n        children: [(_this$props$fields = this.props.fields) === null || _this$props$fields === void 0 ? void 0 : _this$props$fields.map(el => /*#__PURE__*/_jsxDEV(Column, {\n          className: el.className,\n          expander: el.expander,\n          field: el.field,\n          filterFunction: el.filterFunction,\n          filterField: el.filterField,\n          header: el.showHeader ? el.header : null,\n          sortable: el.sortable,\n          filter: el.filter,\n          filterMatchMode: el.filterMatchMode,\n          filterElement: dateFilter,\n          filterPlaceholder: el.filterPlaceholder,\n          body: el.body !== undefined ? row => this.handlers[el.body](row) : null,\n          headerStyle: el.headerStyle,\n          selectionMode: el.selectionMode\n        }, Math.random(), false, {\n          fileName: _jsxFileName,\n          lineNumber: 2086,\n          columnNumber: 25\n        }, this)), this.props.actionsColumn && /*#__PURE__*/_jsxDEV(Column, {\n          className: \"tableMenu\",\n          field: \"action\",\n          body: e => !e.area || (e === null || e === void 0 ? void 0 : e.area) !== 'N/D' ? /*#__PURE__*/_jsxDEV(MenuItem, {\n            fields: this.props.actionsColumn,\n            rowData: e\n          }, e, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2111,\n            columnNumber: 37\n          }, this) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2106,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2047,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2046,\n      columnNumber: 13\n    }, this);\n  }\n}", "map": {"version": 3, "names": ["React", "ScaricaCSVProva", "MenuItem", "a<PERSON>f", "<PERSON><PERSON><PERSON><PERSON>", "DataTable", "Column", "<PERSON><PERSON>", "<PERSON><PERSON>", "InputText", "Dropdown", "Calendar", "SplitButton", "confirmDialog", "distributore", "OverlayPanelGen", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomDataTable", "Component", "constructor", "props", "deliveryDateBodyTemplate", "results", "deliveryDate", "undefined", "convert", "date<PERSON><PERSON><PERSON>", "split", "map", "datetime", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "join", "output", "children", "className", "DeliveryDate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "orderDateBodyTemplate", "hour", "minute", "second", "orderDate", "dInserimento", "createdAtBodyTemplate", "createdAt", "date_start", "ValidFrom", "date_end", "ValidTo", "createAtBodyTemplate", "createAt", "updateAtBodyTemplate", "updateAt", "dAggiornamento", "validFromBodyTemplate", "validFrom", "validToBodyTemplate", "validTo", "isValidBodyTemplate", "rowData", "<PERSON><PERSON><PERSON><PERSON>", "is_valid", "Validità", "newDate", "getMonth", "getDate", "getFullYear", "userBodyTemplate", "users", "<PERSON><PERSON><PERSON>", "length", "usernameBodyTemplate", "username", "firstNameBodyTemplate", "rSociale", "firstName", "lastName", "addressBodyTemplate", "<PERSON><PERSON><PERSON><PERSON>", "address", "cityBodyTemplate", "Città", "city", "capBodyTemplate", "CodPost", "cap", "pIvaBodyTemplate", "pIva", "telBodyTemplate", "Tel", "tel", "emailBodyTemplate", "Email", "email", "idBodyTemplate", "id", "descriptionBodyTemplate", "Nome", "description", "externalCodeBodyTemplate", "exCode", "externalCode", "exCodeBodyTemplate", "idProduct2", "supplyingCodeBodyTemplate", "CodForn", "supplyingCode", "eanCodeBodyTemplate", "eanCode", "statusBodyTemplate", "status", "Attivo", "referenteBodyTemplate", "<PERSON><PERSON><PERSON>", "referente", "externalSystemBodyTemplate", "externalSystem", "externalSystemBodyTemplate2", "idExternalSystem", "externalSistemName", "deliveryDestinationBodyTemplate", "Destinazione", "deliveryDestination", "paymentStatusBodyTemplate", "StatPag", "paymentStatus", "termsPaymentBodyTemplate", "_results$idRetailer", "Pagamento", "idRetailer", "idRegistry", "paymentMetod", "statOrdBodyTemplate", "StatOrd", "statAlignBodyTemplate", "Stato", "totBodyTemplate", "state", "role", "<PERSON><PERSON>", "NumberFormat", "style", "currency", "maximumFractionDigits", "total", "totIvaBodyTemplate", "TotTax", "totalTaxed", "totalPayedBodyTemplate", "TotPag", "parseFloat", "totalPayed", "toFixed", "taxBodyTemplate", "<PERSON><PERSON>", "ivaBodyTemplate", "tax", "prodivaBodyTemplate", "iva", "firstNameOrdBodyTemplate", "firstNameAlignBodyTemplate", "usernameAlignBodyTemplate", "addressUserBodyTemplate", "cityUserBodyTemplate", "capUserBodyTemplate", "pIvaUserBodyTemplate", "telUserBodyTemplate", "emailUserBodyTemplate", "qta", "Quantità", "totale", "externalcode", "statusAgentBodyTemplate", "toLowerCase", "name", "value", "selectedStatus", "options", "onChange", "e", "onStatusChange", "optionLabel", "emptyMessage", "placeholder", "autistaBodyTemplate", "<PERSON><PERSON>", "au<PERSON>", "availabilityBodyTemplate", "Giacenza", "availability", "unitMeasureBodyTemplate", "Unità", "unitMeasure", "colliBodyTemplate", "quantity", "pcsXpackage", "quantityBodyTemplate", "totQta", "orderNumberBodyTemplate", "N_ord", "orderNumber", "deliveryStatusBodyTemplate", "ClassColor", "deliveryStatus", "StatCons", "termsPaymentBodyTemplate2", "TermPag", "termsPayment", "noteBodyTemplate", "_results$note", "max", "note", "substr", "Note", "unitPriceBodyTemplate", "Prezzo", "unitPrice", "priceBodyTemplate", "price", "sell_in", "fee", "prodNameBodyTemplate", "idProduct", "product", "productDescriptionBodyTemplate", "prodIdBodyTemplate", "statDocBodyTemplate", "results2", "resultDialog", "resultDialog2", "resultDialog3", "deleteResultDialog", "deleteResultsDialog", "result", "emptyResult", "submitted", "globalFilter", "showModal", "showModal2", "loading", "extraButton", "extraButton2", "exportCsvButton", "classNameButton", "classInputSearch", "classHeader", "splitButtonClass", "singleButton", "addCSVBTN", "handlers", "image", "imageBodyTemplate", "statOrd", "firstNameOrd", "statusAgent", "externalSystem2", "pcxpkgBodyTemplate", "colli", "termsPayment2", "prodName", "prodId", "productDescription", "prodiva", "lotto", "lottoBodyTemplate", "scadenza", "scadenzaBodyTemplate", "giac<PERSON>za", "giacenzaBodyTemplate", "qtaOrd", "qtaOrdBodyTemplate", "qtaPrep", "qtaPrepBodyTemplate", "qtaA<PERSON>quist", "qtaAcquistare", "alert", "alertBodyTemplate", "firstNameAlign", "usernameAlign", "statAlign", "date", "dateBodyTemplate", "orderProductsExternalCode", "orderProductsDescription", "orderProductsQuantità", "productsAvailabilities", "typeDoc", "taskDate", "documentDate", "docExCode", "docProdName", "docEanCode", "um", "colliPreventivo", "colliConsuntivo", "scadenzaDoc", "nDoc", "statDoc", "addressUser", "cityUser", "capUser", "pIvaUser", "telUser", "emailUser", "descriptionPrev", "prodExCode", "prodDesc", "prodArea", "prodScaffale", "prodRipiano", "prodPosizione", "numberDoc", "typeDocTask", "opDoc", "formato", "icona", "<PERSON><PERSON>", "taskDate2", "supplying", "retailer", "scadenzaDocBodyTemplate", "assigned", "operator", "nameBodyTemplate", "controller", "erpSync", "warehouse", "retailerBt", "retailerBodyTemplate", "operatorBt", "operatorBodyTemplate", "manager", "respDoc", "physicalStock", "committedCustomer", "selectFormato", "discount_active", "inflation_active", "idLogEmail", "numero", "numeroBodyTemplate", "ragione_sociale", "ragioneSocialeoBodyTemplate", "ordinato_unitario", "ordinatoUnitarioBodyTemplate", "data_documento", "dataDocumentoBodyTemplate", "tipo_documento", "tipoDocumentoBodyTemplate", "affiliate", "Supplying", "logMessage", "affiliates", "retailers", "discount_payment", "discountPayment", "pfa", "target", "correlati", "sconto", "conditioned_discount", "pesoNetto", "filterDate", "bind", "saveResult", "filterData", "onDateFilterChange", "<PERSON><PERSON><PERSON><PERSON>", "displayCondition", "componentDidMount", "setState", "localStorage", "getItem", "showExtraButton", "showExportCsvButton", "showExtraButton2", "document", "getElementsByClassName", "remove", "find", "items", "el", "label", "esportaCSV", "classExtraButton", "_rowData$idProduct", "src", "baseProxy", "onError", "alt", "idproduct", "message", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "reject", "url", "prodotti", "orderProducts", "for<PERSON>ach", "element", "prod", "productId", "orderId", "push", "body", "products", "numberOfCustomers", "res", "APIRequest", "console", "log", "data", "window", "location", "reload", "moltiplicatore", "qtaSuggerita", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color", "DataOrdAlert", "Data", "_el$idWarehouse", "idWarehouse", "codDep", "type", "idDocument", "DataTask", "toLocaleDateString", "DataDoc", "_results$idProductsPa", "idProductsPackaging", "_results$idProductsPa2", "<PERSON><PERSON><PERSON>", "_results$idProductsPa3", "_results$idProductsPa4", "UnitMis", "concat", "pcsXPackage", "pcs", "number", "NDoc", "_results$idProductsPa5", "_results$idProductsPa6", "Area", "idWarehouseComposition", "area", "<PERSON><PERSON><PERSON><PERSON>", "scaffale", "Ripiano", "<PERSON><PERSON>", "Posizione", "posizione", "_results$operator", "_results$operator$idU", "Operatore", "idUser", "_results$tasks", "_results$tasks$manage", "_results$tasks$manage2", "Responsabile", "tasks", "_results$idProductsPa7", "_results$idProductsPa8", "Formato", "_results$idSupplying", "_results$idSupplying2", "idSupplying", "_results$idRetailer2", "_results$idRetailer3", "_results$tasks2", "_results$tasks3", "_results$tasks4", "_results$tasks5", "_results$tasks6", "_results$tasks7", "_results$tasks8", "_results$tasks9", "_results$tasks0", "_results$tasks1", "_results$tasks10", "_results$tasks11", "_results$tasks11$oper", "_results$tasks11$oper2", "Documento", "<PERSON><PERSON><PERSON><PERSON>", "cliente", "_el$idWarehouse2", "GiacenzaFisica", "_el$idWarehouse3", "ImpegnataCliente", "_results$idProductsPa9", "_results$idProductsPa0", "PesoNetto", "classColor", "Inviata", "NonInviata", "formati", "productsPackagings", "filter", "Mex", "_results$discount_act", "_results$discount_act2", "percent", "fixed", "ScontoAttivo", "key", "_results$inflation_ac", "_results$inflation_ac2", "<PERSON><PERSON><PERSON><PERSON>", "scontiCondizionati", "_results$conditioned_", "a<PERSON><PERSON><PERSON><PERSON>", "condizione", "unità", "item", "rice<PERSON>", "Object", "keys", "omaggio", "di", "values", "obj", "ScontoCondizionato", "badge", "Affiliato", "Fornitore", "<PERSON><PERSON><PERSON><PERSON>", "Clienti", "PremioFineAnno", "idPaymentMethod", "parseInt", "dateFilter", "getElementById", "querySelectorAll", "classList", "classIcon", "filtro", "dateFormat", "onClick", "event", "dt", "formatDate", "trim", "exportCSVGhostClick", "querySelector", "click", "render", "_this$props$items", "_this$props$fields", "onInput", "actionExtraButton", "disabled", "disabledExtraButton", "tooltip", "tooltipOptions", "position", "labelExtraButton", "actionExtraButton2", "disabledExtraButton2", "labelExtraButton2", "fileNames", "optionalButton", "classButton", "unshift", "command", "model", "hidden", "ref", "dataKey", "paginator", "rows", "rowsPerPageOptions", "sortField", "sortOrder", "autoLayout", "<PERSON><PERSON>ead<PERSON>", "EmptyMessage", "selectionMode", "selection", "onSelectionChange", "responsiveLayout", "onRowSelect", "expandedRows", "onRowToggle", "onRowExpand", "onRowCollapse", "rowExpansionTemplate", "onCellSelect", "field", "actionsColumn", "handler", "cellSelection", "editMode", "onPage", "first", "lazy", "totalRecords", "filterDisplay", "onSort", "onFilter", "filters", "csvSeparator", "fields", "expander", "filterFunction", "filterField", "showHeader", "sortable", "filterMatchMode", "filterElement", "filterPlaceholder", "row", "headerStyle", "Math", "random"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/customDataTable.jsx"], "sourcesContent": ["import React from 'react';\nimport ScaricaCSVProva from '../common/distributore/aggiunta file/scaricaCSVProva';\nimport MenuItem from './menuItem';\nimport * as apiref from './generalizzazioni/apireq';\nimport Immagine from '../img/mktplaceholder.jpg';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { Costanti } from './traduttore/const';\nimport { Button } from 'primereact/button';\nimport { InputText } from 'primereact/inputtext';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Calendar } from \"primereact/calendar\";\nimport { SplitButton } from 'primereact/splitbutton';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { distributore } from './route';\nimport { OverlayPanelGen } from './generalizzazioni/overlayPanelGen';\nimport '../css/DataTableDemo.css';\n\nexport default class CustomDataTable extends React.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            deleteResultDialog: false,\n            deleteResultsDialog: false,\n            result: this.emptyResult,\n            submitted: false,\n            globalFilter: null,\n            showModal: false,\n            showModal2: false,\n            loading: true,\n            extraButton: 'd-none p-button ml-0 ml-sm-3',\n            extraButton2: 'd-none p-button ml-0 ml-sm-3',\n            exportCsvButton: 'd-none p-button ml-0 ml-sm-3 mr-0 mr-sm-3',\n            classNameButton: 'd-none csv col-12 col-lg-8 col-xl-6',\n            classInputSearch: 'p-input-icon-left p-input-icon-right d-block mx-auto',\n            classHeader: 'table-header row',\n            splitButtonClass: 'd-none',\n            singleButton: 'd-none',\n            addCSVBTN: false,\n            role: ''\n        }\n        this.handlers = {\n            id: this.idBodyTemplate,\n            description: this.descriptionBodyTemplate,\n            validFrom: this.validFromBodyTemplate,\n            validTo: this.validToBodyTemplate,\n            isValid: this.isValidBodyTemplate,\n            userBodyTemplate: this.userBodyTemplate,\n            username: this.usernameBodyTemplate,\n            firstName: this.firstNameBodyTemplate,\n            address: this.addressBodyTemplate,\n            city: this.cityBodyTemplate,\n            cap: this.capBodyTemplate,\n            pIva: this.pIvaBodyTemplate,\n            tel: this.telBodyTemplate,\n            email: this.emailBodyTemplate,\n            image: this.imageBodyTemplate,\n            externalCode: this.externalCodeBodyTemplate,\n            supplyingCode: this.supplyingCodeBodyTemplate,\n            eanCode: this.eanCodeBodyTemplate,\n            createAt: this.createAtBodyTemplate,\n            createdAt: this.createdAtBodyTemplate,\n            updateAt: this.updateAtBodyTemplate,\n            status: this.statusBodyTemplate,\n            referente: this.referenteBodyTemplate,\n            externalSystem: this.externalSystemBodyTemplate,\n            deliveryDestination: this.deliveryDestinationBodyTemplate,\n            orderDate: this.orderDateBodyTemplate,\n            deliveryDate: this.deliveryDateBodyTemplate,\n            termsPayment: this.termsPaymentBodyTemplate,\n            paymentStatus: this.paymentStatusBodyTemplate,\n            statOrd: this.statOrdBodyTemplate,\n            total: this.totBodyTemplate,\n            totalTaxed: this.totIvaBodyTemplate,\n            totalPayed: this.totalPayedBodyTemplate,\n            tax: this.taxBodyTemplate,\n            firstNameOrd: this.firstNameOrdBodyTemplate,\n            statusAgent: this.statusAgentBodyTemplate,\n            autista: this.autistaBodyTemplate,\n            externalSystem2: this.externalSystemBodyTemplate2,\n            availability: this.availabilityBodyTemplate,\n            unitMeasure: this.unitMeasureBodyTemplate,\n            pcsXpackage: this.pcxpkgBodyTemplate,\n            colli: this.colliBodyTemplate,\n            quantity: this.quantityBodyTemplate,\n            orderNumber: this.orderNumberBodyTemplate,\n            deliveryStatus: this.deliveryStatusBodyTemplate,\n            termsPayment2: this.termsPaymentBodyTemplate2,\n            note: this.noteBodyTemplate,\n            unitPrice: this.unitPriceBodyTemplate,\n            iva: this.ivaBodyTemplate,\n            prodName: this.prodNameBodyTemplate,\n            prodId: this.prodIdBodyTemplate,\n            productDescription: this.productDescriptionBodyTemplate,\n            exCode: this.exCodeBodyTemplate,\n            price: this.priceBodyTemplate,\n            prodiva: this.prodivaBodyTemplate,\n            lotto: this.lottoBodyTemplate,\n            scadenza: this.scadenzaBodyTemplate,\n            giacenza: this.giacenzaBodyTemplate,\n            qtaOrd: this.qtaOrdBodyTemplate,\n            qtaPrep: this.qtaPrepBodyTemplate,\n            qtaAcquist: this.qtaAcquistare,\n            alert: this.alertBodyTemplate,\n            firstNameAlign: this.firstNameAlignBodyTemplate,\n            usernameAlign: this.usernameAlignBodyTemplate,\n            statAlign: this.statAlignBodyTemplate,\n            date: this.dateBodyTemplate,\n            orderProductsExternalCode: this.orderProductsExternalCode,\n            orderProductsDescription: this.orderProductsDescription,\n            orderProductsQuantità: this.orderProductsQuantità,\n            productsAvailabilities: this.productsAvailabilities,\n            typeDoc: this.typeDoc,\n            taskDate: this.taskDate,\n            documentDate: this.documentDate,\n            docExCode: this.docExCode,\n            docProdName: this.docProdName,\n            docEanCode: this.docEanCode,\n            um: this.um,\n            colliPreventivo: this.colliPreventivo,\n            colliConsuntivo: this.colliConsuntivo,\n            scadenzaDoc: this.scadenzaDoc,\n            fee: this.fee,\n            nDoc: this.nDoc,\n            statDoc: this.statDocBodyTemplate,\n            addressUser: this.addressUserBodyTemplate,\n            cityUser: this.cityUserBodyTemplate,\n            capUser: this.capUserBodyTemplate,\n            pIvaUser: this.pIvaUserBodyTemplate,\n            telUser: this.telUserBodyTemplate,\n            emailUser: this.emailUserBodyTemplate,\n            descriptionPrev: this.descriptionPrev,\n            prodExCode: this.prodExCode,\n            prodDesc: this.prodDesc,\n            prodArea: this.prodArea,\n            prodScaffale: this.prodScaffale,\n            prodRipiano: this.prodRipiano,\n            prodPosizione: this.prodPosizione,\n            numberDoc: this.numberDoc,\n            typeDocTask: this.typeDocTask,\n            opDoc: this.opDoc,\n            formato: this.formato,\n            icona: this.icona,\n            Colli: this.colli,\n            taskDate2: this.taskDate2,\n            supplying: this.supplying,\n            retailer: this.retailer,\n            scadenzaDocBodyTemplate: this.scadenzaDocBodyTemplate,\n            assigned: this.assigned,\n            operator: this.operator,\n            name: this.nameBodyTemplate,\n            controller: this.controller,\n            erpSync: this.erpSync,\n            warehouse: this.warehouse,\n            retailerBt: this.retailerBodyTemplate,\n            operatorBt: this.operatorBodyTemplate,\n            manager: this.respDoc,\n            paymentMetod: this.paymentMetod,\n            physicalStock: this.physicalStock,\n            committedCustomer: this.committedCustomer,\n            selectFormato: this.selectFormato,\n            date_start: this.date_start,\n            date_end: this.date_end,\n            discount_active: this.discount_active,\n            inflation_active: this.inflation_active,\n            externalcode: this.externalcode,\n            totale: this.totale,\n            qta: this.qta,\n            idLogEmail: this.idLogEmail,\n            numero: this.numeroBodyTemplate,\n            ragione_sociale: this.ragioneSocialeoBodyTemplate,\n            ordinato_unitario: this.ordinatoUnitarioBodyTemplate,\n            data_documento: this.dataDocumentoBodyTemplate,\n            tipo_documento: this.tipoDocumentoBodyTemplate,\n            affiliate: this.affiliate,\n            Supplying: this.Supplying,\n            logMessage: this.logMessage,\n            affiliates: this.affiliates,\n            retailers: this.retailers,\n            discount_payment: this.discountPayment,\n            pfa: this.pfa,\n            target: this.target,\n            correlati: this.correlati,\n            sconto: this.sconto,\n            conditioned_discount: this.conditioned_discount,\n            pesoNetto: this.pesoNetto,\n            /* Handler filtri */\n            filterDate: this.filterDate\n        };\n        //Dichiarazione funzioni e metodi\n        this.onStatusChange = this.onStatusChange.bind(this);\n        this.saveResult = this.saveResult.bind(this);\n        /* Filtri */\n        this.filterData = this.filterData.bind(this);\n        this.filterDate = this.filterDate.bind(this);\n        this.onDateFilterChange = this.onDateFilterChange.bind(this);\n        this.delFilter = this.delFilter.bind(this);\n        this.displayCondition = this.displayCondition.bind(this);\n    }\n    // definiamo per prima cosa il valore delle classi in base a props\n    componentDidMount() {\n        this.setState({ role: localStorage.getItem('role') })\n        if (this.props.showExtraButton) {\n            this.setState({\n                extraButton: 'p-button ml-0 ml-sm-3 mr-2',\n                classNameButton: 'csv col-12 col-lg-8 col-xl-6'\n            })\n        }\n        if (this.props.showExportCsvButton) {\n            this.setState({\n                exportCsvButton: 'p-button ml-0 ml-sm-3 mr-0 mr-sm-3 mt-2',\n                classNameButton: 'csv col-12 col-lg-8 col-xl-6'\n            })\n        }\n        if (this.props.showExtraButton2) {\n            this.setState({\n                extraButton2: 'p-button ml-0 ml-sm-3 mr-2'\n            })\n        }\n        if (this.props.classHeader === true) {\n            document.getElementsByClassName(\"p-datatable-header\")[0].remove()\n            this.setState({\n                classHeader: 'table-header row d-none'\n            })\n        }\n        if (this.props.splitButtonClass === true) {\n            var find = this.props.items.find(el => el.label === Costanti.esportaCSV)\n            if (find === undefined) {\n                this.setState({ addCSVBTN: true })\n            }\n            this.setState({\n                splitButtonClass: 'col-12 col-lg-8 col-xl-6',\n                classNameButton: 'd-none',\n                exportCsvButton: 'p-button ml-0 ml-sm-3 mr-0 mr-sm-3 mt-2',\n            })\n        }\n        if (this.props.singleButton !== undefined) {\n            this.setState({\n                classNameButton: 'd-none',\n                singleButton: 'csv col-12 col-lg-8 col-xl-6'\n            })\n        }\n        if (this.props.classExtraButton !== undefined) {\n            this.setState({\n                extraButton: this.props.classExtraButton\n            })\n        }\n        if (this.props.classInputSearch === false) {\n            this.setState({\n                classInputSearch: 'd-none'\n            })\n        }\n    }\n    //Converto data e ora in formato IT\n    deliveryDateBodyTemplate = (results) => {\n        if (results.deliveryDate !== null && results.deliveryDate !== undefined) {\n            // results.order_data\n            const convert = dateRange =>\n                dateRange\n                    .split(\" - \")\n                    .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                    .join(\" - \");\n            const output = convert(results?.deliveryDate);\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.DeliveryDate}</span>\n                    {output}\n                </React.Fragment>\n            );\n        }\n    }\n    //Converto data e ora in formato IT\n    orderDateBodyTemplate = (results) => {\n        // results.order_data\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.orderDate);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.dInserimento}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    //Converto data e ora in formato IT\n    createdAtBodyTemplate = (results) => {\n        // results.order_data\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.createdAt);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.dInserimento}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    date_start = (results) => {\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.date_start);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ValidFrom}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    date_end = (results) => {\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.date_end);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ValidTo}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    //Converto data e ora in formato IT\n    createAtBodyTemplate = (results) => {\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.createAt);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.dInserimento}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    //Converto data e ora in formato IT\n    updateAtBodyTemplate = (results) => {\n        const convert = dateRange =>\n            dateRange\n                .split(\" - \")\n                .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                .join(\" - \");\n        const output = convert(results.updateAt);\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.dAggiornamento}</span>\n                {output}\n            </React.Fragment>\n        );\n    }\n    //Converto data e ora in formato IT\n    validFromBodyTemplate = (results) => {\n        if (results !== undefined) {\n            // results.order_data\n            const convert = dateRange =>\n                dateRange\n                    .split(\" - \")\n                    .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                    .join(\" - \");\n            const output = convert(results.validFrom);\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.ValidFrom}</span>\n                    {output}\n                </React.Fragment>\n            );\n        } else {\n            return null;\n        }\n    }\n    //Converto data e ora in formato IT\n    validToBodyTemplate = (results) => {\n        if (results !== undefined) {\n            // results.order_data\n            const convert = dateRange =>\n                dateRange\n                    .split(\" - \")\n                    .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(datetime)))\n                    .join(\" - \");\n            const output = convert(results.validTo);\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.ValidTo}</span>\n                    {output}\n                </React.Fragment>\n            );\n        } else {\n            return null;\n        }\n    }\n    //Metodo per ricavare il nome prodotto\n    isValidBodyTemplate = (rowData) => {\n        if (rowData.isValid !== undefined || rowData.is_valid !== undefined) {\n            if (rowData.isValid === true || rowData.is_valid === true) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Validità}</span>\n                        <span className=\"pi pi-check\" ></span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Validità}</span>\n                        <span className=\"pi pi-ban\" ></span>\n                    </React.Fragment>\n                );\n            }\n        } else {\n            /* GET CURRENT DATE */\n            var newDate = new Date()/* .toLocaleString() */;\n            /* let year = newDate.getFullYear(); */\n            let month = newDate.getMonth() + 1;\n            let day = newDate.getDate();\n            /* newDate = newDate.slice(0, 8) */\n            if (month < 10) {\n                month = '0' + month;\n            }\n            if (day < 10) {\n                day = '0' + day;\n            }\n            newDate = newDate.getFullYear() + '-' + month + '-' + day;\n            if (rowData.validTo > newDate) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Validità}</span>\n                        <span className=\"pi pi-check\" ></span>\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.Validità}</span>\n                        <span className=\"pi pi-ban\" ></span>\n                    </React.Fragment>\n                );\n            }\n        }\n    }\n    userBodyTemplate = (results) => {\n        if (results.users !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Utenti}</span>\n                    {results.users.length}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Utenti}</span>\n                    0\n                </React.Fragment>\n            )\n        }\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    usernameBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{\"Username\"}</span>\n                <b>{results.username}</b>\n            </React.Fragment>\n        );\n    }//Definizione dati nome per la tabella dell'anagrafica cliente\n    firstNameBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.rSociale}</span>\n                <b>{results.firstName} {results.lastName}</b>\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    addressBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Indirizzo}</span>\n                {results.address}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    cityBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Città}</span>\n                {results.city}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    capBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.CodPost}</span>\n                {results.cap}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    pIvaBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.pIva}</span>\n                {results.pIva}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    telBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Tel}</span>\n                {results.tel}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    emailBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Email}</span>\n                {results.email}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati id per la tabella dell'anagrafica cliente\n    idBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">ID</span>\n                {results.id}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    descriptionBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.description}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    externalCodeBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.externalCode}\n            </React.Fragment>\n        );\n    }\n    exCodeBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.idProduct2.externalCode}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    supplyingCodeBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.CodForn}</span>\n                {results.supplyingCode}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    eanCodeBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.eanCode}</span>\n                {results.eanCode}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    statusBodyTemplate = (results) => {\n        if (results.status === \"In uso\") {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Attivo}</span>\n                    <span className=\"pi pi-check\" ></span>\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Attivo}</span>\n                    <span className=\"pi pi-ban\" ></span>\n                </React.Fragment>\n            );\n        }\n    }\n    imageBodyTemplate(rowData) {\n        return (\n            <div className='immaginiProdTab'>\n                <React.Fragment>\n                    {rowData.id !== undefined &&\n                        <img src={apiref.baseProxy + 'asset/prodotti/' + rowData.id + '.jpg'} onError={(e) => e.target.src = Immagine} alt={rowData.description} className=\"product-image\" />\n                    }\n                    {rowData.id === undefined &&\n                        <img src={apiref.baseProxy + 'asset/prodotti/' + (rowData.product !== undefined ? rowData.product.id : (rowData.idProduct?.id !== undefined ? rowData.idProduct.id : rowData.idproduct)) + '.jpg'} onError={(e) => e.target.src = Immagine} alt={rowData.description} className=\"product-image\" />\n                    }\n                </React.Fragment>\n            </div>\n        )\n    }\n    referenteBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Referente}</span>\n                <b>{results.referente}</b>\n            </React.Fragment>\n        );\n    }\n    externalSystemBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.externalSystem}</span>\n                {results.externalSystem}\n            </React.Fragment>\n        );\n    }\n    externalSystemBodyTemplate2 = (results) => {\n        if (results.idExternalSystem !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.externalSystem}</span>\n                    {results.idExternalSystem.externalSistemName}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.externalSystem}</span>\n                    {results.externalSistemName}\n                </React.Fragment>\n            );\n        }\n    }\n    //Definizione dati indirizzo per la tabella dell'anagrafica cliente\n    deliveryDestinationBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Destinazione}</span>\n                {results.deliveryDestination}\n            </React.Fragment>\n        );\n    }\n    paymentStatusBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.StatPag}</span>\n                {results.paymentStatus}\n            </React.Fragment>\n        );\n    }\n    termsPaymentBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Pagamento}</span>\n                {results.idRetailer?.idRegistry.paymentMetod}\n            </React.Fragment>\n        );\n    }\n    statOrdBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.StatOrd}</span>\n                {/* <Dropdown value={this.state.selectedStatus} options={this.status} onChange={(e) => this.onStatusChange(e, results)} optionLabel=\"name\" placeholder= */}{results.status}{/* />  cambiare con data entry stato ordine */}\n            </React.Fragment>\n        );\n    }\n    statAlignBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Stato}</span>\n                {results.status}\n            </React.Fragment>\n        );\n    }\n    totBodyTemplate = (results) => {\n        if (this.state.role === distributore) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Tot}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results.total)}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Tot}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results.total)}\n                </React.Fragment>\n            );\n        }\n\n    }\n    totIvaBodyTemplate = (results) => {\n        if (this.state.role === distributore) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results.totalTaxed)}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.TotTax}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results.totalTaxed)}\n                </React.Fragment>\n            );\n        }\n    }\n    totalPayedBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.TotPag}</span>\n                {parseFloat(results.totalPayed).toFixed(2) + ' €'}\n            </React.Fragment>\n        );\n    }\n    taxBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Iva}</span>\n                {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(parseFloat(results.totalTaxed) - parseFloat(results.total))}\n            </React.Fragment>\n        );\n    }\n    ivaBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Iva}</span>\n                {results.tax}\n            </React.Fragment>\n        );\n    }\n    prodivaBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Iva}</span>\n                {results.idProduct2.iva}%\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'ordine\n    firstNameOrdBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.rSociale}</span>\n                <b>{results.idRetailer.idRegistry.firstName}</b>\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'ordine\n    firstNameAlignBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.rSociale}</span>\n                <b>{results.idRegistry.firstName}</b>\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'ordine\n    usernameAlignBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.rSociale}</span>\n                <b>{results.username}</b>\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    addressUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Indirizzo}</span>\n                {results.idRegistry.address}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    cityUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Città}</span>\n                {results.idRegistry.city}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    capUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.CodPost}</span>\n                {results.idRegistry.cap}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    pIvaUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.pIva}</span>\n                {results.idRegistry.pIva}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    telUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Tel}</span>\n                {results.idRegistry.tel}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati nome per la tabella dell'anagrafica cliente\n    emailUserBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Email}</span>\n                {results.idRegistry.email}\n            </React.Fragment>\n        );\n    }\n    qta = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Quantità}</span>\n                {results.qta}\n            </React.Fragment>\n        );\n    }\n    totale = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Tot}</span>\n                {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results.totale)}\n            </React.Fragment>\n        );\n    }\n    externalcode = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.externalcode}\n            </React.Fragment>\n        );\n    }\n    //Definizione dati stato per la tabella dell'ordine\n    statusAgentBodyTemplate = (results) => {\n        if (results.status !== undefined) {\n            if (results.status.toLowerCase() === \"bozza\") {\n                this.status = [\n                    { name: 'Bozza' },\n                    { name: 'Registrato' }\n                ];\n            } else if (results.status.toLowerCase() === \"registrato\") {\n                /* this.status = [\n                    { name: 'Registrato' }\n                ]; */\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.StatOrd}</span>\n                        {results.status}\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">{Costanti.StatOrd}</span>\n                        {results.status}\n                    </React.Fragment>\n                );\n            }\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.StatOrd}</span>\n                    <Dropdown value={this.state.selectedStatus} options={this.status} onChange={(e) => this.onStatusChange(e, results)} optionLabel=\"name\" emptyMessage='Nessun elemento selezionabile' placeholder={results.status} />\n                </React.Fragment>\n            );\n        }\n    }\n    onStatusChange(e, results) {\n        confirmDialog({\n            message: \"L'azione di registrazione dell'ordine sarà irreversibile procedere?\",\n            header: 'Attenzione!',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: () => this.saveResult(e, results),\n            reject: () => null\n        });\n    }\n    //Salvataggio dati modificati mediante chiamata axios\n    async saveResult(e, results) {\n        results.status = e.value.name\n        this.setState({\n            selectedStatus: e.value.name\n        })\n        let url = 'orders/?id=' + results.id;\n        var prodotti = []\n        results.orderProducts.forEach(element => {\n            var prod = {\n                colli: element.colli,\n                id: element.productId,\n                orderId: element.orderId,\n                pcsXpackage: element.pcsXpackage,\n                product: element.product,\n                productId: element.productId,\n                quantity: element.quantity,\n                tax: element.tax,\n                total: element.total,\n                totalTaxed: element.totalTaxed,\n                unitMeasure: element.unitMeasure,\n                unitPrice: element.unitPrice\n            }\n            prodotti.push(prod)\n        })\n        let body = {\n            deliveryDate: results.deliveryDate,\n            deliveryDestination: results.deliveryDestination,\n            id: results.id,\n            idRetailer: results.idRetailer,\n            note: results.note,\n            orderDate: results.orderDate,\n            orderNumber: results.orderNumber,\n            products: prodotti,\n            termsPayment: results.termsPayment,\n            total: results.total,\n            totalTaxed: results.totalTaxed,\n            status: results.status,\n            numberOfCustomers: results.numberOfCustomers\n        }\n        var res = await apiref.APIRequest('PUT', url, body)\n        console.log(res.data);\n        window.location.reload()\n    }\n    //Definizione dati nome per la tabella dell'ordine\n    autistaBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Autista}</span>\n                {results.autista}\n            </React.Fragment>\n        )\n    }\n    availabilityBodyTemplate = (rowData) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Giacenza}</span>\n                {rowData.availability}\n            </React.Fragment>\n        );\n    }\n    unitMeasureBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Unità}</span>\n                {results.unitMeasure}\n            </React.Fragment>\n        );\n    }\n    pcxpkgBodyTemplate(results) {\n        if (results.moltiplicatore !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Package</span>\n                    {results.moltiplicatore}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">Package</span>\n                    {results.pcsXpackage}\n                </React.Fragment>\n            );\n        }\n    }\n    colliBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Quantità}</span>\n                {results.quantity / results.pcsXpackage}\n            </React.Fragment>\n        );\n    }\n    quantityBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.totQta}</span>\n                {results.quantity}\n            </React.Fragment>\n        );\n    }\n    orderNumberBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.N_ord}</span>\n                {results.orderNumber}\n            </React.Fragment>\n        );\n    }\n    deliveryStatusBodyTemplate = (results) => {\n        var ClassColor = ''\n        if (results.deliveryStatus === 'assigned') {\n            ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary'\n        }\n        else if (results.deliveryStatus === 'create') {\n            ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning'\n        }\n        else if (results.deliveryStatus === 'delivered') {\n            ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success'\n        }\n        else if (results.deliveryStatus === 'not delivered') {\n            ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger'\n        } else { ClassColor = '' }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.StatCons}</span>\n                <div className={ClassColor}>\n                    {results.deliveryStatus}\n                </div>\n            </React.Fragment>\n        );\n    }\n    termsPaymentBodyTemplate2 = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.TermPag}</span>\n                {results.termsPayment}\n            </React.Fragment>\n        );\n    }\n    noteBodyTemplate = (results) => {\n        var max = 100;\n        if (results.note?.length > max) {\n            results.note = results.note.substr(0, max);\n        }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Note}</span>\n                {results.note}\n            </React.Fragment>\n        );\n    }\n    unitPriceBodyTemplate = (results) => {\n        if (this.state.role === distributore) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(results.unitPrice)}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                    {new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(results.unitPrice)}\n                </React.Fragment>\n            );\n        }\n    }\n    priceBodyTemplate = (results) => {\n        if (this.state.role === distributore) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                    {new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results.price !== undefined ? results.price : results.sell_in))}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.Prezzo}</span>\n                    {new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(parseFloat(results.price !== undefined ? results.price : results.sell_in))}\n                </React.Fragment>\n            );\n        }\n    }\n    fee = (results) => {\n        if (results.fee !== null && results.fee !== '0') {\n            if (this.state.role === distributore) {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">FEE</span>\n                        {new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR', maximumFractionDigits: 6 }).format(parseFloat(results.fee))}\n                    </React.Fragment>\n                );\n            } else {\n                return (\n                    <React.Fragment>\n                        <span className=\"p-column-title\">FEE</span>\n                        {new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(parseFloat(results.fee))}\n                    </React.Fragment>\n                );\n            }\n        }\n\n    }\n    prodNameBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.idProduct !== undefined ? results.idProduct.description : results.product.description}\n            </React.Fragment>\n        );\n    }\n    productDescriptionBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.idProduct2.description}\n            </React.Fragment>\n        );\n    }\n    prodIdBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.idProduct !== undefined ? results.idProduct.externalCode : results.product.externalCode}\n            </React.Fragment>\n        );\n    }\n    lottoBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.lotto}</span>\n                {results.lotto}\n            </React.Fragment>\n        );\n    }\n    scadenzaBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.scadenza}</span>\n                {results.scadenza}\n            </React.Fragment>\n        );\n    }\n    giacenzaBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Giacenza}</span>\n                {results.giacenza}\n            </React.Fragment>\n        );\n    }\n    qtaOrdBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.colliPreventivo}</span>\n                {results.qtaOrd}\n            </React.Fragment>\n        );\n    }\n    qtaPrepBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.colliConsuntivo}</span>\n                {results.qtaPrep}\n            </React.Fragment>\n        );\n    }\n    qtaAcquistare(rowData) {\n        rowData.qtaAcquist = rowData.qtaSuggerita - rowData.giacenza;\n        return rowData.qtaAcquist;\n    }\n    alertBodyTemplate(rowData) {\n        var newDate = new Date();\n        let month = newDate.getMonth() + 1;\n        let day = newDate.getDate();\n        if (month < 10) {\n            month = '0' + month;\n        }\n        if (day < 10) {\n            day = '0' + day;\n        }\n        newDate = newDate.getFullYear() + '-' + month + '-' + day;\n        if (newDate > rowData.giorniRiordino) {\n            var color = \"red\";\n            return (\n                <span style={{ color }}> {rowData.alert = Costanti.DataOrdAlert} </span>\n            )\n        }\n    }\n    dateBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Data}</span>\n                {new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).format(new Date(results.date))}\n            </React.Fragment>\n        );\n    }\n    orderProductsExternalCode(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.product.externalCode}\n            </React.Fragment>\n        );\n    }\n    orderProductsDescription(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.product.description}\n            </React.Fragment>\n        );\n    }\n    orderProductsQuantità(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Quantità}</span>\n                {results.Quantità}\n            </React.Fragment>\n        );\n    }\n    productsAvailabilities(results) {\n        var availability = results.productsAvailabilities.find(el => el.idWarehouse?.codDep === (results?.codDep !== undefined ? results?.codDep : '00'))\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Giacenza}</span>\n                {availability !== undefined ? availability.availability : 'Non disponibile'}\n            </React.Fragment>\n        );\n    }\n    typeDoc(results) {\n        if (results.type !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.type}</span>\n                    {results.type}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.type}</span>\n                    {results.idDocument.type}\n                </React.Fragment>\n            );\n        }\n\n    }\n    taskDate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.DataTask}</span>\n                {new Date(results.createAt).toLocaleDateString()}\n            </React.Fragment>\n        );\n    }\n    documentDate(results) {\n        if (results.documentDate !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.DataDoc}</span>\n                    {new Date(results.documentDate).toLocaleDateString()}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.DataDoc}</span>\n                    {new Date(results.idDocument.documentDate).toLocaleDateString()}\n                </React.Fragment>\n            );\n        }\n    }\n    docExCode(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.idProductsPackaging?.idProduct.externalCode}\n            </React.Fragment>\n        );\n    }\n    docProdName(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Prodotto}</span>\n                {results.idProductsPackaging?.idProduct.description}\n            </React.Fragment>\n        );\n    }\n    descriptionPrev(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Prodotto}</span>\n                {results.idProduct.description}\n            </React.Fragment>\n        );\n    }\n    docEanCode(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.eanCode}</span>\n                {results.idProductsPackaging.idProduct.eanCode}\n            </React.Fragment>\n        );\n    }\n    um(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.UnitMis}</span>\n                {results.idProductsPackaging !== undefined ? `${results.idProductsPackaging?.unitMeasure} x ${results.idProductsPackaging?.pcsXPackage}` : `${results.um} x ${results.pcs}`}\n            </React.Fragment>\n        );\n    }\n    colliPreventivo(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.colliPreventivo}</span>\n                {results.colliPreventivo}\n            </React.Fragment>\n        );\n    }\n    colliConsuntivo(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.colliConsuntivo}</span>\n                {results.colliConsuntivo}\n            </React.Fragment>\n        );\n    }\n    colli(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Colli}</span>\n                {results.colli}\n            </React.Fragment>\n        );\n    }\n    scadenzaDoc(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.scadenza}</span>\n                {results.scadenza}\n            </React.Fragment>\n        );\n    }\n    scadenzaDocBodyTemplate(results) {\n        if (results.scadenza !== null) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.scadenza}</span>\n                    {new Date(results.scadenza).toLocaleDateString().split('T')[0]}\n                </React.Fragment>\n            );\n        }\n    }\n    nDoc(results) {\n        if (results.number !== undefined) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.NDoc}</span>\n                    {results.number}\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{Costanti.NDoc}</span>\n                    {results.idDocument.number}\n                </React.Fragment>\n            );\n        }\n    }\n    numeroBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.NDoc}</span>\n                {results.numero}\n            </React.Fragment>\n        );\n    }\n    ragioneSocialeoBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.rSociale}</span>\n                {results.ragione_sociale}\n            </React.Fragment>\n        );\n    }\n    ordinatoUnitarioBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Quantità}</span>\n                {results.ordinato_unitario}\n            </React.Fragment>\n        );\n    }\n    tipoDocumentoBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.type}</span>\n                {results.tipo_documento}\n            </React.Fragment>\n        );\n    }\n    dataDocumentoBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.DataDoc}</span>\n                {new Date(results.data_documento).toLocaleDateString().split('T')[0]}\n            </React.Fragment>\n        );\n    }\n    statDocBodyTemplate = (results) => {\n        var ClassColor = '';\n        if (results.status === 'approved') {\n            ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success'\n        }\n        else if (results.status === 'prepared') {\n            ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning'\n        }\n        else if (results.status === 'positioned') {\n            ClassColor = 'border border-secondary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-secondary'\n        }\n        else if (results.status === 'canceled') {\n            ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger'\n        }\n        else if (results.status === 'create') {\n            ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary'\n        }\n        else if (results.status === 'counted') {\n            ClassColor = 'border border-info statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-info'\n        }\n        else if (results.status === 'inventoried') {\n            ClassColor = 'border border-dark statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-dark'\n        }\n        else if (results.status === 'assigned') {\n            ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary'\n        }\n        else if (results.status === 'delivered') {\n            ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success'\n        }\n        else if (results.status === 'not delivered') {\n            ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger'\n        }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Stato}</span>\n                <div className={ClassColor}>\n                    {results.status}\n                </div>\n            </React.Fragment>\n        );\n    }\n    prodExCode(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.exCode}</span>\n                {results.idProductsPackaging?.idProduct.externalCode}\n            </React.Fragment>\n        );\n    }\n    prodDesc(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.idProductsPackaging?.idProduct.description}\n            </React.Fragment>\n        );\n    }\n    prodArea(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Area}</span>\n                {results.idWarehouseComposition.area !== 'UNDEFINED' ? results.idWarehouseComposition.area : 'Non specificata'}\n            </React.Fragment>\n        );\n    }\n    prodScaffale(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Scaffale}</span>\n                {results.idWarehouseComposition.scaffale !== 'UNDEFINED' ? results.idWarehouseComposition.scaffale : 'Non specificato'}\n            </React.Fragment>\n        );\n    }\n    prodRipiano(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Ripiano}</span>\n                {results.idWarehouseComposition.ripiano !== 'UNDEFINED' ? results.idWarehouseComposition.ripiano : 'Non specificato'}\n            </React.Fragment>\n        );\n    }\n    prodPosizione(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Posizione}</span>\n                {results.idWarehouseComposition.posizione !== 'UNDEFINED' ? results.idWarehouseComposition.posizione : 'Non specificata'}\n            </React.Fragment>\n        );\n    }\n    numberDoc(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.NDoc}</span>\n                {results.idDocument.number}\n            </React.Fragment>\n        );\n    }\n    typeDocTask(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.type}</span>\n                {results.idDocument.type}\n            </React.Fragment>\n        );\n    }\n    opDoc(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Operatore}</span>\n                {results.operator?.idUser?.username || 'N/A'}\n            </React.Fragment>\n        );\n    }\n    respDoc(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Responsabile}</span>\n                {results.tasks?.manager?.idUser?.username || 'N/A'}\n            </React.Fragment>\n        );\n    }\n    formato(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Formato}</span>\n                {results.idProductsPackaging?.unitMeasure} x {results.idProductsPackaging?.pcsXPackage}\n            </React.Fragment>\n        );\n    }\n    icona(results) {\n        if (results.type === 'flop') {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\"></span>\n                    <ion-icon name=\"arrow-down-outline\"></ion-icon>\n                </React.Fragment>\n            );\n        } else {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\"></span>\n                    <ion-icon name=\"arrow-up-outline\"></ion-icon>\n                </React.Fragment>\n            );\n        }\n    }\n    taskDate2(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.DataTask}</span>\n                {new Date(results.taskDate).toLocaleDateString()}\n            </React.Fragment>\n        );\n    }\n    supplying(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.DataTask}</span>\n                {results.idSupplying?.idRegistry.firstName !== undefined ? results.idSupplying?.idRegistry.firstName : results.supplying}\n            </React.Fragment>\n        );\n    }\n    retailer(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.DataTask}</span>\n                {results.idRetailer?.idRegistry.firstName !== undefined ? results.idRetailer?.idRegistry.firstName : results.retailer}\n            </React.Fragment>\n        );\n    }\n    assigned(results) {\n        var ClassColor = '';\n        if (results.tasks?.status === 'approved') {\n            ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success'\n        }\n        else if (results.tasks?.status === 'prepared') {\n            ClassColor = 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-warning'\n        }\n        else if (results.tasks?.status === 'positioned') {\n            ClassColor = 'border border-secondary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-secondary'\n        }\n        else if (results.tasks?.status === 'canceled') {\n            ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger'\n        }\n        else if (results.tasks?.status === 'create') {\n            ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary'\n        }\n        else if (results.tasks?.status === 'counted') {\n            ClassColor = 'border border-info statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-info'\n        }\n        else if (results.tasks?.status === 'inventoried') {\n            ClassColor = 'border border-dark statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-dark'\n        }\n        else if (results.tasks?.status === 'assigned') {\n            ClassColor = 'border border-primary statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-primary'\n        }\n        else if (results.tasks?.status === 'delivered') {\n            ClassColor = 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-success'\n        }\n        else if (results.tasks?.status === 'not delivered') {\n            ClassColor = 'border border-danger statusLabel rounded px-2 min-w-50 d-flex justify-content-center text-white bg-danger'\n        }\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Stato}</span>\n                <div className={ClassColor}>\n                    {results.tasks?.status || 'N/A'}\n                </div>\n            </React.Fragment>\n        );\n    }\n    operator(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Operatore}</span>\n                {results.tasks?.operator?.idUser?.username || 'N/A'}\n            </React.Fragment>\n        );\n    }\n    nameBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Nome}</span>\n                {results.name}\n            </React.Fragment>\n        );\n    }\n    controller(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.externalSystem}</span>\n                {results.controller}\n            </React.Fragment>\n        );\n    }\n    erpSync(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Documento}</span>\n                <div className={results.erpSync === true ? 'border border-success rounded bg-success d-flex justify-content-center text-white' : 'border border-danger rounded bg-danger d-flex justify-content-center text-white'}>\n                    {results.erpSync === true ? 'Si' : 'No'}\n                </div>\n            </React.Fragment>\n        );\n    }\n    warehouse(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Magazzino}</span>\n                {results.warehouse}\n            </React.Fragment>\n        );\n    }\n    retailerBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.cliente}</span>\n                {results.retailer}\n            </React.Fragment>\n        );\n    }\n    operatorBodyTemplate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Operatore}</span>\n                {results.operator}\n            </React.Fragment>\n        );\n    }\n    paymentMetod(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.paymentMetod}</span>\n                {results.paymentMetod}\n            </React.Fragment>\n        );\n    }\n    physicalStock(results) {\n        var availability = results.productsAvailabilities.find(el => el.idWarehouse?.codDep === (results?.codDep !== undefined ? results?.codDep : '00'))\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.GiacenzaFisica}</span>\n                {availability !== undefined ? availability.physicalStock : 'Non disponibile'}\n            </React.Fragment>\n        );\n    }\n    committedCustomer(results) {\n        var availability = results.productsAvailabilities.find(el => el.idWarehouse?.codDep === (results?.codDep !== undefined ? results?.codDep : '00'))\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ImpegnataCliente}</span>\n                {availability !== undefined ? availability.committedCustomer : 'Non disponibile'}\n            </React.Fragment>\n        );\n    }\n    pesoNetto(results) {\n        var pesoNetto = `${parseFloat(results.idProductsPackaging?.pesoNetto).toFixed(2) * results.idProductsPackaging?.pcsXPackage}kg`\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PesoNetto}</span>\n                {pesoNetto}\n            </React.Fragment>\n        );\n    }\n    idLogEmail(results) {\n        var classColor = results.idLogEmail !== null ? 'border border-success statusLabel rounded px-2 min-w-50 d-flex justify-content-center align-items-center text-white bg-success py-1' : 'border border-warning statusLabel rounded px-2 min-w-50 d-flex justify-content-center align-items-center text-white bg-warning py-1'\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ImpegnataCliente}</span>\n                <div className={classColor}>\n                    {results.idLogEmail !== null ? Costanti.Inviata : Costanti.NonInviata}<i className={results.idLogEmail !== null ? 'pi pi-check ml-2' : 'pi pi-times-circle ml-2'}></i>\n                </div>\n            </React.Fragment>\n        );\n    }\n    selectFormato(results) {\n        var formati = []\n        results.productsPackagings.forEach(element => {\n            formati.push({ name: element.unitMeasure + ' X ' + element.pcsXPackage, value: element.id })\n        })\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ImpegnataCliente}</span>\n                <Dropdown value={results.productsPackagings.length === 1 ? results.productsPackagings[0].id : []} options={formati} onChange={(e) => results.productsPackagings = results.productsPackagings.filter(el => el.id === e.value)} optionLabel=\"name\" placeholder='Seleziona formato' />\n            </React.Fragment>\n        );\n    }\n    logMessage(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Mex}</span>\n                {results.message &&\n                    <>\n                        {results.message}\n                    </>\n                }\n\n            </React.Fragment>\n        );\n    }\n    discount_active(results) {\n        var percent = results.discount_active?.percent\n        var fixed = results.discount_active?.fixed\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.ScontoAttivo}</span>\n                {percent?.length > 0 &&\n                    <>\n                        {percent.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + \"% \"}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n                {fixed?.length > 0 &&\n                    <>\n                        {fixed.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + '€ '}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n            </React.Fragment>\n        );\n    }\n    inflation_active(results) {\n        var percent = results.inflation_active?.percent\n        var fixed = results.inflation_active?.fixed\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Rincaro}</span>\n                {percent?.length > 0 &&\n                    <>\n                        {percent.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + \"% \"}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n                {fixed?.length > 0 &&\n                    <>\n                        {fixed.map((el, key) => {\n                            return (\n                                <React.Fragment key={key}>\n                                    {el + '€ '}\n                                </React.Fragment>\n                            )\n                        })}\n                    </>\n                }\n            </React.Fragment>\n        );\n    }\n    displayCondition(scontiCondizionati) {\n        return (\n            <>\n                {scontiCondizionati &&\n                    scontiCondizionati.map((el, key) => {\n                        return (\n                            <React.Fragment key={key}>\n                                {`${el}`} {key !== scontiCondizionati.length - 1 ? ', ' : ''}\n                            </React.Fragment>\n                        )\n                    })\n                }\n            </>\n        )\n    }\n    conditioned_discount(results) {\n        var scontiCondizionati = []\n        results.conditioned_discount?.map(el => scontiCondizionati.push(el.correlati ? {acquistando: el.condizione, unità: el.correlati.map(item=>item).join(', '), ricevi: Object.keys(el.omaggio)[0], di: Object.values(el.omaggio).map(obj => obj.map(item => `${Object.keys(item)}: ${Object.values(item)}`).join(', '))} : (el.condizione ? `Acquistando ${el.condizione} unità ${el.percent ? `ricevi il ${el.percent}% di sconto` : `ricevi ${el.fixed} di sconto`}` : '')))\n        return (\n            <React.Fragment key={results.id}>\n                <span className=\"p-column-title\">{Costanti.ScontoCondizionato}</span>\n                {scontiCondizionati.length > 0 &&\n                    <OverlayPanelGen values={scontiCondizionati} label='Sconto' badge={true} />\n                }\n            </React.Fragment>\n        );\n    }\n    affiliate(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Affiliato}</span>\n                {results.affiliate}\n            </React.Fragment>\n        );\n    }\n    Supplying(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Fornitore}</span>\n                {results.supplying}\n            </React.Fragment>\n        );\n    }\n    affiliates(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Affiliati}</span>\n                {results.affiliate.length}\n            </React.Fragment>\n        );\n    }\n    retailers(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.Clienti}</span>\n                {results.retailer.length}\n            </React.Fragment>\n        );\n    }\n    pfa(results) {\n        var pfa = results.pfa.map(el => el.correlati !== undefined ? (el.percent !== undefined ? `Target: ${el.condizione} correlati: ${el.correlati}  premio: ${el.percent}%` : `Target: ${el.condizione} correlati: ${el.correlati} premio: ${el.fixed}`) : (el.percent !== undefined ? `Target: ${el.condizione} premio: ${el.percent}%` : `Target: ${el.condizione} premio: ${el.fixed}`)).join(', ')\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PremioFineAnno}</span>\n                {pfa}\n            </React.Fragment>\n        );\n    }\n    discountPayment(results) {\n        var discountPayment = results.discount_payment.map(el => el.correlati !== undefined ? (el.idPaymentMethod !== undefined ? `Id pagamento: ${el.idPaymentMethod} correlati: ${el.correlati} premio: ${el.percent}%` : `Id pagamento: ${el.idPaymentMethod} correlati: ${el.correlati} premio: ${el.fixed}`) : (el.idPaymentMethod !== undefined ? `Id pagamento: ${el.idPaymentMethod} premio: ${el.percent}%` : `Id pagamento: ${el.idPaymentMethod} premio: ${el.fixed}`)).join(', ')\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PremioFineAnno}</span>\n                {discountPayment}\n            </React.Fragment>\n        );\n    }\n    target(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PremioFineAnno}</span>\n                {parseInt(results.target) !== 0 ? results.target : 'Nessun target'}\n            </React.Fragment>\n        );\n    }\n    correlati(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PremioFineAnno}</span>\n                {results.correlati !== undefined ? results.correlati.map(el => el).join(', ') : ''}\n            </React.Fragment>\n        );\n    }\n    sconto(results) {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{Costanti.PremioFineAnno}</span>\n                {results.sconto}\n            </React.Fragment>\n        );\n    }\n    /* filtri custom */\n    delFilter() {\n        this.setState({ dateFilter: '' });\n        this.onDateFilterChange();\n        document.getElementById('filterByOrderDate').querySelectorAll('.p-inputtext')[0].classList.remove(\"p-filled\");\n    }\n    filterData() {\n        var classIcon = ''\n        var filtro = this.state.dateFilter\n        if (filtro !== undefined && filtro !== '') {\n            classIcon = 'pi pi-times ml-1'\n        } else {\n            classIcon = 'pi pi-times ml-1 d-none'\n        }\n        return (\n            <div className='d-flex align-items-center filterByOrderDate'>\n                <Calendar id=\"filterByOrderDate\" value={this.state.dateFilter} onChange={this.onDateFilterChange} placeholder=\"Filtra per data ordine\" dateFormat=\"dd/mm/yy\" className=\"p-column-filter\" />\n                <i className={classIcon} onClick={() => this.delFilter()}></i>\n            </div>\n        );\n    }\n    onDateFilterChange(event) {\n        if (event !== undefined) {\n            if (event.value !== null) {\n                this.dt.filter(this.formatDate(event.value), 'orderDate', 'contains');\n            } else {\n                this.dt.filter(null, 'orderDate', 'equals');\n            }\n            this.setState({ dateFilter: event.value });\n        } else {\n            this.dt.filter(null, 'orderDate', 'equals');\n        }\n    }\n    filterDate(value, filter) {\n        if (filter === undefined || filter === null || (typeof filter === 'string' && filter.trim() === '')) {\n            return true;\n        }\n        if (value === undefined || value === null) {\n            return false;\n        }\n        return value === this.formatDate(filter);\n    }\n    formatDate(date) {\n        if (date !== null) {\n            let month = date.getMonth() + 1;\n            let day = date.getDate();\n            if (month < 10) {\n                month = '0' + month;\n            }\n            if (day < 10) {\n                day = '0' + day;\n            }\n            return date.getFullYear() + '-' + month + '-' + day;\n        }\n    }\n    exportCSVGhostClick() {\n        document.querySelector('#CSVExport').click();\n    }\n    render() {\n        /* elemento generico per i filtri custom */\n        const dateFilter = this.filterData();\n        //Dichiarazione header del componente\n        const header = (\n            <div className={this.state.classHeader}>\n                <div className=\"col-12 col-lg-4 col-xl-6 mt-3 mb-2 mt-lg-0\">\n                    <span className={this.state.classInputSearch}>\n                        <i className=\"pi pi-search\" />\n                        <InputText className=\"w-100 clearControl\" type=\"search\" onInput={(e) => this.setState({ globalFilter: e.target.value })} placeholder=\"Cerca...\" />\n                        <i className=\"clear-icon pi pi-times invisible\" role='button' onClick={(e) => { document.querySelector('.clearControl').value = ''; this.setState({ globalFilter: null }) }} />\n                    </span>\n                </div>\n                <div className={this.state.classNameButton}>\n                    <div className=\"btns-actions d-flex justify-content-center justify-content-lg-end w-auto row\">\n                        <Button className={this.state.extraButton} onClick={this.props.actionExtraButton} disabled={this.props.disabledExtraButton} tooltip={this.props.tooltip} tooltipOptions={{ position: 'top' }} >{this.props.labelExtraButton} </Button>\n                        <Button className={this.state.extraButton2} onClick={this.props.actionExtraButton2} disabled={this.props.disabledExtraButton2} >{this.props.labelExtraButton2} </Button>\n                        {/* Bottone per l'esport CSV */}\n                        <ScaricaCSVProva label={'esportaCSV'} className={this.state.exportCsvButton} results={this.props.value} fileNames={this.props.fileNames} />\n                    </div>\n                </div>\n                <div className={this.state.singleButton}>\n                    <div className=\"btns-actions d-flex justify-content-end justify-content-lg-end w-auto row\">\n                        <Button className={this.state.extraButton} onClick={this.props.actionExtraButton} >{this.props.labelExtraButton} </Button>\n                    </div>\n                </div>\n                <div className={this.state.splitButtonClass}>\n                    <div className=\"btns-actions d-flex justify-content-center justify-content-lg-end w-auto row\">\n                        {this.props.optionalButton &&\n                            <Button className={this.props.classButton} onClick={this.props.actionExtraButton} disabled={this.props.disabledExtraButton} tooltip={this.props.tooltip} tooltipOptions={{ position: 'top' }} >{this.props.labelExtraButton} </Button>\n                        }\n                        {/* 🏴‍☠️ BAD FIX 🏴‍☠️ Nasconde bottone ExportCSV per aggiungerlo a SplitButton Azioni. \n                        Il fix finge un click sul bottone ScaricaCSVProva che è presente nella pagina con attributo hidden */}\n                        <span className='d-none'>\n                            {this.props.value !== null && this.state.addCSVBTN && this.props.items?.unshift({\n                                label: Costanti.esportaCSV,\n                                command: () => {\n                                    this.exportCSVGhostClick()\n                                }\n                            })\n                            }\n                        </span>\n                        <SplitButton label=\"Azioni\" icon='pi pi-cog' model={this.props.items} className=\"splitButtonGen mr-2 mb-0\"></SplitButton>\n                        <ScaricaCSVProva hidden={true} className={this.state.exportCsvButton} results={this.props.value} fileNames={this.props.fileNames} />\n                    </div>\n                </div>\n            </div>\n        );\n        return (\n            <div className=\"card\">\n                <DataTable className=\"p-datatable-responsive-demo\"\n                    ref={(el) => this.dt = el}\n                    value={this.props.value}\n                    loading={this.props.loading}\n                    dataKey={this.props.dataKey}\n                    paginator={this.props.paginator}\n                    rows={this.props.rows}\n                    rowsPerPageOptions={this.props.rowsPerPageOptions}\n                    sortField={this.props.sortField}\n                    sortOrder={this.props.sortOrder}\n                    autoLayout={this.props.autoLayout}\n                    globalFilter={this.state.globalFilter}\n                    header={this.props.hideHeader ? null : (this.props.header ? this.props.header : header)}\n                    emptyMessage={Costanti.EmptyMessage}\n                    selectionMode={this.props.selectionMode}\n                    selection={this.props.selection}\n                    onSelectionChange={this.props.onSelectionChange}\n                    responsiveLayout={this.props.responsiveLayout}\n                    onRowSelect={this.props.onRowSelect}\n                    expandedRows={this.props.expandedRows}\n                    onRowToggle={this.props.onRowToggle}\n                    onRowExpand={this.props.onRowExpand}\n                    onRowCollapse={this.props.onRowCollapse}\n                    rowExpansionTemplate={this.props.rowExpansionTemplate}\n                    onCellSelect={(e) => e.field !== 'action' ? this.props.actionsColumn[0].handler(e.rowData) : null}\n                    cellSelection={this.props.cellSelection}\n                    id={this.props.id}\n                    editMode={this.props.editMode}\n                    onPage={this.props.onPage}\n                    first={this.props.first}\n                    lazy={this.props.lazy}\n                    totalRecords={this.props.totalRecords}\n                    filterDisplay={this.props.filterDisplay}\n                    onSort={this.props.onSort}\n                    onFilter={this.props.onFilter}\n                    filters={this.props.filters}\n                    csvSeparator=\";\"\n                >\n                    {this.props.fields?.map((el) =>\n                        <Column\n                            key={Math.random()}\n                            className={el.className}\n                            expander={el.expander}\n                            field={el.field}\n                            filterFunction={el.filterFunction}\n                            filterField={el.filterField}\n                            header={el.showHeader ? el.header : null}\n                            sortable={el.sortable}\n                            filter={el.filter}\n                            filterMatchMode={el.filterMatchMode}\n                            filterElement={dateFilter}\n                            filterPlaceholder={el.filterPlaceholder}\n                            body={el.body !== undefined ? (row) => this.handlers[el.body](row) : null}\n                            headerStyle={el.headerStyle}\n                            selectionMode={el.selectionMode}\n                        >\n                        </Column>\n                    )}\n                    {this.props.actionsColumn && (\n                        <Column\n                            className='tableMenu'\n                            field='action'\n                            body={(e) =>\n                                !e.area || e?.area !== 'N/D' ? (\n                                    <MenuItem fields={this.props.actionsColumn} key={e}\n                                        rowData={e}>\n                                    </MenuItem>\n                                ) :\n                                    null\n                            }\n                        />\n                    )}\n                </DataTable>\n            </div>\n        );\n    }\n}\n\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,sDAAsD;AAClF,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,KAAKC,MAAM,MAAM,2BAA2B;AACnD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,eAAe,QAAQ,oCAAoC;AACpE,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,eAAe,MAAMC,eAAe,SAASpB,KAAK,CAACqB,SAAS,CAAC;EACzDC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IA8OhB;IAAA,KACAC,wBAAwB,GAAIC,OAAO,IAAK;MACpC,IAAIA,OAAO,CAACC,YAAY,KAAK,IAAI,IAAID,OAAO,CAACC,YAAY,KAAKC,SAAS,EAAE;QACrE;QACA,MAAMC,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;QACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,YAAY,CAAC;QAC7C,oBACIT,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACqC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC9DP,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAEzB;IACJ,CAAC;IACD;IAAA,KACAC,qBAAqB,GAAIxB,OAAO,IAAK;MACjC;MACA,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,SAAS;QAAEa,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC,CAACd,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CAC1LQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAAC4B,SAAS,CAAC;MACzC,oBACIpC,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+C;QAAY;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC9DP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IACD;IAAA,KACAO,qBAAqB,GAAI9B,OAAO,IAAK;MACjC;MACA,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAAC+B,SAAS,CAAC;MACzC,oBACIvC,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+C;QAAY;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC9DP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IAAA,KACDS,UAAU,GAAIhC,OAAO,IAAK;MACtB,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAACgC,UAAU,CAAC;MAC1C,oBACIxC,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACmD;QAAS;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3DP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IAAA,KACDW,QAAQ,GAAIlC,OAAO,IAAK;MACpB,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAACkC,QAAQ,CAAC;MACxC,oBACI1C,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACqD;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IACD;IAAA,KACAa,oBAAoB,GAAIpC,OAAO,IAAK;MAChC,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAACqC,QAAQ,CAAC;MACxC,oBACI7C,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+C;QAAY;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC9DP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IACD;IAAA,KACAe,oBAAoB,GAAItC,OAAO,IAAK;MAChC,MAAMG,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;MACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAACuC,QAAQ,CAAC;MACxC,oBACI/C,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC0D;QAAc;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAChEP,MAAM;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAEzB,CAAC;IACD;IAAA,KACAkB,qBAAqB,GAAIzC,OAAO,IAAK;MACjC,IAAIA,OAAO,KAAKE,SAAS,EAAE;QACvB;QACA,MAAMC,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;QACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAAC0C,SAAS,CAAC;QACzC,oBACIlD,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACmD;UAAS;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3DP,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAEzB,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IAAA,KACAoB,mBAAmB,GAAI3C,OAAO,IAAK;MAC/B,IAAIA,OAAO,KAAKE,SAAS,EAAE;QACvB;QACA,MAAMC,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CACnIQ,IAAI,CAAC,KAAK,CAAC;QACpB,MAAMC,MAAM,GAAGb,OAAO,CAACH,OAAO,CAAC4C,OAAO,CAAC;QACvC,oBACIpD,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACqD;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACzDP,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAEzB,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IAAA,KACAsB,mBAAmB,GAAIC,OAAO,IAAK;MAC/B,IAAIA,OAAO,CAACC,OAAO,KAAK7C,SAAS,IAAI4C,OAAO,CAACE,QAAQ,KAAK9C,SAAS,EAAE;QACjE,IAAI4C,OAAO,CAACC,OAAO,KAAK,IAAI,IAAID,OAAO,CAACE,QAAQ,KAAK,IAAI,EAAE;UACvD,oBACIxD,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAACmE;YAAQ;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D/B,OAAA;cAAM0B,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAEzB,CAAC,MAAM;UACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAACmE;YAAQ;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D/B,OAAA;cAAM0B,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAEzB;MACJ,CAAC,MAAM;QACH;QACA,IAAI2B,OAAO,GAAG,IAAIpC,IAAI,CAAC,CAAC;QACxB;QACA,IAAIH,KAAK,GAAGuC,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;QAClC,IAAIzC,GAAG,GAAGwC,OAAO,CAACE,OAAO,CAAC,CAAC;QAC3B;QACA,IAAIzC,KAAK,GAAG,EAAE,EAAE;UACZA,KAAK,GAAG,GAAG,GAAGA,KAAK;QACvB;QACA,IAAID,GAAG,GAAG,EAAE,EAAE;UACVA,GAAG,GAAG,GAAG,GAAGA,GAAG;QACnB;QACAwC,OAAO,GAAGA,OAAO,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG1C,KAAK,GAAG,GAAG,GAAGD,GAAG;QACzD,IAAIoC,OAAO,CAACF,OAAO,GAAGM,OAAO,EAAE;UAC3B,oBACI1D,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAACmE;YAAQ;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D/B,OAAA;cAAM0B,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAEzB,CAAC,MAAM;UACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAACmE;YAAQ;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D/B,OAAA;cAAM0B,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAEzB;MACJ;IACJ,CAAC;IAAA,KACD+B,gBAAgB,GAAItD,OAAO,IAAK;MAC5B,IAAIA,OAAO,CAACuD,KAAK,KAAKrD,SAAS,EAAE;QAC7B,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC0E;UAAM;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxDvB,OAAO,CAACuD,KAAK,CAACE,MAAM;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC0E;UAAM;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAE7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAEzB;IACJ,CAAC;IACD;IAAA,KACAmC,oBAAoB,GAAI1D,OAAO,IAAK;MAChC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD/B,OAAA;UAAAyB,QAAA,EAAIjB,OAAO,CAAC2D;QAAQ;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAEzB,CAAC;IAAA;IAAA,KACDqC,qBAAqB,GAAI5D,OAAO,IAAK;MACjC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+E;QAAQ;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3D/B,OAAA;UAAAyB,QAAA,GAAIjB,OAAO,CAAC8D,SAAS,EAAC,GAAC,EAAC9D,OAAO,CAAC+D,QAAQ;QAAA;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAEzB,CAAC;IACD;IAAA,KACAyC,mBAAmB,GAAIhE,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACmF;QAAS;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACkE,OAAO;MAAA;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEzB,CAAC;IACD;IAAA,KACA4C,gBAAgB,GAAInE,OAAO,IAAK;MAC5B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsF;QAAK;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACqE,IAAI;MAAA;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEzB,CAAC;IACD;IAAA,KACA+C,eAAe,GAAItE,OAAO,IAAK;MAC3B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyF;QAAO;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAACwE,GAAG;MAAA;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAEzB,CAAC;IACD;IAAA,KACAkD,gBAAgB,GAAIzE,OAAO,IAAK;MAC5B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC4F;QAAI;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC0E,IAAI;MAAA;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAoD,eAAe,GAAI3E,OAAO,IAAK;MAC3B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8F;QAAG;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrDvB,OAAO,CAAC6E,GAAG;MAAA;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAEzB,CAAC;IACD;IAAA,KACAuD,iBAAiB,GAAI9E,OAAO,IAAK;MAC7B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiG;QAAK;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACgF,KAAK;MAAA;QAAA5D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEzB,CAAC;IACD;IAAA,KACA0D,cAAc,GAAIjF,OAAO,IAAK;MAC1B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACzCvB,OAAO,CAACkF,EAAE;MAAA;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEzB,CAAC;IACD;IAAA,KACA4D,uBAAuB,GAAInF,OAAO,IAAK;MACnC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsG;QAAI;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAACqF,WAAW;MAAA;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEzB,CAAC;IACD;IAAA,KACA+D,wBAAwB,GAAItF,OAAO,IAAK;MACpC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyG;QAAM;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDvB,OAAO,CAACwF,YAAY;MAAA;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEzB,CAAC;IAAA,KACDkE,kBAAkB,GAAIzF,OAAO,IAAK;MAC9B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyG;QAAM;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDvB,OAAO,CAAC0F,UAAU,CAACF,YAAY;MAAA;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEzB,CAAC;IACD;IAAA,KACAoE,yBAAyB,GAAI3F,OAAO,IAAK;MACrC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8G;QAAO;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAAC6F,aAAa;MAAA;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEzB,CAAC;IACD;IAAA,KACAuE,mBAAmB,GAAI9F,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiH;QAAO;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAAC+F,OAAO;MAAA;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEzB,CAAC;IACD;IAAA,KACAyE,kBAAkB,GAAIhG,OAAO,IAAK;MAC9B,IAAIA,OAAO,CAACiG,MAAM,KAAK,QAAQ,EAAE;QAC7B,oBACIzG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACoH;UAAM;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD/B,OAAA;YAAM0B,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACoH;UAAM;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD/B,OAAA;YAAM0B,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAEzB;IACJ,CAAC;IAAA,KAeD4E,qBAAqB,GAAInG,OAAO,IAAK;MACjC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsH;QAAS;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5D/B,OAAA;UAAAyB,QAAA,EAAIjB,OAAO,CAACqG;QAAS;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEzB,CAAC;IAAA,KACD+E,0BAA0B,GAAItG,OAAO,IAAK;MACtC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyH;QAAc;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAChEvB,OAAO,CAACuG,cAAc;MAAA;QAAAnF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAEzB,CAAC;IAAA,KACDiF,2BAA2B,GAAIxG,OAAO,IAAK;MACvC,IAAIA,OAAO,CAACyG,gBAAgB,KAAKvG,SAAS,EAAE;QACxC,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACyH;UAAc;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAChEvB,OAAO,CAACyG,gBAAgB,CAACC,kBAAkB;QAAA;UAAAtF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACyH;UAAc;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAChEvB,OAAO,CAAC0G,kBAAkB;QAAA;UAAAtF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAEzB;IACJ,CAAC;IACD;IAAA,KACAoF,+BAA+B,GAAI3G,OAAO,IAAK;MAC3C,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8H;QAAY;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC9DvB,OAAO,CAAC6G,mBAAmB;MAAA;QAAAzF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAEzB,CAAC;IAAA,KACDuF,yBAAyB,GAAI9G,OAAO,IAAK;MACrC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiI;QAAO;UAAA3F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAACgH,aAAa;MAAA;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEzB,CAAC;IAAA,KACD0F,wBAAwB,GAAIjH,OAAO,IAAK;MAAA,IAAAkH,mBAAA;MACpC,oBACI1H,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACqI;QAAS;UAAA/F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,GAAA2F,mBAAA,GAC3DlH,OAAO,CAACoH,UAAU,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBG,UAAU,CAACC,YAAY;MAAA;QAAAlG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAEzB,CAAC;IAAA,KACDgG,mBAAmB,GAAIvH,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC0I;QAAO;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACkGvB,OAAO,CAACiG,MAAM;MAAA;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9J,CAAC;IAEzB,CAAC;IAAA,KACDkG,qBAAqB,GAAIzH,OAAO,IAAK;MACjC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC4I;QAAK;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACiG,MAAM;MAAA;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEzB,CAAC;IAAA,KACDoG,eAAe,GAAI3H,OAAO,IAAK;MAC3B,IAAI,IAAI,CAAC4H,KAAK,CAACC,IAAI,KAAKxI,YAAY,EAAE;QAClC,oBACIG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACgJ;UAAG;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACrD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACrH,MAAM,CAACb,OAAO,CAACmI,KAAK,CAAC;QAAA;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACgJ;UAAG;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACrD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACpH,MAAM,CAACb,OAAO,CAACmI,KAAK,CAAC;QAAA;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAEzB;IAEJ,CAAC;IAAA,KACD6G,kBAAkB,GAAIpI,OAAO,IAAK;MAC9B,IAAI,IAAI,CAAC4H,KAAK,CAACC,IAAI,KAAKxI,YAAY,EAAE;QAClC,oBACIG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACuJ;UAAM;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACrH,MAAM,CAACb,OAAO,CAACsI,UAAU,CAAC;QAAA;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChH,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAACuJ;UAAM;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACpH,MAAM,CAACb,OAAO,CAACsI,UAAU,CAAC;QAAA;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC;MAEzB;IACJ,CAAC;IAAA,KACDgH,sBAAsB,GAAIvI,OAAO,IAAK;MAClC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC0J;QAAM;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDkH,UAAU,CAACzI,OAAO,CAAC0I,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;MAAA;QAAAvH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAEzB,CAAC;IAAA,KACDqH,eAAe,GAAI5I,OAAO,IAAK;MAC3B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+J;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACpH,MAAM,CAAC4H,UAAU,CAACzI,OAAO,CAACsI,UAAU,CAAC,GAAGG,UAAU,CAACzI,OAAO,CAACmI,KAAK,CAAC,CAAC;MAAA;QAAA/G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9H,CAAC;IAEzB,CAAC;IAAA,KACDuH,eAAe,GAAI9I,OAAO,IAAK;MAC3B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+J;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrDvB,OAAO,CAAC+I,GAAG;MAAA;QAAA3H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAEzB,CAAC;IAAA,KACDyH,mBAAmB,GAAIhJ,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+J;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrDvB,OAAO,CAAC0F,UAAU,CAACuD,GAAG,EAAC,GAC5B;MAAA;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAEzB,CAAC;IACD;IAAA,KACA2H,wBAAwB,GAAIlJ,OAAO,IAAK;MACpC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+E;QAAQ;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3D/B,OAAA;UAAAyB,QAAA,EAAIjB,OAAO,CAACoH,UAAU,CAACC,UAAU,CAACvD;QAAS;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAEzB,CAAC;IACD;IAAA,KACA4H,0BAA0B,GAAInJ,OAAO,IAAK;MACtC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+E;QAAQ;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3D/B,OAAA;UAAAyB,QAAA,EAAIjB,OAAO,CAACqH,UAAU,CAACvD;QAAS;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAEzB,CAAC;IACD;IAAA,KACA6H,yBAAyB,GAAIpJ,OAAO,IAAK;MACrC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC+E;QAAQ;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3D/B,OAAA;UAAAyB,QAAA,EAAIjB,OAAO,CAAC2D;QAAQ;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAEzB,CAAC;IACD;IAAA,KACA8H,uBAAuB,GAAIrJ,OAAO,IAAK;MACnC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACmF;QAAS;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACqH,UAAU,CAACnD,OAAO;MAAA;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAEzB,CAAC;IACD;IAAA,KACA+H,oBAAoB,GAAItJ,OAAO,IAAK;MAChC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsF;QAAK;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACqH,UAAU,CAAChD,IAAI;MAAA;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAEzB,CAAC;IACD;IAAA,KACAgI,mBAAmB,GAAIvJ,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyF;QAAO;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAACqH,UAAU,CAAC7C,GAAG;MAAA;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAEzB,CAAC;IACD;IAAA,KACAiI,oBAAoB,GAAIxJ,OAAO,IAAK;MAChC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC4F;QAAI;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAACqH,UAAU,CAAC3C,IAAI;MAAA;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAEzB,CAAC;IACD;IAAA,KACAkI,mBAAmB,GAAIzJ,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8F;QAAG;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrDvB,OAAO,CAACqH,UAAU,CAACxC,GAAG;MAAA;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAEzB,CAAC;IACD;IAAA,KACAmI,qBAAqB,GAAI1J,OAAO,IAAK;MACjC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiG;QAAK;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACqH,UAAU,CAACrC,KAAK;MAAA;QAAA5D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAEzB,CAAC;IAAA,KACDoI,GAAG,GAAI3J,OAAO,IAAK;MACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8K;QAAQ;UAAAxI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC2J,GAAG;MAAA;QAAAvI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAEzB,CAAC;IAAA,KACDsI,MAAM,GAAI7J,OAAO,IAAK;MAClB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACgJ;QAAG;UAAA1G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACpH,MAAM,CAACb,OAAO,CAAC6J,MAAM,CAAC;MAAA;QAAAzI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAEzB,CAAC;IAAA,KACDuI,YAAY,GAAI9J,OAAO,IAAK;MACxB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyG;QAAM;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDvB,OAAO,CAAC8J,YAAY;MAAA;QAAA1I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEzB,CAAC;IACD;IAAA,KACAwI,uBAAuB,GAAI/J,OAAO,IAAK;MACnC,IAAIA,OAAO,CAACiG,MAAM,KAAK/F,SAAS,EAAE;QAC9B,IAAIF,OAAO,CAACiG,MAAM,CAAC+D,WAAW,CAAC,CAAC,KAAK,OAAO,EAAE;UAC1C,IAAI,CAAC/D,MAAM,GAAG,CACV;YAAEgE,IAAI,EAAE;UAAQ,CAAC,EACjB;YAAEA,IAAI,EAAE;UAAa,CAAC,CACzB;QACL,CAAC,MAAM,IAAIjK,OAAO,CAACiG,MAAM,CAAC+D,WAAW,CAAC,CAAC,KAAK,YAAY,EAAE;UACtD;AAChB;AACA;UACgB,oBACIxK,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAAC0I;YAAO;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACzDvB,OAAO,CAACiG,MAAM;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEzB,CAAC,MAAM;UACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAEnC,QAAQ,CAAC0I;YAAO;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACzDvB,OAAO,CAACiG,MAAM;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEzB;QACA,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC0I;UAAO;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1D/B,OAAA,CAACP,QAAQ;YAACiL,KAAK,EAAE,IAAI,CAACtC,KAAK,CAACuC,cAAe;YAACC,OAAO,EAAE,IAAI,CAACnE,MAAO;YAACoE,QAAQ,EAAGC,CAAC,IAAK,IAAI,CAACC,cAAc,CAACD,CAAC,EAAEtK,OAAO,CAAE;YAACwK,WAAW,EAAC,MAAM;YAACC,YAAY,EAAC,+BAA+B;YAACC,WAAW,EAAE1K,OAAO,CAACiG;UAAO;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvM,CAAC;MAEzB;IACJ,CAAC;IAwDD;IAAA,KACAoJ,mBAAmB,GAAI3K,OAAO,IAAK;MAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8L;QAAO;UAAAxJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAAC6K,OAAO;MAAA;QAAAzJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEzB,CAAC;IAAA,KACDuJ,wBAAwB,GAAIhI,OAAO,IAAK;MACpC,oBACItD,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiM;QAAQ;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1DuB,OAAO,CAACkI,YAAY;MAAA;QAAA5J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEzB,CAAC;IAAA,KACD0J,uBAAuB,GAAIjL,OAAO,IAAK;MACnC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACoM;QAAK;UAAA9J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAACmL,WAAW;MAAA;QAAA/J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEzB,CAAC;IAAA,KAkBD6J,iBAAiB,GAAIpL,OAAO,IAAK;MAC7B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8K;QAAQ;UAAAxI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1DvB,OAAO,CAACqL,QAAQ,GAAGrL,OAAO,CAACsL,WAAW;MAAA;QAAAlK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAEzB,CAAC;IAAA,KACDgK,oBAAoB,GAAIvL,OAAO,IAAK;MAChC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC0M;QAAM;UAAApK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDvB,OAAO,CAACqL,QAAQ;MAAA;QAAAjK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEzB,CAAC;IAAA,KACDkK,uBAAuB,GAAIzL,OAAO,IAAK;MACnC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC4M;QAAK;UAAAtK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDvB,OAAO,CAAC2L,WAAW;MAAA;QAAAvK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEzB,CAAC;IAAA,KACDqK,0BAA0B,GAAI5L,OAAO,IAAK;MACtC,IAAI6L,UAAU,GAAG,EAAE;MACnB,IAAI7L,OAAO,CAAC8L,cAAc,KAAK,UAAU,EAAE;QACvCD,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAAC8L,cAAc,KAAK,QAAQ,EAAE;QAC1CD,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAAC8L,cAAc,KAAK,WAAW,EAAE;QAC7CD,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAAC8L,cAAc,KAAK,eAAe,EAAE;QACjDD,UAAU,GAAG,2GAA2G;MAC5H,CAAC,MAAM;QAAEA,UAAU,GAAG,EAAE;MAAC;MACzB,oBACIrM,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACiN;QAAQ;UAAA3K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3D/B,OAAA;UAAK0B,SAAS,EAAE2K,UAAW;UAAA5K,QAAA,EACtBjB,OAAO,CAAC8L;QAAc;UAAA1K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAEzB,CAAC;IAAA,KACDyK,yBAAyB,GAAIhM,OAAO,IAAK;MACrC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACmN;QAAO;UAAA7K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzDvB,OAAO,CAACkM,YAAY;MAAA;QAAA9K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEzB,CAAC;IAAA,KACD4K,gBAAgB,GAAInM,OAAO,IAAK;MAAA,IAAAoM,aAAA;MAC5B,IAAIC,GAAG,GAAG,GAAG;MACb,IAAI,EAAAD,aAAA,GAAApM,OAAO,CAACsM,IAAI,cAAAF,aAAA,uBAAZA,aAAA,CAAc3I,MAAM,IAAG4I,GAAG,EAAE;QAC5BrM,OAAO,CAACsM,IAAI,GAAGtM,OAAO,CAACsM,IAAI,CAACC,MAAM,CAAC,CAAC,EAAEF,GAAG,CAAC;MAC9C;MACA,oBACI7M,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC0N;QAAI;UAAApL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAACsM,IAAI;MAAA;QAAAlL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEzB,CAAC;IAAA,KACDkL,qBAAqB,GAAIzM,OAAO,IAAK;MACjC,IAAI,IAAI,CAAC4H,KAAK,CAACC,IAAI,KAAKxI,YAAY,EAAE;QAClC,oBACIG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC4N;UAAM;YAAAtL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACrH,MAAM,CAACb,OAAO,CAAC2M,SAAS,CAAC;QAAA;UAAAvL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC4N;UAAM;YAAAtL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACpH,MAAM,CAACb,OAAO,CAAC2M,SAAS,CAAC;QAAA;UAAAvL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAEzB;IACJ,CAAC;IAAA,KACDqL,iBAAiB,GAAI5M,OAAO,IAAK;MAC7B,IAAI,IAAI,CAAC4H,KAAK,CAACC,IAAI,KAAKxI,YAAY,EAAE;QAClC,oBACIG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC4N;UAAM;YAAAtL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE,KAAK;YAAEC,qBAAqB,EAAE;UAAE,CAAC,CAAC,CAACrH,MAAM,CAAC4H,UAAU,CAACzI,OAAO,CAAC6M,KAAK,KAAK3M,SAAS,GAAGF,OAAO,CAAC6M,KAAK,GAAG7M,OAAO,CAAC8M,OAAO,CAAC,CAAC;QAAA;UAAA1L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvK,CAAC;MAEzB,CAAC,MAAM;QACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,gBACXzB,OAAA;YAAM0B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAEnC,QAAQ,CAAC4N;UAAM;YAAAtL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACpH,MAAM,CAAC4H,UAAU,CAACzI,OAAO,CAAC6M,KAAK,KAAK3M,SAAS,GAAGF,OAAO,CAAC6M,KAAK,GAAG7M,OAAO,CAAC8M,OAAO,CAAC,CAAC;QAAA;UAAA1L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC;MAEzB;IACJ,CAAC;IAAA,KACDwL,GAAG,GAAI/M,OAAO,IAAK;MACf,IAAIA,OAAO,CAAC+M,GAAG,KAAK,IAAI,IAAI/M,OAAO,CAAC+M,GAAG,KAAK,GAAG,EAAE;QAC7C,IAAI,IAAI,CAACnF,KAAK,CAACC,IAAI,KAAKxI,YAAY,EAAE;UAClC,oBACIG,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC1C,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE,KAAK;cAAEC,qBAAqB,EAAE;YAAE,CAAC,CAAC,CAACrH,MAAM,CAAC4H,UAAU,CAACzI,OAAO,CAAC+M,GAAG,CAAC,CAAC;UAAA;YAAA3L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAEzB,CAAC,MAAM;UACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,gBACXzB,OAAA;cAAM0B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC1C,IAAIf,IAAI,CAACuH,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACpH,MAAM,CAAC4H,UAAU,CAACzI,OAAO,CAAC+M,GAAG,CAAC,CAAC;UAAA;YAAA3L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAEzB;MACJ;IAEJ,CAAC;IAAA,KACDyL,oBAAoB,GAAIhN,OAAO,IAAK;MAChC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsG;QAAI;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAACiN,SAAS,KAAK/M,SAAS,GAAGF,OAAO,CAACiN,SAAS,CAAC5H,WAAW,GAAGrF,OAAO,CAACkN,OAAO,CAAC7H,WAAW;MAAA;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAEzB,CAAC;IAAA,KACD4L,8BAA8B,GAAInN,OAAO,IAAK;MAC1C,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACsG;QAAI;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC0F,UAAU,CAACL,WAAW;MAAA;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEzB,CAAC;IAAA,KACD6L,kBAAkB,GAAIpN,OAAO,IAAK;MAC9B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACyG;QAAM;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxDvB,OAAO,CAACiN,SAAS,KAAK/M,SAAS,GAAGF,OAAO,CAACiN,SAAS,CAACzH,YAAY,GAAGxF,OAAO,CAACkN,OAAO,CAAC1H,YAAY;MAAA;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAEzB,CAAC;IAAA,KA8RD8L,mBAAmB,GAAIrN,OAAO,IAAK;MAC/B,IAAI6L,UAAU,GAAG,EAAE;MACnB,IAAI7L,OAAO,CAACiG,MAAM,KAAK,UAAU,EAAE;QAC/B4F,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,UAAU,EAAE;QACpC4F,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,YAAY,EAAE;QACtC4F,UAAU,GAAG,iHAAiH;MAClI,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,UAAU,EAAE;QACpC4F,UAAU,GAAG,2GAA2G;MAC5H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,QAAQ,EAAE;QAClC4F,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,SAAS,EAAE;QACnC4F,UAAU,GAAG,uGAAuG;MACxH,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,aAAa,EAAE;QACvC4F,UAAU,GAAG,uGAAuG;MACxH,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,UAAU,EAAE;QACpC4F,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,WAAW,EAAE;QACrC4F,UAAU,GAAG,6GAA6G;MAC9H,CAAC,MACI,IAAI7L,OAAO,CAACiG,MAAM,KAAK,eAAe,EAAE;QACzC4F,UAAU,GAAG,2GAA2G;MAC5H;MACA,oBACIrM,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC4I;QAAK;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxD/B,OAAA;UAAK0B,SAAS,EAAE2K,UAAW;UAAA5K,QAAA,EACtBjB,OAAO,CAACiG;QAAM;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAEzB,CAAC;IA96CG,IAAI,CAACqG,KAAK,GAAG;MACT5H,OAAO,EAAE,IAAI;MACbsN,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,KAAK;MACzBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,IAAI,CAACC,WAAW;MACxBC,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE,8BAA8B;MAC3CC,YAAY,EAAE,8BAA8B;MAC5CC,eAAe,EAAE,2CAA2C;MAC5DC,eAAe,EAAE,qCAAqC;MACtDC,gBAAgB,EAAE,sDAAsD;MACxEC,WAAW,EAAE,kBAAkB;MAC/BC,gBAAgB,EAAE,QAAQ;MAC1BC,YAAY,EAAE,QAAQ;MACtBC,SAAS,EAAE,KAAK;MAChB9G,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAAC+G,QAAQ,GAAG;MACZ1J,EAAE,EAAE,IAAI,CAACD,cAAc;MACvBI,WAAW,EAAE,IAAI,CAACF,uBAAuB;MACzCzC,SAAS,EAAE,IAAI,CAACD,qBAAqB;MACrCG,OAAO,EAAE,IAAI,CAACD,mBAAmB;MACjCI,OAAO,EAAE,IAAI,CAACF,mBAAmB;MACjCS,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCK,QAAQ,EAAE,IAAI,CAACD,oBAAoB;MACnCI,SAAS,EAAE,IAAI,CAACF,qBAAqB;MACrCM,OAAO,EAAE,IAAI,CAACF,mBAAmB;MACjCK,IAAI,EAAE,IAAI,CAACF,gBAAgB;MAC3BK,GAAG,EAAE,IAAI,CAACF,eAAe;MACzBI,IAAI,EAAE,IAAI,CAACD,gBAAgB;MAC3BI,GAAG,EAAE,IAAI,CAACF,eAAe;MACzBK,KAAK,EAAE,IAAI,CAACF,iBAAiB;MAC7B+J,KAAK,EAAE,IAAI,CAACC,iBAAiB;MAC7BtJ,YAAY,EAAE,IAAI,CAACF,wBAAwB;MAC3CO,aAAa,EAAE,IAAI,CAACF,yBAAyB;MAC7CI,OAAO,EAAE,IAAI,CAACD,mBAAmB;MACjCzD,QAAQ,EAAE,IAAI,CAACD,oBAAoB;MACnCL,SAAS,EAAE,IAAI,CAACD,qBAAqB;MACrCS,QAAQ,EAAE,IAAI,CAACD,oBAAoB;MACnC2D,MAAM,EAAE,IAAI,CAACD,kBAAkB;MAC/BK,SAAS,EAAE,IAAI,CAACF,qBAAqB;MACrCI,cAAc,EAAE,IAAI,CAACD,0BAA0B;MAC/CO,mBAAmB,EAAE,IAAI,CAACF,+BAA+B;MACzD/E,SAAS,EAAE,IAAI,CAACJ,qBAAqB;MACrCvB,YAAY,EAAE,IAAI,CAACF,wBAAwB;MAC3CmM,YAAY,EAAE,IAAI,CAACjF,wBAAwB;MAC3CD,aAAa,EAAE,IAAI,CAACF,yBAAyB;MAC7CiI,OAAO,EAAE,IAAI,CAACxH,mBAAmB;MACjCY,KAAK,EAAE,IAAI,CAACR,eAAe;MAC3BW,UAAU,EAAE,IAAI,CAACF,kBAAkB;MACnCM,UAAU,EAAE,IAAI,CAACH,sBAAsB;MACvCQ,GAAG,EAAE,IAAI,CAACH,eAAe;MACzBoG,YAAY,EAAE,IAAI,CAAC9F,wBAAwB;MAC3C+F,WAAW,EAAE,IAAI,CAAClF,uBAAuB;MACzCc,OAAO,EAAE,IAAI,CAACF,mBAAmB;MACjCuE,eAAe,EAAE,IAAI,CAAC1I,2BAA2B;MACjDwE,YAAY,EAAE,IAAI,CAACF,wBAAwB;MAC3CK,WAAW,EAAE,IAAI,CAACF,uBAAuB;MACzCK,WAAW,EAAE,IAAI,CAAC6D,kBAAkB;MACpCC,KAAK,EAAE,IAAI,CAAChE,iBAAiB;MAC7BC,QAAQ,EAAE,IAAI,CAACE,oBAAoB;MACnCI,WAAW,EAAE,IAAI,CAACF,uBAAuB;MACzCK,cAAc,EAAE,IAAI,CAACF,0BAA0B;MAC/CyD,aAAa,EAAE,IAAI,CAACrD,yBAAyB;MAC7CM,IAAI,EAAE,IAAI,CAACH,gBAAgB;MAC3BQ,SAAS,EAAE,IAAI,CAACF,qBAAqB;MACrCxD,GAAG,EAAE,IAAI,CAACH,eAAe;MACzBwG,QAAQ,EAAE,IAAI,CAACtC,oBAAoB;MACnCuC,MAAM,EAAE,IAAI,CAACnC,kBAAkB;MAC/BoC,kBAAkB,EAAE,IAAI,CAACrC,8BAA8B;MACvD5H,MAAM,EAAE,IAAI,CAACE,kBAAkB;MAC/BoH,KAAK,EAAE,IAAI,CAACD,iBAAiB;MAC7B6C,OAAO,EAAE,IAAI,CAACzG,mBAAmB;MACjC0G,KAAK,EAAE,IAAI,CAACC,iBAAiB;MAC7BC,QAAQ,EAAE,IAAI,CAACC,oBAAoB;MACnCC,QAAQ,EAAE,IAAI,CAACC,oBAAoB;MACnCC,MAAM,EAAE,IAAI,CAACC,kBAAkB;MAC/BC,OAAO,EAAE,IAAI,CAACC,mBAAmB;MACjCC,UAAU,EAAE,IAAI,CAACC,aAAa;MAC9BC,KAAK,EAAE,IAAI,CAACC,iBAAiB;MAC7BC,cAAc,EAAE,IAAI,CAACrH,0BAA0B;MAC/CsH,aAAa,EAAE,IAAI,CAACrH,yBAAyB;MAC7CsH,SAAS,EAAE,IAAI,CAACjJ,qBAAqB;MACrCkJ,IAAI,EAAE,IAAI,CAACC,gBAAgB;MAC3BC,yBAAyB,EAAE,IAAI,CAACA,yBAAyB;MACzDC,wBAAwB,EAAE,IAAI,CAACA,wBAAwB;MACvDC,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;MACjDC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,EAAE,EAAE,IAAI,CAACA,EAAE;MACXC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B3E,GAAG,EAAE,IAAI,CAACA,GAAG;MACb4E,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,OAAO,EAAE,IAAI,CAACvE,mBAAmB;MACjCwE,WAAW,EAAE,IAAI,CAACxI,uBAAuB;MACzCyI,QAAQ,EAAE,IAAI,CAACxI,oBAAoB;MACnCyI,OAAO,EAAE,IAAI,CAACxI,mBAAmB;MACjCyI,QAAQ,EAAE,IAAI,CAACxI,oBAAoB;MACnCyI,OAAO,EAAE,IAAI,CAACxI,mBAAmB;MACjCyI,SAAS,EAAE,IAAI,CAACxI,qBAAqB;MACrCyI,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,KAAK,EAAE,IAAI,CAAC3D,KAAK;MACjB4D,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,uBAAuB,EAAE,IAAI,CAACA,uBAAuB;MACrDC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBpJ,IAAI,EAAE,IAAI,CAACqJ,gBAAgB;MAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,UAAU,EAAE,IAAI,CAACC,oBAAoB;MACrCC,UAAU,EAAE,IAAI,CAACC,oBAAoB;MACrCC,OAAO,EAAE,IAAI,CAACC,OAAO;MACrBzM,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B0M,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjClS,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBiS,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCtK,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BD,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBF,GAAG,EAAE,IAAI,CAACA,GAAG;MACb0K,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,MAAM,EAAE,IAAI,CAACC,kBAAkB;MAC/BC,eAAe,EAAE,IAAI,CAACC,2BAA2B;MACjDC,iBAAiB,EAAE,IAAI,CAACC,4BAA4B;MACpDC,cAAc,EAAE,IAAI,CAACC,yBAAyB;MAC9CC,cAAc,EAAE,IAAI,CAACC,yBAAyB;MAC9CC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,gBAAgB,EAAE,IAAI,CAACC,eAAe;MACtCC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/CC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB;MACAC,UAAU,EAAE,IAAI,CAACA;IACrB,CAAC;IACD;IACA,IAAI,CAACtL,cAAc,GAAG,IAAI,CAACA,cAAc,CAACuL,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5C;IACA,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACD,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACI,SAAS,GAAG,IAAI,CAACA,SAAS,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACL,IAAI,CAAC,IAAI,CAAC;EAC5D;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAExO,IAAI,EAAEyO,YAAY,CAACC,OAAO,CAAC,MAAM;IAAE,CAAC,CAAC;IACrD,IAAI,IAAI,CAACzW,KAAK,CAAC0W,eAAe,EAAE;MAC5B,IAAI,CAACH,QAAQ,CAAC;QACVlI,WAAW,EAAE,4BAA4B;QACzCG,eAAe,EAAE;MACrB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACxO,KAAK,CAAC2W,mBAAmB,EAAE;MAChC,IAAI,CAACJ,QAAQ,CAAC;QACVhI,eAAe,EAAE,yCAAyC;QAC1DC,eAAe,EAAE;MACrB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACxO,KAAK,CAAC4W,gBAAgB,EAAE;MAC7B,IAAI,CAACL,QAAQ,CAAC;QACVjI,YAAY,EAAE;MAClB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACtO,KAAK,CAAC0O,WAAW,KAAK,IAAI,EAAE;MACjCmI,QAAQ,CAACC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACjE,IAAI,CAACR,QAAQ,CAAC;QACV7H,WAAW,EAAE;MACjB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC1O,KAAK,CAAC2O,gBAAgB,KAAK,IAAI,EAAE;MACtC,IAAIqI,IAAI,GAAG,IAAI,CAAChX,KAAK,CAACiX,KAAK,CAACD,IAAI,CAACE,EAAE,IAAIA,EAAE,CAACC,KAAK,KAAKnY,QAAQ,CAACoY,UAAU,CAAC;MACxE,IAAIJ,IAAI,KAAK5W,SAAS,EAAE;QACpB,IAAI,CAACmW,QAAQ,CAAC;UAAE1H,SAAS,EAAE;QAAK,CAAC,CAAC;MACtC;MACA,IAAI,CAAC0H,QAAQ,CAAC;QACV5H,gBAAgB,EAAE,0BAA0B;QAC5CH,eAAe,EAAE,QAAQ;QACzBD,eAAe,EAAE;MACrB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACvO,KAAK,CAAC4O,YAAY,KAAKxO,SAAS,EAAE;MACvC,IAAI,CAACmW,QAAQ,CAAC;QACV/H,eAAe,EAAE,QAAQ;QACzBI,YAAY,EAAE;MAClB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC5O,KAAK,CAACqX,gBAAgB,KAAKjX,SAAS,EAAE;MAC3C,IAAI,CAACmW,QAAQ,CAAC;QACVlI,WAAW,EAAE,IAAI,CAACrO,KAAK,CAACqX;MAC5B,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACrX,KAAK,CAACyO,gBAAgB,KAAK,KAAK,EAAE;MACvC,IAAI,CAAC8H,QAAQ,CAAC;QACV9H,gBAAgB,EAAE;MACtB,CAAC,CAAC;IACN;EACJ;EAqWAO,iBAAiBA,CAAChM,OAAO,EAAE;IAAA,IAAAsU,kBAAA;IACvB,oBACI5X,OAAA;MAAK0B,SAAS,EAAC,iBAAiB;MAAAD,QAAA,eAC5BzB,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,GACV6B,OAAO,CAACoC,EAAE,KAAKhF,SAAS,iBACrBV,OAAA;UAAK6X,GAAG,EAAE3Y,MAAM,CAAC4Y,SAAS,GAAG,iBAAiB,GAAGxU,OAAO,CAACoC,EAAE,GAAG,MAAO;UAACqS,OAAO,EAAGjN,CAAC,IAAKA,CAAC,CAACkL,MAAM,CAAC6B,GAAG,GAAG1Y,QAAS;UAAC6Y,GAAG,EAAE1U,OAAO,CAACuC,WAAY;UAACnE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAExKuB,OAAO,CAACoC,EAAE,KAAKhF,SAAS,iBACrBV,OAAA;UAAK6X,GAAG,EAAE3Y,MAAM,CAAC4Y,SAAS,GAAG,iBAAiB,IAAIxU,OAAO,CAACoK,OAAO,KAAKhN,SAAS,GAAG4C,OAAO,CAACoK,OAAO,CAAChI,EAAE,GAAI,EAAAkS,kBAAA,GAAAtU,OAAO,CAACmK,SAAS,cAAAmK,kBAAA,uBAAjBA,kBAAA,CAAmBlS,EAAE,MAAKhF,SAAS,GAAG4C,OAAO,CAACmK,SAAS,CAAC/H,EAAE,GAAGpC,OAAO,CAAC2U,SAAU,CAAC,GAAG,MAAO;UAACF,OAAO,EAAGjN,CAAC,IAAKA,CAAC,CAACkL,MAAM,CAAC6B,GAAG,GAAG1Y,QAAS;UAAC6Y,GAAG,EAAE1U,OAAO,CAACuC,WAAY;UAACnE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1R;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEd;EAyRAgJ,cAAcA,CAACD,CAAC,EAAEtK,OAAO,EAAE;IACvBZ,aAAa,CAAC;MACVsY,OAAO,EAAE,qEAAqE;MAC9EC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAChC,UAAU,CAACzL,CAAC,EAAEtK,OAAO,CAAC;MACzCgY,MAAM,EAAEA,CAAA,KAAM;IAClB,CAAC,CAAC;EACN;EACA;EACA,MAAMjC,UAAUA,CAACzL,CAAC,EAAEtK,OAAO,EAAE;IACzBA,OAAO,CAACiG,MAAM,GAAGqE,CAAC,CAACJ,KAAK,CAACD,IAAI;IAC7B,IAAI,CAACoM,QAAQ,CAAC;MACVlM,cAAc,EAAEG,CAAC,CAACJ,KAAK,CAACD;IAC5B,CAAC,CAAC;IACF,IAAIgO,GAAG,GAAG,aAAa,GAAGjY,OAAO,CAACkF,EAAE;IACpC,IAAIgT,QAAQ,GAAG,EAAE;IACjBlY,OAAO,CAACmY,aAAa,CAACC,OAAO,CAACC,OAAO,IAAI;MACrC,IAAIC,IAAI,GAAG;QACPlJ,KAAK,EAAEiJ,OAAO,CAACjJ,KAAK;QACpBlK,EAAE,EAAEmT,OAAO,CAACE,SAAS;QACrBC,OAAO,EAAEH,OAAO,CAACG,OAAO;QACxBlN,WAAW,EAAE+M,OAAO,CAAC/M,WAAW;QAChC4B,OAAO,EAAEmL,OAAO,CAACnL,OAAO;QACxBqL,SAAS,EAAEF,OAAO,CAACE,SAAS;QAC5BlN,QAAQ,EAAEgN,OAAO,CAAChN,QAAQ;QAC1BtC,GAAG,EAAEsP,OAAO,CAACtP,GAAG;QAChBZ,KAAK,EAAEkQ,OAAO,CAAClQ,KAAK;QACpBG,UAAU,EAAE+P,OAAO,CAAC/P,UAAU;QAC9B6C,WAAW,EAAEkN,OAAO,CAAClN,WAAW;QAChCwB,SAAS,EAAE0L,OAAO,CAAC1L;MACvB,CAAC;MACDuL,QAAQ,CAACO,IAAI,CAACH,IAAI,CAAC;IACvB,CAAC,CAAC;IACF,IAAII,IAAI,GAAG;MACPzY,YAAY,EAAED,OAAO,CAACC,YAAY;MAClC4G,mBAAmB,EAAE7G,OAAO,CAAC6G,mBAAmB;MAChD3B,EAAE,EAAElF,OAAO,CAACkF,EAAE;MACdkC,UAAU,EAAEpH,OAAO,CAACoH,UAAU;MAC9BkF,IAAI,EAAEtM,OAAO,CAACsM,IAAI;MAClB1K,SAAS,EAAE5B,OAAO,CAAC4B,SAAS;MAC5B+J,WAAW,EAAE3L,OAAO,CAAC2L,WAAW;MAChCgN,QAAQ,EAAET,QAAQ;MAClBhM,YAAY,EAAElM,OAAO,CAACkM,YAAY;MAClC/D,KAAK,EAAEnI,OAAO,CAACmI,KAAK;MACpBG,UAAU,EAAEtI,OAAO,CAACsI,UAAU;MAC9BrC,MAAM,EAAEjG,OAAO,CAACiG,MAAM;MACtB2S,iBAAiB,EAAE5Y,OAAO,CAAC4Y;IAC/B,CAAC;IACD,IAAIC,GAAG,GAAG,MAAMna,MAAM,CAACoa,UAAU,CAAC,KAAK,EAAEb,GAAG,EAAES,IAAI,CAAC;IACnDK,OAAO,CAACC,GAAG,CAACH,GAAG,CAACI,IAAI,CAAC;IACrBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC5B;EA0BAjK,kBAAkBA,CAACnP,OAAO,EAAE;IACxB,IAAIA,OAAO,CAACqZ,cAAc,KAAKnZ,SAAS,EAAE;MACtC,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9CvB,OAAO,CAACqZ,cAAc;MAAA;QAAAjY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAEzB,CAAC,MAAM;MACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9CvB,OAAO,CAACsL,WAAW;MAAA;QAAAlK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEzB;EACJ;EAkJAoO,iBAAiBA,CAAC3P,OAAO,EAAE;IACvB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC4Q;MAAK;QAAAtO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvDvB,OAAO,CAAC0P,KAAK;IAAA;MAAAtO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEzB;EACAsO,oBAAoBA,CAAC7P,OAAO,EAAE;IAC1B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8Q;MAAQ;QAAAxO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC4P,QAAQ;IAAA;MAAAxO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACAwO,oBAAoBA,CAAC/P,OAAO,EAAE;IAC1B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACiM;MAAQ;QAAA3J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC8P,QAAQ;IAAA;MAAA1O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACA0O,kBAAkBA,CAACjQ,OAAO,EAAE;IACxB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC0S;MAAe;QAAApQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACjEvB,OAAO,CAACgQ,MAAM;IAAA;MAAA5O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEzB;EACA4O,mBAAmBA,CAACnQ,OAAO,EAAE;IACzB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC2S;MAAe;QAAArQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACjEvB,OAAO,CAACkQ,OAAO;IAAA;MAAA9O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEzB;EACA8O,aAAaA,CAACvN,OAAO,EAAE;IACnBA,OAAO,CAACsN,UAAU,GAAGtN,OAAO,CAACwW,YAAY,GAAGxW,OAAO,CAACgN,QAAQ;IAC5D,OAAOhN,OAAO,CAACsN,UAAU;EAC7B;EACAG,iBAAiBA,CAACzN,OAAO,EAAE;IACvB,IAAII,OAAO,GAAG,IAAIpC,IAAI,CAAC,CAAC;IACxB,IAAIH,KAAK,GAAGuC,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,IAAIzC,GAAG,GAAGwC,OAAO,CAACE,OAAO,CAAC,CAAC;IAC3B,IAAIzC,KAAK,GAAG,EAAE,EAAE;MACZA,KAAK,GAAG,GAAG,GAAGA,KAAK;IACvB;IACA,IAAID,GAAG,GAAG,EAAE,EAAE;MACVA,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACAwC,OAAO,GAAGA,OAAO,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG1C,KAAK,GAAG,GAAG,GAAGD,GAAG;IACzD,IAAIwC,OAAO,GAAGJ,OAAO,CAACyW,cAAc,EAAE;MAClC,IAAIC,KAAK,GAAG,KAAK;MACjB,oBACIha,OAAA;QAAMwI,KAAK,EAAE;UAAEwR;QAAM,CAAE;QAAAvY,QAAA,GAAC,GAAC,EAAC6B,OAAO,CAACwN,KAAK,GAAGxR,QAAQ,CAAC2a,YAAY,EAAC,GAAC;MAAA;QAAArY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAEhF;EACJ;EACAqP,gBAAgBA,CAAC5Q,OAAO,EAAE;IACtB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC4a;MAAI;QAAAtY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtD,IAAIf,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,SAAS;QAAEa,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC,CAACd,MAAM,CAAC,IAAIC,IAAI,CAACd,OAAO,CAAC2Q,IAAI,CAAC,CAAC;IAAA;MAAAvP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClK,CAAC;EAEzB;EACAsP,yBAAyBA,CAAC7Q,OAAO,EAAE;IAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACyG;MAAM;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACxDvB,OAAO,CAACkN,OAAO,CAAC1H,YAAY;IAAA;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEzB;EACAuP,wBAAwBA,CAAC9Q,OAAO,EAAE;IAC9B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACsG;MAAI;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAACkN,OAAO,CAAC7H,WAAW;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEzB;EACAwP,qBAAqBA,CAAC/Q,OAAO,EAAE;IAC3B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8K;MAAQ;QAAAxI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC4J,QAAQ;IAAA;MAAAxI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACAyP,sBAAsBA,CAAChR,OAAO,EAAE;IAC5B,IAAIgL,YAAY,GAAGhL,OAAO,CAACgR,sBAAsB,CAAC8F,IAAI,CAACE,EAAE;MAAA,IAAA2C,eAAA;MAAA,OAAI,EAAAA,eAAA,GAAA3C,EAAE,CAAC4C,WAAW,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,OAAM,CAAA7Z,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,MAAK3Z,SAAS,GAAGF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,GAAG,IAAI,CAAC;IAAA,EAAC;IACjJ,oBACIra,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACiM;MAAQ;QAAA3J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DyJ,YAAY,KAAK9K,SAAS,GAAG8K,YAAY,CAACA,YAAY,GAAG,iBAAiB;IAAA;MAAA5J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAEzB;EACA0P,OAAOA,CAACjR,OAAO,EAAE;IACb,IAAIA,OAAO,CAAC8Z,IAAI,KAAK5Z,SAAS,EAAE;MAC5B,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACgb;QAAI;UAAA1Y,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC8Z,IAAI;MAAA;QAAA1Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEzB,CAAC,MAAM;MACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACgb;QAAI;UAAA1Y,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC+Z,UAAU,CAACD,IAAI;MAAA;QAAA1Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAEzB;EAEJ;EACA2P,QAAQA,CAAClR,OAAO,EAAE;IACd,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACkb;MAAQ;QAAA5Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1D,IAAIT,IAAI,CAACd,OAAO,CAACqC,QAAQ,CAAC,CAAC4X,kBAAkB,CAAC,CAAC;IAAA;MAAA7Y,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEzB;EACA4P,YAAYA,CAACnR,OAAO,EAAE;IAClB,IAAIA,OAAO,CAACmR,YAAY,KAAKjR,SAAS,EAAE;MACpC,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACob;QAAO;UAAA9Y,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzD,IAAIT,IAAI,CAACd,OAAO,CAACmR,YAAY,CAAC,CAAC8I,kBAAkB,CAAC,CAAC;MAAA;QAAA7Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAEzB,CAAC,MAAM;MACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACob;QAAO;UAAA9Y,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACzD,IAAIT,IAAI,CAACd,OAAO,CAAC+Z,UAAU,CAAC5I,YAAY,CAAC,CAAC8I,kBAAkB,CAAC,CAAC;MAAA;QAAA7Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAEzB;EACJ;EACA6P,SAASA,CAACpR,OAAO,EAAE;IAAA,IAAAma,qBAAA;IACf,oBACI3a,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACyG;MAAM;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAA4Y,qBAAA,GACxDna,OAAO,CAACoa,mBAAmB,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6BlN,SAAS,CAACzH,YAAY;IAAA;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEzB;EACA8P,WAAWA,CAACrR,OAAO,EAAE;IAAA,IAAAqa,sBAAA;IACjB,oBACI7a,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwb;MAAQ;QAAAlZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAA8Y,sBAAA,GAC1Dra,OAAO,CAACoa,mBAAmB,cAAAC,sBAAA,uBAA3BA,sBAAA,CAA6BpN,SAAS,CAAC5H,WAAW;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEzB;EACA4Q,eAAeA,CAACnS,OAAO,EAAE;IACrB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwb;MAAQ;QAAAlZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAACiN,SAAS,CAAC5H,WAAW;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEzB;EACA+P,UAAUA,CAACtR,OAAO,EAAE;IAChB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACiH;MAAO;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDvB,OAAO,CAACoa,mBAAmB,CAACnN,SAAS,CAAClH,OAAO;IAAA;MAAA3E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEzB;EACAgQ,EAAEA,CAACvR,OAAO,EAAE;IAAA,IAAAua,sBAAA,EAAAC,sBAAA;IACR,oBACIhb,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC2b;MAAO;QAAArZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDvB,OAAO,CAACoa,mBAAmB,KAAKla,SAAS,MAAAwa,MAAA,EAAAH,sBAAA,GAAMva,OAAO,CAACoa,mBAAmB,cAAAG,sBAAA,uBAA3BA,sBAAA,CAA6BpP,WAAW,SAAAuP,MAAA,EAAAF,sBAAA,GAAMxa,OAAO,CAACoa,mBAAmB,cAAAI,sBAAA,uBAA3BA,sBAAA,CAA6BG,WAAW,OAAAD,MAAA,CAAQ1a,OAAO,CAACuR,EAAE,SAAAmJ,MAAA,CAAM1a,OAAO,CAAC4a,GAAG,CAAE;IAAA;MAAAxZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/J,CAAC;EAEzB;EACAiQ,eAAeA,CAACxR,OAAO,EAAE;IACrB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC0S;MAAe;QAAApQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACjEvB,OAAO,CAACwR,eAAe;IAAA;MAAApQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEzB;EACAkQ,eAAeA,CAACzR,OAAO,EAAE;IACrB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC2S;MAAe;QAAArQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACjEvB,OAAO,CAACyR,eAAe;IAAA;MAAArQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEzB;EACA6N,KAAKA,CAACpP,OAAO,EAAE;IACX,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACiU;MAAK;QAAA3R,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvDvB,OAAO,CAACoP,KAAK;IAAA;MAAAhO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEzB;EACAmQ,WAAWA,CAAC1R,OAAO,EAAE;IACjB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8Q;MAAQ;QAAAxO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC4P,QAAQ;IAAA;MAAAxO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACA4R,uBAAuBA,CAACnT,OAAO,EAAE;IAC7B,IAAIA,OAAO,CAAC4P,QAAQ,KAAK,IAAI,EAAE;MAC3B,oBACIpQ,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAAC8Q;QAAQ;UAAAxO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC1D,IAAIT,IAAI,CAACd,OAAO,CAAC4P,QAAQ,CAAC,CAACqK,kBAAkB,CAAC,CAAC,CAAC5Z,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEzB;EACJ;EACAoQ,IAAIA,CAAC3R,OAAO,EAAE;IACV,IAAIA,OAAO,CAAC6a,MAAM,KAAK3a,SAAS,EAAE;MAC9B,oBACIV,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACgc;QAAI;UAAA1Z,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC6a,MAAM;MAAA;QAAAzZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEzB,CAAC,MAAM;MACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEnC,QAAQ,CAACgc;QAAI;UAAA1Z,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC+Z,UAAU,CAACc,MAAM;MAAA;QAAAzZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEzB;EACJ;EACAgT,kBAAkBA,CAACvU,OAAO,EAAE;IACxB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACgc;MAAI;QAAA1Z,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAACsU,MAAM;IAAA;MAAAlT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEzB;EACAkT,2BAA2BA,CAACzU,OAAO,EAAE;IACjC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC+E;MAAQ;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAACwU,eAAe;IAAA;MAAApT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEzB;EACAoT,4BAA4BA,CAAC3U,OAAO,EAAE;IAClC,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8K;MAAQ;QAAAxI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAAC0U,iBAAiB;IAAA;MAAAtT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEzB;EACAwT,yBAAyBA,CAAC/U,OAAO,EAAE;IAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACgb;MAAI;QAAA1Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC8U,cAAc;IAAA;MAAA1T,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEzB;EACAsT,yBAAyBA,CAAC7U,OAAO,EAAE;IAC/B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACob;MAAO;QAAA9Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzD,IAAIT,IAAI,CAACd,OAAO,CAAC4U,cAAc,CAAC,CAACqF,kBAAkB,CAAC,CAAC,CAAC5Z,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAA;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAEzB;EA0CA6Q,UAAUA,CAACpS,OAAO,EAAE;IAAA,IAAA+a,sBAAA;IAChB,oBACIvb,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACyG;MAAM;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAAwZ,sBAAA,GACxD/a,OAAO,CAACoa,mBAAmB,cAAAW,sBAAA,uBAA3BA,sBAAA,CAA6B9N,SAAS,CAACzH,YAAY;IAAA;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEzB;EACA8Q,QAAQA,CAACrS,OAAO,EAAE;IAAA,IAAAgb,sBAAA;IACd,oBACIxb,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACsG;MAAI;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAAyZ,sBAAA,GACtDhb,OAAO,CAACoa,mBAAmB,cAAAY,sBAAA,uBAA3BA,sBAAA,CAA6B/N,SAAS,CAAC5H,WAAW;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEzB;EACA+Q,QAAQA,CAACtS,OAAO,EAAE;IACd,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACmc;MAAI;QAAA7Z,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAACkb,sBAAsB,CAACC,IAAI,KAAK,WAAW,GAAGnb,OAAO,CAACkb,sBAAsB,CAACC,IAAI,GAAG,iBAAiB;IAAA;MAAA/Z,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClG,CAAC;EAEzB;EACAgR,YAAYA,CAACvS,OAAO,EAAE;IAClB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACsc;MAAQ;QAAAha,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1DvB,OAAO,CAACkb,sBAAsB,CAACG,QAAQ,KAAK,WAAW,GAAGrb,OAAO,CAACkb,sBAAsB,CAACG,QAAQ,GAAG,iBAAiB;IAAA;MAAAja,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1G,CAAC;EAEzB;EACAiR,WAAWA,CAACxS,OAAO,EAAE;IACjB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwc;MAAO;QAAAla,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDvB,OAAO,CAACkb,sBAAsB,CAACK,OAAO,KAAK,WAAW,GAAGvb,OAAO,CAACkb,sBAAsB,CAACK,OAAO,GAAG,iBAAiB;IAAA;MAAAna,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC;EAEzB;EACAkR,aAAaA,CAACzS,OAAO,EAAE;IACnB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC0c;MAAS;QAAApa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACkb,sBAAsB,CAACO,SAAS,KAAK,WAAW,GAAGzb,OAAO,CAACkb,sBAAsB,CAACO,SAAS,GAAG,iBAAiB;IAAA;MAAAra,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5G,CAAC;EAEzB;EACAmR,SAASA,CAAC1S,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACgc;MAAI;QAAA1Z,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC+Z,UAAU,CAACc,MAAM;IAAA;MAAAzZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEzB;EACAoR,WAAWA,CAAC3S,OAAO,EAAE;IACjB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACgb;MAAI;QAAA1Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAAC+Z,UAAU,CAACD,IAAI;IAAA;MAAA1Y,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEzB;EACAqR,KAAKA,CAAC5S,OAAO,EAAE;IAAA,IAAA0b,iBAAA,EAAAC,qBAAA;IACX,oBACInc,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8c;MAAS;QAAAxa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3D,EAAAma,iBAAA,GAAA1b,OAAO,CAACqT,QAAQ,cAAAqI,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBG,MAAM,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BhY,QAAQ,KAAI,KAAK;IAAA;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAEzB;EACAwS,OAAOA,CAAC/T,OAAO,EAAE;IAAA,IAAA8b,cAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACb,oBACIxc,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACmd;MAAY;QAAA7a,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC9D,EAAAua,cAAA,GAAA9b,OAAO,CAACkc,KAAK,cAAAJ,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAehI,OAAO,cAAAiI,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBF,MAAM,cAAAG,sBAAA,uBAA9BA,sBAAA,CAAgCrY,QAAQ,KAAI,KAAK;IAAA;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAEzB;EACAsR,OAAOA,CAAC7S,OAAO,EAAE;IAAA,IAAAmc,sBAAA,EAAAC,sBAAA;IACb,oBACI5c,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACud;MAAO;QAAAjb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAAA4a,sBAAA,GACzDnc,OAAO,CAACoa,mBAAmB,cAAA+B,sBAAA,uBAA3BA,sBAAA,CAA6BhR,WAAW,EAAC,KAAG,GAAAiR,sBAAA,GAACpc,OAAO,CAACoa,mBAAmB,cAAAgC,sBAAA,uBAA3BA,sBAAA,CAA6BzB,WAAW;IAAA;MAAAvZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CAAC;EAEzB;EACAuR,KAAKA,CAAC9S,OAAO,EAAE;IACX,IAAIA,OAAO,CAAC8Z,IAAI,KAAK,MAAM,EAAE;MACzB,oBACIta,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxC/B,OAAA;UAAUyK,IAAI,EAAC;QAAoB;UAAA7I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEzB,CAAC,MAAM;MACH,oBACI/B,OAAA,CAACjB,KAAK,CAACkB,QAAQ;QAAAwB,QAAA,gBACXzB,OAAA;UAAM0B,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxC/B,OAAA;UAAUyK,IAAI,EAAC;QAAkB;UAAA7I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAEzB;EACJ;EACAyR,SAASA,CAAChT,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACkb;MAAQ;QAAA5Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1D,IAAIT,IAAI,CAACd,OAAO,CAACkR,QAAQ,CAAC,CAAC+I,kBAAkB,CAAC,CAAC;IAAA;MAAA7Y,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEzB;EACA0R,SAASA,CAACjT,OAAO,EAAE;IAAA,IAAAsc,oBAAA,EAAAC,qBAAA;IACf,oBACI/c,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACkb;MAAQ;QAAA5Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1D,EAAA+a,oBAAA,GAAAtc,OAAO,CAACwc,WAAW,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBjV,UAAU,CAACvD,SAAS,MAAK5D,SAAS,IAAAqc,qBAAA,GAAGvc,OAAO,CAACwc,WAAW,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqBlV,UAAU,CAACvD,SAAS,GAAG9D,OAAO,CAACiT,SAAS;IAAA;MAAA7R,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5G,CAAC;EAEzB;EACA2R,QAAQA,CAAClT,OAAO,EAAE;IAAA,IAAAyc,oBAAA,EAAAC,oBAAA;IACd,oBACIld,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACkb;MAAQ;QAAA5Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1D,EAAAkb,oBAAA,GAAAzc,OAAO,CAACoH,UAAU,cAAAqV,oBAAA,uBAAlBA,oBAAA,CAAoBpV,UAAU,CAACvD,SAAS,MAAK5D,SAAS,IAAAwc,oBAAA,GAAG1c,OAAO,CAACoH,UAAU,cAAAsV,oBAAA,uBAAlBA,oBAAA,CAAoBrV,UAAU,CAACvD,SAAS,GAAG9D,OAAO,CAACkT,QAAQ;IAAA;MAAA9R,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzG,CAAC;EAEzB;EACA6R,QAAQA,CAACpT,OAAO,EAAE;IAAA,IAAA2c,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,gBAAA;IACd,IAAIxR,UAAU,GAAG,EAAE;IACnB,IAAI,EAAA8Q,eAAA,GAAA3c,OAAO,CAACkc,KAAK,cAAAS,eAAA,uBAAbA,eAAA,CAAe1W,MAAM,MAAK,UAAU,EAAE;MACtC4F,UAAU,GAAG,6GAA6G;IAC9H,CAAC,MACI,IAAI,EAAA+Q,eAAA,GAAA5c,OAAO,CAACkc,KAAK,cAAAU,eAAA,uBAAbA,eAAA,CAAe3W,MAAM,MAAK,UAAU,EAAE;MAC3C4F,UAAU,GAAG,6GAA6G;IAC9H,CAAC,MACI,IAAI,EAAAgR,eAAA,GAAA7c,OAAO,CAACkc,KAAK,cAAAW,eAAA,uBAAbA,eAAA,CAAe5W,MAAM,MAAK,YAAY,EAAE;MAC7C4F,UAAU,GAAG,iHAAiH;IAClI,CAAC,MACI,IAAI,EAAAiR,eAAA,GAAA9c,OAAO,CAACkc,KAAK,cAAAY,eAAA,uBAAbA,eAAA,CAAe7W,MAAM,MAAK,UAAU,EAAE;MAC3C4F,UAAU,GAAG,2GAA2G;IAC5H,CAAC,MACI,IAAI,EAAAkR,eAAA,GAAA/c,OAAO,CAACkc,KAAK,cAAAa,eAAA,uBAAbA,eAAA,CAAe9W,MAAM,MAAK,QAAQ,EAAE;MACzC4F,UAAU,GAAG,6GAA6G;IAC9H,CAAC,MACI,IAAI,EAAAmR,eAAA,GAAAhd,OAAO,CAACkc,KAAK,cAAAc,eAAA,uBAAbA,eAAA,CAAe/W,MAAM,MAAK,SAAS,EAAE;MAC1C4F,UAAU,GAAG,uGAAuG;IACxH,CAAC,MACI,IAAI,EAAAoR,eAAA,GAAAjd,OAAO,CAACkc,KAAK,cAAAe,eAAA,uBAAbA,eAAA,CAAehX,MAAM,MAAK,aAAa,EAAE;MAC9C4F,UAAU,GAAG,uGAAuG;IACxH,CAAC,MACI,IAAI,EAAAqR,eAAA,GAAAld,OAAO,CAACkc,KAAK,cAAAgB,eAAA,uBAAbA,eAAA,CAAejX,MAAM,MAAK,UAAU,EAAE;MAC3C4F,UAAU,GAAG,6GAA6G;IAC9H,CAAC,MACI,IAAI,EAAAsR,eAAA,GAAAnd,OAAO,CAACkc,KAAK,cAAAiB,eAAA,uBAAbA,eAAA,CAAelX,MAAM,MAAK,WAAW,EAAE;MAC5C4F,UAAU,GAAG,6GAA6G;IAC9H,CAAC,MACI,IAAI,EAAAuR,eAAA,GAAApd,OAAO,CAACkc,KAAK,cAAAkB,eAAA,uBAAbA,eAAA,CAAenX,MAAM,MAAK,eAAe,EAAE;MAChD4F,UAAU,GAAG,2GAA2G;IAC5H;IACA,oBACIrM,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC4I;MAAK;QAAAtG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxD/B,OAAA;QAAK0B,SAAS,EAAE2K,UAAW;QAAA5K,QAAA,EACtB,EAAAoc,gBAAA,GAAArd,OAAO,CAACkc,KAAK,cAAAmB,gBAAA,uBAAbA,gBAAA,CAAepX,MAAM,KAAI;MAAK;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEzB;EACA8R,QAAQA,CAACrT,OAAO,EAAE;IAAA,IAAAsd,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACd,oBACIhe,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8c;MAAS;QAAAxa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3D,EAAA+b,gBAAA,GAAAtd,OAAO,CAACkc,KAAK,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAejK,QAAQ,cAAAkK,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyB1B,MAAM,cAAA2B,sBAAA,uBAA/BA,sBAAA,CAAiC7Z,QAAQ,KAAI,KAAK;IAAA;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEzB;EACA+R,gBAAgBA,CAACtT,OAAO,EAAE;IACtB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACsG;MAAI;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACtDvB,OAAO,CAACiK,IAAI;IAAA;MAAA7I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEzB;EACAgS,UAAUA,CAACvT,OAAO,EAAE;IAChB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACyH;MAAc;QAAAnF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEvB,OAAO,CAACuT,UAAU;IAAA;MAAAnS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEzB;EACAiS,OAAOA,CAACxT,OAAO,EAAE;IACb,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC2e;MAAS;QAAArc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5D/B,OAAA;QAAK0B,SAAS,EAAElB,OAAO,CAACwT,OAAO,KAAK,IAAI,GAAG,mFAAmF,GAAG,iFAAkF;QAAAvS,QAAA,EAC9MjB,OAAO,CAACwT,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG;MAAI;QAAApS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEzB;EACAkS,SAASA,CAACzT,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC4e;MAAS;QAAAtc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACyT,SAAS;IAAA;MAAArS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEzB;EACAoS,oBAAoBA,CAAC3T,OAAO,EAAE;IAC1B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC6e;MAAO;QAAAvc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDvB,OAAO,CAACkT,QAAQ;IAAA;MAAA9R,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACAsS,oBAAoBA,CAAC7T,OAAO,EAAE;IAC1B,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC8c;MAAS;QAAAxa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACqT,QAAQ;IAAA;MAAAjS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEzB;EACA+F,YAAYA,CAACtH,OAAO,EAAE;IAClB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwI;MAAY;QAAAlG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC9DvB,OAAO,CAACsH,YAAY;IAAA;MAAAlG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEzB;EACAyS,aAAaA,CAAChU,OAAO,EAAE;IACnB,IAAIgL,YAAY,GAAGhL,OAAO,CAACgR,sBAAsB,CAAC8F,IAAI,CAACE,EAAE;MAAA,IAAA4G,gBAAA;MAAA,OAAI,EAAAA,gBAAA,GAAA5G,EAAE,CAAC4C,WAAW,cAAAgE,gBAAA,uBAAdA,gBAAA,CAAgB/D,MAAM,OAAM,CAAA7Z,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,MAAK3Z,SAAS,GAAGF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,GAAG,IAAI,CAAC;IAAA,EAAC;IACjJ,oBACIra,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC+e;MAAc;QAAAzc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEyJ,YAAY,KAAK9K,SAAS,GAAG8K,YAAY,CAACgJ,aAAa,GAAG,iBAAiB;IAAA;MAAA5S,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAEzB;EACA0S,iBAAiBA,CAACjU,OAAO,EAAE;IACvB,IAAIgL,YAAY,GAAGhL,OAAO,CAACgR,sBAAsB,CAAC8F,IAAI,CAACE,EAAE;MAAA,IAAA8G,gBAAA;MAAA,OAAI,EAAAA,gBAAA,GAAA9G,EAAE,CAAC4C,WAAW,cAAAkE,gBAAA,uBAAdA,gBAAA,CAAgBjE,MAAM,OAAM,CAAA7Z,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,MAAK3Z,SAAS,GAAGF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6Z,MAAM,GAAG,IAAI,CAAC;IAAA,EAAC;IACjJ,oBACIra,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACif;MAAgB;QAAA3c,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAClEyJ,YAAY,KAAK9K,SAAS,GAAG8K,YAAY,CAACiJ,iBAAiB,GAAG,iBAAiB;IAAA;MAAA7S,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAEzB;EACAqU,SAASA,CAAC5V,OAAO,EAAE;IAAA,IAAAge,sBAAA,EAAAC,sBAAA;IACf,IAAIrI,SAAS,MAAA8E,MAAA,CAAMjS,UAAU,EAAAuV,sBAAA,GAAChe,OAAO,CAACoa,mBAAmB,cAAA4D,sBAAA,uBAA3BA,sBAAA,CAA6BpI,SAAS,CAAC,CAACjN,OAAO,CAAC,CAAC,CAAC,KAAAsV,sBAAA,GAAGje,OAAO,CAACoa,mBAAmB,cAAA6D,sBAAA,uBAA3BA,sBAAA,CAA6BtD,WAAW,QAAI;IAC/H,oBACInb,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACof;MAAS;QAAA9c,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DqU,SAAS;IAAA;MAAAxU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEzB;EACA8S,UAAUA,CAACrU,OAAO,EAAE;IAChB,IAAIme,UAAU,GAAGne,OAAO,CAACqU,UAAU,KAAK,IAAI,GAAG,qIAAqI,GAAG,qIAAqI;IAC5T,oBACI7U,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACif;MAAgB;QAAA3c,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnE/B,OAAA;QAAK0B,SAAS,EAAEid,UAAW;QAAAld,QAAA,GACtBjB,OAAO,CAACqU,UAAU,KAAK,IAAI,GAAGvV,QAAQ,CAACsf,OAAO,GAAGtf,QAAQ,CAACuf,UAAU,eAAC7e,OAAA;UAAG0B,SAAS,EAAElB,OAAO,CAACqU,UAAU,KAAK,IAAI,GAAG,kBAAkB,GAAG;QAA0B;UAAAjT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEzB;EACA2S,aAAaA,CAAClU,OAAO,EAAE;IACnB,IAAIse,OAAO,GAAG,EAAE;IAChBte,OAAO,CAACue,kBAAkB,CAACnG,OAAO,CAACC,OAAO,IAAI;MAC1CiG,OAAO,CAAC7F,IAAI,CAAC;QAAExO,IAAI,EAAEoO,OAAO,CAAClN,WAAW,GAAG,KAAK,GAAGkN,OAAO,CAACsC,WAAW;QAAEzQ,KAAK,EAAEmO,OAAO,CAACnT;MAAG,CAAC,CAAC;IAChG,CAAC,CAAC;IACF,oBACI1F,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACif;MAAgB;QAAA3c,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnE/B,OAAA,CAACP,QAAQ;QAACiL,KAAK,EAAElK,OAAO,CAACue,kBAAkB,CAAC9a,MAAM,KAAK,CAAC,GAAGzD,OAAO,CAACue,kBAAkB,CAAC,CAAC,CAAC,CAACrZ,EAAE,GAAG,EAAG;QAACkF,OAAO,EAAEkU,OAAQ;QAACjU,QAAQ,EAAGC,CAAC,IAAKtK,OAAO,CAACue,kBAAkB,GAAGve,OAAO,CAACue,kBAAkB,CAACC,MAAM,CAACxH,EAAE,IAAIA,EAAE,CAAC9R,EAAE,KAAKoF,CAAC,CAACJ,KAAK,CAAE;QAACM,WAAW,EAAC,MAAM;QAACE,WAAW,EAAC;MAAmB;QAAAtJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvQ,CAAC;EAEzB;EACA2T,UAAUA,CAAClV,OAAO,EAAE;IAChB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAAC2f;MAAG;QAAArd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACrDvB,OAAO,CAAC0X,OAAO,iBACZlY,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACKjB,OAAO,CAAC0X;MAAO,gBAClB,CAAC;IAAA;MAAAtW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGK,CAAC;EAEzB;EACA4S,eAAeA,CAACnU,OAAO,EAAE;IAAA,IAAA0e,qBAAA,EAAAC,sBAAA;IACrB,IAAIC,OAAO,IAAAF,qBAAA,GAAG1e,OAAO,CAACmU,eAAe,cAAAuK,qBAAA,uBAAvBA,qBAAA,CAAyBE,OAAO;IAC9C,IAAIC,KAAK,IAAAF,sBAAA,GAAG3e,OAAO,CAACmU,eAAe,cAAAwK,sBAAA,uBAAvBA,sBAAA,CAAyBE,KAAK;IAC1C,oBACIrf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACggB;MAAY;QAAA1d,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC9D,CAAAqd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnb,MAAM,IAAG,CAAC,iBAChBjE,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACK2d,OAAO,CAACte,GAAG,CAAC,CAAC0W,EAAE,EAAE+H,GAAG,KAAK;UACtB,oBACIvf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,EACV+V,EAAE,GAAG;UAAI,GADO+H,GAAG;YAAA3d,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC,EAEN,CAAAsd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEpb,MAAM,IAAG,CAAC,iBACdjE,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACK4d,KAAK,CAACve,GAAG,CAAC,CAAC0W,EAAE,EAAE+H,GAAG,KAAK;UACpB,oBACIvf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,EACV+V,EAAE,GAAG;UAAI,GADO+H,GAAG;YAAA3d,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEK,CAAC;EAEzB;EACA6S,gBAAgBA,CAACpU,OAAO,EAAE;IAAA,IAAAgf,qBAAA,EAAAC,sBAAA;IACtB,IAAIL,OAAO,IAAAI,qBAAA,GAAGhf,OAAO,CAACoU,gBAAgB,cAAA4K,qBAAA,uBAAxBA,qBAAA,CAA0BJ,OAAO;IAC/C,IAAIC,KAAK,IAAAI,sBAAA,GAAGjf,OAAO,CAACoU,gBAAgB,cAAA6K,sBAAA,uBAAxBA,sBAAA,CAA0BJ,KAAK;IAC3C,oBACIrf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACogB;MAAO;QAAA9d,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzD,CAAAqd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnb,MAAM,IAAG,CAAC,iBAChBjE,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACK2d,OAAO,CAACte,GAAG,CAAC,CAAC0W,EAAE,EAAE+H,GAAG,KAAK;UACtB,oBACIvf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,EACV+V,EAAE,GAAG;UAAI,GADO+H,GAAG;YAAA3d,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC,EAEN,CAAAsd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEpb,MAAM,IAAG,CAAC,iBACdjE,OAAA,CAAAE,SAAA;QAAAuB,QAAA,EACK4d,KAAK,CAACve,GAAG,CAAC,CAAC0W,EAAE,EAAE+H,GAAG,KAAK;UACpB,oBACIvf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;YAAAwB,QAAA,EACV+V,EAAE,GAAG;UAAI,GADO+H,GAAG;YAAA3d,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER,CAAC;QAEzB,CAAC;MAAC,gBACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEK,CAAC;EAEzB;EACA4U,gBAAgBA,CAACgJ,kBAAkB,EAAE;IACjC,oBACI3f,OAAA,CAAAE,SAAA;MAAAuB,QAAA,EACKke,kBAAkB,IACfA,kBAAkB,CAAC7e,GAAG,CAAC,CAAC0W,EAAE,EAAE+H,GAAG,KAAK;QAChC,oBACIvf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;UAAAwB,QAAA,MAAAyZ,MAAA,CACP1D,EAAE,GAAG,GAAC,EAAC+H,GAAG,KAAKI,kBAAkB,CAAC1b,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;QAAA,GAD3Csb,GAAG;UAAA3d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAER,CAAC;MAEzB,CAAC;IAAC,gBAER,CAAC;EAEX;EACAoU,oBAAoBA,CAAC3V,OAAO,EAAE;IAAA,IAAAof,qBAAA;IAC1B,IAAID,kBAAkB,GAAG,EAAE;IAC3B,CAAAC,qBAAA,GAAApf,OAAO,CAAC2V,oBAAoB,cAAAyJ,qBAAA,uBAA5BA,qBAAA,CAA8B9e,GAAG,CAAC0W,EAAE,IAAImI,kBAAkB,CAAC1G,IAAI,CAACzB,EAAE,CAACvB,SAAS,GAAG;MAAC4J,WAAW,EAAErI,EAAE,CAACsI,UAAU;MAAEC,KAAK,EAAEvI,EAAE,CAACvB,SAAS,CAACnV,GAAG,CAACkf,IAAI,IAAEA,IAAI,CAAC,CAACze,IAAI,CAAC,IAAI,CAAC;MAAE0e,MAAM,EAAEC,MAAM,CAACC,IAAI,CAAC3I,EAAE,CAAC4I,OAAO,CAAC,CAAC,CAAC,CAAC;MAAEC,EAAE,EAAEH,MAAM,CAACI,MAAM,CAAC9I,EAAE,CAAC4I,OAAO,CAAC,CAACtf,GAAG,CAACyf,GAAG,IAAIA,GAAG,CAACzf,GAAG,CAACkf,IAAI,OAAA9E,MAAA,CAAOgF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,QAAA9E,MAAA,CAAKgF,MAAM,CAACI,MAAM,CAACN,IAAI,CAAC,CAAE,CAAC,CAACze,IAAI,CAAC,IAAI,CAAC;IAAC,CAAC,GAAIiW,EAAE,CAACsI,UAAU,kBAAA5E,MAAA,CAAkB1D,EAAE,CAACsI,UAAU,gBAAA5E,MAAA,CAAU1D,EAAE,CAAC4H,OAAO,gBAAAlE,MAAA,CAAgB1D,EAAE,CAAC4H,OAAO,6BAAAlE,MAAA,CAA0B1D,EAAE,CAAC6H,KAAK,eAAY,IAAK,EAAG,CAAC,CAAC;IAC3c,oBACIrf,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACkhB;MAAkB;QAAA5e,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACpE4d,kBAAkB,CAAC1b,MAAM,GAAG,CAAC,iBAC1BjE,OAAA,CAACF,eAAe;QAACwgB,MAAM,EAAEX,kBAAmB;QAAClI,KAAK,EAAC,QAAQ;QAACgJ,KAAK,EAAE;MAAK;QAAA7e,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,GAH9DvB,OAAO,CAACkF,EAAE;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKf,CAAC;EAEzB;EACAyT,SAASA,CAAChV,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACohB;MAAS;QAAA9e,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACgV,SAAS;IAAA;MAAA5T,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEzB;EACA0T,SAASA,CAACjV,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACqhB;MAAS;QAAA/e,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACiT,SAAS;IAAA;MAAA7R,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEzB;EACA4T,UAAUA,CAACnV,OAAO,EAAE;IAChB,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACshB;MAAS;QAAAhf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC3DvB,OAAO,CAACgV,SAAS,CAACvR,MAAM;IAAA;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEzB;EACA6T,SAASA,CAACpV,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACuhB;MAAO;QAAAjf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDvB,OAAO,CAACkT,QAAQ,CAACzP,MAAM;IAAA;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEzB;EACAgU,GAAGA,CAACvV,OAAO,EAAE;IACT,IAAIuV,GAAG,GAAGvV,OAAO,CAACuV,GAAG,CAACjV,GAAG,CAAC0W,EAAE,IAAIA,EAAE,CAACvB,SAAS,KAAKvV,SAAS,GAAI8W,EAAE,CAAC4H,OAAO,KAAK1e,SAAS,cAAAwa,MAAA,CAAc1D,EAAE,CAACsI,UAAU,kBAAA5E,MAAA,CAAe1D,EAAE,CAACvB,SAAS,gBAAAiF,MAAA,CAAa1D,EAAE,CAAC4H,OAAO,oBAAAlE,MAAA,CAAiB1D,EAAE,CAACsI,UAAU,kBAAA5E,MAAA,CAAe1D,EAAE,CAACvB,SAAS,eAAAiF,MAAA,CAAY1D,EAAE,CAAC6H,KAAK,CAAE,GAAK7H,EAAE,CAAC4H,OAAO,KAAK1e,SAAS,cAAAwa,MAAA,CAAc1D,EAAE,CAACsI,UAAU,eAAA5E,MAAA,CAAY1D,EAAE,CAAC4H,OAAO,oBAAAlE,MAAA,CAAiB1D,EAAE,CAACsI,UAAU,eAAA5E,MAAA,CAAY1D,EAAE,CAAC6H,KAAK,CAAG,CAAC,CAAC9d,IAAI,CAAC,IAAI,CAAC;IACjY,oBACIvB,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwhB;MAAc;QAAAlf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEgU,GAAG;IAAA;MAAAnU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAEzB;EACA+T,eAAeA,CAACtV,OAAO,EAAE;IACrB,IAAIsV,eAAe,GAAGtV,OAAO,CAACqV,gBAAgB,CAAC/U,GAAG,CAAC0W,EAAE,IAAIA,EAAE,CAACvB,SAAS,KAAKvV,SAAS,GAAI8W,EAAE,CAACuJ,eAAe,KAAKrgB,SAAS,oBAAAwa,MAAA,CAAoB1D,EAAE,CAACuJ,eAAe,kBAAA7F,MAAA,CAAe1D,EAAE,CAACvB,SAAS,eAAAiF,MAAA,CAAY1D,EAAE,CAAC4H,OAAO,0BAAAlE,MAAA,CAAuB1D,EAAE,CAACuJ,eAAe,kBAAA7F,MAAA,CAAe1D,EAAE,CAACvB,SAAS,eAAAiF,MAAA,CAAY1D,EAAE,CAAC6H,KAAK,CAAE,GAAK7H,EAAE,CAACuJ,eAAe,KAAKrgB,SAAS,oBAAAwa,MAAA,CAAoB1D,EAAE,CAACuJ,eAAe,eAAA7F,MAAA,CAAY1D,EAAE,CAAC4H,OAAO,0BAAAlE,MAAA,CAAuB1D,EAAE,CAACuJ,eAAe,eAAA7F,MAAA,CAAY1D,EAAE,CAAC6H,KAAK,CAAG,CAAC,CAAC9d,IAAI,CAAC,IAAI,CAAC;IACrd,oBACIvB,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwhB;MAAc;QAAAlf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChE+T,eAAe;IAAA;MAAAlU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEzB;EACAiU,MAAMA,CAACxV,OAAO,EAAE;IACZ,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwhB;MAAc;QAAAlf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEif,QAAQ,CAACxgB,OAAO,CAACwV,MAAM,CAAC,KAAK,CAAC,GAAGxV,OAAO,CAACwV,MAAM,GAAG,eAAe;IAAA;MAAApU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAEzB;EACAkU,SAASA,CAACzV,OAAO,EAAE;IACf,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwhB;MAAc;QAAAlf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEvB,OAAO,CAACyV,SAAS,KAAKvV,SAAS,GAAGF,OAAO,CAACyV,SAAS,CAACnV,GAAG,CAAC0W,EAAE,IAAIA,EAAE,CAAC,CAACjW,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;IAAA;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAEzB;EACAmU,MAAMA,CAAC1V,OAAO,EAAE;IACZ,oBACIR,OAAA,CAACjB,KAAK,CAACkB,QAAQ;MAAAwB,QAAA,gBACXzB,OAAA;QAAM0B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAEnC,QAAQ,CAACwhB;MAAc;QAAAlf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEvB,OAAO,CAAC0V,MAAM;IAAA;MAAAtU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEzB;EACA;EACA2U,SAASA,CAAA,EAAG;IACR,IAAI,CAACG,QAAQ,CAAC;MAAEoK,UAAU,EAAE;IAAG,CAAC,CAAC;IACjC,IAAI,CAACxK,kBAAkB,CAAC,CAAC;IACzBU,QAAQ,CAAC+J,cAAc,CAAC,mBAAmB,CAAC,CAACC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC/J,MAAM,CAAC,UAAU,CAAC;EACjH;EACAb,UAAUA,CAAA,EAAG;IACT,IAAI6K,SAAS,GAAG,EAAE;IAClB,IAAIC,MAAM,GAAG,IAAI,CAAClZ,KAAK,CAAC6Y,UAAU;IAClC,IAAIK,MAAM,KAAK5gB,SAAS,IAAI4gB,MAAM,KAAK,EAAE,EAAE;MACvCD,SAAS,GAAG,kBAAkB;IAClC,CAAC,MAAM;MACHA,SAAS,GAAG,yBAAyB;IACzC;IACA,oBACIrhB,OAAA;MAAK0B,SAAS,EAAC,6CAA6C;MAAAD,QAAA,gBACxDzB,OAAA,CAACN,QAAQ;QAACgG,EAAE,EAAC,mBAAmB;QAACgF,KAAK,EAAE,IAAI,CAACtC,KAAK,CAAC6Y,UAAW;QAACpW,QAAQ,EAAE,IAAI,CAAC4L,kBAAmB;QAACvL,WAAW,EAAC,wBAAwB;QAACqW,UAAU,EAAC,UAAU;QAAC7f,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3L/B,OAAA;QAAG0B,SAAS,EAAE2f,SAAU;QAACG,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9K,SAAS,CAAC;MAAE;QAAA9U,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAEd;EACA0U,kBAAkBA,CAACgL,KAAK,EAAE;IACtB,IAAIA,KAAK,KAAK/gB,SAAS,EAAE;MACrB,IAAI+gB,KAAK,CAAC/W,KAAK,KAAK,IAAI,EAAE;QACtB,IAAI,CAACgX,EAAE,CAAC1C,MAAM,CAAC,IAAI,CAAC2C,UAAU,CAACF,KAAK,CAAC/W,KAAK,CAAC,EAAE,WAAW,EAAE,UAAU,CAAC;MACzE,CAAC,MAAM;QACH,IAAI,CAACgX,EAAE,CAAC1C,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC;MAC/C;MACA,IAAI,CAACnI,QAAQ,CAAC;QAAEoK,UAAU,EAAEQ,KAAK,CAAC/W;MAAM,CAAC,CAAC;IAC9C,CAAC,MAAM;MACH,IAAI,CAACgX,EAAE,CAAC1C,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC;IAC/C;EACJ;EACA3I,UAAUA,CAAC3L,KAAK,EAAEsU,MAAM,EAAE;IACtB,IAAIA,MAAM,KAAKte,SAAS,IAAIse,MAAM,KAAK,IAAI,IAAK,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAG,EAAE;MACjG,OAAO,IAAI;IACf;IACA,IAAIlX,KAAK,KAAKhK,SAAS,IAAIgK,KAAK,KAAK,IAAI,EAAE;MACvC,OAAO,KAAK;IAChB;IACA,OAAOA,KAAK,KAAK,IAAI,CAACiX,UAAU,CAAC3C,MAAM,CAAC;EAC5C;EACA2C,UAAUA,CAACxQ,IAAI,EAAE;IACb,IAAIA,IAAI,KAAK,IAAI,EAAE;MACf,IAAIhQ,KAAK,GAAGgQ,IAAI,CAACxN,QAAQ,CAAC,CAAC,GAAG,CAAC;MAC/B,IAAIzC,GAAG,GAAGiQ,IAAI,CAACvN,OAAO,CAAC,CAAC;MACxB,IAAIzC,KAAK,GAAG,EAAE,EAAE;QACZA,KAAK,GAAG,GAAG,GAAGA,KAAK;MACvB;MACA,IAAID,GAAG,GAAG,EAAE,EAAE;QACVA,GAAG,GAAG,GAAG,GAAGA,GAAG;MACnB;MACA,OAAOiQ,IAAI,CAACtN,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG1C,KAAK,GAAG,GAAG,GAAGD,GAAG;IACvD;EACJ;EACA2gB,mBAAmBA,CAAA,EAAG;IAClB1K,QAAQ,CAAC2K,aAAa,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAC;EAChD;EACAC,MAAMA,CAAA,EAAG;IAAA,IAAAC,iBAAA,EAAAC,kBAAA;IACL;IACA,MAAMjB,UAAU,GAAG,IAAI,CAACzK,UAAU,CAAC,CAAC;IACpC;IACA,MAAM2B,MAAM,gBACRnY,OAAA;MAAK0B,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAAC4G,WAAY;MAAAvN,QAAA,gBACnCzB,OAAA;QAAK0B,SAAS,EAAC,4CAA4C;QAAAD,QAAA,eACvDzB,OAAA;UAAM0B,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAAC2G,gBAAiB;UAAAtN,QAAA,gBACzCzB,OAAA;YAAG0B,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9B/B,OAAA,CAACR,SAAS;YAACkC,SAAS,EAAC,oBAAoB;YAAC4Y,IAAI,EAAC,QAAQ;YAAC6H,OAAO,EAAGrX,CAAC,IAAK,IAAI,CAAC+L,QAAQ,CAAC;cAAEtI,YAAY,EAAEzD,CAAC,CAACkL,MAAM,CAACtL;YAAM,CAAC,CAAE;YAACQ,WAAW,EAAC;UAAU;YAAAtJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClJ/B,OAAA;YAAG0B,SAAS,EAAC,kCAAkC;YAAC2G,IAAI,EAAC,QAAQ;YAACmZ,OAAO,EAAG1W,CAAC,IAAK;cAAEqM,QAAQ,CAAC2K,aAAa,CAAC,eAAe,CAAC,CAACpX,KAAK,GAAG,EAAE;cAAE,IAAI,CAACmM,QAAQ,CAAC;gBAAEtI,YAAY,EAAE;cAAK,CAAC,CAAC;YAAC;UAAE;YAAA3M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7K;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/B,OAAA;QAAK0B,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAAC0G,eAAgB;QAAArN,QAAA,eACvCzB,OAAA;UAAK0B,SAAS,EAAC,8EAA8E;UAAAD,QAAA,gBACzFzB,OAAA,CAACT,MAAM;YAACmC,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAACuG,WAAY;YAAC6S,OAAO,EAAE,IAAI,CAAClhB,KAAK,CAAC8hB,iBAAkB;YAACC,QAAQ,EAAE,IAAI,CAAC/hB,KAAK,CAACgiB,mBAAoB;YAACC,OAAO,EAAE,IAAI,CAACjiB,KAAK,CAACiiB,OAAQ;YAACC,cAAc,EAAE;cAAEC,QAAQ,EAAE;YAAM,CAAE;YAAAhhB,QAAA,GAAG,IAAI,CAACnB,KAAK,CAACoiB,gBAAgB,EAAC,GAAC;UAAA;YAAA9gB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtO/B,OAAA,CAACT,MAAM;YAACmC,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAACwG,YAAa;YAAC4S,OAAO,EAAE,IAAI,CAAClhB,KAAK,CAACqiB,kBAAmB;YAACN,QAAQ,EAAE,IAAI,CAAC/hB,KAAK,CAACsiB,oBAAqB;YAAAnhB,QAAA,GAAG,IAAI,CAACnB,KAAK,CAACuiB,iBAAiB,EAAC,GAAC;UAAA;YAAAjhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAExK/B,OAAA,CAAChB,eAAe;YAACyY,KAAK,EAAE,YAAa;YAAC/V,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAACyG,eAAgB;YAACrO,OAAO,EAAE,IAAI,CAACF,KAAK,CAACoK,KAAM;YAACoY,SAAS,EAAE,IAAI,CAACxiB,KAAK,CAACwiB;UAAU;YAAAlhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1I;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/B,OAAA;QAAK0B,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAAC8G,YAAa;QAAAzN,QAAA,eACpCzB,OAAA;UAAK0B,SAAS,EAAC,2EAA2E;UAAAD,QAAA,eACtFzB,OAAA,CAACT,MAAM;YAACmC,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAACuG,WAAY;YAAC6S,OAAO,EAAE,IAAI,CAAClhB,KAAK,CAAC8hB,iBAAkB;YAAA3gB,QAAA,GAAG,IAAI,CAACnB,KAAK,CAACoiB,gBAAgB,EAAC,GAAC;UAAA;YAAA9gB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/B,OAAA;QAAK0B,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAAC6G,gBAAiB;QAAAxN,QAAA,eACxCzB,OAAA;UAAK0B,SAAS,EAAC,8EAA8E;UAAAD,QAAA,GACxF,IAAI,CAACnB,KAAK,CAACyiB,cAAc,iBACtB/iB,OAAA,CAACT,MAAM;YAACmC,SAAS,EAAE,IAAI,CAACpB,KAAK,CAAC0iB,WAAY;YAACxB,OAAO,EAAE,IAAI,CAAClhB,KAAK,CAAC8hB,iBAAkB;YAACC,QAAQ,EAAE,IAAI,CAAC/hB,KAAK,CAACgiB,mBAAoB;YAACC,OAAO,EAAE,IAAI,CAACjiB,KAAK,CAACiiB,OAAQ;YAACC,cAAc,EAAE;cAAEC,QAAQ,EAAE;YAAM,CAAE;YAAAhhB,QAAA,GAAG,IAAI,CAACnB,KAAK,CAACoiB,gBAAgB,EAAC,GAAC;UAAA;YAAA9gB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAI1O/B,OAAA;YAAM0B,SAAS,EAAC,QAAQ;YAAAD,QAAA,EACnB,IAAI,CAACnB,KAAK,CAACoK,KAAK,KAAK,IAAI,IAAI,IAAI,CAACtC,KAAK,CAAC+G,SAAS,MAAA8S,iBAAA,GAAI,IAAI,CAAC3hB,KAAK,CAACiX,KAAK,cAAA0K,iBAAA,uBAAhBA,iBAAA,CAAkBgB,OAAO,CAAC;cAC5ExL,KAAK,EAAEnY,QAAQ,CAACoY,UAAU;cAC1BwL,OAAO,EAAEA,CAAA,KAAM;gBACX,IAAI,CAACrB,mBAAmB,CAAC,CAAC;cAC9B;YACJ,CAAC,CAAC;UAAA;YAAAjgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEA,CAAC,eACP/B,OAAA,CAACL,WAAW;YAAC8X,KAAK,EAAC,QAAQ;YAACW,IAAI,EAAC,WAAW;YAAC+K,KAAK,EAAE,IAAI,CAAC7iB,KAAK,CAACiX,KAAM;YAAC7V,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACzH/B,OAAA,CAAChB,eAAe;YAACokB,MAAM,EAAE,IAAK;YAAC1hB,SAAS,EAAE,IAAI,CAAC0G,KAAK,CAACyG,eAAgB;YAACrO,OAAO,EAAE,IAAI,CAACF,KAAK,CAACoK,KAAM;YAACoY,SAAS,EAAE,IAAI,CAACxiB,KAAK,CAACwiB;UAAU;YAAAlhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD,oBACI/B,OAAA;MAAK0B,SAAS,EAAC,MAAM;MAAAD,QAAA,eACjBzB,OAAA,CAACZ,SAAS;QAACsC,SAAS,EAAC,6BAA6B;QAC9C2hB,GAAG,EAAG7L,EAAE,IAAK,IAAI,CAACkK,EAAE,GAAGlK,EAAG;QAC1B9M,KAAK,EAAE,IAAI,CAACpK,KAAK,CAACoK,KAAM;QACxBgE,OAAO,EAAE,IAAI,CAACpO,KAAK,CAACoO,OAAQ;QAC5B4U,OAAO,EAAE,IAAI,CAAChjB,KAAK,CAACgjB,OAAQ;QAC5BC,SAAS,EAAE,IAAI,CAACjjB,KAAK,CAACijB,SAAU;QAChCC,IAAI,EAAE,IAAI,CAACljB,KAAK,CAACkjB,IAAK;QACtBC,kBAAkB,EAAE,IAAI,CAACnjB,KAAK,CAACmjB,kBAAmB;QAClDC,SAAS,EAAE,IAAI,CAACpjB,KAAK,CAACojB,SAAU;QAChCC,SAAS,EAAE,IAAI,CAACrjB,KAAK,CAACqjB,SAAU;QAChCC,UAAU,EAAE,IAAI,CAACtjB,KAAK,CAACsjB,UAAW;QAClCrV,YAAY,EAAE,IAAI,CAACnG,KAAK,CAACmG,YAAa;QACtC4J,MAAM,EAAE,IAAI,CAAC7X,KAAK,CAACujB,UAAU,GAAG,IAAI,GAAI,IAAI,CAACvjB,KAAK,CAAC6X,MAAM,GAAG,IAAI,CAAC7X,KAAK,CAAC6X,MAAM,GAAGA,MAAQ;QACxFlN,YAAY,EAAE3L,QAAQ,CAACwkB,YAAa;QACpCC,aAAa,EAAE,IAAI,CAACzjB,KAAK,CAACyjB,aAAc;QACxCC,SAAS,EAAE,IAAI,CAAC1jB,KAAK,CAAC0jB,SAAU;QAChCC,iBAAiB,EAAE,IAAI,CAAC3jB,KAAK,CAAC2jB,iBAAkB;QAChDC,gBAAgB,EAAE,IAAI,CAAC5jB,KAAK,CAAC4jB,gBAAiB;QAC9CC,WAAW,EAAE,IAAI,CAAC7jB,KAAK,CAAC6jB,WAAY;QACpCC,YAAY,EAAE,IAAI,CAAC9jB,KAAK,CAAC8jB,YAAa;QACtCC,WAAW,EAAE,IAAI,CAAC/jB,KAAK,CAAC+jB,WAAY;QACpCC,WAAW,EAAE,IAAI,CAAChkB,KAAK,CAACgkB,WAAY;QACpCC,aAAa,EAAE,IAAI,CAACjkB,KAAK,CAACikB,aAAc;QACxCC,oBAAoB,EAAE,IAAI,CAAClkB,KAAK,CAACkkB,oBAAqB;QACtDC,YAAY,EAAG3Z,CAAC,IAAKA,CAAC,CAAC4Z,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACpkB,KAAK,CAACqkB,aAAa,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC9Z,CAAC,CAACxH,OAAO,CAAC,GAAG,IAAK;QAClGuhB,aAAa,EAAE,IAAI,CAACvkB,KAAK,CAACukB,aAAc;QACxCnf,EAAE,EAAE,IAAI,CAACpF,KAAK,CAACoF,EAAG;QAClBof,QAAQ,EAAE,IAAI,CAACxkB,KAAK,CAACwkB,QAAS;QAC9BC,MAAM,EAAE,IAAI,CAACzkB,KAAK,CAACykB,MAAO;QAC1BC,KAAK,EAAE,IAAI,CAAC1kB,KAAK,CAAC0kB,KAAM;QACxBC,IAAI,EAAE,IAAI,CAAC3kB,KAAK,CAAC2kB,IAAK;QACtBC,YAAY,EAAE,IAAI,CAAC5kB,KAAK,CAAC4kB,YAAa;QACtCC,aAAa,EAAE,IAAI,CAAC7kB,KAAK,CAAC6kB,aAAc;QACxCC,MAAM,EAAE,IAAI,CAAC9kB,KAAK,CAAC8kB,MAAO;QAC1BC,QAAQ,EAAE,IAAI,CAAC/kB,KAAK,CAAC+kB,QAAS;QAC9BC,OAAO,EAAE,IAAI,CAAChlB,KAAK,CAACglB,OAAQ;QAC5BC,YAAY,EAAC,GAAG;QAAA9jB,QAAA,IAAAygB,kBAAA,GAEf,IAAI,CAAC5hB,KAAK,CAACklB,MAAM,cAAAtD,kBAAA,uBAAjBA,kBAAA,CAAmBphB,GAAG,CAAE0W,EAAE,iBACvBxX,OAAA,CAACX,MAAM;UAEHqC,SAAS,EAAE8V,EAAE,CAAC9V,SAAU;UACxB+jB,QAAQ,EAAEjO,EAAE,CAACiO,QAAS;UACtBf,KAAK,EAAElN,EAAE,CAACkN,KAAM;UAChBgB,cAAc,EAAElO,EAAE,CAACkO,cAAe;UAClCC,WAAW,EAAEnO,EAAE,CAACmO,WAAY;UAC5BxN,MAAM,EAAEX,EAAE,CAACoO,UAAU,GAAGpO,EAAE,CAACW,MAAM,GAAG,IAAK;UACzC0N,QAAQ,EAAErO,EAAE,CAACqO,QAAS;UACtB7G,MAAM,EAAExH,EAAE,CAACwH,MAAO;UAClB8G,eAAe,EAAEtO,EAAE,CAACsO,eAAgB;UACpCC,aAAa,EAAE9E,UAAW;UAC1B+E,iBAAiB,EAAExO,EAAE,CAACwO,iBAAkB;UACxC9M,IAAI,EAAE1B,EAAE,CAAC0B,IAAI,KAAKxY,SAAS,GAAIulB,GAAG,IAAK,IAAI,CAAC7W,QAAQ,CAACoI,EAAE,CAAC0B,IAAI,CAAC,CAAC+M,GAAG,CAAC,GAAG,IAAK;UAC1EC,WAAW,EAAE1O,EAAE,CAAC0O,WAAY;UAC5BnC,aAAa,EAAEvM,EAAE,CAACuM;QAAc,GAd3BoC,IAAI,CAACC,MAAM,CAAC,CAAC;UAAAxkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBd,CACZ,CAAC,EACA,IAAI,CAACzB,KAAK,CAACqkB,aAAa,iBACrB3kB,OAAA,CAACX,MAAM;UACHqC,SAAS,EAAC,WAAW;UACrBgjB,KAAK,EAAC,QAAQ;UACdxL,IAAI,EAAGpO,CAAC,IACJ,CAACA,CAAC,CAAC6Q,IAAI,IAAI,CAAA7Q,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAE6Q,IAAI,MAAK,KAAK,gBACxB3b,OAAA,CAACf,QAAQ;YAACumB,MAAM,EAAE,IAAI,CAACllB,KAAK,CAACqkB,aAAc;YACvCrhB,OAAO,EAAEwH;UAAE,GADkCA,CAAC;YAAAlJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAExC,CAAC,GAEX;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}