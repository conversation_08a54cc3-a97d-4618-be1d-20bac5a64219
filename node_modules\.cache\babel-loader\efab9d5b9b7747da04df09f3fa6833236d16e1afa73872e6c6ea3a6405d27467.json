{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/* eslint-disable no-underscore-dangle,react/require-default-props */\nimport * as React from 'react';\nimport raf from './raf';\nimport Portal from './Portal';\nimport canUseDom from './Dom/canUseDom';\nimport switchScrollingEffect from './switchScrollingEffect';\nimport setStyle from './setStyle';\nimport ScrollLocker from './Dom/scrollLocker';\nvar openCount = 0;\nvar supportDom = canUseDom();\n/** @private Test usage only */\n\nexport function getOpenCount() {\n  return process.env.NODE_ENV === 'test' ? openCount : 0;\n} // https://github.com/ant-design/ant-design/issues/19340\n// https://github.com/ant-design/ant-design/issues/19332\n\nvar cacheOverflow = {};\nvar getParent = function getParent(getContainer) {\n  if (!supportDom) {\n    return null;\n  }\n  if (getContainer) {\n    if (typeof getContainer === 'string') {\n      return document.querySelectorAll(getContainer)[0];\n    }\n    if (typeof getContainer === 'function') {\n      return getContainer();\n    }\n    if (_typeof(getContainer) === 'object' && getContainer instanceof window.HTMLElement) {\n      return getContainer;\n    }\n  }\n  return document.body;\n};\nvar PortalWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(PortalWrapper, _React$Component);\n  var _super = _createSuper(PortalWrapper);\n  function PortalWrapper(props) {\n    var _this;\n    _classCallCheck(this, PortalWrapper);\n    _this = _super.call(this, props);\n    _this.container = void 0;\n    _this.componentRef = /*#__PURE__*/React.createRef();\n    _this.rafId = void 0;\n    _this.scrollLocker = void 0;\n    _this.renderComponent = void 0;\n    _this.updateScrollLocker = function (prevProps) {\n      var _ref = prevProps || {},\n        prevVisible = _ref.visible;\n      var _this$props = _this.props,\n        getContainer = _this$props.getContainer,\n        visible = _this$props.visible;\n      if (visible && visible !== prevVisible && supportDom && getParent(getContainer) !== _this.scrollLocker.getContainer()) {\n        _this.scrollLocker.reLock({\n          container: getParent(getContainer)\n        });\n      }\n    };\n    _this.updateOpenCount = function (prevProps) {\n      var _ref2 = prevProps || {},\n        prevVisible = _ref2.visible,\n        prevGetContainer = _ref2.getContainer;\n      var _this$props2 = _this.props,\n        visible = _this$props2.visible,\n        getContainer = _this$props2.getContainer; // Update count\n\n      if (visible !== prevVisible && supportDom && getParent(getContainer) === document.body) {\n        if (visible && !prevVisible) {\n          openCount += 1;\n        } else if (prevProps) {\n          openCount -= 1;\n        }\n      } // Clean up container if needed\n\n      var getContainerIsFunc = typeof getContainer === 'function' && typeof prevGetContainer === 'function';\n      if (getContainerIsFunc ? getContainer.toString() !== prevGetContainer.toString() : getContainer !== prevGetContainer) {\n        _this.removeCurrentContainer();\n      }\n    };\n    _this.attachToParent = function () {\n      var force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (force || _this.container && !_this.container.parentNode) {\n        var parent = getParent(_this.props.getContainer);\n        if (parent) {\n          parent.appendChild(_this.container);\n          return true;\n        }\n        return false;\n      }\n      return true;\n    };\n    _this.getContainer = function () {\n      if (!supportDom) {\n        return null;\n      }\n      if (!_this.container) {\n        _this.container = document.createElement('div');\n        _this.attachToParent(true);\n      }\n      _this.setWrapperClassName();\n      return _this.container;\n    };\n    _this.setWrapperClassName = function () {\n      var wrapperClassName = _this.props.wrapperClassName;\n      if (_this.container && wrapperClassName && wrapperClassName !== _this.container.className) {\n        _this.container.className = wrapperClassName;\n      }\n    };\n    _this.removeCurrentContainer = function () {\n      var _this$container, _this$container$paren;\n\n      // Portal will remove from `parentNode`.\n      // Let's handle this again to avoid refactor issue.\n      (_this$container = _this.container) === null || _this$container === void 0 ? void 0 : (_this$container$paren = _this$container.parentNode) === null || _this$container$paren === void 0 ? void 0 : _this$container$paren.removeChild(_this.container);\n    };\n    _this.switchScrollingEffect = function () {\n      if (openCount === 1 && !Object.keys(cacheOverflow).length) {\n        switchScrollingEffect(); // Must be set after switchScrollingEffect\n\n        cacheOverflow = setStyle({\n          overflow: 'hidden',\n          overflowX: 'hidden',\n          overflowY: 'hidden'\n        });\n      } else if (!openCount) {\n        setStyle(cacheOverflow);\n        cacheOverflow = {};\n        switchScrollingEffect(true);\n      }\n    };\n    _this.scrollLocker = new ScrollLocker({\n      container: getParent(props.getContainer)\n    });\n    return _this;\n  }\n  _createClass(PortalWrapper, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      this.updateOpenCount();\n      if (!this.attachToParent()) {\n        this.rafId = raf(function () {\n          _this2.forceUpdate();\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      this.updateOpenCount(prevProps);\n      this.updateScrollLocker(prevProps);\n      this.setWrapperClassName();\n      this.attachToParent();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$props3 = this.props,\n        visible = _this$props3.visible,\n        getContainer = _this$props3.getContainer;\n      if (supportDom && getParent(getContainer) === document.body) {\n        // 离开时不会 render， 导到离开时数值不变，改用 func 。。\n        openCount = visible && openCount ? openCount - 1 : openCount;\n      }\n      this.removeCurrentContainer();\n      raf.cancel(this.rafId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        children = _this$props4.children,\n        forceRender = _this$props4.forceRender,\n        visible = _this$props4.visible;\n      var portal = null;\n      var childProps = {\n        getOpenCount: function getOpenCount() {\n          return openCount;\n        },\n        getContainer: this.getContainer,\n        switchScrollingEffect: this.switchScrollingEffect,\n        scrollLocker: this.scrollLocker\n      };\n      if (forceRender || visible || this.componentRef.current) {\n        portal = /*#__PURE__*/React.createElement(Portal, {\n          getContainer: this.getContainer,\n          ref: this.componentRef\n        }, children(childProps));\n      }\n      return portal;\n    }\n  }]);\n  return PortalWrapper;\n}(React.Component);\nexport default PortalWrapper;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "_typeof", "React", "raf", "Portal", "canUseDom", "switchScrollingEffect", "setStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openCount", "supportDom", "getOpenCount", "process", "env", "NODE_ENV", "cacheOverflow", "getParent", "getContainer", "document", "querySelectorAll", "window", "HTMLElement", "body", "PortalWrapper", "_React$Component", "_super", "props", "_this", "call", "container", "componentRef", "createRef", "rafId", "scrollLocker", "renderComponent", "updateScrollLocker", "prevProps", "_ref", "prevVisible", "visible", "_this$props", "reLock", "updateOpenCount", "_ref2", "prevGetContainer", "_this$props2", "getContainerIsFunc", "toString", "removeCurrentContainer", "attachToParent", "force", "arguments", "length", "undefined", "parentNode", "parent", "append<PERSON><PERSON><PERSON>", "createElement", "setWrapperClassName", "wrapperClassName", "className", "_this$container", "_this$container$paren", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "overflow", "overflowX", "overflowY", "key", "value", "componentDidMount", "_this2", "forceUpdate", "componentDidUpdate", "componentWillUnmount", "_this$props3", "cancel", "render", "_this$props4", "children", "forceRender", "portal", "childProps", "current", "ref", "Component"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/PortalWrapper.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/* eslint-disable no-underscore-dangle,react/require-default-props */\nimport * as React from 'react';\nimport raf from './raf';\nimport Portal from './Portal';\nimport canUseDom from './Dom/canUseDom';\nimport switchScrollingEffect from './switchScrollingEffect';\nimport setStyle from './setStyle';\nimport ScrollLocker from './Dom/scrollLocker';\nvar openCount = 0;\nvar supportDom = canUseDom();\n/** @private Test usage only */\n\nexport function getOpenCount() {\n  return process.env.NODE_ENV === 'test' ? openCount : 0;\n} // https://github.com/ant-design/ant-design/issues/19340\n// https://github.com/ant-design/ant-design/issues/19332\n\nvar cacheOverflow = {};\n\nvar getParent = function getParent(getContainer) {\n  if (!supportDom) {\n    return null;\n  }\n\n  if (getContainer) {\n    if (typeof getContainer === 'string') {\n      return document.querySelectorAll(getContainer)[0];\n    }\n\n    if (typeof getContainer === 'function') {\n      return getContainer();\n    }\n\n    if (_typeof(getContainer) === 'object' && getContainer instanceof window.HTMLElement) {\n      return getContainer;\n    }\n  }\n\n  return document.body;\n};\n\nvar PortalWrapper = /*#__PURE__*/function (_React$Component) {\n  _inherits(PortalWrapper, _React$Component);\n\n  var _super = _createSuper(PortalWrapper);\n\n  function PortalWrapper(props) {\n    var _this;\n\n    _classCallCheck(this, PortalWrapper);\n\n    _this = _super.call(this, props);\n    _this.container = void 0;\n    _this.componentRef = /*#__PURE__*/React.createRef();\n    _this.rafId = void 0;\n    _this.scrollLocker = void 0;\n    _this.renderComponent = void 0;\n\n    _this.updateScrollLocker = function (prevProps) {\n      var _ref = prevProps || {},\n          prevVisible = _ref.visible;\n\n      var _this$props = _this.props,\n          getContainer = _this$props.getContainer,\n          visible = _this$props.visible;\n\n      if (visible && visible !== prevVisible && supportDom && getParent(getContainer) !== _this.scrollLocker.getContainer()) {\n        _this.scrollLocker.reLock({\n          container: getParent(getContainer)\n        });\n      }\n    };\n\n    _this.updateOpenCount = function (prevProps) {\n      var _ref2 = prevProps || {},\n          prevVisible = _ref2.visible,\n          prevGetContainer = _ref2.getContainer;\n\n      var _this$props2 = _this.props,\n          visible = _this$props2.visible,\n          getContainer = _this$props2.getContainer; // Update count\n\n      if (visible !== prevVisible && supportDom && getParent(getContainer) === document.body) {\n        if (visible && !prevVisible) {\n          openCount += 1;\n        } else if (prevProps) {\n          openCount -= 1;\n        }\n      } // Clean up container if needed\n\n\n      var getContainerIsFunc = typeof getContainer === 'function' && typeof prevGetContainer === 'function';\n\n      if (getContainerIsFunc ? getContainer.toString() !== prevGetContainer.toString() : getContainer !== prevGetContainer) {\n        _this.removeCurrentContainer();\n      }\n    };\n\n    _this.attachToParent = function () {\n      var force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n      if (force || _this.container && !_this.container.parentNode) {\n        var parent = getParent(_this.props.getContainer);\n\n        if (parent) {\n          parent.appendChild(_this.container);\n          return true;\n        }\n\n        return false;\n      }\n\n      return true;\n    };\n\n    _this.getContainer = function () {\n      if (!supportDom) {\n        return null;\n      }\n\n      if (!_this.container) {\n        _this.container = document.createElement('div');\n\n        _this.attachToParent(true);\n      }\n\n      _this.setWrapperClassName();\n\n      return _this.container;\n    };\n\n    _this.setWrapperClassName = function () {\n      var wrapperClassName = _this.props.wrapperClassName;\n\n      if (_this.container && wrapperClassName && wrapperClassName !== _this.container.className) {\n        _this.container.className = wrapperClassName;\n      }\n    };\n\n    _this.removeCurrentContainer = function () {\n      var _this$container, _this$container$paren;\n\n      // Portal will remove from `parentNode`.\n      // Let's handle this again to avoid refactor issue.\n      (_this$container = _this.container) === null || _this$container === void 0 ? void 0 : (_this$container$paren = _this$container.parentNode) === null || _this$container$paren === void 0 ? void 0 : _this$container$paren.removeChild(_this.container);\n    };\n\n    _this.switchScrollingEffect = function () {\n      if (openCount === 1 && !Object.keys(cacheOverflow).length) {\n        switchScrollingEffect(); // Must be set after switchScrollingEffect\n\n        cacheOverflow = setStyle({\n          overflow: 'hidden',\n          overflowX: 'hidden',\n          overflowY: 'hidden'\n        });\n      } else if (!openCount) {\n        setStyle(cacheOverflow);\n        cacheOverflow = {};\n        switchScrollingEffect(true);\n      }\n    };\n\n    _this.scrollLocker = new ScrollLocker({\n      container: getParent(props.getContainer)\n    });\n    return _this;\n  }\n\n  _createClass(PortalWrapper, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n\n      this.updateOpenCount();\n\n      if (!this.attachToParent()) {\n        this.rafId = raf(function () {\n          _this2.forceUpdate();\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      this.updateOpenCount(prevProps);\n      this.updateScrollLocker(prevProps);\n      this.setWrapperClassName();\n      this.attachToParent();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$props3 = this.props,\n          visible = _this$props3.visible,\n          getContainer = _this$props3.getContainer;\n\n      if (supportDom && getParent(getContainer) === document.body) {\n        // 离开时不会 render， 导到离开时数值不变，改用 func 。。\n        openCount = visible && openCount ? openCount - 1 : openCount;\n      }\n\n      this.removeCurrentContainer();\n      raf.cancel(this.rafId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n          children = _this$props4.children,\n          forceRender = _this$props4.forceRender,\n          visible = _this$props4.visible;\n      var portal = null;\n      var childProps = {\n        getOpenCount: function getOpenCount() {\n          return openCount;\n        },\n        getContainer: this.getContainer,\n        switchScrollingEffect: this.switchScrollingEffect,\n        scrollLocker: this.scrollLocker\n      };\n\n      if (forceRender || visible || this.componentRef.current) {\n        portal = /*#__PURE__*/React.createElement(Portal, {\n          getContainer: this.getContainer,\n          ref: this.componentRef\n        }, children(childProps));\n      }\n\n      return portal;\n    }\n  }]);\n\n  return PortalWrapper;\n}(React.Component);\n\nexport default PortalWrapper;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,UAAU,GAAGL,SAAS,CAAC,CAAC;AAC5B;;AAEA,OAAO,SAASM,YAAYA,CAAA,EAAG;EAC7B,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,GAAGL,SAAS,GAAG,CAAC;AACxD,CAAC,CAAC;AACF;;AAEA,IAAIM,aAAa,GAAG,CAAC,CAAC;AAEtB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,YAAY,EAAE;EAC/C,IAAI,CAACP,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAIO,YAAY,EAAE;IAChB,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpC,OAAOC,QAAQ,CAACC,gBAAgB,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;MACtC,OAAOA,YAAY,CAAC,CAAC;IACvB;IAEA,IAAIhB,OAAO,CAACgB,YAAY,CAAC,KAAK,QAAQ,IAAIA,YAAY,YAAYG,MAAM,CAACC,WAAW,EAAE;MACpF,OAAOJ,YAAY;IACrB;EACF;EAEA,OAAOC,QAAQ,CAACI,IAAI;AACtB,CAAC;AAED,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DzB,SAAS,CAACwB,aAAa,EAAEC,gBAAgB,CAAC;EAE1C,IAAIC,MAAM,GAAGzB,YAAY,CAACuB,aAAa,CAAC;EAExC,SAASA,aAAaA,CAACG,KAAK,EAAE;IAC5B,IAAIC,KAAK;IAET9B,eAAe,CAAC,IAAI,EAAE0B,aAAa,CAAC;IAEpCI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,SAAS,GAAG,KAAK,CAAC;IACxBF,KAAK,CAACG,YAAY,GAAG,aAAa5B,KAAK,CAAC6B,SAAS,CAAC,CAAC;IACnDJ,KAAK,CAACK,KAAK,GAAG,KAAK,CAAC;IACpBL,KAAK,CAACM,YAAY,GAAG,KAAK,CAAC;IAC3BN,KAAK,CAACO,eAAe,GAAG,KAAK,CAAC;IAE9BP,KAAK,CAACQ,kBAAkB,GAAG,UAAUC,SAAS,EAAE;MAC9C,IAAIC,IAAI,GAAGD,SAAS,IAAI,CAAC,CAAC;QACtBE,WAAW,GAAGD,IAAI,CAACE,OAAO;MAE9B,IAAIC,WAAW,GAAGb,KAAK,CAACD,KAAK;QACzBT,YAAY,GAAGuB,WAAW,CAACvB,YAAY;QACvCsB,OAAO,GAAGC,WAAW,CAACD,OAAO;MAEjC,IAAIA,OAAO,IAAIA,OAAO,KAAKD,WAAW,IAAI5B,UAAU,IAAIM,SAAS,CAACC,YAAY,CAAC,KAAKU,KAAK,CAACM,YAAY,CAAChB,YAAY,CAAC,CAAC,EAAE;QACrHU,KAAK,CAACM,YAAY,CAACQ,MAAM,CAAC;UACxBZ,SAAS,EAAEb,SAAS,CAACC,YAAY;QACnC,CAAC,CAAC;MACJ;IACF,CAAC;IAEDU,KAAK,CAACe,eAAe,GAAG,UAAUN,SAAS,EAAE;MAC3C,IAAIO,KAAK,GAAGP,SAAS,IAAI,CAAC,CAAC;QACvBE,WAAW,GAAGK,KAAK,CAACJ,OAAO;QAC3BK,gBAAgB,GAAGD,KAAK,CAAC1B,YAAY;MAEzC,IAAI4B,YAAY,GAAGlB,KAAK,CAACD,KAAK;QAC1Ba,OAAO,GAAGM,YAAY,CAACN,OAAO;QAC9BtB,YAAY,GAAG4B,YAAY,CAAC5B,YAAY,CAAC,CAAC;;MAE9C,IAAIsB,OAAO,KAAKD,WAAW,IAAI5B,UAAU,IAAIM,SAAS,CAACC,YAAY,CAAC,KAAKC,QAAQ,CAACI,IAAI,EAAE;QACtF,IAAIiB,OAAO,IAAI,CAACD,WAAW,EAAE;UAC3B7B,SAAS,IAAI,CAAC;QAChB,CAAC,MAAM,IAAI2B,SAAS,EAAE;UACpB3B,SAAS,IAAI,CAAC;QAChB;MACF,CAAC,CAAC;;MAGF,IAAIqC,kBAAkB,GAAG,OAAO7B,YAAY,KAAK,UAAU,IAAI,OAAO2B,gBAAgB,KAAK,UAAU;MAErG,IAAIE,kBAAkB,GAAG7B,YAAY,CAAC8B,QAAQ,CAAC,CAAC,KAAKH,gBAAgB,CAACG,QAAQ,CAAC,CAAC,GAAG9B,YAAY,KAAK2B,gBAAgB,EAAE;QACpHjB,KAAK,CAACqB,sBAAsB,CAAC,CAAC;MAChC;IACF,CAAC;IAEDrB,KAAK,CAACsB,cAAc,GAAG,YAAY;MACjC,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAErF,IAAID,KAAK,IAAIvB,KAAK,CAACE,SAAS,IAAI,CAACF,KAAK,CAACE,SAAS,CAACyB,UAAU,EAAE;QAC3D,IAAIC,MAAM,GAAGvC,SAAS,CAACW,KAAK,CAACD,KAAK,CAACT,YAAY,CAAC;QAEhD,IAAIsC,MAAM,EAAE;UACVA,MAAM,CAACC,WAAW,CAAC7B,KAAK,CAACE,SAAS,CAAC;UACnC,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;IAEDF,KAAK,CAACV,YAAY,GAAG,YAAY;MAC/B,IAAI,CAACP,UAAU,EAAE;QACf,OAAO,IAAI;MACb;MAEA,IAAI,CAACiB,KAAK,CAACE,SAAS,EAAE;QACpBF,KAAK,CAACE,SAAS,GAAGX,QAAQ,CAACuC,aAAa,CAAC,KAAK,CAAC;QAE/C9B,KAAK,CAACsB,cAAc,CAAC,IAAI,CAAC;MAC5B;MAEAtB,KAAK,CAAC+B,mBAAmB,CAAC,CAAC;MAE3B,OAAO/B,KAAK,CAACE,SAAS;IACxB,CAAC;IAEDF,KAAK,CAAC+B,mBAAmB,GAAG,YAAY;MACtC,IAAIC,gBAAgB,GAAGhC,KAAK,CAACD,KAAK,CAACiC,gBAAgB;MAEnD,IAAIhC,KAAK,CAACE,SAAS,IAAI8B,gBAAgB,IAAIA,gBAAgB,KAAKhC,KAAK,CAACE,SAAS,CAAC+B,SAAS,EAAE;QACzFjC,KAAK,CAACE,SAAS,CAAC+B,SAAS,GAAGD,gBAAgB;MAC9C;IACF,CAAC;IAEDhC,KAAK,CAACqB,sBAAsB,GAAG,YAAY;MACzC,IAAIa,eAAe,EAAEC,qBAAqB;;MAE1C;MACA;MACA,CAACD,eAAe,GAAGlC,KAAK,CAACE,SAAS,MAAM,IAAI,IAAIgC,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACP,UAAU,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,WAAW,CAACpC,KAAK,CAACE,SAAS,CAAC;IACvP,CAAC;IAEDF,KAAK,CAACrB,qBAAqB,GAAG,YAAY;MACxC,IAAIG,SAAS,KAAK,CAAC,IAAI,CAACuD,MAAM,CAACC,IAAI,CAAClD,aAAa,CAAC,CAACqC,MAAM,EAAE;QACzD9C,qBAAqB,CAAC,CAAC,CAAC,CAAC;;QAEzBS,aAAa,GAAGR,QAAQ,CAAC;UACvB2D,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAAC3D,SAAS,EAAE;QACrBF,QAAQ,CAACQ,aAAa,CAAC;QACvBA,aAAa,GAAG,CAAC,CAAC;QAClBT,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDqB,KAAK,CAACM,YAAY,GAAG,IAAIzB,YAAY,CAAC;MACpCqB,SAAS,EAAEb,SAAS,CAACU,KAAK,CAACT,YAAY;IACzC,CAAC,CAAC;IACF,OAAOU,KAAK;EACd;EAEA7B,YAAY,CAACyB,aAAa,EAAE,CAAC;IAC3B8C,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC9B,eAAe,CAAC,CAAC;MAEtB,IAAI,CAAC,IAAI,CAACO,cAAc,CAAC,CAAC,EAAE;QAC1B,IAAI,CAACjB,KAAK,GAAG7B,GAAG,CAAC,YAAY;UAC3BqE,MAAM,CAACC,WAAW,CAAC,CAAC;QACtB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASI,kBAAkBA,CAACtC,SAAS,EAAE;MAC5C,IAAI,CAACM,eAAe,CAACN,SAAS,CAAC;MAC/B,IAAI,CAACD,kBAAkB,CAACC,SAAS,CAAC;MAClC,IAAI,CAACsB,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACT,cAAc,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACDoB,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASK,oBAAoBA,CAAA,EAAG;MACrC,IAAIC,YAAY,GAAG,IAAI,CAAClD,KAAK;QACzBa,OAAO,GAAGqC,YAAY,CAACrC,OAAO;QAC9BtB,YAAY,GAAG2D,YAAY,CAAC3D,YAAY;MAE5C,IAAIP,UAAU,IAAIM,SAAS,CAACC,YAAY,CAAC,KAAKC,QAAQ,CAACI,IAAI,EAAE;QAC3D;QACAb,SAAS,GAAG8B,OAAO,IAAI9B,SAAS,GAAGA,SAAS,GAAG,CAAC,GAAGA,SAAS;MAC9D;MAEA,IAAI,CAACuC,sBAAsB,CAAC,CAAC;MAC7B7C,GAAG,CAAC0E,MAAM,CAAC,IAAI,CAAC7C,KAAK,CAAC;IACxB;EACF,CAAC,EAAE;IACDqC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASQ,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACrD,KAAK;QACzBsD,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtC1C,OAAO,GAAGwC,YAAY,CAACxC,OAAO;MAClC,IAAI2C,MAAM,GAAG,IAAI;MACjB,IAAIC,UAAU,GAAG;QACfxE,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;UACpC,OAAOF,SAAS;QAClB,CAAC;QACDQ,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BX,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;QACjD2B,YAAY,EAAE,IAAI,CAACA;MACrB,CAAC;MAED,IAAIgD,WAAW,IAAI1C,OAAO,IAAI,IAAI,CAACT,YAAY,CAACsD,OAAO,EAAE;QACvDF,MAAM,GAAG,aAAahF,KAAK,CAACuD,aAAa,CAACrD,MAAM,EAAE;UAChDa,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BoE,GAAG,EAAE,IAAI,CAACvD;QACZ,CAAC,EAAEkD,QAAQ,CAACG,UAAU,CAAC,CAAC;MAC1B;MAEA,OAAOD,MAAM;IACf;EACF,CAAC,CAAC,CAAC;EAEH,OAAO3D,aAAa;AACtB,CAAC,CAACrB,KAAK,CAACoF,SAAS,CAAC;AAElB,eAAe/D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}