{"version": 3, "file": "unbuilt-imports.js", "sourceRoot": "", "sources": ["../../src/unbuilt-imports.ts"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,4DAA4D;AAC5D,OAAO,EAAE,aAAa,EAAE,MAAM,IAAI,CAAA;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAA;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,MAAM,CAAA;AACtD,OAAO,EACL,uBAAuB,EACvB,sBAAsB,EACtB,sBAAsB,GACvB,MAAM,gBAAgB,CAAA;AACvB,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAGvC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAA;AAElC,8CAA8C;AAC9C,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,CAAS,EAAW,EAAE;IACzC,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;IACpC,IAAI,CAAC,KAAK,CAAC,MAAM;QAAE,OAAO,KAAK,CAAA;IAC/B,MAAM,IAAI,GAAG,IAAI,GAAG,CAAS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAClE,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAA;IACtC,aAAa,CACX,CAAC,EACD;;eAEW,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;;EAEtC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;CAIrD,CACE,CAAA;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,IAAI,OAAO,GAAyB,SAAS,CAAA;AAC7C,mEAAmE;AACnE,oBAAoB;AACpB,MAAM,UAAU,GAAG,KAAK,EAAE,OAA4B,EAAE,EAAE;IACxD,MAAM,KAAK,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC,MAAM,CACnD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAC7B,CAAA;IACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QACtC,YAAY;QACZ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,CAAC;IACD,MAAM,IAAI,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAA;IAC5C,MAAM,CAAC,GAAG,IAAI,GAAG,EAAU,CAAA;IAC3B,MAAM,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;IAClC,KAAK,MAAM,UAAU,IAAI,IAAI,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,MAAM,sBAAsB,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,CAAA;QAC7D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,eAAe;YACf,IAAI,OAAO,GAAG,KAAK,QAAQ;gBAAE,SAAQ;YACrC,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAA;YAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;YACtC,gEAAgE;YAChE,IACE,CAAC,GAAG;gBACJ,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;gBAC1B,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;gBAC3B,GAAG,CAAC,UAAU,CAAC,cAAc,GAAG,GAAG,CAAC;gBAEpC,SAAQ;YACV,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IACD,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AAC3B,CAAC,CAAA;AAED,MAAM,OAAO,GAAG,IAAI,GAAG,EAAkB,CAAA;AAEzC,4DAA4D;AAC5D,MAAM,CAAC,MAAM,IAAI,GAAG,KAAK,EACvB,GAAY,EACZ,GAAW,EACX,IAAI,GAAG,KAAK,EACZ,EAAE;IACF,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAA;IACvB,IAAI,CAAC,OAAO;QAAE,OAAM;IACpB,IAAI,CAAC,OAAO;QAAE,OAAO,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,OAAO,CAAC,MAAM;QAAE,OAAM;IAC3B,OAAO,CAAC,KAAK,CAAC,0BAA0B,GAAG,EAAE,EAAE,OAAO,CAAC,CAAA;IACvD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;IACjD,MAAM,GAAG,GAAmB,EAAE,CAAA;IAC9B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAChC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,KAAK,GACT,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAChB,EAAE;aACC,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;aAChB,IAAI,CAAC,EAAE,CAAC,CACZ,CAAA;QACH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAA;QAC1B,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,CAAA;QACjC,IAAI,IAAI;YAAE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAChC,GAAG,CAAC,IAAI,CACN,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAClB,IAAI,CAAC,CAAC,CAAC,EAAE;YACR,qDAAqD;YACrD,qDAAqD;YACrD,IAAI,CAAC,IAAI,IAAI,CAAC;gBAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAClC,CAAA;IACH,CAAC;IACD,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACxB,CAAC,CAAA;AAED,gEAAgE;AAChE,MAAM,CAAC,MAAM,MAAM,GAAG,KAAK,EAAE,GAAY,EAAE,GAAW,EAAE,EAAE;IACxD,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAA;IACvB,IAAI,CAAC,OAAO;QAAE,OAAM;IACpB,yCAAyC;IACzC,qBAAqB;IACrB,IAAI,CAAC,OAAO;QAAE,OAAO,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,CAAA;IACjD,oBAAoB;IACpB,OAAO,CAAC,KAAK,CAAC,4BAA4B,GAAG,EAAE,EAAE,OAAO,CAAC,CAAA;IACzD,MAAM,GAAG,GAAmB,EAAE,CAAA;IAC9B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAC5B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;IACxB,CAAC;IACD,KAAK,MAAM,CAAC,IAAI,QAAQ;QAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7C,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACxB,CAAC,CAAA", "sourcesContent": ["// this is the thing that supports top-level package.json imports\n// via symlinks, not the tshy.imports which are just config.\nimport { writeFileSync } from 'fs'\nimport { symlink } from 'fs/promises'\nimport { mkdirp } from 'mkdirp'\nimport { dirname, relative, resolve, sep } from 'path'\nimport {\n  getAllConditionalValues,\n  getUniqueConditionSets,\n  resolveAllLocalImports,\n} from 'resolve-import'\nimport { rimraf } from 'rimraf'\nimport { fileURLToPath } from 'url'\nimport * as console from './console.js'\nimport { Package } from './types.js'\n\nconst dirsMade = new Set<string>()\n\n// write out the steps to the save file script\nexport const save = (f: string): boolean => {\n  const links = [...saveSet.entries()]\n  if (!links.length) return false\n  const dirs = new Set<string>(links.map(([dest]) => dirname(dest)))\n  console.debug('save import linker', f)\n  writeFileSync(\n    f,\n    `import { mkdirSync } from 'node:fs'\nimport { symlink } from 'node:fs/promises'\nconst dirs = ${JSON.stringify([...dirs])}\nconst links = [\n${links.map(l => `  ${JSON.stringify(l)},\\n`).join('')}]\nconst e = (er) => { if (er.code !== 'EEXIST') throw er }\nfor (const d of dirs) mkdirSync(d, { recursive: true })\nPromise.all(links.map(([dest, src]) => symlink(src, dest).catch(e)))\n`,\n  )\n  return true\n}\n\nlet targets: undefined | string[] = undefined\n// Get the targets that will have to be linked, because they're not\n// a target in ./src\nconst getTargets = async (imports: Record<string, any>) => {\n  const conds = getAllConditionalValues(imports).filter(\n    c => !c.startsWith('./src/'),\n  )\n  if (!conds.some(c => c.includes('*'))) {\n    // fast path\n    return (targets = conds.filter(c => c.startsWith('./')))\n  }\n  const sets = getUniqueConditionSets(imports)\n  const t = new Set<string>()\n  const pj = resolve('package.json')\n  for (const conditions of sets) {\n    const imps = await resolveAllLocalImports(pj, { conditions })\n    for (const url of Object.values(imps)) {\n      // node builtin\n      if (typeof url === 'string') continue\n      const p = fileURLToPath(url)\n      const rel = relative(process.cwd(), p)\n      // if it's empty, a dep in node_modules, or a built module, skip\n      if (\n        !rel ||\n        rel.startsWith('..' + sep) ||\n        rel.startsWith('src' + sep) ||\n        rel.startsWith('node_modules' + sep)\n      )\n        continue\n      t.add('./' + rel.replace(/\\\\/g, '/'))\n    }\n  }\n  return (targets = [...t])\n}\n\nconst saveSet = new Map<string, string>()\n\n// create symlinks for the package imports in the target dir\nexport const link = async (\n  pkg: Package,\n  dir: string,\n  save = false,\n) => {\n  const { imports } = pkg\n  if (!imports) return\n  if (!targets) targets = await getTargets(imports)\n  if (!targets.length) return\n  console.debug(`link import targets in ${dir}`, targets)\n  const rel = relative(resolve(dir), process.cwd())\n  const lps: Promise<any>[] = []\n  for (const t of targets) {\n    const l = t.replace(/^\\.\\//, '')\n    const df = dirname(l)\n    const dfrel =\n      df === '.' ? '' : (\n        df\n          .split('/')\n          .map(() => '../')\n          .join('')\n      )\n    const dest = dir + '/' + l\n    const src = rel + '/' + dfrel + l\n    if (save) saveSet.set(dest, src)\n    lps.push(\n      mkdirp(dirname(dest))\n        .then(d => {\n          // if we aren't saving, then this is a transient link\n          // save the dirs created so that we can clean them up\n          if (!save && d) dirsMade.add(d)\n          return rimraf(dest)\n        })\n        .then(() => symlink(src, dest)),\n    )\n  }\n  await Promise.all(lps)\n}\n\n// remove symlinks created for package imports in the target dir\nexport const unlink = async (pkg: Package, dir: string) => {\n  const { imports } = pkg\n  if (!imports) return\n  // will always have targets by this point\n  /* c8 ignore start */\n  if (!targets) targets = await getTargets(imports)\n  /* c8 ignore stop */\n  console.debug(`unlink import targets in ${dir}`, targets)\n  const lps: Promise<any>[] = []\n  for (const t of targets) {\n    const dest = resolve(dir, t)\n    lps.push(rimraf(dest))\n  }\n  for (const d of dirsMade) lps.push(rimraf(d))\n  await Promise.all(lps)\n}\n"]}