export default ViewportDimensions;
declare class ViewportDimensions extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts.ViewportDimensions>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts.ViewportDimensions>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=viewport-dimensions.d.ts.map