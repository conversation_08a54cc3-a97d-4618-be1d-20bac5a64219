{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\associaListinoPV.jsx\";\n/* /**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AssociaListini - operazioni sull'associazione listini e modifica prezzi per i punti vendita (manager-price)\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from \"../components/navigation/Nav\";\nimport Immagine from '../img/mktplaceholder.jpg';\nimport ScaricaCSVProva from '../common/distributore/aggiunta file/scaricaCSVProva';\nimport { APIRequest, baseProxy } from '../components/generalizzazioni/apireq';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Tooltip } from 'primereact/tooltip';\nimport { Dialog } from 'primereact/dialog';\nimport { AggProdInList } from './aggiungiProdInListinoPDV';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Sidebar } from 'primereact/sidebar';\nimport { JoyrideGen } from '../components/footer/joyride';\nimport { Toolbar } from 'primereact/toolbar';\nimport { PanelMenu } from 'primereact/panelmenu';\nimport { SplitButton } from 'primereact/splitbutton';\nimport '../css/modale.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass AssociaListiniPV extends Component {\n  constructor(props) {\n    super(props);\n    //Dichiarazione variabili di scena\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      idProduct: null,\n      description: '',\n      address: '',\n      pIva: '',\n      email: '',\n      isValid: '',\n      createAt: '',\n      updateAt: ''\n    };\n    /* Seleziono il punto vendita e controllo che abbia un listino associato */\n    this.onPDVChange = async e => {\n      this.setState({\n        selectedPDV: e.value\n      });\n      window.sessionStorage.setItem(\"PDV\", e.value);\n      if (e.value.length > 1) {\n        this.setState({\n          results: this.state.results2,\n          results4: this.state.results2,\n          idPriceList: this.state.results2.idPriceList,\n          classe: 'd-none'\n        });\n      } else if (e.value.length !== 0) {\n        var url = 'pricelistretailer/?idRetailer=' + e.value;\n        await APIRequest('GET', url).then(res => {\n          if (res.data !== '') {\n            var prodotti = [];\n            res.data.idPriceList2.priceListProducts.forEach(element => {\n              var x = {\n                idProduct: element.idProduct2.id,\n                externalCode: element.idProduct2.externalCode,\n                description: element.idProduct2.description,\n                price: element.price,\n                brand: element.idProduct2.brand,\n                subfamily: element.idProduct2.subfamily,\n                group: element.idProduct2.group,\n                family: element.idProduct2.family\n              };\n              prodotti.push(x);\n            });\n            this.setState({\n              results: prodotti,\n              idPriceList: res.data.idPriceList\n            });\n          } else {\n            /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n            return confirmDialog({\n              message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n              header: 'Siamo spiacenti',\n              icon: 'pi pi-exclamation-triangle',\n              acceptLabel: \"Si\",\n              rejectLabel: \"No\",\n              accept: () => this.createPricelist(e),\n              reject: null\n            });\n          }\n        }).catch(e => {\n          console.log(e);\n          /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n          confirmDialog({\n            message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n            header: 'Siamo spiacenti',\n            icon: 'pi pi-exclamation-triangle',\n            accept: () => this.createPricelist(e),\n            reject: null\n          });\n        });\n      }\n      this.items = [{\n        label: Costanti.Categorie,\n        icon: 'pi pi-fw pi-file',\n        items: []\n      }];\n      this.defineSort();\n      this.findPrice();\n    };\n    /* Ricavo il prezzo dei prodotti dal listino dell'affiliato */\n    this.findPrice = () => {\n      var prod = [];\n      if (this.state.results !== null) {\n        prod = this.state.results;\n        for (var i = 0; i < prod.length; i++) {\n          prod[i].basePrice = prod[i].price;\n          for (var j = 0; j < this.state.results2.length; j++) {\n            if (prod[i].idProduct === this.state.results2[j].idProduct) {\n              prod[i].priceDad = this.state.results2[j].price;\n              break;\n            }\n          }\n        }\n        return this.setState({\n          results: prod,\n          results4: prod\n        });\n      }\n    };\n    /* Ricavo il nome del prodotto */\n    this.productDescriptionBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il codice esterno del prodotto */\n    this.externalCodeBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.externalCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo la famiglia del prodotto */\n    this.familyBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.family\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il formato del prodotto */\n    this.formatBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.format\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il gruppo del prodotto */\n    this.groupBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.group\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo l'iva del prodotto */\n    this.ivaBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.iva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo la nazione di provenienza del prodotto */\n    this.nationalityBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.nationality\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo la regione di provenienza del prodotto */\n    this.regionBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.region\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo la sottofamiglia del prodotto */\n    this.subfamilyBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.subfamily\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il sottogruppo del prodotto */\n    this.subgroupBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idProduct2.subgroup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 13\n      }, this);\n    };\n    //Converto data e ora in formato IT\n    this.orderdataBodyTemplate = results => {\n      if (results !== undefined) {\n        // results.order_data\n        const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit'\n        }).format(new Date(datetime))).join(\" - \");\n        const output = convert(results.idPriceList2.validFrom);\n        return output;\n        // console.log(output);\n        // document.body.innerHTML = output;\n      } else {\n        return null;\n      }\n    };\n    //Converto data e ora in formato IT\n    this.orderdataBodyTemplate2 = results => {\n      if (results !== undefined) {\n        // results.order_data\n        const convert = dateRange => dateRange.split(\" - \").map(datetime => new Intl.DateTimeFormat(\"it-IT\", {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit'\n        }).format(new Date(datetime))).join(\" - \");\n        const output = convert(results.idPriceList2.validTo);\n        return output;\n        // console.log(output);\n        // document.body.innerHTML = output;\n      } else {\n        return null;\n      }\n    };\n    /* Ricavo il nome del prodotto */\n    this.descriptionBodyTemplate = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: results.idPriceList2.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il prezzo del prodotto e lo formatto in Euro */\n    this.priceBodyTemplate2 = results => {\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-column-title\",\n          children: new Intl.NumberFormat('it-IT', {\n            style: 'currency',\n            currency: 'EUR'\n          }).format(results.price)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 13\n      }, this);\n    };\n    /* Ricavo il prezzo del prodotto dal listino dell'affiliato */\n    this.priceDadBodyTemplate = results => {\n      if (results.priceDad) {\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-column-title\",\n            children: new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(results.priceDad)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 17\n        }, this);\n      } else {\n        return '0,00 €';\n      }\n    };\n    //Metodo di invio dati mediante chiamata axios per la creazione del punto vendita con i valori necessari alla creazione\n    this.Invia = async () => {\n      var prodotti = [];\n      /* Salvo id e prezzo di tutti i prodotti */\n      this.state.results4.forEach(element => {\n        if (element.newPrice !== undefined) {\n          prodotti.push({\n            id: element.idProduct,\n            price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\"))\n          });\n        } else {\n          prodotti.push({\n            id: element.idProduct,\n            price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\"))\n          });\n        }\n      });\n      var completa = {\n        products: prodotti\n      };\n      var url = 'pricelist/?id=' + this.state.idPriceList;\n      await APIRequest('PUT', url, completa).then(async res => {\n        console.log(this.state.selectedPDV);\n        let pdvs = [...this.state.PDVs];\n        let find = pdvs.find(el => el.id === this.state.selectedPDV);\n        console.log(find);\n        if (find) {\n          var _find$idRegistry$user;\n          let body = {\n            idUserSender: JSON.parse(localStorage.getItem('user')).id,\n            idUserReciver: (_find$idRegistry$user = find.idRegistry.users[0]) === null || _find$idRegistry$user === void 0 ? void 0 : _find$idRegistry$user.id,\n            message: 'Il listino è stato aggiornato. Scopri le nuove offerte!'\n          };\n          await APIRequest('POST', 'notifyuser', body).then(res => {\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: 'La modifica è avvenuta con successo e la notifica è stata inviata correttamente',\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            this.toast.show({\n              severity: 'warn',\n              summary: 'Attenzione!',\n              detail: 'La modifica è avvenuta con successo ma la notifica non è stata inviata correttamente',\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          });\n        } else {\n          this.toast.show({\n            severity: 'warn',\n            summary: 'Attenzione!',\n            detail: 'La modifica è avvenuta con successo ma la notifica non è stata inviata correttamente',\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"La modifica non \\xE8 avvenuta con successo. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      PDVs: null,\n      selectedPDV: null,\n      globalFilter: null,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      result: this.emptyResult,\n      deleteResultDialog: false,\n      deleteProductsDialog: false,\n      selectedProducts: null,\n      loading: true,\n      value: 0,\n      idPriceList: 0,\n      value1: '',\n      value2: '',\n      value3: '',\n      search: '',\n      search2: '',\n      classe: '',\n      viewJoyride: false,\n      family: [],\n      sottoCategorie: [],\n      brand: [],\n      group: []\n    };\n    /* Ricerca per nome prodotto o codice esterno */\n    this.handleChange = e => {\n      this.setState({\n        search: e.target.value\n      });\n      var description = [];\n      var externalCode = [];\n      var risultato = [];\n      var data = this.state.results4;\n      var search = e.target.value.trim().toLowerCase();\n      if (search.length > 0) {\n        data.forEach(element => {\n          description.push(element.description);\n          externalCode.push(element.externalCode);\n        });\n        var desc = description.filter(function (i) {\n          return i.toLowerCase().match(search);\n        });\n        var exCode = externalCode.filter(function (i) {\n          return i.toLowerCase().match(search);\n        });\n        if (desc.length > 0) {\n          desc.forEach(item => {\n            risultato.push(data.find(element => element.description === item));\n          });\n        }\n        if (exCode.length > 0) {\n          exCode.forEach(item => {\n            risultato.push(data.find(element => element.externalCode === item));\n          });\n        }\n        if (risultato.length > 0) {\n          this.setState({\n            results: risultato\n          });\n        } else {\n          this.setState({\n            results: []\n          });\n        }\n      } else {\n        this.setState({\n          results: this.state.results4\n        });\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterProd = e => {\n      this.setState({\n        search2: e.item.label\n      });\n      var subfamily = [];\n      /* var family = []; */\n      var finRes = [];\n      var risultato = [];\n      var brand = [];\n      var group = [];\n      var data = this.state.results4;\n      var filter = '';\n      if (e.item.label === 'ALTRO') {\n        filter = null;\n      } else {\n        filter = e.item.label.trim().toLowerCase();\n      }\n      if (filter === null || filter.length > 0) {\n        data.forEach(element => {\n          if (element.subfamily !== null && element.subfamily !== '') {\n            subfamily.push(element.subfamily);\n          } else {\n            if (e.item.value === 'subfamily') {\n              finRes = data.filter(element => element.subfamily === null || element.subfamily === '');\n            }\n          }\n          if (element.group !== null && element.group !== '') {\n            group.push(element.group);\n          } else {\n            if (e.item.value === 'group') {\n              finRes = data.filter(element => element.subfamily === e.item.dad && (element.group === null || element.group === ''));\n            }\n          }\n          /*  if (element.family !== null && element.family !== '') {\n               family.push(element.family)\n           } else {\n               risultato = data.filter(element => element.family === null || element.family === '');\n           } */\n          if (element.brand !== null && element.brand !== '') {\n            brand.push(element.brand);\n          } else {\n            if (e.item.value === 'brand') {\n              finRes = data.filter(element => element.brand === null || element.brand === '');\n            }\n          }\n        });\n        var subf = subfamily.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        var gruppo = group.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        /*  var fam = family.filter(function (i) {\n             return i.toLowerCase().match(filter);\n         }); */\n        var brandPresent = brand.filter(function (i) {\n          return i.toLowerCase().match(filter);\n        });\n        if (subf.length > 0) {\n          risultato = data.filter(element => element.subfamily === subf[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (gruppo.length > 0) {\n          risultato = data.filter(element => element.group === gruppo[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        } /* if (fam.length > 0) {\n            risultato = data.filter(element => element.family === fam[0]);\n          } */\n        if (brandPresent.length > 0) {\n          risultato = data.filter(element => element.brand === brandPresent[0]);\n          risultato.forEach(el => {\n            finRes.push(el);\n          });\n        }\n        if (finRes.length > 0) {\n          this.setState({\n            results: finRes\n          });\n        } else {\n          this.setState({\n            results: []\n          });\n        }\n      } else {\n        this.setState({\n          results: this.state.results4\n        });\n      }\n    };\n    this.items = [{\n      label: Costanti.Categorie,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.items2 = [{\n      label: \"Brand\",\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.PDV = [];\n    //Dichiarazione funzioni e metodi\n    this.descriptionBodyTemplate = this.descriptionBodyTemplate.bind(this);\n    this.productDescriptionBodyTemplate = this.productDescriptionBodyTemplate.bind(this);\n    this.externalCodeBodyTemplate = this.externalCodeBodyTemplate.bind(this);\n    this.familyBodyTemplate = this.familyBodyTemplate.bind(this);\n    this.formatBodyTemplate = this.formatBodyTemplate.bind(this);\n    this.groupBodyTemplate = this.groupBodyTemplate.bind(this);\n    this.ivaBodyTemplate = this.ivaBodyTemplate.bind(this);\n    this.nationalityBodyTemplate = this.nationalityBodyTemplate.bind(this);\n    this.regionBodyTemplate = this.regionBodyTemplate.bind(this);\n    this.subfamilyBodyTemplate = this.subfamilyBodyTemplate.bind(this);\n    this.subgroupBodyTemplate = this.subgroupBodyTemplate.bind(this);\n    this.imageBodyTemplate = this.imageBodyTemplate.bind(this);\n    this.createPricelist = this.createPricelist.bind(this);\n    this.onPDVChange = this.onPDVChange.bind(this);\n    this.findPrice = this.findPrice.bind(this);\n    this.priceBodyTemplate2 = this.priceBodyTemplate2.bind(this);\n    this.priceDadBodyTemplate = this.priceDadBodyTemplate.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.actionBodyTemplate = this.actionBodyTemplate.bind(this);\n    this.aggiungiProdInList = this.aggiungiProdInList.bind(this);\n    this.hideaggiungiProdInList = this.hideaggiungiProdInList.bind(this);\n    this.leftToolbarTemplate = this.leftToolbarTemplate.bind(this);\n    this.rightToolbarTemplate = this.rightToolbarTemplate.bind(this);\n    this.confirmDeleteSelected = this.confirmDeleteSelected.bind(this);\n    this.hideDeleteProductsDialog = this.hideDeleteProductsDialog.bind(this);\n    this.deleteSelectedProducts = this.deleteSelectedProducts.bind(this);\n    this.defineSort = this.defineSort.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n  async componentDidMount() {\n    /* Reperisco listino affiliato */\n    await APIRequest('GET', 'pricelistaffiliate/').then(res => {\n      this.setState({\n        results2: res.data[0].idPriceList2.priceListProducts,\n        results3: res.data[0].idPriceList2,\n        loading: false\n      });\n    }).catch(e => {\n      var _e$response3, _e$response4;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile visualizzare il suo listino. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n        life: 3000\n      });\n    });\n    /* Mappo i punti vendita per nome e id per visualizzarli nel dropdown */\n    await APIRequest('GET', 'retailers/').then(res => {\n      for (var entry of res.data) {\n        this.PDV.push({\n          name: entry.idRegistry.firstName,\n          value: entry.id\n        });\n      }\n      this.setState({\n        PDVs: res.data\n      });\n    }).catch(e => {\n      var _e$response5, _e$response6;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile visualizzare i suoi punti vendita. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n        life: 3000\n      });\n    });\n    var pdv = JSON.parse(window.sessionStorage.getItem(\"PDV\"));\n    if (pdv !== null && pdv !== 0) {\n      this.setState({\n        selectedPDV: pdv\n      });\n      if (pdv.length > 1) {\n        this.setState({\n          results: this.state.results2,\n          results4: this.state.results2,\n          idPriceList: this.state.results2.idPriceList,\n          classe: 'd-none'\n        });\n      } else if (pdv.length !== 0) {\n        var url = 'pricelistretailer/?idRetailer=' + pdv;\n        await APIRequest('GET', url).then(res => {\n          if (res.data !== '') {\n            var prodotti = [];\n            res.data.idPriceList2.priceListProducts.forEach(element => {\n              var x = {\n                idProduct: element.idProduct2.id,\n                externalCode: element.idProduct2.externalCode,\n                description: element.idProduct2.description,\n                price: element.price,\n                brand: element.idProduct2.brand,\n                subfamily: element.idProduct2.subfamily,\n                group: element.idProduct2.group,\n                family: element.idProduct2.family\n              };\n              prodotti.push(x);\n            });\n            this.setState({\n              results: prodotti,\n              idPriceList: res.data.idPriceList\n            });\n          } else {\n            /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n            return confirmDialog({\n              message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n              header: 'Siamo spiacenti',\n              icon: 'pi pi-exclamation-triangle',\n              acceptLabel: \"Si\",\n              rejectLabel: \"No\",\n              accept: () => this.createPricelist(pdv),\n              reject: null\n            });\n          }\n        }).catch(e => {\n          console.log(e);\n          /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n          confirmDialog({\n            message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n            header: 'Siamo spiacenti',\n            icon: 'pi pi-exclamation-triangle',\n            acceptLabel: \"Si\",\n            rejectLabel: \"No\",\n            accept: () => this.createPricelist(e),\n            reject: null\n          });\n        });\n      }\n      this.defineSort();\n      this.findPrice();\n    } else {\n      this.setState({\n        resultDialog2: true,\n        viewJoyride: true\n      });\n    }\n  }\n  componentDidUpdate() {\n    var DelBtn = document.getElementById(\"DeleteMultiple\");\n    if (!DelBtn.hasAttribute(\"disabled\")) {\n      if (DelBtn.classList.contains(\"moved-in-footer\")) {\n        // Nothing to do right now\n      } else {\n        var footerActions = document.querySelector(\"#invia\");\n        footerActions.parentNode.appendChild(DelBtn, footerActions.nextSibling);\n      }\n      DelBtn.classList.remove(\"d-none\");\n      DelBtn.classList.add(\"deleteSelected\", \"moved-in-footer\", \"fade-in-bottom\");\n    }\n  }\n  /* Definiamo le categorie per il filtraggio  */\n  defineSort() {\n    var family = [];\n    var sottoCategorie = [];\n    var brand = [];\n    var group = [];\n    var mix = [];\n    if (this.state.results !== null) {\n      this.state.results.forEach(element => {\n        family.push(element.family);\n        sottoCategorie.push(element.subfamily);\n        brand.push(element.brand);\n        mix.push(element.subfamily + ' ' + element.group);\n        group.push(element.group);\n      });\n      family = [...new Set(family)].sort();\n      sottoCategorie = [...new Set(sottoCategorie)].sort();\n      group = [...new Set(group)].sort();\n      mix = [...new Set(mix)];\n      brand = [...new Set(brand)].sort();\n      var elementnull = [];\n      brand.forEach(element => {\n        if (element !== '') {\n          this.items2[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'brand',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items2[0].items.push(items);\n      });\n      elementnull = [];\n      sottoCategorie.forEach(element => {\n        if (element !== null && element !== '') {\n          this.items[0].items.push({\n            label: element,\n            command: e => {\n              this.filterProd(e);\n            },\n            items: []\n          });\n        } else {\n          elementnull.push({\n            label: \"ALTRO\",\n            value: 'subfamily',\n            command: e => {\n              this.filterProd(e);\n            }\n          });\n        }\n      });\n      elementnull.forEach(items => {\n        this.items[0].items.push(items);\n      });\n      group.forEach(items => {\n        this.items[0].items.forEach(item => {\n          if (item.label !== null) {\n            if (mix.includes(item.label.concat(' ' + items))) {\n              if (items !== null && items !== '') {\n                item.items.push({\n                  label: items,\n                  command: e => {\n                    this.filterProd(e);\n                  }\n                });\n              } else {\n                item.items.push({\n                  label: \"ALTRO\",\n                  value: 'group',\n                  dad: item.label,\n                  command: e => {\n                    this.filterProd(e);\n                  }\n                });\n              }\n            }\n          }\n        });\n      });\n      this.setState({\n        family: family,\n        sottoCategorie: sottoCategorie,\n        brand: brand,\n        group: group\n      });\n    }\n  }\n  /* Creo il listino per il punto vendita */\n  async createPricelist(e) {\n    var completa = {\n      idRetailer: this.state.selectedPDV\n    };\n    await APIRequest('POST', 'pricelistretailer/', completa).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: 'Il listino è stato associato con successo',\n        life: 3000\n      });\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile visualizzare i listini per qesto punto vendita. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    });\n    this.onPDVChange(e);\n  }\n  /* Effettuo modifica per il nuovo prezzo imposto dall'affiliato */\n  onEditorValueChange(productKey, props, value) {\n    if (value !== null) {\n      let updatedProducts = [...props.value];\n      updatedProducts[props.rowIndex]['newPrice'] /* [props.field] */ = new Intl.NumberFormat('it-IT', {\n        style: 'currency',\n        currency: 'EUR'\n      }).format(value);\n      this.setState({\n        [\"\".concat(productKey)]: updatedProducts\n      });\n    }\n  }\n  /* Componente InputNumber per modifica del prezzo per il punto vendita */\n  priceEditor(productKey, props) {\n    return /*#__PURE__*/_jsxDEV(InputNumber, {\n      onValueChange: e => this.onEditorValueChange(productKey, props, e.value),\n      mode: \"currency\",\n      currency: \"EUR\",\n      locale: \"it-IT\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 16\n    }, this);\n  }\n  /* Formatto il prezzo modificato in Euro */\n  priceBodyTemplate(rowData) {\n    if (rowData.newPrice !== undefined) {\n      var prezzi = 0;\n      if (typeof rowData.newPrice === 'string') {\n        prezzi = parseFloat(rowData.newPrice.replace(\"€\", \"\").replace(\",\", \".\"));\n      } else {\n        prezzi = rowData.newPrice;\n      }\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"priceAdded price-filled\",\n        children: new Intl.NumberFormat('it-IT', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(prezzi)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 20\n      }, this);\n    } else {\n      return null;\n    }\n  }\n  /* Moltiplico i prezzi di tutti i prodotti con percentuale inserita dall'affiliato */\n  moltiplicaPrezzi(e) {\n    var prod = [];\n    if (e.value !== 0) {\n      prod = this.state.results;\n      if (prod !== null) {\n        prod.forEach(element => {\n          var prezzoMagg = 0;\n          if (element.priceDad !== undefined) {\n            prezzoMagg = parseFloat(element.priceDad);\n            prezzoMagg += parseFloat(element.priceDad) * e.value / 100;\n            element.newPrice = new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(prezzoMagg);\n          } else {\n            prezzoMagg = parseFloat(element.price);\n            prezzoMagg += parseFloat(element.price) * e.value / 100;\n            element.newPrice = new Intl.NumberFormat('it-IT', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(prezzoMagg);\n          }\n        });\n      }\n      this.setState({\n        results: prod\n      });\n    } else if (e.value === 0) {\n      if (this.state.results !== null) {\n        prod = this.state.results;\n        prod.forEach(element => {\n          if (element.priceDad !== undefined) {\n            element.newPrice = element.priceDad;\n          } else {\n            element.newPrice = element.basePrice;\n          }\n        });\n        this.setState({\n          results: prod\n        });\n      }\n    }\n  }\n  /* Ricavo immagine prodotto mediante chiamata con id prodotto */\n  imageBodyTemplate(rowData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"immaginiProdTab\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: baseProxy + 'asset/prodotti/' + rowData.idProduct + '.jpg',\n        onError: e => e.target.src = Immagine,\n        alt: rowData.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 13\n    }, this);\n  }\n  //Chiusura dialogo eliminazione senza azioni\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.idProduct !== this.state.result.idProduct);\n    this.setState({\n      results,\n      results4: results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    var prodotti = [];\n    results.forEach(element => {\n      if (element.newPrice !== undefined) {\n        prodotti.push({\n          id: element.idProduct,\n          price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\"))\n        });\n      } else {\n        prodotti.push({\n          id: element.idProduct,\n          price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\"))\n        });\n      }\n    });\n    var completa = {\n      products: prodotti\n    };\n    let url = 'pricelist/?id=' + this.state.idPriceList;\n    await APIRequest('PUT', url, completa).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: 'Prodotto eliminato con successo',\n        life: 3000\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile eliminare il prodotto selezionato. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  /* Icona per eliminazione prodotto */\n  actionBodyTemplate(rowData) {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".p-button-rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-rounded\",\n        \"data-pr-tooltip\": \"Elimina prodotto dal listino\",\n        \"data-pr-position\": \"top\",\n        onClick: () => this.confirmDeleteResult(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 13\n    }, this);\n  }\n  //Apertura dialogo aggiunta\n  aggiungiProdInList() {\n    if (this.state.selectedPDV !== null) {\n      var find = this.state.results4.find(el => el.newPrice !== undefined);\n      var datiComodo = {\n        idRetailer: this.state.selectedPDV,\n        idPriceList: this.state.idPriceList\n      };\n      if (find === undefined) {\n        localStorage.setItem(\"datiComodo\", JSON.stringify(datiComodo));\n        this.setState({\n          resultDialog: true\n        });\n      } else {\n        confirmDialog({\n          message: 'Le modifiche effettuate sui prezzi di vendita andranno perdute continuare?',\n          header: 'Attenzione',\n          icon: 'pi pi-exclamation-triangle',\n          acceptLabel: \"Si\",\n          rejectLabel: \"No\",\n          accept: () => this.conferma(datiComodo),\n          reject: null\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'warn',\n        summary: 'Attenzione',\n        detail: 'Selezionare un punto vendita per poter aggiungere prodotti al listino',\n        life: 3000\n      });\n    }\n  }\n  conferma(datiComodo) {\n    localStorage.setItem(\"datiComodo\", JSON.stringify(datiComodo));\n    this.setState({\n      resultDialog: true\n    });\n  }\n  //Chiusura dialogo aggiunta\n  async hideaggiungiProdInList() {\n    this.setState({\n      resultDialog: false\n    });\n    /* Ripeto la chiamata per visualizzare i prodotti appena inseriti */\n    var url = 'pricelistretailer/?idRetailer=' + this.state.selectedPDV;\n    await APIRequest('GET', url).then(res => {\n      if (res.data !== '') {\n        var prodotti = [];\n        res.data.idPriceList2.priceListProducts.forEach(element => {\n          var x = {\n            idProduct: element.idProduct2.id,\n            externalCode: element.idProduct2.externalCode,\n            description: element.idProduct2.description,\n            price: element.price,\n            brand: element.idProduct2.brand,\n            subfamily: element.idProduct2.subfamily,\n            group: element.idProduct2.group,\n            family: element.idProduct2.family\n          };\n          prodotti.push(x);\n        });\n        this.setState({\n          results: prodotti,\n          results4: prodotti,\n          idPriceList: res.data.idPriceList\n        });\n      }\n    }).catch(e => {\n      console.log(e);\n    });\n    this.items = [{\n      label: Costanti.Categorie,\n      icon: 'pi pi-fw pi-file',\n      items: []\n    }];\n    this.defineSort();\n    this.findPrice();\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  reset() {\n    this.setState({\n      results: this.state.results4,\n      search2: ''\n    });\n  }\n  /* Reselt filtro categorie */\n  resetDesc() {\n    this.setState({\n      results: this.state.results4,\n      search2: ''\n    });\n  }\n  confirmDeleteSelected() {\n    this.setState({\n      deleteProductsDialog: true\n    });\n  }\n  hideDeleteProductsDialog() {\n    this.setState({\n      deleteProductsDialog: false\n    });\n  }\n  async deleteSelectedProducts() {\n    let results = this.state.results.filter(val => !this.state.selectedProducts.includes(val));\n    this.setState({\n      results,\n      deleteProductsDialog: false,\n      selectedProducts: null\n    });\n    var prodotti = [];\n    results.forEach(element => {\n      if (element.newPrice !== undefined) {\n        prodotti.push({\n          id: element.idProduct,\n          price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\"))\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      } else {\n        prodotti.push({\n          id: element.idProduct,\n          price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\"))\n        });\n      }\n    });\n    var completa = {\n      products: prodotti\n    };\n    let url = 'pricelist/?id=' + this.state.idPriceList;\n    await APIRequest('PUT', url, completa).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: 'success',\n        summary: 'Ottimo !',\n        detail: 'Prodotti eliminati con successo',\n        life: 3000\n      });\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"Non \\xE8 stato possibile eliminare i prodotti selezionati. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  leftToolbarTemplate() {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mx-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex col-md-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row rincaroPrezzi\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 d-flex flex-column flex-sm-row align-items-center justify-content-center px-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"mb-2 mb-sm-0 mr-0 mr-sm-3\",\n                htmlFor: \"minmax-buttons\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.PDV\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 103\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"mr-0 mr-md-4\",\n                value: this.state.selectedPDV,\n                options: this.PDV,\n                onChange: this.onPDVChange,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona punto vendita\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 983,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 982,\n      columnNumber: 13\n    }, this);\n  }\n  rightToolbarTemplate() {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 d-flex flex-column flex-sm-row align-items-center justify-content-center px-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"mb-2 mb-sm-0 mr-0 mr-sm-3\",\n          htmlFor: \"minmax-buttons\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: Costanti.RincPrezz\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 91\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n          inputId: \"minmax-buttons\",\n          className: \"cstm-input-number\",\n          suffix: \"%\",\n          min: 0,\n          max: 100,\n          value: this.state.value,\n          onValueChange: e => this.moltiplicaPrezzi(e),\n          mode: \"decimal\",\n          showButtons: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        id: \"DeleteMultiple\",\n        className: \"p-button-danger d-none\",\n        onClick: this.confirmDeleteSelected,\n        disabled: !this.state.selectedProducts || !this.state.selectedProducts.length,\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-trash mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 197\n        }, this), Costanti.DelSel]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1003,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 998,\n      columnNumber: 13\n    }, this);\n  }\n  closeSelectBefore() {\n    if (this.state.selectedPDV !== null) {\n      this.setState({\n        resultDialog2: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog3: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  render() {\n    const items = [{\n      label: Costanti.AggProdList,\n      icon: 'pi pi-plus-circle',\n      command: () => {\n        this.aggiungiProdInList();\n      }\n    }];\n    //Dichiarazione header del componente\n    const header = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-rows\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header row flex-row flex-md-row-reverse flex-lg-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6 mb-3 mb-sm-0\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-input-icon-left d-block mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12 col-sm-12 col-md-12 px-0 pb-2 pb-sm-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inputSearchbar mktplaceSearch\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-search mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Effettua una ricerca...\",\n                  value: this.state.search,\n                  onChange: this.handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"csv col-12 col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btns-actions d-flex justify-content-center justify-content-md-end pb-3 pb-md-0 w-auto\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"mr-2\",\n              onClick: this.openFilter,\n              tooltip: \"Filtri\",\n              tooltipOptions: {\n                position: 'top'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n                className: \"mr-2\",\n                name: \"filter-outline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 135\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(SplitButton, {\n              label: \"Azioni\",\n              icon: \"pi pi-cog\",\n              model: items,\n              className: \"splitButtonGen mr-2 mb-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(ScaricaCSVProva, {\n              label: 'esportaCSV',\n              results: this.state.results\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1043,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dell'aggiunta \n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.hideaggiungiProdInList,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1075,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1074,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1080,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1079,\n      columnNumber: 13\n    }, this);\n    //Elementi di conferma o annullamento del dialogo di cancellazione\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1089,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1087,\n      columnNumber: 13\n    }, this);\n    const deleteProductsDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteProductsDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1094,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        label: \"Si\",\n        icon: \"pi pi-check\",\n        className: \"p-button-text\",\n        onClick: this.deleteSelectedProducts\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1093,\n      columnNumber: 13\n    }, this);\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search2 !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-crud-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1107,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.GestioneListiniPDV\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1111,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1110,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card editable-prices-table dtRefresh border-0\",\n        children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n          left: this.leftToolbarTemplate,\n          right: this.rightToolbarTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1114,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          selection: this.state.selectedProducts,\n          onSelectionChange: e => this.setState({\n            selectedProducts: e.value\n          }),\n          dataKey: \"idProduct\",\n          paginator: true,\n          rows: 10,\n          rowsPerPageOptions: [10, 20, 50],\n          style: {\n            overflow: 'auto'\n          },\n          globalFilter: this.state.globalFilter,\n          editMode: \"cell\",\n          className: \"editable-cells-table\",\n          header: header,\n          autoLayout: \"true\",\n          csvSeparator: \";\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            selectionMode: \"multiple\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"image\",\n            body: this.imageBodyTemplate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"externalCode\",\n            header: Costanti.exCode,\n            body: this.externalCodeBodyTemplate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"description\",\n            header: Costanti.Nome,\n            body: this.productDescriptionBodyTemplate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"priceDad\",\n            header: Costanti.PrezAcq,\n            body: this.priceDadBodyTemplate,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"price\",\n            header: Costanti.PrezVen,\n            body: this.priceBodyTemplate2,\n            sortable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1126,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            header: Costanti.PrezVenImp,\n            body: this.priceBodyTemplate,\n            editor: props => this.priceEditor('products', props)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1127,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            body: this.actionBodyTemplate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1128,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1116,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1113,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        id: \"footerActions\",\n        className: \"d-flex justify-content-center flex-row align-items-center sticky-bottom py-3 border-top\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          id: \"invia\",\n          className: \"float-right ionicon my-0 mr-3 px-4\",\n          onClick: this.Invia,\n          children: Costanti.salva\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.AggProdList,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hideaggiungiProdInList,\n        children: /*#__PURE__*/_jsxDEV(AggProdInList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1135,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: '2rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1141,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteProd, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.description, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1142,\n              columnNumber: 78\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1142,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1140,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteProductsDialog,\n        header: Costanti.ElProds,\n        modal: true,\n        footer: deleteProductsDialogFooter,\n        onHide: this.hideDeleteProductsDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle mr-3\",\n            style: {\n              fontSize: '2rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteProds, \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1148,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter2,\n        children: [this.state.viewJoyride && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un punto vendita \",\n          target: \".selPDV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1153,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 46\n            }, this), Costanti.PDV]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1156,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1157,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selPDV\",\n            value: this.state.selectedPDV,\n            options: this.PDV,\n            onChange: this.onPDVChange,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona punto vendita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1151,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog3,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1163,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1164,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1164,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1168,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1168,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1169,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1167,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1171,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(PanelMenu, {\n          className: \"panelMenuClass mb-2\",\n          model: this.items\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1172,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1161,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1105,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default AssociaListiniPV;", "map": {"version": 3, "names": ["React", "Component", "Nav", "<PERSON><PERSON><PERSON><PERSON>", "ScaricaCSVProva", "APIRequest", "baseProxy", "confirmDialog", "<PERSON><PERSON><PERSON>", "Dialog", "AggProdInList", "<PERSON><PERSON>", "<PERSON><PERSON>", "Toast", "DataTable", "Column", "InputNumber", "Dropdown", "Sidebar", "JoyrideGen", "<PERSON><PERSON><PERSON>", "PanelMenu", "SplitButton", "jsxDEV", "_jsxDEV", "AssociaListiniPV", "constructor", "props", "emptyResult", "idProduct", "description", "address", "pIva", "email", "<PERSON><PERSON><PERSON><PERSON>", "createAt", "updateAt", "onPDVChange", "e", "setState", "selectedPDV", "value", "window", "sessionStorage", "setItem", "length", "results", "state", "results2", "results4", "idPriceList", "classe", "url", "then", "res", "data", "prodotti", "idPriceList2", "priceListProducts", "for<PERSON>ach", "element", "x", "idProduct2", "id", "externalCode", "price", "brand", "subfamily", "group", "family", "push", "message", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "createPricelist", "reject", "catch", "console", "log", "items", "label", "Categorie", "defineSort", "findPrice", "prod", "i", "basePrice", "j", "priceDad", "productDescriptionBodyTemplate", "Fragment", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "externalCodeBodyTemplate", "familyBodyTemplate", "formatBodyTemplate", "format", "groupBodyTemplate", "ivaBodyTemplate", "iva", "nationalityBodyTemplate", "nationality", "regionBodyTemplate", "region", "subfamilyBodyTemplate", "subgroupBodyTemplate", "subgroup", "orderdataBodyTemplate", "undefined", "convert", "date<PERSON><PERSON><PERSON>", "split", "map", "datetime", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "second", "Date", "join", "output", "validFrom", "orderdataBodyTemplate2", "validTo", "descriptionBodyTemplate", "priceBodyTemplate2", "NumberFormat", "style", "currency", "priceDadBodyTemplate", "Invia", "newPrice", "parseFloat", "replace", "completa", "products", "pdvs", "PDVs", "find", "el", "_find$idRegistry$user", "body", "idUserSender", "JSON", "parse", "localStorage", "getItem", "idUserReciver", "idRegistry", "users", "toast", "show", "severity", "summary", "detail", "life", "setTimeout", "location", "reload", "_e$response", "_e$response2", "concat", "response", "results3", "globalFilter", "resultDialog", "resultDialog2", "resultDialog3", "result", "deleteResultDialog", "deleteProductsDialog", "selectedProducts", "loading", "value1", "value2", "value3", "search", "search2", "viewJoyride", "sottoCategorie", "handleChange", "target", "risultato", "trim", "toLowerCase", "desc", "filter", "match", "exCode", "item", "filterProd", "finRes", "dad", "subf", "gruppo", "brandPresent", "items2", "PDV", "bind", "imageBodyTemplate", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "actionBodyTemplate", "aggiungiProdInList", "hideaggiungiProdInList", "leftToolbarTemplate", "rightToolbarTemplate", "confirmDeleteSelected", "hideDeleteProductsDialog", "deleteSelectedProducts", "reset", "resetDesc", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "_e$response3", "_e$response4", "entry", "name", "firstName", "_e$response5", "_e$response6", "pdv", "componentDidUpdate", "DelBtn", "document", "getElementById", "hasAttribute", "classList", "contains", "footerActions", "querySelector", "parentNode", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "remove", "add", "mix", "Set", "sort", "elementnull", "command", "includes", "idRetailer", "_e$response7", "_e$response8", "onEditorValueChange", "productKey", "updatedProducts", "rowIndex", "priceEditor", "onValueChange", "mode", "locale", "priceBodyTemplate", "rowData", "<PERSON>zzi", "moltiplicaPrezzi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "src", "onError", "alt", "val", "_e$response9", "_e$response0", "onClick", "datiComodo", "stringify", "conferma", "_e$response1", "_e$response10", "htmlFor", "options", "onChange", "optionLabel", "placeholder", "RincPrezz", "inputId", "suffix", "min", "max", "showButtons", "disabled", "DelSel", "render", "AggProdList", "type", "tooltip", "tooltipOptions", "position", "model", "resultD<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "resultDialogFooter2", "deleteResultDialogFooter", "Si", "deleteProductsDialogFooter", "filterDnone", "ref", "GestioneListiniPDV", "left", "right", "dt", "selection", "onSelectionChange", "dataKey", "paginator", "rows", "rowsPerPageOptions", "overflow", "editMode", "autoLayout", "csvSeparator", "selectionMode", "field", "Nome", "PrezAcq", "sortable", "PrezVen", "PrezVenImp", "editor", "salva", "visible", "modal", "footer", "onHide", "Conferma", "fontSize", "ResDeleteProd", "ElProds", "ResDeleteProds", "Primadiproseguire", "title", "content", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/associaListinoPV.jsx"], "sourcesContent": ["/* /**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AssociaListini - operazioni sull'associazione listini e modifica prezzi per i punti vendita (manager-price)\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from \"../components/navigation/Nav\";\nimport Immagine from '../img/mktplaceholder.jpg';\nimport ScaricaCSVProva from '../common/distributore/aggiunta file/scaricaCSVProva';\nimport { APIRequest, baseProxy } from '../components/generalizzazioni/apireq';\nimport { confirmDialog } from 'primereact/confirmdialog';\nimport { Tooltip } from 'primereact/tooltip';\nimport { Dialog } from 'primereact/dialog';\nimport { AggProdInList } from './aggiungiProdInListinoPDV';\nimport { Button } from 'primereact/button';\nimport { Costanti } from '../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport { DataTable } from 'primereact/datatable';\nimport { Column } from 'primereact/column';\nimport { InputNumber } from 'primereact/inputnumber';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Sidebar } from 'primereact/sidebar';\nimport { JoyrideGen } from '../components/footer/joyride';\nimport { Toolbar } from 'primereact/toolbar';\nimport { PanelMenu } from 'primereact/panelmenu';\nimport { SplitButton } from 'primereact/splitbutton';\nimport '../css/modale.css';\n\nclass AssociaListiniPV extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        idProduct: null,\n        description: '',\n        address: '',\n        pIva: '',\n        email: '',\n        isValid: '',\n        createAt: '',\n        updateAt: ''\n    };\n    constructor(props) {\n        super(props);\n        //Dichiarazione variabili di scena\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            PDVs: null,\n            selectedPDV: null,\n            globalFilter: null,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            result: this.emptyResult,\n            deleteResultDialog: false,\n            deleteProductsDialog: false,\n            selectedProducts: null,\n            loading: true,\n            value: 0,\n            idPriceList: 0,\n            value1: '',\n            value2: '',\n            value3: '',\n            search: '',\n            search2: '',\n            classe: '',\n            viewJoyride: false,\n            family: [],\n            sottoCategorie: [],\n            brand: [],\n            group: []\n        };\n        /* Ricerca per nome prodotto o codice esterno */\n        this.handleChange = e => {\n            this.setState({\n                search: e.target.value\n            });\n            var description = [];\n            var externalCode = [];\n            var risultato = []\n            var data = this.state.results4;\n            var search = e.target.value.trim().toLowerCase();\n            if (search.length > 0) {\n                data.forEach(element => {\n                    description.push(element.description)\n                    externalCode.push(element.externalCode)\n                })\n                var desc = description.filter(function (i) {\n                    return i.toLowerCase().match(search);\n                });\n                var exCode = externalCode.filter(function (i) {\n                    return i.toLowerCase().match(search);\n                });\n                if (desc.length > 0) {\n                    desc.forEach(item => {\n                        risultato.push(data.find(element => element.description === item));\n                    })\n                } if (exCode.length > 0) {\n                    exCode.forEach(item => {\n                        risultato.push(data.find(element => element.externalCode === item));\n                    })\n                }\n                if (risultato.length > 0) {\n                    this.setState({\n                        results: risultato\n                    })\n                }\n                else {\n                    this.setState({\n                        results: []\n                    })\n                }\n            } else {\n                this.setState({\n                    results: this.state.results4\n                })\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterProd = e => {\n            this.setState({\n                search2: e.item.label\n            });\n            var subfamily = [];\n            /* var family = []; */\n            var finRes = [];\n            var risultato = [];\n            var brand = [];\n            var group = [];\n            var data = this.state.results4;\n            var filter = ''\n            if (e.item.label === 'ALTRO') {\n                filter = null\n            } else {\n                filter = e.item.label.trim().toLowerCase();\n            }\n            if (filter === null || filter.length > 0) {\n                data.forEach(element => {\n                    if (element.subfamily !== null && element.subfamily !== '') {\n                        subfamily.push(element.subfamily)\n                    } else {\n                        if (e.item.value === 'subfamily') {\n                            finRes = data.filter(element => element.subfamily === null || element.subfamily === '');\n                        }\n                    } if (element.group !== null && element.group !== '') {\n                        group.push(element.group)\n                    } else {\n                        if (e.item.value === 'group') {\n                            finRes = data.filter(element => element.subfamily === e.item.dad && (element.group === null || element.group === ''));\n                        }\n                    }\n                    /*  if (element.family !== null && element.family !== '') {\n                         family.push(element.family)\n                     } else {\n                         risultato = data.filter(element => element.family === null || element.family === '');\n                     } */\n                    if (element.brand !== null && element.brand !== '') {\n                        brand.push(element.brand)\n                    } else {\n                        if (e.item.value === 'brand') {\n                            finRes = data.filter(element => element.brand === null || element.brand === '');\n                        }\n                    }\n                })\n                var subf = subfamily.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                var gruppo = group.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                /*  var fam = family.filter(function (i) {\n                     return i.toLowerCase().match(filter);\n                 }); */\n                var brandPresent = brand.filter(function (i) {\n                    return i.toLowerCase().match(filter);\n                });\n                if (subf.length > 0) {\n                    risultato = data.filter(element => element.subfamily === subf[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                }\n                if (gruppo.length > 0) {\n                    risultato = data.filter(element => element.group === gruppo[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                } /* if (fam.length > 0) {\n                    risultato = data.filter(element => element.family === fam[0]);\n                } */ if (brandPresent.length > 0) {\n                    risultato = data.filter(element => element.brand === brandPresent[0]);\n                    risultato.forEach(el => {\n                        finRes.push(el)\n                    })\n                } if (finRes.length > 0) {\n                    this.setState({\n                        results: finRes\n                    })\n                }\n                else {\n                    this.setState({\n                        results: []\n                    })\n                }\n            } else {\n                this.setState({\n                    results: this.state.results4\n                })\n            }\n        };\n        this.items = [{\n            label: Costanti.Categorie,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.items2 = [{\n            label: \"Brand\",\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.PDV = []\n        //Dichiarazione funzioni e metodi\n        this.descriptionBodyTemplate = this.descriptionBodyTemplate.bind(this);\n        this.productDescriptionBodyTemplate = this.productDescriptionBodyTemplate.bind(this);\n        this.externalCodeBodyTemplate = this.externalCodeBodyTemplate.bind(this);\n        this.familyBodyTemplate = this.familyBodyTemplate.bind(this);\n        this.formatBodyTemplate = this.formatBodyTemplate.bind(this);\n        this.groupBodyTemplate = this.groupBodyTemplate.bind(this);\n        this.ivaBodyTemplate = this.ivaBodyTemplate.bind(this);\n        this.nationalityBodyTemplate = this.nationalityBodyTemplate.bind(this);\n        this.regionBodyTemplate = this.regionBodyTemplate.bind(this);\n        this.subfamilyBodyTemplate = this.subfamilyBodyTemplate.bind(this);\n        this.subgroupBodyTemplate = this.subgroupBodyTemplate.bind(this);\n        this.imageBodyTemplate = this.imageBodyTemplate.bind(this);\n        this.createPricelist = this.createPricelist.bind(this);\n        this.onPDVChange = this.onPDVChange.bind(this);\n        this.findPrice = this.findPrice.bind(this);\n        this.priceBodyTemplate2 = this.priceBodyTemplate2.bind(this);\n        this.priceDadBodyTemplate = this.priceDadBodyTemplate.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.actionBodyTemplate = this.actionBodyTemplate.bind(this);\n        this.aggiungiProdInList = this.aggiungiProdInList.bind(this);\n        this.hideaggiungiProdInList = this.hideaggiungiProdInList.bind(this);\n        this.leftToolbarTemplate = this.leftToolbarTemplate.bind(this);\n        this.rightToolbarTemplate = this.rightToolbarTemplate.bind(this);\n        this.confirmDeleteSelected = this.confirmDeleteSelected.bind(this);\n        this.hideDeleteProductsDialog = this.hideDeleteProductsDialog.bind(this);\n        this.deleteSelectedProducts = this.deleteSelectedProducts.bind(this);\n        this.defineSort = this.defineSort.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n\n    }\n    //Chiamata axios con metodo componentDidMount per far si che sia effettuata una sola volta\n    async componentDidMount() {\n        /* Reperisco listino affiliato */\n        await APIRequest('GET', 'pricelistaffiliate/')\n            .then(res => {\n                this.setState({\n                    results2: res.data[0].idPriceList2.priceListProducts,\n                    results3: res.data[0].idPriceList2,\n                    loading: false,\n                })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile visualizzare il suo listino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n        /* Mappo i punti vendita per nome e id per visualizzarli nel dropdown */\n        await APIRequest('GET', 'retailers/')\n            .then(res => {\n                for (var entry of res.data) {\n                    this.PDV.push({\n                        name: entry.idRegistry.firstName,\n                        value: entry.id\n                    })\n                }\n                this.setState({ PDVs: res.data })\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile visualizzare i suoi punti vendita. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n        var pdv = JSON.parse(window.sessionStorage.getItem(\"PDV\"))\n        if (pdv !== null && pdv !== 0) {\n            this.setState({\n                selectedPDV: pdv\n            })\n            if (pdv.length > 1) {\n                this.setState({\n                    results: this.state.results2,\n                    results4: this.state.results2,\n                    idPriceList: this.state.results2.idPriceList,\n                    classe: 'd-none'\n                })\n            } else if (pdv.length !== 0) {\n                var url = 'pricelistretailer/?idRetailer=' + pdv;\n                await APIRequest('GET', url)\n                    .then(res => {\n                        if (res.data !== '') {\n                            var prodotti = []\n                            res.data.idPriceList2.priceListProducts.forEach(element => {\n                                var x = {\n                                    idProduct: element.idProduct2.id,\n                                    externalCode: element.idProduct2.externalCode,\n                                    description: element.idProduct2.description,\n                                    price: element.price,\n                                    brand: element.idProduct2.brand,\n                                    subfamily: element.idProduct2.subfamily,\n                                    group: element.idProduct2.group,\n                                    family: element.idProduct2.family\n                                }\n                                prodotti.push(x)\n                            })\n                            this.setState({\n                                results: prodotti,\n                                idPriceList: res.data.idPriceList\n                            })\n                        } else {\n                            /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n                            return confirmDialog({\n                                message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n                                header: 'Siamo spiacenti',\n                                icon: 'pi pi-exclamation-triangle',\n                                acceptLabel: \"Si\",\n                                rejectLabel: \"No\",\n                                accept: () => this.createPricelist(pdv),\n                                reject: null\n                            });\n                        }\n                    }).catch((e) => {\n                        console.log(e)\n                        /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n                        confirmDialog({\n                            message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n                            header: 'Siamo spiacenti',\n                            icon: 'pi pi-exclamation-triangle',\n                            acceptLabel: \"Si\",\n                            rejectLabel: \"No\",\n                            accept: () => this.createPricelist(e),\n                            reject: null\n                        });\n                    })\n            }\n            this.defineSort();\n            this.findPrice()\n        } else {\n            this.setState({ resultDialog2: true, viewJoyride: true })\n        }\n    }\n    componentDidUpdate() {\n        var DelBtn = document.getElementById(\"DeleteMultiple\");\n        if (!DelBtn.hasAttribute(\"disabled\")) {\n            if (DelBtn.classList.contains(\"moved-in-footer\")) {\n                // Nothing to do right now\n            } else {\n                var footerActions = document.querySelector(\"#invia\");\n                footerActions.parentNode.appendChild(DelBtn, footerActions.nextSibling)\n            }\n            DelBtn.classList.remove(\"d-none\");\n            DelBtn.classList.add(\"deleteSelected\", \"moved-in-footer\", \"fade-in-bottom\");\n        }\n    }\n    /* Seleziono il punto vendita e controllo che abbia un listino associato */\n    onPDVChange = async (e) => {\n        this.setState({ selectedPDV: e.value });\n        window.sessionStorage.setItem(\"PDV\", e.value)\n        if (e.value.length > 1) {\n            this.setState({\n                results: this.state.results2,\n                results4: this.state.results2,\n                idPriceList: this.state.results2.idPriceList,\n                classe: 'd-none'\n            })\n        } else if (e.value.length !== 0) {\n            var url = 'pricelistretailer/?idRetailer=' + e.value;\n            await APIRequest('GET', url)\n                .then(res => {\n                    if (res.data !== '') {\n                        var prodotti = []\n                        res.data.idPriceList2.priceListProducts.forEach(element => {\n                            var x = {\n                                idProduct: element.idProduct2.id,\n                                externalCode: element.idProduct2.externalCode,\n                                description: element.idProduct2.description,\n                                price: element.price,\n                                brand: element.idProduct2.brand,\n                                subfamily: element.idProduct2.subfamily,\n                                group: element.idProduct2.group,\n                                family: element.idProduct2.family\n                            }\n                            prodotti.push(x)\n                        })\n                        this.setState({\n                            results: prodotti,\n                            idPriceList: res.data.idPriceList\n                        })\n                    } else {\n                        /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n                        return confirmDialog({\n                            message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n                            header: 'Siamo spiacenti',\n                            icon: 'pi pi-exclamation-triangle',\n                            acceptLabel: \"Si\",\n                            rejectLabel: \"No\",\n                            accept: () => this.createPricelist(e),\n                            reject: null\n                        });\n                    }\n                }).catch((e) => {\n                    console.log(e)\n                    /* Non trovo il listino e chiedo all'utente affiliato se vuole associare al punto vendita il suo listino */\n                    confirmDialog({\n                        message: 'Non è stato trovato nessun listino per il punto vendita selezionato vuole crearne uno?',\n                        header: 'Siamo spiacenti',\n                        icon: 'pi pi-exclamation-triangle',\n                        accept: () => this.createPricelist(e),\n                        reject: null\n                    });\n                })\n        }\n        this.items = [{\n            label: Costanti.Categorie,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.defineSort();\n        this.findPrice()\n    }\n    /* Definiamo le categorie per il filtraggio  */\n    defineSort() {\n        var family = []\n        var sottoCategorie = []\n        var brand = []\n        var group = []\n        var mix = []\n        if (this.state.results !== null) {\n            this.state.results.forEach(element => {\n                family.push(element.family)\n                sottoCategorie.push(element.subfamily)\n                brand.push(element.brand)\n                mix.push(element.subfamily + ' ' + element.group)\n                group.push(element.group)\n            })\n            family = [...new Set(family)].sort();\n            sottoCategorie = [...new Set(sottoCategorie)].sort();\n            group = [...new Set(group)].sort();\n            mix = [...new Set(mix)];\n            brand = [...new Set(brand)].sort();\n            var elementnull = []\n            brand.forEach(element => {\n                if (element !== '') {\n                    this.items2[0].items.push({ label: element, command: (e) => { this.filterProd(e) } })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'brand', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items2[0].items.push(items)\n            })\n            elementnull = []\n            sottoCategorie.forEach(element => {\n                if (element !== null && element !== '') {\n                    this.items[0].items.push({ label: element, command: (e) => { this.filterProd(e) }, items: [] })\n                } else {\n                    elementnull.push({ label: \"ALTRO\", value: 'subfamily', command: (e) => { this.filterProd(e) } })\n                }\n            })\n            elementnull.forEach(items => {\n                this.items[0].items.push(items)\n            })\n            group.forEach(items => {\n                this.items[0].items.forEach(item => {\n                    if (item.label !== null) {\n                        if (mix.includes(item.label.concat(' ' + items))) {\n                            if (items !== null && items !== '') {\n                                item.items.push({ label: items, command: (e) => { this.filterProd(e) }, })\n                            } else {\n                                item.items.push({ label: \"ALTRO\", value: 'group', dad: item.label, command: (e) => { this.filterProd(e) } })\n                            }\n                        }\n                    }\n\n                })\n            })\n            this.setState({\n                family: family,\n                sottoCategorie: sottoCategorie,\n                brand: brand,\n                group: group\n            })\n        }\n    }\n    /* Creo il listino per il punto vendita */\n    async createPricelist(e) {\n        var completa = {\n            idRetailer: this.state.selectedPDV\n        }\n        await APIRequest('POST', 'pricelistretailer/', completa)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: 'Il listino è stato associato con successo', life: 3000 });\n            }).catch((e) => {\n                console.log(e);\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile visualizzare i listini per qesto punto vendita. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            })\n        this.onPDVChange(e)\n    }\n    /* Ricavo il prezzo dei prodotti dal listino dell'affiliato */\n    findPrice = () => {\n        var prod = []\n        if (this.state.results !== null) {\n            prod = this.state.results;\n            for (var i = 0; i < prod.length; i++) {\n                prod[i].basePrice = prod[i].price;\n                for (var j = 0; j < this.state.results2.length; j++) {\n                    if (prod[i].idProduct === this.state.results2[j].idProduct) {\n                        prod[i].priceDad = this.state.results2[j].price;\n                        break;\n                    }\n                }\n            }\n            return (\n                this.setState({\n                    results: prod,\n                    results4: prod\n                })\n            );\n        }\n    }\n    /* Ricavo il nome del prodotto */\n    productDescriptionBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.description}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il codice esterno del prodotto */\n    externalCodeBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.externalCode}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo la famiglia del prodotto */\n    familyBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.family}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il formato del prodotto */\n    formatBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.format}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il gruppo del prodotto */\n    groupBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.group}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo l'iva del prodotto */\n    ivaBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.iva}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo la nazione di provenienza del prodotto */\n    nationalityBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.nationality}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo la regione di provenienza del prodotto */\n    regionBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.region}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo la sottofamiglia del prodotto */\n    subfamilyBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.subfamily}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il sottogruppo del prodotto */\n    subgroupBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idProduct2.subgroup}</span>\n            </React.Fragment>\n        );\n\n    }\n    //Converto data e ora in formato IT\n    orderdataBodyTemplate = (results) => {\n        if (results !== undefined) {\n            // results.order_data\n            const convert = dateRange =>\n                dateRange\n                    .split(\" - \")\n                    .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).format(new Date(datetime)))\n                    .join(\" - \");\n            const output = convert(results.idPriceList2.validFrom);\n            return output;\n            // console.log(output);\n            // document.body.innerHTML = output;\n        } else {\n            return null;\n        }\n    }\n    //Converto data e ora in formato IT\n    orderdataBodyTemplate2 = (results) => {\n        if (results !== undefined) {\n            // results.order_data\n            const convert = dateRange =>\n                dateRange\n                    .split(\" - \")\n                    .map(datetime => new Intl.DateTimeFormat(\"it-IT\", { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }).format(new Date(datetime)))\n                    .join(\" - \");\n            const output = convert(results.idPriceList2.validTo);\n            return output;\n            // console.log(output);\n            // document.body.innerHTML = output;\n        } else {\n            return null;\n        }\n    }\n    /* Ricavo il nome del prodotto */\n    descriptionBodyTemplate = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{results.idPriceList2.description}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il prezzo del prodotto e lo formatto in Euro */\n    priceBodyTemplate2 = (results) => {\n        return (\n            <React.Fragment>\n                <span className=\"p-column-title\">{new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(results.price)}</span>\n            </React.Fragment>\n        );\n    }\n    /* Ricavo il prezzo del prodotto dal listino dell'affiliato */\n    priceDadBodyTemplate = (results) => {\n        if (results.priceDad) {\n            return (\n                <React.Fragment>\n                    <span className=\"p-column-title\">{new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(results.priceDad)}</span>\n                </React.Fragment>\n            );\n        } else {\n            return '0,00 €'\n        }\n    }\n    /* Effettuo modifica per il nuovo prezzo imposto dall'affiliato */\n    onEditorValueChange(productKey, props, value) {\n        if (value !== null) {\n            let updatedProducts = [...props.value];\n            updatedProducts[props.rowIndex]['newPrice']/* [props.field] */ = new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(value);\n            this.setState({ [`${productKey}`]: updatedProducts });\n        }\n    }\n    /* Componente InputNumber per modifica del prezzo per il punto vendita */\n    priceEditor(productKey, props) {\n        return <InputNumber onValueChange={(e) => this.onEditorValueChange(productKey, props, e.value)} mode=\"currency\" currency=\"EUR\" locale=\"it-IT\" />\n    }\n    /* Formatto il prezzo modificato in Euro */\n    priceBodyTemplate(rowData) {\n        if (rowData.newPrice !== undefined) {\n            var prezzi = 0;\n            if (typeof (rowData.newPrice) === 'string') {\n                prezzi = parseFloat(rowData.newPrice.replace(\"€\", \"\").replace(\",\", \".\"));\n            } else {\n                prezzi = rowData.newPrice\n            }\n            return <span className=\"priceAdded price-filled\">{new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(prezzi)}</span>\n        } else {\n            return null\n        }\n    }\n    /* Moltiplico i prezzi di tutti i prodotti con percentuale inserita dall'affiliato */\n    moltiplicaPrezzi(e) {\n        var prod = [];\n        if (e.value !== 0) {\n            prod = this.state.results\n            if (prod !== null) {\n                prod.forEach(element => {\n                    var prezzoMagg = 0;\n                    if (element.priceDad !== undefined) {\n                        prezzoMagg = parseFloat(element.priceDad)\n                        prezzoMagg += parseFloat(element.priceDad) * e.value / 100;\n                        element.newPrice = new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(prezzoMagg);\n                    } else {\n                        prezzoMagg = parseFloat(element.price)\n                        prezzoMagg += parseFloat(element.price) * e.value / 100;\n                        element.newPrice = new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(prezzoMagg);\n                    }\n                })\n            }\n            this.setState({ results: prod })\n        } else if (e.value === 0) {\n            if (this.state.results !== null) {\n                prod = this.state.results\n                prod.forEach(element => {\n                    if (element.priceDad !== undefined) {\n                        element.newPrice = element.priceDad;\n                    } else {\n                        element.newPrice = element.basePrice\n                    }\n                })\n                this.setState({ results: prod })\n            }\n        }\n    }\n    //Metodo di invio dati mediante chiamata axios per la creazione del punto vendita con i valori necessari alla creazione\n    Invia = async () => {\n        var prodotti = [];\n        /* Salvo id e prezzo di tutti i prodotti */\n        this.state.results4.forEach(element => {\n            if (element.newPrice !== undefined) {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\")) });\n            } else {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\")) });\n            }\n        })\n        var completa = {\n            products: prodotti\n        }\n        var url = 'pricelist/?id=' + this.state.idPriceList\n        await APIRequest('PUT', url, completa)\n            .then(async (res) => {\n                console.log(this.state.selectedPDV)\n                let pdvs = [...this.state.PDVs]\n                let find = pdvs.find(el => el.id === this.state.selectedPDV)\n                console.log(find)\n                if (find) {\n                    let body = {\n                        idUserSender: JSON.parse(localStorage.getItem('user')).id,\n                        idUserReciver: find.idRegistry.users[0]?.id,\n                        message: 'Il listino è stato aggiornato. Scopri le nuove offerte!'\n                    }\n                    await APIRequest('POST', 'notifyuser', body)\n                        .then(res => {\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: 'La modifica è avvenuta con successo e la notifica è stata inviata correttamente', life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            this.toast.show({ severity: 'warn', summary: 'Attenzione!', detail: 'La modifica è avvenuta con successo ma la notifica non è stata inviata correttamente', life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        })\n                } else {\n                    this.toast.show({ severity: 'warn', summary: 'Attenzione!', detail: 'La modifica è avvenuta con successo ma la notifica non è stata inviata correttamente', life: 3000 });\n                    setTimeout(() => {\n                        window.location.reload()\n                    }, 3000)\n                }\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `La modifica non è avvenuta con successo. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            })\n    }\n    /* Ricavo immagine prodotto mediante chiamata con id prodotto */\n    imageBodyTemplate(rowData) {\n        return (\n            <div className='immaginiProdTab'>\n                <img src={baseProxy + 'asset/prodotti/' + rowData.idProduct + '.jpg'} onError={(e) => e.target.src = Immagine} alt={rowData.description} />\n            </div>\n        )\n    }\n    //Chiusura dialogo eliminazione senza azioni\n    hideDeleteResultDialog() {\n        this.setState({ deleteResultDialog: false });\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(val => val.idProduct !== this.state.result.idProduct);\n        this.setState({\n            results,\n            results4: results,\n            deleteResultDialog: false,\n            result: this.emptyResult\n        });\n        var prodotti = [];\n        results.forEach(element => {\n            if (element.newPrice !== undefined) {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\")) });\n            } else {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\")) });\n            }\n        })\n        var completa = {\n            products: prodotti\n        }\n        let url = 'pricelist/?id=' + this.state.idPriceList;\n        await APIRequest('PUT', url, completa)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: 'Prodotto eliminato con successo', life: 3000 });\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile eliminare il prodotto selezionato. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    /* Icona per eliminazione prodotto */\n    actionBodyTemplate(rowData) {\n        return (\n            <React.Fragment>\n                <Tooltip target=\".p-button-rounded\" />\n                <Button icon=\"pi pi-trash\" className=\"p-button-rounded\" data-pr-tooltip=\"Elimina prodotto dal listino\" data-pr-position=\"top\" onClick={() => this.confirmDeleteResult(rowData)} />\n            </React.Fragment>\n        );\n    }\n    //Apertura dialogo aggiunta\n    aggiungiProdInList() {\n        if (this.state.selectedPDV !== null) {\n            var find = this.state.results4.find(el => el.newPrice !== undefined)\n            var datiComodo = {\n                idRetailer: this.state.selectedPDV,\n                idPriceList: this.state.idPriceList\n            }\n            if (find === undefined) {\n                localStorage.setItem(\"datiComodo\", JSON.stringify(datiComodo))\n                this.setState({\n                    resultDialog: true,\n                });\n            } else {\n                confirmDialog({\n                    message: 'Le modifiche effettuate sui prezzi di vendita andranno perdute continuare?',\n                    header: 'Attenzione',\n                    icon: 'pi pi-exclamation-triangle',\n                    acceptLabel: \"Si\",\n                    rejectLabel: \"No\",\n                    accept: () => this.conferma(datiComodo),\n                    reject: null\n                });\n            }\n        } else {\n            this.toast.show({ severity: 'warn', summary: 'Attenzione', detail: 'Selezionare un punto vendita per poter aggiungere prodotti al listino', life: 3000 });\n        }\n    }\n    conferma(datiComodo) {\n        localStorage.setItem(\"datiComodo\", JSON.stringify(datiComodo))\n        this.setState({\n            resultDialog: true,\n        });\n    }\n    //Chiusura dialogo aggiunta\n    async hideaggiungiProdInList() {\n        this.setState({\n            resultDialog: false\n        });\n        /* Ripeto la chiamata per visualizzare i prodotti appena inseriti */\n        var url = 'pricelistretailer/?idRetailer=' + this.state.selectedPDV;\n        await APIRequest('GET', url)\n            .then(res => {\n                if (res.data !== '') {\n                    var prodotti = []\n                    res.data.idPriceList2.priceListProducts.forEach(element => {\n                        var x = {\n                            idProduct: element.idProduct2.id,\n                            externalCode: element.idProduct2.externalCode,\n                            description: element.idProduct2.description,\n                            price: element.price,\n                            brand: element.idProduct2.brand,\n                            subfamily: element.idProduct2.subfamily,\n                            group: element.idProduct2.group,\n                            family: element.idProduct2.family\n                        }\n                        prodotti.push(x)\n                    })\n                    this.setState({\n                        results: prodotti,\n                        results4: prodotti,\n                        idPriceList: res.data.idPriceList\n                    })\n                }\n            }).catch((e) => {\n                console.log(e)\n            })\n        this.items = [{\n            label: Costanti.Categorie,\n            icon: 'pi pi-fw pi-file',\n            items: []\n        }]\n        this.defineSort();\n        this.findPrice()\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    reset() {\n        this.setState({\n            results: this.state.results4,\n            search2: ''\n        })\n    }\n    /* Reselt filtro categorie */\n    resetDesc() {\n        this.setState({\n            results: this.state.results4,\n            search2: ''\n        })\n    }\n    confirmDeleteSelected() {\n        this.setState({ deleteProductsDialog: true });\n    }\n    hideDeleteProductsDialog() {\n        this.setState({ deleteProductsDialog: false });\n    }\n    async deleteSelectedProducts() {\n        let results = this.state.results.filter(val => !this.state.selectedProducts.includes(val));\n        this.setState({\n            results,\n            deleteProductsDialog: false,\n            selectedProducts: null\n        });\n        var prodotti = [];\n        results.forEach(element => {\n            if (element.newPrice !== undefined) {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.newPrice.replace(\"€\", \"\").replace(\",\", \".\")) });\n                setTimeout(() => {\n                    window.location.reload()\n                }, 3000)\n            } else {\n                prodotti.push({ id: element.idProduct, price: parseFloat(element.price.replace(\"€\", \"\").replace(\",\", \".\")) });\n            }\n        })\n        var completa = {\n            products: prodotti\n        }\n        let url = 'pricelist/?id=' + this.state.idPriceList;\n        await APIRequest('PUT', url, completa)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({ severity: 'success', summary: 'Ottimo !', detail: 'Prodotti eliminati con successo', life: 3000 });\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile eliminare i prodotti selezionati. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n            })\n    }\n    leftToolbarTemplate() {\n        return (\n            <React.Fragment>\n                <div className=\"row mx-0\">\n                    <div className=\"d-flex col-md-12\">\n                        <div className=\"row rincaroPrezzi\">\n                            <div className='col-12 d-flex flex-column flex-sm-row align-items-center justify-content-center px-0'>\n                                <label className=\"mb-2 mb-sm-0 mr-0 mr-sm-3\" htmlFor=\"minmax-buttons\"><strong>{Costanti.PDV}</strong></label>\n                                <Dropdown className=\"mr-0 mr-md-4\" value={this.state.selectedPDV} options={this.PDV} onChange={this.onPDVChange} optionLabel=\"name\" placeholder=\"Seleziona punto vendita\" />\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        )\n    }\n    rightToolbarTemplate() {\n        return (\n            <React.Fragment>\n                <div className='col-12 d-flex flex-column flex-sm-row align-items-center justify-content-center px-0'>\n                    <label className=\"mb-2 mb-sm-0 mr-0 mr-sm-3\" htmlFor=\"minmax-buttons\"><strong>{Costanti.RincPrezz}</strong></label>\n                    <InputNumber inputId=\"minmax-buttons\" className=\"cstm-input-number\" suffix=\"%\" min={0} max={100} value={this.state.value} onValueChange={(e) => this.moltiplicaPrezzi(e)} mode=\"decimal\" showButtons />\n                </div>\n                <Button id=\"DeleteMultiple\" className=\"p-button-danger d-none\" onClick={this.confirmDeleteSelected} disabled={!this.state.selectedProducts || !this.state.selectedProducts.length} ><i className='pi pi-trash mr-2'></i>{Costanti.DelSel}</Button>\n            </React.Fragment>\n        )\n    }\n    closeSelectBefore() {\n        if (this.state.selectedPDV !== null) {\n            this.setState({\n                resultDialog2: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog3: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog3: false\n        })\n    }\n    render() {\n        const items = [\n            {\n                label: Costanti.AggProdList,\n                icon: 'pi pi-plus-circle',\n                command: () => {\n                    this.aggiungiProdInList()\n                }\n            },\n        ]\n        //Dichiarazione header del componente\n        const header = (\n            <div className=\"container-rows\">\n                <div className=\"table-header row flex-row flex-md-row-reverse flex-lg-row\">\n                    <div className=\"col-12 col-md-6 mb-3 mb-sm-0\">\n                        <span className=\"p-input-icon-left d-block mx-auto\">\n                            <div className=\"col-12 col-sm-12 col-md-12 px-0 pb-2 pb-sm-0\">\n                                <div className=\"inputSearchbar mktplaceSearch\">\n                                    <i className=\"pi pi-search mr-2\" />\n                                    <input\n                                        type=\"text\"\n                                        placeholder=\"Effettua una ricerca...\"\n                                        value={this.state.search}\n                                        onChange={this.handleChange}\n                                    />\n                                </div>\n                            </div>\n                        </span>\n                    </div>\n                    <div className=\"csv col-12 col-md-6\">\n                        <div className=\"btns-actions d-flex justify-content-center justify-content-md-end pb-3 pb-md-0 w-auto\">\n                            <Button className=\"mr-2\" onClick={this.openFilter} tooltip='Filtri' tooltipOptions={{ position: 'top' }} ><ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon></Button>\n                            {/* Bottone di aggiunta prodotti al listino con annesso metodo di apertura modale */}\n                            <SplitButton label=\"Azioni\" icon='pi pi-cog' model={items} className=\"splitButtonGen mr-2 mb-0\"></SplitButton>\n                            {/* <Button className=\"p-button ml-0 ml-sm-3 mr-2\" onClick={this.aggiungiProdInList} > {Costanti.AggProdList} </Button> */}\n                            <ScaricaCSVProva label={'esportaCSV'} results={this.state.results} />\n                        </div>\n                    </div>\n                </div>\n            </div>\n        );\n        //Elementi del footer nelle finestre di dialogo dell'aggiunta \n        const resultDialogFooter = (\n            <React.Fragment>\n                <Button className=\"p-button-text\" onClick={this.hideaggiungiProdInList} > {Costanti.Chiudi} </Button>\n            </React.Fragment>\n        );\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi di conferma o annullamento del dialogo di cancellazione\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button label=\"No\" icon=\"pi pi-times\" className=\"p-button-text\" onClick={this.hideDeleteResultDialog} />\n                <Button className=\"p-button-text\" onClick={this.deleteResult} > {Costanti.Si} </Button>\n            </React.Fragment>\n        );\n        const deleteProductsDialogFooter = (\n            <React.Fragment>\n                <Button label=\"No\" icon=\"pi pi-times\" className=\"p-button-text\" onClick={this.hideDeleteProductsDialog} />\n                <Button label=\"Si\" icon=\"pi pi-check\" className=\"p-button-text\" onClick={this.deleteSelectedProducts} />\n            </React.Fragment>\n        );\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search2 !== '') {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-crud-demo wrapper\" >\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                < Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header ed il menù di navigazione */}\n                < Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.GestioneListiniPDV}</h1>\n                </div>\n                <div className=\"card editable-prices-table dtRefresh border-0\">\n                    <Toolbar left={this.leftToolbarTemplate} right={this.rightToolbarTemplate} />\n                    {/* Componente primereact per la creazione della tabella */}\n                    <DataTable ref={(el) => this.dt = el} value={this.state.results} loading={this.state.loading}\n                        selection={this.state.selectedProducts} onSelectionChange={(e) => this.setState({ selectedProducts: e.value })}\n                        dataKey=\"idProduct\" paginator rows={10} rowsPerPageOptions={[10, 20, 50]} style={{ overflow: 'auto' }}\n                        globalFilter={this.state.globalFilter} editMode=\"cell\" className=\"editable-cells-table\"\n                        header={header} autoLayout='true' csvSeparator=\";\">\n                        <Column selectionMode=\"multiple\" ></Column>\n                        <Column field=\"image\" body={this.imageBodyTemplate}></Column>\n                        <Column field=\"externalCode\" header={Costanti.exCode} body={this.externalCodeBodyTemplate} />\n                        <Column field=\"description\" header={Costanti.Nome} body={this.productDescriptionBodyTemplate} />\n                        <Column field=\"priceDad\" header={Costanti.PrezAcq} body={this.priceDadBodyTemplate} sortable ></Column>\n                        <Column field=\"price\" header={Costanti.PrezVen} body={this.priceBodyTemplate2} sortable ></Column>\n                        <Column header={Costanti.PrezVenImp} body={this.priceBodyTemplate} editor={(props) => this.priceEditor('products', props)} ></Column>\n                        <Column body={this.actionBodyTemplate} ></Column>\n                    </DataTable>\n                </div>\n                <div id=\"footerActions\" className='d-flex justify-content-center flex-row align-items-center sticky-bottom py-3 border-top'>\n                    <Button id=\"invia\" className=\"float-right ionicon my-0 mr-3 px-4\" onClick={this.Invia}>{Costanti.salva}</Button>\n                </div>\n                {/* Struttura dialogo per la visualizzazione dei prodotti aggiungibili al listino */}\n                < Dialog visible={this.state.resultDialog} header={Costanti.AggProdList} modal className=\"p-fluid modalBox\" footer={resultDialogFooter} onHide={this.hideaggiungiProdInList} >\n                    <AggProdInList />\n                </Dialog >\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog visible={this.state.deleteResultDialog} header={Costanti.Conferma} modal footer={deleteResultDialogFooter} onHide={this.hideDeleteResultDialog} >\n                    <div className=\"confirmation-content\">\n                        <i className=\"pi pi-exclamation-triangle p-mr-3\" style={{ fontSize: '2rem' }} />\n                        {this.state.result && <span>{Costanti.ResDeleteProd} <b>{this.state.result.description}?</b></span>}\n                    </div>\n                </Dialog >\n                <Dialog visible={this.state.deleteProductsDialog} header={Costanti.ElProds} modal footer={deleteProductsDialogFooter} onHide={this.hideDeleteProductsDialog}>\n                    <div className=\"confirmation-content\">\n                        <i className=\"pi pi-exclamation-triangle mr-3\" style={{ fontSize: '2rem' }} />\n                        {this.state.result && <span>{Costanti.ResDeleteProds}?</span>}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog2} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter2}>\n                    {this.state.viewJoyride &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un punto vendita ' target='.selPDV' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-user mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.PDV}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selPDV\" value={this.state.selectedPDV} options={this.PDV} onChange={this.onPDVChange} optionLabel=\"name\" placeholder=\"Seleziona punto vendita\" />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog3} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <PanelMenu className=\"panelMenuClass mb-2\" model={this.items} />\n                </Sidebar>\n            </div >\n        );\n    }\n}\n\nexport default AssociaListiniPV;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,8BAA8B;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,sDAAsD;AAClF,SAASC,UAAU,EAAEC,SAAS,QAAQ,uCAAuC;AAC7E,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,gBAAgB,SAASxB,SAAS,CAAC;EAYrCyB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ;IAbJ;IAAA,KACAC,WAAW,GAAG;MACVC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACd,CAAC;IAwUD;IAAA,KACAC,WAAW,GAAG,MAAOC,CAAC,IAAK;MACvB,IAAI,CAACC,QAAQ,CAAC;QAAEC,WAAW,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MACvCC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,KAAK,EAAEN,CAAC,CAACG,KAAK,CAAC;MAC7C,IAAIH,CAAC,CAACG,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;QACpB,IAAI,CAACN,QAAQ,CAAC;UACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACC,QAAQ;UAC5BC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACC,QAAQ;UAC7BE,WAAW,EAAE,IAAI,CAACH,KAAK,CAACC,QAAQ,CAACE,WAAW;UAC5CC,MAAM,EAAE;QACZ,CAAC,CAAC;MACN,CAAC,MAAM,IAAIb,CAAC,CAACG,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;QAC7B,IAAIO,GAAG,GAAG,gCAAgC,GAAGd,CAAC,CAACG,KAAK;QACpD,MAAMpC,UAAU,CAAC,KAAK,EAAE+C,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;UACT,IAAIA,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;YACjB,IAAIC,QAAQ,GAAG,EAAE;YACjBF,GAAG,CAACC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACC,OAAO,CAACC,OAAO,IAAI;cACvD,IAAIC,CAAC,GAAG;gBACJhC,SAAS,EAAE+B,OAAO,CAACE,UAAU,CAACC,EAAE;gBAChCC,YAAY,EAAEJ,OAAO,CAACE,UAAU,CAACE,YAAY;gBAC7ClC,WAAW,EAAE8B,OAAO,CAACE,UAAU,CAAChC,WAAW;gBAC3CmC,KAAK,EAAEL,OAAO,CAACK,KAAK;gBACpBC,KAAK,EAAEN,OAAO,CAACE,UAAU,CAACI,KAAK;gBAC/BC,SAAS,EAAEP,OAAO,CAACE,UAAU,CAACK,SAAS;gBACvCC,KAAK,EAAER,OAAO,CAACE,UAAU,CAACM,KAAK;gBAC/BC,MAAM,EAAET,OAAO,CAACE,UAAU,CAACO;cAC/B,CAAC;cACDb,QAAQ,CAACc,IAAI,CAACT,CAAC,CAAC;YACpB,CAAC,CAAC;YACF,IAAI,CAACtB,QAAQ,CAAC;cACVO,OAAO,EAAEU,QAAQ;cACjBN,WAAW,EAAEI,GAAG,CAACC,IAAI,CAACL;YAC1B,CAAC,CAAC;UACN,CAAC,MAAM;YACH;YACA,OAAO3C,aAAa,CAAC;cACjBgE,OAAO,EAAE,wFAAwF;cACjGC,MAAM,EAAE,iBAAiB;cACzBC,IAAI,EAAE,4BAA4B;cAClCC,WAAW,EAAE,IAAI;cACjBC,WAAW,EAAE,IAAI;cACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAACvC,CAAC,CAAC;cACrCwC,MAAM,EAAE;YACZ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC,CAACC,KAAK,CAAEzC,CAAC,IAAK;UACZ0C,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;UACd;UACA/B,aAAa,CAAC;YACVgE,OAAO,EAAE,wFAAwF;YACjGC,MAAM,EAAE,iBAAiB;YACzBC,IAAI,EAAE,4BAA4B;YAClCG,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAACvC,CAAC,CAAC;YACrCwC,MAAM,EAAE;UACZ,CAAC,CAAC;QACN,CAAC,CAAC;MACV;MACA,IAAI,CAACI,KAAK,GAAG,CAAC;QACVC,KAAK,EAAEvE,QAAQ,CAACwE,SAAS;QACzBX,IAAI,EAAE,kBAAkB;QACxBS,KAAK,EAAE;MACX,CAAC,CAAC;MACF,IAAI,CAACG,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,SAAS,CAAC,CAAC;IACpB,CAAC;IAmFD;IAAA,KACAA,SAAS,GAAG,MAAM;MACd,IAAIC,IAAI,GAAG,EAAE;MACb,IAAI,IAAI,CAACxC,KAAK,CAACD,OAAO,KAAK,IAAI,EAAE;QAC7ByC,IAAI,GAAG,IAAI,CAACxC,KAAK,CAACD,OAAO;QACzB,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAAC1C,MAAM,EAAE2C,CAAC,EAAE,EAAE;UAClCD,IAAI,CAACC,CAAC,CAAC,CAACC,SAAS,GAAGF,IAAI,CAACC,CAAC,CAAC,CAACvB,KAAK;UACjC,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3C,KAAK,CAACC,QAAQ,CAACH,MAAM,EAAE6C,CAAC,EAAE,EAAE;YACjD,IAAIH,IAAI,CAACC,CAAC,CAAC,CAAC3D,SAAS,KAAK,IAAI,CAACkB,KAAK,CAACC,QAAQ,CAAC0C,CAAC,CAAC,CAAC7D,SAAS,EAAE;cACxD0D,IAAI,CAACC,CAAC,CAAC,CAACG,QAAQ,GAAG,IAAI,CAAC5C,KAAK,CAACC,QAAQ,CAAC0C,CAAC,CAAC,CAACzB,KAAK;cAC/C;YACJ;UACJ;QACJ;QACA,OACI,IAAI,CAAC1B,QAAQ,CAAC;UACVO,OAAO,EAAEyC,IAAI;UACbtC,QAAQ,EAAEsC;QACd,CAAC,CAAC;MAEV;IACJ,CAAC;IACD;IAAA,KACAK,8BAA8B,GAAI9C,OAAO,IAAK;MAC1C,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAAChB;QAAW;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAC,wBAAwB,GAAItD,OAAO,IAAK;MACpC,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACkB;QAAY;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAE,kBAAkB,GAAIvD,OAAO,IAAK;MAC9B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACO;QAAM;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAG,kBAAkB,GAAIxD,OAAO,IAAK;MAC9B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACyC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAK,iBAAiB,GAAI1D,OAAO,IAAK;MAC7B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACM;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAM,eAAe,GAAI3D,OAAO,IAAK;MAC3B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAAC4C;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAQ,uBAAuB,GAAI7D,OAAO,IAAK;MACnC,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAAC8C;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAEzB,CAAC;IACD;IAAA,KACAU,kBAAkB,GAAI/D,OAAO,IAAK;MAC9B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACgD;QAAM;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAEzB,CAAC;IACD;IAAA,KACAY,qBAAqB,GAAIjE,OAAO,IAAK;MACjC,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACK;QAAS;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAEzB,CAAC;IACD;IAAA,KACAa,oBAAoB,GAAIlE,OAAO,IAAK;MAChC,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACgB,UAAU,CAACmD;QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAGzB,CAAC;IACD;IAAA,KACAe,qBAAqB,GAAIpE,OAAO,IAAK;MACjC,IAAIA,OAAO,KAAKqE,SAAS,EAAE;QACvB;QACA,MAAMC,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAU,CAAC,CAAC,CAACzB,MAAM,CAAC,IAAI0B,IAAI,CAACT,QAAQ,CAAC,CAAC,CAAC,CAC1LU,IAAI,CAAC,KAAK,CAAC;QACpB,MAAMC,MAAM,GAAGf,OAAO,CAACtE,OAAO,CAACW,YAAY,CAAC2E,SAAS,CAAC;QACtD,OAAOD,MAAM;QACb;QACA;MACJ,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IAAA,KACAE,sBAAsB,GAAIvF,OAAO,IAAK;MAClC,IAAIA,OAAO,KAAKqE,SAAS,EAAE;QACvB;QACA,MAAMC,OAAO,GAAGC,SAAS,IACrBA,SAAS,CACJC,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAACC,QAAQ,IAAI,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;UAAEC,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAU,CAAC,CAAC,CAACzB,MAAM,CAAC,IAAI0B,IAAI,CAACT,QAAQ,CAAC,CAAC,CAAC,CAC1LU,IAAI,CAAC,KAAK,CAAC;QACpB,MAAMC,MAAM,GAAGf,OAAO,CAACtE,OAAO,CAACW,YAAY,CAAC6E,OAAO,CAAC;QACpD,OAAOH,MAAM;QACb;QACA;MACJ,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IAAA,KACAI,uBAAuB,GAAIzF,OAAO,IAAK;MACnC,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAEhD,OAAO,CAACW,YAAY,CAAC3B;QAAW;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAEzB,CAAC;IACD;IAAA,KACAqC,kBAAkB,GAAI1F,OAAO,IAAK;MAC9B,oBACItB,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;QAAAC,QAAA,eACXtE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAE,IAAI2B,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;YAAEC,KAAK,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAC,CAACpC,MAAM,CAACzD,OAAO,CAACmB,KAAK;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1H,CAAC;IAEzB,CAAC;IACD;IAAA,KACAyC,oBAAoB,GAAI9F,OAAO,IAAK;MAChC,IAAIA,OAAO,CAAC6C,QAAQ,EAAE;QAClB,oBACInE,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;UAAAC,QAAA,eACXtE,OAAA;YAAMuE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAE,IAAI2B,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACpC,MAAM,CAACzD,OAAO,CAAC6C,QAAQ;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7H,CAAC;MAEzB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IA6DD;IAAA,KACA0C,KAAK,GAAG,YAAY;MAChB,IAAIrF,QAAQ,GAAG,EAAE;MACjB;MACA,IAAI,CAACT,KAAK,CAACE,QAAQ,CAACU,OAAO,CAACC,OAAO,IAAI;QACnC,IAAIA,OAAO,CAACkF,QAAQ,KAAK3B,SAAS,EAAE;UAChC3D,QAAQ,CAACc,IAAI,CAAC;YAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;YAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACkF,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAAE,CAAC,CAAC;QACpH,CAAC,MAAM;UACHxF,QAAQ,CAACc,IAAI,CAAC;YAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;YAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACK,KAAK,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAAE,CAAC,CAAC;QACjH;MACJ,CAAC,CAAC;MACF,IAAIC,QAAQ,GAAG;QACXC,QAAQ,EAAE1F;MACd,CAAC;MACD,IAAIJ,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAACL,KAAK,CAACG,WAAW;MACnD,MAAM7C,UAAU,CAAC,KAAK,EAAE+C,GAAG,EAAE6F,QAAQ,CAAC,CACjC5F,IAAI,CAAC,MAAOC,GAAG,IAAK;QACjB0B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClC,KAAK,CAACP,WAAW,CAAC;QACnC,IAAI2G,IAAI,GAAG,CAAC,GAAG,IAAI,CAACpG,KAAK,CAACqG,IAAI,CAAC;QAC/B,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACvF,EAAE,KAAK,IAAI,CAAChB,KAAK,CAACP,WAAW,CAAC;QAC5DwC,OAAO,CAACC,GAAG,CAACoE,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UAAA,IAAAE,qBAAA;UACN,IAAIC,IAAI,GAAG;YACPC,YAAY,EAAEC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC9F,EAAE;YACzD+F,aAAa,GAAAP,qBAAA,GAAEF,IAAI,CAACU,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BxF,EAAE;YAC3CQ,OAAO,EAAE;UACb,CAAC;UACD,MAAMlE,UAAU,CAAC,MAAM,EAAE,YAAY,EAAEmJ,IAAI,CAAC,CACvCnG,IAAI,CAACC,GAAG,IAAI;YACT,IAAI,CAAC2G,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,iFAAiF;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;YAClKC,UAAU,CAAC,MAAM;cACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAC1F,KAAK,CAAEzC,CAAC,IAAK;YACZ,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,aAAa;cAAEC,MAAM,EAAE,sFAAsF;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;YACzKC,UAAU,CAAC,MAAM;cACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC;QACV,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,MAAM;YAAEC,OAAO,EAAE,aAAa;YAAEC,MAAM,EAAE,sFAAsF;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UACzKC,UAAU,CAAC,MAAM;YACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ;MACJ,CAAC,CAAC,CAAC1F,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAoI,WAAA,EAAAC,YAAA;QACZ3F,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;QACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,mEAAAO,MAAA,CAAgE,EAAAF,WAAA,GAAApI,CAAC,CAACuI,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAYnH,IAAI,MAAK4D,SAAS,IAAAwD,YAAA,GAAGrI,CAAC,CAACuI,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAYpH,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;UAAE+F,IAAI,EAAE;QAAK,CAAC,CAAC;QACrNC,UAAU,CAAC,MAAM;UACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV,CAAC;IA7uBG,IAAI,CAAC1H,KAAK,GAAG;MACTD,OAAO,EAAE,IAAI;MACbE,QAAQ,EAAE,IAAI;MACd8H,QAAQ,EAAE,IAAI;MACd7H,QAAQ,EAAE,IAAI;MACdmG,IAAI,EAAE,IAAI;MACV5G,WAAW,EAAE,IAAI;MACjBuI,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE,IAAI,CAACvJ,WAAW;MACxBwJ,kBAAkB,EAAE,KAAK;MACzBC,oBAAoB,EAAE,KAAK;MAC3BC,gBAAgB,EAAE,IAAI;MACtBC,OAAO,EAAE,IAAI;MACb9I,KAAK,EAAE,CAAC;MACRS,WAAW,EAAE,CAAC;MACdsI,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXzI,MAAM,EAAE,EAAE;MACV0I,WAAW,EAAE,KAAK;MAClBxH,MAAM,EAAE,EAAE;MACVyH,cAAc,EAAE,EAAE;MAClB5H,KAAK,EAAE,EAAE;MACTE,KAAK,EAAE;IACX,CAAC;IACD;IACA,IAAI,CAAC2H,YAAY,GAAGzJ,CAAC,IAAI;MACrB,IAAI,CAACC,QAAQ,CAAC;QACVoJ,MAAM,EAAErJ,CAAC,CAAC0J,MAAM,CAACvJ;MACrB,CAAC,CAAC;MACF,IAAIX,WAAW,GAAG,EAAE;MACpB,IAAIkC,YAAY,GAAG,EAAE;MACrB,IAAIiI,SAAS,GAAG,EAAE;MAClB,IAAI1I,IAAI,GAAG,IAAI,CAACR,KAAK,CAACE,QAAQ;MAC9B,IAAI0I,MAAM,GAAGrJ,CAAC,CAAC0J,MAAM,CAACvJ,KAAK,CAACyJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAChD,IAAIR,MAAM,CAAC9I,MAAM,GAAG,CAAC,EAAE;QACnBU,IAAI,CAACI,OAAO,CAACC,OAAO,IAAI;UACpB9B,WAAW,CAACwC,IAAI,CAACV,OAAO,CAAC9B,WAAW,CAAC;UACrCkC,YAAY,CAACM,IAAI,CAACV,OAAO,CAACI,YAAY,CAAC;QAC3C,CAAC,CAAC;QACF,IAAIoI,IAAI,GAAGtK,WAAW,CAACuK,MAAM,CAAC,UAAU7G,CAAC,EAAE;UACvC,OAAOA,CAAC,CAAC2G,WAAW,CAAC,CAAC,CAACG,KAAK,CAACX,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIY,MAAM,GAAGvI,YAAY,CAACqI,MAAM,CAAC,UAAU7G,CAAC,EAAE;UAC1C,OAAOA,CAAC,CAAC2G,WAAW,CAAC,CAAC,CAACG,KAAK,CAACX,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIS,IAAI,CAACvJ,MAAM,GAAG,CAAC,EAAE;UACjBuJ,IAAI,CAACzI,OAAO,CAAC6I,IAAI,IAAI;YACjBP,SAAS,CAAC3H,IAAI,CAACf,IAAI,CAAC8F,IAAI,CAACzF,OAAO,IAAIA,OAAO,CAAC9B,WAAW,KAAK0K,IAAI,CAAC,CAAC;UACtE,CAAC,CAAC;QACN;QAAE,IAAID,MAAM,CAAC1J,MAAM,GAAG,CAAC,EAAE;UACrB0J,MAAM,CAAC5I,OAAO,CAAC6I,IAAI,IAAI;YACnBP,SAAS,CAAC3H,IAAI,CAACf,IAAI,CAAC8F,IAAI,CAACzF,OAAO,IAAIA,OAAO,CAACI,YAAY,KAAKwI,IAAI,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;QACA,IAAIP,SAAS,CAACpJ,MAAM,GAAG,CAAC,EAAE;UACtB,IAAI,CAACN,QAAQ,CAAC;YACVO,OAAO,EAAEmJ;UACb,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAAC1J,QAAQ,CAAC;YACVO,OAAO,EAAE;UACb,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,CAACP,QAAQ,CAAC;UACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACE;QACxB,CAAC,CAAC;MACN;IACJ,CAAC;IACD;IACA,IAAI,CAACwJ,UAAU,GAAGnK,CAAC,IAAI;MACnB,IAAI,CAACC,QAAQ,CAAC;QACVqJ,OAAO,EAAEtJ,CAAC,CAACkK,IAAI,CAACrH;MACpB,CAAC,CAAC;MACF,IAAIhB,SAAS,GAAG,EAAE;MAClB;MACA,IAAIuI,MAAM,GAAG,EAAE;MACf,IAAIT,SAAS,GAAG,EAAE;MAClB,IAAI/H,KAAK,GAAG,EAAE;MACd,IAAIE,KAAK,GAAG,EAAE;MACd,IAAIb,IAAI,GAAG,IAAI,CAACR,KAAK,CAACE,QAAQ;MAC9B,IAAIoJ,MAAM,GAAG,EAAE;MACf,IAAI/J,CAAC,CAACkK,IAAI,CAACrH,KAAK,KAAK,OAAO,EAAE;QAC1BkH,MAAM,GAAG,IAAI;MACjB,CAAC,MAAM;QACHA,MAAM,GAAG/J,CAAC,CAACkK,IAAI,CAACrH,KAAK,CAAC+G,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9C;MACA,IAAIE,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACxJ,MAAM,GAAG,CAAC,EAAE;QACtCU,IAAI,CAACI,OAAO,CAACC,OAAO,IAAI;UACpB,IAAIA,OAAO,CAACO,SAAS,KAAK,IAAI,IAAIP,OAAO,CAACO,SAAS,KAAK,EAAE,EAAE;YACxDA,SAAS,CAACG,IAAI,CAACV,OAAO,CAACO,SAAS,CAAC;UACrC,CAAC,MAAM;YACH,IAAI7B,CAAC,CAACkK,IAAI,CAAC/J,KAAK,KAAK,WAAW,EAAE;cAC9BiK,MAAM,GAAGnJ,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACO,SAAS,KAAK,IAAI,IAAIP,OAAO,CAACO,SAAS,KAAK,EAAE,CAAC;YAC3F;UACJ;UAAE,IAAIP,OAAO,CAACQ,KAAK,KAAK,IAAI,IAAIR,OAAO,CAACQ,KAAK,KAAK,EAAE,EAAE;YAClDA,KAAK,CAACE,IAAI,CAACV,OAAO,CAACQ,KAAK,CAAC;UAC7B,CAAC,MAAM;YACH,IAAI9B,CAAC,CAACkK,IAAI,CAAC/J,KAAK,KAAK,OAAO,EAAE;cAC1BiK,MAAM,GAAGnJ,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACO,SAAS,KAAK7B,CAAC,CAACkK,IAAI,CAACG,GAAG,KAAK/I,OAAO,CAACQ,KAAK,KAAK,IAAI,IAAIR,OAAO,CAACQ,KAAK,KAAK,EAAE,CAAC,CAAC;YACzH;UACJ;UACA;AACpB;AACA;AACA;AACA;UACoB,IAAIR,OAAO,CAACM,KAAK,KAAK,IAAI,IAAIN,OAAO,CAACM,KAAK,KAAK,EAAE,EAAE;YAChDA,KAAK,CAACI,IAAI,CAACV,OAAO,CAACM,KAAK,CAAC;UAC7B,CAAC,MAAM;YACH,IAAI5B,CAAC,CAACkK,IAAI,CAAC/J,KAAK,KAAK,OAAO,EAAE;cAC1BiK,MAAM,GAAGnJ,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACM,KAAK,KAAK,IAAI,IAAIN,OAAO,CAACM,KAAK,KAAK,EAAE,CAAC;YACnF;UACJ;QACJ,CAAC,CAAC;QACF,IAAI0I,IAAI,GAAGzI,SAAS,CAACkI,MAAM,CAAC,UAAU7G,CAAC,EAAE;UACrC,OAAOA,CAAC,CAAC2G,WAAW,CAAC,CAAC,CAACG,KAAK,CAACD,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIQ,MAAM,GAAGzI,KAAK,CAACiI,MAAM,CAAC,UAAU7G,CAAC,EAAE;UACnC,OAAOA,CAAC,CAAC2G,WAAW,CAAC,CAAC,CAACG,KAAK,CAACD,MAAM,CAAC;QACxC,CAAC,CAAC;QACF;AAChB;AACA;QACgB,IAAIS,YAAY,GAAG5I,KAAK,CAACmI,MAAM,CAAC,UAAU7G,CAAC,EAAE;UACzC,OAAOA,CAAC,CAAC2G,WAAW,CAAC,CAAC,CAACG,KAAK,CAACD,MAAM,CAAC;QACxC,CAAC,CAAC;QACF,IAAIO,IAAI,CAAC/J,MAAM,GAAG,CAAC,EAAE;UACjBoJ,SAAS,GAAG1I,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACO,SAAS,KAAKyI,IAAI,CAAC,CAAC,CAAC,CAAC;UACjEX,SAAS,CAACtI,OAAO,CAAC2F,EAAE,IAAI;YACpBoD,MAAM,CAACpI,IAAI,CAACgF,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QACA,IAAIuD,MAAM,CAAChK,MAAM,GAAG,CAAC,EAAE;UACnBoJ,SAAS,GAAG1I,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACQ,KAAK,KAAKyI,MAAM,CAAC,CAAC,CAAC,CAAC;UAC/DZ,SAAS,CAACtI,OAAO,CAAC2F,EAAE,IAAI;YACpBoD,MAAM,CAACpI,IAAI,CAACgF,EAAE,CAAC;UACnB,CAAC,CAAC;QACN,CAAC,CAAC;AAClB;AACA;QAAqB,IAAIwD,YAAY,CAACjK,MAAM,GAAG,CAAC,EAAE;UAC9BoJ,SAAS,GAAG1I,IAAI,CAAC8I,MAAM,CAACzI,OAAO,IAAIA,OAAO,CAACM,KAAK,KAAK4I,YAAY,CAAC,CAAC,CAAC,CAAC;UACrEb,SAAS,CAACtI,OAAO,CAAC2F,EAAE,IAAI;YACpBoD,MAAM,CAACpI,IAAI,CAACgF,EAAE,CAAC;UACnB,CAAC,CAAC;QACN;QAAE,IAAIoD,MAAM,CAAC7J,MAAM,GAAG,CAAC,EAAE;UACrB,IAAI,CAACN,QAAQ,CAAC;YACVO,OAAO,EAAE4J;UACb,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAACnK,QAAQ,CAAC;YACVO,OAAO,EAAE;UACb,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,CAACP,QAAQ,CAAC;UACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACE;QACxB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACiC,KAAK,GAAG,CAAC;MACVC,KAAK,EAAEvE,QAAQ,CAACwE,SAAS;MACzBX,IAAI,EAAE,kBAAkB;MACxBS,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAAC6H,MAAM,GAAG,CAAC;MACX5H,KAAK,EAAE,OAAO;MACdV,IAAI,EAAE,kBAAkB;MACxBS,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAAC8H,GAAG,GAAG,EAAE;IACb;IACA,IAAI,CAACzE,uBAAuB,GAAG,IAAI,CAACA,uBAAuB,CAAC0E,IAAI,CAAC,IAAI,CAAC;IACtE,IAAI,CAACrH,8BAA8B,GAAG,IAAI,CAACA,8BAA8B,CAACqH,IAAI,CAAC,IAAI,CAAC;IACpF,IAAI,CAAC7G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC6G,IAAI,CAAC,IAAI,CAAC;IACxE,IAAI,CAAC5G,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC4G,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAAC3G,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC2G,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACzG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACyG,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACxG,eAAe,GAAG,IAAI,CAACA,eAAe,CAACwG,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACtG,uBAAuB,GAAG,IAAI,CAACA,uBAAuB,CAACsG,IAAI,CAAC,IAAI,CAAC;IACtE,IAAI,CAACpG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACoG,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAAClG,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACkG,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAACjG,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACiG,IAAI,CAAC,IAAI,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACpI,eAAe,GAAG,IAAI,CAACA,eAAe,CAACoI,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAAC5K,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC4K,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC3H,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC2H,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACzE,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACyE,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACrE,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACqE,IAAI,CAAC,IAAI,CAAC;IAChE,IAAI,CAACE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACH,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACI,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACK,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACL,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACO,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACP,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACR,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACS,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACT,IAAI,CAAC,IAAI,CAAC;IAChE,IAAI,CAACU,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACV,IAAI,CAAC,IAAI,CAAC;IAClE,IAAI,CAACW,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACX,IAAI,CAAC,IAAI,CAAC;IACxE,IAAI,CAACY,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACZ,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAAC5H,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC4H,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACa,KAAK,GAAG,IAAI,CAACA,KAAK,CAACb,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACc,SAAS,GAAG,IAAI,CAACA,SAAS,CAACd,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACe,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACf,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACgB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACiB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACjB,IAAI,CAAC,IAAI,CAAC;EAElD;EACA;EACA,MAAMkB,iBAAiBA,CAAA,EAAG;IACtB;IACA,MAAM9N,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACzCgD,IAAI,CAACC,GAAG,IAAI;MACT,IAAI,CAACf,QAAQ,CAAC;QACVS,QAAQ,EAAEM,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,CAACC,iBAAiB;QACpDoH,QAAQ,EAAExH,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY;QAClC8H,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,CAAC,CAACxG,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAA8L,YAAA,EAAAC,YAAA;MACZrJ,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;MACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,6EAAAO,MAAA,CAA0E,EAAAwD,YAAA,GAAA9L,CAAC,CAACuI,QAAQ,cAAAuD,YAAA,uBAAVA,YAAA,CAAY7K,IAAI,MAAK4D,SAAS,IAAAkH,YAAA,GAAG/L,CAAC,CAACuI,QAAQ,cAAAwD,YAAA,uBAAVA,YAAA,CAAY9K,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;QAAE+F,IAAI,EAAE;MAAK,CAAC,CAAC;IACnO,CAAC,CAAC;IACN;IACA,MAAMjK,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChCgD,IAAI,CAACC,GAAG,IAAI;MACT,KAAK,IAAIgL,KAAK,IAAIhL,GAAG,CAACC,IAAI,EAAE;QACxB,IAAI,CAACyJ,GAAG,CAAC1I,IAAI,CAAC;UACViK,IAAI,EAAED,KAAK,CAACvE,UAAU,CAACyE,SAAS;UAChC/L,KAAK,EAAE6L,KAAK,CAACvK;QACjB,CAAC,CAAC;MACN;MACA,IAAI,CAACxB,QAAQ,CAAC;QAAE6G,IAAI,EAAE9F,GAAG,CAACC;MAAK,CAAC,CAAC;IACrC,CAAC,CAAC,CAACwB,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAmM,YAAA,EAAAC,YAAA;MACZ1J,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;MACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,mFAAAO,MAAA,CAAgF,EAAA6D,YAAA,GAAAnM,CAAC,CAACuI,QAAQ,cAAA4D,YAAA,uBAAVA,YAAA,CAAYlL,IAAI,MAAK4D,SAAS,IAAAuH,YAAA,GAAGpM,CAAC,CAACuI,QAAQ,cAAA6D,YAAA,uBAAVA,YAAA,CAAYnL,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;QAAE+F,IAAI,EAAE;MAAK,CAAC,CAAC;IACzO,CAAC,CAAC;IACN,IAAIqE,GAAG,GAAGjF,IAAI,CAACC,KAAK,CAACjH,MAAM,CAACC,cAAc,CAACkH,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1D,IAAI8E,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACpM,QAAQ,CAAC;QACVC,WAAW,EAAEmM;MACjB,CAAC,CAAC;MACF,IAAIA,GAAG,CAAC9L,MAAM,GAAG,CAAC,EAAE;QAChB,IAAI,CAACN,QAAQ,CAAC;UACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACC,QAAQ;UAC5BC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACC,QAAQ;UAC7BE,WAAW,EAAE,IAAI,CAACH,KAAK,CAACC,QAAQ,CAACE,WAAW;UAC5CC,MAAM,EAAE;QACZ,CAAC,CAAC;MACN,CAAC,MAAM,IAAIwL,GAAG,CAAC9L,MAAM,KAAK,CAAC,EAAE;QACzB,IAAIO,GAAG,GAAG,gCAAgC,GAAGuL,GAAG;QAChD,MAAMtO,UAAU,CAAC,KAAK,EAAE+C,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;UACT,IAAIA,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;YACjB,IAAIC,QAAQ,GAAG,EAAE;YACjBF,GAAG,CAACC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACC,OAAO,CAACC,OAAO,IAAI;cACvD,IAAIC,CAAC,GAAG;gBACJhC,SAAS,EAAE+B,OAAO,CAACE,UAAU,CAACC,EAAE;gBAChCC,YAAY,EAAEJ,OAAO,CAACE,UAAU,CAACE,YAAY;gBAC7ClC,WAAW,EAAE8B,OAAO,CAACE,UAAU,CAAChC,WAAW;gBAC3CmC,KAAK,EAAEL,OAAO,CAACK,KAAK;gBACpBC,KAAK,EAAEN,OAAO,CAACE,UAAU,CAACI,KAAK;gBAC/BC,SAAS,EAAEP,OAAO,CAACE,UAAU,CAACK,SAAS;gBACvCC,KAAK,EAAER,OAAO,CAACE,UAAU,CAACM,KAAK;gBAC/BC,MAAM,EAAET,OAAO,CAACE,UAAU,CAACO;cAC/B,CAAC;cACDb,QAAQ,CAACc,IAAI,CAACT,CAAC,CAAC;YACpB,CAAC,CAAC;YACF,IAAI,CAACtB,QAAQ,CAAC;cACVO,OAAO,EAAEU,QAAQ;cACjBN,WAAW,EAAEI,GAAG,CAACC,IAAI,CAACL;YAC1B,CAAC,CAAC;UACN,CAAC,MAAM;YACH;YACA,OAAO3C,aAAa,CAAC;cACjBgE,OAAO,EAAE,wFAAwF;cACjGC,MAAM,EAAE,iBAAiB;cACzBC,IAAI,EAAE,4BAA4B;cAClCC,WAAW,EAAE,IAAI;cACjBC,WAAW,EAAE,IAAI;cACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAAC8J,GAAG,CAAC;cACvC7J,MAAM,EAAE;YACZ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC,CAACC,KAAK,CAAEzC,CAAC,IAAK;UACZ0C,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;UACd;UACA/B,aAAa,CAAC;YACVgE,OAAO,EAAE,wFAAwF;YACjGC,MAAM,EAAE,iBAAiB;YACzBC,IAAI,EAAE,4BAA4B;YAClCC,WAAW,EAAE,IAAI;YACjBC,WAAW,EAAE,IAAI;YACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAACvC,CAAC,CAAC;YACrCwC,MAAM,EAAE;UACZ,CAAC,CAAC;QACN,CAAC,CAAC;MACV;MACA,IAAI,CAACO,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,SAAS,CAAC,CAAC;IACpB,CAAC,MAAM;MACH,IAAI,CAAC/C,QAAQ,CAAC;QAAE0I,aAAa,EAAE,IAAI;QAAEY,WAAW,EAAE;MAAK,CAAC,CAAC;IAC7D;EACJ;EACA+C,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,MAAM,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IACtD,IAAI,CAACF,MAAM,CAACG,YAAY,CAAC,UAAU,CAAC,EAAE;MAClC,IAAIH,MAAM,CAACI,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC9C;MAAA,CACH,MAAM;QACH,IAAIC,aAAa,GAAGL,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC;QACpDD,aAAa,CAACE,UAAU,CAACC,WAAW,CAACT,MAAM,EAAEM,aAAa,CAACI,WAAW,CAAC;MAC3E;MACAV,MAAM,CAACI,SAAS,CAACO,MAAM,CAAC,QAAQ,CAAC;MACjCX,MAAM,CAACI,SAAS,CAACQ,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC;IAC/E;EACJ;EAmEA;EACApK,UAAUA,CAAA,EAAG;IACT,IAAIhB,MAAM,GAAG,EAAE;IACf,IAAIyH,cAAc,GAAG,EAAE;IACvB,IAAI5H,KAAK,GAAG,EAAE;IACd,IAAIE,KAAK,GAAG,EAAE;IACd,IAAIsL,GAAG,GAAG,EAAE;IACZ,IAAI,IAAI,CAAC3M,KAAK,CAACD,OAAO,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACC,KAAK,CAACD,OAAO,CAACa,OAAO,CAACC,OAAO,IAAI;QAClCS,MAAM,CAACC,IAAI,CAACV,OAAO,CAACS,MAAM,CAAC;QAC3ByH,cAAc,CAACxH,IAAI,CAACV,OAAO,CAACO,SAAS,CAAC;QACtCD,KAAK,CAACI,IAAI,CAACV,OAAO,CAACM,KAAK,CAAC;QACzBwL,GAAG,CAACpL,IAAI,CAACV,OAAO,CAACO,SAAS,GAAG,GAAG,GAAGP,OAAO,CAACQ,KAAK,CAAC;QACjDA,KAAK,CAACE,IAAI,CAACV,OAAO,CAACQ,KAAK,CAAC;MAC7B,CAAC,CAAC;MACFC,MAAM,GAAG,CAAC,GAAG,IAAIsL,GAAG,CAACtL,MAAM,CAAC,CAAC,CAACuL,IAAI,CAAC,CAAC;MACpC9D,cAAc,GAAG,CAAC,GAAG,IAAI6D,GAAG,CAAC7D,cAAc,CAAC,CAAC,CAAC8D,IAAI,CAAC,CAAC;MACpDxL,KAAK,GAAG,CAAC,GAAG,IAAIuL,GAAG,CAACvL,KAAK,CAAC,CAAC,CAACwL,IAAI,CAAC,CAAC;MAClCF,GAAG,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACD,GAAG,CAAC,CAAC;MACvBxL,KAAK,GAAG,CAAC,GAAG,IAAIyL,GAAG,CAACzL,KAAK,CAAC,CAAC,CAAC0L,IAAI,CAAC,CAAC;MAClC,IAAIC,WAAW,GAAG,EAAE;MACpB3L,KAAK,CAACP,OAAO,CAACC,OAAO,IAAI;QACrB,IAAIA,OAAO,KAAK,EAAE,EAAE;UAChB,IAAI,CAACmJ,MAAM,CAAC,CAAC,CAAC,CAAC7H,KAAK,CAACZ,IAAI,CAAC;YAAEa,KAAK,EAAEvB,OAAO;YAAEkM,OAAO,EAAGxN,CAAC,IAAK;cAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACzF,CAAC,MAAM;UACHuN,WAAW,CAACvL,IAAI,CAAC;YAAEa,KAAK,EAAE,OAAO;YAAE1C,KAAK,EAAE,OAAO;YAAEqN,OAAO,EAAGxN,CAAC,IAAK;cAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QAChG;MACJ,CAAC,CAAC;MACFuN,WAAW,CAAClM,OAAO,CAACuB,KAAK,IAAI;QACzB,IAAI,CAAC6H,MAAM,CAAC,CAAC,CAAC,CAAC7H,KAAK,CAACZ,IAAI,CAACY,KAAK,CAAC;MACpC,CAAC,CAAC;MACF2K,WAAW,GAAG,EAAE;MAChB/D,cAAc,CAACnI,OAAO,CAACC,OAAO,IAAI;QAC9B,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;UACpC,IAAI,CAACsB,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACZ,IAAI,CAAC;YAAEa,KAAK,EAAEvB,OAAO;YAAEkM,OAAO,EAAGxN,CAAC,IAAK;cAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;YAAC,CAAC;YAAE4C,KAAK,EAAE;UAAG,CAAC,CAAC;QACnG,CAAC,MAAM;UACH2K,WAAW,CAACvL,IAAI,CAAC;YAAEa,KAAK,EAAE,OAAO;YAAE1C,KAAK,EAAE,WAAW;YAAEqN,OAAO,EAAGxN,CAAC,IAAK;cAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;YAAC;UAAE,CAAC,CAAC;QACpG;MACJ,CAAC,CAAC;MACFuN,WAAW,CAAClM,OAAO,CAACuB,KAAK,IAAI;QACzB,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACZ,IAAI,CAACY,KAAK,CAAC;MACnC,CAAC,CAAC;MACFd,KAAK,CAACT,OAAO,CAACuB,KAAK,IAAI;QACnB,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACvB,OAAO,CAAC6I,IAAI,IAAI;UAChC,IAAIA,IAAI,CAACrH,KAAK,KAAK,IAAI,EAAE;YACrB,IAAIuK,GAAG,CAACK,QAAQ,CAACvD,IAAI,CAACrH,KAAK,CAACyF,MAAM,CAAC,GAAG,GAAG1F,KAAK,CAAC,CAAC,EAAE;cAC9C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;gBAChCsH,IAAI,CAACtH,KAAK,CAACZ,IAAI,CAAC;kBAAEa,KAAK,EAAED,KAAK;kBAAE4K,OAAO,EAAGxN,CAAC,IAAK;oBAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;kBAAC;gBAAG,CAAC,CAAC;cAC9E,CAAC,MAAM;gBACHkK,IAAI,CAACtH,KAAK,CAACZ,IAAI,CAAC;kBAAEa,KAAK,EAAE,OAAO;kBAAE1C,KAAK,EAAE,OAAO;kBAAEkK,GAAG,EAAEH,IAAI,CAACrH,KAAK;kBAAE2K,OAAO,EAAGxN,CAAC,IAAK;oBAAE,IAAI,CAACmK,UAAU,CAACnK,CAAC,CAAC;kBAAC;gBAAE,CAAC,CAAC;cAChH;YACJ;UACJ;QAEJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACC,QAAQ,CAAC;QACV8B,MAAM,EAAEA,MAAM;QACdyH,cAAc,EAAEA,cAAc;QAC9B5H,KAAK,EAAEA,KAAK;QACZE,KAAK,EAAEA;MACX,CAAC,CAAC;IACN;EACJ;EACA;EACA,MAAMS,eAAeA,CAACvC,CAAC,EAAE;IACrB,IAAI2G,QAAQ,GAAG;MACX+G,UAAU,EAAE,IAAI,CAACjN,KAAK,CAACP;IAC3B,CAAC;IACD,MAAMnC,UAAU,CAAC,MAAM,EAAE,oBAAoB,EAAE4I,QAAQ,CAAC,CACnD5F,IAAI,CAACC,GAAG,IAAI;MACT0B,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAAC0G,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,2CAA2C;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAClI,CAAC,CAAC,CAACvF,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAA2N,YAAA,EAAAC,YAAA;MACZlL,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;MACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,gGAAAO,MAAA,CAA6F,EAAAqF,YAAA,GAAA3N,CAAC,CAACuI,QAAQ,cAAAoF,YAAA,uBAAVA,YAAA,CAAY1M,IAAI,MAAK4D,SAAS,IAAA+I,YAAA,GAAG5N,CAAC,CAACuI,QAAQ,cAAAqF,YAAA,uBAAVA,YAAA,CAAY3M,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;QAAE+F,IAAI,EAAE;MAAK,CAAC,CAAC;MAClPC,UAAU,CAAC,MAAM;QACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC;IACN,IAAI,CAACpI,WAAW,CAACC,CAAC,CAAC;EACvB;EAsKA;EACA6N,mBAAmBA,CAACC,UAAU,EAAEzO,KAAK,EAAEc,KAAK,EAAE;IAC1C,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI4N,eAAe,GAAG,CAAC,GAAG1O,KAAK,CAACc,KAAK,CAAC;MACtC4N,eAAe,CAAC1O,KAAK,CAAC2O,QAAQ,CAAC,CAAC,UAAU,CAAC,uBAAsB,IAAI7I,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC,CAACpC,MAAM,CAAC9D,KAAK,CAAC;MACrJ,IAAI,CAACF,QAAQ,CAAC;QAAE,IAAAqI,MAAA,CAAIwF,UAAU,IAAKC;MAAgB,CAAC,CAAC;IACzD;EACJ;EACA;EACAE,WAAWA,CAACH,UAAU,EAAEzO,KAAK,EAAE;IAC3B,oBAAOH,OAAA,CAACR,WAAW;MAACwP,aAAa,EAAGlO,CAAC,IAAK,IAAI,CAAC6N,mBAAmB,CAACC,UAAU,EAAEzO,KAAK,EAAEW,CAAC,CAACG,KAAK,CAAE;MAACgO,IAAI,EAAC,UAAU;MAAC9H,QAAQ,EAAC,KAAK;MAAC+H,MAAM,EAAC;IAAO;MAAA1K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpJ;EACA;EACAwK,iBAAiBA,CAACC,OAAO,EAAE;IACvB,IAAIA,OAAO,CAAC9H,QAAQ,KAAK3B,SAAS,EAAE;MAChC,IAAI0J,MAAM,GAAG,CAAC;MACd,IAAI,OAAQD,OAAO,CAAC9H,QAAS,KAAK,QAAQ,EAAE;QACxC+H,MAAM,GAAG9H,UAAU,CAAC6H,OAAO,CAAC9H,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC5E,CAAC,MAAM;QACH6H,MAAM,GAAGD,OAAO,CAAC9H,QAAQ;MAC7B;MACA,oBAAOtH,OAAA;QAAMuE,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAE,IAAI2B,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,UAAU;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAAC,CAACpC,MAAM,CAACsK,MAAM;MAAC;QAAA7K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IACnJ,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EACA;EACA2K,gBAAgBA,CAACxO,CAAC,EAAE;IAChB,IAAIiD,IAAI,GAAG,EAAE;IACb,IAAIjD,CAAC,CAACG,KAAK,KAAK,CAAC,EAAE;MACf8C,IAAI,GAAG,IAAI,CAACxC,KAAK,CAACD,OAAO;MACzB,IAAIyC,IAAI,KAAK,IAAI,EAAE;QACfA,IAAI,CAAC5B,OAAO,CAACC,OAAO,IAAI;UACpB,IAAImN,UAAU,GAAG,CAAC;UAClB,IAAInN,OAAO,CAAC+B,QAAQ,KAAKwB,SAAS,EAAE;YAChC4J,UAAU,GAAGhI,UAAU,CAACnF,OAAO,CAAC+B,QAAQ,CAAC;YACzCoL,UAAU,IAAIhI,UAAU,CAACnF,OAAO,CAAC+B,QAAQ,CAAC,GAAGrD,CAAC,CAACG,KAAK,GAAG,GAAG;YAC1DmB,OAAO,CAACkF,QAAQ,GAAG,IAAIrB,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACpC,MAAM,CAACwK,UAAU,CAAC;UAChH,CAAC,MAAM;YACHA,UAAU,GAAGhI,UAAU,CAACnF,OAAO,CAACK,KAAK,CAAC;YACtC8M,UAAU,IAAIhI,UAAU,CAACnF,OAAO,CAACK,KAAK,CAAC,GAAG3B,CAAC,CAACG,KAAK,GAAG,GAAG;YACvDmB,OAAO,CAACkF,QAAQ,GAAG,IAAIrB,IAAI,CAACgB,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACpC,MAAM,CAACwK,UAAU,CAAC;UAChH;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACxO,QAAQ,CAAC;QAAEO,OAAO,EAAEyC;MAAK,CAAC,CAAC;IACpC,CAAC,MAAM,IAAIjD,CAAC,CAACG,KAAK,KAAK,CAAC,EAAE;MACtB,IAAI,IAAI,CAACM,KAAK,CAACD,OAAO,KAAK,IAAI,EAAE;QAC7ByC,IAAI,GAAG,IAAI,CAACxC,KAAK,CAACD,OAAO;QACzByC,IAAI,CAAC5B,OAAO,CAACC,OAAO,IAAI;UACpB,IAAIA,OAAO,CAAC+B,QAAQ,KAAKwB,SAAS,EAAE;YAChCvD,OAAO,CAACkF,QAAQ,GAAGlF,OAAO,CAAC+B,QAAQ;UACvC,CAAC,MAAM;YACH/B,OAAO,CAACkF,QAAQ,GAAGlF,OAAO,CAAC6B,SAAS;UACxC;QACJ,CAAC,CAAC;QACF,IAAI,CAAClD,QAAQ,CAAC;UAAEO,OAAO,EAAEyC;QAAK,CAAC,CAAC;MACpC;IACJ;EACJ;EAsDA;EACA2H,iBAAiBA,CAAC0D,OAAO,EAAE;IACvB,oBACIpP,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAAAD,QAAA,eAC5BtE,OAAA;QAAKwP,GAAG,EAAE1Q,SAAS,GAAG,iBAAiB,GAAGsQ,OAAO,CAAC/O,SAAS,GAAG,MAAO;QAACoP,OAAO,EAAG3O,CAAC,IAAKA,CAAC,CAAC0J,MAAM,CAACgF,GAAG,GAAG7Q,QAAS;QAAC+Q,GAAG,EAAEN,OAAO,CAAC9O;MAAY;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1I,CAAC;EAEd;EACA;EACAiH,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC7K,QAAQ,CAAC;MAAE6I,kBAAkB,EAAE;IAAM,CAAC,CAAC;EAChD;EACA;EACAiC,mBAAmBA,CAAClC,MAAM,EAAE;IACxB,IAAI,CAAC5I,QAAQ,CAAC;MACV4I,MAAM;MACNC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAM+B,YAAYA,CAAA,EAAG;IACjB,IAAIrK,OAAO,GAAG,IAAI,CAACC,KAAK,CAACD,OAAO,CAACuJ,MAAM,CAAC8E,GAAG,IAAIA,GAAG,CAACtP,SAAS,KAAK,IAAI,CAACkB,KAAK,CAACoI,MAAM,CAACtJ,SAAS,CAAC;IAC7F,IAAI,CAACU,QAAQ,CAAC;MACVO,OAAO;MACPG,QAAQ,EAAEH,OAAO;MACjBsI,kBAAkB,EAAE,KAAK;MACzBD,MAAM,EAAE,IAAI,CAACvJ;IACjB,CAAC,CAAC;IACF,IAAI4B,QAAQ,GAAG,EAAE;IACjBV,OAAO,CAACa,OAAO,CAACC,OAAO,IAAI;MACvB,IAAIA,OAAO,CAACkF,QAAQ,KAAK3B,SAAS,EAAE;QAChC3D,QAAQ,CAACc,IAAI,CAAC;UAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;UAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACkF,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE,CAAC,CAAC;MACpH,CAAC,MAAM;QACHxF,QAAQ,CAACc,IAAI,CAAC;UAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;UAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACK,KAAK,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE,CAAC,CAAC;MACjH;IACJ,CAAC,CAAC;IACF,IAAIC,QAAQ,GAAG;MACXC,QAAQ,EAAE1F;IACd,CAAC;IACD,IAAIJ,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAACL,KAAK,CAACG,WAAW;IACnD,MAAM7C,UAAU,CAAC,KAAK,EAAE+C,GAAG,EAAE6F,QAAQ,CAAC,CACjC5F,IAAI,CAACC,GAAG,IAAI;MACT0B,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAAC0G,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,iCAAiC;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACxH,CAAC,CAAC,CAACvF,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAA8O,YAAA,EAAAC,YAAA;MACZrM,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;MACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,mFAAAO,MAAA,CAAgF,EAAAwG,YAAA,GAAA9O,CAAC,CAACuI,QAAQ,cAAAuG,YAAA,uBAAVA,YAAA,CAAY7N,IAAI,MAAK4D,SAAS,IAAAkK,YAAA,GAAG/O,CAAC,CAACuI,QAAQ,cAAAwG,YAAA,uBAAVA,YAAA,CAAY9N,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;QAAE+F,IAAI,EAAE;MAAK,CAAC,CAAC;IACzO,CAAC,CAAC;EACV;EACA;EACAgD,kBAAkBA,CAACsD,OAAO,EAAE;IACxB,oBACIpP,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,gBACXtE,OAAA,CAAChB,OAAO;QAACwL,MAAM,EAAC;MAAmB;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtC3E,OAAA,CAACb,MAAM;QAAC8D,IAAI,EAAC,aAAa;QAACsB,SAAS,EAAC,kBAAkB;QAAC,mBAAgB,8BAA8B;QAAC,oBAAiB,KAAK;QAACuL,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACjE,mBAAmB,CAACuD,OAAO;MAAE;QAAA5K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtK,CAAC;EAEzB;EACA;EACAoH,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACxK,KAAK,CAACP,WAAW,KAAK,IAAI,EAAE;MACjC,IAAI6G,IAAI,GAAG,IAAI,CAACtG,KAAK,CAACE,QAAQ,CAACoG,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACR,QAAQ,KAAK3B,SAAS,CAAC;MACpE,IAAIoK,UAAU,GAAG;QACbvB,UAAU,EAAE,IAAI,CAACjN,KAAK,CAACP,WAAW;QAClCU,WAAW,EAAE,IAAI,CAACH,KAAK,CAACG;MAC5B,CAAC;MACD,IAAImG,IAAI,KAAKlC,SAAS,EAAE;QACpByC,YAAY,CAAChH,OAAO,CAAC,YAAY,EAAE8G,IAAI,CAAC8H,SAAS,CAACD,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAChP,QAAQ,CAAC;UACVyI,YAAY,EAAE;QAClB,CAAC,CAAC;MACN,CAAC,MAAM;QACHzK,aAAa,CAAC;UACVgE,OAAO,EAAE,4EAA4E;UACrFC,MAAM,EAAE,YAAY;UACpBC,IAAI,EAAE,4BAA4B;UAClCC,WAAW,EAAE,IAAI;UACjBC,WAAW,EAAE,IAAI;UACjBC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6M,QAAQ,CAACF,UAAU,CAAC;UACvCzM,MAAM,EAAE;QACZ,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACmF,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEC,OAAO,EAAE,YAAY;QAAEC,MAAM,EAAE,uEAAuE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC7J;EACJ;EACAmH,QAAQA,CAACF,UAAU,EAAE;IACjB3H,YAAY,CAAChH,OAAO,CAAC,YAAY,EAAE8G,IAAI,CAAC8H,SAAS,CAACD,UAAU,CAAC,CAAC;IAC9D,IAAI,CAAChP,QAAQ,CAAC;MACVyI,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACA,MAAMwC,sBAAsBA,CAAA,EAAG;IAC3B,IAAI,CAACjL,QAAQ,CAAC;MACVyI,YAAY,EAAE;IAClB,CAAC,CAAC;IACF;IACA,IAAI5H,GAAG,GAAG,gCAAgC,GAAG,IAAI,CAACL,KAAK,CAACP,WAAW;IACnE,MAAMnC,UAAU,CAAC,KAAK,EAAE+C,GAAG,CAAC,CACvBC,IAAI,CAACC,GAAG,IAAI;MACT,IAAIA,GAAG,CAACC,IAAI,KAAK,EAAE,EAAE;QACjB,IAAIC,QAAQ,GAAG,EAAE;QACjBF,GAAG,CAACC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACC,OAAO,CAACC,OAAO,IAAI;UACvD,IAAIC,CAAC,GAAG;YACJhC,SAAS,EAAE+B,OAAO,CAACE,UAAU,CAACC,EAAE;YAChCC,YAAY,EAAEJ,OAAO,CAACE,UAAU,CAACE,YAAY;YAC7ClC,WAAW,EAAE8B,OAAO,CAACE,UAAU,CAAChC,WAAW;YAC3CmC,KAAK,EAAEL,OAAO,CAACK,KAAK;YACpBC,KAAK,EAAEN,OAAO,CAACE,UAAU,CAACI,KAAK;YAC/BC,SAAS,EAAEP,OAAO,CAACE,UAAU,CAACK,SAAS;YACvCC,KAAK,EAAER,OAAO,CAACE,UAAU,CAACM,KAAK;YAC/BC,MAAM,EAAET,OAAO,CAACE,UAAU,CAACO;UAC/B,CAAC;UACDb,QAAQ,CAACc,IAAI,CAACT,CAAC,CAAC;QACpB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACVO,OAAO,EAAEU,QAAQ;UACjBP,QAAQ,EAAEO,QAAQ;UAClBN,WAAW,EAAEI,GAAG,CAACC,IAAI,CAACL;QAC1B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CAAC6B,KAAK,CAAEzC,CAAC,IAAK;MACZ0C,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,IAAI,CAAC4C,KAAK,GAAG,CAAC;MACVC,KAAK,EAAEvE,QAAQ,CAACwE,SAAS;MACzBX,IAAI,EAAE,kBAAkB;MACxBS,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACG,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACA;EACAwI,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACvL,QAAQ,CAAC;MACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACE,QAAQ;MAC5B2I,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA;EACAmC,SAASA,CAAA,EAAG;IACR,IAAI,CAACxL,QAAQ,CAAC;MACVO,OAAO,EAAE,IAAI,CAACC,KAAK,CAACE,QAAQ;MAC5B2I,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA+B,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACpL,QAAQ,CAAC;MAAE8I,oBAAoB,EAAE;IAAK,CAAC,CAAC;EACjD;EACAuC,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACrL,QAAQ,CAAC;MAAE8I,oBAAoB,EAAE;IAAM,CAAC,CAAC;EAClD;EACA,MAAMwC,sBAAsBA,CAAA,EAAG;IAC3B,IAAI/K,OAAO,GAAG,IAAI,CAACC,KAAK,CAACD,OAAO,CAACuJ,MAAM,CAAC8E,GAAG,IAAI,CAAC,IAAI,CAACpO,KAAK,CAACuI,gBAAgB,CAACyE,QAAQ,CAACoB,GAAG,CAAC,CAAC;IAC1F,IAAI,CAAC5O,QAAQ,CAAC;MACVO,OAAO;MACPuI,oBAAoB,EAAE,KAAK;MAC3BC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IACF,IAAI9H,QAAQ,GAAG,EAAE;IACjBV,OAAO,CAACa,OAAO,CAACC,OAAO,IAAI;MACvB,IAAIA,OAAO,CAACkF,QAAQ,KAAK3B,SAAS,EAAE;QAChC3D,QAAQ,CAACc,IAAI,CAAC;UAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;UAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACkF,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE,CAAC,CAAC;QAChHuB,UAAU,CAAC,MAAM;UACb7H,MAAM,CAAC8H,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QACHjH,QAAQ,CAACc,IAAI,CAAC;UAAEP,EAAE,EAAEH,OAAO,CAAC/B,SAAS;UAAEoC,KAAK,EAAE8E,UAAU,CAACnF,OAAO,CAACK,KAAK,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE,CAAC,CAAC;MACjH;IACJ,CAAC,CAAC;IACF,IAAIC,QAAQ,GAAG;MACXC,QAAQ,EAAE1F;IACd,CAAC;IACD,IAAIJ,GAAG,GAAG,gBAAgB,GAAG,IAAI,CAACL,KAAK,CAACG,WAAW;IACnD,MAAM7C,UAAU,CAAC,KAAK,EAAE+C,GAAG,EAAE6F,QAAQ,CAAC,CACjC5F,IAAI,CAACC,GAAG,IAAI;MACT0B,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAACC,IAAI,CAAC;MACrB,IAAI,CAAC0G,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,iCAAiC;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACxH,CAAC,CAAC,CAACvF,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAoP,YAAA,EAAAC,aAAA;MACZ3M,OAAO,CAACC,GAAG,CAAC3C,CAAC,CAAC;MACd,IAAI,CAAC2H,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,kFAAAO,MAAA,CAA+E,EAAA8G,YAAA,GAAApP,CAAC,CAACuI,QAAQ,cAAA6G,YAAA,uBAAVA,YAAA,CAAYnO,IAAI,MAAK4D,SAAS,IAAAwK,aAAA,GAAGrP,CAAC,CAACuI,QAAQ,cAAA8G,aAAA,uBAAVA,aAAA,CAAYpO,IAAI,GAAGjB,CAAC,CAACiC,OAAO,CAAE;QAAE+F,IAAI,EAAE;MAAK,CAAC,CAAC;IACxO,CAAC,CAAC;EACV;EACAmD,mBAAmBA,CAAA,EAAG;IAClB,oBACIjM,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,eACXtE,OAAA;QAAKuE,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBtE,OAAA;UAAKuE,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BtE,OAAA;YAAKuE,SAAS,EAAC,mBAAmB;YAAAD,QAAA,eAC9BtE,OAAA;cAAKuE,SAAS,EAAC,sFAAsF;cAAAD,QAAA,gBACjGtE,OAAA;gBAAOuE,SAAS,EAAC,2BAA2B;gBAAC6L,OAAO,EAAC,gBAAgB;gBAAA9L,QAAA,eAACtE,OAAA;kBAAAsE,QAAA,EAASlF,QAAQ,CAACoM;gBAAG;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7G3E,OAAA,CAACP,QAAQ;gBAAC8E,SAAS,EAAC,cAAc;gBAACtD,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,WAAY;gBAACqP,OAAO,EAAE,IAAI,CAAC7E,GAAI;gBAAC8E,QAAQ,EAAE,IAAI,CAACzP,WAAY;gBAAC0P,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC;cAAyB;gBAAAhM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEzB;EACAuH,oBAAoBA,CAAA,EAAG;IACnB,oBACIlM,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,gBACXtE,OAAA;QAAKuE,SAAS,EAAC,sFAAsF;QAAAD,QAAA,gBACjGtE,OAAA;UAAOuE,SAAS,EAAC,2BAA2B;UAAC6L,OAAO,EAAC,gBAAgB;UAAA9L,QAAA,eAACtE,OAAA;YAAAsE,QAAA,EAASlF,QAAQ,CAACqR;UAAS;YAAAjM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnH3E,OAAA,CAACR,WAAW;UAACkR,OAAO,EAAC,gBAAgB;UAACnM,SAAS,EAAC,mBAAmB;UAACoM,MAAM,EAAC,GAAG;UAACC,GAAG,EAAE,CAAE;UAACC,GAAG,EAAE,GAAI;UAAC5P,KAAK,EAAE,IAAI,CAACM,KAAK,CAACN,KAAM;UAAC+N,aAAa,EAAGlO,CAAC,IAAK,IAAI,CAACwO,gBAAgB,CAACxO,CAAC,CAAE;UAACmO,IAAI,EAAC,SAAS;UAAC6B,WAAW;QAAA;UAAAtM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtM,CAAC,eACN3E,OAAA,CAACb,MAAM;QAACoD,EAAE,EAAC,gBAAgB;QAACgC,SAAS,EAAC,wBAAwB;QAACuL,OAAO,EAAE,IAAI,CAAC3D,qBAAsB;QAAC4E,QAAQ,EAAE,CAAC,IAAI,CAACxP,KAAK,CAACuI,gBAAgB,IAAI,CAAC,IAAI,CAACvI,KAAK,CAACuI,gBAAgB,CAACzI,MAAO;QAAAiD,QAAA,gBAAEtE,OAAA;UAAGuE,SAAS,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAACvF,QAAQ,CAAC4R,MAAM;MAAA;QAAAxM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtO,CAAC;EAEzB;EACA6H,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACjL,KAAK,CAACP,WAAW,KAAK,IAAI,EAAE;MACjC,IAAI,CAACD,QAAQ,CAAC;QACV0I,aAAa,EAAE;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAAChB,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEC,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA2D,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC1L,QAAQ,CAAC;MACV2I,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAgD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC3L,QAAQ,CAAC;MACV2I,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAuH,MAAMA,CAAA,EAAG;IACL,MAAMvN,KAAK,GAAG,CACV;MACIC,KAAK,EAAEvE,QAAQ,CAAC8R,WAAW;MAC3BjO,IAAI,EAAE,mBAAmB;MACzBqL,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACvC,kBAAkB,CAAC,CAAC;MAC7B;IACJ,CAAC,CACJ;IACD;IACA,MAAM/I,MAAM,gBACRhD,OAAA;MAAKuE,SAAS,EAAC,gBAAgB;MAAAD,QAAA,eAC3BtE,OAAA;QAAKuE,SAAS,EAAC,2DAA2D;QAAAD,QAAA,gBACtEtE,OAAA;UAAKuE,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eACzCtE,OAAA;YAAMuE,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAC/CtE,OAAA;cAAKuE,SAAS,EAAC,8CAA8C;cAAAD,QAAA,eACzDtE,OAAA;gBAAKuE,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,gBAC1CtE,OAAA;kBAAGuE,SAAS,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnC3E,OAAA;kBACImR,IAAI,EAAC,MAAM;kBACXX,WAAW,EAAC,yBAAyB;kBACrCvP,KAAK,EAAE,IAAI,CAACM,KAAK,CAAC4I,MAAO;kBACzBmG,QAAQ,EAAE,IAAI,CAAC/F;gBAAa;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3E,OAAA;UAAKuE,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAChCtE,OAAA;YAAKuE,SAAS,EAAC,uFAAuF;YAAAD,QAAA,gBAClGtE,OAAA,CAACb,MAAM;cAACoF,SAAS,EAAC,MAAM;cAACuL,OAAO,EAAE,IAAI,CAACrD,UAAW;cAAC2E,OAAO,EAAC,QAAQ;cAACC,cAAc,EAAE;gBAAEC,QAAQ,EAAE;cAAM,CAAE;cAAAhN,QAAA,eAAEtE,OAAA;gBAAUuE,SAAS,EAAC,MAAM;gBAACwI,IAAI,EAAC;cAAgB;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAE/K3E,OAAA,CAACF,WAAW;cAAC6D,KAAK,EAAC,QAAQ;cAACV,IAAI,EAAC,WAAW;cAACsO,KAAK,EAAE7N,KAAM;cAACa,SAAS,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAE9G3E,OAAA,CAACpB,eAAe;cAAC+E,KAAK,EAAE,YAAa;cAACrC,OAAO,EAAE,IAAI,CAACC,KAAK,CAACD;YAAQ;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IACD;IACA,MAAM6M,kBAAkB,gBACpBxR,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,eACXtE,OAAA,CAACb,MAAM;QAACoF,SAAS,EAAC,eAAe;QAACuL,OAAO,EAAE,IAAI,CAAC9D,sBAAuB;QAAA1H,QAAA,GAAE,GAAC,EAAClF,QAAQ,CAACqS,MAAM,EAAC,GAAC;MAAA;QAAAjN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CACnB;IACD,MAAM+M,mBAAmB,gBACrB1R,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,eACXtE,OAAA;QAAKuE,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DtE,OAAA,CAACb,MAAM;UAACoF,SAAS,EAAC,0BAA0B;UAACuL,OAAO,EAAE,IAAI,CAACtD,iBAAkB;UAAAlI,QAAA,GAAE,GAAC,EAAClF,QAAQ,CAACqS,MAAM,EAAC,GAAC;QAAA;UAAAjN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMgN,wBAAwB,gBAC1B3R,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,gBACXtE,OAAA,CAACb,MAAM;QAACwE,KAAK,EAAC,IAAI;QAACV,IAAI,EAAC,aAAa;QAACsB,SAAS,EAAC,eAAe;QAACuL,OAAO,EAAE,IAAI,CAAClE;MAAuB;QAAApH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxG3E,OAAA,CAACb,MAAM;QAACoF,SAAS,EAAC,eAAe;QAACuL,OAAO,EAAE,IAAI,CAACnE,YAAa;QAAArH,QAAA,GAAE,GAAC,EAAClF,QAAQ,CAACwS,EAAE,EAAC,GAAC;MAAA;QAAApN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CACnB;IACD,MAAMkN,0BAA0B,gBAC5B7R,OAAA,CAACxB,KAAK,CAAC6F,QAAQ;MAAAC,QAAA,gBACXtE,OAAA,CAACb,MAAM;QAACwE,KAAK,EAAC,IAAI;QAACV,IAAI,EAAC,aAAa;QAACsB,SAAS,EAAC,eAAe;QAACuL,OAAO,EAAE,IAAI,CAAC1D;MAAyB;QAAA5H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1G3E,OAAA,CAACb,MAAM;QAACwE,KAAK,EAAC,IAAI;QAACV,IAAI,EAAC,aAAa;QAACsB,SAAS,EAAC,eAAe;QAACuL,OAAO,EAAE,IAAI,CAACzD;MAAuB;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CACnB;IACD,IAAImN,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACvQ,KAAK,CAAC6I,OAAO,KAAK,EAAE,EAAE;MAC3B0H,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI9R,OAAA;MAAKuE,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAExCtE,OAAA,CAAEX,KAAK;QAAC0S,GAAG,EAAGjK,EAAE,IAAK,IAAI,CAACW,KAAK,GAAGX;MAAG;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExC3E,OAAA,CAAEtB,GAAG;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACR3E,OAAA;QAAKuE,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCtE,OAAA;UAAAsE,QAAA,EAAKlF,QAAQ,CAAC4S;QAAkB;UAAAxN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACN3E,OAAA;QAAKuE,SAAS,EAAC,+CAA+C;QAAAD,QAAA,gBAC1DtE,OAAA,CAACJ,OAAO;UAACqS,IAAI,EAAE,IAAI,CAAChG,mBAAoB;UAACiG,KAAK,EAAE,IAAI,CAAChG;QAAqB;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE7E3E,OAAA,CAACV,SAAS;UAACyS,GAAG,EAAGjK,EAAE,IAAK,IAAI,CAACqK,EAAE,GAAGrK,EAAG;UAAC7G,KAAK,EAAE,IAAI,CAACM,KAAK,CAACD,OAAQ;UAACyI,OAAO,EAAE,IAAI,CAACxI,KAAK,CAACwI,OAAQ;UACzFqI,SAAS,EAAE,IAAI,CAAC7Q,KAAK,CAACuI,gBAAiB;UAACuI,iBAAiB,EAAGvR,CAAC,IAAK,IAAI,CAACC,QAAQ,CAAC;YAAE+I,gBAAgB,EAAEhJ,CAAC,CAACG;UAAM,CAAC,CAAE;UAC/GqR,OAAO,EAAC,WAAW;UAACC,SAAS;UAACC,IAAI,EAAE,EAAG;UAACC,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UAACvL,KAAK,EAAE;YAAEwL,QAAQ,EAAE;UAAO,CAAE;UACtGnJ,YAAY,EAAE,IAAI,CAAChI,KAAK,CAACgI,YAAa;UAACoJ,QAAQ,EAAC,MAAM;UAACpO,SAAS,EAAC,sBAAsB;UACvFvB,MAAM,EAAEA,MAAO;UAAC4P,UAAU,EAAC,MAAM;UAACC,YAAY,EAAC,GAAG;UAAAvO,QAAA,gBAClDtE,OAAA,CAACT,MAAM;YAACuT,aAAa,EAAC;UAAU;YAAAtO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC3C3E,OAAA,CAACT,MAAM;YAACwT,KAAK,EAAC,OAAO;YAAC/K,IAAI,EAAE,IAAI,CAAC0D;UAAkB;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC7D3E,OAAA,CAACT,MAAM;YAACwT,KAAK,EAAC,cAAc;YAAC/P,MAAM,EAAE5D,QAAQ,CAAC2L,MAAO;YAAC/C,IAAI,EAAE,IAAI,CAACpD;UAAyB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7F3E,OAAA,CAACT,MAAM;YAACwT,KAAK,EAAC,aAAa;YAAC/P,MAAM,EAAE5D,QAAQ,CAAC4T,IAAK;YAAChL,IAAI,EAAE,IAAI,CAAC5D;UAA+B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChG3E,OAAA,CAACT,MAAM;YAACwT,KAAK,EAAC,UAAU;YAAC/P,MAAM,EAAE5D,QAAQ,CAAC6T,OAAQ;YAACjL,IAAI,EAAE,IAAI,CAACZ,oBAAqB;YAAC8L,QAAQ;UAAA;YAAA1O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACvG3E,OAAA,CAACT,MAAM;YAACwT,KAAK,EAAC,OAAO;YAAC/P,MAAM,EAAE5D,QAAQ,CAAC+T,OAAQ;YAACnL,IAAI,EAAE,IAAI,CAAChB,kBAAmB;YAACkM,QAAQ;UAAA;YAAA1O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAClG3E,OAAA,CAACT,MAAM;YAACyD,MAAM,EAAE5D,QAAQ,CAACgU,UAAW;YAACpL,IAAI,EAAE,IAAI,CAACmH,iBAAkB;YAACkE,MAAM,EAAGlT,KAAK,IAAK,IAAI,CAAC4O,WAAW,CAAC,UAAU,EAAE5O,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACrI3E,OAAA,CAACT,MAAM;YAACyI,IAAI,EAAE,IAAI,CAAC8D;UAAmB;YAAAtH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACN3E,OAAA;QAAKuC,EAAE,EAAC,eAAe;QAACgC,SAAS,EAAC,yFAAyF;QAAAD,QAAA,eACvHtE,OAAA,CAACb,MAAM;UAACoD,EAAE,EAAC,OAAO;UAACgC,SAAS,EAAC,oCAAoC;UAACuL,OAAO,EAAE,IAAI,CAACzI,KAAM;UAAA/C,QAAA,EAAElF,QAAQ,CAACkU;QAAK;UAAA9O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eAEN3E,OAAA,CAAEf,MAAM;QAACsU,OAAO,EAAE,IAAI,CAAChS,KAAK,CAACiI,YAAa;QAACxG,MAAM,EAAE5D,QAAQ,CAAC8R,WAAY;QAACsC,KAAK;QAACjP,SAAS,EAAC,kBAAkB;QAACkP,MAAM,EAAEjC,kBAAmB;QAACkC,MAAM,EAAE,IAAI,CAAC1H,sBAAuB;QAAA1H,QAAA,eACxKtE,OAAA,CAACd,aAAa;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEV3E,OAAA,CAACf,MAAM;QAACsU,OAAO,EAAE,IAAI,CAAChS,KAAK,CAACqI,kBAAmB;QAAC5G,MAAM,EAAE5D,QAAQ,CAACuU,QAAS;QAACH,KAAK;QAACC,MAAM,EAAE9B,wBAAyB;QAAC+B,MAAM,EAAE,IAAI,CAAC9H,sBAAuB;QAAAtH,QAAA,eACnJtE,OAAA;UAAKuE,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCtE,OAAA;YAAGuE,SAAS,EAAC,mCAAmC;YAAC2C,KAAK,EAAE;cAAE0M,QAAQ,EAAE;YAAO;UAAE;YAAApP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC/E,IAAI,CAACpD,KAAK,CAACoI,MAAM,iBAAI3J,OAAA;YAAAsE,QAAA,GAAOlF,QAAQ,CAACyU,aAAa,EAAC,GAAC,eAAA7T,OAAA;cAAAsE,QAAA,GAAI,IAAI,CAAC/C,KAAK,CAACoI,MAAM,CAACrJ,WAAW,EAAC,GAAC;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACV3E,OAAA,CAACf,MAAM;QAACsU,OAAO,EAAE,IAAI,CAAChS,KAAK,CAACsI,oBAAqB;QAAC7G,MAAM,EAAE5D,QAAQ,CAAC0U,OAAQ;QAACN,KAAK;QAACC,MAAM,EAAE5B,0BAA2B;QAAC6B,MAAM,EAAE,IAAI,CAACtH,wBAAyB;QAAA9H,QAAA,eACxJtE,OAAA;UAAKuE,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCtE,OAAA;YAAGuE,SAAS,EAAC,iCAAiC;YAAC2C,KAAK,EAAE;cAAE0M,QAAQ,EAAE;YAAO;UAAE;YAAApP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC7E,IAAI,CAACpD,KAAK,CAACoI,MAAM,iBAAI3J,OAAA;YAAAsE,QAAA,GAAOlF,QAAQ,CAAC2U,cAAc,EAAC,GAAC;UAAA;YAAAvP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT3E,OAAA,CAACf,MAAM;QAACsU,OAAO,EAAE,IAAI,CAAChS,KAAK,CAACkI,aAAc;QAACzG,MAAM,EAAE5D,QAAQ,CAAC4U,iBAAkB;QAACR,KAAK;QAACjP,SAAS,EAAC,kBAAkB;QAACmP,MAAM,EAAE,IAAI,CAAClH,iBAAkB;QAACiH,MAAM,EAAE/B,mBAAoB;QAAApN,QAAA,GACzK,IAAI,CAAC/C,KAAK,CAAC8I,WAAW,iBACnBrK,OAAA,CAACL,UAAU;UAACsU,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,6BAA6B;UAAC1J,MAAM,EAAC;QAAS;UAAAhG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEpG3E,OAAA;UAAKuE,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DtE,OAAA;YAAIuE,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACtE,OAAA;cAAGuE,SAAS,EAAC,iBAAiB;cAAC2C,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACvF,QAAQ,CAACoM,GAAG;UAAA;YAAAhH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1G3E,OAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3E,OAAA,CAACP,QAAQ;YAAC8E,SAAS,EAAC,QAAQ;YAACtD,KAAK,EAAE,IAAI,CAACM,KAAK,CAACP,WAAY;YAACqP,OAAO,EAAE,IAAI,CAAC7E,GAAI;YAAC8E,QAAQ,EAAE,IAAI,CAACzP,WAAY;YAAC0P,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC;UAAyB;YAAAhM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT3E,OAAA,CAACN,OAAO;QAAC6T,OAAO,EAAE,IAAI,CAAChS,KAAK,CAACmI,aAAc;QAAC4H,QAAQ,EAAC,MAAM;QAACoC,MAAM,EAAE,IAAI,CAAChH,WAAY;QAAApI,QAAA,gBACjFtE,OAAA;UAAKuC,EAAE,EAAC,cAAc;UAACgC,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKtE,OAAA;YAAGuE,SAAS,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C3E,OAAA;YAAIuE,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACtE,OAAA;cAAGuE,SAAS,EAAC,mBAAmB;cAAC2C,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACvF,QAAQ,CAAC+U,MAAM;UAAA;YAAA3P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G3E,OAAA,CAACb,MAAM;YAACoD,EAAE,EAAC,iBAAiB;YAACgC,SAAS,EAAEuN,WAAY;YAAChC,OAAO,EAAE,IAAI,CAACxD,KAAM;YAAAhI,QAAA,gBAACtE,OAAA;cAAGuE,SAAS,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA3E,OAAA;cAAAsE,QAAA,EAAM;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN3E,OAAA;UAAKuC,EAAE,EAAC,kBAAkB;UAACgC,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DtE,OAAA;YAAIuE,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACtE,OAAA;cAAGuE,SAAS,EAAC,mBAAmB;cAAC2C,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAACvF,QAAQ,CAAC+U,MAAM;UAAA;YAAA3P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G3E,OAAA,CAACb,MAAM;YAACoD,EAAE,EAAC,kBAAkB;YAACgC,SAAS,EAAEuN,WAAY;YAAChC,OAAO,EAAE,IAAI,CAACvD,SAAU;YAAAjI,QAAA,gBAACtE,OAAA;cAAGuE,SAAS,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA3E,OAAA;cAAAsE,QAAA,EAAM;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN3E,OAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3E,OAAA,CAACH,SAAS;UAAC0E,SAAS,EAAC,qBAAqB;UAACgN,KAAK,EAAE,IAAI,CAAC7N;QAAM;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEf;AACJ;AAEA,eAAe1E,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}