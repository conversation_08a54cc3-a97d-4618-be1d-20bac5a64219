{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport classNames from 'classnames';\nimport { Field, FieldContext, ListContext } from 'rc-field-form';\nimport { supportRef } from \"rc-util/es/ref\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport omit from \"rc-util/es/omit\";\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport Row from '../grid/row';\nimport { ConfigContext } from '../config-provider';\nimport { tuple } from '../_util/type';\nimport devWarning from '../_util/devWarning';\nimport FormItemLabel from './FormItemLabel';\nimport FormItemInput from './FormItemInput';\nimport { FormContext, FormItemInputContext, NoStyleItemContext } from './context';\nimport { toArray, getFieldId } from './util';\nimport { cloneElement, isValidElement } from '../_util/reactNode';\nimport useFrameState from './hooks/useFrameState';\nimport useDebounce from './hooks/useDebounce';\nimport useItemRef from './hooks/useItemRef';\nvar NAME_SPLIT = '__SPLIT__';\nvar ValidateStatuses = tuple('success', 'warning', 'error', 'validating', '');\nvar MemoInput = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  return prev.value === next.value && prev.update === next.update;\n});\nfunction hasValidName(name) {\n  if (name === null) {\n    devWarning(false, 'Form.Item', '`null` is passed as `name` property');\n  }\n  return !(name === undefined || name === null);\n}\nfunction genEmptyMeta() {\n  return {\n    errors: [],\n    warnings: [],\n    touched: false,\n    validating: false,\n    name: []\n  };\n}\nvar iconMap = {\n  success: CheckCircleFilled,\n  warning: ExclamationCircleFilled,\n  error: CloseCircleFilled,\n  validating: LoadingOutlined\n};\nfunction FormItem(props) {\n  var name = props.name,\n    noStyle = props.noStyle,\n    dependencies = props.dependencies,\n    customizePrefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    shouldUpdate = props.shouldUpdate,\n    hasFeedback = props.hasFeedback,\n    help = props.help,\n    rules = props.rules,\n    validateStatus = props.validateStatus,\n    children = props.children,\n    required = props.required,\n    label = props.label,\n    messageVariables = props.messageVariables,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? 'onChange' : _props$trigger,\n    validateTrigger = props.validateTrigger,\n    hidden = props.hidden,\n    restProps = __rest(props, [\"name\", \"noStyle\", \"dependencies\", \"prefixCls\", \"style\", \"className\", \"shouldUpdate\", \"hasFeedback\", \"help\", \"rules\", \"validateStatus\", \"children\", \"required\", \"label\", \"messageVariables\", \"trigger\", \"validateTrigger\", \"hidden\"]);\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var _useContext2 = useContext(FormContext),\n    formName = _useContext2.name,\n    requiredMark = _useContext2.requiredMark;\n  var isRenderProps = typeof children === 'function';\n  var notifyParentMetaChange = useContext(NoStyleItemContext);\n  var _useContext3 = useContext(FieldContext),\n    contextValidateTrigger = _useContext3.validateTrigger;\n  var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;\n  var hasName = hasValidName(name);\n  var prefixCls = getPrefixCls('form', customizePrefixCls); // ========================= MISC =========================\n  // Get `noStyle` required info\n\n  var listContext = React.useContext(ListContext);\n  var fieldKeyPathRef = React.useRef(); // ======================== Errors ========================\n  // >>>>> Collect sub field errors\n\n  var _useFrameState = useFrameState({}),\n    _useFrameState2 = _slicedToArray(_useFrameState, 2),\n    subFieldErrors = _useFrameState2[0],\n    setSubFieldErrors = _useFrameState2[1]; // >>>>> Current field errors\n\n  var _useState = useState(function () {\n      return genEmptyMeta();\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    meta = _useState2[0],\n    setMeta = _useState2[1];\n  var onMetaChange = function onMetaChange(nextMeta) {\n    // This keyInfo is not correct when field is removed\n    // Since origin keyManager no longer keep the origin key anymore\n    // Which means we need cache origin one and reuse when removed\n    var keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name); // Destroy will reset all the meta\n\n    setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true); // Bump to parent since noStyle\n\n    if (noStyle && notifyParentMetaChange) {\n      var namePath = nextMeta.name;\n      if (!nextMeta.destroy) {\n        if (keyInfo !== undefined) {\n          var _keyInfo = _slicedToArray(keyInfo, 2),\n            fieldKey = _keyInfo[0],\n            restPath = _keyInfo[1];\n          namePath = [fieldKey].concat(_toConsumableArray(restPath));\n          fieldKeyPathRef.current = namePath;\n        }\n      } else {\n        // Use origin cache data\n        namePath = fieldKeyPathRef.current || namePath;\n      }\n      notifyParentMetaChange(nextMeta, namePath);\n    }\n  }; // >>>>> Collect noStyle Field error to the top FormItem\n\n  var onSubItemMetaChange = function onSubItemMetaChange(subMeta, uniqueKeys) {\n    // Only `noStyle` sub item will trigger\n    setSubFieldErrors(function (prevSubFieldErrors) {\n      var clone = _extends({}, prevSubFieldErrors); // name: ['user', 1] + key: [4] = ['user', 4]\n\n      var mergedNamePath = [].concat(_toConsumableArray(subMeta.name.slice(0, -1)), _toConsumableArray(uniqueKeys));\n      var mergedNameKey = mergedNamePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        // Remove\n        delete clone[mergedNameKey];\n      } else {\n        // Update\n        clone[mergedNameKey] = subMeta;\n      }\n      return clone;\n    });\n  }; // >>>>> Get merged errors\n\n  var _React$useMemo = React.useMemo(function () {\n      var errorList = _toConsumableArray(meta.errors);\n      var warningList = _toConsumableArray(meta.warnings);\n      Object.values(subFieldErrors).forEach(function (subFieldError) {\n        errorList.push.apply(errorList, _toConsumableArray(subFieldError.errors || []));\n        warningList.push.apply(warningList, _toConsumableArray(subFieldError.warnings || []));\n      });\n      return [errorList, warningList];\n    }, [subFieldErrors, meta.errors, meta.warnings]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    mergedErrors = _React$useMemo2[0],\n    mergedWarnings = _React$useMemo2[1];\n  var debounceErrors = useDebounce(mergedErrors);\n  var debounceWarnings = useDebounce(mergedWarnings); // ===================== Children Ref =====================\n\n  var getItemRef = useItemRef(); // ======================== Status ========================\n\n  var mergedValidateStatus = '';\n  if (validateStatus !== undefined) {\n    mergedValidateStatus = validateStatus;\n  } else if (meta === null || meta === void 0 ? void 0 : meta.validating) {\n    mergedValidateStatus = 'validating';\n  } else if (debounceErrors.length) {\n    mergedValidateStatus = 'error';\n  } else if (debounceWarnings.length) {\n    mergedValidateStatus = 'warning';\n  } else if (meta === null || meta === void 0 ? void 0 : meta.touched) {\n    mergedValidateStatus = 'success';\n  }\n  var formItemStatusContext = useMemo(function () {\n    var feedbackIcon;\n    if (hasFeedback) {\n      var IconNode = mergedValidateStatus && iconMap[mergedValidateStatus];\n      feedbackIcon = IconNode ? /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-item-feedback-icon\"), \"\".concat(prefixCls, \"-item-feedback-icon-\").concat(mergedValidateStatus))\n      }, /*#__PURE__*/React.createElement(IconNode, null)) : null;\n    }\n    return {\n      status: mergedValidateStatus,\n      hasFeedback: hasFeedback,\n      feedbackIcon: feedbackIcon,\n      isFormItemInput: true\n    };\n  }, [mergedValidateStatus, hasFeedback]); // ======================== Render ========================\n\n  function renderLayout(baseChildren, fieldId, isRequired) {\n    var _itemClassName;\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n    var itemClassName = (_itemClassName = {}, _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-with-help\"), help !== undefined && help !== null || debounceErrors.length || debounceWarnings.length), _defineProperty(_itemClassName, \"\".concat(className), !!className), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-feedback\"), mergedValidateStatus && hasFeedback), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-success\"), mergedValidateStatus === 'success'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-warning\"), mergedValidateStatus === 'warning'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-error\"), mergedValidateStatus === 'error'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-is-validating\"), mergedValidateStatus === 'validating'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-hidden\"), hidden), _itemClassName); // ======================= Children =======================\n\n    return /*#__PURE__*/React.createElement(Row, _extends({\n      className: classNames(itemClassName),\n      style: style,\n      key: \"row\"\n    }, omit(restProps, ['colon', 'extra', 'fieldKey', 'requiredMark', 'getValueFromEvent', 'getValueProps', 'htmlFor', 'id', 'initialValue', 'isListField', 'labelAlign', 'labelWrap', 'labelCol', 'normalize', 'preserve', 'tooltip', 'validateFirst', 'valuePropName', 'wrapperCol', '_internalItemRender'])), /*#__PURE__*/React.createElement(FormItemLabel, _extends({\n      htmlFor: fieldId,\n      required: isRequired,\n      requiredMark: requiredMark\n    }, props, {\n      prefixCls: prefixCls\n    })), /*#__PURE__*/React.createElement(FormItemInput, _extends({}, props, meta, {\n      errors: debounceErrors,\n      warnings: debounceWarnings,\n      prefixCls: prefixCls,\n      status: mergedValidateStatus,\n      help: help\n    }), /*#__PURE__*/React.createElement(NoStyleItemContext.Provider, {\n      value: onSubItemMetaChange\n    }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n      value: formItemStatusContext\n    }, baseChildren))));\n  }\n  if (!hasName && !isRenderProps && !dependencies) {\n    return renderLayout(children);\n  }\n  var variables = {};\n  if (typeof label === 'string') {\n    variables.label = label;\n  } else if (name) {\n    variables.label = String(name);\n  }\n  if (messageVariables) {\n    variables = _extends(_extends({}, variables), messageVariables);\n  } // >>>>> With Field\n\n  return /*#__PURE__*/React.createElement(Field, _extends({}, props, {\n    messageVariables: variables,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange\n  }), function (control, renderMeta, context) {\n    var mergedName = toArray(name).length && renderMeta ? renderMeta.name : [];\n    var fieldId = getFieldId(mergedName, formName);\n    var isRequired = required !== undefined ? required : !!(rules && rules.some(function (rule) {\n      if (rule && _typeof(rule) === 'object' && rule.required && !rule.warningOnly) {\n        return true;\n      }\n      if (typeof rule === 'function') {\n        var ruleEntity = rule(context);\n        return ruleEntity && ruleEntity.required && !ruleEntity.warningOnly;\n      }\n      return false;\n    })); // ======================= Children =======================\n\n    var mergedControl = _extends({}, control);\n    var childNode = null;\n    devWarning(!(shouldUpdate && dependencies), 'Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together. See https://ant.design/components/form/#dependencies.\");\n    if (Array.isArray(children) && hasName) {\n      devWarning(false, 'Form.Item', '`children` is array of render props cannot have `name`.');\n      childNode = children;\n    } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {\n      devWarning(!!(shouldUpdate || dependencies), 'Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.');\n      devWarning(!hasName, 'Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\");\n    } else if (dependencies && !isRenderProps && !hasName) {\n      devWarning(false, 'Form.Item', 'Must set `name` or use render props when `dependencies` is set.');\n    } else if (isValidElement(children)) {\n      devWarning(children.props.defaultValue === undefined, 'Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.');\n      var childProps = _extends(_extends({}, children.props), mergedControl);\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      if (supportRef(children)) {\n        childProps.ref = getItemRef(mergedName, children);\n      } // We should keep user origin event handler\n\n      var triggers = new Set([].concat(_toConsumableArray(toArray(trigger)), _toConsumableArray(toArray(mergedValidateTrigger))));\n      triggers.forEach(function (eventName) {\n        childProps[eventName] = function () {\n          var _a2, _c2;\n          var _a, _b, _c;\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [mergedControl].concat(args));\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [_b].concat(args));\n        };\n      });\n      childNode = /*#__PURE__*/React.createElement(MemoInput, {\n        value: mergedControl[props.valuePropName || 'value'],\n        update: children\n      }, cloneElement(children, childProps));\n    } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {\n      childNode = children(context);\n    } else {\n      devWarning(!mergedName.length, 'Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.');\n      childNode = children;\n    }\n    return renderLayout(childNode, fieldId, isRequired);\n  });\n}\nexport default FormItem;", "map": {"version": 3, "names": ["_typeof", "_defineProperty", "_extends", "_toConsumableArray", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useContext", "useMemo", "classNames", "Field", "FieldContext", "ListContext", "supportRef", "useState", "omit", "CheckCircleFilled", "ExclamationCircleFilled", "CloseCircleFilled", "LoadingOutlined", "Row", "ConfigContext", "tuple", "dev<PERSON><PERSON><PERSON>", "FormItemLabel", "FormItemInput", "FormContext", "FormItemInputContext", "NoStyleItemContext", "toArray", "getFieldId", "cloneElement", "isValidElement", "useFrameState", "useDebounce", "useItemRef", "NAME_SPLIT", "ValidateStatuses", "MemoInput", "memo", "_ref", "children", "prev", "next", "value", "update", "hasValidName", "name", "undefined", "genEmptyMeta", "errors", "warnings", "touched", "validating", "iconMap", "success", "warning", "error", "FormItem", "props", "noStyle", "dependencies", "customizePrefixCls", "prefixCls", "style", "className", "shouldUpdate", "hasFeedback", "help", "rules", "validateStatus", "required", "label", "messageVariables", "_props$trigger", "trigger", "validate<PERSON><PERSON>ger", "hidden", "restProps", "_useContext", "getPrefixCls", "_useContext2", "formName", "requiredMark", "isRenderProps", "notifyParentMetaChange", "_useContext3", "contextValidateTrigger", "mergedValidateTrigger", "<PERSON><PERSON><PERSON>", "listContext", "fieldKeyPathRef", "useRef", "_useFrameState", "_useFrameState2", "subFieldErrors", "setSubFieldErrors", "_useState", "_useState2", "meta", "setMeta", "onMetaChange", "nextMeta", "keyInfo", "<PERSON><PERSON><PERSON>", "destroy", "namePath", "_keyInfo", "<PERSON><PERSON><PERSON>", "restPath", "concat", "current", "onSubItemMetaChange", "subMeta", "uniqueKeys", "prevSubFieldErrors", "clone", "mergedNamePath", "slice", "mergedNameKey", "join", "_React$useMemo", "errorList", "warningList", "values", "for<PERSON>ach", "subFieldError", "push", "apply", "_React$useMemo2", "mergedErrors", "mergedWarnings", "debounceErrors", "debounce<PERSON><PERSON><PERSON>s", "getItemRef", "mergedValidateStatus", "formItemStatusContext", "feedbackIcon", "IconNode", "createElement", "status", "isFormItemInput", "renderLayout", "baseChildren", "fieldId", "isRequired", "_itemClassName", "itemClassName", "key", "htmlFor", "Provider", "variables", "String", "control", "renderMeta", "context", "mergedName", "some", "rule", "warningOnly", "ruleEntity", "mergedControl", "childNode", "Array", "isArray", "defaultValue", "childProps", "id", "ref", "triggers", "Set", "eventName", "_a2", "_c2", "_a", "_b", "_c", "_len", "arguments", "args", "_key", "valuePropName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/form/FormItem.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport classNames from 'classnames';\nimport { Field, FieldContext, ListContext } from 'rc-field-form';\nimport { supportRef } from \"rc-util/es/ref\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport omit from \"rc-util/es/omit\";\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport Row from '../grid/row';\nimport { ConfigContext } from '../config-provider';\nimport { tuple } from '../_util/type';\nimport devWarning from '../_util/devWarning';\nimport FormItemLabel from './FormItemLabel';\nimport FormItemInput from './FormItemInput';\nimport { FormContext, FormItemInputContext, NoStyleItemContext } from './context';\nimport { toArray, getFieldId } from './util';\nimport { cloneElement, isValidElement } from '../_util/reactNode';\nimport useFrameState from './hooks/useFrameState';\nimport useDebounce from './hooks/useDebounce';\nimport useItemRef from './hooks/useItemRef';\nvar NAME_SPLIT = '__SPLIT__';\nvar ValidateStatuses = tuple('success', 'warning', 'error', 'validating', '');\nvar MemoInput = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  return prev.value === next.value && prev.update === next.update;\n});\n\nfunction hasValidName(name) {\n  if (name === null) {\n    devWarning(false, 'Form.Item', '`null` is passed as `name` property');\n  }\n\n  return !(name === undefined || name === null);\n}\n\nfunction genEmptyMeta() {\n  return {\n    errors: [],\n    warnings: [],\n    touched: false,\n    validating: false,\n    name: []\n  };\n}\n\nvar iconMap = {\n  success: CheckCircleFilled,\n  warning: ExclamationCircleFilled,\n  error: CloseCircleFilled,\n  validating: LoadingOutlined\n};\n\nfunction FormItem(props) {\n  var name = props.name,\n      noStyle = props.noStyle,\n      dependencies = props.dependencies,\n      customizePrefixCls = props.prefixCls,\n      style = props.style,\n      className = props.className,\n      shouldUpdate = props.shouldUpdate,\n      hasFeedback = props.hasFeedback,\n      help = props.help,\n      rules = props.rules,\n      validateStatus = props.validateStatus,\n      children = props.children,\n      required = props.required,\n      label = props.label,\n      messageVariables = props.messageVariables,\n      _props$trigger = props.trigger,\n      trigger = _props$trigger === void 0 ? 'onChange' : _props$trigger,\n      validateTrigger = props.validateTrigger,\n      hidden = props.hidden,\n      restProps = __rest(props, [\"name\", \"noStyle\", \"dependencies\", \"prefixCls\", \"style\", \"className\", \"shouldUpdate\", \"hasFeedback\", \"help\", \"rules\", \"validateStatus\", \"children\", \"required\", \"label\", \"messageVariables\", \"trigger\", \"validateTrigger\", \"hidden\"]);\n\n  var _useContext = useContext(ConfigContext),\n      getPrefixCls = _useContext.getPrefixCls;\n\n  var _useContext2 = useContext(FormContext),\n      formName = _useContext2.name,\n      requiredMark = _useContext2.requiredMark;\n\n  var isRenderProps = typeof children === 'function';\n  var notifyParentMetaChange = useContext(NoStyleItemContext);\n\n  var _useContext3 = useContext(FieldContext),\n      contextValidateTrigger = _useContext3.validateTrigger;\n\n  var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;\n  var hasName = hasValidName(name);\n  var prefixCls = getPrefixCls('form', customizePrefixCls); // ========================= MISC =========================\n  // Get `noStyle` required info\n\n  var listContext = React.useContext(ListContext);\n  var fieldKeyPathRef = React.useRef(); // ======================== Errors ========================\n  // >>>>> Collect sub field errors\n\n  var _useFrameState = useFrameState({}),\n      _useFrameState2 = _slicedToArray(_useFrameState, 2),\n      subFieldErrors = _useFrameState2[0],\n      setSubFieldErrors = _useFrameState2[1]; // >>>>> Current field errors\n\n\n  var _useState = useState(function () {\n    return genEmptyMeta();\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      meta = _useState2[0],\n      setMeta = _useState2[1];\n\n  var onMetaChange = function onMetaChange(nextMeta) {\n    // This keyInfo is not correct when field is removed\n    // Since origin keyManager no longer keep the origin key anymore\n    // Which means we need cache origin one and reuse when removed\n    var keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name); // Destroy will reset all the meta\n\n    setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true); // Bump to parent since noStyle\n\n    if (noStyle && notifyParentMetaChange) {\n      var namePath = nextMeta.name;\n\n      if (!nextMeta.destroy) {\n        if (keyInfo !== undefined) {\n          var _keyInfo = _slicedToArray(keyInfo, 2),\n              fieldKey = _keyInfo[0],\n              restPath = _keyInfo[1];\n\n          namePath = [fieldKey].concat(_toConsumableArray(restPath));\n          fieldKeyPathRef.current = namePath;\n        }\n      } else {\n        // Use origin cache data\n        namePath = fieldKeyPathRef.current || namePath;\n      }\n\n      notifyParentMetaChange(nextMeta, namePath);\n    }\n  }; // >>>>> Collect noStyle Field error to the top FormItem\n\n\n  var onSubItemMetaChange = function onSubItemMetaChange(subMeta, uniqueKeys) {\n    // Only `noStyle` sub item will trigger\n    setSubFieldErrors(function (prevSubFieldErrors) {\n      var clone = _extends({}, prevSubFieldErrors); // name: ['user', 1] + key: [4] = ['user', 4]\n\n\n      var mergedNamePath = [].concat(_toConsumableArray(subMeta.name.slice(0, -1)), _toConsumableArray(uniqueKeys));\n      var mergedNameKey = mergedNamePath.join(NAME_SPLIT);\n\n      if (subMeta.destroy) {\n        // Remove\n        delete clone[mergedNameKey];\n      } else {\n        // Update\n        clone[mergedNameKey] = subMeta;\n      }\n\n      return clone;\n    });\n  }; // >>>>> Get merged errors\n\n\n  var _React$useMemo = React.useMemo(function () {\n    var errorList = _toConsumableArray(meta.errors);\n\n    var warningList = _toConsumableArray(meta.warnings);\n\n    Object.values(subFieldErrors).forEach(function (subFieldError) {\n      errorList.push.apply(errorList, _toConsumableArray(subFieldError.errors || []));\n      warningList.push.apply(warningList, _toConsumableArray(subFieldError.warnings || []));\n    });\n    return [errorList, warningList];\n  }, [subFieldErrors, meta.errors, meta.warnings]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      mergedErrors = _React$useMemo2[0],\n      mergedWarnings = _React$useMemo2[1];\n\n  var debounceErrors = useDebounce(mergedErrors);\n  var debounceWarnings = useDebounce(mergedWarnings); // ===================== Children Ref =====================\n\n  var getItemRef = useItemRef(); // ======================== Status ========================\n\n  var mergedValidateStatus = '';\n\n  if (validateStatus !== undefined) {\n    mergedValidateStatus = validateStatus;\n  } else if (meta === null || meta === void 0 ? void 0 : meta.validating) {\n    mergedValidateStatus = 'validating';\n  } else if (debounceErrors.length) {\n    mergedValidateStatus = 'error';\n  } else if (debounceWarnings.length) {\n    mergedValidateStatus = 'warning';\n  } else if (meta === null || meta === void 0 ? void 0 : meta.touched) {\n    mergedValidateStatus = 'success';\n  }\n\n  var formItemStatusContext = useMemo(function () {\n    var feedbackIcon;\n\n    if (hasFeedback) {\n      var IconNode = mergedValidateStatus && iconMap[mergedValidateStatus];\n      feedbackIcon = IconNode ? /*#__PURE__*/React.createElement(\"span\", {\n        className: classNames(\"\".concat(prefixCls, \"-item-feedback-icon\"), \"\".concat(prefixCls, \"-item-feedback-icon-\").concat(mergedValidateStatus))\n      }, /*#__PURE__*/React.createElement(IconNode, null)) : null;\n    }\n\n    return {\n      status: mergedValidateStatus,\n      hasFeedback: hasFeedback,\n      feedbackIcon: feedbackIcon,\n      isFormItemInput: true\n    };\n  }, [mergedValidateStatus, hasFeedback]); // ======================== Render ========================\n\n  function renderLayout(baseChildren, fieldId, isRequired) {\n    var _itemClassName;\n\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n\n    var itemClassName = (_itemClassName = {}, _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-with-help\"), help !== undefined && help !== null || debounceErrors.length || debounceWarnings.length), _defineProperty(_itemClassName, \"\".concat(className), !!className), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-feedback\"), mergedValidateStatus && hasFeedback), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-success\"), mergedValidateStatus === 'success'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-warning\"), mergedValidateStatus === 'warning'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-has-error\"), mergedValidateStatus === 'error'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-is-validating\"), mergedValidateStatus === 'validating'), _defineProperty(_itemClassName, \"\".concat(prefixCls, \"-item-hidden\"), hidden), _itemClassName); // ======================= Children =======================\n\n    return /*#__PURE__*/React.createElement(Row, _extends({\n      className: classNames(itemClassName),\n      style: style,\n      key: \"row\"\n    }, omit(restProps, ['colon', 'extra', 'fieldKey', 'requiredMark', 'getValueFromEvent', 'getValueProps', 'htmlFor', 'id', 'initialValue', 'isListField', 'labelAlign', 'labelWrap', 'labelCol', 'normalize', 'preserve', 'tooltip', 'validateFirst', 'valuePropName', 'wrapperCol', '_internalItemRender'])), /*#__PURE__*/React.createElement(FormItemLabel, _extends({\n      htmlFor: fieldId,\n      required: isRequired,\n      requiredMark: requiredMark\n    }, props, {\n      prefixCls: prefixCls\n    })), /*#__PURE__*/React.createElement(FormItemInput, _extends({}, props, meta, {\n      errors: debounceErrors,\n      warnings: debounceWarnings,\n      prefixCls: prefixCls,\n      status: mergedValidateStatus,\n      help: help\n    }), /*#__PURE__*/React.createElement(NoStyleItemContext.Provider, {\n      value: onSubItemMetaChange\n    }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n      value: formItemStatusContext\n    }, baseChildren))));\n  }\n\n  if (!hasName && !isRenderProps && !dependencies) {\n    return renderLayout(children);\n  }\n\n  var variables = {};\n\n  if (typeof label === 'string') {\n    variables.label = label;\n  } else if (name) {\n    variables.label = String(name);\n  }\n\n  if (messageVariables) {\n    variables = _extends(_extends({}, variables), messageVariables);\n  } // >>>>> With Field\n\n\n  return /*#__PURE__*/React.createElement(Field, _extends({}, props, {\n    messageVariables: variables,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange\n  }), function (control, renderMeta, context) {\n    var mergedName = toArray(name).length && renderMeta ? renderMeta.name : [];\n    var fieldId = getFieldId(mergedName, formName);\n    var isRequired = required !== undefined ? required : !!(rules && rules.some(function (rule) {\n      if (rule && _typeof(rule) === 'object' && rule.required && !rule.warningOnly) {\n        return true;\n      }\n\n      if (typeof rule === 'function') {\n        var ruleEntity = rule(context);\n        return ruleEntity && ruleEntity.required && !ruleEntity.warningOnly;\n      }\n\n      return false;\n    })); // ======================= Children =======================\n\n    var mergedControl = _extends({}, control);\n\n    var childNode = null;\n    devWarning(!(shouldUpdate && dependencies), 'Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together. See https://ant.design/components/form/#dependencies.\");\n\n    if (Array.isArray(children) && hasName) {\n      devWarning(false, 'Form.Item', '`children` is array of render props cannot have `name`.');\n      childNode = children;\n    } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {\n      devWarning(!!(shouldUpdate || dependencies), 'Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.');\n      devWarning(!hasName, 'Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\");\n    } else if (dependencies && !isRenderProps && !hasName) {\n      devWarning(false, 'Form.Item', 'Must set `name` or use render props when `dependencies` is set.');\n    } else if (isValidElement(children)) {\n      devWarning(children.props.defaultValue === undefined, 'Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.');\n\n      var childProps = _extends(_extends({}, children.props), mergedControl);\n\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n\n      if (supportRef(children)) {\n        childProps.ref = getItemRef(mergedName, children);\n      } // We should keep user origin event handler\n\n\n      var triggers = new Set([].concat(_toConsumableArray(toArray(trigger)), _toConsumableArray(toArray(mergedValidateTrigger))));\n      triggers.forEach(function (eventName) {\n        childProps[eventName] = function () {\n          var _a2, _c2;\n\n          var _a, _b, _c;\n\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n\n          (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [mergedControl].concat(args));\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [_b].concat(args));\n        };\n      });\n      childNode = /*#__PURE__*/React.createElement(MemoInput, {\n        value: mergedControl[props.valuePropName || 'value'],\n        update: children\n      }, cloneElement(children, childProps));\n    } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {\n      childNode = children(context);\n    } else {\n      devWarning(!mergedName.length, 'Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.');\n      childNode = children;\n    }\n\n    return renderLayout(childNode, fieldId, isRequired);\n  });\n}\n\nexport default FormItem;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AAErE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,EAAEC,YAAY,EAAEC,WAAW,QAAQ,eAAe;AAChE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,uBAAuB,MAAM,oDAAoD;AACxF,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,WAAW,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,WAAW;AACjF,SAASC,OAAO,EAAEC,UAAU,QAAQ,QAAQ;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AACjE,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,IAAIC,UAAU,GAAG,WAAW;AAC5B,IAAIC,gBAAgB,GAAGf,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;AAC7E,IAAIgB,SAAS,GAAG,aAAahC,KAAK,CAACiC,IAAI,CAAC,UAAUC,IAAI,EAAE;EACtD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,IAAI,EAAEC,IAAI,EAAE;EACvB,OAAOD,IAAI,CAACE,KAAK,KAAKD,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACG,MAAM,KAAKF,IAAI,CAACE,MAAM;AACjE,CAAC,CAAC;AAEF,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjBxB,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,qCAAqC,CAAC;EACvE;EAEA,OAAO,EAAEwB,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,IAAI,CAAC;AAC/C;AAEA,SAASE,YAAYA,CAAA,EAAG;EACtB,OAAO;IACLC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBN,IAAI,EAAE;EACR,CAAC;AACH;AAEA,IAAIO,OAAO,GAAG;EACZC,OAAO,EAAEvC,iBAAiB;EAC1BwC,OAAO,EAAEvC,uBAAuB;EAChCwC,KAAK,EAAEvC,iBAAiB;EACxBmC,UAAU,EAAElC;AACd,CAAC;AAED,SAASuC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIZ,IAAI,GAAGY,KAAK,CAACZ,IAAI;IACjBa,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,kBAAkB,GAAGH,KAAK,CAACI,SAAS;IACpCC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,cAAc,GAAGX,KAAK,CAACW,cAAc;IACrC7B,QAAQ,GAAGkB,KAAK,CAAClB,QAAQ;IACzB8B,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,gBAAgB,GAAGd,KAAK,CAACc,gBAAgB;IACzCC,cAAc,GAAGf,KAAK,CAACgB,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,cAAc;IACjEE,eAAe,GAAGjB,KAAK,CAACiB,eAAe;IACvCC,MAAM,GAAGlB,KAAK,CAACkB,MAAM;IACrBC,SAAS,GAAGtF,MAAM,CAACmE,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;EAEpQ,IAAIoB,WAAW,GAAGxE,UAAU,CAACc,aAAa,CAAC;IACvC2D,YAAY,GAAGD,WAAW,CAACC,YAAY;EAE3C,IAAIC,YAAY,GAAG1E,UAAU,CAACmB,WAAW,CAAC;IACtCwD,QAAQ,GAAGD,YAAY,CAAClC,IAAI;IAC5BoC,YAAY,GAAGF,YAAY,CAACE,YAAY;EAE5C,IAAIC,aAAa,GAAG,OAAO3C,QAAQ,KAAK,UAAU;EAClD,IAAI4C,sBAAsB,GAAG9E,UAAU,CAACqB,kBAAkB,CAAC;EAE3D,IAAI0D,YAAY,GAAG/E,UAAU,CAACI,YAAY,CAAC;IACvC4E,sBAAsB,GAAGD,YAAY,CAACV,eAAe;EAEzD,IAAIY,qBAAqB,GAAGZ,eAAe,KAAK5B,SAAS,GAAG4B,eAAe,GAAGW,sBAAsB;EACpG,IAAIE,OAAO,GAAG3C,YAAY,CAACC,IAAI,CAAC;EAChC,IAAIgB,SAAS,GAAGiB,YAAY,CAAC,MAAM,EAAElB,kBAAkB,CAAC,CAAC,CAAC;EAC1D;;EAEA,IAAI4B,WAAW,GAAGpF,KAAK,CAACC,UAAU,CAACK,WAAW,CAAC;EAC/C,IAAI+E,eAAe,GAAGrF,KAAK,CAACsF,MAAM,CAAC,CAAC,CAAC,CAAC;EACtC;;EAEA,IAAIC,cAAc,GAAG5D,aAAa,CAAC,CAAC,CAAC,CAAC;IAClC6D,eAAe,GAAGvG,cAAc,CAACsG,cAAc,EAAE,CAAC,CAAC;IACnDE,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;IACnCE,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,SAAS,GAAGnF,QAAQ,CAAC,YAAY;MACnC,OAAOmC,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;IACEiD,UAAU,GAAG3G,cAAc,CAAC0G,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACC,QAAQ,EAAE;IACjD;IACA;IACA;IACA,IAAIC,OAAO,GAAGb,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACc,MAAM,CAACF,QAAQ,CAACvD,IAAI,CAAC,CAAC,CAAC;;IAE3GqD,OAAO,CAACE,QAAQ,CAACG,OAAO,GAAGxD,YAAY,CAAC,CAAC,GAAGqD,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE7D,IAAI1C,OAAO,IAAIyB,sBAAsB,EAAE;MACrC,IAAIqB,QAAQ,GAAGJ,QAAQ,CAACvD,IAAI;MAE5B,IAAI,CAACuD,QAAQ,CAACG,OAAO,EAAE;QACrB,IAAIF,OAAO,KAAKvD,SAAS,EAAE;UACzB,IAAI2D,QAAQ,GAAGpH,cAAc,CAACgH,OAAO,EAAE,CAAC,CAAC;YACrCK,QAAQ,GAAGD,QAAQ,CAAC,CAAC,CAAC;YACtBE,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;UAE1BD,QAAQ,GAAG,CAACE,QAAQ,CAAC,CAACE,MAAM,CAACxH,kBAAkB,CAACuH,QAAQ,CAAC,CAAC;UAC1DlB,eAAe,CAACoB,OAAO,GAAGL,QAAQ;QACpC;MACF,CAAC,MAAM;QACL;QACAA,QAAQ,GAAGf,eAAe,CAACoB,OAAO,IAAIL,QAAQ;MAChD;MAEArB,sBAAsB,CAACiB,QAAQ,EAAEI,QAAQ,CAAC;IAC5C;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIM,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAEC,UAAU,EAAE;IAC1E;IACAlB,iBAAiB,CAAC,UAAUmB,kBAAkB,EAAE;MAC9C,IAAIC,KAAK,GAAG/H,QAAQ,CAAC,CAAC,CAAC,EAAE8H,kBAAkB,CAAC,CAAC,CAAC;;MAG9C,IAAIE,cAAc,GAAG,EAAE,CAACP,MAAM,CAACxH,kBAAkB,CAAC2H,OAAO,CAAClE,IAAI,CAACuE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEhI,kBAAkB,CAAC4H,UAAU,CAAC,CAAC;MAC7G,IAAIK,aAAa,GAAGF,cAAc,CAACG,IAAI,CAACpF,UAAU,CAAC;MAEnD,IAAI6E,OAAO,CAACR,OAAO,EAAE;QACnB;QACA,OAAOW,KAAK,CAACG,aAAa,CAAC;MAC7B,CAAC,MAAM;QACL;QACAH,KAAK,CAACG,aAAa,CAAC,GAAGN,OAAO;MAChC;MAEA,OAAOG,KAAK;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGH,IAAIK,cAAc,GAAGnH,KAAK,CAACE,OAAO,CAAC,YAAY;MAC7C,IAAIkH,SAAS,GAAGpI,kBAAkB,CAAC6G,IAAI,CAACjD,MAAM,CAAC;MAE/C,IAAIyE,WAAW,GAAGrI,kBAAkB,CAAC6G,IAAI,CAAChD,QAAQ,CAAC;MAEnDtD,MAAM,CAAC+H,MAAM,CAAC7B,cAAc,CAAC,CAAC8B,OAAO,CAAC,UAAUC,aAAa,EAAE;QAC7DJ,SAAS,CAACK,IAAI,CAACC,KAAK,CAACN,SAAS,EAAEpI,kBAAkB,CAACwI,aAAa,CAAC5E,MAAM,IAAI,EAAE,CAAC,CAAC;QAC/EyE,WAAW,CAACI,IAAI,CAACC,KAAK,CAACL,WAAW,EAAErI,kBAAkB,CAACwI,aAAa,CAAC3E,QAAQ,IAAI,EAAE,CAAC,CAAC;MACvF,CAAC,CAAC;MACF,OAAO,CAACuE,SAAS,EAAEC,WAAW,CAAC;IACjC,CAAC,EAAE,CAAC5B,cAAc,EAAEI,IAAI,CAACjD,MAAM,EAAEiD,IAAI,CAAChD,QAAQ,CAAC,CAAC;IAC5C8E,eAAe,GAAG1I,cAAc,CAACkI,cAAc,EAAE,CAAC,CAAC;IACnDS,YAAY,GAAGD,eAAe,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,eAAe,CAAC,CAAC,CAAC;EAEvC,IAAIG,cAAc,GAAGlG,WAAW,CAACgG,YAAY,CAAC;EAC9C,IAAIG,gBAAgB,GAAGnG,WAAW,CAACiG,cAAc,CAAC,CAAC,CAAC;;EAEpD,IAAIG,UAAU,GAAGnG,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIoG,oBAAoB,GAAG,EAAE;EAE7B,IAAIjE,cAAc,KAAKtB,SAAS,EAAE;IAChCuF,oBAAoB,GAAGjE,cAAc;EACvC,CAAC,MAAM,IAAI6B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC9C,UAAU,EAAE;IACtEkF,oBAAoB,GAAG,YAAY;EACrC,CAAC,MAAM,IAAIH,cAAc,CAAChI,MAAM,EAAE;IAChCmI,oBAAoB,GAAG,OAAO;EAChC,CAAC,MAAM,IAAIF,gBAAgB,CAACjI,MAAM,EAAE;IAClCmI,oBAAoB,GAAG,SAAS;EAClC,CAAC,MAAM,IAAIpC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC/C,OAAO,EAAE;IACnEmF,oBAAoB,GAAG,SAAS;EAClC;EAEA,IAAIC,qBAAqB,GAAGhI,OAAO,CAAC,YAAY;IAC9C,IAAIiI,YAAY;IAEhB,IAAItE,WAAW,EAAE;MACf,IAAIuE,QAAQ,GAAGH,oBAAoB,IAAIjF,OAAO,CAACiF,oBAAoB,CAAC;MACpEE,YAAY,GAAGC,QAAQ,GAAG,aAAapI,KAAK,CAACqI,aAAa,CAAC,MAAM,EAAE;QACjE1E,SAAS,EAAExD,UAAU,CAAC,EAAE,CAACqG,MAAM,CAAC/C,SAAS,EAAE,qBAAqB,CAAC,EAAE,EAAE,CAAC+C,MAAM,CAAC/C,SAAS,EAAE,sBAAsB,CAAC,CAAC+C,MAAM,CAACyB,oBAAoB,CAAC;MAC9I,CAAC,EAAE,aAAajI,KAAK,CAACqI,aAAa,CAACD,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI;IAC7D;IAEA,OAAO;MACLE,MAAM,EAAEL,oBAAoB;MAC5BpE,WAAW,EAAEA,WAAW;MACxBsE,YAAY,EAAEA,YAAY;MAC1BI,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACN,oBAAoB,EAAEpE,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEzC,SAAS2E,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAE;IACvD,IAAIC,cAAc;IAElB,IAAItF,OAAO,IAAI,CAACiB,MAAM,EAAE;MACtB,OAAOkE,YAAY;IACrB;IAEA,IAAII,aAAa,IAAID,cAAc,GAAG,CAAC,CAAC,EAAE9J,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE3E,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,iBAAiB,CAAC,EAAEK,IAAI,KAAKpB,SAAS,IAAIoB,IAAI,KAAK,IAAI,IAAIgE,cAAc,CAAChI,MAAM,IAAIiI,gBAAgB,CAACjI,MAAM,CAAC,EAAEhB,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC7C,SAAS,CAAC,EAAE,CAAC,CAACA,SAAS,CAAC,EAAE7E,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,oBAAoB,CAAC,EAAEwE,oBAAoB,IAAIpE,WAAW,CAAC,EAAE/E,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,mBAAmB,CAAC,EAAEwE,oBAAoB,KAAK,SAAS,CAAC,EAAEnJ,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,mBAAmB,CAAC,EAAEwE,oBAAoB,KAAK,SAAS,CAAC,EAAEnJ,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,iBAAiB,CAAC,EAAEwE,oBAAoB,KAAK,OAAO,CAAC,EAAEnJ,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,qBAAqB,CAAC,EAAEwE,oBAAoB,KAAK,YAAY,CAAC,EAAEnJ,eAAe,CAAC8J,cAAc,EAAE,EAAE,CAACpC,MAAM,CAAC/C,SAAS,EAAE,cAAc,CAAC,EAAEc,MAAM,CAAC,EAAEqE,cAAc,CAAC,CAAC,CAAC;;IAE1+B,OAAO,aAAa5I,KAAK,CAACqI,aAAa,CAACvH,GAAG,EAAE/B,QAAQ,CAAC;MACpD4E,SAAS,EAAExD,UAAU,CAAC0I,aAAa,CAAC;MACpCnF,KAAK,EAAEA,KAAK;MACZoF,GAAG,EAAE;IACP,CAAC,EAAErI,IAAI,CAAC+D,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,mBAAmB,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,aAAaxE,KAAK,CAACqI,aAAa,CAACnH,aAAa,EAAEnC,QAAQ,CAAC;MACpWgK,OAAO,EAAEL,OAAO;MAChBzE,QAAQ,EAAE0E,UAAU;MACpB9D,YAAY,EAAEA;IAChB,CAAC,EAAExB,KAAK,EAAE;MACRI,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC,EAAE,aAAazD,KAAK,CAACqI,aAAa,CAAClH,aAAa,EAAEpC,QAAQ,CAAC,CAAC,CAAC,EAAEsE,KAAK,EAAEwC,IAAI,EAAE;MAC7EjD,MAAM,EAAEkF,cAAc;MACtBjF,QAAQ,EAAEkF,gBAAgB;MAC1BtE,SAAS,EAAEA,SAAS;MACpB6E,MAAM,EAAEL,oBAAoB;MAC5BnE,IAAI,EAAEA;IACR,CAAC,CAAC,EAAE,aAAa9D,KAAK,CAACqI,aAAa,CAAC/G,kBAAkB,CAAC0H,QAAQ,EAAE;MAChE1G,KAAK,EAAEoE;IACT,CAAC,EAAE,aAAa1G,KAAK,CAACqI,aAAa,CAAChH,oBAAoB,CAAC2H,QAAQ,EAAE;MACjE1G,KAAK,EAAE4F;IACT,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,CAAC;EACrB;EAEA,IAAI,CAACtD,OAAO,IAAI,CAACL,aAAa,IAAI,CAACvB,YAAY,EAAE;IAC/C,OAAOiF,YAAY,CAACrG,QAAQ,CAAC;EAC/B;EAEA,IAAI8G,SAAS,GAAG,CAAC,CAAC;EAElB,IAAI,OAAO/E,KAAK,KAAK,QAAQ,EAAE;IAC7B+E,SAAS,CAAC/E,KAAK,GAAGA,KAAK;EACzB,CAAC,MAAM,IAAIzB,IAAI,EAAE;IACfwG,SAAS,CAAC/E,KAAK,GAAGgF,MAAM,CAACzG,IAAI,CAAC;EAChC;EAEA,IAAI0B,gBAAgB,EAAE;IACpB8E,SAAS,GAAGlK,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkK,SAAS,CAAC,EAAE9E,gBAAgB,CAAC;EACjE,CAAC,CAAC;;EAGF,OAAO,aAAanE,KAAK,CAACqI,aAAa,CAACjI,KAAK,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEsE,KAAK,EAAE;IACjEc,gBAAgB,EAAE8E,SAAS;IAC3B5E,OAAO,EAAEA,OAAO;IAChBC,eAAe,EAAEY,qBAAqB;IACtCa,YAAY,EAAEA;EAChB,CAAC,CAAC,EAAE,UAAUoD,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAE;IAC1C,IAAIC,UAAU,GAAG/H,OAAO,CAACkB,IAAI,CAAC,CAAC3C,MAAM,IAAIsJ,UAAU,GAAGA,UAAU,CAAC3G,IAAI,GAAG,EAAE;IAC1E,IAAIiG,OAAO,GAAGlH,UAAU,CAAC8H,UAAU,EAAE1E,QAAQ,CAAC;IAC9C,IAAI+D,UAAU,GAAG1E,QAAQ,KAAKvB,SAAS,GAAGuB,QAAQ,GAAG,CAAC,EAAEF,KAAK,IAAIA,KAAK,CAACwF,IAAI,CAAC,UAAUC,IAAI,EAAE;MAC1F,IAAIA,IAAI,IAAI3K,OAAO,CAAC2K,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAACvF,QAAQ,IAAI,CAACuF,IAAI,CAACC,WAAW,EAAE;QAC5E,OAAO,IAAI;MACb;MAEA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAIE,UAAU,GAAGF,IAAI,CAACH,OAAO,CAAC;QAC9B,OAAOK,UAAU,IAAIA,UAAU,CAACzF,QAAQ,IAAI,CAACyF,UAAU,CAACD,WAAW;MACrE;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,IAAIE,aAAa,GAAG5K,QAAQ,CAAC,CAAC,CAAC,EAAEoK,OAAO,CAAC;IAEzC,IAAIS,SAAS,GAAG,IAAI;IACpB3I,UAAU,CAAC,EAAE2C,YAAY,IAAIL,YAAY,CAAC,EAAE,WAAW,EAAE,qHAAqH,CAAC;IAE/K,IAAIsG,KAAK,CAACC,OAAO,CAAC3H,QAAQ,CAAC,IAAIgD,OAAO,EAAE;MACtClE,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,yDAAyD,CAAC;MACzF2I,SAAS,GAAGzH,QAAQ;IACtB,CAAC,MAAM,IAAI2C,aAAa,KAAK,EAAElB,YAAY,IAAIL,YAAY,CAAC,IAAI4B,OAAO,CAAC,EAAE;MACxElE,UAAU,CAAC,CAAC,EAAE2C,YAAY,IAAIL,YAAY,CAAC,EAAE,WAAW,EAAE,6EAA6E,CAAC;MACxItC,UAAU,CAAC,CAACkE,OAAO,EAAE,WAAW,EAAE,2EAA2E,CAAC;IAChH,CAAC,MAAM,IAAI5B,YAAY,IAAI,CAACuB,aAAa,IAAI,CAACK,OAAO,EAAE;MACrDlE,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,iEAAiE,CAAC;IACnG,CAAC,MAAM,IAAIS,cAAc,CAACS,QAAQ,CAAC,EAAE;MACnClB,UAAU,CAACkB,QAAQ,CAACkB,KAAK,CAAC0G,YAAY,KAAKrH,SAAS,EAAE,WAAW,EAAE,mGAAmG,CAAC;MAEvK,IAAIsH,UAAU,GAAGjL,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoD,QAAQ,CAACkB,KAAK,CAAC,EAAEsG,aAAa,CAAC;MAEtE,IAAI,CAACK,UAAU,CAACC,EAAE,EAAE;QAClBD,UAAU,CAACC,EAAE,GAAGvB,OAAO;MACzB;MAEA,IAAInI,UAAU,CAAC4B,QAAQ,CAAC,EAAE;QACxB6H,UAAU,CAACE,GAAG,GAAGlC,UAAU,CAACsB,UAAU,EAAEnH,QAAQ,CAAC;MACnD,CAAC,CAAC;;MAGF,IAAIgI,QAAQ,GAAG,IAAIC,GAAG,CAAC,EAAE,CAAC5D,MAAM,CAACxH,kBAAkB,CAACuC,OAAO,CAAC8C,OAAO,CAAC,CAAC,EAAErF,kBAAkB,CAACuC,OAAO,CAAC2D,qBAAqB,CAAC,CAAC,CAAC,CAAC;MAC3HiF,QAAQ,CAAC5C,OAAO,CAAC,UAAU8C,SAAS,EAAE;QACpCL,UAAU,CAACK,SAAS,CAAC,GAAG,YAAY;UAClC,IAAIC,GAAG,EAAEC,GAAG;UAEZ,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;UAEd,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAAC9K,MAAM,EAAE+K,IAAI,GAAG,IAAIhB,KAAK,CAACc,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;YACvFD,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;UAC9B;UAEA,CAACN,EAAE,GAAGb,aAAa,CAACU,SAAS,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACF,GAAG,GAAGE,EAAE,EAAE9K,IAAI,CAACgI,KAAK,CAAC4C,GAAG,EAAE,CAACX,aAAa,CAAC,CAACnD,MAAM,CAACqE,IAAI,CAAC,CAAC;UAC7H,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGtI,QAAQ,CAACkB,KAAK,EAAEgH,SAAS,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACH,GAAG,GAAGG,EAAE,EAAEhL,IAAI,CAACgI,KAAK,CAAC6C,GAAG,EAAE,CAACE,EAAE,CAAC,CAACjE,MAAM,CAACqE,IAAI,CAAC,CAAC;QAC5H,CAAC;MACH,CAAC,CAAC;MACFjB,SAAS,GAAG,aAAa5J,KAAK,CAACqI,aAAa,CAACrG,SAAS,EAAE;QACtDM,KAAK,EAAEqH,aAAa,CAACtG,KAAK,CAAC0H,aAAa,IAAI,OAAO,CAAC;QACpDxI,MAAM,EAAEJ;MACV,CAAC,EAAEV,YAAY,CAACU,QAAQ,EAAE6H,UAAU,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIlF,aAAa,KAAKlB,YAAY,IAAIL,YAAY,CAAC,IAAI,CAAC4B,OAAO,EAAE;MACtEyE,SAAS,GAAGzH,QAAQ,CAACkH,OAAO,CAAC;IAC/B,CAAC,MAAM;MACLpI,UAAU,CAAC,CAACqI,UAAU,CAACxJ,MAAM,EAAE,WAAW,EAAE,6HAA6H,CAAC;MAC1K8J,SAAS,GAAGzH,QAAQ;IACtB;IAEA,OAAOqG,YAAY,CAACoB,SAAS,EAAElB,OAAO,EAAEC,UAAU,CAAC;EACrD,CAAC,CAAC;AACJ;AAEA,eAAevF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}