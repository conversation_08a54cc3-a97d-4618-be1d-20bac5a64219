# 🤖 PROMPT PER BACKEND AGENT - E-Procurement System

## 📋 **CONTESTO PROGETTO**

Stai lavorando su un sistema e-procurement con:
- **Frontend React** già configurato e funzionante su `http://localhost:3000`
- **Backend TypeScript** esistente su `http://localhost:3002` (Docker)
- **Database PostgreSQL** attivo e connesso
- **Obiettivo**: Implementare endpoint mancanti e correggere errori esistenti

## 🎯 **SITUAZIONE ATTUALE**

### ✅ **Completato dal Frontend Agent**
- Configurazione API corretta (no `/api/` prefix in development)
- Frontend Docker e npm start funzionanti
- Struttura componenti e routing completi
- Testing framework configurato

### ❌ **Problemi da Risolvere (TUA RESPONSABILITÀ)**
1. **Login fallisce** - endpoint `/auth/` restituisce errori
2. **Endpoint mancanti** - `/user/license`, `/analytics/*`, `/health`
3. **Funzionalità incomplete** - gestione PDV, notifiche
4. **Testing backend** - coverage insufficiente

## 🚨 **ENDPOINT CRITICI DA IMPLEMENTARE**

### 1. **Sistema Licenze** (PRIORITÀ MASSIMA)
```typescript
GET /user/license
Response: {
  license: {
    type: "premium" | "basic" | "enterprise",
    features: string[],
    expiryDate: string,
    maxUsers: number,
    isActive: boolean
  }
}
```
**Motivo**: Il frontend chiama questo endpoint per feature flags e fallisce.

### 2. **Correzione Login** (PRIORITÀ MASSIMA)
```typescript
POST /auth/
Body: { email: string, password: string }
Response: {
  token: string,
  user: { id: number, email: string, role: string },
  expiresIn: number
}
```
**Motivo**: Login attualmente fallisce, blocca tutto il sistema.

### 3. **Health Check** (PRIORITÀ ALTA)
```typescript
GET /health
Response: {
  status: "ok" | "error",
  timestamp: string,
  services: { database: "ok" | "error" }
}
```

### 4. **Analytics Dashboard** (PRIORITÀ ALTA)
```typescript
GET /analytics/admin
GET /analytics/distributore
```

## 🔧 **STRUTTURA BACKEND ESISTENTE**

### **File Principali Identificati**
```
/app/src/
├── app.ts                 # Entry point
├── cu-acl-fe-api.ts      # Main API setup
├── routes/
│   ├── index.ts          # Route definitions
│   ├── auth.ts           # Authentication routes
│   ├── user.ts           # User management
│   └── ...               # Altri endpoint
├── controllers/
├── entity/               # TypeORM entities
└── middlewares/
```

### **Endpoint Esistenti Funzionanti**
- `POST /auth/` (con errori)
- `GET /products/`
- `GET /user/`
- `GET /orders/`
- Altri endpoint CRUD base

## 🗄️ **DATABASE SCHEMA**

### **Tabelle da Aggiungere**
```sql
-- Licenze utente
CREATE TABLE user_licenses (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  license_type VARCHAR(50) DEFAULT 'basic',
  features JSONB DEFAULT '[]',
  expiry_date TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Notifiche
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  title VARCHAR(255) NOT NULL,
  message TEXT,
  type VARCHAR(50) DEFAULT 'info',
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🧪 **TESTING REQUIREMENTS**

### **Test da Implementare**
```bash
# Unit Tests
npm test -- --coverage

# Integration Tests con Frontend
curl -X POST http://localhost:3002/auth/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

curl -X GET http://localhost:3002/user/license \
  -H "auth: JWT_TOKEN"
```

## 📋 **PIANO DI LAVORO SUGGERITO**

### **Fase 1 - Debug e Correzioni (Oggi)**
1. **Analizza errore login** - controlla logs, database, validazione
2. **Implementa `/user/license`** - endpoint semplice per sbloccare frontend
3. **Implementa `/health`** - monitoring di base
4. **Test con frontend** - verifica che login funzioni

### **Fase 2 - Analytics (Domani)**
1. **Implementa analytics endpoints** - query database per statistiche
2. **Ottimizza performance** - indici e cache se necessario
3. **Test integration** - verifica dashboard frontend

### **Fase 3 - Nuove Feature (Prossimi giorni)**
1. **Sistema notifiche** - CRUD completo
2. **Gestione PDV autonoma** - per agenti
3. **WebSocket** - notifiche real-time
4. **Documentazione** - API specs aggiornate

## 🔄 **COORDINAMENTO**

### **Frontend Agent (Io)**
- ✅ Configurazione completata
- 🔄 Attendo endpoint backend per test completi
- 🔄 Implementerò UI per nuove feature quando BE sarà pronto

### **Backend Agent (Tu)**
- 🔄 Debug e fix endpoint esistenti
- 🔄 Implementazione endpoint mancanti
- 🔄 Testing e documentazione
- 🔄 Coordinamento per deploy

## 🎯 **DELIVERABLES ATTESI**

1. **Login funzionante** - priorità assoluta
2. **Endpoint `/user/license`** - per feature flags
3. **Analytics endpoints** - per dashboard
4. **Testing coverage 80%+** - qualità del codice
5. **API documentation** - per coordinamento

## 📞 **COMUNICAZIONE**

**Status Update**: Condividi progressi su endpoint critici  
**Blockers**: Segnala problemi database o configurazione  
**Testing**: Coordiniamo test integration quando BE è pronto  

## 🚀 **QUICK START**

1. **Analizza il backend esistente** - struttura e configurazione
2. **Identifica causa errore login** - logs e debug
3. **Implementa endpoint mancanti** - partendo da `/user/license`
4. **Testa con frontend** - `http://localhost:3000` già attivo
5. **Documenta e comunica** - progressi e blockers

---

**Il frontend è pronto e ti aspetta! 🎯**  
**Focus su login e `/user/license` per sbloccare tutto il sistema.**

---

*Questo prompt contiene tutto il contesto necessario per procedere con l'implementazione backend. Il frontend agent è in standby per test e integration.*
