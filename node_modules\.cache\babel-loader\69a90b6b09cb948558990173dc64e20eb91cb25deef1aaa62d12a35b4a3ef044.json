{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { defaults, makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    allowMultiLoading: false,\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = defaults(options, this.options || {}, getDefaults());\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      this.options.request(this.options, url, undefined, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message && err.message.indexOf('Failed to fetch') > -1) return callback('failed loading ' + url + ': ' + err.message, true);\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (callback) callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n  return Backend;\n}();\nBackend.type = 'backend';\nexport default Backend;", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_defineProperty", "obj", "value", "defaults", "makePromise", "request", "getDefaults", "loadPath", "addPath", "allowMultiLoading", "parse", "data", "JSON", "stringify", "parsePayload", "namespace", "fallback<PERSON><PERSON><PERSON>", "reloadInterval", "window", "customHeaders", "queryStringParams", "crossDomain", "withCredentials", "overrideMimeType", "requestOptions", "mode", "credentials", "cache", "Backend", "services", "options", "arguments", "undefined", "allOptions", "type", "init", "_this", "setInterval", "reload", "readMulti", "languages", "namespaces", "callback", "_readAny", "read", "language", "loadUrlLanguages", "loadUrlNamespaces", "_this2", "then", "resolvedLoadPath", "url", "interpolator", "interpolate", "lng", "join", "ns", "loadUrl", "_this3", "err", "res", "status", "message", "indexOf", "ret", "parseErr", "e", "create", "_this4", "payload", "finished", "dataArray", "resArray", "for<PERSON>ach", "push", "_this5", "_this$services", "backendConnector", "languageUtils", "logger", "currentLanguage", "toLowerCase", "toLoad", "append", "lngs", "toResolveHierarchy", "l", "preload", "warn", "concat", "log", "loaded"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/i18next-http-backend/esm/index.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { defaults, makePromise } from './utils.js';\nimport request from './request.js';\n\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    allowMultiLoading: false,\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\n\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n    _classCallCheck(this, Backend);\n\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n\n  _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = defaults(options, this.options || {}, getDefaults());\n      this.allOptions = allOptions;\n\n      if (this.services && this.options.reloadInterval) {\n        setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n\n      var loadPath = this.options.loadPath;\n\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n\n      this.options.request(this.options, url, undefined, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message && err.message.indexOf('Failed to fetch') > -1) return callback('failed loading ' + url + ': ' + err.message, true);\n        if (err) return callback(err, false);\n        var ret, parseErr;\n\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n\n          if (finished === languages.length) {\n            if (callback) callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n\n      var _this$services = this.services,\n          backendConnector = _this$services.backendConnector,\n          languageUtils = _this$services.languageUtils,\n          logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n\n  return Backend;\n}();\n\nBackend.type = 'backend';\nexport default Backend;"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAEL,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAE5R,SAASkB,eAAeA,CAACC,GAAG,EAAEN,GAAG,EAAEO,KAAK,EAAE;EAAE,IAAIP,GAAG,IAAIM,GAAG,EAAE;IAAER,MAAM,CAACC,cAAc,CAACO,GAAG,EAAEN,GAAG,EAAE;MAAEO,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAES,GAAG,CAACN,GAAG,CAAC,GAAGO,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASE,QAAQ,EAAEC,WAAW,QAAQ,YAAY;AAClD,OAAOC,OAAO,MAAM,cAAc;AAElC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,OAAO;IACLC,QAAQ,EAAE,8BAA8B;IACxCC,OAAO,EAAE,6BAA6B;IACtCC,iBAAiB,EAAE,KAAK;IACxBC,KAAK,EAAE,SAASA,KAAKA,CAACC,IAAI,EAAE;MAC1B,OAAOC,IAAI,CAACF,KAAK,CAACC,IAAI,CAAC;IACzB,CAAC;IACDE,SAAS,EAAED,IAAI,CAACC,SAAS;IACzBC,YAAY,EAAE,SAASA,YAAYA,CAACC,SAAS,EAAEpB,GAAG,EAAEqB,aAAa,EAAE;MACjE,OAAOhB,eAAe,CAAC,CAAC,CAAC,EAAEL,GAAG,EAAEqB,aAAa,IAAI,EAAE,CAAC;IACtD,CAAC;IACDX,OAAO,EAAEA,OAAO;IAChBY,cAAc,EAAE,OAAOC,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACtEC,aAAa,EAAE,CAAC,CAAC;IACjBC,iBAAiB,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE;MACdC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC;AAED,IAAIC,OAAO,GAAG,YAAY;EACxB,SAASA,OAAOA,CAACC,QAAQ,EAAE;IACzB,IAAIC,OAAO,GAAGC,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIE,UAAU,GAAGF,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEvFnD,eAAe,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAE9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB,IAAI,CAACC,IAAI,CAACN,QAAQ,EAAEC,OAAO,EAAEG,UAAU,CAAC;EAC1C;EAEArC,YAAY,CAACgC,OAAO,EAAE,CAAC;IACrBjC,GAAG,EAAE,MAAM;IACXO,KAAK,EAAE,SAASiC,IAAIA,CAACN,QAAQ,EAAE;MAC7B,IAAIO,KAAK,GAAG,IAAI;MAEhB,IAAIN,OAAO,GAAGC,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIE,UAAU,GAAGF,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvF,IAAI,CAACF,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,OAAO,GAAG3B,QAAQ,CAAC2B,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAExB,WAAW,CAAC,CAAC,CAAC;MACnE,IAAI,CAAC2B,UAAU,GAAGA,UAAU;MAE5B,IAAI,IAAI,CAACJ,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACb,cAAc,EAAE;QAChDoB,WAAW,CAAC,YAAY;UACtB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAAC;QACvB,CAAC,EAAE,IAAI,CAACR,OAAO,CAACb,cAAc,CAAC;MACjC;IACF;EACF,CAAC,EAAE;IACDtB,GAAG,EAAE,WAAW;IAChBO,KAAK,EAAE,SAASqC,SAASA,CAACC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAE;MACzD,IAAI,CAACC,QAAQ,CAACH,SAAS,EAAEA,SAAS,EAAEC,UAAU,EAAEA,UAAU,EAAEC,QAAQ,CAAC;IACvE;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,MAAM;IACXO,KAAK,EAAE,SAAS0C,IAAIA,CAACC,QAAQ,EAAE9B,SAAS,EAAE2B,QAAQ,EAAE;MAClD,IAAI,CAACC,QAAQ,CAAC,CAACE,QAAQ,CAAC,EAAEA,QAAQ,EAAE,CAAC9B,SAAS,CAAC,EAAEA,SAAS,EAAE2B,QAAQ,CAAC;IACvE;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,UAAU;IACfO,KAAK,EAAE,SAASyC,QAAQA,CAACH,SAAS,EAAEM,gBAAgB,EAAEL,UAAU,EAAEM,iBAAiB,EAAEL,QAAQ,EAAE;MAC7F,IAAIM,MAAM,GAAG,IAAI;MAEjB,IAAIzC,QAAQ,GAAG,IAAI,CAACuB,OAAO,CAACvB,QAAQ;MAEpC,IAAI,OAAO,IAAI,CAACuB,OAAO,CAACvB,QAAQ,KAAK,UAAU,EAAE;QAC/CA,QAAQ,GAAG,IAAI,CAACuB,OAAO,CAACvB,QAAQ,CAACiC,SAAS,EAAEC,UAAU,CAAC;MACzD;MAEAlC,QAAQ,GAAGH,WAAW,CAACG,QAAQ,CAAC;MAChCA,QAAQ,CAAC0C,IAAI,CAAC,UAAUC,gBAAgB,EAAE;QACxC,IAAI,CAACA,gBAAgB,EAAE,OAAOR,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAEhD,IAAIS,GAAG,GAAGH,MAAM,CAACnB,QAAQ,CAACuB,YAAY,CAACC,WAAW,CAACH,gBAAgB,EAAE;UACnEI,GAAG,EAAEd,SAAS,CAACe,IAAI,CAAC,GAAG,CAAC;UACxBC,EAAE,EAAEf,UAAU,CAACc,IAAI,CAAC,GAAG;QACzB,CAAC,CAAC;QAEFP,MAAM,CAACS,OAAO,CAACN,GAAG,EAAET,QAAQ,EAAEI,gBAAgB,EAAEC,iBAAiB,CAAC;MACpE,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDpD,GAAG,EAAE,SAAS;IACdO,KAAK,EAAE,SAASuD,OAAOA,CAACN,GAAG,EAAET,QAAQ,EAAEF,SAAS,EAAEC,UAAU,EAAE;MAC5D,IAAIiB,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC5B,OAAO,CAACzB,OAAO,CAAC,IAAI,CAACyB,OAAO,EAAEqB,GAAG,EAAEnB,SAAS,EAAE,UAAU2B,GAAG,EAAEC,GAAG,EAAE;QACrE,IAAIA,GAAG,KAAKA,GAAG,CAACC,MAAM,IAAI,GAAG,IAAID,GAAG,CAACC,MAAM,GAAG,GAAG,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE,OAAOnB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,iBAAiB,GAAGS,GAAG,CAACC,MAAM,EAAE,IAAI,CAAC;QAClJ,IAAID,GAAG,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,IAAID,GAAG,CAACC,MAAM,GAAG,GAAG,EAAE,OAAOnB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,iBAAiB,GAAGS,GAAG,CAACC,MAAM,EAAE,KAAK,CAAC;QAClI,IAAI,CAACD,GAAG,IAAID,GAAG,IAAIA,GAAG,CAACG,OAAO,IAAIH,GAAG,CAACG,OAAO,CAACC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAOrB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,IAAI,GAAGQ,GAAG,CAACG,OAAO,EAAE,IAAI,CAAC;QAClJ,IAAIH,GAAG,EAAE,OAAOjB,QAAQ,CAACiB,GAAG,EAAE,KAAK,CAAC;QACpC,IAAIK,GAAG,EAAEC,QAAQ;QAEjB,IAAI;UACF,IAAI,OAAOL,GAAG,CAACjD,IAAI,KAAK,QAAQ,EAAE;YAChCqD,GAAG,GAAGN,MAAM,CAAC5B,OAAO,CAACpB,KAAK,CAACkD,GAAG,CAACjD,IAAI,EAAE6B,SAAS,EAAEC,UAAU,CAAC;UAC7D,CAAC,MAAM;YACLuB,GAAG,GAAGJ,GAAG,CAACjD,IAAI;UAChB;QACF,CAAC,CAAC,OAAOuD,CAAC,EAAE;UACVD,QAAQ,GAAG,iBAAiB,GAAGd,GAAG,GAAG,UAAU;QACjD;QAEA,IAAIc,QAAQ,EAAE,OAAOvB,QAAQ,CAACuB,QAAQ,EAAE,KAAK,CAAC;QAC9CvB,QAAQ,CAAC,IAAI,EAAEsB,GAAG,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,QAAQ;IACbO,KAAK,EAAE,SAASiE,MAAMA,CAAC3B,SAAS,EAAEzB,SAAS,EAAEpB,GAAG,EAAEqB,aAAa,EAAE0B,QAAQ,EAAE;MACzE,IAAI0B,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACtC,OAAO,CAACtB,OAAO,EAAE;MAC3B,IAAI,OAAOgC,SAAS,KAAK,QAAQ,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;MAC1D,IAAI6B,OAAO,GAAG,IAAI,CAACvC,OAAO,CAAChB,YAAY,CAACC,SAAS,EAAEpB,GAAG,EAAEqB,aAAa,CAAC;MACtE,IAAIsD,QAAQ,GAAG,CAAC;MAChB,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIC,QAAQ,GAAG,EAAE;MACjBhC,SAAS,CAACiC,OAAO,CAAC,UAAUnB,GAAG,EAAE;QAC/B,IAAI9C,OAAO,GAAG4D,MAAM,CAACtC,OAAO,CAACtB,OAAO;QAEpC,IAAI,OAAO4D,MAAM,CAACtC,OAAO,CAACtB,OAAO,KAAK,UAAU,EAAE;UAChDA,OAAO,GAAG4D,MAAM,CAACtC,OAAO,CAACtB,OAAO,CAAC8C,GAAG,EAAEvC,SAAS,CAAC;QAClD;QAEA,IAAIoC,GAAG,GAAGiB,MAAM,CAACvC,QAAQ,CAACuB,YAAY,CAACC,WAAW,CAAC7C,OAAO,EAAE;UAC1D8C,GAAG,EAAEA,GAAG;UACRE,EAAE,EAAEzC;QACN,CAAC,CAAC;QAEFqD,MAAM,CAACtC,OAAO,CAACzB,OAAO,CAAC+D,MAAM,CAACtC,OAAO,EAAEqB,GAAG,EAAEkB,OAAO,EAAE,UAAU1D,IAAI,EAAEiD,GAAG,EAAE;UACxEU,QAAQ,IAAI,CAAC;UACbC,SAAS,CAACG,IAAI,CAAC/D,IAAI,CAAC;UACpB6D,QAAQ,CAACE,IAAI,CAACd,GAAG,CAAC;UAElB,IAAIU,QAAQ,KAAK9B,SAAS,CAACpD,MAAM,EAAE;YACjC,IAAIsD,QAAQ,EAAEA,QAAQ,CAAC6B,SAAS,EAAEC,QAAQ,CAAC;UAC7C;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD7E,GAAG,EAAE,QAAQ;IACbO,KAAK,EAAE,SAASoC,MAAMA,CAAA,EAAG;MACvB,IAAIqC,MAAM,GAAG,IAAI;MAEjB,IAAIC,cAAc,GAAG,IAAI,CAAC/C,QAAQ;QAC9BgD,gBAAgB,GAAGD,cAAc,CAACC,gBAAgB;QAClDC,aAAa,GAAGF,cAAc,CAACE,aAAa;QAC5CC,MAAM,GAAGH,cAAc,CAACG,MAAM;MAClC,IAAIC,eAAe,GAAGH,gBAAgB,CAAChC,QAAQ;MAC/C,IAAImC,eAAe,IAAIA,eAAe,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnE,IAAIC,MAAM,GAAG,EAAE;MAEf,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAC7B,GAAG,EAAE;QAChC,IAAI8B,IAAI,GAAGN,aAAa,CAACO,kBAAkB,CAAC/B,GAAG,CAAC;QAChD8B,IAAI,CAACX,OAAO,CAAC,UAAUa,CAAC,EAAE;UACxB,IAAIJ,MAAM,CAACnB,OAAO,CAACuB,CAAC,CAAC,GAAG,CAAC,EAAEJ,MAAM,CAACR,IAAI,CAACY,CAAC,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC;MAEDH,MAAM,CAACH,eAAe,CAAC;MACvB,IAAI,IAAI,CAAC/C,UAAU,CAACsD,OAAO,EAAE,IAAI,CAACtD,UAAU,CAACsD,OAAO,CAACd,OAAO,CAAC,UAAUa,CAAC,EAAE;QACxE,OAAOH,MAAM,CAACG,CAAC,CAAC;MAClB,CAAC,CAAC;MACFJ,MAAM,CAACT,OAAO,CAAC,UAAUnB,GAAG,EAAE;QAC5BqB,MAAM,CAAC1C,UAAU,CAACuB,EAAE,CAACiB,OAAO,CAAC,UAAUjB,EAAE,EAAE;UACzCqB,gBAAgB,CAACjC,IAAI,CAACU,GAAG,EAAEE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAUG,GAAG,EAAEhD,IAAI,EAAE;YACtE,IAAIgD,GAAG,EAAEoB,MAAM,CAACS,IAAI,CAAC,oBAAoB,CAACC,MAAM,CAACjC,EAAE,EAAE,gBAAgB,CAAC,CAACiC,MAAM,CAACnC,GAAG,EAAE,SAAS,CAAC,EAAEK,GAAG,CAAC;YACnG,IAAI,CAACA,GAAG,IAAIhD,IAAI,EAAEoE,MAAM,CAACW,GAAG,CAAC,mBAAmB,CAACD,MAAM,CAACjC,EAAE,EAAE,gBAAgB,CAAC,CAACiC,MAAM,CAACnC,GAAG,CAAC,EAAE3C,IAAI,CAAC;YAChGkE,gBAAgB,CAACc,MAAM,CAAC,EAAE,CAACF,MAAM,CAACnC,GAAG,EAAE,GAAG,CAAC,CAACmC,MAAM,CAACjC,EAAE,CAAC,EAAEG,GAAG,EAAEhD,IAAI,CAAC;UACpE,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EAEH,OAAOiB,OAAO;AAChB,CAAC,CAAC,CAAC;AAEHA,OAAO,CAACM,IAAI,GAAG,SAAS;AACxB,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}