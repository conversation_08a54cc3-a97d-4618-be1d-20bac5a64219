{"name": "webpack", "version": "5.72.0", "author": "<PERSON> @sokra", "description": "Packs CommonJs/AMD modules for the browser. Allows to split your codebase into multiple bundles, which can be loaded on demand. Support loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^0.0.51", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.4.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.9.2", "es-module-lexer": "^0.9.0", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-better-errors": "^1.0.2", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.3.1", "webpack-sources": "^3.2.3"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/preset-react": "^7.10.4", "@types/es-module-lexer": "^0.4.1", "@types/jest": "^27.4.0", "@types/node": "^17.0.16", "assemblyscript": "^0.19.16", "babel-loader": "^8.1.0", "benchmark": "^2.1.4", "bundle-loader": "^0.5.6", "coffee-loader": "^1.0.0", "coffeescript": "^2.5.1", "core-js": "^3.6.5", "coveralls": "^3.1.0", "cspell": "^4.0.63", "css-loader": "^5.0.1", "date-fns": "^2.15.0", "es5-ext": "^0.10.53", "es6-promise-polyfill": "^1.2.0", "eslint": "^7.14.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-jest": "^24.7.0", "eslint-plugin-jsdoc": "^33.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^4.0.0", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^6.0.5", "hash-wasm": "^4.9.0", "husky": "^6.0.0", "is-ci": "^3.0.0", "istanbul": "^0.4.5", "jest": "^27.5.0", "jest-circus": "^27.5.0", "jest-cli": "^27.5.0", "jest-diff": "^27.5.0", "jest-junit": "^13.0.0", "json-loader": "^0.5.7", "json5": "^2.1.3", "less": "^4.0.0", "less-loader": "^8.0.0", "lint-staged": "^11.0.0", "loader-utils": "^2.0.0", "lodash": "^4.17.19", "lodash-es": "^4.17.15", "memfs": "^3.2.0", "mini-css-extract-plugin": "^1.6.1", "mini-svg-data-uri": "^1.2.3", "nyc": "^15.1.0", "open-cli": "^6.0.1", "prettier": "^2.2.0", "pretty-format": "^27.0.2", "pug": "^3.0.0", "pug-loader": "^2.4.0", "raw-loader": "^4.0.1", "react": "^17.0.1", "react-dom": "^17.0.1", "rimraf": "^3.0.2", "script-loader": "^0.7.2", "simple-git": "^2.17.0", "strip-ansi": "^6.0.0", "style-loader": "^2.0.0", "terser": "^5.7.0", "toml": "^3.0.0", "tooling": "webpack/tooling#v1.21.0", "ts-loader": "^8.0.2", "typescript": "^4.5.5", "url-loader": "^4.1.0", "wast-loader": "^1.11.0", "webassembly-feature": "1.3.0", "webpack-cli": "^4.3.0", "xxhashjs": "^0.2.2", "yamljs": "^0.3.0", "yarn-deduplicate": "^3.1.0"}, "engines": {"node": ">=10.13.0"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "main": "lib/index.js", "bin": {"webpack": "bin/webpack.js"}, "types": "types.d.ts", "files": ["lib/", "bin/", "hot/", "schemas/", "SECURITY.md", "module.d.ts", "types.d.ts"], "scripts": {"setup": "node ./setup/setup.js", "jest": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test:update-snapshots": "yarn jest -u", "test:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\"", "test:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\"", "test:unit": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "build:examples": "cd examples && node buildAll.js", "type-report": "rimraf coverage && yarn cover:types && yarn cover:report && open-cli coverage/lcov-report/index.html", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn special-lint && yarn type-lint && yarn typings-test && yarn module-typings-test && yarn yarn-lint && yarn pretty-lint && yarn spellcheck", "code-lint": "eslint . --ext '.js' --cache", "type-lint": "tsc", "typings-test": "tsc -p tsconfig.types.test.json", "module-typings-test": "tsc -p tsconfig.module.test.json", "spellcheck": "cspell \"**/*\"", "special-lint": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/schemas-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-schemas && node tooling/generate-runtime-code.js && node tooling/generate-wasm-code.js && node node_modules/tooling/format-file-header && node node_modules/tooling/compile-to-definitions && node node_modules/tooling/precompile-schemas && node node_modules/tooling/generate-types --no-template-literals", "special-lint-fix": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-schemas --write && node tooling/generate-runtime-code.js --write && node tooling/generate-wasm-code.js --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/compile-to-definitions --write && node node_modules/tooling/precompile-schemas --write && node node_modules/tooling/generate-types --no-template-literals --write", "fix": "yarn code-lint --fix && yarn special-lint-fix && yarn pretty-lint-fix", "prepare": "husky install", "pretty-lint-base": "prettier \"*.{ts,json,yml,yaml,md}\" \"{setup,lib,bin,hot,benchmark,tooling,schemas}/**/*.json\" \"examples/*.md\"", "pretty-lint-base-all": "yarn pretty-lint-base \"*.js\" \"{setup,lib,bin,hot,benchmark,tooling,schemas}/**/*.js\" \"module.d.ts\" \"test/*.js\" \"test/helpers/*.js\" \"test/{configCases,watchCases,statsCases,hotCases,benchmarkCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "pretty-lint-fix": "yarn pretty-lint-base-all --loglevel warn --write", "pretty-lint": "yarn pretty-lint-base --check", "yarn-lint": "yarn-deduplicate --fail --list -s highest yarn.lock", "yarn-lint-fix": "yarn-deduplicate -s highest yarn.lock", "benchmark": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "cover": "yarn cover:all && yarn cover:report", "cover:clean": "rimraf .nyc_output coverage", "cover:all": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --coverage", "cover:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\" --coverage", "cover:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\" --coverage", "cover:integration:a": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,test}.js\" --coverage", "cover:integration:b": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.longtest.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:types": "node node_modules/tooling/type-coverage", "cover:merge": "yarn mkdirp .nyc_output && nyc merge .nyc_output coverage/coverage-nyc.json && rimraf .nyc_output", "cover:report": "nyc report -t coverage"}, "lint-staged": {"*.js|{lib,setup,bin,hot,tooling,schemas}/**/*.js|test/*.js|{test,examples}/**/webpack.config.js}": ["eslint --cache"], "*.{ts,json,yml,yaml,md}|examples/*.md": ["prettier --check"], "*.md|{.github,benchmark,bin,examples,hot,lib,schemas,setup,tooling}/**/*.{md,yml,yaml,js,json}": ["cspell"]}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.basictest.js", "<rootDir>/test/*.longtest.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/assembly", "<rootDir>/tooling", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "node", "coverageReporters": ["json"]}}