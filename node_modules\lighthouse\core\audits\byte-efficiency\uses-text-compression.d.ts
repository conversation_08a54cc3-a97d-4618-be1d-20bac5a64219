export default ResponsesAreCompressed;
declare class ResponsesAreCompressed extends ByteEfficiencyAudit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {import('./byte-efficiency-audit.js').ByteEfficiencyProduct}
     */
    static audit_(artifacts: LH.Artifacts): import('./byte-efficiency-audit.js').ByteEfficiencyProduct;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { ByteEfficiencyAudit } from "./byte-efficiency-audit.js";
//# sourceMappingURL=uses-text-compression.d.ts.map