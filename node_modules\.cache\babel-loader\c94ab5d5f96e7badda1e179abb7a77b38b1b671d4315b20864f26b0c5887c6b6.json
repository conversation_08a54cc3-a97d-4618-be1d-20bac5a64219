{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useTextValueMapping(_ref) {\n  var valueTexts = _ref.valueTexts,\n    onTextChange = _ref.onTextChange;\n  var _React$useState = React.useState(''),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    text = _React$useState2[0],\n    setInnerText = _React$useState2[1];\n  var valueTextsRef = React.useRef([]);\n  valueTextsRef.current = valueTexts;\n  function triggerTextChange(value) {\n    setInnerText(value);\n    onTextChange(value);\n  }\n  function resetText() {\n    setInnerText(valueTextsRef.current[0]);\n  }\n  React.useEffect(function () {\n    if (valueTexts.every(function (valText) {\n      return valText !== text;\n    })) {\n      resetText();\n    }\n  }, [valueTexts.join('||')]);\n  return [text, triggerTextChange, resetText];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useTextValueMapping", "_ref", "valueTexts", "onTextChange", "_React$useState", "useState", "_React$useState2", "text", "setInnerText", "valueTextsRef", "useRef", "current", "triggerTextChange", "value", "resetText", "useEffect", "every", "valText", "join"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-picker/es/hooks/useTextValueMapping.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useTextValueMapping(_ref) {\n  var valueTexts = _ref.valueTexts,\n      onTextChange = _ref.onTextChange;\n\n  var _React$useState = React.useState(''),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      text = _React$useState2[0],\n      setInnerText = _React$useState2[1];\n\n  var valueTextsRef = React.useRef([]);\n  valueTextsRef.current = valueTexts;\n\n  function triggerTextChange(value) {\n    setInnerText(value);\n    onTextChange(value);\n  }\n\n  function resetText() {\n    setInnerText(valueTextsRef.current[0]);\n  }\n\n  React.useEffect(function () {\n    if (valueTexts.every(function (valText) {\n      return valText !== text;\n    })) {\n      resetText();\n    }\n  }, [valueTexts.join('||')]);\n  return [text, triggerTextChange, resetText];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EAChD,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC5BC,YAAY,GAAGF,IAAI,CAACE,YAAY;EAEpC,IAAIC,eAAe,GAAGL,KAAK,CAACM,QAAQ,CAAC,EAAE,CAAC;IACpCC,gBAAgB,GAAGR,cAAc,CAACM,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,aAAa,GAAGV,KAAK,CAACW,MAAM,CAAC,EAAE,CAAC;EACpCD,aAAa,CAACE,OAAO,GAAGT,UAAU;EAElC,SAASU,iBAAiBA,CAACC,KAAK,EAAE;IAChCL,YAAY,CAACK,KAAK,CAAC;IACnBV,YAAY,CAACU,KAAK,CAAC;EACrB;EAEA,SAASC,SAASA,CAAA,EAAG;IACnBN,YAAY,CAACC,aAAa,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;EACxC;EAEAZ,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,IAAIb,UAAU,CAACc,KAAK,CAAC,UAAUC,OAAO,EAAE;MACtC,OAAOA,OAAO,KAAKV,IAAI;IACzB,CAAC,CAAC,EAAE;MACFO,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACZ,UAAU,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3B,OAAO,CAACX,IAAI,EAAEK,iBAAiB,EAAEE,SAAS,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}