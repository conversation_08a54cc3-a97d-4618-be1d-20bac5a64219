{"ast": null, "code": "export function toArray(value) {\n  if (value === undefined || value === null) {\n    return [];\n  }\n  return Array.isArray(value) ? value : [value];\n}", "map": {"version": 3, "names": ["toArray", "value", "undefined", "Array", "isArray"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-field-form/es/utils/typeUtil.js"], "sourcesContent": ["export function toArray(value) {\n  if (value === undefined || value === null) {\n    return [];\n  }\n\n  return Array.isArray(value) ? value : [value];\n}"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAO,EAAE;EACX;EAEA,OAAOE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}