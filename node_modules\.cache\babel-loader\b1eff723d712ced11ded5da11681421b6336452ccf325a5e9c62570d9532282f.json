{"ast": null, "code": "import { createContext } from 'react';\nvar LocaleContext = /*#__PURE__*/createContext(undefined);\nexport default LocaleContext;", "map": {"version": 3, "names": ["createContext", "LocaleContext", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/locale-provider/context.js"], "sourcesContent": ["import { createContext } from 'react';\nvar LocaleContext = /*#__PURE__*/createContext(undefined);\nexport default LocaleContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,IAAIC,aAAa,GAAG,aAAaD,aAAa,CAACE,SAAS,CAAC;AACzD,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}