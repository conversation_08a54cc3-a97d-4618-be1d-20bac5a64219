{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport { ConfigContext } from '../config-provider';\nvar MenuDivider = function MenuDivider(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    dashed = _a.dashed,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"dashed\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('menu', customizePrefixCls);\n  var classString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-item-divider-dashed\"), !!dashed), className);\n  return /*#__PURE__*/React.createElement(Divider, _extends({\n    className: classString\n  }, restProps));\n};\nexport default MenuDivider;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "Divider", "ConfigContext", "MenuDivider", "_a", "customizePrefixCls", "prefixCls", "className", "dashed", "restProps", "_React$useContext", "useContext", "getPrefixCls", "classString", "concat", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/menu/MenuDivider.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport { ConfigContext } from '../config-provider';\n\nvar MenuDivider = function MenuDivider(_a) {\n  var customizePrefixCls = _a.prefixCls,\n      className = _a.className,\n      dashed = _a.dashed,\n      restProps = __rest(_a, [\"prefixCls\", \"className\", \"dashed\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('menu', customizePrefixCls);\n  var classString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-item-divider-dashed\"), !!dashed), className);\n  return /*#__PURE__*/React.createElement(Divider, _extends({\n    className: classString\n  }, restProps));\n};\n\nexport default MenuDivider;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAE;EACzC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACjCC,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBC,MAAM,GAAGJ,EAAE,CAACI,MAAM;IAClBC,SAAS,GAAGxB,MAAM,CAACmB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;EAEhE,IAAIM,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;IACnDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIN,SAAS,GAAGM,YAAY,CAAC,MAAM,EAAEP,kBAAkB,CAAC;EACxD,IAAIQ,WAAW,GAAGb,UAAU,CAAChB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8B,MAAM,CAACR,SAAS,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAACE,MAAM,CAAC,EAAED,SAAS,CAAC;EACpH,OAAO,aAAaR,KAAK,CAACgB,aAAa,CAACd,OAAO,EAAElB,QAAQ,CAAC;IACxDwB,SAAS,EAAEM;EACb,CAAC,EAAEJ,SAAS,CAAC,CAAC;AAChB,CAAC;AAED,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}