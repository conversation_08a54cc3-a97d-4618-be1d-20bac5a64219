{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { login } from '../utils';\nimport { <PERSON><PERSON> } from \"./traduttore/const\";\nimport logo from '../img/tm_logo-01.svg';\nimport { recuperaPassword, registrati } from \"./route\";\n/* import Logos from \"../resources/Logos\"; */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = props => {\n  _s();\n  useEffect(() => {\n    // aggiungo classe loginPage a body per stile login\n    document.body.classList.add('loginPage');\n    return () => {\n      // rimuovo classe loginPage a body se non sono in login\n      document.body.classList.remove('loginPage');\n    };\n  });\n  const handleLogin = async e => {\n    e.preventDefault(); // Prevent form submission\n\n    const email = document.getElementById('loginEmail').value;\n    const password = document.getElementById('loginPassword').value;\n    if (!email || !password) {\n      alert('Inserisci email e password');\n      return;\n    }\n    try {\n      await login(props);\n    } catch (error) {\n      console.error('❌ Login failed:', error);\n      // Don't show alert here - login function handles user feedback\n    }\n  };\n  function myFunction() {\n    var x = document.getElementById(\"loginPassword\");\n    if (x.type === \"password\") {\n      x.type = \"text\";\n    } else {\n      x.type = \"password\";\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"redirectToWinet\",\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        className: \"d-flex align-items-center\",\n        href: \"https://tmselezioni.it/\",\n        children: [/*#__PURE__*/_jsxDEV(\"ion-icon\", {\n          name: \"arrow-back-circle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 89\n        }, this), \"TMSELEZIONI\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"form-login row\",\n      onSubmit: handleLogin,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"fontLogo\",\n          className: \"logo text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: logo,\n            onError: e => e.target.src = logo,\n            alt: \"Logo\",\n            width: \"300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 69\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mb-1 mt-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-2 opacity-3\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            children: Costanti.InserisciCredenzialiLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mt-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"loginEmail\",\n          className: \"login-cred d-none\",\n          children: Costanti.InsEmail\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-input-icon-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            className: \"form-control\",\n            id: \"loginEmail\",\n            \"aria-describedby\": \"Email\",\n            placeholder: \"Email\",\n            required: true,\n            autoFocus: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-input-icon-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-key\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"loginEmail\",\n            className: \"login-cred d-none\",\n            children: Costanti.InsPass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            className: \"form-control m-0\",\n            id: \"loginPassword\",\n            \"aria-describedby\": \"Password\",\n            placeholder: \"Password\",\n            required: true,\n            autoFocus: \"\",\n            autoComplete: \"off\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-eye iconaLogin\",\n            onClick: myFunction\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mb-2 mt-0 border-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: recuperaPassword /* target={'_self'} */,\n          alt: \"Password dimenticata?\",\n          children: Costanti.PassDimenticata\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"mt-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-lg btn-primary btn-block\",\n          children: Costanti.ACCEDI\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12 mt-3\",\n        children: [Costanti.NonRegistrato, \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: registrati,\n          children: Costanti.registrati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 46\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useEffect", "login", "<PERSON><PERSON>", "logo", "recuperaPassword", "registrati", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "props", "_s", "document", "body", "classList", "add", "remove", "handleLogin", "e", "preventDefault", "email", "getElementById", "value", "password", "alert", "error", "console", "myFunction", "x", "type", "className", "children", "href", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "id", "src", "onError", "target", "alt", "width", "InserisciCredenzialiLogin", "htmlFor", "InsEmail", "placeholder", "required", "autoFocus", "InsPass", "autoComplete", "onClick", "PassDimenticata", "ACCEDI", "NonRegistrato", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/Login.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { login } from '../utils';\nimport { <PERSON>nti } from \"./traduttore/const\";\nimport logo from '../img/tm_logo-01.svg';\nimport { recuperaPassword, registrati } from \"./route\";\n/* import Logos from \"../resources/Logos\"; */\n\nconst Login = (props) => {\n\n\n    useEffect(() => {\n        // aggiungo classe loginPage a body per stile login\n        document.body.classList.add('loginPage');\n        return () => {\n            // rimuovo classe loginPage a body se non sono in login\n            document.body.classList.remove('loginPage');\n        };\n    });\n\n\n    const handleLogin = async (e) => {\n        e.preventDefault(); // Prevent form submission\n\n        const email = document.getElementById('loginEmail').value;\n        const password = document.getElementById('loginPassword').value;\n\n        if (!email || !password) {\n            alert('Inserisci email e password');\n            return;\n        }\n\n        try {\n            await login(props);\n        } catch (error) {\n            console.error('❌ <PERSON><PERSON> failed:', error);\n            // Don't show alert here - login function handles user feedback\n        }\n    }\n\n    function myFunction() {\n        var x = document.getElementById(\"loginPassword\");\n        if (x.type === \"password\") {\n            x.type = \"text\";\n        } else {\n            x.type = \"password\";\n        }\n    }\n\n    return (\n        <div className=\"App\">\n            <div className=\"redirectToWinet\">\n                <a className=\"d-flex align-items-center\" href='https://tmselezioni.it/'><ion-icon name=\"arrow-back-circle\" />TMSELEZIONI</a>\n            </div>\n            <form className=\"form-login row\" onSubmit={handleLogin}>\n                <div className=\"col-md-12\">\n                    {/* <img className=\"mb-4\" src={Logos.logo} alt=\"Logo\" width=\"300\" /> */}\n                    <div id=\"fontLogo\" className=\"logo text-center\"><img src={logo} onError={(e) => e.target.src = logo} alt=\"Logo\" width=\"300\" />{/* <span>central</span> unit <span className=\"payoff text-center\">food & beverage • e-procurement system</span> */}</div>\n                    <hr className=\"mb-1 mt-0\"></hr>\n                </div>\n                <div className=\"col-md-12\">\n                    <p className=\"mb-2 opacity-3\"><small>{Costanti.InserisciCredenzialiLogin}</small></p>\n                    <hr className=\"mt-2\"></hr>\n                </div>\n                <div className=\"col-md-12 mb-3\">\n                    <label htmlFor=\"loginEmail\" className=\"login-cred d-none\">{Costanti.InsEmail}</label>\n                    <span className=\"p-input-icon-left\">\n                        <i className=\"pi pi-user\" />\n                        <input type=\"email\" className=\"form-control\" id=\"loginEmail\" aria-describedby=\"Email\" placeholder=\"Email\" required autoFocus=\"\" />\n                    </span>\n                </div>\n                <div className=\"col-md-12 mb-1\">\n                    <span className=\"p-input-icon-left\">\n                        <i className=\"pi pi-key\" />\n                        <label htmlFor=\"loginEmail\" className=\"login-cred d-none\">{Costanti.InsPass}</label>\n                        <input type=\"password\" className=\"form-control m-0\" id=\"loginPassword\" aria-describedby=\"Password\" placeholder=\"Password\" required autoFocus=\"\" autoComplete=\"off\" />\n                        <i className=\"pi pi-eye iconaLogin\" onClick={myFunction} />\n                    </span>\n                    <hr className=\"mb-2 mt-0 border-0\"></hr>\n                    <a href={recuperaPassword} /* target={'_self'} */ alt=\"Password dimenticata?\">{Costanti.PassDimenticata}</a>\n                    <hr className=\"mt-2\"></hr>\n                </div>\n                <div className=\"col-md-12\">\n                    <button type=\"submit\" className=\"btn btn-lg btn-primary btn-block\">{Costanti.ACCEDI}</button>\n                </div>\n                <div className=\"col-md-12 mt-3\">\n                    {Costanti.NonRegistrato} <a href={registrati}>{Costanti.registrati}</a>\n                </div>\n            </form>\n        </div>\n    )\n}\n\n\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,IAAI,MAAM,uBAAuB;AACxC,SAASC,gBAAgB,EAAEC,UAAU,QAAQ,SAAS;AACtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,KAAK,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAGrBV,SAAS,CAAC,MAAM;IACZ;IACAW,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;IACxC,OAAO,MAAM;MACT;MACAH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;IAC/C,CAAC;EACL,CAAC,CAAC;EAGF,MAAMC,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAEpB,MAAMC,KAAK,GAAGR,QAAQ,CAACS,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK;IACzD,MAAMC,QAAQ,GAAGX,QAAQ,CAACS,cAAc,CAAC,eAAe,CAAC,CAACC,KAAK;IAE/D,IAAI,CAACF,KAAK,IAAI,CAACG,QAAQ,EAAE;MACrBC,KAAK,CAAC,4BAA4B,CAAC;MACnC;IACJ;IAEA,IAAI;MACA,MAAMtB,KAAK,CAACQ,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC;IACJ;EACJ,CAAC;EAED,SAASE,UAAUA,CAAA,EAAG;IAClB,IAAIC,CAAC,GAAGhB,QAAQ,CAACS,cAAc,CAAC,eAAe,CAAC;IAChD,IAAIO,CAAC,CAACC,IAAI,KAAK,UAAU,EAAE;MACvBD,CAAC,CAACC,IAAI,GAAG,MAAM;IACnB,CAAC,MAAM;MACHD,CAAC,CAACC,IAAI,GAAG,UAAU;IACvB;EACJ;EAEA,oBACIrB,OAAA;IAAKsB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAChBvB,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5BvB,OAAA;QAAGsB,SAAS,EAAC,2BAA2B;QAACE,IAAI,EAAC,yBAAyB;QAAAD,QAAA,gBAACvB,OAAA;UAAUyB,IAAI,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAAW;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3H,CAAC,eACN7B,OAAA;MAAMsB,SAAS,EAAC,gBAAgB;MAACQ,QAAQ,EAAErB,WAAY;MAAAc,QAAA,gBACnDvB,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAEtBvB,OAAA;UAAK+B,EAAE,EAAC,UAAU;UAACT,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAACvB,OAAA;YAAKgC,GAAG,EAAEpC,IAAK;YAACqC,OAAO,EAAGvB,CAAC,IAAKA,CAAC,CAACwB,MAAM,CAACF,GAAG,GAAGpC,IAAK;YAACuC,GAAG,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAyH,CAAC,eACxP7B,OAAA;UAAIsB,SAAS,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACN7B,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBvB,OAAA;UAAGsB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAACvB,OAAA;YAAAuB,QAAA,EAAQ5B,QAAQ,CAAC0C;UAAyB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrF7B,OAAA;UAAIsB,SAAS,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACN7B,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BvB,OAAA;UAAOsC,OAAO,EAAC,YAAY;UAAChB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAE5B,QAAQ,CAAC4C;QAAQ;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACrF7B,OAAA;UAAMsB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC/BvB,OAAA;YAAGsB,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B7B,OAAA;YAAOqB,IAAI,EAAC,OAAO;YAACC,SAAS,EAAC,cAAc;YAACS,EAAE,EAAC,YAAY;YAAC,oBAAiB,OAAO;YAACS,WAAW,EAAC,OAAO;YAACC,QAAQ;YAACC,SAAS,EAAC;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN7B,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BvB,OAAA;UAAMsB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC/BvB,OAAA;YAAGsB,SAAS,EAAC;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3B7B,OAAA;YAAOsC,OAAO,EAAC,YAAY;YAAChB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAE5B,QAAQ,CAACgD;UAAO;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpF7B,OAAA;YAAOqB,IAAI,EAAC,UAAU;YAACC,SAAS,EAAC,kBAAkB;YAACS,EAAE,EAAC,eAAe;YAAC,oBAAiB,UAAU;YAACS,WAAW,EAAC,UAAU;YAACC,QAAQ;YAACC,SAAS,EAAC,EAAE;YAACE,YAAY,EAAC;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrK7B,OAAA;YAAGsB,SAAS,EAAC,sBAAsB;YAACuB,OAAO,EAAE1B;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACP7B,OAAA;UAAIsB,SAAS,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxC7B,OAAA;UAAGwB,IAAI,EAAE3B,gBAAiB,CAAC;UAAuBsC,GAAG,EAAC,uBAAuB;UAAAZ,QAAA,EAAE5B,QAAQ,CAACmD;QAAe;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5G7B,OAAA;UAAIsB,SAAS,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACN7B,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBvB,OAAA;UAAQqB,IAAI,EAAC,QAAQ;UAACC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAE5B,QAAQ,CAACoD;QAAM;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eACN7B,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC1B5B,QAAQ,CAACqD,aAAa,EAAC,GAAC,eAAAhD,OAAA;UAAGwB,IAAI,EAAE1B,UAAW;UAAAyB,QAAA,EAAE5B,QAAQ,CAACG;QAAU;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAA1B,EAAA,CAnFKF,KAAK;AAAAgD,EAAA,GAALhD,KAAK;AAuFX,eAAeA,KAAK;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}