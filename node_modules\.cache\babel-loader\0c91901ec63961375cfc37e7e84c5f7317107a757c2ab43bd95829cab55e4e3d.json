{"ast": null, "code": "/*!\n  Copyright (c) 2015 <PERSON>.\n  Based on code that is Copyright 2013-2015, Facebook, Inc.\n  All rights reserved.\n*/\n/* global define */\n\n(function () {\n  'use strict';\n\n  var canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n  var ExecutionEnvironment = {\n    canUseDOM: canUseDOM,\n    canUseWorkers: typeof Worker !== 'undefined',\n    canUseEventListeners: canUseDOM && !!(window.addEventListener || window.attachEvent),\n    canUseViewport: canUseDOM && !!window.screen\n  };\n  if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n    define(function () {\n      return ExecutionEnvironment;\n    });\n  } else if (typeof module !== 'undefined' && module.exports) {\n    module.exports = ExecutionEnvironment;\n  } else {\n    window.ExecutionEnvironment = ExecutionEnvironment;\n  }\n})();", "map": {"version": 3, "names": ["canUseDOM", "window", "document", "createElement", "ExecutionEnvironment", "canUseWorkers", "Worker", "canUseEventListeners", "addEventListener", "attachEvent", "canUseViewport", "screen", "define", "amd", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/exenv/index.js"], "sourcesContent": ["/*!\n  Copyright (c) 2015 <PERSON>.\n  Based on code that is Copyright 2013-2015, Facebook, Inc.\n  All rights reserved.\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar canUseDOM = !!(\n\t\ttypeof window !== 'undefined' &&\n\t\twindow.document &&\n\t\twindow.document.createElement\n\t);\n\n\tvar ExecutionEnvironment = {\n\n\t\tcanUseDOM: canUseDOM,\n\n\t\tcanUseWorkers: typeof Worker !== 'undefined',\n\n\t\tcanUseEventListeners:\n\t\t\tcanUseDOM && !!(window.addEventListener || window.attachEvent),\n\n\t\tcanUseViewport: canUseDOM && !!window.screen\n\n\t};\n\n\tif (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\tdefine(function () {\n\t\t\treturn ExecutionEnvironment;\n\t\t});\n\t} else if (typeof module !== 'undefined' && module.exports) {\n\t\tmodule.exports = ExecutionEnvironment;\n\t} else {\n\t\twindow.ExecutionEnvironment = ExecutionEnvironment;\n\t}\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEC,aAAY;EACZ,YAAY;;EAEZ,IAAIA,SAAS,GAAG,CAAC,EAChB,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACC,QAAQ,IACfD,MAAM,CAACC,QAAQ,CAACC,aAAa,CAC7B;EAED,IAAIC,oBAAoB,GAAG;IAE1BJ,SAAS,EAAEA,SAAS;IAEpBK,aAAa,EAAE,OAAOC,MAAM,KAAK,WAAW;IAE5CC,oBAAoB,EACnBP,SAAS,IAAI,CAAC,EAAEC,MAAM,CAACO,gBAAgB,IAAIP,MAAM,CAACQ,WAAW,CAAC;IAE/DC,cAAc,EAAEV,SAAS,IAAI,CAAC,CAACC,MAAM,CAACU;EAEvC,CAAC;EAED,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,GAAG,KAAK,QAAQ,IAAID,MAAM,CAACC,GAAG,EAAE;IACjFD,MAAM,CAAC,YAAY;MAClB,OAAOR,oBAAoB;IAC5B,CAAC,CAAC;EACH,CAAC,MAAM,IAAI,OAAOU,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IAC3DD,MAAM,CAACC,OAAO,GAAGX,oBAAoB;EACtC,CAAC,MAAM;IACNH,MAAM,CAACG,oBAAoB,GAAGA,oBAAoB;EACnD;AAED,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}