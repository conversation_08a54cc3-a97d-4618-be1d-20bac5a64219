{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport raf from \"rc-util/es/raf\";\nimport CacheMap from '../utils/CacheMap';\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var collectRafRef = useRef();\n  function cancelRaf() {\n    raf.cancel(collectRafRef.current);\n  }\n  function collectHeight() {\n    cancelRaf();\n    collectRafRef.current = raf(function () {\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var htmlElement = findDOMNode(element);\n          var offsetHeight = htmlElement.offsetHeight;\n          if (heightsRef.current.get(key) !== offsetHeight) {\n            heightsRef.current.set(key, htmlElement.offsetHeight);\n          }\n        }\n      }); // Always trigger update mark to tell parent that should re-calculate heights when resized\n\n      setUpdatedMark(function (c) {\n        return c + 1;\n      });\n    });\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    } // Instance changed\n\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 ? void 0 : onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "React", "useRef", "useEffect", "findDOMNode", "raf", "CacheMap", "useHeights", "<PERSON><PERSON><PERSON>", "onItemAdd", "onItemRemove", "_React$useState", "useState", "_React$useState2", "updatedMark", "setUpdatedMark", "instanceRef", "Map", "heightsRef", "collectRafRef", "cancelRaf", "cancel", "current", "collectHeight", "for<PERSON>ach", "element", "key", "offsetParent", "htmlElement", "offsetHeight", "get", "set", "c", "setInstanceRef", "item", "instance", "origin", "delete"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-virtual-list/es/hooks/useHeights.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport raf from \"rc-util/es/raf\";\nimport CacheMap from '../utils/CacheMap';\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      updatedMark = _React$useState2[0],\n      setUpdatedMark = _React$useState2[1];\n\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var collectRafRef = useRef();\n\n  function cancelRaf() {\n    raf.cancel(collectRafRef.current);\n  }\n\n  function collectHeight() {\n    cancelRaf();\n    collectRafRef.current = raf(function () {\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var htmlElement = findDOMNode(element);\n          var offsetHeight = htmlElement.offsetHeight;\n\n          if (heightsRef.current.get(key) !== offsetHeight) {\n            heightsRef.current.set(key, htmlElement.offsetHeight);\n          }\n        }\n      }); // Always trigger update mark to tell parent that should re-calculate heights when resized\n\n      setUpdatedMark(function (c) {\n        return c + 1;\n      });\n    });\n  }\n\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    } // Instance changed\n\n\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 ? void 0 : onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(item);\n      }\n    }\n  }\n\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAO,KAAKsC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClE,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IACnCC,gBAAgB,GAAGnD,cAAc,CAACiD,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,WAAW,GAAGd,MAAM,CAAC,IAAIe,GAAG,CAAC,CAAC,CAAC;EACnC,IAAIC,UAAU,GAAGhB,MAAM,CAAC,IAAII,QAAQ,CAAC,CAAC,CAAC;EACvC,IAAIa,aAAa,GAAGjB,MAAM,CAAC,CAAC;EAE5B,SAASkB,SAASA,CAAA,EAAG;IACnBf,GAAG,CAACgB,MAAM,CAACF,aAAa,CAACG,OAAO,CAAC;EACnC;EAEA,SAASC,aAAaA,CAAA,EAAG;IACvBH,SAAS,CAAC,CAAC;IACXD,aAAa,CAACG,OAAO,GAAGjB,GAAG,CAAC,YAAY;MACtCW,WAAW,CAACM,OAAO,CAACE,OAAO,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;QAClD,IAAID,OAAO,IAAIA,OAAO,CAACE,YAAY,EAAE;UACnC,IAAIC,WAAW,GAAGxB,WAAW,CAACqB,OAAO,CAAC;UACtC,IAAII,YAAY,GAAGD,WAAW,CAACC,YAAY;UAE3C,IAAIX,UAAU,CAACI,OAAO,CAACQ,GAAG,CAACJ,GAAG,CAAC,KAAKG,YAAY,EAAE;YAChDX,UAAU,CAACI,OAAO,CAACS,GAAG,CAACL,GAAG,EAAEE,WAAW,CAACC,YAAY,CAAC;UACvD;QACF;MACF,CAAC,CAAC,CAAC,CAAC;;MAEJd,cAAc,CAAC,UAAUiB,CAAC,EAAE;QAC1B,OAAOA,CAAC,GAAG,CAAC;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,SAASC,cAAcA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACtC,IAAIT,GAAG,GAAGlB,MAAM,CAAC0B,IAAI,CAAC;IACtB,IAAIE,MAAM,GAAGpB,WAAW,CAACM,OAAO,CAACQ,GAAG,CAACJ,GAAG,CAAC;IAEzC,IAAIS,QAAQ,EAAE;MACZnB,WAAW,CAACM,OAAO,CAACS,GAAG,CAACL,GAAG,EAAES,QAAQ,CAAC;MACtCZ,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLP,WAAW,CAACM,OAAO,CAACe,MAAM,CAACX,GAAG,CAAC;IACjC,CAAC,CAAC;;IAGF,IAAI,CAACU,MAAM,KAAK,CAACD,QAAQ,EAAE;MACzB,IAAIA,QAAQ,EAAE;QACZ1B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACyB,IAAI,CAAC;MACvE,CAAC,MAAM;QACLxB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACwB,IAAI,CAAC;MAChF;IACF;EACF;EAEA/B,SAAS,CAAC,YAAY;IACpB,OAAOiB,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACa,cAAc,EAAEV,aAAa,EAAEL,UAAU,CAACI,OAAO,EAAER,WAAW,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}