{"ast": null, "code": "function t(t) {\n  return \"object\" == typeof t && null != t && 1 === t.nodeType;\n}\nfunction e(t, e) {\n  return (!e || \"hidden\" !== t) && \"visible\" !== t && \"clip\" !== t;\n}\nfunction n(t, n) {\n  if (t.clientHeight < t.scrollHeight || t.clientWidth < t.scrollWidth) {\n    var r = getComputedStyle(t, null);\n    return e(r.overflowY, n) || e(r.overflowX, n) || function (t) {\n      var e = function (t) {\n        if (!t.ownerDocument || !t.ownerDocument.defaultView) return null;\n        try {\n          return t.ownerDocument.defaultView.frameElement;\n        } catch (t) {\n          return null;\n        }\n      }(t);\n      return !!e && (e.clientHeight < t.scrollHeight || e.clientWidth < t.scrollWidth);\n    }(t);\n  }\n  return !1;\n}\nfunction r(t, e, n, r, i, o, l, d) {\n  return o < t && l > e || o > t && l < e ? 0 : o <= t && d <= n || l >= e && d >= n ? o - t - r : l > e && d < n || o < t && d > n ? l - e + i : 0;\n}\nexport default function (e, i) {\n  var o = window,\n    l = i.scrollMode,\n    d = i.block,\n    u = i.inline,\n    h = i.boundary,\n    a = i.skipOverflowHiddenElements,\n    c = \"function\" == typeof h ? h : function (t) {\n      return t !== h;\n    };\n  if (!t(e)) throw new TypeError(\"Invalid target\");\n  for (var f = document.scrollingElement || document.documentElement, s = [], p = e; t(p) && c(p);) {\n    if ((p = p.parentElement) === f) {\n      s.push(p);\n      break;\n    }\n    null != p && p === document.body && n(p) && !n(document.documentElement) || null != p && n(p, a) && s.push(p);\n  }\n  for (var m = o.visualViewport ? o.visualViewport.width : innerWidth, g = o.visualViewport ? o.visualViewport.height : innerHeight, w = window.scrollX || pageXOffset, v = window.scrollY || pageYOffset, W = e.getBoundingClientRect(), b = W.height, H = W.width, y = W.top, E = W.right, M = W.bottom, V = W.left, x = \"start\" === d || \"nearest\" === d ? y : \"end\" === d ? M : y + b / 2, I = \"center\" === u ? V + H / 2 : \"end\" === u ? E : V, C = [], T = 0; T < s.length; T++) {\n    var k = s[T],\n      B = k.getBoundingClientRect(),\n      D = B.height,\n      O = B.width,\n      R = B.top,\n      X = B.right,\n      Y = B.bottom,\n      L = B.left;\n    if (\"if-needed\" === l && y >= 0 && V >= 0 && M <= g && E <= m && y >= R && M <= Y && V >= L && E <= X) return C;\n    var S = getComputedStyle(k),\n      j = parseInt(S.borderLeftWidth, 10),\n      q = parseInt(S.borderTopWidth, 10),\n      z = parseInt(S.borderRightWidth, 10),\n      A = parseInt(S.borderBottomWidth, 10),\n      F = 0,\n      G = 0,\n      J = \"offsetWidth\" in k ? k.offsetWidth - k.clientWidth - j - z : 0,\n      K = \"offsetHeight\" in k ? k.offsetHeight - k.clientHeight - q - A : 0;\n    if (f === k) F = \"start\" === d ? x : \"end\" === d ? x - g : \"nearest\" === d ? r(v, v + g, g, q, A, v + x, v + x + b, b) : x - g / 2, G = \"start\" === u ? I : \"center\" === u ? I - m / 2 : \"end\" === u ? I - m : r(w, w + m, m, j, z, w + I, w + I + H, H), F = Math.max(0, F + v), G = Math.max(0, G + w);else {\n      F = \"start\" === d ? x - R - q : \"end\" === d ? x - Y + A + K : \"nearest\" === d ? r(R, Y, D, q, A + K, x, x + b, b) : x - (R + D / 2) + K / 2, G = \"start\" === u ? I - L - j : \"center\" === u ? I - (L + O / 2) + J / 2 : \"end\" === u ? I - X + z + J : r(L, X, O, j, z + J, I, I + H, H);\n      var N = k.scrollLeft,\n        P = k.scrollTop;\n      x += P - (F = Math.max(0, Math.min(P + F, k.scrollHeight - D + K))), I += N - (G = Math.max(0, Math.min(N + G, k.scrollWidth - O + J)));\n    }\n    C.push({\n      el: k,\n      top: F,\n      left: G\n    });\n  }\n  return C;\n}", "map": {"version": 3, "names": ["t", "nodeType", "e", "n", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "r", "getComputedStyle", "overflowY", "overflowX", "ownerDocument", "defaultView", "frameElement", "i", "o", "l", "d", "window", "scrollMode", "block", "u", "inline", "h", "boundary", "a", "skipOverflowHiddenElements", "c", "TypeError", "f", "document", "scrollingElement", "documentElement", "s", "p", "parentElement", "push", "body", "m", "visualViewport", "width", "innerWidth", "g", "height", "innerHeight", "w", "scrollX", "pageXOffset", "v", "scrollY", "pageYOffset", "W", "getBoundingClientRect", "b", "H", "y", "top", "E", "right", "M", "bottom", "V", "left", "x", "I", "C", "T", "length", "k", "B", "D", "O", "R", "X", "Y", "L", "S", "j", "parseInt", "borderLeftWidth", "q", "borderTopWidth", "z", "borderRightWidth", "A", "borderBottomWidth", "F", "G", "J", "offsetWidth", "K", "offsetHeight", "Math", "max", "N", "scrollLeft", "P", "scrollTop", "min", "el"], "sources": ["C:\\Users\\<USER>\\Workspace\\cu-frontend-coll\\node_modules\\compute-scroll-into-view\\src\\index.ts"], "sourcesContent": ["// Compute what scrolling needs to be done on required scrolling boxes for target to be in view\n\n// The type names here are named after the spec to make it easier to find more information around what they mean:\n// To reduce churn and reduce things that need be maintained things from the official TS DOM library is used here\n// https://drafts.csswg.org/cssom-view/\n\n// For a definition on what is \"block flow direction\" exactly, check this: https://drafts.csswg.org/css-writing-modes-4/#block-flow-direction\n\n// add support for visualViewport object currently implemented in chrome\ninterface visualViewport {\n  height: number\n  width: number\n}\n\ntype ScrollLogicalPosition = 'start' | 'center' | 'end' | 'nearest'\n// This new option is tracked in this PR, which is the most likely candidate at the time: https://github.com/w3c/csswg-drafts/pull/1805\ntype ScrollMode = 'always' | 'if-needed'\n// New option that skips auto-scrolling all nodes with overflow: hidden set\n// See FF implementation: https://hg.mozilla.org/integration/fx-team/rev/c48c3ec05012#l7.18\ntype SkipOverflowHiddenElements = boolean\n\ninterface Options {\n  block?: ScrollLogicalPosition\n  inline?: ScrollLogicalPosition\n  scrollMode?: ScrollMode\n  boundary?: CustomScrollBoundary\n  skipOverflowHiddenElements?: SkipOverflowHiddenElements\n}\n\n// Custom behavior, not in any spec\ntype CustomScrollBoundaryCallback = (parent: Element) => boolean\ntype CustomScrollBoundary = Element | CustomScrollBoundaryCallback | null\ninterface CustomScrollAction {\n  el: Element\n  top: number\n  left: number\n}\n\n// @TODO better shadowdom test, 11 = document fragment\nfunction isElement(el: any): el is Element {\n  return typeof el === 'object' && el != null && el.nodeType === 1\n}\n\nfunction canOverflow(\n  overflow: string | null,\n  skipOverflowHiddenElements?: boolean\n) {\n  if (skipOverflowHiddenElements && overflow === 'hidden') {\n    return false\n  }\n\n  return overflow !== 'visible' && overflow !== 'clip'\n}\n\nfunction getFrameElement(el: Element) {\n  if (!el.ownerDocument || !el.ownerDocument.defaultView) {\n    return null\n  }\n\n  try {\n    return el.ownerDocument.defaultView.frameElement\n  } catch (e) {\n    return null\n  }\n}\n\nfunction isHiddenByFrame(el: Element): boolean {\n  const frame = getFrameElement(el)\n  if (!frame) {\n    return false\n  }\n\n  return (\n    frame.clientHeight < el.scrollHeight || frame.clientWidth < el.scrollWidth\n  )\n}\n\nfunction isScrollable(el: Element, skipOverflowHiddenElements?: boolean) {\n  if (el.clientHeight < el.scrollHeight || el.clientWidth < el.scrollWidth) {\n    const style = getComputedStyle(el, null)\n    return (\n      canOverflow(style.overflowY, skipOverflowHiddenElements) ||\n      canOverflow(style.overflowX, skipOverflowHiddenElements) ||\n      isHiddenByFrame(el)\n    )\n  }\n\n  return false\n}\n/**\n * Find out which edge to align against when logical scroll position is \"nearest\"\n * Interesting fact: \"nearest\" works similarily to \"if-needed\", if the element is fully visible it will not scroll it\n *\n * Legends:\n * ┌────────┐ ┏ ━ ━ ━ ┓\n * │ target │   frame\n * └────────┘ ┗ ━ ━ ━ ┛\n */\nfunction alignNearest(\n  scrollingEdgeStart: number,\n  scrollingEdgeEnd: number,\n  scrollingSize: number,\n  scrollingBorderStart: number,\n  scrollingBorderEnd: number,\n  elementEdgeStart: number,\n  elementEdgeEnd: number,\n  elementSize: number\n) {\n  /**\n   * If element edge A and element edge B are both outside scrolling box edge A and scrolling box edge B\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓\n   *          │  │\n   *        ┃ │  │ ┃        do nothing\n   *          │  │\n   *        ┗━│━━│━┛\n   *          └──┘\n   *\n   *  If element edge C and element edge D are both outside scrolling box edge C and scrolling box edge D\n   *\n   *    ┏ ━ ━ ━ ━ ┓\n   *   ┌───────────┐\n   *   │┃         ┃│        do nothing\n   *   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart < scrollingEdgeStart &&\n      elementEdgeEnd > scrollingEdgeEnd) ||\n    (elementEdgeStart > scrollingEdgeStart && elementEdgeEnd < scrollingEdgeEnd)\n  ) {\n    return 0\n  }\n\n  /**\n   * If element edge A is outside scrolling box edge A and element height is less than scrolling box height\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓         ┏━┌━━┐━┓\n   *          └──┘             │  │\n   *  from  ┃      ┃     to  ┃ └──┘ ┃\n   *\n   *        ┗━ ━━ ━┛         ┗━ ━━ ━┛\n   *\n   * If element edge B is outside scrolling box edge B and element height is greater than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━┌━━┐━┓\n   *                           │  │\n   *  from  ┃ ┌──┐ ┃     to  ┃ │  │ ┃\n   *          │  │             │  │\n   *        ┗━│━━│━┛         ┗━│━━│━┛\n   *          │  │             └──┘\n   *          │  │\n   *          └──┘\n   *\n   * If element edge C is outside scrolling box edge C and element width is less than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───┐                 ┌───┐\n   *  │ ┃ │       ┃         ┃   │     ┃\n   *  └───┘                 └───┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is greater than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *        ┌───────────┐   ┌───────────┐\n   *    ┃   │     ┃     │   ┃         ┃ │\n   *        └───────────┘   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart <= scrollingEdgeStart && elementSize <= scrollingSize) ||\n    (elementEdgeEnd >= scrollingEdgeEnd && elementSize >= scrollingSize)\n  ) {\n    return elementEdgeStart - scrollingEdgeStart - scrollingBorderStart\n  }\n\n  /**\n   * If element edge B is outside scrolling box edge B and element height is less than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━ ━━ ━┓\n   *\n   *  from  ┃      ┃     to  ┃ ┌──┐ ┃\n   *          ┌──┐             │  │\n   *        ┗━│━━│━┛         ┗━└━━┘━┛\n   *          └──┘\n   *\n   * If element edge A is outside scrolling box edge A and element height is greater than scrolling box height\n   *\n   *          ┌──┐\n   *          │  │\n   *          │  │             ┌──┐\n   *        ┏━│━━│━┓         ┏━│━━│━┓\n   *          │  │             │  │\n   *  from  ┃ └──┘ ┃     to  ┃ │  │ ┃\n   *                           │  │\n   *        ┗━ ━━ ━┛         ┗━└━━┘━┛\n   *\n   * If element edge C is outside scrolling box edge C and element width is greater than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───────────┐           ┌───────────┐\n   *  │     ┃     │   ┃       │ ┃         ┃\n   *  └───────────┘           └───────────┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is less than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *                ┌───┐             ┌───┐\n   *        ┃       │ ┃ │       ┃     │   ┃\n   *                └───┘             └───┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   */\n  if (\n    (elementEdgeEnd > scrollingEdgeEnd && elementSize < scrollingSize) ||\n    (elementEdgeStart < scrollingEdgeStart && elementSize > scrollingSize)\n  ) {\n    return elementEdgeEnd - scrollingEdgeEnd + scrollingBorderEnd\n  }\n\n  return 0\n}\n\nexport default (target: Element, options: Options): CustomScrollAction[] => {\n  //TODO: remove this hack when microbundle will support typescript >= 4.0\n  const windowWithViewport = (window as unknown) as Window & {\n    visualViewport: visualViewport\n  }\n\n  const {\n    scrollMode,\n    block,\n    inline,\n    boundary,\n    skipOverflowHiddenElements,\n  } = options\n  // Allow using a callback to check the boundary\n  // The default behavior is to check if the current target matches the boundary element or not\n  // If undefined it'll check that target is never undefined (can happen as we recurse up the tree)\n  const checkBoundary =\n    typeof boundary === 'function' ? boundary : (node: any) => node !== boundary\n\n  if (!isElement(target)) {\n    throw new TypeError('Invalid target')\n  }\n\n  // Used to handle the top most element that can be scrolled\n  const scrollingElement = document.scrollingElement || document.documentElement\n\n  // Collect all the scrolling boxes, as defined in the spec: https://drafts.csswg.org/cssom-view/#scrolling-box\n  const frames: Element[] = []\n  let cursor: Element | null = target\n  while (isElement(cursor) && checkBoundary(cursor)) {\n    // Move cursor to parent\n    cursor = cursor.parentElement\n\n    // Stop when we reach the viewport\n    if (cursor === scrollingElement) {\n      frames.push(cursor)\n      break\n    }\n\n    // Skip document.body if it's not the scrollingElement and documentElement isn't independently scrollable\n    if (\n      cursor != null &&\n      cursor === document.body &&\n      isScrollable(cursor) &&\n      !isScrollable(document.documentElement)\n    ) {\n      continue\n    }\n\n    // Now we check if the element is scrollable, this code only runs if the loop haven't already hit the viewport or a custom boundary\n    if (cursor != null && isScrollable(cursor, skipOverflowHiddenElements)) {\n      frames.push(cursor)\n    }\n  }\n\n  // Support pinch-zooming properly, making sure elements scroll into the visual viewport\n  // Browsers that don't support visualViewport will report the layout viewport dimensions on document.documentElement.clientWidth/Height\n  // and viewport dimensions on window.innerWidth/Height\n  // https://www.quirksmode.org/mobile/viewports2.html\n  // https://bokand.github.io/viewport/index.html\n  const viewportWidth = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.width\n    : innerWidth\n  const viewportHeight = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.height\n    : innerHeight\n\n  // Newer browsers supports scroll[X|Y], page[X|Y]Offset is\n  const viewportX = window.scrollX || pageXOffset\n  const viewportY = window.scrollY || pageYOffset\n\n  const {\n    height: targetHeight,\n    width: targetWidth,\n    top: targetTop,\n    right: targetRight,\n    bottom: targetBottom,\n    left: targetLeft,\n  } = target.getBoundingClientRect()\n\n  // These values mutate as we loop through and generate scroll coordinates\n  let targetBlock: number =\n    block === 'start' || block === 'nearest'\n      ? targetTop\n      : block === 'end'\n      ? targetBottom\n      : targetTop + targetHeight / 2 // block === 'center\n  let targetInline: number =\n    inline === 'center'\n      ? targetLeft + targetWidth / 2\n      : inline === 'end'\n      ? targetRight\n      : targetLeft // inline === 'start || inline === 'nearest\n\n  // Collect new scroll positions\n  const computations: CustomScrollAction[] = []\n  // In chrome there's no longer a difference between caching the `frames.length` to a var or not, so we don't in this case (size > speed anyways)\n  for (let index = 0; index < frames.length; index++) {\n    const frame = frames[index]\n\n    // @TODO add a shouldScroll hook here that allows userland code to take control\n\n    const {\n      height,\n      width,\n      top,\n      right,\n      bottom,\n      left,\n    } = frame.getBoundingClientRect()\n\n    // If the element is already visible we can end it here\n    // @TODO targetBlock and targetInline should be taken into account to be compliant with https://github.com/w3c/csswg-drafts/pull/1805/files#diff-3c17f0e43c20f8ecf89419d49e7ef5e0R1333\n    if (\n      scrollMode === 'if-needed' &&\n      targetTop >= 0 &&\n      targetLeft >= 0 &&\n      targetBottom <= viewportHeight &&\n      targetRight <= viewportWidth &&\n      targetTop >= top &&\n      targetBottom <= bottom &&\n      targetLeft >= left &&\n      targetRight <= right\n    ) {\n      // Break the loop and return the computations for things that are not fully visible\n      return computations\n    }\n\n    const frameStyle = getComputedStyle(frame)\n    const borderLeft = parseInt(frameStyle.borderLeftWidth as string, 10)\n    const borderTop = parseInt(frameStyle.borderTopWidth as string, 10)\n    const borderRight = parseInt(frameStyle.borderRightWidth as string, 10)\n    const borderBottom = parseInt(frameStyle.borderBottomWidth as string, 10)\n\n    let blockScroll: number = 0\n    let inlineScroll: number = 0\n\n    // The property existance checks for offfset[Width|Height] is because only HTMLElement objects have them, but any Element might pass by here\n    // @TODO find out if the \"as HTMLElement\" overrides can be dropped\n    const scrollbarWidth =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth -\n          (frame as HTMLElement).clientWidth -\n          borderLeft -\n          borderRight\n        : 0\n    const scrollbarHeight =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight -\n          (frame as HTMLElement).clientHeight -\n          borderTop -\n          borderBottom\n        : 0\n\n    if (scrollingElement === frame) {\n      // Handle viewport logic (document.documentElement or document.body)\n\n      if (block === 'start') {\n        blockScroll = targetBlock\n      } else if (block === 'end') {\n        blockScroll = targetBlock - viewportHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          viewportY,\n          viewportY + viewportHeight,\n          viewportHeight,\n          borderTop,\n          borderBottom,\n          viewportY + targetBlock,\n          viewportY + targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - viewportHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - viewportWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - viewportWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          viewportX,\n          viewportX + viewportWidth,\n          viewportWidth,\n          borderLeft,\n          borderRight,\n          viewportX + targetInline,\n          viewportX + targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      // Apply scroll position offsets and ensure they are within bounds\n      // @TODO add more test cases to cover this 100%\n      blockScroll = Math.max(0, blockScroll + viewportY)\n      inlineScroll = Math.max(0, inlineScroll + viewportX)\n    } else {\n      // Handle each scrolling frame that might exist between the target and the viewport\n\n      if (block === 'start') {\n        blockScroll = targetBlock - top - borderTop\n      } else if (block === 'end') {\n        blockScroll = targetBlock - bottom + borderBottom + scrollbarHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          top,\n          bottom,\n          height,\n          borderTop,\n          borderBottom + scrollbarHeight,\n          targetBlock,\n          targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - (top + height / 2) + scrollbarHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline - left - borderLeft\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - (left + width / 2) + scrollbarWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - right + borderRight + scrollbarWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          left,\n          right,\n          width,\n          borderLeft,\n          borderRight + scrollbarWidth,\n          targetInline,\n          targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      const { scrollLeft, scrollTop } = frame\n      // Ensure scroll coordinates are not out of bounds while applying scroll offsets\n      blockScroll = Math.max(\n        0,\n        Math.min(\n          scrollTop + blockScroll,\n          frame.scrollHeight - height + scrollbarHeight\n        )\n      )\n      inlineScroll = Math.max(\n        0,\n        Math.min(\n          scrollLeft + inlineScroll,\n          frame.scrollWidth - width + scrollbarWidth\n        )\n      )\n\n      // Cache the offset so that parent frames can scroll this into view correctly\n      targetBlock += scrollTop - blockScroll\n      targetInline += scrollLeft - inlineScroll\n    }\n\n    computations.push({ el: frame, top: blockScroll, left: inlineScroll })\n  }\n\n  return computations\n}\n"], "mappings": "AAuCA,SAASA,EAAUA,CAAA;EACjB,OAAqB,mBAAPA,CAAA,IAAyB,QAANA,CAAA,IAA8B,MAAhBA,CAAA,CAAGC,QAAA;AAAA;AAGpD,SAASC,EACPF,CAAA,EACAE,CAAA;EAEA,SAAIA,CAAA,IAA2C,aAAbF,CAAA,KAId,cAAbA,CAAA,IAAuC,WAAbA,CAAA;AAAA;AA0BnC,SAASG,EAAaH,CAAA,EAAaG,CAAA;EACjC,IAAIH,CAAA,CAAGI,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBL,CAAA,CAAGM,WAAA,GAAcN,CAAA,CAAGO,WAAA,EAAa;IACxE,IAAMC,CAAA,GAAQC,gBAAA,CAAiBT,CAAA,EAAI;IACnC,OACEE,CAAA,CAAYM,CAAA,CAAME,SAAA,EAAWP,CAAA,KAC7BD,CAAA,CAAYM,CAAA,CAAMG,SAAA,EAAWR,CAAA,KAhBnC,UAAyBH,CAAA;MACvB,IAAME,CAAA,GAbR,UAAyBF,CAAA;QACvB,KAAKA,CAAA,CAAGY,aAAA,KAAkBZ,CAAA,CAAGY,aAAA,CAAcC,WAAA,EACzC;QAGF;UACE,OAAOb,CAAA,CAAGY,aAAA,CAAcC,WAAA,CAAYC,YAAA;QAAA,CACpC,QAAOd,CAAA;UACP;QAAA;MAAA,CAKY,CAAgBA,CAAA;MAC9B,SAAKE,CAAA,KAKHA,CAAA,CAAME,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBH,CAAA,CAAMI,WAAA,GAAcN,CAAA,CAAGO,WAAA;IAAA,CAU7D,CAAgBP,CAAA;EAAA;EAIpB;AAAA;AAWF,SAASQ,EACPR,CAAA,EACAE,CAAA,EACAC,CAAA,EACAK,CAAA,EACAO,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EAqBA,OACGF,CAAA,GAAmBhB,CAAA,IAClBiB,CAAA,GAAiBf,CAAA,IAClBc,CAAA,GAAmBhB,CAAA,IAAsBiB,CAAA,GAAiBf,CAAA,OA6C1Dc,CAAA,IAAoBhB,CAAA,IAAsBkB,CAAA,IAAef,CAAA,IACzDc,CAAA,IAAkBf,CAAA,IAAoBgB,CAAA,IAAef,CAAA,GAE/Ca,CAAA,GAAmBhB,CAAA,GAAqBQ,CAAA,GA4C9CS,CAAA,GAAiBf,CAAA,IAAoBgB,CAAA,GAAcf,CAAA,IACnDa,CAAA,GAAmBhB,CAAA,IAAsBkB,CAAA,GAAcf,CAAA,GAEjDc,CAAA,GAAiBf,CAAA,GAAmBa,CAAA;AAAA;AAAA,yBAM/Bb,CAAA,EAAiBa,CAAA;EAE/B,IAAMC,CAAA,GAAsBG,MAAA;IAK1BF,CAAA,GAKEF,CAAA,CALFK,UAAA;IACAF,CAAA,GAIEH,CAAA,CAJFM,KAAA;IACAC,CAAA,GAGEP,CAAA,CAHFQ,MAAA;IACAC,CAAA,GAEET,CAAA,CAFFU,QAAA;IACAC,CAAA,GACEX,CAAA,CADFY,0BAAA;IAKIC,CAAA,GACgB,qBAAbJ,CAAA,GAA0BA,CAAA,GAAW,UAACxB,CAAA;MAAA,OAAcA,CAAA,KAASwB,CAAA;IAAA;EAEtE,KAAKxB,CAAA,CAAUE,CAAA,GACb,UAAU2B,SAAA,CAAU;EAStB,KALA,IAAMC,CAAA,GAAmBC,QAAA,CAASC,gBAAA,IAAoBD,QAAA,CAASE,eAAA,EAGzDC,CAAA,GAAoB,IACtBC,CAAA,GAAyBjC,CAAA,EACtBF,CAAA,CAAUmC,CAAA,KAAWP,CAAA,CAAcO,CAAA,IAAS;IAKjD,KAHAA,CAAA,GAASA,CAAA,CAAOC,aAAA,MAGDN,CAAA,EAAkB;MAC/BI,CAAA,CAAOG,IAAA,CAAKF,CAAA;MACZ;IAAA;IAKU,QAAVA,CAAA,IACAA,CAAA,KAAWJ,QAAA,CAASO,IAAA,IACpBnC,CAAA,CAAagC,CAAA,MACZhC,CAAA,CAAa4B,QAAA,CAASE,eAAA,KAMX,QAAVE,CAAA,IAAkBhC,CAAA,CAAagC,CAAA,EAAQT,CAAA,KACzCQ,CAAA,CAAOG,IAAA,CAAKF,CAAA;EAAA;EA8ChB,KArCA,IAAMI,CAAA,GAAgBvB,CAAA,CAAmBwB,cAAA,GACrCxB,CAAA,CAAmBwB,cAAA,CAAeC,KAAA,GAClCC,UAAA,EACEC,CAAA,GAAiB3B,CAAA,CAAmBwB,cAAA,GACtCxB,CAAA,CAAmBwB,cAAA,CAAeI,MAAA,GAClCC,WAAA,EAGEC,CAAA,GAAY3B,MAAA,CAAO4B,OAAA,IAAWC,WAAA,EAC9BC,CAAA,GAAY9B,MAAA,CAAO+B,OAAA,IAAWC,WAAA,EAAAC,CAAA,GAShClD,CAAA,CAAOmD,qBAAA,IANDC,CAAA,GAAAF,CAAA,CAARR,MAAA,EACOW,CAAA,GAAAH,CAAA,CAAPX,KAAA,EACKe,CAAA,GAAAJ,CAAA,CAALK,GAAA,EACOC,CAAA,GAAAN,CAAA,CAAPO,KAAA,EACQC,CAAA,GAAAR,CAAA,CAARS,MAAA,EACMC,CAAA,GAAAV,CAAA,CAANW,IAAA,EAIEC,CAAA,GACQ,YAAV9C,CAAA,IAA+B,cAAVA,CAAA,GACjBsC,CAAA,GACU,UAAVtC,CAAA,GACA0C,CAAA,GACAJ,CAAA,GAAYF,CAAA,GAAe,GAC7BW,CAAA,GACS,aAAX3C,CAAA,GACIwC,CAAA,GAAaP,CAAA,GAAc,IAChB,UAAXjC,CAAA,GACAoC,CAAA,GACAI,CAAA,EAGAI,CAAA,GAAqC,IAElCC,CAAA,GAAQ,GAAGA,CAAA,GAAQjC,CAAA,CAAOkC,MAAA,EAAQD,CAAA,IAAS;IAClD,IAAME,CAAA,GAAQnC,CAAA,CAAOiC,CAAA;MAAAG,CAAA,GAWjBD,CAAA,CAAMhB,qBAAA;MANRkB,CAAA,GAAAD,CAAA,CAAA1B,MAAA;MACA4B,CAAA,GAAAF,CAAA,CAAA7B,KAAA;MACAgC,CAAA,GAAAH,CAAA,CAAAb,GAAA;MACAiB,CAAA,GAAAJ,CAAA,CAAAX,KAAA;MACAgB,CAAA,GAAAL,CAAA,CAAAT,MAAA;MACAe,CAAA,GAAAN,CAAA,CAAAP,IAAA;IAKF,IACiB,gBAAf9C,CAAA,IACAuC,CAAA,IAAa,KACbM,CAAA,IAAc,KACdF,CAAA,IAAgBjB,CAAA,IAChBe,CAAA,IAAenB,CAAA,IACfiB,CAAA,IAAaiB,CAAA,IACbb,CAAA,IAAgBe,CAAA,IAChBb,CAAA,IAAcc,CAAA,IACdlB,CAAA,IAAegB,CAAA,EAGf,OAAOR,CAAA;IAGT,IAAMW,CAAA,GAAapE,gBAAA,CAAiB4D,CAAA;MAC9BS,CAAA,GAAaC,QAAA,CAASF,CAAA,CAAWG,eAAA,EAA2B;MAC5DC,CAAA,GAAYF,QAAA,CAASF,CAAA,CAAWK,cAAA,EAA0B;MAC1DC,CAAA,GAAcJ,QAAA,CAASF,CAAA,CAAWO,gBAAA,EAA4B;MAC9DC,CAAA,GAAeN,QAAA,CAASF,CAAA,CAAWS,iBAAA,EAA6B;MAElEC,CAAA,GAAsB;MACtBC,CAAA,GAAuB;MAIrBC,CAAA,GACJ,iBAAiBpB,CAAA,GACZA,CAAA,CAAsBqB,WAAA,GACtBrB,CAAA,CAAsB/D,WAAA,GACvBwE,CAAA,GACAK,CAAA,GACA;MACAQ,CAAA,GACJ,kBAAkBtB,CAAA,GACbA,CAAA,CAAsBuB,YAAA,GACtBvB,CAAA,CAAsBjE,YAAA,GACvB6E,CAAA,GACAI,CAAA,GACA;IAEN,IAAIvD,CAAA,KAAqBuC,CAAA,EAIrBkB,CAAA,GADY,YAAVrE,CAAA,GACY8C,CAAA,GACK,UAAV9C,CAAA,GACK8C,CAAA,GAAcrB,CAAA,GACT,cAAVzB,CAAA,GACKV,CAAA,CACZyC,CAAA,EACAA,CAAA,GAAYN,CAAA,EACZA,CAAA,EACAsC,CAAA,EACAI,CAAA,EACApC,CAAA,GAAYe,CAAA,EACZf,CAAA,GAAYe,CAAA,GAAcV,CAAA,EAC1BA,CAAA,IAIYU,CAAA,GAAcrB,CAAA,GAAiB,GAI7C6C,CAAA,GADa,YAAXlE,CAAA,GACa2C,CAAA,GACK,aAAX3C,CAAA,GACM2C,CAAA,GAAe1B,CAAA,GAAgB,IAC1B,UAAXjB,CAAA,GACM2C,CAAA,GAAe1B,CAAA,GAGf/B,CAAA,CACbsC,CAAA,EACAA,CAAA,GAAYP,CAAA,EACZA,CAAA,EACAuC,CAAA,EACAK,CAAA,EACArC,CAAA,GAAYmB,CAAA,EACZnB,CAAA,GAAYmB,CAAA,GAAeV,CAAA,EAC3BA,CAAA,GAMJgC,CAAA,GAAcM,IAAA,CAAKC,GAAA,CAAI,GAAGP,CAAA,GAActC,CAAA,GACxCuC,CAAA,GAAeK,IAAA,CAAKC,GAAA,CAAI,GAAGN,CAAA,GAAe1C,CAAA,OACrC;MAIHyC,CAAA,GADY,YAAVrE,CAAA,GACY8C,CAAA,GAAcS,CAAA,GAAMQ,CAAA,GACf,UAAV/D,CAAA,GACK8C,CAAA,GAAcW,CAAA,GAASU,CAAA,GAAeM,CAAA,GACjC,cAAVzE,CAAA,GACKV,CAAA,CACZiE,CAAA,EACAE,CAAA,EACAJ,CAAA,EACAU,CAAA,EACAI,CAAA,GAAeM,CAAA,EACf3B,CAAA,EACAA,CAAA,GAAcV,CAAA,EACdA,CAAA,IAIYU,CAAA,IAAeS,CAAA,GAAMF,CAAA,GAAS,KAAKoB,CAAA,GAAkB,GAInEH,CAAA,GADa,YAAXlE,CAAA,GACa2C,CAAA,GAAeW,CAAA,GAAOE,CAAA,GACjB,aAAXxD,CAAA,GACM2C,CAAA,IAAgBW,CAAA,GAAOJ,CAAA,GAAQ,KAAKiB,CAAA,GAAiB,IAChD,UAAXnE,CAAA,GACM2C,CAAA,GAAeS,CAAA,GAAQS,CAAA,GAAcM,CAAA,GAGrCjF,CAAA,CACboE,CAAA,EACAF,CAAA,EACAF,CAAA,EACAM,CAAA,EACAK,CAAA,GAAcM,CAAA,EACdxB,CAAA,EACAA,CAAA,GAAeV,CAAA,EACfA,CAAA;MAvCC,IA2CGwC,CAAA,GAA0B1B,CAAA,CAA1B2B,UAAA;QAAYC,CAAA,GAAc5B,CAAA,CAAd6B,SAAA;MAkBpBlC,CAAA,IAAeiC,CAAA,IAhBfV,CAAA,GAAcM,IAAA,CAAKC,GAAA,CACjB,GACAD,IAAA,CAAKM,GAAA,CACHF,CAAA,GAAYV,CAAA,EACZlB,CAAA,CAAMhE,YAAA,GAAekE,CAAA,GAASoB,CAAA,KAalC1B,CAAA,IAAgB8B,CAAA,IAVhBP,CAAA,GAAeK,IAAA,CAAKC,GAAA,CAClB,GACAD,IAAA,CAAKM,GAAA,CACHJ,CAAA,GAAaP,CAAA,EACbnB,CAAA,CAAM9D,WAAA,GAAciE,CAAA,GAAQiB,CAAA;IAAA;IASlCvB,CAAA,CAAa7B,IAAA,CAAK;MAAE+D,EAAA,EAAI/B,CAAA;MAAOZ,GAAA,EAAK8B,CAAA;MAAaxB,IAAA,EAAMyB;IAAA;EAAA;EAGzD,OAAOtB,CAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}