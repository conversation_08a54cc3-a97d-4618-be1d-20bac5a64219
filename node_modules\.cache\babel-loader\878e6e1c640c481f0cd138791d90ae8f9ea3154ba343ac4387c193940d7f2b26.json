{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\footer\\\\footer.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PACKAGE = require('./../../../package.json');\nexport const APP_VERSION = PACKAGE.version;\nclass Footer extends React.Component {\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer mt-0 p-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-12 text-center py-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"px-2 mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"d-block d-md-inline\",\n              children: [\"\\xA9 \", new Date().getFullYear(), \" e-procurement - powered by TM Selezioni S.r.l. - Tutti i diritti sono riservati \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 50\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"d-block d-md-inline\",\n              children: [\" - v. \", APP_VERSION]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 206\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Footer;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PACKAGE", "require", "APP_VERSION", "version", "Footer", "Component", "render", "className", "children", "Date", "getFullYear", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/footer/footer.jsx"], "sourcesContent": ["import React from 'react';\n\nconst PACKAGE = require('./../../../package.json');\nexport const APP_VERSION = PACKAGE.version\n\nclass Footer extends React.Component {\n    render() {\n        return (\n            <footer className=\"footer mt-0 p-0\">\n                <div className=\"row\">\n                    <div className=\"col-md-12 text-center py-3\">\n                        <p className=\"px-2 mb-0\"><span className='d-block d-md-inline'>© {(new Date().getFullYear())} e-procurement - powered by TM Selezioni S.r.l. - Tutti i diritti sono riservati </span><span className='d-block d-md-inline'> - v. {APP_VERSION}</span></p>\n                    </div>\n                </div>\n            </footer>\n        )\n    }\n}\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AAClD,OAAO,MAAMC,WAAW,GAAGF,OAAO,CAACG,OAAO;AAE1C,MAAMC,MAAM,SAASP,KAAK,CAACQ,SAAS,CAAC;EACjCC,MAAMA,CAAA,EAAG;IACL,oBACIP,OAAA;MAAQQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC/BT,OAAA;QAAKQ,SAAS,EAAC,KAAK;QAAAC,QAAA,eAChBT,OAAA;UAAKQ,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACvCT,OAAA;YAAGQ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAACT,OAAA;cAAMQ,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,OAAE,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,mFAAiF;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAAAf,OAAA;cAAMQ,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,QAAM,EAACN,WAAW;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEjB;AACJ;AACA,eAAeV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}