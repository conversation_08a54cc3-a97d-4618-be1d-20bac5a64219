{"ast": null, "code": "var E_NOSCROLL = new Error('Element already at target scroll position');\nvar E_CANCELLED = new Error('Scroll cancelled');\nvar min = Math.min;\nvar ms = Date.now;\nmodule.exports = {\n  left: make('scrollLeft'),\n  top: make('scrollTop')\n};\nfunction make(prop) {\n  return function scroll(el, to, opts, cb) {\n    opts = opts || {};\n    if (typeof opts == 'function') cb = opts, opts = {};\n    if (typeof cb != 'function') cb = noop;\n    var start = ms();\n    var from = el[prop];\n    var ease = opts.ease || inOutSine;\n    var duration = !isNaN(opts.duration) ? +opts.duration : 350;\n    var cancelled = false;\n    return from === to ? cb(E_NOSCROLL, el[prop]) : requestAnimationFrame(animate), cancel;\n    function cancel() {\n      cancelled = true;\n    }\n    function animate(timestamp) {\n      if (cancelled) return cb(E_CANCELLED, el[prop]);\n      var now = ms();\n      var time = min(1, (now - start) / duration);\n      var eased = ease(time);\n      el[prop] = eased * (to - from) + from;\n      time < 1 ? requestAnimationFrame(animate) : requestAnimationFrame(function () {\n        cb(null, el[prop]);\n      });\n    }\n  };\n}\nfunction inOutSine(n) {\n  return 0.5 * (1 - Math.cos(Math.PI * n));\n}\nfunction noop() {}", "map": {"version": 3, "names": ["E_NOSCROLL", "Error", "E_CANCELLED", "min", "Math", "ms", "Date", "now", "module", "exports", "left", "make", "top", "prop", "scroll", "el", "to", "opts", "cb", "noop", "start", "from", "ease", "inOutSine", "duration", "isNaN", "cancelled", "requestAnimationFrame", "animate", "cancel", "timestamp", "time", "eased", "n", "cos", "PI"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/scroll/index.js"], "sourcesContent": ["var E_NOSCROLL = new Error('Element already at target scroll position')\nvar E_CANCELLED = new Error('Scroll cancelled')\nvar min = Math.min\nvar ms = Date.now\n\nmodule.exports = {\n  left: make('scrollLeft'),\n  top: make('scrollTop')\n}\n\nfunction make (prop) {\n  return function scroll (el, to, opts, cb) {\n    opts = opts || {}\n\n    if (typeof opts == 'function') cb = opts, opts = {}\n    if (typeof cb != 'function') cb = noop\n\n    var start = ms()\n    var from = el[prop]\n    var ease = opts.ease || inOutSine\n    var duration = !isNaN(opts.duration) ? +opts.duration : 350\n    var cancelled = false\n\n    return from === to ?\n      cb(E_NOSCROLL, el[prop]) :\n      requestAnimationFrame(animate), cancel\n\n    function cancel () {\n      cancelled = true\n    }\n\n    function animate (timestamp) {\n      if (cancelled) return cb(E_CANCELLED, el[prop])\n\n      var now = ms()\n      var time = min(1, ((now - start) / duration))\n      var eased = ease(time)\n\n      el[prop] = (eased * (to - from)) + from\n\n      time < 1 ?\n        requestAnimationFrame(animate) :\n        requestAnimationFrame(function () {\n          cb(null, el[prop])\n        })\n    }\n  }\n}\n\nfunction inOutSine (n) {\n  return 0.5 * (1 - Math.cos(Math.PI * n))\n}\n\nfunction noop () {}\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,IAAIC,KAAK,CAAC,2CAA2C,CAAC;AACvE,IAAIC,WAAW,GAAG,IAAID,KAAK,CAAC,kBAAkB,CAAC;AAC/C,IAAIE,GAAG,GAAGC,IAAI,CAACD,GAAG;AAClB,IAAIE,EAAE,GAAGC,IAAI,CAACC,GAAG;AAEjBC,MAAM,CAACC,OAAO,GAAG;EACfC,IAAI,EAAEC,IAAI,CAAC,YAAY,CAAC;EACxBC,GAAG,EAAED,IAAI,CAAC,WAAW;AACvB,CAAC;AAED,SAASA,IAAIA,CAAEE,IAAI,EAAE;EACnB,OAAO,SAASC,MAAMA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;IACxCD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IAEjB,IAAI,OAAOA,IAAI,IAAI,UAAU,EAAEC,EAAE,GAAGD,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;IACnD,IAAI,OAAOC,EAAE,IAAI,UAAU,EAAEA,EAAE,GAAGC,IAAI;IAEtC,IAAIC,KAAK,GAAGf,EAAE,CAAC,CAAC;IAChB,IAAIgB,IAAI,GAAGN,EAAE,CAACF,IAAI,CAAC;IACnB,IAAIS,IAAI,GAAGL,IAAI,CAACK,IAAI,IAAIC,SAAS;IACjC,IAAIC,QAAQ,GAAG,CAACC,KAAK,CAACR,IAAI,CAACO,QAAQ,CAAC,GAAG,CAACP,IAAI,CAACO,QAAQ,GAAG,GAAG;IAC3D,IAAIE,SAAS,GAAG,KAAK;IAErB,OAAOL,IAAI,KAAKL,EAAE,GAChBE,EAAE,CAAClB,UAAU,EAAEe,EAAE,CAACF,IAAI,CAAC,CAAC,GACxBc,qBAAqB,CAACC,OAAO,CAAC,EAAEC,MAAM;IAExC,SAASA,MAAMA,CAAA,EAAI;MACjBH,SAAS,GAAG,IAAI;IAClB;IAEA,SAASE,OAAOA,CAAEE,SAAS,EAAE;MAC3B,IAAIJ,SAAS,EAAE,OAAOR,EAAE,CAAChB,WAAW,EAAEa,EAAE,CAACF,IAAI,CAAC,CAAC;MAE/C,IAAIN,GAAG,GAAGF,EAAE,CAAC,CAAC;MACd,IAAI0B,IAAI,GAAG5B,GAAG,CAAC,CAAC,EAAG,CAACI,GAAG,GAAGa,KAAK,IAAII,QAAS,CAAC;MAC7C,IAAIQ,KAAK,GAAGV,IAAI,CAACS,IAAI,CAAC;MAEtBhB,EAAE,CAACF,IAAI,CAAC,GAAImB,KAAK,IAAIhB,EAAE,GAAGK,IAAI,CAAC,GAAIA,IAAI;MAEvCU,IAAI,GAAG,CAAC,GACNJ,qBAAqB,CAACC,OAAO,CAAC,GAC9BD,qBAAqB,CAAC,YAAY;QAChCT,EAAE,CAAC,IAAI,EAAEH,EAAE,CAACF,IAAI,CAAC,CAAC;MACpB,CAAC,CAAC;IACN;EACF,CAAC;AACH;AAEA,SAASU,SAASA,CAAEU,CAAC,EAAE;EACrB,OAAO,GAAG,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,GAAG,CAAC9B,IAAI,CAAC+B,EAAE,GAAGF,CAAC,CAAC,CAAC;AAC1C;AAEA,SAASd,IAAIA,CAAA,EAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}