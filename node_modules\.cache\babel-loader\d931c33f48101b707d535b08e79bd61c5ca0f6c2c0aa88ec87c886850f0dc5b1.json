{"ast": null, "code": "import canUseDom from './canUseDom';\nvar isStyleNameSupport = function isStyleNameSupport(styleName) {\n  if (canUseDom() && window.document.documentElement) {\n    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    var documentElement = window.document.documentElement;\n    return styleNameList.some(function (name) {\n      return name in documentElement.style;\n    });\n  }\n  return false;\n};\nvar isStyleValueSupport = function isStyleValueSupport(styleName, value) {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n  var ele = document.createElement('div');\n  var origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n  return isStyleNameSupport(styleName);\n}", "map": {"version": 3, "names": ["canUseDom", "isStyleNameSupport", "styleName", "window", "document", "documentElement", "styleNameList", "Array", "isArray", "some", "name", "style", "isStyleValueSupport", "value", "ele", "createElement", "origin", "isStyleSupport", "styleValue", "undefined"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/es/Dom/styleChecker.js"], "sourcesContent": ["import canUseDom from './canUseDom';\n\nvar isStyleNameSupport = function isStyleNameSupport(styleName) {\n  if (canUseDom() && window.document.documentElement) {\n    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    var documentElement = window.document.documentElement;\n    return styleNameList.some(function (name) {\n      return name in documentElement.style;\n    });\n  }\n\n  return false;\n};\n\nvar isStyleValueSupport = function isStyleValueSupport(styleName, value) {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n\n  var ele = document.createElement('div');\n  var origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\n\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n\n  return isStyleNameSupport(styleName);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AAEnC,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAE;EAC9D,IAAIF,SAAS,CAAC,CAAC,IAAIG,MAAM,CAACC,QAAQ,CAACC,eAAe,EAAE;IAClD,IAAIC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACtE,IAAIG,eAAe,GAAGF,MAAM,CAACC,QAAQ,CAACC,eAAe;IACrD,OAAOC,aAAa,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;MACxC,OAAOA,IAAI,IAAIL,eAAe,CAACM,KAAK;IACtC,CAAC,CAAC;EACJ;EAEA,OAAO,KAAK;AACd,CAAC;AAED,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACV,SAAS,EAAEW,KAAK,EAAE;EACvE,IAAI,CAACZ,kBAAkB,CAACC,SAAS,CAAC,EAAE;IAClC,OAAO,KAAK;EACd;EAEA,IAAIY,GAAG,GAAGV,QAAQ,CAACW,aAAa,CAAC,KAAK,CAAC;EACvC,IAAIC,MAAM,GAAGF,GAAG,CAACH,KAAK,CAACT,SAAS,CAAC;EACjCY,GAAG,CAACH,KAAK,CAACT,SAAS,CAAC,GAAGW,KAAK;EAC5B,OAAOC,GAAG,CAACH,KAAK,CAACT,SAAS,CAAC,KAAKc,MAAM;AACxC,CAAC;AAED,OAAO,SAASC,cAAcA,CAACf,SAAS,EAAEgB,UAAU,EAAE;EACpD,IAAI,CAACX,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,IAAIgB,UAAU,KAAKC,SAAS,EAAE;IACzD,OAAOP,mBAAmB,CAACV,SAAS,EAAEgB,UAAU,CAAC;EACnD;EAEA,OAAOjB,kBAAkB,CAACC,SAAS,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}