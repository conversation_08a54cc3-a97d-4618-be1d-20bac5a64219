# [@cypress/vue-v4.2.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v4.2.0...@cypress/vue-v4.2.1) (2022-10-13)


### Bug Fixes

* fix regression in npm/vue ([#23954](https://github.com/cypress-io/cypress/issues/23954)) ([78779a2](https://github.com/cypress-io/cypress/commit/78779a2db13ca6555a6b830dbabeefd3d37bbfe5))
* **npm/vue:** update types ([#23890](https://github.com/cypress-io/cypress/issues/23890)) ([eb8ae02](https://github.com/cypress-io/cypress/commit/eb8ae02b61304d034136f7627da1ab23537e3ba4))

# [@cypress/vue-v4.2.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v4.1.0...@cypress/vue-v4.2.0) (2022-08-30)


### Features

* adding svelte component testing support ([#23553](https://github.com/cypress-io/cypress/issues/23553)) ([f6eaad4](https://github.com/cypress-io/cypress/commit/f6eaad40e1836fa9db87c60defa5ae6f390c8fd8))

# [@cypress/vue-v4.1.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v4.0.0...@cypress/vue-v4.1.0) (2022-08-11)


### Bug Fixes

* remove CT side effects from mount when e2e testing ([#22633](https://github.com/cypress-io/cypress/issues/22633)) ([a9476ec](https://github.com/cypress-io/cypress/commit/a9476ecb3d43f628b689e060294a1952937cb1a7))
* remove dependency causing semantic-release to fail ([#23142](https://github.com/cypress-io/cypress/issues/23142)) ([20f89bf](https://github.com/cypress-io/cypress/commit/20f89bfa32636baa8922896e719962c703129abd))


### Features

* **npm/vue:** expose Test Utils API ([#22757](https://github.com/cypress-io/cypress/issues/22757)) ([8e07318](https://github.com/cypress-io/cypress/commit/8e07318a9f72c3df012be47d500007571165a87e))
* update to Vite 3 ([#22915](https://github.com/cypress-io/cypress/issues/22915)) ([6adba46](https://github.com/cypress-io/cypress/commit/6adba462ea6b76dbb96f99aa3837492ca1f17ed3))

# [@cypress/vue-v4.0.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.1.2...@cypress/vue-v4.0.0) (2022-06-13)


### Bug Fixes

* display cy.mount command log ([#21500](https://github.com/cypress-io/cypress/issues/21500)) ([140b4ba](https://github.com/cypress-io/cypress/commit/140b4ba2110243712a614a39b2408c30cce4d0b1))
* Doc changes around vue2 ([#21066](https://github.com/cypress-io/cypress/issues/21066)) ([17905a7](https://github.com/cypress-io/cypress/commit/17905a79ee5106b0d72c8e74bb717fcd7b796dee))


### chore

* prep npm packages for use with Cypress v10 ([b924d08](https://github.com/cypress-io/cypress/commit/b924d086ee2e2ccc93303731e001b2c9e9d0af17))


### Features

* Update "typescript" dev dependency ([e977b60](https://github.com/cypress-io/cypress/commit/e977b60521b863a227eb37189ab1b3081af00d9f))
* embedding mount into the cypress binary (real dependency) ([#20930](https://github.com/cypress-io/cypress/issues/20930)) ([3fe5f50](https://github.com/cypress-io/cypress/commit/3fe5f50e7832a4bfb20df8e71648434eb7f263d5))
* swap the #__cy_root id selector to become data-cy-root for component mounting ([#20951](https://github.com/cypress-io/cypress/issues/20951)) ([0e7b555](https://github.com/cypress-io/cypress/commit/0e7b555f93fb403f431c5de4a07ae7ad6ac89ba2))
* update on-links ([#19235](https://github.com/cypress-io/cypress/issues/19235)) ([cc2d734](https://github.com/cypress-io/cypress/commit/cc2d7348185e2a090c60d92d9319ab460d8c7827))
* Update "@vue/test-utils" dependency ([#20956](https://github.com/cypress-io/cypress/issues/20956)) ([57659c4](https://github.com/cypress-io/cypress/commit/57659c42468591265143aae2ff06bae4e440085f))


### BREAKING CHANGES

* new version of packages for Cypress v10

# [@cypress/vue-v3.1.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.1.1...@cypress/vue-v3.1.2) (2022-05-03)


### Bug Fixes

* head content reset, fix [#19721](https://github.com/cypress-io/cypress/issues/19721) ([#21291](https://github.com/cypress-io/cypress/issues/21291)) ([77ab6a5](https://github.com/cypress-io/cypress/commit/77ab6a51a0de1929171a2275e9cec9580c57241d))

# [@cypress/vue-v3.1.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.1.0...@cypress/vue-v3.1.1) (2022-02-10)


### Bug Fixes

* create a dummy commit to trigger release ([97e6c14](https://github.com/cypress-io/cypress/commit/97e6c14b91661658b856038da8a0f5fa4319b19b))

# [@cypress/vue-v3.1.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.5...@cypress/vue-v3.1.0) (2021-12-16)


### Features

* use hoisted yarn install in binary build ([#17285](https://github.com/cypress-io/cypress/issues/17285)) ([e4f5b10](https://github.com/cypress-io/cypress/commit/e4f5b106d49d6ac0857c5fdac886f83b99558c88))

# [@cypress/vue-v3.0.5](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.4...@cypress/vue-v3.0.5) (2021-11-10)


### Bug Fixes

* remove outdated npm registry links ([#18727](https://github.com/cypress-io/cypress/issues/18727)) ([4ded6c9](https://github.com/cypress-io/cypress/commit/4ded6c9624134fe6203f5377d62d62809cd27cda))

# [@cypress/vue-v3.0.4](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.3...@cypress/vue-v3.0.4) (2021-10-29)


### Bug Fixes

* remove outdated registry link ([#18710](https://github.com/cypress-io/cypress/issues/18710)) ([e2a869d](https://github.com/cypress-io/cypress/commit/e2a869d2a984abb6703aec966dd9124ee693b8c1))

# [@cypress/vue-v3.0.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.2...@cypress/vue-v3.0.3) (2021-07-31)


### Bug Fixes

* vue 3 types, beta suffix & component name ([#17508](https://github.com/cypress-io/cypress/issues/17508)) ([b4733a6](https://github.com/cypress-io/cypress/commit/b4733a6179577b0447af9bcdcf417713d5fb38b9))

# [@cypress/vue-v3.0.0-beta.4](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-beta.3...@cypress/vue-v3.0.0-beta.4) (2021-07-27)


### Bug Fixes

* do not register CT specific event in e2e mode ([5836203](https://github.com/cypress-io/cypress/commit/58362037fd231c6fdc587e422fc139bf7546ac2d))

# [@cypress/vue-v3.0.0-beta.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-beta.2...@cypress/vue-v3.0.0-beta.3) (2021-06-24)


### Bug Fixes

* bumps deps for npm/vue ([#17096](https://github.com/cypress-io/cypress/issues/17096)) ([96af8bf](https://github.com/cypress-io/cypress/commit/96af8bf29d69537af0c10c69b33a0a889edcaa37))

# [@cypress/vue-v3.0.0-beta.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-beta.1...@cypress/vue-v3.0.0-beta.2) (2021-06-17)


### Bug Fixes

* add latest channel to properly release npm packages ([#16994](https://github.com/cypress-io/cypress/issues/16994)) ([ac16efc](https://github.com/cypress-io/cypress/commit/ac16efca80f33e12153b0c2bd0fc3f04983ed305))

# [@cypress/vue-v3.0.0-beta.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.2.3...@cypress/vue-v3.0.0-beta.1) (2021-05-31)


### Continuous Integration

* deliver vue3 on master ([#16728](https://github.com/cypress-io/cypress/issues/16728)) ([0ee001f](https://github.com/cypress-io/cypress/commit/0ee001f6250604711653caf5365d8aca063a9cad)), closes [#15100](https://github.com/cypress-io/cypress/issues/15100)


### BREAKING CHANGES

* no support for vue 2 anymore

* build: disable auto deliver of next vue

* Revert "feat(vue): vue 3 support in @cypress/vue"

This reverts commit 8f55d7baaff1f240677239ae5fdc4180c4a06475.

* Revert "build: disable auto deliver of next vue"

This reverts commit ed46c9e5c551e57901dbdc75db2e83bf194c4b18.

* chore: release @cypress/vue-v1.1.0-alpha.1

[skip ci]
* dropped support for vue 2 in favor of vue 3

* test: remove filter tests not relevant in vue 3

* build: try publishing as a private new major

* chore: release @cypress/vue-v3.0.0-alpha.1

[skip ci]

* chore: bring back access public

* fix: update dependency to webpack dev server

* chore: release @cypress/vue-v3.0.0-alpha.2

[skip ci]

* chore: remove unnecessary dependency

* fix: mistreatment of monorepo dependency

* chore: release @cypress/vue-v3.0.0-alpha.3

[skip ci]

* chore: release @cypress/vue-v3.0.0-alpha.4

[skip ci]

* fix: use __cy_root at the root element

* build: avoid using array spread (tslib imports issue)

* fix: setup for cypress vue tests

* fix: add cleanup event

* test: make sure we use the right build of compiler

* chore: downgrade VTU to rc-1

* chore: release @cypress/vue-v3.0.0

[skip ci]

* chore: upgrade vue version to 3.0.11

* fix: adjust optional peer deps

* fix: allow fo any VTU 2 version using ^

* test: ignore nuxt example

* test: update yarn lock on vue cli

* chore: release @cypress/vue-v3.0.1

[skip ci]

* ci: release vue@next on master

* test: fix vue3 examples

* ci: open only needed server in circle npm-vue

Co-authored-by: semantic-release-bot <<EMAIL>>
Co-authored-by: Lachlan Miller <<EMAIL>>

# [@cypress/vue-v3.0.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0...@cypress/vue-v3.0.1) (2021-04-08)

### Bug Fixes

* adjust optional peer deps ([735d61d](https://github.com/cypress-io/cypress/commit/735d61d15a34c04729bff692861faad2dc1b6a35))
* allow fo any VTU 2 version using ^ ([a1da052](https://github.com/cypress-io/cypress/commit/a1da0520651eaa1d83db96fe9d692b28ad2fc727))

# [@cypress/vue-v3.0.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.2.0...@cypress/vue-v3.0.0) (2021-04-07)


### Bug Fixes

* add cleanup event ([cf51935](https://github.com/cypress-io/cypress/commit/cf51935bf5a49594b012e9a6bd9cabe983fbe500))
* mistreatment of monorepo dependency ([a21afb2](https://github.com/cypress-io/cypress/commit/a21afb2f2204debd191586bcb250bb64ecbdfd25))
* setup for cypress vue tests ([c27dc77](https://github.com/cypress-io/cypress/commit/c27dc77ecdb9b33c68737db690432e0418181286))
* update dependency to webpack dev server ([717ea3a](https://github.com/cypress-io/cypress/commit/717ea3a628c26743a9fe8868e01291ad6b8c0977))
* use __cy_root at the root element ([9cf102a](https://github.com/cypress-io/cypress/commit/9cf102ad6248a90a91ca766e2ff9267db0f208fa))


### Features

* vue3 support for @cypress/vue ([#15100](https://github.com/cypress-io/cypress/issues/15100)) ([71e85a0](https://github.com/cypress-io/cypress/commit/71e85a03682d577344e705548b5350ec84c29382))


### Reverts

* Revert "feat(vue): vue 3 support in @cypress/vue" ([53fc995](https://github.com/cypress-io/cypress/commit/53fc9958d111a8e60c6dcd873c9d89666c86dfc8))


### BREAKING CHANGES

* dropped support for vue 2 in favor of vue 3

* test: remove filter tests not relevant in vue 3

# [@cypress/vue-v3.0.0-alpha.4](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-alpha.3...@cypress/vue-v3.0.0-alpha.4) (2021-03-10)


### Bug Fixes

* **component-testing:** video recording for single browser session mode ([#15328](https://github.com/cypress-io/cypress/issues/15328)) ([cce08d2](https://github.com/cypress-io/cypress/commit/cce08d23b781b219635d43419e5b6177927e1ba5))

# [@cypress/vue-v3.0.0-alpha.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-alpha.2...@cypress/vue-v3.0.0-alpha.3) (2021-02-24)


### Bug Fixes

* mistreatment of monorepo dependency ([a21afb2](https://github.com/cypress-io/cypress/commit/a21afb2f2204debd191586bcb250bb64ecbdfd25))

# [@cypress/vue-v3.0.0-alpha.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v3.0.0-alpha.1...@cypress/vue-v3.0.0-alpha.2) (2021-02-24)


### Bug Fixes

* make webpack-dev-server a peer dependency ([#15163](https://github.com/cypress-io/cypress/issues/15163)) ([fa969fb](https://github.com/cypress-io/cypress/commit/fa969fba78d86494b5d920f573768677301fad13))
* update dependency to webpack dev server ([717ea3a](https://github.com/cypress-io/cypress/commit/717ea3a628c26743a9fe8868e01291ad6b8c0977))

# [@cypress/vue-v3.0.0-alpha.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.0.1...@cypress/vue-v3.0.0-alpha.1) (2021-02-18)


### Features

* vue3 support for @cypress/vue ([#15100](https://github.com/cypress-io/cypress/issues/15100)) ([71e85a0](https://github.com/cypress-io/cypress/commit/71e85a03682d577344e705548b5350ec84c29382))


### Reverts

* Revert "feat(vue): vue 3 support in @cypress/vue" ([53fc995](https://github.com/cypress-io/cypress/commit/53fc9958d111a8e60c6dcd873c9d89666c86dfc8))


### BREAKING CHANGES

* dropped support for vue 2 in favor of vue 3

* test: remove filter tests not relevant in vue 3

# [@cypress/vue-v2.2.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.2.2...@cypress/vue-v2.2.3) (2021-05-11)

### Bug Fixes

* accept webapck 4 & 5 as peer dependencies of @cypress/vue and @cypress/react ([#16290](https://github.com/cypress-io/cypress/issues/16290)) ([c4151fb](https://github.com/cypress-io/cypress/commit/c4151fbd9f3c10de28e3e8dd3a75d0e0973b52e2))

# [@cypress/vue-v2.2.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.2.1...@cypress/vue-v2.2.2) (2021-05-05)

* accept webapck 4 & 5 as peer dependencies of @cypress/vue and @cypress/react ([#16290](https://github.com/cypress-io/cypress/issues/16290)) ([500cab9](https://github.com/cypress-io/cypress/commit/500cab95ef7a7d6b74b366ba8066bcf73f2955aa))

# [@cypress/vue-v2.2.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.2.0...@cypress/vue-v2.2.1) (2021-04-21)

* improve handling of userland injected styles in component testing ([#16024](https://github.com/cypress-io/cypress/issues/16024)) ([fe0b63c](https://github.com/cypress-io/cypress/commit/fe0b63c299947470c9cdce3a0d00364a1e224bdb))

# [@cypress/vue-v2.2.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.1.1...@cypress/vue-v2.2.0) (2021-04-05)


### Bug Fixes

* **@cypress/react:** Devtools unpredictable resets ([#15612](https://github.com/cypress-io/cypress/issues/15612)) ([b1f831a](https://github.com/cypress-io/cypress/commit/b1f831a86a8bcc6646067bc8a9e67871026ff575)), closes [#15634](https://github.com/cypress-io/cypress/issues/15634)


### Features

* simplify vite server ([#15416](https://github.com/cypress-io/cypress/issues/15416)) ([adc2fc8](https://github.com/cypress-io/cypress/commit/adc2fc893fbf32f1f6121d18ddb8a8096e5ebf39))
* support ct/e2e specific overrides in cypress.json ([#15526](https://github.com/cypress-io/cypress/issues/15526)) ([43c8ae2](https://github.com/cypress-io/cypress/commit/43c8ae2a7c20ba70a0bb0b45b8f6a086e2782f29))

# [@cypress/vue-v2.1.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.1.0...@cypress/vue-v2.1.1) (2021-03-16)


### Bug Fixes

* Revert cypress.json changes ([#15499](https://github.com/cypress-io/cypress/issues/15499)) ([237c426](https://github.com/cypress-io/cypress/commit/237c426707714a287ff20ef2bdabff5f0c39e93a))

# [@cypress/vue-v2.1.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.0.2...@cypress/vue-v2.1.0) (2021-03-15)


### Bug Fixes

* **component-testing:** video recording for single browser session mode ([#15328](https://github.com/cypress-io/cypress/issues/15328)) ([cce08d2](https://github.com/cypress-io/cypress/commit/cce08d23b781b219635d43419e5b6177927e1ba5))
* make webpack-dev-server a peer dependency ([#15163](https://github.com/cypress-io/cypress/issues/15163)) ([fa969fb](https://github.com/cypress-io/cypress/commit/fa969fba78d86494b5d920f573768677301fad13))


### Features

* support ct/e2e specific overrides in cypress.json ([#15444](https://github.com/cypress-io/cypress/issues/15444)) ([a94c9d5](https://github.com/cypress-io/cypress/commit/a94c9d5ef0da8559f20391fc14396d71fdca7a2f))

# [@cypress/vue-v2.0.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.0.1...@cypress/vue-v2.0.2) (2021-03-10)


### Bug Fixes

* trigger release of the packages ([#15405](https://github.com/cypress-io/cypress/issues/15405)) ([1ce5755](https://github.com/cypress-io/cypress/commit/1ce57554e260850472cf753de68858f47b3f7b3d))

# [@cypress/vue-v2.0.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v2.0.0...@cypress/vue-v2.0.1) (2021-02-17)


### Bug Fixes

* trigger semantic release ([#15128](https://github.com/cypress-io/cypress/issues/15128)) ([3a6f3b1](https://github.com/cypress-io/cypress/commit/3a6f3b1928277f7086062b1107f424e5a0247e00))

# [@cypress/vue-v2.0.0](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.0...@cypress/vue-v2.0.0) (2021-02-16)


### Bug Fixes

* code coverage for vue ([68032c1](https://github.com/cypress-io/cypress/commit/68032c1fcb19999a917c89374ea0a85a9c0a9150))
* update dependencies of npm/react-vue ([#15095](https://github.com/cypress-io/cypress/issues/15095)) ([e028262](https://github.com/cypress-io/cypress/commit/e028262aed485865c4f40162c1f8102970ef91f8))
* **Component Testing:** Broken links in docs ([#14251](https://github.com/cypress-io/cypress/issues/14251)) ([a72529f](https://github.com/cypress-io/cypress/commit/a72529f396baee669c9b112d9296d314177f8cc1))
* **deps:** update dependency debug to version 4.3.1 🌟 ([#14583](https://github.com/cypress-io/cypress/issues/14583)) ([9be6165](https://github.com/cypress-io/cypress/commit/9be61657f4150ba5dee7b67f806d810f3106d13b))
* add discord chat link for component testing readme ([#8780](https://github.com/cypress-io/cypress/issues/8780)) ([529abdc](https://github.com/cypress-io/cypress/commit/529abdc07c9848fabacf3e304d1dad0def18ebc3))
* adding build-prod tasks to all of the npm dependencies that need artifacts ([#9045](https://github.com/cypress-io/cypress/issues/9045)) ([550c05c](https://github.com/cypress-io/cypress/commit/550c05cc3d7a2a179de21138ae5f8118277df6ef))
* adding build-prod tasks to all of the npm dependencies that need artifacts ([#9046](https://github.com/cypress-io/cypress/issues/9046)) ([462829b](https://github.com/cypress-io/cypress/commit/462829bea1d903b0f1666d4ef2dd85e56636b725))
* Fix cypress-vue-unit-test reference ([#8756](https://github.com/cypress-io/cypress/issues/8756)) ([fa253d2](https://github.com/cypress-io/cypress/commit/fa253d24c04d4f4cecca94729651fcacd48b4ff1))
* make imported @cypress/react working and pass CI ([#8718](https://github.com/cypress-io/cypress/issues/8718)) ([5e4b638](https://github.com/cypress-io/cypress/commit/5e4b6383854a78d10249621ffea9e4e20effe192))
* reset head between tests to avoid style bleed ([#8828](https://github.com/cypress-io/cypress/issues/8828)) ([ee7b819](https://github.com/cypress-io/cypress/commit/ee7b8196c8c0e0a9a55b44885e8f43f6120d4869))
* update bugs link in package.json ([#9015](https://github.com/cypress-io/cypress/issues/9015)) ([34186cb](https://github.com/cypress-io/cypress/commit/34186cb8b76c230a2506cabb0358d44c3205e0c4))


### Features

* component testing ([#14479](https://github.com/cypress-io/cypress/issues/14479)) ([af26fbe](https://github.com/cypress-io/cypress/commit/af26fbebe6bc609132013a0493a116cc78bb1bd4))
* create-cypress-tests wizard ([#8857](https://github.com/cypress-io/cypress/issues/8857)) ([21ee591](https://github.com/cypress-io/cypress/commit/21ee591d1e9c4083a0c67f2062ced92708c0cedd))


### BREAKING CHANGES

* change of architecture for
component testing

Co-authored-by: Dmitriy Kovalenko <<EMAIL>>

# [@cypress/vue-v1.1.0-alpha.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.1.0-alpha.2...@cypress/vue-v1.1.0-alpha.3) (2021-01-27)


### Bug Fixes

* **deps:** update dependency debug to version 4.3.1 🌟 ([#14583](https://github.com/cypress-io/cypress/issues/14583)) ([9be6165](https://github.com/cypress-io/cypress/commit/9be61657f4150ba5dee7b67f806d810f3106d13b))

# [@cypress/vue-v1.1.0-alpha.2](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.1.0-alpha.1...@cypress/vue-v1.1.0-alpha.2) (2021-01-05)


### Bug Fixes

* **Component Testing:** Broken links in docs ([#14251](https://github.com/cypress-io/cypress/issues/14251)) ([a72529f](https://github.com/cypress-io/cypress/commit/a72529f396baee669c9b112d9296d314177f8cc1))

# [@cypress/vue-v1.1.0-alpha.1](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.7...@cypress/vue-v1.1.0-alpha.1) (2020-11-30)


### Features

* create-cypress-tests wizard ([#8857](https://github.com/cypress-io/cypress/issues/8857)) ([21ee591](https://github.com/cypress-io/cypress/commit/21ee591d1e9c4083a0c67f2062ced92708c0cedd))

# [@cypress/vue-v1.0.1-alpha.7](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.6...@cypress/vue-v1.0.1-alpha.7) (2020-11-10)


### Bug Fixes

* adding build-prod tasks to all of the npm dependencies that need artifacts ([#9045](https://github.com/cypress-io/cypress/issues/9045)) ([550c05c](https://github.com/cypress-io/cypress/commit/550c05cc3d7a2a179de21138ae5f8118277df6ef))

# [@cypress/vue-v1.0.1-alpha.6](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.5...@cypress/vue-v1.0.1-alpha.6) (2020-10-30)


### Bug Fixes

* adding build-prod tasks to all of the npm dependencies that need artifacts ([#9046](https://github.com/cypress-io/cypress/issues/9046)) ([462829b](https://github.com/cypress-io/cypress/commit/462829bea1d903b0f1666d4ef2dd85e56636b725))

# [@cypress/vue-v1.0.1-alpha.5](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.4...@cypress/vue-v1.0.1-alpha.5) (2020-10-29)


### Bug Fixes

* update bugs link in package.json ([#9015](https://github.com/cypress-io/cypress/issues/9015)) ([34186cb](https://github.com/cypress-io/cypress/commit/34186cb8b76c230a2506cabb0358d44c3205e0c4))

# [@cypress/vue-v1.0.1-alpha.4](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.3...@cypress/vue-v1.0.1-alpha.4) (2020-10-14)


### Bug Fixes

* reset head between tests to avoid style bleed ([#8828](https://github.com/cypress-io/cypress/issues/8828)) ([ee7b819](https://github.com/cypress-io/cypress/commit/ee7b8196c8c0e0a9a55b44885e8f43f6120d4869))

# [@cypress/vue-v1.0.1-alpha.3](https://github.com/cypress-io/cypress/compare/@cypress/vue-v1.0.1-alpha.2...@cypress/vue-v1.0.1-alpha.3) (2020-10-14)


### Bug Fixes

* make imported @cypress/react working and pass CI ([#8718](https://github.com/cypress-io/cypress/issues/8718)) ([5e4b638](https://github.com/cypress-io/cypress/commit/5e4b6383854a78d10249621ffea9e4e20effe192))
