{"ast": null, "code": "import * as React from 'react';\nvar SelectContext = /*#__PURE__*/React.createContext(null);\nexport default SelectContext;", "map": {"version": 3, "names": ["React", "SelectContext", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-select/es/SelectContext.js"], "sourcesContent": ["import * as React from 'react';\nvar SelectContext = /*#__PURE__*/React.createContext(null);\nexport default SelectContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}