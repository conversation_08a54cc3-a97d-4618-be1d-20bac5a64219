{"ast": null, "code": "var global = require('../internals/global');\nvar String = global.String;\nmodule.exports = function (argument) {\n  try {\n    return String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};", "map": {"version": 3, "names": ["global", "require", "String", "module", "exports", "argument", "error"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/core-js-pure/internals/try-to-string.js"], "sourcesContent": ["var global = require('../internals/global');\n\nvar String = global.String;\n\nmodule.exports = function (argument) {\n  try {\n    return String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE3C,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;AAE1BC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAI;IACF,OAAOH,MAAM,CAACG,QAAQ,CAAC;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,QAAQ;EACjB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}