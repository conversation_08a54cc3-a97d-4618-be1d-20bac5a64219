{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\"; // fix ssr render\n\nvar defaultContainer = canUseDom() ? window : null;\n/** Sticky header hooks */\n\nexport default function useSticky(sticky, prefixCls) {\n  var _ref = _typeof(sticky) === 'object' ? sticky : {},\n    _ref$offsetHeader = _ref.offsetHeader,\n    offsetHeader = _ref$offsetHeader === void 0 ? 0 : _ref$offsetHeader,\n    _ref$offsetSummary = _ref.offsetSummary,\n    offsetSummary = _ref$offsetSummary === void 0 ? 0 : _ref$offsetSummary,\n    _ref$offsetScroll = _ref.offsetScroll,\n    offsetScroll = _ref$offsetScroll === void 0 ? 0 : _ref$offsetScroll,\n    _ref$getContainer = _ref.getContainer,\n    getContainer = _ref$getContainer === void 0 ? function () {\n      return defaultContainer;\n    } : _ref$getContainer;\n  var container = getContainer() || defaultContainer;\n  return React.useMemo(function () {\n    var isSticky = !!sticky;\n    return {\n      isSticky: isSticky,\n      stickyClassName: isSticky ? \"\".concat(prefixCls, \"-sticky-holder\") : '',\n      offsetHeader: offsetHeader,\n      offsetSummary: offsetSummary,\n      offsetScroll: offsetScroll,\n      container: container\n    };\n  }, [offsetScroll, offsetHeader, offsetSummary, prefixCls, container]);\n}", "map": {"version": 3, "names": ["_typeof", "React", "canUseDom", "defaultContainer", "window", "useSticky", "sticky", "prefixCls", "_ref", "_ref$offsetHeader", "offsetHeader", "_ref$offsetSummary", "offsetSummary", "_ref$offsetScroll", "offsetScroll", "_ref$getContainer", "getContainer", "container", "useMemo", "isSticky", "stickyClassName", "concat"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/hooks/useSticky.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\"; // fix ssr render\n\nvar defaultContainer = canUseDom() ? window : null;\n/** Sticky header hooks */\n\nexport default function useSticky(sticky, prefixCls) {\n  var _ref = _typeof(sticky) === 'object' ? sticky : {},\n      _ref$offsetHeader = _ref.offsetHeader,\n      offsetHeader = _ref$offsetHeader === void 0 ? 0 : _ref$offsetHeader,\n      _ref$offsetSummary = _ref.offsetSummary,\n      offsetSummary = _ref$offsetSummary === void 0 ? 0 : _ref$offsetSummary,\n      _ref$offsetScroll = _ref.offsetScroll,\n      offsetScroll = _ref$offsetScroll === void 0 ? 0 : _ref$offsetScroll,\n      _ref$getContainer = _ref.getContainer,\n      getContainer = _ref$getContainer === void 0 ? function () {\n    return defaultContainer;\n  } : _ref$getContainer;\n\n  var container = getContainer() || defaultContainer;\n  return React.useMemo(function () {\n    var isSticky = !!sticky;\n    return {\n      isSticky: isSticky,\n      stickyClassName: isSticky ? \"\".concat(prefixCls, \"-sticky-holder\") : '',\n      offsetHeader: offsetHeader,\n      offsetSummary: offsetSummary,\n      offsetScroll: offsetScroll,\n      container: container\n    };\n  }, [offsetScroll, offsetHeader, offsetSummary, prefixCls, container]);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B,CAAC,CAAC;;AAElD,IAAIC,gBAAgB,GAAGD,SAAS,CAAC,CAAC,GAAGE,MAAM,GAAG,IAAI;AAClD;;AAEA,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACnD,IAAIC,IAAI,GAAGR,OAAO,CAACM,MAAM,CAAC,KAAK,QAAQ,GAAGA,MAAM,GAAG,CAAC,CAAC;IACjDG,iBAAiB,GAAGD,IAAI,CAACE,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;IACnEE,kBAAkB,GAAGH,IAAI,CAACI,aAAa;IACvCA,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IACtEE,iBAAiB,GAAGL,IAAI,CAACM,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;IACnEE,iBAAiB,GAAGP,IAAI,CAACQ,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,YAAY;MAC5D,OAAOZ,gBAAgB;IACzB,CAAC,GAAGY,iBAAiB;EAErB,IAAIE,SAAS,GAAGD,YAAY,CAAC,CAAC,IAAIb,gBAAgB;EAClD,OAAOF,KAAK,CAACiB,OAAO,CAAC,YAAY;IAC/B,IAAIC,QAAQ,GAAG,CAAC,CAACb,MAAM;IACvB,OAAO;MACLa,QAAQ,EAAEA,QAAQ;MAClBC,eAAe,EAAED,QAAQ,GAAG,EAAE,CAACE,MAAM,CAACd,SAAS,EAAE,gBAAgB,CAAC,GAAG,EAAE;MACvEG,YAAY,EAAEA,YAAY;MAC1BE,aAAa,EAAEA,aAAa;MAC5BE,YAAY,EAAEA,YAAY;MAC1BG,SAAS,EAAEA;IACb,CAAC;EACH,CAAC,EAAE,CAACH,YAAY,EAAEJ,YAAY,EAAEE,aAAa,EAAEL,SAAS,EAAEU,SAAS,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}