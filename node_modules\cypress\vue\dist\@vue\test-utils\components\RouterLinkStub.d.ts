export declare const RouterLinkStub: import("vue").DefineComponent<{
    to: {
        type: (StringConstructor | ObjectConstructor)[];
        required: true;
    };
    custom: {
        type: BooleanConstructor;
        default: boolean;
    };
}, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    to: {
        type: (StringConstructor | ObjectConstructor)[];
        required: true;
    };
    custom: {
        type: BooleanConstructor;
        default: boolean;
    };
}>>, {
    custom: boolean;
}>;
