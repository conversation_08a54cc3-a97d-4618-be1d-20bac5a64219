{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\ufficioVendite.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\nimport { Sidebar } from 'primereact/sidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass UfficioVendite extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      resultDialog: false,\n      resultDialog2: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      selectedWarehouse: null,\n      selectedDocuments: null,\n      loading: true,\n      displayed: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      totalRecords: 0,\n      search: '',\n      value: null,\n      value2: null,\n      clienti: null,\n      param: '?idWarehouses=',\n      param2: '&idRetailer=',\n      selectedRetailer: null,\n      deleteResultDialog: null,\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedRetailer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.retailers = [];\n    this.loadLazyTimeout = null;\n    this.warehouse = [];\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.creaDDT = this.creaDDT.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks3;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response5, _e$response6;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.setState({\n        resultDialog: true,\n        displayed: true\n      });\n    }\n    await APIRequest(\"GET\", \"warehouses/\").then(res => {\n      for (var entry of res.data) {\n        this.warehouse.push({\n          name: entry.warehouseName,\n          value: entry.id\n        });\n      }\n    }).catch(e => {\n      var _e$response7, _e$response8;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n        life: 3000\n      });\n    });\n    await APIRequest('GET', 'retailers/').then(res => {\n      res.data.forEach(element => {\n        if (element && element.idRegistry) {\n          var x = {\n            name: element.idRegistry.firstName || 'Nome non disponibile',\n            code: element.id || 0\n          };\n          this.retailers.push(x);\n        }\n      });\n    }).catch(e => {\n      console.log(e);\n    });\n    await APIRequest(\"GET\", \"employees?employeesEffort=true\").then(res => {\n      this.setState({\n        results4: res.data\n      });\n    }).catch(e => {\n      var _e$response9, _e$response0;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?idDocumentHead=' + result.id;\n    var task = [];\n    var documentBody = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response1, _e$response10;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      respMag: respMag,\n      mex: message\n    });\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedRetailer: null,\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: ''\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks5;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.status,\n            idDocumentHeadOrig: element.idDocumentHeadOrig\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response13, _e$response14;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response15, _e$response16;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            retailer: element.idRetailer.idRegistry.firstName,\n            documentDate: element.documentDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  async creaDDT(result) {\n    if (result.tasks !== null) {\n      let tipi = [];\n      let url = '';\n      if (result.idDocumentHeadOrig.length > 0) {\n        for (var x = 0; x < ((_result$idDocumentHea = result.idDocumentHeadOrig) === null || _result$idDocumentHea === void 0 ? void 0 : _result$idDocumentHea.length); x++) {\n          var _result$idDocumentHea;\n          url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest;\n          await APIRequest(\"GET\", url).then(res => {\n            console.log(res.data);\n            tipi.push(res.data.type);\n          }).catch(e => {\n            console.log(e);\n          });\n        }\n        var find = tipi.find(el => el === 'CLI-DDT');\n        if (find !== undefined) {\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Il documento è già associato ad una task di tipo CLI-DDT\",\n            life: 3000\n          });\n        } else {\n          url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n          //Chiamata axios per la creazione del documento\n          await APIRequest('POST', url).then(res => {\n            console.log(res.data);\n            this.toast.show({\n              severity: 'success',\n              summary: 'Ottimo',\n              detail: \"Il documento è stato inserito con successo\",\n              life: 3000\n            });\n            setTimeout(() => {\n              window.location.reload();\n            }, 3000);\n          }).catch(e => {\n            var _e$response19, _e$response20;\n            console.log(e);\n            this.toast.show({\n              severity: 'error',\n              summary: 'Siamo spiacenti',\n              detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n              life: 3000\n            });\n          });\n        }\n      } else {\n        url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\";\n        //Chiamata axios per la creazione del documento\n        await APIRequest('POST', url).then(res => {\n          console.log(res.data);\n          this.toast.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il documento è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response21, _e$response22;\n          console.log(e);\n          this.toast.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il documento. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n            life: 3000\n          });\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: 'error',\n        summary: 'Siamo spiacenti',\n        detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\",\n        life: 3000\n      });\n    }\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => {\n        var _element$tasks8;\n        return ((_element$tasks8 = element.tasks) === null || _element$tasks8 === void 0 ? void 0 : _element$tasks8.operator) !== null;\n      });\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response23, _e$response24;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response23 = e.response) === null || _e$response23 === void 0 ? void 0 : _e$response23.data) !== undefined ? (_e$response24 = e.response) === null || _e$response24 === void 0 ? void 0 : _e$response24.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog4: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  render() {\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 13\n    }, this);\n    const fields = [{\n      selectionMode: \"multiple\",\n      headerStyle: {\n        width: \"3em\"\n      }\n    }, {\n      field: \"number\",\n      header: Costanti.NDoc,\n      body: \"nDoc\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"type\",\n      header: Costanti.type,\n      body: \"typeDoc\",\n      showHeader: true\n    }, {\n      field: \"retailer\",\n      header: Costanti.cliente,\n      body: \"retailer\",\n      showHeader: true\n    }, {\n      field: \"documentDate\",\n      header: Costanti.DataDoc,\n      body: \"documentDate\",\n      sortable: true,\n      showHeader: true\n    }, {\n      field: \"tasks.manager.idUser.username\",\n      header: Costanti.Responsabile,\n      body: \"manager\",\n      showHeader: true\n    }, {\n      field: \"tasks.operator.idUser.username\",\n      header: Costanti.Operatore,\n      body: \"operator\",\n      showHeader: true\n    }, {\n      field: \"tasks.status\",\n      header: Costanti.StatoTask,\n      body: \"assigned\",\n      showHeader: true\n    }, {\n      field: \"erpSync\",\n      header: \"ERP Sync\",\n      body: \"erpSync\",\n      sortable: true,\n      showHeader: true\n    }];\n    const actionFields = [{\n      name: Costanti.VisDett,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-eye\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 45\n      }, this),\n      handler: this.visualizzaDett\n    }, {\n      name: Costanti.assegnaLavorazione,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-ellipsis-h\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 56\n      }, this),\n      handler: this.editResult,\n      status: 'create',\n      status2: 'counted'\n    }, {\n      name: Costanti.CreaDDT,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-car\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 45\n      }, this),\n      handler: this.creaDDT,\n      status: 'prepared'\n    }, {\n      name: Costanti.Elimina,\n      icon: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-trash\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 45\n      }, this),\n      handler: this.confirmDeleteResult\n    }];\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 878,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiOrdineVendita\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\",\n                emptyMessage: \"Nessun elemento disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 886,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          autoLayout: true,\n          actionsColumn: actionFields,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton2: true,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Vendite\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter2,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          distributore: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 967,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 46\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\",\n            emptyMessage: \"Nessun elemento disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 986,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog4,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1006,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedRetailer,\n          options: this.retailers,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona cliente\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 996,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 876,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default UfficioVendite;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "Dialog", "Dropdown", "JoyrideGen", "Toast", "<PERSON><PERSON>", "Print", "VisualizzaDocumenti", "Nav", "CustomDataTable", "SelezionaOperatore", "Sidebar", "jsxDEV", "_jsxDEV", "UfficioVendite", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "url", "state", "<PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "window", "sessionStorage", "setItem", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$tasks", "x", "number", "type", "retailer", "idRetailer", "idRegistry", "documentDate", "tasks", "erpSync", "idDocumentHeadOrig", "push", "results", "results5", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "results2", "results3", "results4", "resultDialog", "resultDialog2", "resultDialog3", "resultDialog4", "selectedDocuments", "displayed", "opMag", "respMag", "mex", "result", "search", "value2", "clienti", "param", "deleteResultDialog", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$tasks2", "_e$response3", "_e$response4", "retailers", "loadLazyTimeout", "warehouse", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "onPage", "onSort", "onFilter", "creaDDT", "assegnaLavorazioni", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "JSON", "parse", "getItem", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "task", "documentBody", "documentBodies", "_e$response1", "_e$response10", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "role", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "_element$tasks4", "_e$response11", "_e$response12", "_element$tasks5", "_e$response13", "_e$response14", "event", "clearTimeout", "setTimeout", "_element$tasks6", "_e$response15", "_e$response16", "Math", "random", "field", "_element$tasks7", "_e$response17", "_e$response18", "loadLazyData", "tipi", "length", "_result$idDocumentHea", "idDocDest", "find", "el", "idDocument", "location", "reload", "_e$response19", "_e$response20", "_e$response21", "_e$response22", "FilterOp", "_element$tasks8", "operator", "_e$response23", "_e$response24", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resultDialogFooter2", "doc", "resultDialogFooter3", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "cliente", "DataDoc", "Responsabile", "Operatore", "StatoTask", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "assegnaLavorazione", "status2", "CreaDDT", "Elimina", "filterDnone", "ref", "DocumentiOrdineVendita", "style", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "emptyMessage", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "autoLayout", "actionsColumn", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "distributore", "Conferma", "fontSize", "ResDeleteDoc", "Primadiproseguire", "title", "content", "target", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/ufficioVendite.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioVendite - operazioni sull'ufficio vendite\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport '../../css/DataTableDemo.css';\nimport { Sidebar } from 'primereact/sidebar';\n\nclass UfficioVendite extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            resultDialog: false,\n            resultDialog2: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            selectedWarehouse: null,\n            selectedDocuments: null,\n            loading: true,\n            displayed: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            totalRecords: 0,\n            search: '',\n            value: null,\n            value2: null,\n            clienti: null,\n            param: '?idWarehouses=',\n            param2: '&idRetailer=',\n            selectedRetailer: null,\n            deleteResultDialog: null,\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedRetailer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.retailers = []\n        this.loadLazyTimeout = null;\n        this.warehouse = [];\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.creaDDT = this.creaDDT.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.setState({ resultDialog: true, displayed: true })\n        }\n        await APIRequest(\"GET\", \"warehouses/\")\n            .then((res) => {\n                for (var entry of res.data) {\n                    this.warehouse.push({\n                        name: entry.warehouseName,\n                        value: entry.id\n                    })\n                }\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        await APIRequest('GET', 'retailers/')\n            .then(res => {\n                res.data.forEach(element => {\n                    if (element && element.idRegistry) {\n                        var x = {\n                            name: element.idRegistry.firstName || 'Nome non disponibile',\n                            code: element.id || 0\n                        }\n                        this.retailers.push(x)\n                    }\n                })\n            }).catch((e) => {\n                console.log(e)\n            })\n        await APIRequest(\"GET\", \"employees?employeesEffort=true\")\n            .then((res) => {\n                this.setState({\n                    results4: res.data,\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        var url = 'documents?idWarehouses=' + e.value + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        retailer: element.idRetailer.idRegistry.firstName,\n                        documentDate: element.documentDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idDocumentHeadOrig: element.idDocumentHeadOrig\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?idDocumentHead=' + result.id\n        var task = []\n        var documentBody = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n        });\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false,\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedRetailer: null, selectedWarehouse: idWarehouse, loading: true, search: '' });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idDocumentHeadOrig: element.idDocumentHeadOrig\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedRetailer !== null ? this.state.param2 + this.state.selectedRetailer.code : '') + '&documentType=CLI-ORDINE,CLI-NOTACR&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            retailer: element.idRetailer.idRegistry.firstName,\n                            documentDate: element.documentDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    async creaDDT(result) {\n        if (result.tasks !== null) {\n            let tipi = []\n            let url = ''\n            if (result.idDocumentHeadOrig.length > 0) {\n                for (var x = 0; x < result.idDocumentHeadOrig?.length; x++) {\n                    url = 'documents?idDocumentHead=' + result.idDocumentHeadOrig[x].idDocDest\n                    await APIRequest(\"GET\", url)\n                        .then((res) => {\n                            console.log(res.data)\n                            tipi.push(res.data.type)\n                        })\n                        .catch((e) => {\n                            console.log(e);\n                        });\n                }\n                var find = tipi.find(el => el === 'CLI-DDT')\n                if (find !== undefined) {\n                    this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Il documento è già associato ad una task di tipo CLI-DDT\", life: 3000 });\n                } else {\n                    url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                    //Chiamata axios per la creazione del documento\n                    await APIRequest('POST', url)\n                        .then(res => {\n                            console.log(res.data);\n                            this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                            setTimeout(() => {\n                                window.location.reload()\n                            }, 3000)\n                        }).catch((e) => {\n                            console.log(e)\n                            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                        })\n                }\n            } else {\n                url = \"documents/?idWarehouses=\" + this.state.selectedWarehouse + \"&idDocument=\" + result.id + \"&idRetailer=\" + result.tasks.idDocument.idRetailer.id + \"&documentTypeToCreate=CLI-DDT\"\n                //Chiamata axios per la creazione del documento\n                await APIRequest('POST', url)\n                    .then(res => {\n                        console.log(res.data);\n                        this.toast.show({ severity: 'success', summary: 'Ottimo', detail: \"Il documento è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.log(e)\n                        this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            }\n\n        } else {\n            this.toast.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"E' necessario che il documento abbia una task associata in stato prepared per creare il DDT\", life: 3000 });\n        }\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks?.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog4: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog4: false\n        })\n    }\n    render() {\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const fields = [\n            { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n            {\n                field: \"number\",\n                header: Costanti.NDoc,\n                body: \"nDoc\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"type\",\n                header: Costanti.type,\n                body: \"typeDoc\",\n                showHeader: true,\n            },\n            {\n                field: \"retailer\",\n                header: Costanti.cliente,\n                body: \"retailer\",\n                showHeader: true,\n            },\n            {\n                field: \"documentDate\",\n                header: Costanti.DataDoc,\n                body: \"documentDate\",\n                sortable: true,\n                showHeader: true,\n            },\n            {\n                field: \"tasks.manager.idUser.username\",\n                header: Costanti.Responsabile,\n                body: \"manager\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.operator.idUser.username\",\n                header: Costanti.Operatore,\n                body: \"operator\",\n                showHeader: true,\n            },\n            {\n                field: \"tasks.status\",\n                header: Costanti.StatoTask,\n                body: \"assigned\",\n                showHeader: true,\n            },\n            {\n                field: \"erpSync\",\n                header: \"ERP Sync\",\n                body: \"erpSync\",\n                sortable: true,\n                showHeader: true,\n            }\n        ];\n        const actionFields = [\n            { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n            { name: Costanti.CreaDDT, icon: <i className=\"pi pi-car\" />, handler: this.creaDDT, status: 'prepared' },\n            { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n        ];\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '' || this.state.value !== null || this.state.value2 !== null) {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiOrdineVendita}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        autoLayout={true}\n                        actionsColumn={actionFields}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButton2={true}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Vendite\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter2}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} distributore={true} />\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n                    <div className='d-flex justify-content-center flex-column pb-3'>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                        <hr></hr>\n                        <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" emptyMessage='Nessun elemento disponibile' />\n                    </div>\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog4} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedRetailer} options={this.retailers} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona cliente\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default UfficioVendite;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,2CAA2C;AACjE,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAO,6BAA6B;AACpC,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,cAAc,SAAShB,SAAS,CAAC;EAanCiB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IAyMD;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7C,IAAIC,GAAG,GAAG,yBAAyB,GAAGJ,CAAC,CAACG,KAAK,IAAI,IAAI,CAACE,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,2CAA2C,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACrQC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEd,CAAC,CAACG,KAAK,CAAC;MACrD,MAAM/B,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAwB,cAAA,GAAED,OAAO,CAACS,KAAK,cAAAR,cAAA,uBAAbA,cAAA,CAAexB,MAAM;YAC7BkC,kBAAkB,EAAEX,OAAO,CAACW;UAChC,CAAC;UACDf,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA0C,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA1C,CAAC,CAACoD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAYxB,IAAI,MAAKmC,SAAS,IAAAV,YAAA,GAAG3C,CAAC,CAACoD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IA7OG,IAAI,CAAClD,KAAK,GAAG;MACT6B,OAAO,EAAE,IAAI;MACbsB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdvB,QAAQ,EAAE,IAAI;MACdwB,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpB5D,iBAAiB,EAAE,IAAI;MACvB6D,iBAAiB,EAAE,IAAI;MACvBvB,OAAO,EAAE,IAAI;MACbwB,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,IAAI,CAAC/E,WAAW;MACxB+C,YAAY,EAAE,CAAC;MACfiC,MAAM,EAAE,EAAE;MACVlE,KAAK,EAAE,IAAI;MACXmE,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,gBAAgB;MACvBjE,MAAM,EAAE,cAAc;MACtBD,gBAAgB,EAAE,IAAI;MACtBmE,kBAAkB,EAAE,IAAI;MACxBhE,UAAU,EAAE;QACR6B,KAAK,EAAE,CAAC;QACR5B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACP+D,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAEzE,KAAK,EAAE,EAAE;YAAE0E,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAE1E,KAAK,EAAE,EAAE;YAAE0E,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAE1E,KAAK,EAAE,EAAE;YAAE0E,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM9E,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACVuC,OAAO,EAAE,IAAI;QACb6B,MAAM,EAAErE,CAAC,CAACG,KAAK,CAAC4E,IAAI;QACpBzE,gBAAgB,EAAEN,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAIC,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACmE,KAAK,GAAG,IAAI,CAACnE,KAAK,CAACH,iBAAiB,GAAG,IAAI,CAACG,KAAK,CAACE,MAAM,GAAGP,CAAC,CAACG,KAAK,CAACK,IAAI,GAAG,2CAA2C,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC7N,MAAMvC,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA2D,eAAA;UAClC,IAAIzD,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAkF,eAAA,GAAE3D,OAAO,CAACS,KAAK,cAAAkD,eAAA,uBAAbA,eAAA,CAAelF,MAAM;YAC7BkC,kBAAkB,EAAEX,OAAO,CAACW;UAChC,CAAC;UACDf,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAiF,YAAA,EAAAC,YAAA;QACVtC,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8B,YAAA,GAAAjF,CAAC,CAACoD,QAAQ,cAAA6B,YAAA,uBAAVA,YAAA,CAAY/D,IAAI,MAAKmC,SAAS,IAAA6B,YAAA,GAAGlF,CAAC,CAACoD,QAAQ,cAAA8B,YAAA,uBAAVA,YAAA,CAAYhE,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAAC4B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACxF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACwF,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,MAAM,GAAG,IAAI,CAACA,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACS,OAAO,GAAG,IAAI,CAACA,OAAO,CAACT,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACU,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACX,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACY,YAAY,GAAG,IAAI,CAACA,YAAY,CAACZ,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACa,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACb,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACd,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACe,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACf,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACgB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACiB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACjB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMkB,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAChG,MAAM,CAACC,cAAc,CAACgG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3CA,WAAW,GAAGA,WAAW,CAAClG,IAAI,KAAK6C,SAAS,GAAGqD,WAAW,CAAClG,IAAI,GAAGkG,WAAW;MAC7E,IAAItG,GAAG,GAAG,yBAAyB,GAAGsG,WAAW,GAAG,2CAA2C,GAAG,IAAI,CAACrG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACpK,IAAI,CAACV,QAAQ,CAAC;QAAEC,iBAAiB,EAAEwG;MAAY,CAAC,CAAC;MACjD,MAAMtI,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyF,eAAA;UAClC,IAAIvF,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAgH,eAAA,GAAEzF,OAAO,CAACS,KAAK,cAAAgF,eAAA,uBAAbA,eAAA,CAAehH,MAAM;YAC7BkC,kBAAkB,EAAEX,OAAO,CAACW;UAChC,CAAC;UACDf,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA+G,YAAA,EAAAC,YAAA;QACVpE,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA4D,YAAA,GAAA/G,CAAC,CAACoD,QAAQ,cAAA2D,YAAA,uBAAVA,YAAA,CAAY7F,IAAI,MAAKmC,SAAS,IAAA2D,YAAA,GAAGhH,CAAC,CAACoD,QAAQ,cAAA4D,YAAA,uBAAVA,YAAA,CAAY9F,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACtD,QAAQ,CAAC;QAAE0D,YAAY,EAAE,IAAI;QAAEK,SAAS,EAAE;MAAK,CAAC,CAAC;IAC1D;IACA,MAAM5F,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC2C,IAAI,CAAEC,GAAG,IAAK;MACX,KAAK,IAAIiG,KAAK,IAAIjG,GAAG,CAACE,IAAI,EAAE;QACxB,IAAI,CAACmE,SAAS,CAACpD,IAAI,CAAC;UAChB8C,IAAI,EAAEkC,KAAK,CAACC,aAAa;UACzB/G,KAAK,EAAE8G,KAAK,CAAC3H;QACjB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDmD,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAmH,YAAA,EAAAC,YAAA;MACVxE,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAgE,YAAA,GAAAnH,CAAC,CAACoD,QAAQ,cAAA+D,YAAA,uBAAVA,YAAA,CAAYjG,IAAI,MAAKmC,SAAS,IAAA+D,YAAA,GAAGpH,CAAC,CAACoD,QAAQ,cAAAgE,YAAA,uBAAVA,YAAA,CAAYlG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QAC5IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,MAAMnF,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC2C,IAAI,CAACC,GAAG,IAAI;MACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;QACxB,IAAIA,OAAO,IAAIA,OAAO,CAACO,UAAU,EAAE;UAC/B,IAAIL,CAAC,GAAG;YACJwD,IAAI,EAAE1D,OAAO,CAACO,UAAU,CAACrC,SAAS,IAAI,sBAAsB;YAC5DiB,IAAI,EAAEa,OAAO,CAAC/B,EAAE,IAAI;UACxB,CAAC;UACD,IAAI,CAAC6F,SAAS,CAAClD,IAAI,CAACV,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC,CAACkB,KAAK,CAAEzC,CAAC,IAAK;MACZ4C,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;IAClB,CAAC,CAAC;IACN,MAAM5B,UAAU,CAAC,KAAK,EAAE,gCAAgC,CAAC,CACpD2C,IAAI,CAAEC,GAAG,IAAK;MACX,IAAI,CAACf,QAAQ,CAAC;QACVyD,QAAQ,EAAE1C,GAAG,CAACE;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDuB,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAqH,YAAA,EAAAC,YAAA;MACV1E,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAAkE,YAAA,GAAArH,CAAC,CAACoD,QAAQ,cAAAiE,YAAA,uBAAVA,YAAA,CAAYnG,IAAI,MAAKmC,SAAS,IAAAiE,YAAA,GAAGtH,CAAC,CAACoD,QAAQ,cAAAkE,YAAA,uBAAVA,YAAA,CAAYpG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QAC3JC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EAyCA;EACA,MAAM+B,cAAcA,CAAClB,MAAM,EAAE;IACzB,IAAIhE,GAAG,GAAG,2BAA2B,GAAGgE,MAAM,CAAC9E,EAAE;IACjD,IAAIiI,IAAI,GAAG,EAAE;IACb,IAAIC,YAAY,GAAG,EAAE;IACrB,MAAMpJ,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;MACXwG,YAAY,GAAGxG,GAAG,CAACE,IAAI,CAACuG,cAAc;MACtCrD,MAAM,CAACqD,cAAc,GAAGzG,GAAG,CAACE,IAAI,CAACuG,cAAc;MAC/CF,IAAI,GAAGvG,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACDuB,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAA0H,YAAA,EAAAC,aAAA;MACV/E,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAuE,YAAA,GAAA1H,CAAC,CAACoD,QAAQ,cAAAsE,YAAA,uBAAVA,YAAA,CAAYxG,IAAI,MAAKmC,SAAS,IAAAsE,aAAA,GAAG3H,CAAC,CAACoD,QAAQ,cAAAuE,aAAA,uBAAVA,aAAA,CAAYzG,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QACxJC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBc,MAAM,CAAC5C,MAAM,GACb,OAAO,GACP,IAAIoG,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAC9D,MAAM,CAACvC,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC5B,QAAQ,CAAC;MACV2D,aAAa,EAAE,IAAI;MACnBQ,MAAM,EAAEmD,IAAI;MACZ/D,QAAQ,EAAE,IAAI,CAACnD,KAAK,CAAC6B,OAAO,CAACiG,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC9I,EAAE,KAAK8E,MAAM,CAAC9E,EAAE,CAAC;MAClEmE,QAAQ,EAAE+D,YAAY;MACtBrD,GAAG,EAAEb;IACT,CAAC,CAAC;EACN;EACA;EACAkC,kBAAkBA,CAACpB,MAAM,EAAE;IACvB,IAAI,CAACnE,QAAQ,CAAC;MACVmE,MAAM,EAAEA,MAAM;MACdR,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA6B,UAAUA,CAACrB,MAAM,EAAE;IACf,IAAId,OAAO,GACP,oBAAoB,GACpBc,MAAM,CAAC5C,MAAM,GACb,OAAO,GACP,IAAIoG,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAC9D,MAAM,CAACvC,YAAY,CAAC,CAAC;IAC5C,IAAIoC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC7D,KAAK,CAACqD,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAACrD,KAAK,CAACqD,QAAQ,CAACtC,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAACgH,IAAI,KAAK,QAAQ,IAAIjE,MAAM,CAACtC,KAAK,KAAK,IAAI,EAAE;UACpD,IAAIwG,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAAClH,OAAO,CAACmH,WAAW,CAAC,GAAGD,QAAQ,CAAClH,OAAO,CAACoH,YAAY,CAAC,GAAGF,QAAQ,CAAClH,OAAO,CAACqH,aAAa,CAAC;UAC1GzE,KAAK,CAAChC,IAAI,CAAC;YACP0G,KAAK,EAAEtH,OAAO,CAACuH,UAAU,GAAG,GAAG,GAAGvH,OAAO,CAACwH,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1FnI,KAAK,EAAEkB,OAAO,CAACyH;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIzH,OAAO,CAACgH,IAAI,KAAK,UAAU,IAAIjE,MAAM,CAACtC,KAAK,KAAK,IAAI,EAAE;UAC7DoC,OAAO,CAACjC,IAAI,CAAC;YACT0G,KAAK,EAAEtH,OAAO,CAACuH,UAAU,GAAG,GAAG,GAAGvH,OAAO,CAACwH,SAAS;YACnD1I,KAAK,EAAEkB,OAAO,CAACyH;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC7E,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAChE,QAAQ,CAAC;MACVmE,MAAM,EAAA2E,aAAA,KAAO3E,MAAM,CAAE;MACrBP,aAAa,EAAE,IAAI;MACnBI,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEA,OAAO;MAChBC,GAAG,EAAEb;IACT,CAAC,CAAC;EACN;EACA;EACAoC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzF,QAAQ,CAAC;MACV4D,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACA,MAAM8B,KAAKA,CAAA,EAAG;IACV,IAAIe,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAChG,MAAM,CAACC,cAAc,CAACgG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAItG,GAAG,GAAG,yBAAyB,GAAGsG,WAAW,GAAG,2CAA2C,GAAG,IAAI,CAACrG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACpK,IAAI,CAACV,QAAQ,CAAC;QAAEK,gBAAgB,EAAE,IAAI;QAAEJ,iBAAiB,EAAEwG,WAAW;QAAElE,OAAO,EAAE,IAAI;QAAE6B,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMjG,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA2H,eAAA;UAClC,IAAIzH,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAkJ,eAAA,GAAE3H,OAAO,CAACS,KAAK,cAAAkH,eAAA,uBAAbA,eAAA,CAAelJ,MAAM;YAC7BkC,kBAAkB,EAAEX,OAAO,CAACW;UAChC,CAAC;UACDf,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAiJ,aAAA,EAAAC,aAAA;QACVtG,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA8F,aAAA,GAAAjJ,CAAC,CAACoD,QAAQ,cAAA6F,aAAA,uBAAVA,aAAA,CAAY/H,IAAI,MAAKmC,SAAS,IAAA6F,aAAA,GAAGlJ,CAAC,CAACoD,QAAQ,cAAA8F,aAAA,uBAAVA,aAAA,CAAYhI,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAMqC,SAASA,CAAA,EAAG;IACd,IAAIc,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAChG,MAAM,CAACC,cAAc,CAACgG,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC3C,IAAItG,GAAG,GAAG,yBAAyB,GAAGsG,WAAW,GAAG,2CAA2C,GAAG,IAAI,CAACrG,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACpK,IAAI,CAACV,QAAQ,CAAC;QAAEK,gBAAgB,EAAE,IAAI;QAAEJ,iBAAiB,EAAEwG,WAAW;QAAElE,OAAO,EAAE,IAAI;QAAE6B,MAAM,EAAE;MAAG,CAAC,CAAC;MACpG,MAAMjG,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA8H,eAAA;UAClC,IAAI5H,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAqJ,eAAA,GAAE9H,OAAO,CAACS,KAAK,cAAAqH,eAAA,uBAAbA,eAAA,CAAerJ,MAAM;YAC7BkC,kBAAkB,EAAEX,OAAO,CAACW;UAChC,CAAC;UACDf,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE;YAAE6B,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAK;YAAE5B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE4B,SAAS,EAAEvB,GAAG,CAACE,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAChC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL8B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAoJ,aAAA,EAAAC,aAAA;QACVzG,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAiG,aAAA,GAAApJ,CAAC,CAACoD,QAAQ,cAAAgG,aAAA,uBAAVA,aAAA,CAAYlI,IAAI,MAAKmC,SAAS,IAAAgG,aAAA,GAAGrJ,CAAC,CAACoD,QAAQ,cAAAiG,aAAA,uBAAVA,aAAA,CAAYnI,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACAsC,MAAMA,CAACyD,KAAK,EAAE;IACV,IAAI,CAACrJ,QAAQ,CAAC;MAAEuC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAAC4C,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAIpJ,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACmE,KAAK,GAAG,IAAI,CAACnE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,2CAA2C,GAAG8I,KAAK,CAAC5I,IAAI,GAAG,QAAQ,GAAG4I,KAAK,CAAC3I,IAAI;MAC/P,MAAMvC,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAoI,eAAA;UAClC,IAAIlI,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAA2J,eAAA,GAAEpI,OAAO,CAACS,KAAK,cAAA2H,eAAA,uBAAbA,eAAA,CAAe3J;UAC3B,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAE6I,KAAK;UACjB9G,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAA0J,aAAA,EAAAC,aAAA;QACV/G,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAuG,aAAA,GAAA1J,CAAC,CAACoD,QAAQ,cAAAsG,aAAA,uBAAVA,aAAA,CAAYxI,IAAI,MAAKmC,SAAS,IAAAsG,aAAA,GAAG3J,CAAC,CAACoD,QAAQ,cAAAuG,aAAA,uBAAVA,aAAA,CAAYzI,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEqG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACA/D,MAAMA,CAACwD,KAAK,EAAE;IACV,IAAI,CAACrJ,QAAQ,CAAC;MAAEuC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIsH,KAAK,GAAGR,KAAK,CAAC5E,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAG4E,KAAK,CAAC5E,SAAS;IAChG,IAAI,IAAI,CAACU,eAAe,EAAE;MACtBmE,YAAY,CAAC,IAAI,CAACnE,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGoE,UAAU,CAAC,YAAY;MAC1C,IAAIpJ,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACmE,KAAK,GAAG,IAAI,CAACnE,KAAK,CAACH,iBAAiB,IAAI,IAAI,CAACG,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,2CAA2C,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAGmJ,KAAK,GAAG,WAAW,IAAIR,KAAK,CAAC3E,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MAC5W,MAAMvG,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA0I,eAAA;UAClC,IAAIxI,CAAC,GAAG;YACJjC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdkC,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;YAClBC,QAAQ,EAAEL,OAAO,CAACM,UAAU,CAACC,UAAU,CAACrC,SAAS;YACjDsC,YAAY,EAAER,OAAO,CAACQ,YAAY;YAClCC,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBC,OAAO,EAAEV,OAAO,CAACU,OAAO;YACxBjC,MAAM,GAAAiK,eAAA,GAAE1I,OAAO,CAACS,KAAK,cAAAiI,eAAA,uBAAbA,eAAA,CAAejK;UAC3B,CAAC;UACDmB,SAAS,CAACgB,IAAI,CAACV,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACtB,QAAQ,CAAC;UACViC,OAAO,EAAEjB,SAAS;UAClBkB,QAAQ,EAAElB,SAAS;UACnBmB,YAAY,EAAEpB,GAAG,CAACE,IAAI,CAACmB,UAAU;UACjC5B,UAAU,EAAAsI,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC1I,KAAK,CAACI,UAAU;YAAEiE,SAAS,EAAE4E,KAAK,CAAC5E,SAAS;YAAEC,SAAS,EAAE2E,KAAK,CAAC3E;UAAS,EAAE;UAChGnC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAEzC,CAAC,IAAK;QAAA,IAAAgK,aAAA,EAAAC,aAAA;QACVrH,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;QACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA6G,aAAA,GAAAhK,CAAC,CAACoD,QAAQ,cAAA4G,aAAA,uBAAVA,aAAA,CAAY9I,IAAI,MAAKmC,SAAS,IAAA4G,aAAA,GAAGjK,CAAC,CAACoD,QAAQ,cAAA6G,aAAA,uBAAVA,aAAA,CAAY/I,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEqG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEA9D,QAAQA,CAACuD,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACrJ,QAAQ,CAAC;MAAEQ,UAAU,EAAE6I;IAAM,CAAC,EAAE,IAAI,CAACY,YAAY,CAAC;EAC3D;EACA,MAAMlE,OAAOA,CAAC5B,MAAM,EAAE;IAClB,IAAIA,MAAM,CAACtC,KAAK,KAAK,IAAI,EAAE;MACvB,IAAIqI,IAAI,GAAG,EAAE;MACb,IAAI/J,GAAG,GAAG,EAAE;MACZ,IAAIgE,MAAM,CAACpC,kBAAkB,CAACoI,MAAM,GAAG,CAAC,EAAE;QACtC,KAAK,IAAI7I,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAA8I,qBAAA,GAAGjG,MAAM,CAACpC,kBAAkB,cAAAqI,qBAAA,uBAAzBA,qBAAA,CAA2BD,MAAM,GAAE7I,CAAC,EAAE,EAAE;UAAA,IAAA8I,qBAAA;UACxDjK,GAAG,GAAG,2BAA2B,GAAGgE,MAAM,CAACpC,kBAAkB,CAACT,CAAC,CAAC,CAAC+I,SAAS;UAC1E,MAAMlM,UAAU,CAAC,KAAK,EAAEgC,GAAG,CAAC,CACvBW,IAAI,CAAEC,GAAG,IAAK;YACX4B,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAACE,IAAI,CAAC;YACrBiJ,IAAI,CAAClI,IAAI,CAACjB,GAAG,CAACE,IAAI,CAACO,IAAI,CAAC;UAC5B,CAAC,CAAC,CACDgB,KAAK,CAAEzC,CAAC,IAAK;YACV4C,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;UAClB,CAAC,CAAC;QACV;QACA,IAAIuK,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAK,SAAS,CAAC;QAC5C,IAAID,IAAI,KAAKlH,SAAS,EAAE;UACpB,IAAI,CAACP,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,EAAE,0DAA0D;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;QACtJ,CAAC,MAAM;UACHnD,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,cAAc,GAAGkE,MAAM,CAAC9E,EAAE,GAAG,cAAc,GAAG8E,MAAM,CAACtC,KAAK,CAAC2I,UAAU,CAAC9I,UAAU,CAACrC,EAAE,GAAG,+BAA+B;UACvL;UACA,MAAMlB,UAAU,CAAC,MAAM,EAAEgC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;YACT4B,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAACE,IAAI,CAAC;YACrB,IAAI,CAAC4B,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,SAAS;cAAEC,OAAO,EAAE,QAAQ;cAAEC,MAAM,EAAE,4CAA4C;cAAEK,IAAI,EAAE;YAAK,CAAC,CAAC;YAC7HiG,UAAU,CAAC,MAAM;cACb5I,MAAM,CAAC8J,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UACZ,CAAC,CAAC,CAAClI,KAAK,CAAEzC,CAAC,IAAK;YAAA,IAAA4K,aAAA,EAAAC,aAAA;YACZjI,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;YACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,iBAAiB;cAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAAyH,aAAA,GAAA5K,CAAC,CAACoD,QAAQ,cAAAwH,aAAA,uBAAVA,aAAA,CAAY1J,IAAI,MAAKmC,SAAS,IAAAwH,aAAA,GAAG7K,CAAC,CAACoD,QAAQ,cAAAyH,aAAA,uBAAVA,aAAA,CAAY3J,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAC;UAC/N,CAAC,CAAC;QACV;MACJ,CAAC,MAAM;QACHnD,GAAG,GAAG,0BAA0B,GAAG,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,cAAc,GAAGkE,MAAM,CAAC9E,EAAE,GAAG,cAAc,GAAG8E,MAAM,CAACtC,KAAK,CAAC2I,UAAU,CAAC9I,UAAU,CAACrC,EAAE,GAAG,+BAA+B;QACvL;QACA,MAAMlB,UAAU,CAAC,MAAM,EAAEgC,GAAG,CAAC,CACxBW,IAAI,CAACC,GAAG,IAAI;UACT4B,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAACE,IAAI,CAAC;UACrB,IAAI,CAAC4B,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,4CAA4C;YAAEK,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7HiG,UAAU,CAAC,MAAM;YACb5I,MAAM,CAAC8J,QAAQ,CAACC,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAAClI,KAAK,CAAEzC,CAAC,IAAK;UAAA,IAAA8K,aAAA,EAAAC,aAAA;UACZnI,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;UACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,yEAAAC,MAAA,CAAsE,EAAA2H,aAAA,GAAA9K,CAAC,CAACoD,QAAQ,cAAA0H,aAAA,uBAAVA,aAAA,CAAY5J,IAAI,MAAKmC,SAAS,IAAA0H,aAAA,GAAG/K,CAAC,CAACoD,QAAQ,cAAA2H,aAAA,uBAAVA,aAAA,CAAY7J,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/N,CAAC,CAAC;MACV;IAEJ,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,iBAAiB;QAAEC,MAAM,EAAE,6FAA6F;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACzL;EACJ;EACA0C,kBAAkBA,CAAA,EAAG;IACjB,IAAI3C,OAAO,GACP,iCAAiC;IACrC,IAAIW,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIiE,MAAM,GAAG,IAAI,CAAC9H,KAAK,CAAC0D,iBAAiB,CAACoE,MAAM,CAACqC,EAAE,IAAIA,EAAE,CAAC1I,KAAK,KAAK,IAAI,CAAC;IACzE,IAAIqG,MAAM,CAACiC,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAAC/J,KAAK,CAACqD,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAACrD,KAAK,CAACqD,QAAQ,CAACtC,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAACgH,IAAI,KAAK,UAAU,EAAE;YAC7BnE,OAAO,CAACjC,IAAI,CAAC;cACT0G,KAAK,EAAEtH,OAAO,CAACuH,UAAU,GAAG,GAAG,GAAGvH,OAAO,CAACwH,SAAS;cACnD1I,KAAK,EAAEkB,OAAO,CAACyH;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAC7E,KAAK,GAAGA,KAAK;MAClB,IAAI,CAAChE,QAAQ,CAAC;QACVmE,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAAC0D,iBAAiB;QACpCF,aAAa,EAAE,IAAI;QACnBI,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAEb;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAI6E,MAAM,CAACiC,MAAM,KAAK,IAAI,CAAC/J,KAAK,CAAC0D,iBAAiB,CAACqG,MAAM,EAAE;MAC9D,IAAIY,QAAQ,GAAG,IAAI,CAAC3K,KAAK,CAAC0D,iBAAiB,CAACoE,MAAM,CAAC9G,OAAO;QAAA,IAAA4J,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAA5J,OAAO,CAACS,KAAK,cAAAmJ,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,MAAK,IAAI;MAAA,EAAC;MAC/F,IAAIF,QAAQ,CAACZ,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIY,QAAQ,CAACZ,MAAM,KAAK,IAAI,CAAC/J,KAAK,CAAC0D,iBAAiB,CAACqG,MAAM,EAAE;UACzD,IAAI,IAAI,CAAC/J,KAAK,CAACqD,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAACrD,KAAK,CAACqD,QAAQ,CAACtC,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAACgH,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAIC,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAAClH,OAAO,CAACmH,WAAW,CAAC,GAAGD,QAAQ,CAAClH,OAAO,CAACoH,YAAY,CAAC,GAAGF,QAAQ,CAAClH,OAAO,CAACqH,aAAa,CAAC;gBAC1GzE,KAAK,CAAChC,IAAI,CAAC;kBACP0G,KAAK,EAAEtH,OAAO,CAACuH,UAAU,GAAG,GAAG,GAAGvH,OAAO,CAACwH,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1FnI,KAAK,EAAEkB,OAAO,CAACyH;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAAC7E,KAAK,GAAGA,KAAK;UAClB,IAAI,CAAChE,QAAQ,CAAC;YACVmE,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAAC0D,iBAAiB;YACpCF,aAAa,EAAE,IAAI;YACnBI,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAEb;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAAClD,KAAK,CAACqD,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAACrD,KAAK,CAACqD,QAAQ,CAACtC,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACgH,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAIC,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAAClH,OAAO,CAACmH,WAAW,CAAC,GAAGD,QAAQ,CAAClH,OAAO,CAACoH,YAAY,CAAC,GAAGF,QAAQ,CAAClH,OAAO,CAACqH,aAAa,CAAC;cAC1GzE,KAAK,CAAChC,IAAI,CAAC;gBACP0G,KAAK,EAAEtH,OAAO,CAACuH,UAAU,GAAG,GAAG,GAAGvH,OAAO,CAACwH,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1FnI,KAAK,EAAEkB,OAAO,CAACyH;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAC7E,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAChE,QAAQ,CAAC;UACVmE,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAAC0D,iBAAiB;UACpCF,aAAa,EAAE,IAAI;UACnBI,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAEb;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EAEA2C,gBAAgBA,CAAClG,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAE8D,iBAAiB,EAAE/D,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;;EAEA;EACAkG,mBAAmBA,CAACjC,MAAM,EAAE;IACxB,IAAI,CAACnE,QAAQ,CAAC;MACVmE,MAAM;MACNK,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAM0B,YAAYA,CAAA,EAAG;IACjB,IAAIjE,OAAO,GAAG,IAAI,CAAC7B,KAAK,CAAC6B,OAAO,CAACiG,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAAC9I,EAAE,KAAK,IAAI,CAACe,KAAK,CAAC+D,MAAM,CAAC9E,EAC1C,CAAC;IACD,IAAI,CAACW,QAAQ,CAAC;MACViC,OAAO;MACPuC,kBAAkB,EAAE,KAAK;MACzBL,MAAM,EAAE,IAAI,CAAC/E;IACjB,CAAC,CAAC;IACF,IAAIe,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACC,KAAK,CAAC+D,MAAM,CAAC9E,EAAE;IAC7D,MAAMlB,UAAU,CAAC,QAAQ,EAAEgC,GAAG,CAAC,CAC1BW,IAAI,CAACC,GAAG,IAAI;MACT4B,OAAO,CAACC,GAAG,CAAC7B,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAAC4B,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;MACF3C,MAAM,CAAC8J,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAClI,KAAK,CAAEzC,CAAC,IAAK;MAAA,IAAAmL,aAAA,EAAAC,aAAA;MACZxI,OAAO,CAACC,GAAG,CAAC7C,CAAC,CAAC;MACd,IAAI,CAAC8C,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAC,MAAA,CAAqE,EAAAgI,aAAA,GAAAnL,CAAC,CAACoD,QAAQ,cAAA+H,aAAA,uBAAVA,aAAA,CAAYjK,IAAI,MAAKmC,SAAS,IAAA+H,aAAA,GAAGpL,CAAC,CAACoD,QAAQ,cAAAgI,aAAA,uBAAVA,aAAA,CAAYlK,IAAI,GAAGlB,CAAC,CAACsD,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA6C,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACnG,QAAQ,CAAC;MACVwE,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA6B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACjG,KAAK,CAACH,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACV0D,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACb,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAgD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACtG,QAAQ,CAAC;MACV6D,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA0C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvG,QAAQ,CAAC;MACV6D,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAuH,MAAMA,CAAA,EAAG;IACL,MAAMC,kBAAkB,gBACpBrM,OAAA,CAAChB,KAAK,CAACsN,QAAQ;MAAAC,QAAA,eACXvM,OAAA;QAAKwM,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DvM,OAAA,CAACR,MAAM;UAACgN,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACpF,iBAAkB;UAAAkF,QAAA,GAAE,GAAC,EAACrN,QAAQ,CAACwN,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMC,mBAAmB,gBACrB/M,OAAA,CAAChB,KAAK,CAACsN,QAAQ;MAAAC,QAAA,eACXvM,OAAA;QAAKwM,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBvM,OAAA;UAAKwM,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBvM,OAAA;YAAKwM,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCvM,OAAA,CAACR,MAAM;cACHgN,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAAClG,kBAAmB;cAAAgG,QAAA,GAEhC,GAAG,EACHrN,QAAQ,CAACwN,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACT9M,OAAA,CAACP,KAAK;cACFuC,SAAS,EAAE,IAAI,CAACZ,KAAK,CAAC+D,MAAO;cAC7BX,QAAQ,EAAE,IAAI,CAACpD,KAAK,CAACoD,QAAS;cAC9BU,GAAG,EAAE,IAAI,CAAC9D,KAAK,CAAC8D,GAAI;cACpB8H,GAAG,EAAE;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrBjN,OAAA,CAAChB,KAAK,CAACsN,QAAQ;MAAAC,QAAA,eACXvM,OAAA,CAACR,MAAM;QAACgN,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAChG,UAAW;QAAA8F,QAAA,GACjE,GAAG,EACHrN,QAAQ,CAACwN,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMI,wBAAwB,gBAC1BlN,OAAA,CAAChB,KAAK,CAACsN,QAAQ;MAAAC,QAAA,gBACXvM,OAAA,CAACR,MAAM;QACHkK,KAAK,EAAC,IAAI;QACVyD,IAAI,EAAC,aAAa;QAClBX,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAACtF;MAAuB;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACF9M,OAAA,CAACR,MAAM;QAACgN,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAACvF,YAAa;QAAAqF,QAAA,GACxD,GAAG,EACHrN,QAAQ,CAACkO,EAAE,EAAE,GAAG;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMO,MAAM,GAAG,CACX;MAAEC,aAAa,EAAE,UAAU;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAC,EAC5D;MACI3C,KAAK,EAAE,QAAQ;MACf4C,MAAM,EAAEvO,QAAQ,CAACwO,IAAI;MACrBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,MAAM;MACb4C,MAAM,EAAEvO,QAAQ,CAACsD,IAAI;MACrBmL,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,UAAU;MACjB4C,MAAM,EAAEvO,QAAQ,CAAC4O,OAAO;MACxBH,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,cAAc;MACrB4C,MAAM,EAAEvO,QAAQ,CAAC6O,OAAO;MACxBJ,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,+BAA+B;MACtC4C,MAAM,EAAEvO,QAAQ,CAAC8O,YAAY;MAC7BL,IAAI,EAAE,SAAS;MACfE,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,gCAAgC;MACvC4C,MAAM,EAAEvO,QAAQ,CAAC+O,SAAS;MAC1BN,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,cAAc;MACrB4C,MAAM,EAAEvO,QAAQ,CAACgP,SAAS;MAC1BP,IAAI,EAAE,UAAU;MAChBE,UAAU,EAAE;IAChB,CAAC,EACD;MACIhD,KAAK,EAAE,SAAS;MAChB4C,MAAM,EAAE,UAAU;MAClBE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,MAAMM,YAAY,GAAG,CACjB;MAAErI,IAAI,EAAE5G,QAAQ,CAACkP,OAAO;MAAEjB,IAAI,eAAEnN,OAAA;QAAGwM,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAAChI;IAAe,CAAC,EAC3F;MAAEP,IAAI,EAAE5G,QAAQ,CAACoP,kBAAkB;MAAEnB,IAAI,eAAEnN,OAAA;QAAGwM,SAAS,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAAC7H,UAAU;MAAE3F,MAAM,EAAE,QAAQ;MAAE0N,OAAO,EAAE;IAAU,CAAC,EAC/I;MAAEzI,IAAI,EAAE5G,QAAQ,CAACsP,OAAO;MAAErB,IAAI,eAAEnN,OAAA;QAAGwM,SAAS,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACtH,OAAO;MAAElG,MAAM,EAAE;IAAW,CAAC,EACxG;MAAEiF,IAAI,EAAE5G,QAAQ,CAACuP,OAAO;MAAEtB,IAAI,eAAEnN,OAAA;QAAGwM,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEuB,OAAO,EAAE,IAAI,CAACjH;IAAoB,CAAC,CACrG;IACD,IAAIsH,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACtN,KAAK,CAACgE,MAAM,KAAK,EAAE,IAAI,IAAI,CAAChE,KAAK,CAACF,KAAK,KAAK,IAAI,IAAI,IAAI,CAACE,KAAK,CAACiE,MAAM,KAAK,IAAI,EAAE;MACrFqJ,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI1O,OAAA;MAAKwM,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CvM,OAAA,CAACT,KAAK;QAACoP,GAAG,EAAGpD,EAAE,IAAK,IAAI,CAAC1H,KAAK,GAAG0H;MAAG;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC9M,OAAA,CAACL,GAAG;QAAAgN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEP9M,OAAA;QAAKwM,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCvM,OAAA;UAAAuM,QAAA,EAAKrN,QAAQ,CAAC0P;QAAsB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACL,IAAI,CAAC1L,KAAK,CAACH,iBAAiB,KAAK,IAAI,iBAClCjB,OAAA;QAAKwM,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCvM,OAAA;UAAIwM,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEvM,OAAA;YAAIwM,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEvM,OAAA;cAAKwM,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DvM,OAAA;gBAAIwM,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACvM,OAAA;kBAAGwM,SAAS,EAAC,iBAAiB;kBAACqC,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC4P,SAAS,EAAC,GAAC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H9M,OAAA,CAACX,QAAQ;gBAACmN,SAAS,EAAC,QAAQ;gBAACtL,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;gBAAC8N,OAAO,EAAE,IAAI,CAAC3I,SAAU;gBAAC4I,QAAQ,EAAE,IAAI,CAAClO,iBAAkB;gBAACmO,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAAChG,MAAM;gBAACiG,QAAQ,EAAC,MAAM;gBAACC,YAAY,EAAC;cAA6B;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV9M,OAAA;QAAKwM,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBvM,OAAA,CAACJ,eAAe;UACZ+O,GAAG,EAAGpD,EAAE,IAAK,IAAI,CAAC8D,EAAE,GAAG9D,EAAG;UAC1BrK,KAAK,EAAE,IAAI,CAACE,KAAK,CAAC6B,OAAQ;UAC1BM,OAAO,EAAE,IAAI,CAACnC,KAAK,CAACmC,OAAQ;UAC5B8J,MAAM,EAAEA,MAAO;UACfiC,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACT7I,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBvD,KAAK,EAAE,IAAI,CAACjC,KAAK,CAACI,UAAU,CAAC6B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,YAAa;UACtC1B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjCiO,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,UAAU,EAAE,IAAK;UACjBC,aAAa,EAAEzB,YAAa;UAC5B0B,mBAAmB,EAAE,IAAK;UAC1BvC,aAAa,EAAC,UAAU;UACxBwC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAAC1J,cAAe;UAClC2J,SAAS,EAAE,IAAI,CAAC5O,KAAK,CAAC0D,iBAAkB;UACxCmL,iBAAiB,EAAGlP,CAAC,IAAK,IAAI,CAACkG,gBAAgB,CAAClG,CAAC,CAAE;UACnDmP,gBAAgB,EAAE,IAAK;UACvBC,kBAAkB,EAAE,IAAI,CAACnJ,kBAAmB;UAC5CoJ,iBAAiB,EAAElR,QAAQ,CAAC8H,kBAAmB;UAC/CqJ,oBAAoB,EAAE,CAAC,IAAI,CAACjP,KAAK,CAAC0D,iBAAiB,IAAI,CAAC,IAAI,CAAC1D,KAAK,CAAC0D,iBAAiB,CAACqG,MAAO;UAC5FmF,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACjJ,UAAW;UACnCkJ,gBAAgB,eAAExQ,OAAA;YAAUwM,SAAS,EAAC,MAAM;YAAC1G,IAAI,EAAC;UAAgB;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E2D,OAAO,EAAC,QAAQ;UAChB5J,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBpB,SAAS,EAAE,IAAI,CAACrE,KAAK,CAACI,UAAU,CAACiE,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAACtE,KAAK,CAACI,UAAU,CAACkE,SAAU;UAC3CoB,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBnB,OAAO,EAAE,IAAI,CAACvE,KAAK,CAACI,UAAU,CAACmE,OAAQ;UACvC+K,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAS;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9M,OAAA,CAACZ,MAAM;QACHwR,OAAO,EAAE,IAAI,CAACxP,KAAK,CAACuD,aAAc;QAClC8I,MAAM,EAAEvO,QAAQ,CAAC2R,MAAO;QACxBC,KAAK;QACLtE,SAAS,EAAC,kBAAkB;QAC5BuE,MAAM,EAAEhE,mBAAoB;QAC5BiE,MAAM,EAAE,IAAI,CAACzK,kBAAmB;QAChC0K,SAAS,EAAE,KAAM;QAAA1E,QAAA,eAEjBvM,OAAA,CAACN,mBAAmB;UAChByF,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAACoD,QAAS;UAC5BvB,OAAO,EAAE,IAAI,CAAC7B,KAAK,CAAC+D,MAAO;UAC3BnD,SAAS,EAAE,IAAI,CAACZ,KAAK,CAAC+D,MAAO;UAC7B+L,MAAM,EAAE;QAAK;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAET9M,OAAA,CAACZ,MAAM;QACHwR,OAAO,EAAE,IAAI,CAACxP,KAAK,CAACwD,aAAc;QAClC6I,MAAM,EAAE,IAAI,CAACrM,KAAK,CAAC8D,GAAI;QACvB4L,KAAK;QACLtE,SAAS,EAAC,kBAAkB;QAC5BuE,MAAM,EAAE9D,mBAAoB;QAC5B+D,MAAM,EAAE,IAAI,CAACvK,UAAW;QAAA8F,QAAA,eAExBvM,OAAA,CAACH,kBAAkB;UAACsF,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAAC+D,MAAO;UAACH,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,OAAQ;UAACkM,YAAY,EAAE;QAAK;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eAET9M,OAAA,CAACZ,MAAM;QACHwR,OAAO,EAAE,IAAI,CAACxP,KAAK,CAACoE,kBAAmB;QACvCiI,MAAM,EAAEvO,QAAQ,CAACkS,QAAS;QAC1BN,KAAK;QACLC,MAAM,EAAE7D,wBAAyB;QACjC8D,MAAM,EAAE,IAAI,CAAC7J,sBAAuB;QAAAoF,QAAA,eAEpCvM,OAAA;UAAKwM,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCvM,OAAA;YACIwM,SAAS,EAAC,mCAAmC;YAC7CqC,KAAK,EAAE;cAAEwC,QAAQ,EAAE;YAAO;UAAE;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAAC1L,KAAK,CAAC+D,MAAM,iBACdnF,OAAA;YAAAuM,QAAA,GACKrN,QAAQ,CAACoS,YAAY,EAAC,GAAC,eAAAtR,OAAA;cAAAuM,QAAA,GAAI,IAAI,CAACnL,KAAK,CAAC+D,MAAM,CAAC5C,MAAM,EAAC,GAAC;YAAA;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT9M,OAAA,CAACZ,MAAM;QAACwR,OAAO,EAAE,IAAI,CAACxP,KAAK,CAACsD,YAAa;QAAC+I,MAAM,EAAEvO,QAAQ,CAACqS,iBAAkB;QAACT,KAAK;QAACtE,SAAS,EAAC,kBAAkB;QAACwE,MAAM,EAAE,IAAI,CAAC3J,iBAAkB;QAAC0J,MAAM,EAAE1E,kBAAmB;QAAAE,QAAA,GACvK,IAAI,CAACnL,KAAK,CAAC2D,SAAS,iBACjB/E,OAAA,CAACV,UAAU;UAACkS,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACC,MAAM,EAAC;QAAS;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhG9M,OAAA;UAAKwM,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DvM,OAAA;YAAIwM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvM,OAAA;cAAGwM,SAAS,EAAC,iBAAiB;cAACqC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC4P,SAAS;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH9M,OAAA;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9M,OAAA,CAACX,QAAQ;YAACmN,SAAS,EAAC,QAAQ;YAACtL,KAAK,EAAE,IAAI,CAACE,KAAK,CAACH,iBAAkB;YAAC8N,OAAO,EAAE,IAAI,CAAC3I,SAAU;YAAC4I,QAAQ,EAAE,IAAI,CAAClO,iBAAkB;YAACmO,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAAChG,MAAM;YAACiG,QAAQ,EAAC,MAAM;YAACC,YAAY,EAAC;UAA6B;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT9M,OAAA,CAACF,OAAO;QAAC8Q,OAAO,EAAE,IAAI,CAACxP,KAAK,CAACyD,aAAc;QAAC8M,QAAQ,EAAC,MAAM;QAACX,MAAM,EAAE,IAAI,CAACzJ,WAAY;QAAAgF,QAAA,gBACjFvM,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACmM,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKvM,OAAA;YAAGwM,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C9M,OAAA;YAAIwM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvM,OAAA;cAAGwM,SAAS,EAAC,mBAAmB;cAACqC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC0S,MAAM;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9M,OAAA,CAACR,MAAM;YAACa,EAAE,EAAC,iBAAiB;YAACmM,SAAS,EAAEkC,WAAY;YAACjC,OAAO,EAAE,IAAI,CAAC/F,KAAM;YAAA6F,QAAA,gBAACvM,OAAA;cAAGwM,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9M,OAAA;cAAAuM,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN9M,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACmM,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DvM,OAAA;YAAIwM,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvM,OAAA;cAAGwM,SAAS,EAAC,mBAAmB;cAACqC,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC5N,QAAQ,CAAC0S,MAAM;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9M,OAAA,CAACR,MAAM;YAACa,EAAE,EAAC,kBAAkB;YAACmM,SAAS,EAAEkC,WAAY;YAACjC,OAAO,EAAE,IAAI,CAAC9F,SAAU;YAAA4F,QAAA,gBAACvM,OAAA;cAAGwM,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9M,OAAA;cAAAuM,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN9M,OAAA;UAAA2M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9M,OAAA,CAACX,QAAQ;UAACmN,SAAS,EAAC,OAAO;UAACtL,KAAK,EAAE,IAAI,CAACE,KAAK,CAACC,gBAAiB;UAAC0N,OAAO,EAAE,IAAI,CAAC7I,SAAU;UAAC8I,QAAQ,EAAE,IAAI,CAACnJ,SAAU;UAACoJ,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,mBAAmB;UAAChG,MAAM;UAACiG,QAAQ,EAAC;QAAM;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe7M,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}