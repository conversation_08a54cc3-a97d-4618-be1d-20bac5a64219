{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\newPwd.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Costanti } from \"./traduttore/const\";\nimport { Toast } from 'primereact/toast';\nimport classNames from 'classnames/bind';\nimport { Button } from 'primereact/button';\nimport logo from '../img/tm_logo-01.svg';\nimport { Password } from 'primereact/password';\nimport { Divider } from \"primereact/divider\";\nimport { Form, Field } from 'react-final-form';\nimport axios from \"axios\";\nimport { baseProxy } from \"./generalizzazioni/apireq\";\nimport { basePath } from \"./route\";\n/* import Logos from \"../resources/Logos\"; */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NewPwd = props => {\n  _s();\n  const toast = useRef(null);\n  const [classApp, setClassApp] = useState('');\n  const query = new URLSearchParams(window.location.search);\n  const pwdtoken = query.get('token');\n  const miouser = query.get('username');\n  useEffect(() => {\n    var url = 'auth/checktoken?username=' + miouser;\n    axios({\n      timeout: 60 * 60 * 1000,\n      method: 'GET',\n      url: baseProxy + url,\n      headers: {\n        auth: pwdtoken\n      }\n    }).then(res => {\n      console.log(res.data);\n      setClassApp('App');\n    }).catch(e => {\n      console.log(e);\n      var newPassReset = document.getElementById(\"newPassReset\");\n      if (newPassReset !== null) {\n        newPassReset.remove();\n      }\n      var urlExpired = document.getElementById(\"urlExpired\");\n      urlExpired.classList.remove(\"d-none\");\n    });\n\n    // aggiungo classe NewPwd a body per stile login\n    document.body.classList.add('loginPage', 'NewPwd');\n    return () => {\n      // rimuovo classe NewPwd a body se non sono in login\n      document.body.classList.remove('loginPage', 'NewPwd');\n    };\n  }, [miouser, pwdtoken]);\n  const validate = data => {\n    let errors = {};\n    if (!data.password) {\n      errors.password = Costanti.PassObb;\n    }\n    if (!data.confirmPassword) {\n      errors.confirmPassword = Costanti.ConfPassObb;\n    } else if (data.confirmPassword !== data.password) {\n      errors.confirmPassword = Costanti.PassValid;\n    }\n    return errors;\n  };\n  const onSubmit = async (data, form) => {\n    let setpassword = {\n      password: data.password\n    };\n    var url = 'auth/changepwd';\n    axios({\n      timeout: 60 * 60 * 1000,\n      method: 'PUT',\n      url: baseProxy + url,\n      headers: {\n        auth: pwdtoken\n      },\n      data: setpassword\n    }).then(res => {\n      console.log(res.data);\n      form.restart();\n      toast.current.show({\n        severity: 'success',\n        summary: 'Password salvata con successo',\n        detail: \"La tua nuova password è pronta all'uso. Ti stiamo reindirizzando alla login.\",\n        life: 5000\n      });\n      setTimeout(() => {\n        window.location.pathname = '/login';\n      }, 5000);\n    }).catch(e => {\n      var _e$response, _e$response2;\n      console.log(e);\n      toast.current.show({\n        severity: 'error',\n        summary: 'Si è verificato un errore',\n        detail: \"Non \\xE8 stato possibile finalizzare il processo a causa di un errore interno o di corretta compilazione dei campi. Per favore riprova. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n        life: 5000\n      });\n    });\n  };\n  const isFormFieldValid = meta => !!(meta.touched && meta.error);\n  const getFormErrorMessage = meta => {\n    return isFormFieldValid(meta) && /*#__PURE__*/_jsxDEV(\"small\", {\n      className: \"p-error\",\n      children: meta.error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 42\n    }, this);\n  };\n  const passwordHeader = /*#__PURE__*/_jsxDEV(\"h6\", {\n    children: Costanti.SelectPass\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 28\n  }, this);\n  const passwordFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"p-mt-2\",\n      children: Costanti.PassDevCont\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"p-pl-2 p-ml-2 p-mt-0\",\n      style: {\n        lineHeight: '1.5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Minuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Maiuscola\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.AlmUnNum\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: Costanti.Alm8Car\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"newPassReset\",\n      className: classApp,\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"redirectToWinet\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          className: \"d-flex align-items-center\",\n          href: \"/\",\n          children: [/*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            name: \"arrow-back-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 71\n          }, this), Costanti.Indietro]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: onSubmit,\n        initialValues: {\n          password: '',\n          confirmPassword: ''\n        },\n        validate: validate,\n        render: _ref => {\n          let {\n            handleSubmit\n          } = _ref;\n          return /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"form-login row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"fontLogo\",\n                className: \"logo text-center mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: logo,\n                  onError: e => e.target.src = logo,\n                  alt: \"Logo\",\n                  width: \"200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 82\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.recuperaPassTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"mb-1 mt-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2 opacity-3\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: Costanti.newPassTxt\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 59\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"mt-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-12 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-grid p-fluid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-col-12 p-md-12\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-left\",\n                    children: Costanti.ChooseNewPass\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(Field, {\n                    name: \"password\",\n                    render: _ref2 => {\n                      let {\n                        input,\n                        meta\n                      } = _ref2;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-inputgroup flex-column\",\n                        children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                          id: \"password\"\n                        }, input), {}, {\n                          toggleMask: true,\n                          className: classNames({\n                            'p-invalid': isFormFieldValid(meta)\n                          }) + \" d-flex\",\n                          header: passwordHeader,\n                          footer: passwordFooter\n                        }), void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 131,\n                          columnNumber: 45\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex\",\n                          children: getFormErrorMessage(meta)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 132,\n                          columnNumber: 45\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 130,\n                        columnNumber: 41\n                      }, this);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-col-12 p-md-12\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-left\",\n                    children: Costanti.ConfirmNewPass\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(Field, {\n                    name: \"confirmPassword\",\n                    render: _ref3 => {\n                      let {\n                        input,\n                        meta\n                      } = _ref3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-inputgroup flex-column\",\n                        children: [/*#__PURE__*/_jsxDEV(Password, _objectSpread(_objectSpread({\n                          id: \"confirmPassword\"\n                        }, input), {}, {\n                          toggleMask: true,\n                          className: classNames({\n                            'p-invalid': isFormFieldValid(meta)\n                          }) + \" d-flex w-100\",\n                          feedback: false\n                        }), void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 140,\n                          columnNumber: 45\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex\",\n                          children: getFormErrorMessage(meta)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 141,\n                          columnNumber: 45\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 41\n                      }, this);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"mt-3 mb-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-100\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                className: \"btn btn-lg btn-primary buttonreg px-5 mx-auto w-auto d-block\",\n                children: [Costanti.salva, \" password\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 21\n          }, this);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"urlExpired\",\n      className: \"App d-none\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: onSubmit,\n        initialValues: {\n          password: '',\n          confirmPassword: ''\n        },\n        validate: validate,\n        render: _ref4 => {\n          let {\n            handleSubmit\n          } = _ref4;\n          return /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"form-login row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"fontLogo\",\n                className: \"logo text-center mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: logo,\n                  onError: e => e.target.src = logo,\n                  alt: \"Logo\",\n                  width: \"200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 82\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: Costanti.recuperaPassTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"mb-1 mt-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-2 opacity-3\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: Costanti.newPassUrlExpired\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 59\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"mt-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-100\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: basePath,\n                className: \"p-button p-component btn btn-lg btn-primary buttonreg p-0 mx-auto w-auto px-5 py-2 text-light\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-arrow-circle-left mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 154\n                }, this), \" \", Costanti.Indietro]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 21\n          }, this);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(NewPwd, \"U+ZiXJ5l6V/ZsgHjByn1hgTGaYA=\");\n_c = NewPwd;\nexport default NewPwd;\nvar _c;\n$RefreshReg$(_c, \"NewPwd\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Toast", "classNames", "<PERSON><PERSON>", "logo", "Password", "Divider", "Form", "Field", "axios", "baseProxy", "basePath", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NewPwd", "props", "_s", "toast", "classApp", "setClassApp", "query", "URLSearchParams", "window", "location", "search", "pwdtoken", "get", "miouser", "url", "timeout", "method", "headers", "auth", "then", "res", "console", "log", "data", "catch", "e", "newPassReset", "document", "getElementById", "remove", "urlExpired", "classList", "body", "add", "validate", "errors", "password", "PassObb", "confirmPassword", "ConfPassObb", "PassValid", "onSubmit", "form", "setpassword", "restart", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "pathname", "_e$response", "_e$response2", "concat", "response", "undefined", "message", "isFormFieldValid", "meta", "touched", "error", "getFormErrorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "passwordHeader", "SelectPass", "passwordFooter", "PassDevCont", "style", "lineHeight", "Minus<PERSON>", "<PERSON><PERSON><PERSON>", "AlmUnNum", "Alm8Car", "id", "ref", "href", "name", "Indietro", "initialValues", "render", "_ref", "handleSubmit", "src", "onError", "target", "alt", "width", "recuperaPassTitle", "newPassTxt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "input", "_objectSpread", "toggleMask", "header", "footer", "ConfirmNewPass", "_ref3", "feedback", "type", "salva", "_ref4", "newPassUrlExpired", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/newPwd.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { <PERSON><PERSON> } from \"./traduttore/const\";\nimport { Toast } from 'primereact/toast';\nimport classNames from 'classnames/bind';\nimport { But<PERSON> } from 'primereact/button';\nimport logo from '../img/tm_logo-01.svg';\nimport { Password } from 'primereact/password';\nimport { Divider } from \"primereact/divider\";\nimport { Form, Field } from 'react-final-form';\nimport axios from \"axios\";\nimport { baseProxy } from \"./generalizzazioni/apireq\";\nimport { basePath } from \"./route\";\n/* import Logos from \"../resources/Logos\"; */\n\nconst NewPwd = (props) => {\n    const toast = useRef(null);\n    const [classApp, setClassApp] = useState('')\n\n    const query = new URLSearchParams(window.location.search);\n    const pwdtoken = query.get('token')\n    const miouser = query.get('username')\n\n    useEffect(() => {\n        var url = 'auth/checktoken?username=' + miouser\n        axios({\n            timeout: 60 * 60 * 1000,\n            method: 'GET',\n            url: baseProxy + url,\n            headers: { auth: pwdtoken },\n        })\n            .then(res => {\n                console.log(res.data);\n                setClassApp('App')\n            }).catch((e) => {\n                console.log(e)\n                var newPassReset = document.getElementById(\"newPassReset\");\n                if (newPassReset !== null) {\n                    newPassReset.remove();\n                }\n                var urlExpired = document.getElementById(\"urlExpired\");\n                urlExpired.classList.remove(\"d-none\");\n            })\n\n        // aggiungo classe NewPwd a body per stile login\n        document.body.classList.add('loginPage', 'NewPwd');\n        return () => {\n            // rimuovo classe NewPwd a body se non sono in login\n            document.body.classList.remove('loginPage', 'NewPwd');\n        };\n    }, [miouser, pwdtoken]);\n\n    const validate = (data) => {\n        let errors = {};\n        if (!data.password) {\n            errors.password = Costanti.PassObb;\n        }\n        if (!data.confirmPassword) {\n            errors.confirmPassword = Costanti.ConfPassObb;\n        }\n        else if (data.confirmPassword !== data.password) {\n            errors.confirmPassword = Costanti.PassValid;\n        }\n        return errors;\n    };\n    const onSubmit = async (data, form) => {\n        let setpassword = {\n            password: data.password,\n        }\n        var url = 'auth/changepwd'\n        axios({\n            timeout: 60 * 60 * 1000,\n            method: 'PUT',\n            url: baseProxy + url,\n            headers: { auth: pwdtoken },\n            data: setpassword\n        })\n            .then(res => {\n                console.log(res.data);\n                form.restart();\n                toast.current.show({ severity: 'success', summary: 'Password salvata con successo', detail: \"La tua nuova password è pronta all'uso. Ti stiamo reindirizzando alla login.\", life: 5000 });\n                setTimeout(() => {\n                    window.location.pathname = '/login'\n                }, 5000)\n            }).catch(e => {\n                console.log(e);\n                toast.current.show({ severity: 'error', summary: 'Si è verificato un errore', detail: `Non è stato possibile finalizzare il processo a causa di un errore interno o di corretta compilazione dei campi. Per favore riprova. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 5000 });\n            })\n    };\n    const isFormFieldValid = (meta) => !!(meta.touched && meta.error);\n    const getFormErrorMessage = (meta) => {\n        return isFormFieldValid(meta) && <small className=\"p-error\">{meta.error}</small>;\n    };\n    const passwordHeader = <h6>{Costanti.SelectPass}</h6>;\n    const passwordFooter = (\n        <React.Fragment>\n            <Divider />\n            <p className=\"p-mt-2\">{Costanti.PassDevCont}</p>\n            <ul className=\"p-pl-2 p-ml-2 p-mt-0\" style={{ lineHeight: '1.5' }}>\n                <li>{Costanti.Minuscola}</li>\n                <li>{Costanti.Maiuscola}</li>\n                <li>{Costanti.AlmUnNum}</li>\n                <li>{Costanti.Alm8Car}</li>\n            </ul>\n        </React.Fragment>\n    );\n\n    return (\n        <>\n            <div id=\"newPassReset\" className={classApp}>\n                <Toast ref={toast} />\n                <div className=\"redirectToWinet\">\n                    <a className=\"d-flex align-items-center\" href='/'><ion-icon name=\"arrow-back-circle\" />{Costanti.Indietro}</a>\n                </div>\n                <Form onSubmit={onSubmit} initialValues={{ password: '', confirmPassword: '' }} validate={validate} render={({ handleSubmit }) => (\n                    <form onSubmit={handleSubmit} className=\"form-login row\">\n                        <div className=\"col-md-12\">\n                            <div id=\"fontLogo\" className=\"logo text-center mb-2\"><img src={logo} onError={(e) => e.target.src = logo} alt=\"Logo\" width=\"200\" /></div>\n                            <p className=\"mb-2\"><strong>{Costanti.recuperaPassTitle}</strong></p>\n                            <hr className=\"mb-1 mt-0\"></hr>\n                        </div>\n                        <div className=\"col-md-12\">\n                            <p className=\"mb-2 opacity-3\"><small>{Costanti.newPassTxt}</small></p>\n                            <hr className=\"mt-2\"></hr>\n                        </div>\n                        <div className=\"col-md-12 mb-3\">\n                            <div className=\"p-grid p-fluid\">\n                                <div className=\"p-col-12 p-md-12\">\n                                    <h6 className=\"text-left\">{Costanti.ChooseNewPass}</h6>\n                                    <Field name=\"password\" render={({ input, meta }) => (\n                                        <div className=\"p-inputgroup flex-column\">\n                                            <Password id=\"password\" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) }) + \" d-flex\"} header={passwordHeader} footer={passwordFooter} />\n                                            <div className=\"d-flex\">{getFormErrorMessage(meta)}</div>\n                                        </div>\n                                    )} />\n                                </div>\n                                <div className=\"p-col-12 p-md-12\">\n                                    <h6 className=\"text-left\">{Costanti.ConfirmNewPass}</h6>\n                                    <Field name=\"confirmPassword\" render={({ input, meta }) => (\n                                        <div className=\"p-inputgroup flex-column\">\n                                            <Password id=\"confirmPassword\" {...input} toggleMask className={classNames({ 'p-invalid': isFormFieldValid(meta) }) + \" d-flex w-100\"} feedback={false} />\n                                            <div className=\"d-flex\">{getFormErrorMessage(meta)}</div>\n                                        </div>\n                                    )} />\n                                </div>\n                            </div>\n                            <hr className=\"mt-3 mb-0\"></hr>\n                        </div>\n                        <div className=\"w-100\">\n                            {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                            <Button type=\"submit\" className=\"btn btn-lg btn-primary buttonreg px-5 mx-auto w-auto d-block\" >{Costanti.salva} password</Button>\n                        </div>\n                    </form>\n                )} />\n            </div>\n            <div id=\"urlExpired\" className=\"App d-none\">\n                <Form onSubmit={onSubmit} initialValues={{ password: '', confirmPassword: '' }} validate={validate} render={({ handleSubmit }) => (\n                    <form onSubmit={handleSubmit} className=\"form-login row\">\n                        <div className=\"col-md-12\">\n                            <div id=\"fontLogo\" className=\"logo text-center mb-2\"><img src={logo} onError={(e) => e.target.src = logo} alt=\"Logo\" width=\"200\" /></div>\n                            <p className=\"mb-2\"><strong>{Costanti.recuperaPassTitle}</strong></p>\n                            <hr className=\"mb-1 mt-0\"></hr>\n                        </div>\n                        <div className=\"col-md-12\">\n                            <p className=\"mb-2 opacity-3\"><small>{Costanti.newPassUrlExpired}</small></p>\n                            <hr className=\"mt-2\"></hr>\n                        </div>\n                        <div className=\"w-100\">\n                            {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                            <a href={basePath} className=\"p-button p-component btn btn-lg btn-primary buttonreg p-0 mx-auto w-auto px-5 py-2 text-light\"><i className=\"pi pi-arrow-circle-left mr-2\"></i> {Costanti.Indietro}</a>\n                        </div>\n                    </form>\n                )} />\n            </div>\n        </>\n    )\n}\n\n\n\nexport default NewPwd;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,uBAAuB;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,IAAI,EAAEC,KAAK,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,QAAQ,QAAQ,SAAS;AAClC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,MAAM,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACtB,MAAMC,KAAK,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMyB,KAAK,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EACzD,MAAMC,QAAQ,GAAGL,KAAK,CAACM,GAAG,CAAC,OAAO,CAAC;EACnC,MAAMC,OAAO,GAAGP,KAAK,CAACM,GAAG,CAAC,UAAU,CAAC;EAErC7B,SAAS,CAAC,MAAM;IACZ,IAAI+B,GAAG,GAAG,2BAA2B,GAAGD,OAAO;IAC/CpB,KAAK,CAAC;MACFsB,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MACvBC,MAAM,EAAE,KAAK;MACbF,GAAG,EAAEpB,SAAS,GAAGoB,GAAG;MACpBG,OAAO,EAAE;QAAEC,IAAI,EAAEP;MAAS;IAC9B,CAAC,CAAC,CACGQ,IAAI,CAACC,GAAG,IAAI;MACTC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACG,IAAI,CAAC;MACrBlB,WAAW,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,CAACmB,KAAK,CAAEC,CAAC,IAAK;MACZJ,OAAO,CAACC,GAAG,CAACG,CAAC,CAAC;MACd,IAAIC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;MAC1D,IAAIF,YAAY,KAAK,IAAI,EAAE;QACvBA,YAAY,CAACG,MAAM,CAAC,CAAC;MACzB;MACA,IAAIC,UAAU,GAAGH,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;MACtDE,UAAU,CAACC,SAAS,CAACF,MAAM,CAAC,QAAQ,CAAC;IACzC,CAAC,CAAC;;IAEN;IACAF,QAAQ,CAACK,IAAI,CAACD,SAAS,CAACE,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClD,OAAO,MAAM;MACT;MACAN,QAAQ,CAACK,IAAI,CAACD,SAAS,CAACF,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC;IACzD,CAAC;EACL,CAAC,EAAE,CAAChB,OAAO,EAAEF,QAAQ,CAAC,CAAC;EAEvB,MAAMuB,QAAQ,GAAIX,IAAI,IAAK;IACvB,IAAIY,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,CAACZ,IAAI,CAACa,QAAQ,EAAE;MAChBD,MAAM,CAACC,QAAQ,GAAGpD,QAAQ,CAACqD,OAAO;IACtC;IACA,IAAI,CAACd,IAAI,CAACe,eAAe,EAAE;MACvBH,MAAM,CAACG,eAAe,GAAGtD,QAAQ,CAACuD,WAAW;IACjD,CAAC,MACI,IAAIhB,IAAI,CAACe,eAAe,KAAKf,IAAI,CAACa,QAAQ,EAAE;MAC7CD,MAAM,CAACG,eAAe,GAAGtD,QAAQ,CAACwD,SAAS;IAC/C;IACA,OAAOL,MAAM;EACjB,CAAC;EACD,MAAMM,QAAQ,GAAG,MAAAA,CAAOlB,IAAI,EAAEmB,IAAI,KAAK;IACnC,IAAIC,WAAW,GAAG;MACdP,QAAQ,EAAEb,IAAI,CAACa;IACnB,CAAC;IACD,IAAItB,GAAG,GAAG,gBAAgB;IAC1BrB,KAAK,CAAC;MACFsB,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MACvBC,MAAM,EAAE,KAAK;MACbF,GAAG,EAAEpB,SAAS,GAAGoB,GAAG;MACpBG,OAAO,EAAE;QAAEC,IAAI,EAAEP;MAAS,CAAC;MAC3BY,IAAI,EAAEoB;IACV,CAAC,CAAC,CACGxB,IAAI,CAACC,GAAG,IAAI;MACTC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACG,IAAI,CAAC;MACrBmB,IAAI,CAACE,OAAO,CAAC,CAAC;MACdzC,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,+BAA+B;QAAEC,MAAM,EAAE,8EAA8E;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACzLC,UAAU,CAAC,MAAM;QACb3C,MAAM,CAACC,QAAQ,CAAC2C,QAAQ,GAAG,QAAQ;MACvC,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CAAC5B,KAAK,CAACC,CAAC,IAAI;MAAA,IAAA4B,WAAA,EAAAC,YAAA;MACVjC,OAAO,CAACC,GAAG,CAACG,CAAC,CAAC;MACdtB,KAAK,CAAC0C,OAAO,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,2BAA2B;QAAEC,MAAM,+JAAAM,MAAA,CAA4J,EAAAF,WAAA,GAAA5B,CAAC,CAAC+B,QAAQ,cAAAH,WAAA,uBAAVA,WAAA,CAAY9B,IAAI,MAAKkC,SAAS,IAAAH,YAAA,GAAG7B,CAAC,CAAC+B,QAAQ,cAAAF,YAAA,uBAAVA,YAAA,CAAY/B,IAAI,GAAGE,CAAC,CAACiC,OAAO,CAAE;QAAER,IAAI,EAAE;MAAK,CAAC,CAAC;IAClU,CAAC,CAAC;EACV,CAAC;EACD,MAAMS,gBAAgB,GAAIC,IAAI,IAAK,CAAC,EAAEA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,CAAC;EACjE,MAAMC,mBAAmB,GAAIH,IAAI,IAAK;IAClC,OAAOD,gBAAgB,CAACC,IAAI,CAAC,iBAAI/D,OAAA;MAAOmE,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEL,IAAI,CAACE;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACpF,CAAC;EACD,MAAMC,cAAc,gBAAGzE,OAAA;IAAAoE,QAAA,EAAKjF,QAAQ,CAACuF;EAAU;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrD,MAAMG,cAAc,gBAChB3E,OAAA,CAACjB,KAAK,CAACkB,QAAQ;IAAAmE,QAAA,gBACXpE,OAAA,CAACP,OAAO;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXxE,OAAA;MAAGmE,SAAS,EAAC,QAAQ;MAAAC,QAAA,EAAEjF,QAAQ,CAACyF;IAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChDxE,OAAA;MAAImE,SAAS,EAAC,sBAAsB;MAACU,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAV,QAAA,gBAC9DpE,OAAA;QAAAoE,QAAA,EAAKjF,QAAQ,CAAC4F;MAAS;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7BxE,OAAA;QAAAoE,QAAA,EAAKjF,QAAQ,CAAC6F;MAAS;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7BxE,OAAA;QAAAoE,QAAA,EAAKjF,QAAQ,CAAC8F;MAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5BxE,OAAA;QAAAoE,QAAA,EAAKjF,QAAQ,CAAC+F;MAAO;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CACnB;EAED,oBACIxE,OAAA,CAAAE,SAAA;IAAAkE,QAAA,gBACIpE,OAAA;MAAKmF,EAAE,EAAC,cAAc;MAAChB,SAAS,EAAE5D,QAAS;MAAA6D,QAAA,gBACvCpE,OAAA,CAACZ,KAAK;QAACgG,GAAG,EAAE9E;MAAM;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBxE,OAAA;QAAKmE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BpE,OAAA;UAAGmE,SAAS,EAAC,2BAA2B;UAACkB,IAAI,EAAC,GAAG;UAAAjB,QAAA,gBAACpE,OAAA;YAAUsF,IAAI,EAAC;UAAmB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAACrF,QAAQ,CAACoG,QAAQ;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7G,CAAC,eACNxE,OAAA,CAACN,IAAI;QAACkD,QAAQ,EAAEA,QAAS;QAAC4C,aAAa,EAAE;UAAEjD,QAAQ,EAAE,EAAE;UAAEE,eAAe,EAAE;QAAG,CAAE;QAACJ,QAAQ,EAAEA,QAAS;QAACoD,MAAM,EAAEC,IAAA;UAAA,IAAC;YAAEC;UAAa,CAAC,GAAAD,IAAA;UAAA,oBACzH1F,OAAA;YAAM4C,QAAQ,EAAE+C,YAAa;YAACxB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBACpDpE,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBpE,OAAA;gBAAKmF,EAAE,EAAC,UAAU;gBAAChB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAACpE,OAAA;kBAAK4F,GAAG,EAAErG,IAAK;kBAACsG,OAAO,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACF,GAAG,GAAGrG,IAAK;kBAACwG,GAAG,EAAC,MAAM;kBAACC,KAAK,EAAC;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzIxE,OAAA;gBAAGmE,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAACpE,OAAA;kBAAAoE,QAAA,EAASjF,QAAQ,CAAC8G;gBAAiB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrExE,OAAA;gBAAImE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBpE,OAAA;gBAAGmE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAACpE,OAAA;kBAAAoE,QAAA,EAAQjF,QAAQ,CAAC+G;gBAAU;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtExE,OAAA;gBAAImE,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BpE,OAAA;gBAAKmE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3BpE,OAAA;kBAAKmE,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC7BpE,OAAA;oBAAImE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEjF,QAAQ,CAACgH;kBAAa;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvDxE,OAAA,CAACL,KAAK;oBAAC2F,IAAI,EAAC,UAAU;oBAACG,MAAM,EAAEW,KAAA;sBAAA,IAAC;wBAAEC,KAAK;wBAAEtC;sBAAK,CAAC,GAAAqC,KAAA;sBAAA,oBAC3CpG,OAAA;wBAAKmE,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,gBACrCpE,OAAA,CAACR,QAAQ,EAAA8G,aAAA,CAAAA,aAAA;0BAACnB,EAAE,EAAC;wBAAU,GAAKkB,KAAK;0BAAEE,UAAU;0BAACpC,SAAS,EAAE9E,UAAU,CAAC;4BAAE,WAAW,EAAEyE,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAC,GAAG,SAAU;0BAACyC,MAAM,EAAE/B,cAAe;0BAACgC,MAAM,EAAE9B;wBAAe;0BAAAN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5KxE,OAAA;0BAAKmE,SAAS,EAAC,QAAQ;0BAAAC,QAAA,EAAEF,mBAAmB,CAACH,IAAI;wBAAC;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxE,OAAA;kBAAKmE,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC7BpE,OAAA;oBAAImE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEjF,QAAQ,CAACuH;kBAAc;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDxE,OAAA,CAACL,KAAK;oBAAC2F,IAAI,EAAC,iBAAiB;oBAACG,MAAM,EAAEkB,KAAA;sBAAA,IAAC;wBAAEN,KAAK;wBAAEtC;sBAAK,CAAC,GAAA4C,KAAA;sBAAA,oBAClD3G,OAAA;wBAAKmE,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,gBACrCpE,OAAA,CAACR,QAAQ,EAAA8G,aAAA,CAAAA,aAAA;0BAACnB,EAAE,EAAC;wBAAiB,GAAKkB,KAAK;0BAAEE,UAAU;0BAACpC,SAAS,EAAE9E,UAAU,CAAC;4BAAE,WAAW,EAAEyE,gBAAgB,CAACC,IAAI;0BAAE,CAAC,CAAC,GAAG,eAAgB;0BAAC6C,QAAQ,EAAE;wBAAM;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1JxE,OAAA;0BAAKmE,SAAS,EAAC,QAAQ;0BAAAC,QAAA,EAAEF,mBAAmB,CAACH,IAAI;wBAAC;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNxE,OAAA;gBAAImE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,OAAO;cAAAC,QAAA,eAElBpE,OAAA,CAACV,MAAM;gBAACuH,IAAI,EAAC,QAAQ;gBAAC1C,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,GAAGjF,QAAQ,CAAC2H,KAAK,EAAC,WAAS;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNxE,OAAA;MAAKmF,EAAE,EAAC,YAAY;MAAChB,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvCpE,OAAA,CAACN,IAAI;QAACkD,QAAQ,EAAEA,QAAS;QAAC4C,aAAa,EAAE;UAAEjD,QAAQ,EAAE,EAAE;UAAEE,eAAe,EAAE;QAAG,CAAE;QAACJ,QAAQ,EAAEA,QAAS;QAACoD,MAAM,EAAEsB,KAAA;UAAA,IAAC;YAAEpB;UAAa,CAAC,GAAAoB,KAAA;UAAA,oBACzH/G,OAAA;YAAM4C,QAAQ,EAAE+C,YAAa;YAACxB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBACpDpE,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBpE,OAAA;gBAAKmF,EAAE,EAAC,UAAU;gBAAChB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAACpE,OAAA;kBAAK4F,GAAG,EAAErG,IAAK;kBAACsG,OAAO,EAAGjE,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAACF,GAAG,GAAGrG,IAAK;kBAACwG,GAAG,EAAC,MAAM;kBAACC,KAAK,EAAC;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzIxE,OAAA;gBAAGmE,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAACpE,OAAA;kBAAAoE,QAAA,EAASjF,QAAQ,CAAC8G;gBAAiB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrExE,OAAA;gBAAImE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBpE,OAAA;gBAAGmE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAACpE,OAAA;kBAAAoE,QAAA,EAAQjF,QAAQ,CAAC6H;gBAAiB;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7ExE,OAAA;gBAAImE,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,OAAO;cAAAC,QAAA,eAElBpE,OAAA;gBAAGqF,IAAI,EAAEvF,QAAS;gBAACqE,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,gBAACpE,OAAA;kBAAGmE,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,KAAC,EAACrF,QAAQ,CAACoG,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAnE,EAAA,CAjKKF,MAAM;AAAA8G,EAAA,GAAN9G,MAAM;AAqKZ,eAAeA,MAAM;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}