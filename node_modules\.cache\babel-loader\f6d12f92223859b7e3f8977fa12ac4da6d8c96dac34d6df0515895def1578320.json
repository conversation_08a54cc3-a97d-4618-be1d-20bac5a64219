{"ast": null, "code": "import * as React from 'react';\nvar isValidElement = React.isValidElement;\nexport { isValidElement };\nexport function replaceElement(element, replacement, props) {\n  if (!isValidElement(element)) return replacement;\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n}\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}", "map": {"version": 3, "names": ["React", "isValidElement", "replaceElement", "element", "replacement", "props", "cloneElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/_util/reactNode.js"], "sourcesContent": ["import * as React from 'react';\nvar isValidElement = React.isValidElement;\nexport { isValidElement };\nexport function replaceElement(element, replacement, props) {\n  if (!isValidElement(element)) return replacement;\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n}\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;AACzC,SAASA,cAAc;AACvB,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAC1D,IAAI,CAACJ,cAAc,CAACE,OAAO,CAAC,EAAE,OAAOC,WAAW;EAChD,OAAO,aAAaJ,KAAK,CAACM,YAAY,CAACH,OAAO,EAAE,OAAOE,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACF,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC;AACnH;AACA,OAAO,SAASC,YAAYA,CAACH,OAAO,EAAEE,KAAK,EAAE;EAC3C,OAAOH,cAAc,CAACC,OAAO,EAAEA,OAAO,EAAEE,KAAK,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}