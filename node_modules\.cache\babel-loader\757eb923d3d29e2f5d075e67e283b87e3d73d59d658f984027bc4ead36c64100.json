{"ast": null, "code": "import React, { Component } from 'react';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport PropTypes from 'prop-types';\nimport warning from 'tiny-warning';\nvar MAX_SIGNED_31_BIT_INT = **********;\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {};\nfunction getUniqueId() {\n  var key = '__global_unique_id__';\n  return commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1;\n}\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n  var contextProp = '__create-react-context-' + getUniqueId() + '__';\n  var Provider = /*#__PURE__*/function (_Component) {\n    _inheritsLoose(Provider, _Component);\n    function Provider() {\n      var _this;\n      _this = _Component.apply(this, arguments) || this;\n      _this.emitter = createEventEmitter(_this.props.value);\n      return _this;\n    }\n    var _proto = Provider.prototype;\n    _proto.getChildContext = function getChildContext() {\n      var _ref;\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n    _proto.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits;\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0;\n        } else {\n          changedBits = typeof calculateChangedBits === 'function' ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== 'production') {\n            warning((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, 'calculateChangedBits: Expected the return value to be a ' + '31-bit integer. Instead received: ' + changedBits);\n          }\n          changedBits |= 0;\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n    _proto.render = function render() {\n      return this.props.children;\n    };\n    return Provider;\n  }(Component);\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = PropTypes.object.isRequired, _Provider$childContex);\n  var Consumer = /*#__PURE__*/function (_Component2) {\n    _inheritsLoose(Consumer, _Component2);\n    function Consumer() {\n      var _this2;\n      _this2 = _Component2.apply(this, arguments) || this;\n      _this2.state = {\n        value: _this2.getValue()\n      };\n      _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({\n            value: _this2.getValue()\n          });\n        }\n      };\n      return _this2;\n    }\n    var _proto2 = Consumer.prototype;\n    _proto2.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT : observedBits;\n    };\n    _proto2.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT : observedBits;\n    };\n    _proto2.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n    _proto2.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n    _proto2.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n    return Consumer;\n  }(Component);\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = PropTypes.object, _Consumer$contextType);\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\nvar index = React.createContext || createReactContext;\nexport default index;", "map": {"version": 3, "names": ["React", "Component", "_inherits<PERSON><PERSON>e", "PropTypes", "warning", "MAX_SIGNED_31_BIT_INT", "commonjsGlobal", "globalThis", "window", "global", "getUniqueId", "key", "objectIs", "x", "y", "createEventEmitter", "value", "handlers", "on", "handler", "push", "off", "filter", "h", "get", "set", "newValue", "changedBits", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "children", "Array", "isArray", "createReactContext", "defaultValue", "calculateChangedBits", "_Provider$childContex", "_Consumer$contextType", "contextProp", "Provider", "_Component", "_this", "apply", "arguments", "emitter", "props", "_proto", "prototype", "getChildContext", "_ref", "componentWillReceiveProps", "nextProps", "oldValue", "process", "env", "NODE_ENV", "render", "childContextTypes", "object", "isRequired", "Consumer", "_Component2", "_this2", "state", "getValue", "onUpdate", "observedBits", "setState", "_proto2", "undefined", "componentDidMount", "context", "componentWillUnmount", "contextTypes", "index", "createContext"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/mini-create-react-context/dist/esm/index.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport PropTypes from 'prop-types';\nimport warning from 'tiny-warning';\n\nvar MAX_SIGNED_31_BIT_INT = **********;\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {};\n\nfunction getUniqueId() {\n  var key = '__global_unique_id__';\n  return commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1;\n}\n\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n\n  var contextProp = '__create-react-context-' + getUniqueId() + '__';\n\n  var Provider = /*#__PURE__*/function (_Component) {\n    _inheritsLoose(Provider, _Component);\n\n    function Provider() {\n      var _this;\n\n      _this = _Component.apply(this, arguments) || this;\n      _this.emitter = createEventEmitter(_this.props.value);\n      return _this;\n    }\n\n    var _proto = Provider.prototype;\n\n    _proto.getChildContext = function getChildContext() {\n      var _ref;\n\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n\n    _proto.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0;\n        } else {\n          changedBits = typeof calculateChangedBits === 'function' ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n\n          if (process.env.NODE_ENV !== 'production') {\n            warning((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, 'calculateChangedBits: Expected the return value to be a ' + '31-bit integer. Instead received: ' + changedBits);\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n\n    _proto.render = function render() {\n      return this.props.children;\n    };\n\n    return Provider;\n  }(Component);\n\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = PropTypes.object.isRequired, _Provider$childContex);\n\n  var Consumer = /*#__PURE__*/function (_Component2) {\n    _inheritsLoose(Consumer, _Component2);\n\n    function Consumer() {\n      var _this2;\n\n      _this2 = _Component2.apply(this, arguments) || this;\n      _this2.state = {\n        value: _this2.getValue()\n      };\n\n      _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({\n            value: _this2.getValue()\n          });\n        }\n      };\n\n      return _this2;\n    }\n\n    var _proto2 = Consumer.prototype;\n\n    _proto2.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT : observedBits;\n    };\n\n    _proto2.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n\n      var observedBits = this.props.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT : observedBits;\n    };\n\n    _proto2.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n\n    _proto2.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n\n    _proto2.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n\n    return Consumer;\n  }(Component);\n\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = PropTypes.object, _Consumer$contextType);\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\n\nvar index = React.createContext || createReactContext;\n\nexport default index;\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,OAAO,MAAM,cAAc;AAElC,IAAIC,qBAAqB,GAAG,UAAU;AACtC,IAAIC,cAAc,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;AAE1J,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,GAAG,GAAG,sBAAsB;EAChC,OAAOL,cAAc,CAACK,GAAG,CAAC,GAAG,CAACL,cAAc,CAACK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7D;AAEA,SAASC,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAOD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC;EACnC,CAAC,MAAM;IACL,OAAOD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;EAC3B;AACF;AAEA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAIC,QAAQ,GAAG,EAAE;EACjB,OAAO;IACLC,EAAE,EAAE,SAASA,EAAEA,CAACC,OAAO,EAAE;MACvBF,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;IACxB,CAAC;IACDE,GAAG,EAAE,SAASA,GAAGA,CAACF,OAAO,EAAE;MACzBF,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAAC,UAAUC,CAAC,EAAE;QACtC,OAAOA,CAAC,KAAKJ,OAAO;MACtB,CAAC,CAAC;IACJ,CAAC;IACDK,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAOR,KAAK;IACd,CAAC;IACDS,GAAG,EAAE,SAASA,GAAGA,CAACC,QAAQ,EAAEC,WAAW,EAAE;MACvCX,KAAK,GAAGU,QAAQ;MAChBT,QAAQ,CAACW,OAAO,CAAC,UAAUT,OAAO,EAAE;QAClC,OAAOA,OAAO,CAACH,KAAK,EAAEW,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AAEA,SAASE,SAASA,CAACC,QAAQ,EAAE;EAC3B,OAAOC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ;AACzD;AAEA,SAASG,kBAAkBA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;EAC9D,IAAIC,qBAAqB,EAAEC,qBAAqB;EAEhD,IAAIC,WAAW,GAAG,yBAAyB,GAAG5B,WAAW,CAAC,CAAC,GAAG,IAAI;EAElE,IAAI6B,QAAQ,GAAG,aAAa,UAAUC,UAAU,EAAE;IAChDtC,cAAc,CAACqC,QAAQ,EAAEC,UAAU,CAAC;IAEpC,SAASD,QAAQA,CAAA,EAAG;MAClB,IAAIE,KAAK;MAETA,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;MACjDF,KAAK,CAACG,OAAO,GAAG7B,kBAAkB,CAAC0B,KAAK,CAACI,KAAK,CAAC7B,KAAK,CAAC;MACrD,OAAOyB,KAAK;IACd;IAEA,IAAIK,MAAM,GAAGP,QAAQ,CAACQ,SAAS;IAE/BD,MAAM,CAACE,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;MAClD,IAAIC,IAAI;MAER,OAAOA,IAAI,GAAG,CAAC,CAAC,EAAEA,IAAI,CAACX,WAAW,CAAC,GAAG,IAAI,CAACM,OAAO,EAAEK,IAAI;IAC1D,CAAC;IAEDH,MAAM,CAACI,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,SAAS,EAAE;MAC/E,IAAI,IAAI,CAACN,KAAK,CAAC7B,KAAK,KAAKmC,SAAS,CAACnC,KAAK,EAAE;QACxC,IAAIoC,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC7B,KAAK;QAC/B,IAAIU,QAAQ,GAAGyB,SAAS,CAACnC,KAAK;QAC9B,IAAIW,WAAW;QAEf,IAAIf,QAAQ,CAACwC,QAAQ,EAAE1B,QAAQ,CAAC,EAAE;UAChCC,WAAW,GAAG,CAAC;QACjB,CAAC,MAAM;UACLA,WAAW,GAAG,OAAOQ,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACiB,QAAQ,EAAE1B,QAAQ,CAAC,GAAGrB,qBAAqB;UAE3H,IAAIgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCnD,OAAO,CAAC,CAACuB,WAAW,GAAGtB,qBAAqB,MAAMsB,WAAW,EAAE,0DAA0D,GAAG,oCAAoC,GAAGA,WAAW,CAAC;UACjL;UAEAA,WAAW,IAAI,CAAC;UAEhB,IAAIA,WAAW,KAAK,CAAC,EAAE;YACrB,IAAI,CAACiB,OAAO,CAACnB,GAAG,CAAC0B,SAAS,CAACnC,KAAK,EAAEW,WAAW,CAAC;UAChD;QACF;MACF;IACF,CAAC;IAEDmB,MAAM,CAACU,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MAChC,OAAO,IAAI,CAACX,KAAK,CAACf,QAAQ;IAC5B,CAAC;IAED,OAAOS,QAAQ;EACjB,CAAC,CAACtC,SAAS,CAAC;EAEZsC,QAAQ,CAACkB,iBAAiB,IAAIrB,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAACE,WAAW,CAAC,GAAGnC,SAAS,CAACuD,MAAM,CAACC,UAAU,EAAEvB,qBAAqB,CAAC;EAElJ,IAAIwB,QAAQ,GAAG,aAAa,UAAUC,WAAW,EAAE;IACjD3D,cAAc,CAAC0D,QAAQ,EAAEC,WAAW,CAAC;IAErC,SAASD,QAAQA,CAAA,EAAG;MAClB,IAAIE,MAAM;MAEVA,MAAM,GAAGD,WAAW,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;MACnDmB,MAAM,CAACC,KAAK,GAAG;QACb/C,KAAK,EAAE8C,MAAM,CAACE,QAAQ,CAAC;MACzB,CAAC;MAEDF,MAAM,CAACG,QAAQ,GAAG,UAAUvC,QAAQ,EAAEC,WAAW,EAAE;QACjD,IAAIuC,YAAY,GAAGJ,MAAM,CAACI,YAAY,GAAG,CAAC;QAE1C,IAAI,CAACA,YAAY,GAAGvC,WAAW,MAAM,CAAC,EAAE;UACtCmC,MAAM,CAACK,QAAQ,CAAC;YACdnD,KAAK,EAAE8C,MAAM,CAACE,QAAQ,CAAC;UACzB,CAAC,CAAC;QACJ;MACF,CAAC;MAED,OAAOF,MAAM;IACf;IAEA,IAAIM,OAAO,GAAGR,QAAQ,CAACb,SAAS;IAEhCqB,OAAO,CAAClB,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,SAAS,EAAE;MAChF,IAAIe,YAAY,GAAGf,SAAS,CAACe,YAAY;MACzC,IAAI,CAACA,YAAY,GAAGA,YAAY,KAAKG,SAAS,IAAIH,YAAY,KAAK,IAAI,GAAG7D,qBAAqB,GAAG6D,YAAY;IAChH,CAAC;IAEDE,OAAO,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;MACvD,IAAI,IAAI,CAACC,OAAO,CAACjC,WAAW,CAAC,EAAE;QAC7B,IAAI,CAACiC,OAAO,CAACjC,WAAW,CAAC,CAACpB,EAAE,CAAC,IAAI,CAAC+C,QAAQ,CAAC;MAC7C;MAEA,IAAIC,YAAY,GAAG,IAAI,CAACrB,KAAK,CAACqB,YAAY;MAC1C,IAAI,CAACA,YAAY,GAAGA,YAAY,KAAKG,SAAS,IAAIH,YAAY,KAAK,IAAI,GAAG7D,qBAAqB,GAAG6D,YAAY;IAChH,CAAC;IAEDE,OAAO,CAACI,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;MAC7D,IAAI,IAAI,CAACD,OAAO,CAACjC,WAAW,CAAC,EAAE;QAC7B,IAAI,CAACiC,OAAO,CAACjC,WAAW,CAAC,CAACjB,GAAG,CAAC,IAAI,CAAC4C,QAAQ,CAAC;MAC9C;IACF,CAAC;IAEDG,OAAO,CAACJ,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACO,OAAO,CAACjC,WAAW,CAAC,EAAE;QAC7B,OAAO,IAAI,CAACiC,OAAO,CAACjC,WAAW,CAAC,CAACd,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,OAAOU,YAAY;MACrB;IACF,CAAC;IAEDkC,OAAO,CAACZ,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MACjC,OAAO3B,SAAS,CAAC,IAAI,CAACgB,KAAK,CAACf,QAAQ,CAAC,CAAC,IAAI,CAACiC,KAAK,CAAC/C,KAAK,CAAC;IACzD,CAAC;IAED,OAAO4C,QAAQ;EACjB,CAAC,CAAC3D,SAAS,CAAC;EAEZ2D,QAAQ,CAACa,YAAY,IAAIpC,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAACC,WAAW,CAAC,GAAGnC,SAAS,CAACuD,MAAM,EAAErB,qBAAqB,CAAC;EAClI,OAAO;IACLE,QAAQ,EAAEA,QAAQ;IAClBqB,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEA,IAAIc,KAAK,GAAG1E,KAAK,CAAC2E,aAAa,IAAI1C,kBAAkB;AAErD,eAAeyC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}