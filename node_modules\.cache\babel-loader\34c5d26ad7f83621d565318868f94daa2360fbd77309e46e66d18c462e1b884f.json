{"ast": null, "code": "import * as React from 'react';\nimport padEnd from 'lodash/padEnd';\nvar StatisticNumber = function StatisticNumber(props) {\n  var value = props.value,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    _props$groupSeparator = props.groupSeparator,\n    groupSeparator = _props$groupSeparator === void 0 ? '' : _props$groupSeparator,\n    prefixCls = props.prefixCls;\n  var valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    var val = String(value);\n    var cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/); // Process if illegal number\n\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      var negative = cells[1];\n      var int = cells[2] || '0';\n      var decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = padEnd(decimal, precision, '0').slice(0, precision);\n      }\n      if (decimal) {\n        decimal = \"\".concat(decimalSeparator).concat(decimal);\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: \"\".concat(prefixCls, \"-content-value-int\")\n      }, negative, int), decimal && /*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: \"\".concat(prefixCls, \"-content-value-decimal\")\n      }, decimal)];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-value\")\n  }, valueNode);\n};\nexport default StatisticNumber;", "map": {"version": 3, "names": ["React", "padEnd", "StatisticNumber", "props", "value", "formatter", "precision", "decimalSeparator", "_props$groupSeparator", "groupSeparator", "prefixCls", "valueNode", "val", "String", "cells", "match", "negative", "int", "decimal", "replace", "slice", "concat", "createElement", "key", "className"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/statistic/Number.js"], "sourcesContent": ["import * as React from 'react';\nimport padEnd from 'lodash/padEnd';\n\nvar StatisticNumber = function StatisticNumber(props) {\n  var value = props.value,\n      formatter = props.formatter,\n      precision = props.precision,\n      decimalSeparator = props.decimalSeparator,\n      _props$groupSeparator = props.groupSeparator,\n      groupSeparator = _props$groupSeparator === void 0 ? '' : _props$groupSeparator,\n      prefixCls = props.prefixCls;\n  var valueNode;\n\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    var val = String(value);\n    var cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/); // Process if illegal number\n\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      var negative = cells[1];\n      var int = cells[2] || '0';\n      var decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n\n      if (typeof precision === 'number') {\n        decimal = padEnd(decimal, precision, '0').slice(0, precision);\n      }\n\n      if (decimal) {\n        decimal = \"\".concat(decimalSeparator).concat(decimal);\n      }\n\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: \"\".concat(prefixCls, \"-content-value-int\")\n      }, negative, int), decimal && /*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: \"\".concat(prefixCls, \"-content-value-decimal\")\n      }, decimal)];\n    }\n  }\n\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-value\")\n  }, valueNode);\n};\n\nexport default StatisticNumber;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,eAAe;AAElC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;EACpD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,gBAAgB,GAAGJ,KAAK,CAACI,gBAAgB;IACzCC,qBAAqB,GAAGL,KAAK,CAACM,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9EE,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC/B,IAAIC,SAAS;EAEb,IAAI,OAAON,SAAS,KAAK,UAAU,EAAE;IACnC;IACAM,SAAS,GAAGN,SAAS,CAACD,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL;IACA,IAAIQ,GAAG,GAAGC,MAAM,CAACT,KAAK,CAAC;IACvB,IAAIU,KAAK,GAAGF,GAAG,CAACG,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;;IAEhD,IAAI,CAACD,KAAK,IAAIF,GAAG,KAAK,GAAG,EAAE;MACzBD,SAAS,GAAGC,GAAG;IACjB,CAAC,MAAM;MACL,IAAII,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;MACvB,IAAIG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;MACzB,IAAII,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAC5BG,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,uBAAuB,EAAEV,cAAc,CAAC;MAE1D,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;QACjCY,OAAO,GAAGjB,MAAM,CAACiB,OAAO,EAAEZ,SAAS,EAAE,GAAG,CAAC,CAACc,KAAK,CAAC,CAAC,EAAEd,SAAS,CAAC;MAC/D;MAEA,IAAIY,OAAO,EAAE;QACXA,OAAO,GAAG,EAAE,CAACG,MAAM,CAACd,gBAAgB,CAAC,CAACc,MAAM,CAACH,OAAO,CAAC;MACvD;MAEAP,SAAS,GAAG,CAAC,aAAaX,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;QACpDC,GAAG,EAAE,KAAK;QACVC,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,oBAAoB;MACtD,CAAC,EAAEM,QAAQ,EAAEC,GAAG,CAAC,EAAEC,OAAO,IAAI,aAAalB,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;QACrEC,GAAG,EAAE,SAAS;QACdC,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,wBAAwB;MAC1D,CAAC,EAAEQ,OAAO,CAAC,CAAC;IACd;EACF;EAEA,OAAO,aAAalB,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;IAC9CE,SAAS,EAAE,EAAE,CAACH,MAAM,CAACX,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAEC,SAAS,CAAC;AACf,CAAC;AAED,eAAeT,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}