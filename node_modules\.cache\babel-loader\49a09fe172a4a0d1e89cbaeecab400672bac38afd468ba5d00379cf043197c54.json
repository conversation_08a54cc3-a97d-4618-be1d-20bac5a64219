{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { useState, useCallback, useMemo } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { arrAdd, arrDel } from \"rc-tree/es/util\";\nimport { INTERNAL_COL_DEFINE } from 'rc-table';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Checkbox from '../../checkbox';\nimport Dropdown from '../../dropdown';\nimport Menu from '../../menu';\nimport Radio from '../../radio';\nimport devWarning from '../../_util/devWarning'; // TODO: warning if use ajax!!!\n\nexport var SELECTION_COLUMN = {};\nexport var SELECTION_ALL = 'SELECT_ALL';\nexport var SELECTION_INVERT = 'SELECT_INVERT';\nexport var SELECTION_NONE = 'SELECT_NONE';\nvar EMPTY_LIST = [];\nfunction flattenData(data, childrenColumnName) {\n  var list = [];\n  (data || []).forEach(function (record) {\n    list.push(record);\n    if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n      list = [].concat(_toConsumableArray(list), _toConsumableArray(flattenData(record[childrenColumnName], childrenColumnName)));\n    }\n  });\n  return list;\n}\nexport default function useSelection(rowSelection, config) {\n  var _ref = rowSelection || {},\n    preserveSelectedRowKeys = _ref.preserveSelectedRowKeys,\n    selectedRowKeys = _ref.selectedRowKeys,\n    defaultSelectedRowKeys = _ref.defaultSelectedRowKeys,\n    getCheckboxProps = _ref.getCheckboxProps,\n    onSelectionChange = _ref.onChange,\n    onSelect = _ref.onSelect,\n    onSelectAll = _ref.onSelectAll,\n    onSelectInvert = _ref.onSelectInvert,\n    onSelectNone = _ref.onSelectNone,\n    onSelectMultiple = _ref.onSelectMultiple,\n    selectionColWidth = _ref.columnWidth,\n    selectionType = _ref.type,\n    selections = _ref.selections,\n    fixed = _ref.fixed,\n    customizeRenderCell = _ref.renderCell,\n    hideSelectAll = _ref.hideSelectAll,\n    _ref$checkStrictly = _ref.checkStrictly,\n    checkStrictly = _ref$checkStrictly === void 0 ? true : _ref$checkStrictly;\n  var prefixCls = config.prefixCls,\n    data = config.data,\n    pageData = config.pageData,\n    getRecordByKey = config.getRecordByKey,\n    getRowKey = config.getRowKey,\n    expandType = config.expandType,\n    childrenColumnName = config.childrenColumnName,\n    tableLocale = config.locale,\n    getPopupContainer = config.getPopupContainer; // ========================= Keys =========================\n\n  var _useMergedState = useMergedState(selectedRowKeys || defaultSelectedRowKeys || EMPTY_LIST, {\n      value: selectedRowKeys\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSelectedKeys = _useMergedState2[0],\n    setMergedSelectedKeys = _useMergedState2[1]; // ======================== Caches ========================\n\n  var preserveRecordsRef = React.useRef(new Map());\n  var updatePreserveRecordsCache = useCallback(function (keys) {\n    if (preserveSelectedRowKeys) {\n      var newCache = new Map(); // Keep key if mark as preserveSelectedRowKeys\n\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (!record && preserveRecordsRef.current.has(key)) {\n          record = preserveRecordsRef.current.get(key);\n        }\n        newCache.set(key, record);\n      }); // Refresh to new cache\n\n      preserveRecordsRef.current = newCache;\n    }\n  }, [getRecordByKey, preserveSelectedRowKeys]); // Update cache with selectedKeys\n\n  React.useEffect(function () {\n    updatePreserveRecordsCache(mergedSelectedKeys);\n  }, [mergedSelectedKeys]);\n  var _useMemo = useMemo(function () {\n      return checkStrictly ? {\n        keyEntities: null\n      } : convertDataToEntities(data, {\n        externalGetKey: getRowKey,\n        childrenPropName: childrenColumnName\n      });\n    }, [data, getRowKey, checkStrictly, childrenColumnName]),\n    keyEntities = _useMemo.keyEntities; // Get flatten data\n\n  var flattedData = useMemo(function () {\n    return flattenData(pageData, childrenColumnName);\n  }, [pageData, childrenColumnName]); // Get all checkbox props\n\n  var checkboxPropsMap = useMemo(function () {\n    var map = new Map();\n    flattedData.forEach(function (record, index) {\n      var key = getRowKey(record, index);\n      var checkboxProps = (getCheckboxProps ? getCheckboxProps(record) : null) || {};\n      map.set(key, checkboxProps);\n      if (process.env.NODE_ENV !== 'production' && ('checked' in checkboxProps || 'defaultChecked' in checkboxProps)) {\n        devWarning(false, 'Table', 'Do not set `checked` or `defaultChecked` in `getCheckboxProps`. Please use `selectedRowKeys` instead.');\n      }\n    });\n    return map;\n  }, [flattedData, getRowKey, getCheckboxProps]);\n  var isCheckboxDisabled = useCallback(function (r) {\n    var _a;\n    return !!((_a = checkboxPropsMap.get(getRowKey(r))) === null || _a === void 0 ? void 0 : _a.disabled);\n  }, [checkboxPropsMap, getRowKey]);\n  var _useMemo2 = useMemo(function () {\n      if (checkStrictly) {\n        return [mergedSelectedKeys || [], []];\n      }\n      var _conductCheck = conductCheck(mergedSelectedKeys, true, keyEntities, isCheckboxDisabled),\n        checkedKeys = _conductCheck.checkedKeys,\n        halfCheckedKeys = _conductCheck.halfCheckedKeys;\n      return [checkedKeys || [], halfCheckedKeys];\n    }, [mergedSelectedKeys, checkStrictly, keyEntities, isCheckboxDisabled]),\n    _useMemo3 = _slicedToArray(_useMemo2, 2),\n    derivedSelectedKeys = _useMemo3[0],\n    derivedHalfSelectedKeys = _useMemo3[1];\n  var derivedSelectedKeySet = useMemo(function () {\n    var keys = selectionType === 'radio' ? derivedSelectedKeys.slice(0, 1) : derivedSelectedKeys;\n    return new Set(keys);\n  }, [derivedSelectedKeys, selectionType]);\n  var derivedHalfSelectedKeySet = useMemo(function () {\n    return selectionType === 'radio' ? new Set() : new Set(derivedHalfSelectedKeys);\n  }, [derivedHalfSelectedKeys, selectionType]); // Save last selected key to enable range selection\n\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    lastSelectedKey = _useState2[0],\n    setLastSelectedKey = _useState2[1]; // Reset if rowSelection reset\n\n  React.useEffect(function () {\n    if (!rowSelection) {\n      setMergedSelectedKeys(EMPTY_LIST);\n    }\n  }, [!!rowSelection]);\n  var setSelectedKeys = useCallback(function (keys) {\n    var availableKeys;\n    var records;\n    updatePreserveRecordsCache(keys);\n    if (preserveSelectedRowKeys) {\n      availableKeys = keys;\n      records = keys.map(function (key) {\n        return preserveRecordsRef.current.get(key);\n      });\n    } else {\n      // Filter key which not exist in the `dataSource`\n      availableKeys = [];\n      records = [];\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (record !== undefined) {\n          availableKeys.push(key);\n          records.push(record);\n        }\n      });\n    }\n    setMergedSelectedKeys(availableKeys);\n    onSelectionChange === null || onSelectionChange === void 0 ? void 0 : onSelectionChange(availableKeys, records);\n  }, [setMergedSelectedKeys, getRecordByKey, onSelectionChange, preserveSelectedRowKeys]); // ====================== Selections ======================\n  // Trigger single `onSelect` event\n\n  var triggerSingleSelection = useCallback(function (key, selected, keys, event) {\n    if (onSelect) {\n      var rows = keys.map(function (k) {\n        return getRecordByKey(k);\n      });\n      onSelect(getRecordByKey(key), selected, rows, event);\n    }\n    setSelectedKeys(keys);\n  }, [onSelect, getRecordByKey, setSelectedKeys]);\n  var mergedSelections = useMemo(function () {\n    if (!selections || hideSelectAll) {\n      return null;\n    }\n    var selectionList = selections === true ? [SELECTION_ALL, SELECTION_INVERT, SELECTION_NONE] : selections;\n    return selectionList.map(function (selection) {\n      if (selection === SELECTION_ALL) {\n        return {\n          key: 'all',\n          text: tableLocale.selectionAll,\n          onSelect: function onSelect() {\n            setSelectedKeys(data.map(function (record, index) {\n              return getRowKey(record, index);\n            }).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return !(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled) || derivedSelectedKeySet.has(key);\n            }));\n          }\n        };\n      }\n      if (selection === SELECTION_INVERT) {\n        return {\n          key: 'invert',\n          text: tableLocale.selectInvert,\n          onSelect: function onSelect() {\n            var keySet = new Set(derivedSelectedKeySet);\n            pageData.forEach(function (record, index) {\n              var key = getRowKey(record, index);\n              var checkProps = checkboxPropsMap.get(key);\n              if (!(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled)) {\n                if (keySet.has(key)) {\n                  keySet[\"delete\"](key);\n                } else {\n                  keySet.add(key);\n                }\n              }\n            });\n            var keys = Array.from(keySet);\n            if (onSelectInvert) {\n              devWarning(false, 'Table', '`onSelectInvert` will be removed in future. Please use `onChange` instead.');\n              onSelectInvert(keys);\n            }\n            setSelectedKeys(keys);\n          }\n        };\n      }\n      if (selection === SELECTION_NONE) {\n        return {\n          key: 'none',\n          text: tableLocale.selectNone,\n          onSelect: function onSelect() {\n            onSelectNone === null || onSelectNone === void 0 ? void 0 : onSelectNone();\n            setSelectedKeys(Array.from(derivedSelectedKeySet).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled;\n            }));\n          }\n        };\n      }\n      return selection;\n    });\n  }, [selections, derivedSelectedKeySet, pageData, getRowKey, onSelectInvert, setSelectedKeys]); // ======================= Columns ========================\n\n  var transformColumns = useCallback(function (columns) {\n    var _a; // >>>>>>>>>>> Skip if not exists `rowSelection`\n\n    if (!rowSelection) {\n      if (process.env.NODE_ENV !== 'production') {\n        devWarning(!columns.includes(SELECTION_COLUMN), 'Table', '`rowSelection` is not config but `SELECTION_COLUMN` exists in the `columns`.');\n      }\n      return columns.filter(function (col) {\n        return col !== SELECTION_COLUMN;\n      });\n    } // >>>>>>>>>>> Support selection\n\n    var cloneColumns = _toConsumableArray(columns);\n    var keySet = new Set(derivedSelectedKeySet); // Record key only need check with enabled\n\n    var recordKeys = flattedData.map(getRowKey).filter(function (key) {\n      return !checkboxPropsMap.get(key).disabled;\n    });\n    var checkedCurrentAll = recordKeys.every(function (key) {\n      return keySet.has(key);\n    });\n    var checkedCurrentSome = recordKeys.some(function (key) {\n      return keySet.has(key);\n    });\n    var onSelectAllChange = function onSelectAllChange() {\n      var changeKeys = [];\n      if (checkedCurrentAll) {\n        recordKeys.forEach(function (key) {\n          keySet[\"delete\"](key);\n          changeKeys.push(key);\n        });\n      } else {\n        recordKeys.forEach(function (key) {\n          if (!keySet.has(key)) {\n            keySet.add(key);\n            changeKeys.push(key);\n          }\n        });\n      }\n      var keys = Array.from(keySet);\n      onSelectAll === null || onSelectAll === void 0 ? void 0 : onSelectAll(!checkedCurrentAll, keys.map(function (k) {\n        return getRecordByKey(k);\n      }), changeKeys.map(function (k) {\n        return getRecordByKey(k);\n      }));\n      setSelectedKeys(keys);\n    }; // ===================== Render =====================\n    // Title Cell\n\n    var title;\n    if (selectionType !== 'radio') {\n      var customizeSelections;\n      if (mergedSelections) {\n        var menu = /*#__PURE__*/React.createElement(Menu, {\n          getPopupContainer: getPopupContainer,\n          items: mergedSelections.map(function (selection, index) {\n            var key = selection.key,\n              text = selection.text,\n              onSelectionClick = selection.onSelect;\n            return {\n              key: key || index,\n              onClick: function onClick() {\n                onSelectionClick === null || onSelectionClick === void 0 ? void 0 : onSelectionClick(recordKeys);\n              },\n              label: text\n            };\n          })\n        });\n        customizeSelections = /*#__PURE__*/React.createElement(\"div\", {\n          className: \"\".concat(prefixCls, \"-selection-extra\")\n        }, /*#__PURE__*/React.createElement(Dropdown, {\n          overlay: menu,\n          getPopupContainer: getPopupContainer\n        }, /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(DownOutlined, null))));\n      }\n      var allDisabledData = flattedData.map(function (record, index) {\n        var key = getRowKey(record, index);\n        var checkboxProps = checkboxPropsMap.get(key) || {};\n        return _extends({\n          checked: keySet.has(key)\n        }, checkboxProps);\n      }).filter(function (_ref2) {\n        var disabled = _ref2.disabled;\n        return disabled;\n      });\n      var allDisabled = !!allDisabledData.length && allDisabledData.length === flattedData.length;\n      var allDisabledAndChecked = allDisabled && allDisabledData.every(function (_ref3) {\n        var checked = _ref3.checked;\n        return checked;\n      });\n      var allDisabledSomeChecked = allDisabled && allDisabledData.some(function (_ref4) {\n        var checked = _ref4.checked;\n        return checked;\n      });\n      title = !hideSelectAll && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-selection\")\n      }, /*#__PURE__*/React.createElement(Checkbox, {\n        checked: !allDisabled ? !!flattedData.length && checkedCurrentAll : allDisabledAndChecked,\n        indeterminate: !allDisabled ? !checkedCurrentAll && checkedCurrentSome : !allDisabledAndChecked && allDisabledSomeChecked,\n        onChange: onSelectAllChange,\n        disabled: flattedData.length === 0 || allDisabled,\n        skipGroup: true\n      }), customizeSelections);\n    } // Body Cell\n\n    var renderCell;\n    if (selectionType === 'radio') {\n      renderCell = function renderCell(_, record, index) {\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        return {\n          node: /*#__PURE__*/React.createElement(Radio, _extends({}, checkboxPropsMap.get(key), {\n            checked: checked,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(event) {\n              if (!keySet.has(key)) {\n                triggerSingleSelection(key, true, [key], event.nativeEvent);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    } else {\n      renderCell = function renderCell(_, record, index) {\n        var _a;\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        var indeterminate = derivedHalfSelectedKeySet.has(key);\n        var checkboxProps = checkboxPropsMap.get(key);\n        var mergedIndeterminate;\n        if (expandType === 'nest') {\n          mergedIndeterminate = indeterminate;\n          devWarning(typeof (checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== 'boolean', 'Table', 'set `indeterminate` using `rowSelection.getCheckboxProps` is not allowed with tree structured dataSource.');\n        } else {\n          mergedIndeterminate = (_a = checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== null && _a !== void 0 ? _a : indeterminate;\n        } // Record checked\n\n        return {\n          node: /*#__PURE__*/React.createElement(Checkbox, _extends({}, checkboxProps, {\n            indeterminate: mergedIndeterminate,\n            checked: checked,\n            skipGroup: true,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(_ref5) {\n              var nativeEvent = _ref5.nativeEvent;\n              var shiftKey = nativeEvent.shiftKey;\n              var startIndex = -1;\n              var endIndex = -1; // Get range of this\n\n              if (shiftKey && checkStrictly) {\n                var pointKeys = new Set([lastSelectedKey, key]);\n                recordKeys.some(function (recordKey, recordIndex) {\n                  if (pointKeys.has(recordKey)) {\n                    if (startIndex === -1) {\n                      startIndex = recordIndex;\n                    } else {\n                      endIndex = recordIndex;\n                      return true;\n                    }\n                  }\n                  return false;\n                });\n              }\n              if (endIndex !== -1 && startIndex !== endIndex && checkStrictly) {\n                // Batch update selections\n                var rangeKeys = recordKeys.slice(startIndex, endIndex + 1);\n                var changedKeys = [];\n                if (checked) {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet[\"delete\"](recordKey);\n                    }\n                  });\n                } else {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (!keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet.add(recordKey);\n                    }\n                  });\n                }\n                var keys = Array.from(keySet);\n                onSelectMultiple === null || onSelectMultiple === void 0 ? void 0 : onSelectMultiple(!checked, keys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }), changedKeys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }));\n                setSelectedKeys(keys);\n              } else {\n                // Single record selected\n                var originCheckedKeys = derivedSelectedKeys;\n                if (checkStrictly) {\n                  var checkedKeys = checked ? arrDel(originCheckedKeys, key) : arrAdd(originCheckedKeys, key);\n                  triggerSingleSelection(key, !checked, checkedKeys, nativeEvent);\n                } else {\n                  // Always fill first\n                  var result = conductCheck([].concat(_toConsumableArray(originCheckedKeys), [key]), true, keyEntities, isCheckboxDisabled);\n                  var _checkedKeys = result.checkedKeys,\n                    halfCheckedKeys = result.halfCheckedKeys;\n                  var nextCheckedKeys = _checkedKeys; // If remove, we do it again to correction\n\n                  if (checked) {\n                    var tempKeySet = new Set(_checkedKeys);\n                    tempKeySet[\"delete\"](key);\n                    nextCheckedKeys = conductCheck(Array.from(tempKeySet), {\n                      checked: false,\n                      halfCheckedKeys: halfCheckedKeys\n                    }, keyEntities, isCheckboxDisabled).checkedKeys;\n                  }\n                  triggerSingleSelection(key, !checked, nextCheckedKeys, nativeEvent);\n                }\n              }\n              setLastSelectedKey(key);\n            }\n          })),\n          checked: checked\n        };\n      };\n    }\n    var renderSelectionCell = function renderSelectionCell(_, record, index) {\n      var _renderCell = renderCell(_, record, index),\n        node = _renderCell.node,\n        checked = _renderCell.checked;\n      if (customizeRenderCell) {\n        return customizeRenderCell(checked, record, index, node);\n      }\n      return node;\n    }; // Insert selection column if not exist\n\n    if (!cloneColumns.includes(SELECTION_COLUMN)) {\n      // Always after expand icon\n      if (cloneColumns.findIndex(function (col) {\n        var _a;\n        return ((_a = col[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN';\n      }) === 0) {\n        var _cloneColumns = cloneColumns,\n          _cloneColumns2 = _toArray(_cloneColumns),\n          expandColumn = _cloneColumns2[0],\n          restColumns = _cloneColumns2.slice(1);\n        cloneColumns = [expandColumn, SELECTION_COLUMN].concat(_toConsumableArray(restColumns));\n      } else {\n        // Normal insert at first column\n        cloneColumns = [SELECTION_COLUMN].concat(_toConsumableArray(cloneColumns));\n      }\n    } // Deduplicate selection column\n\n    var selectionColumnIndex = cloneColumns.indexOf(SELECTION_COLUMN);\n    if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (col) {\n      return col === SELECTION_COLUMN;\n    }).length > 1) {\n      devWarning(false, 'Table', 'Multiple `SELECTION_COLUMN` exist in `columns`.');\n    }\n    cloneColumns = cloneColumns.filter(function (column, index) {\n      return column !== SELECTION_COLUMN || index === selectionColumnIndex;\n    }); // Fixed column logic\n\n    var prevCol = cloneColumns[selectionColumnIndex - 1];\n    var nextCol = cloneColumns[selectionColumnIndex + 1];\n    var mergedFixed = fixed;\n    if (mergedFixed === undefined) {\n      if ((nextCol === null || nextCol === void 0 ? void 0 : nextCol.fixed) !== undefined) {\n        mergedFixed = nextCol.fixed;\n      } else if ((prevCol === null || prevCol === void 0 ? void 0 : prevCol.fixed) !== undefined) {\n        mergedFixed = prevCol.fixed;\n      }\n    }\n    if (mergedFixed && prevCol && ((_a = prevCol[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN' && prevCol.fixed === undefined) {\n      prevCol.fixed = mergedFixed;\n    } // Replace with real selection column\n\n    var selectionColumn = _defineProperty({\n      fixed: mergedFixed,\n      width: selectionColWidth,\n      className: \"\".concat(prefixCls, \"-selection-column\"),\n      title: rowSelection.columnTitle || title,\n      render: renderSelectionCell\n    }, INTERNAL_COL_DEFINE, {\n      className: \"\".concat(prefixCls, \"-selection-col\")\n    });\n    return cloneColumns.map(function (col) {\n      return col === SELECTION_COLUMN ? selectionColumn : col;\n    });\n  }, [getRowKey, flattedData, rowSelection, derivedSelectedKeys, derivedSelectedKeySet, derivedHalfSelectedKeySet, selectionColWidth, mergedSelections, expandType, lastSelectedKey, checkboxPropsMap, onSelectMultiple, triggerSingleSelection, isCheckboxDisabled]);\n  return [transformColumns, derivedSelectedKeySet];\n}", "map": {"version": 3, "names": ["_defineProperty", "_toArray", "_extends", "_slicedToArray", "_toConsumableArray", "_typeof", "React", "useState", "useCallback", "useMemo", "DownOutlined", "convertDataToEntities", "conduct<PERSON>heck", "arrAdd", "arr<PERSON><PERSON>", "INTERNAL_COL_DEFINE", "useMergedState", "Checkbox", "Dropdown", "<PERSON><PERSON>", "Radio", "dev<PERSON><PERSON><PERSON>", "SELECTION_COLUMN", "SELECTION_ALL", "SELECTION_INVERT", "SELECTION_NONE", "EMPTY_LIST", "flattenData", "data", "childrenColumnName", "list", "for<PERSON>ach", "record", "push", "concat", "useSelection", "rowSelection", "config", "_ref", "preserveSelectedRowKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultSelectedRowKeys", "getCheckboxProps", "onSelectionChange", "onChange", "onSelect", "onSelectAll", "onSelectInvert", "onSelectNone", "onSelectMultiple", "selection<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnWidth", "selectionType", "type", "selections", "fixed", "customizeRenderCell", "renderCell", "hideSelectAll", "_ref$checkStrictly", "checkStrictly", "prefixCls", "pageData", "getRecordByKey", "getRowKey", "expandType", "tableLocale", "locale", "getPopupContainer", "_useMergedState", "value", "_useMergedState2", "mergedSelectedKeys", "setMergedSelectedKeys", "preserveRecordsRef", "useRef", "Map", "updatePreserveRecordsCache", "keys", "newCache", "key", "current", "has", "get", "set", "useEffect", "_useMemo", "keyEntities", "externalGetKey", "childrenPropName", "flattedData", "checkboxPropsMap", "map", "index", "checkboxProps", "process", "env", "NODE_ENV", "isCheckboxDisabled", "r", "_a", "disabled", "_useMemo2", "_conductCheck", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "_useMemo3", "derivedSelectedKeys", "derivedHalfSelectedKeys", "derivedSelectedKeySet", "slice", "Set", "derivedHalfSelectedKeySet", "_useState", "_useState2", "lastSelectedKey", "setLastSelectedKey", "setSelectedKeys", "availableKeys", "records", "undefined", "triggerSingleSelection", "selected", "event", "rows", "k", "mergedSelections", "selectionList", "selection", "text", "selectionAll", "filter", "checkProps", "selectInvert", "keySet", "add", "Array", "from", "selectNone", "transformColumns", "columns", "includes", "col", "cloneColumns", "recordKeys", "checkedCurrentAll", "every", "checkedCurrentSome", "some", "onSelectAllChange", "changeKeys", "title", "customizeSelections", "menu", "createElement", "items", "onSelectionClick", "onClick", "label", "className", "overlay", "allDisabledData", "checked", "_ref2", "allDisabled", "length", "allDisabledAndChecked", "_ref3", "allDisabledSomeChecked", "_ref4", "indeterminate", "skipGroup", "_", "node", "e", "stopPropagation", "nativeEvent", "mergedIndeterminate", "_ref5", "shift<PERSON>ey", "startIndex", "endIndex", "point<PERSON>eys", "<PERSON><PERSON>ey", "recordIndex", "rangeKeys", "changed<PERSON><PERSON><PERSON>", "originCheckedKeys", "result", "_checked<PERSON><PERSON><PERSON>", "next<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tempKeySet", "renderSelectionCell", "_renderCell", "findIndex", "columnType", "_cloneColumns", "_cloneColumns2", "expandColumn", "restColumns", "selectionColumnIndex", "indexOf", "column", "prevCol", "nextCol", "mergedFixed", "selectionColumn", "width", "columnTitle", "render"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/table/hooks/useSelection.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { useState, useCallback, useMemo } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { arrAdd, arrDel } from \"rc-tree/es/util\";\nimport { INTERNAL_COL_DEFINE } from 'rc-table';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Checkbox from '../../checkbox';\nimport Dropdown from '../../dropdown';\nimport Menu from '../../menu';\nimport Radio from '../../radio';\nimport devWarning from '../../_util/devWarning'; // TODO: warning if use ajax!!!\n\nexport var SELECTION_COLUMN = {};\nexport var SELECTION_ALL = 'SELECT_ALL';\nexport var SELECTION_INVERT = 'SELECT_INVERT';\nexport var SELECTION_NONE = 'SELECT_NONE';\nvar EMPTY_LIST = [];\n\nfunction flattenData(data, childrenColumnName) {\n  var list = [];\n  (data || []).forEach(function (record) {\n    list.push(record);\n\n    if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n      list = [].concat(_toConsumableArray(list), _toConsumableArray(flattenData(record[childrenColumnName], childrenColumnName)));\n    }\n  });\n  return list;\n}\n\nexport default function useSelection(rowSelection, config) {\n  var _ref = rowSelection || {},\n      preserveSelectedRowKeys = _ref.preserveSelectedRowKeys,\n      selectedRowKeys = _ref.selectedRowKeys,\n      defaultSelectedRowKeys = _ref.defaultSelectedRowKeys,\n      getCheckboxProps = _ref.getCheckboxProps,\n      onSelectionChange = _ref.onChange,\n      onSelect = _ref.onSelect,\n      onSelectAll = _ref.onSelectAll,\n      onSelectInvert = _ref.onSelectInvert,\n      onSelectNone = _ref.onSelectNone,\n      onSelectMultiple = _ref.onSelectMultiple,\n      selectionColWidth = _ref.columnWidth,\n      selectionType = _ref.type,\n      selections = _ref.selections,\n      fixed = _ref.fixed,\n      customizeRenderCell = _ref.renderCell,\n      hideSelectAll = _ref.hideSelectAll,\n      _ref$checkStrictly = _ref.checkStrictly,\n      checkStrictly = _ref$checkStrictly === void 0 ? true : _ref$checkStrictly;\n\n  var prefixCls = config.prefixCls,\n      data = config.data,\n      pageData = config.pageData,\n      getRecordByKey = config.getRecordByKey,\n      getRowKey = config.getRowKey,\n      expandType = config.expandType,\n      childrenColumnName = config.childrenColumnName,\n      tableLocale = config.locale,\n      getPopupContainer = config.getPopupContainer; // ========================= Keys =========================\n\n  var _useMergedState = useMergedState(selectedRowKeys || defaultSelectedRowKeys || EMPTY_LIST, {\n    value: selectedRowKeys\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedSelectedKeys = _useMergedState2[0],\n      setMergedSelectedKeys = _useMergedState2[1]; // ======================== Caches ========================\n\n\n  var preserveRecordsRef = React.useRef(new Map());\n  var updatePreserveRecordsCache = useCallback(function (keys) {\n    if (preserveSelectedRowKeys) {\n      var newCache = new Map(); // Keep key if mark as preserveSelectedRowKeys\n\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n\n        if (!record && preserveRecordsRef.current.has(key)) {\n          record = preserveRecordsRef.current.get(key);\n        }\n\n        newCache.set(key, record);\n      }); // Refresh to new cache\n\n      preserveRecordsRef.current = newCache;\n    }\n  }, [getRecordByKey, preserveSelectedRowKeys]); // Update cache with selectedKeys\n\n  React.useEffect(function () {\n    updatePreserveRecordsCache(mergedSelectedKeys);\n  }, [mergedSelectedKeys]);\n\n  var _useMemo = useMemo(function () {\n    return checkStrictly ? {\n      keyEntities: null\n    } : convertDataToEntities(data, {\n      externalGetKey: getRowKey,\n      childrenPropName: childrenColumnName\n    });\n  }, [data, getRowKey, checkStrictly, childrenColumnName]),\n      keyEntities = _useMemo.keyEntities; // Get flatten data\n\n\n  var flattedData = useMemo(function () {\n    return flattenData(pageData, childrenColumnName);\n  }, [pageData, childrenColumnName]); // Get all checkbox props\n\n  var checkboxPropsMap = useMemo(function () {\n    var map = new Map();\n    flattedData.forEach(function (record, index) {\n      var key = getRowKey(record, index);\n      var checkboxProps = (getCheckboxProps ? getCheckboxProps(record) : null) || {};\n      map.set(key, checkboxProps);\n\n      if (process.env.NODE_ENV !== 'production' && ('checked' in checkboxProps || 'defaultChecked' in checkboxProps)) {\n        devWarning(false, 'Table', 'Do not set `checked` or `defaultChecked` in `getCheckboxProps`. Please use `selectedRowKeys` instead.');\n      }\n    });\n    return map;\n  }, [flattedData, getRowKey, getCheckboxProps]);\n  var isCheckboxDisabled = useCallback(function (r) {\n    var _a;\n\n    return !!((_a = checkboxPropsMap.get(getRowKey(r))) === null || _a === void 0 ? void 0 : _a.disabled);\n  }, [checkboxPropsMap, getRowKey]);\n\n  var _useMemo2 = useMemo(function () {\n    if (checkStrictly) {\n      return [mergedSelectedKeys || [], []];\n    }\n\n    var _conductCheck = conductCheck(mergedSelectedKeys, true, keyEntities, isCheckboxDisabled),\n        checkedKeys = _conductCheck.checkedKeys,\n        halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n    return [checkedKeys || [], halfCheckedKeys];\n  }, [mergedSelectedKeys, checkStrictly, keyEntities, isCheckboxDisabled]),\n      _useMemo3 = _slicedToArray(_useMemo2, 2),\n      derivedSelectedKeys = _useMemo3[0],\n      derivedHalfSelectedKeys = _useMemo3[1];\n\n  var derivedSelectedKeySet = useMemo(function () {\n    var keys = selectionType === 'radio' ? derivedSelectedKeys.slice(0, 1) : derivedSelectedKeys;\n    return new Set(keys);\n  }, [derivedSelectedKeys, selectionType]);\n  var derivedHalfSelectedKeySet = useMemo(function () {\n    return selectionType === 'radio' ? new Set() : new Set(derivedHalfSelectedKeys);\n  }, [derivedHalfSelectedKeys, selectionType]); // Save last selected key to enable range selection\n\n  var _useState = useState(null),\n      _useState2 = _slicedToArray(_useState, 2),\n      lastSelectedKey = _useState2[0],\n      setLastSelectedKey = _useState2[1]; // Reset if rowSelection reset\n\n\n  React.useEffect(function () {\n    if (!rowSelection) {\n      setMergedSelectedKeys(EMPTY_LIST);\n    }\n  }, [!!rowSelection]);\n  var setSelectedKeys = useCallback(function (keys) {\n    var availableKeys;\n    var records;\n    updatePreserveRecordsCache(keys);\n\n    if (preserveSelectedRowKeys) {\n      availableKeys = keys;\n      records = keys.map(function (key) {\n        return preserveRecordsRef.current.get(key);\n      });\n    } else {\n      // Filter key which not exist in the `dataSource`\n      availableKeys = [];\n      records = [];\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n\n        if (record !== undefined) {\n          availableKeys.push(key);\n          records.push(record);\n        }\n      });\n    }\n\n    setMergedSelectedKeys(availableKeys);\n    onSelectionChange === null || onSelectionChange === void 0 ? void 0 : onSelectionChange(availableKeys, records);\n  }, [setMergedSelectedKeys, getRecordByKey, onSelectionChange, preserveSelectedRowKeys]); // ====================== Selections ======================\n  // Trigger single `onSelect` event\n\n  var triggerSingleSelection = useCallback(function (key, selected, keys, event) {\n    if (onSelect) {\n      var rows = keys.map(function (k) {\n        return getRecordByKey(k);\n      });\n      onSelect(getRecordByKey(key), selected, rows, event);\n    }\n\n    setSelectedKeys(keys);\n  }, [onSelect, getRecordByKey, setSelectedKeys]);\n  var mergedSelections = useMemo(function () {\n    if (!selections || hideSelectAll) {\n      return null;\n    }\n\n    var selectionList = selections === true ? [SELECTION_ALL, SELECTION_INVERT, SELECTION_NONE] : selections;\n    return selectionList.map(function (selection) {\n      if (selection === SELECTION_ALL) {\n        return {\n          key: 'all',\n          text: tableLocale.selectionAll,\n          onSelect: function onSelect() {\n            setSelectedKeys(data.map(function (record, index) {\n              return getRowKey(record, index);\n            }).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return !(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled) || derivedSelectedKeySet.has(key);\n            }));\n          }\n        };\n      }\n\n      if (selection === SELECTION_INVERT) {\n        return {\n          key: 'invert',\n          text: tableLocale.selectInvert,\n          onSelect: function onSelect() {\n            var keySet = new Set(derivedSelectedKeySet);\n            pageData.forEach(function (record, index) {\n              var key = getRowKey(record, index);\n              var checkProps = checkboxPropsMap.get(key);\n\n              if (!(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled)) {\n                if (keySet.has(key)) {\n                  keySet[\"delete\"](key);\n                } else {\n                  keySet.add(key);\n                }\n              }\n            });\n            var keys = Array.from(keySet);\n\n            if (onSelectInvert) {\n              devWarning(false, 'Table', '`onSelectInvert` will be removed in future. Please use `onChange` instead.');\n              onSelectInvert(keys);\n            }\n\n            setSelectedKeys(keys);\n          }\n        };\n      }\n\n      if (selection === SELECTION_NONE) {\n        return {\n          key: 'none',\n          text: tableLocale.selectNone,\n          onSelect: function onSelect() {\n            onSelectNone === null || onSelectNone === void 0 ? void 0 : onSelectNone();\n            setSelectedKeys(Array.from(derivedSelectedKeySet).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled;\n            }));\n          }\n        };\n      }\n\n      return selection;\n    });\n  }, [selections, derivedSelectedKeySet, pageData, getRowKey, onSelectInvert, setSelectedKeys]); // ======================= Columns ========================\n\n  var transformColumns = useCallback(function (columns) {\n    var _a; // >>>>>>>>>>> Skip if not exists `rowSelection`\n\n\n    if (!rowSelection) {\n      if (process.env.NODE_ENV !== 'production') {\n        devWarning(!columns.includes(SELECTION_COLUMN), 'Table', '`rowSelection` is not config but `SELECTION_COLUMN` exists in the `columns`.');\n      }\n\n      return columns.filter(function (col) {\n        return col !== SELECTION_COLUMN;\n      });\n    } // >>>>>>>>>>> Support selection\n\n\n    var cloneColumns = _toConsumableArray(columns);\n\n    var keySet = new Set(derivedSelectedKeySet); // Record key only need check with enabled\n\n    var recordKeys = flattedData.map(getRowKey).filter(function (key) {\n      return !checkboxPropsMap.get(key).disabled;\n    });\n    var checkedCurrentAll = recordKeys.every(function (key) {\n      return keySet.has(key);\n    });\n    var checkedCurrentSome = recordKeys.some(function (key) {\n      return keySet.has(key);\n    });\n\n    var onSelectAllChange = function onSelectAllChange() {\n      var changeKeys = [];\n\n      if (checkedCurrentAll) {\n        recordKeys.forEach(function (key) {\n          keySet[\"delete\"](key);\n          changeKeys.push(key);\n        });\n      } else {\n        recordKeys.forEach(function (key) {\n          if (!keySet.has(key)) {\n            keySet.add(key);\n            changeKeys.push(key);\n          }\n        });\n      }\n\n      var keys = Array.from(keySet);\n      onSelectAll === null || onSelectAll === void 0 ? void 0 : onSelectAll(!checkedCurrentAll, keys.map(function (k) {\n        return getRecordByKey(k);\n      }), changeKeys.map(function (k) {\n        return getRecordByKey(k);\n      }));\n      setSelectedKeys(keys);\n    }; // ===================== Render =====================\n    // Title Cell\n\n\n    var title;\n\n    if (selectionType !== 'radio') {\n      var customizeSelections;\n\n      if (mergedSelections) {\n        var menu = /*#__PURE__*/React.createElement(Menu, {\n          getPopupContainer: getPopupContainer,\n          items: mergedSelections.map(function (selection, index) {\n            var key = selection.key,\n                text = selection.text,\n                onSelectionClick = selection.onSelect;\n            return {\n              key: key || index,\n              onClick: function onClick() {\n                onSelectionClick === null || onSelectionClick === void 0 ? void 0 : onSelectionClick(recordKeys);\n              },\n              label: text\n            };\n          })\n        });\n        customizeSelections = /*#__PURE__*/React.createElement(\"div\", {\n          className: \"\".concat(prefixCls, \"-selection-extra\")\n        }, /*#__PURE__*/React.createElement(Dropdown, {\n          overlay: menu,\n          getPopupContainer: getPopupContainer\n        }, /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(DownOutlined, null))));\n      }\n\n      var allDisabledData = flattedData.map(function (record, index) {\n        var key = getRowKey(record, index);\n        var checkboxProps = checkboxPropsMap.get(key) || {};\n        return _extends({\n          checked: keySet.has(key)\n        }, checkboxProps);\n      }).filter(function (_ref2) {\n        var disabled = _ref2.disabled;\n        return disabled;\n      });\n      var allDisabled = !!allDisabledData.length && allDisabledData.length === flattedData.length;\n      var allDisabledAndChecked = allDisabled && allDisabledData.every(function (_ref3) {\n        var checked = _ref3.checked;\n        return checked;\n      });\n      var allDisabledSomeChecked = allDisabled && allDisabledData.some(function (_ref4) {\n        var checked = _ref4.checked;\n        return checked;\n      });\n      title = !hideSelectAll && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-selection\")\n      }, /*#__PURE__*/React.createElement(Checkbox, {\n        checked: !allDisabled ? !!flattedData.length && checkedCurrentAll : allDisabledAndChecked,\n        indeterminate: !allDisabled ? !checkedCurrentAll && checkedCurrentSome : !allDisabledAndChecked && allDisabledSomeChecked,\n        onChange: onSelectAllChange,\n        disabled: flattedData.length === 0 || allDisabled,\n        skipGroup: true\n      }), customizeSelections);\n    } // Body Cell\n\n\n    var renderCell;\n\n    if (selectionType === 'radio') {\n      renderCell = function renderCell(_, record, index) {\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        return {\n          node: /*#__PURE__*/React.createElement(Radio, _extends({}, checkboxPropsMap.get(key), {\n            checked: checked,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(event) {\n              if (!keySet.has(key)) {\n                triggerSingleSelection(key, true, [key], event.nativeEvent);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    } else {\n      renderCell = function renderCell(_, record, index) {\n        var _a;\n\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        var indeterminate = derivedHalfSelectedKeySet.has(key);\n        var checkboxProps = checkboxPropsMap.get(key);\n        var mergedIndeterminate;\n\n        if (expandType === 'nest') {\n          mergedIndeterminate = indeterminate;\n          devWarning(typeof (checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== 'boolean', 'Table', 'set `indeterminate` using `rowSelection.getCheckboxProps` is not allowed with tree structured dataSource.');\n        } else {\n          mergedIndeterminate = (_a = checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== null && _a !== void 0 ? _a : indeterminate;\n        } // Record checked\n\n\n        return {\n          node: /*#__PURE__*/React.createElement(Checkbox, _extends({}, checkboxProps, {\n            indeterminate: mergedIndeterminate,\n            checked: checked,\n            skipGroup: true,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(_ref5) {\n              var nativeEvent = _ref5.nativeEvent;\n              var shiftKey = nativeEvent.shiftKey;\n              var startIndex = -1;\n              var endIndex = -1; // Get range of this\n\n              if (shiftKey && checkStrictly) {\n                var pointKeys = new Set([lastSelectedKey, key]);\n                recordKeys.some(function (recordKey, recordIndex) {\n                  if (pointKeys.has(recordKey)) {\n                    if (startIndex === -1) {\n                      startIndex = recordIndex;\n                    } else {\n                      endIndex = recordIndex;\n                      return true;\n                    }\n                  }\n\n                  return false;\n                });\n              }\n\n              if (endIndex !== -1 && startIndex !== endIndex && checkStrictly) {\n                // Batch update selections\n                var rangeKeys = recordKeys.slice(startIndex, endIndex + 1);\n                var changedKeys = [];\n\n                if (checked) {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet[\"delete\"](recordKey);\n                    }\n                  });\n                } else {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (!keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet.add(recordKey);\n                    }\n                  });\n                }\n\n                var keys = Array.from(keySet);\n                onSelectMultiple === null || onSelectMultiple === void 0 ? void 0 : onSelectMultiple(!checked, keys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }), changedKeys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }));\n                setSelectedKeys(keys);\n              } else {\n                // Single record selected\n                var originCheckedKeys = derivedSelectedKeys;\n\n                if (checkStrictly) {\n                  var checkedKeys = checked ? arrDel(originCheckedKeys, key) : arrAdd(originCheckedKeys, key);\n                  triggerSingleSelection(key, !checked, checkedKeys, nativeEvent);\n                } else {\n                  // Always fill first\n                  var result = conductCheck([].concat(_toConsumableArray(originCheckedKeys), [key]), true, keyEntities, isCheckboxDisabled);\n                  var _checkedKeys = result.checkedKeys,\n                      halfCheckedKeys = result.halfCheckedKeys;\n                  var nextCheckedKeys = _checkedKeys; // If remove, we do it again to correction\n\n                  if (checked) {\n                    var tempKeySet = new Set(_checkedKeys);\n                    tempKeySet[\"delete\"](key);\n                    nextCheckedKeys = conductCheck(Array.from(tempKeySet), {\n                      checked: false,\n                      halfCheckedKeys: halfCheckedKeys\n                    }, keyEntities, isCheckboxDisabled).checkedKeys;\n                  }\n\n                  triggerSingleSelection(key, !checked, nextCheckedKeys, nativeEvent);\n                }\n              }\n\n              setLastSelectedKey(key);\n            }\n          })),\n          checked: checked\n        };\n      };\n    }\n\n    var renderSelectionCell = function renderSelectionCell(_, record, index) {\n      var _renderCell = renderCell(_, record, index),\n          node = _renderCell.node,\n          checked = _renderCell.checked;\n\n      if (customizeRenderCell) {\n        return customizeRenderCell(checked, record, index, node);\n      }\n\n      return node;\n    }; // Insert selection column if not exist\n\n\n    if (!cloneColumns.includes(SELECTION_COLUMN)) {\n      // Always after expand icon\n      if (cloneColumns.findIndex(function (col) {\n        var _a;\n\n        return ((_a = col[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN';\n      }) === 0) {\n        var _cloneColumns = cloneColumns,\n            _cloneColumns2 = _toArray(_cloneColumns),\n            expandColumn = _cloneColumns2[0],\n            restColumns = _cloneColumns2.slice(1);\n\n        cloneColumns = [expandColumn, SELECTION_COLUMN].concat(_toConsumableArray(restColumns));\n      } else {\n        // Normal insert at first column\n        cloneColumns = [SELECTION_COLUMN].concat(_toConsumableArray(cloneColumns));\n      }\n    } // Deduplicate selection column\n\n\n    var selectionColumnIndex = cloneColumns.indexOf(SELECTION_COLUMN);\n\n    if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (col) {\n      return col === SELECTION_COLUMN;\n    }).length > 1) {\n      devWarning(false, 'Table', 'Multiple `SELECTION_COLUMN` exist in `columns`.');\n    }\n\n    cloneColumns = cloneColumns.filter(function (column, index) {\n      return column !== SELECTION_COLUMN || index === selectionColumnIndex;\n    }); // Fixed column logic\n\n    var prevCol = cloneColumns[selectionColumnIndex - 1];\n    var nextCol = cloneColumns[selectionColumnIndex + 1];\n    var mergedFixed = fixed;\n\n    if (mergedFixed === undefined) {\n      if ((nextCol === null || nextCol === void 0 ? void 0 : nextCol.fixed) !== undefined) {\n        mergedFixed = nextCol.fixed;\n      } else if ((prevCol === null || prevCol === void 0 ? void 0 : prevCol.fixed) !== undefined) {\n        mergedFixed = prevCol.fixed;\n      }\n    }\n\n    if (mergedFixed && prevCol && ((_a = prevCol[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN' && prevCol.fixed === undefined) {\n      prevCol.fixed = mergedFixed;\n    } // Replace with real selection column\n\n\n    var selectionColumn = _defineProperty({\n      fixed: mergedFixed,\n      width: selectionColWidth,\n      className: \"\".concat(prefixCls, \"-selection-column\"),\n      title: rowSelection.columnTitle || title,\n      render: renderSelectionCell\n    }, INTERNAL_COL_DEFINE, {\n      className: \"\".concat(prefixCls, \"-selection-col\")\n    });\n\n    return cloneColumns.map(function (col) {\n      return col === SELECTION_COLUMN ? selectionColumn : col;\n    });\n  }, [getRowKey, flattedData, rowSelection, derivedSelectedKeys, derivedSelectedKeySet, derivedHalfSelectedKeySet, selectionColWidth, mergedSelections, expandType, lastSelectedKey, checkboxPropsMap, onSelectMultiple, triggerSingleSelection, isCheckboxDisabled]);\n  return [transformColumns, derivedSelectedKeySet];\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACtD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,MAAM,EAAEC,MAAM,QAAQ,iBAAiB;AAChD,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,wBAAwB,CAAC,CAAC;;AAEjD,OAAO,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AAChC,OAAO,IAAIC,aAAa,GAAG,YAAY;AACvC,OAAO,IAAIC,gBAAgB,GAAG,eAAe;AAC7C,OAAO,IAAIC,cAAc,GAAG,aAAa;AACzC,IAAIC,UAAU,GAAG,EAAE;AAEnB,SAASC,WAAWA,CAACC,IAAI,EAAEC,kBAAkB,EAAE;EAC7C,IAAIC,IAAI,GAAG,EAAE;EACb,CAACF,IAAI,IAAI,EAAE,EAAEG,OAAO,CAAC,UAAUC,MAAM,EAAE;IACrCF,IAAI,CAACG,IAAI,CAACD,MAAM,CAAC;IAEjB,IAAIA,MAAM,IAAI3B,OAAO,CAAC2B,MAAM,CAAC,KAAK,QAAQ,IAAIH,kBAAkB,IAAIG,MAAM,EAAE;MAC1EF,IAAI,GAAG,EAAE,CAACI,MAAM,CAAC9B,kBAAkB,CAAC0B,IAAI,CAAC,EAAE1B,kBAAkB,CAACuB,WAAW,CAACK,MAAM,CAACH,kBAAkB,CAAC,EAAEA,kBAAkB,CAAC,CAAC,CAAC;IAC7H;EACF,CAAC,CAAC;EACF,OAAOC,IAAI;AACb;AAEA,eAAe,SAASK,YAAYA,CAACC,YAAY,EAAEC,MAAM,EAAE;EACzD,IAAIC,IAAI,GAAGF,YAAY,IAAI,CAAC,CAAC;IACzBG,uBAAuB,GAAGD,IAAI,CAACC,uBAAuB;IACtDC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,sBAAsB,GAAGH,IAAI,CAACG,sBAAsB;IACpDC,gBAAgB,GAAGJ,IAAI,CAACI,gBAAgB;IACxCC,iBAAiB,GAAGL,IAAI,CAACM,QAAQ;IACjCC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,WAAW,GAAGR,IAAI,CAACQ,WAAW;IAC9BC,cAAc,GAAGT,IAAI,CAACS,cAAc;IACpCC,YAAY,GAAGV,IAAI,CAACU,YAAY;IAChCC,gBAAgB,GAAGX,IAAI,CAACW,gBAAgB;IACxCC,iBAAiB,GAAGZ,IAAI,CAACa,WAAW;IACpCC,aAAa,GAAGd,IAAI,CAACe,IAAI;IACzBC,UAAU,GAAGhB,IAAI,CAACgB,UAAU;IAC5BC,KAAK,GAAGjB,IAAI,CAACiB,KAAK;IAClBC,mBAAmB,GAAGlB,IAAI,CAACmB,UAAU;IACrCC,aAAa,GAAGpB,IAAI,CAACoB,aAAa;IAClCC,kBAAkB,GAAGrB,IAAI,CAACsB,aAAa;IACvCA,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,kBAAkB;EAE7E,IAAIE,SAAS,GAAGxB,MAAM,CAACwB,SAAS;IAC5BjC,IAAI,GAAGS,MAAM,CAACT,IAAI;IAClBkC,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ;IAC1BC,cAAc,GAAG1B,MAAM,CAAC0B,cAAc;IACtCC,SAAS,GAAG3B,MAAM,CAAC2B,SAAS;IAC5BC,UAAU,GAAG5B,MAAM,CAAC4B,UAAU;IAC9BpC,kBAAkB,GAAGQ,MAAM,CAACR,kBAAkB;IAC9CqC,WAAW,GAAG7B,MAAM,CAAC8B,MAAM;IAC3BC,iBAAiB,GAAG/B,MAAM,CAAC+B,iBAAiB,CAAC,CAAC;;EAElD,IAAIC,eAAe,GAAGrD,cAAc,CAACwB,eAAe,IAAIC,sBAAsB,IAAIf,UAAU,EAAE;MAC5F4C,KAAK,EAAE9B;IACT,CAAC,CAAC;IACE+B,gBAAgB,GAAGpE,cAAc,CAACkE,eAAe,EAAE,CAAC,CAAC;IACrDG,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGjD,IAAIG,kBAAkB,GAAGpE,KAAK,CAACqE,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAChD,IAAIC,0BAA0B,GAAGrE,WAAW,CAAC,UAAUsE,IAAI,EAAE;IAC3D,IAAIvC,uBAAuB,EAAE;MAC3B,IAAIwC,QAAQ,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;;MAE1BE,IAAI,CAAC/C,OAAO,CAAC,UAAUiD,GAAG,EAAE;QAC1B,IAAIhD,MAAM,GAAG+B,cAAc,CAACiB,GAAG,CAAC;QAEhC,IAAI,CAAChD,MAAM,IAAI0C,kBAAkB,CAACO,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;UAClDhD,MAAM,GAAG0C,kBAAkB,CAACO,OAAO,CAACE,GAAG,CAACH,GAAG,CAAC;QAC9C;QAEAD,QAAQ,CAACK,GAAG,CAACJ,GAAG,EAAEhD,MAAM,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC;;MAEJ0C,kBAAkB,CAACO,OAAO,GAAGF,QAAQ;IACvC;EACF,CAAC,EAAE,CAAChB,cAAc,EAAExB,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAE/CjC,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1BR,0BAA0B,CAACL,kBAAkB,CAAC;EAChD,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,IAAIc,QAAQ,GAAG7E,OAAO,CAAC,YAAY;MACjC,OAAOmD,aAAa,GAAG;QACrB2B,WAAW,EAAE;MACf,CAAC,GAAG5E,qBAAqB,CAACiB,IAAI,EAAE;QAC9B4D,cAAc,EAAExB,SAAS;QACzByB,gBAAgB,EAAE5D;MACpB,CAAC,CAAC;IACJ,CAAC,EAAE,CAACD,IAAI,EAAEoC,SAAS,EAAEJ,aAAa,EAAE/B,kBAAkB,CAAC,CAAC;IACpD0D,WAAW,GAAGD,QAAQ,CAACC,WAAW,CAAC,CAAC;;EAGxC,IAAIG,WAAW,GAAGjF,OAAO,CAAC,YAAY;IACpC,OAAOkB,WAAW,CAACmC,QAAQ,EAAEjC,kBAAkB,CAAC;EAClD,CAAC,EAAE,CAACiC,QAAQ,EAAEjC,kBAAkB,CAAC,CAAC,CAAC,CAAC;;EAEpC,IAAI8D,gBAAgB,GAAGlF,OAAO,CAAC,YAAY;IACzC,IAAImF,GAAG,GAAG,IAAIhB,GAAG,CAAC,CAAC;IACnBc,WAAW,CAAC3D,OAAO,CAAC,UAAUC,MAAM,EAAE6D,KAAK,EAAE;MAC3C,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;MAClC,IAAIC,aAAa,GAAG,CAACpD,gBAAgB,GAAGA,gBAAgB,CAACV,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;MAC9E4D,GAAG,CAACR,GAAG,CAACJ,GAAG,EAAEc,aAAa,CAAC;MAE3B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAK,SAAS,IAAIH,aAAa,IAAI,gBAAgB,IAAIA,aAAa,CAAC,EAAE;QAC9GzE,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,uGAAuG,CAAC;MACrI;IACF,CAAC,CAAC;IACF,OAAOuE,GAAG;EACZ,CAAC,EAAE,CAACF,WAAW,EAAE1B,SAAS,EAAEtB,gBAAgB,CAAC,CAAC;EAC9C,IAAIwD,kBAAkB,GAAG1F,WAAW,CAAC,UAAU2F,CAAC,EAAE;IAChD,IAAIC,EAAE;IAEN,OAAO,CAAC,EAAE,CAACA,EAAE,GAAGT,gBAAgB,CAACR,GAAG,CAACnB,SAAS,CAACmC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,CAAC;EACvG,CAAC,EAAE,CAACV,gBAAgB,EAAE3B,SAAS,CAAC,CAAC;EAEjC,IAAIsC,SAAS,GAAG7F,OAAO,CAAC,YAAY;MAClC,IAAImD,aAAa,EAAE;QACjB,OAAO,CAACY,kBAAkB,IAAI,EAAE,EAAE,EAAE,CAAC;MACvC;MAEA,IAAI+B,aAAa,GAAG3F,YAAY,CAAC4D,kBAAkB,EAAE,IAAI,EAAEe,WAAW,EAAEW,kBAAkB,CAAC;QACvFM,WAAW,GAAGD,aAAa,CAACC,WAAW;QACvCC,eAAe,GAAGF,aAAa,CAACE,eAAe;MAEnD,OAAO,CAACD,WAAW,IAAI,EAAE,EAAEC,eAAe,CAAC;IAC7C,CAAC,EAAE,CAACjC,kBAAkB,EAAEZ,aAAa,EAAE2B,WAAW,EAAEW,kBAAkB,CAAC,CAAC;IACpEQ,SAAS,GAAGvG,cAAc,CAACmG,SAAS,EAAE,CAAC,CAAC;IACxCK,mBAAmB,GAAGD,SAAS,CAAC,CAAC,CAAC;IAClCE,uBAAuB,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE1C,IAAIG,qBAAqB,GAAGpG,OAAO,CAAC,YAAY;IAC9C,IAAIqE,IAAI,GAAG1B,aAAa,KAAK,OAAO,GAAGuD,mBAAmB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGH,mBAAmB;IAC5F,OAAO,IAAII,GAAG,CAACjC,IAAI,CAAC;EACtB,CAAC,EAAE,CAAC6B,mBAAmB,EAAEvD,aAAa,CAAC,CAAC;EACxC,IAAI4D,yBAAyB,GAAGvG,OAAO,CAAC,YAAY;IAClD,OAAO2C,aAAa,KAAK,OAAO,GAAG,IAAI2D,GAAG,CAAC,CAAC,GAAG,IAAIA,GAAG,CAACH,uBAAuB,CAAC;EACjF,CAAC,EAAE,CAACA,uBAAuB,EAAExD,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE9C,IAAI6D,SAAS,GAAG1G,QAAQ,CAAC,IAAI,CAAC;IAC1B2G,UAAU,GAAG/G,cAAc,CAAC8G,SAAS,EAAE,CAAC,CAAC;IACzCE,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGxC5G,KAAK,CAAC+E,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACjD,YAAY,EAAE;MACjBqC,qBAAqB,CAAC/C,UAAU,CAAC;IACnC;EACF,CAAC,EAAE,CAAC,CAAC,CAACU,YAAY,CAAC,CAAC;EACpB,IAAIiF,eAAe,GAAG7G,WAAW,CAAC,UAAUsE,IAAI,EAAE;IAChD,IAAIwC,aAAa;IACjB,IAAIC,OAAO;IACX1C,0BAA0B,CAACC,IAAI,CAAC;IAEhC,IAAIvC,uBAAuB,EAAE;MAC3B+E,aAAa,GAAGxC,IAAI;MACpByC,OAAO,GAAGzC,IAAI,CAACc,GAAG,CAAC,UAAUZ,GAAG,EAAE;QAChC,OAAON,kBAAkB,CAACO,OAAO,CAACE,GAAG,CAACH,GAAG,CAAC;MAC5C,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAsC,aAAa,GAAG,EAAE;MAClBC,OAAO,GAAG,EAAE;MACZzC,IAAI,CAAC/C,OAAO,CAAC,UAAUiD,GAAG,EAAE;QAC1B,IAAIhD,MAAM,GAAG+B,cAAc,CAACiB,GAAG,CAAC;QAEhC,IAAIhD,MAAM,KAAKwF,SAAS,EAAE;UACxBF,aAAa,CAACrF,IAAI,CAAC+C,GAAG,CAAC;UACvBuC,OAAO,CAACtF,IAAI,CAACD,MAAM,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;IAEAyC,qBAAqB,CAAC6C,aAAa,CAAC;IACpC3E,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC2E,aAAa,EAAEC,OAAO,CAAC;EACjH,CAAC,EAAE,CAAC9C,qBAAqB,EAAEV,cAAc,EAAEpB,iBAAiB,EAAEJ,uBAAuB,CAAC,CAAC,CAAC,CAAC;EACzF;;EAEA,IAAIkF,sBAAsB,GAAGjH,WAAW,CAAC,UAAUwE,GAAG,EAAE0C,QAAQ,EAAE5C,IAAI,EAAE6C,KAAK,EAAE;IAC7E,IAAI9E,QAAQ,EAAE;MACZ,IAAI+E,IAAI,GAAG9C,IAAI,CAACc,GAAG,CAAC,UAAUiC,CAAC,EAAE;QAC/B,OAAO9D,cAAc,CAAC8D,CAAC,CAAC;MAC1B,CAAC,CAAC;MACFhF,QAAQ,CAACkB,cAAc,CAACiB,GAAG,CAAC,EAAE0C,QAAQ,EAAEE,IAAI,EAAED,KAAK,CAAC;IACtD;IAEAN,eAAe,CAACvC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACjC,QAAQ,EAAEkB,cAAc,EAAEsD,eAAe,CAAC,CAAC;EAC/C,IAAIS,gBAAgB,GAAGrH,OAAO,CAAC,YAAY;IACzC,IAAI,CAAC6C,UAAU,IAAII,aAAa,EAAE;MAChC,OAAO,IAAI;IACb;IAEA,IAAIqE,aAAa,GAAGzE,UAAU,KAAK,IAAI,GAAG,CAAC/B,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,CAAC,GAAG6B,UAAU;IACxG,OAAOyE,aAAa,CAACnC,GAAG,CAAC,UAAUoC,SAAS,EAAE;MAC5C,IAAIA,SAAS,KAAKzG,aAAa,EAAE;QAC/B,OAAO;UACLyD,GAAG,EAAE,KAAK;UACViD,IAAI,EAAE/D,WAAW,CAACgE,YAAY;UAC9BrF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5BwE,eAAe,CAACzF,IAAI,CAACgE,GAAG,CAAC,UAAU5D,MAAM,EAAE6D,KAAK,EAAE;cAChD,OAAO7B,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;YACjC,CAAC,CAAC,CAACsC,MAAM,CAAC,UAAUnD,GAAG,EAAE;cACvB,IAAIoD,UAAU,GAAGzC,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAC1C,OAAO,EAAEoD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC/B,QAAQ,CAAC,IAAIQ,qBAAqB,CAAC3B,GAAG,CAACF,GAAG,CAAC;YACzH,CAAC,CAAC,CAAC;UACL;QACF,CAAC;MACH;MAEA,IAAIgD,SAAS,KAAKxG,gBAAgB,EAAE;QAClC,OAAO;UACLwD,GAAG,EAAE,QAAQ;UACbiD,IAAI,EAAE/D,WAAW,CAACmE,YAAY;UAC9BxF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5B,IAAIyF,MAAM,GAAG,IAAIvB,GAAG,CAACF,qBAAqB,CAAC;YAC3C/C,QAAQ,CAAC/B,OAAO,CAAC,UAAUC,MAAM,EAAE6D,KAAK,EAAE;cACxC,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;cAClC,IAAIuC,UAAU,GAAGzC,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAE1C,IAAI,EAAEoD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC/B,QAAQ,CAAC,EAAE;gBAClF,IAAIiC,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC,EAAE;kBACnBsD,MAAM,CAAC,QAAQ,CAAC,CAACtD,GAAG,CAAC;gBACvB,CAAC,MAAM;kBACLsD,MAAM,CAACC,GAAG,CAACvD,GAAG,CAAC;gBACjB;cACF;YACF,CAAC,CAAC;YACF,IAAIF,IAAI,GAAG0D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;YAE7B,IAAIvF,cAAc,EAAE;cAClB1B,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,4EAA4E,CAAC;cACxG0B,cAAc,CAAC+B,IAAI,CAAC;YACtB;YAEAuC,eAAe,CAACvC,IAAI,CAAC;UACvB;QACF,CAAC;MACH;MAEA,IAAIkD,SAAS,KAAKvG,cAAc,EAAE;QAChC,OAAO;UACLuD,GAAG,EAAE,MAAM;UACXiD,IAAI,EAAE/D,WAAW,CAACwE,UAAU;UAC5B7F,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5BG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC;YAC1EqE,eAAe,CAACmB,KAAK,CAACC,IAAI,CAAC5B,qBAAqB,CAAC,CAACsB,MAAM,CAAC,UAAUnD,GAAG,EAAE;cACtE,IAAIoD,UAAU,GAAGzC,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAC1C,OAAOoD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC/B,QAAQ;YACpF,CAAC,CAAC,CAAC;UACL;QACF,CAAC;MACH;MAEA,OAAO2B,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1E,UAAU,EAAEuD,qBAAqB,EAAE/C,QAAQ,EAAEE,SAAS,EAAEjB,cAAc,EAAEsE,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE/F,IAAIsB,gBAAgB,GAAGnI,WAAW,CAAC,UAAUoI,OAAO,EAAE;IACpD,IAAIxC,EAAE,CAAC,CAAC;;IAGR,IAAI,CAAChE,YAAY,EAAE;MACjB,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC5E,UAAU,CAAC,CAACuH,OAAO,CAACC,QAAQ,CAACvH,gBAAgB,CAAC,EAAE,OAAO,EAAE,8EAA8E,CAAC;MAC1I;MAEA,OAAOsH,OAAO,CAACT,MAAM,CAAC,UAAUW,GAAG,EAAE;QACnC,OAAOA,GAAG,KAAKxH,gBAAgB;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAIyH,YAAY,GAAG3I,kBAAkB,CAACwI,OAAO,CAAC;IAE9C,IAAIN,MAAM,GAAG,IAAIvB,GAAG,CAACF,qBAAqB,CAAC,CAAC,CAAC;;IAE7C,IAAImC,UAAU,GAAGtD,WAAW,CAACE,GAAG,CAAC5B,SAAS,CAAC,CAACmE,MAAM,CAAC,UAAUnD,GAAG,EAAE;MAChE,OAAO,CAACW,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,CAACqB,QAAQ;IAC5C,CAAC,CAAC;IACF,IAAI4C,iBAAiB,GAAGD,UAAU,CAACE,KAAK,CAAC,UAAUlE,GAAG,EAAE;MACtD,OAAOsD,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,IAAImE,kBAAkB,GAAGH,UAAU,CAACI,IAAI,CAAC,UAAUpE,GAAG,EAAE;MACtD,OAAOsD,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC;IACxB,CAAC,CAAC;IAEF,IAAIqE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;MACnD,IAAIC,UAAU,GAAG,EAAE;MAEnB,IAAIL,iBAAiB,EAAE;QACrBD,UAAU,CAACjH,OAAO,CAAC,UAAUiD,GAAG,EAAE;UAChCsD,MAAM,CAAC,QAAQ,CAAC,CAACtD,GAAG,CAAC;UACrBsE,UAAU,CAACrH,IAAI,CAAC+C,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLgE,UAAU,CAACjH,OAAO,CAAC,UAAUiD,GAAG,EAAE;UAChC,IAAI,CAACsD,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC,EAAE;YACpBsD,MAAM,CAACC,GAAG,CAACvD,GAAG,CAAC;YACfsE,UAAU,CAACrH,IAAI,CAAC+C,GAAG,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;MAEA,IAAIF,IAAI,GAAG0D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;MAC7BxF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAACmG,iBAAiB,EAAEnE,IAAI,CAACc,GAAG,CAAC,UAAUiC,CAAC,EAAE;QAC9G,OAAO9D,cAAc,CAAC8D,CAAC,CAAC;MAC1B,CAAC,CAAC,EAAEyB,UAAU,CAAC1D,GAAG,CAAC,UAAUiC,CAAC,EAAE;QAC9B,OAAO9D,cAAc,CAAC8D,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC;MACHR,eAAe,CAACvC,IAAI,CAAC;IACvB,CAAC,CAAC,CAAC;IACH;;IAGA,IAAIyE,KAAK;IAET,IAAInG,aAAa,KAAK,OAAO,EAAE;MAC7B,IAAIoG,mBAAmB;MAEvB,IAAI1B,gBAAgB,EAAE;QACpB,IAAI2B,IAAI,GAAG,aAAanJ,KAAK,CAACoJ,aAAa,CAACvI,IAAI,EAAE;UAChDiD,iBAAiB,EAAEA,iBAAiB;UACpCuF,KAAK,EAAE7B,gBAAgB,CAAClC,GAAG,CAAC,UAAUoC,SAAS,EAAEnC,KAAK,EAAE;YACtD,IAAIb,GAAG,GAAGgD,SAAS,CAAChD,GAAG;cACnBiD,IAAI,GAAGD,SAAS,CAACC,IAAI;cACrB2B,gBAAgB,GAAG5B,SAAS,CAACnF,QAAQ;YACzC,OAAO;cACLmC,GAAG,EAAEA,GAAG,IAAIa,KAAK;cACjBgE,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;gBAC1BD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACZ,UAAU,CAAC;cAClG,CAAC;cACDc,KAAK,EAAE7B;YACT,CAAC;UACH,CAAC;QACH,CAAC,CAAC;QACFuB,mBAAmB,GAAG,aAAalJ,KAAK,CAACoJ,aAAa,CAAC,KAAK,EAAE;UAC5DK,SAAS,EAAE,EAAE,CAAC7H,MAAM,CAAC2B,SAAS,EAAE,kBAAkB;QACpD,CAAC,EAAE,aAAavD,KAAK,CAACoJ,aAAa,CAACxI,QAAQ,EAAE;UAC5C8I,OAAO,EAAEP,IAAI;UACbrF,iBAAiB,EAAEA;QACrB,CAAC,EAAE,aAAa9D,KAAK,CAACoJ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAapJ,KAAK,CAACoJ,aAAa,CAAChJ,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC3G;MAEA,IAAIuJ,eAAe,GAAGvE,WAAW,CAACE,GAAG,CAAC,UAAU5D,MAAM,EAAE6D,KAAK,EAAE;QAC7D,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAIC,aAAa,GAAGH,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,OAAO9E,QAAQ,CAAC;UACdgK,OAAO,EAAE5B,MAAM,CAACpD,GAAG,CAACF,GAAG;QACzB,CAAC,EAAEc,aAAa,CAAC;MACnB,CAAC,CAAC,CAACqC,MAAM,CAAC,UAAUgC,KAAK,EAAE;QACzB,IAAI9D,QAAQ,GAAG8D,KAAK,CAAC9D,QAAQ;QAC7B,OAAOA,QAAQ;MACjB,CAAC,CAAC;MACF,IAAI+D,WAAW,GAAG,CAAC,CAACH,eAAe,CAACI,MAAM,IAAIJ,eAAe,CAACI,MAAM,KAAK3E,WAAW,CAAC2E,MAAM;MAC3F,IAAIC,qBAAqB,GAAGF,WAAW,IAAIH,eAAe,CAACf,KAAK,CAAC,UAAUqB,KAAK,EAAE;QAChF,IAAIL,OAAO,GAAGK,KAAK,CAACL,OAAO;QAC3B,OAAOA,OAAO;MAChB,CAAC,CAAC;MACF,IAAIM,sBAAsB,GAAGJ,WAAW,IAAIH,eAAe,CAACb,IAAI,CAAC,UAAUqB,KAAK,EAAE;QAChF,IAAIP,OAAO,GAAGO,KAAK,CAACP,OAAO;QAC3B,OAAOA,OAAO;MAChB,CAAC,CAAC;MACFX,KAAK,GAAG,CAAC7F,aAAa,IAAI,aAAapD,KAAK,CAACoJ,aAAa,CAAC,KAAK,EAAE;QAChEK,SAAS,EAAE,EAAE,CAAC7H,MAAM,CAAC2B,SAAS,EAAE,YAAY;MAC9C,CAAC,EAAE,aAAavD,KAAK,CAACoJ,aAAa,CAACzI,QAAQ,EAAE;QAC5CiJ,OAAO,EAAE,CAACE,WAAW,GAAG,CAAC,CAAC1E,WAAW,CAAC2E,MAAM,IAAIpB,iBAAiB,GAAGqB,qBAAqB;QACzFI,aAAa,EAAE,CAACN,WAAW,GAAG,CAACnB,iBAAiB,IAAIE,kBAAkB,GAAG,CAACmB,qBAAqB,IAAIE,sBAAsB;QACzH5H,QAAQ,EAAEyG,iBAAiB;QAC3BhD,QAAQ,EAAEX,WAAW,CAAC2E,MAAM,KAAK,CAAC,IAAID,WAAW;QACjDO,SAAS,EAAE;MACb,CAAC,CAAC,EAAEnB,mBAAmB,CAAC;IAC1B,CAAC,CAAC;;IAGF,IAAI/F,UAAU;IAEd,IAAIL,aAAa,KAAK,OAAO,EAAE;MAC7BK,UAAU,GAAG,SAASA,UAAUA,CAACmH,CAAC,EAAE5I,MAAM,EAAE6D,KAAK,EAAE;QACjD,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAIqE,OAAO,GAAG5B,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC;QAC7B,OAAO;UACL6F,IAAI,EAAE,aAAavK,KAAK,CAACoJ,aAAa,CAACtI,KAAK,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEyF,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,EAAE;YACpFkF,OAAO,EAAEA,OAAO;YAChBL,OAAO,EAAE,SAASA,OAAOA,CAACiB,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B,CAAC;YACDnI,QAAQ,EAAE,SAASA,QAAQA,CAAC+E,KAAK,EAAE;cACjC,IAAI,CAACW,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC,EAAE;gBACpByC,sBAAsB,CAACzC,GAAG,EAAE,IAAI,EAAE,CAACA,GAAG,CAAC,EAAE2C,KAAK,CAACqD,WAAW,CAAC;cAC7D;YACF;UACF,CAAC,CAAC,CAAC;UACHd,OAAO,EAAEA;QACX,CAAC;MACH,CAAC;IACH,CAAC,MAAM;MACLzG,UAAU,GAAG,SAASA,UAAUA,CAACmH,CAAC,EAAE5I,MAAM,EAAE6D,KAAK,EAAE;QACjD,IAAIO,EAAE;QAEN,IAAIpB,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAIqE,OAAO,GAAG5B,MAAM,CAACpD,GAAG,CAACF,GAAG,CAAC;QAC7B,IAAI0F,aAAa,GAAG1D,yBAAyB,CAAC9B,GAAG,CAACF,GAAG,CAAC;QACtD,IAAIc,aAAa,GAAGH,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;QAC7C,IAAIiG,mBAAmB;QAEvB,IAAIhH,UAAU,KAAK,MAAM,EAAE;UACzBgH,mBAAmB,GAAGP,aAAa;UACnCrJ,UAAU,CAAC,QAAQyE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4E,aAAa,CAAC,KAAK,SAAS,EAAE,OAAO,EAAE,2GAA2G,CAAC;QACpP,CAAC,MAAM;UACLO,mBAAmB,GAAG,CAAC7E,EAAE,GAAGN,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4E,aAAa,MAAM,IAAI,IAAItE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGsE,aAAa;QACvK,CAAC,CAAC;;QAGF,OAAO;UACLG,IAAI,EAAE,aAAavK,KAAK,CAACoJ,aAAa,CAACzI,QAAQ,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,EAAE;YAC3E4E,aAAa,EAAEO,mBAAmB;YAClCf,OAAO,EAAEA,OAAO;YAChBS,SAAS,EAAE,IAAI;YACfd,OAAO,EAAE,SAASA,OAAOA,CAACiB,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B,CAAC;YACDnI,QAAQ,EAAE,SAASA,QAAQA,CAACsI,KAAK,EAAE;cACjC,IAAIF,WAAW,GAAGE,KAAK,CAACF,WAAW;cACnC,IAAIG,QAAQ,GAAGH,WAAW,CAACG,QAAQ;cACnC,IAAIC,UAAU,GAAG,CAAC,CAAC;cACnB,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;;cAEnB,IAAIF,QAAQ,IAAIvH,aAAa,EAAE;gBAC7B,IAAI0H,SAAS,GAAG,IAAIvE,GAAG,CAAC,CAACI,eAAe,EAAEnC,GAAG,CAAC,CAAC;gBAC/CgE,UAAU,CAACI,IAAI,CAAC,UAAUmC,SAAS,EAAEC,WAAW,EAAE;kBAChD,IAAIF,SAAS,CAACpG,GAAG,CAACqG,SAAS,CAAC,EAAE;oBAC5B,IAAIH,UAAU,KAAK,CAAC,CAAC,EAAE;sBACrBA,UAAU,GAAGI,WAAW;oBAC1B,CAAC,MAAM;sBACLH,QAAQ,GAAGG,WAAW;sBACtB,OAAO,IAAI;oBACb;kBACF;kBAEA,OAAO,KAAK;gBACd,CAAC,CAAC;cACJ;cAEA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAID,UAAU,KAAKC,QAAQ,IAAIzH,aAAa,EAAE;gBAC/D;gBACA,IAAI6H,SAAS,GAAGzC,UAAU,CAAClC,KAAK,CAACsE,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC;gBAC1D,IAAIK,WAAW,GAAG,EAAE;gBAEpB,IAAIxB,OAAO,EAAE;kBACXuB,SAAS,CAAC1J,OAAO,CAAC,UAAUwJ,SAAS,EAAE;oBACrC,IAAIjD,MAAM,CAACpD,GAAG,CAACqG,SAAS,CAAC,EAAE;sBACzBG,WAAW,CAACzJ,IAAI,CAACsJ,SAAS,CAAC;sBAC3BjD,MAAM,CAAC,QAAQ,CAAC,CAACiD,SAAS,CAAC;oBAC7B;kBACF,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACLE,SAAS,CAAC1J,OAAO,CAAC,UAAUwJ,SAAS,EAAE;oBACrC,IAAI,CAACjD,MAAM,CAACpD,GAAG,CAACqG,SAAS,CAAC,EAAE;sBAC1BG,WAAW,CAACzJ,IAAI,CAACsJ,SAAS,CAAC;sBAC3BjD,MAAM,CAACC,GAAG,CAACgD,SAAS,CAAC;oBACvB;kBACF,CAAC,CAAC;gBACJ;gBAEA,IAAIzG,IAAI,GAAG0D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;gBAC7BrF,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC,CAACiH,OAAO,EAAEpF,IAAI,CAACc,GAAG,CAAC,UAAU2F,SAAS,EAAE;kBAC3H,OAAOxH,cAAc,CAACwH,SAAS,CAAC;gBAClC,CAAC,CAAC,EAAEG,WAAW,CAAC9F,GAAG,CAAC,UAAU2F,SAAS,EAAE;kBACvC,OAAOxH,cAAc,CAACwH,SAAS,CAAC;gBAClC,CAAC,CAAC,CAAC;gBACHlE,eAAe,CAACvC,IAAI,CAAC;cACvB,CAAC,MAAM;gBACL;gBACA,IAAI6G,iBAAiB,GAAGhF,mBAAmB;gBAE3C,IAAI/C,aAAa,EAAE;kBACjB,IAAI4C,WAAW,GAAG0D,OAAO,GAAGpJ,MAAM,CAAC6K,iBAAiB,EAAE3G,GAAG,CAAC,GAAGnE,MAAM,CAAC8K,iBAAiB,EAAE3G,GAAG,CAAC;kBAC3FyC,sBAAsB,CAACzC,GAAG,EAAE,CAACkF,OAAO,EAAE1D,WAAW,EAAEwE,WAAW,CAAC;gBACjE,CAAC,MAAM;kBACL;kBACA,IAAIY,MAAM,GAAGhL,YAAY,CAAC,EAAE,CAACsB,MAAM,CAAC9B,kBAAkB,CAACuL,iBAAiB,CAAC,EAAE,CAAC3G,GAAG,CAAC,CAAC,EAAE,IAAI,EAAEO,WAAW,EAAEW,kBAAkB,CAAC;kBACzH,IAAI2F,YAAY,GAAGD,MAAM,CAACpF,WAAW;oBACjCC,eAAe,GAAGmF,MAAM,CAACnF,eAAe;kBAC5C,IAAIqF,eAAe,GAAGD,YAAY,CAAC,CAAC;;kBAEpC,IAAI3B,OAAO,EAAE;oBACX,IAAI6B,UAAU,GAAG,IAAIhF,GAAG,CAAC8E,YAAY,CAAC;oBACtCE,UAAU,CAAC,QAAQ,CAAC,CAAC/G,GAAG,CAAC;oBACzB8G,eAAe,GAAGlL,YAAY,CAAC4H,KAAK,CAACC,IAAI,CAACsD,UAAU,CAAC,EAAE;sBACrD7B,OAAO,EAAE,KAAK;sBACdzD,eAAe,EAAEA;oBACnB,CAAC,EAAElB,WAAW,EAAEW,kBAAkB,CAAC,CAACM,WAAW;kBACjD;kBAEAiB,sBAAsB,CAACzC,GAAG,EAAE,CAACkF,OAAO,EAAE4B,eAAe,EAAEd,WAAW,CAAC;gBACrE;cACF;cAEA5D,kBAAkB,CAACpC,GAAG,CAAC;YACzB;UACF,CAAC,CAAC,CAAC;UACHkF,OAAO,EAAEA;QACX,CAAC;MACH,CAAC;IACH;IAEA,IAAI8B,mBAAmB,GAAG,SAASA,mBAAmBA,CAACpB,CAAC,EAAE5I,MAAM,EAAE6D,KAAK,EAAE;MACvE,IAAIoG,WAAW,GAAGxI,UAAU,CAACmH,CAAC,EAAE5I,MAAM,EAAE6D,KAAK,CAAC;QAC1CgF,IAAI,GAAGoB,WAAW,CAACpB,IAAI;QACvBX,OAAO,GAAG+B,WAAW,CAAC/B,OAAO;MAEjC,IAAI1G,mBAAmB,EAAE;QACvB,OAAOA,mBAAmB,CAAC0G,OAAO,EAAElI,MAAM,EAAE6D,KAAK,EAAEgF,IAAI,CAAC;MAC1D;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC,CAAC;;IAGH,IAAI,CAAC9B,YAAY,CAACF,QAAQ,CAACvH,gBAAgB,CAAC,EAAE;MAC5C;MACA,IAAIyH,YAAY,CAACmD,SAAS,CAAC,UAAUpD,GAAG,EAAE;QACxC,IAAI1C,EAAE;QAEN,OAAO,CAAC,CAACA,EAAE,GAAG0C,GAAG,CAAC/H,mBAAmB,CAAC,MAAM,IAAI,IAAIqF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+F,UAAU,MAAM,eAAe;MACjH,CAAC,CAAC,KAAK,CAAC,EAAE;QACR,IAAIC,aAAa,GAAGrD,YAAY;UAC5BsD,cAAc,GAAGpM,QAAQ,CAACmM,aAAa,CAAC;UACxCE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;UAChCE,WAAW,GAAGF,cAAc,CAACvF,KAAK,CAAC,CAAC,CAAC;QAEzCiC,YAAY,GAAG,CAACuD,YAAY,EAAEhL,gBAAgB,CAAC,CAACY,MAAM,CAAC9B,kBAAkB,CAACmM,WAAW,CAAC,CAAC;MACzF,CAAC,MAAM;QACL;QACAxD,YAAY,GAAG,CAACzH,gBAAgB,CAAC,CAACY,MAAM,CAAC9B,kBAAkB,CAAC2I,YAAY,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC;;IAGF,IAAIyD,oBAAoB,GAAGzD,YAAY,CAAC0D,OAAO,CAACnL,gBAAgB,CAAC;IAEjE,IAAIyE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI8C,YAAY,CAACZ,MAAM,CAAC,UAAUW,GAAG,EAAE;MAC9E,OAAOA,GAAG,KAAKxH,gBAAgB;IACjC,CAAC,CAAC,CAAC+I,MAAM,GAAG,CAAC,EAAE;MACbhJ,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,iDAAiD,CAAC;IAC/E;IAEA0H,YAAY,GAAGA,YAAY,CAACZ,MAAM,CAAC,UAAUuE,MAAM,EAAE7G,KAAK,EAAE;MAC1D,OAAO6G,MAAM,KAAKpL,gBAAgB,IAAIuE,KAAK,KAAK2G,oBAAoB;IACtE,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIG,OAAO,GAAG5D,YAAY,CAACyD,oBAAoB,GAAG,CAAC,CAAC;IACpD,IAAII,OAAO,GAAG7D,YAAY,CAACyD,oBAAoB,GAAG,CAAC,CAAC;IACpD,IAAIK,WAAW,GAAGtJ,KAAK;IAEvB,IAAIsJ,WAAW,KAAKrF,SAAS,EAAE;MAC7B,IAAI,CAACoF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACrJ,KAAK,MAAMiE,SAAS,EAAE;QACnFqF,WAAW,GAAGD,OAAO,CAACrJ,KAAK;MAC7B,CAAC,MAAM,IAAI,CAACoJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACpJ,KAAK,MAAMiE,SAAS,EAAE;QAC1FqF,WAAW,GAAGF,OAAO,CAACpJ,KAAK;MAC7B;IACF;IAEA,IAAIsJ,WAAW,IAAIF,OAAO,IAAI,CAAC,CAACvG,EAAE,GAAGuG,OAAO,CAAC5L,mBAAmB,CAAC,MAAM,IAAI,IAAIqF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+F,UAAU,MAAM,eAAe,IAAIQ,OAAO,CAACpJ,KAAK,KAAKiE,SAAS,EAAE;MACzKmF,OAAO,CAACpJ,KAAK,GAAGsJ,WAAW;IAC7B,CAAC,CAAC;;IAGF,IAAIC,eAAe,GAAG9M,eAAe,CAAC;MACpCuD,KAAK,EAAEsJ,WAAW;MAClBE,KAAK,EAAE7J,iBAAiB;MACxB6G,SAAS,EAAE,EAAE,CAAC7H,MAAM,CAAC2B,SAAS,EAAE,mBAAmB,CAAC;MACpD0F,KAAK,EAAEnH,YAAY,CAAC4K,WAAW,IAAIzD,KAAK;MACxC0D,MAAM,EAAEjB;IACV,CAAC,EAAEjL,mBAAmB,EAAE;MACtBgJ,SAAS,EAAE,EAAE,CAAC7H,MAAM,CAAC2B,SAAS,EAAE,gBAAgB;IAClD,CAAC,CAAC;IAEF,OAAOkF,YAAY,CAACnD,GAAG,CAAC,UAAUkD,GAAG,EAAE;MACrC,OAAOA,GAAG,KAAKxH,gBAAgB,GAAGwL,eAAe,GAAGhE,GAAG;IACzD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9E,SAAS,EAAE0B,WAAW,EAAEtD,YAAY,EAAEuE,mBAAmB,EAAEE,qBAAqB,EAAEG,yBAAyB,EAAE9D,iBAAiB,EAAE4E,gBAAgB,EAAE7D,UAAU,EAAEkD,eAAe,EAAExB,gBAAgB,EAAE1C,gBAAgB,EAAEwE,sBAAsB,EAAEvB,kBAAkB,CAAC,CAAC;EACnQ,OAAO,CAACyC,gBAAgB,EAAE9B,qBAAqB,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}