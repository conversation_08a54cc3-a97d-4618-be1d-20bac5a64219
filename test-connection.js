/**
 * Test rapido per verificare se frontend e backend sono attivi
 */

const http = require('http');

console.log('🧪 Test Connessione Frontend e Backend\n');

// Test Frontend
console.log('📱 Test Frontend (localhost:3000)...');
testConnection('localhost', 3000)
    .then(() => {
        console.log('✅ Frontend ATTIVO su http://localhost:3000');
        
        // Test Backend
        console.log('\n🔧 Test Backend (localhost:3001)...');
        return testConnection('localhost', 3001);
    })
    .then(() => {
        console.log('✅ Backend ATTIVO su http://localhost:3001');
        console.log('\n🎉 Tutto configurato correttamente!');
        console.log('🌐 Apri http://localhost:3000 nel browser');
        console.log('🔍 Il pulsante di test backend dovrebbe essere nascosto');
    })
    .catch(error => {
        console.log('❌ Errore:', error);
        console.log('\n🔧 Azioni necessarie:');
        
        if (error.includes('3000')) {
            console.log('- Avvia il frontend: npm start');
        }
        if (error.includes('3001')) {
            console.log('- Avvia il backend sulla porta 3001');
            console.log('- Il pulsante di test backend sarà visibile e animato');
        }
    });

function testConnection(host, port) {
    return new Promise((resolve, reject) => {
        const req = http.request({
            hostname: host,
            port: port,
            method: 'GET',
            timeout: 2000
        }, (res) => {
            resolve();
        });
        
        req.on('error', () => {
            reject(`Servizio non raggiungibile su porta ${port}`);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(`Timeout connessione porta ${port}`);
        });
        
        req.end();
    });
}
