export { UnusedCSSComputed as UnusedCSS };
export type StyleSheetInfo = LH.Artifacts.CSSStyleSheetInfo & {
    networkRecord: LH.Artifacts.NetworkRequest;
    usedRules: Array<LH.Crdp.CSS.RuleUsage>;
};
declare const UnusedCSSComputed: typeof UnusedCSS & {
    request: (dependencies: {
        CSSUsage: LH.Artifacts['CSSUsage'];
        devtoolsLog: import("../index.js").DevtoolsLog;
    }, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../types/audit.js").default.ByteEfficiencyItem[]>;
};
/** @typedef {LH.Artifacts.CSSStyleSheetInfo & {networkRecord: LH.Artifacts.NetworkRequest, usedRules: Array<LH.Crdp.CSS.RuleUsage>}} StyleSheetInfo */
declare class UnusedCSS {
    /**
     * @param {Array<LH.Artifacts.CSSStyleSheetInfo>} styles The output of the Styles gatherer.
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Object<string, StyleSheetInfo>} A map of styleSheetId to stylesheet information.
     */
    static indexStylesheetsById(styles: Array<LH.Artifacts.CSSStyleSheetInfo>, networkRecords: Array<LH.Artifacts.NetworkRequest>): {
        [x: string]: StyleSheetInfo;
    };
    /**
     * Adds used rules to their corresponding stylesheet.
     * @param {Array<LH.Crdp.CSS.RuleUsage>} rules The output of the CSSUsage gatherer.
     * @param {Object<string, StyleSheetInfo>} indexedStylesheets Stylesheet information indexed by id.
     */
    static indexUsedRules(rules: Array<LH.Crdp.CSS.RuleUsage>, indexedStylesheets: {
        [x: string]: StyleSheetInfo;
    }): void;
    /**
     * @param {StyleSheetInfo} stylesheetInfo
     * @return {{wastedBytes: number, totalBytes: number, wastedPercent: number}}
     */
    static computeUsage(stylesheetInfo: StyleSheetInfo): {
        wastedBytes: number;
        totalBytes: number;
        wastedPercent: number;
    };
    /**
     * Trims stylesheet content down to the first rule-set definition.
     * @param {string=} content
     * @return {string}
     */
    static determineContentPreview(content?: string | undefined): string;
    /**
     * @param {StyleSheetInfo} stylesheetInfo The stylesheetInfo object.
     * @return {LH.Audit.ByteEfficiencyItem}
     */
    static mapSheetToResult(stylesheetInfo: StyleSheetInfo): LH.Audit.ByteEfficiencyItem;
    /**
     * @param {{CSSUsage: LH.Artifacts['CSSUsage'], devtoolsLog: LH.DevtoolsLog}} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Audit.ByteEfficiencyItem[]>}
    */
    static compute_(data: {
        CSSUsage: LH.Artifacts['CSSUsage'];
        devtoolsLog: import("../index.js").DevtoolsLog;
    }, context: LH.Artifacts.ComputedContext): Promise<LH.Audit.ByteEfficiencyItem[]>;
}
//# sourceMappingURL=unused-css.d.ts.map