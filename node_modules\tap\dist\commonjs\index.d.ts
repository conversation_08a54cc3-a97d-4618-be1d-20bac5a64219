/**
 * The main tap export, CJS style
 *
 * The only different between this and the ESM export is that, because a
 * CJS default export cannot co-exist with exported types, we have to make
 * the types available as a global namespace. Which, isn't exactly the most
 * elegant thing in the world, since it can conflict with any other module
 * that defines a `tap` global namespace, but at least it's common enough
 * that it doesn't read as too strange or unintuitive.
 *
 * @module
 */
import * as items from './main.js';
declare global {
    /**
     * All exported types from the `@tapjs/core` module are exported
     * here into the global `tap` namespace.
     */
    namespace tap {
        type Base = items.Base;
        type BaseOpts = items.BaseOpts;
        type Counts = items.Counts;
        type Extra = items.Extra;
        type Lists = items.Lists;
        type Minimal = items.Minimal;
        type Spawn = items.Spawn;
        type SpawnEvents = items.SpawnEvents;
        type SpawnOpts = items.SpawnOpts;
        type Stdin = items.Stdin;
        type StdinOpts = items.StdinOpts;
        type TapFile = items.TapFile;
        type TapFileEvents = items.TapFileEvents;
        type TapFileOpts = items.TapFileOpts;
        type TapBaseEvents = items.TapBaseEvents;
        type TestBase = items.TestBase;
        type TestBaseEvents = items.TestBaseEvents;
        type TestBaseOpts = items.TestBaseOpts;
        type Worker = items.Worker;
        type WorkerEvents = items.WorkerEvents;
        type WorkerOpts = items.WorkerOpts;
        type TapPlugin<T extends Object, O extends unknown = unknown> = items.TapPlugin<T, O>;
    }
}
declare const _default: items.TAP & typeof items;
export = _default;
//# sourceMappingURL=index-cjs.d.cts.map