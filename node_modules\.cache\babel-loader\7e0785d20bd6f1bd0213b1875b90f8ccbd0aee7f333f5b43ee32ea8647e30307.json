{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\PDV\\\\dashboardPDV\\\\dashboardPDV.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardPDV - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from 'primereact/messages';\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardPDV extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      disabled: false,\n      disabledMex: \"\",\n      priceList: \"d-none\"\n    };\n  }\n  //Chiamata axios effettuata una sola volta grazie a componentDidMount\n  componentDidMount() {\n    this.msgs1.show([{\n      severity: 'error',\n      summary: '',\n      detail: 'Siamo spiacenti al suo profilo utente non è associato un retailer per tanto le è impossibile compiere azioni',\n      sticky: true\n    }]);\n    var controlloListino = '';\n    controlloListino = JSON.parse(window.sessionStorage.getItem('PDV'));\n    if (controlloListino === false) {\n      this.msgs2.show([{\n        severity: 'error',\n        summary: '',\n        detail: 'Al suo profilo non è stato associato un listino per tanto finché non le verrà associato non sarà possibile effettuare ordini',\n        sticky: true\n      }]);\n      this.setState({\n        priceList: \"\"\n      });\n    }\n    var userData = [];\n    userData = JSON.parse(localStorage.getItem(\"userid\"));\n    if (userData.retailers === null) {\n      this.setState({\n        disabled: true\n      });\n    } else {\n      this.setState({\n        disabledMex: \"d-none\"\n      });\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"album\",\n          children: /*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: this.state.disabledMex,\n          children: /*#__PURE__*/_jsxDEV(Messages, {\n            ref: el => this.msgs1 = el\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: this.state.priceList,\n          children: /*#__PURE__*/_jsxDEV(Messages, {\n            ref: el => this.msgs2 = el\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mt-5\",\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {\n            dashAgent: true,\n            disabled: this.state.disabled\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardPDV;", "map": {"version": 3, "names": ["React", "Component", "Messages", "BannerWelcome", "Dashboard", "Nav", "jsxDEV", "_jsxDEV", "DashboardPDV", "constructor", "props", "state", "disabled", "disabledMex", "priceList", "componentDidMount", "msgs1", "show", "severity", "summary", "detail", "sticky", "controlloListino", "JSON", "parse", "window", "sessionStorage", "getItem", "msgs2", "setState", "userData", "localStorage", "retailers", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "el", "dashAgent"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/PDV/dashboardPDV/dashboardPDV.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardPDV - dashboard\n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from 'primereact/messages';\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport Nav from \"../../../components/navigation/Nav\";\n\nclass DashboardPDV extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            disabled: false,\n            disabledMex: \"\",\n            priceList: \"d-none\"\n        }\n    }\n    //Chiamata axios effettuata una sola volta grazie a componentDidMount\n    componentDidMount() {\n        this.msgs1.show([\n            { severity: 'error', summary: '', detail: 'Siamo spiacenti al suo profilo utente non è associato un retailer per tanto le è impossibile compiere azioni', sticky: true }\n        ]);\n\n        var controlloListino = ''\n        controlloListino = JSON.parse(window.sessionStorage.getItem('PDV'))\n        if (controlloListino === false) {\n            this.msgs2.show([\n                { severity: 'error', summary: '', detail: 'Al suo profilo non è stato associato un listino per tanto finché non le verrà associato non sarà possibile effettuare ordini', sticky: true }\n            ]);\n            this.setState({ priceList: \"\" })\n        }\n        var userData = [];\n        userData = JSON.parse(localStorage.getItem(\"userid\"))\n        if (userData.retailers === null) {\n            this.setState({\n                disabled: true,\n            })\n        } else {\n            this.setState({ disabledMex: \"d-none\" })\n        }\n    }\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <div className=\"album\">\n                    <div className=\"album\">\n                        <BannerWelcome />\n                    </div>\n                    <div className={this.state.disabledMex}>\n                        <Messages ref={(el) => this.msgs1 = el} />\n                    </div>\n                    <div className={this.state.priceList}>\n                        <Messages ref={(el) => this.msgs2 = el} />\n                    </div>\n                    <div className=\"container mt-5\" >\n                        <Dashboard dashAgent={true} disabled={this.state.disabled} />\n                    </div>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardPDV;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,aAAa,QAAQ,oDAAoD;AAClF,OAAOC,SAAS,MAAM,0CAA0C;AAChE,OAAOC,GAAG,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,YAAY,SAASP,SAAS,CAAC;EACjCQ,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACf,CAAC;EACL;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,CACZ;MAAEC,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,8GAA8G;MAAEC,MAAM,EAAE;IAAK,CAAC,CAC3K,CAAC;IAEF,IAAIC,gBAAgB,GAAG,EAAE;IACzBA,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC;IACnE,IAAIL,gBAAgB,KAAK,KAAK,EAAE;MAC5B,IAAI,CAACM,KAAK,CAACX,IAAI,CAAC,CACZ;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,8HAA8H;QAAEC,MAAM,EAAE;MAAK,CAAC,CAC3L,CAAC;MACF,IAAI,CAACQ,QAAQ,CAAC;QAAEf,SAAS,EAAE;MAAG,CAAC,CAAC;IACpC;IACA,IAAIgB,QAAQ,GAAG,EAAE;IACjBA,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACO,YAAY,CAACJ,OAAO,CAAC,QAAQ,CAAC,CAAC;IACrD,IAAIG,QAAQ,CAACE,SAAS,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACH,QAAQ,CAAC;QACVjB,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACiB,QAAQ,CAAC;QAAEhB,WAAW,EAAE;MAAS,CAAC,CAAC;IAC5C;EACJ;EACAoB,MAAMA,CAAA,EAAG;IACL,oBACI1B,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9B5B,OAAA,CAACF,GAAG;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPhC,OAAA;QAAK2B,SAAS,EAAC,OAAO;QAAAC,QAAA,gBAClB5B,OAAA;UAAK2B,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClB5B,OAAA,CAACJ,aAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAE,IAAI,CAACvB,KAAK,CAACE,WAAY;UAAAsB,QAAA,eACnC5B,OAAA,CAACL,QAAQ;YAACsC,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACzB,KAAK,GAAGyB;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAE,IAAI,CAACvB,KAAK,CAACG,SAAU;UAAAqB,QAAA,eACjC5B,OAAA,CAACL,QAAQ;YAACsC,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACb,KAAK,GAAGa;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3B5B,OAAA,CAACH,SAAS;YAACsC,SAAS,EAAE,IAAK;YAAC9B,QAAQ,EAAE,IAAI,CAACD,KAAK,CAACC;UAAS;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAe/B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}