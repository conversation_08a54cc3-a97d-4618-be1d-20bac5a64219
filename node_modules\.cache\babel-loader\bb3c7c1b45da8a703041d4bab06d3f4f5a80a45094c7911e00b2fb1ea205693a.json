{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\chain\\\\aggiunta file\\\\aggiungiCSV.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiCSV - operazioni sull'aggiunta CSV \n*\n*/\nimport React, { useRef, useState } from \"react\";\nimport { APIRequest } from \"../../../components/generalizzazioni/apireq\";\nimport { Costanti } from \"../../../components/traduttore/const\";\nimport { Button } from \"primereact/button\";\nimport { FileUpload } from \"primereact/fileupload\";\nimport { Toast } from \"primereact/toast\";\nimport { InputText } from \"primereact/inputtext\";\nimport { SelectButton } from \"primereact/selectbutton\";\nimport { chainGestioneProdotti } from \"../../../components/route\";\nimport ScaricaCSVProva from \"./scaricaCSVProva\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggiungiCSV = props => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [result, setResult] = useState(null);\n  const [disabled, setDisabled] = useState('');\n  const [value1, setValue1] = useState(null);\n  const [value2, setValue2] = useState(null);\n  const [value3, setValue3] = useState('COD_PROD');\n  const options = [{\n    name: 'Codice prodotto',\n    value: 'COD_PROD'\n  }, {\n    name: 'ID prodotto',\n    value: 'ID_PROD'\n  }];\n  const toast = useRef(null);\n  useEffect(() => {\n    var results = [];\n    var result = props.results.splice(1, 10);\n    result.forEach(element => {\n      delete element.id;\n      delete element.productsAvailabilities;\n      delete element.productsPackagings;\n      delete element.createAt;\n      delete element.updateAt;\n      results.push(element);\n    });\n    setResult(results);\n  }, [props]);\n  const onCancel = () => {\n    setDisabled(false);\n  };\n  const uploadFile = e => {\n    console.log(e);\n    if (e.files[0].size < 1300000) {\n      setSelectedFile(e.files[0]);\n      setDisabled(true);\n    }\n  };\n  const Invia = async () => {\n    if (window.location.pathname === chainGestioneProdotti) {\n      if (selectedFile !== null) {\n        // Create an object of formData \n        const formData = new FormData();\n        // Update the formData object \n        formData.append(\"csv\", selectedFile);\n        await APIRequest('POST', 'uploads/products', formData).then(res => {\n          console.log(res.data);\n          toast.current.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il CSV è stato inserito con successo\",\n            life: 3000\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 3000);\n        }).catch(e => {\n          var _e$response, _e$response2, _e$response3, _e$response3$data;\n          console.error('❌ Errore caricamento CSV prodotti:', e);\n\n          // Gestione specifica degli errori\n          const errorStatus = (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status;\n          const errorData = (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data;\n          const errorMessage = ((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : (_e$response3$data = _e$response3.data) === null || _e$response3$data === void 0 ? void 0 : _e$response3$data.message) || e.message;\n          let userMessage = '';\n          let summary = 'Errore';\n          if (errorStatus === 400) {\n            // Errori di validazione file\n            summary = '📄 File non valido';\n            if (errorMessage.toLowerCase().includes('formato') || errorMessage.toLowerCase().includes('csv')) {\n              userMessage = 'Il file CSV non è nel formato corretto. Verificare la struttura del file.';\n            } else if (errorMessage.toLowerCase().includes('dimensione') || errorMessage.toLowerCase().includes('size')) {\n              userMessage = 'Il file è troppo grande. Ridurre le dimensioni del file e riprovare.';\n            } else {\n              userMessage = \"File non valido: \".concat(errorMessage);\n            }\n          } else if (errorStatus === 413) {\n            // File troppo grande\n            summary = '📦 File troppo grande';\n            userMessage = 'Il file supera la dimensione massima consentita. Ridurre le dimensioni e riprovare.';\n          } else if (errorStatus === 422) {\n            // Errori di contenuto\n            summary = '📝 Contenuto non valido';\n            userMessage = \"Il contenuto del CSV non \\xE8 valido: \".concat(errorMessage);\n          } else if (errorStatus === 500) {\n            // Errore server\n            summary = '🔧 Errore del server';\n            userMessage = 'Si è verificato un errore interno durante l\\'elaborazione del file.';\n          } else if (!errorStatus) {\n            // Errore di rete\n            summary = '🌐 Errore di connessione';\n            userMessage = 'Impossibile caricare il file. Verificare la connessione internet.';\n          } else {\n            // Altri errori\n            summary = '❌ Errore imprevisto';\n            userMessage = \"Si \\xE8 verificato un errore imprevisto: \".concat(errorMessage);\n          }\n          toast.current.show({\n            severity: 'error',\n            summary: summary,\n            detail: userMessage,\n            life: 6000\n          });\n\n          // Log dettagliato per debugging\n          console.error('Dettagli errore CSV prodotti:', {\n            status: errorStatus,\n            data: errorData,\n            message: errorMessage,\n            fullError: e\n          });\n        });\n      } else {\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non è stato selezionato un CSV per l'importazione\",\n          life: 3000\n        });\n      }\n    } else if (props.importPriceList) {\n      if (selectedFile !== null) {\n        // Create an object of formData \n        const formData = new FormData();\n        // Update the formData object \n        formData.append(\"csv\", selectedFile);\n        var url = 'uploads/pricelist?separator=' + value1 + '&decimalDelimeter=' + value2 + '&idPriceList=' + localStorage.getItem(\"datiComodo\") + '&type=' + value3;\n        await APIRequest('POST', url, formData).then(res => {\n          console.log(res.data);\n          toast.current.show({\n            severity: 'success',\n            summary: 'Ottimo',\n            detail: \"Il CSV è stato inserito con successo\",\n            life: 3000\n          });\n        }).catch(e => {\n          var _e$response4, _e$response5;\n          console.log(e);\n          toast.current.show({\n            severity: 'error',\n            summary: 'Siamo spiacenti',\n            detail: \"Non \\xE8 stato possibile aggiungere il CSV. Messaggio errore: \".concat(((_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data) !== undefined ? (_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data : e.message),\n            life: 3000\n          });\n        });\n      } else {\n        toast.current.show({\n          severity: 'error',\n          summary: 'Siamo spiacenti',\n          detail: \"Non è stato selezionato un CSV per l'importazione\",\n          life: 3000\n        });\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-2\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), props.importPriceList && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row px-5 pb-5 pt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: [Costanti.SelSep, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(InputText, {\n          value: value1,\n          onChange: e => setValue1(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: [Costanti.SelDelDec, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(InputText, {\n          value: value2,\n          onChange: e => setValue2(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 d-flex justify-content-center flex-column align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: [Costanti.SelectType, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(SelectButton, {\n          className: \"w-100\",\n          value: value3,\n          options: options,\n          optionLabel: \"name\",\n          optionValue: \"value\",\n          onChange: e => setValue3(e.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mt-2 text-center\",\n          children: Costanti.SelCSV\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(FileUpload, {\n          id: \"upload\",\n          onSelect: e => uploadFile(e),\n          className: \"form-control border-0\",\n          chooseLabel: \"Seleziona\" /*uploadLabel=\"Carica\" cancelLabel=\"Elimina\"*/,\n          uploadOptions: {\n            className: 'd-none'\n          },\n          cancelOptions: {\n            className: 'd-none'\n          },\n          maxFileSize: \"1300000\",\n          invalidFileSizeMessageSummary: \"Il file selezionato supera la dimensione massima consentita\",\n          invalidFileSizeMessageDetail: \"\",\n          disabled: disabled,\n          onRemove: onCancel,\n          accept: \".CSV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-6 border-right mb-3 mb-md-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: Costanti.CSVEX\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(ScaricaCSVProva, {\n              label: 'ScaricaCSV',\n              results: result\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: Costanti.ProcediImportCSV\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              className: \"d-flex justify-content-center\",\n              onClick: Invia,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"pi pi-upload mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 95\n              }, this), Costanti.Importa]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiCSV, \"PudO1Sz+qOyCimF6QuY6uHCJ4Ns=\");\n_c = AggiungiCSV;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCSV\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "APIRequest", "<PERSON><PERSON>", "<PERSON><PERSON>", "FileUpload", "Toast", "InputText", "SelectButton", "chainGestioneProdotti", "ScaricaCSVProva", "useEffect", "jsxDEV", "_jsxDEV", "AggiungiCSV", "props", "_s", "selectedFile", "setSelectedFile", "result", "setResult", "disabled", "setDisabled", "value1", "setValue1", "value2", "setValue2", "value3", "setValue3", "options", "name", "value", "toast", "results", "splice", "for<PERSON>ach", "element", "id", "productsAvailabilities", "productsPackagings", "createAt", "updateAt", "push", "onCancel", "uploadFile", "e", "console", "log", "files", "size", "Invia", "window", "location", "pathname", "formData", "FormData", "append", "then", "res", "data", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "reload", "catch", "_e$response", "_e$response2", "_e$response3", "_e$response3$data", "error", "errorStatus", "response", "status", "errorData", "errorMessage", "message", "userMessage", "toLowerCase", "includes", "concat", "fullError", "importPriceList", "url", "localStorage", "getItem", "_e$response4", "_e$response5", "undefined", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SelSep", "onChange", "target", "SelDelDec", "SelectType", "optionLabel", "optionValue", "SelCSV", "onSelect", "<PERSON><PERSON><PERSON><PERSON>", "uploadOptions", "cancelOptions", "maxFileSize", "invalidFileSizeMessageSummary", "invalidFileSizeMessageDetail", "onRemove", "accept", "CSVEX", "label", "ProcediImportCSV", "onClick", "Importa", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/chain/aggiunta file/aggiungiCSV.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiCSV - operazioni sull'aggiunta CSV \n*\n*/\nimport React, { useRef, useState } from \"react\";\nimport { APIRequest } from \"../../../components/generalizzazioni/apireq\";\nimport { <PERSON>nti } from \"../../../components/traduttore/const\";\nimport { Button } from \"primereact/button\";\nimport { FileUpload } from \"primereact/fileupload\";\nimport { Toast } from \"primereact/toast\";\nimport { InputText } from \"primereact/inputtext\";\nimport { SelectButton } from \"primereact/selectbutton\";\nimport { chainGestioneProdotti } from \"../../../components/route\";\nimport ScaricaCSVProva from \"./scaricaCSVProva\";\nimport { useEffect } from \"react\";\n\nexport const AggiungiCSV = (props) => {\n    const [selectedFile, setSelectedFile] = useState(null);\n    const [result, setResult] = useState(null);\n    const [disabled, setDisabled] = useState('');\n    const [value1, setValue1] = useState(null);\n    const [value2, setValue2] = useState(null);\n    const [value3, setValue3] = useState('COD_PROD');\n    const options = [{ name: 'Codice prodotto', value: 'COD_PROD' }, { name: 'ID prodotto', value: 'ID_PROD' }];\n    const toast = useRef(null);\n\n    useEffect(() => {\n        var results = []\n        var result = props.results.splice(1,10)\n        result.forEach(element => {\n            delete element.id\n            delete element.productsAvailabilities\n            delete element.productsPackagings\n            delete element.createAt\n            delete element.updateAt\n            results.push(element)\n        });\n        setResult(results)\n    }, [props])\n\n    const onCancel = () => {\n        setDisabled(false)\n    }\n    const uploadFile = (e) => {\n        console.log(e)\n        if (e.files[0].size < 1300000) {\n            setSelectedFile(e.files[0])\n            setDisabled(true)\n        }\n    }\n    const Invia = async () => {\n        if (window.location.pathname === chainGestioneProdotti) {\n            if (selectedFile !== null) {\n                // Create an object of formData \n                const formData = new FormData();\n                // Update the formData object \n                formData.append(\n                    \"csv\",\n                    selectedFile\n                );\n                await APIRequest('POST', 'uploads/products', formData)\n                    .then(res => {\n                        console.log(res.data);\n                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"Il CSV è stato inserito con successo\", life: 3000 });\n                        setTimeout(() => {\n                            window.location.reload()\n                        }, 3000)\n                    }).catch((e) => {\n                        console.error('❌ Errore caricamento CSV prodotti:', e);\n\n                        // Gestione specifica degli errori\n                        const errorStatus = e.response?.status;\n                        const errorData = e.response?.data;\n                        const errorMessage = e.response?.data?.message || e.message;\n\n                        let userMessage = '';\n                        let summary = 'Errore';\n\n                        if (errorStatus === 400) {\n                            // Errori di validazione file\n                            summary = '📄 File non valido';\n                            if (errorMessage.toLowerCase().includes('formato') || errorMessage.toLowerCase().includes('csv')) {\n                                userMessage = 'Il file CSV non è nel formato corretto. Verificare la struttura del file.';\n                            } else if (errorMessage.toLowerCase().includes('dimensione') || errorMessage.toLowerCase().includes('size')) {\n                                userMessage = 'Il file è troppo grande. Ridurre le dimensioni del file e riprovare.';\n                            } else {\n                                userMessage = `File non valido: ${errorMessage}`;\n                            }\n                        } else if (errorStatus === 413) {\n                            // File troppo grande\n                            summary = '📦 File troppo grande';\n                            userMessage = 'Il file supera la dimensione massima consentita. Ridurre le dimensioni e riprovare.';\n                        } else if (errorStatus === 422) {\n                            // Errori di contenuto\n                            summary = '📝 Contenuto non valido';\n                            userMessage = `Il contenuto del CSV non è valido: ${errorMessage}`;\n                        } else if (errorStatus === 500) {\n                            // Errore server\n                            summary = '🔧 Errore del server';\n                            userMessage = 'Si è verificato un errore interno durante l\\'elaborazione del file.';\n                        } else if (!errorStatus) {\n                            // Errore di rete\n                            summary = '🌐 Errore di connessione';\n                            userMessage = 'Impossibile caricare il file. Verificare la connessione internet.';\n                        } else {\n                            // Altri errori\n                            summary = '❌ Errore imprevisto';\n                            userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;\n                        }\n\n                        toast.current.show({\n                            severity: 'error',\n                            summary: summary,\n                            detail: userMessage,\n                            life: 6000\n                        });\n\n                        // Log dettagliato per debugging\n                        console.error('Dettagli errore CSV prodotti:', {\n                            status: errorStatus,\n                            data: errorData,\n                            message: errorMessage,\n                            fullError: e\n                        });\n                    })\n            } else {\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Non è stato selezionato un CSV per l'importazione\", life: 3000 });\n            }\n        } else if (props.importPriceList) {\n            if (selectedFile !== null) {\n                // Create an object of formData \n                const formData = new FormData();\n                // Update the formData object \n                formData.append(\n                    \"csv\",\n                    selectedFile\n                );\n                var url = 'uploads/pricelist?separator=' + value1 + '&decimalDelimeter=' + value2 + '&idPriceList=' + localStorage.getItem(\"datiComodo\") + '&type=' + value3\n                await APIRequest('POST', url, formData)\n                    .then(res => {\n                        console.log(res.data);\n                        toast.current.show({ severity: 'success', summary: 'Ottimo', detail: \"Il CSV è stato inserito con successo\", life: 3000 });\n                    }).catch((e) => {\n                        console.log(e)\n                        toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: `Non è stato possibile aggiungere il CSV. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    })\n            } else {\n                toast.current.show({ severity: 'error', summary: 'Siamo spiacenti', detail: \"Non è stato selezionato un CSV per l'importazione\", life: 3000 });\n            }\n        }\n    }\n    return (\n        <div className=\"card p-2\">\n            <Toast ref={toast} />\n            {props.importPriceList &&\n                <div className=\"row px-5 pb-5 pt-3\">\n                    <div className=\"col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center\">\n                        <h4>{Costanti.SelSep}:</h4>\n                        <InputText value={value1} onChange={(e) => setValue1(e.target.value)} />\n                    </div>\n                    <div className=\"col-12 col-md-6 mb-3 d-flex justify-content-center flex-column align-items-center\">\n                        <h4>{Costanti.SelDelDec}:</h4>\n                        <InputText value={value2} onChange={(e) => setValue2(e.target.value)} />\n                    </div>\n                    <div className=\"col-12 d-flex justify-content-center flex-column align-items-center\">\n                        <h4>{Costanti.SelectType}:</h4>\n                        <SelectButton className=\"w-100\" value={value3} options={options} optionLabel='name' optionValue=\"value\" onChange={(e) => setValue3(e.value)} />\n                    </div>\n                </div>\n            }\n            <div className=\"row\">\n                <div className=\"col-12 d-flex justify-content-center\">\n                    <h5 className=\"mt-2 text-center\">{Costanti.SelCSV}</h5>\n                </div>\n                <div className=\"col-12\">\n                    <FileUpload id=\"upload\" onSelect={e => uploadFile(e)} className=\"form-control border-0\" chooseLabel=\"Seleziona\" /*uploadLabel=\"Carica\" cancelLabel=\"Elimina\"*/\n                        uploadOptions={{ className: 'd-none' }} cancelOptions={{ className: 'd-none' }} maxFileSize='1300000'\n                        invalidFileSizeMessageSummary=\"Il file selezionato supera la dimensione massima consentita\" invalidFileSizeMessageDetail=\"\"\n                        disabled={disabled} onRemove={onCancel} accept=\".CSV\"\n                    />\n                </div>\n                <div className=\"col-12\">\n                    <hr />\n                    <div className=\"row\">\n                        <div className=\"col-12 col-md-6 border-right mb-3 mb-md-0\">\n                            <div className=\"d-flex justify-content-center\">\n                                <p>{Costanti.CSVEX}</p>\n                            </div>\n                            <ScaricaCSVProva label={'ScaricaCSV'} results={result} />\n                        </div>\n                        <div className=\"col-12 col-md-6\">\n                            <div className=\"d-flex justify-content-center text-center\">\n                                <p>{Costanti.ProcediImportCSV}</p>\n                            </div>\n                            <Button className=\"d-flex justify-content-center\" onClick={Invia}><span className='pi pi-upload mr-2' />{Costanti.Importa}</Button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    )\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,OAAO,MAAMC,WAAW,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,UAAU,CAAC;EAChD,MAAM4B,OAAO,GAAG,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAW,CAAC,EAAE;IAAED,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAU,CAAC,CAAC;EAC3G,MAAMC,KAAK,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAE1BW,SAAS,CAAC,MAAM;IACZ,IAAIsB,OAAO,GAAG,EAAE;IAChB,IAAId,MAAM,GAAGJ,KAAK,CAACkB,OAAO,CAACC,MAAM,CAAC,CAAC,EAAC,EAAE,CAAC;IACvCf,MAAM,CAACgB,OAAO,CAACC,OAAO,IAAI;MACtB,OAAOA,OAAO,CAACC,EAAE;MACjB,OAAOD,OAAO,CAACE,sBAAsB;MACrC,OAAOF,OAAO,CAACG,kBAAkB;MACjC,OAAOH,OAAO,CAACI,QAAQ;MACvB,OAAOJ,OAAO,CAACK,QAAQ;MACvBR,OAAO,CAACS,IAAI,CAACN,OAAO,CAAC;IACzB,CAAC,CAAC;IACFhB,SAAS,CAACa,OAAO,CAAC;EACtB,CAAC,EAAE,CAAClB,KAAK,CAAC,CAAC;EAEX,MAAM4B,QAAQ,GAAGA,CAAA,KAAM;IACnBrB,WAAW,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMsB,UAAU,GAAIC,CAAC,IAAK;IACtBC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IACd,IAAIA,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,GAAG,OAAO,EAAE;MAC3B/B,eAAe,CAAC2B,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3B1B,WAAW,CAAC,IAAI,CAAC;IACrB;EACJ,CAAC;EACD,MAAM4B,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK5C,qBAAqB,EAAE;MACpD,IAAIQ,YAAY,KAAK,IAAI,EAAE;QACvB;QACA,MAAMqC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/B;QACAD,QAAQ,CAACE,MAAM,CACX,KAAK,EACLvC,YACJ,CAAC;QACD,MAAMf,UAAU,CAAC,MAAM,EAAE,kBAAkB,EAAEoD,QAAQ,CAAC,CACjDG,IAAI,CAACC,GAAG,IAAI;UACTZ,OAAO,CAACC,GAAG,CAACW,GAAG,CAACC,IAAI,CAAC;UACrB3B,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,sCAAsC;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;UAC1HC,UAAU,CAAC,MAAM;YACbf,MAAM,CAACC,QAAQ,CAACe,MAAM,CAAC,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,CAAC,CAACC,KAAK,CAAEvB,CAAC,IAAK;UAAA,IAAAwB,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA;UACZ1B,OAAO,CAAC2B,KAAK,CAAC,oCAAoC,EAAE5B,CAAC,CAAC;;UAEtD;UACA,MAAM6B,WAAW,IAAAL,WAAA,GAAGxB,CAAC,CAAC8B,QAAQ,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,MAAM;UACtC,MAAMC,SAAS,IAAAP,YAAA,GAAGzB,CAAC,CAAC8B,QAAQ,cAAAL,YAAA,uBAAVA,YAAA,CAAYX,IAAI;UAClC,MAAMmB,YAAY,GAAG,EAAAP,YAAA,GAAA1B,CAAC,CAAC8B,QAAQ,cAAAJ,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYZ,IAAI,cAAAa,iBAAA,uBAAhBA,iBAAA,CAAkBO,OAAO,KAAIlC,CAAC,CAACkC,OAAO;UAE3D,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIjB,OAAO,GAAG,QAAQ;UAEtB,IAAIW,WAAW,KAAK,GAAG,EAAE;YACrB;YACAX,OAAO,GAAG,oBAAoB;YAC9B,IAAIe,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC9FF,WAAW,GAAG,2EAA2E;YAC7F,CAAC,MAAM,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;cACzGF,WAAW,GAAG,sEAAsE;YACxF,CAAC,MAAM;cACHA,WAAW,uBAAAG,MAAA,CAAuBL,YAAY,CAAE;YACpD;UACJ,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,EAAE;YAC5B;YACAX,OAAO,GAAG,uBAAuB;YACjCiB,WAAW,GAAG,qFAAqF;UACvG,CAAC,MAAM,IAAIN,WAAW,KAAK,GAAG,EAAE;YAC5B;YACAX,OAAO,GAAG,yBAAyB;YACnCiB,WAAW,4CAAAG,MAAA,CAAyCL,YAAY,CAAE;UACtE,CAAC,MAAM,IAAIJ,WAAW,KAAK,GAAG,EAAE;YAC5B;YACAX,OAAO,GAAG,sBAAsB;YAChCiB,WAAW,GAAG,qEAAqE;UACvF,CAAC,MAAM,IAAI,CAACN,WAAW,EAAE;YACrB;YACAX,OAAO,GAAG,0BAA0B;YACpCiB,WAAW,GAAG,mEAAmE;UACrF,CAAC,MAAM;YACH;YACAjB,OAAO,GAAG,qBAAqB;YAC/BiB,WAAW,+CAAAG,MAAA,CAA4CL,YAAY,CAAE;UACzE;UAEA9C,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;YACfC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAEA,OAAO;YAChBC,MAAM,EAAEgB,WAAW;YACnBf,IAAI,EAAE;UACV,CAAC,CAAC;;UAEF;UACAnB,OAAO,CAAC2B,KAAK,CAAC,+BAA+B,EAAE;YAC3CG,MAAM,EAAEF,WAAW;YACnBf,IAAI,EAAEkB,SAAS;YACfE,OAAO,EAAED,YAAY;YACrBM,SAAS,EAAEvC;UACf,CAAC,CAAC;QACN,CAAC,CAAC;MACV,CAAC,MAAM;QACHb,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,EAAE,mDAAmD;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAClJ;IACJ,CAAC,MAAM,IAAIlD,KAAK,CAACsE,eAAe,EAAE;MAC9B,IAAIpE,YAAY,KAAK,IAAI,EAAE;QACvB;QACA,MAAMqC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/B;QACAD,QAAQ,CAACE,MAAM,CACX,KAAK,EACLvC,YACJ,CAAC;QACD,IAAIqE,GAAG,GAAG,8BAA8B,GAAG/D,MAAM,GAAG,oBAAoB,GAAGE,MAAM,GAAG,eAAe,GAAG8D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG7D,MAAM;QAC5J,MAAMzB,UAAU,CAAC,MAAM,EAAEoF,GAAG,EAAEhC,QAAQ,CAAC,CAClCG,IAAI,CAACC,GAAG,IAAI;UACTZ,OAAO,CAACC,GAAG,CAACW,GAAG,CAACC,IAAI,CAAC;UACrB3B,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,SAAS;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,sCAAsC;YAAEC,IAAI,EAAE;UAAK,CAAC,CAAC;QAC9H,CAAC,CAAC,CAACG,KAAK,CAAEvB,CAAC,IAAK;UAAA,IAAA4C,YAAA,EAAAC,YAAA;UACZ5C,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;UACdb,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;YAAEC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,MAAM,mEAAAmB,MAAA,CAAgE,EAAAM,YAAA,GAAA5C,CAAC,CAAC8B,QAAQ,cAAAc,YAAA,uBAAVA,YAAA,CAAY9B,IAAI,MAAKgC,SAAS,IAAAD,YAAA,GAAG7C,CAAC,CAAC8B,QAAQ,cAAAe,YAAA,uBAAVA,YAAA,CAAY/B,IAAI,GAAGd,CAAC,CAACkC,OAAO,CAAE;YAAEd,IAAI,EAAE;UAAK,CAAC,CAAC;QAC5N,CAAC,CAAC;MACV,CAAC,MAAM;QACHjC,KAAK,CAAC4B,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,iBAAiB;UAAEC,MAAM,EAAE,mDAAmD;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAClJ;IACJ;EACJ,CAAC;EACD,oBACIpD,OAAA;IAAK+E,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACrBhF,OAAA,CAACP,KAAK;MAACwF,GAAG,EAAE9D;IAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACpBnF,KAAK,CAACsE,eAAe,iBAClBxE,OAAA;MAAK+E,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAC/BhF,OAAA;QAAK+E,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAC9FhF,OAAA;UAAAgF,QAAA,GAAK1F,QAAQ,CAACgG,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BrF,OAAA,CAACN,SAAS;UAACwB,KAAK,EAAER,MAAO;UAAC6E,QAAQ,EAAGvD,CAAC,IAAKrB,SAAS,CAACqB,CAAC,CAACwD,MAAM,CAACtE,KAAK;QAAE;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACNrF,OAAA;QAAK+E,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAC9FhF,OAAA;UAAAgF,QAAA,GAAK1F,QAAQ,CAACmG,SAAS,EAAC,GAAC;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BrF,OAAA,CAACN,SAAS;UAACwB,KAAK,EAAEN,MAAO;UAAC2E,QAAQ,EAAGvD,CAAC,IAAKnB,SAAS,CAACmB,CAAC,CAACwD,MAAM,CAACtE,KAAK;QAAE;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACNrF,OAAA;QAAK+E,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAChFhF,OAAA;UAAAgF,QAAA,GAAK1F,QAAQ,CAACoG,UAAU,EAAC,GAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BrF,OAAA,CAACL,YAAY;UAACoF,SAAS,EAAC,OAAO;UAAC7D,KAAK,EAAEJ,MAAO;UAACE,OAAO,EAAEA,OAAQ;UAAC2E,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,OAAO;UAACL,QAAQ,EAAGvD,CAAC,IAAKjB,SAAS,CAACiB,CAAC,CAACd,KAAK;QAAE;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9I,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEVrF,OAAA;MAAK+E,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBhF,OAAA;QAAK+E,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACjDhF,OAAA;UAAI+E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE1F,QAAQ,CAACuG;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNrF,OAAA;QAAK+E,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACnBhF,OAAA,CAACR,UAAU;UAACgC,EAAE,EAAC,QAAQ;UAACsE,QAAQ,EAAE9D,CAAC,IAAID,UAAU,CAACC,CAAC,CAAE;UAAC+C,SAAS,EAAC,uBAAuB;UAACgB,WAAW,EAAC,WAAW,CAAC;UAC5GC,aAAa,EAAE;YAAEjB,SAAS,EAAE;UAAS,CAAE;UAACkB,aAAa,EAAE;YAAElB,SAAS,EAAE;UAAS,CAAE;UAACmB,WAAW,EAAC,SAAS;UACrGC,6BAA6B,EAAC,6DAA6D;UAACC,4BAA4B,EAAC,EAAE;UAC3H5F,QAAQ,EAAEA,QAAS;UAAC6F,QAAQ,EAAEvE,QAAS;UAACwE,MAAM,EAAC;QAAM;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNrF,OAAA;QAAK+E,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnBhF,OAAA;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrF,OAAA;UAAK+E,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAChBhF,OAAA;YAAK+E,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACtDhF,OAAA;cAAK+E,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC1ChF,OAAA;gBAAAgF,QAAA,EAAI1F,QAAQ,CAACiH;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACNrF,OAAA,CAACH,eAAe;cAAC2G,KAAK,EAAE,YAAa;cAACpF,OAAO,EAAEd;YAAO;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNrF,OAAA;YAAK+E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BhF,OAAA;cAAK+E,SAAS,EAAC,2CAA2C;cAAAC,QAAA,eACtDhF,OAAA;gBAAAgF,QAAA,EAAI1F,QAAQ,CAACmH;cAAgB;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNrF,OAAA,CAACT,MAAM;cAACwF,SAAS,EAAC,+BAA+B;cAAC2B,OAAO,EAAErE,KAAM;cAAA2C,QAAA,gBAAChF,OAAA;gBAAM+E,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAAC/F,QAAQ,CAACqH,OAAO;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAlF,EAAA,CAzLYF,WAAW;AAAA2G,EAAA,GAAX3G,WAAW;AAAA,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}