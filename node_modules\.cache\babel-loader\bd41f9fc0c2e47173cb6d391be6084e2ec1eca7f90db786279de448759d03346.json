{"ast": null, "code": "import OriginModal from './Modal';\nimport confirm, { withWarn, withInfo, withSuccess, withError, withConfirm, modalGlobalConfig } from './confirm';\nimport useModal from './useModal';\nimport destroyFns from './destroyFns';\nfunction modalWarn(props) {\n  return confirm(withWarn(props));\n}\nvar Modal = OriginModal;\nModal.useModal = useModal;\nModal.info = function infoFn(props) {\n  return confirm(withInfo(props));\n};\nModal.success = function successFn(props) {\n  return confirm(withSuccess(props));\n};\nModal.error = function errorFn(props) {\n  return confirm(withError(props));\n};\nModal.warning = modalWarn;\nModal.warn = modalWarn;\nModal.confirm = function confirmFn(props) {\n  return confirm(withConfirm(props));\n};\nModal.destroyAll = function destroyAllFn() {\n  while (destroyFns.length) {\n    var close = destroyFns.pop();\n    if (close) {\n      close();\n    }\n  }\n};\nModal.config = modalGlobalConfig;\nexport default Modal;", "map": {"version": 3, "names": ["OriginModal", "confirm", "with<PERSON><PERSON><PERSON>", "withInfo", "withSuccess", "with<PERSON><PERSON><PERSON>", "withConfirm", "modalGlobalConfig", "useModal", "destroyFns", "modalWarn", "props", "Modal", "info", "infoFn", "success", "successFn", "error", "errorFn", "warning", "warn", "confirmFn", "destroyAll", "destroyAllFn", "length", "close", "pop", "config"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/modal/index.js"], "sourcesContent": ["import OriginModal from './Modal';\nimport confirm, { withWarn, withInfo, withSuccess, withError, withConfirm, modalGlobalConfig } from './confirm';\nimport useModal from './useModal';\nimport destroyFns from './destroyFns';\n\nfunction modalWarn(props) {\n  return confirm(withWarn(props));\n}\n\nvar Modal = OriginModal;\nModal.useModal = useModal;\n\nModal.info = function infoFn(props) {\n  return confirm(withInfo(props));\n};\n\nModal.success = function successFn(props) {\n  return confirm(withSuccess(props));\n};\n\nModal.error = function errorFn(props) {\n  return confirm(withError(props));\n};\n\nModal.warning = modalWarn;\nModal.warn = modalWarn;\n\nModal.confirm = function confirmFn(props) {\n  return confirm(withConfirm(props));\n};\n\nModal.destroyAll = function destroyAllFn() {\n  while (destroyFns.length) {\n    var close = destroyFns.pop();\n\n    if (close) {\n      close();\n    }\n  }\n};\n\nModal.config = modalGlobalConfig;\nexport default Modal;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,SAAS;AACjC,OAAOC,OAAO,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,WAAW;AAC/G,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AAErC,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOV,OAAO,CAACC,QAAQ,CAACS,KAAK,CAAC,CAAC;AACjC;AAEA,IAAIC,KAAK,GAAGZ,WAAW;AACvBY,KAAK,CAACJ,QAAQ,GAAGA,QAAQ;AAEzBI,KAAK,CAACC,IAAI,GAAG,SAASC,MAAMA,CAACH,KAAK,EAAE;EAClC,OAAOV,OAAO,CAACE,QAAQ,CAACQ,KAAK,CAAC,CAAC;AACjC,CAAC;AAEDC,KAAK,CAACG,OAAO,GAAG,SAASC,SAASA,CAACL,KAAK,EAAE;EACxC,OAAOV,OAAO,CAACG,WAAW,CAACO,KAAK,CAAC,CAAC;AACpC,CAAC;AAEDC,KAAK,CAACK,KAAK,GAAG,SAASC,OAAOA,CAACP,KAAK,EAAE;EACpC,OAAOV,OAAO,CAACI,SAAS,CAACM,KAAK,CAAC,CAAC;AAClC,CAAC;AAEDC,KAAK,CAACO,OAAO,GAAGT,SAAS;AACzBE,KAAK,CAACQ,IAAI,GAAGV,SAAS;AAEtBE,KAAK,CAACX,OAAO,GAAG,SAASoB,SAASA,CAACV,KAAK,EAAE;EACxC,OAAOV,OAAO,CAACK,WAAW,CAACK,KAAK,CAAC,CAAC;AACpC,CAAC;AAEDC,KAAK,CAACU,UAAU,GAAG,SAASC,YAAYA,CAAA,EAAG;EACzC,OAAOd,UAAU,CAACe,MAAM,EAAE;IACxB,IAAIC,KAAK,GAAGhB,UAAU,CAACiB,GAAG,CAAC,CAAC;IAE5B,IAAID,KAAK,EAAE;MACTA,KAAK,CAAC,CAAC;IACT;EACF;AACF,CAAC;AAEDb,KAAK,CAACe,MAAM,GAAGpB,iBAAiB;AAChC,eAAeK,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}