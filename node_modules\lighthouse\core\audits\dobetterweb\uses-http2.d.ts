export default UsesHTTP2Audit;
export type Simulator = import('../../lib/dependency-graph/simulator/simulator').Simulator;
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare class UsesHTTP2Audit extends Audit {
    /**
     * Computes the estimated effect all results being converted to use http/2, the max of:
     *
     * - end time of the last long task in the provided graph
     * - end time of the last node in the graph
     *
     * @param {Array<{url: string}>} results
     * @param {Node} graph
     * @param {Simulator} simulator
     * @return {number}
     */
    static computeWasteWithTTIGraph(results: Array<{
        url: string;
    }>, graph: Node, simulator: Simulator): number;
    /**
     * Determines whether a network request is a "static resource" that would benefit from H2 multiplexing.
     * XHRs, tracking pixels, etc generally don't benefit as much because they aren't requested en-masse
     * for the same origin at the exact same time.
     *
     * @param {LH.Artifacts.NetworkRequest} networkRequest
     * @param {LH.Artifacts.EntityClassification} classifiedEntities
     * @return {boolean}
     */
    static isStaticAsset(networkRequest: LH.Artifacts.NetworkRequest, classifiedEntities: LH.Artifacts.EntityClassification): boolean;
    /**
     * Determine the set of resources that aren't HTTP/2 but should be.
     * We're a little conservative about what we surface for a few reasons:
     *
     *    - The simulator approximation of HTTP/2 is a little more generous than reality.
     *    - There's a bit of debate surrounding HTTP/2 due to its worse performance in environments with high packet loss.**
     *    - It's something that you'd have absolutely zero control over with a third-party (can't defer to fix it for example).
     *
     * Therefore, we only surface requests that were...
     *
     *    - Served over HTTP/1.1 or earlier
     *    - Served over an origin that serves at least 6 static asset requests
     *      (if there aren't more requests than browser's max/host, multiplexing isn't as big a deal)
     *    - Not served on localhost (h2 is a pain to deal with locally & and CI)
     *
     * ** = https://news.ycombinator.com/item?id=19086639
     *      https://www.twilio.com/blog/2017/10/http2-issues.html
     *      https://www.cachefly.com/http-2-is-not-a-magic-bullet/
     *
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {LH.Artifacts.EntityClassification} classifiedEntities
     * @return {Array<{url: string, protocol: string}>}
     */
    static determineNonHttp2Resources(networkRecords: Array<LH.Artifacts.NetworkRequest>, classifiedEntities: LH.Artifacts.EntityClassification): Array<{
        url: string;
        protocol: string;
    }>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const displayValue: string;
    const columnProtocol: string;
}
import { Audit } from "../audit.js";
import { NetworkRequest } from "../../lib/network-request.js";
import { EntityClassification } from "../../computed/entity-classification.js";
//# sourceMappingURL=uses-http2.d.ts.map