{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n\n/* eslint-disable react/prop-types */\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport shallowEqual from 'shallowequal';\nimport PanelContent from './PanelContent';\nvar CollapsePanel = /*#__PURE__*/function (_React$Component) {\n  _inherits(CollapsePanel, _React$Component);\n  var _super = _createSuper(CollapsePanel);\n  function CollapsePanel() {\n    var _this;\n    _classCallCheck(this, CollapsePanel);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.handleItemClick = function () {\n      var _this$props = _this.props,\n        onItemClick = _this$props.onItemClick,\n        panelKey = _this$props.panelKey;\n      if (typeof onItemClick === 'function') {\n        onItemClick(panelKey);\n      }\n    };\n    _this.handleKeyPress = function (e) {\n      if (e.key === 'Enter' || e.keyCode === 13 || e.which === 13) {\n        _this.handleItemClick();\n      }\n    };\n    return _this;\n  }\n  _createClass(CollapsePanel, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps) {\n      return !shallowEqual(this.props, nextProps);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n        _classNames2,\n        _this2 = this;\n      var _this$props2 = this.props,\n        className = _this$props2.className,\n        id = _this$props2.id,\n        style = _this$props2.style,\n        prefixCls = _this$props2.prefixCls,\n        header = _this$props2.header,\n        headerClass = _this$props2.headerClass,\n        children = _this$props2.children,\n        isActive = _this$props2.isActive,\n        showArrow = _this$props2.showArrow,\n        destroyInactivePanel = _this$props2.destroyInactivePanel,\n        accordion = _this$props2.accordion,\n        forceRender = _this$props2.forceRender,\n        openMotion = _this$props2.openMotion,\n        expandIcon = _this$props2.expandIcon,\n        extra = _this$props2.extra,\n        collapsible = _this$props2.collapsible;\n      var disabled = collapsible === 'disabled';\n      var headerCls = classNames(\"\".concat(prefixCls, \"-header\"), (_classNames = {}, _defineProperty(_classNames, headerClass, headerClass), _defineProperty(_classNames, \"\".concat(prefixCls, \"-header-collapsible-only\"), collapsible === 'header'), _classNames));\n      var itemCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-active\"), isActive), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-disabled\"), disabled), _classNames2), className);\n      var icon = /*#__PURE__*/React.createElement(\"i\", {\n        className: \"arrow\"\n      });\n      if (showArrow && typeof expandIcon === 'function') {\n        icon = expandIcon(this.props);\n      }\n      var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: itemCls,\n        style: style,\n        id: id\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: headerCls,\n        onClick: function onClick() {\n          return collapsible !== 'header' && _this2.handleItemClick();\n        },\n        role: accordion ? 'tab' : 'button',\n        tabIndex: disabled ? -1 : 0,\n        \"aria-expanded\": isActive,\n        onKeyPress: this.handleKeyPress\n      }, showArrow && icon, collapsible === 'header' ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: this.handleItemClick,\n        className: \"\".concat(prefixCls, \"-header-text\")\n      }, header) : header, ifExtraExist && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-extra\")\n      }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n        visible: isActive,\n        leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n      }, openMotion, {\n        forceRender: forceRender,\n        removeOnLeave: destroyInactivePanel\n      }), function (_ref, ref) {\n        var motionClassName = _ref.className,\n          motionStyle = _ref.style;\n        return /*#__PURE__*/React.createElement(PanelContent, {\n          ref: ref,\n          prefixCls: prefixCls,\n          className: motionClassName,\n          style: motionStyle,\n          isActive: isActive,\n          forceRender: forceRender,\n          role: accordion ? 'tabpanel' : null\n        }, children);\n      }));\n    }\n  }]);\n  return CollapsePanel;\n}(React.Component);\nCollapsePanel.defaultProps = {\n  showArrow: true,\n  isActive: false,\n  onItemClick: function onItemClick() {},\n  headerClass: '',\n  forceRender: false\n};\nexport default CollapsePanel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "classNames", "CSSMotion", "shallowEqual", "PanelContent", "CollapsePanel", "_React$Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "handleItemClick", "_this$props", "props", "onItemClick", "<PERSON><PERSON><PERSON>", "handleKeyPress", "e", "key", "keyCode", "which", "value", "shouldComponentUpdate", "nextProps", "render", "_classNames", "_classNames2", "_this2", "_this$props2", "className", "id", "style", "prefixCls", "header", "headerClass", "children", "isActive", "showArrow", "destroyInactivePanel", "accordion", "forceRender", "openMotion", "expandIcon", "extra", "collapsible", "disabled", "headerCls", "itemCls", "icon", "createElement", "ifExtraExist", "undefined", "onClick", "role", "tabIndex", "onKeyPress", "visible", "leavedClassName", "removeOnLeave", "_ref", "ref", "motionClassName", "motionStyle", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-collapse/es/Panel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n\n/* eslint-disable react/prop-types */\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport shallowEqual from 'shallowequal';\nimport PanelContent from './PanelContent';\n\nvar CollapsePanel = /*#__PURE__*/function (_React$Component) {\n  _inherits(CollapsePanel, _React$Component);\n\n  var _super = _createSuper(CollapsePanel);\n\n  function CollapsePanel() {\n    var _this;\n\n    _classCallCheck(this, CollapsePanel);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _this.handleItemClick = function () {\n      var _this$props = _this.props,\n          onItemClick = _this$props.onItemClick,\n          panelKey = _this$props.panelKey;\n\n      if (typeof onItemClick === 'function') {\n        onItemClick(panelKey);\n      }\n    };\n\n    _this.handleKeyPress = function (e) {\n      if (e.key === 'Enter' || e.keyCode === 13 || e.which === 13) {\n        _this.handleItemClick();\n      }\n    };\n\n    return _this;\n  }\n\n  _createClass(CollapsePanel, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps) {\n      return !shallowEqual(this.props, nextProps);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n          _classNames2,\n          _this2 = this;\n\n      var _this$props2 = this.props,\n          className = _this$props2.className,\n          id = _this$props2.id,\n          style = _this$props2.style,\n          prefixCls = _this$props2.prefixCls,\n          header = _this$props2.header,\n          headerClass = _this$props2.headerClass,\n          children = _this$props2.children,\n          isActive = _this$props2.isActive,\n          showArrow = _this$props2.showArrow,\n          destroyInactivePanel = _this$props2.destroyInactivePanel,\n          accordion = _this$props2.accordion,\n          forceRender = _this$props2.forceRender,\n          openMotion = _this$props2.openMotion,\n          expandIcon = _this$props2.expandIcon,\n          extra = _this$props2.extra,\n          collapsible = _this$props2.collapsible;\n      var disabled = collapsible === 'disabled';\n      var headerCls = classNames(\"\".concat(prefixCls, \"-header\"), (_classNames = {}, _defineProperty(_classNames, headerClass, headerClass), _defineProperty(_classNames, \"\".concat(prefixCls, \"-header-collapsible-only\"), collapsible === 'header'), _classNames));\n      var itemCls = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-active\"), isActive), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-disabled\"), disabled), _classNames2), className);\n      var icon = /*#__PURE__*/React.createElement(\"i\", {\n        className: \"arrow\"\n      });\n\n      if (showArrow && typeof expandIcon === 'function') {\n        icon = expandIcon(this.props);\n      }\n\n      var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: itemCls,\n        style: style,\n        id: id\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: headerCls,\n        onClick: function onClick() {\n          return collapsible !== 'header' && _this2.handleItemClick();\n        },\n        role: accordion ? 'tab' : 'button',\n        tabIndex: disabled ? -1 : 0,\n        \"aria-expanded\": isActive,\n        onKeyPress: this.handleKeyPress\n      }, showArrow && icon, collapsible === 'header' ? /*#__PURE__*/React.createElement(\"span\", {\n        onClick: this.handleItemClick,\n        className: \"\".concat(prefixCls, \"-header-text\")\n      }, header) : header, ifExtraExist && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-extra\")\n      }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n        visible: isActive,\n        leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n      }, openMotion, {\n        forceRender: forceRender,\n        removeOnLeave: destroyInactivePanel\n      }), function (_ref, ref) {\n        var motionClassName = _ref.className,\n            motionStyle = _ref.style;\n        return /*#__PURE__*/React.createElement(PanelContent, {\n          ref: ref,\n          prefixCls: prefixCls,\n          className: motionClassName,\n          style: motionStyle,\n          isActive: isActive,\n          forceRender: forceRender,\n          role: accordion ? 'tabpanel' : null\n        }, children);\n      }));\n    }\n  }]);\n\n  return CollapsePanel;\n}(React.Component);\n\nCollapsePanel.defaultProps = {\n  showArrow: true,\n  isActive: false,\n  onItemClick: function onItemClick() {},\n  headerClass: '',\n  forceRender: false\n};\nexport default CollapsePanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;;AAEjE;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DR,SAAS,CAACO,aAAa,EAAEC,gBAAgB,CAAC;EAE1C,IAAIC,MAAM,GAAGR,YAAY,CAACM,aAAa,CAAC;EAExC,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAIG,KAAK;IAETZ,eAAe,CAAC,IAAI,EAAES,aAAa,CAAC;IAEpC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IAEtDJ,KAAK,CAACU,eAAe,GAAG,YAAY;MAClC,IAAIC,WAAW,GAAGX,KAAK,CAACY,KAAK;QACzBC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;MAEnC,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;QACrCA,WAAW,CAACC,QAAQ,CAAC;MACvB;IACF,CAAC;IAEDd,KAAK,CAACe,cAAc,GAAG,UAAUC,CAAC,EAAE;MAClC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACE,OAAO,KAAK,EAAE,IAAIF,CAAC,CAACG,KAAK,KAAK,EAAE,EAAE;QAC3DnB,KAAK,CAACU,eAAe,CAAC,CAAC;MACzB;IACF,CAAC;IAED,OAAOV,KAAK;EACd;EAEAX,YAAY,CAACQ,aAAa,EAAE,CAAC;IAC3BoB,GAAG,EAAE,uBAAuB;IAC5BG,KAAK,EAAE,SAASC,qBAAqBA,CAACC,SAAS,EAAE;MAC/C,OAAO,CAAC3B,YAAY,CAAC,IAAI,CAACiB,KAAK,EAAEU,SAAS,CAAC;IAC7C;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,QAAQ;IACbG,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;QACXC,YAAY;QACZC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACf,KAAK;QACzBgB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,EAAE,GAAGF,YAAY,CAACE,EAAE;QACpBC,KAAK,GAAGH,YAAY,CAACG,KAAK;QAC1BC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,MAAM,GAAGL,YAAY,CAACK,MAAM;QAC5BC,WAAW,GAAGN,YAAY,CAACM,WAAW;QACtCC,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,QAAQ,GAAGR,YAAY,CAACQ,QAAQ;QAChCC,SAAS,GAAGT,YAAY,CAACS,SAAS;QAClCC,oBAAoB,GAAGV,YAAY,CAACU,oBAAoB;QACxDC,SAAS,GAAGX,YAAY,CAACW,SAAS;QAClCC,WAAW,GAAGZ,YAAY,CAACY,WAAW;QACtCC,UAAU,GAAGb,YAAY,CAACa,UAAU;QACpCC,UAAU,GAAGd,YAAY,CAACc,UAAU;QACpCC,KAAK,GAAGf,YAAY,CAACe,KAAK;QAC1BC,WAAW,GAAGhB,YAAY,CAACgB,WAAW;MAC1C,IAAIC,QAAQ,GAAGD,WAAW,KAAK,UAAU;MACzC,IAAIE,SAAS,GAAGpD,UAAU,CAAC,EAAE,CAACgB,MAAM,CAACsB,SAAS,EAAE,SAAS,CAAC,GAAGP,WAAW,GAAG,CAAC,CAAC,EAAErC,eAAe,CAACqC,WAAW,EAAES,WAAW,EAAEA,WAAW,CAAC,EAAE9C,eAAe,CAACqC,WAAW,EAAE,EAAE,CAACf,MAAM,CAACsB,SAAS,EAAE,0BAA0B,CAAC,EAAEY,WAAW,KAAK,QAAQ,CAAC,EAAEnB,WAAW,CAAC,CAAC;MAC9P,IAAIsB,OAAO,GAAGrD,UAAU,EAAEgC,YAAY,GAAG,CAAC,CAAC,EAAEtC,eAAe,CAACsC,YAAY,EAAE,EAAE,CAAChB,MAAM,CAACsB,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE5C,eAAe,CAACsC,YAAY,EAAE,EAAE,CAAChB,MAAM,CAACsB,SAAS,EAAE,cAAc,CAAC,EAAEI,QAAQ,CAAC,EAAEhD,eAAe,CAACsC,YAAY,EAAE,EAAE,CAAChB,MAAM,CAACsB,SAAS,EAAE,gBAAgB,CAAC,EAAEa,QAAQ,CAAC,EAAEnB,YAAY,GAAGG,SAAS,CAAC;MAC1S,IAAImB,IAAI,GAAG,aAAavD,KAAK,CAACwD,aAAa,CAAC,GAAG,EAAE;QAC/CpB,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,IAAIQ,SAAS,IAAI,OAAOK,UAAU,KAAK,UAAU,EAAE;QACjDM,IAAI,GAAGN,UAAU,CAAC,IAAI,CAAC7B,KAAK,CAAC;MAC/B;MAEA,IAAIqC,YAAY,GAAGP,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKQ,SAAS,IAAI,OAAOR,KAAK,KAAK,SAAS;MACtF,OAAO,aAAalD,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;QAC7CpB,SAAS,EAAEkB,OAAO;QAClBhB,KAAK,EAAEA,KAAK;QACZD,EAAE,EAAEA;MACN,CAAC,EAAE,aAAarC,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;QACzCpB,SAAS,EAAEiB,SAAS;QACpBM,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1B,OAAOR,WAAW,KAAK,QAAQ,IAAIjB,MAAM,CAAChB,eAAe,CAAC,CAAC;QAC7D,CAAC;QACD0C,IAAI,EAAEd,SAAS,GAAG,KAAK,GAAG,QAAQ;QAClCe,QAAQ,EAAET,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;QAC3B,eAAe,EAAET,QAAQ;QACzBmB,UAAU,EAAE,IAAI,CAACvC;MACnB,CAAC,EAAEqB,SAAS,IAAIW,IAAI,EAAEJ,WAAW,KAAK,QAAQ,GAAG,aAAanD,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;QACxFG,OAAO,EAAE,IAAI,CAACzC,eAAe;QAC7BkB,SAAS,EAAE,EAAE,CAACnB,MAAM,CAACsB,SAAS,EAAE,cAAc;MAChD,CAAC,EAAEC,MAAM,CAAC,GAAGA,MAAM,EAAEiB,YAAY,IAAI,aAAazD,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;QAC3EpB,SAAS,EAAE,EAAE,CAACnB,MAAM,CAACsB,SAAS,EAAE,QAAQ;MAC1C,CAAC,EAAEW,KAAK,CAAC,CAAC,EAAE,aAAalD,KAAK,CAACwD,aAAa,CAACtD,SAAS,EAAER,QAAQ,CAAC;QAC/DqE,OAAO,EAAEpB,QAAQ;QACjBqB,eAAe,EAAE,EAAE,CAAC/C,MAAM,CAACsB,SAAS,EAAE,iBAAiB;MACzD,CAAC,EAAES,UAAU,EAAE;QACbD,WAAW,EAAEA,WAAW;QACxBkB,aAAa,EAAEpB;MACjB,CAAC,CAAC,EAAE,UAAUqB,IAAI,EAAEC,GAAG,EAAE;QACvB,IAAIC,eAAe,GAAGF,IAAI,CAAC9B,SAAS;UAChCiC,WAAW,GAAGH,IAAI,CAAC5B,KAAK;QAC5B,OAAO,aAAatC,KAAK,CAACwD,aAAa,CAACpD,YAAY,EAAE;UACpD+D,GAAG,EAAEA,GAAG;UACR5B,SAAS,EAAEA,SAAS;UACpBH,SAAS,EAAEgC,eAAe;UAC1B9B,KAAK,EAAE+B,WAAW;UAClB1B,QAAQ,EAAEA,QAAQ;UAClBI,WAAW,EAAEA,WAAW;UACxBa,IAAI,EAAEd,SAAS,GAAG,UAAU,GAAG;QACjC,CAAC,EAAEJ,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOrC,aAAa;AACtB,CAAC,CAACL,KAAK,CAACsE,SAAS,CAAC;AAElBjE,aAAa,CAACkE,YAAY,GAAG;EAC3B3B,SAAS,EAAE,IAAI;EACfD,QAAQ,EAAE,KAAK;EACftB,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG,CAAC,CAAC;EACtCoB,WAAW,EAAE,EAAE;EACfM,WAAW,EAAE;AACf,CAAC;AACD,eAAe1C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}