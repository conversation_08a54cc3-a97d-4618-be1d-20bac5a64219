{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"component\", \"restricted\"];\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\PublicRoute.js\";\nimport React from 'react';\nimport { Route, Redirect } from 'react-router-dom';\nimport { isLogin } from '../utils';\nimport { basePath } from './route';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PublicRoute = _ref => {\n  let {\n      component: Component,\n      restricted\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsxDEV(Route, _objectSpread(_objectSpread({}, rest), {}, {\n    render: props => isLogin() && restricted ? /*#__PURE__*/_jsxDEV(Redirect, {\n      to: basePath\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(Component, _objectSpread({}, props), void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 46\n    }, this)\n  }), void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = PublicRoute;\nexport default PublicRoute;\nvar _c;\n$RefreshReg$(_c, \"PublicRoute\");", "map": {"version": 3, "names": ["React", "Route", "Redirect", "is<PERSON>ogin", "basePath", "jsxDEV", "_jsxDEV", "PublicRoute", "_ref", "component", "Component", "restricted", "rest", "_objectWithoutProperties", "_excluded", "_objectSpread", "render", "props", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/PublicRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Route, Redirect } from 'react-router-dom';\nimport { isLogin } from '../utils';\nimport { basePath } from './route';\n\nconst PublicRoute = ({ component: Component, restricted, ...rest }) => {\n    return (\n        <Route {...rest} render={props => (\n            isLogin() && restricted ?\n                <Redirect to={basePath} /> : <Component {...props} />\n        )} />\n    );\n};\n\nexport default PublicRoute;"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAClD,SAASC,OAAO,QAAQ,UAAU;AAClC,SAASC,QAAQ,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,WAAW,GAAGC,IAAA,IAAmD;EAAA,IAAlD;MAAEC,SAAS,EAAEC,SAAS;MAAEC;IAAoB,CAAC,GAAAH,IAAA;IAANI,IAAI,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAC5D,oBACIR,OAAA,CAACL,KAAK,EAAAc,aAAA,CAAAA,aAAA,KAAKH,IAAI;IAAEI,MAAM,EAAEC,KAAK,IAC1Bd,OAAO,CAAC,CAAC,IAAIQ,UAAU,gBACnBL,OAAA,CAACJ,QAAQ;MAACgB,EAAE,EAAEd;IAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGhB,OAAA,CAACI,SAAS,EAAAK,aAAA,KAAKE,KAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAC1D;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAEb,CAAC;AAACC,EAAA,GAPIhB,WAAW;AASjB,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}