{"core/audits/accessibility/accesskeys.js | description": {"message": "快速鍵可讓使用者快速聚焦網頁的特定部分。如要讓使用者正確瀏覽，每個快速鍵一律不可重複。[進一步瞭解快速鍵](https://dequeuniversity.com/rules/axe/4.6/accesskeys)。"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` 的值重複"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` 值獨一無二"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "每個 ARIA「`role`」都支援一部分特定的「`aria-*`」屬性。配對錯誤會導致「`aria-*`」屬性無效。[瞭解如何讓 ARIA 屬性與其角色相符](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)。"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 屬性與其角色不符"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 屬性與其角色相符"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "如果元素沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該欄位。[瞭解如何讓指令元素更容易使用](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)。"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`、`link` 和 `menuitem` 元素沒有無障礙名稱。"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`、`link` 和 `menuitem` 元素具有無障礙名稱"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "在 `<body>` 文件上設定 `aria-hidden=\"true\"` 時，輔助技術 (例如螢幕閱讀器) 的運作將無法保持一致。[瞭解 `aria-hidden` 如何影響文件內文](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)。"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` 有在文件 `<body>` 上顯示"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` 沒有在文件 `<body>` 上顯示"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "`[aria-hidden=\"true\"]` 元素中可聚焦的子代會禁止使用輔助技術 (例如螢幕閱讀器) 的使用者運用這些互動元素。[瞭解 `aria-hidden` 如何影響可聚焦的元素](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)。"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` 元素含有可聚焦的子代"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` 元素不含可聚焦的子代"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "如果輸入欄位沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該欄位。[進一步瞭解輸入欄位標籤](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)。"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA 輸入欄位沒有輔助名稱"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA 輸入欄位有輔助名稱"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "如果計量器元素沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該欄位。[瞭解如何命名`meter`元素](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)。"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` 元素沒有無障礙名稱。"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` 元素具有無障礙名稱"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "如果 `progressbar` 元素沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該元素。[瞭解如何為 `progressbar` 元素加上標籤](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)。"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` 元素沒有無障礙名稱。"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` 元素具有無障礙名稱"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "部分 ARIA 角色的必要屬性會向螢幕閱讀器的使用者說明元素狀態。[進一步瞭解角色和必要屬性](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)。"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` 未具備所有必要的 `[aria-*]` 屬性"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` 具備所有必要的 `[aria-*]` 屬性"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "部分 ARIA 父角色必須包含特定的子角色，才能正確執行無障礙功能。[進一步瞭解角色和需要的子元素](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)。"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "包含 ARIA `[role]` 且要求子元素包含特定 `[role]` 的元素缺少部分或全部的必要子元素。"}, "core/audits/accessibility/aria-required-children.js | title": {"message": "包含 ARIA `[role]` 且要求子元素包含特定 `[role]` 的元素具有全部必要的子元素。"}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "部分 ARIA 子角色必須包括在特定的父角色中，才能正確執行無障礙功能。[進一步瞭解 ARIA 角色和需要的父元素](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)。"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` 未包含在必要的父元素中"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` 已包含在必要的父元素中"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA 角色必須具備有效的值，才能執行無障礙功能。[進一步瞭解有效的 ARIA 角色](https://dequeuniversity.com/rules/axe/4.6/aria-roles)。"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` 值無效"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` 值有效"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "如果切換欄位沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該欄位。[進一步瞭解切換欄位](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)。"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA 切換欄位沒有輔助名稱"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA 切換欄位有輔助名稱"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "如果提示元素沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該欄位。[瞭解如何命名`tooltip`元素](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)。"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` 元素沒有無障礙名稱。"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` 元素具有無障礙名稱"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "如果 `treeitem` 元素沒有無障礙名稱，螢幕閱讀器只會讀出一般名稱，導致依賴螢幕閱讀器的使用者無法使用該元素。[進一步瞭解如何為 `treeitem` 元素加上標籤](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)。"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` 元素沒有無障礙名稱。"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` 元素具有無障礙名稱"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "輔助技術 (如螢幕閱讀器) 無法解讀具有無效值的 ARIA 屬性。[進一步瞭解 ARIA 屬性的有效值](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)。"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 屬性並無有效的值"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 屬性具備有效的值"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "輔助技術 (例如螢幕閱讀器) 無法解讀名稱無效的 ARIA 屬性。[進一步瞭解有效的 ARIA 屬性](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)。"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 屬性無效或拼字錯誤"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 屬性有效且無拼字錯誤"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "審核失敗的元素"}, "core/audits/accessibility/button-name.js | description": {"message": "如果按鈕沒有無障礙名稱，螢幕閱讀器只會讀出「按鈕」，導致依賴螢幕閱讀器的使用者無法使用該按鈕。[瞭解如何讓使用者更容易使用按鈕](https://dequeuniversity.com/rules/axe/4.6/button-name)。"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "按鈕沒有可存取的名稱"}, "core/audits/accessibility/button-name.js | title": {"message": "按鈕有可存取的名稱"}, "core/audits/accessibility/bypass.js | description": {"message": "為重複的內容新增略過選項，可提高鍵盤使用者的網頁瀏覽效率。[進一步瞭解如何繞過區塊](https://dequeuniversity.com/rules/axe/4.6/bypass)。"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "網頁中沒有標題、略過連結或地標區域"}, "core/audits/accessibility/bypass.js | title": {"message": "網頁包含標題、略過連結或地標區域"}, "core/audits/accessibility/color-contrast.js | description": {"message": "很多使用者難以閱讀或無法閱讀低對比度的文字。[瞭解如何提供充足的顏色對比度](https://dequeuniversity.com/rules/axe/4.6/color-contrast)。"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "背景和前景顏色沒有足夠的對比度。"}, "core/audits/accessibility/color-contrast.js | title": {"message": "背景和前景顏色有足夠的對比度"}, "core/audits/accessibility/definition-list.js | description": {"message": "如果定義清單的標記不正確，螢幕閱讀器可能會輸出令人混淆或不正確的內容。[瞭解如何正確建構定義清單](https://dequeuniversity.com/rules/axe/4.6/definition-list)。"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` 並非只包含排序正確的 `<dt>` 和 `<dd>` 群組、`<script>`、`<template>` 或 `<div>` 元素。"}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` 只包含排序正確的 `<dt>` 和 `<dd>` 群組、`<script>`、`<template>` 或 `<div>` 元素。"}, "core/audits/accessibility/dlitem.js | description": {"message": "定義清單項目 (`<dt>` 和 `<dd>`) 必須納入在父 `<dl>` 元素中，才能確保螢幕閱讀器正確朗讀這些項目。[瞭解如何正確建構定義清單](https://dequeuniversity.com/rules/axe/4.6/dlitem)。"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "定義清單項目未納入在 `<dl>` 元素中"}, "core/audits/accessibility/dlitem.js | title": {"message": "定義清單項目已納入在 `<dl>` 元素中"}, "core/audits/accessibility/document-title.js | description": {"message": "標題可讓螢幕閱讀器使用者概略瞭解網頁內容；搜尋引擎使用者經常需要使用此資料，判斷網頁內容是否與他們的搜尋查詢有關。[進一步瞭解文件標題](https://dequeuniversity.com/rules/axe/4.6/document-title)。"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "文件並無有效的 `<title>` 元素"}, "core/audits/accessibility/document-title.js | title": {"message": "文件具備 `<title>` 元素"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "所有可聚焦元素都必須有不重複的 `id`，以確保輔助技術可以查看這些元素。[瞭解如何修正 `id` 重複的問題](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)。"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "已啟用且可聚焦的元素有重複的 `[id]` 屬性"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "已啟用且可聚焦的元素沒有重複的 `[id]` 屬性"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA ID 的值不可重複，以免輔助技術忽略其他例項。[瞭解如何修正重複的 ARIA ID](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)。"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ID 重複"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ID 沒有重複"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "使用第一個、最後一個或所有標籤的螢幕閱讀器等輔助技術，可能無法將包含多個標籤的表格欄位正確讀出。[瞭解如何使用表格標籤](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)。"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "表格欄位含有多個標籤"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "沒有表格欄位含有多個標籤"}, "core/audits/accessibility/frame-title.js | description": {"message": "螢幕閱讀器使用者依賴頁框標題來瞭解頁框內容。[進一步瞭解頁框標題](https://dequeuniversity.com/rules/axe/4.6/frame-title)。"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` 或 `<iframe>` 元素沒有標題"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` 或 `<iframe>` 元素包含名稱"}, "core/audits/accessibility/heading-order.js | description": {"message": "排序正確且未略過層級的標題可傳達網頁的語意結構，讓您在使用輔助技術時更容易瀏覽及理解。[進一步瞭解標題排序](https://dequeuniversity.com/rules/axe/4.6/heading-order)。"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "標題元素未遞減排序"}, "core/audits/accessibility/heading-order.js | title": {"message": "標題元素已遞減排序"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "如果網頁未指定 `lang` 屬性，螢幕閱讀器會假設網頁採用使用者設定螢幕閱讀器時選擇的預設語言。如果網頁實際並非採用預設語言，螢幕閱讀器可能無法正確朗讀文字。[進一步瞭解 `lang` 屬性](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)。"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 元素並無 `[lang]` 屬性"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 元素具備 `[lang]` 屬性"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "指定有效的 [BCP 47 語言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)可協助螢幕閱讀器正確朗讀文字。[瞭解如何使用 `lang` 屬性](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)。"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 元素的 `[lang]` 屬性並無有效的值。"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 元素的 `[lang]` 屬性具備有效的值"}, "core/audits/accessibility/image-alt.js | description": {"message": "資訊型元素應提供簡短貼切的替代文字。只要將 alt 屬性留空，系統便會忽略該裝飾元素。[進一步瞭解此 `alt` 屬性](https://dequeuniversity.com/rules/axe/4.6/image-alt)。"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "圖片元素並無 `[alt]` 屬性"}, "core/audits/accessibility/image-alt.js | title": {"message": "圖片元素具有 `[alt]` 屬性"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "如果 `<input>` 按鈕是以圖片呈現，提供替代文字可協助螢幕閱讀器使用者瞭解該按鈕的用途。[瞭解輸入圖片說明文字](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)。"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 元素未設定 `[alt]` 文字"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 元素具有 `[alt]` 文字"}, "core/audits/accessibility/label.js | description": {"message": "標籤可以確保輔助技術 (例如螢幕閱讀器) 正確朗讀表格控制項。[進一步瞭解表格元素標籤](https://dequeuniversity.com/rules/axe/4.6/label)。"}, "core/audits/accessibility/label.js | failureTitle": {"message": "表格元素沒有相關聯的標籤"}, "core/audits/accessibility/label.js | title": {"message": "表格元素具有相關聯的標籤"}, "core/audits/accessibility/link-name.js | description": {"message": "使用可辨別、不重複且可聚焦的連結文字 (以及連結圖片的替代文字)，有助改善螢幕閱讀器使用者的瀏覽體驗。[瞭解如何讓連結更易用](https://dequeuniversity.com/rules/axe/4.6/link-name)。"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "連結並無可辨別的名稱"}, "core/audits/accessibility/link-name.js | title": {"message": "連結具有可辨別的名稱"}, "core/audits/accessibility/list.js | description": {"message": "螢幕閱讀器會以特定方式朗讀清單。確認清單採用正確的結構有助螢幕閱讀器順利輸出內容。[進一步瞭解正確的清單結構](https://dequeuniversity.com/rules/axe/4.6/list)。"}, "core/audits/accessibility/list.js | failureTitle": {"message": "清單中並非只包含 `<li>` 元素和指令碼支援元素 (`<script>` 和 `<template>`)。"}, "core/audits/accessibility/list.js | title": {"message": "清單只包含 `<li>` 元素和支援指令碼的元素 (`<script>` 和 `<template>`)。"}, "core/audits/accessibility/listitem.js | description": {"message": "清單項目 (`<li>`) 必須包含在父元素 `<ul>`、`<ol>` 或 `<menu>` 中，螢幕閱讀器才能正確朗讀這些項目。[進一步瞭解正確的清單結構](https://dequeuniversity.com/rules/axe/4.6/listitem)。"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "清單項目 (`<li>`) 未包含在 `<ul>`、`<ol>` 或 `<menu>` 父元素中。"}, "core/audits/accessibility/listitem.js | title": {"message": "清單項目 (`<li>`) 已包含在 `<ul>`、`<ol>` 或 `<menu>` 父元素中"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "使用者不會預期系統自動重新整理網頁，且執行此操作會將焦點移回網頁頂端。這可能會對使用者造成困擾或混淆。[進一步瞭解重新整理中繼標籤](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)。"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "文件使用 `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "文件未使用 `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "停用縮放功能會對低視力人士造成困擾，他們需要透過螢幕放大功能才能清楚看見網頁內容。[進一步瞭解檢視區中繼標記](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)。"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`<meta name=\"viewport\">` 元素中使用了 `[user-scalable=\"no\"]`，或 `[maximum-scale]` 屬性少於 5。"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`<meta name=\"viewport\">` 元素中未有使用 `[user-scalable=\"no\"]` 元素，而且 `[maximum-scale]` 屬性少於 5。"}, "core/audits/accessibility/object-alt.js | description": {"message": "螢幕閱讀器無法翻譯非文字內容。為 `<object>` 元素新增替代文字，可協助螢幕閱讀器向使用者傳達其意思。[進一步瞭解 `object` 元素的說明文字](https://dequeuniversity.com/rules/axe/4.6/object-alt)。"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 元素未設定替代文字"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 元素具有替代文字"}, "core/audits/accessibility/tabindex.js | description": {"message": "如果值大於 0，代表已採用明確的瀏覽排序。雖然此做法在技術上可行，但通常會對依賴輔助技術的使用者造成困擾。[進一步瞭解此 `tabindex` 屬性](https://dequeuniversity.com/rules/axe/4.6/tabindex)。"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "部分元素的 `[tabindex]` 值大於 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "所有元素的 `[tabindex]` 值皆未超過 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "螢幕閱讀器的功能可讓使用者更輕鬆瀏覽表格。如能確保採用 `[headers]` 屬性的 `<td>` 儲存格只參照同一表格中的其他儲存格，或許能改善螢幕閱讀器的使用體驗。[進一步瞭解 `headers` 屬性](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)。"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` 元素中採用 `[headers]` 屬性的儲存格參照了 `id`，無法在同一表格中找到此元素。"}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` 元素中採用 `[headers]` 屬性的儲存格，參照了同一表格中的其他儲存格。"}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "螢幕閱讀器的功能可讓使用者更輕鬆瀏覽表格。如能確保表格標題一律參照特定一組儲存格，或許能有助改善螢幕閱讀器使用者的體驗。[進一步瞭解表格標題](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)。"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 元素及帶有 `[role=\"columnheader\"/\"rowheader\"]` 的元素沒有所描述的資料儲存格。"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 元素和帶有 `[role=\"columnheader\"/\"rowheader\"]` 的元素有其描述的資料儲存格。"}, "core/audits/accessibility/valid-lang.js | description": {"message": "為元素指定有效的 [BCP 47 語言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)，可協助螢幕閱讀器正確朗讀文字。[瞭解如何使用 `lang` 屬性](https://dequeuniversity.com/rules/axe/4.6/valid-lang)。"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 屬性並無有效的值"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 屬性具備有效的值"}, "core/audits/accessibility/video-caption.js | description": {"message": "如果在影片中提供字幕，將有助於失聰或聽障使用者取得影片資料。[進一步瞭解影片字幕](https://dequeuniversity.com/rules/axe/4.6/video-caption)。"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 元素不含任何帶有 `[kind=\"captions\"]` 的 `<track>` 元素"}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 元素包含帶有 `[kind=\"captions\"]` 的 `<track>` 元素"}, "core/audits/autocomplete.js | columnCurrent": {"message": "目前的值"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "建議的憑證"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` 有助使用者更快提交表格。如要減少使用者需要執行的動作，建議您將 `autocomplete` 屬性設定為有效值以啟用此功能。[進一步瞭解表格中的 `autocomplete`](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` 元素沒有正確的 `autocomplete` 屬性"}, "core/audits/autocomplete.js | manualReview": {"message": "需要人手審查"}, "core/audits/autocomplete.js | reviewOrder": {"message": "查看憑證次序"}, "core/audits/autocomplete.js | title": {"message": "`<input>` 元素正確使用 `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` 憑證：{snippet} 中的「{token}」無效"}, "core/audits/autocomplete.js | warningOrder": {"message": "查看憑證次序：{snippet} 中的「{tokens}」"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "可以採取行動"}, "core/audits/bf-cache.js | description": {"message": "很多瀏覽操作都在來回往返頁面間進行。向前/返回快取 (bfcache) 可加快這些返回導覽的速度。[進一步瞭解向前/返回快取](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 個失敗原因}other{# 個失敗原因}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "失敗原因"}, "core/audits/bf-cache.js | failureTitle": {"message": "網頁已禁止還原向前/返回快取"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "失敗類型"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "無法操作"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "尚不支援瀏覽器"}, "core/audits/bf-cache.js | title": {"message": "網頁未有禁止還原向前/返回快取"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 擴充程式會對此頁面的載入效能產生負面影響。建議透過無痕模式或使用未安裝擴充程式的 Chrome 設定檔來審核頁面。"}, "core/audits/bootup-time.js | columnScriptEval": {"message": "指令碼評估"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "指令碼剖析"}, "core/audits/bootup-time.js | columnTotal": {"message": "CPU 總執行時間"}, "core/audits/bootup-time.js | description": {"message": "建議減少剖析、編譯和執行 JS 所用的時間。傳送較小的 JS 負載可能有所幫助。[瞭解如何縮短 JavaScript 的執行時間](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)。"}, "core/audits/bootup-time.js | failureTitle": {"message": "縮短 JavaScript 執行時間"}, "core/audits/bootup-time.js | title": {"message": "JavaScript 執行時間"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "從套件中移除重複的大型 JavaScript 模組，以減少網絡活動耗用不必要的字節。 "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "請移除 JavaScript 套件中的重複模組"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "使用大型 GIF 檔案呈現動畫內容會降低網絡傳輸效率。建議改用 MPEG4/WebM 格式的動畫影片和 PNG/WebP 格式的靜態圖片取代 GIF，以節省網絡字節。[進一步瞭解有效率的影片格式](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "使用影片格式的動畫內容"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill 和轉換可讓舊版瀏覽器使用新版 JavaScript 的功能。不過，有很多 Polyfill 和轉換都不是新型瀏覽器的必要項目。請使用模組/非模組的功能偵測，為 JavaScript 套件採用新型的指令碼部署策略，以減少送往新型瀏覽器的程式碼數量，同時保留對舊版瀏覽器的支援。[瞭解如何使用新型的 JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "避免向新型瀏覽器提供舊版 JavaScript"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP 和 AVIF 等圖片格式通常比 PNG 或 JPEG 有更好的壓縮效果，能夠更快完成下載及減少數據用量。[進一步瞭解新型圖像格式](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)。"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "提供 next-gen 格式的圖片"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "建議在所有重要資源載入完成前，延遲載入螢幕外和隱藏的圖片，以縮短可互動所需時間。[瞭解如何延遲載入螢幕外的圖片](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)。"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "延遲載入螢幕外圖片"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "過多資源往往會阻止系統首次繪製頁面。建議內嵌重要的 JS/CSS，延遲所有不重要的 JS/樣式。[瞭解如何移除阻止輸出的資源](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)。"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "排除阻止呈現的資源"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "網絡負載過大會造成使用者的費用負擔，且往往與過長載入時間息息相關。[瞭解如何減低負載大小](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)。"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "總大小為 {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "避免網絡負載過大"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "避免網絡負載過大"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "壓縮 CSS 檔案可減少網絡負載大小。[瞭解如何壓縮 CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)。"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "壓縮 CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "壓縮 JavaScript 檔案可減少負載大小和指令碼剖析時間。[瞭解如何壓縮 JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)。"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "壓縮 JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "從樣式表中減少未使用的規則，並延遲毋需捲動的當眼位置內容中未使用的 CSS，減少網絡活動耗用的字節。[瞭解如何減少未使用的 CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)。"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "減少未使用的 CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "減少未使用的 JavaScript，並延後載入指令碼，直至系統需要指令碼來減少網絡活動耗用的字節數為止。[瞭解如何減少未使用的 JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)。"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "減少未使用的 JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "延長快取期限可加快重覆瀏覽頁面的速度。[進一步瞭解有效率的快取政策](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)。"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{已找到 1 項資源}other{已找到 # 項資源}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "採用有效的快取政策提供靜態資產"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "使用有效的快取政策處理靜態資產"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "優化圖片以加快載入速度，減少流動數據用量。[瞭解如何有效率地進行圖片編碼](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "有效地進行圖片編碼"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "實際尺寸"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "顯示的尺寸"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "圖片大於其顯示大小"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "圖片適合其顯示大小"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "提供適當大小的圖片有助節省流動數據用量，並縮短載入時間。[瞭解如何調整圖片大小](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)。"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "適當調整圖片大小"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "文字資源應經過 (gzip、deflate 或 brotli) 壓縮，以將網絡字節總數減至最少。[進一步瞭解文字壓縮](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)。"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "啟用文字壓縮"}, "core/audits/content-width.js | description": {"message": "如果應用程式內容寬度與檢視區的寬度不相符，應用程式可能無法在流動裝置螢幕上呈現優化效果。[瞭解如何根據檢視區調整內容大小](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)。"}, "core/audits/content-width.js | explanation": {"message": "檢視區大小 ({innerWidth} 像素) 與視窗大小 ({outerWidth} 像素) 不相符。"}, "core/audits/content-width.js | failureTitle": {"message": "尚未為檢視區正確調整內容大小"}, "core/audits/content-width.js | title": {"message": "已將檢視區正確調整內容大小"}, "core/audits/critical-request-chains.js | description": {"message": "下方的「關鍵要求鏈結」顯示以高優先次序發佈的資源。為了提高頁面載入速度，建議您縮短鏈結長度，縮減下載資源的大小，或延遲下載不必要資源。[瞭解如何避免鏈結關鍵要求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)。"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{已找到 1 個鏈結}other{已找到 # 個鏈結}}"}, "core/audits/critical-request-chains.js | title": {"message": "避免鏈結重要要求"}, "core/audits/csp-xss.js | columnDirective": {"message": "指令"}, "core/audits/csp-xss.js | columnSeverity": {"message": "嚴重性"}, "core/audits/csp-xss.js | description": {"message": "強大的內容安全政策 (CSP) 可大幅降低被跨網站指令碼 (XSS) 攻擊的風險。[瞭解如何使用內容安全政策防範 XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "語法"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "網頁包含在 <meta> 標籤中定義的內容安全政策。建議將 CSP 移至 HTTP 標題，或在 HTTP 標題中定義其他嚴格 CSP。"}, "core/audits/csp-xss.js | noCsp": {"message": "找不到處於強制執行模式的 CSP"}, "core/audits/csp-xss.js | title": {"message": "確保 CSP 能有效防範 XSS 攻擊"}, "core/audits/deprecations.js | columnDeprecate": {"message": "淘汰/警告"}, "core/audits/deprecations.js | columnLine": {"message": "行數"}, "core/audits/deprecations.js | description": {"message": "系統最終會從瀏覽器中移除已淘汰的 API。[進一步瞭解已淘汰的 API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)。"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{發現 1 個警告}other{發現 # 個警告}}"}, "core/audits/deprecations.js | failureTitle": {"message": "使用已淘汰的 API"}, "core/audits/deprecations.js | title": {"message": "避免使用已淘汰的 API"}, "core/audits/dobetterweb/charset.js | description": {"message": "需要字元編碼宣告。您可以在 HTML 的前 1024 個字節中使用 `<meta>` 標籤定義，或在 Content-Type HTTP 回應標題中定義。[進一步瞭解聲明字元編碼](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)。"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "字元集宣告遺失或在 HTML 中太遲出現"}, "core/audits/dobetterweb/charset.js | title": {"message": "正確定義字元集"}, "core/audits/dobetterweb/doctype.js | description": {"message": "指定 DOCTYPE 能防止瀏覽器切換至怪異模式。[進一步瞭解 DOCTYPE 聲明](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)。"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE 名稱必須是字串 `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "文件包含會觸發`limited-quirks-mode`的 `doctype`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "文件必須包含 doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId 必須為空白字串"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId 必須為空白字串"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "文件包含會觸發`quirks-mode`的 `doctype`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "網頁缺少 HTML DOCTYPE，因此觸發了怪異模式"}, "core/audits/dobetterweb/doctype.js | title": {"message": "網頁含有 HTML DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "統計資料"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "值"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "大型 DOM 會增加記憶體用量、延長[樣式運算](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)的時間，並產生費用高昂的[版面配置重排](https://developers.google.com/speed/articles/reflow)。[瞭解如何避免 DOM 過大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 個元素}other{# 個元素}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "避免 DOM 過大"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "DOM 深度上限"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM 元素總數"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "子元素數量上限"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "避免 DOM 過大"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "如果未提供其他資訊就要求存取使用者的位置，會讓使用者感到困惑而不信任網站。建議您在使用者執行特定動作時，再提出這項要求。[進一步瞭解地理位置權限](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)。"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "在載入網頁時要求存取使用者的地理位置"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "避免在載入網頁時要求存取使用者的地理位置"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "問題類型"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "如果 Chrome Devtools 中的「`Issues`」面板有問題記錄，表示系統仍有問題尚待解決，例如網絡要求錯誤、安全控制不足和其他瀏覽器問題。在 Chrome DevTools 中開啟「問題」面板，瞭解每個問題的更多詳情。"}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "問題已記錄於 Chrome Devtools 的「`Issues`」面板"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "已根據跨來源政策封鎖"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "廣告的大量資源使用情況"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome Devtools 的 `Issues` 面板中沒有任何問題"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "版本"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "在此網頁上偵測到的所有前端 JavaScript 程式庫。[進一步瞭解此 JavaScript 程式庫偵測診斷審核](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)。"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "偵測到的 JavaScript 媒體庫"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "對於連線速度較慢的使用者，透過 `document.write()` 動態插入的外部指令碼可能會導致網頁延遲載入數十秒。[瞭解如何避免 document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)。"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "避免使用 `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "避免使用 `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "如果未提供其他資訊就要求使用者允許網站顯示通知，會讓使用者感到困惑而不信任網站。建議您在使用者操作特定手勢時，再提出這項要求。[進一步瞭解以負責任的方式取得通知權限](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)。"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "在載入網頁時要求使用者允許網站顯示通知"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "避免在載入網頁時要求使用者允許網站顯示通知"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "通訊協定"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 具備很多 HTTP/1.1 沒有的優點，包括二進制標題和多工處理。[進一步瞭解 HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{有 1 個要求未透過 HTTP/2 傳送}other{有 # 個要求未透過 HTTP/2 傳送}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "使用 HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "建議將輕觸動作和滑鼠滾輪事件監聽器標示為 `passive`，以提升網頁的捲動效能。[進一步瞭解如何採用被動事件監聽器器](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)。"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "未使用被動事件監聽器來提升捲動效能"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "使用被動活動監聽器來提升捲動效能"}, "core/audits/errors-in-console.js | description": {"message": "如果控制台有錯誤記錄，表示系統仍有問題尚待解決，例如網絡要求錯誤和其他瀏覽器問題。[進一步瞭解在控制台診斷審核中的這些錯誤](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "瀏覽器錯誤已記錄在控制台"}, "core/audits/errors-in-console.js | title": {"message": "系統未在管理中心記錄瀏覽器發生的錯誤"}, "core/audits/font-display.js | description": {"message": "利用 `font-display` CSS 功能，確保系統在載入網頁字型時使用者可以看到文字。[進一步瞭解 `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)。"}, "core/audits/font-display.js | failureTitle": {"message": "確保文字在網頁字型載入時仍然顯示"}, "core/audits/font-display.js | title": {"message": "在網頁字型載入時，所有文字仍然顯示"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse 無法為來源 ({fontOrigin}) 自動檢查 `font-display` 數值。}other{Lighthouse 無法為來源 ({fontOrigin}) 自動檢查 `font-display` 數值。}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "實際的圖片長寬比"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "網頁上顯示的圖片長寬比"}, "core/audits/image-aspect-ratio.js | description": {"message": "圖片顯示尺寸應符合正常長寬比。[進一步瞭解圖片長寬比](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)。"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "顯示的圖片長寬比不正確"}, "core/audits/image-aspect-ratio.js | title": {"message": "顯示的圖片長寬比正確"}, "core/audits/image-size-responsive.js | columnActual": {"message": "實際大小"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "顯示大小"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "預期大小"}, "core/audits/image-size-responsive.js | description": {"message": "圖片自然尺寸應與顯示大小及像素比例成正比，才能令圖片達到最清晰。[瞭解如何提供回應式圖片](https://web.dev/serve-responsive-images/)。"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "提供的圖片解像度過低"}, "core/audits/image-size-responsive.js | title": {"message": "提供的圖片解像度適當"}, "core/audits/installable-manifest.js | already-installed": {"message": "應用程式已安裝"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "無法從資訊清單下載必要圖示"}, "core/audits/installable-manifest.js | columnValue": {"message": "失敗原因"}, "core/audits/installable-manifest.js | description": {"message": "Service Worker 技術可讓您的應用程式使用多項漸進式網絡應用程式的功能，例如離線存取、新增到主畫面和推送通知。瀏覽器可透過適當的 Service Worker 和資訊清單設置主動提示使用者，建議他們將您的應用程式新增至主畫面，藉此提高參與度[進一步瞭解資訊清單可安裝性規定](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)。"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 個原因}other{# 個原因}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "網絡應用程式資訊清單或 Service Worker 不符合可安裝性要求"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "「Play 商店」應用程式網址與「Play 商店」ID 不相符"}, "core/audits/installable-manifest.js | in-incognito": {"message": "頁面在無痕式視窗中載入"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "資訊清單的「display」屬性必須為「standalone」、「fullscreen」或「minimal-ui」"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "資訊清單包含「display_override」欄位；首個支援的顯示模式必須為「standalone」、「fullscreen」或「minimal-ui」"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "無法擷取或剖析資訊清單，或剖析資訊沒有內容"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "資訊清單網址已在擷取資訊清單時變更。"}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "資訊清單未包含「name」或「short_name」欄位"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "資訊清單未包含合適的圖示 - 必須採用至少 {value0} 像素的 PNG、SVG 或 WebP 格式；必須設定大小屬性；如設定目的屬性，則必須包含「any」。"}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "所有提供的圖示均並非至少為 {value0} 像素的 PNG、SVG 或 WebP 格式正方形圖像，而且沒有設定目的屬性或將目的屬性設定為「任何 (any)」"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "已下載的圖示空白或已損毀"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "沒有提供「Play 商店」ID"}, "core/audits/installable-manifest.js | no-manifest": {"message": "頁面並無資訊清單 <link> 網址"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "未偵測到任何相符的 Service Worker。您可能需要重新載入頁面，或檢查目前頁面的 Service Worker 範圍是否包含資訊清單範圍和起始網址。"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "由於資訊清單中沒有「start_url」欄位，因此無法檢查 Service Worker"}, "core/audits/installable-manifest.js | noErrorId": {"message": "無法識別可安裝性錯誤 ID「{errorId}」"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "頁面並非透過安全來源提供"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "頁面未在主頁框內載入"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "頁面無法離線工作"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA 已解除安裝並將重設安裝規定檢查。"}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Android 不支援此指定的應用平台"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "資訊清單指定 prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications 僅支援 Android 裝置上的 Chrome Beta 版本和穩定版。"}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse 無法判斷是否有 Service Worker。請試使較新的 Chrome 版本。"}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Android 不支援資訊清單網址配置 ({scheme})。"}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "資訊清單起始網址無效"}, "core/audits/installable-manifest.js | title": {"message": "網絡應用程式資訊清單和 Service Worker 符合可安裝性要求"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "資訊清單中的網址包含使用者名稱、密碼或連接埠"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "頁面無法離線工作。Chrome 93 (2021 年 8 月穩定發行版) 發佈後，頁面將不會被視為可安裝。"}, "core/audits/is-on-https.js | allowed": {"message": "允許"}, "core/audits/is-on-https.js | blocked": {"message": "已封鎖"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "不安全的網址"}, "core/audits/is-on-https.js | columnResolution": {"message": "要求解決"}, "core/audits/is-on-https.js | description": {"message": "所有網站都應該使用 HTTPS 確保安全，即使網站不處理敏感資料亦然。這包括避免[混合內容](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)，即使初始要求透過 HTTPS 提供，混合內容中的部分資源仍會透過 HTTP 載入。HTTPS 能防範入侵者竄改或被動監聽應用程式與使用者之間的通訊，且 HTTP/2 和很多新的網絡平台 API 都要求使用 HTTPS。[進一步瞭解 HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)。"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{發現 1 個不安全的要求}other{發現 # 個不安全的要求}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "未使用 HTTPS"}, "core/audits/is-on-https.js | title": {"message": "使用 HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "已自動升級為 HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "允許 (附警告)"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "這是在檢視區中繪製的最大內容元素。[進一步瞭解「最大內容繪製」元素](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "「最大內容繪製」元素"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "對 CLS 的影響"}, "core/audits/layout-shift-elements.js | description": {"message": "這些 DOM 元素在頁面造成最多累計版面配置轉移 (CLS)。[瞭解如何改善累計版面配置轉移 (CLS)](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "避免大幅度的版面配置轉移"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "系統會在頁面生命週期的較後時段輸出延遲載入的毋需捲動的當眼位置圖片，這可能導致最大內容繪製發生延遲。[進一步瞭解最佳延遲載入](https://web.dev/lcp-lazy-loading/)。"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "「最大內容繪製」圖片延遲載入"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "「最大內容繪製」圖片沒有延遲載入"}, "core/audits/long-tasks.js | description": {"message": "列出主要執行緒上執行時間最長的工作，有助辨識導致輸入延遲的主因。[瞭解如何避免長時間的主要執行緒工作](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{找到 # 項長時間執行的工作}other{找到 # 項長時間執行的工作}}"}, "core/audits/long-tasks.js | title": {"message": "避免長時間在主要執行緒上執行的工作"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "類別"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "建議減少剖析、編譯和執行 JS 所用的時間。傳送較小的 JS 負載可能有所幫助。[瞭解如何將主要執行緒工作減到最少](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "將主要執行緒的工作減至最少"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "將主要執行緒的工作減至最少"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "為盡量觸及最多使用者，網站應能在每個主要瀏覽器上暢順運作。[瞭解跨瀏覽器兼容性](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)。"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "網站可以在不同瀏覽器上運作"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "確保每個網頁都可透過網址進行深層連結，且具有不重複網址，方便您在社交媒體上分享。[進一步瞭解如何提供深層連結](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)。"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "每個網頁都有一個網址"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "即使網絡速度緩慢，只要使用者瀏覽不同網頁也能營造流暢切換的感覺。這就是為使用者帶來高效感觀的關鍵。[進一步瞭解網頁切換](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)。"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "使用者在切換頁面時不會覺得網絡速度緩慢"}, "core/audits/maskable-icon.js | description": {"message": "在裝置上安裝應用程式時，罩蓋式圖示可確保圖片會填滿整個形狀，不會產生上下黑邊。[瞭解罩蓋式資訊清單圖示](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)。"}, "core/audits/maskable-icon.js | failureTitle": {"message": "資訊清單沒有可遮蓋的圖示"}, "core/audits/maskable-icon.js | title": {"message": "資訊清單中有可遮蓋的圖示"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "「累計版面配置轉移」會測量檢視區內可見元素的移動。[進一步瞭解「累計版面配置轉移」數據](https://web.dev/cls/)。"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "「互動至下一個繪製」會測量網頁回應速度，亦即網頁明顯回應使用者輸入內容所需的時間。[進一步瞭解「互動至下一個繪製」數據](https://web.dev/inp/)。"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "「首次內容繪製時間」標示繪製首個文字/首張圖片的時間。[進一步瞭解「首次內容繪製時間」數據](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)。"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "「首次有效繪製時間」評估頁面主要內容顯示的時間。[進一步瞭解「首次有效繪製時間」數據](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)。"}, "core/audits/metrics/interactive.js | description": {"message": "「可互動所需時間」是網頁進入完整互動狀態前所花的時間。[進一步瞭解「可互動所需時間」數據](https://developer.chrome.com/docs/lighthouse/performance/interactive/)。"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "「最大內容繪製」是指繪製最大的文字或圖片的時間。[進一步瞭解「最大內容繪製」數據](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "使用者可以體驗到最長的「首次輸入延遲時間」就是最長的工作持續時間。[進一步瞭解「最長的首次輸入延遲時間」數據](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)。"}, "core/audits/metrics/speed-index.js | description": {"message": "「速度指數」會顯示頁面內容的展現速度。[進一步瞭解「速度指數」數據](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)。"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "當工作長度超過 50 毫秒時，所有 FCP 和「可互動所需時間」之間的時長總和 (以毫秒為單位)。[進一步瞭解「總封鎖時間」數據](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)。"}, "core/audits/network-rtt.js | description": {"message": "網絡來回通訊時間 (RTT) 對效能有很大影響。如果系統傳送到某個來源的來回通訊時間很高，表示靠近使用者端的伺服器可改善效能。[進一步瞭解「來回通訊時間」](https://hpbn.co/primer-on-latency-and-bandwidth/)。"}, "core/audits/network-rtt.js | title": {"message": "網絡來回通訊時間"}, "core/audits/network-server-latency.js | description": {"message": "伺服器延遲時間可能會影響網頁效能。如果來源端的伺服器延遲時間高，代表伺服器已超載或後端效能欠佳。[進一步瞭解伺服器回應時間](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)。"}, "core/audits/network-server-latency.js | title": {"message": "伺服器後端延遲時間"}, "core/audits/no-unload-listeners.js | description": {"message": "「`unload`」事件無法保證觸發，而偵聽此事件可能會防礙瀏覽器優化項目操作，例如向前/返回快取。請改用「`pagehide`」或「`visibilitychange`」事件。[進一步瞭解卸載事件監聽器](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "發現 `unload` 偵聽器"}, "core/audits/no-unload-listeners.js | title": {"message": "避免 `unload` 事件偵聽器"}, "core/audits/non-composited-animations.js | description": {"message": "未合成的動畫可能無法順暢播放，而且會增加累計版面配置轉移 (CLS)。[瞭解如何避免使用非合成動畫](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{找到 # 個動畫元素}other{找到 # 個動畫元素}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "篩選器相關的屬性可能會移動像素"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "目標包含其他不兼容的動畫"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "效果包含「replace」以外的合成模式"}, "core/audits/non-composited-animations.js | title": {"message": "避免使用非合成的動畫"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "轉換相關屬性取決於方塊大小"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{不支援的 CSS 屬性：{properties}}other{不支援的 CSS 屬性：{properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "效果包含不支援的時間參數"}, "core/audits/performance-budget.js | description": {"message": "讓網絡要求的數量和大小低於使用者根據效能預算所設定的目標。[進一步瞭解效能預算](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 個要求}other{# 個要求}}"}, "core/audits/performance-budget.js | title": {"message": "效能預算"}, "core/audits/preload-fonts.js | description": {"message": "請預先載入 `optional` 字型，以便新訪客使用。[進一步瞭解如何預先載入字型](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "並未預先載入使用 `font-display: optional` 的字型"}, "core/audits/preload-fonts.js | title": {"message": "已預先載入使用 `font-display: optional` 的字型"}, "core/audits/prioritize-lcp-image.js | description": {"message": "如果 LCP 元素是以動態方式加入網頁，您應預先載入圖片才能改善 LCP。[進一步瞭解預先載入 LCP 元素](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)。"}, "core/audits/prioritize-lcp-image.js | title": {"message": "預先載入「最大內容繪製」圖片"}, "core/audits/redirects.js | description": {"message": "重新導向會導致頁面延遲載入。[瞭解如何避免網頁重新導向](https://developer.chrome.com/docs/lighthouse/performance/redirects/)。"}, "core/audits/redirects.js | title": {"message": "避免多次頁面重新導向"}, "core/audits/resource-summary.js | description": {"message": "如要設定網頁資源的數量和大小的預算，請新增 budget.json 檔案。[進一步瞭解效能預算](https://web.dev/use-lighthouse-for-performance-budgets/)。"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 個要求 • {byteCount, number, bytes} KiB}other{# 個要求 • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "降低要求數量並減少傳輸大小"}, "core/audits/seo/canonical.js | description": {"message": "標準連結會建議要在搜尋結果中顯示哪個網址。[進一步瞭解標準連結](https://developer.chrome.com/docs/lighthouse/seo/canonical/)。"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "多個互相衝突的網址 ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "網址無效 ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "指向其他 `hreflang` 位置 ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "不是絕對網址 ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "指向目標為網域的根網址 (首頁)，而非相應的內容網頁"}, "core/audits/seo/canonical.js | failureTitle": {"message": "文件並無有效的 `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "文件具備有效的 `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "無法檢索的連結"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "搜尋引擎可能會使用連結上的 `href` 屬性來檢索網站。請確認錨點元素的 `href` 屬性可連結適當的目的地，以便系統檢索網站上的更多網頁。[瞭解如何讓連結可供檢索](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "無法檢索的連結"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "可檢索的連結"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "其他難以辨識的文字"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "字型大小"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "頁面文字百分比"}, "core/audits/seo/font-size.js | columnSelector": {"message": "選取器"}, "core/audits/seo/font-size.js | description": {"message": "如果字型小於 12 像素，文字會太小而難以辨識，流動裝置訪客需要「兩指縮放」才能閱讀內容。網頁中應有超過 60% 採用最少 12 像素的文字。[進一步瞭解清晰易讀的字型大小](https://developer.chrome.com/docs/lighthouse/seo/font-size/)。"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} 的文字清晰可讀"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "由於網頁沒有為流動裝置螢幕設定合適的檢視區中繼標記，因文字難以辨識。"}, "core/audits/seo/font-size.js | failureTitle": {"message": "文件使用的字型大小難以辨識"}, "core/audits/seo/font-size.js | legibleText": {"message": "清晰可讀的文字"}, "core/audits/seo/font-size.js | title": {"message": "文件使用的字型大小清晰可讀"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang 連結會通知搜尋引擎，應在特定語言或區域的搜尋結果中該顯示哪個版本的網頁。[進一步瞭解 `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)。"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "文件並無有效的 `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "相對 href 數值"}, "core/audits/seo/hreflang.js | title": {"message": "文件具備有效的 `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "非預期的語言代碼"}, "core/audits/seo/http-status-code.js | description": {"message": "如果網頁傳回失敗的 HTTP 狀態碼，可能無法正確加入索引。[進一步瞭解 HTTP 狀態碼](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)。"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "網頁傳回失敗的 HTTP 狀態碼"}, "core/audits/seo/http-status-code.js | title": {"message": "網頁傳回成功的 HTTP 狀態碼"}, "core/audits/seo/is-crawlable.js | description": {"message": "如果搜尋引擎沒有檢索網頁的權限，將無法在搜尋結果中顯示您的網頁。[進一步瞭解檢索器指令](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)。"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "網頁的索引功能被封鎖"}, "core/audits/seo/is-crawlable.js | title": {"message": "網頁的索引功能未被封鎖"}, "core/audits/seo/link-text.js | description": {"message": "連結說明文字可協助搜尋引擎瞭解您的內容。[瞭解如何讓連結更易用](https://developer.chrome.com/docs/lighthouse/seo/link-text/)。"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{找到 1 個連結}other{找到 # 個連結}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "連結並無說明文字"}, "core/audits/seo/link-text.js | title": {"message": "連結具有說明文字"}, "core/audits/seo/manual/structured-data.js | description": {"message": "執行[結構化資料測試工具](https://search.google.com/structured-data/testing-tool/)和[結構化資料 Linter](http://linter.structured-data.org/) 來驗證結構化資料。[進一步瞭解結構化資料](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)。"}, "core/audits/seo/manual/structured-data.js | title": {"message": "結構化資料有效"}, "core/audits/seo/meta-description.js | description": {"message": "您可在搜尋結果中加入中繼說明，簡要描述網頁內容。[進一步瞭解中繼說明](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)。"}, "core/audits/seo/meta-description.js | explanation": {"message": "沒有說明文字。"}, "core/audits/seo/meta-description.js | failureTitle": {"message": "文件並無中繼說明"}, "core/audits/seo/meta-description.js | title": {"message": "文件具有中繼說明"}, "core/audits/seo/plugins.js | description": {"message": "搜尋引擎無法為外掛程式內容加入索引，而且很多裝置對外掛程式都設有限制甚至不提供支援。[進一步瞭解如何避免使用外掛程式](https://developer.chrome.com/docs/lighthouse/seo/plugins/)。"}, "core/audits/seo/plugins.js | failureTitle": {"message": "文件使用外掛程式"}, "core/audits/seo/plugins.js | title": {"message": "文件避免使用外掛程式"}, "core/audits/seo/robots-txt.js | description": {"message": "如果您的 robots.txt 檔案格式錯誤，檢索器可能無法瞭解您偏好的網站檢索或加入索引方式。[進一步瞭解 robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)。"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt 要求傳回以下 HTTP 狀態：{statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{找到 1 個錯誤}other{找到 # 個錯誤}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse 無法下載 robots.txt 檔"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt 無效"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt 有效"}, "core/audits/seo/tap-targets.js | description": {"message": "按鈕和連結等互動元素的大小應至少有 48x48 像素，且周圍應保留足夠空間以便使用者輕按，同時避免與其他元素重疊的情況。[進一步瞭解輕按目標](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)。"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} 的輕按目標大小適中"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "由於網頁沒有為流動裝置螢幕設定合適的檢視區中繼標記，因此輕按目標太小"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "輕按目標未設定成適當大小"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "重疊的目標"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "輕按目標"}, "core/audits/seo/tap-targets.js | title": {"message": "輕按目標已設定成適當大小"}, "core/audits/server-response-time.js | description": {"message": "請確保主要文件的伺服器回應時間不會太長，因為這會影響所有其他要求。[進一步瞭解「首個字節時間」數據](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)。"}, "core/audits/server-response-time.js | displayValue": {"message": "根文件回應時間為 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/server-response-time.js | failureTitle": {"message": "縮短初始伺服器回應時間"}, "core/audits/server-response-time.js | title": {"message": "初始伺服器回應時間短暫"}, "core/audits/service-worker.js | description": {"message": "Service Worker 技術可讓您的應用程式使用多項漸進式網絡應用程式的功能，例如離線存取、新增到主畫面和推送通知。[進一步瞭解 Service Worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)。"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "此網頁由 Service Worker 控制，但系統無法將資訊清單剖析為有效的 JSON，因此找不到任何 `start_url`"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "此網頁由 Service Worker 控制，但 `start_url` ({startUrl}) 不在 Service Worker 的範圍內 ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "此網頁由 Service Worker 控制，但系統未能擷取任何資訊清單，因此找不到任何 `start_url`。"}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "此來源包含一個或多個 Service Worker，但該頁面 ({pageUrl}) 不在 Service Worker 的範圍內。"}, "core/audits/service-worker.js | failureTitle": {"message": "未註冊可控制網頁和 `start_url` 的 Service Worker"}, "core/audits/service-worker.js | title": {"message": "已註冊可控制網頁和 `start_url` 的 Service Worker"}, "core/audits/splash-screen.js | description": {"message": "透過設定啟動畫面的主題，可確保使用者從主畫面啟動您的應用程式時享有優質體驗。[進一步瞭解啟動畫面](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)。"}, "core/audits/splash-screen.js | failureTitle": {"message": "未設定自訂啟動畫面"}, "core/audits/splash-screen.js | title": {"message": "設有自訂啟動畫面"}, "core/audits/themed-omnibox.js | description": {"message": "您可以將瀏覽器網址列的主題設定為與網站相符。[進一步瞭解如何設定網址列的主題](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)。"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "未設定網址列的主題顏色。"}, "core/audits/themed-omnibox.js | title": {"message": "設定網址列的主題顏色。"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (客戶支援)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (市場推廣)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (社交)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (影片)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "產品"}, "core/audits/third-party-facades.js | description": {"message": "部分第三方嵌入內容可延遲載入。建議您在必要時才以外觀取代。[瞭解如何透過外觀延遲載入第三方內容](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)。"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{可使用 # 個替代外觀}other{可使用 # 個替代外觀}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "部分第三方資源可透過外觀延遲載入"}, "core/audits/third-party-facades.js | title": {"message": "延遲載入設有外觀的第三方資源"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "第三方"}, "core/audits/third-party-summary.js | description": {"message": "第三方程式碼可能會嚴重影響載入效能。請盡量減少不必要的第三方供應商，並在網頁的主要內容載入完成後，再載入第三方程式碼。[瞭解如何盡量減少第三方程式碼的影響](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)。"}, "core/audits/third-party-summary.js | displayValue": {"message": "第三方程式碼將主要執行緒封鎖了 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/third-party-summary.js | failureTitle": {"message": "減低第三方程式碼的影響"}, "core/audits/third-party-summary.js | title": {"message": "減少第三方程式碼使用量"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "測量"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "數據"}, "core/audits/timing-budget.js | description": {"message": "設定時間預算可助您隨時留意網站的效能。效能穩定的網站能快速載入網頁，並迅速回應使用者的輸入事件。[進一步瞭解效能預算](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "core/audits/timing-budget.js | title": {"message": "時間預算"}, "core/audits/unsized-images.js | description": {"message": "請明確設定圖片元素的寬度和高度，以減少版面配置轉移及改善累計版面配置轉移 (CLS)。[瞭解如何設定圖片尺寸](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "圖片元素並無顯性`width`和`height`"}, "core/audits/unsized-images.js | title": {"message": "圖片元素具有顯性`width`和`height`"}, "core/audits/user-timings.js | columnType": {"message": "類別"}, "core/audits/user-timings.js | description": {"message": "建議使用 User Timing API 評估應用程式在關鍵使用者體驗期間的實際成效。[進一步瞭解「用戶使用時間」標記](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)。"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 個用戶使用時間標記}other{# 個用戶使用時間標記}}"}, "core/audits/user-timings.js | title": {"message": "用戶使用時間標記和測量結果"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "{securityO<PERSON>in} 有「`<link rel=preconnect>`」，但瀏覽器沒有使用。請檢查您使用 `crossorigin` 屬性的方式是否正確。"}, "core/audits/uses-rel-preconnect.js | description": {"message": "建議您新增 `preconnect` 或 `dns-prefetch` 資源提示，及早連線至重要的第三方來源。[瞭解如何預先連線至需要的來源](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)。"}, "core/audits/uses-rel-preconnect.js | title": {"message": "預先連接至必要來源"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "已找到超過 2 個「`<link rel=preconnect>`」的連結。這些連結應盡量少用，並只用於最重要的來源。"}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "{securityO<PERSON>in} 有「`<link rel=preconnect>`」，但瀏覽器沒有使用。請只為載入網頁一定會要求的重要來源使用「`preconnect`」。"}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "「{preloadURL}」有預先載入的 `<link>`，但瀏覽器沒有使用。請檢查您使用 `crossorigin` 屬性的方式是否正確。"}, "core/audits/uses-rel-preload.js | description": {"message": "建議使用 `<link rel=preload>` 來指定優先需要的網絡要求，並預先擷取資源。[瞭解如何預先載入關鍵要求](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)。"}, "core/audits/uses-rel-preload.js | title": {"message": "預先載入關鍵要求"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "對應網址"}, "core/audits/valid-source-maps.js | description": {"message": "來源對應會將經壓縮的程式碼轉譯成原始碼。此功能可協助開發人員在正式版本中偵錯。此外，Lighthouse 亦能提供進一步的分析資料。建議您部署來源對應，以善用這些優勢。[進一步瞭解來源對應](https://developer.chrome.com/docs/devtools/javascript/source-maps/)。"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "缺少大型第一方 JavaScript 的來源對應"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "大型 JavaScript 檔案缺少來源對應"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{警告：`.sourcesContent` 中缺少 1 個項目}other{警告：`.sourcesContent` 中缺少 # 個項目}}"}, "core/audits/valid-source-maps.js | title": {"message": "頁面包含有效的來源對應"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` 不但會針對流動裝置螢幕大小來優化應用程式，還能夠防止[使用者輸入時發生 300 毫秒的延遲](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。[進一步瞭解使用檢視區中繼標記](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)。"}, "core/audits/viewport.js | explanationNoTag": {"message": "找不到任何 `<meta name=\"viewport\">` 標籤"}, "core/audits/viewport.js | failureTitle": {"message": "缺少包括 `width` 或 `initial-scale` 的 `<meta name=\"viewport\">` 標籤"}, "core/audits/viewport.js | title": {"message": "具備包括 `<meta name=\"viewport\">` 或 `width` 的 `initial-scale` 標籤"}, "core/audits/work-during-interaction.js | description": {"message": "這是在「互動至下一個繪製」測量期間發生的執行緒封鎖工作。[進一步瞭解「互動至下一個繪製」數據](https://web.dev/inp/)。"}, "core/audits/work-during-interaction.js | displayValue": {"message": "事件「{interactionType}」已花費 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "事件目標"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "在重要互動時將工作減至最少"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "輸入延遲"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "螢幕畫面分享延遲"}, "core/audits/work-during-interaction.js | processingTime": {"message": "處理時間"}, "core/audits/work-during-interaction.js | title": {"message": "在重要互動時將工作減至最少"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "這些提示可協助改善 ARIA 在應用程式中的使用情況，進而提升輔助技術 (例如螢幕閱讀器) 使用者的體驗。"}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "這些審核結果建議您為音訊和影片提供替代內容。這或許能改善聽障或視障人士的使用體驗。"}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "音訊和影片"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "這些審核項目會提供常見的無障礙功能最佳做法。"}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "最佳做法"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "這些檢查會提供[網頁應用程式無障礙功能的改善建議](https://developer.chrome.com/docs/lighthouse/accessibility/)。系統只能自動偵測一部分的無障礙功能問題，因此建議您另行手動測試。"}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "這些審核項目會檢查自動化測試工具未涵蓋的區域。詳情請參閱[無障礙功能審查的執行指南](https://web.dev/how-to-review/)。"}, "core/config/default-config.js | a11yCategoryTitle": {"message": "無障礙功能"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "這些提示有助提高內容的易讀性。"}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "對比"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "您可以根據這些提示作出改善，讓其他地區的使用者更容易理解您的內容。"}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "國際化和本地化"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "這些提示可協助提高應用程式中的控制項語義品質。這或許能改善輔助技術 (例如螢幕閱讀器) 的使用體驗。"}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "名稱和標籤"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "這些審核結果可協助提高應用程式中的鍵盤瀏覽體驗。"}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "導覽"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "這些審核結果建議您運用輔助技術 (例如螢幕閱讀器)，改善表格或清單資料的閱讀體驗。"}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "表格和清單"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "瀏覽器兼容性"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "最佳做法"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "一般"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "信任與安全性"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "使用者體驗"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "您可根據效能預算設定網站效能的標準。"}, "core/config/default-config.js | budgetsGroupTitle": {"message": "預算"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "進一步瞭解應用程式效能。這些數字不會[直接影響](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)「效能」分數。"}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "診斷"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "評估效能的最重要指標在於像素在畫面上的呈現速度。關鍵數據：「首次內容繪製時間」、「首次有效繪製時間」"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "首次繪製改進"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "這些建議可加快網頁載入速度，但不會[直接影響](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)「效能」分數。"}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "優化建議"}, "core/config/default-config.js | metricGroupTitle": {"message": "數據"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "改善整體載入體驗，令網頁反應更靈敏快捷，盡快可供用戶使用。關鍵數據：可互動時間、速度指數"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "整體改進"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "效能"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "這些檢查項目可驗證漸進式網絡應用程式的不同層面。[瞭解良好的漸進式網頁應用程式有什麼特點](https://web.dev/pwa-checklist/)。"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "這些是基本 [PWA 檢查清單](https://web.dev/pwa-checklist/)規定的檢查項目，但 Lighthouse 不會自動檢查這些項目。它們不會影響您的分數，但請務必手動驗證這些項目。"}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA : 漸進式網頁應用程式 (PWA)"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "可安裝"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "已優化 PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "這些檢查可確保網頁符合基本搜尋引擎優化建議。這裡有很多其他因素不在 Lighthouse 評分範圍內，但仍可能會影響您的搜尋排名，包括[網站使用體驗核心指標報告](https://web.dev/learn-core-web-vitals/)的效能。[進一步瞭解 Google Search Essentials](https://support.google.com/webmasters/answer/35769)。"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "在您的網站上執行這些額外的驗證工具，以檢查其他 SEO 最佳做法。"}, "core/config/default-config.js | seoCategoryTitle": {"message": "搜尋引擎優化 (SEO)"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "請設定您的 HTML 格式，讓檢索器更能瞭解應用程式內容。"}, "core/config/default-config.js | seoContentGroupTitle": {"message": "關於內容的最佳做法"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "檢索器需要存取您的應用程式，網站才會在搜尋結果中顯示。"}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "檢索和加入索引"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "請確保您的頁面適合透過流動裝置瀏覽，讓使用者無需兩指縮放或放大螢幕即可閱讀網頁內容。[瞭解如何讓頁面適合流動裝置瀏覽](https://developers.google.com/search/mobile-sites/)。"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "適合透過流動裝置瀏覽"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "已測試裝置的 CPU 似乎比 Lighthouse 所預期的慢，因此可能會對您的效能分數產生負面影響。進一步瞭解[如何校準適當的 CPU 減速乘數](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)。"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "您的測試網址 ({requested}) 已重新導向至 {final}，因此頁面未必可以如預期載入。請嘗試直接測試第二個網址。"}, "core/gather/driver/navigation.js | warningTimeout": {"message": "頁面載入過慢，無法於時限內完成，結果可能並不完整。"}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "清除瀏覽器快取已逾時。請嘗試再次審核此頁面。如果問題仍未解決，請報告錯誤。"}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{此位置可能有儲存資料正在影響載入效能：{locations}。請透過無痕式視窗審核此頁面，以免這些資源影響分數。}other{這些位置可能有儲存資料正在影響載入效能：{locations}。請透過無痕式視窗審核此頁面，以免這些資源影響分數。}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "清除原始資料已逾時。請嘗試再次審核此頁面。如果問題仍未解決，請報告錯誤。"}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "只有透過 GET 要求載入的網頁才符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "只有狀態碼為 2XX 的網頁才可快取。"}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome 偵測到嘗試在快取中執行 JavaScript。"}, "core/lib/bf-cache-strings.js | appBanner": {"message": "要求 AppBanner 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "標記已停用向前/返回快取。如要在此裝置上啟用該功能，請前往 chrome://flags/#back-forward-cache。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "指令列已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "由於記憶體不足，因此系統已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "委派目標不支援向前/返回快取。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "已為預先轉譯器停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "網頁有包含已註冊監聽器的 BroadcastChannel 例項，因此系統無法快取網頁。"}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "包含 cache-control:no-store 標題的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "有人刻意清除快取。"}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "為了讓系統可快取其他網頁，此網頁已從快取中移除。"}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "包含外掛程式的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "使用 FileChooser API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "使用檔案系統存取 API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "使用媒體裝置調度工具的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "使用者離開網頁時，媒體播放器正在播放內容。"}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "使用 MediaSession API 並設定播放狀態的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "使用 MediaSession API 並設定動作處理常式的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "由於螢幕閱讀器的關係，系統已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "使用 SecurityHandler 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "使用序號 API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "使用 WebBluetooth API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "使用 WebUSB API 的網頁不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "使用專屬 Worker 或 Worklet 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "文件未在使用者離開前完成載入。"}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "使用者離開網頁時，系統會顯示「應用程式橫額」。"}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "使用者離開網頁時，系統會顯示 Chrome 密碼管理工具。"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "使用者離開網頁時，DOM distillation 正在處理中。"}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "使用者離開網頁時，系統會顯示 DOM Distiller Viewer。"}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "由於擴充程式使用訊息 API，因此系統已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "在儲存至往向前/返回快取前，可持續連線的擴充程式應中斷連線。"}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "可持續連線的擴充程式嘗試在向前/返回快取中傳送訊息給頁框。"}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "由於擴充程式的關係，系統已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "使用者離開網頁時，系統會在該頁面顯示強制回應對話方塊，例如重新提交表格或 HTTP 密碼對話框。"}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "使用者離開網頁時，系統會顯示離線頁面。"}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "使用者離開網頁時，系統會顯示 Out-Of-Memory Intervention。"}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "使用者離開網頁時，系統會要求權限。"}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "使用者離開網頁時，系統會顯示彈出式視窗封鎖器。"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "使用者離開網頁時，系統會顯示安全瀏覽詳細資料。"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "安全瀏覽功能將此網頁視為濫用，並已封鎖彈出式視窗。"}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Service Worker 已在網頁儲存於向前/返回快取時啟用。"}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "由於發生文件錯誤，向前/返回快取已停用。"}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "使用 FencedFrames 的頁面無法儲存在向前/返回快取之中。"}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "為了讓系統可快取其他網頁，此網頁已從快取中移除。"}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "授予媒體串流播放權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "使用入口網站的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | idleManager": {"message": "使用 IdleManager 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "包含開放式 IndexedDB 連線的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "使用了不符合資格的 API。"}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "由擴充程式插入 JavaScript 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "由擴充程式插入 StyleSheet 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | internalError": {"message": "內部錯誤。"}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "由於有 Keepalive 要求，因此系統已停用向前/返回快取。"}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "使用鍵盤鎖定的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | loading": {"message": "網頁在使用者離開前尚未完成載入。"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "主要資源包含 cache-control:no-cache 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "主要資源包含 cache-control:no-store 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "瀏覽操作已在網頁從向前/返回快取中還原前取消。"}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "由於有效的網路連線收到太多資料，因此網頁已從快取中移除。Chrome 會限制網頁可在快取時接收的資料量。"}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "包含 in-flight fetch() 或 XHR 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "有效的網絡要求涉及重新導向，因此網頁已從往返快取中移除。"}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "由於網絡連線開啟時間過長，因此網頁已從快取中移除。Chrome 會限制網頁可在快取時接收資料的時間長度。"}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "沒有有效回應標題的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "導覽已在主頁框以外的頁框中執行。"}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "網頁包含已建立索引的資料庫所執行的進行中交易，目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "包含 in-flight 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "正在傳送擷取網絡要求的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "包含 in-flight 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "正在傳送 XHR 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "使用 PaymentManager 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "使用「畫中畫」的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | portal": {"message": "使用入口網站的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | printing": {"message": "顯示列印使用者介面的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "網頁已透過「`window.open()`」開啟且其他分頁含有該網頁的參照內容，或網頁已開啟視窗。"}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "向前/返回快取中網頁的轉譯器處理程序已當機。"}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "向前/返回快取中網頁的轉譯器處理程序已中斷。"}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "要求音訊擷取權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "已要求感應器權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "要求背景同步處理或擷取權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "要求 MIDI 權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "要求通知權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "要求儲存空間存取權的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "要求影片擷取權限的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "只能快取網址配置為 HTTP/HTTPS 的網頁。"}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "在網頁處於向前/返回快取時，由 Service Worker 聲明擁有權。"}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Service Worker 已嘗試向於向前/返回快取中的網頁傳送 `MessageEvent`。"}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Service Worker 已在網頁儲存於向前/返回快取時取消註冊。"}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "由於系統已啟用 Service Worker，因此網頁已從向前/返回快取中移除。"}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome 已重新啟動，並清除向前/返回快取項目。"}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "使用 SharedWorker 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "使用 SpeechRecognizer 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "使用 SpeechSynthesis 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "網頁上 iframe 啟動的導覽並未完成。"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "子資源包含 cache-control:no-cache 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "子資源包含 cache-control:no-store 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | timeout": {"message": "網頁已超出向前/返回快取中的時間上限，因此已過期。"}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "將網頁儲存至向前/返回快取時已逾時 (可能是由於網頁隱藏事件處理常式的執行時間太長所致)。"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "網頁的主頁框中有卸載處理常式。"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "網頁在子頁框中含有卸載處理常式。"}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "瀏覽器已變更用戶代理程式覆寫標題。"}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "授權錄製影片或音效的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "使用 WebDatabase 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webHID": {"message": "使用 WebHID 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webLocks": {"message": "使用 WebLocks 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webNfc": {"message": "使用 WebNfc 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "使用 WebOTPService 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webRTC": {"message": "使用 WebRTC 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | webShare": {"message": "使用 WebShare 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/bf-cache-strings.js | webSocket": {"message": "使用 WebSocket 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | webTransport": {"message": "使用 WebTransport 的網頁無法儲存至向前/返回快取。"}, "core/lib/bf-cache-strings.js | webXR": {"message": "使用 WebXR 的網頁目前不符合向前/返回快取的資格。"}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "建議新增 https: 和 http: 網址配置，以便退回兼容舊版瀏覽器 (支援「strict-dynamic」的瀏覽器會忽略這些架構)。"}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener 已於 CSP3 淘汰，請改用 Cross-Origin-Opener-Policy 標題。"}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer 已於 CSP2 淘汰，請改用 Referrer-Policy 標題。"}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss 已於 CSP2 淘汰，請改用 X-XSS-Protection 標題。"}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "如果遺漏 base-uri，有心人士便可透過已插入的 <base> 標籤將所有相對網址 (例如指令碼) 的基底網址設定為攻擊者控制的網域。建議將 base-uri 指定為「none」或「self」。"}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "缺少 object-src 會容許有心人士插入會執行不安全指令碼的外掛程式。建議您盡可能將 object-src 設為「none」。"}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "缺少 script-src 指令。這可能導致系統執行不安全的指令碼。"}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "忘記使用分號嗎？{keyword} 似乎是指令，而不是關鍵字。"}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "nonce 應使用 base64 字元集。"}, "core/lib/csp-evaluator.js | nonceLength": {"message": "nonce 的長度至少要有 8 個字元。"}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "請避免在此指令之中使用純網址配置 ({keyword})。純網址配置會容許有心人士從不安全的網域取得指令碼。"}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "請避免在此指令中使用純萬用字元 ({keyword})。純萬用字元會容許有心人士從不安全的網域取得指令碼。"}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "報告目的地只能透過 report-to 指令設定。由於這項指令只受到以 Chromium 為基礎的瀏覽器支援，因此建議同時使用 report-uri 指令。"}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "沒有 CSP 設定報告目的地。這會導致系統難以持續維護 CSP 及監察故障情況。"}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "由於主機許可名單經常被忽略，建議您改為使用 CSP nonce 或 hash，如有需要，請同時使用「strict-dynamic」。"}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "CSP 指令不明。"}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} 似乎是無效關鍵字。"}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "「unsafe-inline」會允許執行不安全的頁面內嵌指令碼和事件處理常式。建議使用 CSP nonce 或 hash 逐一允許指令碼。"}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "建議新增「unsafe-inline」，以便退回兼容舊版瀏覽器 (支援 nonce/hash 的瀏覽器會忽略 unsafe-inline)。"}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "CORS `Access-Control-Allow-Headers` 處理操作中的萬用字元符號 (*) 不包含授權。"}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "系統將封鎖網址同時包含已移除的空白 `(n|r|t)` 字元和小於字元 (`<`) 的資源要求。請從元素屬性值等位置移除換行符號，並編碼小於字元，以便載入這些資源。"}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` 已淘汰，請改用標準化 API：Navigation Timing 2。"}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` 已淘汰，請改用標準化 API：Paint Timing。"}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` 已淘汰，請改用標準化 API：Navigation Timing 2 中的 `nextHopProtocol`。"}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "系統將會拒絕包含 `(0|r|n)` 字元的 Cookie，而非縮短。"}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "透過設定 `document.domain` 啟用相同來源政策的功能已淘汰，並將根據預設移除。此淘汰警告是針對透過設定 `document.domain` 來啟用跨來源存取功能。"}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "從跨來源 iframe 觸發 {PH1} 的功能已淘汰，並將會在日後移除。"}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "應使用 `disableRemotePlayback` 屬性停用預設的「投放整合」功能，而非 `-internal-media-controls-overlay-cast-button` 選取器。"}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} 已淘汰。請改用 {PH2}。"}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "此例子是經翻譯的淘汰問題訊息。"}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "透過設定 `document.domain` 啟用相同來源政策的功能已淘汰，並將根據預設移除。如要繼續使用此功能，請傳送文件和頁框的 `Origin-Agent-Cluster: ?0` 標題和 HTTP 回應，藉此選擇不採用 origin-keyed 代理程式叢集。詳情請參閱 https://developer.chrome.com/blog/immutable-document-domain/。"}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` 已淘汰，並將會移除。請改用 `Event.composedPath()`。"}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "`Expect-CT` 標題已淘汰，並將會移除。Chrome 要求 2018 年 4 月 30 日後發出的所有公開信任憑證都必須符合《憑證透明度政策》的規定。"}, "core/lib/deprecations-strings.js | feature": {"message": "詳情請參閱「功能狀態」頁面。"}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` 和 `watchPosition()` 無法再在不安全來源上運作。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "不安全來源上的 `getCurrentPosition()` 和 `watchPosition()` 已淘汰。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` 無法再在不安全來源上運作。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` 已淘汰。請改用 `RTCPeerConnectionIceErrorEvent.address` 或 `RTCPeerConnectionIceErrorEvent.port`。"}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "「`canmakepayment`」Service Worker 事件的商家來源和任意資料目前已淘汰，並將在之後移除：`topOrigin`、`paymentRequestOrigin`、`methodData`、`modifiers`。"}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "網站根據使用者具備權限的網絡位置，透過只能存取的網絡要求一個子資源。這些要求會向互聯網暴露非公開的裝置和伺服器，因而增加偽造跨網站要求 (CSRF) 攻擊和/或資料洩漏的風險。為降低風險，Chrome 會忽略不安全內容向非公開子資源發出的要求，並將開始封鎖這類要求。"}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "除非 CSS 以 `.css` 副檔名作結，否則無法從 `file:` 網址載入。"}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "由於規格變更，系統已淘汰使用 `SourceBuffer.abort()` 取消 `remove()` 的非同步範圍移除操作，日後亦將停止支援此功能。建議您改為監聽 `updateend` 事件。`abort()` 的用途只限於取消非同步媒體附加內容或重設剖析器狀態。"}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "由於規格變更，系統已淘汰將 `MediaSource.duration` 設定為低於任何緩衝編碼頁框的最高顯示時間戳記。日後將停止支援對已縮短緩衝媒體的隱含移除操作。請改為在 `newDuration < oldDuration` 的所有 `sourceBuffers` 上執行明確 `remove(newDuration, oldDuration)`。"}, "core/lib/deprecations-strings.js | milestone": {"message": "此變更將會在第 {milestone} 個里程碑時生效。"}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "即使在 `MIDIOptions` 中未指定系統專用 (SysEx) 訊息，Web MIDI 亦會要求使用權限。"}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "系統可能無法再使用不安全來源的 Notification API。請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "系統可能無法再透過跨來源 iframe 要求 Notification API。請考慮透過頂層頁框要求權限，或改為開啟新視窗。"}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "您的合作夥伴正在交涉已過時的傳輸層安全標準 (TLS)/DTLS 版本。請向您的合作夥伴查詢，以便修正問題。"}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "不安全環境中的 WebSQL 已淘汰，並即將移除。請使用「網絡儲存空間」或「已加入索引的資料庫」。"}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "如果指定 img、video 和 canvas 標籤的「`overflow: visible`」，可能會導致這些標籤產生的視覺內容超出元素邊界。詳情請參閱 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` 已淘汰。請改用付款處理常式的即時安裝方法。"}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "您的「`PaymentRequest`」調用已繞過內容安全政策 (CSP)「`connect-src`」指令，但目前已無法再繞過。請將 `PaymentRequest` API 的付款方法識別碼 (位於「`supportedMethods`」欄位) 新增至 CSP「`connect-src`」指令。"}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` 已淘汰。請改用標準化的 `navigator.storage`。"}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "具有 `<picture>` 父項的 `<source src>` 無效，因此系統會忽略。請改用「`<source srcset>`」。"}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` 已淘汰。請改用標準化的 `navigator.storage`。"}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "系統將封鎖網址包含嵌入憑證 (例如 `**********************/`) 的子資源要求。"}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "已移除 `DtlsSrtpKeyAgreement` 限制。系統將您為此限制指定的 `false` 值解讀為嘗試使用已移除的「`SDES key negotiation`」方法。此功能已移除，請改用支援「`DTLS key negotiation`」的方法。"}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "已移除 `DtlsSrtpKeyAgreement` 限制。您為此限制指定的 `true` 值已不再適用，不過您可將此限制移除以保持畫面整潔。"}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "已偵測到「`Complex Plan B SDP`」。系統已不再支援「`Session Description Protocol`」的方言。請改用「`Unified Plan SDP`」。"}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "使用 `{sdpSemantics:plan-b}` 建構 `RTCPeerConnection` 時使用「`Plan B SDP semantics`」是「`Session Description Protocol`」舊款非標準版的做法，現已從「網絡平台」中永久移除。雖然使用 `IS_FUCHSIA` 建構時仍可使用此做法，但我們打算盡快刪除，因此請停止使用此功能。請參閱 https://crbug.com/1302249 瞭解狀態。"}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` 選項已淘汰，並將會移除。"}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` 會要求跨來源隔離。詳情請參閱 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "無需使用者啟用即可呼叫 `speechSynthesis.speak()` 的功能已淘汰，並將會移除。"}, "core/lib/deprecations-strings.js | title": {"message": "使用已淘汰的功能"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "擴充程式應選擇接受跨來源隔離功能，以繼續使用 `SharedArrayBuffer`。詳情請參閱 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} 為供應商專用功能，請改用標準 {PH2}。"}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "`XMLHttpRequest` 中的 JSON 回應不支援 UTF-16。"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主要執行緒上同步的 `XMLHttpRequest` 會對使用者體驗造成負面影響，因此已淘汰。如需進一步說明，請查看 https://xhr.spec.whatwg.org/。"}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` 已淘汰。請改用 `isSessionSupported()` 並查看解析的布林值。"}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "主要執行緒封鎖時間"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "快取 TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "說明"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "時間長度"}, "core/lib/i18n/i18n.js | columnElement": {"message": "元素"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "審核失敗的元素"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "位置"}, "core/lib/i18n/i18n.js | columnName": {"message": "名稱"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "超出預算"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "要求"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "資源大小"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "資源類型"}, "core/lib/i18n/i18n.js | columnSize": {"message": "大小"}, "core/lib/i18n/i18n.js | columnSource": {"message": "來源"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "開始時間"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "所用的時間"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "傳輸大小"}, "core/lib/i18n/i18n.js | columnURL": {"message": "網址"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "可節省的數據用量"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "可節省的數據用量"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "可節省 {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{找到 1 個元素}other{找到 # 個元素}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "可減少 {wastedMs, number, milliseconds} 毫秒"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "文件"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "首次有效繪製時間"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "字型"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "圖片"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "互動至「下一個繪製」"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "高"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "低"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "中"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "首次輸入延遲時間最長預計值"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "媒體"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} 毫秒"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "其他"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "其他資源"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "指令碼"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 秒"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "樣式表"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "第三方"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "總計"}, "core/lib/lh-error.js | badTraceRecording": {"message": "追蹤記錄網頁載入情況時發生錯誤。請重新執行 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "等待「Debugger 通訊協定」初始連線時逾時。"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome 在網頁載入期間未能收集任何螢幕擷取畫面。請確認網頁上有可見內容，然後嘗試重新執行 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS 伺服器無法解析您提供的網域。"}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "必要的 {artifactName} 收集程式發生錯誤：{errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Chrome 發生內部錯誤。請重新啟動 Chrome，並嘗試重新執行 Lighthouse。"}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "未執行必要的 {artifactName} 收集程式。"}, "core/lib/lh-error.js | noFcp": {"message": "頁面未套用任何內容。請確保在載入期間將瀏覽器視窗保持在前景，然後再試一次。({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "此網頁顯示的內容不符合最大內容繪製 (LCP) 的資格，請確保網頁包含有效的 LCP 元素，然後再試一次。({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "提供的頁面並非 HTML (MIME 類 {mimeType})。"}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "這個版本的 Chrome 太舊，無法支援「{featureName}」。請使用較新版本的 Chrome 以便查看完整結果。"}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。"}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "您要求的網頁已停止回應，因此 Lighthouse 無法穩定載入該網址。"}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "您提供的網址並無有效的安全憑證。{securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome 使用插頁式畫面阻止系統載入網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。"}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。(詳情：{errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。(狀態代碼：{statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "網頁的載入時間過長。請按照報告中的建議縮短網頁的載入時間，並嘗試重新啟動 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "等待 DevTools 通訊協定回應的時間超出系統分配上限。(方法：{protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "擷取資源內容的時間超出系統分配上限。"}, "core/lib/lh-error.js | urlInvalid": {"message": "您提供的網址無效。"}, "core/lib/navigation-error.js | warningXhtml": {"message": "網頁 MIME 類型為 XHTML：Lighthouse 未明確支援此文件類型"}, "core/user-flow.js | defaultFlowName": {"message": "使用者流量 ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "導覽報告 ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "快覽報告 ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "時間範圍報告 ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "全部報告"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "類別"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "無障礙功能"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "最佳做法"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "效能"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "漸進式網絡應用程式"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "搜尋引擎優化 (SEO)"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "桌面電腦"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "瞭解 Lighthouse 流程報告"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "解讀流程"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "使用「導覽」報告來…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "使用「快覽」報告來…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "使用「時間範圍」報告來…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "取得 Lighthouse 效能分數。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "測量頁面載入效能數據，例如「最大內容繪製」和「速度指數」。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "存取「漸進式網頁應用程式」功能。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "在單頁應用程式或複合式表格中尋找無障礙功能問題。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "評估互動背後安排選單和使用者介面元素的最佳做法。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "測量一連串互動的版面配置轉移和 JavaScript 執行時間。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "探索效能優化建議，以便改善永久頁面和單頁應用程式的使用體驗。"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "最大影響"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} 項資訊型審核}other{{numInformative} 項資訊型審核}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "流動裝置"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "網頁載入"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "「導覽」報告會分析單次網頁載入情況，方式與原來的 Lighthouse 報告完全相同。"}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "導覽報告"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} 項導覽報告}other{{numNavigation} 項導覽報告}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} 項可通過的審核}other{{numPassableAudits} 項可通過的審核}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{已通過 {numPassed} 項審核}other{已通過 {numPassed} 項審核}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "一般"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "錯誤"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "欠佳"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "良好"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "儲存"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "已採集網頁狀態"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "「快覽」報告會分析處於特定狀態 (通常是在使用者進行互動之後) 的頁面。"}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "快覽報告"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} 項快覽報告}other{{numSnapshot} 項快覽報告}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "摘要"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "用戶的互動行為"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "「時間範圍」報告會分析任意一個時段 (通常包含使用者互動)。"}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "時間範圍報告"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} 項時間範圍報告}other{{numTimespan} 項時間範圍報告}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse 用戶流程報告"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "當動畫內容在顯示螢幕之外時，您可以使用 [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) 標籤來節省 CPU 用量。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "建議以 WebP 格式顯示您的所有 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 組件，同時為其他瀏覽器指定適合的備用圖片。[瞭解詳情](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "請確保您正在使用 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)，讓圖片自動延遲載入。[瞭解詳情](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "使用 [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) 等工具在[伺服器端輸出 AMP 版面配置](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "請參閱 [AMP 文件](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)，確保所有樣式均受支援。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 組件支援 [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) 屬性，可根據螢幕大小指定要使用的圖片資產。[瞭解詳情](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "如果要輸出很大型的清單，建議使用組件開發套件 (CDK) 進行虛擬捲動。[瞭解詳情](https://web.dev/virtualize-lists-with-angular-cdk/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "套用[路線層級的程式碼分割](https://web.dev/route-level-code-splitting-in-angular/)，盡可能降低您的 JavaScript 套裝大小。此外，建議使用 [Angular Service Worker](https://web.dev/precaching-with-the-angular-service-worker/) 預先快取資產。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "如果您使用 Angular CLI，請確保在正式版本的模式中產生版本。[瞭解詳情](https://angular.io/guide/deployment#enable-runtime-production-mode)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "如果您使用 Angular CLI，請在正式版本中包含來源對應以檢查套裝。[瞭解詳情](https://angular.io/guide/deployment#inspect-the-bundles)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "預先載入路線以加快導航速度。[瞭解詳情](https://web.dev/route-preloading-in-angular/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "建議使用組件開發套件 (CDK) 中的 `BreakpointObserver` 公用程式來管理圖片中斷點。[瞭解詳情](https://material.angular.io/cdk/layout/overview)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "建議將 GIF 上載至可將 GIF 用作 HTML5 影片嵌入的伺服器。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "在主題中定義自訂字體時，請指定 `@font-display`。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "請考慮在網站上設定[含有 Convert 圖片樣式的 WebP 圖片格式](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "請安裝可以延遲載入圖片的 [Drupal 模組](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search)。這些模組可延遲載入所有畫面外的圖片，進而提升效能。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "建議使用模組來內嵌重要的 CSS 和 JavaScript，或透過[進階 CSS/JS 彙整](https://www.drupal.org/project/advagg)模組等 JavaScript 來非同步載入資產。請注意，此模組提供的優化可能令網站無法正常運作，因此您可能需要修改程式碼。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "主題、模組和伺服器規格均會影響伺服器回應時間。建議尋找更優化的主題、謹慎選擇優化模組和/或升級伺服器。您的代管伺服器應使用 PHP opcode 快取、記憶體快取來降低資料庫查詢時間 (例如 Redis 或 Memcached)，還可優化應用程式邏輯以提升頁面載入速度。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "建議使用[回應式圖片樣式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)來縮減頁面上載入圖片的大小。如果您使用 Views 在頁面上顯示多個內容項目，建議透過分頁來控制特定頁面上顯示的內容項目數量。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "請確保您已啟用「管理 » 設定 » 開發」頁面上的「彙整 CSS 檔案」。您亦可透過[額外模組](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search)設定更進階的彙整選項，從而透過串連、縮小及壓縮 CSS 樣式來提升網站速度。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "請確保您已啟用「管理 » 設定 » 開發」頁面上的「彙整 JavaScript 檔案」。您亦可透過[額外模組](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search)設定更進階的彙整選項，從而透過串連、縮小及壓縮 JavaScript 資產來提升網站速度。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "建議移除未使用的 CSS 規則，並只將需要的 Drupal 程式庫附加至相關頁面或頁面內的組件。查看 [Drupal 文件連結](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)即可瞭解詳情。如要找出會新增多餘 CSS 的附加程式庫，請嘗試在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可在 Drupal 網站上停用 CSS 彙整時，透過樣式表網址找出有問題的主題/模組。請留意清單中包含很多樣式表，且程式碼覆蓋率中有大量紅色標示的主題/模組。主題/模組應只將網頁上實際使用的樣式表加入清單。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "建議移除未使用的 JavaScript 資產，並只將需要的 Drupal 程式庫附加至相關頁面或頁面內的組件。查看 [Drupal 文件連結](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)即可瞭解詳情。如要找出會新增多餘 JavaScript 的附加程式庫，請嘗試在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可在 Drupal 網站上停用 JavaScript 彙整時，透過指令碼網址找出有問題的主題/模組。請留意清單中包含很多指令碼，且程式碼覆蓋率中有大量紅色標示的主題/模組。主題/模組應只將網頁上實際使用的指令碼加入清單。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "在「管理 » 設定 » 開發」頁面中設定「瀏覽器和 Proxy 快取最大期限」。讀取 [Drupal 快取並優化效能](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "建議使用可自動優化並縮減透過網站上載的圖片大小，同時能維持品質的[模組](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)。此外，請確保網站上所有輸出的圖片都使用 Drupal 內置的[回應式圖片樣式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (於 Drupal 8 及以上版本提供)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "您可透過安裝和設定為用戶代理程式資源提示提供設施的[模組](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)，新增預先連接或預先擷取 DNS 的資源提示。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "請確保您正在使用 Drupal 內置的[回應式圖片樣式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (於 Drupal 8 及以上版本提供)。由檢視模式、檢視畫面或透過 WYSIWYG 編輯器上載的圖片輸出圖片欄位時，請使用回應式圖片樣式。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Optimize Fonts` 以自動利用 `font-display` CSS 功能，確保系統在載入網站頁字型時使用者可以看到文字。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Next-Gen Formats` 以將圖片轉換為 WebP。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Lazy Load Images` 以延遲載入螢幕關閉圖片，直到需要時才載入。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Critical CSS` 和 `Script Delay` 以延遲不重要的 JS/CSS。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "使用 [Ezoic Cloud 快取](https://pubdash.ezoic.com/speed/caching)，以便在我們的全球網絡上快取您的內容，改善載入首個字節的時間。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Minify CSS` 以自動壓縮 CSS 來減少網絡負載大小。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Minify Javascript` 以自動壓縮 JS 來減少網絡負載大小。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Remove Unused CSS` 以協助解決此問題。此功能會識別在您網站的每個頁面上實際使用的 CSS 類別，並移除任何其他類別，以維持小的檔案大小。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Efficient Static Cache Policy` 以便在快取標題中為靜態資產設定建議的值。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Next-Gen Formats` 以將圖片轉換為 WebP。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Pre-Connect Origins` 以自動新增 `preconnect` 資源提示，及早連線至重要的第三方來源。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Preload Fonts` 和 `Preload Background Images` 來加入 `preload` 連結，以便優先擷取目前在網頁載入時較後要求的資源。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 並啟用 `Resize Images` 以將圖片大小調整至適合裝置的尺寸，減少網絡負載大小。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "建議將 GIF 上載至可將 GIF 用作 HTML5 影片嵌入的伺服器。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "建議使用會自動將已上載圖片轉換成最佳格式的[外掛程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp)或服務。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "安裝可延遲載入所有畫面外圖片的[延遲載入 Joomla 外掛程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading)，或改用提供此功能的範本。從 Joomla 4.0 版本開始，所有新圖片將[自動](https://github.com/joomla/joomla-cms/pull/30748)從核心程式取得 `loading` 屬性。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "部分 Joomla 外掛程式可助您[內嵌重要資產](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)或[延後載入較不重要的資源](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)。請注意，這些外掛程式的優化設定可能影響範本或外掛程式的功能，因此您需要全面測試這些功能。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "範本、擴充程式和伺服器規格均會影響伺服器回應時間。建議尋找更優化的範本、謹慎選擇優化擴充程式和/或升級伺服器。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "建議您在文章類別中顯示摘錄 (例如透過「閱讀更多」連結)、減少特定頁面顯示的文章數量、將較長的文章分為多個頁面，或使用可延遲載入留言的外掛程式。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "部分 [Joomla 擴充程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)可透過串連、縮小及壓縮 CSS 樣式來提升網站速度。部分範本亦提供此功能。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "部分 [Joomla 擴充程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)可透過串連、縮小及壓縮指令碼來提升網站速度。部分範本亦提供此功能。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "部分 [Joomla 擴充程式](https://extensions.joomla.org/)會在網頁中載入未使用的 CSS，建議您減少這類擴充程式的數量，或改用其他擴充程式。如要找出會新增多餘 CSS 的擴充程式，請嘗試在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可透過樣式表網址找出有問題的主題/外掛程式。請留意清單中包含很多樣式表，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只將網頁上實際使用的樣式表加入清單。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "部分 [Joomla 擴充程式](https://extensions.joomla.org/)會在網頁中載入未使用的 JavaScript，建議您減少這類擴充程式的數量，或改用其他擴充程式。如要找出會新增多餘 JS 的外掛程式，請嘗試在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可透過指令碼網址找出有問題的擴充程式。請留意清單中包含很多指令碼，且程式碼覆蓋率中有大量紅色標示的擴充程式。擴充程式應只將網頁上實際使用的指令碼加入清單。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "瞭解 [Joomla 的瀏覽器快取功能](https://docs.joomla.org/Cache)。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "建議使用[圖片優化外掛程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)，在壓縮圖片時保持畫質。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "建議使用[回應式圖片外掛程式](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)，以在內容中使用回應式圖片。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "您可透過啟用 Joomla 中的 Gzip 頁面壓縮 ([系統] > [通用設定] > [伺服器]) 來啟用文字壓縮功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "如果您尚未建立自己的 JavaScript 資產套件，建議使用 [baler](https://github.com/magento/baler)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "停用 Magento 內置的 [JavaScript 組合及壓縮功能](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)，並建議改用 [baler](https://github.com/magento/baler/)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "在[定義自訂字體](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)時指定 `@font-display`。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "建議在 [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) 搜尋更多第三方擴充程式，以便運用較新的圖片格式。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "建議修改產品和目錄範本，以使用網絡平台的[延遲載入](https://web.dev/native-lazy-loading)功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "使用 Magento 的 [Varnish 整合](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "在商店的開發人員設定中啟用「壓縮 CSS 檔案」選項。[瞭解詳情](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "使用 [Terser](https://www.npmjs.com/package/terser) 來壓縮來自靜態內容部署的所有 JavaScript 資產，並停用內置的壓縮功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "停用 Magento 的內置 [JavaScript 組合功能](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "建議在 [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) 搜尋更多第三方擴充程式，以優化圖片。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "您可透過[修改主題版面配置](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)，新增預先連接或預先擷取 DNS 的資源提示。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "您可透過[修改主題版面配置](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)來新增 `<link rel=preload>` 標籤。"}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "請使用 `next/image` 組件讓系統自動優化圖片格式，而非 `<img>`。[瞭解詳情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "如要自動延遲載入圖片，請使用 `next/image` 組件，而非 `<img>`。[瞭解詳情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "使用 `next/image` 組件並將「priority」設定為 True 以預先載入 LCP 圖片。[瞭解詳情](https://nextjs.org/docs/api-reference/next/image#priority)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "請使用 `next/script` 組件延遲載入非關鍵的第三方指令碼。[瞭解詳情](https://nextjs.org/docs/basic-features/script)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "請使用 `next/image` 組件確保圖片一律會調整成適當大小。[瞭解詳情](https://nextjs.org/docs/api-reference/next/image#width)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "建議您在 `Next.js` 設定中設定 `PurgeCSS` 以從樣式表中移除未使用的規則。[瞭解詳情](https://purgecss.com/guides/next.html)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "使用 `Webpack Bundle Analyzer` 偵測未使用的 JavaScript 程式碼。[瞭解詳情](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "建議您使用 `Next.js Analytics` 評估應用程式的實際效能。[瞭解詳情](https://nextjs.org/docs/advanced-features/measuring-performance)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "請設定不可變動資產和 `Server-side Rendered` (SSR) 頁面的快取。[瞭解詳情](https://nextjs.org/docs/going-to-production#caching)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "請使用 `next/image` 組件調整圖片品質，而非 `<img>`。[瞭解詳情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "請使用 `next/image` 組件設定適當的 `sizes`。[瞭解詳情](https://nextjs.org/docs/api-reference/next/image#sizes)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "請為您的 Next.js 伺服器啟用壓縮功能。[瞭解詳情](https://nextjs.org/docs/api-reference/next.config.js/compression)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "請使用 `nuxt/image` 組件並設定 `format=\"webp\"`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#format)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "如要延遲載入畫面外圖片，請使用 `nuxt/image` 組件並設定 `loading=\"lazy\"`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#loading)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "請使用 `nuxt/image` 組件，並為 LCP 圖片指定`preload`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#preload)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "請使用 `nuxt/image` 組件，並明確指定`width`和`height`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#width--height)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "請使用 `nuxt/image` 組件設定適當的`quality`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#quality)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "請使用 `nuxt/image` 組件設定適當的`sizes`。[瞭解詳情](https://image.nuxtjs.org/components/nuxt-img#sizes)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[以影片取代動態 GIF](https://web.dev/replace-gifs-with-videos/) 以加快網頁載入速度，並考慮使用新型檔案格式，例如 [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) 或 [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)，以將壓縮效率提升 30% 以上 (與目前最先進的影片編解碼器 VP9 相比)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "建議使用會自動將已上載圖片轉換成最佳格式的[外掛程式](https://octobercms.com/plugins?search=image)或服務。[WebP 無損圖片](https://developers.google.com/speed/webp)的尺寸比 PNG 小 26%，並且在相等 SSIM 畫質指標下，比相類的 JPEG 圖片小 25-34%。其他建議使用的 next-gen 圖像格式包括 [AVIF](https://jakearchibald.com/2020/avif-has-landed/)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "建議安裝可延遲載入所有畫面外圖片的[圖片延遲載入外掛程式](https://octobercms.com/plugins?search=lazy)，或改用提供此功能的主題。您亦可考慮使用 [AMP 外掛程式](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "很多外掛程式都可助您[內嵌重要資產](https://octobercms.com/plugins?search=css)。這些外掛程式可能會破壞其他外掛程式，因此您需要全面測試。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "主題、外掛程式和伺服器規格均會影響伺服器回應時間。建議尋找更優化的主題、謹慎選擇優化外掛程式及/或升級伺服器。October CMS 亦可讓開發人員使用 [`Queues`](https://octobercms.com/docs/services/queues) 來延遲處理耗時工作，例如傳送電郵，從而大幅提升網絡要求的速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "建議在文章清單中顯示摘錄 (例如使用 `show more` 按鈕)、減少特定頁面顯示的文章數量、將較長的文章分為多個頁面，或使用可延遲載入留言的外掛程式。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "很多[外掛程式](https://octobercms.com/plugins?search=css)可以透過串連、縮小及壓縮樣式來提升網站速度。建立流程以直接進行此壓縮操作，可以提升開發速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "很多[外掛程式](https://octobercms.com/plugins?search=javascript)可以透過串連、縮小及壓縮指令碼來提升網站速度。建立流程以直接進行此壓縮操作，可以提升開發速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "建議檢查會在網站中載入未啟用 CSS 的[外掛程式](https://octobercms.com/plugins)。如要找出會新增不必要 CSS 的外掛程式，請在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。透過樣式表網址找出有問題的主題/外掛程式。請留意包含很多外掛程式，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只新增網頁上實際使用的樣式表。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "請檢查會在網頁中載入未啟用 JavaScript 的 [外掛程式](https://octobercms.com/plugins?search=javascript)。如要找出會新增不必要 JavaScript 的外掛程式，請在 Chrome DevTools 中執行[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。透過指令碼網址找出有問題的主題/外掛程式。請留意包含很多指令碼，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只新增網頁上實際使用的指令碼。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "瞭解[如何透過 HTTP 快取防止不必要的網絡要求](https://web.dev/http-cache/#caching-checklist)。您可以使用很多[外掛程式](https://octobercms.com/plugins?search=Caching)來提升快取速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "建議使用[圖片優化外掛程式](https://octobercms.com/plugins?search=image)，在壓縮圖片時保持畫質。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "您可直接透過媒體管理工具上載圖片，確保可使用所需的圖片大小。建議使用[調整大小篩選器](https://octobercms.com/docs/markup/filter-resize)或[調整圖片大小外掛程式](https://octobercms.com/plugins?search=image)，確保使用優化的圖片大小。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "在網絡伺服器設定中啟用文字壓縮功能。"}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "如果您要在網頁上輸出很多重複的元素，建議使用 `react-window` 等「視窗化」程式庫來壓縮建立的 DOM 節點數量。[瞭解詳情](https://web.dev/virtualize-long-lists-react-window/)。此外，如果您使用 `Effect` 鈎子 (Hook) 來改善執行階段的效能，請僅在已變更特定依賴性時才使用 [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action)、[`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) 或 [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) 和[略過效果](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects)，藉此減少不必要的重新輸出。"}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "如果使用 React Router，請盡量避免在[路線導航](https://reacttraining.com/react-router/web/api/Redirect)使用 `<Redirect>` 組件。"}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "如果您在伺服器端輸出任何 React 組件，建議使用 `renderToPipeableStream()` 或 `renderToStaticNodeStream()`，允許用戶端接收並混合標記的不同部分，而非同時完成。[瞭解詳情](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "如果您建立自動壓縮 CSS 檔案的系統，請確定部署的應用程式為正式版本。您可以使用 React Developer Tools 擴充程式執行這項檢查。[瞭解詳情](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "如果您建立自動壓縮 JS 檔案的系統，請確定部署的應用程式為正式版本。您可以使用 React Developer Tools 擴充程式執行這項檢查。[瞭解詳情](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "如果沒有使用伺服器端輸出，請以 `React.lazy()` [分割您的 JavaScript 套件](https://web.dev/code-splitting-suspense/)。否則請使用[可載入的組件](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/) 等第三方程式庫分割程式碼。"}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "使用 React DevTools Profiler，採用效能分析 API 來測量組件的輸出效能。[瞭解詳情。](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "建議將 GIF 上載至可將 GIF 用作 HTML5 影片嵌入的伺服器。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "如果支援，建議您使用 [Performance Lab](https://wordpress.org/plugins/performance-lab/) 外掛程式，將上載的 JPEG 圖片自動轉換成 WebP。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "安裝可延遲載入所有畫面外圖片的[延遲載入 WordPress 外掛程式](https://wordpress.org/plugins/search/lazy+load/)，或改用提供此功能的主題。您亦可考慮使用 [AMP 外掛程式](https://wordpress.org/plugins/amp/)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "部分 WordPress 外掛程式可助您[內嵌重要資產](https://wordpress.org/plugins/search/critical+css/)或[延後載入較不重要的資源](https://wordpress.org/plugins/search/defer+css+javascript/)。請注意，這些外掛程式的優化設定可能影響主題或外掛程式的功能，因此您可能需要變更程式碼。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "主題、外掛程式和伺服器規格均會影響伺服器回應時間。建議尋找更優化的主題、謹慎選擇優化外掛程式和/或升級伺服器。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "建議在文章清單中顯示摘錄 (例如加入更多標籤)、減少特定頁面顯示的文章數量、將較長的文章分為多個頁面，或使用可延遲載入留言的外掛程式。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/search/minify+css/) 可以透過串連、縮小及壓縮樣式來提升網站速度。可以的話，您亦可建立流程來直接進行此壓縮操作。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/search/minify+javascript/) 可透過串連、縮小及壓縮指令碼來提升網站速度。可以的話，您亦可建立流程來直接進行此壓縮操作。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/)會在網頁中載入未使用的 CSS，建議你減少這類外掛程式的數量，或改用其他外掛程式。如要找出會新增多餘 CSS 的外掛程式，請嘗試在 Chrome DevTools 中執[程式碼覆蓋率](https://developer.chrome.com/docs/devtools/coverage/)。您可透過樣式表網址找出有問題的主題/外掛程式。請留意清單中包含很多樣式表，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只將網頁上實際使用的樣式表加入清單。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/)會在網頁中載入未使用的 JavaScript，建議你減少這類外掛程式的數量，或改用其他外掛程式。如要找出會新增多餘 JS 的外掛程式，請嘗試在 Chrome DevTools 中執[程式碼覆蓋率](https://developer.chrome.com/docs/devtools/coverage/)。您可透過指令碼網址找出有問題的主題/外掛程式。請留意清單中包含很多指令碼，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只將網頁上實際使用的指令碼加入清單。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "瞭解 [WordPress 的瀏覽器快取功能](https://wordpress.org/support/article/optimization/#browser-caching)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "建議使用[圖片優化 WordPress 外掛程式](https://wordpress.org/plugins/search/optimize+images/)，在壓縮圖片時保持畫質。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "您可直接透過[媒體庫](https://wordpress.org/support/article/media-library-screen/)上載圖片，確保可使用所需的圖片大小，然後從媒體庫插入圖片，或使用圖片小工具來確保您使用優化的圖片大小 (包括回應式中斷點適用的圖片大小)。除非可用空間足夠，否則請避免使用「`Full Size`」圖片。[瞭解詳情](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "您可在網絡伺服器設定中啟用文字壓縮功能。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "如要將圖片轉換成 WebP 格式，請在「WP Rocket」的「圖片優化」分頁中啟用「Imagify」。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "如要修正此建議，請在 WP Rocket 中啟用「[延遲載入](https://docs.wp-rocket.me/article/1141-lazyload-for-images)」。此功能會延遲載入圖片，直至訪客向下捲動網頁並實際看到圖片為止。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "如要修正此建議，請在「WP Rocket」中啟用「[移除未使用的 CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)」和「[載入 JavaScript 延遲](https://docs.wp-rocket.me/article/1265-load-javascript-deferred)」。這些功能將分別優化 CSS 和 JavaScript 檔案，避免網頁因這兩種檔案而無法輸出。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "為修正此問題，請在「WP Rocket」中啟用「[壓縮 CSS 檔案](https://docs.wp-rocket.me/article/1350-css-minify-combine)」，移除網站 CSS 檔案中的任何空格和留言，從而縮小檔案並提升下載速度。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "為修正此問題，請在「WP Rocket」中啟用「[壓縮 JavaScript 檔案](https://docs.wp-rocket.me/article/1351-javascript-minify-combine)」，移除 JavaScript 檔案中的空格和留言，從而縮小檔案並提升下載速度。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "為修正此問題，請在「WP Rocket」中啟用「[移除未使用的 CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)」，移除所有未使用的 CSS 和樣式表，同時只保留每個網頁的已使用 CSS，從而減少網頁大小。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "為修正此問題，請在「WP Rocket」中啟用「[延遲執行 JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution)」，讓系統等到使用者開始互動時才執行指令碼，從而改善網頁載入方式。如果網站有 iframe，您亦可使用　WP Rocket　的「[延遲載入 iframe 和影片](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)」，以及「[透過預覽圖片以取代 YouTube iframe](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)」。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "如要壓縮圖片，請在「WP Rocket」的「圖片優化」分頁中啟用「Imagify」，並執行「大量優化」。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "在「WP Rocket」中使用「[預先擷取 DNS 要求](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)」，即可新增「dns-prefetch」並提高與外部網域的連線速度。此外，「WP Rocket」會自動將「預先連線」新增至 [Google Fonts 網域](https://docs.wp-rocket.me/article/1312-optimize-google-fonts)，透過「[啟用 CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)」功能新增的任何 CNAME。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "為修正此字型問題，請在「WP Rocket」中啟用「[移除未使用的 CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css)」，系統將會優先預先載入網站的重要字型。"}, "report/renderer/report-utils.js | calculatorLink": {"message": "查看計算機。"}, "report/renderer/report-utils.js | collapseView": {"message": "收合檢視"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "初始導覽"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "關鍵路徑延遲時間上限："}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "複製 JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "切換至深色主題背景"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "已展開列印"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "列印摘要"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "另存為 Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "另存為 HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "另存為 JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "在檢視器中開啟"}, "report/renderer/report-utils.js | errorLabel": {"message": "發生錯誤！"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "報告錯誤：無審核資料"}, "report/renderer/report-utils.js | expandView": {"message": "展開檢視"}, "report/renderer/report-utils.js | footerIssue": {"message": "報告問題"}, "report/renderer/report-utils.js | hide": {"message": "隱藏"}, "report/renderer/report-utils.js | labDataTitle": {"message": "實驗室數據"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) 在模擬流動網絡上對目前網頁進行的分析。此為預計值，可能與實際值有所不同。"}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "其他手動檢查項目"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "不適用"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "優化建議"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "預計節省的時間"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "已通過的審核"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "初始頁面載入"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "自訂節流"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "模擬桌面電腦"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "沒有任何模擬的裝置"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe 版本"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "不受限的 CPU/記憶體效能"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU 節流"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "裝置"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "網絡節流"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "螢幕模擬"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "用戶代理程式 (網絡)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "單次網頁載入"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "這項資料取自單次網頁載入，而不是綜合多個工作階段的現實資料。"}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "慢速 4G 節流"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "不明"}, "report/renderer/report-utils.js | show": {"message": "顯示"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "顯示與下列數據相關的審核："}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "收合片段"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "展開片段"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "顯示第三方資源"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "由環境提供"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "導致這次 Lighthouse 無法順利執行的問題："}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "此為預計值，可能與實際值有所不同。[效能分數將利用這些數據直接計算](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)。"}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "查看原始追蹤記錄"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "檢視追蹤記錄"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "查看矩形樹圖"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "經過審核，但附有警告訊息"}, "report/renderer/report-utils.js | warningHeader": {"message": "警告： "}, "treemap/app/src/util.js | allLabel": {"message": "所有"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "所有指令碼"}, "treemap/app/src/util.js | coverageColumnName": {"message": "覆蓋率"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "重複的模組"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "資源字節"}, "treemap/app/src/util.js | tableColumnName": {"message": "名稱"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "顯示/隱藏表格"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "未使用的字節"}}