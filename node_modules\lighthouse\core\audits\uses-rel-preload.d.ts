export default UsesRelPreloadAudit;
declare class UsesRelPreloadAudit extends Audit {
    /**
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @return {Set<string>}
     */
    static getURLsToPreload(mainResource: LH.Artifacts.NetworkRequest, graph: LH.Gatherer.Simulation.GraphNode): Set<string>;
    /**
     * Finds which URLs were attempted to be preloaded, but failed to be reused and were requested again.
     *
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @return {Set<string>}
     */
    static getURLsFailedToPreload(graph: LH.Gatherer.Simulation.GraphNode): Set<string>;
    /**
     * We want to preload all first party critical requests at depth 2.
     * Third party requests can be tricky to know the URL ahead of time.
     * Critical requests at depth 1 would already be identified by the browser for preloading.
     * Critical requests deeper than depth 2 are more likely to be a case-by-case basis such that it
     * would be a little risky to recommend blindly.
     *
     * @param {LH.Artifacts.NetworkRequest} request
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {Array<LH.Gatherer.Simulation.GraphNode>} initiatorPath
     * @return {boolean}
     */
    static shouldPreloadRequest(request: LH.Artifacts.NetworkRequest, mainResource: LH.Artifacts.NetworkRequest, initiatorPath: Array<LH.Gatherer.Simulation.GraphNode>): boolean;
    /**
     * Computes the estimated effect of preloading all the resources.
     * @param {Set<string>} urls The array of byte savings results per resource
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @param {LH.Gatherer.Simulation.Simulator} simulator
     * @return {{wastedMs: number, results: Array<{url: string, wastedMs: number}>}}
     */
    static computeWasteWithGraph(urls: Set<string>, graph: LH.Gatherer.Simulation.GraphNode, simulator: LH.Gatherer.Simulation.Simulator): {
        wastedMs: number;
        results: Array<{
            url: string;
            wastedMs: number;
        }>;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit_(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
    /**
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const crossoriginWarning: string;
}
import { Audit } from "./audit.js";
import { NetworkRequest } from "../lib/network-request.js";
//# sourceMappingURL=uses-rel-preload.d.ts.map