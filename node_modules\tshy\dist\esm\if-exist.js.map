{"version": 3, "file": "if-exist.js", "sourceRoot": "", "sources": ["../../src/if-exist.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,IAAI,CAAA;AAEvD,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;AAC5D,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAU,EAAE,EAAE,CACvC,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;AAEpC,eAAe;IACb,MAAM;IACN,MAAM;CACP,CAAA", "sourcesContent": ["import { existsSync, renameSync, unlinkSync } from 'fs'\n\nconst unlink = (f: string) => existsSync(f) && unlinkSync(f)\nconst rename = (f: string, to: string) =>\n  existsSync(f) && renameSync(f, to)\n\nexport default {\n  unlink,\n  rename,\n}\n"]}