{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAA;AACrC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AAC3C,OAAO,SAAS,MAAM,gBAAgB,CAAA;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAA;AACxC,OAAO,EACL,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,WAAW,GACZ,MAAM,SAAS,CAAA;AAChB,OAAO,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,OAAO,GACR,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,WAAW,CAAA;AAC3D,OAAO,EAAQ,UAAU,EAAE,MAAM,aAAa,CAAA;AAC9C,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AAE3C,MAAM,aAAa,GAAG,KAAK,EAAE,GAAW,EAAE,EAAE;IAC1C,IAAI;QACF,OAAO,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA;KACzB;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;QACjC,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA;QACjB,OAAO,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA;KACzB;AACH,CAAC,CAAA;AAED,MAAM,iBAAiB,GAAG,CAAC,GAAW,EAAE,EAAE;IACxC,IAAI;QACF,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA;KACvB;IAAC,OAAO,CAAC,EAAE;QACV,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;QAC/B,UAAU,CAAC,GAAG,CAAC,CAAA;QACf,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA;KACvB;AACH,CAAC,CAAA;AAED,MAAM,QAAQ,GAAG,KAAK,EAAE,GAAS,EAAE,IAAU,EAAE,EAAE;IAC/C,sCAAsC;IACtC,kDAAkD;IAClD,qBAAqB;IACrB,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;QAChE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACnD,OAAM;KACP;IACD,oBAAoB;IAEpB,IAAI,GAAG,CAAC,cAAc,EAAE,EAAE;QACxB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;YACrE,IAAI,EAAE,KAAK,MAAM;gBAAE,OAAM;YACzB,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SAC9B;QACD,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;QAC7C,MAAM,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACtC,qBAAqB;QACrB,IAAI,SAAS,CAAC,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;YACnC,IAAI;gBACF,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aACvC;YAAC,MAAM,GAAE;SACX;QACD,oBAAoB;QACpB,OAAM;KACP;IAED,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,MAAM,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SACrC;KACF;SAAM;QACL,eAAe;QACf,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAAE,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;aAChE,IAAI,MAAM,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;YAAE,KAAK,GAAG,KAAK,CAAA;QACrD,IAAI,KAAK,EAAE;YACT,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;YAC7C,oBAAoB;YACpB,qBAAqB;YACrB,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CACrD,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAC1C,CAAA;YACD,oBAAoB;SACrB;KACF;IACD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;IACrB,qBAAqB;IACrB,IAAI,CAAC,IAAI;QAAE,OAAM;IACjB,oBAAoB;IACpB,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAA;AACpC,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,GAAS,EAAE,IAAU,EAAE,EAAE;IAC7C,sCAAsC;IACtC,kDAAkD;IAClD,qBAAqB;IACrB,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;QAChE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACjD,OAAM;KACP;IACD,oBAAoB;IAEpB,IAAI,GAAG,CAAC,cAAc,EAAE,EAAE;QACxB,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;YACjE,IAAI,EAAE,KAAK,MAAM;gBAAE,OAAM;YACzB,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SAC5B;QACD,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;QAC3C,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACpC,qBAAqB;QACrB,IAAI,SAAS,CAAC,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;YACnC,IAAI;gBACF,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aACrC;YAAC,MAAM,GAAE;SACX;QACD,oBAAoB;QACpB,OAAM;KACP;IAED,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SACnC;KACF;SAAM;QACL,eAAe;QACf,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;aAC9D,IAAI,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC;YAAE,KAAK,GAAG,KAAK,CAAA;QACnD,IAAI,KAAK,EAAE;YACT,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;YAC3C,oBAAoB;YACpB,qBAAqB;YACrB,IAAI;gBACF,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;aAC1C;YAAC,MAAM;gBACN,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;aAC9C;YACD,oBAAoB;SACrB;KACF;IACD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;IACrB,qBAAqB;IACrB,IAAI,CAAC,IAAI;QAAE,OAAM;IACjB,oBAAoB;IACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAA;AAClC,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,KAAK,EAAE,GAAS,EAAE,IAAU,EAAE,EAAE;IACnD,IAAI;QACF,OAAO,CACL,UAAU,CAAC,QAAQ,CAAC;aACjB,MAAM,CAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;aACtC,MAAM,CAAC,KAAK,CAAC;YAChB,UAAU,CAAC,QAAQ,CAAC;iBACjB,MAAM,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;iBACvC,MAAM,CAAC,KAAK,CAAC,CACjB,CAAA;QACD,oEAAoE;QACpE,yDAAyD;QACzD,qBAAqB;KACtB;IAAC,MAAM;QACN,OAAO,KAAK,CAAA;KACb;IACD,oBAAoB;AACtB,CAAC,CAAA;AAED,MAAM,gBAAgB,GAAG,CAAC,GAAS,EAAE,IAAU,EAAE,EAAE;IACjD,IAAI;QACF,OAAO,CACL,UAAU,CAAC,QAAQ,CAAC;aACjB,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;aACpC,MAAM,CAAC,KAAK,CAAC;YAChB,UAAU,CAAC,QAAQ,CAAC;iBACjB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;iBACrC,MAAM,CAAC,KAAK,CAAC,CACjB,CAAA;QACD,oEAAoE;QACpE,yDAAyD;QACzD,qBAAqB;KACtB;IAAC,MAAM;QACN,OAAO,KAAK,CAAA;KACb;IACD,oBAAoB;AACtB,CAAC,CAAA;AAED,iEAAiE;AACjE,+CAA+C;AAC/C,MAAM,IAAI,GAAG,KAAK,GAAG,EAAE,CAAA;AACvB,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,CAAS,EAAU,EAAE;IACnD,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAA;IACnE,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,WAAW,GAAG,KAAK,EAAE,IAAY,EAAE,EAAU,EAAE,EAAE;IAC5D,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;IACnC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;IACvB,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;QACpD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;KAC9C;IACD,qBAAqB;IACrB,IAAI,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;QAC3B,oBAAoB;QACpB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAA;KACnE;IACD,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACpC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;QACjD,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;KACpD,CAAC,CAAA;IACF,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAC,CAAC,EAAC,EAAE;YACnB,qBAAqB;YACrB,IAAI,CAAC,CAAC,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAC5D,oBAAoB;YACpB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;YAC/C,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAA;YACvB,qBAAqB;YACrB,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAC1D,oBAAoB;YACpB,MAAM,CAAC,CAAC,KAAK,EAAE,CAAA;YACf,MAAM,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtC,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACtB,CAAC,CAAC;QACF,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAC,CAAC,EAAC,EAAE;YACpB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;YAC1D,MAAM,CAAC,CAAC,KAAK,EAAE,CAAA;YACf,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChB,OAAO;gBACP,qBAAqB;gBACrB,IAAI;oBACF,MAAM,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;iBAC3B;gBAAC,MAAM,GAAE;gBACV,oBAAoB;aACrB;QACH,CAAC,CAAC;KACH,CAAC,CAAA;AACJ,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,IAAY,EAAE,EAAU,EAAE,EAAE;IAC1D,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;IACnC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;IACvB,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;QACpD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;KAC9C;IACD,IAAI,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAA;KACnE;IACD,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG;QAClB,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;QAC/C,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;KAClD,CAAA;IACD,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;QACnB,qBAAqB;QACrB,IAAI,CAAC,CAAC,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC5D,oBAAoB;QACpB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;QAC/C,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAA;QACvB,qBAAqB;QACrB,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC1D,oBAAoB;QACpB,CAAC,CAAC,SAAS,EAAE,CAAA;QACb,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;QACpC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KACnB;IACD,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;QACpB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;QAC1D,CAAC,CAAC,SAAS,EAAE,CAAA;QACb,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAChB,OAAO;YACP,qBAAqB;YACrB,IAAI;gBACF,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;aACzB;YAAC,MAAM,GAAE;YACV,oBAAoB;SACrB;KACF;AACH,CAAC,CAAA", "sourcesContent": ["import { glob, globSync } from 'glob'\nimport { mkdirp, mkdirpSync } from 'mkdirp'\nimport constants from 'node:constants'\nimport { createHash } from 'node:crypto'\nimport {\n  chmodSync,\n  copyFileSync,\n  linkSync,\n  readFileSync,\n  readlinkSync,\n  symlinkSync,\n} from 'node:fs'\nimport {\n  chmod,\n  copyFile,\n  link,\n  readFile,\n  readlink,\n  symlink,\n} from 'node:fs/promises'\nimport { dirname, relative, resolve, sep } from 'node:path'\nimport { Path, PathScurry } from 'path-scurry'\nimport { rimraf, rimrafSync } from 'rimraf'\n\nconst mkdirpClobber = async (dir: string) => {\n  try {\n    return await mkdirp(dir)\n  } catch (e) {\n    await mkdirpClobber(dirname(dir))\n    await rimraf(dir)\n    return await mkdirp(dir)\n  }\n}\n\nconst mkdirpClobberSync = (dir: string) => {\n  try {\n    return mkdirpSync(dir)\n  } catch (e) {\n    mkdirpClobberSync(dirname(dir))\n    rimrafSync(dir)\n    return mkdirpSync(dir)\n  }\n}\n\nconst syncFile = async (src: Path, dest: Path) => {\n  // only sync files, dirs, and symlinks\n  // Creating these is a pain to test cross platform\n  /* c8 ignore start */\n  if (!(src.isSymbolicLink() || src.isDirectory() || src.isFile())) {\n    if (!dest.isENOENT()) await rimraf(dest.fullpath())\n    return\n  }\n  /* c8 ignore stop */\n\n  if (src.isSymbolicLink()) {\n    const target = await readlink(src.fullpath())\n    if (!dest.isENOENT()) {\n      const dp = dest.isSymbolicLink() && (await readlink(dest.fullpath()))\n      if (dp === target) return\n      await rimraf(dest.fullpath())\n    }\n    await mkdirpClobber(dirname(dest.fullpath()))\n    await symlink(target, dest.fullpath())\n    /* c8 ignore start */\n    if (constants.O_SYMLINK && src.mode) {\n      try {\n        await chmod(dest.fullpath(), src.mode)\n      } catch {}\n    }\n    /* c8 ignore stop */\n    return\n  }\n\n  if (src.isDirectory()) {\n    if (!dest.isDirectory()) {\n      await mkdirpClobber(dest.fullpath())\n    }\n  } else {\n    // must be file\n    let write = true\n    if (!dest.isENOENT() && !dest.isFile()) await rimraf(dest.fullpath())\n    else if (await contentMatch(src, dest)) write = false\n    if (write) {\n      await mkdirpClobber(dirname(dest.fullpath()))\n      // platform specific\n      /* c8 ignore start */\n      await link(src.fullpath(), dest.fullpath()).catch(() =>\n        copyFile(src.fullpath(), dest.fullpath())\n      )\n      /* c8 ignore stop */\n    }\n  }\n  const mode = src.mode\n  /* c8 ignore start */\n  if (!mode) return\n  /* c8 ignore stop */\n  await chmod(dest.fullpath(), mode)\n}\n\nconst syncFileSync = (src: Path, dest: Path) => {\n  // only sync files, dirs, and symlinks\n  // Creating these is a pain to test cross platform\n  /* c8 ignore start */\n  if (!(src.isSymbolicLink() || src.isDirectory() || src.isFile())) {\n    if (!dest.isENOENT()) rimrafSync(dest.fullpath())\n    return\n  }\n  /* c8 ignore stop */\n\n  if (src.isSymbolicLink()) {\n    const target = readlinkSync(src.fullpath())\n    if (!dest.isENOENT()) {\n      const dp = dest.isSymbolicLink() && readlinkSync(dest.fullpath())\n      if (dp === target) return\n      rimrafSync(dest.fullpath())\n    }\n    mkdirpClobberSync(dirname(dest.fullpath()))\n    symlinkSync(target, dest.fullpath())\n    /* c8 ignore start */\n    if (constants.O_SYMLINK && src.mode) {\n      try {\n        chmodSync(dest.fullpath(), src.mode)\n      } catch {}\n    }\n    /* c8 ignore stop */\n    return\n  }\n\n  if (src.isDirectory()) {\n    if (!dest.isDirectory()) {\n      mkdirpClobberSync(dest.fullpath())\n    }\n  } else {\n    // must be file\n    let write = true\n    if (!dest.isENOENT() && !dest.isFile()) rimrafSync(dest.fullpath())\n    else if (contentMatchSync(src, dest)) write = false\n    if (write) {\n      mkdirpClobberSync(dirname(dest.fullpath()))\n      // platform specific\n      /* c8 ignore start */\n      try {\n        linkSync(src.fullpath(), dest.fullpath())\n      } catch {\n        copyFileSync(src.fullpath(), dest.fullpath())\n      }\n      /* c8 ignore stop */\n    }\n  }\n  const mode = src.mode\n  /* c8 ignore start */\n  if (!mode) return\n  /* c8 ignore stop */\n  chmodSync(dest.fullpath(), mode)\n}\n\nconst contentMatch = async (src: Path, dest: Path) => {\n  try {\n    return (\n      createHash('sha512')\n        .update(await readFile(src.fullpath()))\n        .digest('hex') ===\n      createHash('sha512')\n        .update(await readFile(dest.fullpath()))\n        .digest('hex')\n    )\n    // we should only be doing this if we know it's a valid file already\n    // but just in case we can't read it, that's not a match.\n    /* c8 ignore start */\n  } catch {\n    return false\n  }\n  /* c8 ignore stop */\n}\n\nconst contentMatchSync = (src: Path, dest: Path) => {\n  try {\n    return (\n      createHash('sha512')\n        .update(readFileSync(src.fullpath()))\n        .digest('hex') ===\n      createHash('sha512')\n        .update(readFileSync(dest.fullpath()))\n        .digest('hex')\n    )\n    // we should only be doing this if we know it's a valid file already\n    // but just in case we can't read it, that's not a match.\n    /* c8 ignore start */\n  } catch {\n    return false\n  }\n  /* c8 ignore stop */\n}\n\n// if a is a parent of b, or b is a parent of a, then one of them\n// will not start with .. in the relative path.\nconst dots = `..${sep}`\nconst dirsRelated = (a: string, b: string):boolean => {\n  if (a === b) return true\n  const relab = relative(a, b)\n  const relba = relative(a, b)\n  if (!relab.startsWith(dots) || !relba.startsWith(dots)) return true\n  return false\n}\n\nexport const syncContent = async (from: string, to: string) => {\n  const scurry = new PathScurry(from)\n  const rfrom = resolve(from)\n  const rto = resolve(to)\n  if (dirname(rfrom) === rfrom || dirname(rto) === rto) {\n    throw new Error('cannot sync root directory')\n  }\n  /* c8 ignore start */\n  if (dirsRelated(rto, rfrom)) {\n    /* c8 ignore stop */\n    throw new Error('cannot copy directory into itself or its parent')\n  }\n  const [src, dest] = await Promise.all([\n    await glob('**', { scurry, withFileTypes: true }),\n    await glob('**', { cwd: rto, withFileTypes: true }),\n  ])\n  await Promise.all([\n    ...src.map(async s => {\n      /* c8 ignore start */\n      if (!s.parent) throw new Error('cannot sync root directory')\n      /* c8 ignore stop */\n      const d = s.resolve(resolve(rto, s.relative()))\n      const parent = d.parent\n      /* c8 ignore start */\n      if (!parent) throw new Error('cannot sync root directory')\n      /* c8 ignore stop */\n      await d.lstat()\n      await mkdirpClobber(parent.fullpath())\n      await syncFile(s, d)\n    }),\n    ...dest.map(async d => {\n      const s = scurry.cwd.resolve(resolve(rfrom, d.relative()))\n      await s.lstat()\n      if (s.isENOENT()) {\n        // race\n        /* c8 ignore start */\n        try {\n          await rimraf(d.fullpath())\n        } catch {}\n        /* c8 ignore stop */\n      }\n    }),\n  ])\n}\n\nexport const syncContentSync = (from: string, to: string) => {\n  const scurry = new PathScurry(from)\n  const rfrom = resolve(from)\n  const rto = resolve(to)\n  if (dirname(rfrom) === rfrom || dirname(rto) === rto) {\n    throw new Error('cannot sync root directory')\n  }\n  if (dirsRelated(rto, rfrom)) {\n    throw new Error('cannot copy directory into itself or its parent')\n  }\n  const [src, dest] = [\n    globSync('**', { scurry, withFileTypes: true }),\n    globSync('**', { cwd: rto, withFileTypes: true }),\n  ]\n  for (const s of src) {\n    /* c8 ignore start */\n    if (!s.parent) throw new Error('cannot sync root directory')\n    /* c8 ignore stop */\n    const d = s.resolve(resolve(rto, s.relative()))\n    const parent = d.parent\n    /* c8 ignore start */\n    if (!parent) throw new Error('cannot sync root directory')\n    /* c8 ignore stop */\n    d.lstatSync()\n    mkdirpClobberSync(parent.fullpath())\n    syncFileSync(s, d)\n  }\n  for (const d of dest) {\n    const s = scurry.cwd.resolve(resolve(rfrom, d.relative()))\n    s.lstatSync()\n    if (s.isENOENT()) {\n      // race\n      /* c8 ignore start */\n      try {\n        rimrafSync(d.fullpath())\n      } catch {}\n      /* c8 ignore stop */\n    }\n  }\n}\n"]}