{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nvar TimelineItem = function TimelineItem(_a) {\n  var _classNames, _classNames2;\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    _a$color = _a.color,\n    color = _a$color === void 0 ? 'blue' : _a$color,\n    dot = _a.dot,\n    _a$pending = _a.pending,\n    pending = _a$pending === void 0 ? false : _a$pending,\n    position = _a.position,\n    label = _a.label,\n    children = _a.children,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"color\", \"dot\", \"pending\", \"position\", \"label\", \"children\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  var itemClassName = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-pending\"), pending), _classNames), className);\n  var dotClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head-custom\"), !!dot), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head-\").concat(color), true), _classNames2));\n  var customColor = /blue|red|green|gray/.test(color || '') ? undefined : color;\n  return /*#__PURE__*/React.createElement(\"li\", _extends({}, restProps, {\n    className: itemClassName\n  }), label && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-label\")\n  }, label), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-tail\")\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: dotClassName,\n    style: {\n      borderColor: customColor,\n      color: customColor\n    }\n  }, dot), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-content\")\n  }, children));\n};\nexport default TimelineItem;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ConfigContext", "TimelineItem", "_a", "_classNames", "_classNames2", "customizePrefixCls", "prefixCls", "className", "_a$color", "color", "dot", "_a$pending", "pending", "position", "label", "children", "restProps", "_React$useContext", "useContext", "getPrefixCls", "itemClassName", "concat", "dotClassName", "customColor", "test", "undefined", "createElement", "style", "borderColor"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/timeline/TimelineItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\n\nvar TimelineItem = function TimelineItem(_a) {\n  var _classNames, _classNames2;\n\n  var customizePrefixCls = _a.prefixCls,\n      className = _a.className,\n      _a$color = _a.color,\n      color = _a$color === void 0 ? 'blue' : _a$color,\n      dot = _a.dot,\n      _a$pending = _a.pending,\n      pending = _a$pending === void 0 ? false : _a$pending,\n      position = _a.position,\n      label = _a.label,\n      children = _a.children,\n      restProps = __rest(_a, [\"prefixCls\", \"className\", \"color\", \"dot\", \"pending\", \"position\", \"label\", \"children\"]);\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  var itemClassName = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-pending\"), pending), _classNames), className);\n  var dotClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head-custom\"), !!dot), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-head-\").concat(color), true), _classNames2));\n  var customColor = /blue|red|green|gray/.test(color || '') ? undefined : color;\n  return /*#__PURE__*/React.createElement(\"li\", _extends({}, restProps, {\n    className: itemClassName\n  }), label && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-label\")\n  }, label), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-tail\")\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: dotClassName,\n    style: {\n      borderColor: customColor,\n      color: customColor\n    }\n  }, dot), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-content\")\n  }, children));\n};\n\nexport default TimelineItem;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AAEvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EAEA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AAED,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAElD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,EAAE,EAAE;EAC3C,IAAIC,WAAW,EAAEC,YAAY;EAE7B,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACjCC,SAAS,GAAGL,EAAE,CAACK,SAAS;IACxBC,QAAQ,GAAGN,EAAE,CAACO,KAAK;IACnBA,KAAK,GAAGD,QAAQ,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,QAAQ;IAC/CE,GAAG,GAAGR,EAAE,CAACQ,GAAG;IACZC,UAAU,GAAGT,EAAE,CAACU,OAAO;IACvBA,OAAO,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,UAAU;IACpDE,QAAQ,GAAGX,EAAE,CAACW,QAAQ;IACtBC,KAAK,GAAGZ,EAAE,CAACY,KAAK;IAChBC,QAAQ,GAAGb,EAAE,CAACa,QAAQ;IACtBC,SAAS,GAAGhC,MAAM,CAACkB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;EAElH,IAAIe,iBAAiB,GAAGnB,KAAK,CAACoB,UAAU,CAAClB,aAAa,CAAC;IACnDmB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIb,SAAS,GAAGa,YAAY,CAAC,UAAU,EAAEd,kBAAkB,CAAC;EAC5D,IAAIe,aAAa,GAAGrB,UAAU,EAAEI,WAAW,GAAG,CAAC,CAAC,EAAEpB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACf,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAEvB,eAAe,CAACoB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACf,SAAS,EAAE,eAAe,CAAC,EAAEM,OAAO,CAAC,EAAET,WAAW,GAAGI,SAAS,CAAC;EAC3N,IAAIe,YAAY,GAAGvB,UAAU,EAAEK,YAAY,GAAG,CAAC,CAAC,EAAErB,eAAe,CAACqB,YAAY,EAAE,EAAE,CAACiB,MAAM,CAACf,SAAS,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,EAAEvB,eAAe,CAACqB,YAAY,EAAE,EAAE,CAACiB,MAAM,CAACf,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAACI,GAAG,CAAC,EAAE3B,eAAe,CAACqB,YAAY,EAAE,EAAE,CAACiB,MAAM,CAACf,SAAS,EAAE,aAAa,CAAC,CAACe,MAAM,CAACZ,KAAK,CAAC,EAAE,IAAI,CAAC,EAAEL,YAAY,CAAC,CAAC;EAClT,IAAImB,WAAW,GAAG,qBAAqB,CAACC,IAAI,CAACf,KAAK,IAAI,EAAE,CAAC,GAAGgB,SAAS,GAAGhB,KAAK;EAC7E,OAAO,aAAaX,KAAK,CAAC4B,aAAa,CAAC,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEkC,SAAS,EAAE;IACpET,SAAS,EAAEa;EACb,CAAC,CAAC,EAAEN,KAAK,IAAI,aAAahB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACnDnB,SAAS,EAAE,EAAE,CAACc,MAAM,CAACf,SAAS,EAAE,aAAa;EAC/C,CAAC,EAAEQ,KAAK,CAAC,EAAE,aAAahB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACjDnB,SAAS,EAAE,EAAE,CAACc,MAAM,CAACf,SAAS,EAAE,YAAY;EAC9C,CAAC,CAAC,EAAE,aAAaR,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC1CnB,SAAS,EAAEe,YAAY;IACvBK,KAAK,EAAE;MACLC,WAAW,EAAEL,WAAW;MACxBd,KAAK,EAAEc;IACT;EACF,CAAC,EAAEb,GAAG,CAAC,EAAE,aAAaZ,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC/CnB,SAAS,EAAE,EAAE,CAACc,MAAM,CAACf,SAAS,EAAE,eAAe;EACjD,CAAC,EAAES,QAAQ,CAAC,CAAC;AACf,CAAC;AAED,eAAed,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}