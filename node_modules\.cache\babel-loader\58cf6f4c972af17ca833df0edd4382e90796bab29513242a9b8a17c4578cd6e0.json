{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\visualizzaListini.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* VisualizzaListini - visualizzazione listini lato affiliato\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Nav from \"../../components/navigation/Nav\";\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { BannerWelcome } from '../../components/generalizzazioni/bannerWelcome';\nimport { basePath } from '../../components/route';\nimport '../../css/header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaListino = () => {\n  _s();\n  const [results, setResults] = useState(null);\n  const [results2, setResults2] = useState(null);\n  const [padre, setPadre] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const toast = useRef(null);\n  //Chiamata axios effettuata una sola volta grazie a useEffect\n  useEffect(() => {\n    localStorage.setItem(\"Prodotti\", []);\n    localStorage.setItem(\"Cart\", []);\n    localStorage.setItem(\"OrdineRecuperato\", []);\n    localStorage.setItem(\"DatiConsegna\", []);\n    window.sessionStorage.setItem(\"Carrello\", 0);\n    window.sessionStorage.setItem(\"totCart\", 0);\n    localStorage.setItem(\"datiComodo\", 0);\n    async function fetchData() {\n      await APIRequest('GET', 'pricelistaffiliate/').then(data => {\n        var datasource = data.data[0].idPriceList2.priceListProducts;\n        setResults(datasource);\n        setResults2(datasource);\n        setLoading(false);\n        setPadre(true);\n      }).catch(e => {\n        var _e$response, _e$response2;\n        toast.current.show({\n          severity: 'error',\n          summary: 'Attenzione',\n          detail: \"Al momento non \\xE8 stato associato nessun listino a questo profilo. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.pathname = basePath;\n        }, 3000);\n      });\n    }\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dataview-demo\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Caricamento, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dataview-demo\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(BannerWelcome, {\n      nome: Costanti.visualizzaListini\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MarketplaceGen, {\n      results: results,\n      results2: results2,\n      padre: padre,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 9\n  }, this);\n};\n_s(VisualizzaListino, \"SEadMYd9Z88rhFgZtMzElDyiuNk=\");\n_c = VisualizzaListino;\nexport default VisualizzaListino;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaListino\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Nav", "MarketplaceGen", "Caricamento", "<PERSON><PERSON>", "APIRequest", "Toast", "BannerWelcome", "basePath", "jsxDEV", "_jsxDEV", "VisualizzaListino", "_s", "results", "setResults", "results2", "setResults2", "padre", "set<PERSON><PERSON>", "loading", "setLoading", "toast", "localStorage", "setItem", "window", "sessionStorage", "fetchData", "then", "data", "datasource", "idPriceList2", "priceListProducts", "catch", "e", "_e$response", "_e$response2", "current", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "setTimeout", "location", "pathname", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nome", "visualizzaListini", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/visualizzaListini.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* VisualizzaListini - visualizzazione listini lato affiliato\n*\n*/\nimport React, { useEffect, useRef, useState } from 'react';\nimport Nav from \"../../components/navigation/Nav\";\nimport MarketplaceGen from '../../components/generalizzazioni/marketplace/marketplace';\nimport Caricamento from '../../utils/caricamento';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { BannerWelcome } from '../../components/generalizzazioni/bannerWelcome';\nimport { basePath } from '../../components/route';\nimport '../../css/header.css';\n\nconst VisualizzaListino = () => {\n    const [results, setResults] = useState(null)\n    const [results2, setResults2] = useState(null)\n    const [padre, setPadre] = useState(false)\n    const [loading, setLoading] = useState(true)\n    const toast = useRef(null);\n    //Chiamata axios effettuata una sola volta grazie a useEffect\n    useEffect(() => {\n        localStorage.setItem(\"Prodotti\", []);\n        localStorage.setItem(\"Cart\", []);\n        localStorage.setItem(\"OrdineRecuperato\", []);\n        localStorage.setItem(\"DatiConsegna\", []);\n        window.sessionStorage.setItem(\"Carrello\", 0);\n        window.sessionStorage.setItem(\"totCart\", 0);\n        localStorage.setItem(\"datiComodo\", 0);\n        async function fetchData() {\n            await APIRequest('GET', 'pricelistaffiliate/')\n                .then(data => {\n                    var datasource = data.data[0].idPriceList2.priceListProducts;\n                    setResults(datasource)\n                    setResults2(datasource)\n                    setLoading(false)\n                    setPadre(true)\n                }).catch((e) => {\n                    toast.current.show({ severity: 'error', summary: 'Attenzione', detail: `Al momento non è stato associato nessun listino a questo profilo. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`, life: 3000 });\n                    setTimeout(() => {\n                        window.location.pathname = basePath;\n                    }, 3000)\n                })\n        }\n        fetchData()\n    }, []);\n    if (loading) {\n        return <div className=\"dataview-demo\">\n            <Toast ref={toast} />\n            <Caricamento />\n        </div>\n    }\n    return (\n        <div className=\"dataview-demo\">\n            <Toast ref={toast} />\n            <Nav />\n            {/* <div className=\"col-12 px-0 solid-head\">\n                <h1>{Costanti.ListinoProd}</h1>\n            </div> */}\n            <BannerWelcome nome={Costanti.visualizzaListini} />\n            <MarketplaceGen results={results} results2={results2} padre={padre} loading={loading} />\n        </div>\n    )\n}\n\nexport default VisualizzaListino;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,cAAc,MAAM,2DAA2D;AACtF,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,aAAa,QAAQ,iDAAiD;AAC/E,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMqB,KAAK,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAD,SAAS,CAAC,MAAM;IACZwB,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACpCD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IAChCD,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAC5CD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACxCC,MAAM,CAACC,cAAc,CAACF,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAC5CC,MAAM,CAACC,cAAc,CAACF,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;IAC3CD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;IACrC,eAAeG,SAASA,CAAA,EAAG;MACvB,MAAMrB,UAAU,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACzCsB,IAAI,CAACC,IAAI,IAAI;QACV,IAAIC,UAAU,GAAGD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,CAACC,iBAAiB;QAC5DjB,UAAU,CAACe,UAAU,CAAC;QACtBb,WAAW,CAACa,UAAU,CAAC;QACvBT,UAAU,CAAC,KAAK,CAAC;QACjBF,QAAQ,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,CAACc,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAC,WAAA,EAAAC,YAAA;QACZd,KAAK,CAACe,OAAO,CAACC,IAAI,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,YAAY;UAAEC,MAAM,4FAAAC,MAAA,CAAyF,EAAAP,WAAA,GAAAD,CAAC,CAACS,QAAQ,cAAAR,WAAA,uBAAVA,WAAA,CAAYN,IAAI,MAAKe,SAAS,IAAAR,YAAA,GAAGF,CAAC,CAACS,QAAQ,cAAAP,YAAA,uBAAVA,YAAA,CAAYP,IAAI,GAAGK,CAAC,CAACW,OAAO,CAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;QAC5OC,UAAU,CAAC,MAAM;UACbtB,MAAM,CAACuB,QAAQ,CAACC,QAAQ,GAAGxC,QAAQ;QACvC,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACV;IACAkB,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAIP,OAAO,EAAE;IACT,oBAAOT,OAAA;MAAKuC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBACjCxC,OAAA,CAACJ,KAAK;QAAC6C,GAAG,EAAE9B;MAAM;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB7C,OAAA,CAACP,WAAW;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EACV;EACA,oBACI7C,OAAA;IAAKuC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC1BxC,OAAA,CAACJ,KAAK;MAAC6C,GAAG,EAAE9B;IAAM;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB7C,OAAA,CAACT,GAAG;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAIP7C,OAAA,CAACH,aAAa;MAACiD,IAAI,EAAEpD,QAAQ,CAACqD;IAAkB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnD7C,OAAA,CAACR,cAAc;MAACW,OAAO,EAAEA,OAAQ;MAACE,QAAQ,EAAEA,QAAS;MAACE,KAAK,EAAEA,KAAM;MAACE,OAAO,EAAEA;IAAQ;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvF,CAAC;AAEd,CAAC;AAAA3C,EAAA,CAjDKD,iBAAiB;AAAA+C,EAAA,GAAjB/C,iBAAiB;AAmDvB,eAAeA,iBAAiB;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}