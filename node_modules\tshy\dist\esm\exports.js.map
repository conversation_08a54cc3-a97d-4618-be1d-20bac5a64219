{"version": 3, "file": "exports.js", "sourceRoot": "", "sources": ["../../src/exports.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAA;AAMnD,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,OAAO,QAAQ,MAAM,eAAe,CAAA;AACpC,OAAO,IAAI,MAAM,WAAW,CAAA;AAC5B,OAAO,GAAG,MAAM,cAAc,CAAA;AAE9B,OAAO,SAAS,MAAM,gBAAgB,CAAA;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAA;AAEnD,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,MAAM,CAAA;AAE1D,MAAM,OAAO,GACX,MAAM,CAAC,OAAO;IACd,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,SAAS;IACrC,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM,CAAA;AAEpC,MAAM,4BAA4B,GAAG,CACnC,CAAyC,EACzC,OAAU,EACV,SAEG,EACH,IAEoB,EACpB,YAAsC,IAAI,GAAG,EAAE,EACpB,EAAE;IAC7B,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,SAAS,CAAA;IACrC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,gCAAgC;QAChC,MAAM,GAAG,GAAG,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAA;QACjD,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,OAAO,SAAS,CAAA;QACrC,MAAM,EAAE,GAAG,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAA;QACnD,MAAM,GAAG,GAAG,QAAQ,CAClB,OAAO,CAAC,OAAO,CAAC,EAChB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAC5C,CAAA;QACD,MAAM,MAAM,GACV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;QACxD,OAAO,CACL,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,OAAO,IAAI,MAAM,EAAE;gBACzD,CAAC,CAAC,SAAS,CACZ,CAAA;IACH,CAAC;IACD,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;AACtC,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,CAC1B,CAAyC,EACzC,YAAsC,IAAI,GAAG,EAAE,EAC/C,EAAE,CACF,4BAA4B,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;AAEpE,MAAM,CAAC,MAAM,YAAY,GAAG,CAC1B,CAAyC,EACzC,YAAsC,IAAI,GAAG,EAAE,EAC/C,EAAE,CACF,4BAA4B,CAC1B,CAAC,EACD,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,CACV,CAAA;AAEH,MAAM,UAAU,GAAG,CACjB,CAAa,EACqB,EAAE;IACpC,kEAAkE;IAClE,qBAAqB;IACrB,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,qDAAqD,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IACD,oBAAoB;IACpB,MAAM,CAAC,GAAqC,EAAE,CAAA;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACjD,mCAAmC;QACnC,IACE,CAAC,KAAK,IAAI;YACV,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAClD,CAAC;YACD,4CAA4C;YAC5C,CAAC,CAAC,GAAG,CAAC,GAAG,CAAqB,CAAA;YAC9B,SAAQ;QACV,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,KAAK,IAAI;YAAE,SAAQ;QAExB,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;QAE5C,uBAAuB;QACvB,qBAAqB;QACrB,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS;YAAE,SAAQ;QACtC,oBAAoB;QAEpB,MAAM,GAAG,GAA2B,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAA;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEvD,MAAM,MAAM,GAAG,4BAA4B,CACzC,CAAC,EACD,CAAC,EACD,CAAC,EACD,KAAK,EACL,SAAS,CACV,CAAA;gBACD,IAAI,MAAM,EAAE,CAAC;oBACX,GAAG,CAAC,CAAC,CAAC;wBACJ,OAAO,CAAC,CAAC;4BACP;gCACE,MAAM;gCACN,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;gCAC/B,OAAO,EAAE,MAAM;6BAChB;4BACH,CAAC,CAAC;gCACE,MAAM;gCACN,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;gCAC/B,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;gCACvC,OAAO,EAAE,MAAM;6BAChB,CAAA;gBACP,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;gBACvD,MAAM,MAAM,GAAG,4BAA4B,CACzC,CAAC,EACD,CAAC,EACD,CAAC,EACD,UAAU,EACV,SAAS,CACV,CAAA;gBACD,IAAI,MAAM,EAAE,CAAC;oBACX,GAAG,CAAC,CAAC,CAAC;wBACJ,OAAO,CAAC,CAAC;4BACP;gCACE,MAAM;gCACN,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;gCAC/B,OAAO,EAAE,MAAM;6BAChB;4BACH,CAAC,CAAC;gCACE,MAAM;gCACN,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;gCAC/B,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;gCACvC,OAAO,EAAE,MAAM;6BAChB,CAAA;gBACP,CAAC;YACH,CAAC;QACH,CAAC;QACD,mEAAmE;QACnE,IAAI,SAAS,EAAE,CAAC;YACd,GAAG,CAAC,MAAM;gBACR,OAAO,CAAC,CAAC;oBACP;wBACE,MAAM,EAAE,CAAC;wBACT,OAAO,EAAE,SAAS;qBACnB;oBACH,CAAC,CAAC;wBACE,MAAM,EAAE,CAAC;wBACT,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC;wBAChD,OAAO,EAAE,SAAS;qBACnB,CAAA;QACP,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACd,GAAG,CAAC,OAAO;gBACT,OAAO,CAAC,CAAC;oBACP;wBACE,MAAM,EAAE,CAAC;wBACT,OAAO,EAAE,SAAS;qBACnB;oBACH,CAAC,CAAC;wBACE,MAAM,EAAE,CAAC;wBACT,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC;wBAChD,OAAO,EAAE,SAAS;qBACnB,CAAA;QACP,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAED,MAAM,iBAAiB,GAAG,CAAC,MAAc,EAAE,CAAa,EAAE,EAAE;IAC1D,MAAM,EAAE,cAAc,EAAE,GAAG,CAAC,CAAA;IAC5B,IAAI,CAAC,cAAc;QAAE,OAAO,EAAE,CAAA;IAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;AACjE,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,OAAO,GAAG,CACrB,CAAyB,EACzB,GAA2C,EAC3C,EAAE;IACF,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;IACxD,MAAM,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAA;IAC7B,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,IAAI,CAAC,wDAAwD,CAAC,CAAA;YAC9D,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QACD,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC5C,SAAS;YACT,OAAO;SACR,CAAC,CAAA;QACF,GAAG,CAAC,IAAI,GAAG,GAAG,CAAA;QACd,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG;YAAE,GAAG,CAAC,KAAK,GAAG,KAAK,CAAA;;YACxC,OAAO,GAAG,CAAC,KAAK,CAAA;IACvB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK;YAAE,OAAO,CAAC,CAAC,IAAI,CAAA;QACxC,OAAO,GAAG,CAAC,IAAI,CAAA;QACf,OAAO,GAAG,CAAC,KAAK,CAAA;IAClB,CAAC;IACD,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAA;IAE1D,yCAAyC;IACzC,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC7D,MAAM,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,SAAS,CAAA;IACvC,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,yDAAyD,CAAC,CAAA;YAC/D,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;QAED,GAAG,CAAC,MAAM,GAAG,SAAS,CAAA;IACxB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK;YAAE,OAAO,CAAC,CAAC,MAAM,CAAA;QAC5C,OAAO,GAAG,CAAC,MAAM,CAAA;IACnB,CAAC;AACH,CAAC,CAAA;AAED,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAA;AAEhC,OAAO,CAAC,MAAM,EAAE,GAA6C,CAAC,CAAA;AAC9D,eAAe,GAAG,CAAC,OAAO,CAAA", "sourcesContent": ["import { relative, resolve } from 'node:path/posix'\nimport {\n  ConditionalValue,\n  ConditionalValueObject,\n  ExportsSubpaths,\n} from 'resolve-import'\nimport config from './config.js'\nimport dialects from './dialects.js'\nimport fail from './fail.js'\nimport pkg from './package.js'\nimport type { PolyfillSet } from './polyfills.js'\nimport polyfills from './polyfills.js'\nimport { resolveExport } from './resolve-export.js'\nimport { Package, TshyConfig, TshyExport } from './types.js'\nconst { esmDialects = [], commonjsDialects = [] } = config\n\nconst liveDev =\n  config.liveDev &&\n  process.env.npm_command !== 'publish' &&\n  process.env.npm_command !== 'pack'\n\nconst getTargetForDialectCondition = <T extends string>(\n  s: string | TshyExport | undefined | null,\n  dialect: T,\n  condition: T extends 'esm' ? 'import'\n  : T extends 'commonjs' ? 'require'\n  : T,\n  type: T extends 'esm' ? 'esm'\n  : T extends 'commonjs' ? 'commonjs'\n  : 'esm' | 'commonjs',\n  polyfills: Map<string, PolyfillSet> = new Map(),\n): string | undefined | null => {\n  if (s === undefined) return undefined\n  if (typeof s === 'string') {\n    // the excluded filename pattern\n    const xts = type === 'commonjs' ? '.mts' : '.cts'\n    if (s.endsWith(xts)) return undefined\n    const pf = dialect === 'commonjs' ? 'cjs' : dialect\n    const rel = relative(\n      resolve('./src'),\n      resolve(polyfills.get(pf)?.map.get(s) ?? s),\n    )\n    const target =\n      liveDev ? rel : rel.replace(/\\.([mc]?)tsx?$/, '.$1js')\n    return (\n      !s || !s.startsWith('./src/') ? s\n      : dialects.includes(type) ? `./dist/${dialect}/${target}`\n      : undefined\n    )\n  }\n  return resolveExport(s, [condition])\n}\n\nexport const getImpTarget = (\n  s: string | TshyExport | undefined | null,\n  polyfills: Map<string, PolyfillSet> = new Map(),\n) =>\n  getTargetForDialectCondition(s, 'esm', 'import', 'esm', polyfills)\n\nexport const getReqTarget = (\n  s: string | TshyExport | undefined | null,\n  polyfills: Map<string, PolyfillSet> = new Map(),\n) =>\n  getTargetForDialectCondition(\n    s,\n    'commonjs',\n    'require',\n    'commonjs',\n    polyfills,\n  )\n\nconst getExports = (\n  c: TshyConfig,\n): Record<string, ConditionalValue> => {\n  // by this time it always exports, will get the default if missing\n  /* c8 ignore start */\n  if (!c.exports) {\n    fail('no exports on tshy config (is there code in ./src?)')\n    return process.exit(1)\n  }\n  /* c8 ignore stop */\n  const e: Record<string, ConditionalValue> = {}\n  for (const [sub, s] of Object.entries(c.exports)) {\n    // external export, not built by us\n    if (\n      s !== null &&\n      (typeof s !== 'string' || !s.startsWith('./src/'))\n    ) {\n      // already been validated, just accept as-is\n      e[sub] = s as ConditionalValue\n      continue\n    }\n\n    /* c8 ignore next - already guarded */\n    if (s === null) continue\n\n    const impTarget = getImpTarget(s, polyfills)\n    const reqTarget = getReqTarget(s, polyfills)\n\n    // should be impossible\n    /* c8 ignore start */\n    if (!impTarget && !reqTarget) continue\n    /* c8 ignore stop */\n\n    const exp: ConditionalValueObject = (e[sub] = {})\n    if (impTarget) {\n      for (const d of esmDialects) {\n        const source = s && (polyfills.get(d)?.map.get(s) ?? s)\n\n        const target = getTargetForDialectCondition(\n          s,\n          d,\n          d,\n          'esm',\n          polyfills,\n        )\n        if (target) {\n          exp[d] =\n            liveDev ?\n              {\n                source,\n                ...getSourceDialects(source, c),\n                default: target,\n              }\n            : {\n                source,\n                ...getSourceDialects(source, c),\n                types: target.replace(/\\.js$/, '.d.ts'),\n                default: target,\n              }\n        }\n      }\n    }\n\n    if (reqTarget) {\n      for (const d of commonjsDialects) {\n        const source = s && (polyfills.get(d)?.map.get(s) ?? s)\n        const target = getTargetForDialectCondition(\n          s,\n          d,\n          d,\n          'commonjs',\n          polyfills,\n        )\n        if (target) {\n          exp[d] =\n            liveDev ?\n              {\n                source,\n                ...getSourceDialects(source, c),\n                default: target,\n              }\n            : {\n                source,\n                ...getSourceDialects(source, c),\n                types: target.replace(/\\.js$/, '.d.ts'),\n                default: target,\n              }\n        }\n      }\n    }\n    // put the default import/require after all the other special ones.\n    if (impTarget) {\n      exp.import =\n        liveDev ?\n          {\n            source: s,\n            default: impTarget,\n          }\n        : {\n            source: s,\n            types: impTarget.replace(/\\.(m?)js$/, '.d.$1ts'),\n            default: impTarget,\n          }\n    }\n    if (reqTarget) {\n      exp.require =\n        liveDev ?\n          {\n            source: s,\n            default: reqTarget,\n          }\n        : {\n            source: s,\n            types: reqTarget.replace(/\\.(c?)js$/, '.d.$1ts'),\n            default: reqTarget,\n          }\n    }\n  }\n  return e\n}\n\nconst getSourceDialects = (source: string, c: TshyConfig) => {\n  const { sourceDialects } = c\n  if (!sourceDialects) return {}\n  return Object.fromEntries(sourceDialects.map(s => [s, source]))\n}\n\nexport const setMain = (\n  c: TshyConfig | undefined,\n  pkg: Package & { exports: ExportsSubpaths },\n) => {\n  const mod = resolveExport(pkg.exports['.'], ['require'])\n  const main = c?.main ?? !!mod\n  if (main) {\n    if (!mod) {\n      fail(`could not resolve exports['.'] for tshy.main 'require'`)\n      return process.exit(1)\n    }\n    const types = resolveExport(pkg.exports['.'], [\n      'require',\n      'types',\n    ])\n    pkg.main = mod\n    if (types && types !== mod) pkg.types = types\n    else delete pkg.types\n  } else {\n    if (c && c.main !== false) delete c.main\n    delete pkg.main\n    delete pkg.types\n  }\n  pkg.type = pkg.type === 'commonjs' ? 'commonjs' : 'module'\n\n  // Set the package module to exports[\".\"]\n  const importMod = resolveExport(pkg.exports['.'], ['import'])\n  const module = c?.module ?? !!importMod\n  if (module) {\n    if (!importMod) {\n      fail(`could not resolve exports['.'] for tshy.module 'import'`)\n      return process.exit(1)\n    }\n\n    pkg.module = importMod\n  } else {\n    if (c && c.module !== false) delete c.module\n    delete pkg.module\n  }\n}\n\npkg.exports = getExports(config)\n\nsetMain(config, pkg as Package & { exports: ExportsSubpaths })\nexport default pkg.exports\n"]}