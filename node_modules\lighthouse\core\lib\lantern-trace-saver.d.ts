declare namespace _default {
    export const simulationNamesToIgnore: string[];
    export { convertNodeTimingsToTrace };
}
export default _default;
export type Node = import('./dependency-graph/base-node.js').Node;
export type CompleteNodeTiming = import('./dependency-graph/simulator/simulator.js').CompleteNodeTiming;
/**
 * @license Copyright 2018 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/** @typedef {import('./dependency-graph/base-node.js').Node} Node */
/** @typedef {import('./dependency-graph/simulator/simulator.js').CompleteNodeTiming} CompleteNodeTiming */
/**
 * @param {Map<Node, CompleteNodeTiming>} nodeTimings
 * @return {LH.Trace}
 */
declare function convertNodeTimingsToTrace(nodeTimings: Map<Node, CompleteNodeTiming>): LH.Trace;
//# sourceMappingURL=lantern-trace-saver.d.ts.map