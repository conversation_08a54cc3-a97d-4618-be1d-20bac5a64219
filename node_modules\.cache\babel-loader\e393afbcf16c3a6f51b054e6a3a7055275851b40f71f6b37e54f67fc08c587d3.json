{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useState from \"rc-util/es/hooks/useState\";\nimport ArrowLeftOutlined from \"@ant-design/icons/es/icons/ArrowLeftOutlined\";\nimport ArrowRightOutlined from \"@ant-design/icons/es/icons/ArrowRightOutlined\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { ConfigConsumer } from '../config-provider';\nimport Breadcrumb from '../breadcrumb';\nimport Avatar from '../avatar';\nimport Space from '../space';\nimport TransButton from '../_util/transButton';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nvar renderBack = function renderBack(prefixCls, backIcon, onBack) {\n  if (!backIcon || !onBack) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"PageHeader\"\n  }, function (_ref) {\n    var back = _ref.back;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-back\")\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      onClick: function onClick(e) {\n        onBack === null || onBack === void 0 ? void 0 : onBack(e);\n      },\n      className: \"\".concat(prefixCls, \"-back-button\"),\n      \"aria-label\": back\n    }, backIcon));\n  });\n};\nvar renderBreadcrumb = function renderBreadcrumb(breadcrumb) {\n  return /*#__PURE__*/React.createElement(Breadcrumb, breadcrumb);\n};\nvar getBackIcon = function getBackIcon(props) {\n  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'ltr';\n  if (props.backIcon !== undefined) {\n    return props.backIcon;\n  }\n  return direction === 'rtl' ? /*#__PURE__*/React.createElement(ArrowRightOutlined, null) : /*#__PURE__*/React.createElement(ArrowLeftOutlined, null);\n};\nvar renderTitle = function renderTitle(prefixCls, props) {\n  var direction = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ltr';\n  var title = props.title,\n    avatar = props.avatar,\n    subTitle = props.subTitle,\n    tags = props.tags,\n    extra = props.extra,\n    onBack = props.onBack;\n  var headingPrefixCls = \"\".concat(prefixCls, \"-heading\");\n  var hasHeading = title || subTitle || tags || extra; // If there is nothing, return a null\n\n  if (!hasHeading) {\n    return null;\n  }\n  var backIcon = getBackIcon(props, direction);\n  var backIconDom = renderBack(prefixCls, backIcon, onBack);\n  var hasTitle = backIconDom || avatar || hasHeading;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: headingPrefixCls\n  }, hasTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(headingPrefixCls, \"-left\")\n  }, backIconDom, avatar && /*#__PURE__*/React.createElement(Avatar, avatar), title && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-sub-title\"),\n    title: typeof subTitle === 'string' ? subTitle : undefined\n  }, subTitle), tags && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-tags\")\n  }, tags)), extra && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-extra\")\n  }, /*#__PURE__*/React.createElement(Space, null, extra)));\n};\nvar renderFooter = function renderFooter(prefixCls, footer) {\n  if (footer) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  return null;\n};\nvar renderChildren = function renderChildren(prefixCls, children) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children);\n};\nvar PageHeader = function PageHeader(props) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    compact = _useState2[0],\n    updateCompact = _useState2[1];\n  var onResize = function onResize(_ref2) {\n    var width = _ref2.width;\n    updateCompact(width < 768, true);\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref3) {\n    var _classNames;\n    var getPrefixCls = _ref3.getPrefixCls,\n      pageHeader = _ref3.pageHeader,\n      direction = _ref3.direction;\n    var _a;\n    var customizePrefixCls = props.prefixCls,\n      style = props.style,\n      footer = props.footer,\n      children = props.children,\n      breadcrumb = props.breadcrumb,\n      breadcrumbRender = props.breadcrumbRender,\n      customizeClassName = props.className;\n    var ghost = true; // Use `ghost` from `props` or from `ConfigProvider` instead.\n\n    if ('ghost' in props) {\n      ghost = props.ghost;\n    } else if (pageHeader && 'ghost' in pageHeader) {\n      ghost = pageHeader.ghost;\n    }\n    var prefixCls = getPrefixCls('page-header', customizePrefixCls);\n    var getDefaultBreadcrumbDom = function getDefaultBreadcrumbDom() {\n      if (breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.routes) {\n        return renderBreadcrumb(breadcrumb);\n      }\n      return null;\n    };\n    var defaultBreadcrumbDom = getDefaultBreadcrumbDom();\n    var isBreadcrumbComponent = breadcrumb && 'props' in breadcrumb; //  support breadcrumbRender function\n\n    var breadcrumbRenderDomFromProps = (_a = breadcrumbRender === null || breadcrumbRender === void 0 ? void 0 : breadcrumbRender(props, defaultBreadcrumbDom)) !== null && _a !== void 0 ? _a : defaultBreadcrumbDom;\n    var breadcrumbDom = isBreadcrumbComponent ? breadcrumb : breadcrumbRenderDomFromProps;\n    var className = classNames(prefixCls, customizeClassName, (_classNames = {\n      'has-breadcrumb': !!breadcrumbDom,\n      'has-footer': !!footer\n    }, _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), ghost), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), compact), _classNames));\n    return /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onResize\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: className,\n      style: style\n    }, breadcrumbDom, renderTitle(prefixCls, props, direction), children && renderChildren(prefixCls, children), renderFooter(prefixCls, footer)));\n  });\n};\nexport default PageHeader;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "React", "classNames", "useState", "ArrowLeftOutlined", "ArrowRightOutlined", "ResizeObserver", "ConfigConsumer", "Breadcrumb", "Avatar", "Space", "TransButton", "LocaleReceiver", "renderBack", "prefixCls", "backIcon", "onBack", "createElement", "componentName", "_ref", "back", "className", "concat", "onClick", "e", "renderBreadcrumb", "breadcrumb", "getBackIcon", "props", "direction", "arguments", "length", "undefined", "renderTitle", "title", "avatar", "subTitle", "tags", "extra", "headingPrefixCls", "hasHeading", "backIconDom", "hasTitle", "renderFooter", "footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "<PERSON><PERSON><PERSON><PERSON>", "_useState", "_useState2", "compact", "updateCompact", "onResize", "_ref2", "width", "_ref3", "_classNames", "getPrefixCls", "pageHeader", "_a", "customizePrefixCls", "style", "breadcrumbRender", "customizeClassName", "ghost", "getDefaultBreadcrumbDom", "routes", "defaultBreadcrumbDom", "isBreadcrumbComponent", "breadcrumbRenderDomFromProps", "breadcrumbDom"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/page-header/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useState from \"rc-util/es/hooks/useState\";\nimport ArrowLeftOutlined from \"@ant-design/icons/es/icons/ArrowLeftOutlined\";\nimport ArrowRightOutlined from \"@ant-design/icons/es/icons/ArrowRightOutlined\";\nimport ResizeObserver from 'rc-resize-observer';\nimport { ConfigConsumer } from '../config-provider';\nimport Breadcrumb from '../breadcrumb';\nimport Avatar from '../avatar';\nimport Space from '../space';\nimport TransButton from '../_util/transButton';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\n\nvar renderBack = function renderBack(prefixCls, backIcon, onBack) {\n  if (!backIcon || !onBack) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"PageHeader\"\n  }, function (_ref) {\n    var back = _ref.back;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-back\")\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      onClick: function onClick(e) {\n        onBack === null || onBack === void 0 ? void 0 : onBack(e);\n      },\n      className: \"\".concat(prefixCls, \"-back-button\"),\n      \"aria-label\": back\n    }, backIcon));\n  });\n};\n\nvar renderBreadcrumb = function renderBreadcrumb(breadcrumb) {\n  return /*#__PURE__*/React.createElement(Breadcrumb, breadcrumb);\n};\n\nvar getBackIcon = function getBackIcon(props) {\n  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'ltr';\n\n  if (props.backIcon !== undefined) {\n    return props.backIcon;\n  }\n\n  return direction === 'rtl' ? /*#__PURE__*/React.createElement(ArrowRightOutlined, null) : /*#__PURE__*/React.createElement(ArrowLeftOutlined, null);\n};\n\nvar renderTitle = function renderTitle(prefixCls, props) {\n  var direction = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ltr';\n  var title = props.title,\n      avatar = props.avatar,\n      subTitle = props.subTitle,\n      tags = props.tags,\n      extra = props.extra,\n      onBack = props.onBack;\n  var headingPrefixCls = \"\".concat(prefixCls, \"-heading\");\n  var hasHeading = title || subTitle || tags || extra; // If there is nothing, return a null\n\n  if (!hasHeading) {\n    return null;\n  }\n\n  var backIcon = getBackIcon(props, direction);\n  var backIconDom = renderBack(prefixCls, backIcon, onBack);\n  var hasTitle = backIconDom || avatar || hasHeading;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: headingPrefixCls\n  }, hasTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(headingPrefixCls, \"-left\")\n  }, backIconDom, avatar && /*#__PURE__*/React.createElement(Avatar, avatar), title && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-sub-title\"),\n    title: typeof subTitle === 'string' ? subTitle : undefined\n  }, subTitle), tags && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-tags\")\n  }, tags)), extra && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-extra\")\n  }, /*#__PURE__*/React.createElement(Space, null, extra)));\n};\n\nvar renderFooter = function renderFooter(prefixCls, footer) {\n  if (footer) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n\n  return null;\n};\n\nvar renderChildren = function renderChildren(prefixCls, children) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children);\n};\n\nvar PageHeader = function PageHeader(props) {\n  var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      compact = _useState2[0],\n      updateCompact = _useState2[1];\n\n  var onResize = function onResize(_ref2) {\n    var width = _ref2.width;\n    updateCompact(width < 768, true);\n  };\n\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref3) {\n    var _classNames;\n\n    var getPrefixCls = _ref3.getPrefixCls,\n        pageHeader = _ref3.pageHeader,\n        direction = _ref3.direction;\n\n    var _a;\n\n    var customizePrefixCls = props.prefixCls,\n        style = props.style,\n        footer = props.footer,\n        children = props.children,\n        breadcrumb = props.breadcrumb,\n        breadcrumbRender = props.breadcrumbRender,\n        customizeClassName = props.className;\n    var ghost = true; // Use `ghost` from `props` or from `ConfigProvider` instead.\n\n    if ('ghost' in props) {\n      ghost = props.ghost;\n    } else if (pageHeader && 'ghost' in pageHeader) {\n      ghost = pageHeader.ghost;\n    }\n\n    var prefixCls = getPrefixCls('page-header', customizePrefixCls);\n\n    var getDefaultBreadcrumbDom = function getDefaultBreadcrumbDom() {\n      if (breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.routes) {\n        return renderBreadcrumb(breadcrumb);\n      }\n\n      return null;\n    };\n\n    var defaultBreadcrumbDom = getDefaultBreadcrumbDom();\n    var isBreadcrumbComponent = breadcrumb && 'props' in breadcrumb; //  support breadcrumbRender function\n\n    var breadcrumbRenderDomFromProps = (_a = breadcrumbRender === null || breadcrumbRender === void 0 ? void 0 : breadcrumbRender(props, defaultBreadcrumbDom)) !== null && _a !== void 0 ? _a : defaultBreadcrumbDom;\n    var breadcrumbDom = isBreadcrumbComponent ? breadcrumb : breadcrumbRenderDomFromProps;\n    var className = classNames(prefixCls, customizeClassName, (_classNames = {\n      'has-breadcrumb': !!breadcrumbDom,\n      'has-footer': !!footer\n    }, _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), ghost), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), compact), _classNames));\n    return /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onResize\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: className,\n      style: style\n    }, breadcrumbDom, renderTitle(prefixCls, props, direction), children && renderChildren(prefixCls, children), renderFooter(prefixCls, footer)));\n  });\n};\n\nexport default PageHeader;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,cAAc,MAAM,mCAAmC;AAE9D,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAChE,IAAI,CAACD,QAAQ,IAAI,CAACC,MAAM,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,OAAO,aAAaf,KAAK,CAACgB,aAAa,CAACL,cAAc,EAAE;IACtDM,aAAa,EAAE;EACjB,CAAC,EAAE,UAAUC,IAAI,EAAE;IACjB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IACpB,OAAO,aAAanB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;MAC7CI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,OAAO;IACzC,CAAC,EAAE,aAAab,KAAK,CAACgB,aAAa,CAACN,WAAW,EAAE;MAC/CY,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3BR,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,CAAC,CAAC;MAC3D,CAAC;MACDH,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,cAAc,CAAC;MAC/C,YAAY,EAAEM;IAChB,CAAC,EAAEL,QAAQ,CAAC,CAAC;EACf,CAAC,CAAC;AACJ,CAAC;AAED,IAAIU,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;EAC3D,OAAO,aAAazB,KAAK,CAACgB,aAAa,CAACT,UAAU,EAAEkB,UAAU,CAAC;AACjE,CAAC;AAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAEzF,IAAIF,KAAK,CAACb,QAAQ,KAAKiB,SAAS,EAAE;IAChC,OAAOJ,KAAK,CAACb,QAAQ;EACvB;EAEA,OAAOc,SAAS,KAAK,KAAK,GAAG,aAAa5B,KAAK,CAACgB,aAAa,CAACZ,kBAAkB,EAAE,IAAI,CAAC,GAAG,aAAaJ,KAAK,CAACgB,aAAa,CAACb,iBAAiB,EAAE,IAAI,CAAC;AACrJ,CAAC;AAED,IAAI6B,WAAW,GAAG,SAASA,WAAWA,CAACnB,SAAS,EAAEc,KAAK,EAAE;EACvD,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAII,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBtB,MAAM,GAAGY,KAAK,CAACZ,MAAM;EACzB,IAAIuB,gBAAgB,GAAG,EAAE,CAACjB,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC;EACvD,IAAI0B,UAAU,GAAGN,KAAK,IAAIE,QAAQ,IAAIC,IAAI,IAAIC,KAAK,CAAC,CAAC;;EAErD,IAAI,CAACE,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAIzB,QAAQ,GAAGY,WAAW,CAACC,KAAK,EAAEC,SAAS,CAAC;EAC5C,IAAIY,WAAW,GAAG5B,UAAU,CAACC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,CAAC;EACzD,IAAI0B,QAAQ,GAAGD,WAAW,IAAIN,MAAM,IAAIK,UAAU;EAClD,OAAO,aAAavC,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7CI,SAAS,EAAEkB;EACb,CAAC,EAAEG,QAAQ,IAAI,aAAazC,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACrDI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACiB,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAEE,WAAW,EAAEN,MAAM,IAAI,aAAalC,KAAK,CAACgB,aAAa,CAACR,MAAM,EAAE0B,MAAM,CAAC,EAAED,KAAK,IAAI,aAAajC,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC5HI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACiB,gBAAgB,EAAE,QAAQ,CAAC;IAChDL,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGF;EAC7C,CAAC,EAAEE,KAAK,CAAC,EAAEE,QAAQ,IAAI,aAAanC,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC9DI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACiB,gBAAgB,EAAE,YAAY,CAAC;IACpDL,KAAK,EAAE,OAAOE,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGJ;EACnD,CAAC,EAAEI,QAAQ,CAAC,EAAEC,IAAI,IAAI,aAAapC,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC7DI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACiB,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAEF,IAAI,CAAC,CAAC,EAAEC,KAAK,IAAI,aAAarC,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC3DI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACiB,gBAAgB,EAAE,QAAQ;EACjD,CAAC,EAAE,aAAatC,KAAK,CAACgB,aAAa,CAACP,KAAK,EAAE,IAAI,EAAE4B,KAAK,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAAC7B,SAAS,EAAE8B,MAAM,EAAE;EAC1D,IAAIA,MAAM,EAAE;IACV,OAAO,aAAa3C,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;MAC7CI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE8B,MAAM,CAAC;EACZ;EAEA,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAC/B,SAAS,EAAEgC,QAAQ,EAAE;EAChE,OAAO,aAAa7C,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7CI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEgC,QAAQ,CAAC;AACd,CAAC;AAED,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACnB,KAAK,EAAE;EAC1C,IAAIoB,SAAS,GAAG7C,QAAQ,CAAC,KAAK,CAAC;IAC3B8C,UAAU,GAAGjD,cAAc,CAACgD,SAAS,EAAE,CAAC,CAAC;IACzCE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEjC,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACvBH,aAAa,CAACG,KAAK,GAAG,GAAG,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,OAAO,aAAarD,KAAK,CAACgB,aAAa,CAACV,cAAc,EAAE,IAAI,EAAE,UAAUgD,KAAK,EAAE;IAC7E,IAAIC,WAAW;IAEf,IAAIC,YAAY,GAAGF,KAAK,CAACE,YAAY;MACjCC,UAAU,GAAGH,KAAK,CAACG,UAAU;MAC7B7B,SAAS,GAAG0B,KAAK,CAAC1B,SAAS;IAE/B,IAAI8B,EAAE;IAEN,IAAIC,kBAAkB,GAAGhC,KAAK,CAACd,SAAS;MACpC+C,KAAK,GAAGjC,KAAK,CAACiC,KAAK;MACnBjB,MAAM,GAAGhB,KAAK,CAACgB,MAAM;MACrBE,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;MACzBpB,UAAU,GAAGE,KAAK,CAACF,UAAU;MAC7BoC,gBAAgB,GAAGlC,KAAK,CAACkC,gBAAgB;MACzCC,kBAAkB,GAAGnC,KAAK,CAACP,SAAS;IACxC,IAAI2C,KAAK,GAAG,IAAI,CAAC,CAAC;;IAElB,IAAI,OAAO,IAAIpC,KAAK,EAAE;MACpBoC,KAAK,GAAGpC,KAAK,CAACoC,KAAK;IACrB,CAAC,MAAM,IAAIN,UAAU,IAAI,OAAO,IAAIA,UAAU,EAAE;MAC9CM,KAAK,GAAGN,UAAU,CAACM,KAAK;IAC1B;IAEA,IAAIlD,SAAS,GAAG2C,YAAY,CAAC,aAAa,EAAEG,kBAAkB,CAAC;IAE/D,IAAIK,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;MAC/D,IAAIvC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACwC,MAAM,EAAE;QAC7E,OAAOzC,gBAAgB,CAACC,UAAU,CAAC;MACrC;MAEA,OAAO,IAAI;IACb,CAAC;IAED,IAAIyC,oBAAoB,GAAGF,uBAAuB,CAAC,CAAC;IACpD,IAAIG,qBAAqB,GAAG1C,UAAU,IAAI,OAAO,IAAIA,UAAU,CAAC,CAAC;;IAEjE,IAAI2C,4BAA4B,GAAG,CAACV,EAAE,GAAGG,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAClC,KAAK,EAAEuC,oBAAoB,CAAC,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGQ,oBAAoB;IACjN,IAAIG,aAAa,GAAGF,qBAAqB,GAAG1C,UAAU,GAAG2C,4BAA4B;IACrF,IAAIhD,SAAS,GAAGnB,UAAU,CAACY,SAAS,EAAEiD,kBAAkB,GAAGP,WAAW,GAAG;MACvE,gBAAgB,EAAE,CAAC,CAACc,aAAa;MACjC,YAAY,EAAE,CAAC,CAAC1B;IAClB,CAAC,EAAE7C,eAAe,CAACyD,WAAW,EAAE,EAAE,CAAClC,MAAM,CAACR,SAAS,EAAE,QAAQ,CAAC,EAAEkD,KAAK,CAAC,EAAEjE,eAAe,CAACyD,WAAW,EAAE,EAAE,CAAClC,MAAM,CAACR,SAAS,EAAE,MAAM,CAAC,EAAEe,SAAS,KAAK,KAAK,CAAC,EAAE9B,eAAe,CAACyD,WAAW,EAAE,EAAE,CAAClC,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC,EAAEoC,OAAO,CAAC,EAAEM,WAAW,CAAC,CAAC;IAC/O,OAAO,aAAavD,KAAK,CAACgB,aAAa,CAACX,cAAc,EAAE;MACtD8C,QAAQ,EAAEA;IACZ,CAAC,EAAE,aAAanD,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;MACzCI,SAAS,EAAEA,SAAS;MACpBwC,KAAK,EAAEA;IACT,CAAC,EAAES,aAAa,EAAErC,WAAW,CAACnB,SAAS,EAAEc,KAAK,EAAEC,SAAS,CAAC,EAAEiB,QAAQ,IAAID,cAAc,CAAC/B,SAAS,EAAEgC,QAAQ,CAAC,EAAEH,YAAY,CAAC7B,SAAS,EAAE8B,MAAM,CAAC,CAAC,CAAC;EAChJ,CAAC,CAAC;AACJ,CAAC;AAED,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}