{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport * as React from 'react';\nimport RcUpload from 'rc-upload';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport UploadList from './UploadList';\nimport { file2Obj, getFileItem, removeFileItem, updateFileList } from './utils';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nexport var LIST_IGNORE = \"__LIST_IGNORE_\".concat(Date.now(), \"__\");\nvar InternalUpload = function InternalUpload(props, ref) {\n  var _classNames2;\n  var fileList = props.fileList,\n    defaultFileList = props.defaultFileList,\n    onRemove = props.onRemove,\n    showUploadList = props.showUploadList,\n    listType = props.listType,\n    onPreview = props.onPreview,\n    onDownload = props.onDownload,\n    onChange = props.onChange,\n    onDrop = props.onDrop,\n    previewFile = props.previewFile,\n    disabled = props.disabled,\n    propLocale = props.locale,\n    iconRender = props.iconRender,\n    isImageUrl = props.isImageUrl,\n    progress = props.progress,\n    customizePrefixCls = props.prefixCls,\n    className = props.className,\n    type = props.type,\n    children = props.children,\n    style = props.style,\n    itemRender = props.itemRender,\n    maxCount = props.maxCount;\n  var _useMergedState = useMergedState(defaultFileList || [], {\n      value: fileList,\n      postState: function postState(list) {\n        return list !== null && list !== void 0 ? list : [];\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedFileList = _useMergedState2[0],\n    setMergedFileList = _useMergedState2[1];\n  var _React$useState = React.useState('drop'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    dragState = _React$useState2[0],\n    setDragState = _React$useState2[1];\n  var upload = React.useRef();\n  React.useEffect(function () {\n    devWarning('fileList' in props || !('value' in props), 'Upload', '`value` is not a valid prop, do you mean `fileList`?');\n    devWarning(!('transformFile' in props), 'Upload', '`transformFile` is deprecated. Please use `beforeUpload` directly.');\n  }, []); // Control mode will auto fill file uid if not provided\n\n  React.useMemo(function () {\n    var timestamp = Date.now();\n    (fileList || []).forEach(function (file, index) {\n      if (!file.uid && !Object.isFrozen(file)) {\n        file.uid = \"__AUTO__\".concat(timestamp, \"_\").concat(index, \"__\");\n      }\n    });\n  }, [fileList]);\n  var onInternalChange = function onInternalChange(file, changedFileList, event) {\n    var cloneList = _toConsumableArray(changedFileList); // Cut to match count\n\n    if (maxCount === 1) {\n      cloneList = cloneList.slice(-1);\n    } else if (maxCount) {\n      cloneList = cloneList.slice(0, maxCount);\n    }\n    setMergedFileList(cloneList);\n    var changeInfo = {\n      file: file,\n      fileList: cloneList\n    };\n    if (event) {\n      changeInfo.event = event;\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);\n  };\n  var mergedBeforeUpload = function mergedBeforeUpload(file, fileListArgs) {\n    return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      var beforeUpload, transformFile, parsedFile, result;\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              beforeUpload = props.beforeUpload, transformFile = props.transformFile;\n              parsedFile = file;\n              if (!beforeUpload) {\n                _context.next = 13;\n                break;\n              }\n              _context.next = 5;\n              return beforeUpload(file, fileListArgs);\n            case 5:\n              result = _context.sent;\n              if (!(result === false)) {\n                _context.next = 8;\n                break;\n              }\n              return _context.abrupt(\"return\", false);\n            case 8:\n              // Hack for LIST_IGNORE, we add additional info to remove from the list\n              delete file[LIST_IGNORE];\n              if (!(result === LIST_IGNORE)) {\n                _context.next = 12;\n                break;\n              }\n              Object.defineProperty(file, LIST_IGNORE, {\n                value: true,\n                configurable: true\n              });\n              return _context.abrupt(\"return\", false);\n            case 12:\n              if (_typeof(result) === 'object' && result) {\n                parsedFile = result;\n              }\n            case 13:\n              if (!transformFile) {\n                _context.next = 17;\n                break;\n              }\n              _context.next = 16;\n              return transformFile(parsedFile);\n            case 16:\n              parsedFile = _context.sent;\n            case 17:\n              return _context.abrupt(\"return\", parsedFile);\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee);\n    }));\n  };\n  var onBatchStart = function onBatchStart(batchFileInfoList) {\n    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list\n    var filteredFileInfoList = batchFileInfoList.filter(function (info) {\n      return !info.file[LIST_IGNORE];\n    }); // Nothing to do since no file need upload\n\n    if (!filteredFileInfoList.length) {\n      return;\n    }\n    var objectFileList = filteredFileInfoList.map(function (info) {\n      return file2Obj(info.file);\n    }); // Concat new files with prev files\n\n    var newFileList = _toConsumableArray(mergedFileList);\n    objectFileList.forEach(function (fileObj) {\n      // Replace file if exist\n      newFileList = updateFileList(fileObj, newFileList);\n    });\n    objectFileList.forEach(function (fileObj, index) {\n      // Repeat trigger `onChange` event for compatible\n      var triggerFileObj = fileObj;\n      if (!filteredFileInfoList[index].parsedFile) {\n        // `beforeUpload` return false\n        var originFileObj = fileObj.originFileObj;\n        var clone;\n        try {\n          clone = new File([originFileObj], originFileObj.name, {\n            type: originFileObj.type\n          });\n        } catch (e) {\n          clone = new Blob([originFileObj], {\n            type: originFileObj.type\n          });\n          clone.name = originFileObj.name;\n          clone.lastModifiedDate = new Date();\n          clone.lastModified = new Date().getTime();\n        }\n        clone.uid = fileObj.uid;\n        triggerFileObj = clone;\n      } else {\n        // Inject `uploading` status\n        fileObj.status = 'uploading';\n      }\n      onInternalChange(triggerFileObj, newFileList);\n    });\n  };\n  var onSuccess = function onSuccess(response, file, xhr) {\n    try {\n      if (typeof response === 'string') {\n        response = JSON.parse(response);\n      }\n    } catch (e) {\n      /* do nothing */\n    } // removed\n\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'done';\n    targetItem.percent = 100;\n    targetItem.response = response;\n    targetItem.xhr = xhr;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var onProgress = function onProgress(e, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.status = 'uploading';\n    targetItem.percent = e.percent;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList, e);\n  };\n  var onError = function onError(error, response, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n    var targetItem = file2Obj(file);\n    targetItem.error = error;\n    targetItem.response = response;\n    targetItem.status = 'error';\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n  var handleRemove = function handleRemove(file) {\n    var currentFile;\n    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(function (ret) {\n      var _a; // Prevent removing file\n\n      if (ret === false) {\n        return;\n      }\n      var removedFileList = removeFileItem(file, mergedFileList);\n      if (removedFileList) {\n        currentFile = _extends(_extends({}, file), {\n          status: 'removed'\n        });\n        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(function (item) {\n          var matchKey = currentFile.uid !== undefined ? 'uid' : 'name';\n          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {\n            item.status = 'removed';\n          }\n        });\n        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);\n        onInternalChange(currentFile, removedFileList);\n      }\n    });\n  };\n  var onFileDrop = function onFileDrop(e) {\n    setDragState(e.type);\n    if (e.type === 'drop') {\n      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);\n    }\n  }; // Test needs\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      onBatchStart: onBatchStart,\n      onSuccess: onSuccess,\n      onProgress: onProgress,\n      onError: onError,\n      fileList: mergedFileList,\n      upload: upload.current\n    };\n  });\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('upload', customizePrefixCls);\n  var rcUploadProps = _extends(_extends({\n    onBatchStart: onBatchStart,\n    onError: onError,\n    onProgress: onProgress,\n    onSuccess: onSuccess\n  }, props), {\n    prefixCls: prefixCls,\n    beforeUpload: mergedBeforeUpload,\n    onChange: undefined\n  });\n  delete rcUploadProps.className;\n  delete rcUploadProps.style; // Remove id to avoid open by label when trigger is hidden\n  // !children: https://github.com/ant-design/ant-design/issues/14298\n  // disabled: https://github.com/ant-design/ant-design/issues/16478\n  //           https://github.com/ant-design/ant-design/issues/24197\n\n  if (!children || disabled) {\n    delete rcUploadProps.id;\n  }\n  var renderUploadList = function renderUploadList(button, buttonVisible) {\n    return showUploadList ? /*#__PURE__*/React.createElement(LocaleReceiver, {\n      componentName: \"Upload\",\n      defaultLocale: defaultLocale.Upload\n    }, function (locale) {\n      var _ref = typeof showUploadList === 'boolean' ? {} : showUploadList,\n        showRemoveIcon = _ref.showRemoveIcon,\n        showPreviewIcon = _ref.showPreviewIcon,\n        showDownloadIcon = _ref.showDownloadIcon,\n        removeIcon = _ref.removeIcon,\n        previewIcon = _ref.previewIcon,\n        downloadIcon = _ref.downloadIcon;\n      return /*#__PURE__*/React.createElement(UploadList, {\n        prefixCls: prefixCls,\n        listType: listType,\n        items: mergedFileList,\n        previewFile: previewFile,\n        onPreview: onPreview,\n        onDownload: onDownload,\n        onRemove: handleRemove,\n        showRemoveIcon: !disabled && showRemoveIcon,\n        showPreviewIcon: showPreviewIcon,\n        showDownloadIcon: showDownloadIcon,\n        removeIcon: removeIcon,\n        previewIcon: previewIcon,\n        downloadIcon: downloadIcon,\n        iconRender: iconRender,\n        locale: _extends(_extends({}, locale), propLocale),\n        isImageUrl: isImageUrl,\n        progress: progress,\n        appendAction: button,\n        appendActionVisible: buttonVisible,\n        itemRender: itemRender\n      });\n    }) : button;\n  };\n  if (type === 'drag') {\n    var _classNames;\n    var dragCls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-uploading\"), mergedFileList.some(function (file) {\n      return file.status === 'uploading';\n    })), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-hover\"), dragState === 'dragover'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(\"div\", {\n      className: dragCls,\n      onDrop: onFileDrop,\n      onDragOver: onFileDrop,\n      onDragLeave: onFileDrop,\n      style: style\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload,\n      className: \"\".concat(prefixCls, \"-btn\")\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-drag-container\")\n    }, children))), renderUploadList());\n  }\n  var uploadButtonCls = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select-\").concat(listType), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2));\n  var renderUploadButton = function renderUploadButton(uploadButtonStyle) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: uploadButtonCls,\n      style: uploadButtonStyle\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload\n    })));\n  };\n  if (listType === 'picture-card') {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(prefixCls, \"-picture-card-wrapper\"), className)\n    }, renderUploadList(renderUploadButton(), !!children));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className\n  }, renderUploadButton(children ? undefined : {\n    display: 'none'\n  }), renderUploadList());\n};\nvar Upload = /*#__PURE__*/React.forwardRef(InternalUpload);\nUpload.displayName = 'Upload';\nUpload.defaultProps = {\n  type: 'select',\n  multiple: false,\n  action: '',\n  data: {},\n  accept: '',\n  showUploadList: true,\n  listType: 'text',\n  className: '',\n  disabled: false,\n  supportServerRender: true\n};\nexport default Upload;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_typeof", "_toConsumableArray", "_slicedToArray", "_regeneratorRuntime", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "React", "RcUpload", "useMergedState", "classNames", "UploadList", "file2Obj", "getFileItem", "removeFileItem", "updateFileList", "LocaleReceiver", "defaultLocale", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "LIST_IGNORE", "concat", "Date", "now", "InternalUpload", "props", "ref", "_classNames2", "fileList", "defaultFileList", "onRemove", "showUploadList", "listType", "onPreview", "onDownload", "onChange", "onDrop", "previewFile", "disabled", "propLocale", "locale", "iconRender", "isImageUrl", "progress", "customizePrefixCls", "prefixCls", "className", "type", "children", "style", "itemRender", "maxCount", "_useMergedState", "postState", "list", "_useMergedState2", "mergedFileList", "setMergedFileList", "_React$useState", "useState", "_React$useState2", "dragState", "setDragState", "upload", "useRef", "useEffect", "useMemo", "timestamp", "for<PERSON>ach", "file", "index", "uid", "Object", "isFrozen", "onInternalChange", "changedFileList", "event", "cloneList", "slice", "changeInfo", "mergedBeforeUpload", "fileListArgs", "mark", "_callee", "beforeUpload", "transformFile", "parsedFile", "wrap", "_callee$", "_context", "prev", "sent", "abrupt", "defineProperty", "configurable", "stop", "onBatchStart", "batchFileInfoList", "filteredFileInfoList", "filter", "info", "length", "objectFileList", "map", "newFileList", "fileObj", "triggerFileObj", "originFileObj", "clone", "File", "name", "Blob", "lastModifiedDate", "lastModified", "getTime", "status", "onSuccess", "response", "xhr", "JSON", "parse", "targetItem", "percent", "nextFileList", "onProgress", "onError", "error", "handleRemove", "currentFile", "ret", "_a", "removedFileList", "item", "matchKey", "undefined", "current", "abort", "onFileDrop", "useImperativeHandle", "_React$useContext", "useContext", "getPrefixCls", "direction", "rcUploadProps", "id", "renderUploadList", "button", "buttonVisible", "createElement", "componentName", "Upload", "_ref", "showRemoveIcon", "showPreviewIcon", "showDownloadIcon", "removeIcon", "previewIcon", "downloadIcon", "items", "appendAction", "appendActionVisible", "_classNames", "dragCls", "some", "onDragOver", "onDragLeave", "uploadButtonCls", "renderUploadButton", "uploadButtonStyle", "display", "forwardRef", "displayName", "defaultProps", "multiple", "action", "data", "accept", "supportServerRender"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/upload/Upload.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nimport * as React from 'react';\nimport RcUpload from 'rc-upload';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport UploadList from './UploadList';\nimport { file2Obj, getFileItem, removeFileItem, updateFileList } from './utils';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nexport var LIST_IGNORE = \"__LIST_IGNORE_\".concat(Date.now(), \"__\");\n\nvar InternalUpload = function InternalUpload(props, ref) {\n  var _classNames2;\n\n  var fileList = props.fileList,\n      defaultFileList = props.defaultFileList,\n      onRemove = props.onRemove,\n      showUploadList = props.showUploadList,\n      listType = props.listType,\n      onPreview = props.onPreview,\n      onDownload = props.onDownload,\n      onChange = props.onChange,\n      onDrop = props.onDrop,\n      previewFile = props.previewFile,\n      disabled = props.disabled,\n      propLocale = props.locale,\n      iconRender = props.iconRender,\n      isImageUrl = props.isImageUrl,\n      progress = props.progress,\n      customizePrefixCls = props.prefixCls,\n      className = props.className,\n      type = props.type,\n      children = props.children,\n      style = props.style,\n      itemRender = props.itemRender,\n      maxCount = props.maxCount;\n\n  var _useMergedState = useMergedState(defaultFileList || [], {\n    value: fileList,\n    postState: function postState(list) {\n      return list !== null && list !== void 0 ? list : [];\n    }\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      mergedFileList = _useMergedState2[0],\n      setMergedFileList = _useMergedState2[1];\n\n  var _React$useState = React.useState('drop'),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      dragState = _React$useState2[0],\n      setDragState = _React$useState2[1];\n\n  var upload = React.useRef();\n  React.useEffect(function () {\n    devWarning('fileList' in props || !('value' in props), 'Upload', '`value` is not a valid prop, do you mean `fileList`?');\n    devWarning(!('transformFile' in props), 'Upload', '`transformFile` is deprecated. Please use `beforeUpload` directly.');\n  }, []); // Control mode will auto fill file uid if not provided\n\n  React.useMemo(function () {\n    var timestamp = Date.now();\n    (fileList || []).forEach(function (file, index) {\n      if (!file.uid && !Object.isFrozen(file)) {\n        file.uid = \"__AUTO__\".concat(timestamp, \"_\").concat(index, \"__\");\n      }\n    });\n  }, [fileList]);\n\n  var onInternalChange = function onInternalChange(file, changedFileList, event) {\n    var cloneList = _toConsumableArray(changedFileList); // Cut to match count\n\n\n    if (maxCount === 1) {\n      cloneList = cloneList.slice(-1);\n    } else if (maxCount) {\n      cloneList = cloneList.slice(0, maxCount);\n    }\n\n    setMergedFileList(cloneList);\n    var changeInfo = {\n      file: file,\n      fileList: cloneList\n    };\n\n    if (event) {\n      changeInfo.event = event;\n    }\n\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);\n  };\n\n  var mergedBeforeUpload = function mergedBeforeUpload(file, fileListArgs) {\n    return __awaiter(void 0, void 0, void 0, /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      var beforeUpload, transformFile, parsedFile, result;\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              beforeUpload = props.beforeUpload, transformFile = props.transformFile;\n              parsedFile = file;\n\n              if (!beforeUpload) {\n                _context.next = 13;\n                break;\n              }\n\n              _context.next = 5;\n              return beforeUpload(file, fileListArgs);\n\n            case 5:\n              result = _context.sent;\n\n              if (!(result === false)) {\n                _context.next = 8;\n                break;\n              }\n\n              return _context.abrupt(\"return\", false);\n\n            case 8:\n              // Hack for LIST_IGNORE, we add additional info to remove from the list\n              delete file[LIST_IGNORE];\n\n              if (!(result === LIST_IGNORE)) {\n                _context.next = 12;\n                break;\n              }\n\n              Object.defineProperty(file, LIST_IGNORE, {\n                value: true,\n                configurable: true\n              });\n              return _context.abrupt(\"return\", false);\n\n            case 12:\n              if (_typeof(result) === 'object' && result) {\n                parsedFile = result;\n              }\n\n            case 13:\n              if (!transformFile) {\n                _context.next = 17;\n                break;\n              }\n\n              _context.next = 16;\n              return transformFile(parsedFile);\n\n            case 16:\n              parsedFile = _context.sent;\n\n            case 17:\n              return _context.abrupt(\"return\", parsedFile);\n\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee);\n    }));\n  };\n\n  var onBatchStart = function onBatchStart(batchFileInfoList) {\n    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list\n    var filteredFileInfoList = batchFileInfoList.filter(function (info) {\n      return !info.file[LIST_IGNORE];\n    }); // Nothing to do since no file need upload\n\n    if (!filteredFileInfoList.length) {\n      return;\n    }\n\n    var objectFileList = filteredFileInfoList.map(function (info) {\n      return file2Obj(info.file);\n    }); // Concat new files with prev files\n\n    var newFileList = _toConsumableArray(mergedFileList);\n\n    objectFileList.forEach(function (fileObj) {\n      // Replace file if exist\n      newFileList = updateFileList(fileObj, newFileList);\n    });\n    objectFileList.forEach(function (fileObj, index) {\n      // Repeat trigger `onChange` event for compatible\n      var triggerFileObj = fileObj;\n\n      if (!filteredFileInfoList[index].parsedFile) {\n        // `beforeUpload` return false\n        var originFileObj = fileObj.originFileObj;\n        var clone;\n\n        try {\n          clone = new File([originFileObj], originFileObj.name, {\n            type: originFileObj.type\n          });\n        } catch (e) {\n          clone = new Blob([originFileObj], {\n            type: originFileObj.type\n          });\n          clone.name = originFileObj.name;\n          clone.lastModifiedDate = new Date();\n          clone.lastModified = new Date().getTime();\n        }\n\n        clone.uid = fileObj.uid;\n        triggerFileObj = clone;\n      } else {\n        // Inject `uploading` status\n        fileObj.status = 'uploading';\n      }\n\n      onInternalChange(triggerFileObj, newFileList);\n    });\n  };\n\n  var onSuccess = function onSuccess(response, file, xhr) {\n    try {\n      if (typeof response === 'string') {\n        response = JSON.parse(response);\n      }\n    } catch (e) {\n      /* do nothing */\n    } // removed\n\n\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n\n    var targetItem = file2Obj(file);\n    targetItem.status = 'done';\n    targetItem.percent = 100;\n    targetItem.response = response;\n    targetItem.xhr = xhr;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n\n  var onProgress = function onProgress(e, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n\n    var targetItem = file2Obj(file);\n    targetItem.status = 'uploading';\n    targetItem.percent = e.percent;\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList, e);\n  };\n\n  var onError = function onError(error, response, file) {\n    // removed\n    if (!getFileItem(file, mergedFileList)) {\n      return;\n    }\n\n    var targetItem = file2Obj(file);\n    targetItem.error = error;\n    targetItem.response = response;\n    targetItem.status = 'error';\n    var nextFileList = updateFileList(targetItem, mergedFileList);\n    onInternalChange(targetItem, nextFileList);\n  };\n\n  var handleRemove = function handleRemove(file) {\n    var currentFile;\n    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(function (ret) {\n      var _a; // Prevent removing file\n\n\n      if (ret === false) {\n        return;\n      }\n\n      var removedFileList = removeFileItem(file, mergedFileList);\n\n      if (removedFileList) {\n        currentFile = _extends(_extends({}, file), {\n          status: 'removed'\n        });\n        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(function (item) {\n          var matchKey = currentFile.uid !== undefined ? 'uid' : 'name';\n\n          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {\n            item.status = 'removed';\n          }\n        });\n        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);\n        onInternalChange(currentFile, removedFileList);\n      }\n    });\n  };\n\n  var onFileDrop = function onFileDrop(e) {\n    setDragState(e.type);\n\n    if (e.type === 'drop') {\n      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);\n    }\n  }; // Test needs\n\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      onBatchStart: onBatchStart,\n      onSuccess: onSuccess,\n      onProgress: onProgress,\n      onError: onError,\n      fileList: mergedFileList,\n      upload: upload.current\n    };\n  });\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var prefixCls = getPrefixCls('upload', customizePrefixCls);\n\n  var rcUploadProps = _extends(_extends({\n    onBatchStart: onBatchStart,\n    onError: onError,\n    onProgress: onProgress,\n    onSuccess: onSuccess\n  }, props), {\n    prefixCls: prefixCls,\n    beforeUpload: mergedBeforeUpload,\n    onChange: undefined\n  });\n\n  delete rcUploadProps.className;\n  delete rcUploadProps.style; // Remove id to avoid open by label when trigger is hidden\n  // !children: https://github.com/ant-design/ant-design/issues/14298\n  // disabled: https://github.com/ant-design/ant-design/issues/16478\n  //           https://github.com/ant-design/ant-design/issues/24197\n\n  if (!children || disabled) {\n    delete rcUploadProps.id;\n  }\n\n  var renderUploadList = function renderUploadList(button, buttonVisible) {\n    return showUploadList ? /*#__PURE__*/React.createElement(LocaleReceiver, {\n      componentName: \"Upload\",\n      defaultLocale: defaultLocale.Upload\n    }, function (locale) {\n      var _ref = typeof showUploadList === 'boolean' ? {} : showUploadList,\n          showRemoveIcon = _ref.showRemoveIcon,\n          showPreviewIcon = _ref.showPreviewIcon,\n          showDownloadIcon = _ref.showDownloadIcon,\n          removeIcon = _ref.removeIcon,\n          previewIcon = _ref.previewIcon,\n          downloadIcon = _ref.downloadIcon;\n\n      return /*#__PURE__*/React.createElement(UploadList, {\n        prefixCls: prefixCls,\n        listType: listType,\n        items: mergedFileList,\n        previewFile: previewFile,\n        onPreview: onPreview,\n        onDownload: onDownload,\n        onRemove: handleRemove,\n        showRemoveIcon: !disabled && showRemoveIcon,\n        showPreviewIcon: showPreviewIcon,\n        showDownloadIcon: showDownloadIcon,\n        removeIcon: removeIcon,\n        previewIcon: previewIcon,\n        downloadIcon: downloadIcon,\n        iconRender: iconRender,\n        locale: _extends(_extends({}, locale), propLocale),\n        isImageUrl: isImageUrl,\n        progress: progress,\n        appendAction: button,\n        appendActionVisible: buttonVisible,\n        itemRender: itemRender\n      });\n    }) : button;\n  };\n\n  if (type === 'drag') {\n    var _classNames;\n\n    var dragCls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-uploading\"), mergedFileList.some(function (file) {\n      return file.status === 'uploading';\n    })), _defineProperty(_classNames, \"\".concat(prefixCls, \"-drag-hover\"), dragState === 'dragover'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n    return /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(\"div\", {\n      className: dragCls,\n      onDrop: onFileDrop,\n      onDragOver: onFileDrop,\n      onDragLeave: onFileDrop,\n      style: style\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload,\n      className: \"\".concat(prefixCls, \"-btn\")\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-drag-container\")\n    }, children))), renderUploadList());\n  }\n\n  var uploadButtonCls = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select\"), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-select-\").concat(listType), true), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2));\n\n  var renderUploadButton = function renderUploadButton(uploadButtonStyle) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: uploadButtonCls,\n      style: uploadButtonStyle\n    }, /*#__PURE__*/React.createElement(RcUpload, _extends({}, rcUploadProps, {\n      ref: upload\n    })));\n  };\n\n  if (listType === 'picture-card') {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(prefixCls, \"-picture-card-wrapper\"), className)\n    }, renderUploadList(renderUploadButton(), !!children));\n  }\n\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className\n  }, renderUploadButton(children ? undefined : {\n    display: 'none'\n  }), renderUploadList());\n};\n\nvar Upload = /*#__PURE__*/React.forwardRef(InternalUpload);\nUpload.displayName = 'Upload';\nUpload.defaultProps = {\n  type: 'select',\n  multiple: false,\n  action: '',\n  data: {},\n  accept: '',\n  showUploadList: true,\n  listType: 'text',\n  className: '',\n  disabled: false,\n  supportServerRender: true\n};\nexport default Upload;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,mBAAmB,MAAM,4BAA4B;AAE5D,IAAIC,SAAS,GAAG,IAAI,IAAI,IAAI,CAACA,SAAS,IAAI,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IACpB,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAC3DA,OAAO,CAACD,KAAK,CAAC;IAChB,CAAC,CAAC;EACJ;EAEA,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACzD,SAASC,SAASA,CAACJ,KAAK,EAAE;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IAEA,SAASC,QAAQA,CAACR,KAAK,EAAE;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IAEA,SAASF,IAAIA,CAACI,MAAM,EAAE;MACpBA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IACrF;IAEAH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,KAAKO,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,QAAQ,SAAS;AAC/E,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAO,IAAIC,WAAW,GAAG,gBAAgB,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAElE,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,IAAIC,YAAY;EAEhB,IAAIC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,eAAe,GAAGJ,KAAK,CAACI,eAAe;IACvCC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,UAAU,GAAGT,KAAK,CAACS,UAAU;IAC7BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACe,MAAM;IACzBC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7BC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,kBAAkB,GAAGnB,KAAK,CAACoB,SAAS;IACpCC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,IAAI,GAAGtB,KAAK,CAACsB,IAAI;IACjBC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;IACzBC,KAAK,GAAGxB,KAAK,CAACwB,KAAK;IACnBC,UAAU,GAAGzB,KAAK,CAACyB,UAAU;IAC7BC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;EAE7B,IAAIC,eAAe,GAAG3C,cAAc,CAACoB,eAAe,IAAI,EAAE,EAAE;MAC1DnC,KAAK,EAAEkC,QAAQ;MACfyB,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE;MACrD;IACF,CAAC,CAAC;IACEC,gBAAgB,GAAGrE,cAAc,CAACkE,eAAe,EAAE,CAAC,CAAC;IACrDI,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,eAAe,GAAGnD,KAAK,CAACoD,QAAQ,CAAC,MAAM,CAAC;IACxCC,gBAAgB,GAAG1E,cAAc,CAACwE,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,MAAM,GAAGxD,KAAK,CAACyD,MAAM,CAAC,CAAC;EAC3BzD,KAAK,CAAC0D,SAAS,CAAC,YAAY;IAC1B9C,UAAU,CAAC,UAAU,IAAIM,KAAK,IAAI,EAAE,OAAO,IAAIA,KAAK,CAAC,EAAE,QAAQ,EAAE,sDAAsD,CAAC;IACxHN,UAAU,CAAC,EAAE,eAAe,IAAIM,KAAK,CAAC,EAAE,QAAQ,EAAE,oEAAoE,CAAC;EACzH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERlB,KAAK,CAAC2D,OAAO,CAAC,YAAY;IACxB,IAAIC,SAAS,GAAG7C,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,CAACK,QAAQ,IAAI,EAAE,EAAEwC,OAAO,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;MAC9C,IAAI,CAACD,IAAI,CAACE,GAAG,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACJ,IAAI,CAAC,EAAE;QACvCA,IAAI,CAACE,GAAG,GAAG,UAAU,CAAClD,MAAM,CAAC8C,SAAS,EAAE,GAAG,CAAC,CAAC9C,MAAM,CAACiD,KAAK,EAAE,IAAI,CAAC;MAClE;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1C,QAAQ,CAAC,CAAC;EAEd,IAAI8C,gBAAgB,GAAG,SAASA,gBAAgBA,CAACL,IAAI,EAAEM,eAAe,EAAEC,KAAK,EAAE;IAC7E,IAAIC,SAAS,GAAG5F,kBAAkB,CAAC0F,eAAe,CAAC,CAAC,CAAC;;IAGrD,IAAIxB,QAAQ,KAAK,CAAC,EAAE;MAClB0B,SAAS,GAAGA,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI3B,QAAQ,EAAE;MACnB0B,SAAS,GAAGA,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE3B,QAAQ,CAAC;IAC1C;IAEAM,iBAAiB,CAACoB,SAAS,CAAC;IAC5B,IAAIE,UAAU,GAAG;MACfV,IAAI,EAAEA,IAAI;MACVzC,QAAQ,EAAEiD;IACZ,CAAC;IAED,IAAID,KAAK,EAAE;MACTG,UAAU,CAACH,KAAK,GAAGA,KAAK;IAC1B;IAEAzC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC4C,UAAU,CAAC;EAC1E,CAAC;EAED,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACX,IAAI,EAAEY,YAAY,EAAE;IACvE,OAAO7F,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAaD,mBAAmB,CAAC+F,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;MAChG,IAAIC,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAEnF,MAAM;MACnD,OAAOhB,mBAAmB,CAACoG,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;QAC1D,OAAO,CAAC,EAAE;UACR,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACzF,IAAI;YACnC,KAAK,CAAC;cACJoF,YAAY,GAAG3D,KAAK,CAAC2D,YAAY,EAAEC,aAAa,GAAG5D,KAAK,CAAC4D,aAAa;cACtEC,UAAU,GAAGjB,IAAI;cAEjB,IAAI,CAACe,YAAY,EAAE;gBACjBK,QAAQ,CAACzF,IAAI,GAAG,EAAE;gBAClB;cACF;cAEAyF,QAAQ,CAACzF,IAAI,GAAG,CAAC;cACjB,OAAOoF,YAAY,CAACf,IAAI,EAAEY,YAAY,CAAC;YAEzC,KAAK,CAAC;cACJ9E,MAAM,GAAGsF,QAAQ,CAACE,IAAI;cAEtB,IAAI,EAAExF,MAAM,KAAK,KAAK,CAAC,EAAE;gBACvBsF,QAAQ,CAACzF,IAAI,GAAG,CAAC;gBACjB;cACF;cAEA,OAAOyF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;YAEzC,KAAK,CAAC;cACJ;cACA,OAAOvB,IAAI,CAACjD,WAAW,CAAC;cAExB,IAAI,EAAEjB,MAAM,KAAKiB,WAAW,CAAC,EAAE;gBAC7BqE,QAAQ,CAACzF,IAAI,GAAG,EAAE;gBAClB;cACF;cAEAwE,MAAM,CAACqB,cAAc,CAACxB,IAAI,EAAEjD,WAAW,EAAE;gBACvC1B,KAAK,EAAE,IAAI;gBACXoG,YAAY,EAAE;cAChB,CAAC,CAAC;cACF,OAAOL,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;YAEzC,KAAK,EAAE;cACL,IAAI5G,OAAO,CAACmB,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,EAAE;gBAC1CmF,UAAU,GAAGnF,MAAM;cACrB;YAEF,KAAK,EAAE;cACL,IAAI,CAACkF,aAAa,EAAE;gBAClBI,QAAQ,CAACzF,IAAI,GAAG,EAAE;gBAClB;cACF;cAEAyF,QAAQ,CAACzF,IAAI,GAAG,EAAE;cAClB,OAAOqF,aAAa,CAACC,UAAU,CAAC;YAElC,KAAK,EAAE;cACLA,UAAU,GAAGG,QAAQ,CAACE,IAAI;YAE5B,KAAK,EAAE;cACL,OAAOF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEN,UAAU,CAAC;YAE9C,KAAK,EAAE;YACP,KAAK,KAAK;cACR,OAAOG,QAAQ,CAACM,IAAI,CAAC,CAAC;UAC1B;QACF;MACF,CAAC,EAAEZ,OAAO,CAAC;IACb,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAIa,YAAY,GAAG,SAASA,YAAYA,CAACC,iBAAiB,EAAE;IAC1D;IACA,IAAIC,oBAAoB,GAAGD,iBAAiB,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAE;MAClE,OAAO,CAACA,IAAI,CAAC/B,IAAI,CAACjD,WAAW,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI,CAAC8E,oBAAoB,CAACG,MAAM,EAAE;MAChC;IACF;IAEA,IAAIC,cAAc,GAAGJ,oBAAoB,CAACK,GAAG,CAAC,UAAUH,IAAI,EAAE;MAC5D,OAAOxF,QAAQ,CAACwF,IAAI,CAAC/B,IAAI,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAImC,WAAW,GAAGvH,kBAAkB,CAACuE,cAAc,CAAC;IAEpD8C,cAAc,CAAClC,OAAO,CAAC,UAAUqC,OAAO,EAAE;MACxC;MACAD,WAAW,GAAGzF,cAAc,CAAC0F,OAAO,EAAED,WAAW,CAAC;IACpD,CAAC,CAAC;IACFF,cAAc,CAAClC,OAAO,CAAC,UAAUqC,OAAO,EAAEnC,KAAK,EAAE;MAC/C;MACA,IAAIoC,cAAc,GAAGD,OAAO;MAE5B,IAAI,CAACP,oBAAoB,CAAC5B,KAAK,CAAC,CAACgB,UAAU,EAAE;QAC3C;QACA,IAAIqB,aAAa,GAAGF,OAAO,CAACE,aAAa;QACzC,IAAIC,KAAK;QAET,IAAI;UACFA,KAAK,GAAG,IAAIC,IAAI,CAAC,CAACF,aAAa,CAAC,EAAEA,aAAa,CAACG,IAAI,EAAE;YACpD/D,IAAI,EAAE4D,aAAa,CAAC5D;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO9C,CAAC,EAAE;UACV2G,KAAK,GAAG,IAAIG,IAAI,CAAC,CAACJ,aAAa,CAAC,EAAE;YAChC5D,IAAI,EAAE4D,aAAa,CAAC5D;UACtB,CAAC,CAAC;UACF6D,KAAK,CAACE,IAAI,GAAGH,aAAa,CAACG,IAAI;UAC/BF,KAAK,CAACI,gBAAgB,GAAG,IAAI1F,IAAI,CAAC,CAAC;UACnCsF,KAAK,CAACK,YAAY,GAAG,IAAI3F,IAAI,CAAC,CAAC,CAAC4F,OAAO,CAAC,CAAC;QAC3C;QAEAN,KAAK,CAACrC,GAAG,GAAGkC,OAAO,CAAClC,GAAG;QACvBmC,cAAc,GAAGE,KAAK;MACxB,CAAC,MAAM;QACL;QACAH,OAAO,CAACU,MAAM,GAAG,WAAW;MAC9B;MAEAzC,gBAAgB,CAACgC,cAAc,EAAEF,WAAW,CAAC;IAC/C,CAAC,CAAC;EACJ,CAAC;EAED,IAAIY,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAEhD,IAAI,EAAEiD,GAAG,EAAE;IACtD,IAAI;MACF,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOpH,CAAC,EAAE;MACV;IAAA,CACD,CAAC;;IAGF,IAAI,CAACY,WAAW,CAACwD,IAAI,EAAEb,cAAc,CAAC,EAAE;MACtC;IACF;IAEA,IAAIiE,UAAU,GAAG7G,QAAQ,CAACyD,IAAI,CAAC;IAC/BoD,UAAU,CAACN,MAAM,GAAG,MAAM;IAC1BM,UAAU,CAACC,OAAO,GAAG,GAAG;IACxBD,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;IAC9BI,UAAU,CAACH,GAAG,GAAGA,GAAG;IACpB,IAAIK,YAAY,GAAG5G,cAAc,CAAC0G,UAAU,EAAEjE,cAAc,CAAC;IAC7DkB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,CAAC;EAC5C,CAAC;EAED,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAC3H,CAAC,EAAEoE,IAAI,EAAE;IAC5C;IACA,IAAI,CAACxD,WAAW,CAACwD,IAAI,EAAEb,cAAc,CAAC,EAAE;MACtC;IACF;IAEA,IAAIiE,UAAU,GAAG7G,QAAQ,CAACyD,IAAI,CAAC;IAC/BoD,UAAU,CAACN,MAAM,GAAG,WAAW;IAC/BM,UAAU,CAACC,OAAO,GAAGzH,CAAC,CAACyH,OAAO;IAC9B,IAAIC,YAAY,GAAG5G,cAAc,CAAC0G,UAAU,EAAEjE,cAAc,CAAC;IAC7DkB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,EAAE1H,CAAC,CAAC;EAC/C,CAAC;EAED,IAAI4H,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAET,QAAQ,EAAEhD,IAAI,EAAE;IACpD;IACA,IAAI,CAACxD,WAAW,CAACwD,IAAI,EAAEb,cAAc,CAAC,EAAE;MACtC;IACF;IAEA,IAAIiE,UAAU,GAAG7G,QAAQ,CAACyD,IAAI,CAAC;IAC/BoD,UAAU,CAACK,KAAK,GAAGA,KAAK;IACxBL,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;IAC9BI,UAAU,CAACN,MAAM,GAAG,OAAO;IAC3B,IAAIQ,YAAY,GAAG5G,cAAc,CAAC0G,UAAU,EAAEjE,cAAc,CAAC;IAC7DkB,gBAAgB,CAAC+C,UAAU,EAAEE,YAAY,CAAC;EAC5C,CAAC;EAED,IAAII,YAAY,GAAG,SAASA,YAAYA,CAAC1D,IAAI,EAAE;IAC7C,IAAI2D,WAAW;IACfpI,OAAO,CAACD,OAAO,CAAC,OAAOmC,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACuC,IAAI,CAAC,GAAGvC,QAAQ,CAAC,CAACzB,IAAI,CAAC,UAAU4H,GAAG,EAAE;MAC9F,IAAIC,EAAE,CAAC,CAAC;;MAGR,IAAID,GAAG,KAAK,KAAK,EAAE;QACjB;MACF;MAEA,IAAIE,eAAe,GAAGrH,cAAc,CAACuD,IAAI,EAAEb,cAAc,CAAC;MAE1D,IAAI2E,eAAe,EAAE;QACnBH,WAAW,GAAGjJ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsF,IAAI,CAAC,EAAE;UACzC8C,MAAM,EAAE;QACV,CAAC,CAAC;QACF3D,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACY,OAAO,CAAC,UAAUgE,IAAI,EAAE;UACrG,IAAIC,QAAQ,GAAGL,WAAW,CAACzD,GAAG,KAAK+D,SAAS,GAAG,KAAK,GAAG,MAAM;UAE7D,IAAIF,IAAI,CAACC,QAAQ,CAAC,KAAKL,WAAW,CAACK,QAAQ,CAAC,IAAI,CAAC7D,MAAM,CAACC,QAAQ,CAAC2D,IAAI,CAAC,EAAE;YACtEA,IAAI,CAACjB,MAAM,GAAG,SAAS;UACzB;QACF,CAAC,CAAC;QACF,CAACe,EAAE,GAAGnE,MAAM,CAACwE,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAACR,WAAW,CAAC;QAChFtD,gBAAgB,CAACsD,WAAW,EAAEG,eAAe,CAAC;MAChD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIM,UAAU,GAAG,SAASA,UAAUA,CAACxI,CAAC,EAAE;IACtC6D,YAAY,CAAC7D,CAAC,CAAC8C,IAAI,CAAC;IAEpB,IAAI9C,CAAC,CAAC8C,IAAI,KAAK,MAAM,EAAE;MACrBX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnC,CAAC,CAAC;IAC3D;EACF,CAAC,CAAC,CAAC;;EAGHM,KAAK,CAACmI,mBAAmB,CAAChH,GAAG,EAAE,YAAY;IACzC,OAAO;MACLsE,YAAY,EAAEA,YAAY;MAC1BoB,SAAS,EAAEA,SAAS;MACpBQ,UAAU,EAAEA,UAAU;MACtBC,OAAO,EAAEA,OAAO;MAChBjG,QAAQ,EAAE4B,cAAc;MACxBO,MAAM,EAAEA,MAAM,CAACwE;IACjB,CAAC;EACH,CAAC,CAAC;EAEF,IAAII,iBAAiB,GAAGpI,KAAK,CAACqI,UAAU,CAAC1H,aAAa,CAAC;IACnD2H,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIjG,SAAS,GAAGgG,YAAY,CAAC,QAAQ,EAAEjG,kBAAkB,CAAC;EAE1D,IAAImG,aAAa,GAAGhK,QAAQ,CAACA,QAAQ,CAAC;IACpCiH,YAAY,EAAEA,YAAY;IAC1B6B,OAAO,EAAEA,OAAO;IAChBD,UAAU,EAAEA,UAAU;IACtBR,SAAS,EAAEA;EACb,CAAC,EAAE3F,KAAK,CAAC,EAAE;IACToB,SAAS,EAAEA,SAAS;IACpBuC,YAAY,EAAEJ,kBAAkB;IAChC7C,QAAQ,EAAEmG;EACZ,CAAC,CAAC;EAEF,OAAOS,aAAa,CAACjG,SAAS;EAC9B,OAAOiG,aAAa,CAAC9F,KAAK,CAAC,CAAC;EAC5B;EACA;EACA;;EAEA,IAAI,CAACD,QAAQ,IAAIV,QAAQ,EAAE;IACzB,OAAOyG,aAAa,CAACC,EAAE;EACzB;EAEA,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,MAAM,EAAEC,aAAa,EAAE;IACtE,OAAOpH,cAAc,GAAG,aAAaxB,KAAK,CAAC6I,aAAa,CAACpI,cAAc,EAAE;MACvEqI,aAAa,EAAE,QAAQ;MACvBpI,aAAa,EAAEA,aAAa,CAACqI;IAC/B,CAAC,EAAE,UAAU9G,MAAM,EAAE;MACnB,IAAI+G,IAAI,GAAG,OAAOxH,cAAc,KAAK,SAAS,GAAG,CAAC,CAAC,GAAGA,cAAc;QAChEyH,cAAc,GAAGD,IAAI,CAACC,cAAc;QACpCC,eAAe,GAAGF,IAAI,CAACE,eAAe;QACtCC,gBAAgB,GAAGH,IAAI,CAACG,gBAAgB;QACxCC,UAAU,GAAGJ,IAAI,CAACI,UAAU;QAC5BC,WAAW,GAAGL,IAAI,CAACK,WAAW;QAC9BC,YAAY,GAAGN,IAAI,CAACM,YAAY;MAEpC,OAAO,aAAatJ,KAAK,CAAC6I,aAAa,CAACzI,UAAU,EAAE;QAClDkC,SAAS,EAAEA,SAAS;QACpBb,QAAQ,EAAEA,QAAQ;QAClB8H,KAAK,EAAEtG,cAAc;QACrBnB,WAAW,EAAEA,WAAW;QACxBJ,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBJ,QAAQ,EAAEiG,YAAY;QACtByB,cAAc,EAAE,CAAClH,QAAQ,IAAIkH,cAAc;QAC3CC,eAAe,EAAEA,eAAe;QAChCC,gBAAgB,EAAEA,gBAAgB;QAClCC,UAAU,EAAEA,UAAU;QACtBC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA,YAAY;QAC1BpH,UAAU,EAAEA,UAAU;QACtBD,MAAM,EAAEzD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyD,MAAM,CAAC,EAAED,UAAU,CAAC;QAClDG,UAAU,EAAEA,UAAU;QACtBC,QAAQ,EAAEA,QAAQ;QAClBoH,YAAY,EAAEb,MAAM;QACpBc,mBAAmB,EAAEb,aAAa;QAClCjG,UAAU,EAAEA;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,GAAGgG,MAAM;EACb,CAAC;EAED,IAAInG,IAAI,KAAK,MAAM,EAAE;IACnB,IAAIkH,WAAW;IAEf,IAAIC,OAAO,GAAGxJ,UAAU,CAACmC,SAAS,GAAGoH,WAAW,GAAG,CAAC,CAAC,EAAEnL,eAAe,CAACmL,WAAW,EAAE,EAAE,CAAC5I,MAAM,CAACwB,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE/D,eAAe,CAACmL,WAAW,EAAE,EAAE,CAAC5I,MAAM,CAACwB,SAAS,EAAE,iBAAiB,CAAC,EAAEW,cAAc,CAAC2G,IAAI,CAAC,UAAU9F,IAAI,EAAE;MAClO,OAAOA,IAAI,CAAC8C,MAAM,KAAK,WAAW;IACpC,CAAC,CAAC,CAAC,EAAErI,eAAe,CAACmL,WAAW,EAAE,EAAE,CAAC5I,MAAM,CAACwB,SAAS,EAAE,aAAa,CAAC,EAAEgB,SAAS,KAAK,UAAU,CAAC,EAAE/E,eAAe,CAACmL,WAAW,EAAE,EAAE,CAAC5I,MAAM,CAACwB,SAAS,EAAE,WAAW,CAAC,EAAEP,QAAQ,CAAC,EAAExD,eAAe,CAACmL,WAAW,EAAE,EAAE,CAAC5I,MAAM,CAACwB,SAAS,EAAE,MAAM,CAAC,EAAEiG,SAAS,KAAK,KAAK,CAAC,EAAEmB,WAAW,GAAGnH,SAAS,CAAC;IACtR,OAAO,aAAavC,KAAK,CAAC6I,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa7I,KAAK,CAAC6I,aAAa,CAAC,KAAK,EAAE;MAC5FtG,SAAS,EAAEoH,OAAO;MAClB9H,MAAM,EAAEqG,UAAU;MAClB2B,UAAU,EAAE3B,UAAU;MACtB4B,WAAW,EAAE5B,UAAU;MACvBxF,KAAK,EAAEA;IACT,CAAC,EAAE,aAAa1C,KAAK,CAAC6I,aAAa,CAAC5I,QAAQ,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEgK,aAAa,EAAE;MACxErH,GAAG,EAAEqC,MAAM;MACXjB,SAAS,EAAE,EAAE,CAACzB,MAAM,CAACwB,SAAS,EAAE,MAAM;IACxC,CAAC,CAAC,EAAE,aAAatC,KAAK,CAAC6I,aAAa,CAAC,KAAK,EAAE;MAC1CtG,SAAS,EAAE,EAAE,CAACzB,MAAM,CAACwB,SAAS,EAAE,iBAAiB;IACnD,CAAC,EAAEG,QAAQ,CAAC,CAAC,CAAC,EAAEiG,gBAAgB,CAAC,CAAC,CAAC;EACrC;EAEA,IAAIqB,eAAe,GAAG5J,UAAU,CAACmC,SAAS,GAAGlB,YAAY,GAAG,CAAC,CAAC,EAAE7C,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACN,MAAM,CAACwB,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE/D,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACN,MAAM,CAACwB,SAAS,EAAE,UAAU,CAAC,CAACxB,MAAM,CAACW,QAAQ,CAAC,EAAE,IAAI,CAAC,EAAElD,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACN,MAAM,CAACwB,SAAS,EAAE,WAAW,CAAC,EAAEP,QAAQ,CAAC,EAAExD,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACN,MAAM,CAACwB,SAAS,EAAE,MAAM,CAAC,EAAEiG,SAAS,KAAK,KAAK,CAAC,EAAEnH,YAAY,CAAC,CAAC;EAE1Y,IAAI4I,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,iBAAiB,EAAE;IACtE,OAAO,aAAajK,KAAK,CAAC6I,aAAa,CAAC,KAAK,EAAE;MAC7CtG,SAAS,EAAEwH,eAAe;MAC1BrH,KAAK,EAAEuH;IACT,CAAC,EAAE,aAAajK,KAAK,CAAC6I,aAAa,CAAC5I,QAAQ,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEgK,aAAa,EAAE;MACxErH,GAAG,EAAEqC;IACP,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;EAED,IAAI/B,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO,aAAazB,KAAK,CAAC6I,aAAa,CAAC,MAAM,EAAE;MAC9CtG,SAAS,EAAEpC,UAAU,CAAC,EAAE,CAACW,MAAM,CAACwB,SAAS,EAAE,uBAAuB,CAAC,EAAEC,SAAS;IAChF,CAAC,EAAEmG,gBAAgB,CAACsB,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAACvH,QAAQ,CAAC,CAAC;EACxD;EAEA,OAAO,aAAazC,KAAK,CAAC6I,aAAa,CAAC,MAAM,EAAE;IAC9CtG,SAAS,EAAEA;EACb,CAAC,EAAEyH,kBAAkB,CAACvH,QAAQ,GAAGsF,SAAS,GAAG;IAC3CmC,OAAO,EAAE;EACX,CAAC,CAAC,EAAExB,gBAAgB,CAAC,CAAC,CAAC;AACzB,CAAC;AAED,IAAIK,MAAM,GAAG,aAAa/I,KAAK,CAACmK,UAAU,CAAClJ,cAAc,CAAC;AAC1D8H,MAAM,CAACqB,WAAW,GAAG,QAAQ;AAC7BrB,MAAM,CAACsB,YAAY,GAAG;EACpB7H,IAAI,EAAE,QAAQ;EACd8H,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,CAAC,CAAC;EACRC,MAAM,EAAE,EAAE;EACVjJ,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE,MAAM;EAChBc,SAAS,EAAE,EAAE;EACbR,QAAQ,EAAE,KAAK;EACf2I,mBAAmB,EAAE;AACvB,CAAC;AACD,eAAe3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}