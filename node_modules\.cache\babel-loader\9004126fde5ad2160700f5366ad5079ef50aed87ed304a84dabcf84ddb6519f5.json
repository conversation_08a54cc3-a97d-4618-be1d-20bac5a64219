{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.call = call;\nexports.default = void 0;\nexports.note = note;\nexports.noteOnce = noteOnce;\nexports.resetWarned = resetWarned;\nexports.warning = warning;\nexports.warningOnce = warningOnce;\n\n/* eslint-disable no-console */\nvar warned = {};\nfunction warning(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.error(\"Warning: \".concat(message));\n  }\n}\nfunction note(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.warn(\"Note: \".concat(message));\n  }\n}\nfunction resetWarned() {\n  warned = {};\n}\nfunction call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\nfunction warningOnce(valid, message) {\n  call(warning, valid, message);\n}\nfunction noteOnce(valid, message) {\n  call(note, valid, message);\n}\nvar _default = warningOnce;\n/* eslint-enable */\n\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "call", "default", "note", "noteOnce", "resetWarned", "warning", "warningOnce", "warned", "valid", "message", "process", "env", "NODE_ENV", "console", "undefined", "error", "concat", "warn", "method", "_default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-util/lib/warning.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.call = call;\nexports.default = void 0;\nexports.note = note;\nexports.noteOnce = noteOnce;\nexports.resetWarned = resetWarned;\nexports.warning = warning;\nexports.warningOnce = warningOnce;\n\n/* eslint-disable no-console */\nvar warned = {};\n\nfunction warning(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.error(\"Warning: \".concat(message));\n  }\n}\n\nfunction note(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.warn(\"Note: \".concat(message));\n  }\n}\n\nfunction resetWarned() {\n  warned = {};\n}\n\nfunction call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\nfunction warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\nfunction noteOnce(valid, message) {\n  call(note, valid, message);\n}\n\nvar _default = warningOnce;\n/* eslint-enable */\n\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AACxBH,OAAO,CAACI,IAAI,GAAGA,IAAI;AACnBJ,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3BL,OAAO,CAACM,WAAW,GAAGA,WAAW;AACjCN,OAAO,CAACO,OAAO,GAAGA,OAAO;AACzBP,OAAO,CAACQ,WAAW,GAAGA,WAAW;;AAEjC;AACA,IAAIC,MAAM,GAAG,CAAC,CAAC;AAEf,SAASF,OAAOA,CAACG,KAAK,EAAEC,OAAO,EAAE;EAC/B;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5ED,OAAO,CAACE,KAAK,CAAC,WAAW,CAACC,MAAM,CAACP,OAAO,CAAC,CAAC;EAC5C;AACF;AAEA,SAASP,IAAIA,CAACM,KAAK,EAAEC,OAAO,EAAE;EAC5B;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5ED,OAAO,CAACI,IAAI,CAAC,QAAQ,CAACD,MAAM,CAACP,OAAO,CAAC,CAAC;EACxC;AACF;AAEA,SAASL,WAAWA,CAAA,EAAG;EACrBG,MAAM,GAAG,CAAC,CAAC;AACb;AAEA,SAASP,IAAIA,CAACkB,MAAM,EAAEV,KAAK,EAAEC,OAAO,EAAE;EACpC,IAAI,CAACD,KAAK,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC,EAAE;IAC9BS,MAAM,CAAC,KAAK,EAAET,OAAO,CAAC;IACtBF,MAAM,CAACE,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;AAEA,SAASH,WAAWA,CAACE,KAAK,EAAEC,OAAO,EAAE;EACnCT,IAAI,CAACK,OAAO,EAAEG,KAAK,EAAEC,OAAO,CAAC;AAC/B;AAEA,SAASN,QAAQA,CAACK,KAAK,EAAEC,OAAO,EAAE;EAChCT,IAAI,CAACE,IAAI,EAAEM,KAAK,EAAEC,OAAO,CAAC;AAC5B;AAEA,IAAIU,QAAQ,GAAGb,WAAW;AAC1B;;AAEAR,OAAO,CAACG,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}