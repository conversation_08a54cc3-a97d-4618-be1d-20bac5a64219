{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\affiliato\\\\riepilogoOrdineAff.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Carrello - operazioni sul carrello\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from '../../components/navigation/Nav';\nimport CarrelloGen from '../../components/generalizzazioni/marketplace/carrello';\nimport { distributoreCarrello } from '../../components/route';\nimport { Costanti } from '../../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../../css/carrello.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass RiepilogoOrdineAff extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      results: null,\n      results2: null,\n      datiConsegna: null,\n      loading: true\n    };\n  }\n  //Chiamata axios effettuata una sola volta grazie a componentDidMount\n  async componentDidMount() {\n    var prodotti = [];\n    if (localStorage.getItem(\"Cart\") !== '') {\n      prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n    } else {\n      prodotti = [];\n    }\n    if (prodotti.length > 0) {\n      for (var i = 0; i < prodotti.length; i++) {\n        if (prodotti[i].total === undefined) {\n          var totale = [];\n          totale = prodotti;\n          totale[i].totale = prodotti[i].price !== undefined ? parseFloat(prodotti[i].price).toFixed(2) : parseFloat(prodotti[i].sell_in).toFixed(2) * prodotti[i].quantity;\n          prodotti = totale;\n        } else {\n          prodotti[i].idProduct2 = prodotti[i].product;\n          prodotti[i].price = prodotti[i].unitPrice;\n          prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity;\n        }\n      }\n    }\n    if (localStorage.getItem(\"DatiConsegna\") !== '') {\n      var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n    }\n    var userid = [];\n    if (window.location.pathname !== distributoreCarrello) {\n      userid = JSON.parse(localStorage.getItem(\"userid\"));\n    } else {\n      userid = dati !== null && dati !== void 0 && dati.idRegistry ? dati === null || dati === void 0 ? void 0 : dati.idRegistry : JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n    }\n    this.setState({\n      results: userid,\n      results2: prodotti,\n      datiConsegna: dati,\n      loading: false\n    });\n  }\n  render() {\n    if (this.state.loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card form-complete border-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card form-complete border-0\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {\n        disabled: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.Riepilogo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CarrelloGen, {\n        results: this.state.results,\n        results2: this.state.results2,\n        datiConsegna: this.state.datiConsegna\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default RiepilogoOrdineAff;", "map": {"version": 3, "names": ["React", "Component", "Nav", "CarrelloGen", "distributoreCarrello", "<PERSON><PERSON>", "Toast", "jsxDEV", "_jsxDEV", "RiepilogoOrdineAff", "constructor", "props", "state", "results", "results2", "dati<PERSON>ons<PERSON>na", "loading", "componentDidMount", "prodotti", "localStorage", "getItem", "JSON", "parse", "length", "i", "total", "undefined", "totale", "price", "parseFloat", "toFixed", "sell_in", "quantity", "idProduct2", "product", "unitPrice", "dati", "userid", "window", "location", "pathname", "idRegistry", "setState", "render", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "disabled", "ref", "el", "toast", "Riepilogo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/affiliato/riepilogoOrdineAff.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* Carrello - operazioni sul carrello\n*\n*/\nimport React, { Component } from 'react';\nimport Nav from '../../components/navigation/Nav';\nimport CarrelloGen from '../../components/generalizzazioni/marketplace/carrello';\nimport { distributoreCarrello } from '../../components/route';\nimport { Costanti } from '../../components/traduttore/const';\nimport { Toast } from 'primereact/toast';\nimport '../../css/carrello.css';\n\nclass RiepilogoOrdineAff extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            results: null,\n            results2: null,\n            datiConsegna: null,\n            loading: true\n        }\n    }\n    //Chiamata axios effettuata una sola volta grazie a componentDidMount\n    async componentDidMount() {\n        var prodotti = []\n        if (localStorage.getItem(\"Cart\") !== '') {\n            prodotti = JSON.parse(localStorage.getItem(\"Cart\"));\n        } else {\n            prodotti = []\n        }\n        if (prodotti.length > 0) {\n            for (var i = 0; i < prodotti.length; i++) {\n                if (prodotti[i].total === undefined) {\n                    var totale = [];\n                    totale = prodotti;\n                    totale[i].totale = prodotti[i].price !== undefined ? parseFloat(prodotti[i].price).toFixed(2) : parseFloat(prodotti[i].sell_in).toFixed(2) * prodotti[i].quantity;\n                    prodotti = totale;\n                } else {\n                    prodotti[i].idProduct2 = prodotti[i].product;\n                    prodotti[i].price = prodotti[i].unitPrice\n                    prodotti[i].totale = parseFloat(prodotti[i].unitPrice).toFixed(2) * prodotti[i].quantity\n                }\n            }\n        }\n        if (localStorage.getItem(\"DatiConsegna\") !== '') {\n            var dati = JSON.parse(localStorage.getItem(\"DatiConsegna\"));\n        }\n        var userid = []\n        if(window.location.pathname !== distributoreCarrello){\n            userid = JSON.parse(localStorage.getItem(\"userid\"));\n        }else {\n            userid = dati?.idRegistry ? dati?.idRegistry : JSON.parse(localStorage.getItem(\"DatiConsegna\"))\n        }\n        this.setState({\n            results: userid,\n            results2: prodotti,\n            datiConsegna: dati,\n            loading: false\n        })\n    }\n    render() {\n        if (this.state.loading) {\n            return (\n                <div className=\"card form-complete border-0\"></div>\n            )\n        }\n        return (\n            <div className=\"card form-complete border-0\">\n                <Nav disabled={true} />\n                <Toast ref={(el) => this.toast = el} />\n                <div className=\"col-12\">\n                    <h1>{Costanti.Riepilogo}</h1>\n                </div>\n                <hr />\n                <CarrelloGen results={this.state.results} results2={this.state.results2} datiConsegna={this.state.datiConsegna} />\n            </div>\n        );\n    }\n}\nexport default RiepilogoOrdineAff;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,WAAW,MAAM,wDAAwD;AAChF,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,kBAAkB,SAASR,SAAS,CAAC;EACvCS,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;IACb,CAAC;EACL;EACA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACrCF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC,MAAM;MACHF,QAAQ,GAAG,EAAE;IACjB;IACA,IAAIA,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAE;MACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACK,MAAM,EAAEC,CAAC,EAAE,EAAE;QACtC,IAAIN,QAAQ,CAACM,CAAC,CAAC,CAACC,KAAK,KAAKC,SAAS,EAAE;UACjC,IAAIC,MAAM,GAAG,EAAE;UACfA,MAAM,GAAGT,QAAQ;UACjBS,MAAM,CAACH,CAAC,CAAC,CAACG,MAAM,GAAGT,QAAQ,CAACM,CAAC,CAAC,CAACI,KAAK,KAAKF,SAAS,GAAGG,UAAU,CAACX,QAAQ,CAACM,CAAC,CAAC,CAACI,KAAK,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,GAAGD,UAAU,CAACX,QAAQ,CAACM,CAAC,CAAC,CAACO,OAAO,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,GAAGZ,QAAQ,CAACM,CAAC,CAAC,CAACQ,QAAQ;UACjKd,QAAQ,GAAGS,MAAM;QACrB,CAAC,MAAM;UACHT,QAAQ,CAACM,CAAC,CAAC,CAACS,UAAU,GAAGf,QAAQ,CAACM,CAAC,CAAC,CAACU,OAAO;UAC5ChB,QAAQ,CAACM,CAAC,CAAC,CAACI,KAAK,GAAGV,QAAQ,CAACM,CAAC,CAAC,CAACW,SAAS;UACzCjB,QAAQ,CAACM,CAAC,CAAC,CAACG,MAAM,GAAGE,UAAU,CAACX,QAAQ,CAACM,CAAC,CAAC,CAACW,SAAS,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,GAAGZ,QAAQ,CAACM,CAAC,CAAC,CAACQ,QAAQ;QAC5F;MACJ;IACJ;IACA,IAAIb,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE;MAC7C,IAAIgB,IAAI,GAAGf,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC/D;IACA,IAAIiB,MAAM,GAAG,EAAE;IACf,IAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKpC,oBAAoB,EAAC;MACjDiC,MAAM,GAAGhB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC,MAAK;MACFiB,MAAM,GAAGD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,UAAU,GAAGL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,UAAU,GAAGpB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;IACnG;IACA,IAAI,CAACsB,QAAQ,CAAC;MACV7B,OAAO,EAAEwB,MAAM;MACfvB,QAAQ,EAAEI,QAAQ;MAClBH,YAAY,EAAEqB,IAAI;MAClBpB,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA2B,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC/B,KAAK,CAACI,OAAO,EAAE;MACpB,oBACIR,OAAA;QAAKoC,SAAS,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAE3D;IACA,oBACIxC,OAAA;MAAKoC,SAAS,EAAC,6BAA6B;MAAAK,QAAA,gBACxCzC,OAAA,CAACN,GAAG;QAACgD,QAAQ,EAAE;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBxC,OAAA,CAACF,KAAK;QAAC6C,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACC,KAAK,GAAGD;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCxC,OAAA;QAAKoC,SAAS,EAAC,QAAQ;QAAAK,QAAA,eACnBzC,OAAA;UAAAyC,QAAA,EAAK5C,QAAQ,CAACiD;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNxC,OAAA;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNxC,OAAA,CAACL,WAAW;QAACU,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAQ;QAACC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE,QAAS;QAACC,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG;MAAa;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjH,CAAC;EAEd;AACJ;AACA,eAAevC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}