{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from './MeasureCell';\nimport raf from \"rc-util/es/raf\";\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    columnsKey = _ref.columnsKey,\n    onColumnResize = _ref.onColumnResize;\n  // delay state update while resize continuously, e.g. window resize\n  var resizedColumnsRef = React.useRef(new Map());\n  var rafIdRef = React.useRef(null);\n  var delayOnColumnResize = function delayOnColumnResize() {\n    if (rafIdRef.current === null) {\n      rafIdRef.current = raf(function () {\n        resizedColumnsRef.current.forEach(function (width, columnKey) {\n          onColumnResize(columnKey, width);\n        });\n        resizedColumnsRef.current.clear();\n        rafIdRef.current = null;\n      }, 2);\n    }\n  };\n  React.useEffect(function () {\n    return function () {\n      raf.cancel(rafIdRef.current);\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    }\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      infoList.forEach(function (_ref2) {\n        var columnKey = _ref2.data,\n          size = _ref2.size;\n        resizedColumnsRef.current.set(columnKey, size.offsetWidth);\n      });\n      delayOnColumnResize();\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "MeasureCell", "raf", "MeasureRow", "_ref", "prefixCls", "columnsKey", "onColumnResize", "resizedColumnsRef", "useRef", "Map", "rafIdRef", "delayOnColumnResize", "current", "for<PERSON>ach", "width", "column<PERSON>ey", "clear", "useEffect", "cancel", "createElement", "className", "concat", "style", "height", "fontSize", "Collection", "onBatchResize", "infoList", "_ref2", "data", "size", "set", "offsetWidth", "map", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-table/es/Body/MeasureRow.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from './MeasureCell';\nimport raf from \"rc-util/es/raf\";\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n      columnsKey = _ref.columnsKey,\n      onColumnResize = _ref.onColumnResize;\n  // delay state update while resize continuously, e.g. window resize\n  var resizedColumnsRef = React.useRef(new Map());\n  var rafIdRef = React.useRef(null);\n\n  var delayOnColumnResize = function delayOnColumnResize() {\n    if (rafIdRef.current === null) {\n      rafIdRef.current = raf(function () {\n        resizedColumnsRef.current.forEach(function (width, columnKey) {\n          onColumnResize(columnKey, width);\n        });\n        resizedColumnsRef.current.clear();\n        rafIdRef.current = null;\n      }, 2);\n    }\n  };\n\n  React.useEffect(function () {\n    return function () {\n      raf.cancel(rafIdRef.current);\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    }\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      infoList.forEach(function (_ref2) {\n        var columnKey = _ref2.data,\n            size = _ref2.size;\n        resizedColumnsRef.current.set(columnKey, size.offsetWidth);\n      });\n      delayOnColumnResize();\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAE;EACvC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACxC;EACA,IAAIC,iBAAiB,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAC/C,IAAIC,QAAQ,GAAGZ,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAEjC,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IACvD,IAAID,QAAQ,CAACE,OAAO,KAAK,IAAI,EAAE;MAC7BF,QAAQ,CAACE,OAAO,GAAGX,GAAG,CAAC,YAAY;QACjCM,iBAAiB,CAACK,OAAO,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAEC,SAAS,EAAE;UAC5DT,cAAc,CAACS,SAAS,EAAED,KAAK,CAAC;QAClC,CAAC,CAAC;QACFP,iBAAiB,CAACK,OAAO,CAACI,KAAK,CAAC,CAAC;QACjCN,QAAQ,CAACE,OAAO,GAAG,IAAI;MACzB,CAAC,EAAE,CAAC,CAAC;IACP;EACF,CAAC;EAEDd,KAAK,CAACmB,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBhB,GAAG,CAACiB,MAAM,CAACR,QAAQ,CAACE,OAAO,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAad,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;IAC5C,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACjB,SAAS,EAAE,cAAc,CAAC;IAC/CkB,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,aAAa1B,KAAK,CAACqB,aAAa,CAACpB,cAAc,CAAC0B,UAAU,EAAE;IAC7DC,aAAa,EAAE,SAASA,aAAaA,CAACC,QAAQ,EAAE;MAC9CA,QAAQ,CAACd,OAAO,CAAC,UAAUe,KAAK,EAAE;QAChC,IAAIb,SAAS,GAAGa,KAAK,CAACC,IAAI;UACtBC,IAAI,GAAGF,KAAK,CAACE,IAAI;QACrBvB,iBAAiB,CAACK,OAAO,CAACmB,GAAG,CAAChB,SAAS,EAAEe,IAAI,CAACE,WAAW,CAAC;MAC5D,CAAC,CAAC;MACFrB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAEN,UAAU,CAAC4B,GAAG,CAAC,UAAUlB,SAAS,EAAE;IACrC,OAAO,aAAajB,KAAK,CAACqB,aAAa,CAACnB,WAAW,EAAE;MACnDkC,GAAG,EAAEnB,SAAS;MACdA,SAAS,EAAEA,SAAS;MACpBT,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}