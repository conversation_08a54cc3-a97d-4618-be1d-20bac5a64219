{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\distributore\\\\ufficioAcquisti.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { affiliato, distributore } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { Sidebar } from 'primereact/sidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass UfficioAcquisti extends Component {\n  constructor(props) {\n    super(props);\n    //Stato iniziale elementi tabella\n    this.emptyResult = {\n      id: null,\n      firstName: \"\",\n      referente: \"\",\n      deliveryDestination: \"\",\n      orderDate: \"\",\n      deliveryDate: \"\",\n      termsPayment: \"\",\n      paymentStatus: \"\",\n      status: \"\"\n    };\n    /* Seleziono il magazzino per la get sui documenti */\n    this.onWarehouseSelect = async e => {\n      this.setState({\n        selectedWarehouse: e.value\n      });\n      window.sessionStorage.setItem(\"idWarehouse\", e.value);\n      var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying, _element$tasks;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying = element.idSupplying) === null || _element$idSupplying === void 0 ? void 0 : _element$idSupplying.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks = element.tasks) === null || _element$tasks === void 0 ? void 0 : _element$tasks.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response, _e$response2;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.data) !== undefined ? (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this._isMounted = false;\n    this.state = {\n      results: null,\n      results2: null,\n      results3: null,\n      results4: null,\n      results5: null,\n      selectedWarehouse: null,\n      selectedDocuments: null,\n      loading: true,\n      resultDialog: false,\n      resultDialog2: false,\n      displayed: false,\n      resultDialog3: false,\n      resultDialog4: false,\n      resultDialog5: false,\n      opMag: '',\n      respMag: '',\n      mex: '',\n      result: this.emptyResult,\n      value: null,\n      value2: null,\n      totalRecords: 0,\n      search: '',\n      selectedMail: '',\n      param: '?idWarehouses=',\n      param2: '&idSupplying=',\n      selectedSupplyer: null,\n      deleteResultDialog: null,\n      role: localStorage.getItem('role'),\n      lazyParams: {\n        first: 0,\n        rows: 20,\n        page: 0,\n        sortField: null,\n        sortOrder: null,\n        filters: {\n          'number': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'type': {\n            value: '',\n            matchMode: 'contains'\n          },\n          'documentDate': {\n            value: '',\n            matchMode: 'contains'\n          }\n        }\n      }\n    };\n    /* Ricerca elementi per categoria selezionata */\n    this.filterDoc = async e => {\n      this.setState({\n        loading: true,\n        search: e.value.name,\n        selectedSupplyer: e.value\n      });\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying2, _element$tasks2;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying2 = element.idSupplying) === null || _element$idSupplying2 === void 0 ? void 0 : _element$idSupplying2.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks2 = element.tasks) === null || _element$tasks2 === void 0 ? void 0 : _element$tasks2.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response3, _e$response4;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: \".concat(((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : _e$response3.data) !== undefined ? (_e$response4 = e.response) === null || _e$response4 === void 0 ? void 0 : _e$response4.data : e.message),\n          life: 3000\n        });\n      });\n    };\n    this.supplyer = [];\n    this.warehouse = [];\n    this.loadLazyTimeout = null;\n    this.visualizzaDett = this.visualizzaDett.bind(this);\n    this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n    this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n    this.editResult = this.editResult.bind(this);\n    this.hideDialog = this.hideDialog.bind(this);\n    this.reset = this.reset.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n    this.onPage = this.onPage.bind(this);\n    this.onSort = this.onSort.bind(this);\n    this.onFilter = this.onFilter.bind(this);\n    this.sendMail = this.sendMail.bind(this);\n    this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n    this.senderMail = this.senderMail.bind(this);\n    this.selectionHandler = this.selectionHandler.bind(this);\n    this.deleteResult = this.deleteResult.bind(this);\n    this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n    this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n    this.editDocResult = this.editDocResult.bind(this);\n    this.closeSelectBefore = this.closeSelectBefore.bind(this);\n    this.openFilter = this.openFilter.bind(this);\n    this.closeFilter = this.closeFilter.bind(this);\n  }\n  async componentDidMount() {\n    this._isMounted = true;\n    if (this.state.role !== affiliato) {\n      var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n      if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n        idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse;\n        this.setState({\n          selectedWarehouse: idWarehouse\n        });\n        var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url).then(res => {\n          var documento = [];\n          res.data.documents.forEach(element => {\n            var _element$idSupplying3, _element$tasks3;\n            var x = {\n              id: element.id,\n              number: element.number,\n              type: element.type,\n              supplying: (_element$idSupplying3 = element.idSupplying) === null || _element$idSupplying3 === void 0 ? void 0 : _element$idSupplying3.idRegistry.firstName,\n              idSupplying: element.idSupplying,\n              documentDate: element.documentDate,\n              documentBodies: element.documentBodies,\n              deliveryDate: element.deliveryDate,\n              tasks: element.tasks,\n              erpSync: element.erpSync,\n              status: (_element$tasks3 = element.tasks) === null || _element$tasks3 === void 0 ? void 0 : _element$tasks3.status,\n              idLogEmail: element.idLogEmail,\n              total: element.total,\n              totalTaxed: element.totalTaxed\n            };\n            documento.push(x);\n          });\n          this.setState({\n            results: documento,\n            results5: documento,\n            totalRecords: res.data.totalCount,\n            lazyParams: {\n              first: this.state.lazyParams.first,\n              rows: this.state.lazyParams.rows,\n              page: this.state.lazyParams.page,\n              pageCount: res.data.totalCount / this.state.lazyParams.rows\n            },\n            loading: false\n          });\n        }).catch(e => {\n          var _e$response5, _e$response6;\n          console.log(e);\n          this.toast.show({\n            severity: \"error\",\n            summary: \"Siamo spiacenti\",\n            detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response5 = e.response) === null || _e$response5 === void 0 ? void 0 : _e$response5.data) !== undefined ? (_e$response6 = e.response) === null || _e$response6 === void 0 ? void 0 : _e$response6.data : e.message),\n            life: 3000\n          });\n        });\n      } else {\n        this.setState({\n          resultDialog: true,\n          displayed: true\n        });\n      }\n      await APIRequest(\"GET\", \"warehouses/\").then(res => {\n        for (var entry of res.data) {\n          this.warehouse.push({\n            name: entry.warehouseName + ' ' + entry.address + ' ' + entry.citta + ' (' + entry.prov + '), ' + entry.cap,\n            value: entry.id\n          });\n        }\n      }).catch(e => {\n        var _e$response7, _e$response8;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare i magazzini. Messaggio errore: \".concat(((_e$response7 = e.response) === null || _e$response7 === void 0 ? void 0 : _e$response7.data) !== undefined ? (_e$response8 = e.response) === null || _e$response8 === void 0 ? void 0 : _e$response8.data : e.message),\n          life: 3000\n        });\n      });\n      await APIRequest(\"GET\", \"employees/?employeesEffort=true\").then(res => {\n        if (this._isMounted) {\n          this.setState({\n            results4: res.data\n          });\n        }\n      }).catch(e => {\n        var _e$response9, _e$response0;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare gli operatori di magazzino. Messaggio errore: \".concat(((_e$response9 = e.response) === null || _e$response9 === void 0 ? void 0 : _e$response9.data) !== undefined ? (_e$response0 = e.response) === null || _e$response0 === void 0 ? void 0 : _e$response0.data : e.message),\n          life: 3000\n        });\n      });\n      await APIRequest('GET', 'supplying/').then(res => {\n        res.data.forEach(element => {\n          if (element && element.idRegistry) {\n            var x = {\n              name: element.idRegistry.firstName || 'Nome non disponibile',\n              code: element.id || 0\n            };\n            this.supplyer.push(x);\n          }\n        });\n      }).catch(e => {\n        console.log(e);\n      });\n    } else {\n      this.setState({\n        selectedWarehouse: JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse\n      });\n      await APIRequest(\"GET\", \"documents?idWarehouses=\".concat(JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse, \"&documentType=FOR-ORDINE&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page)).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying4, _element$tasks4;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying4 = element.idSupplying) === null || _element$idSupplying4 === void 0 ? void 0 : _element$idSupplying4.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks4 = element.tasks) === null || _element$tasks4 === void 0 ? void 0 : _element$tasks4.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response1, _e$response10;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response1 = e.response) === null || _e$response1 === void 0 ? void 0 : _e$response1.data) !== undefined ? (_e$response10 = e.response) === null || _e$response10 === void 0 ? void 0 : _e$response10.data : e.message),\n          life: 3000\n        });\n      });\n      await APIRequest(\"GET\", \"supplyingaffiliate\").then(res => {\n        res.data.forEach(element => {\n          var x = {\n            name: element.idSupplying.idRegistry.firstName,\n            code: element.idSupplying.id\n          };\n          this.supplyer.push(x);\n        });\n      }).catch(e => {\n        var _e$response11, _e$response12;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare i fornitori. Messaggio errore: \".concat(((_e$response11 = e.response) === null || _e$response11 === void 0 ? void 0 : _e$response11.data) !== undefined ? (_e$response12 = e.response) === null || _e$response12 === void 0 ? void 0 : _e$response12.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  componentWillUnmount() {\n    this._isMounted = false;\n  }\n  //Apertura dialogo aggiunta\n  async visualizzaDett(result) {\n    var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id;\n    var documentBody = [];\n    var task = [];\n    await APIRequest(\"GET\", url).then(res => {\n      documentBody = res.data.documentBodies;\n      result.documentBodies = res.data.documentBodies;\n      task = res.data;\n    }).catch(e => {\n      var _e$response13, _e$response14;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile visualizzare il documento. Messaggio errore: \".concat(((_e$response13 = e.response) === null || _e$response13 === void 0 ? void 0 : _e$response13.data) !== undefined ? (_e$response14 = e.response) === null || _e$response14 === void 0 ? void 0 : _e$response14.data : e.message),\n        life: 3000\n      });\n    });\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    this.setState({\n      resultDialog2: true,\n      result: task,\n      results2: this.state.results.filter(val => val.id === result.id),\n      results3: documentBody,\n      mex: message\n    });\n  }\n  //Chiusura dialogo visualizzazione\n  hidevisualizzaDett(result) {\n    this.setState({\n      result: result,\n      resultDialog2: false\n    });\n  }\n  //Apertura dialogo modifica\n  editResult(result) {\n    var message = \"Documento numero: \" + result.number + \" del \" + new Intl.DateTimeFormat(\"it-IT\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    }).format(new Date(result.documentDate));\n    var opMag = [];\n    var respMag = [];\n    if (this.state.results4 !== []) {\n      this.state.results4.forEach(element => {\n        if (element.role === 'OP_MAG' && result.tasks !== null) {\n          var taskAss = 0;\n          taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n          opMag.push({\n            label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n            value: element.idemployee\n          });\n        } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n          respMag.push({\n            label: element.first_name + ' ' + element.last_name,\n            value: element.idemployee\n          });\n        }\n      });\n    }\n    this.opMag = opMag;\n    this.setState({\n      result: _objectSpread({}, result),\n      resultDialog3: true,\n      opMag: opMag,\n      mex: message,\n      respMag: respMag\n    });\n  }\n  assegnaLavorazioni() {\n    var message = \"Trasmissione multipla documenti\";\n    var opMag = [];\n    var respMag = [];\n    var filter = this.state.selectedDocuments.filter(el => el.tasks !== null);\n    if (filter.length === 0) {\n      if (this.state.results4 !== []) {\n        this.state.results4.forEach(element => {\n          if (element.role === 'RESP_MAG') {\n            respMag.push({\n              label: element.first_name + ' ' + element.last_name,\n              value: element.idemployee\n            });\n          }\n        });\n      }\n      this.opMag = opMag;\n      this.setState({\n        result: this.state.selectedDocuments,\n        resultDialog3: true,\n        opMag: opMag,\n        respMag: respMag,\n        mex: message\n      });\n    } else if (filter.length === this.state.selectedDocuments.length) {\n      var FilterOp = this.state.selectedDocuments.filter(element => {\n        var _element$tasks5;\n        return ((_element$tasks5 = element.tasks) === null || _element$tasks5 === void 0 ? void 0 : _element$tasks5.operator) !== null;\n      });\n      if (FilterOp.length > 0) {\n        if (FilterOp.length === this.state.selectedDocuments.length) {\n          if (this.state.results4 !== []) {\n            this.state.results4.forEach(element => {\n              if (element.role === 'OP_MAG') {\n                var taskAss = 0;\n                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n                opMag.push({\n                  label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                  value: element.idemployee\n                });\n              }\n            });\n          }\n          this.opMag = opMag;\n          this.setState({\n            result: this.state.selectedDocuments,\n            resultDialog3: true,\n            opMag: opMag,\n            respMag: respMag,\n            mex: message\n          });\n        } else {\n          this.toast.show({\n            severity: \"warn\",\n            summary: \"Attenzione!\",\n            detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n            life: 3000\n          });\n        }\n      } else {\n        if (this.state.results4 !== []) {\n          this.state.results4.forEach(element => {\n            if (element.role === 'OP_MAG') {\n              var taskAss = 0;\n              taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved);\n              opMag.push({\n                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                value: element.idemployee\n              });\n            }\n          });\n        }\n        this.opMag = opMag;\n        this.setState({\n          result: this.state.selectedDocuments,\n          resultDialog3: true,\n          opMag: opMag,\n          respMag: respMag,\n          mex: message\n        });\n      }\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n        life: 3000\n      });\n    }\n  }\n  //Chiusura dialogo modifica\n  hideDialog() {\n    this.setState({\n      resultDialog3: false\n    });\n  }\n  sendMail(result) {\n    var _result$idSupplying, _result$idSupplying2;\n    this.setState({\n      result,\n      resultDialog4: true,\n      selectedMail: ((_result$idSupplying = result.idSupplying) === null || _result$idSupplying === void 0 ? void 0 : _result$idSupplying.idRegistry.email) !== null ? (_result$idSupplying2 = result.idSupplying) === null || _result$idSupplying2 === void 0 ? void 0 : _result$idSupplying2.idRegistry.email : ''\n    });\n  }\n  hideDialogSendMail() {\n    this.setState({\n      resultDialog4: false\n    });\n  }\n  async senderMail() {\n    var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail;\n    await APIRequest(\"GET\", url).then(res => {\n      var _this$toast;\n      console.log(res.data);\n      (_this$toast = this.toast) === null || _this$toast === void 0 ? void 0 : _this$toast.show({\n        severity: \"success\",\n        summary: \"Ottimo !\",\n        detail: \"L'email è stata inviata con successo\",\n        life: 3000\n      });\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    }).catch(e => {\n      var _this$toast2, _e$response15, _e$response16;\n      console.log(e);\n      (_this$toast2 = this.toast) === null || _this$toast2 === void 0 ? void 0 : _this$toast2.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile inviare l'email. Messaggio errore: \".concat(((_e$response15 = e.response) === null || _e$response15 === void 0 ? void 0 : _e$response15.data) !== undefined ? (_e$response16 = e.response) === null || _e$response16 === void 0 ? void 0 : _e$response16.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  async editDocResult(result) {\n    if (result.tasks === null && !result.erpSync) {\n      await APIRequest(\"GET\", \"documents?documentType=FOR-ORDINE&idDocumentHead=\".concat(result.id)).then(res => {\n        console.log(res.data);\n        var products = [];\n        var totProd = 0;\n        var total = 0;\n        res.data.documentBodies.forEach(el => {\n          totProd += el.colliPreventivo;\n          var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id);\n          if (find !== undefined) {\n            products.push(_objectSpread(_objectSpread({}, find), {}, {\n              quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage,\n              moltiplicatore: el.idProductsPackaging.pcsXPackage,\n              initPrice: find.sell_in,\n              qtaIns: el.colliPreventivo,\n              idProduct2: el.idProductsPackaging.idProduct\n            }));\n          }\n        });\n        var iva = res.data.total === res.data.totalTaxed ? true : false;\n        total = new Intl.NumberFormat(\"it-IT\", {\n          style: \"currency\",\n          currency: \"EUR\",\n          maximumFractionDigits: 6\n        }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0));\n        localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n        localStorage.setItem(\"Cart\", JSON.stringify(products));\n        localStorage.setItem(\"DatiConsegna\", JSON.stringify({\n          idRegistry: res.data.idSupplying.idRegistry,\n          note: res.data.note,\n          termsPayment: res.data.termsPayment,\n          deliveryDate: res.data.deliveryDate,\n          iva: iva\n        }));\n        window.sessionStorage.setItem(\"idDocument\", JSON.stringify({\n          id: result.id,\n          number: result.number,\n          documentDate: result.documentDate\n        }));\n        window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id);\n        window.sessionStorage.setItem(\"Carrello\", totProd);\n        window.sessionStorage.setItem(\"totCart\", total);\n        window.sessionStorage.setItem('idWarehouse', JSON.stringify({\n          name: \"\".concat(res.data.idWarehouses.warehouseName, \" \").concat(res.data.idWarehouses.address, \" \").concat(res.data.idWarehouses.citta, \" (\").concat(res.data.idWarehouses.prov, \"), \").concat(res.data.idWarehouses.cap),\n          code: res.data.idWarehouses.id\n        }));\n        window.location.pathname = '/distributore/Ordina';\n      }).catch(e => {\n        var _e$response17, _e$response18;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non siamo riusciti a reperire il documento da modificare. Messaggio errore: \".concat(((_e$response17 = e.response) === null || _e$response17 === void 0 ? void 0 : _e$response17.data) !== undefined ? (_e$response18 = e.response) === null || _e$response18 === void 0 ? void 0 : _e$response18.data : e.message),\n          life: 3000\n        });\n      });\n    } else {\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Per modificare il documento \\xE8 necessario che non ci sia una task associata e che il documento non sia stato gi\\xE0 sincronizzato sull'ERP\",\n        life: 3000\n      });\n    }\n  }\n  /* Reselt filtro descrizione e codice esterno */\n  async reset() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying5, _element$tasks6;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying5 = element.idSupplying) === null || _element$idSupplying5 === void 0 ? void 0 : _element$idSupplying5.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks6 = element.tasks) === null || _element$tasks6 === void 0 ? void 0 : _element$tasks6.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response19, _e$response20;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response19 = e.response) === null || _e$response19 === void 0 ? void 0 : _e$response19.data) !== undefined ? (_e$response20 = e.response) === null || _e$response20 === void 0 ? void 0 : _e$response20.data : e.message),\n          life: 3000\n        });\n      });\n    } else if (this.state.role === affiliato) {\n      this.setState({\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", \"documents?idWarehouses=\".concat(this.state.selectedWarehouse, \"&documentType=FOR-ORDINE&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page)).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying6, _element$tasks7;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying6 = element.idSupplying) === null || _element$idSupplying6 === void 0 ? void 0 : _element$idSupplying6.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks7 = element.tasks) === null || _element$tasks7 === void 0 ? void 0 : _element$tasks7.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response21, _e$response22;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response21 = e.response) === null || _e$response21 === void 0 ? void 0 : _e$response21.data) !== undefined ? (_e$response22 = e.response) === null || _e$response22 === void 0 ? void 0 : _e$response22.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  /* Reselt filtro categorie */\n  async resetDesc() {\n    var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"));\n    if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n      var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n      this.setState({\n        selectedWarehouse: idWarehouse,\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying7, _element$tasks8;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying7 = element.idSupplying) === null || _element$idSupplying7 === void 0 ? void 0 : _element$idSupplying7.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks8 = element.tasks) === null || _element$tasks8 === void 0 ? void 0 : _element$tasks8.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response23, _e$response24;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response23 = e.response) === null || _e$response23 === void 0 ? void 0 : _e$response23.data) !== undefined ? (_e$response24 = e.response) === null || _e$response24 === void 0 ? void 0 : _e$response24.data : e.message),\n          life: 3000\n        });\n      });\n    } else if (this.state.role === affiliato) {\n      this.setState({\n        loading: true,\n        search: '',\n        selectedSupplyer: null\n      });\n      await APIRequest(\"GET\", \"documents?idWarehouses=\".concat(this.state.selectedWarehouse, \"&documentType=FOR-ORDINE&take=\").concat(this.state.lazyParams.rows, \"&skip=\").concat(this.state.lazyParams.page)).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying8, _element$tasks9;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying8 = element.idSupplying) === null || _element$idSupplying8 === void 0 ? void 0 : _element$idSupplying8.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks9 = element.tasks) === null || _element$tasks9 === void 0 ? void 0 : _element$tasks9.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: {\n            first: this.state.lazyParams.first,\n            rows: this.state.lazyParams.rows,\n            page: this.state.lazyParams.page,\n            pageCount: res.data.totalCount / this.state.lazyParams.rows\n          },\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response25, _e$response26;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response25 = e.response) === null || _e$response25 === void 0 ? void 0 : _e$response25.data) !== undefined ? (_e$response26 = e.response) === null || _e$response26 === void 0 ? void 0 : _e$response26.data : e.message),\n          life: 3000\n        });\n      });\n    }\n  }\n  onPage(event) {\n    this.setState({\n      loading: true\n    });\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying9, _element$tasks0;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying9 = element.idSupplying) === null || _element$idSupplying9 === void 0 ? void 0 : _element$idSupplying9.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks0 = element.tasks) === null || _element$tasks0 === void 0 ? void 0 : _element$tasks0.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: event,\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response27, _e$response28;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response27 = e.response) === null || _e$response27 === void 0 ? void 0 : _e$response27.data) !== undefined ? (_e$response28 = e.response) === null || _e$response28 === void 0 ? void 0 : _e$response28.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onSort(event) {\n    this.setState({\n      loading: true\n    });\n    var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField;\n    if (this.loadLazyTimeout) {\n      clearTimeout(this.loadLazyTimeout);\n    }\n    this.loadLazyTimeout = setTimeout(async () => {\n      var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n      await APIRequest(\"GET\", url).then(res => {\n        var documento = [];\n        res.data.documents.forEach(element => {\n          var _element$idSupplying0, _element$tasks1;\n          var x = {\n            id: element.id,\n            number: element.number,\n            type: element.type,\n            supplying: (_element$idSupplying0 = element.idSupplying) === null || _element$idSupplying0 === void 0 ? void 0 : _element$idSupplying0.idRegistry.firstName,\n            idSupplying: element.idSupplying,\n            documentDate: element.documentDate,\n            documentBodies: element.documentBodies,\n            deliveryDate: element.deliveryDate,\n            tasks: element.tasks,\n            erpSync: element.erpSync,\n            status: (_element$tasks1 = element.tasks) === null || _element$tasks1 === void 0 ? void 0 : _element$tasks1.status,\n            idLogEmail: element.idLogEmail,\n            total: element.total,\n            totalTaxed: element.totalTaxed\n          };\n          documento.push(x);\n        });\n        this.setState({\n          results: documento,\n          results5: documento,\n          totalRecords: res.data.totalCount,\n          lazyParams: _objectSpread(_objectSpread({}, this.state.lazyParams), {}, {\n            sortField: event.sortField,\n            sortOrder: event.sortOrder\n          }),\n          loading: false\n        });\n      }).catch(e => {\n        var _e$response29, _e$response30;\n        console.log(e);\n        this.toast.show({\n          severity: \"error\",\n          summary: \"Siamo spiacenti\",\n          detail: \"Non \\xE8 stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: \".concat(((_e$response29 = e.response) === null || _e$response29 === void 0 ? void 0 : _e$response29.data) !== undefined ? (_e$response30 = e.response) === null || _e$response30 === void 0 ? void 0 : _e$response30.data : e.message),\n          life: 3000\n        });\n      });\n    }, Math.random() * 1000 + 250);\n  }\n  onFilter(event) {\n    event['first'] = 0;\n    this.setState({\n      lazyParams: event\n    }, this.loadLazyData);\n  }\n  selectionHandler(e) {\n    this.setState({\n      selectedDocuments: e.value\n    });\n  }\n  //Apertura dialogo elimina\n  confirmDeleteResult(result) {\n    this.setState({\n      result,\n      deleteResultDialog: true\n    });\n  }\n  //Metodo di cancellazione definitivo grazie alla chiamata axios\n  async deleteResult() {\n    let results = this.state.results.filter(val => val.id !== this.state.result.id);\n    this.setState({\n      results,\n      deleteResultDialog: false,\n      result: this.emptyResult\n    });\n    let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n    await APIRequest(\"DELETE\", url).then(res => {\n      console.log(res.data);\n      this.toast.show({\n        severity: \"success\",\n        summary: \"Ottimo\",\n        detail: \"Documento eliminato con successo\",\n        life: 3000\n      });\n      window.location.reload();\n    }).catch(e => {\n      var _e$response31, _e$response32;\n      console.log(e);\n      this.toast.show({\n        severity: \"error\",\n        summary: \"Siamo spiacenti\",\n        detail: \"Non \\xE8 stato possibile eliminare il documento. Messaggio errore: \".concat(((_e$response31 = e.response) === null || _e$response31 === void 0 ? void 0 : _e$response31.data) !== undefined ? (_e$response32 = e.response) === null || _e$response32 === void 0 ? void 0 : _e$response32.data : e.message),\n        life: 3000\n      });\n    });\n  }\n  hideDeleteResultDialog() {\n    this.setState({\n      deleteResultDialog: false\n    });\n  }\n  closeSelectBefore() {\n    if (this.state.selectedWarehouse !== null) {\n      this.setState({\n        resultDialog: false\n      });\n    } else {\n      this.toast.show({\n        severity: \"warn\",\n        summary: \"Attenzione!\",\n        detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n        life: 3000\n      });\n    }\n  }\n  openFilter() {\n    this.setState({\n      resultDialog5: true\n    });\n  }\n  closeFilter() {\n    this.setState({\n      resultDialog5: false\n    });\n  }\n  render() {\n    //Elementi del footer nelle finestre di dialogo dellaggiunta\n    const resultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row pt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              className: \"p-button-text closeModal\",\n              onClick: this.hidevisualizzaDett,\n              children: [\" \", Costanti.Chiudi, \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Print, {\n              documento: this.state.result,\n              results3: this.state.results3,\n              mex: this.state.mex,\n              doc: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 987,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 986,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 985,\n      columnNumber: 13\n    }, this);\n    const resultDialogFooter2 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end align-items-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"p-button-text closeModal\",\n          onClick: this.closeSelectBefore,\n          children: [\" \", Costanti.Chiudi, \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1009,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1008,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter3 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialog,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1017,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1016,\n      columnNumber: 13\n    }, this);\n    //Elementi del footer nelle finestre di dialogo della modifica\n    const resultDialogFooter4 = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.senderMail,\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-send mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 21\n        }, this), Costanti.Invia, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text closeModal\",\n        onClick: this.hideDialogSendMail,\n        children: [\" \", Costanti.Chiudi, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1025,\n      columnNumber: 13\n    }, this);\n    const deleteResultDialogFooter = /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        label: \"No\",\n        icon: \"pi pi-times\",\n        className: \"p-button-text\",\n        onClick: this.hideDeleteResultDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"p-button-text\",\n        onClick: this.deleteResult,\n        children: [\" \", Costanti.Si, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1037,\n      columnNumber: 13\n    }, this);\n    var fields = [];\n    if (this.state.role === distributore) {\n      fields = [{\n        selectionMode: \"multiple\",\n        headerStyle: {\n          width: \"3em\"\n        }\n      }, {\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"supplying\",\n        header: Costanti.Fornitore,\n        body: \"supplying\",\n        showHeader: true\n      }, {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"deliveryDate\",\n        header: Costanti.DCons,\n        body: \"deliveryDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"tasks.manager.idUser.username\",\n        header: Costanti.Responsabile,\n        body: \"manager\",\n        showHeader: true\n      }, {\n        field: \"tasks.operator.idUser.username\",\n        header: Costanti.Operatore,\n        body: \"operator\",\n        showHeader: true\n      }, {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true\n      }, {\n        field: \"erpSync\",\n        header: \"ERP Sync\",\n        body: \"erpSync\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"idLogEmail\",\n        header: Costanti.mail,\n        body: \"idLogEmail\",\n        showHeader: true\n      }];\n    } else {\n      fields = [{\n        field: \"number\",\n        header: Costanti.NDoc,\n        body: \"nDoc\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"supplying\",\n        header: Costanti.Fornitore,\n        body: \"supplying\",\n        showHeader: true\n      }, {\n        field: \"documentDate\",\n        header: Costanti.DataDoc,\n        body: \"documentDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"deliveryDate\",\n        header: Costanti.DCons,\n        body: \"deliveryDate\",\n        sortable: true,\n        showHeader: true\n      }, {\n        field: \"tasks.status\",\n        header: Costanti.StatoTask,\n        body: \"assigned\",\n        showHeader: true\n      }, {\n        field: \"idLogEmail\",\n        header: Costanti.mail,\n        body: \"idLogEmail\",\n        showHeader: true\n      }];\n    }\n    var actionFields = [];\n    if (this.state.role === distributore) {\n      actionFields = [{\n        name: Costanti.VisDett,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-eye\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1159,\n          columnNumber: 49\n        }, this),\n        handler: this.visualizzaDett\n      }, {\n        name: Costanti.inviaMail,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-send\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1160,\n          columnNumber: 51\n        }, this),\n        handler: this.sendMail\n      }, {\n        name: Costanti.assegnaLavorazione,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-ellipsis-h\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1161,\n          columnNumber: 60\n        }, this),\n        handler: this.editResult,\n        status: 'create',\n        status2: 'counted'\n      }, {\n        name: Costanti.Modifica,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-pencil\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 50\n        }, this),\n        handler: this.editDocResult\n      }, {\n        name: Costanti.Elimina,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-trash\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1163,\n          columnNumber: 49\n        }, this),\n        handler: this.confirmDeleteResult\n      }];\n    } else {\n      actionFields = [{\n        name: Costanti.VisDett,\n        icon: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-eye\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1167,\n          columnNumber: 49\n        }, this),\n        handler: this.visualizzaDett\n      }];\n    }\n    var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    if (this.state.search !== '') {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto';\n    } else {\n      filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datatable-responsive-demo wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.toast = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1180,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1182,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 px-0 solid-head\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: Costanti.DocumentiOrdineAcquisto\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1184,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1183,\n        columnNumber: 17\n      }, this), this.state.selectedWarehouse !== null && this.state.role !== affiliato && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activeFilterContainer p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"activeFilterUl d-flex flex-row align-items-center mb-0 p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mr-3 mb-0 w-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-home mr-2\",\n                  style: {\n                    'fontSize': '.8em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 69\n                }, this), Costanti.Magazzino, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1191,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                className: \"selWar\",\n                value: this.state.selectedWarehouse,\n                options: this.warehouse,\n                onChange: this.onWarehouseSelect,\n                optionLabel: \"name\",\n                placeholder: \"Seleziona magazzino\",\n                filter: true,\n                filterBy: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1190,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1189,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1188,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1187,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CustomDataTable, {\n          ref: el => this.dt = el,\n          value: this.state.results,\n          loading: this.state.loading,\n          fields: fields,\n          dataKey: \"id\",\n          lazy: true,\n          filterDisplay: \"row\",\n          paginator: true,\n          onPage: this.onPage,\n          first: this.state.lazyParams.first,\n          totalRecords: this.state.totalRecords,\n          rows: this.state.lazyParams.rows,\n          rowsPerPageOptions: [10, 20, 50],\n          actionsColumn: actionFields,\n          autoLayout: true,\n          showExportCsvButton: true,\n          selectionMode: \"checkbox\",\n          cellSelection: true,\n          onCellSelect: this.visualizzaDett,\n          selection: this.state.selectedDocuments,\n          onSelectionChange: e => this.selectionHandler(e),\n          showExtraButton2: this.state.role === distributore ? true : false,\n          actionExtraButton2: this.assegnaLavorazioni,\n          labelExtraButton2: Costanti.assegnaLavorazioni,\n          disabledExtraButton2: !this.state.selectedDocuments || !this.state.selectedDocuments.length,\n          onSort: this.onSort,\n          sortField: this.state.lazyParams.sortField,\n          sortOrder: this.state.lazyParams.sortOrder,\n          showExtraButton: true,\n          actionExtraButton: this.openFilter,\n          labelExtraButton: /*#__PURE__*/_jsxDEV(\"ion-icon\", {\n            className: \"mr-2\",\n            name: \"filter-outline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 43\n          }, this),\n          tooltip: \"Filtri\",\n          onFilter: this.onFilter,\n          filters: this.state.lazyParams.filters,\n          classInputSearch: false,\n          fileNames: \"Acquisti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1200,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1198,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog2,\n        header: Costanti.DocAll,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter,\n        onHide: this.hidevisualizzaDett,\n        draggable: false,\n        children: /*#__PURE__*/_jsxDEV(VisualizzaDocumenti, {\n          result: this.state.results3,\n          results: this.state.result,\n          documento: this.state.result,\n          orders: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1249,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1240,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog3,\n        header: this.state.mex,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter3,\n        onHide: this.hideDialog,\n        children: /*#__PURE__*/_jsxDEV(SelezionaOperatore, {\n          result: this.state.result,\n          opMag: this.opMag,\n          respMag: this.state.respMag,\n          distributore: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1265,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1257,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog4,\n        header: Costanti.inviaMail,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        footer: resultDialogFooter4,\n        onHide: this.hideDialogSendMail,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: Costanti.SendMailFornMex\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1276,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(InputText, {\n              value: this.state.selectedMail,\n              onChange: e => this.setState({\n                selectedMail: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1278,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1277,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: [\"* \", Costanti.MailSepVirg]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1280,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1280,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1275,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1267,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.deleteResultDialog,\n        header: Costanti.Conferma,\n        modal: true,\n        footer: deleteResultDialogFooter,\n        onHide: this.hideDeleteResultDialog,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-exclamation-triangle p-mr-3\",\n            style: {\n              fontSize: \"2rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1292,\n            columnNumber: 25\n          }, this), this.state.result && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Costanti.ResDeleteDoc, \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: [this.state.result.number, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1298,\n              columnNumber: 57\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1297,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1291,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1284,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        visible: this.state.resultDialog,\n        header: Costanti.Primadiproseguire,\n        modal: true,\n        className: \"p-fluid modalBox\",\n        onHide: this.closeSelectBefore,\n        footer: resultDialogFooter2,\n        children: [this.state.displayed && /*#__PURE__*/_jsxDEV(JoyrideGen, {\n          title: \"Prima di procedere\",\n          content: \"Seleziona un magazzino \",\n          target: \".selWar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1305,\n          columnNumber: 25\n        }, this), this.state.role !== affiliato && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center flex-column pb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-home mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 50\n            }, this), Costanti.Magazzino]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1311,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"selWar\",\n            value: this.state.selectedWarehouse,\n            options: this.warehouse,\n            onChange: this.onWarehouseSelect,\n            optionLabel: \"name\",\n            placeholder: \"Seleziona magazzino\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1312,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1309,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        visible: this.state.resultDialog5,\n        position: \"left\",\n        onHide: this.closeFilter,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeader\",\n          className: \"filterTitle d-none\",\n          \"data-toggle\": \"collapse\",\n          \"data-target\": \"#filterListContainer\",\n          \"aria-expanded\": \"false\",\n          \"aria-controls\": \"filterListContainer\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-chevron-right mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1318,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1319,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1319,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters\",\n            className: filterDnone,\n            onClick: this.reset,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1320,\n              columnNumber: 99\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1320,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1320,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1317,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"filterHeaderDesk\",\n          className: \"filterTitle d-none d-md-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-filter mr-2\",\n              style: {\n                'fontSize': '.8em'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1323,\n              columnNumber: 46\n            }, this), Costanti.Filtri]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            id: \"resetAllFilters2\",\n            className: filterDnone,\n            onClick: this.resetDesc,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-times mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 104\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 140\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1324,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1322,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1326,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"w-100\",\n          value: this.state.selectedSupplyer,\n          options: this.supplyer,\n          onChange: this.filterDoc,\n          optionLabel: \"name\",\n          placeholder: \"Seleziona fornitore\",\n          filter: true,\n          filterBy: \"name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1327,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1316,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1178,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default UfficioAcquisti;", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "APIRequest", "<PERSON><PERSON>", "Dialog", "Dropdown", "JoyrideGen", "InputText", "affiliato", "distributore", "Toast", "Print", "SelezionaOperatore", "VisualizzaDocumenti", "Nav", "CustomDataTable", "Sidebar", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "props", "emptyResult", "id", "firstName", "referente", "deliveryDestination", "orderDate", "deliveryDate", "termsPayment", "paymentStatus", "status", "onWarehouseSelect", "e", "setState", "selectedWarehouse", "value", "window", "sessionStorage", "setItem", "url", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "param2", "code", "lazyParams", "rows", "page", "then", "res", "documento", "data", "documents", "for<PERSON>ach", "element", "_element$idSupplying", "_element$tasks", "x", "number", "type", "supplying", "idSupplying", "idRegistry", "documentDate", "documentBodies", "tasks", "erpSync", "idLogEmail", "total", "totalTaxed", "push", "results", "results5", "totalRecords", "totalCount", "first", "pageCount", "loading", "catch", "_e$response", "_e$response2", "console", "log", "toast", "show", "severity", "summary", "detail", "concat", "response", "undefined", "message", "life", "_isMounted", "results2", "results3", "results4", "selectedDocuments", "resultDialog", "resultDialog2", "displayed", "resultDialog3", "resultDialog4", "resultDialog5", "opMag", "respMag", "mex", "result", "value2", "search", "selectedMail", "param", "deleteResultDialog", "role", "localStorage", "getItem", "sortField", "sortOrder", "filters", "matchMode", "filterDoc", "name", "_element$idSupplying2", "_element$tasks2", "_e$response3", "_e$response4", "supplyer", "warehouse", "loadLazyTimeout", "visualizzaDett", "bind", "hidevisualizzaDett", "editR<PERSON>ult", "hideDialog", "reset", "resetDesc", "assegnaLavorazioni", "onPage", "onSort", "onFilter", "sendMail", "hideDialogSendMail", "senderMail", "<PERSON><PERSON><PERSON><PERSON>", "deleteResult", "hideDeleteResultDialog", "confirmDeleteResult", "editDocResult", "closeSelectBefore", "openFilter", "closeFilter", "componentDidMount", "idWarehouse", "JSON", "parse", "_element$idSupplying3", "_element$tasks3", "_e$response5", "_e$response6", "entry", "warehouseName", "address", "citta", "prov", "cap", "_e$response7", "_e$response8", "_e$response9", "_e$response0", "warehousesCross", "_element$idSupplying4", "_element$tasks4", "_e$response1", "_e$response10", "_e$response11", "_e$response12", "componentWillUnmount", "documentBody", "task", "_e$response13", "_e$response14", "Intl", "DateTimeFormat", "day", "month", "year", "format", "Date", "filter", "val", "taskAss", "parseInt", "task_create", "task_counted", "task_approved", "label", "first_name", "last_name", "idemployee", "_objectSpread", "el", "length", "FilterOp", "_element$tasks5", "operator", "_result$idSupplying", "_result$idSupplying2", "email", "_this$toast", "setTimeout", "location", "reload", "_this$toast2", "_e$response15", "_e$response16", "products", "totProd", "colliPreventivo", "find", "idProductsPackaging", "idProduct", "supplyingProducts", "obj", "quantity", "pcsXPackage", "moltiplicatore", "initPrice", "sell_in", "qtaIns", "idProduct2", "iva", "NumberFormat", "style", "currency", "maximumFractionDigits", "map", "item", "reduce", "prev", "curr", "stringify", "note", "idWarehouses", "pathname", "_e$response17", "_e$response18", "_element$idSupplying5", "_element$tasks6", "_e$response19", "_e$response20", "_element$idSupplying6", "_element$tasks7", "_e$response21", "_e$response22", "_element$idSupplying7", "_element$tasks8", "_e$response23", "_e$response24", "_element$idSupplying8", "_element$tasks9", "_e$response25", "_e$response26", "event", "clearTimeout", "_element$idSupplying9", "_element$tasks0", "_e$response27", "_e$response28", "Math", "random", "field", "_element$idSupplying0", "_element$tasks1", "_e$response29", "_e$response30", "loadLazyData", "_e$response31", "_e$response32", "render", "resultD<PERSON><PERSON><PERSON><PERSON>er", "Fragment", "children", "className", "onClick", "<PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "resultDialogFooter2", "resultDialogFooter3", "resultDialogFooter4", "Invia", "deleteResultDialogFooter", "icon", "Si", "fields", "selectionMode", "headerStyle", "width", "header", "NDoc", "body", "sortable", "showHeader", "Fornitore", "DataDoc", "DCons", "Responsabile", "Operatore", "StatoTask", "mail", "actionFields", "<PERSON><PERSON><PERSON><PERSON>", "handler", "inviaMail", "assegnaLavorazione", "status2", "Modifica", "Elimina", "filterDnone", "ref", "DocumentiOrdineAcquisto", "<PERSON><PERSON><PERSON><PERSON>", "options", "onChange", "optionLabel", "placeholder", "filterBy", "dt", "dataKey", "lazy", "filterDisplay", "paginator", "rowsPerPageOptions", "actionsColumn", "autoLayout", "showExportCsvButton", "cellSelection", "onCellSelect", "selection", "onSelectionChange", "showExtraButton2", "actionExtraButton2", "labelExtraButton2", "disabledExtraButton2", "showExtraButton", "actionExtraButton", "labelExtraButton", "tooltip", "classInputSearch", "fileNames", "visible", "DocAll", "modal", "footer", "onHide", "draggable", "orders", "SendMailFornMex", "target", "MailSepVirg", "Conferma", "fontSize", "ResDeleteDoc", "Primadiproseguire", "title", "content", "position", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/distributore/ufficioAcquisti.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* UfficioAcquisti - operazioni sull'ufficio acquisti\n*\n*/\nimport React, { Component } from 'react';\nimport { Costanti } from '../../components/traduttore/const';\nimport { APIRequest } from '../../components/generalizzazioni/apireq';\nimport { Button } from 'primereact/button';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { JoyrideGen } from '../../components/footer/joyride';\nimport { InputText } from 'primereact/inputtext';\nimport { affiliato, distributore } from '../../components/route';\nimport { Toast } from 'primereact/toast';\nimport { Print } from '../../components/print/templateOrderPrint';\nimport SelezionaOperatore from '../respMagazzino/selezionaOperatore';\nimport VisualizzaDocumenti from '../../components/generalizzazioni/visualizzaDocumenti';\nimport Nav from \"../../components/navigation/Nav\";\nimport CustomDataTable from '../../components/customDataTable';\nimport '../../css/DataTableDemo.css';\nimport { Sidebar } from 'primereact/sidebar';\n\nclass UfficioAcquisti extends Component {\n    //Stato iniziale elementi tabella\n    emptyResult = {\n        id: null,\n        firstName: \"\",\n        referente: \"\",\n        deliveryDestination: \"\",\n        orderDate: \"\",\n        deliveryDate: \"\",\n        termsPayment: \"\",\n        paymentStatus: \"\",\n        status: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this._isMounted = false;\n        this.state = {\n            results: null,\n            results2: null,\n            results3: null,\n            results4: null,\n            results5: null,\n            selectedWarehouse: null,\n            selectedDocuments: null,\n            loading: true,\n            resultDialog: false,\n            resultDialog2: false,\n            displayed: false,\n            resultDialog3: false,\n            resultDialog4: false,\n            resultDialog5: false,\n            opMag: '',\n            respMag: '',\n            mex: '',\n            result: this.emptyResult,\n            value: null,\n            value2: null,\n            totalRecords: 0,\n            search: '',\n            selectedMail: '',\n            param: '?idWarehouses=',\n            param2: '&idSupplying=',\n            selectedSupplyer: null,\n            deleteResultDialog: null,\n            role: localStorage.getItem('role'),\n            lazyParams: {\n                first: 0,\n                rows: 20,\n                page: 0,\n                sortField: null,\n                sortOrder: null,\n                filters: {\n                    'number': { value: '', matchMode: 'contains' },\n                    'type': { value: '', matchMode: 'contains' },\n                    'documentDate': { value: '', matchMode: 'contains' },\n                }\n            }\n        };\n        /* Ricerca elementi per categoria selezionata */\n        this.filterDoc = async e => {\n            this.setState({\n                loading: true,\n                search: e.value.name,\n                selectedSupplyer: e.value\n            });\n\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + this.state.param2 + e.value.code + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei CLI ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        };\n        this.supplyer = []\n        this.warehouse = [];\n        this.loadLazyTimeout = null;\n        this.visualizzaDett = this.visualizzaDett.bind(this);\n        this.hidevisualizzaDett = this.hidevisualizzaDett.bind(this);\n        this.onWarehouseSelect = this.onWarehouseSelect.bind(this);\n        this.editResult = this.editResult.bind(this);\n        this.hideDialog = this.hideDialog.bind(this);\n        this.reset = this.reset.bind(this);\n        this.resetDesc = this.resetDesc.bind(this);\n        this.assegnaLavorazioni = this.assegnaLavorazioni.bind(this);\n        this.onPage = this.onPage.bind(this);\n        this.onSort = this.onSort.bind(this);\n        this.onFilter = this.onFilter.bind(this);\n        this.sendMail = this.sendMail.bind(this);\n        this.hideDialogSendMail = this.hideDialogSendMail.bind(this);\n        this.senderMail = this.senderMail.bind(this);\n        this.selectionHandler = this.selectionHandler.bind(this);\n        this.deleteResult = this.deleteResult.bind(this);\n        this.hideDeleteResultDialog = this.hideDeleteResultDialog.bind(this);\n        this.confirmDeleteResult = this.confirmDeleteResult.bind(this);\n        this.editDocResult = this.editDocResult.bind(this);\n        this.closeSelectBefore = this.closeSelectBefore.bind(this);\n        this.openFilter = this.openFilter.bind(this);\n        this.closeFilter = this.closeFilter.bind(this);\n    }\n    async componentDidMount() {\n        this._isMounted = true;\n        if (this.state.role !== affiliato) {\n            var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n            if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n                idWarehouse = idWarehouse.code !== undefined ? idWarehouse.code : idWarehouse\n                this.setState({ selectedWarehouse: idWarehouse });\n                var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n                await APIRequest(\"GET\", url)\n                    .then((res) => {\n                        var documento = []\n                        res.data.documents.forEach(element => {\n                            var x = {\n                                id: element.id,\n                                number: element.number,\n                                type: element.type,\n                                supplying: element.idSupplying?.idRegistry.firstName,\n                                idSupplying: element.idSupplying,\n                                documentDate: element.documentDate,\n                                documentBodies: element.documentBodies,\n                                deliveryDate: element.deliveryDate,\n                                tasks: element.tasks,\n                                erpSync: element.erpSync,\n                                status: element.tasks?.status,\n                                idLogEmail: element.idLogEmail,\n                                total: element.total,\n                                totalTaxed: element.totalTaxed\n                            }\n                            documento.push(x)\n                        })\n                        this.setState({\n                            results: documento,\n                            results5: documento,\n                            totalRecords: res.data.totalCount,\n                            lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                            loading: false\n                        });\n                    })\n                    .catch((e) => {\n                        console.log(e);\n                        this.toast.show({\n                            severity: \"error\",\n                            summary: \"Siamo spiacenti\",\n                            detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                            life: 3000,\n                        });\n                    });\n            } else {\n                this.setState({ resultDialog: true, displayed: true })\n            }\n            await APIRequest(\"GET\", \"warehouses/\")\n                .then((res) => {\n                    for (var entry of res.data) {\n                        this.warehouse.push({\n                            name: entry.warehouseName + ' ' + entry.address + ' ' + entry.citta + ' (' + entry.prov + '), ' + entry.cap,\n                            value: entry.id\n                        })\n                    }\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare i magazzini. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            await APIRequest(\"GET\", \"employees/?employeesEffort=true\")\n                .then((res) => {\n                    if (this._isMounted) {\n                        this.setState({\n                            results4: res.data,\n                        });\n                    }\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare gli operatori di magazzino. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            await APIRequest('GET', 'supplying/')\n                .then(res => {\n                    res.data.forEach(element => {\n                        if (element && element.idRegistry) {\n                            var x = {\n                                name: element.idRegistry.firstName || 'Nome non disponibile',\n                                code: element.id || 0\n                            }\n                            this.supplyer.push(x)\n                        }\n                    })\n                }).catch((e) => {\n                    console.log(e)\n                })\n        } else {\n            this.setState({ selectedWarehouse: JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse })\n            await APIRequest(\"GET\", `documents?idWarehouses=${JSON.parse(localStorage.getItem('user')).warehousesCross[0].idWarehouse}&documentType=FOR-ORDINE&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n            await APIRequest(\"GET\", \"supplyingaffiliate\")\n                .then((res) => {\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.idSupplying.idRegistry.firstName,\n                            code: element.idSupplying.id\n                        }\n                        this.supplyer.push(x)\n                    })\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare i fornitori. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n\n    componentWillUnmount() {\n        this._isMounted = false;\n    }\n\n    /* Seleziono il magazzino per la get sui documenti */\n    onWarehouseSelect = async (e) => {\n        this.setState({ selectedWarehouse: e.value });\n        window.sessionStorage.setItem(\"idWarehouse\", e.value);\n        var url = 'documents?idWarehouses=' + e.value + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                var documento = []\n                res.data.documents.forEach(element => {\n                    var x = {\n                        id: element.id,\n                        number: element.number,\n                        type: element.type,\n                        supplying: element.idSupplying?.idRegistry.firstName,\n                        idSupplying: element.idSupplying,\n                        documentDate: element.documentDate,\n                        documentBodies: element.documentBodies,\n                        deliveryDate: element.deliveryDate,\n                        tasks: element.tasks,\n                        erpSync: element.erpSync,\n                        status: element.tasks?.status,\n                        idLogEmail: element.idLogEmail,\n                        total: element.total,\n                        totalTaxed: element.totalTaxed\n                    }\n                    documento.push(x)\n                })\n                this.setState({\n                    results: documento,\n                    results5: documento,\n                    totalRecords: res.data.totalCount,\n                    lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                    loading: false\n                });\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    //Apertura dialogo aggiunta\n    async visualizzaDett(result) {\n        var url = 'documents?documentType=FOR-ORDINE&idDocumentHead=' + result.id\n        var documentBody = []\n        var task = []\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                documentBody = res.data.documentBodies\n                result.documentBodies = res.data.documentBodies\n                task = res.data\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile visualizzare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        this.setState({\n            resultDialog2: true,\n            result: task,\n            results2: this.state.results.filter((val) => val.id === result.id),\n            results3: documentBody,\n            mex: message,\n        });\n    }\n    //Chiusura dialogo visualizzazione\n    hidevisualizzaDett(result) {\n        this.setState({\n            result: result,\n            resultDialog2: false,\n        });\n    }\n    //Apertura dialogo modifica\n    editResult(result) {\n        var message =\n            \"Documento numero: \" +\n            result.number +\n            \" del \" +\n            new Intl.DateTimeFormat(\"it-IT\", {\n                day: \"2-digit\",\n                month: \"2-digit\",\n                year: \"numeric\",\n            }).format(new Date(result.documentDate));\n        var opMag = [];\n        var respMag = [];\n        if (this.state.results4 !== []) {\n            this.state.results4.forEach((element) => {\n                if (element.role === 'OP_MAG' && result.tasks !== null) {\n                    var taskAss = 0\n                    taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                    opMag.push({\n                        label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                        value: element.idemployee,\n                    });\n                } else if (element.role === 'RESP_MAG' && result.tasks === null) {\n                    respMag.push({\n                        label: element.first_name + ' ' + element.last_name,\n                        value: element.idemployee,\n                    });\n                }\n            });\n        }\n        this.opMag = opMag;\n        this.setState({\n            result: { ...result },\n            resultDialog3: true,\n            opMag: opMag,\n            mex: message,\n            respMag: respMag\n        });\n    }\n    assegnaLavorazioni() {\n        var message =\n            \"Trasmissione multipla documenti\"\n        var opMag = [];\n        var respMag = [];\n        var filter = this.state.selectedDocuments.filter(el => el.tasks !== null)\n        if (filter.length === 0) {\n            if (this.state.results4 !== []) {\n                this.state.results4.forEach((element) => {\n                    if (element.role === 'RESP_MAG') {\n                        respMag.push({\n                            label: element.first_name + ' ' + element.last_name,\n                            value: element.idemployee,\n                        });\n                    }\n                })\n            }\n            this.opMag = opMag;\n            this.setState({\n                result: this.state.selectedDocuments,\n                resultDialog3: true,\n                opMag: opMag,\n                respMag: respMag,\n                mex: message,\n            });\n        } else if (filter.length === this.state.selectedDocuments.length) {\n            var FilterOp = this.state.selectedDocuments.filter(element => element.tasks?.operator !== null)\n            if (FilterOp.length > 0) {\n                if (FilterOp.length === this.state.selectedDocuments.length) {\n                    if (this.state.results4 !== []) {\n                        this.state.results4.forEach((element) => {\n                            if (element.role === 'OP_MAG') {\n                                var taskAss = 0\n                                taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                                opMag.push({\n                                    label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                    value: element.idemployee,\n                                });\n                            }\n                        })\n                    }\n                    this.opMag = opMag;\n                    this.setState({\n                        result: this.state.selectedDocuments,\n                        resultDialog3: true,\n                        opMag: opMag,\n                        respMag: respMag,\n                        mex: message,\n                    });\n                } else {\n                    this.toast.show({\n                        severity: \"warn\",\n                        summary: \"Attenzione!\",\n                        detail: \"In alcuni dei documenti selezionati non sono presenti assegnazioni di operatori di magazzino assegnarli prima di procedere ad un eventuale modifica\",\n                        life: 3000,\n                    });\n                }\n            } else {\n                if (this.state.results4 !== []) {\n                    this.state.results4.forEach((element) => {\n                        if (element.role === 'OP_MAG') {\n                            var taskAss = 0\n                            taskAss = parseInt(element.task_create) + parseInt(element.task_counted) + parseInt(element.task_approved)\n                            opMag.push({\n                                label: element.first_name + ' ' + element.last_name + ' (task assegnate: ' + taskAss + ')',\n                                value: element.idemployee,\n                            });\n                        }\n                    })\n                }\n                this.opMag = opMag;\n                this.setState({\n                    result: this.state.selectedDocuments,\n                    resultDialog3: true,\n                    opMag: opMag,\n                    respMag: respMag,\n                    mex: message,\n                });\n            }\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"I documenti selezionati devono avere tutti la task con il manager già assegnato o non devono essere associati ad alcuna task\",\n                life: 3000,\n            });\n        }\n    }\n    //Chiusura dialogo modifica\n    hideDialog() {\n        this.setState({\n            resultDialog3: false,\n        });\n    }\n    sendMail(result) {\n        this.setState({\n            result,\n            resultDialog4: true,\n            selectedMail: result.idSupplying?.idRegistry.email !== null ? result.idSupplying?.idRegistry.email : ''\n        });\n    }\n    hideDialogSendMail() {\n        this.setState({\n            resultDialog4: false,\n        });\n    }\n    async senderMail() {\n        var url = 'email/sendfororders?idDocument=' + this.state.result.id + '&emailSuppliyng=' + this.state.selectedMail\n        await APIRequest(\"GET\", url)\n            .then((res) => {\n                console.log(res.data)\n                this.toast?.show({\n                    severity: \"success\",\n                    summary: \"Ottimo !\",\n                    detail: \"L'email è stata inviata con successo\",\n                    life: 3000,\n                });\n                setTimeout(() => {\n                    window.location.reload();\n                }, 3000)\n            })\n            .catch((e) => {\n                console.log(e);\n                this.toast?.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile inviare l'email. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            });\n    }\n    async editDocResult(result) {\n        if (result.tasks === null && !result.erpSync) {\n            await APIRequest(\"GET\", `documents?documentType=FOR-ORDINE&idDocumentHead=${result.id}`)\n                .then((res) => {\n                    console.log(res.data)\n                    var products = []\n                    var totProd = 0\n                    var total = 0\n                    res.data.documentBodies.forEach(el => {\n                        totProd += el.colliPreventivo\n                        var find = el.idProductsPackaging.idProduct.supplyingProducts.find(obj => obj.idSupplying.id === res.data.idSupplying.id)\n                        if (find !== undefined) {\n                            products.push({ ...find, quantity: el.colliPreventivo * el.idProductsPackaging.pcsXPackage, moltiplicatore: el.idProductsPackaging.pcsXPackage, initPrice: find.sell_in, qtaIns: el.colliPreventivo, idProduct2: el.idProductsPackaging.idProduct })\n                        }\n                    })\n                    var iva = res.data.total === res.data.totalTaxed ? true : false\n                    total = new Intl.NumberFormat(\"it-IT\", {\n                        style: \"currency\",\n                        currency: \"EUR\",\n                        maximumFractionDigits: 6\n                    }).format(products.map(item => item.quantity * item.sell_in).reduce((prev, curr) => prev + curr, 0))\n                    localStorage.setItem(\"Prodotti\", JSON.stringify(products));\n                    localStorage.setItem(\"Cart\", JSON.stringify(products));\n                    localStorage.setItem(\"DatiConsegna\", JSON.stringify({ idRegistry: res.data.idSupplying.idRegistry, note: res.data.note, termsPayment: res.data.termsPayment, deliveryDate: res.data.deliveryDate, iva: iva }))\n                    window.sessionStorage.setItem(\"idDocument\", JSON.stringify({ id: result.id, number: result.number, documentDate: result.documentDate }))\n                    window.sessionStorage.setItem(\"idSupplier\", res.data.idSupplying.id)\n                    window.sessionStorage.setItem(\"Carrello\", totProd)\n                    window.sessionStorage.setItem(\"totCart\", total)\n                    window.sessionStorage.setItem('idWarehouse', JSON.stringify({ name: `${res.data.idWarehouses.warehouseName} ${res.data.idWarehouses.address} ${res.data.idWarehouses.citta} (${res.data.idWarehouses.prov}), ${res.data.idWarehouses.cap}`, code: res.data.idWarehouses.id }))\n                    window.location.pathname = '/distributore/Ordina'\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non siamo riusciti a reperire il documento da modificare. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else {\n            this.toast.show({\n                severity: \"error\",\n                summary: \"Siamo spiacenti\",\n                detail: `Per modificare il documento è necessario che non ci sia una task associata e che il documento non sia stato già sincronizzato sull'ERP`,\n                life: 3000,\n            });\n        }\n    }\n    /* Reselt filtro descrizione e codice esterno */\n    async reset() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else if (this.state.role === affiliato) {\n            this.setState({ loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", `documents?idWarehouses=${this.state.selectedWarehouse}&documentType=FOR-ORDINE&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    /* Reselt filtro categorie */\n    async resetDesc() {\n        var idWarehouse = JSON.parse(window.sessionStorage.getItem(\"idWarehouse\"))\n        if (idWarehouse !== null && idWarehouse !== 0 && idWarehouse !== undefined) {\n            var url = 'documents?idWarehouses=' + idWarehouse + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page;\n            this.setState({ selectedWarehouse: idWarehouse, loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        } else if (this.state.role === affiliato) {\n            this.setState({ loading: true, search: '', selectedSupplyer: null });\n            await APIRequest(\"GET\", `documents?idWarehouses=${this.state.selectedWarehouse}&documentType=FOR-ORDINE&take=${this.state.lazyParams.rows}&skip=${this.state.lazyParams.page}`)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { first: this.state.lazyParams.first, rows: this.state.lazyParams.rows, page: this.state.lazyParams.page, pageCount: res.data.totalCount / this.state.lazyParams.rows, },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }\n    }\n    onPage(event) {\n        this.setState({ loading: true });\n\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + event.rows + '&skip=' + event.page;\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: event,\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n    onSort(event) {\n        this.setState({ loading: true });\n        var field = event.sortField === 'retailer' ? 'idRetailer.idRegistry.firstName' : event.sortField\n        if (this.loadLazyTimeout) {\n            clearTimeout(this.loadLazyTimeout);\n        }\n        this.loadLazyTimeout = setTimeout(async () => {\n            var url = 'documents' + this.state.param + this.state.selectedWarehouse + (this.state.selectedSupplyer !== null ? this.state.param2 + this.state.selectedSupplyer.code : '') + '&documentType=FOR-ORDINE&take=' + this.state.lazyParams.rows + '&skip=' + this.state.lazyParams.page + '&field=' + field + '&sorting=' + (event.sortOrder === 1 ? 'ASC' : 'DESC');\n            await APIRequest(\"GET\", url)\n                .then((res) => {\n                    var documento = []\n                    res.data.documents.forEach(element => {\n                        var x = {\n                            id: element.id,\n                            number: element.number,\n                            type: element.type,\n                            supplying: element.idSupplying?.idRegistry.firstName,\n                            idSupplying: element.idSupplying,\n                            documentDate: element.documentDate,\n                            documentBodies: element.documentBodies,\n                            deliveryDate: element.deliveryDate,\n                            tasks: element.tasks,\n                            erpSync: element.erpSync,\n                            status: element.tasks?.status,\n                            idLogEmail: element.idLogEmail,\n                            total: element.total,\n                            totalTaxed: element.totalTaxed\n                        }\n                        documento.push(x)\n                    })\n                    this.setState({\n                        results: documento,\n                        results5: documento,\n                        totalRecords: res.data.totalCount,\n                        lazyParams: { ...this.state.lazyParams, sortField: event.sortField, sortOrder: event.sortOrder },\n                        loading: false\n                    });\n                })\n                .catch((e) => {\n                    console.log(e);\n                    this.toast.show({\n                        severity: \"error\",\n                        summary: \"Siamo spiacenti\",\n                        detail: `Non è stato possibile visualizzare la lista dei FOR ordine. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                        life: 3000,\n                    });\n                });\n        }, Math.random() * 1000 + 250);\n    }\n\n    onFilter(event) {\n        event['first'] = 0;\n        this.setState({ lazyParams: event }, this.loadLazyData);\n    }\n    selectionHandler(e) {\n        this.setState({ selectedDocuments: e.value })\n    }\n    //Apertura dialogo elimina\n    confirmDeleteResult(result) {\n        this.setState({\n            result,\n            deleteResultDialog: true,\n        });\n    }\n    //Metodo di cancellazione definitivo grazie alla chiamata axios\n    async deleteResult() {\n        let results = this.state.results.filter(\n            (val) => val.id !== this.state.result.id\n        );\n        this.setState({\n            results,\n            deleteResultDialog: false,\n            result: this.emptyResult,\n        });\n        let url = \"documents/?idDocumentHead=\" + this.state.result.id;\n        await APIRequest(\"DELETE\", url)\n            .then(res => {\n                console.log(res.data);\n                this.toast.show({\n                    severity: \"success\",\n                    summary: \"Ottimo\",\n                    detail: \"Documento eliminato con successo\",\n                    life: 3000,\n                });\n                window.location.reload();\n            }).catch((e) => {\n                console.log(e)\n                this.toast.show({\n                    severity: \"error\",\n                    summary: \"Siamo spiacenti\",\n                    detail: `Non è stato possibile eliminare il documento. Messaggio errore: ${e.response?.data !== undefined ? e.response?.data : e.message}`,\n                    life: 3000,\n                });\n            })\n    }\n    hideDeleteResultDialog() {\n        this.setState({\n            deleteResultDialog: false,\n        });\n    }\n    closeSelectBefore() {\n        if (this.state.selectedWarehouse !== null) {\n            this.setState({\n                resultDialog: false\n            })\n        } else {\n            this.toast.show({\n                severity: \"warn\",\n                summary: \"Attenzione!\",\n                detail: \"È necessario inserire i parametri richiesti per poter procedere\",\n                life: 3000,\n            });\n        }\n    }\n    openFilter() {\n        this.setState({\n            resultDialog5: true\n        })\n    }\n    closeFilter() {\n        this.setState({\n            resultDialog5: false\n        })\n    }\n    render() {\n        //Elementi del footer nelle finestre di dialogo dellaggiunta\n        const resultDialogFooter = (\n            <React.Fragment>\n                <div className=\"row pt-2\">\n                    <div className=\"col-12\">\n                        <div className=\"d-flex justify-content-end\">\n                            <Button\n                                className=\"p-button-text closeModal\"\n                                onClick={this.hidevisualizzaDett}\n                            >\n                                {\" \"}\n                                {Costanti.Chiudi}{\" \"}\n                            </Button>\n                            <Print\n                                documento={this.state.result}\n                                results3={this.state.results3}\n                                mex={this.state.mex}\n                                doc={true}\n                            />\n                        </div>\n                    </div>\n                </div>\n            </React.Fragment>\n        );\n        const resultDialogFooter2 = (\n            <React.Fragment>\n                <div className='d-flex justify-content-end align-items-center'>\n                    <Button className=\"p-button-text closeModal\" onClick={this.closeSelectBefore} > {Costanti.Chiudi} </Button>\n                </div>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter3 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialog}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        //Elementi del footer nelle finestre di dialogo della modifica\n        const resultDialogFooter4 = (\n            <React.Fragment>\n                <Button className=\"p-button-text closeModal\" onClick={this.senderMail}>\n                    {\" \"}\n                    <i className='pi pi-send mr-2'></i>{Costanti.Invia}{\" \"}\n                </Button>\n                <Button className=\"p-button-text closeModal\" onClick={this.hideDialogSendMail}>\n                    {\" \"}\n                    {Costanti.Chiudi}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        const deleteResultDialogFooter = (\n            <React.Fragment>\n                <Button\n                    label=\"No\"\n                    icon=\"pi pi-times\"\n                    className=\"p-button-text\"\n                    onClick={this.hideDeleteResultDialog}\n                />\n                <Button className=\"p-button-text\" onClick={this.deleteResult}>\n                    {\" \"}\n                    {Costanti.Si}{\" \"}\n                </Button>\n            </React.Fragment>\n        );\n        var fields = []\n        if (this.state.role === distributore) {\n            fields = [\n                { selectionMode: \"multiple\", headerStyle: { width: \"3em\" } },\n                {\n                    field: \"number\",\n                    header: Costanti.NDoc,\n                    body: \"nDoc\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"supplying\",\n                    header: Costanti.Fornitore,\n                    body: \"supplying\",\n                    showHeader: true,\n                },\n                {\n                    field: \"documentDate\",\n                    header: Costanti.DataDoc,\n                    body: \"documentDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"deliveryDate\",\n                    header: Costanti.DCons,\n                    body: \"deliveryDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.manager.idUser.username\",\n                    header: Costanti.Responsabile,\n                    body: \"manager\",\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.operator.idUser.username\",\n                    header: Costanti.Operatore,\n                    body: \"operator\",\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.status\",\n                    header: Costanti.StatoTask,\n                    body: \"assigned\",\n                    showHeader: true,\n                },\n                {\n                    field: \"erpSync\",\n                    header: \"ERP Sync\",\n                    body: \"erpSync\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"idLogEmail\",\n                    header: Costanti.mail,\n                    body: \"idLogEmail\",\n                    showHeader: true,\n                }\n            ];\n        } else {\n            fields = [\n                {\n                    field: \"number\",\n                    header: Costanti.NDoc,\n                    body: \"nDoc\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"supplying\",\n                    header: Costanti.Fornitore,\n                    body: \"supplying\",\n                    showHeader: true,\n                },\n                {\n                    field: \"documentDate\",\n                    header: Costanti.DataDoc,\n                    body: \"documentDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"deliveryDate\",\n                    header: Costanti.DCons,\n                    body: \"deliveryDate\",\n                    sortable: true,\n                    showHeader: true,\n                },\n                {\n                    field: \"tasks.status\",\n                    header: Costanti.StatoTask,\n                    body: \"assigned\",\n                    showHeader: true,\n                },\n                {\n                    field: \"idLogEmail\",\n                    header: Costanti.mail,\n                    body: \"idLogEmail\",\n                    showHeader: true,\n                }\n            ];\n        }\n        var actionFields = []\n        if (this.state.role === distributore) {\n            actionFields = [\n                { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n                { name: Costanti.inviaMail, icon: <i className=\"pi pi-send\" />, handler: this.sendMail },\n                { name: Costanti.assegnaLavorazione, icon: <i className=\"pi pi-ellipsis-h\" />, handler: this.editResult, status: 'create', status2: 'counted' },\n                { name: Costanti.Modifica, icon: <i className=\"pi pi-pencil\" />, handler: this.editDocResult },\n                { name: Costanti.Elimina, icon: <i className=\"pi pi-trash\" />, handler: this.confirmDeleteResult },\n            ];\n        } else {\n            actionFields = [\n                { name: Costanti.VisDett, icon: <i className=\"pi pi-eye\" />, handler: this.visualizzaDett },\n            ];\n        }\n\n        var filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        if (this.state.search !== '') {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto'\n        } else {\n            filterDnone = 'resetFilters mx-0 py-1 ml-auto d-none'\n        }\n        return (\n            <div className=\"datatable-responsive-demo wrapper\">\n                {/* Il componente Toast permette di creare e visualizzare messaggi */}\n                <Toast ref={(el) => this.toast = el} />\n                {/* Il componente Nav contiene l'header */}\n                <Nav />\n                <div className=\"col-12 px-0 solid-head\">\n                    <h1>{Costanti.DocumentiOrdineAcquisto}</h1>\n                </div>\n                {this.state.selectedWarehouse !== null && this.state.role !== affiliato &&\n                    <div className='activeFilterContainer p-2'>\n                        <ul className='activeFilterUl d-flex flex-row align-items-center mb-0 p-2'>\n                            <li className='d-flex align-items-center mr-2 px-3 py-2 my-2 my-lg-0'>\n                                <div className='d-flex justify-content-center align-items-center'>\n                                    <h5 className=\"mr-3 mb-0 w-100\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}:</h5>\n                                    <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                }\n                <div className=\"card\">\n                    {/* Componente primereact per la creazione della tabella */}\n                    <CustomDataTable\n                        ref={(el) => this.dt = el}\n                        value={this.state.results}\n                        loading={this.state.loading}\n                        fields={fields}\n                        dataKey=\"id\"\n                        lazy\n                        filterDisplay=\"row\"\n                        paginator\n                        onPage={this.onPage}\n                        first={this.state.lazyParams.first}\n                        totalRecords={this.state.totalRecords}\n                        rows={this.state.lazyParams.rows}\n                        rowsPerPageOptions={[10, 20, 50]}\n                        actionsColumn={actionFields}\n                        autoLayout={true}\n                        showExportCsvButton={true}\n                        selectionMode=\"checkbox\"\n                        cellSelection={true}\n                        onCellSelect={this.visualizzaDett}\n                        selection={this.state.selectedDocuments}\n                        onSelectionChange={(e) => this.selectionHandler(e)}\n                        showExtraButton2={this.state.role === distributore ? true : false}\n                        actionExtraButton2={this.assegnaLavorazioni}\n                        labelExtraButton2={Costanti.assegnaLavorazioni}\n                        disabledExtraButton2={!this.state.selectedDocuments || !this.state.selectedDocuments.length}\n                        onSort={this.onSort}\n                        sortField={this.state.lazyParams.sortField}\n                        sortOrder={this.state.lazyParams.sortOrder}\n                        showExtraButton={true}\n                        actionExtraButton={this.openFilter}\n                        labelExtraButton={<ion-icon className=\"mr-2\" name=\"filter-outline\"></ion-icon>}\n                        tooltip='Filtri'\n                        onFilter={this.onFilter}\n                        filters={this.state.lazyParams.filters}\n                        classInputSearch={false}\n                        fileNames=\"Acquisti\"\n                    />\n                </div>\n                {/* Struttura dialogo per la visualizzazione dettaglio ordine */}\n                <Dialog\n                    visible={this.state.resultDialog2}\n                    header={Costanti.DocAll}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter}\n                    onHide={this.hidevisualizzaDett}\n                    draggable={false}\n                >\n                    <VisualizzaDocumenti\n                        result={this.state.results3}\n                        results={this.state.result}\n                        documento={this.state.result}\n                        orders={true}\n                    />\n                </Dialog>\n                {/* Struttura dialogo per la modifica */}\n                <Dialog\n                    visible={this.state.resultDialog3}\n                    header={this.state.mex}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter3}\n                    onHide={this.hideDialog}\n                >\n                    <SelezionaOperatore result={this.state.result} opMag={this.opMag} respMag={this.state.respMag} distributore={true} />\n                </Dialog>\n                <Dialog\n                    visible={this.state.resultDialog4}\n                    header={Costanti.inviaMail}\n                    modal\n                    className=\"p-fluid modalBox\"\n                    footer={resultDialogFooter4}\n                    onHide={this.hideDialogSendMail}\n                >\n                    <div className='row'>\n                        <div className='col-12'><span>{Costanti.SendMailFornMex}</span></div>\n                        <div className='col-12'>\n                            <InputText value={this.state.selectedMail} onChange={(e) => this.setState({ selectedMail: e.target.value })} />\n                        </div>\n                        <div className='col-12'><span className='text-danger'>* {Costanti.MailSepVirg}</span></div>\n                    </div>\n                </Dialog>\n                {/* Struttura dialogo per la cancellazione */}\n                <Dialog\n                    visible={this.state.deleteResultDialog}\n                    header={Costanti.Conferma}\n                    modal\n                    footer={deleteResultDialogFooter}\n                    onHide={this.hideDeleteResultDialog}\n                >\n                    <div className=\"confirmation-content\">\n                        <i\n                            className=\"pi pi-exclamation-triangle p-mr-3\"\n                            style={{ fontSize: \"2rem\" }}\n                        />\n                        {this.state.result && (\n                            <span>\n                                {Costanti.ResDeleteDoc} <b>{this.state.result.number}?</b>\n                            </span>\n                        )}\n                    </div>\n                </Dialog>\n                <Dialog visible={this.state.resultDialog} header={Costanti.Primadiproseguire} modal className=\"p-fluid modalBox\" onHide={this.closeSelectBefore} footer={resultDialogFooter2}>\n                    {this.state.displayed &&\n                        <JoyrideGen title='Prima di procedere' content='Seleziona un magazzino ' target='.selWar' />\n                    }\n\n                    {this.state.role !== affiliato &&\n                        <div className='d-flex justify-content-center flex-column pb-3'>\n                            <h5 className=\"mb-0\"><i className=\"pi pi-home mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Magazzino}</h5>\n                            <hr></hr>\n                            <Dropdown className=\"selWar\" value={this.state.selectedWarehouse} options={this.warehouse} onChange={this.onWarehouseSelect} optionLabel=\"name\" placeholder=\"Seleziona magazzino\" filter filterBy=\"name\" />\n                        </div>\n                    }\n                </Dialog>\n                <Sidebar visible={this.state.resultDialog5} position='left' onHide={this.closeFilter}>\n                    <div id=\"filterHeader\" className='filterTitle d-none' data-toggle=\"collapse\" data-target=\"#filterListContainer\" aria-expanded=\"false\" aria-controls=\"filterListContainer\">\n                        <i className=\"pi pi-chevron-right mr-2\"></i>\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters\" className={filterDnone} onClick={this.reset}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <div id=\"filterHeaderDesk\" className=\"filterTitle d-none d-md-flex\">\n                        <h5 className=\"mb-0\"><i className=\"pi pi-filter mr-2\" style={{ 'fontSize': '.8em' }}></i>{Costanti.Filtri}</h5>\n                        <Button id=\"resetAllFilters2\" className={filterDnone} onClick={this.resetDesc}><i className=\"pi pi-times mr-2\"></i><span>Reset</span></Button>\n                    </div>\n                    <hr></hr>\n                    <Dropdown className='w-100' value={this.state.selectedSupplyer} options={this.supplyer} onChange={this.filterDoc} optionLabel=\"name\" placeholder=\"Seleziona fornitore\" filter filterBy=\"name\" />\n                </Sidebar>\n            </div>\n        );\n    }\n}\n\nexport default UfficioAcquisti;"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,SAAS,EAAEC,YAAY,QAAQ,wBAAwB;AAChE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,KAAK,QAAQ,2CAA2C;AACjE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,mBAAmB,MAAM,uDAAuD;AACvF,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,6BAA6B;AACpC,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,eAAe,SAASnB,SAAS,CAAC;EAapCoB,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAbhB;IAAA,KACAC,WAAW,GAAG;MACVC,EAAE,EAAE,IAAI;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE;IACZ,CAAC;IA+RD;IAAA,KACAC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;MAC7B,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEF,CAAC,CAACG;MAAM,CAAC,CAAC;MAC7CC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEN,CAAC,CAACG,KAAK,CAAC;MACrD,IAAII,GAAG,GAAG,yBAAyB,GAAGP,CAAC,CAACG,KAAK,IAAI,IAAI,CAACK,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAC1P,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAC,oBAAA,EAAAC,cAAA;UAClC,IAAIC,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAL,oBAAA,GAAED,OAAO,CAACO,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBO,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAyB,cAAA,GAAEF,OAAO,CAACW,KAAK,cAAAT,cAAA,uBAAbA,cAAA,CAAezB,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA8C,WAAA,EAAAC,YAAA;QACVC,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAT,WAAA,GAAA9C,CAAC,CAACwD,QAAQ,cAAAV,WAAA,uBAAVA,WAAA,CAAY5B,IAAI,MAAKuC,SAAS,IAAAV,YAAA,GAAG/C,CAAC,CAACwD,QAAQ,cAAAT,YAAA,uBAAVA,YAAA,CAAY7B,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IAxUG,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACpD,KAAK,GAAG;MACT8B,OAAO,EAAE,IAAI;MACbuB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdxB,QAAQ,EAAE,IAAI;MACdrC,iBAAiB,EAAE,IAAI;MACvB8D,iBAAiB,EAAE,IAAI;MACvBpB,OAAO,EAAE,IAAI;MACbqB,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,IAAI,CAACrF,WAAW;MACxBc,KAAK,EAAE,IAAI;MACXwE,MAAM,EAAE,IAAI;MACZnC,YAAY,EAAE,CAAC;MACfoC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,gBAAgB;MACvBpE,MAAM,EAAE,eAAe;MACvBD,gBAAgB,EAAE,IAAI;MACtBsE,kBAAkB,EAAE,IAAI;MACxBC,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAClCtE,UAAU,EAAE;QACR8B,KAAK,EAAE,CAAC;QACR7B,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,CAAC;QACPqE,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACL,QAAQ,EAAE;YAAElF,KAAK,EAAE,EAAE;YAAEmF,SAAS,EAAE;UAAW,CAAC;UAC9C,MAAM,EAAE;YAAEnF,KAAK,EAAE,EAAE;YAAEmF,SAAS,EAAE;UAAW,CAAC;UAC5C,cAAc,EAAE;YAAEnF,KAAK,EAAE,EAAE;YAAEmF,SAAS,EAAE;UAAW;QACvD;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAMvF,CAAC,IAAI;MACxB,IAAI,CAACC,QAAQ,CAAC;QACV2C,OAAO,EAAE,IAAI;QACbgC,MAAM,EAAE5E,CAAC,CAACG,KAAK,CAACqF,IAAI;QACpB/E,gBAAgB,EAAET,CAAC,CAACG;MACxB,CAAC,CAAC;MAEF,IAAII,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACsE,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACN,iBAAiB,GAAG,IAAI,CAACM,KAAK,CAACE,MAAM,GAAGV,CAAC,CAACG,KAAK,CAACQ,IAAI,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MAClN,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAoE,qBAAA,EAAAC,eAAA;UAClC,IAAIlE,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA8D,qBAAA,GAAEpE,OAAO,CAACO,WAAW,cAAA6D,qBAAA,uBAAnBA,qBAAA,CAAqB5D,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA4F,eAAA,GAAErE,OAAO,CAACW,KAAK,cAAA0D,eAAA,uBAAbA,eAAA,CAAe5F,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA2F,YAAA,EAAAC,YAAA;QACV5C,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoC,YAAA,GAAA3F,CAAC,CAACwD,QAAQ,cAAAmC,YAAA,uBAAVA,YAAA,CAAYzE,IAAI,MAAKuC,SAAS,IAAAmC,YAAA,GAAG5F,CAAC,CAACwD,QAAQ,cAAAoC,YAAA,uBAAVA,YAAA,CAAY1E,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACkC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAAClG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACkG,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACG,UAAU,GAAG,IAAI,CAACA,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACJ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACN,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACR,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACT,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACU,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACV,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAACW,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACX,IAAI,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACY,UAAU,GAAG,IAAI,CAACA,UAAU,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACa,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACb,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACc,YAAY,GAAG,IAAI,CAACA,YAAY,CAACd,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACe,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACf,IAAI,CAAC,IAAI,CAAC;IACpE,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAChB,IAAI,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACjB,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACkB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAClB,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACnB,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACoB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpB,IAAI,CAAC,IAAI,CAAC;EAClD;EACA,MAAMqB,iBAAiBA,CAAA,EAAG;IACtB,IAAI,CAAC1D,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACpD,KAAK,CAACwE,IAAI,KAAKzG,SAAS,EAAE;MAC/B,IAAIgJ,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACrH,MAAM,CAACC,cAAc,CAAC6E,OAAO,CAAC,aAAa,CAAC,CAAC;MAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK9D,SAAS,EAAE;QACxE8D,WAAW,GAAGA,WAAW,CAAC5G,IAAI,KAAK8C,SAAS,GAAG8D,WAAW,CAAC5G,IAAI,GAAG4G,WAAW;QAC7E,IAAI,CAACtH,QAAQ,CAAC;UAAEC,iBAAiB,EAAEqH;QAAY,CAAC,CAAC;QACjD,IAAIhH,GAAG,GAAG,yBAAyB,GAAGgH,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC/G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;QACzJ,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;UACX,IAAIC,SAAS,GAAG,EAAE;UAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;YAAA,IAAAqG,qBAAA,EAAAC,eAAA;YAClC,IAAInG,CAAC,GAAG;cACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;cACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;cACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;cAClBC,SAAS,GAAA+F,qBAAA,GAAErG,OAAO,CAACO,WAAW,cAAA8F,qBAAA,uBAAnBA,qBAAA,CAAqB7F,UAAU,CAACtC,SAAS;cACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;cAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;cAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;cACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;cAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;cACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;cACxBnC,MAAM,GAAA6H,eAAA,GAAEtG,OAAO,CAACW,KAAK,cAAA2F,eAAA,uBAAbA,eAAA,CAAe7H,MAAM;cAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;cAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;cACpBC,UAAU,EAAEf,OAAO,CAACe;YACxB,CAAC;YACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;UACrB,CAAC,CAAC;UACF,IAAI,CAACvB,QAAQ,CAAC;YACVqC,OAAO,EAAErB,SAAS;YAClBsB,QAAQ,EAAEtB,SAAS;YACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;YACjC7B,UAAU,EAAE;cAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;cAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;cAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;cAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;YAAM,CAAC;YACpL+B,OAAO,EAAE;UACb,CAAC,CAAC;QACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;UAAA,IAAA4H,YAAA,EAAAC,YAAA;UACV7E,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;UACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAqE,YAAA,GAAA5H,CAAC,CAACwD,QAAQ,cAAAoE,YAAA,uBAAVA,YAAA,CAAY1G,IAAI,MAAKuC,SAAS,IAAAoE,YAAA,GAAG7H,CAAC,CAACwD,QAAQ,cAAAqE,YAAA,uBAAVA,YAAA,CAAY3G,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;YACxJC,IAAI,EAAE;UACV,CAAC,CAAC;QACN,CAAC,CAAC;MACV,CAAC,MAAM;QACH,IAAI,CAAC1D,QAAQ,CAAC;UAAEgE,YAAY,EAAE,IAAI;UAAEE,SAAS,EAAE;QAAK,CAAC,CAAC;MAC1D;MACA,MAAMlG,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CACjC8C,IAAI,CAAEC,GAAG,IAAK;QACX,KAAK,IAAI8G,KAAK,IAAI9G,GAAG,CAACE,IAAI,EAAE;UACxB,IAAI,CAAC4E,SAAS,CAACzD,IAAI,CAAC;YAChBmD,IAAI,EAAEsC,KAAK,CAACC,aAAa,GAAG,GAAG,GAAGD,KAAK,CAACE,OAAO,GAAG,GAAG,GAAGF,KAAK,CAACG,KAAK,GAAG,IAAI,GAAGH,KAAK,CAACI,IAAI,GAAG,KAAK,GAAGJ,KAAK,CAACK,GAAG;YAC3GhI,KAAK,EAAE2H,KAAK,CAACxI;UACjB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,CACDuD,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAoI,YAAA,EAAAC,YAAA;QACVrF,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAA6E,YAAA,GAAApI,CAAC,CAACwD,QAAQ,cAAA4E,YAAA,uBAAVA,YAAA,CAAYlH,IAAI,MAAKuC,SAAS,IAAA4E,YAAA,GAAGrI,CAAC,CAACwD,QAAQ,cAAA6E,YAAA,uBAAVA,YAAA,CAAYnH,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UAC5IC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,MAAM1F,UAAU,CAAC,KAAK,EAAE,iCAAiC,CAAC,CACrD8C,IAAI,CAAEC,GAAG,IAAK;QACX,IAAI,IAAI,CAAC4C,UAAU,EAAE;UACjB,IAAI,CAAC3D,QAAQ,CAAC;YACV8D,QAAQ,EAAE/C,GAAG,CAACE;UAClB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,CACD2B,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAsI,YAAA,EAAAC,YAAA;QACVvF,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,yFAAAC,MAAA,CAAsF,EAAA+E,YAAA,GAAAtI,CAAC,CAACwD,QAAQ,cAAA8E,YAAA,uBAAVA,YAAA,CAAYpH,IAAI,MAAKuC,SAAS,IAAA8E,YAAA,GAAGvI,CAAC,CAACwD,QAAQ,cAAA+E,YAAA,uBAAVA,YAAA,CAAYrH,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UAC3JC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,MAAM1F,UAAU,CAAC,KAAK,EAAE,YAAY,CAAC,CAChC8C,IAAI,CAACC,GAAG,IAAI;QACTA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIA,OAAO,IAAIA,OAAO,CAACQ,UAAU,EAAE;YAC/B,IAAIL,CAAC,GAAG;cACJgE,IAAI,EAAEnE,OAAO,CAACQ,UAAU,CAACtC,SAAS,IAAI,sBAAsB;cAC5DoB,IAAI,EAAEU,OAAO,CAAC/B,EAAE,IAAI;YACxB,CAAC;YACD,IAAI,CAACuG,QAAQ,CAACxD,IAAI,CAACb,CAAC,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC,CAAC,CAACqB,KAAK,CAAE7C,CAAC,IAAK;QACZgD,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MAClB,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACC,QAAQ,CAAC;QAAEC,iBAAiB,EAAEsH,IAAI,CAACC,KAAK,CAACxC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACsD,eAAe,CAAC,CAAC,CAAC,CAACjB;MAAY,CAAC,CAAC;MAC7G,MAAMtJ,UAAU,CAAC,KAAK,4BAAAsF,MAAA,CAA4BiE,IAAI,CAACC,KAAK,CAACxC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACsD,eAAe,CAAC,CAAC,CAAC,CAACjB,WAAW,oCAAAhE,MAAA,CAAiC,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACC,IAAI,YAAA0C,MAAA,CAAS,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACE,IAAI,CAAE,CAAC,CACrNC,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAoH,qBAAA,EAAAC,eAAA;UAClC,IAAIlH,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA8G,qBAAA,GAAEpH,OAAO,CAACO,WAAW,cAAA6G,qBAAA,uBAAnBA,qBAAA,CAAqB5G,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA4I,eAAA,GAAErH,OAAO,CAACW,KAAK,cAAA0G,eAAA,uBAAbA,eAAA,CAAe5I,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA2I,YAAA,EAAAC,aAAA;QACV5F,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoF,YAAA,GAAA3I,CAAC,CAACwD,QAAQ,cAAAmF,YAAA,uBAAVA,YAAA,CAAYzH,IAAI,MAAKuC,SAAS,IAAAmF,aAAA,GAAG5I,CAAC,CAACwD,QAAQ,cAAAoF,aAAA,uBAAVA,aAAA,CAAY1H,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;MACN,MAAM1F,UAAU,CAAC,KAAK,EAAE,oBAAoB,CAAC,CACxC8C,IAAI,CAAEC,GAAG,IAAK;QACXA,GAAG,CAACE,IAAI,CAACE,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIG,CAAC,GAAG;YACJgE,IAAI,EAAEnE,OAAO,CAACO,WAAW,CAACC,UAAU,CAACtC,SAAS;YAC9CoB,IAAI,EAAEU,OAAO,CAACO,WAAW,CAACtC;UAC9B,CAAC;UACD,IAAI,CAACuG,QAAQ,CAACxD,IAAI,CAACb,CAAC,CAAC;QACzB,CAAC,CAAC;MACN,CAAC,CAAC,CACDqB,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA6I,aAAA,EAAAC,aAAA;QACV9F,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,0EAAAC,MAAA,CAAuE,EAAAsF,aAAA,GAAA7I,CAAC,CAACwD,QAAQ,cAAAqF,aAAA,uBAAVA,aAAA,CAAY3H,IAAI,MAAKuC,SAAS,IAAAqF,aAAA,GAAG9I,CAAC,CAACwD,QAAQ,cAAAsF,aAAA,uBAAVA,aAAA,CAAY5H,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UAC5IC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EAEAoF,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACnF,UAAU,GAAG,KAAK;EAC3B;EA+CA;EACA,MAAMoC,cAAcA,CAACtB,MAAM,EAAE;IACzB,IAAInE,GAAG,GAAG,mDAAmD,GAAGmE,MAAM,CAACpF,EAAE;IACzE,IAAI0J,YAAY,GAAG,EAAE;IACrB,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMhL,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;MACXgI,YAAY,GAAGhI,GAAG,CAACE,IAAI,CAACa,cAAc;MACtC2C,MAAM,CAAC3C,cAAc,GAAGf,GAAG,CAACE,IAAI,CAACa,cAAc;MAC/CkH,IAAI,GAAGjI,GAAG,CAACE,IAAI;IACnB,CAAC,CAAC,CACD2B,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAAkJ,aAAA,EAAAC,aAAA;MACVnG,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,2EAAAC,MAAA,CAAwE,EAAA2F,aAAA,GAAAlJ,CAAC,CAACwD,QAAQ,cAAA0F,aAAA,uBAAVA,aAAA,CAAYhI,IAAI,MAAKuC,SAAS,IAAA0F,aAAA,GAAGnJ,CAAC,CAACwD,QAAQ,cAAA2F,aAAA,uBAAVA,aAAA,CAAYjI,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC7IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACN,IAAID,OAAO,GACP,oBAAoB,GACpBgB,MAAM,CAACjD,MAAM,GACb,OAAO,GACP,IAAI2H,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAChF,MAAM,CAAC5C,YAAY,CAAC,CAAC;IAC5C,IAAI,CAAC7B,QAAQ,CAAC;MACViE,aAAa,EAAE,IAAI;MACnBQ,MAAM,EAAEuE,IAAI;MACZpF,QAAQ,EAAE,IAAI,CAACrD,KAAK,CAAC8B,OAAO,CAACqH,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACtK,EAAE,KAAKoF,MAAM,CAACpF,EAAE,CAAC;MAClEwE,QAAQ,EAAEkF,YAAY;MACtBvE,GAAG,EAAEf;IACT,CAAC,CAAC;EACN;EACA;EACAwC,kBAAkBA,CAACxB,MAAM,EAAE;IACvB,IAAI,CAACzE,QAAQ,CAAC;MACVyE,MAAM,EAAEA,MAAM;MACdR,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA;EACAiC,UAAUA,CAACzB,MAAM,EAAE;IACf,IAAIhB,OAAO,GACP,oBAAoB,GACpBgB,MAAM,CAACjD,MAAM,GACb,OAAO,GACP,IAAI2H,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC7BC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIC,IAAI,CAAChF,MAAM,CAAC5C,YAAY,CAAC,CAAC;IAC5C,IAAIyC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAChE,KAAK,CAACuD,QAAQ,KAAK,EAAE,EAAE;MAC5B,IAAI,CAACvD,KAAK,CAACuD,QAAQ,CAAC3C,OAAO,CAAEC,OAAO,IAAK;QACrC,IAAIA,OAAO,CAAC2D,IAAI,KAAK,QAAQ,IAAIN,MAAM,CAAC1C,KAAK,KAAK,IAAI,EAAE;UACpD,IAAI6H,OAAO,GAAG,CAAC;UACfA,OAAO,GAAGC,QAAQ,CAACzI,OAAO,CAAC0I,WAAW,CAAC,GAAGD,QAAQ,CAACzI,OAAO,CAAC2I,YAAY,CAAC,GAAGF,QAAQ,CAACzI,OAAO,CAAC4I,aAAa,CAAC;UAC1G1F,KAAK,CAAClC,IAAI,CAAC;YACP6H,KAAK,EAAE7I,OAAO,CAAC8I,UAAU,GAAG,GAAG,GAAG9I,OAAO,CAAC+I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;YAC1F1J,KAAK,EAAEkB,OAAO,CAACgJ;UACnB,CAAC,CAAC;QACN,CAAC,MAAM,IAAIhJ,OAAO,CAAC2D,IAAI,KAAK,UAAU,IAAIN,MAAM,CAAC1C,KAAK,KAAK,IAAI,EAAE;UAC7DwC,OAAO,CAACnC,IAAI,CAAC;YACT6H,KAAK,EAAE7I,OAAO,CAAC8I,UAAU,GAAG,GAAG,GAAG9I,OAAO,CAAC+I,SAAS;YACnDjK,KAAK,EAAEkB,OAAO,CAACgJ;UACnB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC9F,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtE,QAAQ,CAAC;MACVyE,MAAM,EAAA4F,aAAA,KAAO5F,MAAM,CAAE;MACrBN,aAAa,EAAE,IAAI;MACnBG,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAEf,OAAO;MACZc,OAAO,EAAEA;IACb,CAAC,CAAC;EACN;EACA+B,kBAAkBA,CAAA,EAAG;IACjB,IAAI7C,OAAO,GACP,iCAAiC;IACrC,IAAIa,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAImF,MAAM,GAAG,IAAI,CAACnJ,KAAK,CAACwD,iBAAiB,CAAC2F,MAAM,CAACY,EAAE,IAAIA,EAAE,CAACvI,KAAK,KAAK,IAAI,CAAC;IACzE,IAAI2H,MAAM,CAACa,MAAM,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAAChK,KAAK,CAACuD,QAAQ,KAAK,EAAE,EAAE;QAC5B,IAAI,CAACvD,KAAK,CAACuD,QAAQ,CAAC3C,OAAO,CAAEC,OAAO,IAAK;UACrC,IAAIA,OAAO,CAAC2D,IAAI,KAAK,UAAU,EAAE;YAC7BR,OAAO,CAACnC,IAAI,CAAC;cACT6H,KAAK,EAAE7I,OAAO,CAAC8I,UAAU,GAAG,GAAG,GAAG9I,OAAO,CAAC+I,SAAS;cACnDjK,KAAK,EAAEkB,OAAO,CAACgJ;YACnB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAC9F,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACtE,QAAQ,CAAC;QACVyE,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACwD,iBAAiB;QACpCI,aAAa,EAAE,IAAI;QACnBG,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA,OAAO;QAChBC,GAAG,EAAEf;MACT,CAAC,CAAC;IACN,CAAC,MAAM,IAAIiG,MAAM,CAACa,MAAM,KAAK,IAAI,CAAChK,KAAK,CAACwD,iBAAiB,CAACwG,MAAM,EAAE;MAC9D,IAAIC,QAAQ,GAAG,IAAI,CAACjK,KAAK,CAACwD,iBAAiB,CAAC2F,MAAM,CAACtI,OAAO;QAAA,IAAAqJ,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAArJ,OAAO,CAACW,KAAK,cAAA0I,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,MAAK,IAAI;MAAA,EAAC;MAC/F,IAAIF,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,CAACD,MAAM,KAAK,IAAI,CAAChK,KAAK,CAACwD,iBAAiB,CAACwG,MAAM,EAAE;UACzD,IAAI,IAAI,CAAChK,KAAK,CAACuD,QAAQ,KAAK,EAAE,EAAE;YAC5B,IAAI,CAACvD,KAAK,CAACuD,QAAQ,CAAC3C,OAAO,CAAEC,OAAO,IAAK;cACrC,IAAIA,OAAO,CAAC2D,IAAI,KAAK,QAAQ,EAAE;gBAC3B,IAAI6E,OAAO,GAAG,CAAC;gBACfA,OAAO,GAAGC,QAAQ,CAACzI,OAAO,CAAC0I,WAAW,CAAC,GAAGD,QAAQ,CAACzI,OAAO,CAAC2I,YAAY,CAAC,GAAGF,QAAQ,CAACzI,OAAO,CAAC4I,aAAa,CAAC;gBAC1G1F,KAAK,CAAClC,IAAI,CAAC;kBACP6H,KAAK,EAAE7I,OAAO,CAAC8I,UAAU,GAAG,GAAG,GAAG9I,OAAO,CAAC+I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;kBAC1F1J,KAAK,EAAEkB,OAAO,CAACgJ;gBACnB,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA,IAAI,CAAC9F,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACtE,QAAQ,CAAC;YACVyE,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACwD,iBAAiB;YACpCI,aAAa,EAAE,IAAI;YACnBG,KAAK,EAAEA,KAAK;YACZC,OAAO,EAAEA,OAAO;YAChBC,GAAG,EAAEf;UACT,CAAC,CAAC;QACN,CAAC,MAAM;UACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;YACZC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,aAAa;YACtBC,MAAM,EAAE,qJAAqJ;YAC7JK,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,CAACnD,KAAK,CAACuD,QAAQ,KAAK,EAAE,EAAE;UAC5B,IAAI,CAACvD,KAAK,CAACuD,QAAQ,CAAC3C,OAAO,CAAEC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAAC2D,IAAI,KAAK,QAAQ,EAAE;cAC3B,IAAI6E,OAAO,GAAG,CAAC;cACfA,OAAO,GAAGC,QAAQ,CAACzI,OAAO,CAAC0I,WAAW,CAAC,GAAGD,QAAQ,CAACzI,OAAO,CAAC2I,YAAY,CAAC,GAAGF,QAAQ,CAACzI,OAAO,CAAC4I,aAAa,CAAC;cAC1G1F,KAAK,CAAClC,IAAI,CAAC;gBACP6H,KAAK,EAAE7I,OAAO,CAAC8I,UAAU,GAAG,GAAG,GAAG9I,OAAO,CAAC+I,SAAS,GAAG,oBAAoB,GAAGP,OAAO,GAAG,GAAG;gBAC1F1J,KAAK,EAAEkB,OAAO,CAACgJ;cACnB,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAC9F,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACtE,QAAQ,CAAC;UACVyE,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACwD,iBAAiB;UACpCI,aAAa,EAAE,IAAI;UACnBG,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBC,GAAG,EAAEf;QACT,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH,IAAI,CAACR,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,8HAA8H;QACtIK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACAyC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnG,QAAQ,CAAC;MACVmE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACAuC,QAAQA,CAACjC,MAAM,EAAE;IAAA,IAAAkG,mBAAA,EAAAC,oBAAA;IACb,IAAI,CAAC5K,QAAQ,CAAC;MACVyE,MAAM;MACNL,aAAa,EAAE,IAAI;MACnBQ,YAAY,EAAE,EAAA+F,mBAAA,GAAAlG,MAAM,CAAC9C,WAAW,cAAAgJ,mBAAA,uBAAlBA,mBAAA,CAAoB/I,UAAU,CAACiJ,KAAK,MAAK,IAAI,IAAAD,oBAAA,GAAGnG,MAAM,CAAC9C,WAAW,cAAAiJ,oBAAA,uBAAlBA,oBAAA,CAAoBhJ,UAAU,CAACiJ,KAAK,GAAG;IACzG,CAAC,CAAC;EACN;EACAlE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC3G,QAAQ,CAAC;MACVoE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA,MAAMwC,UAAUA,CAAA,EAAG;IACf,IAAItG,GAAG,GAAG,iCAAiC,GAAG,IAAI,CAACC,KAAK,CAACkE,MAAM,CAACpF,EAAE,GAAG,kBAAkB,GAAG,IAAI,CAACkB,KAAK,CAACqE,YAAY;IACjH,MAAM5G,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;MAAA,IAAA+J,WAAA;MACX/H,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;MACrB,CAAA6J,WAAA,OAAI,CAAC7H,KAAK,cAAA6H,WAAA,uBAAVA,WAAA,CAAY5H,IAAI,CAAC;QACbC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,sCAAsC;QAC9CK,IAAI,EAAE;MACV,CAAC,CAAC;MACFqH,UAAU,CAAC,MAAM;QACb5K,MAAM,CAAC6K,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,CAAC,CACDrI,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAAmL,YAAA,EAAAC,aAAA,EAAAC,aAAA;MACVrI,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,CAAAmL,YAAA,OAAI,CAACjI,KAAK,cAAAiI,YAAA,uBAAVA,YAAA,CAAYhI,IAAI,CAAC;QACbC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,iEAAAC,MAAA,CAA8D,EAAA6H,aAAA,GAAApL,CAAC,CAACwD,QAAQ,cAAA4H,aAAA,uBAAVA,aAAA,CAAYlK,IAAI,MAAKuC,SAAS,IAAA4H,aAAA,GAAGrL,CAAC,CAACwD,QAAQ,cAAA6H,aAAA,uBAAVA,aAAA,CAAYnK,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QACnIC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACA,MAAMuD,aAAaA,CAACxC,MAAM,EAAE;IACxB,IAAIA,MAAM,CAAC1C,KAAK,KAAK,IAAI,IAAI,CAAC0C,MAAM,CAACzC,OAAO,EAAE;MAC1C,MAAMhE,UAAU,CAAC,KAAK,sDAAAsF,MAAA,CAAsDmB,MAAM,CAACpF,EAAE,CAAE,CAAC,CACnFyB,IAAI,CAAEC,GAAG,IAAK;QACXgC,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;QACrB,IAAIoK,QAAQ,GAAG,EAAE;QACjB,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIpJ,KAAK,GAAG,CAAC;QACbnB,GAAG,CAACE,IAAI,CAACa,cAAc,CAACX,OAAO,CAACmJ,EAAE,IAAI;UAClCgB,OAAO,IAAIhB,EAAE,CAACiB,eAAe;UAC7B,IAAIC,IAAI,GAAGlB,EAAE,CAACmB,mBAAmB,CAACC,SAAS,CAACC,iBAAiB,CAACH,IAAI,CAACI,GAAG,IAAIA,GAAG,CAACjK,WAAW,CAACtC,EAAE,KAAK0B,GAAG,CAACE,IAAI,CAACU,WAAW,CAACtC,EAAE,CAAC;UACzH,IAAImM,IAAI,KAAKhI,SAAS,EAAE;YACpB6H,QAAQ,CAACjJ,IAAI,CAAAiI,aAAA,CAAAA,aAAA,KAAMmB,IAAI;cAAEK,QAAQ,EAAEvB,EAAE,CAACiB,eAAe,GAAGjB,EAAE,CAACmB,mBAAmB,CAACK,WAAW;cAAEC,cAAc,EAAEzB,EAAE,CAACmB,mBAAmB,CAACK,WAAW;cAAEE,SAAS,EAAER,IAAI,CAACS,OAAO;cAAEC,MAAM,EAAE5B,EAAE,CAACiB,eAAe;cAAEY,UAAU,EAAE7B,EAAE,CAACmB,mBAAmB,CAACC;YAAS,EAAE,CAAC;UACxP;QACJ,CAAC,CAAC;QACF,IAAIU,GAAG,GAAGrL,GAAG,CAACE,IAAI,CAACiB,KAAK,KAAKnB,GAAG,CAACE,IAAI,CAACkB,UAAU,GAAG,IAAI,GAAG,KAAK;QAC/DD,KAAK,GAAG,IAAIiH,IAAI,CAACkD,YAAY,CAAC,OAAO,EAAE;UACnCC,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,KAAK;UACfC,qBAAqB,EAAE;QAC3B,CAAC,CAAC,CAAChD,MAAM,CAAC6B,QAAQ,CAACoB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,QAAQ,GAAGa,IAAI,CAACT,OAAO,CAAC,CAACU,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpG7H,YAAY,CAAC3E,OAAO,CAAC,UAAU,EAAEkH,IAAI,CAACuF,SAAS,CAACzB,QAAQ,CAAC,CAAC;QAC1DrG,YAAY,CAAC3E,OAAO,CAAC,MAAM,EAAEkH,IAAI,CAACuF,SAAS,CAACzB,QAAQ,CAAC,CAAC;QACtDrG,YAAY,CAAC3E,OAAO,CAAC,cAAc,EAAEkH,IAAI,CAACuF,SAAS,CAAC;UAAElL,UAAU,EAAEb,GAAG,CAACE,IAAI,CAACU,WAAW,CAACC,UAAU;UAAEmL,IAAI,EAAEhM,GAAG,CAACE,IAAI,CAAC8L,IAAI;UAAEpN,YAAY,EAAEoB,GAAG,CAACE,IAAI,CAACtB,YAAY;UAAED,YAAY,EAAEqB,GAAG,CAACE,IAAI,CAACvB,YAAY;UAAE0M,GAAG,EAAEA;QAAI,CAAC,CAAC,CAAC;QAC9MjM,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEkH,IAAI,CAACuF,SAAS,CAAC;UAAEzN,EAAE,EAAEoF,MAAM,CAACpF,EAAE;UAAEmC,MAAM,EAAEiD,MAAM,CAACjD,MAAM;UAAEK,YAAY,EAAE4C,MAAM,CAAC5C;QAAa,CAAC,CAAC,CAAC;QACxI1B,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEU,GAAG,CAACE,IAAI,CAACU,WAAW,CAACtC,EAAE,CAAC;QACpEc,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEiL,OAAO,CAAC;QAClDnL,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,SAAS,EAAE6B,KAAK,CAAC;QAC/C/B,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEkH,IAAI,CAACuF,SAAS,CAAC;UAAEvH,IAAI,KAAAjC,MAAA,CAAKvC,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAAClF,aAAa,OAAAxE,MAAA,CAAIvC,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAACjF,OAAO,OAAAzE,MAAA,CAAIvC,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAAChF,KAAK,QAAA1E,MAAA,CAAKvC,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAAC/E,IAAI,SAAA3E,MAAA,CAAMvC,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAAC9E,GAAG,CAAE;UAAExH,IAAI,EAAEK,GAAG,CAACE,IAAI,CAAC+L,YAAY,CAAC3N;QAAG,CAAC,CAAC,CAAC;QAC9Qc,MAAM,CAAC6K,QAAQ,CAACiC,QAAQ,GAAG,sBAAsB;MACrD,CAAC,CAAC,CACDrK,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAmN,aAAA,EAAAC,aAAA;QACVpK,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,iFAAAC,MAAA,CAAiF,EAAA4J,aAAA,GAAAnN,CAAC,CAACwD,QAAQ,cAAA2J,aAAA,uBAAVA,aAAA,CAAYjM,IAAI,MAAKuC,SAAS,IAAA2J,aAAA,GAAGpN,CAAC,CAACwD,QAAQ,cAAA4J,aAAA,uBAAVA,aAAA,CAAYlM,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACtJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,IAAI,CAACT,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,gJAA0I;QAChJK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACA;EACA,MAAM0C,KAAKA,CAAA,EAAG;IACV,IAAIkB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACrH,MAAM,CAACC,cAAc,CAAC6E,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK9D,SAAS,EAAE;MACxE,IAAIlD,GAAG,GAAG,yBAAyB,GAAGgH,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC/G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACb,QAAQ,CAAC;QAAEC,iBAAiB,EAAEqH,WAAW;QAAE3E,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAEnE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMxC,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAgM,qBAAA,EAAAC,eAAA;UAClC,IAAI9L,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA0L,qBAAA,GAAEhM,OAAO,CAACO,WAAW,cAAAyL,qBAAA,uBAAnBA,qBAAA,CAAqBxL,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAwN,eAAA,GAAEjM,OAAO,CAACW,KAAK,cAAAsL,eAAA,uBAAbA,eAAA,CAAexN,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAuN,aAAA,EAAAC,aAAA;QACVxK,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAgK,aAAA,GAAAvN,CAAC,CAACwD,QAAQ,cAAA+J,aAAA,uBAAVA,aAAA,CAAYrM,IAAI,MAAKuC,SAAS,IAAA+J,aAAA,GAAGxN,CAAC,CAACwD,QAAQ,cAAAgK,aAAA,uBAAVA,aAAA,CAAYtM,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM,IAAI,IAAI,CAACnD,KAAK,CAACwE,IAAI,KAAKzG,SAAS,EAAE;MACtC,IAAI,CAAC0B,QAAQ,CAAC;QAAE2C,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAEnE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpE,MAAMxC,UAAU,CAAC,KAAK,4BAAAsF,MAAA,CAA4B,IAAI,CAAC/C,KAAK,CAACN,iBAAiB,oCAAAqD,MAAA,CAAiC,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACC,IAAI,YAAA0C,MAAA,CAAS,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACE,IAAI,CAAE,CAAC,CAC1KC,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAoM,qBAAA,EAAAC,eAAA;UAClC,IAAIlM,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA8L,qBAAA,GAAEpM,OAAO,CAACO,WAAW,cAAA6L,qBAAA,uBAAnBA,qBAAA,CAAqB5L,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA4N,eAAA,GAAErM,OAAO,CAACW,KAAK,cAAA0L,eAAA,uBAAbA,eAAA,CAAe5N,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA2N,aAAA,EAAAC,aAAA;QACV5K,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAoK,aAAA,GAAA3N,CAAC,CAACwD,QAAQ,cAAAmK,aAAA,uBAAVA,aAAA,CAAYzM,IAAI,MAAKuC,SAAS,IAAAmK,aAAA,GAAG5N,CAAC,CAACwD,QAAQ,cAAAoK,aAAA,uBAAVA,aAAA,CAAY1M,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA;EACA,MAAM2C,SAASA,CAAA,EAAG;IACd,IAAIiB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACrH,MAAM,CAACC,cAAc,CAAC6E,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAIqC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK9D,SAAS,EAAE;MACxE,IAAIlD,GAAG,GAAG,yBAAyB,GAAGgH,WAAW,GAAG,gCAAgC,GAAG,IAAI,CAAC/G,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI;MACzJ,IAAI,CAACb,QAAQ,CAAC;QAAEC,iBAAiB,EAAEqH,WAAW;QAAE3E,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAEnE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpG,MAAMxC,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAwM,qBAAA,EAAAC,eAAA;UAClC,IAAItM,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAkM,qBAAA,GAAExM,OAAO,CAACO,WAAW,cAAAiM,qBAAA,uBAAnBA,qBAAA,CAAqBhM,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAgO,eAAA,GAAEzM,OAAO,CAACW,KAAK,cAAA8L,eAAA,uBAAbA,eAAA,CAAehO,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAA+N,aAAA,EAAAC,aAAA;QACVhL,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAwK,aAAA,GAAA/N,CAAC,CAACwD,QAAQ,cAAAuK,aAAA,uBAAVA,aAAA,CAAY7M,IAAI,MAAKuC,SAAS,IAAAuK,aAAA,GAAGhO,CAAC,CAACwD,QAAQ,cAAAwK,aAAA,uBAAVA,aAAA,CAAY9M,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM,IAAI,IAAI,CAACnD,KAAK,CAACwE,IAAI,KAAKzG,SAAS,EAAE;MACtC,IAAI,CAAC0B,QAAQ,CAAC;QAAE2C,OAAO,EAAE,IAAI;QAAEgC,MAAM,EAAE,EAAE;QAAEnE,gBAAgB,EAAE;MAAK,CAAC,CAAC;MACpE,MAAMxC,UAAU,CAAC,KAAK,4BAAAsF,MAAA,CAA4B,IAAI,CAAC/C,KAAK,CAACN,iBAAiB,oCAAAqD,MAAA,CAAiC,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACC,IAAI,YAAA0C,MAAA,CAAS,IAAI,CAAC/C,KAAK,CAACI,UAAU,CAACE,IAAI,CAAE,CAAC,CAC1KC,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAA4M,qBAAA,EAAAC,eAAA;UAClC,IAAI1M,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAsM,qBAAA,GAAE5M,OAAO,CAACO,WAAW,cAAAqM,qBAAA,uBAAnBA,qBAAA,CAAqBpM,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAoO,eAAA,GAAE7M,OAAO,CAACW,KAAK,cAAAkM,eAAA,uBAAbA,eAAA,CAAepO,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAE;YAAE8B,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAK;YAAE7B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAI;YAAEC,IAAI,EAAE,IAAI,CAACN,KAAK,CAACI,UAAU,CAACE,IAAI;YAAE6B,SAAS,EAAE3B,GAAG,CAACE,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACjC,KAAK,CAACI,UAAU,CAACC;UAAM,CAAC;UACpL+B,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAmO,aAAA,EAAAC,aAAA;QACVpL,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAA4K,aAAA,GAAAnO,CAAC,CAACwD,QAAQ,cAAA2K,aAAA,uBAAVA,aAAA,CAAYjN,IAAI,MAAKuC,SAAS,IAAA2K,aAAA,GAAGpO,CAAC,CAACwD,QAAQ,cAAA4K,aAAA,uBAAVA,aAAA,CAAYlN,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV;EACJ;EACA6C,MAAMA,CAAC6H,KAAK,EAAE;IACV,IAAI,CAACpO,QAAQ,CAAC;MAAE2C,OAAO,EAAE;IAAK,CAAC,CAAC;IAEhC,IAAI,IAAI,CAACmD,eAAe,EAAE;MACtBuI,YAAY,CAAC,IAAI,CAACvI,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGiF,UAAU,CAAC,YAAY;MAC1C,IAAIzK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACsE,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACN,iBAAiB,IAAI,IAAI,CAACM,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG0N,KAAK,CAACxN,IAAI,GAAG,QAAQ,GAAGwN,KAAK,CAACvN,IAAI;MACpP,MAAM7C,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAkN,qBAAA,EAAAC,eAAA;UAClC,IAAIhN,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAA4M,qBAAA,GAAElN,OAAO,CAACO,WAAW,cAAA2M,qBAAA,uBAAnBA,qBAAA,CAAqB1M,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAA0O,eAAA,GAAEnN,OAAO,CAACW,KAAK,cAAAwM,eAAA,uBAAbA,eAAA,CAAe1O,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAEyN,KAAK;UACjBzL,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAyO,aAAA,EAAAC,aAAA;QACV1L,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAkL,aAAA,GAAAzO,CAAC,CAACwD,QAAQ,cAAAiL,aAAA,uBAAVA,aAAA,CAAYvN,IAAI,MAAKuC,SAAS,IAAAiL,aAAA,GAAG1O,CAAC,CAACwD,QAAQ,cAAAkL,aAAA,uBAAVA,aAAA,CAAYxN,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEgL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EACAnI,MAAMA,CAAC4H,KAAK,EAAE;IACV,IAAI,CAACpO,QAAQ,CAAC;MAAE2C,OAAO,EAAE;IAAK,CAAC,CAAC;IAChC,IAAIiM,KAAK,GAAGR,KAAK,CAAClJ,SAAS,KAAK,UAAU,GAAG,iCAAiC,GAAGkJ,KAAK,CAAClJ,SAAS;IAChG,IAAI,IAAI,CAACY,eAAe,EAAE;MACtBuI,YAAY,CAAC,IAAI,CAACvI,eAAe,CAAC;IACtC;IACA,IAAI,CAACA,eAAe,GAAGiF,UAAU,CAAC,YAAY;MAC1C,IAAIzK,GAAG,GAAG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACsE,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACN,iBAAiB,IAAI,IAAI,CAACM,KAAK,CAACC,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAACE,IAAI,GAAG,EAAE,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACI,UAAU,CAACE,IAAI,GAAG,SAAS,GAAG+N,KAAK,GAAG,WAAW,IAAIR,KAAK,CAACjJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;MACjW,MAAMnH,UAAU,CAAC,KAAK,EAAEsC,GAAG,CAAC,CACvBQ,IAAI,CAAEC,GAAG,IAAK;QACX,IAAIC,SAAS,GAAG,EAAE;QAClBD,GAAG,CAACE,IAAI,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;UAAA,IAAAyN,qBAAA,EAAAC,eAAA;UAClC,IAAIvN,CAAC,GAAG;YACJlC,EAAE,EAAE+B,OAAO,CAAC/B,EAAE;YACdmC,MAAM,EAAEJ,OAAO,CAACI,MAAM;YACtBC,IAAI,EAAEL,OAAO,CAACK,IAAI;YAClBC,SAAS,GAAAmN,qBAAA,GAAEzN,OAAO,CAACO,WAAW,cAAAkN,qBAAA,uBAAnBA,qBAAA,CAAqBjN,UAAU,CAACtC,SAAS;YACpDqC,WAAW,EAAEP,OAAO,CAACO,WAAW;YAChCE,YAAY,EAAET,OAAO,CAACS,YAAY;YAClCC,cAAc,EAAEV,OAAO,CAACU,cAAc;YACtCpC,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;YAClCqC,KAAK,EAAEX,OAAO,CAACW,KAAK;YACpBC,OAAO,EAAEZ,OAAO,CAACY,OAAO;YACxBnC,MAAM,GAAAiP,eAAA,GAAE1N,OAAO,CAACW,KAAK,cAAA+M,eAAA,uBAAbA,eAAA,CAAejP,MAAM;YAC7BoC,UAAU,EAAEb,OAAO,CAACa,UAAU;YAC9BC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACe;UACxB,CAAC;UACDnB,SAAS,CAACoB,IAAI,CAACb,CAAC,CAAC;QACrB,CAAC,CAAC;QACF,IAAI,CAACvB,QAAQ,CAAC;UACVqC,OAAO,EAAErB,SAAS;UAClBsB,QAAQ,EAAEtB,SAAS;UACnBuB,YAAY,EAAExB,GAAG,CAACE,IAAI,CAACuB,UAAU;UACjC7B,UAAU,EAAA0J,aAAA,CAAAA,aAAA,KAAO,IAAI,CAAC9J,KAAK,CAACI,UAAU;YAAEuE,SAAS,EAAEkJ,KAAK,CAAClJ,SAAS;YAAEC,SAAS,EAAEiJ,KAAK,CAACjJ;UAAS,EAAE;UAChGxC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC,CACDC,KAAK,CAAE7C,CAAC,IAAK;QAAA,IAAAgP,aAAA,EAAAC,aAAA;QACVjM,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;QACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;UACZC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,sFAAAC,MAAA,CAAmF,EAAAyL,aAAA,GAAAhP,CAAC,CAACwD,QAAQ,cAAAwL,aAAA,uBAAVA,aAAA,CAAY9N,IAAI,MAAKuC,SAAS,IAAAwL,aAAA,GAAGjP,CAAC,CAACwD,QAAQ,cAAAyL,aAAA,uBAAVA,aAAA,CAAY/N,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;UACxJC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,EAAEgL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAClC;EAEAlI,QAAQA,CAAC2H,KAAK,EAAE;IACZA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,IAAI,CAACpO,QAAQ,CAAC;MAAEW,UAAU,EAAEyN;IAAM,CAAC,EAAE,IAAI,CAACa,YAAY,CAAC;EAC3D;EACApI,gBAAgBA,CAAC9G,CAAC,EAAE;IAChB,IAAI,CAACC,QAAQ,CAAC;MAAE+D,iBAAiB,EAAEhE,CAAC,CAACG;IAAM,CAAC,CAAC;EACjD;EACA;EACA8G,mBAAmBA,CAACvC,MAAM,EAAE;IACxB,IAAI,CAACzE,QAAQ,CAAC;MACVyE,MAAM;MACNK,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA;EACA,MAAMgC,YAAYA,CAAA,EAAG;IACjB,IAAIzE,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAAC8B,OAAO,CAACqH,MAAM,CAClCC,GAAG,IAAKA,GAAG,CAACtK,EAAE,KAAK,IAAI,CAACkB,KAAK,CAACkE,MAAM,CAACpF,EAC1C,CAAC;IACD,IAAI,CAACW,QAAQ,CAAC;MACVqC,OAAO;MACPyC,kBAAkB,EAAE,KAAK;MACzBL,MAAM,EAAE,IAAI,CAACrF;IACjB,CAAC,CAAC;IACF,IAAIkB,GAAG,GAAG,4BAA4B,GAAG,IAAI,CAACC,KAAK,CAACkE,MAAM,CAACpF,EAAE;IAC7D,MAAMrB,UAAU,CAAC,QAAQ,EAAEsC,GAAG,CAAC,CAC1BQ,IAAI,CAACC,GAAG,IAAI;MACTgC,OAAO,CAACC,GAAG,CAACjC,GAAG,CAACE,IAAI,CAAC;MACrB,IAAI,CAACgC,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,kCAAkC;QAC1CK,IAAI,EAAE;MACV,CAAC,CAAC;MACFvD,MAAM,CAAC6K,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,CAACrI,KAAK,CAAE7C,CAAC,IAAK;MAAA,IAAAmP,aAAA,EAAAC,aAAA;MACZpM,OAAO,CAACC,GAAG,CAACjD,CAAC,CAAC;MACd,IAAI,CAACkD,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,wEAAAC,MAAA,CAAqE,EAAA4L,aAAA,GAAAnP,CAAC,CAACwD,QAAQ,cAAA2L,aAAA,uBAAVA,aAAA,CAAYjO,IAAI,MAAKuC,SAAS,IAAA2L,aAAA,GAAGpP,CAAC,CAACwD,QAAQ,cAAA4L,aAAA,uBAAVA,aAAA,CAAYlO,IAAI,GAAGlB,CAAC,CAAC0D,OAAO,CAAE;QAC1IC,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EACAqD,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC/G,QAAQ,CAAC;MACV8E,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACAoC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC3G,KAAK,CAACN,iBAAiB,KAAK,IAAI,EAAE;MACvC,IAAI,CAACD,QAAQ,CAAC;QACVgE,YAAY,EAAE;MAClB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACf,KAAK,CAACC,IAAI,CAAC;QACZC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,iEAAiE;QACzEK,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAyD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnH,QAAQ,CAAC;MACVqE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA+C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpH,QAAQ,CAAC;MACVqE,aAAa,EAAE;IACnB,CAAC,CAAC;EACN;EACA+K,MAAMA,CAAA,EAAG;IACL;IACA,MAAMC,kBAAkB,gBACpBrQ,OAAA,CAACnB,KAAK,CAACyR,QAAQ;MAAAC,QAAA,eACXvQ,OAAA;QAAKwQ,SAAS,EAAC,UAAU;QAAAD,QAAA,eACrBvQ,OAAA;UAAKwQ,SAAS,EAAC,QAAQ;UAAAD,QAAA,eACnBvQ,OAAA;YAAKwQ,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBACvCvQ,OAAA,CAACf,MAAM;cACHuR,SAAS,EAAC,0BAA0B;cACpCC,OAAO,EAAE,IAAI,CAACxJ,kBAAmB;cAAAsJ,QAAA,GAEhC,GAAG,EACHxR,QAAQ,CAAC2R,MAAM,EAAE,GAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACT9Q,OAAA,CAACP,KAAK;cACFuC,SAAS,EAAE,IAAI,CAACT,KAAK,CAACkE,MAAO;cAC7BZ,QAAQ,EAAE,IAAI,CAACtD,KAAK,CAACsD,QAAS;cAC9BW,GAAG,EAAE,IAAI,CAACjE,KAAK,CAACiE,GAAI;cACpBuL,GAAG,EAAE;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD,MAAME,mBAAmB,gBACrBhR,OAAA,CAACnB,KAAK,CAACyR,QAAQ;MAAAC,QAAA,eACXvQ,OAAA;QAAKwQ,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC1DvQ,OAAA,CAACf,MAAM;UAACuR,SAAS,EAAC,0BAA0B;UAACC,OAAO,EAAE,IAAI,CAACvI,iBAAkB;UAAAqI,QAAA,GAAE,GAAC,EAACxR,QAAQ,CAAC2R,MAAM,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACnB;IACD;IACA,MAAMG,mBAAmB,gBACrBjR,OAAA,CAACnB,KAAK,CAACyR,QAAQ;MAAAC,QAAA,eACXvQ,OAAA,CAACf,MAAM;QAACuR,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAACtJ,UAAW;QAAAoJ,QAAA,GACjE,GAAG,EACHxR,QAAQ,CAAC2R,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD;IACA,MAAMI,mBAAmB,gBACrBlR,OAAA,CAACnB,KAAK,CAACyR,QAAQ;MAAAC,QAAA,gBACXvQ,OAAA,CAACf,MAAM;QAACuR,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC7I,UAAW;QAAA2I,QAAA,GACjE,GAAG,eACJvQ,OAAA;UAAGwQ,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAAC/R,QAAQ,CAACoS,KAAK,EAAE,GAAG;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACT9Q,OAAA,CAACf,MAAM;QAACuR,SAAS,EAAC,0BAA0B;QAACC,OAAO,EAAE,IAAI,CAAC9I,kBAAmB;QAAA4I,QAAA,GACzE,GAAG,EACHxR,QAAQ,CAAC2R,MAAM,EAAE,GAAG;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,MAAMM,wBAAwB,gBAC1BpR,OAAA,CAACnB,KAAK,CAACyR,QAAQ;MAAAC,QAAA,gBACXvQ,OAAA,CAACf,MAAM;QACHgM,KAAK,EAAC,IAAI;QACVoG,IAAI,EAAC,aAAa;QAClBb,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE,IAAI,CAAC1I;MAAuB;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACF9Q,OAAA,CAACf,MAAM;QAACuR,SAAS,EAAC,eAAe;QAACC,OAAO,EAAE,IAAI,CAAC3I,YAAa;QAAAyI,QAAA,GACxD,GAAG,EACHxR,QAAQ,CAACuS,EAAE,EAAE,GAAG;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACnB;IACD,IAAIS,MAAM,GAAG,EAAE;IACf,IAAI,IAAI,CAAChQ,KAAK,CAACwE,IAAI,KAAKxG,YAAY,EAAE;MAClCgS,MAAM,GAAG,CACL;QAAEC,aAAa,EAAE,UAAU;QAAEC,WAAW,EAAE;UAAEC,KAAK,EAAE;QAAM;MAAE,CAAC,EAC5D;QACI9B,KAAK,EAAE,QAAQ;QACf+B,MAAM,EAAE5S,QAAQ,CAAC6S,IAAI;QACrBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,WAAW;QAClB+B,MAAM,EAAE5S,QAAQ,CAACiT,SAAS;QAC1BH,IAAI,EAAE,WAAW;QACjBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACkT,OAAO;QACxBJ,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACmT,KAAK;QACtBL,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,+BAA+B;QACtC+B,MAAM,EAAE5S,QAAQ,CAACoT,YAAY;QAC7BN,IAAI,EAAE,SAAS;QACfE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,gCAAgC;QACvC+B,MAAM,EAAE5S,QAAQ,CAACqT,SAAS;QAC1BP,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACsT,SAAS;QAC1BR,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,SAAS;QAChB+B,MAAM,EAAE,UAAU;QAClBE,IAAI,EAAE,SAAS;QACfC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,YAAY;QACnB+B,MAAM,EAAE5S,QAAQ,CAACuT,IAAI;QACrBT,IAAI,EAAE,YAAY;QAClBE,UAAU,EAAE;MAChB,CAAC,CACJ;IACL,CAAC,MAAM;MACHR,MAAM,GAAG,CACL;QACI3B,KAAK,EAAE,QAAQ;QACf+B,MAAM,EAAE5S,QAAQ,CAAC6S,IAAI;QACrBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,WAAW;QAClB+B,MAAM,EAAE5S,QAAQ,CAACiT,SAAS;QAC1BH,IAAI,EAAE,WAAW;QACjBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACkT,OAAO;QACxBJ,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACmT,KAAK;QACtBL,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,cAAc;QACrB+B,MAAM,EAAE5S,QAAQ,CAACsT,SAAS;QAC1BR,IAAI,EAAE,UAAU;QAChBE,UAAU,EAAE;MAChB,CAAC,EACD;QACInC,KAAK,EAAE,YAAY;QACnB+B,MAAM,EAAE5S,QAAQ,CAACuT,IAAI;QACrBT,IAAI,EAAE,YAAY;QAClBE,UAAU,EAAE;MAChB,CAAC,CACJ;IACL;IACA,IAAIQ,YAAY,GAAG,EAAE;IACrB,IAAI,IAAI,CAAChR,KAAK,CAACwE,IAAI,KAAKxG,YAAY,EAAE;MAClCgT,YAAY,GAAG,CACX;QAAEhM,IAAI,EAAExH,QAAQ,CAACyT,OAAO;QAAEnB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAAC1L;MAAe,CAAC,EAC3F;QAAER,IAAI,EAAExH,QAAQ,CAAC2T,SAAS;QAAErB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAAC/K;MAAS,CAAC,EACxF;QAAEnB,IAAI,EAAExH,QAAQ,CAAC4T,kBAAkB;QAAEtB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAACvL,UAAU;QAAErG,MAAM,EAAE,QAAQ;QAAE+R,OAAO,EAAE;MAAU,CAAC,EAC/I;QAAErM,IAAI,EAAExH,QAAQ,CAAC8T,QAAQ;QAAExB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAACxK;MAAc,CAAC,EAC9F;QAAE1B,IAAI,EAAExH,QAAQ,CAAC+T,OAAO;QAAEzB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAACzK;MAAoB,CAAC,CACrG;IACL,CAAC,MAAM;MACHuK,YAAY,GAAG,CACX;QAAEhM,IAAI,EAAExH,QAAQ,CAACyT,OAAO;QAAEnB,IAAI,eAAErR,OAAA;UAAGwQ,SAAS,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAE2B,OAAO,EAAE,IAAI,CAAC1L;MAAe,CAAC,CAC9F;IACL;IAEA,IAAIgM,WAAW,GAAG,uCAAuC;IACzD,IAAI,IAAI,CAACxR,KAAK,CAACoE,MAAM,KAAK,EAAE,EAAE;MAC1BoN,WAAW,GAAG,gCAAgC;IAClD,CAAC,MAAM;MACHA,WAAW,GAAG,uCAAuC;IACzD;IACA,oBACI/S,OAAA;MAAKwQ,SAAS,EAAC,mCAAmC;MAAAD,QAAA,gBAE9CvQ,OAAA,CAACR,KAAK;QAACwT,GAAG,EAAG1H,EAAE,IAAK,IAAI,CAACrH,KAAK,GAAGqH;MAAG;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvC9Q,OAAA,CAACJ,GAAG;QAAA+Q,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACP9Q,OAAA;QAAKwQ,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACnCvQ,OAAA;UAAAuQ,QAAA,EAAKxR,QAAQ,CAACkU;QAAuB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EACL,IAAI,CAACvP,KAAK,CAACN,iBAAiB,KAAK,IAAI,IAAI,IAAI,CAACM,KAAK,CAACwE,IAAI,KAAKzG,SAAS,iBACnEU,OAAA;QAAKwQ,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACtCvQ,OAAA;UAAIwQ,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACtEvQ,OAAA;YAAIwQ,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACjEvQ,OAAA;cAAKwQ,SAAS,EAAC,kDAAkD;cAAAD,QAAA,gBAC7DvQ,OAAA;gBAAIwQ,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAACvQ,OAAA;kBAAGwQ,SAAS,EAAC,iBAAiB;kBAAClD,KAAK,EAAE;oBAAE,UAAU,EAAE;kBAAO;gBAAE;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAAC/R,QAAQ,CAACmU,SAAS,EAAC,GAAC;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5H9Q,OAAA,CAACb,QAAQ;gBAACqR,SAAS,EAAC,QAAQ;gBAACtP,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,iBAAkB;gBAACkS,OAAO,EAAE,IAAI,CAACtM,SAAU;gBAACuM,QAAQ,EAAE,IAAI,CAACtS,iBAAkB;gBAACuS,WAAW,EAAC,MAAM;gBAACC,WAAW,EAAC,qBAAqB;gBAAC5I,MAAM;gBAAC6I,QAAQ,EAAC;cAAM;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEV9Q,OAAA;QAAKwQ,SAAS,EAAC,MAAM;QAAAD,QAAA,eAEjBvQ,OAAA,CAACH,eAAe;UACZmT,GAAG,EAAG1H,EAAE,IAAK,IAAI,CAACkI,EAAE,GAAGlI,EAAG;UAC1BpK,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC8B,OAAQ;UAC1BM,OAAO,EAAE,IAAI,CAACpC,KAAK,CAACoC,OAAQ;UAC5B4N,MAAM,EAAEA,MAAO;UACfkC,OAAO,EAAC,IAAI;UACZC,IAAI;UACJC,aAAa,EAAC,KAAK;UACnBC,SAAS;UACTrM,MAAM,EAAE,IAAI,CAACA,MAAO;UACpB9D,KAAK,EAAE,IAAI,CAAClC,KAAK,CAACI,UAAU,CAAC8B,KAAM;UACnCF,YAAY,EAAE,IAAI,CAAChC,KAAK,CAACgC,YAAa;UACtC3B,IAAI,EAAE,IAAI,CAACL,KAAK,CAACI,UAAU,CAACC,IAAK;UACjCiS,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCC,aAAa,EAAEvB,YAAa;UAC5BwB,UAAU,EAAE,IAAK;UACjBC,mBAAmB,EAAE,IAAK;UAC1BxC,aAAa,EAAC,UAAU;UACxByC,aAAa,EAAE,IAAK;UACpBC,YAAY,EAAE,IAAI,CAACnN,cAAe;UAClCoN,SAAS,EAAE,IAAI,CAAC5S,KAAK,CAACwD,iBAAkB;UACxCqP,iBAAiB,EAAGrT,CAAC,IAAK,IAAI,CAAC8G,gBAAgB,CAAC9G,CAAC,CAAE;UACnDsT,gBAAgB,EAAE,IAAI,CAAC9S,KAAK,CAACwE,IAAI,KAAKxG,YAAY,GAAG,IAAI,GAAG,KAAM;UAClE+U,kBAAkB,EAAE,IAAI,CAAChN,kBAAmB;UAC5CiN,iBAAiB,EAAExV,QAAQ,CAACuI,kBAAmB;UAC/CkN,oBAAoB,EAAE,CAAC,IAAI,CAACjT,KAAK,CAACwD,iBAAiB,IAAI,CAAC,IAAI,CAACxD,KAAK,CAACwD,iBAAiB,CAACwG,MAAO;UAC5F/D,MAAM,EAAE,IAAI,CAACA,MAAO;UACpBtB,SAAS,EAAE,IAAI,CAAC3E,KAAK,CAACI,UAAU,CAACuE,SAAU;UAC3CC,SAAS,EAAE,IAAI,CAAC5E,KAAK,CAACI,UAAU,CAACwE,SAAU;UAC3CsO,eAAe,EAAE,IAAK;UACtBC,iBAAiB,EAAE,IAAI,CAACvM,UAAW;UACnCwM,gBAAgB,eAAE3U,OAAA;YAAUwQ,SAAS,EAAC,MAAM;YAACjK,IAAI,EAAC;UAAgB;YAAAoK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAE;UAC/E8D,OAAO,EAAC,QAAQ;UAChBnN,QAAQ,EAAE,IAAI,CAACA,QAAS;UACxBrB,OAAO,EAAE,IAAI,CAAC7E,KAAK,CAACI,UAAU,CAACyE,OAAQ;UACvCyO,gBAAgB,EAAE,KAAM;UACxBC,SAAS,EAAC;QAAU;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9Q,OAAA,CAACd,MAAM;QACH6V,OAAO,EAAE,IAAI,CAACxT,KAAK,CAAC0D,aAAc;QAClC0M,MAAM,EAAE5S,QAAQ,CAACiW,MAAO;QACxBC,KAAK;QACLzE,SAAS,EAAC,kBAAkB;QAC5B0E,MAAM,EAAE7E,kBAAmB;QAC3B8E,MAAM,EAAE,IAAI,CAAClO,kBAAmB;QAChCmO,SAAS,EAAE,KAAM;QAAA7E,QAAA,eAEjBvQ,OAAA,CAACL,mBAAmB;UAChB8F,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACsD,QAAS;UAC5BxB,OAAO,EAAE,IAAI,CAAC9B,KAAK,CAACkE,MAAO;UAC3BzD,SAAS,EAAE,IAAI,CAACT,KAAK,CAACkE,MAAO;UAC7B4P,MAAM,EAAE;QAAK;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAET9Q,OAAA,CAACd,MAAM;QACH6V,OAAO,EAAE,IAAI,CAACxT,KAAK,CAAC4D,aAAc;QAClCwM,MAAM,EAAE,IAAI,CAACpQ,KAAK,CAACiE,GAAI;QACvByP,KAAK;QACLzE,SAAS,EAAC,kBAAkB;QAC5B0E,MAAM,EAAEjE,mBAAoB;QAC5BkE,MAAM,EAAE,IAAI,CAAChO,UAAW;QAAAoJ,QAAA,eAExBvQ,OAAA,CAACN,kBAAkB;UAAC+F,MAAM,EAAE,IAAI,CAAClE,KAAK,CAACkE,MAAO;UAACH,KAAK,EAAE,IAAI,CAACA,KAAM;UAACC,OAAO,EAAE,IAAI,CAAChE,KAAK,CAACgE,OAAQ;UAAChG,YAAY,EAAE;QAAK;UAAAoR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eACT9Q,OAAA,CAACd,MAAM;QACH6V,OAAO,EAAE,IAAI,CAACxT,KAAK,CAAC6D,aAAc;QAClCuM,MAAM,EAAE5S,QAAQ,CAAC2T,SAAU;QAC3BuC,KAAK;QACLzE,SAAS,EAAC,kBAAkB;QAC5B0E,MAAM,EAAEhE,mBAAoB;QAC5BiE,MAAM,EAAE,IAAI,CAACxN,kBAAmB;QAAA4I,QAAA,eAEhCvQ,OAAA;UAAKwQ,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAChBvQ,OAAA;YAAKwQ,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAACvQ,OAAA;cAAAuQ,QAAA,EAAOxR,QAAQ,CAACuW;YAAe;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrE9Q,OAAA;YAAKwQ,SAAS,EAAC,QAAQ;YAAAD,QAAA,eACnBvQ,OAAA,CAACX,SAAS;cAAC6B,KAAK,EAAE,IAAI,CAACK,KAAK,CAACqE,YAAa;cAACwN,QAAQ,EAAGrS,CAAC,IAAK,IAAI,CAACC,QAAQ,CAAC;gBAAE4E,YAAY,EAAE7E,CAAC,CAACwU,MAAM,CAACrU;cAAM,CAAC;YAAE;cAAAyP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,eACN9Q,OAAA;YAAKwQ,SAAS,EAAC,QAAQ;YAAAD,QAAA,eAACvQ,OAAA;cAAMwQ,SAAS,EAAC,aAAa;cAAAD,QAAA,GAAC,IAAE,EAACxR,QAAQ,CAACyW,WAAW;YAAA;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAET9Q,OAAA,CAACd,MAAM;QACH6V,OAAO,EAAE,IAAI,CAACxT,KAAK,CAACuE,kBAAmB;QACvC6L,MAAM,EAAE5S,QAAQ,CAAC0W,QAAS;QAC1BR,KAAK;QACLC,MAAM,EAAE9D,wBAAyB;QACjC+D,MAAM,EAAE,IAAI,CAACpN,sBAAuB;QAAAwI,QAAA,eAEpCvQ,OAAA;UAAKwQ,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCvQ,OAAA;YACIwQ,SAAS,EAAC,mCAAmC;YAC7ClD,KAAK,EAAE;cAAEoI,QAAQ,EAAE;YAAO;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACD,IAAI,CAACvP,KAAK,CAACkE,MAAM,iBACdzF,OAAA;YAAAuQ,QAAA,GACKxR,QAAQ,CAAC4W,YAAY,EAAC,GAAC,eAAA3V,OAAA;cAAAuQ,QAAA,GAAI,IAAI,CAAChP,KAAK,CAACkE,MAAM,CAACjD,MAAM,EAAC,GAAC;YAAA;cAAAmO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACT9Q,OAAA,CAACd,MAAM;QAAC6V,OAAO,EAAE,IAAI,CAACxT,KAAK,CAACyD,YAAa;QAAC2M,MAAM,EAAE5S,QAAQ,CAAC6W,iBAAkB;QAACX,KAAK;QAACzE,SAAS,EAAC,kBAAkB;QAAC2E,MAAM,EAAE,IAAI,CAACjN,iBAAkB;QAACgN,MAAM,EAAElE,mBAAoB;QAAAT,QAAA,GACxK,IAAI,CAAChP,KAAK,CAAC2D,SAAS,iBACjBlF,OAAA,CAACZ,UAAU;UAACyW,KAAK,EAAC,oBAAoB;UAACC,OAAO,EAAC,yBAAyB;UAACP,MAAM,EAAC;QAAS;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAG/F,IAAI,CAACvP,KAAK,CAACwE,IAAI,KAAKzG,SAAS,iBAC1BU,OAAA;UAAKwQ,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC3DvQ,OAAA;YAAIwQ,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvQ,OAAA;cAAGwQ,SAAS,EAAC,iBAAiB;cAAClD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/R,QAAQ,CAACmU,SAAS;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChH9Q,OAAA;YAAA2Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9Q,OAAA,CAACb,QAAQ;YAACqR,SAAS,EAAC,QAAQ;YAACtP,KAAK,EAAE,IAAI,CAACK,KAAK,CAACN,iBAAkB;YAACkS,OAAO,EAAE,IAAI,CAACtM,SAAU;YAACuM,QAAQ,EAAE,IAAI,CAACtS,iBAAkB;YAACuS,WAAW,EAAC,MAAM;YAACC,WAAW,EAAC,qBAAqB;YAAC5I,MAAM;YAAC6I,QAAQ,EAAC;UAAM;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC,eACT9Q,OAAA,CAACF,OAAO;QAACiV,OAAO,EAAE,IAAI,CAACxT,KAAK,CAAC8D,aAAc;QAAC0Q,QAAQ,EAAC,MAAM;QAACZ,MAAM,EAAE,IAAI,CAAC/M,WAAY;QAAAmI,QAAA,gBACjFvQ,OAAA;UAAKK,EAAE,EAAC,cAAc;UAACmQ,SAAS,EAAC,oBAAoB;UAAC,eAAY,UAAU;UAAC,eAAY,sBAAsB;UAAC,iBAAc,OAAO;UAAC,iBAAc,qBAAqB;UAAAD,QAAA,gBACrKvQ,OAAA;YAAGwQ,SAAS,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C9Q,OAAA;YAAIwQ,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvQ,OAAA;cAAGwQ,SAAS,EAAC,mBAAmB;cAAClD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/R,QAAQ,CAACiX,MAAM;UAAA;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9Q,OAAA,CAACf,MAAM;YAACoB,EAAE,EAAC,iBAAiB;YAACmQ,SAAS,EAAEuC,WAAY;YAACtC,OAAO,EAAE,IAAI,CAACrJ,KAAM;YAAAmJ,QAAA,gBAACvQ,OAAA;cAAGwQ,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9Q,OAAA;cAAAuQ,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxI,CAAC,eACN9Q,OAAA;UAAKK,EAAE,EAAC,kBAAkB;UAACmQ,SAAS,EAAC,8BAA8B;UAAAD,QAAA,gBAC/DvQ,OAAA;YAAIwQ,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAACvQ,OAAA;cAAGwQ,SAAS,EAAC,mBAAmB;cAAClD,KAAK,EAAE;gBAAE,UAAU,EAAE;cAAO;YAAE;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAAC/R,QAAQ,CAACiX,MAAM;UAAA;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/G9Q,OAAA,CAACf,MAAM;YAACoB,EAAE,EAAC,kBAAkB;YAACmQ,SAAS,EAAEuC,WAAY;YAACtC,OAAO,EAAE,IAAI,CAACpJ,SAAU;YAAAkJ,QAAA,gBAACvQ,OAAA;cAAGwQ,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAAA9Q,OAAA;cAAAuQ,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACN9Q,OAAA;UAAA2Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9Q,OAAA,CAACb,QAAQ;UAACqR,SAAS,EAAC,OAAO;UAACtP,KAAK,EAAE,IAAI,CAACK,KAAK,CAACC,gBAAiB;UAAC2R,OAAO,EAAE,IAAI,CAACvM,QAAS;UAACwM,QAAQ,EAAE,IAAI,CAAC9M,SAAU;UAAC+M,WAAW,EAAC,MAAM;UAACC,WAAW,EAAC,qBAAqB;UAAC5I,MAAM;UAAC6I,QAAQ,EAAC;QAAM;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3L,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEd;AACJ;AAEA,eAAe7Q,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}