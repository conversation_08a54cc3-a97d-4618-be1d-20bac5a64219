{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport { throttleByAnimationFrame } from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { cloneElement } from '../_util/reactNode';\nvar BackTop = function BackTop(props) {\n  var _useMergedState = useMergedState(false, {\n      value: props.visible\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    visible = _useMergedState2[0],\n    setVisible = _useMergedState2[1];\n  var ref = /*#__PURE__*/React.createRef();\n  var scrollEvent = React.useRef();\n  var getDefaultTarget = function getDefaultTarget() {\n    return ref.current && ref.current.ownerDocument ? ref.current.ownerDocument : window;\n  };\n  var handleScroll = throttleByAnimationFrame(function (e) {\n    var visibilityHeight = props.visibilityHeight;\n    var scrollTop = getScroll(e.target, true);\n    setVisible(scrollTop > visibilityHeight);\n  });\n  var bindScrollEvent = function bindScrollEvent() {\n    var target = props.target;\n    var getTarget = target || getDefaultTarget;\n    var container = getTarget();\n    scrollEvent.current = addEventListener(container, 'scroll', function (e) {\n      handleScroll(e);\n    });\n    handleScroll({\n      target: container\n    });\n  };\n  React.useEffect(function () {\n    bindScrollEvent();\n    return function () {\n      if (scrollEvent.current) {\n        scrollEvent.current.remove();\n      }\n      handleScroll.cancel();\n    };\n  }, [props.target]);\n  var scrollToTop = function scrollToTop(e) {\n    var onClick = props.onClick,\n      target = props.target,\n      _props$duration = props.duration,\n      duration = _props$duration === void 0 ? 450 : _props$duration;\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration: duration\n    });\n    if (typeof onClick === 'function') {\n      onClick(e);\n    }\n  };\n  var renderChildren = function renderChildren(_ref) {\n    var prefixCls = _ref.prefixCls,\n      rootPrefixCls = _ref.rootPrefixCls;\n    var children = props.children;\n    var defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-icon\")\n    }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n    return /*#__PURE__*/React.createElement(CSSMotion, {\n      visible: visible,\n      motionName: \"\".concat(rootPrefixCls, \"-fade\")\n    }, function (_ref2) {\n      var motionClassName = _ref2.className;\n      return cloneElement(children || defaultElement, function (_ref3) {\n        var className = _ref3.className;\n        return {\n          className: classNames(motionClassName, className)\n        };\n      });\n    });\n  };\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var classString = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className); // fix https://fb.me/react-unknown-prop\n\n  var divProps = omit(props, ['prefixCls', 'className', 'children', 'visibilityHeight', 'target', 'visible']);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), renderChildren({\n    prefixCls: prefixCls,\n    rootPrefixCls: rootPrefixCls\n  }));\n};\nBackTop.defaultProps = {\n  visibilityHeight: 400\n};\nexport default /*#__PURE__*/React.memo(BackTop);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "CSSMotion", "addEventListener", "useMergedState", "classNames", "omit", "VerticalAlignTopOutlined", "throttleByAnimationFrame", "ConfigContext", "getScroll", "scrollTo", "cloneElement", "BackTop", "props", "_useMergedState", "value", "visible", "_useMergedState2", "setVisible", "ref", "createRef", "scrollEvent", "useRef", "getDefaultTarget", "current", "ownerDocument", "window", "handleScroll", "e", "visibilityHeight", "scrollTop", "target", "bindScrollEvent", "get<PERSON><PERSON><PERSON>", "container", "useEffect", "remove", "cancel", "scrollToTop", "onClick", "_props$duration", "duration", "getContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "prefixCls", "rootPrefixCls", "children", "defaultElement", "createElement", "className", "concat", "motionName", "_ref2", "motionClassName", "_ref3", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "_props$className", "classString", "divProps", "defaultProps", "memo"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/back-top/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport { throttleByAnimationFrame } from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { cloneElement } from '../_util/reactNode';\n\nvar BackTop = function BackTop(props) {\n  var _useMergedState = useMergedState(false, {\n    value: props.visible\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      visible = _useMergedState2[0],\n      setVisible = _useMergedState2[1];\n\n  var ref = /*#__PURE__*/React.createRef();\n  var scrollEvent = React.useRef();\n\n  var getDefaultTarget = function getDefaultTarget() {\n    return ref.current && ref.current.ownerDocument ? ref.current.ownerDocument : window;\n  };\n\n  var handleScroll = throttleByAnimationFrame(function (e) {\n    var visibilityHeight = props.visibilityHeight;\n    var scrollTop = getScroll(e.target, true);\n    setVisible(scrollTop > visibilityHeight);\n  });\n\n  var bindScrollEvent = function bindScrollEvent() {\n    var target = props.target;\n    var getTarget = target || getDefaultTarget;\n    var container = getTarget();\n    scrollEvent.current = addEventListener(container, 'scroll', function (e) {\n      handleScroll(e);\n    });\n    handleScroll({\n      target: container\n    });\n  };\n\n  React.useEffect(function () {\n    bindScrollEvent();\n    return function () {\n      if (scrollEvent.current) {\n        scrollEvent.current.remove();\n      }\n\n      handleScroll.cancel();\n    };\n  }, [props.target]);\n\n  var scrollToTop = function scrollToTop(e) {\n    var onClick = props.onClick,\n        target = props.target,\n        _props$duration = props.duration,\n        duration = _props$duration === void 0 ? 450 : _props$duration;\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration: duration\n    });\n\n    if (typeof onClick === 'function') {\n      onClick(e);\n    }\n  };\n\n  var renderChildren = function renderChildren(_ref) {\n    var prefixCls = _ref.prefixCls,\n        rootPrefixCls = _ref.rootPrefixCls;\n    var children = props.children;\n    var defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-content\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-icon\")\n    }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n    return /*#__PURE__*/React.createElement(CSSMotion, {\n      visible: visible,\n      motionName: \"\".concat(rootPrefixCls, \"-fade\")\n    }, function (_ref2) {\n      var motionClassName = _ref2.className;\n      return cloneElement(children || defaultElement, function (_ref3) {\n        var className = _ref3.className;\n        return {\n          className: classNames(motionClassName, className)\n        };\n      });\n    });\n  };\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls,\n      direction = _React$useContext.direction;\n\n  var customizePrefixCls = props.prefixCls,\n      _props$className = props.className,\n      className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  var rootPrefixCls = getPrefixCls();\n  var classString = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className); // fix https://fb.me/react-unknown-prop\n\n  var divProps = omit(props, ['prefixCls', 'className', 'children', 'visibilityHeight', 'target', 'visible']);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), renderChildren({\n    prefixCls: prefixCls,\n    rootPrefixCls: rootPrefixCls\n  }));\n};\n\nBackTop.defaultProps = {\n  visibilityHeight: 400\n};\nexport default /*#__PURE__*/React.memo(BackTop);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,wBAAwB,MAAM,qDAAqD;AAC1F,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,eAAe,GAAGX,cAAc,CAAC,KAAK,EAAE;MAC1CY,KAAK,EAAEF,KAAK,CAACG;IACf,CAAC,CAAC;IACEC,gBAAgB,GAAGlB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDE,OAAO,GAAGC,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIE,GAAG,GAAG,aAAanB,KAAK,CAACoB,SAAS,CAAC,CAAC;EACxC,IAAIC,WAAW,GAAGrB,KAAK,CAACsB,MAAM,CAAC,CAAC;EAEhC,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,OAAOJ,GAAG,CAACK,OAAO,IAAIL,GAAG,CAACK,OAAO,CAACC,aAAa,GAAGN,GAAG,CAACK,OAAO,CAACC,aAAa,GAAGC,MAAM;EACtF,CAAC;EAED,IAAIC,YAAY,GAAGpB,wBAAwB,CAAC,UAAUqB,CAAC,EAAE;IACvD,IAAIC,gBAAgB,GAAGhB,KAAK,CAACgB,gBAAgB;IAC7C,IAAIC,SAAS,GAAGrB,SAAS,CAACmB,CAAC,CAACG,MAAM,EAAE,IAAI,CAAC;IACzCb,UAAU,CAACY,SAAS,GAAGD,gBAAgB,CAAC;EAC1C,CAAC,CAAC;EAEF,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAID,MAAM,GAAGlB,KAAK,CAACkB,MAAM;IACzB,IAAIE,SAAS,GAAGF,MAAM,IAAIR,gBAAgB;IAC1C,IAAIW,SAAS,GAAGD,SAAS,CAAC,CAAC;IAC3BZ,WAAW,CAACG,OAAO,GAAGtB,gBAAgB,CAACgC,SAAS,EAAE,QAAQ,EAAE,UAAUN,CAAC,EAAE;MACvED,YAAY,CAACC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFD,YAAY,CAAC;MACXI,MAAM,EAAEG;IACV,CAAC,CAAC;EACJ,CAAC;EAEDlC,KAAK,CAACmC,SAAS,CAAC,YAAY;IAC1BH,eAAe,CAAC,CAAC;IACjB,OAAO,YAAY;MACjB,IAAIX,WAAW,CAACG,OAAO,EAAE;QACvBH,WAAW,CAACG,OAAO,CAACY,MAAM,CAAC,CAAC;MAC9B;MAEAT,YAAY,CAACU,MAAM,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACxB,KAAK,CAACkB,MAAM,CAAC,CAAC;EAElB,IAAIO,WAAW,GAAG,SAASA,WAAWA,CAACV,CAAC,EAAE;IACxC,IAAIW,OAAO,GAAG1B,KAAK,CAAC0B,OAAO;MACvBR,MAAM,GAAGlB,KAAK,CAACkB,MAAM;MACrBS,eAAe,GAAG3B,KAAK,CAAC4B,QAAQ;MAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,eAAe;IACjE9B,QAAQ,CAAC,CAAC,EAAE;MACVgC,YAAY,EAAEX,MAAM,IAAIR,gBAAgB;MACxCkB,QAAQ,EAAEA;IACZ,CAAC,CAAC;IAEF,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;MACjCA,OAAO,CAACX,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;IACjD,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;MAC1BC,aAAa,GAAGF,IAAI,CAACE,aAAa;IACtC,IAAIC,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ;IAC7B,IAAIC,cAAc,GAAG,aAAahD,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;MAC3DC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,UAAU;IAC5C,CAAC,EAAE,aAAa7C,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;MACzCC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,OAAO;IACzC,CAAC,EAAE,aAAa7C,KAAK,CAACiD,aAAa,CAAC3C,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC;IACrE,OAAO,aAAaN,KAAK,CAACiD,aAAa,CAAChD,SAAS,EAAE;MACjDe,OAAO,EAAEA,OAAO;MAChBoC,UAAU,EAAE,EAAE,CAACD,MAAM,CAACL,aAAa,EAAE,OAAO;IAC9C,CAAC,EAAE,UAAUO,KAAK,EAAE;MAClB,IAAIC,eAAe,GAAGD,KAAK,CAACH,SAAS;MACrC,OAAOvC,YAAY,CAACoC,QAAQ,IAAIC,cAAc,EAAE,UAAUO,KAAK,EAAE;QAC/D,IAAIL,SAAS,GAAGK,KAAK,CAACL,SAAS;QAC/B,OAAO;UACLA,SAAS,EAAE9C,UAAU,CAACkD,eAAe,EAAEJ,SAAS;QAClD,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,IAAIM,iBAAiB,GAAGxD,KAAK,CAACyD,UAAU,CAACjD,aAAa,CAAC;IACnDkD,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,kBAAkB,GAAG/C,KAAK,CAACgC,SAAS;IACpCgB,gBAAgB,GAAGhD,KAAK,CAACqC,SAAS;IAClCA,SAAS,GAAGW,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;EACnE,IAAIhB,SAAS,GAAGa,YAAY,CAAC,UAAU,EAAEE,kBAAkB,CAAC;EAC5D,IAAId,aAAa,GAAGY,YAAY,CAAC,CAAC;EAClC,IAAII,WAAW,GAAG1D,UAAU,CAACyC,SAAS,EAAE/C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqD,MAAM,CAACN,SAAS,EAAE,MAAM,CAAC,EAAEc,SAAS,KAAK,KAAK,CAAC,EAAET,SAAS,CAAC,CAAC,CAAC;;EAE5H,IAAIa,QAAQ,GAAG1D,IAAI,CAACQ,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;EAC3G,OAAO,aAAab,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAEpD,QAAQ,CAAC,CAAC,CAAC,EAAEkE,QAAQ,EAAE;IACpEb,SAAS,EAAEY,WAAW;IACtBvB,OAAO,EAAED,WAAW;IACpBnB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEwB,cAAc,CAAC;IACjBE,SAAS,EAAEA,SAAS;IACpBC,aAAa,EAAEA;EACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAEDlC,OAAO,CAACoD,YAAY,GAAG;EACrBnC,gBAAgB,EAAE;AACpB,CAAC;AACD,eAAe,aAAa7B,KAAK,CAACiE,IAAI,CAACrD,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}