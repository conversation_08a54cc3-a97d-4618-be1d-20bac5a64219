# 🏗️ Vercel Build Commands - E-procurement Frontend

## 📋 **Configurazione Build per Vercel Dashboard**

### **⚙️ Build Settings**

Quando configuri il progetto su Vercel Dashboard, usa questi settings:

#### **Framework Preset**
```
Create React App
```

#### **Build Command**
```bash
npm run build
```

#### **Output Directory**
```
build
```

#### **Install Command**
```bash
npm install
```

#### **Development Command**
```bash
npm start
```

### **🌐 Environment Variables da Configurare**

Nel Dashboard Vercel, vai su **Settings > Environment Variables** e aggiungi:

#### **🔑 Variabili Obbligatorie**

| Variable Name | Value | Environment |
|---------------|-------|-------------|
| `REACT_APP_API_URL` | `https://ep-backend-zeta.vercel.app/` | Production |
| `REACT_APP_WS_URL` | `wss://ep-backend-zeta.vercel.app/ws` | Production |
| `REACT_APP_ENVIRONMENT` | `production` | Production |
| `REACT_APP_VERSION` | `1.1.18` | Production |
| `REACT_APP_APP_NAME` | `E-Procurement Frontend` | Production |
| `REACT_APP_RECAPTCHA_SITE_KEY` | `6LekRnkeAAAAAJlPYR3cIG8NrFs5BNf-P3SQa4OU` | Production |
| `REACT_APP_BUILD_DATE` | `2025-07-01` | Production |
| `REACT_APP_BUILD_VERSION` | `1.1.18` | Production |
| `REACT_APP_ENABLE_NOTIFICATIONS` | `true` | Production |
| `REACT_APP_ENABLE_PDV_MANAGEMENT` | `true` | Production |
| `REACT_APP_ENABLE_ANALYTICS` | `true` | Production |
| `REACT_APP_ENABLE_DEBUG_MODE` | `false` | Production |
| `REACT_APP_DEBUG` | `false` | Production |
| `REACT_APP_ENABLE_CONSOLE_LOGS` | `false` | Production |
| `GENERATE_SOURCEMAP` | `false` | Production |
| `INLINE_RUNTIME_CHUNK` | `false` | Production |
| `VERCEL` | `1` | Production |
| `VERCEL_ENV` | `production` | Production |

### **📝 Istruzioni Passo-Passo**

#### **1. Importa Repository**
1. Vai su https://vercel.com/new
2. Seleziona "Import Git Repository"
3. Cerca `ep-frontend` o incolla URL: `https://github.com/Vincenzo-krsc/ep-frontend`
4. Clicca "Import"

#### **2. Configura Progetto**
1. **Project Name**: `ep-frontend`
2. **Framework Preset**: `Create React App`
3. **Root Directory**: `./` (default)
4. **Build Command**: `npm run build`
5. **Output Directory**: `build`
6. **Install Command**: `npm install`

#### **3. Aggiungi Environment Variables**
1. Clicca "Environment Variables"
2. Aggiungi tutte le variabili dalla tabella sopra
3. Per ogni variabile:
   - **Name**: Nome variabile
   - **Value**: Valore corrispondente
   - **Environment**: Seleziona "Production"

#### **4. Deploy**
1. Clicca "Deploy"
2. Attendi completamento build
3. Verifica che non ci siano errori

### **🔧 Build Optimization**

#### **Performance Settings**
```bash
# Disabilita source maps in produzione
GENERATE_SOURCEMAP=false

# Ottimizza runtime chunk
INLINE_RUNTIME_CHUNK=false

# Disabilita debug
REACT_APP_DEBUG=false
```

#### **Bundle Analysis**
Il build mostrerà le dimensioni dei file:
- **Main JS**: ~747 kB (gzipped)
- **Main CSS**: ~180 kB (gzipped)
- **Chunk JS**: ~68 kB (gzipped)

### **🚨 Troubleshooting Build**

#### **Errori Comuni**

**1. Build Failed - Missing Dependencies**
```bash
# Soluzione: Verifica package.json
npm install
npm run build
```

**2. Environment Variables Not Found**
```bash
# Soluzione: Controlla configurazione Dashboard
# Assicurati che tutte le variabili REACT_APP_ siano configurate
```

**3. CORS Errors**
```bash
# Soluzione: Verifica REACT_APP_API_URL
# Deve puntare a: https://ep-backend-zeta.vercel.app
```

**4. Build Warnings**
```bash
# Warning autoprefixer: Normale, non blocca il build
# Warning bundle size: Normale per applicazioni complesse
# Warning unused vars: Normale, non blocca il build
```

### **📊 Build Output Atteso**

```bash
Creating an optimized production build...
Compiled with warnings.

File sizes after gzip:
  747.63 kB  build/static/js/main.8af09b70.js
  180.02 kB  build/static/css/main.56dbd438.css
  68.2 kB    build/static/js/363.625d3b0f.chunk.js

The project was built assuming it is hosted at /.
The build folder is ready to be deployed.
```

### **✅ Verifica Deploy Completato**

Dopo il deploy, verifica:

1. **Sito accessibile**: URL Vercel funzionante
2. **API connectivity**: Backend raggiungibile
3. **Login funzionante**: Autenticazione OK
4. **Funzionalità PDV**: Gestione autonoma attiva
5. **Performance**: Tempi di caricamento ottimali

### **🔗 Link Post-Deploy**

- **Frontend URL**: `https://ep-frontend-[hash].vercel.app`
- **Dashboard**: `https://vercel.com/dashboard`
- **Analytics**: `https://vercel.com/[user]/ep-frontend/analytics`
- **Settings**: `https://vercel.com/[user]/ep-frontend/settings`

---

**Configurazione preparata per**: Vercel Deploy  
**Data**: 2025-07-01  
**Versione**: 1.1.18
