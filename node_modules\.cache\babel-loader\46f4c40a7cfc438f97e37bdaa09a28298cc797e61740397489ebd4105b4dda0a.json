{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\agenti\\\\dashboardAgenti\\\\dashboardAgente.jsx\";\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardAgente - dashboard \n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from 'primereact/messages';\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { BannerUtili } from \"../../../components/generalizzazioni/statistiche/bannerUtili\";\nimport { BannerProd } from \"../../../components/generalizzazioni/statistiche/bannerProd\";\nimport { BannerProd2 } from \"../../../components/generalizzazioni/statistiche/bannerProd2\";\nimport { ChartHorizontal } from \"../../../components/generalizzazioni/statistiche/chartHorizontal\";\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { agenteDettagliOrdine } from \"../../../components/route\";\nimport { TabellaTopFlop } from \"../../../components/generalizzazioni/statistiche/tabellaTopFlop\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass DashboardAgente extends Component {\n  constructor() {\n    super(...arguments);\n    this.returnPath = () => {\n      window.location.pathname = agenteDettagliOrdine;\n    };\n  }\n  componentDidMount() {\n    if (localStorage.getItem(\"datiComodo\") !== '0' && JSON.parse(localStorage.getItem(\"datiComodo\")).tel !== '-/-') {\n      this.msgs.show([{\n        severity: 'info',\n        detail: \"È presente un ordine in sospeso. Clicca quì per riprendere da dove eri rimasto.\",\n        sticky: true\n      }]);\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(BannerWelcome, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album\",\n        children: /*#__PURE__*/_jsxDEV(TabView, {\n          className: \"tabview-custom\",\n          children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n            style: {\n              padding: '0px'\n            },\n            header: \"Bacheca\",\n            leftIcon: \"pi pi-th-large mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container mt-3 pb-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card border-0\",\n                children: [/*#__PURE__*/_jsxDEV(Messages, {\n                  className: \"messageInfo zoomScale\",\n                  ref: el => this.msgs = el,\n                  onClick: this.returnPath\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 46,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-columns mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(BannerUtili, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 49,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(BannerProd, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(BannerProd2, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n            header: \"Trends\",\n            leftIcon: \"pi pi-chart-bar mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container-boxed mt-3 pb-4 px-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"widget-trends\",\n                children: [/*#__PURE__*/_jsxDEV(ChartHorizontal, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TabellaTopFlop, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default DashboardAgente;", "map": {"version": 3, "names": ["React", "Component", "Messages", "BannerWelcome", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BannerProd2", "ChartHorizontal", "TabView", "TabPanel", "agenteDettagliOrdine", "TabellaTop<PERSON>lop", "Nav", "Dashboard", "jsxDEV", "_jsxDEV", "DashboardAgente", "constructor", "arguments", "returnPath", "window", "location", "pathname", "componentDidMount", "localStorage", "getItem", "JSON", "parse", "tel", "msgs", "show", "severity", "detail", "sticky", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "header", "leftIcon", "ref", "el", "onClick"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/agenti/dashboardAgenti/dashboardAgente.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* DashboardAgente - dashboard \n*\n*/\nimport React, { Component } from \"react\";\nimport { Messages } from 'primereact/messages';\nimport { BannerWelcome } from \"../../../components/generalizzazioni/bannerWelcome\";\nimport { BannerUtili } from \"../../../components/generalizzazioni/statistiche/bannerUtili\";\nimport { BannerProd } from \"../../../components/generalizzazioni/statistiche/bannerProd\";\nimport { BannerProd2 } from \"../../../components/generalizzazioni/statistiche/bannerProd2\";\nimport { ChartHorizontal } from \"../../../components/generalizzazioni/statistiche/chartHorizontal\";\nimport { TabView, TabPanel } from 'primereact/tabview';\nimport { agenteDettagliOrdine } from \"../../../components/route\";\nimport { TabellaTopFlop } from \"../../../components/generalizzazioni/statistiche/tabellaTopFlop\";\nimport Nav from \"../../../components/navigation/Nav\";\nimport Dashboard from \"../../../components/navigation/dashboard\";\n\nclass DashboardAgente extends Component {\n    componentDidMount() {\n        if (localStorage.getItem(\"datiComodo\") !== '0' && JSON.parse(localStorage.getItem(\"datiComodo\")).tel !== '-/-') {\n            this.msgs.show([\n                { severity: 'info', detail: \"È presente un ordine in sospeso. Clicca quì per riprendere da dove eri rimasto.\", sticky: true }\n            ]);\n        }\n    }\n    returnPath = () => {\n        window.location.pathname = agenteDettagliOrdine\n    }\n    \n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <BannerWelcome />\n                <div className=\"album\">\n                    <TabView className=\"tabview-custom\">\n                        <TabPanel style={{ padding: '0px' }} header=\"Bacheca\" leftIcon=\"pi pi-th-large mr-2\">\n                            <div className=\"container mt-3 pb-0\">\n                                <div className=\"card border-0\">\n                                    <Messages className=\"messageInfo zoomScale\" ref={(el) => this.msgs = el} onClick={this.returnPath} />\n                                    <Dashboard />\n                                    <div>\n                                        <hr></hr>\n                                    </div>\n                                    <div className='card-columns mt-4'>\n                                        <BannerUtili />\n                                        <BannerProd />\n                                        <BannerProd2 />\n                                    </div>\n                                </div>\n                            </div>\n                        </TabPanel>\n                        <TabPanel header=\"Trends\" leftIcon=\"pi pi-chart-bar mr-2\">\n                            <div className=\"container-boxed mt-3 pb-4 px-4\">\n                                <div className=\"widget-trends\">\n                                    <ChartHorizontal />\n                                    <TabellaTopFlop />\n                                </div>\n                            </div>\n                        </TabPanel>\n                    </TabView>\n                </div>\n            </div>\n        );\n    }\n}\n\nexport default DashboardAgente;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,aAAa,QAAQ,oDAAoD;AAClF,SAASC,WAAW,QAAQ,8DAA8D;AAC1F,SAASC,UAAU,QAAQ,6DAA6D;AACxF,SAASC,WAAW,QAAQ,8DAA8D;AAC1F,SAASC,eAAe,QAAQ,kEAAkE;AAClG,SAASC,OAAO,EAAEC,QAAQ,QAAQ,oBAAoB;AACtD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,cAAc,QAAQ,iEAAiE;AAChG,OAAOC,GAAG,MAAM,oCAAoC;AACpD,OAAOC,SAAS,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,eAAe,SAASf,SAAS,CAAC;EAAAgB,YAAA;IAAA,SAAAC,SAAA;IAAA,KAQpCC,UAAU,GAAG,MAAM;MACfC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGZ,oBAAoB;IACnD,CAAC;EAAA;EATDa,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAIC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACG,GAAG,KAAK,KAAK,EAAE;MAC5G,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,CACX;QAAEC,QAAQ,EAAE,MAAM;QAAEC,MAAM,EAAE,iFAAiF;QAAEC,MAAM,EAAE;MAAK,CAAC,CAChI,CAAC;IACN;EACJ;EAKAC,MAAMA,CAAA,EAAG;IACL,oBACInB,OAAA;MAAKoB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BrB,OAAA,CAACH,GAAG;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPzB,OAAA,CAACZ,aAAa;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjBzB,OAAA;QAAKoB,SAAS,EAAC,OAAO;QAAAC,QAAA,eAClBrB,OAAA,CAACP,OAAO;UAAC2B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC/BrB,OAAA,CAACN,QAAQ;YAACgC,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAM,CAAE;YAACC,MAAM,EAAC,SAAS;YAACC,QAAQ,EAAC,qBAAqB;YAAAR,QAAA,eAChFrB,OAAA;cAAKoB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAChCrB,OAAA;gBAAKoB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BrB,OAAA,CAACb,QAAQ;kBAACiC,SAAS,EAAC,uBAAuB;kBAACU,GAAG,EAAGC,EAAE,IAAK,IAAI,CAACjB,IAAI,GAAGiB,EAAG;kBAACC,OAAO,EAAE,IAAI,CAAC5B;gBAAW;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrGzB,OAAA,CAACF,SAAS;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACbzB,OAAA;kBAAAqB,QAAA,eACIrB,OAAA;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNzB,OAAA;kBAAKoB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9BrB,OAAA,CAACX,WAAW;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACfzB,OAAA,CAACV,UAAU;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACdzB,OAAA,CAACT,WAAW;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACXzB,OAAA,CAACN,QAAQ;YAACkC,MAAM,EAAC,QAAQ;YAACC,QAAQ,EAAC,sBAAsB;YAAAR,QAAA,eACrDrB,OAAA;cAAKoB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC3CrB,OAAA;gBAAKoB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BrB,OAAA,CAACR,eAAe;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnBzB,OAAA,CAACJ,cAAc;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;AACJ;AAEA,eAAexB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}