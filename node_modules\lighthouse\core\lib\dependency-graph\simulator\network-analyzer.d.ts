export type Summary = {
    min: number;
    max: number;
    avg: number;
    median: number;
};
export type RTTEstimateOptions = {
    /**
     * TCP connection handshake information will be used when available, but in some circumstances this data can be unreliable. This flag exposes an option to ignore the handshake data and use the coarse download/TTFB timing data.
     */
    forceCoarseEstimates?: boolean | undefined;
    /**
     * Coarse estimates include lots of extra time and noise multiply by some factor to deflate the estimates a bit.
     */
    coarseEstimateMultiplier?: number | undefined;
    /**
     * Useful for testing to isolate the different methods of estimation.
     */
    useDownloadEstimates?: boolean | undefined;
    /**
     * Useful for testing to isolate the different methods of estimation.
     */
    useSendStartEstimates?: boolean | undefined;
    /**
     * Useful for testing to isolate the different methods of estimation.
     */
    useHeadersEndEstimates?: boolean | undefined;
};
export class NetworkAnalyzer {
    /**
     * @return {string}
     */
    static get SUMMARY(): string;
    /**
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {Map<string, LH.Artifacts.NetworkRequest[]>}
     */
    static groupByOrigin(records: LH.Artifacts.NetworkRequest[]): Map<string, LH.Artifacts.NetworkRequest[]>;
    /**
     * @param {number[]} values
     * @return {Summary}
     */
    static getSummary(values: number[]): Summary;
    /**
     * @param {Map<string,number[]>} values
     * @return {Map<string, Summary>}
     */
    static summarize(values: Map<string, number[]>): Map<string, Summary>;
    /** @typedef {{record: LH.Artifacts.NetworkRequest, timing: LH.Crdp.Network.ResourceTiming, connectionReused?: boolean}} RequestInfo */
    /**
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {(e: RequestInfo) => number | number[] | undefined} iteratee
     * @return {Map<string, number[]>}
     */
    static _estimateValueByOrigin(records: LH.Artifacts.NetworkRequest[], iteratee: (e: {
        record: LH.Artifacts.NetworkRequest;
        timing: LH.Crdp.Network.ResourceTiming;
        connectionReused?: boolean | undefined;
    }) => number | number[] | undefined): Map<string, number[]>;
    /**
     * Estimates the observed RTT to each origin based on how long the TCP handshake took.
     * This is the most accurate and preferred method of measurement when the data is available.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {Map<string, number[]>}
     */
    static _estimateRTTByOriginViaTCPTiming(records: LH.Artifacts.NetworkRequest[]): Map<string, number[]>;
    /**
     * Estimates the observed RTT to each origin based on how long a download took on a fresh connection.
     * NOTE: this will tend to overestimate the actual RTT quite significantly as the download can be
     * slow for other reasons as well such as bandwidth constraints.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {Map<string, number[]>}
     */
    static _estimateRTTByOriginViaDownloadTiming(records: LH.Artifacts.NetworkRequest[]): Map<string, number[]>;
    /**
     * Estimates the observed RTT to each origin based on how long it took until Chrome could
     * start sending the actual request when a new connection was required.
     * NOTE: this will tend to overestimate the actual RTT as the request can be delayed for other
     * reasons as well such as more SSL handshakes if TLS False Start is not enabled.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {Map<string, number[]>}
     */
    static _estimateRTTByOriginViaSendStartTiming(records: LH.Artifacts.NetworkRequest[]): Map<string, number[]>;
    /**
     * Estimates the observed RTT to each origin based on how long it took until Chrome received the
     * headers of the response (~TTFB).
     * NOTE: this is the most inaccurate way to estimate the RTT, but in some environments it's all
     * we have access to :(
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {Map<string, number[]>}
     */
    static _estimateRTTByOriginViaHeadersEndTiming(records: LH.Artifacts.NetworkRequest[]): Map<string, number[]>;
    /**
     * Given the RTT to each origin, estimates the observed server response times.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {Map<string, number>} rttByOrigin
     * @return {Map<string, number[]>}
     */
    static _estimateResponseTimeByOrigin(records: LH.Artifacts.NetworkRequest[], rttByOrigin: Map<string, number>): Map<string, number[]>;
    /**
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @return {boolean}
     */
    static canTrustConnectionInformation(records: LH.Artifacts.NetworkRequest[]): boolean;
    /**
     * Returns a map of requestId -> connectionReused, estimating the information if the information
     * available in the records themselves appears untrustworthy.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {{forceCoarseEstimates: boolean}} [options]
     * @return {Map<string, boolean>}
     */
    static estimateIfConnectionWasReused(records: LH.Artifacts.NetworkRequest[], options?: {
        forceCoarseEstimates: boolean;
    } | undefined): Map<string, boolean>;
    /**
     * Estimates the RTT to each origin by examining observed network timing information.
     * Attempts to use the most accurate information first and falls back to coarser estimates when it
     * is unavailable.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {RTTEstimateOptions} [options]
     * @return {Map<string, Summary>}
     */
    static estimateRTTByOrigin(records: LH.Artifacts.NetworkRequest[], options?: RTTEstimateOptions | undefined): Map<string, Summary>;
    /**
     * Estimates the server response time of each origin. RTT times can be passed in or will be
     * estimated automatically if not provided.
     *
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {RTTEstimateOptions & {rttByOrigin?: Map<string, number>}} [options]
     * @return {Map<string, Summary>}
     */
    static estimateServerResponseTimeByOrigin(records: LH.Artifacts.NetworkRequest[], options?: (RTTEstimateOptions & {
        rttByOrigin?: Map<string, number> | undefined;
    }) | undefined): Map<string, Summary>;
    /**
     * Computes the average throughput for the given records in bits/second.
     * Excludes data URI, failed or otherwise incomplete, and cached requests.
     * Returns Infinity if there were no analyzable network records.
     *
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {number}
     */
    static estimateThroughput(networkRecords: Array<LH.Artifacts.NetworkRequest>): number;
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} records
     * @param {string} resourceUrl
     * @return {LH.Artifacts.NetworkRequest|undefined}
     */
    static findResourceForUrl(records: Array<LH.Artifacts.NetworkRequest>, resourceUrl: string): LH.Artifacts.NetworkRequest | undefined;
    /**
     * Resolves redirect chain given a main document.
     * See: {@link NetworkAnalyzer.findResourceForUrl}) for how to retrieve main document.
     *
     * @param {LH.Artifacts.NetworkRequest} request
     * @return {LH.Artifacts.NetworkRequest}
     */
    static resolveRedirects(request: LH.Artifacts.NetworkRequest): LH.Artifacts.NetworkRequest;
}
//# sourceMappingURL=network-analyzer.d.ts.map