{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\common\\\\amministratore\\\\DashboardAmministratore.jsx\";\n/* Main components*/\nimport React, { Component } from \"react\";\nimport { NavLink } from \"react-router-dom\";\nimport { <PERSON><PERSON> } from \"../../components/traduttore/const\";\nimport { adminGeneratoreDocumenti, adminGestioneCorporate, adminGestioneUtenti } from \"../../components/route\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Logos from \"../../resources/Logos\";\nimport OrdiniGiornalieri from \"../../components/generalizzazioni/tabellaOrdiniGiornalieri\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass dashboardAmministratore extends Component {\n  render() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Nav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"album\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(OrdiniGiornalieri, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-columns\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card mb-4 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"titleBox\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ico-logo\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    width: \"40px\",\n                    src: Logos.mail,\n                    alt: \"Mail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 63\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"titolo\",\n                  children: Costanti.GestCorp\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"accordion\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"serviceBox\",\n                      id: \"headingSeven\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"areaToggle\",\n                        children: /*#__PURE__*/_jsxDEV(NavLink, {\n                          to: adminGestioneCorporate,\n                          className: \"btn btn-link col-12 dash-btn\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Corporate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 73,\n                            columnNumber: 131\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"pi pi-link btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 73,\n                            columnNumber: 157\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 73,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 72,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 71,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card mb-4 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"titleBox\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ico-logo\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    width: \"40px\",\n                    src: Logos.mail,\n                    alt: \"Mail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 63\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"titolo\",\n                  children: Costanti.GestUser\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"accordion\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"serviceBox\",\n                      id: \"headingSeven\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"areaToggle\",\n                        children: /*#__PURE__*/_jsxDEV(NavLink, {\n                          to: adminGestioneUtenti,\n                          className: \"btn btn-link col-12 dash-btn\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: Costanti.Utenti\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 91,\n                            columnNumber: 128\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"pi pi-link btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 91,\n                            columnNumber: 162\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 91,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 89,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card mb-4 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"titleBox\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ico-logo\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    width: \"40px\",\n                    src: Logos.mail,\n                    alt: \"Mail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 63\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"titolo\",\n                  children: Costanti.GeneraDocumenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"accordion\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"serviceBox\",\n                      id: \"headingSeven\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"areaToggle\",\n                        children: /*#__PURE__*/_jsxDEV(NavLink, {\n                          to: adminGeneratoreDocumenti,\n                          className: \"btn btn-link col-12 dash-btn\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: Costanti.GeneraDocumenti\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 109,\n                            columnNumber: 133\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"pi pi-link btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 109,\n                            columnNumber: 176\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 109,\n                          columnNumber: 53\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"push\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default dashboardAmministratore;", "map": {"version": 3, "names": ["React", "Component", "NavLink", "<PERSON><PERSON>", "adminGeneratoreDocumenti", "adminGestioneCorporate", "adminGestioneUtenti", "Nav", "Logos", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "dashboardAmministratore", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "src", "mail", "alt", "GestCorp", "id", "to", "GestUser", "<PERSON><PERSON><PERSON>", "GeneraDocumenti"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/common/amministratore/DashboardAmministratore.jsx"], "sourcesContent": ["/* Main components*/\nimport React, { Component } from \"react\";\nimport { NavLink } from \"react-router-dom\";\nimport { Costanti } from \"../../components/traduttore/const\";\nimport { adminGeneratoreDocumenti, adminGestioneCorporate, adminGestioneUtenti } from \"../../components/route\";\nimport Nav from \"../../components/navigation/Nav\";\nimport Logos from \"../../resources/Logos\";\nimport OrdiniGiornalieri from \"../../components/generalizzazioni/tabellaOrdiniGiornalieri\";\n\nclass dashboardAmministratore extends Component {\n\n    render() {\n        return (\n            <div className=\"dashboard wrapper\">\n                <Nav />\n                <div className=\"album\">\n                    <div className=\"container\">\n                        <OrdiniGiornalieri />\n                        <hr></hr>\n                        <div className=\"card-columns\">\n                            {/*      {/* ONE *//*}\n                            <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.alyante} alt=\"Alyante\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">Alyante</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingOne\">\n                                                <div className=\"areaToggle mb-0\">\n                                                    <NavLink to=\"/admin/ordini\" className=\"btn btn-link col-12 dash-btn\"><span className=\"pi pi-link btn-icon\"></span><strong>{Costanti.Ordini}</strong></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingThree\">\n                                                <div className=\"areaToggle mb-0\">\n                                                    <NavLink to=\"/admin/fatture\" className=\"btn btn-link col-12 dash-btn\"><span className=\"pi pi-link btn-icon\"></span><strong>{Costanti.Fatture}</strong></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingThree\">\n                                                <div className=\"areaToggle mb-0\">\n                                                    <NavLink to=\"/admin/giacenzeAlyante\" className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.Prodotti}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingThree\">\n                                                <div className=\"areaToggle mb-0\">\n                                                    <NavLink to=\"/admin/inventario\" className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.AllineamentoMagazzino}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div> */}\n                            {/* FOUR */}\n                            <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.mail} alt=\"Mail\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">{Costanti.GestCorp}</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to={adminGestioneCorporate} className=\"btn btn-link col-12 dash-btn\"><strong>Corporate</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.mail} alt=\"Mail\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">{Costanti.GestUser}</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to={adminGestioneUtenti} className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.Utenti}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.mail} alt=\"Mail\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">{Costanti.GeneraDocumenti}</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to={adminGeneratoreDocumenti} className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.GeneraDocumenti}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                            {/* TWO */}\n                            {/* <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.prestashop} alt=\"Prestashop\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">Prestashop</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" >\n                                                <div className=\"areaToggle mb-0\">\n                                                    <button className=\"btn btn-link col-12 dash-btn logo-btn\" data-toggle=\"collapse\" data-target=\"#collapseTwo\" aria-expanded=\"false\" aria-controls=\"collapseOne\">\n                                                        <div className=\"roundedLogo\"><span><img alt=\"Mrwine\" src={Logos.mrwine} /></span></div><div className=\"theTitle\"><strong>Mr. Wine</strong></div><div><span className=\"pi pi-plus btn-icon\"></span></div></button>\n                                                    <div id=\"collapseTwo\" className=\"collapse\" aria-labelledby=\"headingOne\" data-parent=\"#accordion\">\n                                                        <div className=\"card-body servicesList\">\n                                                            <NavLink to=\"/admin/giacenze\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"cube-outline\"></ion-icon>{Costanti.Prodotti}</NavLink>\n                                                            <NavLink to=\"/admin/clienti\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"people-circle-outline\"></ion-icon>{Costanti.Clienti}</NavLink>\n                                                            <NavLink to=\"/admin/clienti\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"receipt-outline\"></ion-icon>{Costanti.Ordini}</NavLink>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingFive\">\n                                                <button className=\"btn btn-link col-12 dash-btn logo-btn\" data-toggle=\"collapse\" data-target=\"#collapseTre\" aria-expanded=\"false\" aria-controls=\"collapseOne\">\n                                                    <div className=\"roundedLogo\"><span><img alt=\"Alcolica Artigianale\" src={Logos.alcolica} /></span></div><div className=\"theTitle\"><strong>Alcolica Artigianale</strong></div><div><span className=\"pi pi-plus btn-icon\"></span></div></button>\n                                                <div className=\"areaToggle mb-0\">\n                                                    <div id=\"collapseTre\" className=\"collapse\" aria-labelledby=\"headingOne\" data-parent=\"#accordion\">\n                                                        <div className=\"card-body servicesList\">\n                                                            <NavLink to=\"/admin/prezziAlc\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"cube-outline\"></ion-icon>{Costanti.Prodotti}</NavLink>\n                                                            <NavLink to=\"/admin/clientiAlc\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"people-circle-outline\"></ion-icon>{Costanti.Clienti}</NavLink>\n                                                            <NavLink to=\"/admin/clienti\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"receipt-outline\"></ion-icon>{Costanti.Ordini}</NavLink>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingFive\">\n                                                <button className=\"btn btn-link col-12 dash-btn logo-btn\" data-toggle=\"collapse\" data-target=\"#collapseQuattro\" aria-expanded=\"false\" aria-controls=\"collapseOne\">\n                                                    <div className=\"roundedLogo\"><span><img alt=\"Sabores de Italia\" src={Logos.sabores} /></span></div><div className=\"theTitle\"><strong>Sabores de Italia</strong></div><div><span className=\"pi pi-plus btn-icon\"></span></div></button>\n                                                <div className=\"areaToggle mb-0\">\n                                                    <div id=\"collapseQuattro\" className=\"collapse\" aria-labelledby=\"headingOne\" data-parent=\"#accordion\">\n                                                        <div className=\"card-body servicesList\">\n                                                            <NavLink to=\"/admin/giacenzeSab\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"cube-outline\"></ion-icon>{Costanti.Prodotti}</NavLink>\n                                                            <NavLink to=\"/admin/clientiSab\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"people-circle-outline\"></ion-icon>{Costanti.Clienti}</NavLink>\n                                                            <NavLink to=\"/admin/clienti\" className=\"btn btn-link col-12\"><ion-icon class=\"serviceIcon mr-2\" name=\"receipt-outline\"></ion-icon>{Costanti.Ordini}</NavLink>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div> */}\n\n                            {/* THREE */}\n                            {/* <div className=\"card mb-4 shadow-sm\">\n                                <div className=\"titleBox\">\n                                    <div className=\"ico-logo\"><img width=\"40px\" src={Logos.mail} alt=\"Mail\" /></div>\n                                    <hr />\n                                    <div className=\"titolo\">Mail</div>\n                                </div>\n                                <div className=\"card-body\">\n                                    <div id=\"accordion\">\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to=\"/admin/scraper\" className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.Scraper}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to=\"/admin/logOrd\" className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.LogOrdini}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"card\">\n                                            <div className=\"serviceBox\" id=\"headingSeven\">\n                                                <div className=\"areaToggle\">\n                                                    <NavLink to=\"/admin/logFat\" className=\"btn btn-link col-12 dash-btn\"><strong>{Costanti.LogFatture}</strong><span className=\"pi pi-link btn-icon\"></span></NavLink>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div> */}\n                        </div>\n                    </div>\n                </div>\n                <div className=\"push\"></div>\n            </div>\n        );\n    }\n}\n\nexport default dashboardAmministratore;"], "mappings": ";AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,wBAAwB,EAAEC,sBAAsB,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC9G,OAAOC,GAAG,MAAM,iCAAiC;AACjD,OAAOC,KAAK,MAAM,uBAAuB;AACzC,OAAOC,iBAAiB,MAAM,4DAA4D;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3F,MAAMC,uBAAuB,SAASX,SAAS,CAAC;EAE5CY,MAAMA,CAAA,EAAG;IACL,oBACIF,OAAA;MAAKG,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BJ,OAAA,CAACJ,GAAG;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPR,OAAA;QAAKG,SAAS,EAAC,OAAO;QAAAC,QAAA,eAClBJ,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBJ,OAAA,CAACF,iBAAiB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBR,OAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTR,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBA0CzBJ,OAAA;cAAKG,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBJ,OAAA;kBAAKG,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAACJ,OAAA;oBAAKS,KAAK,EAAC,MAAM;oBAACC,GAAG,EAAEb,KAAK,CAACc,IAAK;oBAACC,GAAG,EAAC;kBAAM;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChFR,OAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNR,OAAA;kBAAKG,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAEZ,QAAQ,CAACqB;gBAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNR,OAAA;gBAAKG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACtBJ,OAAA;kBAAKc,EAAE,EAAC,WAAW;kBAAAV,QAAA,eACfJ,OAAA;oBAAKG,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACjBJ,OAAA;sBAAKG,SAAS,EAAC,YAAY;sBAACW,EAAE,EAAC,cAAc;sBAAAV,QAAA,eACzCJ,OAAA;wBAAKG,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBJ,OAAA,CAACT,OAAO;0BAACwB,EAAE,EAAErB,sBAAuB;0BAACS,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAACJ,OAAA;4BAAAI,QAAA,EAAQ;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAAAR,OAAA;4BAAMG,SAAS,EAAC;0BAAqB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9J;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBJ,OAAA;kBAAKG,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAACJ,OAAA;oBAAKS,KAAK,EAAC,MAAM;oBAACC,GAAG,EAAEb,KAAK,CAACc,IAAK;oBAACC,GAAG,EAAC;kBAAM;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChFR,OAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNR,OAAA;kBAAKG,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAEZ,QAAQ,CAACwB;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNR,OAAA;gBAAKG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACtBJ,OAAA;kBAAKc,EAAE,EAAC,WAAW;kBAAAV,QAAA,eACfJ,OAAA;oBAAKG,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACjBJ,OAAA;sBAAKG,SAAS,EAAC,YAAY;sBAACW,EAAE,EAAC,cAAc;sBAAAV,QAAA,eACzCJ,OAAA;wBAAKG,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBJ,OAAA,CAACT,OAAO;0BAACwB,EAAE,EAAEpB,mBAAoB;0BAACQ,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAACJ,OAAA;4BAAAI,QAAA,EAASZ,QAAQ,CAACyB;0BAAM;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS,CAAC,eAAAR,OAAA;4BAAMG,SAAS,EAAC;0BAAqB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBJ,OAAA;kBAAKG,SAAS,EAAC,UAAU;kBAAAC,QAAA,eAACJ,OAAA;oBAAKS,KAAK,EAAC,MAAM;oBAACC,GAAG,EAAEb,KAAK,CAACc,IAAK;oBAACC,GAAG,EAAC;kBAAM;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChFR,OAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNR,OAAA;kBAAKG,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAEZ,QAAQ,CAAC0B;gBAAe;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNR,OAAA;gBAAKG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACtBJ,OAAA;kBAAKc,EAAE,EAAC,WAAW;kBAAAV,QAAA,eACfJ,OAAA;oBAAKG,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACjBJ,OAAA;sBAAKG,SAAS,EAAC,YAAY;sBAACW,EAAE,EAAC,cAAc;sBAAAV,QAAA,eACzCJ,OAAA;wBAAKG,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBJ,OAAA,CAACT,OAAO;0BAACwB,EAAE,EAAEtB,wBAAyB;0BAACU,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAACJ,OAAA;4BAAAI,QAAA,EAASZ,QAAQ,CAAC0B;0BAAe;4BAAAb,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS,CAAC,eAAAR,OAAA;4BAAMG,SAAS,EAAC;0BAAqB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6FL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNR,OAAA;QAAKG,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEd;AACJ;AAEA,eAAeP,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}