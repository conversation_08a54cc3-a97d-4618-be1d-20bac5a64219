{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar Switch = React.forwardRef(function (_ref, ref) {\n  var _classNames;\n  var _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-switch' : _ref$prefixCls,\n    className = _ref.className,\n    checked = _ref.checked,\n    defaultChecked = _ref.defaultChecked,\n    disabled = _ref.disabled,\n    loadingIcon = _ref.loadingIcon,\n    checkedChildren = _ref.checkedChildren,\n    unCheckedChildren = _ref.unCheckedChildren,\n    onClick = _ref.onClick,\n    onChange = _ref.onChange,\n    onKeyDown = _ref.onKeyDown,\n    restProps = _objectWithoutProperties(_ref, [\"prefixCls\", \"className\", \"checked\", \"defaultChecked\", \"disabled\", \"loadingIcon\", \"checkedChildren\", \"unCheckedChildren\", \"onClick\", \"onChange\", \"onKeyDown\"]);\n  var _useMergedState = useMergedState(false, {\n      value: checked,\n      defaultValue: defaultChecked\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    innerChecked = _useMergedState2[0],\n    setInnerChecked = _useMergedState2[1];\n  function triggerChange(newChecked, event) {\n    var mergedChecked = innerChecked;\n    if (!disabled) {\n      mergedChecked = newChecked;\n      setInnerChecked(mergedChecked);\n      onChange === null || onChange === void 0 ? void 0 : onChange(mergedChecked, event);\n    }\n    return mergedChecked;\n  }\n  function onInternalKeyDown(e) {\n    if (e.which === KeyCode.LEFT) {\n      triggerChange(false, e);\n    } else if (e.which === KeyCode.RIGHT) {\n      triggerChange(true, e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  }\n  function onInternalClick(e) {\n    var ret = triggerChange(!innerChecked, e); // [Legacy] trigger onClick with value\n\n    onClick === null || onClick === void 0 ? void 0 : onClick(ret, e);\n  }\n  var switchClassName = classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), innerChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames));\n  return React.createElement(\"button\", Object.assign({}, restProps, {\n    type: \"button\",\n    role: \"switch\",\n    \"aria-checked\": innerChecked,\n    disabled: disabled,\n    className: switchClassName,\n    ref: ref,\n    onKeyDown: onInternalKeyDown,\n    onClick: onInternalClick\n  }), loadingIcon, React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, innerChecked ? checkedChildren : unCheckedChildren));\n});\nSwitch.displayName = 'Switch';\nexport default Switch;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "_objectWithoutProperties", "React", "classNames", "useMergedState", "KeyCode", "Switch", "forwardRef", "_ref", "ref", "_classNames", "_ref$prefixCls", "prefixCls", "className", "checked", "defaultChecked", "disabled", "loadingIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "onClick", "onChange", "onKeyDown", "restProps", "_useMergedState", "value", "defaultValue", "_useMergedState2", "innerChecked", "setInnerChecked", "trigger<PERSON>hange", "newChecked", "event", "mergedChecked", "onInternalKeyDown", "e", "which", "LEFT", "RIGHT", "onInternalClick", "ret", "switchClassName", "concat", "createElement", "Object", "assign", "type", "role", "displayName"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-switch/es/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nvar Switch = React.forwardRef(function (_ref, ref) {\n  var _classNames;\n\n  var _ref$prefixCls = _ref.prefixCls,\n      prefixCls = _ref$prefixCls === void 0 ? 'rc-switch' : _ref$prefixCls,\n      className = _ref.className,\n      checked = _ref.checked,\n      defaultChecked = _ref.defaultChecked,\n      disabled = _ref.disabled,\n      loadingIcon = _ref.loadingIcon,\n      checkedChildren = _ref.checkedChildren,\n      unCheckedChildren = _ref.unCheckedChildren,\n      onClick = _ref.onClick,\n      onChange = _ref.onChange,\n      onKeyDown = _ref.onKeyDown,\n      restProps = _objectWithoutProperties(_ref, [\"prefixCls\", \"className\", \"checked\", \"defaultChecked\", \"disabled\", \"loadingIcon\", \"checkedChildren\", \"unCheckedChildren\", \"onClick\", \"onChange\", \"onKeyDown\"]);\n\n  var _useMergedState = useMergedState(false, {\n    value: checked,\n    defaultValue: defaultChecked\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      innerChecked = _useMergedState2[0],\n      setInnerChecked = _useMergedState2[1];\n\n  function triggerChange(newChecked, event) {\n    var mergedChecked = innerChecked;\n\n    if (!disabled) {\n      mergedChecked = newChecked;\n      setInnerChecked(mergedChecked);\n      onChange === null || onChange === void 0 ? void 0 : onChange(mergedChecked, event);\n    }\n\n    return mergedChecked;\n  }\n\n  function onInternalKeyDown(e) {\n    if (e.which === KeyCode.LEFT) {\n      triggerChange(false, e);\n    } else if (e.which === KeyCode.RIGHT) {\n      triggerChange(true, e);\n    }\n\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  }\n\n  function onInternalClick(e) {\n    var ret = triggerChange(!innerChecked, e); // [Legacy] trigger onClick with value\n\n    onClick === null || onClick === void 0 ? void 0 : onClick(ret, e);\n  }\n\n  var switchClassName = classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), innerChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames));\n  return React.createElement(\"button\", Object.assign({}, restProps, {\n    type: \"button\",\n    role: \"switch\",\n    \"aria-checked\": innerChecked,\n    disabled: disabled,\n    className: switchClassName,\n    ref: ref,\n    onKeyDown: onInternalKeyDown,\n    onClick: onInternalClick\n  }), loadingIcon, React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, innerChecked ? checkedChildren : unCheckedChildren));\n});\nSwitch.displayName = 'Switch';\nexport default Switch;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,MAAM,GAAGJ,KAAK,CAACK,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EACjD,IAAIC,WAAW;EAEf,IAAIC,cAAc,GAAGH,IAAI,CAACI,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,cAAc;IACpEE,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC1BC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,cAAc,GAAGP,IAAI,CAACO,cAAc;IACpCC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,WAAW,GAAGT,IAAI,CAACS,WAAW;IAC9BC,eAAe,GAAGV,IAAI,CAACU,eAAe;IACtCC,iBAAiB,GAAGX,IAAI,CAACW,iBAAiB;IAC1CC,OAAO,GAAGZ,IAAI,CAACY,OAAO;IACtBC,QAAQ,GAAGb,IAAI,CAACa,QAAQ;IACxBC,SAAS,GAAGd,IAAI,CAACc,SAAS;IAC1BC,SAAS,GAAGtB,wBAAwB,CAACO,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;EAE9M,IAAIgB,eAAe,GAAGpB,cAAc,CAAC,KAAK,EAAE;MAC1CqB,KAAK,EAAEX,OAAO;MACdY,YAAY,EAAEX;IAChB,CAAC,CAAC;IACEY,gBAAgB,GAAG3B,cAAc,CAACwB,eAAe,EAAE,CAAC,CAAC;IACrDI,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,SAASG,aAAaA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACxC,IAAIC,aAAa,GAAGL,YAAY;IAEhC,IAAI,CAACZ,QAAQ,EAAE;MACbiB,aAAa,GAAGF,UAAU;MAC1BF,eAAe,CAACI,aAAa,CAAC;MAC9BZ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACY,aAAa,EAAED,KAAK,CAAC;IACpF;IAEA,OAAOC,aAAa;EACtB;EAEA,SAASC,iBAAiBA,CAACC,CAAC,EAAE;IAC5B,IAAIA,CAAC,CAACC,KAAK,KAAK/B,OAAO,CAACgC,IAAI,EAAE;MAC5BP,aAAa,CAAC,KAAK,EAAEK,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIA,CAAC,CAACC,KAAK,KAAK/B,OAAO,CAACiC,KAAK,EAAE;MACpCR,aAAa,CAAC,IAAI,EAAEK,CAAC,CAAC;IACxB;IAEAb,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACa,CAAC,CAAC;EACpE;EAEA,SAASI,eAAeA,CAACJ,CAAC,EAAE;IAC1B,IAAIK,GAAG,GAAGV,aAAa,CAAC,CAACF,YAAY,EAAEO,CAAC,CAAC,CAAC,CAAC;;IAE3Cf,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,GAAG,EAAEL,CAAC,CAAC;EACnE;EAEA,IAAIM,eAAe,GAAGtC,UAAU,CAACS,SAAS,EAAEC,SAAS,GAAGH,WAAW,GAAG,CAAC,CAAC,EAAEX,eAAe,CAACW,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC9B,SAAS,EAAE,UAAU,CAAC,EAAEgB,YAAY,CAAC,EAAE7B,eAAe,CAACW,WAAW,EAAE,EAAE,CAACgC,MAAM,CAAC9B,SAAS,EAAE,WAAW,CAAC,EAAEI,QAAQ,CAAC,EAAEN,WAAW,CAAC,CAAC;EAChP,OAAOR,KAAK,CAACyC,aAAa,CAAC,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,SAAS,EAAE;IAChEuB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACd,cAAc,EAAEnB,YAAY;IAC5BZ,QAAQ,EAAEA,QAAQ;IAClBH,SAAS,EAAE4B,eAAe;IAC1BhC,GAAG,EAAEA,GAAG;IACRa,SAAS,EAAEY,iBAAiB;IAC5Bd,OAAO,EAAEmB;EACX,CAAC,CAAC,EAAEtB,WAAW,EAAEf,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;IAC3C9B,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC9B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEgB,YAAY,GAAGV,eAAe,GAAGC,iBAAiB,CAAC,CAAC;AACzD,CAAC,CAAC;AACFb,MAAM,CAAC0C,WAAW,GAAG,QAAQ;AAC7B,eAAe1C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}