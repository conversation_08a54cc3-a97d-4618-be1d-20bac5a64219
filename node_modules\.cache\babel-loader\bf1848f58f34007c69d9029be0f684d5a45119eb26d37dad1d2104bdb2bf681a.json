{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\components\\\\generalizzazioni\\\\menuSettings.jsx\";\nimport React, { Component } from 'react';\nimport { <PERSON>ubar } from 'primereact/menubar';\nimport { <PERSON><PERSON> } from '../traduttore/const';\nimport { login, settings } from '../route';\nimport { Logo } from \"../logo\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass Menu extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {};\n  }\n  render() {\n    var items = [{\n      label: JSON.parse(window.localStorage.userid).firstName,\n      icon: '',\n      items: [{\n        label: Costanti.Profilo,\n        icon: 'pi pi-cog',\n        className: 'mt-2',\n        command: () => {\n          window.location.pathname = settings;\n        }\n      }, {\n        separator: true\n      }, {\n        label: Costanti.Esci,\n        icon: 'pi pi-unlock',\n        className: 'mb-2',\n        command: () => {\n          localStorage.removeItem('login_token');\n          localStorage.removeItem('role');\n          window.location.pathname = login;\n        }\n      }]\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"desktopMenuBar text-center d-flex flex-column align-items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-row align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          width: \"20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Menubar, {\n          className: \"menuBar\",\n          model: items\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this);\n  }\n}\nexport default Menu;", "map": {"version": 3, "names": ["React", "Component", "Men<PERSON><PERSON>", "<PERSON><PERSON>", "login", "settings", "Logo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "constructor", "props", "state", "render", "items", "label", "JSON", "parse", "window", "localStorage", "userid", "firstName", "icon", "<PERSON>ilo", "className", "command", "location", "pathname", "separator", "<PERSON><PERSON><PERSON>", "removeItem", "children", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "model"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/components/generalizzazioni/menuSettings.jsx"], "sourcesContent": ["import React, { Component } from 'react';\nimport { <PERSON>ubar } from 'primereact/menubar';\nimport { Costanti } from '../traduttore/const';\nimport { login, settings } from '../route';\nimport { Logo } from \"../logo\";\n\nclass Menu extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {}\n    }\n    render() {\n        var items = [\n            {\n                label: JSON.parse(window.localStorage.userid).firstName,\n                icon: '',\n                items: [\n                    {\n                        label: Costanti.Profilo,\n                        icon: 'pi pi-cog',\n                        className: 'mt-2',\n                        command: () => { window.location.pathname = settings }\n                    },\n                    {\n                        separator: true\n                    },\n                    {\n                        label: Costanti.Esci,\n                        icon: 'pi pi-unlock',\n                        className: 'mb-2',\n                        command: () => {\n                            localStorage.removeItem('login_token');\n                            localStorage.removeItem('role');\n                            window.location.pathname = login\n                        }\n                    },\n                ]\n            },\n        ]\n        return (\n            <div className='desktopMenuBar text-center d-flex flex-column align-items-center'>\n                <div className='d-flex flex-row align-items-center'>\n                    <Logo width='20' />\n                    <Menubar className='menuBar' model={items} />\n                </div>\n\n                {/* <span className=\"Role\">{localStorage.getItem(\"role\")}</span> */}\n            </div>\n        )\n    }\n}\n\nexport default Menu;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,EAAEC,QAAQ,QAAQ,UAAU;AAC1C,SAASC,IAAI,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,IAAI,SAASR,SAAS,CAAC;EACzBS,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,MAAMA,CAAA,EAAG;IACL,IAAIC,KAAK,GAAG,CACR;MACIC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,MAAM,CAAC,CAACC,SAAS;MACvDC,IAAI,EAAE,EAAE;MACRR,KAAK,EAAE,CACH;QACIC,KAAK,EAAEZ,QAAQ,CAACoB,OAAO;QACvBD,IAAI,EAAE,WAAW;QACjBE,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAEA,CAAA,KAAM;UAAEP,MAAM,CAACQ,QAAQ,CAACC,QAAQ,GAAGtB,QAAQ;QAAC;MACzD,CAAC,EACD;QACIuB,SAAS,EAAE;MACf,CAAC,EACD;QACIb,KAAK,EAAEZ,QAAQ,CAAC0B,IAAI;QACpBP,IAAI,EAAE,cAAc;QACpBE,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAEA,CAAA,KAAM;UACXN,YAAY,CAACW,UAAU,CAAC,aAAa,CAAC;UACtCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;UAC/BZ,MAAM,CAACQ,QAAQ,CAACC,QAAQ,GAAGvB,KAAK;QACpC;MACJ,CAAC;IAET,CAAC,CACJ;IACD,oBACII,OAAA;MAAKgB,SAAS,EAAC,kEAAkE;MAAAO,QAAA,eAC7EvB,OAAA;QAAKgB,SAAS,EAAC,oCAAoC;QAAAO,QAAA,gBAC/CvB,OAAA,CAACF,IAAI;UAAC0B,KAAK,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnB5B,OAAA,CAACN,OAAO;UAACsB,SAAS,EAAC,SAAS;UAACa,KAAK,EAAEvB;QAAM;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGL,CAAC;EAEd;AACJ;AAEA,eAAe3B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}