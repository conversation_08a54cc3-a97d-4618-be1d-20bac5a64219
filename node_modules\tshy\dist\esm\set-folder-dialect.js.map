{"version": 3, "file": "set-folder-dialect.js", "sourceRoot": "", "sources": ["../../src/set-folder-dialect.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,aAAa,EAAE,MAAM,IAAI,CAAA;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,UAAU,MAAM,oBAAoB,CAAA;AAC3C,OAAO,GAAG,MAAM,cAAc,CAAA;AAG9B,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,IAAc,EAAE,EAAE;IACnD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IACxC,CAAC;IACD,MAAM,CAAC,GAAoD;QACzD,IAAI,EAAE,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ;QACjD,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC;KACzB,CAAA;IACD,aAAa,CACX,GAAG,CAAC,eAAe,EACnB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAClC,CAAA;AACH,CAAC,CAAA;AAED,eAAe,CAAC,KAAa,EAAE,IAAc,EAAE,EAAE;IAC/C,IAAI,IAAI;QACN,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;IAC/D,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AAC7B,CAAC,CAAA", "sourcesContent": ["import chalk from 'chalk'\nimport { writeFileSync } from 'fs'\nimport { rimrafSync } from 'rimraf'\nimport * as console from './console.js'\nimport getImports from './built-imports.js'\nimport pkg from './package.js'\nimport { Dialect } from './types.js'\n\nconst writeDialectPJ = (d: string, mode?: Dialect) => {\n  if (!mode) {\n    return rimrafSync(`${d}/package.json`)\n  }\n  const v: { type: string; imports?: Record<string, any> } = {\n    type: mode === 'commonjs' ? 'commonjs' : 'module',\n    imports: getImports(pkg),\n  }\n  writeFileSync(\n    `${d}/package.json`,\n    JSON.stringify(v, null, 2) + '\\n',\n  )\n}\n\nexport default (where: string, mode?: Dialect) => {\n  if (mode)\n    console.debug(chalk.cyan.dim('set dialect'), { where, mode })\n  writeDialectPJ(where, mode)\n}\n"]}