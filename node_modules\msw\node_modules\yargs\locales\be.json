{"Commands:": "Каманды:", "Options:": "Опцыі:", "Examples:": "Прыклады:", "boolean": "булевы тып", "count": "падл<PERSON>к", "string": "радковы тып", "number": "<PERSON><PERSON><PERSON>", "array": "масіў", "required": "неабходна", "default": "па змаўчанні", "default:": "па змаўчанні:", "choices:": "магчымасці:", "aliases:": "аліасы:", "generated-value": "згенераванае значэнне", "Not enough non-option arguments: got %s, need at least %s": {"one": "Недастаткова неапцыйных аргументаў: ёсць %s, трэба як мінімум %s", "other": "Недастаткова неапцыйных аргументаў: ёсць %s, трэба як мінімум %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "Занадта шмат неапцыйных аргументаў: ёсць %s, максімум дапушчальна %s", "other": "Занадта шмат неапцыйных аргументаў: ёсць %s, максімум дапушчальна %s"}, "Missing argument value: %s": {"one": "Не хапае значэння аргументу: %s", "other": "Не хапае значэнняў аргументаў: %s"}, "Missing required argument: %s": {"one": "Не хапае неабходнага аргументу: %s", "other": "Не хапае неабходных аргументаў: %s"}, "Unknown argument: %s": {"one": "Невядомы аргумент: %s", "other": "Невядомыя аргументы: %s"}, "Invalid values:": "Несапраўдныя значэння:", "Argument: %s, Given: %s, Choices: %s": "Аргумент: %s, Дадзенае значэнне: %s, Магчымасці: %s", "Argument check failed: %s": "Праверка аргументаў не ўдалася: %s", "Implications failed:": "Дадзены аргумент патрабуе наступны дадатковы аргумент:", "Not enough arguments following: %s": "Недастаткова наступных аргументаў: %s", "Invalid JSON config file: %s": "Несапраўдны файл канфігурацыі JSON: %s", "Path to JSON config file": "Шлях да файла канфігурацыі JSON", "Show help": "Паказаць дапамогу", "Show version number": "Паказаць нумар версіі", "Did you mean %s?": "Вы мелі на ўвазе %s?"}