{"ast": null, "code": "import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, containerSize, tabContentNodeSize, addNodeSize, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var unit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    unit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(containerSize.left);\n  } else {\n    unit = 'height';\n    position = 'top';\n    transformSize = -containerSize.top;\n  }\n  var basicSize = containerSize[unit];\n  var tabContentSize = tabContentNodeSize[unit];\n  var addSize = addNodeSize[unit];\n  var mergedBasicSize = basicSize;\n  if (tabContentSize + addSize > basicSize && tabContentSize < basicSize) {\n    mergedBasicSize = basicSize - addSize;\n  }\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (offset[position] + offset[unit] > transformSize + mergedBasicSize) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return [startIndex, endIndex];\n  }, [tabOffsets, transformSize, mergedBasicSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}", "map": {"version": 3, "names": ["useMemo", "DEFAULT_SIZE", "width", "height", "left", "top", "right", "useVisibleRange", "tabOffsets", "containerSize", "tabContentNodeSize", "addNodeSize", "_ref", "tabs", "tabPosition", "rtl", "unit", "position", "transformSize", "includes", "Math", "abs", "basicSize", "tabContentSize", "addSize", "mergedBasicSize", "length", "len", "endIndex", "i", "offset", "get", "key", "startIndex", "_i", "_offset", "map", "tab", "join"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/hooks/useVisibleRange.js"], "sourcesContent": ["import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, containerSize, tabContentNodeSize, addNodeSize, _ref) {\n  var tabs = _ref.tabs,\n      tabPosition = _ref.tabPosition,\n      rtl = _ref.rtl;\n  var unit;\n  var position;\n  var transformSize;\n\n  if (['top', 'bottom'].includes(tabPosition)) {\n    unit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(containerSize.left);\n  } else {\n    unit = 'height';\n    position = 'top';\n    transformSize = -containerSize.top;\n  }\n\n  var basicSize = containerSize[unit];\n  var tabContentSize = tabContentNodeSize[unit];\n  var addSize = addNodeSize[unit];\n  var mergedBasicSize = basicSize;\n\n  if (tabContentSize + addSize > basicSize && tabContentSize < basicSize) {\n    mergedBasicSize = basicSize - addSize;\n  }\n\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n\n    var len = tabs.length;\n    var endIndex = len;\n\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n\n      if (offset[position] + offset[unit] > transformSize + mergedBasicSize) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n\n    var startIndex = 0;\n\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n\n    return [startIndex, endIndex];\n  }, [tabOffsets, transformSize, mergedBasicSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE;AACT,CAAC;AACD,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,IAAI,EAAE;EACxG,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,WAAW,GAAGF,IAAI,CAACE,WAAW;IAC9BC,GAAG,GAAGH,IAAI,CAACG,GAAG;EAClB,IAAIC,IAAI;EACR,IAAIC,QAAQ;EACZ,IAAIC,aAAa;EAEjB,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACL,WAAW,CAAC,EAAE;IAC3CE,IAAI,GAAG,OAAO;IACdC,QAAQ,GAAGF,GAAG,GAAG,OAAO,GAAG,MAAM;IACjCG,aAAa,GAAGE,IAAI,CAACC,GAAG,CAACZ,aAAa,CAACL,IAAI,CAAC;EAC9C,CAAC,MAAM;IACLY,IAAI,GAAG,QAAQ;IACfC,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAACT,aAAa,CAACJ,GAAG;EACpC;EAEA,IAAIiB,SAAS,GAAGb,aAAa,CAACO,IAAI,CAAC;EACnC,IAAIO,cAAc,GAAGb,kBAAkB,CAACM,IAAI,CAAC;EAC7C,IAAIQ,OAAO,GAAGb,WAAW,CAACK,IAAI,CAAC;EAC/B,IAAIS,eAAe,GAAGH,SAAS;EAE/B,IAAIC,cAAc,GAAGC,OAAO,GAAGF,SAAS,IAAIC,cAAc,GAAGD,SAAS,EAAE;IACtEG,eAAe,GAAGH,SAAS,GAAGE,OAAO;EACvC;EAEA,OAAOxB,OAAO,CAAC,YAAY;IACzB,IAAI,CAACa,IAAI,CAACa,MAAM,EAAE;MAChB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACf;IAEA,IAAIC,GAAG,GAAGd,IAAI,CAACa,MAAM;IACrB,IAAIE,QAAQ,GAAGD,GAAG;IAElB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIC,MAAM,GAAGtB,UAAU,CAACuB,GAAG,CAAClB,IAAI,CAACgB,CAAC,CAAC,CAACG,GAAG,CAAC,IAAI/B,YAAY;MAExD,IAAI6B,MAAM,CAACb,QAAQ,CAAC,GAAGa,MAAM,CAACd,IAAI,CAAC,GAAGE,aAAa,GAAGO,eAAe,EAAE;QACrEG,QAAQ,GAAGC,CAAC,GAAG,CAAC;QAChB;MACF;IACF;IAEA,IAAII,UAAU,GAAG,CAAC;IAElB,KAAK,IAAIC,EAAE,GAAGP,GAAG,GAAG,CAAC,EAAEO,EAAE,IAAI,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE;MACvC,IAAIC,OAAO,GAAG3B,UAAU,CAACuB,GAAG,CAAClB,IAAI,CAACqB,EAAE,CAAC,CAACF,GAAG,CAAC,IAAI/B,YAAY;MAE1D,IAAIkC,OAAO,CAAClB,QAAQ,CAAC,GAAGC,aAAa,EAAE;QACrCe,UAAU,GAAGC,EAAE,GAAG,CAAC;QACnB;MACF;IACF;IAEA,OAAO,CAACD,UAAU,EAAEL,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACpB,UAAU,EAAEU,aAAa,EAAEO,eAAe,EAAEX,WAAW,EAAED,IAAI,CAACuB,GAAG,CAAC,UAAUC,GAAG,EAAE;IACnF,OAAOA,GAAG,CAACL,GAAG;EAChB,CAAC,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC,EAAEvB,GAAG,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}