{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport var SEARCH_MARK = '__rc_cascader_search_mark__';\nvar defaultFilter = function defaultFilter(search, options, _ref) {\n  var label = _ref.label;\n  return options.some(function (opt) {\n    return String(opt[label]).toLowerCase().includes(search.toLowerCase());\n  });\n};\nvar defaultRender = function defaultRender(inputValue, path, prefixCls, fieldNames) {\n  return path.map(function (opt) {\n    return opt[fieldNames.label];\n  }).join(' / ');\n};\nexport default (function (search, options, fieldNames, prefixCls, config, changeOnSelect) {\n  var _config$filter = config.filter,\n    filter = _config$filter === void 0 ? defaultFilter : _config$filter,\n    _config$render = config.render,\n    render = _config$render === void 0 ? defaultRender : _config$render,\n    _config$limit = config.limit,\n    limit = _config$limit === void 0 ? 50 : _config$limit,\n    sort = config.sort;\n  return React.useMemo(function () {\n    var filteredOptions = [];\n    if (!search) {\n      return [];\n    }\n    function dig(list, pathOptions) {\n      list.forEach(function (option) {\n        // Perf saving when `sort` is disabled and `limit` is provided\n        if (!sort && limit > 0 && filteredOptions.length >= limit) {\n          return;\n        }\n        var connectedPathOptions = [].concat(_toConsumableArray(pathOptions), [option]);\n        var children = option[fieldNames.children]; // If current option is filterable\n\n        if (\n        // If is leaf option\n        !children || children.length === 0 ||\n        // If is changeOnSelect\n        changeOnSelect) {\n          if (filter(search, connectedPathOptions, {\n            label: fieldNames.label\n          })) {\n            var _objectSpread2;\n            filteredOptions.push(_objectSpread(_objectSpread({}, option), {}, (_objectSpread2 = {}, _defineProperty(_objectSpread2, fieldNames.label, render(search, connectedPathOptions, prefixCls, fieldNames)), _defineProperty(_objectSpread2, SEARCH_MARK, connectedPathOptions), _objectSpread2)));\n          }\n        }\n        if (children) {\n          dig(option[fieldNames.children], connectedPathOptions);\n        }\n      });\n    }\n    dig(options, []); // Do sort\n\n    if (sort) {\n      filteredOptions.sort(function (a, b) {\n        return sort(a[SEARCH_MARK], b[SEARCH_MARK], search, fieldNames);\n      });\n    }\n    return limit > 0 ? filteredOptions.slice(0, limit) : filteredOptions;\n  }, [search, options, fieldNames, prefixCls, render, changeOnSelect, filter, sort, limit]);\n});", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_toConsumableArray", "React", "SEARCH_MARK", "defaultFilter", "search", "options", "_ref", "label", "some", "opt", "String", "toLowerCase", "includes", "defaultRender", "inputValue", "path", "prefixCls", "fieldNames", "map", "join", "config", "changeOnSelect", "_config$filter", "filter", "_config$render", "render", "_config$limit", "limit", "sort", "useMemo", "filteredOptions", "dig", "list", "pathOptions", "for<PERSON>ach", "option", "length", "connectedPathOptions", "concat", "children", "_objectSpread2", "push", "a", "b", "slice"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-cascader/es/hooks/useSearchOptions.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport var SEARCH_MARK = '__rc_cascader_search_mark__';\n\nvar defaultFilter = function defaultFilter(search, options, _ref) {\n  var label = _ref.label;\n  return options.some(function (opt) {\n    return String(opt[label]).toLowerCase().includes(search.toLowerCase());\n  });\n};\n\nvar defaultRender = function defaultRender(inputValue, path, prefixCls, fieldNames) {\n  return path.map(function (opt) {\n    return opt[fieldNames.label];\n  }).join(' / ');\n};\n\nexport default (function (search, options, fieldNames, prefixCls, config, changeOnSelect) {\n  var _config$filter = config.filter,\n      filter = _config$filter === void 0 ? defaultFilter : _config$filter,\n      _config$render = config.render,\n      render = _config$render === void 0 ? defaultRender : _config$render,\n      _config$limit = config.limit,\n      limit = _config$limit === void 0 ? 50 : _config$limit,\n      sort = config.sort;\n  return React.useMemo(function () {\n    var filteredOptions = [];\n\n    if (!search) {\n      return [];\n    }\n\n    function dig(list, pathOptions) {\n      list.forEach(function (option) {\n        // Perf saving when `sort` is disabled and `limit` is provided\n        if (!sort && limit > 0 && filteredOptions.length >= limit) {\n          return;\n        }\n\n        var connectedPathOptions = [].concat(_toConsumableArray(pathOptions), [option]);\n        var children = option[fieldNames.children]; // If current option is filterable\n\n        if ( // If is leaf option\n        !children || children.length === 0 || // If is changeOnSelect\n        changeOnSelect) {\n          if (filter(search, connectedPathOptions, {\n            label: fieldNames.label\n          })) {\n            var _objectSpread2;\n\n            filteredOptions.push(_objectSpread(_objectSpread({}, option), {}, (_objectSpread2 = {}, _defineProperty(_objectSpread2, fieldNames.label, render(search, connectedPathOptions, prefixCls, fieldNames)), _defineProperty(_objectSpread2, SEARCH_MARK, connectedPathOptions), _objectSpread2)));\n          }\n        }\n\n        if (children) {\n          dig(option[fieldNames.children], connectedPathOptions);\n        }\n      });\n    }\n\n    dig(options, []); // Do sort\n\n    if (sort) {\n      filteredOptions.sort(function (a, b) {\n        return sort(a[SEARCH_MARK], b[SEARCH_MARK], search, fieldNames);\n      });\n    }\n\n    return limit > 0 ? filteredOptions.slice(0, limit) : filteredOptions;\n  }, [search, options, fieldNames, prefixCls, render, changeOnSelect, filter, sort, limit]);\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,WAAW,GAAG,6BAA6B;AAEtD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAChE,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACtB,OAAOF,OAAO,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;IACjC,OAAOC,MAAM,CAACD,GAAG,CAACF,KAAK,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,MAAM,CAACO,WAAW,CAAC,CAAC,CAAC;EACxE,CAAC,CAAC;AACJ,CAAC;AAED,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAClF,OAAOF,IAAI,CAACG,GAAG,CAAC,UAAUT,GAAG,EAAE;IAC7B,OAAOA,GAAG,CAACQ,UAAU,CAACV,KAAK,CAAC;EAC9B,CAAC,CAAC,CAACY,IAAI,CAAC,KAAK,CAAC;AAChB,CAAC;AAED,gBAAgB,UAAUf,MAAM,EAAEC,OAAO,EAAEY,UAAU,EAAED,SAAS,EAAEI,MAAM,EAAEC,cAAc,EAAE;EACxF,IAAIC,cAAc,GAAGF,MAAM,CAACG,MAAM;IAC9BA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGnB,aAAa,GAAGmB,cAAc;IACnEE,cAAc,GAAGJ,MAAM,CAACK,MAAM;IAC9BA,MAAM,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGX,aAAa,GAAGW,cAAc;IACnEE,aAAa,GAAGN,MAAM,CAACO,KAAK;IAC5BA,KAAK,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;IACrDE,IAAI,GAAGR,MAAM,CAACQ,IAAI;EACtB,OAAO3B,KAAK,CAAC4B,OAAO,CAAC,YAAY;IAC/B,IAAIC,eAAe,GAAG,EAAE;IAExB,IAAI,CAAC1B,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,SAAS2B,GAAGA,CAACC,IAAI,EAAEC,WAAW,EAAE;MAC9BD,IAAI,CAACE,OAAO,CAAC,UAAUC,MAAM,EAAE;QAC7B;QACA,IAAI,CAACP,IAAI,IAAID,KAAK,GAAG,CAAC,IAAIG,eAAe,CAACM,MAAM,IAAIT,KAAK,EAAE;UACzD;QACF;QAEA,IAAIU,oBAAoB,GAAG,EAAE,CAACC,MAAM,CAACtC,kBAAkB,CAACiC,WAAW,CAAC,EAAE,CAACE,MAAM,CAAC,CAAC;QAC/E,IAAII,QAAQ,GAAGJ,MAAM,CAAClB,UAAU,CAACsB,QAAQ,CAAC,CAAC,CAAC;;QAE5C;QAAK;QACL,CAACA,QAAQ,IAAIA,QAAQ,CAACH,MAAM,KAAK,CAAC;QAAI;QACtCf,cAAc,EAAE;UACd,IAAIE,MAAM,CAACnB,MAAM,EAAEiC,oBAAoB,EAAE;YACvC9B,KAAK,EAAEU,UAAU,CAACV;UACpB,CAAC,CAAC,EAAE;YACF,IAAIiC,cAAc;YAElBV,eAAe,CAACW,IAAI,CAAC1C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAGK,cAAc,GAAG,CAAC,CAAC,EAAE1C,eAAe,CAAC0C,cAAc,EAAEvB,UAAU,CAACV,KAAK,EAAEkB,MAAM,CAACrB,MAAM,EAAEiC,oBAAoB,EAAErB,SAAS,EAAEC,UAAU,CAAC,CAAC,EAAEnB,eAAe,CAAC0C,cAAc,EAAEtC,WAAW,EAAEmC,oBAAoB,CAAC,EAAEG,cAAc,CAAC,CAAC,CAAC;UAC/R;QACF;QAEA,IAAID,QAAQ,EAAE;UACZR,GAAG,CAACI,MAAM,CAAClB,UAAU,CAACsB,QAAQ,CAAC,EAAEF,oBAAoB,CAAC;QACxD;MACF,CAAC,CAAC;IACJ;IAEAN,GAAG,CAAC1B,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;;IAElB,IAAIuB,IAAI,EAAE;MACRE,eAAe,CAACF,IAAI,CAAC,UAAUc,CAAC,EAAEC,CAAC,EAAE;QACnC,OAAOf,IAAI,CAACc,CAAC,CAACxC,WAAW,CAAC,EAAEyC,CAAC,CAACzC,WAAW,CAAC,EAAEE,MAAM,EAAEa,UAAU,CAAC;MACjE,CAAC,CAAC;IACJ;IAEA,OAAOU,KAAK,GAAG,CAAC,GAAGG,eAAe,CAACc,KAAK,CAAC,CAAC,EAAEjB,KAAK,CAAC,GAAGG,eAAe;EACtE,CAAC,EAAE,CAAC1B,MAAM,EAAEC,OAAO,EAAEY,UAAU,EAAED,SAAS,EAAES,MAAM,EAAEJ,cAAc,EAAEE,MAAM,EAAEK,IAAI,EAAED,KAAK,CAAC,CAAC;AAC3F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}