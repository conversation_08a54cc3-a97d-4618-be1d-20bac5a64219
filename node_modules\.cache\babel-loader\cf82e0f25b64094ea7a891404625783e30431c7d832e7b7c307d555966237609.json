{"ast": null, "code": "var baseRepeat = require('./_baseRepeat'),\n  baseToString = require('./_baseToString'),\n  castSlice = require('./_castSlice'),\n  hasUnicode = require('./_hasUnicode'),\n  stringSize = require('./_stringSize'),\n  stringToArray = require('./_stringToArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars) ? castSlice(stringToArray(result), 0, length).join('') : result.slice(0, length);\n}\nmodule.exports = createPadding;", "map": {"version": 3, "names": ["baseRepeat", "require", "baseToString", "castSlice", "hasUnicode", "stringSize", "stringToArray", "nativeCeil", "Math", "ceil", "createPadding", "length", "chars", "undefined", "chars<PERSON><PERSON><PERSON>", "result", "join", "slice", "module", "exports"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/lodash/_createPadding.js"], "sourcesContent": ["var baseRepeat = require('./_baseRepeat'),\n    baseToString = require('./_baseToString'),\n    castSlice = require('./_castSlice'),\n    hasUnicode = require('./_hasUnicode'),\n    stringSize = require('./_stringSize'),\n    stringToArray = require('./_stringToArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars)\n    ? castSlice(stringToArray(result), 0, length).join('')\n    : result.slice(0, length);\n}\n\nmodule.exports = createPadding;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;EACnCG,UAAU,GAAGH,OAAO,CAAC,eAAe,CAAC;EACrCI,UAAU,GAAGJ,OAAO,CAAC,eAAe,CAAC;EACrCK,aAAa,GAAGL,OAAO,CAAC,kBAAkB,CAAC;;AAE/C;AACA,IAAIM,UAAU,GAAGC,IAAI,CAACC,IAAI;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACpCA,KAAK,GAAGA,KAAK,KAAKC,SAAS,GAAG,GAAG,GAAGX,YAAY,CAACU,KAAK,CAAC;EAEvD,IAAIE,WAAW,GAAGF,KAAK,CAACD,MAAM;EAC9B,IAAIG,WAAW,GAAG,CAAC,EAAE;IACnB,OAAOA,WAAW,GAAGd,UAAU,CAACY,KAAK,EAAED,MAAM,CAAC,GAAGC,KAAK;EACxD;EACA,IAAIG,MAAM,GAAGf,UAAU,CAACY,KAAK,EAAEL,UAAU,CAACI,MAAM,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAC,CAAC;EACtE,OAAOR,UAAU,CAACQ,KAAK,CAAC,GACpBT,SAAS,CAACG,aAAa,CAACS,MAAM,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,GACpDD,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEN,MAAM,CAAC;AAC7B;AAEAO,MAAM,CAACC,OAAO,GAAGT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}