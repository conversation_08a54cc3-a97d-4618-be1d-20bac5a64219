import { FinalPlan } from './final-plan.js';
import type { Parser } from './index.js';
import type { Result, TapError } from './result.js';
/**
 * The summary results provided in the `complete` event when the TAP
 * stream ends.
 */
export declare class FinalResults {
    ok: boolean;
    count: number;
    pass: number;
    fail: number;
    bailout: boolean | string;
    todo: number;
    skip: number;
    failures: TapError[];
    time: number | null;
    passes?: Result[];
    plan: FinalPlan;
    skips: (Result & {
        skip: true | string;
    })[];
    todos: (Result & {
        todo: true | string;
    })[];
    constructor(skipAll: boolean, parser: Parser);
}
//# sourceMappingURL=final-results.d.ts.map