{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nvar Paragraph = function Paragraph(props) {\n  var getWidth = function getWidth(index) {\n    var width = props.width,\n      _props$rows = props.rows,\n      rows = _props$rows === void 0 ? 2 : _props$rows;\n    if (Array.isArray(width)) {\n      return width[index];\n    } // last paragraph\n\n    if (rows - 1 === index) {\n      return width;\n    }\n    return undefined;\n  };\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    rows = props.rows;\n  var rowList = _toConsumableArray(Array(rows)).map(function (_, index) {\n    return (/*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: index,\n        style: {\n          width: getWidth(index)\n        }\n      })\n    );\n  });\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "Paragraph", "props", "getWidth", "index", "width", "_props$rows", "rows", "Array", "isArray", "undefined", "prefixCls", "className", "style", "rowList", "map", "_", "createElement", "key"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/skeleton/Paragraph.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\n\nvar Paragraph = function Paragraph(props) {\n  var getWidth = function getWidth(index) {\n    var width = props.width,\n        _props$rows = props.rows,\n        rows = _props$rows === void 0 ? 2 : _props$rows;\n\n    if (Array.isArray(width)) {\n      return width[index];\n    } // last paragraph\n\n\n    if (rows - 1 === index) {\n      return width;\n    }\n\n    return undefined;\n  };\n\n  var prefixCls = props.prefixCls,\n      className = props.className,\n      style = props.style,\n      rows = props.rows;\n\n  var rowList = _toConsumableArray(Array(rows)).map(function (_, index) {\n    return (\n      /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"li\", {\n        key: index,\n        style: {\n          width: getWidth(index)\n        }\n      })\n    );\n  });\n\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\n\nexport default Paragraph;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AAEnC,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;IACtC,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;MACnBC,WAAW,GAAGJ,KAAK,CAACK,IAAI;MACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAEnD,IAAIE,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACD,KAAK,CAAC;IACrB,CAAC,CAAC;;IAGF,IAAIG,IAAI,GAAG,CAAC,KAAKH,KAAK,EAAE;MACtB,OAAOC,KAAK;IACd;IAEA,OAAOK,SAAS;EAClB,CAAC;EAED,IAAIC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBN,IAAI,GAAGL,KAAK,CAACK,IAAI;EAErB,IAAIO,OAAO,GAAGhB,kBAAkB,CAACU,KAAK,CAACD,IAAI,CAAC,CAAC,CAACQ,GAAG,CAAC,UAAUC,CAAC,EAAEZ,KAAK,EAAE;IACpE,QACE;MACA;MACAL,KAAK,CAACkB,aAAa,CAAC,IAAI,EAAE;QACxBC,GAAG,EAAEd,KAAK;QACVS,KAAK,EAAE;UACLR,KAAK,EAAEF,QAAQ,CAACC,KAAK;QACvB;MACF,CAAC;IAAC;EAEN,CAAC,CAAC;EAEF,OAAO,aAAaL,KAAK,CAACkB,aAAa,CAAC,IAAI,EAAE;IAC5CL,SAAS,EAAEZ,UAAU,CAACW,SAAS,EAAEC,SAAS,CAAC;IAC3CC,KAAK,EAAEA;EACT,CAAC,EAAEC,OAAO,CAAC;AACb,CAAC;AAED,eAAeb,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}