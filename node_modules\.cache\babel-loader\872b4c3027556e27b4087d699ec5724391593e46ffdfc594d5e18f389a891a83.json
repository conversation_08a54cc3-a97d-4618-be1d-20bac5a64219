{"ast": null, "code": "import React, { Component, createRef } from 'react';\nimport { class<PERSON>ames, ObjectUtils, Ripple, CSSTransition, Portal, DomHandler, OverlayService, ZIndexUtils, ConnectedOverlayScrollHandler, FilterUtils, tip } from 'primereact/core';\nimport { VirtualScroller } from 'primereact/virtualscroller';\nimport PrimeReact from 'primereact/api';\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n  return _assertThisInitialized(self);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _createSuper$2(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct$2();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct$2() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar DropdownItem = /*#__PURE__*/function (_Component) {\n  _inherits(DropdownItem, _Component);\n  var _super = _createSuper$2(DropdownItem);\n  function DropdownItem(props) {\n    var _this;\n    _classCallCheck(this, DropdownItem);\n    _this = _super.call(this, props);\n    _this.onClick = _this.onClick.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n  _createClass(DropdownItem, [{\n    key: \"onClick\",\n    value: function onClick(event) {\n      if (this.props.onClick) {\n        this.props.onClick({\n          originalEvent: event,\n          option: this.props.option\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var className = classNames('p-dropdown-item', {\n        'p-highlight': this.props.selected,\n        'p-disabled': this.props.disabled,\n        'p-dropdown-item-empty': !this.props.label || this.props.label.length === 0\n      }, this.props.option.className);\n      var content = this.props.template ? ObjectUtils.getJSXElement(this.props.template, this.props.option) : this.props.label;\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: className,\n        onClick: this.onClick,\n        \"aria-label\": this.props.label,\n        key: this.props.label,\n        role: \"option\",\n        \"aria-selected\": this.props.selected\n      }, content, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }]);\n  return DropdownItem;\n}(Component);\n_defineProperty(DropdownItem, \"defaultProps\", {\n  option: null,\n  label: null,\n  template: null,\n  selected: false,\n  disabled: false,\n  onClick: null\n});\nfunction ownKeys$1(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread$1(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys$1(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys$1(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _createSuper$1(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct$1();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct$1() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar DropdownPanelComponent = /*#__PURE__*/function (_Component) {\n  _inherits(DropdownPanelComponent, _Component);\n  var _super = _createSuper$1(DropdownPanelComponent);\n  function DropdownPanelComponent(props) {\n    var _this;\n    _classCallCheck(this, DropdownPanelComponent);\n    _this = _super.call(this, props);\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onFilterInputChange = _this.onFilterInputChange.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n  _createClass(DropdownPanelComponent, [{\n    key: \"onEnter\",\n    value: function onEnter() {\n      var _this2 = this;\n      this.props.onEnter(function () {\n        if (_this2.virtualScrollerRef) {\n          var selectedIndex = _this2.props.getSelectedOptionIndex();\n          if (selectedIndex !== -1) {\n            _this2.virtualScrollerRef.scrollToIndex(selectedIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      var _this3 = this;\n      this.props.onEntered(function () {\n        if (_this3.props.filter && _this3.props.filterInputAutoFocus) {\n          _this3.filterInput.focus();\n        }\n      });\n    }\n  }, {\n    key: \"onFilterInputChange\",\n    value: function onFilterInputChange(event) {\n      if (this.virtualScrollerRef) {\n        this.virtualScrollerRef.scrollToIndex(0);\n      }\n      this.props.onFilterInputChange && this.props.onFilterInputChange(event);\n    }\n  }, {\n    key: \"isEmptyFilter\",\n    value: function isEmptyFilter() {\n      return !(this.props.visibleOptions && this.props.visibleOptions.length) && this.props.hasFilter();\n    }\n  }, {\n    key: \"renderGroupChildren\",\n    value: function renderGroupChildren(optionGroup) {\n      var _this4 = this;\n      var groupChildren = this.props.getOptionGroupChildren(optionGroup);\n      return groupChildren.map(function (option, j) {\n        var optionLabel = _this4.props.getOptionLabel(option);\n        var optionKey = j + '_' + _this4.props.getOptionRenderKey(option);\n        var disabled = _this4.props.isOptionDisabled(option);\n        return /*#__PURE__*/React.createElement(DropdownItem, {\n          key: optionKey,\n          label: optionLabel,\n          option: option,\n          template: _this4.props.itemTemplate,\n          selected: _this4.props.isSelected(option),\n          disabled: disabled,\n          onClick: _this4.props.onOptionClick\n        });\n      });\n    }\n  }, {\n    key: \"renderEmptyMessage\",\n    value: function renderEmptyMessage(emptyMessage) {\n      var message = ObjectUtils.getJSXElement(emptyMessage, this.props);\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: \"p-dropdown-empty-message\"\n      }, message);\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(option, index) {\n      if (this.props.optionGroupLabel) {\n        var groupContent = this.props.optionGroupTemplate ? ObjectUtils.getJSXElement(this.props.optionGroupTemplate, option, index) : this.props.getOptionGroupLabel(option);\n        var groupChildrenContent = this.renderGroupChildren(option);\n        var key = index + '_' + this.props.getOptionGroupRenderKey(option);\n        return /*#__PURE__*/React.createElement(React.Fragment, {\n          key: key\n        }, /*#__PURE__*/React.createElement(\"li\", {\n          className: \"p-dropdown-item-group\"\n        }, groupContent), groupChildrenContent);\n      } else {\n        var optionLabel = this.props.getOptionLabel(option);\n        var optionKey = index + '_' + this.props.getOptionRenderKey(option);\n        var disabled = this.props.isOptionDisabled(option);\n        return /*#__PURE__*/React.createElement(DropdownItem, {\n          key: optionKey,\n          label: optionLabel,\n          option: option,\n          template: this.props.itemTemplate,\n          selected: this.props.isSelected(option),\n          disabled: disabled,\n          onClick: this.props.onOptionClick\n        });\n      }\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this5 = this;\n      if (this.props.visibleOptions && this.props.visibleOptions.length) {\n        return this.props.visibleOptions.map(function (option, index) {\n          return _this5.renderItem(option, index);\n        });\n      } else if (this.props.hasFilter()) {\n        return this.renderEmptyMessage(this.props.emptyFilterMessage);\n      }\n      return this.renderEmptyMessage(this.props.emptyMessage);\n    }\n  }, {\n    key: \"renderFilterClearIcon\",\n    value: function renderFilterClearIcon() {\n      var _this6 = this;\n      if (this.props.showFilterClear && this.props.filterValue) {\n        return /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-filter-clear-icon pi pi-times\",\n          onClick: function onClick() {\n            return _this6.props.onFilterClearIconClick(function () {\n              return _this6.filterInput.focus();\n            });\n          }\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderFilter\",\n    value: function renderFilter() {\n      var _this7 = this;\n      if (this.props.filter) {\n        var clearIcon = this.renderFilterClearIcon();\n        var containerClassName = classNames('p-dropdown-filter-container', {\n          'p-dropdown-clearable-filter': !!clearIcon\n        });\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-dropdown-header\"\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          className: containerClassName\n        }, /*#__PURE__*/React.createElement(\"input\", {\n          ref: function ref(el) {\n            return _this7.filterInput = el;\n          },\n          type: \"text\",\n          autoComplete: \"off\",\n          className: \"p-dropdown-filter p-inputtext p-component\",\n          placeholder: this.props.filterPlaceholder,\n          onKeyDown: this.props.onFilterInputKeyDown,\n          onChange: this.onFilterInputChange,\n          value: this.props.filterValue\n        }), clearIcon, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-filter-icon pi pi-search\"\n        })));\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this8 = this;\n      if (this.props.virtualScrollerOptions) {\n        var virtualScrollerProps = _objectSpread$1(_objectSpread$1({}, this.props.virtualScrollerOptions), {\n          style: _objectSpread$1(_objectSpread$1({}, this.props.virtualScrollerOptions.style), {\n            height: this.props.scrollHeight\n          }),\n          className: classNames('p-dropdown-items-wrapper', this.props.virtualScrollerOptions.className),\n          items: this.props.visibleOptions,\n          onLazyLoad: function onLazyLoad(event) {\n            return _this8.props.virtualScrollerOptions.onLazyLoad(_objectSpread$1(_objectSpread$1({}, event), {\n              filter: _this8.props.filterValue\n            }));\n          },\n          itemTemplate: function itemTemplate(item, options) {\n            return item && _this8.renderItem(item, options.index);\n          },\n          contentTemplate: function contentTemplate(options) {\n            var className = classNames('p-dropdown-items', options.className);\n            var content = _this8.isEmptyFilter() ? _this8.renderEmptyMessage() : options.children;\n            return /*#__PURE__*/React.createElement(\"ul\", {\n              ref: options.ref,\n              className: className,\n              role: \"listbox\"\n            }, content);\n          }\n        });\n        return /*#__PURE__*/React.createElement(VirtualScroller, _extends({\n          ref: function ref(el) {\n            return _this8.virtualScrollerRef = el;\n          }\n        }, virtualScrollerProps));\n      } else {\n        var items = this.renderItems();\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-dropdown-items-wrapper\",\n          style: {\n            maxHeight: this.props.scrollHeight || 'auto'\n          }\n        }, /*#__PURE__*/React.createElement(\"ul\", {\n          className: \"p-dropdown-items\",\n          role: \"listbox\"\n        }, items));\n      }\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-dropdown-panel p-component', this.props.panelClassName);\n      var filter = this.renderFilter();\n      var content = this.renderContent();\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.props.forwardRef,\n        classNames: \"p-connected-overlay\",\n        in: this.props.in,\n        timeout: {\n          enter: 120,\n          exit: 100\n        },\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntering: this.props.onEntering,\n        onEntered: this.onEntered,\n        onExit: this.props.onExit,\n        onExited: this.props.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.props.forwardRef,\n        className: className,\n        style: this.props.panelStyle,\n        onClick: this.props.onClick\n      }, filter, content));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n  return DropdownPanelComponent;\n}(Component);\nvar DropdownPanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(DropdownPanelComponent, _extends({\n    forwardRef: ref\n  }, props));\n});\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (!it) {\n    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n      var F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function e(_e) {\n          throw _e;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var normalCompletion = true,\n    didErr = false,\n    err;\n  return {\n    s: function s() {\n      it = it.call(o);\n    },\n    n: function n() {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function e(_e2) {\n      didErr = true;\n      err = _e2;\n    },\n    f: function f() {\n      try {\n        if (!normalCompletion && it.return != null) it.return();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar Dropdown = /*#__PURE__*/function (_Component) {\n  _inherits(Dropdown, _Component);\n  var _super = _createSuper(Dropdown);\n  function Dropdown(props) {\n    var _this;\n    _classCallCheck(this, Dropdown);\n    _this = _super.call(this, props);\n    _this.state = {\n      filter: '',\n      focused: false,\n      overlayVisible: false\n    };\n    _this.onClick = _this.onClick.bind(_assertThisInitialized(_this));\n    _this.onInputFocus = _this.onInputFocus.bind(_assertThisInitialized(_this));\n    _this.onInputBlur = _this.onInputBlur.bind(_assertThisInitialized(_this));\n    _this.onInputKeyDown = _this.onInputKeyDown.bind(_assertThisInitialized(_this));\n    _this.onEditableInputChange = _this.onEditableInputChange.bind(_assertThisInitialized(_this));\n    _this.onEditableInputFocus = _this.onEditableInputFocus.bind(_assertThisInitialized(_this));\n    _this.onOptionClick = _this.onOptionClick.bind(_assertThisInitialized(_this));\n    _this.onFilterInputChange = _this.onFilterInputChange.bind(_assertThisInitialized(_this));\n    _this.onFilterInputKeyDown = _this.onFilterInputKeyDown.bind(_assertThisInitialized(_this));\n    _this.onFilterClearIconClick = _this.onFilterClearIconClick.bind(_assertThisInitialized(_this));\n    _this.onPanelClick = _this.onPanelClick.bind(_assertThisInitialized(_this));\n    _this.onOverlayEnter = _this.onOverlayEnter.bind(_assertThisInitialized(_this));\n    _this.onOverlayEntered = _this.onOverlayEntered.bind(_assertThisInitialized(_this));\n    _this.onOverlayExit = _this.onOverlayExit.bind(_assertThisInitialized(_this));\n    _this.onOverlayExited = _this.onOverlayExited.bind(_assertThisInitialized(_this));\n    _this.resetFilter = _this.resetFilter.bind(_assertThisInitialized(_this));\n    _this.clear = _this.clear.bind(_assertThisInitialized(_this));\n    _this.hasFilter = _this.hasFilter.bind(_assertThisInitialized(_this));\n    _this.getOptionLabel = _this.getOptionLabel.bind(_assertThisInitialized(_this));\n    _this.getOptionRenderKey = _this.getOptionRenderKey.bind(_assertThisInitialized(_this));\n    _this.isOptionDisabled = _this.isOptionDisabled.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupChildren = _this.getOptionGroupChildren.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupLabel = _this.getOptionGroupLabel.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupRenderKey = _this.getOptionGroupRenderKey.bind(_assertThisInitialized(_this));\n    _this.getSelectedOptionIndex = _this.getSelectedOptionIndex.bind(_assertThisInitialized(_this));\n    _this.isSelected = _this.isSelected.bind(_assertThisInitialized(_this));\n    _this.overlayRef = /*#__PURE__*/createRef();\n    _this.inputRef = /*#__PURE__*/createRef(_this.props.inputRef);\n    return _this;\n  }\n  _createClass(Dropdown, [{\n    key: \"onClick\",\n    value: function onClick(event) {\n      if (this.props.disabled) {\n        return;\n      }\n      if (DomHandler.hasClass(event.target, 'p-dropdown-clear-icon') || event.target.tagName === 'INPUT') {\n        return;\n      } else if (!this.overlayRef.current || !(this.overlayRef.current && this.overlayRef.current.contains(event.target))) {\n        this.focusInput.focus();\n        if (this.state.overlayVisible) {\n          this.hideOverlay();\n        } else {\n          this.showOverlay();\n        }\n      }\n    }\n  }, {\n    key: \"onInputFocus\",\n    value: function onInputFocus(event) {\n      var _this2 = this;\n      event.persist();\n      if (this.props.showOnFocus && !this.state.overlayVisible) {\n        this.showOverlay();\n      }\n      this.setState({\n        focused: true\n      }, function () {\n        if (_this2.props.onFocus) {\n          _this2.props.onFocus(event);\n        }\n      });\n    }\n  }, {\n    key: \"onInputBlur\",\n    value: function onInputBlur(event) {\n      var _this3 = this;\n      event.persist();\n      this.setState({\n        focused: false\n      }, function () {\n        if (_this3.props.onBlur) {\n          _this3.props.onBlur(event);\n        }\n      });\n    }\n  }, {\n    key: \"onPanelClick\",\n    value: function onPanelClick(event) {\n      OverlayService.emit('overlay-click', {\n        originalEvent: event,\n        target: this.container\n      });\n    }\n  }, {\n    key: \"onInputKeyDown\",\n    value: function onInputKeyDown(event) {\n      switch (event.which) {\n        //down\n        case 40:\n          this.onDownKey(event);\n          break;\n        //up\n\n        case 38:\n          this.onUpKey(event);\n          break;\n        //space\n\n        case 32:\n          if (this.state.overlayVisible) this.hideOverlay();else this.showOverlay();\n          event.preventDefault();\n          break;\n        //enter\n\n        case 13:\n          this.hideOverlay();\n          event.preventDefault();\n          break;\n        //escape and tab\n\n        case 27:\n        case 9:\n          this.hideOverlay();\n          break;\n        default:\n          this.search(event);\n          break;\n      }\n    }\n  }, {\n    key: \"onFilterInputKeyDown\",\n    value: function onFilterInputKeyDown(event) {\n      switch (event.which) {\n        //down\n        case 40:\n          this.onDownKey(event);\n          break;\n        //up\n\n        case 38:\n          this.onUpKey(event);\n          break;\n        //enter and escape\n\n        case 13:\n        case 27:\n          this.hideOverlay();\n          event.preventDefault();\n          break;\n      }\n    }\n  }, {\n    key: \"onUpKey\",\n    value: function onUpKey(event) {\n      var visibleOptions = this.getVisibleOptions();\n      if (visibleOptions) {\n        var prevOption = this.findPrevOption(this.getSelectedOptionIndex());\n        if (prevOption) {\n          this.selectItem({\n            originalEvent: event,\n            option: prevOption\n          });\n        }\n      }\n      event.preventDefault();\n    }\n  }, {\n    key: \"onDownKey\",\n    value: function onDownKey(event) {\n      var visibleOptions = this.getVisibleOptions();\n      if (visibleOptions) {\n        if (!this.state.overlayVisible && event.altKey) {\n          this.showOverlay();\n        } else {\n          var nextOption = this.findNextOption(this.getSelectedOptionIndex());\n          if (nextOption) {\n            this.selectItem({\n              originalEvent: event,\n              option: nextOption\n            });\n          }\n        }\n      }\n      event.preventDefault();\n    }\n  }, {\n    key: \"findNextOption\",\n    value: function findNextOption(index) {\n      var visibleOptions = this.getVisibleOptions();\n      if (this.props.optionGroupLabel) {\n        var groupIndex = index === -1 ? 0 : index.group;\n        var optionIndex = index === -1 ? -1 : index.option;\n        var option = this.findNextOptionInList(this.getOptionGroupChildren(visibleOptions[groupIndex]), optionIndex);\n        if (option) return option;else if (groupIndex + 1 !== visibleOptions.length) return this.findNextOption({\n          group: groupIndex + 1,\n          option: -1\n        });else return null;\n      } else {\n        return this.findNextOptionInList(visibleOptions, index);\n      }\n    }\n  }, {\n    key: \"findNextOptionInList\",\n    value: function findNextOptionInList(list, index) {\n      var i = index + 1;\n      if (i === list.length) {\n        return null;\n      }\n      var option = list[i];\n      if (this.isOptionDisabled(option)) return this.findNextOptionInList(i);else return option;\n    }\n  }, {\n    key: \"findPrevOption\",\n    value: function findPrevOption(index) {\n      if (index === -1) {\n        return null;\n      }\n      var visibleOptions = this.getVisibleOptions();\n      if (this.props.optionGroupLabel) {\n        var groupIndex = index.group;\n        var optionIndex = index.option;\n        var option = this.findPrevOptionInList(this.getOptionGroupChildren(visibleOptions[groupIndex]), optionIndex);\n        if (option) return option;else if (groupIndex > 0) return this.findPrevOption({\n          group: groupIndex - 1,\n          option: this.getOptionGroupChildren(visibleOptions[groupIndex - 1]).length\n        });else return null;\n      } else {\n        return this.findPrevOptionInList(visibleOptions, index);\n      }\n    }\n  }, {\n    key: \"findPrevOptionInList\",\n    value: function findPrevOptionInList(list, index) {\n      var i = index - 1;\n      if (i < 0) {\n        return null;\n      }\n      var option = list[i];\n      if (this.isOptionDisabled(option)) return this.findPrevOption(i);else return option;\n    }\n  }, {\n    key: \"search\",\n    value: function search(event) {\n      var _this4 = this;\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      var char = event.key;\n      this.previousSearchChar = this.currentSearchChar;\n      this.currentSearchChar = char;\n      if (this.previousSearchChar === this.currentSearchChar) this.searchValue = this.currentSearchChar;else this.searchValue = this.searchValue ? this.searchValue + char : char;\n      if (this.searchValue) {\n        var searchIndex = this.getSelectedOptionIndex();\n        var newOption = this.props.optionGroupLabel ? this.searchOptionInGroup(searchIndex) : this.searchOption(++searchIndex);\n        if (newOption) {\n          this.selectItem({\n            originalEvent: event,\n            option: newOption\n          });\n          this.selectedOptionUpdated = true;\n        }\n      }\n      this.searchTimeout = setTimeout(function () {\n        _this4.searchValue = null;\n      }, 250);\n    }\n  }, {\n    key: \"searchOption\",\n    value: function searchOption(index) {\n      var option;\n      if (this.searchValue) {\n        var visibleOptions = this.getVisibleOptions();\n        option = this.searchOptionInRange(index, visibleOptions.length);\n        if (!option) {\n          option = this.searchOptionInRange(0, index);\n        }\n      }\n      return option;\n    }\n  }, {\n    key: \"searchOptionInRange\",\n    value: function searchOptionInRange(start, end) {\n      var visibleOptions = this.getVisibleOptions();\n      for (var i = start; i < end; i++) {\n        var opt = visibleOptions[i];\n        if (this.matchesSearchValue(opt)) {\n          return opt;\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"searchOptionInGroup\",\n    value: function searchOptionInGroup(index) {\n      var searchIndex = index === -1 ? {\n        group: 0,\n        option: -1\n      } : index;\n      var visibleOptions = this.getVisibleOptions();\n      for (var i = searchIndex.group; i < visibleOptions.length; i++) {\n        var groupOptions = this.getOptionGroupChildren(visibleOptions[i]);\n        for (var j = searchIndex.group === i ? searchIndex.option + 1 : 0; j < groupOptions.length; j++) {\n          if (this.matchesSearchValue(groupOptions[j])) {\n            return groupOptions[j];\n          }\n        }\n      }\n      for (var _i = 0; _i <= searchIndex.group; _i++) {\n        var _groupOptions = this.getOptionGroupChildren(visibleOptions[_i]);\n        for (var _j = 0; _j < (searchIndex.group === _i ? searchIndex.option : _groupOptions.length); _j++) {\n          if (this.matchesSearchValue(_groupOptions[_j])) {\n            return _groupOptions[_j];\n          }\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"matchesSearchValue\",\n    value: function matchesSearchValue(option) {\n      var label = this.getOptionLabel(option).toLocaleLowerCase(this.props.filterLocale);\n      return label.startsWith(this.searchValue.toLocaleLowerCase(this.props.filterLocale));\n    }\n  }, {\n    key: \"onEditableInputChange\",\n    value: function onEditableInputChange(event) {\n      if (this.props.onChange) {\n        this.props.onChange({\n          originalEvent: event.originalEvent,\n          value: event.target.value,\n          stopPropagation: function stopPropagation() {},\n          preventDefault: function preventDefault() {},\n          target: {\n            name: this.props.name,\n            id: this.props.id,\n            value: event.target.value\n          }\n        });\n      }\n    }\n  }, {\n    key: \"onEditableInputFocus\",\n    value: function onEditableInputFocus(event) {\n      var _this5 = this;\n      event.persist();\n      this.setState({\n        focused: true\n      }, function () {\n        _this5.hideOverlay();\n        if (_this5.props.onFocus) {\n          _this5.props.onFocus(event);\n        }\n      });\n    }\n  }, {\n    key: \"onOptionClick\",\n    value: function onOptionClick(event) {\n      var option = event.option;\n      if (!option.disabled) {\n        this.selectItem(event);\n        this.focusInput.focus();\n      }\n      this.hideOverlay();\n    }\n  }, {\n    key: \"onFilterInputChange\",\n    value: function onFilterInputChange(event) {\n      var _this6 = this;\n      var filter = event.target.value;\n      this.setState({\n        filter: filter\n      }, function () {\n        if (_this6.props.onFilter) {\n          _this6.props.onFilter({\n            originalEvent: event,\n            filter: filter\n          });\n        }\n      });\n    }\n  }, {\n    key: \"onFilterClearIconClick\",\n    value: function onFilterClearIconClick(callback) {\n      this.resetFilter(callback);\n    }\n  }, {\n    key: \"resetFilter\",\n    value: function resetFilter(callback) {\n      var _this7 = this;\n      var filter = '';\n      this.setState({\n        filter: filter\n      }, function () {\n        _this7.props.onFilter && _this7.props.onFilter({\n          filter: filter\n        });\n        callback && callback();\n      });\n    }\n  }, {\n    key: \"clear\",\n    value: function clear(event) {\n      if (this.props.onChange) {\n        this.props.onChange({\n          originalEvent: event,\n          value: undefined,\n          stopPropagation: function stopPropagation() {},\n          preventDefault: function preventDefault() {},\n          target: {\n            name: this.props.name,\n            id: this.props.id,\n            value: undefined\n          }\n        });\n      }\n      this.updateEditableLabel();\n    }\n  }, {\n    key: \"selectItem\",\n    value: function selectItem(event) {\n      var currentSelectedOption = this.getSelectedOption();\n      if (currentSelectedOption !== event.option) {\n        this.updateEditableLabel(event.option);\n        var optionValue = this.getOptionValue(event.option);\n        if (this.props.onChange) {\n          this.props.onChange({\n            originalEvent: event.originalEvent,\n            value: optionValue,\n            stopPropagation: function stopPropagation() {},\n            preventDefault: function preventDefault() {},\n            target: {\n              name: this.props.name,\n              id: this.props.id,\n              value: optionValue\n            }\n          });\n        }\n      }\n    }\n  }, {\n    key: \"getSelectedOption\",\n    value: function getSelectedOption() {\n      var index = this.getSelectedOptionIndex();\n      var visibleOptions = this.getVisibleOptions();\n      return index !== -1 ? this.props.optionGroupLabel ? this.getOptionGroupChildren(visibleOptions[index.group])[index.option] : visibleOptions[index] : null;\n    }\n  }, {\n    key: \"getSelectedOptionIndex\",\n    value: function getSelectedOptionIndex() {\n      var visibleOptions = this.getVisibleOptions();\n      if (this.props.value != null && visibleOptions) {\n        if (this.props.optionGroupLabel) {\n          for (var i = 0; i < visibleOptions.length; i++) {\n            var selectedOptionIndex = this.findOptionIndexInList(this.props.value, this.getOptionGroupChildren(visibleOptions[i]));\n            if (selectedOptionIndex !== -1) {\n              return {\n                group: i,\n                option: selectedOptionIndex\n              };\n            }\n          }\n        } else {\n          return this.findOptionIndexInList(this.props.value, visibleOptions);\n        }\n      }\n      return -1;\n    }\n  }, {\n    key: \"findOptionIndexInList\",\n    value: function findOptionIndexInList(value, list) {\n      var key = this.equalityKey();\n      for (var i = 0; i < list.length; i++) {\n        if (ObjectUtils.equals(value, this.getOptionValue(list[i]), key)) {\n          return i;\n        }\n      }\n      return -1;\n    }\n  }, {\n    key: \"isSelected\",\n    value: function isSelected(option) {\n      return ObjectUtils.equals(this.props.value, this.getOptionValue(option), this.equalityKey());\n    }\n  }, {\n    key: \"equalityKey\",\n    value: function equalityKey() {\n      return this.props.optionValue ? null : this.props.dataKey;\n    }\n  }, {\n    key: \"showOverlay\",\n    value: function showOverlay() {\n      this.setState({\n        overlayVisible: true\n      });\n    }\n  }, {\n    key: \"hideOverlay\",\n    value: function hideOverlay() {\n      this.setState({\n        overlayVisible: false\n      });\n    }\n  }, {\n    key: \"onOverlayEnter\",\n    value: function onOverlayEnter(callback) {\n      ZIndexUtils.set('overlay', this.overlayRef.current);\n      this.alignOverlay();\n      callback && callback();\n    }\n  }, {\n    key: \"onOverlayEntered\",\n    value: function onOverlayEntered(callback) {\n      this.bindDocumentClickListener();\n      this.bindScrollListener();\n      this.bindResizeListener();\n      callback && callback();\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onOverlayExit\",\n    value: function onOverlayExit() {\n      this.unbindDocumentClickListener();\n      this.unbindScrollListener();\n      this.unbindResizeListener();\n    }\n  }, {\n    key: \"onOverlayExited\",\n    value: function onOverlayExited() {\n      if (this.props.filter && this.props.resetFilterOnHide) {\n        this.resetFilter();\n      }\n      ZIndexUtils.clear(this.overlayRef.current);\n      this.props.onHide && this.props.onHide();\n    }\n  }, {\n    key: \"alignOverlay\",\n    value: function alignOverlay() {\n      DomHandler.alignOverlay(this.overlayRef.current, this.input.parentElement, this.props.appendTo || PrimeReact.appendTo);\n    }\n  }, {\n    key: \"scrollInView\",\n    value: function scrollInView() {\n      var highlightItem = DomHandler.findSingle(this.overlayRef.current, 'li.p-highlight');\n      if (highlightItem) {\n        highlightItem.scrollIntoView({\n          block: 'nearest',\n          inline: 'start'\n        });\n      }\n    }\n  }, {\n    key: \"bindDocumentClickListener\",\n    value: function bindDocumentClickListener() {\n      var _this8 = this;\n      if (!this.documentClickListener) {\n        this.documentClickListener = function (event) {\n          if (_this8.state.overlayVisible && _this8.isOutsideClicked(event)) {\n            _this8.hideOverlay();\n          }\n        };\n        document.addEventListener('click', this.documentClickListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentClickListener\",\n    value: function unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        document.removeEventListener('click', this.documentClickListener);\n        this.documentClickListener = null;\n      }\n    }\n  }, {\n    key: \"bindScrollListener\",\n    value: function bindScrollListener() {\n      var _this9 = this;\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.container, function () {\n          if (_this9.state.overlayVisible) {\n            _this9.hideOverlay();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n  }, {\n    key: \"unbindScrollListener\",\n    value: function unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n  }, {\n    key: \"bindResizeListener\",\n    value: function bindResizeListener() {\n      var _this10 = this;\n      if (!this.resizeListener) {\n        this.resizeListener = function () {\n          if (_this10.state.overlayVisible && !DomHandler.isTouchDevice()) {\n            _this10.hideOverlay();\n          }\n        };\n        window.addEventListener('resize', this.resizeListener);\n      }\n    }\n  }, {\n    key: \"unbindResizeListener\",\n    value: function unbindResizeListener() {\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = null;\n      }\n    }\n  }, {\n    key: \"isOutsideClicked\",\n    value: function isOutsideClicked(event) {\n      return this.container && !(this.container.isSameNode(event.target) || this.isClearClicked(event) || this.container.contains(event.target) || this.overlayRef && this.overlayRef.current.contains(event.target));\n    }\n  }, {\n    key: \"isClearClicked\",\n    value: function isClearClicked(event) {\n      return DomHandler.hasClass(event.target, 'p-dropdown-clear-icon');\n    }\n  }, {\n    key: \"updateEditableLabel\",\n    value: function updateEditableLabel(option) {\n      if (this.input) {\n        this.input.value = option ? this.getOptionLabel(option) : this.props.value || '';\n      }\n    }\n  }, {\n    key: \"hasFilter\",\n    value: function hasFilter() {\n      return this.state.filter && this.state.filter.trim().length > 0;\n    }\n  }, {\n    key: \"getOptionLabel\",\n    value: function getOptionLabel(option) {\n      return this.props.optionLabel ? ObjectUtils.resolveFieldData(option, this.props.optionLabel) : option && option['label'] !== undefined ? option['label'] : option;\n    }\n  }, {\n    key: \"getOptionValue\",\n    value: function getOptionValue(option) {\n      return this.props.optionValue ? ObjectUtils.resolveFieldData(option, this.props.optionValue) : option && option['value'] !== undefined ? option['value'] : option;\n    }\n  }, {\n    key: \"getOptionRenderKey\",\n    value: function getOptionRenderKey(option) {\n      return this.props.dataKey ? ObjectUtils.resolveFieldData(option, this.props.dataKey) : this.getOptionLabel(option);\n    }\n  }, {\n    key: \"isOptionDisabled\",\n    value: function isOptionDisabled(option) {\n      if (this.props.optionDisabled) {\n        return ObjectUtils.isFunction(this.props.optionDisabled) ? this.props.optionDisabled(option) : ObjectUtils.resolveFieldData(option, this.props.optionDisabled);\n      }\n      return option && option['disabled'] !== undefined ? option['disabled'] : false;\n    }\n  }, {\n    key: \"getOptionGroupRenderKey\",\n    value: function getOptionGroupRenderKey(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupLabel);\n    }\n  }, {\n    key: \"getOptionGroupLabel\",\n    value: function getOptionGroupLabel(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupLabel);\n    }\n  }, {\n    key: \"getOptionGroupChildren\",\n    value: function getOptionGroupChildren(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupChildren);\n    }\n  }, {\n    key: \"checkValidity\",\n    value: function checkValidity() {\n      return this.inputRef.current.checkValidity();\n    }\n  }, {\n    key: \"getVisibleOptions\",\n    value: function getVisibleOptions() {\n      if (this.hasFilter()) {\n        var filterValue = this.state.filter.trim().toLocaleLowerCase(this.props.filterLocale);\n        var searchFields = this.props.filterBy ? this.props.filterBy.split(',') : [this.props.optionLabel || 'label'];\n        if (this.props.optionGroupLabel) {\n          var filteredGroups = [];\n          var _iterator = _createForOfIteratorHelper(this.props.options),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var optgroup = _step.value;\n              var filteredSubOptions = FilterUtils.filter(this.getOptionGroupChildren(optgroup), searchFields, filterValue, this.props.filterMatchMode, this.props.filterLocale);\n              if (filteredSubOptions && filteredSubOptions.length) {\n                filteredGroups.push(_objectSpread(_objectSpread({}, optgroup), {\n                  items: filteredSubOptions\n                }));\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n          return filteredGroups;\n        } else {\n          return FilterUtils.filter(this.props.options, searchFields, filterValue, this.props.filterMatchMode, this.props.filterLocale);\n        }\n      } else {\n        return this.props.options;\n      }\n    }\n  }, {\n    key: \"updateInputField\",\n    value: function updateInputField() {\n      if (this.props.editable && this.input) {\n        var selectedOption = this.getSelectedOption();\n        var label = selectedOption ? this.getOptionLabel(selectedOption) : null;\n        var value = label || this.props.value || '';\n        this.input.value = value;\n      }\n    }\n  }, {\n    key: \"updateInputRef\",\n    value: function updateInputRef() {\n      var ref = this.props.inputRef;\n      if (ref) {\n        if (typeof ref === 'function') {\n          ref(this.inputRef.current);\n        } else {\n          ref.current = this.inputRef.current;\n        }\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateInputRef();\n      if (this.props.autoFocus && this.focusInput) {\n        this.focusInput.focus();\n      }\n      if (this.props.tooltip) {\n        this.renderTooltip();\n      }\n      this.updateInputField();\n      this.inputRef.current.selectedIndex = 1;\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindDocumentClickListener();\n      this.unbindResizeListener();\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.tooltip) {\n        this.tooltip.destroy();\n        this.tooltip = null;\n      }\n      if (this.hideTimeout) {\n        clearTimeout(this.hideTimeout);\n        this.hideTimeout = null;\n      }\n      ZIndexUtils.clear(this.overlayRef.current);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.state.overlayVisible) {\n        if (this.props.filter) {\n          this.alignOverlay();\n        }\n        if (prevProps.value !== this.props.value) {\n          this.scrollInView();\n        }\n      }\n      if (prevProps.tooltip !== this.props.tooltip || prevProps.tooltipOptions !== this.props.tooltipOptions) {\n        if (this.tooltip) this.tooltip.update(_objectSpread({\n          content: this.props.tooltip\n        }, this.props.tooltipOptions || {}));else this.renderTooltip();\n      }\n      if (this.state.filter && (!this.props.options || this.props.options.length === 0)) {\n        this.setState({\n          filter: ''\n        });\n      }\n      this.updateInputField();\n      this.inputRef.current.selectedIndex = 1;\n    }\n  }, {\n    key: \"renderHiddenSelect\",\n    value: function renderHiddenSelect(selectedOption) {\n      var placeHolderOption = /*#__PURE__*/React.createElement(\"option\", {\n        value: \"\"\n      }, this.props.placeholder);\n      var option = selectedOption ? /*#__PURE__*/React.createElement(\"option\", {\n        value: selectedOption.value\n      }, this.getOptionLabel(selectedOption)) : null;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-hidden-accessible p-dropdown-hidden-select\"\n      }, /*#__PURE__*/React.createElement(\"select\", {\n        ref: this.inputRef,\n        required: this.props.required,\n        name: this.props.name,\n        tabIndex: -1,\n        \"aria-hidden\": \"true\"\n      }, placeHolderOption, option));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      this.tooltip = tip({\n        target: this.container,\n        content: this.props.tooltip,\n        options: this.props.tooltipOptions\n      });\n    }\n  }, {\n    key: \"renderKeyboardHelper\",\n    value: function renderKeyboardHelper() {\n      var _this11 = this;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-hidden-accessible\"\n      }, /*#__PURE__*/React.createElement(\"input\", {\n        ref: function ref(el) {\n          return _this11.focusInput = el;\n        },\n        id: this.props.inputId,\n        type: \"text\",\n        readOnly: true,\n        \"aria-haspopup\": \"listbox\",\n        onFocus: this.onInputFocus,\n        onBlur: this.onInputBlur,\n        onKeyDown: this.onInputKeyDown,\n        disabled: this.props.disabled,\n        tabIndex: this.props.tabIndex,\n        \"aria-label\": this.props.ariaLabel,\n        \"aria-labelledby\": this.props.ariaLabelledBy\n      }));\n    }\n  }, {\n    key: \"renderLabel\",\n    value: function renderLabel(selectedOption) {\n      var _this12 = this;\n      var label = selectedOption ? this.getOptionLabel(selectedOption) : null;\n      if (this.props.editable) {\n        var value = label || this.props.value || '';\n        return /*#__PURE__*/React.createElement(\"input\", {\n          ref: function ref(el) {\n            return _this12.input = el;\n          },\n          type: \"text\",\n          defaultValue: value,\n          className: \"p-dropdown-label p-inputtext\",\n          disabled: this.props.disabled,\n          placeholder: this.props.placeholder,\n          maxLength: this.props.maxLength,\n          onInput: this.onEditableInputChange,\n          onFocus: this.onEditableInputFocus,\n          onBlur: this.onInputBlur,\n          \"aria-label\": this.props.ariaLabel,\n          \"aria-labelledby\": this.props.ariaLabelledBy,\n          \"aria-haspopup\": \"listbox\"\n        });\n      } else {\n        var className = classNames('p-dropdown-label p-inputtext', {\n          'p-placeholder': label === null && this.props.placeholder,\n          'p-dropdown-label-empty': label === null && !this.props.placeholder\n        });\n        var content = this.props.valueTemplate ? ObjectUtils.getJSXElement(this.props.valueTemplate, selectedOption, this.props) : label || this.props.placeholder || 'empty';\n        return /*#__PURE__*/React.createElement(\"span\", {\n          ref: function ref(el) {\n            return _this12.input = el;\n          },\n          className: className\n        }, content);\n      }\n    }\n  }, {\n    key: \"renderClearIcon\",\n    value: function renderClearIcon() {\n      if (this.props.value != null && this.props.showClear && !this.props.disabled) {\n        return /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-clear-icon pi pi-times\",\n          onClick: this.clear\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderDropdownIcon\",\n    value: function renderDropdownIcon() {\n      var _this13 = this;\n      var iconClassName = classNames('p-dropdown-trigger-icon p-clickable', this.props.dropdownIcon);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this13.trigger = el;\n        },\n        className: \"p-dropdown-trigger\",\n        role: \"button\",\n        \"aria-haspopup\": \"listbox\",\n        \"aria-expanded\": this.state.overlayVisible\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this14 = this;\n      var className = classNames('p-dropdown p-component p-inputwrapper', this.props.className, {\n        'p-disabled': this.props.disabled,\n        'p-focus': this.state.focused,\n        'p-dropdown-clearable': this.props.showClear && !this.props.disabled,\n        'p-inputwrapper-filled': this.props.value,\n        'p-inputwrapper-focus': this.state.focused || this.state.overlayVisible\n      });\n      var visibleOptions = this.getVisibleOptions();\n      var selectedOption = this.getSelectedOption();\n      var hiddenSelect = this.renderHiddenSelect(selectedOption);\n      var keyboardHelper = this.renderKeyboardHelper();\n      var labelElement = this.renderLabel(selectedOption);\n      var dropdownIcon = this.renderDropdownIcon();\n      var clearIcon = this.renderClearIcon();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: this.props.id,\n        ref: function ref(el) {\n          return _this14.container = el;\n        },\n        className: className,\n        style: this.props.style,\n        onClick: this.onClick,\n        onMouseDown: this.props.onMouseDown,\n        onContextMenu: this.props.onContextMenu\n      }, keyboardHelper, hiddenSelect, labelElement, clearIcon, dropdownIcon, /*#__PURE__*/React.createElement(DropdownPanel, _extends({\n        ref: this.overlayRef,\n        visibleOptions: visibleOptions\n      }, this.props, {\n        onClick: this.onPanelClick,\n        onOptionClick: this.onOptionClick,\n        filterValue: this.state.filter,\n        hasFilter: this.hasFilter,\n        onFilterClearIconClick: this.onFilterClearIconClick,\n        onFilterInputKeyDown: this.onFilterInputKeyDown,\n        onFilterInputChange: this.onFilterInputChange,\n        getOptionLabel: this.getOptionLabel,\n        getOptionRenderKey: this.getOptionRenderKey,\n        isOptionDisabled: this.isOptionDisabled,\n        getOptionGroupChildren: this.getOptionGroupChildren,\n        getOptionGroupLabel: this.getOptionGroupLabel,\n        getOptionGroupRenderKey: this.getOptionGroupRenderKey,\n        isSelected: this.isSelected,\n        getSelectedOptionIndex: this.getSelectedOptionIndex,\n        in: this.state.overlayVisible,\n        onEnter: this.onOverlayEnter,\n        onEntered: this.onOverlayEntered,\n        onExit: this.onOverlayExit,\n        onExited: this.onOverlayExited\n      })));\n    }\n  }]);\n  return Dropdown;\n}(Component);\n_defineProperty(Dropdown, \"defaultProps\", {\n  id: null,\n  inputRef: null,\n  name: null,\n  value: null,\n  options: null,\n  optionLabel: null,\n  optionValue: null,\n  optionDisabled: null,\n  optionGroupLabel: null,\n  optionGroupChildren: null,\n  optionGroupTemplate: null,\n  valueTemplate: null,\n  itemTemplate: null,\n  style: null,\n  className: null,\n  virtualScrollerOptions: null,\n  scrollHeight: '200px',\n  filter: false,\n  filterBy: null,\n  filterMatchMode: 'contains',\n  filterPlaceholder: null,\n  filterLocale: undefined,\n  emptyMessage: 'No records found',\n  emptyFilterMessage: 'No results found',\n  editable: false,\n  placeholder: null,\n  required: false,\n  disabled: false,\n  appendTo: null,\n  tabIndex: null,\n  autoFocus: false,\n  filterInputAutoFocus: true,\n  resetFilterOnHide: false,\n  showFilterClear: false,\n  panelClassName: null,\n  panelStyle: null,\n  dataKey: null,\n  inputId: null,\n  showClear: false,\n  maxLength: null,\n  tooltip: null,\n  tooltipOptions: null,\n  ariaLabel: null,\n  ariaLabelledBy: null,\n  transitionOptions: null,\n  dropdownIcon: 'pi pi-chevron-down',\n  showOnFocus: false,\n  onChange: null,\n  onFocus: null,\n  onBlur: null,\n  onMouseDown: null,\n  onContextMenu: null,\n  onShow: null,\n  onHide: null,\n  onFilter: null\n});\nexport { Dropdown };", "map": {"version": 3, "names": ["React", "Component", "createRef", "classNames", "ObjectUtils", "<PERSON><PERSON><PERSON>", "CSSTransition", "Portal", "<PERSON><PERSON><PERSON><PERSON>", "OverlayService", "ZIndexUtils", "ConnectedOverlayScrollHandler", "FilterUtils", "tip", "VirtualScroller", "PrimeReact", "_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_createClass", "protoProps", "staticProps", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "constructor", "value", "_typeof", "obj", "Symbol", "iterator", "_possibleConstructorReturn", "_getPrototypeOf", "getPrototypeOf", "_defineProperty", "_createSuper$2", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct$2", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "DropdownItem", "_Component", "_super", "_this", "onClick", "bind", "event", "originalEvent", "option", "render", "className", "selected", "disabled", "label", "content", "template", "getJSXElement", "createElement", "role", "ownKeys$1", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "_objectSpread$1", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_createSuper$1", "_isNativeReflectConstruct$1", "DropdownPanelComponent", "onEnter", "onEntered", "onFilterInputChange", "_this2", "virtualScrollerRef", "selectedIndex", "getSelectedOptionIndex", "scrollToIndex", "_this3", "filterInputAutoFocus", "filterInput", "focus", "isEmptyFilter", "visibleOptions", "<PERSON><PERSON><PERSON>er", "renderGroupChildren", "optionGroup", "_this4", "groupChildren", "getOptionGroupChildren", "map", "j", "optionLabel", "getOptionLabel", "optionKey", "getOptionRenderKey", "isOptionDisabled", "itemTemplate", "isSelected", "onOptionClick", "renderEmptyMessage", "emptyMessage", "message", "renderItem", "index", "optionGroupLabel", "groupContent", "optionGroupTemplate", "getOptionGroupLabel", "groupChildrenContent", "getOptionGroupRenderKey", "Fragment", "renderItems", "_this5", "emptyFilterMessage", "renderFilterClearIcon", "_this6", "showFilterClear", "filterValue", "onFilterClearIconClick", "renderFilter", "_this7", "clearIcon", "containerClassName", "ref", "el", "type", "autoComplete", "placeholder", "filterPlaceholder", "onKeyDown", "onFilterInputKeyDown", "onChange", "renderContent", "_this8", "virtualScrollerOptions", "virtualScrollerProps", "style", "height", "scrollHeight", "items", "onLazyLoad", "item", "options", "contentTemplate", "children", "maxHeight", "renderElement", "panelClassName", "nodeRef", "forwardRef", "in", "timeout", "enter", "exit", "transitionOptions", "unmountOnExit", "onEntering", "onExit", "onExited", "panelStyle", "element", "appendTo", "DropdownPanel", "ownKeys", "_objectSpread", "_createForOfIteratorHelper", "allowArrayLike", "it", "Array", "isArray", "_unsupportedIterableToArray", "F", "s", "n", "done", "_e", "f", "normalCompletion", "didErr", "err", "step", "next", "_e2", "return", "minLen", "_arrayLikeToArray", "toString", "slice", "name", "from", "test", "arr", "len", "arr2", "_createSuper", "_isNativeReflectConstruct", "Dropdown", "state", "focused", "overlayVisible", "onInputFocus", "onInputBlur", "onInputKeyDown", "onEditableInputChange", "onEditableInputFocus", "onPanelClick", "onOverlayEnter", "onOverlayEntered", "onOverlayExit", "onOverlayExited", "resetFilter", "clear", "overlayRef", "inputRef", "hasClass", "tagName", "current", "contains", "focusInput", "hideOverlay", "showOverlay", "persist", "showOnFocus", "setState", "onFocus", "onBlur", "emit", "container", "which", "onDownKey", "onUpKey", "preventDefault", "search", "getVisibleOptions", "prevOption", "findPrevOption", "selectItem", "altKey", "nextOption", "findNextOption", "groupIndex", "group", "optionIndex", "findNextOptionInList", "list", "findPrevOptionInList", "searchTimeout", "clearTimeout", "char", "previousSearchChar", "currentSearchChar", "searchValue", "searchIndex", "newOption", "searchOptionInGroup", "searchOption", "selectedOptionUpdated", "setTimeout", "searchOptionInRange", "start", "end", "opt", "matchesSearchValue", "groupOptions", "_i", "_groupOptions", "_j", "toLocaleLowerCase", "filterLocale", "startsWith", "stopPropagation", "id", "onFilter", "callback", "undefined", "updateEditableLabel", "currentSelectedOption", "getSelectedOption", "optionValue", "getOptionValue", "selectedOptionIndex", "findOptionIndexInList", "equalityKey", "equals", "dataKey", "set", "alignOverlay", "bindDocumentClickListener", "bindScrollListener", "bindResizeListener", "onShow", "unbindDocumentClickListener", "unbindScrollListener", "unbindResizeListener", "resetFilterOnHide", "onHide", "input", "parentElement", "scrollInView", "highlightItem", "findSingle", "scrollIntoView", "block", "inline", "documentClickListener", "isOutsideClicked", "document", "addEventListener", "removeEventListener", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "_this10", "resizeListener", "isTouchDevice", "window", "isSameNode", "isClearClicked", "trim", "resolveFieldData", "optionDisabled", "isFunction", "optionGroupChildren", "checkValidity", "searchFields", "filterBy", "split", "filteredGroups", "_iterator", "_step", "optgroup", "filteredSubOptions", "filterMatchMode", "updateInputField", "editable", "selectedOption", "updateInputRef", "componentDidMount", "autoFocus", "tooltip", "renderTooltip", "componentWillUnmount", "destroy", "hideTimeout", "componentDidUpdate", "prevProps", "tooltipOptions", "update", "renderHiddenSelect", "placeHolderOption", "required", "tabIndex", "renderKeyboardHelper", "_this11", "inputId", "readOnly", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "renderLabel", "_this12", "defaultValue", "max<PERSON><PERSON><PERSON>", "onInput", "valueTemplate", "renderClearIcon", "showClear", "renderDropdownIcon", "_this13", "iconClassName", "dropdownIcon", "trigger", "_this14", "hiddenSelect", "keyboard<PERSON>elper", "labelElement", "onMouseDown", "onContextMenu"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/primereact/dropdown/dropdown.esm.js"], "sourcesContent": ["import React, { Component, createRef } from 'react';\nimport { class<PERSON>ames, ObjectUtils, Ripple, CSSTransition, Portal, DomHandler, OverlayService, ZIndexUtils, ConnectedOverlayScrollHandler, FilterUtils, tip } from 'primereact/core';\nimport { VirtualScroller } from 'primereact/virtualscroller';\nimport PrimeReact from 'primereact/api';\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _createSuper$2(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct$2(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct$2() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar DropdownItem = /*#__PURE__*/function (_Component) {\n  _inherits(DropdownItem, _Component);\n\n  var _super = _createSuper$2(DropdownItem);\n\n  function DropdownItem(props) {\n    var _this;\n\n    _classCallCheck(this, DropdownItem);\n\n    _this = _super.call(this, props);\n    _this.onClick = _this.onClick.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(DropdownItem, [{\n    key: \"onClick\",\n    value: function onClick(event) {\n      if (this.props.onClick) {\n        this.props.onClick({\n          originalEvent: event,\n          option: this.props.option\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var className = classNames('p-dropdown-item', {\n        'p-highlight': this.props.selected,\n        'p-disabled': this.props.disabled,\n        'p-dropdown-item-empty': !this.props.label || this.props.label.length === 0\n      }, this.props.option.className);\n      var content = this.props.template ? ObjectUtils.getJSXElement(this.props.template, this.props.option) : this.props.label;\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: className,\n        onClick: this.onClick,\n        \"aria-label\": this.props.label,\n        key: this.props.label,\n        role: \"option\",\n        \"aria-selected\": this.props.selected\n      }, content, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  }]);\n\n  return DropdownItem;\n}(Component);\n\n_defineProperty(DropdownItem, \"defaultProps\", {\n  option: null,\n  label: null,\n  template: null,\n  selected: false,\n  disabled: false,\n  onClick: null\n});\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _createSuper$1(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct$1(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct$1() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nvar DropdownPanelComponent = /*#__PURE__*/function (_Component) {\n  _inherits(DropdownPanelComponent, _Component);\n\n  var _super = _createSuper$1(DropdownPanelComponent);\n\n  function DropdownPanelComponent(props) {\n    var _this;\n\n    _classCallCheck(this, DropdownPanelComponent);\n\n    _this = _super.call(this, props);\n    _this.onEnter = _this.onEnter.bind(_assertThisInitialized(_this));\n    _this.onEntered = _this.onEntered.bind(_assertThisInitialized(_this));\n    _this.onFilterInputChange = _this.onFilterInputChange.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(DropdownPanelComponent, [{\n    key: \"onEnter\",\n    value: function onEnter() {\n      var _this2 = this;\n\n      this.props.onEnter(function () {\n        if (_this2.virtualScrollerRef) {\n          var selectedIndex = _this2.props.getSelectedOptionIndex();\n\n          if (selectedIndex !== -1) {\n            _this2.virtualScrollerRef.scrollToIndex(selectedIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"onEntered\",\n    value: function onEntered() {\n      var _this3 = this;\n\n      this.props.onEntered(function () {\n        if (_this3.props.filter && _this3.props.filterInputAutoFocus) {\n          _this3.filterInput.focus();\n        }\n      });\n    }\n  }, {\n    key: \"onFilterInputChange\",\n    value: function onFilterInputChange(event) {\n      if (this.virtualScrollerRef) {\n        this.virtualScrollerRef.scrollToIndex(0);\n      }\n\n      this.props.onFilterInputChange && this.props.onFilterInputChange(event);\n    }\n  }, {\n    key: \"isEmptyFilter\",\n    value: function isEmptyFilter() {\n      return !(this.props.visibleOptions && this.props.visibleOptions.length) && this.props.hasFilter();\n    }\n  }, {\n    key: \"renderGroupChildren\",\n    value: function renderGroupChildren(optionGroup) {\n      var _this4 = this;\n\n      var groupChildren = this.props.getOptionGroupChildren(optionGroup);\n      return groupChildren.map(function (option, j) {\n        var optionLabel = _this4.props.getOptionLabel(option);\n\n        var optionKey = j + '_' + _this4.props.getOptionRenderKey(option);\n\n        var disabled = _this4.props.isOptionDisabled(option);\n\n        return /*#__PURE__*/React.createElement(DropdownItem, {\n          key: optionKey,\n          label: optionLabel,\n          option: option,\n          template: _this4.props.itemTemplate,\n          selected: _this4.props.isSelected(option),\n          disabled: disabled,\n          onClick: _this4.props.onOptionClick\n        });\n      });\n    }\n  }, {\n    key: \"renderEmptyMessage\",\n    value: function renderEmptyMessage(emptyMessage) {\n      var message = ObjectUtils.getJSXElement(emptyMessage, this.props);\n      return /*#__PURE__*/React.createElement(\"li\", {\n        className: \"p-dropdown-empty-message\"\n      }, message);\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(option, index) {\n      if (this.props.optionGroupLabel) {\n        var groupContent = this.props.optionGroupTemplate ? ObjectUtils.getJSXElement(this.props.optionGroupTemplate, option, index) : this.props.getOptionGroupLabel(option);\n        var groupChildrenContent = this.renderGroupChildren(option);\n        var key = index + '_' + this.props.getOptionGroupRenderKey(option);\n        return /*#__PURE__*/React.createElement(React.Fragment, {\n          key: key\n        }, /*#__PURE__*/React.createElement(\"li\", {\n          className: \"p-dropdown-item-group\"\n        }, groupContent), groupChildrenContent);\n      } else {\n        var optionLabel = this.props.getOptionLabel(option);\n        var optionKey = index + '_' + this.props.getOptionRenderKey(option);\n        var disabled = this.props.isOptionDisabled(option);\n        return /*#__PURE__*/React.createElement(DropdownItem, {\n          key: optionKey,\n          label: optionLabel,\n          option: option,\n          template: this.props.itemTemplate,\n          selected: this.props.isSelected(option),\n          disabled: disabled,\n          onClick: this.props.onOptionClick\n        });\n      }\n    }\n  }, {\n    key: \"renderItems\",\n    value: function renderItems() {\n      var _this5 = this;\n\n      if (this.props.visibleOptions && this.props.visibleOptions.length) {\n        return this.props.visibleOptions.map(function (option, index) {\n          return _this5.renderItem(option, index);\n        });\n      } else if (this.props.hasFilter()) {\n        return this.renderEmptyMessage(this.props.emptyFilterMessage);\n      }\n\n      return this.renderEmptyMessage(this.props.emptyMessage);\n    }\n  }, {\n    key: \"renderFilterClearIcon\",\n    value: function renderFilterClearIcon() {\n      var _this6 = this;\n\n      if (this.props.showFilterClear && this.props.filterValue) {\n        return /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-filter-clear-icon pi pi-times\",\n          onClick: function onClick() {\n            return _this6.props.onFilterClearIconClick(function () {\n              return _this6.filterInput.focus();\n            });\n          }\n        });\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderFilter\",\n    value: function renderFilter() {\n      var _this7 = this;\n\n      if (this.props.filter) {\n        var clearIcon = this.renderFilterClearIcon();\n        var containerClassName = classNames('p-dropdown-filter-container', {\n          'p-dropdown-clearable-filter': !!clearIcon\n        });\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-dropdown-header\"\n        }, /*#__PURE__*/React.createElement(\"div\", {\n          className: containerClassName\n        }, /*#__PURE__*/React.createElement(\"input\", {\n          ref: function ref(el) {\n            return _this7.filterInput = el;\n          },\n          type: \"text\",\n          autoComplete: \"off\",\n          className: \"p-dropdown-filter p-inputtext p-component\",\n          placeholder: this.props.filterPlaceholder,\n          onKeyDown: this.props.onFilterInputKeyDown,\n          onChange: this.onFilterInputChange,\n          value: this.props.filterValue\n        }), clearIcon, /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-filter-icon pi pi-search\"\n        })));\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderContent\",\n    value: function renderContent() {\n      var _this8 = this;\n\n      if (this.props.virtualScrollerOptions) {\n        var virtualScrollerProps = _objectSpread$1(_objectSpread$1({}, this.props.virtualScrollerOptions), {\n          style: _objectSpread$1(_objectSpread$1({}, this.props.virtualScrollerOptions.style), {\n            height: this.props.scrollHeight\n          }),\n          className: classNames('p-dropdown-items-wrapper', this.props.virtualScrollerOptions.className),\n          items: this.props.visibleOptions,\n          onLazyLoad: function onLazyLoad(event) {\n            return _this8.props.virtualScrollerOptions.onLazyLoad(_objectSpread$1(_objectSpread$1({}, event), {\n              filter: _this8.props.filterValue\n            }));\n          },\n          itemTemplate: function itemTemplate(item, options) {\n            return item && _this8.renderItem(item, options.index);\n          },\n          contentTemplate: function contentTemplate(options) {\n            var className = classNames('p-dropdown-items', options.className);\n            var content = _this8.isEmptyFilter() ? _this8.renderEmptyMessage() : options.children;\n            return /*#__PURE__*/React.createElement(\"ul\", {\n              ref: options.ref,\n              className: className,\n              role: \"listbox\"\n            }, content);\n          }\n        });\n\n        return /*#__PURE__*/React.createElement(VirtualScroller, _extends({\n          ref: function ref(el) {\n            return _this8.virtualScrollerRef = el;\n          }\n        }, virtualScrollerProps));\n      } else {\n        var items = this.renderItems();\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"p-dropdown-items-wrapper\",\n          style: {\n            maxHeight: this.props.scrollHeight || 'auto'\n          }\n        }, /*#__PURE__*/React.createElement(\"ul\", {\n          className: \"p-dropdown-items\",\n          role: \"listbox\"\n        }, items));\n      }\n    }\n  }, {\n    key: \"renderElement\",\n    value: function renderElement() {\n      var className = classNames('p-dropdown-panel p-component', this.props.panelClassName);\n      var filter = this.renderFilter();\n      var content = this.renderContent();\n      return /*#__PURE__*/React.createElement(CSSTransition, {\n        nodeRef: this.props.forwardRef,\n        classNames: \"p-connected-overlay\",\n        in: this.props.in,\n        timeout: {\n          enter: 120,\n          exit: 100\n        },\n        options: this.props.transitionOptions,\n        unmountOnExit: true,\n        onEnter: this.onEnter,\n        onEntering: this.props.onEntering,\n        onEntered: this.onEntered,\n        onExit: this.props.onExit,\n        onExited: this.props.onExited\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.props.forwardRef,\n        className: className,\n        style: this.props.panelStyle,\n        onClick: this.props.onClick\n      }, filter, content));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var element = this.renderElement();\n      return /*#__PURE__*/React.createElement(Portal, {\n        element: element,\n        appendTo: this.props.appendTo\n      });\n    }\n  }]);\n\n  return DropdownPanelComponent;\n}(Component);\n\nvar DropdownPanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(DropdownPanelComponent, _extends({\n    forwardRef: ref\n  }, props));\n});\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar Dropdown = /*#__PURE__*/function (_Component) {\n  _inherits(Dropdown, _Component);\n\n  var _super = _createSuper(Dropdown);\n\n  function Dropdown(props) {\n    var _this;\n\n    _classCallCheck(this, Dropdown);\n\n    _this = _super.call(this, props);\n    _this.state = {\n      filter: '',\n      focused: false,\n      overlayVisible: false\n    };\n    _this.onClick = _this.onClick.bind(_assertThisInitialized(_this));\n    _this.onInputFocus = _this.onInputFocus.bind(_assertThisInitialized(_this));\n    _this.onInputBlur = _this.onInputBlur.bind(_assertThisInitialized(_this));\n    _this.onInputKeyDown = _this.onInputKeyDown.bind(_assertThisInitialized(_this));\n    _this.onEditableInputChange = _this.onEditableInputChange.bind(_assertThisInitialized(_this));\n    _this.onEditableInputFocus = _this.onEditableInputFocus.bind(_assertThisInitialized(_this));\n    _this.onOptionClick = _this.onOptionClick.bind(_assertThisInitialized(_this));\n    _this.onFilterInputChange = _this.onFilterInputChange.bind(_assertThisInitialized(_this));\n    _this.onFilterInputKeyDown = _this.onFilterInputKeyDown.bind(_assertThisInitialized(_this));\n    _this.onFilterClearIconClick = _this.onFilterClearIconClick.bind(_assertThisInitialized(_this));\n    _this.onPanelClick = _this.onPanelClick.bind(_assertThisInitialized(_this));\n    _this.onOverlayEnter = _this.onOverlayEnter.bind(_assertThisInitialized(_this));\n    _this.onOverlayEntered = _this.onOverlayEntered.bind(_assertThisInitialized(_this));\n    _this.onOverlayExit = _this.onOverlayExit.bind(_assertThisInitialized(_this));\n    _this.onOverlayExited = _this.onOverlayExited.bind(_assertThisInitialized(_this));\n    _this.resetFilter = _this.resetFilter.bind(_assertThisInitialized(_this));\n    _this.clear = _this.clear.bind(_assertThisInitialized(_this));\n    _this.hasFilter = _this.hasFilter.bind(_assertThisInitialized(_this));\n    _this.getOptionLabel = _this.getOptionLabel.bind(_assertThisInitialized(_this));\n    _this.getOptionRenderKey = _this.getOptionRenderKey.bind(_assertThisInitialized(_this));\n    _this.isOptionDisabled = _this.isOptionDisabled.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupChildren = _this.getOptionGroupChildren.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupLabel = _this.getOptionGroupLabel.bind(_assertThisInitialized(_this));\n    _this.getOptionGroupRenderKey = _this.getOptionGroupRenderKey.bind(_assertThisInitialized(_this));\n    _this.getSelectedOptionIndex = _this.getSelectedOptionIndex.bind(_assertThisInitialized(_this));\n    _this.isSelected = _this.isSelected.bind(_assertThisInitialized(_this));\n    _this.overlayRef = /*#__PURE__*/createRef();\n    _this.inputRef = /*#__PURE__*/createRef(_this.props.inputRef);\n    return _this;\n  }\n\n  _createClass(Dropdown, [{\n    key: \"onClick\",\n    value: function onClick(event) {\n      if (this.props.disabled) {\n        return;\n      }\n\n      if (DomHandler.hasClass(event.target, 'p-dropdown-clear-icon') || event.target.tagName === 'INPUT') {\n        return;\n      } else if (!this.overlayRef.current || !(this.overlayRef.current && this.overlayRef.current.contains(event.target))) {\n        this.focusInput.focus();\n\n        if (this.state.overlayVisible) {\n          this.hideOverlay();\n        } else {\n          this.showOverlay();\n        }\n      }\n    }\n  }, {\n    key: \"onInputFocus\",\n    value: function onInputFocus(event) {\n      var _this2 = this;\n\n      event.persist();\n\n      if (this.props.showOnFocus && !this.state.overlayVisible) {\n        this.showOverlay();\n      }\n\n      this.setState({\n        focused: true\n      }, function () {\n        if (_this2.props.onFocus) {\n          _this2.props.onFocus(event);\n        }\n      });\n    }\n  }, {\n    key: \"onInputBlur\",\n    value: function onInputBlur(event) {\n      var _this3 = this;\n\n      event.persist();\n      this.setState({\n        focused: false\n      }, function () {\n        if (_this3.props.onBlur) {\n          _this3.props.onBlur(event);\n        }\n      });\n    }\n  }, {\n    key: \"onPanelClick\",\n    value: function onPanelClick(event) {\n      OverlayService.emit('overlay-click', {\n        originalEvent: event,\n        target: this.container\n      });\n    }\n  }, {\n    key: \"onInputKeyDown\",\n    value: function onInputKeyDown(event) {\n      switch (event.which) {\n        //down\n        case 40:\n          this.onDownKey(event);\n          break;\n        //up\n\n        case 38:\n          this.onUpKey(event);\n          break;\n        //space\n\n        case 32:\n          if (this.state.overlayVisible) this.hideOverlay();else this.showOverlay();\n          event.preventDefault();\n          break;\n        //enter\n\n        case 13:\n          this.hideOverlay();\n          event.preventDefault();\n          break;\n        //escape and tab\n\n        case 27:\n        case 9:\n          this.hideOverlay();\n          break;\n\n        default:\n          this.search(event);\n          break;\n      }\n    }\n  }, {\n    key: \"onFilterInputKeyDown\",\n    value: function onFilterInputKeyDown(event) {\n      switch (event.which) {\n        //down\n        case 40:\n          this.onDownKey(event);\n          break;\n        //up\n\n        case 38:\n          this.onUpKey(event);\n          break;\n        //enter and escape\n\n        case 13:\n        case 27:\n          this.hideOverlay();\n          event.preventDefault();\n          break;\n      }\n    }\n  }, {\n    key: \"onUpKey\",\n    value: function onUpKey(event) {\n      var visibleOptions = this.getVisibleOptions();\n\n      if (visibleOptions) {\n        var prevOption = this.findPrevOption(this.getSelectedOptionIndex());\n\n        if (prevOption) {\n          this.selectItem({\n            originalEvent: event,\n            option: prevOption\n          });\n        }\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    key: \"onDownKey\",\n    value: function onDownKey(event) {\n      var visibleOptions = this.getVisibleOptions();\n\n      if (visibleOptions) {\n        if (!this.state.overlayVisible && event.altKey) {\n          this.showOverlay();\n        } else {\n          var nextOption = this.findNextOption(this.getSelectedOptionIndex());\n\n          if (nextOption) {\n            this.selectItem({\n              originalEvent: event,\n              option: nextOption\n            });\n          }\n        }\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    key: \"findNextOption\",\n    value: function findNextOption(index) {\n      var visibleOptions = this.getVisibleOptions();\n\n      if (this.props.optionGroupLabel) {\n        var groupIndex = index === -1 ? 0 : index.group;\n        var optionIndex = index === -1 ? -1 : index.option;\n        var option = this.findNextOptionInList(this.getOptionGroupChildren(visibleOptions[groupIndex]), optionIndex);\n        if (option) return option;else if (groupIndex + 1 !== visibleOptions.length) return this.findNextOption({\n          group: groupIndex + 1,\n          option: -1\n        });else return null;\n      } else {\n        return this.findNextOptionInList(visibleOptions, index);\n      }\n    }\n  }, {\n    key: \"findNextOptionInList\",\n    value: function findNextOptionInList(list, index) {\n      var i = index + 1;\n\n      if (i === list.length) {\n        return null;\n      }\n\n      var option = list[i];\n      if (this.isOptionDisabled(option)) return this.findNextOptionInList(i);else return option;\n    }\n  }, {\n    key: \"findPrevOption\",\n    value: function findPrevOption(index) {\n      if (index === -1) {\n        return null;\n      }\n\n      var visibleOptions = this.getVisibleOptions();\n\n      if (this.props.optionGroupLabel) {\n        var groupIndex = index.group;\n        var optionIndex = index.option;\n        var option = this.findPrevOptionInList(this.getOptionGroupChildren(visibleOptions[groupIndex]), optionIndex);\n        if (option) return option;else if (groupIndex > 0) return this.findPrevOption({\n          group: groupIndex - 1,\n          option: this.getOptionGroupChildren(visibleOptions[groupIndex - 1]).length\n        });else return null;\n      } else {\n        return this.findPrevOptionInList(visibleOptions, index);\n      }\n    }\n  }, {\n    key: \"findPrevOptionInList\",\n    value: function findPrevOptionInList(list, index) {\n      var i = index - 1;\n\n      if (i < 0) {\n        return null;\n      }\n\n      var option = list[i];\n      if (this.isOptionDisabled(option)) return this.findPrevOption(i);else return option;\n    }\n  }, {\n    key: \"search\",\n    value: function search(event) {\n      var _this4 = this;\n\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n\n      var char = event.key;\n      this.previousSearchChar = this.currentSearchChar;\n      this.currentSearchChar = char;\n      if (this.previousSearchChar === this.currentSearchChar) this.searchValue = this.currentSearchChar;else this.searchValue = this.searchValue ? this.searchValue + char : char;\n\n      if (this.searchValue) {\n        var searchIndex = this.getSelectedOptionIndex();\n        var newOption = this.props.optionGroupLabel ? this.searchOptionInGroup(searchIndex) : this.searchOption(++searchIndex);\n\n        if (newOption) {\n          this.selectItem({\n            originalEvent: event,\n            option: newOption\n          });\n          this.selectedOptionUpdated = true;\n        }\n      }\n\n      this.searchTimeout = setTimeout(function () {\n        _this4.searchValue = null;\n      }, 250);\n    }\n  }, {\n    key: \"searchOption\",\n    value: function searchOption(index) {\n      var option;\n\n      if (this.searchValue) {\n        var visibleOptions = this.getVisibleOptions();\n        option = this.searchOptionInRange(index, visibleOptions.length);\n\n        if (!option) {\n          option = this.searchOptionInRange(0, index);\n        }\n      }\n\n      return option;\n    }\n  }, {\n    key: \"searchOptionInRange\",\n    value: function searchOptionInRange(start, end) {\n      var visibleOptions = this.getVisibleOptions();\n\n      for (var i = start; i < end; i++) {\n        var opt = visibleOptions[i];\n\n        if (this.matchesSearchValue(opt)) {\n          return opt;\n        }\n      }\n\n      return null;\n    }\n  }, {\n    key: \"searchOptionInGroup\",\n    value: function searchOptionInGroup(index) {\n      var searchIndex = index === -1 ? {\n        group: 0,\n        option: -1\n      } : index;\n      var visibleOptions = this.getVisibleOptions();\n\n      for (var i = searchIndex.group; i < visibleOptions.length; i++) {\n        var groupOptions = this.getOptionGroupChildren(visibleOptions[i]);\n\n        for (var j = searchIndex.group === i ? searchIndex.option + 1 : 0; j < groupOptions.length; j++) {\n          if (this.matchesSearchValue(groupOptions[j])) {\n            return groupOptions[j];\n          }\n        }\n      }\n\n      for (var _i = 0; _i <= searchIndex.group; _i++) {\n        var _groupOptions = this.getOptionGroupChildren(visibleOptions[_i]);\n\n        for (var _j = 0; _j < (searchIndex.group === _i ? searchIndex.option : _groupOptions.length); _j++) {\n          if (this.matchesSearchValue(_groupOptions[_j])) {\n            return _groupOptions[_j];\n          }\n        }\n      }\n\n      return null;\n    }\n  }, {\n    key: \"matchesSearchValue\",\n    value: function matchesSearchValue(option) {\n      var label = this.getOptionLabel(option).toLocaleLowerCase(this.props.filterLocale);\n      return label.startsWith(this.searchValue.toLocaleLowerCase(this.props.filterLocale));\n    }\n  }, {\n    key: \"onEditableInputChange\",\n    value: function onEditableInputChange(event) {\n      if (this.props.onChange) {\n        this.props.onChange({\n          originalEvent: event.originalEvent,\n          value: event.target.value,\n          stopPropagation: function stopPropagation() {},\n          preventDefault: function preventDefault() {},\n          target: {\n            name: this.props.name,\n            id: this.props.id,\n            value: event.target.value\n          }\n        });\n      }\n    }\n  }, {\n    key: \"onEditableInputFocus\",\n    value: function onEditableInputFocus(event) {\n      var _this5 = this;\n\n      event.persist();\n      this.setState({\n        focused: true\n      }, function () {\n        _this5.hideOverlay();\n\n        if (_this5.props.onFocus) {\n          _this5.props.onFocus(event);\n        }\n      });\n    }\n  }, {\n    key: \"onOptionClick\",\n    value: function onOptionClick(event) {\n      var option = event.option;\n\n      if (!option.disabled) {\n        this.selectItem(event);\n        this.focusInput.focus();\n      }\n\n      this.hideOverlay();\n    }\n  }, {\n    key: \"onFilterInputChange\",\n    value: function onFilterInputChange(event) {\n      var _this6 = this;\n\n      var filter = event.target.value;\n      this.setState({\n        filter: filter\n      }, function () {\n        if (_this6.props.onFilter) {\n          _this6.props.onFilter({\n            originalEvent: event,\n            filter: filter\n          });\n        }\n      });\n    }\n  }, {\n    key: \"onFilterClearIconClick\",\n    value: function onFilterClearIconClick(callback) {\n      this.resetFilter(callback);\n    }\n  }, {\n    key: \"resetFilter\",\n    value: function resetFilter(callback) {\n      var _this7 = this;\n\n      var filter = '';\n      this.setState({\n        filter: filter\n      }, function () {\n        _this7.props.onFilter && _this7.props.onFilter({\n          filter: filter\n        });\n        callback && callback();\n      });\n    }\n  }, {\n    key: \"clear\",\n    value: function clear(event) {\n      if (this.props.onChange) {\n        this.props.onChange({\n          originalEvent: event,\n          value: undefined,\n          stopPropagation: function stopPropagation() {},\n          preventDefault: function preventDefault() {},\n          target: {\n            name: this.props.name,\n            id: this.props.id,\n            value: undefined\n          }\n        });\n      }\n\n      this.updateEditableLabel();\n    }\n  }, {\n    key: \"selectItem\",\n    value: function selectItem(event) {\n      var currentSelectedOption = this.getSelectedOption();\n\n      if (currentSelectedOption !== event.option) {\n        this.updateEditableLabel(event.option);\n        var optionValue = this.getOptionValue(event.option);\n\n        if (this.props.onChange) {\n          this.props.onChange({\n            originalEvent: event.originalEvent,\n            value: optionValue,\n            stopPropagation: function stopPropagation() {},\n            preventDefault: function preventDefault() {},\n            target: {\n              name: this.props.name,\n              id: this.props.id,\n              value: optionValue\n            }\n          });\n        }\n      }\n    }\n  }, {\n    key: \"getSelectedOption\",\n    value: function getSelectedOption() {\n      var index = this.getSelectedOptionIndex();\n      var visibleOptions = this.getVisibleOptions();\n      return index !== -1 ? this.props.optionGroupLabel ? this.getOptionGroupChildren(visibleOptions[index.group])[index.option] : visibleOptions[index] : null;\n    }\n  }, {\n    key: \"getSelectedOptionIndex\",\n    value: function getSelectedOptionIndex() {\n      var visibleOptions = this.getVisibleOptions();\n\n      if (this.props.value != null && visibleOptions) {\n        if (this.props.optionGroupLabel) {\n          for (var i = 0; i < visibleOptions.length; i++) {\n            var selectedOptionIndex = this.findOptionIndexInList(this.props.value, this.getOptionGroupChildren(visibleOptions[i]));\n\n            if (selectedOptionIndex !== -1) {\n              return {\n                group: i,\n                option: selectedOptionIndex\n              };\n            }\n          }\n        } else {\n          return this.findOptionIndexInList(this.props.value, visibleOptions);\n        }\n      }\n\n      return -1;\n    }\n  }, {\n    key: \"findOptionIndexInList\",\n    value: function findOptionIndexInList(value, list) {\n      var key = this.equalityKey();\n\n      for (var i = 0; i < list.length; i++) {\n        if (ObjectUtils.equals(value, this.getOptionValue(list[i]), key)) {\n          return i;\n        }\n      }\n\n      return -1;\n    }\n  }, {\n    key: \"isSelected\",\n    value: function isSelected(option) {\n      return ObjectUtils.equals(this.props.value, this.getOptionValue(option), this.equalityKey());\n    }\n  }, {\n    key: \"equalityKey\",\n    value: function equalityKey() {\n      return this.props.optionValue ? null : this.props.dataKey;\n    }\n  }, {\n    key: \"showOverlay\",\n    value: function showOverlay() {\n      this.setState({\n        overlayVisible: true\n      });\n    }\n  }, {\n    key: \"hideOverlay\",\n    value: function hideOverlay() {\n      this.setState({\n        overlayVisible: false\n      });\n    }\n  }, {\n    key: \"onOverlayEnter\",\n    value: function onOverlayEnter(callback) {\n      ZIndexUtils.set('overlay', this.overlayRef.current);\n      this.alignOverlay();\n      callback && callback();\n    }\n  }, {\n    key: \"onOverlayEntered\",\n    value: function onOverlayEntered(callback) {\n      this.bindDocumentClickListener();\n      this.bindScrollListener();\n      this.bindResizeListener();\n      callback && callback();\n      this.props.onShow && this.props.onShow();\n    }\n  }, {\n    key: \"onOverlayExit\",\n    value: function onOverlayExit() {\n      this.unbindDocumentClickListener();\n      this.unbindScrollListener();\n      this.unbindResizeListener();\n    }\n  }, {\n    key: \"onOverlayExited\",\n    value: function onOverlayExited() {\n      if (this.props.filter && this.props.resetFilterOnHide) {\n        this.resetFilter();\n      }\n\n      ZIndexUtils.clear(this.overlayRef.current);\n      this.props.onHide && this.props.onHide();\n    }\n  }, {\n    key: \"alignOverlay\",\n    value: function alignOverlay() {\n      DomHandler.alignOverlay(this.overlayRef.current, this.input.parentElement, this.props.appendTo || PrimeReact.appendTo);\n    }\n  }, {\n    key: \"scrollInView\",\n    value: function scrollInView() {\n      var highlightItem = DomHandler.findSingle(this.overlayRef.current, 'li.p-highlight');\n\n      if (highlightItem) {\n        highlightItem.scrollIntoView({\n          block: 'nearest',\n          inline: 'start'\n        });\n      }\n    }\n  }, {\n    key: \"bindDocumentClickListener\",\n    value: function bindDocumentClickListener() {\n      var _this8 = this;\n\n      if (!this.documentClickListener) {\n        this.documentClickListener = function (event) {\n          if (_this8.state.overlayVisible && _this8.isOutsideClicked(event)) {\n            _this8.hideOverlay();\n          }\n        };\n\n        document.addEventListener('click', this.documentClickListener);\n      }\n    }\n  }, {\n    key: \"unbindDocumentClickListener\",\n    value: function unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        document.removeEventListener('click', this.documentClickListener);\n        this.documentClickListener = null;\n      }\n    }\n  }, {\n    key: \"bindScrollListener\",\n    value: function bindScrollListener() {\n      var _this9 = this;\n\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.container, function () {\n          if (_this9.state.overlayVisible) {\n            _this9.hideOverlay();\n          }\n        });\n      }\n\n      this.scrollHandler.bindScrollListener();\n    }\n  }, {\n    key: \"unbindScrollListener\",\n    value: function unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n  }, {\n    key: \"bindResizeListener\",\n    value: function bindResizeListener() {\n      var _this10 = this;\n\n      if (!this.resizeListener) {\n        this.resizeListener = function () {\n          if (_this10.state.overlayVisible && !DomHandler.isTouchDevice()) {\n            _this10.hideOverlay();\n          }\n        };\n\n        window.addEventListener('resize', this.resizeListener);\n      }\n    }\n  }, {\n    key: \"unbindResizeListener\",\n    value: function unbindResizeListener() {\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = null;\n      }\n    }\n  }, {\n    key: \"isOutsideClicked\",\n    value: function isOutsideClicked(event) {\n      return this.container && !(this.container.isSameNode(event.target) || this.isClearClicked(event) || this.container.contains(event.target) || this.overlayRef && this.overlayRef.current.contains(event.target));\n    }\n  }, {\n    key: \"isClearClicked\",\n    value: function isClearClicked(event) {\n      return DomHandler.hasClass(event.target, 'p-dropdown-clear-icon');\n    }\n  }, {\n    key: \"updateEditableLabel\",\n    value: function updateEditableLabel(option) {\n      if (this.input) {\n        this.input.value = option ? this.getOptionLabel(option) : this.props.value || '';\n      }\n    }\n  }, {\n    key: \"hasFilter\",\n    value: function hasFilter() {\n      return this.state.filter && this.state.filter.trim().length > 0;\n    }\n  }, {\n    key: \"getOptionLabel\",\n    value: function getOptionLabel(option) {\n      return this.props.optionLabel ? ObjectUtils.resolveFieldData(option, this.props.optionLabel) : option && option['label'] !== undefined ? option['label'] : option;\n    }\n  }, {\n    key: \"getOptionValue\",\n    value: function getOptionValue(option) {\n      return this.props.optionValue ? ObjectUtils.resolveFieldData(option, this.props.optionValue) : option && option['value'] !== undefined ? option['value'] : option;\n    }\n  }, {\n    key: \"getOptionRenderKey\",\n    value: function getOptionRenderKey(option) {\n      return this.props.dataKey ? ObjectUtils.resolveFieldData(option, this.props.dataKey) : this.getOptionLabel(option);\n    }\n  }, {\n    key: \"isOptionDisabled\",\n    value: function isOptionDisabled(option) {\n      if (this.props.optionDisabled) {\n        return ObjectUtils.isFunction(this.props.optionDisabled) ? this.props.optionDisabled(option) : ObjectUtils.resolveFieldData(option, this.props.optionDisabled);\n      }\n\n      return option && option['disabled'] !== undefined ? option['disabled'] : false;\n    }\n  }, {\n    key: \"getOptionGroupRenderKey\",\n    value: function getOptionGroupRenderKey(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupLabel);\n    }\n  }, {\n    key: \"getOptionGroupLabel\",\n    value: function getOptionGroupLabel(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupLabel);\n    }\n  }, {\n    key: \"getOptionGroupChildren\",\n    value: function getOptionGroupChildren(optionGroup) {\n      return ObjectUtils.resolveFieldData(optionGroup, this.props.optionGroupChildren);\n    }\n  }, {\n    key: \"checkValidity\",\n    value: function checkValidity() {\n      return this.inputRef.current.checkValidity();\n    }\n  }, {\n    key: \"getVisibleOptions\",\n    value: function getVisibleOptions() {\n      if (this.hasFilter()) {\n        var filterValue = this.state.filter.trim().toLocaleLowerCase(this.props.filterLocale);\n        var searchFields = this.props.filterBy ? this.props.filterBy.split(',') : [this.props.optionLabel || 'label'];\n\n        if (this.props.optionGroupLabel) {\n          var filteredGroups = [];\n\n          var _iterator = _createForOfIteratorHelper(this.props.options),\n              _step;\n\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var optgroup = _step.value;\n              var filteredSubOptions = FilterUtils.filter(this.getOptionGroupChildren(optgroup), searchFields, filterValue, this.props.filterMatchMode, this.props.filterLocale);\n\n              if (filteredSubOptions && filteredSubOptions.length) {\n                filteredGroups.push(_objectSpread(_objectSpread({}, optgroup), {\n                  items: filteredSubOptions\n                }));\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n\n          return filteredGroups;\n        } else {\n          return FilterUtils.filter(this.props.options, searchFields, filterValue, this.props.filterMatchMode, this.props.filterLocale);\n        }\n      } else {\n        return this.props.options;\n      }\n    }\n  }, {\n    key: \"updateInputField\",\n    value: function updateInputField() {\n      if (this.props.editable && this.input) {\n        var selectedOption = this.getSelectedOption();\n        var label = selectedOption ? this.getOptionLabel(selectedOption) : null;\n        var value = label || this.props.value || '';\n        this.input.value = value;\n      }\n    }\n  }, {\n    key: \"updateInputRef\",\n    value: function updateInputRef() {\n      var ref = this.props.inputRef;\n\n      if (ref) {\n        if (typeof ref === 'function') {\n          ref(this.inputRef.current);\n        } else {\n          ref.current = this.inputRef.current;\n        }\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateInputRef();\n\n      if (this.props.autoFocus && this.focusInput) {\n        this.focusInput.focus();\n      }\n\n      if (this.props.tooltip) {\n        this.renderTooltip();\n      }\n\n      this.updateInputField();\n      this.inputRef.current.selectedIndex = 1;\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unbindDocumentClickListener();\n      this.unbindResizeListener();\n\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n\n      if (this.tooltip) {\n        this.tooltip.destroy();\n        this.tooltip = null;\n      }\n\n      if (this.hideTimeout) {\n        clearTimeout(this.hideTimeout);\n        this.hideTimeout = null;\n      }\n\n      ZIndexUtils.clear(this.overlayRef.current);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.state.overlayVisible) {\n        if (this.props.filter) {\n          this.alignOverlay();\n        }\n\n        if (prevProps.value !== this.props.value) {\n          this.scrollInView();\n        }\n      }\n\n      if (prevProps.tooltip !== this.props.tooltip || prevProps.tooltipOptions !== this.props.tooltipOptions) {\n        if (this.tooltip) this.tooltip.update(_objectSpread({\n          content: this.props.tooltip\n        }, this.props.tooltipOptions || {}));else this.renderTooltip();\n      }\n\n      if (this.state.filter && (!this.props.options || this.props.options.length === 0)) {\n        this.setState({\n          filter: ''\n        });\n      }\n\n      this.updateInputField();\n      this.inputRef.current.selectedIndex = 1;\n    }\n  }, {\n    key: \"renderHiddenSelect\",\n    value: function renderHiddenSelect(selectedOption) {\n      var placeHolderOption = /*#__PURE__*/React.createElement(\"option\", {\n        value: \"\"\n      }, this.props.placeholder);\n      var option = selectedOption ? /*#__PURE__*/React.createElement(\"option\", {\n        value: selectedOption.value\n      }, this.getOptionLabel(selectedOption)) : null;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-hidden-accessible p-dropdown-hidden-select\"\n      }, /*#__PURE__*/React.createElement(\"select\", {\n        ref: this.inputRef,\n        required: this.props.required,\n        name: this.props.name,\n        tabIndex: -1,\n        \"aria-hidden\": \"true\"\n      }, placeHolderOption, option));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      this.tooltip = tip({\n        target: this.container,\n        content: this.props.tooltip,\n        options: this.props.tooltipOptions\n      });\n    }\n  }, {\n    key: \"renderKeyboardHelper\",\n    value: function renderKeyboardHelper() {\n      var _this11 = this;\n\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"p-hidden-accessible\"\n      }, /*#__PURE__*/React.createElement(\"input\", {\n        ref: function ref(el) {\n          return _this11.focusInput = el;\n        },\n        id: this.props.inputId,\n        type: \"text\",\n        readOnly: true,\n        \"aria-haspopup\": \"listbox\",\n        onFocus: this.onInputFocus,\n        onBlur: this.onInputBlur,\n        onKeyDown: this.onInputKeyDown,\n        disabled: this.props.disabled,\n        tabIndex: this.props.tabIndex,\n        \"aria-label\": this.props.ariaLabel,\n        \"aria-labelledby\": this.props.ariaLabelledBy\n      }));\n    }\n  }, {\n    key: \"renderLabel\",\n    value: function renderLabel(selectedOption) {\n      var _this12 = this;\n\n      var label = selectedOption ? this.getOptionLabel(selectedOption) : null;\n\n      if (this.props.editable) {\n        var value = label || this.props.value || '';\n        return /*#__PURE__*/React.createElement(\"input\", {\n          ref: function ref(el) {\n            return _this12.input = el;\n          },\n          type: \"text\",\n          defaultValue: value,\n          className: \"p-dropdown-label p-inputtext\",\n          disabled: this.props.disabled,\n          placeholder: this.props.placeholder,\n          maxLength: this.props.maxLength,\n          onInput: this.onEditableInputChange,\n          onFocus: this.onEditableInputFocus,\n          onBlur: this.onInputBlur,\n          \"aria-label\": this.props.ariaLabel,\n          \"aria-labelledby\": this.props.ariaLabelledBy,\n          \"aria-haspopup\": \"listbox\"\n        });\n      } else {\n        var className = classNames('p-dropdown-label p-inputtext', {\n          'p-placeholder': label === null && this.props.placeholder,\n          'p-dropdown-label-empty': label === null && !this.props.placeholder\n        });\n        var content = this.props.valueTemplate ? ObjectUtils.getJSXElement(this.props.valueTemplate, selectedOption, this.props) : label || this.props.placeholder || 'empty';\n        return /*#__PURE__*/React.createElement(\"span\", {\n          ref: function ref(el) {\n            return _this12.input = el;\n          },\n          className: className\n        }, content);\n      }\n    }\n  }, {\n    key: \"renderClearIcon\",\n    value: function renderClearIcon() {\n      if (this.props.value != null && this.props.showClear && !this.props.disabled) {\n        return /*#__PURE__*/React.createElement(\"i\", {\n          className: \"p-dropdown-clear-icon pi pi-times\",\n          onClick: this.clear\n        });\n      }\n\n      return null;\n    }\n  }, {\n    key: \"renderDropdownIcon\",\n    value: function renderDropdownIcon() {\n      var _this13 = this;\n\n      var iconClassName = classNames('p-dropdown-trigger-icon p-clickable', this.props.dropdownIcon);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: function ref(el) {\n          return _this13.trigger = el;\n        },\n        className: \"p-dropdown-trigger\",\n        role: \"button\",\n        \"aria-haspopup\": \"listbox\",\n        \"aria-expanded\": this.state.overlayVisible\n      }, /*#__PURE__*/React.createElement(\"span\", {\n        className: iconClassName\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this14 = this;\n\n      var className = classNames('p-dropdown p-component p-inputwrapper', this.props.className, {\n        'p-disabled': this.props.disabled,\n        'p-focus': this.state.focused,\n        'p-dropdown-clearable': this.props.showClear && !this.props.disabled,\n        'p-inputwrapper-filled': this.props.value,\n        'p-inputwrapper-focus': this.state.focused || this.state.overlayVisible\n      });\n      var visibleOptions = this.getVisibleOptions();\n      var selectedOption = this.getSelectedOption();\n      var hiddenSelect = this.renderHiddenSelect(selectedOption);\n      var keyboardHelper = this.renderKeyboardHelper();\n      var labelElement = this.renderLabel(selectedOption);\n      var dropdownIcon = this.renderDropdownIcon();\n      var clearIcon = this.renderClearIcon();\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: this.props.id,\n        ref: function ref(el) {\n          return _this14.container = el;\n        },\n        className: className,\n        style: this.props.style,\n        onClick: this.onClick,\n        onMouseDown: this.props.onMouseDown,\n        onContextMenu: this.props.onContextMenu\n      }, keyboardHelper, hiddenSelect, labelElement, clearIcon, dropdownIcon, /*#__PURE__*/React.createElement(DropdownPanel, _extends({\n        ref: this.overlayRef,\n        visibleOptions: visibleOptions\n      }, this.props, {\n        onClick: this.onPanelClick,\n        onOptionClick: this.onOptionClick,\n        filterValue: this.state.filter,\n        hasFilter: this.hasFilter,\n        onFilterClearIconClick: this.onFilterClearIconClick,\n        onFilterInputKeyDown: this.onFilterInputKeyDown,\n        onFilterInputChange: this.onFilterInputChange,\n        getOptionLabel: this.getOptionLabel,\n        getOptionRenderKey: this.getOptionRenderKey,\n        isOptionDisabled: this.isOptionDisabled,\n        getOptionGroupChildren: this.getOptionGroupChildren,\n        getOptionGroupLabel: this.getOptionGroupLabel,\n        getOptionGroupRenderKey: this.getOptionGroupRenderKey,\n        isSelected: this.isSelected,\n        getSelectedOptionIndex: this.getSelectedOptionIndex,\n        in: this.state.overlayVisible,\n        onEnter: this.onOverlayEnter,\n        onEntered: this.onOverlayEntered,\n        onExit: this.onOverlayExit,\n        onExited: this.onOverlayExited\n      })));\n    }\n  }]);\n\n  return Dropdown;\n}(Component);\n\n_defineProperty(Dropdown, \"defaultProps\", {\n  id: null,\n  inputRef: null,\n  name: null,\n  value: null,\n  options: null,\n  optionLabel: null,\n  optionValue: null,\n  optionDisabled: null,\n  optionGroupLabel: null,\n  optionGroupChildren: null,\n  optionGroupTemplate: null,\n  valueTemplate: null,\n  itemTemplate: null,\n  style: null,\n  className: null,\n  virtualScrollerOptions: null,\n  scrollHeight: '200px',\n  filter: false,\n  filterBy: null,\n  filterMatchMode: 'contains',\n  filterPlaceholder: null,\n  filterLocale: undefined,\n  emptyMessage: 'No records found',\n  emptyFilterMessage: 'No results found',\n  editable: false,\n  placeholder: null,\n  required: false,\n  disabled: false,\n  appendTo: null,\n  tabIndex: null,\n  autoFocus: false,\n  filterInputAutoFocus: true,\n  resetFilterOnHide: false,\n  showFilterClear: false,\n  panelClassName: null,\n  panelStyle: null,\n  dataKey: null,\n  inputId: null,\n  showClear: false,\n  maxLength: null,\n  tooltip: null,\n  tooltipOptions: null,\n  ariaLabel: null,\n  ariaLabelledBy: null,\n  transitionOptions: null,\n  dropdownIcon: 'pi pi-chevron-down',\n  showOnFocus: false,\n  onChange: null,\n  onFocus: null,\n  onBlur: null,\n  onMouseDown: null,\n  onContextMenu: null,\n  onShow: null,\n  onHide: null,\n  onFilter: null\n});\n\nexport { Dropdown };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AACnD,SAASC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,WAAW,EAAEC,6BAA6B,EAAEC,WAAW,EAAEC,GAAG,QAAQ,iBAAiB;AAClL,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,UAAU,MAAM,gBAAgB;AAEvC,SAASC,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;IAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAEzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QACtB,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACrDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC3B;MACF;IACF;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,OAAOH,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AACxC;AAEA,SAASQ,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACd,MAAM,EAAEe,KAAK,EAAE;EACxC,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,KAAK,CAACZ,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIe,UAAU,GAAGD,KAAK,CAACd,CAAC,CAAC;IACzBe,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDrB,MAAM,CAACsB,cAAc,CAACpB,MAAM,EAAEgB,UAAU,CAACX,GAAG,EAAEW,UAAU,CAAC;EAC3D;AACF;AAEA,SAASK,YAAYA,CAACT,WAAW,EAAEU,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAER,iBAAiB,CAACF,WAAW,CAACN,SAAS,EAAEgB,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAET,iBAAiB,CAACF,WAAW,EAAEW,WAAW,CAAC;EAC5D,OAAOX,WAAW;AACpB;AAEA,SAASY,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7BF,eAAe,GAAG7B,MAAM,CAACgC,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACG,SAAS,GAAGF,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIrB,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAoB,QAAQ,CAAC3B,SAAS,GAAGR,MAAM,CAACqC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC5B,SAAS,EAAE;IACrE8B,WAAW,EAAE;MACXC,KAAK,EAAEJ,QAAQ;MACfd,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIgB,UAAU,EAAEP,eAAe,CAACM,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC9B,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACH,WAAW,KAAKI,MAAM,IAAID,GAAG,KAAKC,MAAM,CAAClC,SAAS,GAAG,QAAQ,GAAG,OAAOiC,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASG,0BAA0BA,CAACjB,IAAI,EAAEjB,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAK8B,OAAO,CAAC9B,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACtE,OAAOA,IAAI;EACb;EAEA,OAAOgB,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASkB,eAAeA,CAACf,CAAC,EAAE;EAC1Be,eAAe,GAAG7C,MAAM,CAACgC,cAAc,GAAGhC,MAAM,CAAC8C,cAAc,GAAG,SAASD,eAAeA,CAACf,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAIjC,MAAM,CAAC8C,cAAc,CAAChB,CAAC,CAAC;EAChD,CAAC;EACD,OAAOe,eAAe,CAACf,CAAC,CAAC;AAC3B;AAEA,SAASiB,eAAeA,CAACN,GAAG,EAAElC,GAAG,EAAEgC,KAAK,EAAE;EACxC,IAAIhC,GAAG,IAAIkC,GAAG,EAAE;IACdzC,MAAM,CAACsB,cAAc,CAACmB,GAAG,EAAElC,GAAG,EAAE;MAC9BgC,KAAK,EAAEA,KAAK;MACZpB,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLoB,GAAG,CAAClC,GAAG,CAAC,GAAGgC,KAAK;EAClB;EAEA,OAAOE,GAAG;AACZ;AAEA,SAASO,cAAcA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,2BAA2B,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGR,eAAe,CAACI,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAGV,eAAe,CAAC,IAAI,CAAC,CAACP,WAAW;MAAEgB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEjD,SAAS,EAAEmD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAAC1C,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;IAAE;IAAE,OAAOwC,0BAA0B,CAAC,IAAI,EAAEU,MAAM,CAAC;EAAE,CAAC;AAAE;AAE5a,SAASH,2BAA2BA,CAAA,EAAG;EAAE,IAAI,OAAOK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACnD,IAAI,CAAC8C,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAC1U,IAAIC,YAAY,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpD9B,SAAS,CAAC6B,YAAY,EAAEC,UAAU,CAAC;EAEnC,IAAIC,MAAM,GAAGjB,cAAc,CAACe,YAAY,CAAC;EAEzC,SAASA,YAAYA,CAAC9C,KAAK,EAAE;IAC3B,IAAIiD,KAAK;IAETtD,eAAe,CAAC,IAAI,EAAEmD,YAAY,CAAC;IAEnCG,KAAK,GAAGD,MAAM,CAACvD,IAAI,CAAC,IAAI,EAAEO,KAAK,CAAC;IAChCiD,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACjE,OAAOA,KAAK;EACd;EAEA3C,YAAY,CAACwC,YAAY,EAAE,CAAC;IAC1BxD,GAAG,EAAE,SAAS;IACdgC,KAAK,EAAE,SAAS4B,OAAOA,CAACE,KAAK,EAAE;MAC7B,IAAI,IAAI,CAACpD,KAAK,CAACkD,OAAO,EAAE;QACtB,IAAI,CAAClD,KAAK,CAACkD,OAAO,CAAC;UACjBG,aAAa,EAAED,KAAK;UACpBE,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACsD;QACrB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,IAAIC,SAAS,GAAGvF,UAAU,CAAC,iBAAiB,EAAE;QAC5C,aAAa,EAAE,IAAI,CAAC+B,KAAK,CAACyD,QAAQ;QAClC,YAAY,EAAE,IAAI,CAACzD,KAAK,CAAC0D,QAAQ;QACjC,uBAAuB,EAAE,CAAC,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,IAAI,IAAI,CAAC3D,KAAK,CAAC2D,KAAK,CAACvE,MAAM,KAAK;MAC5E,CAAC,EAAE,IAAI,CAACY,KAAK,CAACsD,MAAM,CAACE,SAAS,CAAC;MAC/B,IAAII,OAAO,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,QAAQ,GAAG3F,WAAW,CAAC4F,aAAa,CAAC,IAAI,CAAC9D,KAAK,CAAC6D,QAAQ,EAAE,IAAI,CAAC7D,KAAK,CAACsD,MAAM,CAAC,GAAG,IAAI,CAACtD,KAAK,CAAC2D,KAAK;MACxH,OAAO,aAAa7F,KAAK,CAACiG,aAAa,CAAC,IAAI,EAAE;QAC5CP,SAAS,EAAEA,SAAS;QACpBN,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB,YAAY,EAAE,IAAI,CAAClD,KAAK,CAAC2D,KAAK;QAC9BrE,GAAG,EAAE,IAAI,CAACU,KAAK,CAAC2D,KAAK;QACrBK,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE,IAAI,CAAChE,KAAK,CAACyD;MAC9B,CAAC,EAAEG,OAAO,EAAE,aAAa9F,KAAK,CAACiG,aAAa,CAAC5F,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7D;EACF,CAAC,CAAC,CAAC;EAEH,OAAO2E,YAAY;AACrB,CAAC,CAAC/E,SAAS,CAAC;AAEZ+D,eAAe,CAACgB,YAAY,EAAE,cAAc,EAAE;EAC5CQ,MAAM,EAAE,IAAI;EACZK,KAAK,EAAE,IAAI;EACXE,QAAQ,EAAE,IAAI;EACdJ,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfR,OAAO,EAAE;AACX,CAAC,CAAC;AAEF,SAASe,SAASA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGrF,MAAM,CAACqF,IAAI,CAACF,MAAM,CAAC;EAAE,IAAInF,MAAM,CAACsF,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGvF,MAAM,CAACsF,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOzF,MAAM,CAAC0F,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACtE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEkE,IAAI,CAACM,IAAI,CAAChF,KAAK,CAAC0E,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAE1V,SAASO,eAAeA,CAAC1F,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAE+E,SAAS,CAAClF,MAAM,CAACM,MAAM,CAAC,EAAE,IAAI,CAAC,CAACuF,OAAO,CAAC,UAAUtF,GAAG,EAAE;QAAEwC,eAAe,CAAC7C,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIP,MAAM,CAAC8F,yBAAyB,EAAE;MAAE9F,MAAM,CAAC+F,gBAAgB,CAAC7F,MAAM,EAAEF,MAAM,CAAC8F,yBAAyB,CAACxF,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAE4E,SAAS,CAAClF,MAAM,CAACM,MAAM,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,GAAG,EAAE;QAAEP,MAAM,CAACsB,cAAc,CAACpB,MAAM,EAAEK,GAAG,EAAEP,MAAM,CAAC0F,wBAAwB,CAACpF,MAAM,EAAEC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAE3hB,SAAS8F,cAAcA,CAAC/C,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAG+C,2BAA2B,CAAC,CAAC;EAAE,OAAO,SAAS7C,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGR,eAAe,CAACI,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAGV,eAAe,CAAC,IAAI,CAAC,CAACP,WAAW;MAAEgB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEjD,SAAS,EAAEmD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAAC1C,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;IAAE;IAAE,OAAOwC,0BAA0B,CAAC,IAAI,EAAEU,MAAM,CAAC;EAAE,CAAC;AAAE;AAE5a,SAAS2C,2BAA2BA,CAAA,EAAG;EAAE,IAAI,OAAOzC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACnD,IAAI,CAAC8C,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAE1U,IAAIoC,sBAAsB,GAAG,aAAa,UAAUlC,UAAU,EAAE;EAC9D9B,SAAS,CAACgE,sBAAsB,EAAElC,UAAU,CAAC;EAE7C,IAAIC,MAAM,GAAG+B,cAAc,CAACE,sBAAsB,CAAC;EAEnD,SAASA,sBAAsBA,CAACjF,KAAK,EAAE;IACrC,IAAIiD,KAAK;IAETtD,eAAe,CAAC,IAAI,EAAEsF,sBAAsB,CAAC;IAE7ChC,KAAK,GAAGD,MAAM,CAACvD,IAAI,CAAC,IAAI,EAAEO,KAAK,CAAC;IAChCiD,KAAK,CAACiC,OAAO,GAAGjC,KAAK,CAACiC,OAAO,CAAC/B,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACjEA,KAAK,CAACkC,SAAS,GAAGlC,KAAK,CAACkC,SAAS,CAAChC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACrEA,KAAK,CAACmC,mBAAmB,GAAGnC,KAAK,CAACmC,mBAAmB,CAACjC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACzF,OAAOA,KAAK;EACd;EAEA3C,YAAY,CAAC2E,sBAAsB,EAAE,CAAC;IACpC3F,GAAG,EAAE,SAAS;IACdgC,KAAK,EAAE,SAAS4D,OAAOA,CAAA,EAAG;MACxB,IAAIG,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACrF,KAAK,CAACkF,OAAO,CAAC,YAAY;QAC7B,IAAIG,MAAM,CAACC,kBAAkB,EAAE;UAC7B,IAAIC,aAAa,GAAGF,MAAM,CAACrF,KAAK,CAACwF,sBAAsB,CAAC,CAAC;UAEzD,IAAID,aAAa,KAAK,CAAC,CAAC,EAAE;YACxBF,MAAM,CAACC,kBAAkB,CAACG,aAAa,CAACF,aAAa,CAAC;UACxD;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,WAAW;IAChBgC,KAAK,EAAE,SAAS6D,SAASA,CAAA,EAAG;MAC1B,IAAIO,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC1F,KAAK,CAACmF,SAAS,CAAC,YAAY;QAC/B,IAAIO,MAAM,CAAC1F,KAAK,CAACuE,MAAM,IAAImB,MAAM,CAAC1F,KAAK,CAAC2F,oBAAoB,EAAE;UAC5DD,MAAM,CAACE,WAAW,CAACC,KAAK,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvG,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAAS8D,mBAAmBA,CAAChC,KAAK,EAAE;MACzC,IAAI,IAAI,CAACkC,kBAAkB,EAAE;QAC3B,IAAI,CAACA,kBAAkB,CAACG,aAAa,CAAC,CAAC,CAAC;MAC1C;MAEA,IAAI,CAACzF,KAAK,CAACoF,mBAAmB,IAAI,IAAI,CAACpF,KAAK,CAACoF,mBAAmB,CAAChC,KAAK,CAAC;IACzE;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAASwE,aAAaA,CAAA,EAAG;MAC9B,OAAO,EAAE,IAAI,CAAC9F,KAAK,CAAC+F,cAAc,IAAI,IAAI,CAAC/F,KAAK,CAAC+F,cAAc,CAAC3G,MAAM,CAAC,IAAI,IAAI,CAACY,KAAK,CAACgG,SAAS,CAAC,CAAC;IACnG;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAAS2E,mBAAmBA,CAACC,WAAW,EAAE;MAC/C,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,aAAa,GAAG,IAAI,CAACpG,KAAK,CAACqG,sBAAsB,CAACH,WAAW,CAAC;MAClE,OAAOE,aAAa,CAACE,GAAG,CAAC,UAAUhD,MAAM,EAAEiD,CAAC,EAAE;QAC5C,IAAIC,WAAW,GAAGL,MAAM,CAACnG,KAAK,CAACyG,cAAc,CAACnD,MAAM,CAAC;QAErD,IAAIoD,SAAS,GAAGH,CAAC,GAAG,GAAG,GAAGJ,MAAM,CAACnG,KAAK,CAAC2G,kBAAkB,CAACrD,MAAM,CAAC;QAEjE,IAAII,QAAQ,GAAGyC,MAAM,CAACnG,KAAK,CAAC4G,gBAAgB,CAACtD,MAAM,CAAC;QAEpD,OAAO,aAAaxF,KAAK,CAACiG,aAAa,CAACjB,YAAY,EAAE;UACpDxD,GAAG,EAAEoH,SAAS;UACd/C,KAAK,EAAE6C,WAAW;UAClBlD,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAEsC,MAAM,CAACnG,KAAK,CAAC6G,YAAY;UACnCpD,QAAQ,EAAE0C,MAAM,CAACnG,KAAK,CAAC8G,UAAU,CAACxD,MAAM,CAAC;UACzCI,QAAQ,EAAEA,QAAQ;UAClBR,OAAO,EAAEiD,MAAM,CAACnG,KAAK,CAAC+G;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDzH,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAAS0F,kBAAkBA,CAACC,YAAY,EAAE;MAC/C,IAAIC,OAAO,GAAGhJ,WAAW,CAAC4F,aAAa,CAACmD,YAAY,EAAE,IAAI,CAACjH,KAAK,CAAC;MACjE,OAAO,aAAalC,KAAK,CAACiG,aAAa,CAAC,IAAI,EAAE;QAC5CP,SAAS,EAAE;MACb,CAAC,EAAE0D,OAAO,CAAC;IACb;EACF,CAAC,EAAE;IACD5H,GAAG,EAAE,YAAY;IACjBgC,KAAK,EAAE,SAAS6F,UAAUA,CAAC7D,MAAM,EAAE8D,KAAK,EAAE;MACxC,IAAI,IAAI,CAACpH,KAAK,CAACqH,gBAAgB,EAAE;QAC/B,IAAIC,YAAY,GAAG,IAAI,CAACtH,KAAK,CAACuH,mBAAmB,GAAGrJ,WAAW,CAAC4F,aAAa,CAAC,IAAI,CAAC9D,KAAK,CAACuH,mBAAmB,EAAEjE,MAAM,EAAE8D,KAAK,CAAC,GAAG,IAAI,CAACpH,KAAK,CAACwH,mBAAmB,CAAClE,MAAM,CAAC;QACrK,IAAImE,oBAAoB,GAAG,IAAI,CAACxB,mBAAmB,CAAC3C,MAAM,CAAC;QAC3D,IAAIhE,GAAG,GAAG8H,KAAK,GAAG,GAAG,GAAG,IAAI,CAACpH,KAAK,CAAC0H,uBAAuB,CAACpE,MAAM,CAAC;QAClE,OAAO,aAAaxF,KAAK,CAACiG,aAAa,CAACjG,KAAK,CAAC6J,QAAQ,EAAE;UACtDrI,GAAG,EAAEA;QACP,CAAC,EAAE,aAAaxB,KAAK,CAACiG,aAAa,CAAC,IAAI,EAAE;UACxCP,SAAS,EAAE;QACb,CAAC,EAAE8D,YAAY,CAAC,EAAEG,oBAAoB,CAAC;MACzC,CAAC,MAAM;QACL,IAAIjB,WAAW,GAAG,IAAI,CAACxG,KAAK,CAACyG,cAAc,CAACnD,MAAM,CAAC;QACnD,IAAIoD,SAAS,GAAGU,KAAK,GAAG,GAAG,GAAG,IAAI,CAACpH,KAAK,CAAC2G,kBAAkB,CAACrD,MAAM,CAAC;QACnE,IAAII,QAAQ,GAAG,IAAI,CAAC1D,KAAK,CAAC4G,gBAAgB,CAACtD,MAAM,CAAC;QAClD,OAAO,aAAaxF,KAAK,CAACiG,aAAa,CAACjB,YAAY,EAAE;UACpDxD,GAAG,EAAEoH,SAAS;UACd/C,KAAK,EAAE6C,WAAW;UAClBlD,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAE,IAAI,CAAC7D,KAAK,CAAC6G,YAAY;UACjCpD,QAAQ,EAAE,IAAI,CAACzD,KAAK,CAAC8G,UAAU,CAACxD,MAAM,CAAC;UACvCI,QAAQ,EAAEA,QAAQ;UAClBR,OAAO,EAAE,IAAI,CAAClD,KAAK,CAAC+G;QACtB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDzH,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASsG,WAAWA,CAAA,EAAG;MAC5B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAC7H,KAAK,CAAC+F,cAAc,IAAI,IAAI,CAAC/F,KAAK,CAAC+F,cAAc,CAAC3G,MAAM,EAAE;QACjE,OAAO,IAAI,CAACY,KAAK,CAAC+F,cAAc,CAACO,GAAG,CAAC,UAAUhD,MAAM,EAAE8D,KAAK,EAAE;UAC5D,OAAOS,MAAM,CAACV,UAAU,CAAC7D,MAAM,EAAE8D,KAAK,CAAC;QACzC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAACpH,KAAK,CAACgG,SAAS,CAAC,CAAC,EAAE;QACjC,OAAO,IAAI,CAACgB,kBAAkB,CAAC,IAAI,CAAChH,KAAK,CAAC8H,kBAAkB,CAAC;MAC/D;MAEA,OAAO,IAAI,CAACd,kBAAkB,CAAC,IAAI,CAAChH,KAAK,CAACiH,YAAY,CAAC;IACzD;EACF,CAAC,EAAE;IACD3H,GAAG,EAAE,uBAAuB;IAC5BgC,KAAK,EAAE,SAASyG,qBAAqBA,CAAA,EAAG;MACtC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAChI,KAAK,CAACiI,eAAe,IAAI,IAAI,CAACjI,KAAK,CAACkI,WAAW,EAAE;QACxD,OAAO,aAAapK,KAAK,CAACiG,aAAa,CAAC,GAAG,EAAE;UAC3CP,SAAS,EAAE,0CAA0C;UACrDN,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;YAC1B,OAAO8E,MAAM,CAAChI,KAAK,CAACmI,sBAAsB,CAAC,YAAY;cACrD,OAAOH,MAAM,CAACpC,WAAW,CAACC,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDvG,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAAS8G,YAAYA,CAAA,EAAG;MAC7B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACrI,KAAK,CAACuE,MAAM,EAAE;QACrB,IAAI+D,SAAS,GAAG,IAAI,CAACP,qBAAqB,CAAC,CAAC;QAC5C,IAAIQ,kBAAkB,GAAGtK,UAAU,CAAC,6BAA6B,EAAE;UACjE,6BAA6B,EAAE,CAAC,CAACqK;QACnC,CAAC,CAAC;QACF,OAAO,aAAaxK,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;UAC7CP,SAAS,EAAE;QACb,CAAC,EAAE,aAAa1F,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;UACzCP,SAAS,EAAE+E;QACb,CAAC,EAAE,aAAazK,KAAK,CAACiG,aAAa,CAAC,OAAO,EAAE;UAC3CyE,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAOJ,MAAM,CAACzC,WAAW,GAAG6C,EAAE;UAChC,CAAC;UACDC,IAAI,EAAE,MAAM;UACZC,YAAY,EAAE,KAAK;UACnBnF,SAAS,EAAE,2CAA2C;UACtDoF,WAAW,EAAE,IAAI,CAAC5I,KAAK,CAAC6I,iBAAiB;UACzCC,SAAS,EAAE,IAAI,CAAC9I,KAAK,CAAC+I,oBAAoB;UAC1CC,QAAQ,EAAE,IAAI,CAAC5D,mBAAmB;UAClC9D,KAAK,EAAE,IAAI,CAACtB,KAAK,CAACkI;QACpB,CAAC,CAAC,EAAEI,SAAS,EAAE,aAAaxK,KAAK,CAACiG,aAAa,CAAC,GAAG,EAAE;UACnDP,SAAS,EAAE;QACb,CAAC,CAAC,CAAC,CAAC;MACN;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAAS2H,aAAaA,CAAA,EAAG;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAAClJ,KAAK,CAACmJ,sBAAsB,EAAE;QACrC,IAAIC,oBAAoB,GAAGzE,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3E,KAAK,CAACmJ,sBAAsB,CAAC,EAAE;UACjGE,KAAK,EAAE1E,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3E,KAAK,CAACmJ,sBAAsB,CAACE,KAAK,CAAC,EAAE;YACnFC,MAAM,EAAE,IAAI,CAACtJ,KAAK,CAACuJ;UACrB,CAAC,CAAC;UACF/F,SAAS,EAAEvF,UAAU,CAAC,0BAA0B,EAAE,IAAI,CAAC+B,KAAK,CAACmJ,sBAAsB,CAAC3F,SAAS,CAAC;UAC9FgG,KAAK,EAAE,IAAI,CAACxJ,KAAK,CAAC+F,cAAc;UAChC0D,UAAU,EAAE,SAASA,UAAUA,CAACrG,KAAK,EAAE;YACrC,OAAO8F,MAAM,CAAClJ,KAAK,CAACmJ,sBAAsB,CAACM,UAAU,CAAC9E,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEvB,KAAK,CAAC,EAAE;cAChGmB,MAAM,EAAE2E,MAAM,CAAClJ,KAAK,CAACkI;YACvB,CAAC,CAAC,CAAC;UACL,CAAC;UACDrB,YAAY,EAAE,SAASA,YAAYA,CAAC6C,IAAI,EAAEC,OAAO,EAAE;YACjD,OAAOD,IAAI,IAAIR,MAAM,CAAC/B,UAAU,CAACuC,IAAI,EAAEC,OAAO,CAACvC,KAAK,CAAC;UACvD,CAAC;UACDwC,eAAe,EAAE,SAASA,eAAeA,CAACD,OAAO,EAAE;YACjD,IAAInG,SAAS,GAAGvF,UAAU,CAAC,kBAAkB,EAAE0L,OAAO,CAACnG,SAAS,CAAC;YACjE,IAAII,OAAO,GAAGsF,MAAM,CAACpD,aAAa,CAAC,CAAC,GAAGoD,MAAM,CAAClC,kBAAkB,CAAC,CAAC,GAAG2C,OAAO,CAACE,QAAQ;YACrF,OAAO,aAAa/L,KAAK,CAACiG,aAAa,CAAC,IAAI,EAAE;cAC5CyE,GAAG,EAAEmB,OAAO,CAACnB,GAAG;cAChBhF,SAAS,EAAEA,SAAS;cACpBQ,IAAI,EAAE;YACR,CAAC,EAAEJ,OAAO,CAAC;UACb;QACF,CAAC,CAAC;QAEF,OAAO,aAAa9F,KAAK,CAACiG,aAAa,CAACnF,eAAe,EAAEE,QAAQ,CAAC;UAChE0J,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAOS,MAAM,CAAC5D,kBAAkB,GAAGmD,EAAE;UACvC;QACF,CAAC,EAAEW,oBAAoB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,IAAII,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC,CAAC;QAC9B,OAAO,aAAa9J,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;UAC7CP,SAAS,EAAE,0BAA0B;UACrC6F,KAAK,EAAE;YACLS,SAAS,EAAE,IAAI,CAAC9J,KAAK,CAACuJ,YAAY,IAAI;UACxC;QACF,CAAC,EAAE,aAAazL,KAAK,CAACiG,aAAa,CAAC,IAAI,EAAE;UACxCP,SAAS,EAAE,kBAAkB;UAC7BQ,IAAI,EAAE;QACR,CAAC,EAAEwF,KAAK,CAAC,CAAC;MACZ;IACF;EACF,CAAC,EAAE;IACDlK,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAASyI,aAAaA,CAAA,EAAG;MAC9B,IAAIvG,SAAS,GAAGvF,UAAU,CAAC,8BAA8B,EAAE,IAAI,CAAC+B,KAAK,CAACgK,cAAc,CAAC;MACrF,IAAIzF,MAAM,GAAG,IAAI,CAAC6D,YAAY,CAAC,CAAC;MAChC,IAAIxE,OAAO,GAAG,IAAI,CAACqF,aAAa,CAAC,CAAC;MAClC,OAAO,aAAanL,KAAK,CAACiG,aAAa,CAAC3F,aAAa,EAAE;QACrD6L,OAAO,EAAE,IAAI,CAACjK,KAAK,CAACkK,UAAU;QAC9BjM,UAAU,EAAE,qBAAqB;QACjCkM,EAAE,EAAE,IAAI,CAACnK,KAAK,CAACmK,EAAE;QACjBC,OAAO,EAAE;UACPC,KAAK,EAAE,GAAG;UACVC,IAAI,EAAE;QACR,CAAC;QACDX,OAAO,EAAE,IAAI,CAAC3J,KAAK,CAACuK,iBAAiB;QACrCC,aAAa,EAAE,IAAI;QACnBtF,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBuF,UAAU,EAAE,IAAI,CAACzK,KAAK,CAACyK,UAAU;QACjCtF,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBuF,MAAM,EAAE,IAAI,CAAC1K,KAAK,CAAC0K,MAAM;QACzBC,QAAQ,EAAE,IAAI,CAAC3K,KAAK,CAAC2K;MACvB,CAAC,EAAE,aAAa7M,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;QACzCyE,GAAG,EAAE,IAAI,CAACxI,KAAK,CAACkK,UAAU;QAC1B1G,SAAS,EAAEA,SAAS;QACpB6F,KAAK,EAAE,IAAI,CAACrJ,KAAK,CAAC4K,UAAU;QAC5B1H,OAAO,EAAE,IAAI,CAAClD,KAAK,CAACkD;MACtB,CAAC,EAAEqB,MAAM,EAAEX,OAAO,CAAC,CAAC;IACtB;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,IAAIsH,OAAO,GAAG,IAAI,CAACd,aAAa,CAAC,CAAC;MAClC,OAAO,aAAajM,KAAK,CAACiG,aAAa,CAAC1F,MAAM,EAAE;QAC9CwM,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAE,IAAI,CAAC9K,KAAK,CAAC8K;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7F,sBAAsB;AAC/B,CAAC,CAAClH,SAAS,CAAC;AAEZ,IAAIgN,aAAa,GAAG,aAAajN,KAAK,CAACoM,UAAU,CAAC,UAAUlK,KAAK,EAAEwI,GAAG,EAAE;EACtE,OAAO,aAAa1K,KAAK,CAACiG,aAAa,CAACkB,sBAAsB,EAAEnG,QAAQ,CAAC;IACvEoL,UAAU,EAAE1B;EACd,CAAC,EAAExI,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASgL,OAAOA,CAAC9G,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGrF,MAAM,CAACqF,IAAI,CAACF,MAAM,CAAC;EAAE,IAAInF,MAAM,CAACsF,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGvF,MAAM,CAACsF,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOzF,MAAM,CAAC0F,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACtE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEkE,IAAI,CAACM,IAAI,CAAChF,KAAK,CAAC0E,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAExV,SAAS6G,aAAaA,CAAChM,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAE8L,OAAO,CAACjM,MAAM,CAACM,MAAM,CAAC,EAAE,IAAI,CAAC,CAACuF,OAAO,CAAC,UAAUtF,GAAG,EAAE;QAAEwC,eAAe,CAAC7C,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIP,MAAM,CAAC8F,yBAAyB,EAAE;MAAE9F,MAAM,CAAC+F,gBAAgB,CAAC7F,MAAM,EAAEF,MAAM,CAAC8F,yBAAyB,CAACxF,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAE2L,OAAO,CAACjM,MAAM,CAACM,MAAM,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,GAAG,EAAE;QAAEP,MAAM,CAACsB,cAAc,CAACpB,MAAM,EAAEK,GAAG,EAAEP,MAAM,CAAC0F,wBAAwB,CAACpF,MAAM,EAAEC,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAErhB,SAASiM,0BAA0BA,CAACrK,CAAC,EAAEsK,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAO3J,MAAM,KAAK,WAAW,IAAIZ,CAAC,CAACY,MAAM,CAACC,QAAQ,CAAC,IAAIb,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,CAACuK,EAAE,EAAE;IAAE,IAAIC,KAAK,CAACC,OAAO,CAACzK,CAAC,CAAC,KAAKuK,EAAE,GAAGG,2BAA2B,CAAC1K,CAAC,CAAC,CAAC,IAAIsK,cAAc,IAAItK,CAAC,IAAI,OAAOA,CAAC,CAACzB,MAAM,KAAK,QAAQ,EAAE;MAAE,IAAIgM,EAAE,EAAEvK,CAAC,GAAGuK,EAAE;MAAE,IAAIlM,CAAC,GAAG,CAAC;MAAE,IAAIsM,CAAC,GAAG,SAASA,CAACA,CAAA,EAAG,CAAC,CAAC;MAAE,OAAO;QAAEC,CAAC,EAAED,CAAC;QAAEE,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;UAAE,IAAIxM,CAAC,IAAI2B,CAAC,CAACzB,MAAM,EAAE,OAAO;YAAEuM,IAAI,EAAE;UAAK,CAAC;UAAE,OAAO;YAAEA,IAAI,EAAE,KAAK;YAAErK,KAAK,EAAET,CAAC,CAAC3B,CAAC,EAAE;UAAE,CAAC;QAAE,CAAC;QAAE2D,CAAC,EAAE,SAASA,CAACA,CAAC+I,EAAE,EAAE;UAAE,MAAMA,EAAE;QAAE,CAAC;QAAEC,CAAC,EAAEL;MAAE,CAAC;IAAE;IAAE,MAAM,IAAI1L,SAAS,CAAC,uIAAuI,CAAC;EAAE;EAAE,IAAIgM,gBAAgB,GAAG,IAAI;IAAEC,MAAM,GAAG,KAAK;IAAEC,GAAG;EAAE,OAAO;IAAEP,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAEL,EAAE,GAAGA,EAAE,CAAC3L,IAAI,CAACoB,CAAC,CAAC;IAAE,CAAC;IAAE6K,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAIO,IAAI,GAAGb,EAAE,CAACc,IAAI,CAAC,CAAC;MAAEJ,gBAAgB,GAAGG,IAAI,CAACN,IAAI;MAAE,OAAOM,IAAI;IAAE,CAAC;IAAEpJ,CAAC,EAAE,SAASA,CAACA,CAACsJ,GAAG,EAAE;MAAEJ,MAAM,GAAG,IAAI;MAAEC,GAAG,GAAGG,GAAG;IAAE,CAAC;IAAEN,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAI;QAAE,IAAI,CAACC,gBAAgB,IAAIV,EAAE,CAACgB,MAAM,IAAI,IAAI,EAAEhB,EAAE,CAACgB,MAAM,CAAC,CAAC;MAAE,CAAC,SAAS;QAAE,IAAIL,MAAM,EAAE,MAAMC,GAAG;MAAE;IAAE;EAAE,CAAC;AAAE;AAEr+B,SAAST,2BAA2BA,CAAC1K,CAAC,EAAEwL,MAAM,EAAE;EAAE,IAAI,CAACxL,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOyL,iBAAiB,CAACzL,CAAC,EAAEwL,MAAM,CAAC;EAAE,IAAIX,CAAC,GAAG3M,MAAM,CAACQ,SAAS,CAACgN,QAAQ,CAAC9M,IAAI,CAACoB,CAAC,CAAC,CAAC2L,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAId,CAAC,KAAK,QAAQ,IAAI7K,CAAC,CAACQ,WAAW,EAAEqK,CAAC,GAAG7K,CAAC,CAACQ,WAAW,CAACoL,IAAI;EAAE,IAAIf,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOL,KAAK,CAACqB,IAAI,CAAC7L,CAAC,CAAC;EAAE,IAAI6K,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACiB,IAAI,CAACjB,CAAC,CAAC,EAAE,OAAOY,iBAAiB,CAACzL,CAAC,EAAEwL,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACM,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAACxN,MAAM,EAAEyN,GAAG,GAAGD,GAAG,CAACxN,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE4N,IAAI,GAAG,IAAIzB,KAAK,CAACwB,GAAG,CAAC,EAAE3N,CAAC,GAAG2N,GAAG,EAAE3N,CAAC,EAAE,EAAE;IAAE4N,IAAI,CAAC5N,CAAC,CAAC,GAAG0N,GAAG,CAAC1N,CAAC,CAAC;EAAE;EAAE,OAAO4N,IAAI;AAAE;AAEtL,SAASC,YAAYA,CAAC/K,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAG+K,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAAS7K,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGR,eAAe,CAACI,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAGV,eAAe,CAAC,IAAI,CAAC,CAACP,WAAW;MAAEgB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEjD,SAAS,EAAEmD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAAC1C,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;IAAE;IAAE,OAAOwC,0BAA0B,CAAC,IAAI,EAAEU,MAAM,CAAC;EAAE,CAAC;AAAE;AAExa,SAAS2K,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOzK,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACnD,IAAI,CAAC8C,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,IAAIoK,QAAQ,GAAG,aAAa,UAAUlK,UAAU,EAAE;EAChD9B,SAAS,CAACgM,QAAQ,EAAElK,UAAU,CAAC;EAE/B,IAAIC,MAAM,GAAG+J,YAAY,CAACE,QAAQ,CAAC;EAEnC,SAASA,QAAQA,CAACjN,KAAK,EAAE;IACvB,IAAIiD,KAAK;IAETtD,eAAe,CAAC,IAAI,EAAEsN,QAAQ,CAAC;IAE/BhK,KAAK,GAAGD,MAAM,CAACvD,IAAI,CAAC,IAAI,EAAEO,KAAK,CAAC;IAChCiD,KAAK,CAACiK,KAAK,GAAG;MACZ3I,MAAM,EAAE,EAAE;MACV4I,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE;IAClB,CAAC;IACDnK,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACjEA,KAAK,CAACoK,YAAY,GAAGpK,KAAK,CAACoK,YAAY,CAAClK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAACqK,WAAW,GAAGrK,KAAK,CAACqK,WAAW,CAACnK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACzEA,KAAK,CAACsK,cAAc,GAAGtK,KAAK,CAACsK,cAAc,CAACpK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/EA,KAAK,CAACuK,qBAAqB,GAAGvK,KAAK,CAACuK,qBAAqB,CAACrK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC7FA,KAAK,CAACwK,oBAAoB,GAAGxK,KAAK,CAACwK,oBAAoB,CAACtK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC3FA,KAAK,CAAC8D,aAAa,GAAG9D,KAAK,CAAC8D,aAAa,CAAC5D,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC7EA,KAAK,CAACmC,mBAAmB,GAAGnC,KAAK,CAACmC,mBAAmB,CAACjC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACzFA,KAAK,CAAC8F,oBAAoB,GAAG9F,KAAK,CAAC8F,oBAAoB,CAAC5F,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC3FA,KAAK,CAACkF,sBAAsB,GAAGlF,KAAK,CAACkF,sBAAsB,CAAChF,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/FA,KAAK,CAACyK,YAAY,GAAGzK,KAAK,CAACyK,YAAY,CAACvK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC3EA,KAAK,CAAC0K,cAAc,GAAG1K,KAAK,CAAC0K,cAAc,CAACxK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/EA,KAAK,CAAC2K,gBAAgB,GAAG3K,KAAK,CAAC2K,gBAAgB,CAACzK,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACnFA,KAAK,CAAC4K,aAAa,GAAG5K,KAAK,CAAC4K,aAAa,CAAC1K,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC7EA,KAAK,CAAC6K,eAAe,GAAG7K,KAAK,CAAC6K,eAAe,CAAC3K,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACjFA,KAAK,CAAC8K,WAAW,GAAG9K,KAAK,CAAC8K,WAAW,CAAC5K,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACzEA,KAAK,CAAC+K,KAAK,GAAG/K,KAAK,CAAC+K,KAAK,CAAC7K,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC7DA,KAAK,CAAC+C,SAAS,GAAG/C,KAAK,CAAC+C,SAAS,CAAC7C,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACrEA,KAAK,CAACwD,cAAc,GAAGxD,KAAK,CAACwD,cAAc,CAACtD,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/EA,KAAK,CAAC0D,kBAAkB,GAAG1D,KAAK,CAAC0D,kBAAkB,CAACxD,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACvFA,KAAK,CAAC2D,gBAAgB,GAAG3D,KAAK,CAAC2D,gBAAgB,CAACzD,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACnFA,KAAK,CAACoD,sBAAsB,GAAGpD,KAAK,CAACoD,sBAAsB,CAAClD,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/FA,KAAK,CAACuE,mBAAmB,GAAGvE,KAAK,CAACuE,mBAAmB,CAACrE,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACzFA,KAAK,CAACyE,uBAAuB,GAAGzE,KAAK,CAACyE,uBAAuB,CAACvE,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACjGA,KAAK,CAACuC,sBAAsB,GAAGvC,KAAK,CAACuC,sBAAsB,CAACrC,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IAC/FA,KAAK,CAAC6D,UAAU,GAAG7D,KAAK,CAAC6D,UAAU,CAAC3D,IAAI,CAAC1C,sBAAsB,CAACwC,KAAK,CAAC,CAAC;IACvEA,KAAK,CAACgL,UAAU,GAAG,aAAajQ,SAAS,CAAC,CAAC;IAC3CiF,KAAK,CAACiL,QAAQ,GAAG,aAAalQ,SAAS,CAACiF,KAAK,CAACjD,KAAK,CAACkO,QAAQ,CAAC;IAC7D,OAAOjL,KAAK;EACd;EAEA3C,YAAY,CAAC2M,QAAQ,EAAE,CAAC;IACtB3N,GAAG,EAAE,SAAS;IACdgC,KAAK,EAAE,SAAS4B,OAAOA,CAACE,KAAK,EAAE;MAC7B,IAAI,IAAI,CAACpD,KAAK,CAAC0D,QAAQ,EAAE;QACvB;MACF;MAEA,IAAIpF,UAAU,CAAC6P,QAAQ,CAAC/K,KAAK,CAACnE,MAAM,EAAE,uBAAuB,CAAC,IAAImE,KAAK,CAACnE,MAAM,CAACmP,OAAO,KAAK,OAAO,EAAE;QAClG;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACH,UAAU,CAACI,OAAO,IAAI,EAAE,IAAI,CAACJ,UAAU,CAACI,OAAO,IAAI,IAAI,CAACJ,UAAU,CAACI,OAAO,CAACC,QAAQ,CAAClL,KAAK,CAACnE,MAAM,CAAC,CAAC,EAAE;QACnH,IAAI,CAACsP,UAAU,CAAC1I,KAAK,CAAC,CAAC;QAEvB,IAAI,IAAI,CAACqH,KAAK,CAACE,cAAc,EAAE;UAC7B,IAAI,CAACoB,WAAW,CAAC,CAAC;QACpB,CAAC,MAAM;UACL,IAAI,CAACC,WAAW,CAAC,CAAC;QACpB;MACF;IACF;EACF,CAAC,EAAE;IACDnP,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAAS+L,YAAYA,CAACjK,KAAK,EAAE;MAClC,IAAIiC,MAAM,GAAG,IAAI;MAEjBjC,KAAK,CAACsL,OAAO,CAAC,CAAC;MAEf,IAAI,IAAI,CAAC1O,KAAK,CAAC2O,WAAW,IAAI,CAAC,IAAI,CAACzB,KAAK,CAACE,cAAc,EAAE;QACxD,IAAI,CAACqB,WAAW,CAAC,CAAC;MACpB;MAEA,IAAI,CAACG,QAAQ,CAAC;QACZzB,OAAO,EAAE;MACX,CAAC,EAAE,YAAY;QACb,IAAI9H,MAAM,CAACrF,KAAK,CAAC6O,OAAO,EAAE;UACxBxJ,MAAM,CAACrF,KAAK,CAAC6O,OAAO,CAACzL,KAAK,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASgM,WAAWA,CAAClK,KAAK,EAAE;MACjC,IAAIsC,MAAM,GAAG,IAAI;MAEjBtC,KAAK,CAACsL,OAAO,CAAC,CAAC;MACf,IAAI,CAACE,QAAQ,CAAC;QACZzB,OAAO,EAAE;MACX,CAAC,EAAE,YAAY;QACb,IAAIzH,MAAM,CAAC1F,KAAK,CAAC8O,MAAM,EAAE;UACvBpJ,MAAM,CAAC1F,KAAK,CAAC8O,MAAM,CAAC1L,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAASoM,YAAYA,CAACtK,KAAK,EAAE;MAClC7E,cAAc,CAACwQ,IAAI,CAAC,eAAe,EAAE;QACnC1L,aAAa,EAAED,KAAK;QACpBnE,MAAM,EAAE,IAAI,CAAC+P;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1P,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASiM,cAAcA,CAACnK,KAAK,EAAE;MACpC,QAAQA,KAAK,CAAC6L,KAAK;QACjB;QACA,KAAK,EAAE;UACL,IAAI,CAACC,SAAS,CAAC9L,KAAK,CAAC;UACrB;QACF;;QAEA,KAAK,EAAE;UACL,IAAI,CAAC+L,OAAO,CAAC/L,KAAK,CAAC;UACnB;QACF;;QAEA,KAAK,EAAE;UACL,IAAI,IAAI,CAAC8J,KAAK,CAACE,cAAc,EAAE,IAAI,CAACoB,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAACC,WAAW,CAAC,CAAC;UACzErL,KAAK,CAACgM,cAAc,CAAC,CAAC;UACtB;QACF;;QAEA,KAAK,EAAE;UACL,IAAI,CAACZ,WAAW,CAAC,CAAC;UAClBpL,KAAK,CAACgM,cAAc,CAAC,CAAC;UACtB;QACF;;QAEA,KAAK,EAAE;QACP,KAAK,CAAC;UACJ,IAAI,CAACZ,WAAW,CAAC,CAAC;UAClB;QAEF;UACE,IAAI,CAACa,MAAM,CAACjM,KAAK,CAAC;UAClB;MACJ;IACF;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAASyH,oBAAoBA,CAAC3F,KAAK,EAAE;MAC1C,QAAQA,KAAK,CAAC6L,KAAK;QACjB;QACA,KAAK,EAAE;UACL,IAAI,CAACC,SAAS,CAAC9L,KAAK,CAAC;UACrB;QACF;;QAEA,KAAK,EAAE;UACL,IAAI,CAAC+L,OAAO,CAAC/L,KAAK,CAAC;UACnB;QACF;;QAEA,KAAK,EAAE;QACP,KAAK,EAAE;UACL,IAAI,CAACoL,WAAW,CAAC,CAAC;UAClBpL,KAAK,CAACgM,cAAc,CAAC,CAAC;UACtB;MACJ;IACF;EACF,CAAC,EAAE;IACD9P,GAAG,EAAE,SAAS;IACdgC,KAAK,EAAE,SAAS6N,OAAOA,CAAC/L,KAAK,EAAE;MAC7B,IAAI2C,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,IAAIvJ,cAAc,EAAE;QAClB,IAAIwJ,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChK,sBAAsB,CAAC,CAAC,CAAC;QAEnE,IAAI+J,UAAU,EAAE;UACd,IAAI,CAACE,UAAU,CAAC;YACdpM,aAAa,EAAED,KAAK;YACpBE,MAAM,EAAEiM;UACV,CAAC,CAAC;QACJ;MACF;MAEAnM,KAAK,CAACgM,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD9P,GAAG,EAAE,WAAW;IAChBgC,KAAK,EAAE,SAAS4N,SAASA,CAAC9L,KAAK,EAAE;MAC/B,IAAI2C,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,IAAIvJ,cAAc,EAAE;QAClB,IAAI,CAAC,IAAI,CAACmH,KAAK,CAACE,cAAc,IAAIhK,KAAK,CAACsM,MAAM,EAAE;UAC9C,IAAI,CAACjB,WAAW,CAAC,CAAC;QACpB,CAAC,MAAM;UACL,IAAIkB,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpK,sBAAsB,CAAC,CAAC,CAAC;UAEnE,IAAImK,UAAU,EAAE;YACd,IAAI,CAACF,UAAU,CAAC;cACdpM,aAAa,EAAED,KAAK;cACpBE,MAAM,EAAEqM;YACV,CAAC,CAAC;UACJ;QACF;MACF;MAEAvM,KAAK,CAACgM,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACD9P,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASsO,cAAcA,CAACxI,KAAK,EAAE;MACpC,IAAIrB,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,IAAI,IAAI,CAACtP,KAAK,CAACqH,gBAAgB,EAAE;QAC/B,IAAIwI,UAAU,GAAGzI,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,KAAK,CAAC0I,KAAK;QAC/C,IAAIC,WAAW,GAAG3I,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK,CAAC9D,MAAM;QAClD,IAAIA,MAAM,GAAG,IAAI,CAAC0M,oBAAoB,CAAC,IAAI,CAAC3J,sBAAsB,CAACN,cAAc,CAAC8J,UAAU,CAAC,CAAC,EAAEE,WAAW,CAAC;QAC5G,IAAIzM,MAAM,EAAE,OAAOA,MAAM,CAAC,KAAK,IAAIuM,UAAU,GAAG,CAAC,KAAK9J,cAAc,CAAC3G,MAAM,EAAE,OAAO,IAAI,CAACwQ,cAAc,CAAC;UACtGE,KAAK,EAAED,UAAU,GAAG,CAAC;UACrBvM,MAAM,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI;MACrB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC0M,oBAAoB,CAACjK,cAAc,EAAEqB,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE;IACD9H,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAAS0O,oBAAoBA,CAACC,IAAI,EAAE7I,KAAK,EAAE;MAChD,IAAIlI,CAAC,GAAGkI,KAAK,GAAG,CAAC;MAEjB,IAAIlI,CAAC,KAAK+Q,IAAI,CAAC7Q,MAAM,EAAE;QACrB,OAAO,IAAI;MACb;MAEA,IAAIkE,MAAM,GAAG2M,IAAI,CAAC/Q,CAAC,CAAC;MACpB,IAAI,IAAI,CAAC0H,gBAAgB,CAACtD,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC0M,oBAAoB,CAAC9Q,CAAC,CAAC,CAAC,KAAK,OAAOoE,MAAM;IAC3F;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASkO,cAAcA,CAACpI,KAAK,EAAE;MACpC,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI;MACb;MAEA,IAAIrB,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,IAAI,IAAI,CAACtP,KAAK,CAACqH,gBAAgB,EAAE;QAC/B,IAAIwI,UAAU,GAAGzI,KAAK,CAAC0I,KAAK;QAC5B,IAAIC,WAAW,GAAG3I,KAAK,CAAC9D,MAAM;QAC9B,IAAIA,MAAM,GAAG,IAAI,CAAC4M,oBAAoB,CAAC,IAAI,CAAC7J,sBAAsB,CAACN,cAAc,CAAC8J,UAAU,CAAC,CAAC,EAAEE,WAAW,CAAC;QAC5G,IAAIzM,MAAM,EAAE,OAAOA,MAAM,CAAC,KAAK,IAAIuM,UAAU,GAAG,CAAC,EAAE,OAAO,IAAI,CAACL,cAAc,CAAC;UAC5EM,KAAK,EAAED,UAAU,GAAG,CAAC;UACrBvM,MAAM,EAAE,IAAI,CAAC+C,sBAAsB,CAACN,cAAc,CAAC8J,UAAU,GAAG,CAAC,CAAC,CAAC,CAACzQ;QACtE,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI;MACrB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC8Q,oBAAoB,CAACnK,cAAc,EAAEqB,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE;IACD9H,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAAS4O,oBAAoBA,CAACD,IAAI,EAAE7I,KAAK,EAAE;MAChD,IAAIlI,CAAC,GAAGkI,KAAK,GAAG,CAAC;MAEjB,IAAIlI,CAAC,GAAG,CAAC,EAAE;QACT,OAAO,IAAI;MACb;MAEA,IAAIoE,MAAM,GAAG2M,IAAI,CAAC/Q,CAAC,CAAC;MACpB,IAAI,IAAI,CAAC0H,gBAAgB,CAACtD,MAAM,CAAC,EAAE,OAAO,IAAI,CAACkM,cAAc,CAACtQ,CAAC,CAAC,CAAC,KAAK,OAAOoE,MAAM;IACrF;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAAS+N,MAAMA,CAACjM,KAAK,EAAE;MAC5B,IAAI+C,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACgK,aAAa,EAAE;QACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAClC;MAEA,IAAIE,IAAI,GAAGjN,KAAK,CAAC9D,GAAG;MACpB,IAAI,CAACgR,kBAAkB,GAAG,IAAI,CAACC,iBAAiB;MAChD,IAAI,CAACA,iBAAiB,GAAGF,IAAI;MAC7B,IAAI,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAAC,KAAK,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI,CAACA,WAAW,GAAGH,IAAI,GAAGA,IAAI;MAE3K,IAAI,IAAI,CAACG,WAAW,EAAE;QACpB,IAAIC,WAAW,GAAG,IAAI,CAACjL,sBAAsB,CAAC,CAAC;QAC/C,IAAIkL,SAAS,GAAG,IAAI,CAAC1Q,KAAK,CAACqH,gBAAgB,GAAG,IAAI,CAACsJ,mBAAmB,CAACF,WAAW,CAAC,GAAG,IAAI,CAACG,YAAY,CAAC,EAAEH,WAAW,CAAC;QAEtH,IAAIC,SAAS,EAAE;UACb,IAAI,CAACjB,UAAU,CAAC;YACdpM,aAAa,EAAED,KAAK;YACpBE,MAAM,EAAEoN;UACV,CAAC,CAAC;UACF,IAAI,CAACG,qBAAqB,GAAG,IAAI;QACnC;MACF;MAEA,IAAI,CAACV,aAAa,GAAGW,UAAU,CAAC,YAAY;QAC1C3K,MAAM,CAACqK,WAAW,GAAG,IAAI;MAC3B,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE;IACDlR,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAASsP,YAAYA,CAACxJ,KAAK,EAAE;MAClC,IAAI9D,MAAM;MAEV,IAAI,IAAI,CAACkN,WAAW,EAAE;QACpB,IAAIzK,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;QAC7ChM,MAAM,GAAG,IAAI,CAACyN,mBAAmB,CAAC3J,KAAK,EAAErB,cAAc,CAAC3G,MAAM,CAAC;QAE/D,IAAI,CAACkE,MAAM,EAAE;UACXA,MAAM,GAAG,IAAI,CAACyN,mBAAmB,CAAC,CAAC,EAAE3J,KAAK,CAAC;QAC7C;MACF;MAEA,OAAO9D,MAAM;IACf;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAASyP,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;MAC9C,IAAIlL,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,KAAK,IAAIpQ,CAAC,GAAG8R,KAAK,EAAE9R,CAAC,GAAG+R,GAAG,EAAE/R,CAAC,EAAE,EAAE;QAChC,IAAIgS,GAAG,GAAGnL,cAAc,CAAC7G,CAAC,CAAC;QAE3B,IAAI,IAAI,CAACiS,kBAAkB,CAACD,GAAG,CAAC,EAAE;UAChC,OAAOA,GAAG;QACZ;MACF;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD5R,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAASqP,mBAAmBA,CAACvJ,KAAK,EAAE;MACzC,IAAIqJ,WAAW,GAAGrJ,KAAK,KAAK,CAAC,CAAC,GAAG;QAC/B0I,KAAK,EAAE,CAAC;QACRxM,MAAM,EAAE,CAAC;MACX,CAAC,GAAG8D,KAAK;MACT,IAAIrB,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,KAAK,IAAIpQ,CAAC,GAAGuR,WAAW,CAACX,KAAK,EAAE5Q,CAAC,GAAG6G,cAAc,CAAC3G,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC9D,IAAIkS,YAAY,GAAG,IAAI,CAAC/K,sBAAsB,CAACN,cAAc,CAAC7G,CAAC,CAAC,CAAC;QAEjE,KAAK,IAAIqH,CAAC,GAAGkK,WAAW,CAACX,KAAK,KAAK5Q,CAAC,GAAGuR,WAAW,CAACnN,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEiD,CAAC,GAAG6K,YAAY,CAAChS,MAAM,EAAEmH,CAAC,EAAE,EAAE;UAC/F,IAAI,IAAI,CAAC4K,kBAAkB,CAACC,YAAY,CAAC7K,CAAC,CAAC,CAAC,EAAE;YAC5C,OAAO6K,YAAY,CAAC7K,CAAC,CAAC;UACxB;QACF;MACF;MAEA,KAAK,IAAI8K,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIZ,WAAW,CAACX,KAAK,EAAEuB,EAAE,EAAE,EAAE;QAC9C,IAAIC,aAAa,GAAG,IAAI,CAACjL,sBAAsB,CAACN,cAAc,CAACsL,EAAE,CAAC,CAAC;QAEnE,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAId,WAAW,CAACX,KAAK,KAAKuB,EAAE,GAAGZ,WAAW,CAACnN,MAAM,GAAGgO,aAAa,CAAClS,MAAM,CAAC,EAAEmS,EAAE,EAAE,EAAE;UAClG,IAAI,IAAI,CAACJ,kBAAkB,CAACG,aAAa,CAACC,EAAE,CAAC,CAAC,EAAE;YAC9C,OAAOD,aAAa,CAACC,EAAE,CAAC;UAC1B;QACF;MACF;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjS,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAAS6P,kBAAkBA,CAAC7N,MAAM,EAAE;MACzC,IAAIK,KAAK,GAAG,IAAI,CAAC8C,cAAc,CAACnD,MAAM,CAAC,CAACkO,iBAAiB,CAAC,IAAI,CAACxR,KAAK,CAACyR,YAAY,CAAC;MAClF,OAAO9N,KAAK,CAAC+N,UAAU,CAAC,IAAI,CAAClB,WAAW,CAACgB,iBAAiB,CAAC,IAAI,CAACxR,KAAK,CAACyR,YAAY,CAAC,CAAC;IACtF;EACF,CAAC,EAAE;IACDnS,GAAG,EAAE,uBAAuB;IAC5BgC,KAAK,EAAE,SAASkM,qBAAqBA,CAACpK,KAAK,EAAE;MAC3C,IAAI,IAAI,CAACpD,KAAK,CAACgJ,QAAQ,EAAE;QACvB,IAAI,CAAChJ,KAAK,CAACgJ,QAAQ,CAAC;UAClB3F,aAAa,EAAED,KAAK,CAACC,aAAa;UAClC/B,KAAK,EAAE8B,KAAK,CAACnE,MAAM,CAACqC,KAAK;UACzBqQ,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG,CAAC,CAAC;UAC9CvC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;UAC5CnQ,MAAM,EAAE;YACNwN,IAAI,EAAE,IAAI,CAACzM,KAAK,CAACyM,IAAI;YACrBmF,EAAE,EAAE,IAAI,CAAC5R,KAAK,CAAC4R,EAAE;YACjBtQ,KAAK,EAAE8B,KAAK,CAACnE,MAAM,CAACqC;UACtB;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAASmM,oBAAoBA,CAACrK,KAAK,EAAE;MAC1C,IAAIyE,MAAM,GAAG,IAAI;MAEjBzE,KAAK,CAACsL,OAAO,CAAC,CAAC;MACf,IAAI,CAACE,QAAQ,CAAC;QACZzB,OAAO,EAAE;MACX,CAAC,EAAE,YAAY;QACbtF,MAAM,CAAC2G,WAAW,CAAC,CAAC;QAEpB,IAAI3G,MAAM,CAAC7H,KAAK,CAAC6O,OAAO,EAAE;UACxBhH,MAAM,CAAC7H,KAAK,CAAC6O,OAAO,CAACzL,KAAK,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9D,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAASyF,aAAaA,CAAC3D,KAAK,EAAE;MACnC,IAAIE,MAAM,GAAGF,KAAK,CAACE,MAAM;MAEzB,IAAI,CAACA,MAAM,CAACI,QAAQ,EAAE;QACpB,IAAI,CAAC+L,UAAU,CAACrM,KAAK,CAAC;QACtB,IAAI,CAACmL,UAAU,CAAC1I,KAAK,CAAC,CAAC;MACzB;MAEA,IAAI,CAAC2I,WAAW,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDlP,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAAS8D,mBAAmBA,CAAChC,KAAK,EAAE;MACzC,IAAI4E,MAAM,GAAG,IAAI;MAEjB,IAAIzD,MAAM,GAAGnB,KAAK,CAACnE,MAAM,CAACqC,KAAK;MAC/B,IAAI,CAACsN,QAAQ,CAAC;QACZrK,MAAM,EAAEA;MACV,CAAC,EAAE,YAAY;QACb,IAAIyD,MAAM,CAAChI,KAAK,CAAC6R,QAAQ,EAAE;UACzB7J,MAAM,CAAChI,KAAK,CAAC6R,QAAQ,CAAC;YACpBxO,aAAa,EAAED,KAAK;YACpBmB,MAAM,EAAEA;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,wBAAwB;IAC7BgC,KAAK,EAAE,SAAS6G,sBAAsBA,CAAC2J,QAAQ,EAAE;MAC/C,IAAI,CAAC/D,WAAW,CAAC+D,QAAQ,CAAC;IAC5B;EACF,CAAC,EAAE;IACDxS,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASyM,WAAWA,CAAC+D,QAAQ,EAAE;MACpC,IAAIzJ,MAAM,GAAG,IAAI;MAEjB,IAAI9D,MAAM,GAAG,EAAE;MACf,IAAI,CAACqK,QAAQ,CAAC;QACZrK,MAAM,EAAEA;MACV,CAAC,EAAE,YAAY;QACb8D,MAAM,CAACrI,KAAK,CAAC6R,QAAQ,IAAIxJ,MAAM,CAACrI,KAAK,CAAC6R,QAAQ,CAAC;UAC7CtN,MAAM,EAAEA;QACV,CAAC,CAAC;QACFuN,QAAQ,IAAIA,QAAQ,CAAC,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDxS,GAAG,EAAE,OAAO;IACZgC,KAAK,EAAE,SAAS0M,KAAKA,CAAC5K,KAAK,EAAE;MAC3B,IAAI,IAAI,CAACpD,KAAK,CAACgJ,QAAQ,EAAE;QACvB,IAAI,CAAChJ,KAAK,CAACgJ,QAAQ,CAAC;UAClB3F,aAAa,EAAED,KAAK;UACpB9B,KAAK,EAAEyQ,SAAS;UAChBJ,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG,CAAC,CAAC;UAC9CvC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;UAC5CnQ,MAAM,EAAE;YACNwN,IAAI,EAAE,IAAI,CAACzM,KAAK,CAACyM,IAAI;YACrBmF,EAAE,EAAE,IAAI,CAAC5R,KAAK,CAAC4R,EAAE;YACjBtQ,KAAK,EAAEyQ;UACT;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE;IACD1S,GAAG,EAAE,YAAY;IACjBgC,KAAK,EAAE,SAASmO,UAAUA,CAACrM,KAAK,EAAE;MAChC,IAAI6O,qBAAqB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAEpD,IAAID,qBAAqB,KAAK7O,KAAK,CAACE,MAAM,EAAE;QAC1C,IAAI,CAAC0O,mBAAmB,CAAC5O,KAAK,CAACE,MAAM,CAAC;QACtC,IAAI6O,WAAW,GAAG,IAAI,CAACC,cAAc,CAAChP,KAAK,CAACE,MAAM,CAAC;QAEnD,IAAI,IAAI,CAACtD,KAAK,CAACgJ,QAAQ,EAAE;UACvB,IAAI,CAAChJ,KAAK,CAACgJ,QAAQ,CAAC;YAClB3F,aAAa,EAAED,KAAK,CAACC,aAAa;YAClC/B,KAAK,EAAE6Q,WAAW;YAClBR,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG,CAAC,CAAC;YAC9CvC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;YAC5CnQ,MAAM,EAAE;cACNwN,IAAI,EAAE,IAAI,CAACzM,KAAK,CAACyM,IAAI;cACrBmF,EAAE,EAAE,IAAI,CAAC5R,KAAK,CAAC4R,EAAE;cACjBtQ,KAAK,EAAE6Q;YACT;UACF,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACD7S,GAAG,EAAE,mBAAmB;IACxBgC,KAAK,EAAE,SAAS4Q,iBAAiBA,CAAA,EAAG;MAClC,IAAI9K,KAAK,GAAG,IAAI,CAAC5B,sBAAsB,CAAC,CAAC;MACzC,IAAIO,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAC7C,OAAOlI,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACpH,KAAK,CAACqH,gBAAgB,GAAG,IAAI,CAAChB,sBAAsB,CAACN,cAAc,CAACqB,KAAK,CAAC0I,KAAK,CAAC,CAAC,CAAC1I,KAAK,CAAC9D,MAAM,CAAC,GAAGyC,cAAc,CAACqB,KAAK,CAAC,GAAG,IAAI;IAC3J;EACF,CAAC,EAAE;IACD9H,GAAG,EAAE,wBAAwB;IAC7BgC,KAAK,EAAE,SAASkE,sBAAsBA,CAAA,EAAG;MACvC,IAAIO,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAE7C,IAAI,IAAI,CAACtP,KAAK,CAACsB,KAAK,IAAI,IAAI,IAAIyE,cAAc,EAAE;QAC9C,IAAI,IAAI,CAAC/F,KAAK,CAACqH,gBAAgB,EAAE;UAC/B,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6G,cAAc,CAAC3G,MAAM,EAAEF,CAAC,EAAE,EAAE;YAC9C,IAAImT,mBAAmB,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACtS,KAAK,CAACsB,KAAK,EAAE,IAAI,CAAC+E,sBAAsB,CAACN,cAAc,CAAC7G,CAAC,CAAC,CAAC,CAAC;YAEtH,IAAImT,mBAAmB,KAAK,CAAC,CAAC,EAAE;cAC9B,OAAO;gBACLvC,KAAK,EAAE5Q,CAAC;gBACRoE,MAAM,EAAE+O;cACV,CAAC;YACH;UACF;QACF,CAAC,MAAM;UACL,OAAO,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACtS,KAAK,CAACsB,KAAK,EAAEyE,cAAc,CAAC;QACrE;MACF;MAEA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,uBAAuB;IAC5BgC,KAAK,EAAE,SAASgR,qBAAqBA,CAAChR,KAAK,EAAE2O,IAAI,EAAE;MACjD,IAAI3Q,GAAG,GAAG,IAAI,CAACiT,WAAW,CAAC,CAAC;MAE5B,KAAK,IAAIrT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Q,IAAI,CAAC7Q,MAAM,EAAEF,CAAC,EAAE,EAAE;QACpC,IAAIhB,WAAW,CAACsU,MAAM,CAAClR,KAAK,EAAE,IAAI,CAAC8Q,cAAc,CAACnC,IAAI,CAAC/Q,CAAC,CAAC,CAAC,EAAEI,GAAG,CAAC,EAAE;UAChE,OAAOJ,CAAC;QACV;MACF;MAEA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,YAAY;IACjBgC,KAAK,EAAE,SAASwF,UAAUA,CAACxD,MAAM,EAAE;MACjC,OAAOpF,WAAW,CAACsU,MAAM,CAAC,IAAI,CAACxS,KAAK,CAACsB,KAAK,EAAE,IAAI,CAAC8Q,cAAc,CAAC9O,MAAM,CAAC,EAAE,IAAI,CAACiP,WAAW,CAAC,CAAC,CAAC;IAC9F;EACF,CAAC,EAAE;IACDjT,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASiR,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAACvS,KAAK,CAACmS,WAAW,GAAG,IAAI,GAAG,IAAI,CAACnS,KAAK,CAACyS,OAAO;IAC3D;EACF,CAAC,EAAE;IACDnT,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASmN,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACG,QAAQ,CAAC;QACZxB,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9N,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASkN,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACI,QAAQ,CAAC;QACZxB,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD9N,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASqM,cAAcA,CAACmE,QAAQ,EAAE;MACvCtT,WAAW,CAACkU,GAAG,CAAC,SAAS,EAAE,IAAI,CAACzE,UAAU,CAACI,OAAO,CAAC;MACnD,IAAI,CAACsE,YAAY,CAAC,CAAC;MACnBb,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDxS,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASsM,gBAAgBA,CAACkE,QAAQ,EAAE;MACzC,IAAI,CAACc,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzBhB,QAAQ,IAAIA,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC9R,KAAK,CAAC+S,MAAM,IAAI,IAAI,CAAC/S,KAAK,CAAC+S,MAAM,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACDzT,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAASuM,aAAaA,CAAA,EAAG;MAC9B,IAAI,CAACmF,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE;IACD5T,GAAG,EAAE,iBAAiB;IACtBgC,KAAK,EAAE,SAASwM,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAAC9N,KAAK,CAACuE,MAAM,IAAI,IAAI,CAACvE,KAAK,CAACmT,iBAAiB,EAAE;QACrD,IAAI,CAACpF,WAAW,CAAC,CAAC;MACpB;MAEAvP,WAAW,CAACwP,KAAK,CAAC,IAAI,CAACC,UAAU,CAACI,OAAO,CAAC;MAC1C,IAAI,CAACrO,KAAK,CAACoT,MAAM,IAAI,IAAI,CAACpT,KAAK,CAACoT,MAAM,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACD9T,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAASqR,YAAYA,CAAA,EAAG;MAC7BrU,UAAU,CAACqU,YAAY,CAAC,IAAI,CAAC1E,UAAU,CAACI,OAAO,EAAE,IAAI,CAACgF,KAAK,CAACC,aAAa,EAAE,IAAI,CAACtT,KAAK,CAAC8K,QAAQ,IAAIjM,UAAU,CAACiM,QAAQ,CAAC;IACxH;EACF,CAAC,EAAE;IACDxL,GAAG,EAAE,cAAc;IACnBgC,KAAK,EAAE,SAASiS,YAAYA,CAAA,EAAG;MAC7B,IAAIC,aAAa,GAAGlV,UAAU,CAACmV,UAAU,CAAC,IAAI,CAACxF,UAAU,CAACI,OAAO,EAAE,gBAAgB,CAAC;MAEpF,IAAImF,aAAa,EAAE;QACjBA,aAAa,CAACE,cAAc,CAAC;UAC3BC,KAAK,EAAE,SAAS;UAChBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDtU,GAAG,EAAE,2BAA2B;IAChCgC,KAAK,EAAE,SAASsR,yBAAyBA,CAAA,EAAG;MAC1C,IAAI1J,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAC2K,qBAAqB,EAAE;QAC/B,IAAI,CAACA,qBAAqB,GAAG,UAAUzQ,KAAK,EAAE;UAC5C,IAAI8F,MAAM,CAACgE,KAAK,CAACE,cAAc,IAAIlE,MAAM,CAAC4K,gBAAgB,CAAC1Q,KAAK,CAAC,EAAE;YACjE8F,MAAM,CAACsF,WAAW,CAAC,CAAC;UACtB;QACF,CAAC;QAEDuF,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACH,qBAAqB,CAAC;MAChE;IACF;EACF,CAAC,EAAE;IACDvU,GAAG,EAAE,6BAA6B;IAClCgC,KAAK,EAAE,SAAS0R,2BAA2BA,CAAA,EAAG;MAC5C,IAAI,IAAI,CAACa,qBAAqB,EAAE;QAC9BE,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACJ,qBAAqB,CAAC;QACjE,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACnC;IACF;EACF,CAAC,EAAE;IACDvU,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAASuR,kBAAkBA,CAAA,EAAG;MACnC,IAAIqB,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;QACvB,IAAI,CAACA,aAAa,GAAG,IAAI1V,6BAA6B,CAAC,IAAI,CAACuQ,SAAS,EAAE,YAAY;UACjF,IAAIkF,MAAM,CAAChH,KAAK,CAACE,cAAc,EAAE;YAC/B8G,MAAM,CAAC1F,WAAW,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC2F,aAAa,CAACtB,kBAAkB,CAAC,CAAC;IACzC;EACF,CAAC,EAAE;IACDvT,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAAS2R,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACkB,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAAClB,oBAAoB,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,EAAE;IACD3T,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAASwR,kBAAkBA,CAAA,EAAG;MACnC,IAAIsB,OAAO,GAAG,IAAI;MAElB,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;QACxB,IAAI,CAACA,cAAc,GAAG,YAAY;UAChC,IAAID,OAAO,CAAClH,KAAK,CAACE,cAAc,IAAI,CAAC9O,UAAU,CAACgW,aAAa,CAAC,CAAC,EAAE;YAC/DF,OAAO,CAAC5F,WAAW,CAAC,CAAC;UACvB;QACF,CAAC;QAED+F,MAAM,CAACP,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACK,cAAc,CAAC;MACxD;IACF;EACF,CAAC,EAAE;IACD/U,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAAS4R,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACmB,cAAc,EAAE;QACvBE,MAAM,CAACN,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACI,cAAc,CAAC;QACzD,IAAI,CAACA,cAAc,GAAG,IAAI;MAC5B;IACF;EACF,CAAC,EAAE;IACD/U,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASwS,gBAAgBA,CAAC1Q,KAAK,EAAE;MACtC,OAAO,IAAI,CAAC4L,SAAS,IAAI,EAAE,IAAI,CAACA,SAAS,CAACwF,UAAU,CAACpR,KAAK,CAACnE,MAAM,CAAC,IAAI,IAAI,CAACwV,cAAc,CAACrR,KAAK,CAAC,IAAI,IAAI,CAAC4L,SAAS,CAACV,QAAQ,CAAClL,KAAK,CAACnE,MAAM,CAAC,IAAI,IAAI,CAACgP,UAAU,IAAI,IAAI,CAACA,UAAU,CAACI,OAAO,CAACC,QAAQ,CAAClL,KAAK,CAACnE,MAAM,CAAC,CAAC;IACjN;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASmT,cAAcA,CAACrR,KAAK,EAAE;MACpC,OAAO9E,UAAU,CAAC6P,QAAQ,CAAC/K,KAAK,CAACnE,MAAM,EAAE,uBAAuB,CAAC;IACnE;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAAS0Q,mBAAmBA,CAAC1O,MAAM,EAAE;MAC1C,IAAI,IAAI,CAAC+P,KAAK,EAAE;QACd,IAAI,CAACA,KAAK,CAAC/R,KAAK,GAAGgC,MAAM,GAAG,IAAI,CAACmD,cAAc,CAACnD,MAAM,CAAC,GAAG,IAAI,CAACtD,KAAK,CAACsB,KAAK,IAAI,EAAE;MAClF;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,WAAW;IAChBgC,KAAK,EAAE,SAAS0E,SAASA,CAAA,EAAG;MAC1B,OAAO,IAAI,CAACkH,KAAK,CAAC3I,MAAM,IAAI,IAAI,CAAC2I,KAAK,CAAC3I,MAAM,CAACmQ,IAAI,CAAC,CAAC,CAACtV,MAAM,GAAG,CAAC;IACjE;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASmF,cAAcA,CAACnD,MAAM,EAAE;MACrC,OAAO,IAAI,CAACtD,KAAK,CAACwG,WAAW,GAAGtI,WAAW,CAACyW,gBAAgB,CAACrR,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACwG,WAAW,CAAC,GAAGlD,MAAM,IAAIA,MAAM,CAAC,OAAO,CAAC,KAAKyO,SAAS,GAAGzO,MAAM,CAAC,OAAO,CAAC,GAAGA,MAAM;IACnK;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAAS8Q,cAAcA,CAAC9O,MAAM,EAAE;MACrC,OAAO,IAAI,CAACtD,KAAK,CAACmS,WAAW,GAAGjU,WAAW,CAACyW,gBAAgB,CAACrR,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACmS,WAAW,CAAC,GAAG7O,MAAM,IAAIA,MAAM,CAAC,OAAO,CAAC,KAAKyO,SAAS,GAAGzO,MAAM,CAAC,OAAO,CAAC,GAAGA,MAAM;IACnK;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAASqF,kBAAkBA,CAACrD,MAAM,EAAE;MACzC,OAAO,IAAI,CAACtD,KAAK,CAACyS,OAAO,GAAGvU,WAAW,CAACyW,gBAAgB,CAACrR,MAAM,EAAE,IAAI,CAACtD,KAAK,CAACyS,OAAO,CAAC,GAAG,IAAI,CAAChM,cAAc,CAACnD,MAAM,CAAC;IACpH;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASsF,gBAAgBA,CAACtD,MAAM,EAAE;MACvC,IAAI,IAAI,CAACtD,KAAK,CAAC4U,cAAc,EAAE;QAC7B,OAAO1W,WAAW,CAAC2W,UAAU,CAAC,IAAI,CAAC7U,KAAK,CAAC4U,cAAc,CAAC,GAAG,IAAI,CAAC5U,KAAK,CAAC4U,cAAc,CAACtR,MAAM,CAAC,GAAGpF,WAAW,CAACyW,gBAAgB,CAACrR,MAAM,EAAE,IAAI,CAACtD,KAAK,CAAC4U,cAAc,CAAC;MAChK;MAEA,OAAOtR,MAAM,IAAIA,MAAM,CAAC,UAAU,CAAC,KAAKyO,SAAS,GAAGzO,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK;IAChF;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,yBAAyB;IAC9BgC,KAAK,EAAE,SAASoG,uBAAuBA,CAACxB,WAAW,EAAE;MACnD,OAAOhI,WAAW,CAACyW,gBAAgB,CAACzO,WAAW,EAAE,IAAI,CAAClG,KAAK,CAACqH,gBAAgB,CAAC;IAC/E;EACF,CAAC,EAAE;IACD/H,GAAG,EAAE,qBAAqB;IAC1BgC,KAAK,EAAE,SAASkG,mBAAmBA,CAACtB,WAAW,EAAE;MAC/C,OAAOhI,WAAW,CAACyW,gBAAgB,CAACzO,WAAW,EAAE,IAAI,CAAClG,KAAK,CAACqH,gBAAgB,CAAC;IAC/E;EACF,CAAC,EAAE;IACD/H,GAAG,EAAE,wBAAwB;IAC7BgC,KAAK,EAAE,SAAS+E,sBAAsBA,CAACH,WAAW,EAAE;MAClD,OAAOhI,WAAW,CAACyW,gBAAgB,CAACzO,WAAW,EAAE,IAAI,CAAClG,KAAK,CAAC8U,mBAAmB,CAAC;IAClF;EACF,CAAC,EAAE;IACDxV,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAASyT,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAAC7G,QAAQ,CAACG,OAAO,CAAC0G,aAAa,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE;IACDzV,GAAG,EAAE,mBAAmB;IACxBgC,KAAK,EAAE,SAASgO,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACtJ,SAAS,CAAC,CAAC,EAAE;QACpB,IAAIkC,WAAW,GAAG,IAAI,CAACgF,KAAK,CAAC3I,MAAM,CAACmQ,IAAI,CAAC,CAAC,CAAClD,iBAAiB,CAAC,IAAI,CAACxR,KAAK,CAACyR,YAAY,CAAC;QACrF,IAAIuD,YAAY,GAAG,IAAI,CAAChV,KAAK,CAACiV,QAAQ,GAAG,IAAI,CAACjV,KAAK,CAACiV,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAClV,KAAK,CAACwG,WAAW,IAAI,OAAO,CAAC;QAE7G,IAAI,IAAI,CAACxG,KAAK,CAACqH,gBAAgB,EAAE;UAC/B,IAAI8N,cAAc,GAAG,EAAE;UAEvB,IAAIC,SAAS,GAAGlK,0BAA0B,CAAC,IAAI,CAAClL,KAAK,CAAC2J,OAAO,CAAC;YAC1D0L,KAAK;UAET,IAAI;YACF,KAAKD,SAAS,CAAC3J,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC4J,KAAK,GAAGD,SAAS,CAAC1J,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;cAClD,IAAI2J,QAAQ,GAAGD,KAAK,CAAC/T,KAAK;cAC1B,IAAIiU,kBAAkB,GAAG7W,WAAW,CAAC6F,MAAM,CAAC,IAAI,CAAC8B,sBAAsB,CAACiP,QAAQ,CAAC,EAAEN,YAAY,EAAE9M,WAAW,EAAE,IAAI,CAAClI,KAAK,CAACwV,eAAe,EAAE,IAAI,CAACxV,KAAK,CAACyR,YAAY,CAAC;cAElK,IAAI8D,kBAAkB,IAAIA,kBAAkB,CAACnW,MAAM,EAAE;gBACnD+V,cAAc,CAACzQ,IAAI,CAACuG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqK,QAAQ,CAAC,EAAE;kBAC7D9L,KAAK,EAAE+L;gBACT,CAAC,CAAC,CAAC;cACL;YACF;UACF,CAAC,CAAC,OAAOvJ,GAAG,EAAE;YACZoJ,SAAS,CAACvS,CAAC,CAACmJ,GAAG,CAAC;UAClB,CAAC,SAAS;YACRoJ,SAAS,CAACvJ,CAAC,CAAC,CAAC;UACf;UAEA,OAAOsJ,cAAc;QACvB,CAAC,MAAM;UACL,OAAOzW,WAAW,CAAC6F,MAAM,CAAC,IAAI,CAACvE,KAAK,CAAC2J,OAAO,EAAEqL,YAAY,EAAE9M,WAAW,EAAE,IAAI,CAAClI,KAAK,CAACwV,eAAe,EAAE,IAAI,CAACxV,KAAK,CAACyR,YAAY,CAAC;QAC/H;MACF,CAAC,MAAM;QACL,OAAO,IAAI,CAACzR,KAAK,CAAC2J,OAAO;MAC3B;IACF;EACF,CAAC,EAAE;IACDrK,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASmU,gBAAgBA,CAAA,EAAG;MACjC,IAAI,IAAI,CAACzV,KAAK,CAAC0V,QAAQ,IAAI,IAAI,CAACrC,KAAK,EAAE;QACrC,IAAIsC,cAAc,GAAG,IAAI,CAACzD,iBAAiB,CAAC,CAAC;QAC7C,IAAIvO,KAAK,GAAGgS,cAAc,GAAG,IAAI,CAAClP,cAAc,CAACkP,cAAc,CAAC,GAAG,IAAI;QACvE,IAAIrU,KAAK,GAAGqC,KAAK,IAAI,IAAI,CAAC3D,KAAK,CAACsB,KAAK,IAAI,EAAE;QAC3C,IAAI,CAAC+R,KAAK,CAAC/R,KAAK,GAAGA,KAAK;MAC1B;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAASsU,cAAcA,CAAA,EAAG;MAC/B,IAAIpN,GAAG,GAAG,IAAI,CAACxI,KAAK,CAACkO,QAAQ;MAE7B,IAAI1F,GAAG,EAAE;QACP,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;UAC7BA,GAAG,CAAC,IAAI,CAAC0F,QAAQ,CAACG,OAAO,CAAC;QAC5B,CAAC,MAAM;UACL7F,GAAG,CAAC6F,OAAO,GAAG,IAAI,CAACH,QAAQ,CAACG,OAAO;QACrC;MACF;IACF;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,mBAAmB;IACxBgC,KAAK,EAAE,SAASuU,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACD,cAAc,CAAC,CAAC;MAErB,IAAI,IAAI,CAAC5V,KAAK,CAAC8V,SAAS,IAAI,IAAI,CAACvH,UAAU,EAAE;QAC3C,IAAI,CAACA,UAAU,CAAC1I,KAAK,CAAC,CAAC;MACzB;MAEA,IAAI,IAAI,CAAC7F,KAAK,CAAC+V,OAAO,EAAE;QACtB,IAAI,CAACC,aAAa,CAAC,CAAC;MACtB;MAEA,IAAI,CAACP,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACvH,QAAQ,CAACG,OAAO,CAAC9I,aAAa,GAAG,CAAC;IACzC;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAAS2U,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACjD,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACE,oBAAoB,CAAC,CAAC;MAE3B,IAAI,IAAI,CAACiB,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAAC+B,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC/B,aAAa,GAAG,IAAI;MAC3B;MAEA,IAAI,IAAI,CAAC4B,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACG,OAAO,CAAC,CAAC;QACtB,IAAI,CAACH,OAAO,GAAG,IAAI;MACrB;MAEA,IAAI,IAAI,CAACI,WAAW,EAAE;QACpB/F,YAAY,CAAC,IAAI,CAAC+F,WAAW,CAAC;QAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;MACzB;MAEA3X,WAAW,CAACwP,KAAK,CAAC,IAAI,CAACC,UAAU,CAACI,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAAS8U,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACnJ,KAAK,CAACE,cAAc,EAAE;QAC7B,IAAI,IAAI,CAACpN,KAAK,CAACuE,MAAM,EAAE;UACrB,IAAI,CAACoO,YAAY,CAAC,CAAC;QACrB;QAEA,IAAI0D,SAAS,CAAC/U,KAAK,KAAK,IAAI,CAACtB,KAAK,CAACsB,KAAK,EAAE;UACxC,IAAI,CAACiS,YAAY,CAAC,CAAC;QACrB;MACF;MAEA,IAAI8C,SAAS,CAACN,OAAO,KAAK,IAAI,CAAC/V,KAAK,CAAC+V,OAAO,IAAIM,SAAS,CAACC,cAAc,KAAK,IAAI,CAACtW,KAAK,CAACsW,cAAc,EAAE;QACtG,IAAI,IAAI,CAACP,OAAO,EAAE,IAAI,CAACA,OAAO,CAACQ,MAAM,CAACtL,aAAa,CAAC;UAClDrH,OAAO,EAAE,IAAI,CAAC5D,KAAK,CAAC+V;QACtB,CAAC,EAAE,IAAI,CAAC/V,KAAK,CAACsW,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAACN,aAAa,CAAC,CAAC;MAChE;MAEA,IAAI,IAAI,CAAC9I,KAAK,CAAC3I,MAAM,KAAK,CAAC,IAAI,CAACvE,KAAK,CAAC2J,OAAO,IAAI,IAAI,CAAC3J,KAAK,CAAC2J,OAAO,CAACvK,MAAM,KAAK,CAAC,CAAC,EAAE;QACjF,IAAI,CAACwP,QAAQ,CAAC;UACZrK,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;MAEA,IAAI,CAACkR,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACvH,QAAQ,CAACG,OAAO,CAAC9I,aAAa,GAAG,CAAC;IACzC;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAASkV,kBAAkBA,CAACb,cAAc,EAAE;MACjD,IAAIc,iBAAiB,GAAG,aAAa3Y,KAAK,CAACiG,aAAa,CAAC,QAAQ,EAAE;QACjEzC,KAAK,EAAE;MACT,CAAC,EAAE,IAAI,CAACtB,KAAK,CAAC4I,WAAW,CAAC;MAC1B,IAAItF,MAAM,GAAGqS,cAAc,GAAG,aAAa7X,KAAK,CAACiG,aAAa,CAAC,QAAQ,EAAE;QACvEzC,KAAK,EAAEqU,cAAc,CAACrU;MACxB,CAAC,EAAE,IAAI,CAACmF,cAAc,CAACkP,cAAc,CAAC,CAAC,GAAG,IAAI;MAC9C,OAAO,aAAa7X,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;QAC7CP,SAAS,EAAE;MACb,CAAC,EAAE,aAAa1F,KAAK,CAACiG,aAAa,CAAC,QAAQ,EAAE;QAC5CyE,GAAG,EAAE,IAAI,CAAC0F,QAAQ;QAClBwI,QAAQ,EAAE,IAAI,CAAC1W,KAAK,CAAC0W,QAAQ;QAC7BjK,IAAI,EAAE,IAAI,CAACzM,KAAK,CAACyM,IAAI;QACrBkK,QAAQ,EAAE,CAAC,CAAC;QACZ,aAAa,EAAE;MACjB,CAAC,EAAEF,iBAAiB,EAAEnT,MAAM,CAAC,CAAC;IAChC;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAAS0U,aAAaA,CAAA,EAAG;MAC9B,IAAI,CAACD,OAAO,GAAGpX,GAAG,CAAC;QACjBM,MAAM,EAAE,IAAI,CAAC+P,SAAS;QACtBpL,OAAO,EAAE,IAAI,CAAC5D,KAAK,CAAC+V,OAAO;QAC3BpM,OAAO,EAAE,IAAI,CAAC3J,KAAK,CAACsW;MACtB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhX,GAAG,EAAE,sBAAsB;IAC3BgC,KAAK,EAAE,SAASsV,oBAAoBA,CAAA,EAAG;MACrC,IAAIC,OAAO,GAAG,IAAI;MAElB,OAAO,aAAa/Y,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;QAC7CP,SAAS,EAAE;MACb,CAAC,EAAE,aAAa1F,KAAK,CAACiG,aAAa,CAAC,OAAO,EAAE;QAC3CyE,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOoO,OAAO,CAACtI,UAAU,GAAG9F,EAAE;QAChC,CAAC;QACDmJ,EAAE,EAAE,IAAI,CAAC5R,KAAK,CAAC8W,OAAO;QACtBpO,IAAI,EAAE,MAAM;QACZqO,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,SAAS;QAC1BlI,OAAO,EAAE,IAAI,CAACxB,YAAY;QAC1ByB,MAAM,EAAE,IAAI,CAACxB,WAAW;QACxBxE,SAAS,EAAE,IAAI,CAACyE,cAAc;QAC9B7J,QAAQ,EAAE,IAAI,CAAC1D,KAAK,CAAC0D,QAAQ;QAC7BiT,QAAQ,EAAE,IAAI,CAAC3W,KAAK,CAAC2W,QAAQ;QAC7B,YAAY,EAAE,IAAI,CAAC3W,KAAK,CAACgX,SAAS;QAClC,iBAAiB,EAAE,IAAI,CAAChX,KAAK,CAACiX;MAChC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD3X,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAAS4V,WAAWA,CAACvB,cAAc,EAAE;MAC1C,IAAIwB,OAAO,GAAG,IAAI;MAElB,IAAIxT,KAAK,GAAGgS,cAAc,GAAG,IAAI,CAAClP,cAAc,CAACkP,cAAc,CAAC,GAAG,IAAI;MAEvE,IAAI,IAAI,CAAC3V,KAAK,CAAC0V,QAAQ,EAAE;QACvB,IAAIpU,KAAK,GAAGqC,KAAK,IAAI,IAAI,CAAC3D,KAAK,CAACsB,KAAK,IAAI,EAAE;QAC3C,OAAO,aAAaxD,KAAK,CAACiG,aAAa,CAAC,OAAO,EAAE;UAC/CyE,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAO0O,OAAO,CAAC9D,KAAK,GAAG5K,EAAE;UAC3B,CAAC;UACDC,IAAI,EAAE,MAAM;UACZ0O,YAAY,EAAE9V,KAAK;UACnBkC,SAAS,EAAE,8BAA8B;UACzCE,QAAQ,EAAE,IAAI,CAAC1D,KAAK,CAAC0D,QAAQ;UAC7BkF,WAAW,EAAE,IAAI,CAAC5I,KAAK,CAAC4I,WAAW;UACnCyO,SAAS,EAAE,IAAI,CAACrX,KAAK,CAACqX,SAAS;UAC/BC,OAAO,EAAE,IAAI,CAAC9J,qBAAqB;UACnCqB,OAAO,EAAE,IAAI,CAACpB,oBAAoB;UAClCqB,MAAM,EAAE,IAAI,CAACxB,WAAW;UACxB,YAAY,EAAE,IAAI,CAACtN,KAAK,CAACgX,SAAS;UAClC,iBAAiB,EAAE,IAAI,CAAChX,KAAK,CAACiX,cAAc;UAC5C,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIzT,SAAS,GAAGvF,UAAU,CAAC,8BAA8B,EAAE;UACzD,eAAe,EAAE0F,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC3D,KAAK,CAAC4I,WAAW;UACzD,wBAAwB,EAAEjF,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC3D,KAAK,CAAC4I;QAC1D,CAAC,CAAC;QACF,IAAIhF,OAAO,GAAG,IAAI,CAAC5D,KAAK,CAACuX,aAAa,GAAGrZ,WAAW,CAAC4F,aAAa,CAAC,IAAI,CAAC9D,KAAK,CAACuX,aAAa,EAAE5B,cAAc,EAAE,IAAI,CAAC3V,KAAK,CAAC,GAAG2D,KAAK,IAAI,IAAI,CAAC3D,KAAK,CAAC4I,WAAW,IAAI,OAAO;QACrK,OAAO,aAAa9K,KAAK,CAACiG,aAAa,CAAC,MAAM,EAAE;UAC9CyE,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;YACpB,OAAO0O,OAAO,CAAC9D,KAAK,GAAG5K,EAAE;UAC3B,CAAC;UACDjF,SAAS,EAAEA;QACb,CAAC,EAAEI,OAAO,CAAC;MACb;IACF;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,iBAAiB;IACtBgC,KAAK,EAAE,SAASkW,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACxX,KAAK,CAACsB,KAAK,IAAI,IAAI,IAAI,IAAI,CAACtB,KAAK,CAACyX,SAAS,IAAI,CAAC,IAAI,CAACzX,KAAK,CAAC0D,QAAQ,EAAE;QAC5E,OAAO,aAAa5F,KAAK,CAACiG,aAAa,CAAC,GAAG,EAAE;UAC3CP,SAAS,EAAE,mCAAmC;UAC9CN,OAAO,EAAE,IAAI,CAAC8K;QAChB,CAAC,CAAC;MACJ;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD1O,GAAG,EAAE,oBAAoB;IACzBgC,KAAK,EAAE,SAASoW,kBAAkBA,CAAA,EAAG;MACnC,IAAIC,OAAO,GAAG,IAAI;MAElB,IAAIC,aAAa,GAAG3Z,UAAU,CAAC,qCAAqC,EAAE,IAAI,CAAC+B,KAAK,CAAC6X,YAAY,CAAC;MAC9F,OAAO,aAAa/Z,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;QAC7CyE,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOkP,OAAO,CAACG,OAAO,GAAGrP,EAAE;QAC7B,CAAC;QACDjF,SAAS,EAAE,oBAAoB;QAC/BQ,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE,SAAS;QAC1B,eAAe,EAAE,IAAI,CAACkJ,KAAK,CAACE;MAC9B,CAAC,EAAE,aAAatP,KAAK,CAACiG,aAAa,CAAC,MAAM,EAAE;QAC1CP,SAAS,EAAEoU;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDtY,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,IAAIwU,OAAO,GAAG,IAAI;MAElB,IAAIvU,SAAS,GAAGvF,UAAU,CAAC,uCAAuC,EAAE,IAAI,CAAC+B,KAAK,CAACwD,SAAS,EAAE;QACxF,YAAY,EAAE,IAAI,CAACxD,KAAK,CAAC0D,QAAQ;QACjC,SAAS,EAAE,IAAI,CAACwJ,KAAK,CAACC,OAAO;QAC7B,sBAAsB,EAAE,IAAI,CAACnN,KAAK,CAACyX,SAAS,IAAI,CAAC,IAAI,CAACzX,KAAK,CAAC0D,QAAQ;QACpE,uBAAuB,EAAE,IAAI,CAAC1D,KAAK,CAACsB,KAAK;QACzC,sBAAsB,EAAE,IAAI,CAAC4L,KAAK,CAACC,OAAO,IAAI,IAAI,CAACD,KAAK,CAACE;MAC3D,CAAC,CAAC;MACF,IAAIrH,cAAc,GAAG,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAC7C,IAAIqG,cAAc,GAAG,IAAI,CAACzD,iBAAiB,CAAC,CAAC;MAC7C,IAAI8F,YAAY,GAAG,IAAI,CAACxB,kBAAkB,CAACb,cAAc,CAAC;MAC1D,IAAIsC,cAAc,GAAG,IAAI,CAACrB,oBAAoB,CAAC,CAAC;MAChD,IAAIsB,YAAY,GAAG,IAAI,CAAChB,WAAW,CAACvB,cAAc,CAAC;MACnD,IAAIkC,YAAY,GAAG,IAAI,CAACH,kBAAkB,CAAC,CAAC;MAC5C,IAAIpP,SAAS,GAAG,IAAI,CAACkP,eAAe,CAAC,CAAC;MACtC,OAAO,aAAa1Z,KAAK,CAACiG,aAAa,CAAC,KAAK,EAAE;QAC7C6N,EAAE,EAAE,IAAI,CAAC5R,KAAK,CAAC4R,EAAE;QACjBpJ,GAAG,EAAE,SAASA,GAAGA,CAACC,EAAE,EAAE;UACpB,OAAOsP,OAAO,CAAC/I,SAAS,GAAGvG,EAAE;QAC/B,CAAC;QACDjF,SAAS,EAAEA,SAAS;QACpB6F,KAAK,EAAE,IAAI,CAACrJ,KAAK,CAACqJ,KAAK;QACvBnG,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiV,WAAW,EAAE,IAAI,CAACnY,KAAK,CAACmY,WAAW;QACnCC,aAAa,EAAE,IAAI,CAACpY,KAAK,CAACoY;MAC5B,CAAC,EAAEH,cAAc,EAAED,YAAY,EAAEE,YAAY,EAAE5P,SAAS,EAAEuP,YAAY,EAAE,aAAa/Z,KAAK,CAACiG,aAAa,CAACgH,aAAa,EAAEjM,QAAQ,CAAC;QAC/H0J,GAAG,EAAE,IAAI,CAACyF,UAAU;QACpBlI,cAAc,EAAEA;MAClB,CAAC,EAAE,IAAI,CAAC/F,KAAK,EAAE;QACbkD,OAAO,EAAE,IAAI,CAACwK,YAAY;QAC1B3G,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCmB,WAAW,EAAE,IAAI,CAACgF,KAAK,CAAC3I,MAAM;QAC9ByB,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBmC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;QACnDY,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAC/C3D,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;QAC7CqB,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCE,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCP,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;QACnDmB,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;QAC7CE,uBAAuB,EAAE,IAAI,CAACA,uBAAuB;QACrDZ,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BtB,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;QACnD2E,EAAE,EAAE,IAAI,CAAC+C,KAAK,CAACE,cAAc;QAC7BlI,OAAO,EAAE,IAAI,CAACyI,cAAc;QAC5BxI,SAAS,EAAE,IAAI,CAACyI,gBAAgB;QAChClD,MAAM,EAAE,IAAI,CAACmD,aAAa;QAC1BlD,QAAQ,EAAE,IAAI,CAACmD;MACjB,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,CAAC,CAAC;EAEH,OAAOb,QAAQ;AACjB,CAAC,CAAClP,SAAS,CAAC;AAEZ+D,eAAe,CAACmL,QAAQ,EAAE,cAAc,EAAE;EACxC2E,EAAE,EAAE,IAAI;EACR1D,QAAQ,EAAE,IAAI;EACdzB,IAAI,EAAE,IAAI;EACVnL,KAAK,EAAE,IAAI;EACXqI,OAAO,EAAE,IAAI;EACbnD,WAAW,EAAE,IAAI;EACjB2L,WAAW,EAAE,IAAI;EACjByC,cAAc,EAAE,IAAI;EACpBvN,gBAAgB,EAAE,IAAI;EACtByN,mBAAmB,EAAE,IAAI;EACzBvN,mBAAmB,EAAE,IAAI;EACzBgQ,aAAa,EAAE,IAAI;EACnB1Q,YAAY,EAAE,IAAI;EAClBwC,KAAK,EAAE,IAAI;EACX7F,SAAS,EAAE,IAAI;EACf2F,sBAAsB,EAAE,IAAI;EAC5BI,YAAY,EAAE,OAAO;EACrBhF,MAAM,EAAE,KAAK;EACb0Q,QAAQ,EAAE,IAAI;EACdO,eAAe,EAAE,UAAU;EAC3B3M,iBAAiB,EAAE,IAAI;EACvB4I,YAAY,EAAEM,SAAS;EACvB9K,YAAY,EAAE,kBAAkB;EAChCa,kBAAkB,EAAE,kBAAkB;EACtC4N,QAAQ,EAAE,KAAK;EACf9M,WAAW,EAAE,IAAI;EACjB8N,QAAQ,EAAE,KAAK;EACfhT,QAAQ,EAAE,KAAK;EACfoH,QAAQ,EAAE,IAAI;EACd6L,QAAQ,EAAE,IAAI;EACdb,SAAS,EAAE,KAAK;EAChBnQ,oBAAoB,EAAE,IAAI;EAC1BwN,iBAAiB,EAAE,KAAK;EACxBlL,eAAe,EAAE,KAAK;EACtB+B,cAAc,EAAE,IAAI;EACpBY,UAAU,EAAE,IAAI;EAChB6H,OAAO,EAAE,IAAI;EACbqE,OAAO,EAAE,IAAI;EACbW,SAAS,EAAE,KAAK;EAChBJ,SAAS,EAAE,IAAI;EACftB,OAAO,EAAE,IAAI;EACbO,cAAc,EAAE,IAAI;EACpBU,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,IAAI;EACpB1M,iBAAiB,EAAE,IAAI;EACvBsN,YAAY,EAAE,oBAAoB;EAClClJ,WAAW,EAAE,KAAK;EAClB3F,QAAQ,EAAE,IAAI;EACd6F,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZqJ,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,IAAI;EACnBrF,MAAM,EAAE,IAAI;EACZK,MAAM,EAAE,IAAI;EACZvB,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEF,SAAS5E,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}