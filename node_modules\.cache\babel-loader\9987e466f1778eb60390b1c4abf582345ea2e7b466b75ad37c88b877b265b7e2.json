{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\aggiunta_dati\\\\aggiungiAnagrafica.jsx\",\n  _s = $RefreshSig$();\n/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { Costanti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport axios from 'axios';\nimport { baseURL, baseProxy } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiAnagrafica = props => {\n  _s();\n  //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n  const [results, setResults] = useState('');\n  const [results2, setResults2] = useState('');\n  const [results3, setResults3] = useState('');\n  const [results4, setResults4] = useState('');\n  const [results5, setResults5] = useState('');\n  const [results6, setResults6] = useState('');\n  const [results7, setResults7] = useState('');\n  const [results8, setResults8] = useState('');\n  const [results9, setResults9] = useState('');\n  const [results10, setResults10] = useState(null);\n  const [modPag, setModPag] = useState(null);\n  const [isLookingUp, setIsLookingUp] = useState(false);\n  const [lookupStatus, setLookupStatus] = useState(null); // 'success', 'error', 'not_found'\n  const [isCompanyLookup, setIsCompanyLookup] = useState(false); // true se è stata trovata un'azienda via P.IVA\n  const toast = useRef(null);\n  //Setto le variabili di stato con il valore di props mediante useEffect\n  useEffect(() => {\n    async function renderString() {\n      if (props.result !== undefined) {\n        setResults(props.result.firstName);\n        setResults2(props.result.idRetailer.idRegistry.email);\n        setResults3(props.result.idRetailer.idRegistry.tel);\n        setResults4(props.result.idRetailer.idRegistry.pIva);\n        setResults5(props.result.idRetailer.idRegistry.address);\n        setResults6(props.result.idRetailer.idRegistry.city);\n        setResults7(props.result.idRetailer.idRegistry.cap);\n        setResults8(props.result.idRetailer.idRegistry.lastName);\n        setResults9(props.result.idRetailer.idRegistry.tel);\n        setResults10(props.result.idRetailer.idRegistry.paymentMetod);\n      }\n      //Chiamata axios per la visualizzazione dei registry\n      await APIRequest('GET', 'paymentmethods/').then(res => {\n        var pm = [];\n        res.data.forEach(element => {\n          var x = {\n            name: element.description,\n            code: element.description\n          };\n          pm.push(x);\n        });\n        setModPag(pm);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n    renderString();\n  }, [props.result]);\n  // Funzione di validazione\n  const validateForm = () => {\n    const errors = [];\n\n    // Validazione campi obbligatori\n    if (!results || results.trim() === '') {\n      errors.push('📝 Nome è obbligatorio');\n    }\n    // Cognome obbligatorio solo se non è un lookup aziendale\n    if (!isCompanyLookup && (!results8 || results8.trim() === '')) {\n      errors.push('📝 Cognome è obbligatorio');\n    }\n    if (!results4 || results4.trim() === '') {\n      errors.push('📝 Partita IVA è obbligatoria');\n    }\n\n    // Validazione formato P.IVA (11 cifre)\n    if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n    }\n\n    // Validazione email obbligatoria\n    if (!results2 || results2.trim() === '') {\n      errors.push('📧 Email è obbligatoria');\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n      errors.push('📧 Indirizzo email non valido');\n    }\n\n    // Validazione contatti: almeno uno tra telefono e cellulare\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n    if (!hasTelefono && !hasCellulare) {\n      errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n    }\n\n    // Validazione formato telefono (se presente)\n    if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n      errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n    }\n\n    // Validazione formato cellulare (se presente)\n    if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n      errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n    }\n    return errors;\n  };\n\n  // Funzione per il lookup automatico tramite P.IVA\n  const lookupPIva = async () => {\n    if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ P.IVA non valida',\n        detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n        life: 4000\n      });\n      return;\n    }\n    setIsLookingUp(true);\n    setLookupStatus(null); // Reset status all'inizio della ricerca\n    setIsCompanyLookup(false); // Reset flag aziendale\n\n    try {\n      // Chiamata diretta per evitare il redirect automatico su errore 500\n      const vatNumber = results4.replace(/\\s/g, '');\n\n      // Costruisce URL come fa APIRequest\n      const cleanURL = baseURL.replace(/\\/$/, '');\n      const cleanProxy = baseProxy.replace(/^\\/|\\/$/g, '');\n      const cleanPath = \"company-lookup?vat=IT\".concat(vatNumber);\n      let fullURL = cleanURL;\n      if (cleanProxy) fullURL += '/' + cleanProxy;\n      if (cleanPath) fullURL += '/' + cleanPath;\n\n      // Chiamata diretta con axios per controllo completo degli errori\n      const response = await axios({\n        method: 'GET',\n        url: fullURL,\n        headers: {\n          auth: localStorage.getItem('login_token'),\n          accept: \"application/json\",\n          \"Content-Type\": \"application/json\"\n        },\n        timeout: 10000\n      });\n      if (response.data && response.data.company) {\n        const data = response.data.company;\n\n        // Popola automaticamente i campi con i dati trovati dal servizio VIES\n        if (data.name) {\n          setResults(data.name); // Nome = Ragione Sociale\n          setResults8(''); // Cognome vuoto per aziende\n          setIsCompanyLookup(true); // Marca come lookup aziendale\n        }\n        if (data.address) {\n          // Estrae città e indirizzo se forniti insieme\n          const addressParts = data.address.split(',');\n          if (addressParts.length > 1) {\n            setResults5(addressParts[0].trim()); // Indirizzo\n            setResults6(addressParts[addressParts.length - 1].trim()); // Città\n          } else {\n            setResults5(data.address);\n          }\n        }\n        if (data.city) setResults6(data.city);\n        if (data.postalCode) setResults7(data.postalCode);\n        setLookupStatus('success');\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Azienda trovata',\n          detail: \"Dati recuperati automaticamente per \".concat(data.name, \". Il campo \\\"Cognome\\\" non \\xE8 richiesto per le aziende.\"),\n          life: 6000\n        });\n      } else {\n        throw new Error('Nessun risultato trovato');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _errorData$error;\n      console.error('❌ Errore lookup P.IVA:', error);\n\n      // Previeni il refresh della pagina gestendo l'errore localmente\n      const errorStatus = (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status;\n      const errorData = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data;\n      const errorCode = errorData === null || errorData === void 0 ? void 0 : (_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.code;\n\n      // Gestione specifica per diversi tipi di errore\n      if (errorStatus === 401) {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '🔐 Sessione scaduta',\n          detail: 'La sessione è scaduta. Ricarica la pagina per effettuare nuovamente il login, oppure continua con la compilazione manuale.',\n          life: 8000\n        });\n      } else if (errorStatus === 404 || errorCode === 'VAT_NOT_FOUND') {\n        setLookupStatus('not_found');\n        toast.current.show({\n          severity: 'info',\n          summary: '🔍 Azienda non trovata',\n          detail: 'Nessuna azienda trovata con questa Partita IVA. Puoi continuare con la compilazione manuale dei campi.',\n          life: 6000\n        });\n      } else if (errorStatus === 503 || errorCode === 'SERVICE_UNAVAILABLE') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⏱️ Servizio temporaneamente non disponibile',\n          detail: 'Il servizio di ricerca P.IVA non è al momento raggiungibile. Puoi continuare con la compilazione manuale.',\n          life: 6000\n        });\n      } else if (errorStatus === 400 || errorCode === 'INVALID_VAT_FORMAT') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'error',\n          summary: '❌ Formato P.IVA non valido',\n          detail: 'Il formato della Partita IVA inserita non è corretto. Verificare e riprovare.',\n          life: 5000\n        });\n      } else if (errorStatus === 500 || errorCode === 'LOOKUP_ERROR') {\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⚠️ Errore temporaneo',\n          detail: 'Si è verificato un errore temporaneo durante la ricerca. Puoi continuare con la compilazione manuale o riprovare più tardi.',\n          life: 6000\n        });\n      } else {\n        // Errore generico - non bloccare l'utente\n        setLookupStatus('error');\n        toast.current.show({\n          severity: 'warn',\n          summary: '⚠️ Ricerca non disponibile',\n          detail: 'La ricerca automatica non è al momento disponibile. Puoi continuare con la compilazione manuale dei campi.',\n          life: 5000\n        });\n      }\n\n      // Non propagare l'errore per evitare il refresh della pagina\n      // L'utente può continuare con la compilazione manuale\n    } finally {\n      setIsLookingUp(false);\n    }\n  };\n  const Invia = async () => {\n    // Validazione frontend\n    const validationErrors = validateForm();\n    if (validationErrors.length > 0) {\n      toast.current.show({\n        severity: 'warn',\n        summary: '⚠️ Dati mancanti o non validi',\n        detail: validationErrors.join('. '),\n        life: 5000\n      });\n      return;\n    }\n\n    // Verifica che tutti i campi obbligatori siano compilati\n    const hasTelefono = results3 && results3.trim() !== '';\n    const hasCellulare = results9 && results9.trim() !== '';\n\n    // Validazione: cognome richiesto solo se non è lookup aziendale\n    const cognomeValido = isCompanyLookup || results8 && results8.trim() !== '';\n    if (results && cognomeValido && results4 && results2 && (hasTelefono || hasCellulare)) {\n      var corpo = {\n        firstName: results,\n        lastName: results8,\n        email: results2,\n        telnum: results3,\n        cellnum: results9,\n        pIva: results4,\n        address: results5,\n        city: results6,\n        cap: results7,\n        paymentMetod: (results10 === null || results10 === void 0 ? void 0 : results10.name) || ''\n      };\n      //Chiamata axios per la creazione del registry\n      await APIRequest('POST', 'registry/', corpo).then(res => {\n        console.log(res.data);\n        toast.current.show({\n          severity: 'success',\n          summary: '✅ Successo',\n          detail: \"L'anagrafica è stata inserita con successo\",\n          life: 3000\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 3000);\n      }).catch(e => {\n        var _e$response, _e$response2, _e$response3, _e$response3$data;\n        console.error('❌ Errore creazione anagrafica:', e);\n\n        // Gestione specifica degli errori\n        const errorStatus = (_e$response = e.response) === null || _e$response === void 0 ? void 0 : _e$response.status;\n        const errorData = (_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : _e$response2.data;\n        const errorMessage = ((_e$response3 = e.response) === null || _e$response3 === void 0 ? void 0 : (_e$response3$data = _e$response3.data) === null || _e$response3$data === void 0 ? void 0 : _e$response3$data.message) || e.message;\n        let userMessage = '';\n        let summary = 'Errore';\n        if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') || errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n          // Violazione unique constraint (P.IVA duplicata)\n          summary = '⚠️ Partita IVA già presente';\n          userMessage = \"La Partita IVA \\\"\".concat(results4, \"\\\" \\xE8 gi\\xE0 registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.\");\n        } else if (errorStatus === 400) {\n          // Errori di validazione\n          summary = '📝 Dati non validi';\n          if (errorMessage.toLowerCase().includes('email')) {\n            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n          } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n          } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n            userMessage = 'Il numero di telefono inserito non è valido.';\n          } else {\n            userMessage = \"Dati inseriti non validi: \".concat(errorMessage);\n          }\n        } else if (errorStatus === 422) {\n          // Errori di business logic\n          summary = '🚫 Operazione non consentita';\n          userMessage = \"Impossibile completare l'operazione: \".concat(errorMessage);\n        } else if (errorStatus === 500 || errorStatus === 501) {\n          // Errori del server\n          summary = '🔧 Errore del server';\n          userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n        } else if (errorStatus === 503) {\n          // Servizio non disponibile\n          summary = '⏱️ Servizio temporaneamente non disponibile';\n          userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n        } else if (!e.response) {\n          // Errori di rete\n          summary = '🌐 Errore di connessione';\n          userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n        } else {\n          // Altri errori\n          summary = '❌ Errore imprevisto';\n          userMessage = \"Si \\xE8 verificato un errore imprevisto: \".concat(errorMessage);\n        }\n        toast.current.show({\n          severity: 'error',\n          summary: summary,\n          detail: userMessage,\n          life: 6000\n        });\n\n        // Log dettagliato per debugging\n        console.error('Dettagli errore:', {\n          status: errorStatus,\n          data: errorData,\n          message: errorMessage,\n          fullError: e\n        });\n      });\n    } else {\n      const campiRichiesti = isCompanyLookup ? \"Nome, Partita IVA, Email e almeno un numero di telefono\" : \"Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\";\n      toast.current.show({\n        severity: 'warn',\n        summary: '📝 Campi obbligatori mancanti',\n        detail: \"Compilare tutti i campi obbligatori: \".concat(campiRichiesti),\n        life: 6000\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBody\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-grid p-fluid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-card p-p-3\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #dee2e6',\n            borderRadius: '8px',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            style: {\n              color: '#495057',\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-search\",\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 29\n            }, this), \"Ricerca Automatica Dati Azienda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6c757d',\n              fontSize: '14px',\n              marginBottom: '15px'\n            },\n            children: \"Inserisci la Partita IVA per compilare automaticamente i dati azienda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-col-12 p-md-6\",\n            style: {\n              padding: '0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [Costanti.pIva, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'red'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-inputgroup\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"p-inputgroup-addon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"pi pi-credit-card\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(InputText, {\n                type: \"text\",\n                value: results4,\n                onChange: e => {\n                  const newValue = e.target.value;\n                  setResults4(newValue);\n\n                  // Reset dello status quando l'utente modifica la P.IVA\n                  if (lookupStatus) {\n                    setLookupStatus(null);\n                  }\n                  // Reset del flag aziendale se era stato impostato\n                  if (isCompanyLookup) {\n                    setIsCompanyLookup(false);\n                  }\n\n                  // Auto-lookup quando P.IVA è valida (11 cifre)\n                  if (/^\\d{11}$/.test(newValue.replace(/\\s/g, ''))) {\n                    // Debounce per evitare troppe chiamate\n                    setTimeout(() => {\n                      if (results4 === newValue) {\n                        // Verifica che il valore non sia cambiato\n                        lookupPIva();\n                      }\n                    }, 500);\n                  }\n                },\n                keyfilter: /^[\\d\\s]+$/,\n                placeholder: \"Inserisci partita IVA (11 cifre) - ricerca automatica\",\n                editable: \"true\",\n                maxLength: 11,\n                className: !results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"button\",\n                icon: isLookingUp ? \"pi pi-spin pi-spinner\" : \"pi pi-search\",\n                className: \"p-button-outlined p-button-secondary\",\n                onClick: lookupPIva,\n                disabled: isLookingUp || !results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')),\n                tooltip: \"Cerca automaticamente i dati aziendali\",\n                tooltipOptions: {\n                  position: 'top'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 29\n            }, this), results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-error\",\n              children: \"La Partita IVA deve contenere esattamente 11 cifre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 33\n            }, this), lookupStatus === 'success' && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-text-success\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-check-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 37\n              }, this), \" Dati aziendali trovati e compilati automaticamente\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 33\n            }, this), lookupStatus === 'not_found' && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-text-warning\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-exclamation-triangle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 37\n              }, this), \" Azienda non trovata - compila manualmente i campi sottostanti\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 33\n            }, this), lookupStatus === 'error' && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-text-warning\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-times-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 37\n              }, this), \" Ricerca automatica non disponibile - compila manualmente i campi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 33\n            }, this), !lookupStatus && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"p-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"pi pi-info-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 37\n              }, this), \" Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"h5\", {\n          style: {\n            color: '#495057',\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-user\",\n            style: {\n              marginRight: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 25\n          }, this), \"Dati Obbligatori\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Nome, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results,\n            onChange: e => setResults(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci nome (obbligatorio)\",\n            editable: \"true\",\n            className: !results || results.trim() === '' ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cognome, !isCompanyLookup && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 46\n          }, this), isCompanyLookup && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6c757d',\n              fontSize: '12px'\n            },\n            children: \" (opzionale per aziende)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 45\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: isCompanyLookup ? \"pi pi-building\" : \"pi pi-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results8,\n            onChange: e => setResults8(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: isCompanyLookup ? \"Opzionale per aziende\" : \"Inserisci cognome (obbligatorio)\",\n            editable: \"true\",\n            className: !isCompanyLookup && (!results8 || results8.trim() === '') ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 21\n        }, this), isCompanyLookup && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-text-secondary\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 29\n          }, this), \" Campo opzionale per le aziende\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Email, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'red'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-envelope\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"email\",\n            value: results2,\n            onChange: e => setResults2(e.target.value),\n            placeholder: \"Inserisci email (obbligatoria)\",\n            editable: \"true\",\n            className: !results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 21\n        }, this), (!results2 || results2.trim() === '') && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Email \\xE8 obbligatoria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 25\n        }, this), results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato email non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Tel, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 40\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results3,\n            onChange: e => setResults3(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci telefono fisso\",\n            editable: \"true\",\n            className: results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 21\n        }, this), results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato telefono non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: [Costanti.Cell, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'orange'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-mobile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"tel\",\n            value: results9,\n            onChange: e => setResults9(e.target.value),\n            keyfilter: /^[\\d\\s\\-\\(\\)\\+]+$/,\n            placeholder: \"Inserisci cellulare\",\n            editable: \"true\",\n            className: results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 21\n        }, this), results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: \"Formato cellulare non valido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 17\n      }, this), (!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 29\n          }, this), \" Inserire almeno un numero di telefono (fisso o cellulare)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Indirizzo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-inputgroup-addon\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-directions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results5,\n            onChange: e => setResults5(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci indirizzo\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Città\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results6,\n            onChange: e => setResults6(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci citt\\xE0\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.CodPost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"text\",\n            value: results7,\n            onChange: e => setResults7(e.target.value),\n            keyfilter: /^[^#<>*!]+$/,\n            placeholder: \"Inserisci C.A.P.\",\n            editable: \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-col-12 p-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: Costanti.Pagamento\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-inputgroup\",\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            className: \"w-100\",\n            value: results10,\n            options: modPag,\n            onChange: e => setResults10(e.target.value),\n            optionLabel: \"name\",\n            placeholder: \"Seleziona metodo di pagamento\",\n            filter: true,\n            filterBy: \"name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center mb-2 mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        id: \"invia\",\n        className: \"p-button saveList justify-content-center float-right ionicon mx-0 w-50\",\n        onClick: Invia,\n        children: Costanti.salva\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 384,\n    columnNumber: 9\n  }, this);\n};\n_s(AggiungiAnagrafica, \"bIj+N2exT8rzpsh6pJMsQLhBVQ8=\");\n_c = AggiungiAnagrafica;\nexport default AggiungiAnagrafica;\nvar _c;\n$RefreshReg$(_c, \"AggiungiAnagrafica\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "InputText", "<PERSON><PERSON>", "APIRequest", "axios", "baseURL", "baseProxy", "Toast", "<PERSON><PERSON>", "Dropdown", "jsxDEV", "_jsxDEV", "AggiungiAnagrafica", "props", "_s", "results", "setResults", "results2", "setResults2", "results3", "setResults3", "results4", "setResults4", "results5", "setResults5", "results6", "setResults6", "results7", "setResults7", "results8", "setResults8", "results9", "setResults9", "results10", "setResults10", "modPag", "setModPag", "isLookingUp", "setIsLookingUp", "lookupStatus", "setLookupStatus", "isCompanyLookup", "setIsCompanyLookup", "toast", "renderString", "result", "undefined", "firstName", "idRetailer", "idRegistry", "email", "tel", "pIva", "address", "city", "cap", "lastName", "paymentMetod", "then", "res", "pm", "data", "for<PERSON>ach", "element", "x", "name", "description", "code", "push", "catch", "e", "console", "log", "validateForm", "errors", "trim", "test", "replace", "hasTelefono", "has<PERSON><PERSON><PERSON><PERSON>", "lookupPIva", "current", "show", "severity", "summary", "detail", "life", "vatNumber", "cleanURL", "cleanProxy", "cleanPath", "concat", "fullURL", "response", "method", "url", "headers", "auth", "localStorage", "getItem", "accept", "timeout", "company", "addressParts", "split", "length", "postalCode", "Error", "error", "_error$response", "_error$response2", "_errorData$error", "errorStatus", "status", "errorData", "errorCode", "Invia", "validationErrors", "join", "cognome<PERSON><PERSON><PERSON>", "corpo", "telnum", "cellnum", "setTimeout", "window", "location", "reload", "_e$response", "_e$response2", "_e$response3", "_e$response3$data", "errorMessage", "message", "userMessage", "toLowerCase", "includes", "fullError", "campiRichiesti", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "border", "borderRadius", "marginBottom", "color", "marginRight", "fontSize", "padding", "type", "value", "onChange", "newValue", "target", "keyfilter", "placeholder", "editable", "max<PERSON><PERSON><PERSON>", "icon", "onClick", "disabled", "tooltip", "tooltipOptions", "position", "Nome", "Cognome", "Email", "Tel", "Cell", "<PERSON><PERSON><PERSON><PERSON>", "Città", "CodPost", "Pagamento", "options", "optionLabel", "filter", "filterBy", "id", "salva", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/aggiunta_dati/aggiungiAnagrafica.jsx"], "sourcesContent": ["/**\n* Winet e-procurement GUI\n* 2020 - Viniexport.com (C)\n*\n* AggiungiAnagrafica - operazioni sull'aggiunta anagrafiche\n*\n*/\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { InputText } from 'primereact/inputtext';\nimport { <PERSON>nti } from '../components/traduttore/const';\nimport { APIRequest } from '../components/generalizzazioni/apireq';\nimport axios from 'axios';\nimport { baseURL, baseProxy } from '../components/generalizzazioni/apireq';\nimport { Toast } from 'primereact/toast';\nimport { Button } from 'primereact/button';\nimport '../css/modale.css';\nimport { Dropdown } from 'primereact/dropdown';\n\nconst AggiungiAnagrafica = (props) => {\n    //Dichiarazione delle constanti per il salvataggio dei valori inseriti e lettura dati\n    const [results, setResults] = useState('');\n    const [results2, setResults2] = useState('');\n    const [results3, setResults3] = useState('');\n    const [results4, setResults4] = useState('');\n    const [results5, setResults5] = useState('');\n    const [results6, setResults6] = useState('');\n    const [results7, setResults7] = useState('');\n    const [results8, setResults8] = useState('');\n    const [results9, setResults9] = useState('');\n    const [results10, setResults10] = useState(null);\n    const [modPag, setModPag] = useState(null);\n    const [isLookingUp, setIsLookingUp] = useState(false);\n    const [lookupStatus, setLookupStatus] = useState(null); // 'success', 'error', 'not_found'\n    const [isCompanyLookup, setIsCompanyLookup] = useState(false); // true se è stata trovata un'azienda via P.IVA\n    const toast = useRef(null);\n    //Setto le variabili di stato con il valore di props mediante useEffect\n    useEffect(() => {\n        async function renderString() {\n            if (props.result !== undefined) {\n                setResults(props.result.firstName)\n                setResults2(props.result.idRetailer.idRegistry.email)\n                setResults3(props.result.idRetailer.idRegistry.tel)\n                setResults4(props.result.idRetailer.idRegistry.pIva)\n                setResults5(props.result.idRetailer.idRegistry.address)\n                setResults6(props.result.idRetailer.idRegistry.city)\n                setResults7(props.result.idRetailer.idRegistry.cap)\n                setResults8(props.result.idRetailer.idRegistry.lastName)\n                setResults9(props.result.idRetailer.idRegistry.tel)\n                setResults10(props.result.idRetailer.idRegistry.paymentMetod)\n            }\n            //Chiamata axios per la visualizzazione dei registry\n            await APIRequest('GET', 'paymentmethods/')\n                .then(res => {\n                    var pm = []\n                    res.data.forEach(element => {\n                        var x = {\n                            name: element.description,\n                            code: element.description\n                        }\n                        pm.push(x)\n                    });\n                    setModPag(pm)\n                }).catch((e) => {\n                    console.log(e)\n                })\n        }\n        renderString();\n    }, [props.result]);\n    // Funzione di validazione\n    const validateForm = () => {\n        const errors = [];\n\n        // Validazione campi obbligatori\n        if (!results || results.trim() === '') {\n            errors.push('📝 Nome è obbligatorio');\n        }\n        // Cognome obbligatorio solo se non è un lookup aziendale\n        if (!isCompanyLookup && (!results8 || results8.trim() === '')) {\n            errors.push('📝 Cognome è obbligatorio');\n        }\n        if (!results4 || results4.trim() === '') {\n            errors.push('📝 Partita IVA è obbligatoria');\n        }\n\n        // Validazione formato P.IVA (11 cifre)\n        if (results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            errors.push('🔢 Partita IVA deve contenere esattamente 11 cifre');\n        }\n\n        // Validazione email obbligatoria\n        if (!results2 || results2.trim() === '') {\n            errors.push('📧 Email è obbligatoria');\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2)) {\n            errors.push('📧 Indirizzo email non valido');\n        }\n\n        // Validazione contatti: almeno uno tra telefono e cellulare\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        if (!hasTelefono && !hasCellulare) {\n            errors.push('📞 Inserire almeno un numero di telefono (fisso o cellulare)');\n        }\n\n        // Validazione formato telefono (se presente)\n        if (results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3)) {\n            errors.push('📞 Numero di telefono fisso non valido (minimo 8 cifre)');\n        }\n\n        // Validazione formato cellulare (se presente)\n        if (results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9)) {\n            errors.push('📱 Numero di cellulare non valido (minimo 8 cifre)');\n        }\n\n        return errors;\n    };\n\n    // Funzione per il lookup automatico tramite P.IVA\n    const lookupPIva = async () => {\n        if (!results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ P.IVA non valida',\n                detail: 'Inserire una Partita IVA valida (11 cifre) per effettuare la ricerca automatica',\n                life: 4000\n            });\n            return;\n        }\n\n        setIsLookingUp(true);\n        setLookupStatus(null); // Reset status all'inizio della ricerca\n        setIsCompanyLookup(false); // Reset flag aziendale\n\n        try {\n            // Chiamata diretta per evitare il redirect automatico su errore 500\n            const vatNumber = results4.replace(/\\s/g, '');\n\n            // Costruisce URL come fa APIRequest\n            const cleanURL = baseURL.replace(/\\/$/, '');\n            const cleanProxy = baseProxy.replace(/^\\/|\\/$/g, '');\n            const cleanPath = `company-lookup?vat=IT${vatNumber}`;\n\n            let fullURL = cleanURL;\n            if (cleanProxy) fullURL += '/' + cleanProxy;\n            if (cleanPath) fullURL += '/' + cleanPath;\n\n            // Chiamata diretta con axios per controllo completo degli errori\n            const response = await axios({\n                method: 'GET',\n                url: fullURL,\n                headers: {\n                    auth: localStorage.getItem('login_token'),\n                    accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                timeout: 10000\n            });\n\n            if (response.data && response.data.company) {\n                const data = response.data.company;\n\n                // Popola automaticamente i campi con i dati trovati dal servizio VIES\n                if (data.name) {\n                    setResults(data.name); // Nome = Ragione Sociale\n                    setResults8(''); // Cognome vuoto per aziende\n                    setIsCompanyLookup(true); // Marca come lookup aziendale\n                }\n                if (data.address) {\n                    // Estrae città e indirizzo se forniti insieme\n                    const addressParts = data.address.split(',');\n                    if (addressParts.length > 1) {\n                        setResults5(addressParts[0].trim()); // Indirizzo\n                        setResults6(addressParts[addressParts.length - 1].trim()); // Città\n                    } else {\n                        setResults5(data.address);\n                    }\n                }\n                if (data.city) setResults6(data.city);\n                if (data.postalCode) setResults7(data.postalCode);\n\n                setLookupStatus('success');\n                toast.current.show({\n                    severity: 'success',\n                    summary: '✅ Azienda trovata',\n                    detail: `Dati recuperati automaticamente per ${data.name}. Il campo \"Cognome\" non è richiesto per le aziende.`,\n                    life: 6000\n                });\n            } else {\n                throw new Error('Nessun risultato trovato');\n            }\n        } catch (error) {\n            console.error('❌ Errore lookup P.IVA:', error);\n\n            // Previeni il refresh della pagina gestendo l'errore localmente\n            const errorStatus = error.response?.status;\n            const errorData = error.response?.data;\n            const errorCode = errorData?.error?.code;\n\n            // Gestione specifica per diversi tipi di errore\n            if (errorStatus === 401) {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '🔐 Sessione scaduta',\n                    detail: 'La sessione è scaduta. Ricarica la pagina per effettuare nuovamente il login, oppure continua con la compilazione manuale.',\n                    life: 8000\n                });\n            } else if (errorStatus === 404 || errorCode === 'VAT_NOT_FOUND') {\n                setLookupStatus('not_found');\n                toast.current.show({\n                    severity: 'info',\n                    summary: '🔍 Azienda non trovata',\n                    detail: 'Nessuna azienda trovata con questa Partita IVA. Puoi continuare con la compilazione manuale dei campi.',\n                    life: 6000\n                });\n            } else if (errorStatus === 503 || errorCode === 'SERVICE_UNAVAILABLE') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⏱️ Servizio temporaneamente non disponibile',\n                    detail: 'Il servizio di ricerca P.IVA non è al momento raggiungibile. Puoi continuare con la compilazione manuale.',\n                    life: 6000\n                });\n            } else if (errorStatus === 400 || errorCode === 'INVALID_VAT_FORMAT') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'error',\n                    summary: '❌ Formato P.IVA non valido',\n                    detail: 'Il formato della Partita IVA inserita non è corretto. Verificare e riprovare.',\n                    life: 5000\n                });\n            } else if (errorStatus === 500 || errorCode === 'LOOKUP_ERROR') {\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⚠️ Errore temporaneo',\n                    detail: 'Si è verificato un errore temporaneo durante la ricerca. Puoi continuare con la compilazione manuale o riprovare più tardi.',\n                    life: 6000\n                });\n            } else {\n                // Errore generico - non bloccare l'utente\n                setLookupStatus('error');\n                toast.current.show({\n                    severity: 'warn',\n                    summary: '⚠️ Ricerca non disponibile',\n                    detail: 'La ricerca automatica non è al momento disponibile. Puoi continuare con la compilazione manuale dei campi.',\n                    life: 5000\n                });\n            }\n\n            // Non propagare l'errore per evitare il refresh della pagina\n            // L'utente può continuare con la compilazione manuale\n        } finally {\n            setIsLookingUp(false);\n        }\n    };\n\n    const Invia = async () => {\n        // Validazione frontend\n        const validationErrors = validateForm();\n        if (validationErrors.length > 0) {\n            toast.current.show({\n                severity: 'warn',\n                summary: '⚠️ Dati mancanti o non validi',\n                detail: validationErrors.join('. '),\n                life: 5000\n            });\n            return;\n        }\n\n        // Verifica che tutti i campi obbligatori siano compilati\n        const hasTelefono = results3 && results3.trim() !== '';\n        const hasCellulare = results9 && results9.trim() !== '';\n\n        // Validazione: cognome richiesto solo se non è lookup aziendale\n        const cognomeValido = isCompanyLookup || (results8 && results8.trim() !== '');\n\n        if (results && cognomeValido && results4 && results2 && (hasTelefono || hasCellulare)) {\n            var corpo = {\n                firstName: results,\n                lastName: results8,\n                email: results2,\n                telnum: results3,\n                cellnum: results9,\n                pIva: results4,\n                address: results5,\n                city: results6,\n                cap: results7,\n                paymentMetod: results10?.name || ''\n            }\n            //Chiamata axios per la creazione del registry\n            await APIRequest('POST', 'registry/', corpo)\n                .then(res => {\n                    console.log(res.data);\n                    toast.current.show({\n                        severity: 'success',\n                        summary: '✅ Successo',\n                        detail: \"L'anagrafica è stata inserita con successo\",\n                        life: 3000\n                    });\n                    setTimeout(() => {\n                        window.location.reload()\n                    }, 3000)\n                }).catch((e) => {\n                    console.error('❌ Errore creazione anagrafica:', e);\n\n                    // Gestione specifica degli errori\n                    const errorStatus = e.response?.status;\n                    const errorData = e.response?.data;\n                    const errorMessage = e.response?.data?.message || e.message;\n\n                    let userMessage = '';\n                    let summary = 'Errore';\n\n                    if (errorStatus === 409 || errorMessage.toLowerCase().includes('unique') ||\n                        errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('già esiste')) {\n                        // Violazione unique constraint (P.IVA duplicata)\n                        summary = '⚠️ Partita IVA già presente';\n                        userMessage = `La Partita IVA \"${results4}\" è già registrata nel sistema. Verificare i dati inseriti o utilizzare una P.IVA diversa.`;\n                    } else if (errorStatus === 400) {\n                        // Errori di validazione\n                        summary = '📝 Dati non validi';\n                        if (errorMessage.toLowerCase().includes('email')) {\n                            userMessage = 'L\\'indirizzo email inserito non è valido. Verificare il formato.';\n                        } else if (errorMessage.toLowerCase().includes('partita iva') || errorMessage.toLowerCase().includes('piva')) {\n                            userMessage = 'La Partita IVA inserita non è valida. Verificare il formato (11 cifre).';\n                        } else if (errorMessage.toLowerCase().includes('telefono') || errorMessage.toLowerCase().includes('tel')) {\n                            userMessage = 'Il numero di telefono inserito non è valido.';\n                        } else {\n                            userMessage = `Dati inseriti non validi: ${errorMessage}`;\n                        }\n                    } else if (errorStatus === 422) {\n                        // Errori di business logic\n                        summary = '🚫 Operazione non consentita';\n                        userMessage = `Impossibile completare l'operazione: ${errorMessage}`;\n                    } else if (errorStatus === 500 || errorStatus === 501) {\n                        // Errori del server\n                        summary = '🔧 Errore del server';\n                        userMessage = 'Si è verificato un errore interno del server. Riprovare tra qualche minuto o contattare l\\'assistenza tecnica.';\n                    } else if (errorStatus === 503) {\n                        // Servizio non disponibile\n                        summary = '⏱️ Servizio temporaneamente non disponibile';\n                        userMessage = 'Il servizio è temporaneamente non disponibile. Riprovare tra qualche minuto.';\n                    } else if (!e.response) {\n                        // Errori di rete\n                        summary = '🌐 Errore di connessione';\n                        userMessage = 'Impossibile connettersi al server. Verificare la connessione internet e riprovare.';\n                    } else {\n                        // Altri errori\n                        summary = '❌ Errore imprevisto';\n                        userMessage = `Si è verificato un errore imprevisto: ${errorMessage}`;\n                    }\n\n                    toast.current.show({\n                        severity: 'error',\n                        summary: summary,\n                        detail: userMessage,\n                        life: 6000\n                    });\n\n                    // Log dettagliato per debugging\n                    console.error('Dettagli errore:', {\n                        status: errorStatus,\n                        data: errorData,\n                        message: errorMessage,\n                        fullError: e\n                    });\n                })\n        } else {\n            const campiRichiesti = isCompanyLookup\n                ? \"Nome, Partita IVA, Email e almeno un numero di telefono\"\n                : \"Nome, Cognome, Partita IVA, Email e almeno un numero di telefono\";\n\n            toast.current.show({\n                severity: 'warn',\n                summary: '📝 Campi obbligatori mancanti',\n                detail: `Compilare tutti i campi obbligatori: ${campiRichiesti}`,\n                life: 6000\n            });\n        }\n    };\n    return (\n        <div className=\"modalBody\">\n            <Toast ref={toast} />\n            <div className=\"p-grid p-fluid\">\n                {/* Sezione Ricerca Automatica P.IVA */}\n                <div className=\"p-col-12\">\n                    <div className=\"p-card p-p-3\" style={{backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '8px', marginBottom: '20px'}}>\n                        <h5 style={{color: '#495057', marginBottom: '10px'}}>\n                            <i className=\"pi pi-search\" style={{marginRight: '8px'}}></i>\n                            Ricerca Automatica Dati Azienda\n                        </h5>\n                        <p style={{color: '#6c757d', fontSize: '14px', marginBottom: '15px'}}>\n                            Inserisci la Partita IVA per compilare automaticamente i dati azienda\n                        </p>\n\n                        <div className=\"p-col-12 p-md-6\" style={{padding: '0'}}>\n                            <h6>{Costanti.pIva} <span style={{color: 'red'}}>*</span></h6>\n                            <div className=\"p-inputgroup\">\n                                <span className=\"p-inputgroup-addon\">\n                                    <i className=\"pi pi-credit-card\"></i>\n                                </span>\n                                <InputText\n                                    type='text'\n                                    value={results4}\n                                    onChange={(e) => {\n                                        const newValue = e.target.value;\n                                        setResults4(newValue);\n\n                                        // Reset dello status quando l'utente modifica la P.IVA\n                                        if (lookupStatus) {\n                                            setLookupStatus(null);\n                                        }\n                                        // Reset del flag aziendale se era stato impostato\n                                        if (isCompanyLookup) {\n                                            setIsCompanyLookup(false);\n                                        }\n\n                                        // Auto-lookup quando P.IVA è valida (11 cifre)\n                                        if (/^\\d{11}$/.test(newValue.replace(/\\s/g, ''))) {\n                                            // Debounce per evitare troppe chiamate\n                                            setTimeout(() => {\n                                                if (results4 === newValue) { // Verifica che il valore non sia cambiato\n                                                    lookupPIva();\n                                                }\n                                            }, 500);\n                                        }\n                                    }}\n                                    keyfilter={/^[\\d\\s]+$/}\n                                    placeholder=\"Inserisci partita IVA (11 cifre) - ricerca automatica\"\n                                    editable='true'\n                                    maxLength={11}\n                                    className={!results4 || results4.trim() === '' || !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) ? 'p-invalid' : ''}\n                                />\n                                <Button\n                                    type=\"button\"\n                                    icon={isLookingUp ? \"pi pi-spin pi-spinner\" : \"pi pi-search\"}\n                                    className=\"p-button-outlined p-button-secondary\"\n                                    onClick={lookupPIva}\n                                    disabled={isLookingUp || !results4 || !/^\\d{11}$/.test(results4.replace(/\\s/g, ''))}\n                                    tooltip=\"Cerca automaticamente i dati aziendali\"\n                                    tooltipOptions={{position: 'top'}}\n                                />\n                            </div>\n                            {results4 && !/^\\d{11}$/.test(results4.replace(/\\s/g, '')) && (\n                                <small className=\"p-error\">La Partita IVA deve contenere esattamente 11 cifre</small>\n                            )}\n\n                            {/* Indicatori di stato lookup */}\n                            {lookupStatus === 'success' && (\n                                <small className=\"p-text-success\">\n                                    <i className=\"pi pi-check-circle\"></i> Dati aziendali trovati e compilati automaticamente\n                                </small>\n                            )}\n                            {lookupStatus === 'not_found' && (\n                                <small className=\"p-text-warning\">\n                                    <i className=\"pi pi-exclamation-triangle\"></i> Azienda non trovata - compila manualmente i campi sottostanti\n                                </small>\n                            )}\n                            {lookupStatus === 'error' && (\n                                <small className=\"p-text-warning\">\n                                    <i className=\"pi pi-times-circle\"></i> Ricerca automatica non disponibile - compila manualmente i campi\n                                </small>\n                            )}\n                            {!lookupStatus && (\n                                <small className=\"p-text-secondary\">\n                                    <i className=\"pi pi-info-circle\"></i> Inserisci una P.IVA valida (11 cifre) per la ricerca automatica dei dati aziendali\n                                </small>\n                            )}\n                        </div>\n                    </div>\n                </div>\n\n                {/* Sezione Dati Obbligatori */}\n                <div className=\"p-col-12\">\n                    <h5 style={{color: '#495057', marginBottom: '15px'}}>\n                        <i className=\"pi pi-user\" style={{marginRight: '8px'}}></i>\n                        Dati Obbligatori\n                    </h5>\n                </div>\n\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Nome} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-user\"></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results}\n                            onChange={(e) => setResults(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder=\"Inserisci nome (obbligatorio)\"\n                            editable='true'\n                            className={!results || results.trim() === '' ? 'p-invalid' : ''}\n                        />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>\n                        {Costanti.Cognome}\n                        {!isCompanyLookup && <span style={{color: 'red'}}>*</span>}\n                        {isCompanyLookup && <span style={{color: '#6c757d', fontSize: '12px'}}> (opzionale per aziende)</span>}\n                    </h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className={isCompanyLookup ? \"pi pi-building\" : \"pi pi-user\"}></i>\n                        </span>\n                        <InputText\n                            type='text'\n                            value={results8}\n                            onChange={(e) => setResults8(e.target.value)}\n                            keyfilter={/^[^#<>*!]+$/}\n                            placeholder={isCompanyLookup ? \"Opzionale per aziende\" : \"Inserisci cognome (obbligatorio)\"}\n                            editable='true'\n                            className={!isCompanyLookup && (!results8 || results8.trim() === '') ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {isCompanyLookup && (\n                        <small className=\"p-text-secondary\">\n                            <i className=\"pi pi-info-circle\"></i> Campo opzionale per le aziende\n                        </small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Email} <span style={{color: 'red'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-envelope\"></i>\n                        </span>\n                        <InputText\n                            type=\"email\"\n                            value={results2}\n                            onChange={(e) => setResults2(e.target.value)}\n                            placeholder=\"Inserisci email (obbligatoria)\"\n                            editable='true'\n                            className={!results2 || results2.trim() === '' || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {(!results2 || results2.trim() === '') && (\n                        <small className=\"p-error\">Email è obbligatoria</small>\n                    )}\n                    {results2 && results2.trim() !== '' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(results2) && (\n                        <small className=\"p-error\">Formato email non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Tel} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-phone\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results3}\n                            onChange={(e) => setResults3(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci telefono fisso\"\n                            editable='true'\n                            className={results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results3 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results3) && (\n                        <small className=\"p-error\">Formato telefono non valido</small>\n                    )}\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Cell} <span style={{color: 'orange'}}>*</span></h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-mobile\"></i>\n                        </span>\n                        <InputText\n                            type=\"tel\"\n                            value={results9}\n                            onChange={(e) => setResults9(e.target.value)}\n                            keyfilter={/^[\\d\\s\\-\\(\\)\\+]+$/}\n                            placeholder=\"Inserisci cellulare\"\n                            editable='true'\n                            className={results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) ? 'p-invalid' : ''}\n                        />\n                    </div>\n                    {results9 && !/^\\+?[\\d\\s\\-\\(\\)]{8,}$/.test(results9) && (\n                        <small className=\"p-error\">Formato cellulare non valido</small>\n                    )}\n                </div>\n\n                {/* Messaggio informativo per i contatti */}\n                {(!results3 || results3.trim() === '') && (!results9 || results9.trim() === '') && (\n                    <div className=\"p-col-12\">\n                        <small className=\"p-error\">\n                            <i className=\"pi pi-info-circle\"></i> Inserire almeno un numero di telefono (fisso o cellulare)\n                        </small>\n                    </div>\n                )}\n\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Indirizzo}</h6>\n                    <div className=\"p-inputgroup\">\n                        <span className=\"p-inputgroup-addon\">\n                            <i className=\"pi pi-directions\"></i>\n                        </span>\n                        <InputText type='text' value={results5} onChange={(e) => setResults5(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci indirizzo\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Città}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results6} onChange={(e) => setResults6(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci città\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.CodPost}</h6>\n                    <div className=\"p-inputgroup\">\n                        <InputText type='text' value={results7} onChange={(e) => setResults7(e.target.value)} keyfilter={/^[^#<>*!]+$/} placeholder=\"Inserisci C.A.P.\" editable='true' />\n                    </div>\n                </div>\n                <div className=\"p-col-12 p-md-6\">\n                    <h6>{Costanti.Pagamento}</h6>\n                    <div className=\"p-inputgroup\">\n                        <Dropdown className='w-100' value={results10} options={modPag} onChange={(e) => setResults10(e.target.value)} optionLabel=\"name\" placeholder=\"Seleziona metodo di pagamento\" filter filterBy='name' />\n                    </div>\n                </div>\n            </div>\n            <div className=\"d-flex justify-content-center mb-2 mt-4\">\n                {/* Bottone di conferma creazione con metodo Invia per la chiamata axios */}\n                <Button id=\"invia\" className=\"p-button saveList justify-content-center float-right ionicon mx-0 w-50\" onClick={Invia}>{Costanti.salva}</Button>\n            </div>\n        </div>\n    );\n}\n\nexport default AggiungiAnagrafica;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,SAAS,QAAQ,uCAAuC;AAC1E,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,MAAM6C,KAAK,GAAG5C,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,SAAS,CAAC,MAAM;IACZ,eAAe4C,YAAYA,CAAA,EAAG;MAC1B,IAAI/B,KAAK,CAACgC,MAAM,KAAKC,SAAS,EAAE;QAC5B9B,UAAU,CAACH,KAAK,CAACgC,MAAM,CAACE,SAAS,CAAC;QAClC7B,WAAW,CAACL,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACC,KAAK,CAAC;QACrD9B,WAAW,CAACP,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnD7B,WAAW,CAACT,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACG,IAAI,CAAC;QACpD5B,WAAW,CAACX,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACI,OAAO,CAAC;QACvD3B,WAAW,CAACb,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACK,IAAI,CAAC;QACpD1B,WAAW,CAACf,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACM,GAAG,CAAC;QACnDzB,WAAW,CAACjB,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACO,QAAQ,CAAC;QACxDxB,WAAW,CAACnB,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACE,GAAG,CAAC;QACnDjB,YAAY,CAACrB,KAAK,CAACgC,MAAM,CAACG,UAAU,CAACC,UAAU,CAACQ,YAAY,CAAC;MACjE;MACA;MACA,MAAMtD,UAAU,CAAC,KAAK,EAAE,iBAAiB,CAAC,CACrCuD,IAAI,CAACC,GAAG,IAAI;QACT,IAAIC,EAAE,GAAG,EAAE;QACXD,GAAG,CAACE,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;UACxB,IAAIC,CAAC,GAAG;YACJC,IAAI,EAAEF,OAAO,CAACG,WAAW;YACzBC,IAAI,EAAEJ,OAAO,CAACG;UAClB,CAAC;UACDN,EAAE,CAACQ,IAAI,CAACJ,CAAC,CAAC;QACd,CAAC,CAAC;QACF5B,SAAS,CAACwB,EAAE,CAAC;MACjB,CAAC,CAAC,CAACS,KAAK,CAAEC,CAAC,IAAK;QACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACV;IACA1B,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC/B,KAAK,CAACgC,MAAM,CAAC,CAAC;EAClB;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI,CAAC3D,OAAO,IAAIA,OAAO,CAAC4D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnCD,MAAM,CAACN,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA;IACA,IAAI,CAAC3B,eAAe,KAAK,CAACZ,QAAQ,IAAIA,QAAQ,CAAC8C,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;MAC3DD,MAAM,CAACN,IAAI,CAAC,2BAA2B,CAAC;IAC5C;IACA,IAAI,CAAC/C,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,IAAI/C,QAAQ,IAAI,CAAC,UAAU,CAACuD,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC3DH,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;;IAEA;IACA,IAAI,CAACnD,QAAQ,IAAIA,QAAQ,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrCD,MAAM,CAACN,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAACQ,IAAI,CAAC3D,QAAQ,CAAC,EAAE;MACrDyD,MAAM,CAACN,IAAI,CAAC,+BAA+B,CAAC;IAChD;;IAEA;IACA,MAAMU,WAAW,GAAG3D,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAGhD,QAAQ,IAAIA,QAAQ,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE;IAEvD,IAAI,CAACG,WAAW,IAAI,CAACC,YAAY,EAAE;MAC/BL,MAAM,CAACN,IAAI,CAAC,8DAA8D,CAAC;IAC/E;;IAEA;IACA,IAAIjD,QAAQ,IAAI,CAAC,uBAAuB,CAACyD,IAAI,CAACzD,QAAQ,CAAC,EAAE;MACrDuD,MAAM,CAACN,IAAI,CAAC,yDAAyD,CAAC;IAC1E;;IAEA;IACA,IAAIrC,QAAQ,IAAI,CAAC,uBAAuB,CAAC6C,IAAI,CAAC7C,QAAQ,CAAC,EAAE;MACrD2C,MAAM,CAACN,IAAI,CAAC,oDAAoD,CAAC;IACrE;IAEA,OAAOM,MAAM;EACjB,CAAC;;EAED;EACA,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAC3D,QAAQ,IAAI,CAAC,UAAU,CAACuD,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;MAC5DlC,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE,iFAAiF;QACzFC,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;IAEAhD,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACvBE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAE3B,IAAI;MACA;MACA,MAAM6C,SAAS,GAAGlE,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;MAE7C;MACA,MAAMW,QAAQ,GAAGnF,OAAO,CAACwE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC3C,MAAMY,UAAU,GAAGnF,SAAS,CAACuE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpD,MAAMa,SAAS,2BAAAC,MAAA,CAA2BJ,SAAS,CAAE;MAErD,IAAIK,OAAO,GAAGJ,QAAQ;MACtB,IAAIC,UAAU,EAAEG,OAAO,IAAI,GAAG,GAAGH,UAAU;MAC3C,IAAIC,SAAS,EAAEE,OAAO,IAAI,GAAG,GAAGF,SAAS;;MAEzC;MACA,MAAMG,QAAQ,GAAG,MAAMzF,KAAK,CAAC;QACzB0F,MAAM,EAAE,KAAK;QACbC,GAAG,EAAEH,OAAO;QACZI,OAAO,EAAE;UACLC,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;UACzCC,MAAM,EAAE,kBAAkB;UAC1B,cAAc,EAAE;QACpB,CAAC;QACDC,OAAO,EAAE;MACb,CAAC,CAAC;MAEF,IAAIR,QAAQ,CAAChC,IAAI,IAAIgC,QAAQ,CAAChC,IAAI,CAACyC,OAAO,EAAE;QACxC,MAAMzC,IAAI,GAAGgC,QAAQ,CAAChC,IAAI,CAACyC,OAAO;;QAElC;QACA,IAAIzC,IAAI,CAACI,IAAI,EAAE;UACXjD,UAAU,CAAC6C,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC;UACvBnC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;UACjBY,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9B;QACA,IAAImB,IAAI,CAACR,OAAO,EAAE;UACd;UACA,MAAMkD,YAAY,GAAG1C,IAAI,CAACR,OAAO,CAACmD,KAAK,CAAC,GAAG,CAAC;UAC5C,IAAID,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;YACzBjF,WAAW,CAAC+E,YAAY,CAAC,CAAC,CAAC,CAAC5B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrCjD,WAAW,CAAC6E,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC9B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,MAAM;YACHnD,WAAW,CAACqC,IAAI,CAACR,OAAO,CAAC;UAC7B;QACJ;QACA,IAAIQ,IAAI,CAACP,IAAI,EAAE5B,WAAW,CAACmC,IAAI,CAACP,IAAI,CAAC;QACrC,IAAIO,IAAI,CAAC6C,UAAU,EAAE9E,WAAW,CAACiC,IAAI,CAAC6C,UAAU,CAAC;QAEjDlE,eAAe,CAAC,SAAS,CAAC;QAC1BG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,mBAAmB;UAC5BC,MAAM,yCAAAM,MAAA,CAAyC9B,IAAI,CAACI,IAAI,8DAAsD;UAC9GqB,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH,MAAM,IAAIqB,KAAK,CAAC,0BAA0B,CAAC;MAC/C;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACZxC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,MAAMI,WAAW,IAAAH,eAAA,GAAGD,KAAK,CAACf,QAAQ,cAAAgB,eAAA,uBAAdA,eAAA,CAAgBI,MAAM;MAC1C,MAAMC,SAAS,IAAAJ,gBAAA,GAAGF,KAAK,CAACf,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBjD,IAAI;MACtC,MAAMsD,SAAS,GAAGD,SAAS,aAATA,SAAS,wBAAAH,gBAAA,GAATG,SAAS,CAAEN,KAAK,cAAAG,gBAAA,uBAAhBA,gBAAA,CAAkB5C,IAAI;;MAExC;MACA,IAAI6C,WAAW,KAAK,GAAG,EAAE;QACrBxE,eAAe,CAAC,OAAO,CAAC;QACxBG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,qBAAqB;UAC9BC,MAAM,EAAE,4HAA4H;UACpIC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAI0B,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,eAAe,EAAE;QAC7D3E,eAAe,CAAC,WAAW,CAAC;QAC5BG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,wBAAwB;UACjCC,MAAM,EAAE,wGAAwG;UAChHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAI0B,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,qBAAqB,EAAE;QACnE3E,eAAe,CAAC,OAAO,CAAC;QACxBG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,6CAA6C;UACtDC,MAAM,EAAE,2GAA2G;UACnHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAI0B,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,oBAAoB,EAAE;QAClE3E,eAAe,CAAC,OAAO,CAAC;QACxBG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE,+EAA+E;UACvFC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM,IAAI0B,WAAW,KAAK,GAAG,IAAIG,SAAS,KAAK,cAAc,EAAE;QAC5D3E,eAAe,CAAC,OAAO,CAAC;QACxBG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,sBAAsB;UAC/BC,MAAM,EAAE,6HAA6H;UACrIC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACA9C,eAAe,CAAC,OAAO,CAAC;QACxBG,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE,4GAA4G;UACpHC,IAAI,EAAE;QACV,CAAC,CAAC;MACN;;MAEA;MACA;IACJ,CAAC,SAAS;MACNhD,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAM8E,KAAK,GAAG,MAAAA,CAAA,KAAY;IACtB;IACA,MAAMC,gBAAgB,GAAG5C,YAAY,CAAC,CAAC;IACvC,IAAI4C,gBAAgB,CAACZ,MAAM,GAAG,CAAC,EAAE;MAC7B9D,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAEgC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;QACnChC,IAAI,EAAE;MACV,CAAC,CAAC;MACF;IACJ;;IAEA;IACA,MAAMR,WAAW,GAAG3D,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE;IACtD,MAAMI,YAAY,GAAGhD,QAAQ,IAAIA,QAAQ,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE;;IAEvD;IACA,MAAM4C,aAAa,GAAG9E,eAAe,IAAKZ,QAAQ,IAAIA,QAAQ,CAAC8C,IAAI,CAAC,CAAC,KAAK,EAAG;IAE7E,IAAI5D,OAAO,IAAIwG,aAAa,IAAIlG,QAAQ,IAAIJ,QAAQ,KAAK6D,WAAW,IAAIC,YAAY,CAAC,EAAE;MACnF,IAAIyC,KAAK,GAAG;QACRzE,SAAS,EAAEhC,OAAO;QAClByC,QAAQ,EAAE3B,QAAQ;QAClBqB,KAAK,EAAEjC,QAAQ;QACfwG,MAAM,EAAEtG,QAAQ;QAChBuG,OAAO,EAAE3F,QAAQ;QACjBqB,IAAI,EAAE/B,QAAQ;QACdgC,OAAO,EAAE9B,QAAQ;QACjB+B,IAAI,EAAE7B,QAAQ;QACd8B,GAAG,EAAE5B,QAAQ;QACb8B,YAAY,EAAE,CAAAxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgC,IAAI,KAAI;MACrC,CAAC;MACD;MACA,MAAM9D,UAAU,CAAC,MAAM,EAAE,WAAW,EAAEqH,KAAK,CAAC,CACvC9D,IAAI,CAACC,GAAG,IAAI;QACTY,OAAO,CAACC,GAAG,CAACb,GAAG,CAACE,IAAI,CAAC;QACrBlB,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,YAAY;UACrBC,MAAM,EAAE,4CAA4C;UACpDC,IAAI,EAAE;QACV,CAAC,CAAC;QACFqC,UAAU,CAAC,MAAM;UACbC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC,CAACzD,KAAK,CAAEC,CAAC,IAAK;QAAA,IAAAyD,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA;QACZ3D,OAAO,CAACqC,KAAK,CAAC,gCAAgC,EAAEtC,CAAC,CAAC;;QAElD;QACA,MAAM0C,WAAW,IAAAe,WAAA,GAAGzD,CAAC,CAACuB,QAAQ,cAAAkC,WAAA,uBAAVA,WAAA,CAAYd,MAAM;QACtC,MAAMC,SAAS,IAAAc,YAAA,GAAG1D,CAAC,CAACuB,QAAQ,cAAAmC,YAAA,uBAAVA,YAAA,CAAYnE,IAAI;QAClC,MAAMsE,YAAY,GAAG,EAAAF,YAAA,GAAA3D,CAAC,CAACuB,QAAQ,cAAAoC,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYpE,IAAI,cAAAqE,iBAAA,uBAAhBA,iBAAA,CAAkBE,OAAO,KAAI9D,CAAC,CAAC8D,OAAO;QAE3D,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAIjD,OAAO,GAAG,QAAQ;QAEtB,IAAI4B,WAAW,KAAK,GAAG,IAAImB,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpEJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UACvG;UACAnD,OAAO,GAAG,6BAA6B;UACvCiD,WAAW,uBAAA1C,MAAA,CAAsBtE,QAAQ,sGAA4F;QACzI,CAAC,MAAM,IAAI2F,WAAW,KAAK,GAAG,EAAE;UAC5B;UACA5B,OAAO,GAAG,oBAAoB;UAC9B,IAAI+C,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9CF,WAAW,GAAG,kEAAkE;UACpF,CAAC,MAAM,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC1GF,WAAW,GAAG,yEAAyE;UAC3F,CAAC,MAAM,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACtGF,WAAW,GAAG,8CAA8C;UAChE,CAAC,MAAM;YACHA,WAAW,gCAAA1C,MAAA,CAAgCwC,YAAY,CAAE;UAC7D;QACJ,CAAC,MAAM,IAAInB,WAAW,KAAK,GAAG,EAAE;UAC5B;UACA5B,OAAO,GAAG,8BAA8B;UACxCiD,WAAW,2CAAA1C,MAAA,CAA2CwC,YAAY,CAAE;QACxE,CAAC,MAAM,IAAInB,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;UACnD;UACA5B,OAAO,GAAG,sBAAsB;UAChCiD,WAAW,GAAG,gHAAgH;QAClI,CAAC,MAAM,IAAIrB,WAAW,KAAK,GAAG,EAAE;UAC5B;UACA5B,OAAO,GAAG,6CAA6C;UACvDiD,WAAW,GAAG,8EAA8E;QAChG,CAAC,MAAM,IAAI,CAAC/D,CAAC,CAACuB,QAAQ,EAAE;UACpB;UACAT,OAAO,GAAG,0BAA0B;UACpCiD,WAAW,GAAG,oFAAoF;QACtG,CAAC,MAAM;UACH;UACAjD,OAAO,GAAG,qBAAqB;UAC/BiD,WAAW,+CAAA1C,MAAA,CAA4CwC,YAAY,CAAE;QACzE;QAEAxF,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;UACfC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAEgD,WAAW;UACnB/C,IAAI,EAAE;QACV,CAAC,CAAC;;QAEF;QACAf,OAAO,CAACqC,KAAK,CAAC,kBAAkB,EAAE;UAC9BK,MAAM,EAAED,WAAW;UACnBnD,IAAI,EAAEqD,SAAS;UACfkB,OAAO,EAAED,YAAY;UACrBK,SAAS,EAAElE;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACV,CAAC,MAAM;MACH,MAAMmE,cAAc,GAAGhG,eAAe,GAChC,yDAAyD,GACzD,kEAAkE;MAExEE,KAAK,CAACsC,OAAO,CAACC,IAAI,CAAC;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,+BAA+B;QACxCC,MAAM,0CAAAM,MAAA,CAA0C8C,cAAc,CAAE;QAChEnD,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ,CAAC;EACD,oBACI3E,OAAA;IAAK+H,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBhI,OAAA,CAACJ,KAAK;MAACqI,GAAG,EAAEjG;IAAM;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBrI,OAAA;MAAK+H,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE3BhI,OAAA;QAAK+H,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBhI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAACO,KAAK,EAAE;YAACC,eAAe,EAAE,SAAS;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE,KAAK;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAV,QAAA,gBACtIhI,OAAA;YAAIsI,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAM,CAAE;YAAAV,QAAA,gBAChDhI,OAAA;cAAG+H,SAAS,EAAC,cAAc;cAACO,KAAK,EAAE;gBAACM,WAAW,EAAE;cAAK;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mCAEjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrI,OAAA;YAAGsI,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEE,QAAQ,EAAE,MAAM;cAAEH,YAAY,EAAE;YAAM,CAAE;YAAAV,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJrI,OAAA;YAAK+H,SAAS,EAAC,iBAAiB;YAACO,KAAK,EAAE;cAACQ,OAAO,EAAE;YAAG,CAAE;YAAAd,QAAA,gBACnDhI,OAAA;cAAAgI,QAAA,GAAKzI,QAAQ,CAACkD,IAAI,EAAC,GAAC,eAAAzC,OAAA;gBAAMsI,KAAK,EAAE;kBAACK,KAAK,EAAE;gBAAK,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DrI,OAAA;cAAK+H,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBhI,OAAA;gBAAM+H,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eAChChI,OAAA;kBAAG+H,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACPrI,OAAA,CAACV,SAAS;gBACNyJ,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEtI,QAAS;gBAChBuI,QAAQ,EAAGtF,CAAC,IAAK;kBACb,MAAMuF,QAAQ,GAAGvF,CAAC,CAACwF,MAAM,CAACH,KAAK;kBAC/BrI,WAAW,CAACuI,QAAQ,CAAC;;kBAErB;kBACA,IAAItH,YAAY,EAAE;oBACdC,eAAe,CAAC,IAAI,CAAC;kBACzB;kBACA;kBACA,IAAIC,eAAe,EAAE;oBACjBC,kBAAkB,CAAC,KAAK,CAAC;kBAC7B;;kBAEA;kBACA,IAAI,UAAU,CAACkC,IAAI,CAACiF,QAAQ,CAAChF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;oBAC9C;oBACA8C,UAAU,CAAC,MAAM;sBACb,IAAItG,QAAQ,KAAKwI,QAAQ,EAAE;wBAAE;wBACzB7E,UAAU,CAAC,CAAC;sBAChB;oBACJ,CAAC,EAAE,GAAG,CAAC;kBACX;gBACJ,CAAE;gBACF+E,SAAS,EAAE,WAAY;gBACvBC,WAAW,EAAC,uDAAuD;gBACnEC,QAAQ,EAAC,MAAM;gBACfC,SAAS,EAAE,EAAG;gBACdxB,SAAS,EAAE,CAACrH,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAACC,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,WAAW,GAAG;cAAG;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC,eACFrI,OAAA,CAACH,MAAM;gBACHkJ,IAAI,EAAC,QAAQ;gBACbS,IAAI,EAAE9H,WAAW,GAAG,uBAAuB,GAAG,cAAe;gBAC7DqG,SAAS,EAAC,sCAAsC;gBAChD0B,OAAO,EAAEpF,UAAW;gBACpBqF,QAAQ,EAAEhI,WAAW,IAAI,CAAChB,QAAQ,IAAI,CAAC,UAAU,CAACuD,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE;gBACpFyF,OAAO,EAAC,wCAAwC;gBAChDC,cAAc,EAAE;kBAACC,QAAQ,EAAE;gBAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL3H,QAAQ,IAAI,CAAC,UAAU,CAACuD,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,iBACtDlE,OAAA;cAAO+H,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACvF,EAGAzG,YAAY,KAAK,SAAS,iBACvB5B,OAAA;cAAO+H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhI,OAAA;gBAAG+H,SAAS,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uDAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACV,EACAzG,YAAY,KAAK,WAAW,iBACzB5B,OAAA;cAAO+H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhI,OAAA;gBAAG+H,SAAS,EAAC;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kEAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACV,EACAzG,YAAY,KAAK,OAAO,iBACrB5B,OAAA;cAAO+H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhI,OAAA;gBAAG+H,SAAS,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qEAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACV,EACA,CAACzG,YAAY,iBACV5B,OAAA;cAAO+H,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BhI,OAAA;gBAAG+H,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uFACzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrI,OAAA;QAAK+H,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBhI,OAAA;UAAIsI,KAAK,EAAE;YAACK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAM,CAAE;UAAAV,QAAA,gBAChDhI,OAAA;YAAG+H,SAAS,EAAC,YAAY;YAACO,KAAK,EAAE;cAACM,WAAW,EAAE;YAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAE/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,GAAKzI,QAAQ,CAACuK,IAAI,EAAC,GAAC,eAAA9J,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPrI,OAAA,CAACV,SAAS;YACNyJ,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE5I,OAAQ;YACf6I,QAAQ,EAAGtF,CAAC,IAAKtD,UAAU,CAACsD,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAC5CI,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAC,+BAA+B;YAC3CC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAAC3H,OAAO,IAAIA,OAAO,CAAC4D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;UAAG;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,GACKzI,QAAQ,CAACwK,OAAO,EAChB,CAACjI,eAAe,iBAAI9B,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACzDvG,eAAe,iBAAI9B,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEE,QAAQ,EAAE;YAAM,CAAE;YAAAb,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC,eACLrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAEjG,eAAe,GAAG,gBAAgB,GAAG;YAAa;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACPrI,OAAA,CAACV,SAAS;YACNyJ,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE9H,QAAS;YAChB+H,QAAQ,EAAGtF,CAAC,IAAKxC,WAAW,CAACwC,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,aAAc;YACzBC,WAAW,EAAEvH,eAAe,GAAG,uBAAuB,GAAG,kCAAmC;YAC5FwH,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAACjG,eAAe,KAAK,CAACZ,QAAQ,IAAIA,QAAQ,CAAC8C,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,GAAG;UAAG;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLvG,eAAe,iBACZ9B,OAAA;UAAO+H,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BhI,OAAA;YAAG+H,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,mCACzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,GAAKzI,QAAQ,CAACyK,KAAK,EAAC,GAAC,eAAAhK,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE;YAAK,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACPrI,OAAA,CAACV,SAAS;YACNyJ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE1I,QAAS;YAChB2I,QAAQ,EAAGtF,CAAC,IAAKpD,WAAW,CAACoD,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAC7CK,WAAW,EAAC,gCAAgC;YAC5CC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE,CAACzH,QAAQ,IAAIA,QAAQ,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAAC3D,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL,CAAC,CAAC/H,QAAQ,IAAIA,QAAQ,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,kBACjChE,OAAA;UAAO+H,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzD,EACA/H,QAAQ,IAAIA,QAAQ,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,4BAA4B,CAACC,IAAI,CAAC3D,QAAQ,CAAC,iBAC/EN,OAAA;UAAO+H,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,GAAKzI,QAAQ,CAAC0K,GAAG,EAAC,GAAC,eAAAjK,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE;YAAQ,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChErI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACPrI,OAAA,CAACV,SAAS;YACNyJ,IAAI,EAAC,KAAK;YACVC,KAAK,EAAExI,QAAS;YAChByI,QAAQ,EAAGtF,CAAC,IAAKlD,WAAW,CAACkD,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAEvH,QAAQ,IAAI,CAAC,uBAAuB,CAACyD,IAAI,CAACzD,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL7H,QAAQ,IAAI,CAAC,uBAAuB,CAACyD,IAAI,CAACzD,QAAQ,CAAC,iBAChDR,OAAA;UAAO+H,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,GAAKzI,QAAQ,CAAC2K,IAAI,EAAC,GAAC,eAAAlK,OAAA;YAAMsI,KAAK,EAAE;cAACK,KAAK,EAAE;YAAQ,CAAE;YAAAX,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjErI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACPrI,OAAA,CAACV,SAAS;YACNyJ,IAAI,EAAC,KAAK;YACVC,KAAK,EAAE5H,QAAS;YAChB6H,QAAQ,EAAGtF,CAAC,IAAKtC,WAAW,CAACsC,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAC7CI,SAAS,EAAE,mBAAoB;YAC/BC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAC,MAAM;YACfvB,SAAS,EAAE3G,QAAQ,IAAI,CAAC,uBAAuB,CAAC6C,IAAI,CAAC7C,QAAQ,CAAC,GAAG,WAAW,GAAG;UAAG;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLjH,QAAQ,IAAI,CAAC,uBAAuB,CAAC6C,IAAI,CAAC7C,QAAQ,CAAC,iBAChDpB,OAAA;UAAO+H,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACjE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGL,CAAC,CAAC7H,QAAQ,IAAIA,QAAQ,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC5C,QAAQ,IAAIA,QAAQ,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,iBAC3EhE,OAAA;QAAK+H,SAAS,EAAC,UAAU;QAAAC,QAAA,eACrBhI,OAAA;UAAO+H,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBhI,OAAA;YAAG+H,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,8DACzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR,eAEDrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,EAAKzI,QAAQ,CAAC4K;QAAS;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBhI,OAAA;YAAM+H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAChChI,OAAA;cAAG+H,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACPrI,OAAA,CAACV,SAAS;YAACyJ,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEpI,QAAS;YAACqI,QAAQ,EAAGtF,CAAC,IAAK9C,WAAW,CAAC8C,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,qBAAqB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,EAAKzI,QAAQ,CAAC6K;QAAK;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzBrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBhI,OAAA,CAACV,SAAS;YAACyJ,IAAI,EAAC,MAAM;YAACC,KAAK,EAAElI,QAAS;YAACmI,QAAQ,EAAGtF,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,oBAAiB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,EAAKzI,QAAQ,CAAC8K;QAAO;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3BrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBhI,OAAA,CAACV,SAAS;YAACyJ,IAAI,EAAC,MAAM;YAACC,KAAK,EAAEhI,QAAS;YAACiI,QAAQ,EAAGtF,CAAC,IAAK1C,WAAW,CAAC0C,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAACI,SAAS,EAAE,aAAc;YAACC,WAAW,EAAC,kBAAkB;YAACC,QAAQ,EAAC;UAAM;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNrI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BhI,OAAA;UAAAgI,QAAA,EAAKzI,QAAQ,CAAC+K;QAAS;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BrI,OAAA;UAAK+H,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBhI,OAAA,CAACF,QAAQ;YAACiI,SAAS,EAAC,OAAO;YAACiB,KAAK,EAAE1H,SAAU;YAACiJ,OAAO,EAAE/I,MAAO;YAACyH,QAAQ,EAAGtF,CAAC,IAAKpC,YAAY,CAACoC,CAAC,CAACwF,MAAM,CAACH,KAAK,CAAE;YAACwB,WAAW,EAAC,MAAM;YAACnB,WAAW,EAAC,+BAA+B;YAACoB,MAAM;YAACC,QAAQ,EAAC;UAAM;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNrI,OAAA;MAAK+H,SAAS,EAAC,yCAAyC;MAAAC,QAAA,eAEpDhI,OAAA,CAACH,MAAM;QAAC8K,EAAE,EAAC,OAAO;QAAC5C,SAAS,EAAC,wEAAwE;QAAC0B,OAAO,EAAEhD,KAAM;QAAAuB,QAAA,EAAEzI,QAAQ,CAACqL;MAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9I,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAlI,EAAA,CApmBKF,kBAAkB;AAAA4K,EAAA,GAAlB5K,kBAAkB;AAsmBxB,eAAeA,kBAAkB;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}