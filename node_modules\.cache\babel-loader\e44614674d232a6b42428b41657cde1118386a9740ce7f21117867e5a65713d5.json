{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n    props = _ref.props,\n    children = _ref.children;\n  var iconNode;\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n  return iconNode || children || null;\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "Icon", "_ref", "icon", "props", "children", "iconNode", "createElement"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-menu/es/Icon.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n      props = _ref.props,\n      children = _ref.children;\n  var iconNode;\n\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n\n  return iconNode || children || null;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,IAAIA,CAACC,IAAI,EAAE;EACjC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,QAAQ;EAEZ,IAAI,OAAOH,IAAI,KAAK,UAAU,EAAE;IAC9BG,QAAQ,GAAG,aAAaN,KAAK,CAACO,aAAa,CAACJ,IAAI,EAAEJ,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,CAAC;EAC7E,CAAC,MAAM;IACL;IACAE,QAAQ,GAAGH,IAAI;EACjB;EAEA,OAAOG,QAAQ,IAAID,QAAQ,IAAI,IAAI;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}