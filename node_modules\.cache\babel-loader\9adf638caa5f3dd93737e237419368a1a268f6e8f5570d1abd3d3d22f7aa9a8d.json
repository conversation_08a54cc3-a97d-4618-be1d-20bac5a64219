{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcCollapse from 'rc-collapse';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\nvar CollapsePanel = function CollapsePanel(props) {\n  devWarning(!('disabled' in props), 'Collapse.Panel', '`disabled` is deprecated. Please use `collapsible=\"disabled\"` instead.');\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var collapsePanelClassName = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-no-arrow\"), !showArrow), className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n};\nexport default CollapsePanel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "RcCollapse", "classNames", "ConfigContext", "dev<PERSON><PERSON><PERSON>", "CollapsePanel", "props", "_React$useContext", "useContext", "getPrefixCls", "customizePrefixCls", "prefixCls", "_props$className", "className", "_props$showArrow", "showArrow", "collapsePanelClassName", "concat", "createElement", "Panel"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/collapse/CollapsePanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport RcCollapse from 'rc-collapse';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport devWarning from '../_util/devWarning';\n\nvar CollapsePanel = function CollapsePanel(props) {\n  devWarning(!('disabled' in props), 'Collapse.Panel', '`disabled` is deprecated. Please use `collapsible=\"disabled\"` instead.');\n\n  var _React$useContext = React.useContext(ConfigContext),\n      getPrefixCls = _React$useContext.getPrefixCls;\n\n  var customizePrefixCls = props.prefixCls,\n      _props$className = props.className,\n      className = _props$className === void 0 ? '' : _props$className,\n      _props$showArrow = props.showArrow,\n      showArrow = _props$showArrow === void 0 ? true : _props$showArrow;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var collapsePanelClassName = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-no-arrow\"), !showArrow), className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n};\n\nexport default CollapsePanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAE5C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChDF,UAAU,CAAC,EAAE,UAAU,IAAIE,KAAK,CAAC,EAAE,gBAAgB,EAAE,wEAAwE,CAAC;EAE9H,IAAIC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU,CAACL,aAAa,CAAC;IACnDM,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAEjD,IAAIC,kBAAkB,GAAGJ,KAAK,CAACK,SAAS;IACpCC,gBAAgB,GAAGN,KAAK,CAACO,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,gBAAgB,GAAGR,KAAK,CAACS,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;EACrE,IAAIH,SAAS,GAAGF,YAAY,CAAC,UAAU,EAAEC,kBAAkB,CAAC;EAC5D,IAAIM,sBAAsB,GAAGd,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkB,MAAM,CAACN,SAAS,EAAE,WAAW,CAAC,EAAE,CAACI,SAAS,CAAC,EAAEF,SAAS,CAAC;EACtH,OAAO,aAAab,KAAK,CAACkB,aAAa,CAACjB,UAAU,CAACkB,KAAK,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IAC5EK,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEG;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}