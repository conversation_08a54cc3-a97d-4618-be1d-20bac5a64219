export default WorkDuringInteraction;
export type EventTimingEvent = import('../computed/metrics/responsiveness.js').EventTimingEvent;
export type TaskNode = import('../lib/tracehouse/main-thread-tasks.js').TaskNode;
/**
 * @fileoverview This metric gives a high-percentile measure of responsiveness to input.
 */
declare class WorkDuringInteraction extends Audit {
    /**
     * @param {TaskNode} task
     * @param {TaskNode|undefined} parent
     * @param {number} startTs
     * @param {number} endTs
     * @return {number}
     */
    static recursivelyClipTasks(task: TaskNode, parent: TaskNode | undefined, startTs: number, endTs: number): number;
    /**
     * Clip the tasks by the start and end points. Take the easy route and drop
     * to duration 0 if out of bounds, since only durations are needed in the
     * end (for now).
     * Assumes owned tasks, so modifies in place. Can be called multiple times on
     * the same `tasks` because always computed from original event timing.
     * @param {Array<TaskNode>} tasks
     * @param {number} startTs
     * @param {number} endTs
     */
    static clipTasksByTs(tasks: Array<TaskNode>, startTs: number, endTs: number): void;
    /**
     * @param {EventTimingEvent} interactionEvent
     */
    static getPhaseTimes(interactionEvent: EventTimingEvent): {
        inputDelay: {
            startTs: number;
            endTs: number;
        };
        processingTime: {
            startTs: number;
            endTs: number;
        };
        presentationDelay: {
            startTs: number;
            endTs: number;
        };
    };
    /**
     * @param {EventTimingEvent} interactionEvent
     * @param {LH.Trace} trace
     * @param {LH.Artifacts.ProcessedTrace} processedTrace
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {{table: LH.Audit.Details.Table, phases: Record<string, {startTs: number, endTs: number}>}}
     */
    static getThreadBreakdownTable(interactionEvent: EventTimingEvent, trace: LH.Trace, processedTrace: LH.Artifacts.ProcessedTrace, networkRecords: Array<LH.Artifacts.NetworkRequest>): {
        table: LH.Audit.Details.Table;
        phases: Record<string, {
            startTs: number;
            endTs: number;
        }>;
    };
    /**
     * @param {LH.Artifacts['TraceElements']} traceElements
     * @return {LH.Audit.Details.Table | undefined}
     */
    static getTraceElementTable(traceElements: LH.Artifacts['TraceElements']): LH.Audit.Details.Table | undefined;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const inputDelay: string;
    const processingTime: string;
    const presentationDelay: string;
    const displayValue: string;
    const eventTarget: string;
}
import { Audit } from "./audit.js";
import { ProcessedTrace } from "../computed/processed-trace.js";
//# sourceMappingURL=work-during-interaction.d.ts.map