{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport memoizeOne from 'memoize-one';\nimport devWarning from '../_util/devWarning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport var ANT_MARK = 'internalMark';\nvar LocaleProvider = /*#__PURE__*/function (_React$Component) {\n  _inherits(LocaleProvider, _React$Component);\n  var _super = _createSuper(LocaleProvider);\n  function LocaleProvider(props) {\n    var _this;\n    _classCallCheck(this, LocaleProvider);\n    _this = _super.call(this, props);\n    _this.getMemoizedContextValue = memoizeOne(function (localeValue) {\n      return _extends(_extends({}, localeValue), {\n        exist: true\n      });\n    });\n    changeConfirmLocale(props.locale && props.locale.Modal);\n    devWarning(props._ANT_MARK__ === ANT_MARK, 'LocaleProvider', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale');\n    return _this;\n  }\n  _createClass(LocaleProvider, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      changeConfirmLocale(this.props.locale && this.props.locale.Modal);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var locale = this.props.locale;\n      if (prevProps.locale !== locale) {\n        changeConfirmLocale(locale && locale.Modal);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      changeConfirmLocale();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        locale = _this$props.locale,\n        children = _this$props.children;\n      var contextValue = this.getMemoizedContextValue(locale);\n      return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n  }]);\n  return LocaleProvider;\n}(React.Component);\nexport { LocaleProvider as default };\nLocaleProvider.defaultProps = {\n  locale: {}\n};", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "memoizeOne", "dev<PERSON><PERSON><PERSON>", "changeConfirmLocale", "LocaleContext", "ANT_MARK", "LocaleProvider", "_React$Component", "_super", "props", "_this", "call", "getMemoizedContextValue", "localeValue", "exist", "locale", "Modal", "_ANT_MARK__", "key", "value", "componentDidMount", "componentDidUpdate", "prevProps", "componentWillUnmount", "render", "_this$props", "children", "contextValue", "createElement", "Provider", "Component", "default", "defaultProps"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/antd/es/locale-provider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport memoizeOne from 'memoize-one';\nimport devWarning from '../_util/devWarning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport var ANT_MARK = 'internalMark';\n\nvar LocaleProvider = /*#__PURE__*/function (_React$Component) {\n  _inherits(LocaleProvider, _React$Component);\n\n  var _super = _createSuper(LocaleProvider);\n\n  function LocaleProvider(props) {\n    var _this;\n\n    _classCallCheck(this, LocaleProvider);\n\n    _this = _super.call(this, props);\n    _this.getMemoizedContextValue = memoizeOne(function (localeValue) {\n      return _extends(_extends({}, localeValue), {\n        exist: true\n      });\n    });\n    changeConfirmLocale(props.locale && props.locale.Modal);\n    devWarning(props._ANT_MARK__ === ANT_MARK, 'LocaleProvider', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale');\n    return _this;\n  }\n\n  _createClass(LocaleProvider, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      changeConfirmLocale(this.props.locale && this.props.locale.Modal);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var locale = this.props.locale;\n\n      if (prevProps.locale !== locale) {\n        changeConfirmLocale(locale && locale.Modal);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      changeConfirmLocale();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          locale = _this$props.locale,\n          children = _this$props.children;\n      var contextValue = this.getMemoizedContextValue(locale);\n      return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n  }]);\n\n  return LocaleProvider;\n}(React.Component);\n\nexport { LocaleProvider as default };\nLocaleProvider.defaultProps = {\n  locale: {}\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAO,IAAIC,QAAQ,GAAG,cAAc;AAEpC,IAAIC,cAAc,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC5DT,SAAS,CAACQ,cAAc,EAAEC,gBAAgB,CAAC;EAE3C,IAAIC,MAAM,GAAGT,YAAY,CAACO,cAAc,CAAC;EAEzC,SAASA,cAAcA,CAACG,KAAK,EAAE;IAC7B,IAAIC,KAAK;IAETd,eAAe,CAAC,IAAI,EAAEU,cAAc,CAAC;IAErCI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,uBAAuB,GAAGX,UAAU,CAAC,UAAUY,WAAW,EAAE;MAChE,OAAOlB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,CAAC,EAAE;QACzCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IACFX,mBAAmB,CAACM,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACC,KAAK,CAAC;IACvDd,UAAU,CAACO,KAAK,CAACQ,WAAW,KAAKZ,QAAQ,EAAE,gBAAgB,EAAE,+GAA+G,CAAC;IAC7K,OAAOK,KAAK;EACd;EAEAb,YAAY,CAACS,cAAc,EAAE,CAAC;IAC5BY,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClCjB,mBAAmB,CAAC,IAAI,CAACM,KAAK,CAACM,MAAM,IAAI,IAAI,CAACN,KAAK,CAACM,MAAM,CAACC,KAAK,CAAC;IACnE;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIP,MAAM,GAAG,IAAI,CAACN,KAAK,CAACM,MAAM;MAE9B,IAAIO,SAAS,CAACP,MAAM,KAAKA,MAAM,EAAE;QAC/BZ,mBAAmB,CAACY,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MAC7C;IACF;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASI,oBAAoBA,CAAA,EAAG;MACrCpB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACDe,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASK,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAAChB,KAAK;QACxBM,MAAM,GAAGU,WAAW,CAACV,MAAM;QAC3BW,QAAQ,GAAGD,WAAW,CAACC,QAAQ;MACnC,IAAIC,YAAY,GAAG,IAAI,CAACf,uBAAuB,CAACG,MAAM,CAAC;MACvD,OAAO,aAAaf,KAAK,CAAC4B,aAAa,CAACxB,aAAa,CAACyB,QAAQ,EAAE;QAC9DV,KAAK,EAAEQ;MACT,CAAC,EAAED,QAAQ,CAAC;IACd;EACF,CAAC,CAAC,CAAC;EAEH,OAAOpB,cAAc;AACvB,CAAC,CAACN,KAAK,CAAC8B,SAAS,CAAC;AAElB,SAASxB,cAAc,IAAIyB,OAAO;AAClCzB,cAAc,CAAC0B,YAAY,GAAG;EAC5BjB,MAAM,EAAE,CAAC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}