export class PerformanceCategory<PERSON><PERSON><PERSON> extends CategoryRenderer {
    /**
     * @param {LH.ReportResult.AuditRef} audit
     * @return {!Element}
     */
    _renderMetric(audit: LH.ReportResult.AuditRef): Element;
    /**
     * @param {LH.ReportResult.AuditRef} audit
     * @param {number} scale
     * @return {!Element}
     */
    _renderOpportunity(audit: LH.ReportResult.AuditRef, scale: number): Element;
    /**
     * Get an audit's wastedMs to sort the opportunity by, and scale the sparkline width
     * Opportunities with an error won't have a details object, so MIN_VALUE is returned to keep any
     * erroring opportunities last in sort order.
     * @param {LH.ReportResult.AuditRef} audit
     * @return {number}
     */
    _getWastedMs(audit: LH.ReportResult.AuditRef): number;
    /**
     * Get a link to the interactive scoring calculator with the metric values.
     * @param {LH.ReportResult.AuditRef[]} auditRefs
     * @return {string}
     */
    _getScoringCalculatorHref(auditRefs: LH.ReportResult.AuditRef[]): string;
    /**
     * For performance, audits with no group should be a diagnostic or opportunity.
     * The audit details type will determine which of the two groups an audit is in.
     *
     * @param {LH.ReportResult.AuditRef} audit
     * @return {'load-opportunity'|'diagnostic'|null}
     */
    _classifyPerformanceAudit(audit: LH.ReportResult.AuditRef): 'load-opportunity' | 'diagnostic' | null;
    /**
     * @param {LH.ReportResult.Category} category
     * @param {Object<string, LH.Result.ReportGroup>} groups
     * @param {{gatherMode: LH.Result.GatherMode}=} options
     * @return {Element}
     * @override
     */
    override render(category: LH.ReportResult.Category, groups: {
        [x: string]: LH.Result.ReportGroup;
    }, options?: {
        gatherMode: LH.Result.GatherMode;
    } | undefined): Element;
    /**
     * Render the control to filter the audits by metric. The filtering is done at runtime by CSS only
     * @param {LH.ReportResult.AuditRef[]} filterableMetrics
     * @param {HTMLDivElement} categoryEl
     */
    renderMetricAuditFilter(filterableMetrics: LH.ReportResult.AuditRef[], categoryEl: HTMLDivElement): void;
}
export type DOM = import('./dom.js').DOM;
import { CategoryRenderer } from "./category-renderer.js";
//# sourceMappingURL=performance-category-renderer.d.ts.map