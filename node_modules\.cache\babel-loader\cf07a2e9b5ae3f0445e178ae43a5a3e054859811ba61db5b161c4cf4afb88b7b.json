{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\cu-frontend-coll\\\\src\\\\App.js\";\nimport React, { Component } from 'react';\nimport { BrowserRouter, <PERSON>, Switch } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ErrorBoundary } from 'react-error-boundary';\nimport { APIRequest } from './components/generalizzazioni/apireq';\nimport { GoToTheTop } from './components/generalizzazioni/icone fisse/goToTopOfPage';\n// import BackendHealthCheck from './components/BackendHealthCheck';\nimport BackendTestButton from './components/BackendTestButton';\nimport Home from './components/Home';\nimport Login from './components/Login';\nimport Registrati from './components/registrati';\nimport ForgotPwd from './components/forgotPwd';\nimport PrivateRoute from './components/PrivateRoute';\nimport PublicRoute from './components/PublicRoute';\nimport AggiungiListini from './aggiunta_dati/aggiungiListino';\nimport ModificaProdottiListino from './aggiunta_dati/aggiungiProdottiListino';\nimport AssociaListiniPV from './aggiunta_dati/associaListinoPV';\nimport ModificaProdottiOrdine from './aggiunta_dati/modificaProdOrdine';\nimport AggiungiDocInevasi from './aggiunta_dati/aggiungiDocInevasi';\nimport DashboardDistributore from './common/distributore/dashboard/dashboardDistributore';\nimport Gestione_Prodotti from './common/distributore/gestioneProdotti';\nimport Gestione_Affiliati from './common/distributore/gestioneAffiliati';\nimport GestioneLogisticaUtenti from './common/distributore/gestioneOPLogistica';\nimport Gestione_Punti_Vendita from './common/distributore/gestionePVendita';\nimport Gestione_Listini from './common/distributore/gestioneListini';\nimport UfficioVendite from './common/distributore/ufficioVendite';\nimport UfficioAcquisti from './common/distributore/ufficioAcquisti';\nimport GestioneAnagrafiche from './common/distributore/gestioneClienti';\nimport GestioneLogisticaOdini from './common/distributore/gestioneLogisticaOrdini';\nimport VisualizzaListiniAssociati from './common/distributore/visualizzaListiniAssociati';\nimport GestioneMagazzinieri from './common/distributore/gestioneMagazzinieri';\nimport GestioneUtentiDistributore from './common/distributore/gestioneUtentiDistributore';\nimport GestioneMagazzini from './common/distributore/gestioneMagazzini';\nimport GestioneScorte from './common/distributore/gestioneScorte';\nimport MerceInevasa from './common/distributore/gestioneDocInevasi';\nimport AssegnaConsegne from './common/distributore/gestioneConsegne';\nimport Alyante from './common/distributore/alyante';\nimport GestioneAgenti from './common/distributore/gestioneAgenti';\nimport GestioneAutisti from './common/distributore/gestioneAutisti';\nimport BuyerPrediction from './common/distributore/previsioneAcquisti';\nimport StoricoFornitori from './common/distributore/storicoFornitori';\nimport GestioneFornitori from './common/distributore/gestioneFornitori';\nimport marketplaceUffAcq from './common/distributore/marketplaceUffAcq';\nimport ListiniAcquisto from './common/distributore/listiniAcquisto';\nimport magazzinoContoTerzi from './common/distributore/magazzinoContoTerzi';\nimport FornitoreAffiliato from './common/distributore/fornitoreAffiliato';\nimport GiacenzeProdotti from './common/distributore/giacenzeProdotti';\nimport RiepilogoApprovvigionamento from './common/affiliato/riepilogoApprovvigionamento';\nimport GestioneScorteAff from './common/affiliato/gestioneScorte';\nimport GestioneDocumentiAFF from './common/affiliato/gestioneDocumenti';\nimport DashboardAffiliato from './common/affiliato/dashboard/dashboardAffiliato';\nimport VisualizzaListino from './common/affiliato/visualizzaListini';\nimport VisualizzaOrdine from './common/affiliato/visualizzaOrdini';\nimport AggiungiOrdineAffiliato from './common/affiliato/creaOrdine';\nimport RiepilogoOrdineAff from './common/affiliato/riepilogoOrdineAff';\nimport Approvvigionamento from './common/affiliato/approvvigionamento';\nimport GestioneOdiniAgente from './common/agenti/gestioneOrdiniAgenti';\nimport Gestione_PDV2 from './common/affiliato/gestionePuntiVendita';\nimport dashboardAmministratore from './common/amministratore/DashboardAmministratore';\nimport DashboardPDV from './common/PDV/dashboardPDV/dashboardPDV';\nimport GestioneOrdiniPDV from './common/PDV/gestioneOrdiniPDV';\nimport GestioneDocumentiPDV from './common/PDV/gestioneDocumenti';\nimport Cart from './common/PDV/carrello/riepilogoOrdinePDV';\nimport store from './common/PDV/carrello/store.jsx';\nimport Marketplace from './common/PDV/listino/listinoPDV';\nimport GestioneUtenti from './common/amministratore/GestioneUtenti';\nimport GestioneLogistica from './common/logistica/gestioneLogistica';\nimport DashboardAgente from './common/agenti/dashboardAgenti/dashboardAgente';\nimport GestioneClientiAgente from './common/agenti/gestioneClientiAgente';\nimport AggiungiOrdineAgente from './common/agenti/listinoAgente';\nimport dettagliOrdineAgente from './common/agenti/dettagliOrdineAgente';\nimport DettagliClienti from './common/agenti/dettagliClienti';\nimport OrdineDiretto from './common/agenti/ordineDiretto';\nimport CompletaOrdine from './common/agenti/completaOrdine';\nimport GestionePDVAutonoma from './common/agenti/GestionePDVAutonoma.jsx';\nimport GestioneConsegne from './common/autista/gestioneConsegne';\nimport DatiConsegnaAutista from './common/autista/riepilogoConsegna';\nimport DashboardRespMagazzino from './common/respMagazzino/dashboardRespMagazzino';\nimport MovimentazioneInIngresso from './common/respMagazzino/movimentazioneInIngresso';\nimport MovimentazioneInUscita from './common/respMagazzino/movimentazioneInUscita';\nimport GestioneLavorazioni from './common/respMagazzino/gestioneLavorazioni';\nimport GestioneOperatori from './common/respMagazzino/gestioneOperatori';\nimport ControlloLottiEScadenze from './common/respMagazzino/controlloLottiEScadenze';\nimport ConfrontoDispQta from './common/respMagazzino/confrontoDispQta';\nimport ComposizioneMagazzino from './common/respMagazzino/composizioneMagazzino';\nimport Inventario from './common/respMagazzino/inventario';\nimport GestioneProdPos from './common/respMagazzino/gestioneProdPos';\nimport GestioneProdotti from './common/respMagazzino/gestioneProdotti';\nimport DashboardChain from './common/chain/dashboard/dashboardChain';\nimport GestioneProd from './common/chain/gestioneProdotti';\nimport GestionePDV from './common/chain/gestionePuntiVendita';\nimport DocumentiVendite from './common/chain/ufficioVendite';\nimport DocumentiAcquisti from './common/chain/ufficioAcquisti';\nimport AnagraficheChain from './common/chain/gestioneClienti';\nimport GestioneMagazziniChain from './common/chain/gestioneMagazzini';\nimport GestioneScorteChain from './common/chain/gestioneScorte';\nimport AssegnaConsegneChain from './common/chain/gestioneConsegne';\nimport marketplaceUffAcqChain from './common/chain/marketplaceUffAcq';\nimport ListiniAcquistoChain from './common/chain/listiniAcquisto';\nimport GestioneFornitoriChain from './common/chain/gestioneFornitori';\nimport ControlloLottiEScadenzeChain from './common/chain/controlloLottiEScadenze';\nimport ConfrontoDispQtaChain from './common/chain/confrontoDispQta';\nimport GestioneAutistiChain from './common/chain/gestioneAutisti';\nimport GestioneLogisticaUtentiChain from './common/chain/gestioneOPLogistica';\nimport GestioneMagazzinieriChain from './common/chain/gestioneMagazzinieri';\nimport ComposizioneMagazzinoChain from './common/chain/composizioneMagazzino';\nimport DashboardRING from './common/ring/dashboard/dashboardRing';\nimport MarketplaceRing from './common/ring/listinoRing';\nimport CaricoMagRing from './common/ring/caricoMagRing';\nimport ScaricoMagRing from './common/ring/scaricoMagRing';\nimport GestioneScorteRing from './common/ring/gestioneScorte';\nimport ControlloLottiEScadenzeRing from './common/ring/controlloLottiEScadenze';\nimport ConfrontoDispQtaRing from './common/ring/confrontoDispQta';\nimport OrdinatoRing from './common/ring/ordinatoRing';\nimport ComposizioneMagazzinoRing from './common/ring/composizioneMagazzino';\nimport ScaricoMagazzinoRing from './common/ring/scaricoMagazzino';\nimport Footer from './components/footer/footer';\nimport NoMatch from './components/handleError';\nimport NewPwd from './components/newPwd';\nimport menu from './components/generalizzazioni/menu.json';\nimport AccountSettings from './components/accountSettings';\nimport FreePriceList from './components/freePriceList/freePriceList';\nimport './css/frontend.css';\nimport './App.css';\nimport { chain, chainCarrello, chainDashboard, chainDocumentiOrdineAcquisto, chainDocumentiOrdineVendita, chainFornitori, chainGenerali, chainGestioneConsegne, chainGestioneMagazzini, chainGestioneProdotti, chainGestioneScorte, chainListiniAcquisto, chainOrdina, chainGestionePVendita, chainmagazzinoContoTerzi, admin, adminGestioneUtenti, affiliato, affiliatoCarrello, affiliatoCreaOrdine, affiliatoDashboard, affiliatoLogistica, affiliatoDocumentiOrdineAcquisto, affiliatoFornitori, affiliatoGestioneDocumenti, affiliatoGestioneListiniPDV, affiliatoGestionePuntiVendita, affiliatoGestioneScorte, affiliatoListiniAcquisto, affiliatoOrdina, affiliatoRiepilogo, affiliatoVisualizzaListini, affiliatoVisualizzaOrdini, agente, agenteCompletaOrdine, agenteCreaOrdine, agenteDashboard, agenteDettaglioCliente, agenteDettagliOrdine, agenteExternalsystems, agenteGestioneClientiAgente, agenteGestioneOrdiniAgente, agenteGestionePDVAutonoma, agenteMerceInevasa, agenteOrdineDiretto, agenteStoricoDocumenti, agenteVisualizzaListini, autista, autistaDatiConsegnaAutista, autistaGestioneConsegne, basePath, dashboard, distributore, distributoreAggiungiDocInevasi, distributoreAggiungiListini, distributoreCarrello, distributoreDashboard, distributoreDocumentiOrdineAcquisto, distributoreDocumentiOrdineVendita, distributoreExternalsystems, distributoreFornitoreAffiliato, distributoreFornitori, distributoreGenerali, distributoreGestioneAffiliati, distributoreGestioneAgenti, distributoreGestioneAutisti, distributoreGestioneConsegne, distributoreGestioneListini, distributoreGestioneLogistica, distributoreGestioneLogisticaOrdini, distributoreGestioneMagazzini, distributoreGestioneMagazzinieri, distributoreGestioneProdotti, distributoreGestionePVendita, distributoreGestioneScorte, distributoreGestioneUtenti, distributoreListiniAcquisto, distributoreListiniAssociati, distributoremagazzinoContoTerzi, distributoreMerceInevasa, distributoreModificaProdottiListino, distributoreModificaProdottiOrdine, distributoreOrdina, distributorePrevisioneAcquisti, distributoreStoricoFornitori, error, listino, login, logistica, logisticaGestioneLogistica, nuovaPassword, pdv, pdvCarrello, pdvDashboard, pdvGestioneLogisticaOrdini, pdvListinoProdotti, pdvStoricoDocumenti, recuperaPassword, registrati, respMag, resp_mag, resp_magComposizioneMagazzino, resp_magConfrontoDispQta, resp_magControlloLottiEScadenze, resp_magExternalsystems, resp_magGestioneLavorazioni, resp_magGestioneProdotti, resp_magGestioneProdottiPosizionati, resp_magInventario, resp_magMovimentazioneInIngresso, resp_magMovimentazioneInUscita, resp_magOperatori, settings, sistemiEsterni, visibility, distributoreGiacenzeProdotti, ringDashboard, ringCaricoMagazzino, ringScaricoMagazzino, ring, ringListinoProdotti, ringCarrello, ringGestioneScorte, ringControlloLottiEScadenze, ringConfrontoDispQta, ringOrdinato, chainControlloLottiEScadenze, chainConfrontoDispQta, chainGestioneAutisti, chainGestioneLogistica, chainGestioneMagazzinieri, ringComposizioneMagazzino, chainComposizioneMagazzino, scaricoMagazzino, distributoreGestioneRotture, distributoreGestioneResi, resp_magGestioneRotture, resp_magGestioneResi, adminGestioneCorporate, adminGeneratoreDocumenti, affiliatoGestioneMovimentazioni, agenteMessaggi, pdvMessaggi, affiliatoMessaggi } from './components/route';\nimport DocumentiDiRottura from './common/distributore/gestioneDocumentiDiRottura';\nimport DocumentiDiReso from './common/distributore/gestioneDocumentiDiReso';\nimport GestioneCorporate from './common/amministratore/GestioneCorporates';\nimport GeneratoreDocumenti from './common/amministratore/GeneratoreDocumenti';\nimport GestioneMovimentazioni from './common/affiliato/movimentazioni';\nimport Messaggi from './common/agenti/messaggi.jsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ErrorFallback(_ref) {\n  let {\n    error\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"alert\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Qualcosa \\xE8 andato storto:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n      style: {\n        color: 'red'\n      },\n      children: error.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n}\n_c = ErrorFallback;\nclass App extends Component {\n  async componentDidMount() {\n    if (window.location.pathname !== login && window.location.pathname !== listino && window.location.pathname !== recuperaPassword && window.location.pathname !== registrati) {\n      var user = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : '';\n      if (user && user.role === distributore) {\n        try {\n          await APIRequest('GET', 'externalsystems/').then(res => {\n            if (res && res.data) {\n              var role = localStorage.getItem(\"role\").toLowerCase();\n              Object.entries(menu[role]).forEach(element => {\n                if (element[0] === sistemiEsterni) {\n                  element[1] = [];\n                  element[1].visibility = 'true';\n                  res.data.forEach(el => {\n                    if (el && el.externalSistemName) {\n                      element[1][el.externalSistemName.toLowerCase()] = {\n                        'icona': 'cloud-download',\n                        'visibility': 'true'\n                      };\n                    }\n                  });\n                  menu[role][element[0]] = element[1];\n                }\n              });\n            }\n          });\n        } catch (e) {\n          console.warn('External systems endpoint not available:', e.message);\n          // Continue without external systems - this is not critical for basic functionality\n        }\n      }\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: [/*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(Provider, {\n          store: store,\n          children: [/*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: false,\n            path: basePath,\n            exact: true,\n            component: Home\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: true,\n            path: login,\n            exact: true,\n            component: Login\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: true,\n            path: registrati,\n            exact: true,\n            component: Registrati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: true,\n            path: recuperaPassword,\n            exact: true,\n            component: ForgotPwd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: true,\n            path: nuovaPassword,\n            exact: true,\n            component: NewPwd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PublicRoute, {\n            restricted: false,\n            path: listino,\n            exact: true,\n            component: FreePriceList\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: error,\n            restricted: true,\n            component: NoMatch\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: settings,\n            restricted: true,\n            component: AccountSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(Provider, {\n          store: store,\n          children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: dashboard,\n            restricted: true,\n            roles: [admin],\n            component: dashboardAmministratore\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: adminGestioneUtenti,\n            restricted: true,\n            roles: [admin],\n            component: GestioneUtenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: adminGestioneCorporate,\n            restricted: true,\n            roles: [admin],\n            component: GestioneCorporate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            exact: true,\n            path: adminGeneratoreDocumenti,\n            restricted: true,\n            roles: [admin],\n            component: GeneratoreDocumenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreDashboard,\n              roles: [distributore],\n              component: DashboardDistributore,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGenerali,\n              roles: [distributore],\n              component: GestioneAnagrafiche,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneAffiliati,\n              roles: [distributore],\n              component: Gestione_Affiliati,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneUtenti,\n              roles: [distributore],\n              component: GestioneUtentiDistributore,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneAgenti,\n              roles: [distributore],\n              component: GestioneAgenti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneAutisti,\n              roles: [distributore],\n              component: GestioneAutisti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneLogistica,\n              roles: [distributore],\n              component: GestioneLogisticaUtenti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneMagazzinieri,\n              roles: [distributore],\n              component: GestioneMagazzinieri,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneMagazzini,\n              roles: [distributore],\n              component: GestioneMagazzini,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneScorte,\n              roles: [distributore],\n              component: GestioneScorte,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestionePVendita,\n              roles: [distributore],\n              component: Gestione_Punti_Vendita,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneProdotti,\n              roles: [distributore],\n              component: Gestione_Prodotti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneListini,\n              roles: [distributore],\n              component: Gestione_Listini,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreListiniAssociati,\n              roles: [distributore],\n              component: VisualizzaListiniAssociati,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreAggiungiListini,\n              roles: [distributore],\n              component: AggiungiListini,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreModificaProdottiListino,\n              roles: [distributore],\n              component: ModificaProdottiListino,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreModificaProdottiOrdine,\n              roles: [distributore],\n              component: ModificaProdottiOrdine,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreDocumentiOrdineVendita,\n              roles: [distributore],\n              component: UfficioVendite,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreDocumentiOrdineAcquisto,\n              roles: [distributore],\n              component: UfficioAcquisti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneLogisticaOrdini,\n              roles: [distributore],\n              component: GestioneLogisticaOdini,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreMerceInevasa,\n              roles: [distributore],\n              component: MerceInevasa,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreAggiungiDocInevasi,\n              roles: [distributore],\n              component: AggiungiDocInevasi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreGestioneConsegne,\n              roles: [distributore],\n              component: AssegnaConsegne,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributorePrevisioneAcquisti,\n              roles: [distributore],\n              component: BuyerPrediction,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreStoricoFornitori,\n              roles: [distributore],\n              component: StoricoFornitori,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreFornitori,\n              roles: [distributore],\n              component: GestioneFornitori,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreOrdina,\n              roles: [distributore],\n              component: marketplaceUffAcq,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreListiniAcquisto,\n              roles: [distributore],\n              component: ListiniAcquisto,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoremagazzinoContoTerzi,\n              roles: [distributore],\n              component: magazzinoContoTerzi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: distributoreFornitoreAffiliato,\n              roles: [distributore],\n              component: FornitoreAffiliato,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: distributoreCarrello,\n              roles: [distributore],\n              component: RiepilogoOrdineAff\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: distributoreGiacenzeProdotti,\n              roles: [distributore],\n              component: GiacenzeProdotti\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: distributoreGestioneRotture,\n              roles: [distributore],\n              component: DocumentiDiRottura\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: distributoreGestioneResi,\n              roles: [distributore],\n              component: DocumentiDiReso\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), Object.entries(menu.distributore || {}).map((el, index) => {\n              if (el[0] === sistemiEsterni) {\n                Object.keys(el[1]).forEach((element, key) => {\n                  if (element !== visibility) {\n                    return /*#__PURE__*/_jsxDEV(Link, {\n                      to: distributoreExternalsystems + element\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"span\", {}, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 29\n                  }, this);\n                });\n              }\n              return /*#__PURE__*/_jsxDEV(\"span\", {}, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 25\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: \"/distributore/externalsystems/:element\",\n              roles: [distributore],\n              component: Alyante,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoDashboard,\n              roles: affiliato,\n              component: DashboardAffiliato,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoVisualizzaListini,\n              roles: affiliato,\n              component: VisualizzaListino,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoGestioneListiniPDV,\n              roles: affiliato,\n              component: AssociaListiniPV,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoVisualizzaOrdini,\n              roles: [affiliato],\n              component: VisualizzaOrdine,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoGestionePuntiVendita,\n              roles: [affiliato],\n              component: Gestione_PDV2,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoCreaOrdine,\n              roles: [affiliato],\n              component: AggiungiOrdineAffiliato,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoCarrello,\n              roles: [affiliato],\n              component: RiepilogoOrdineAff\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoGestioneScorte,\n              roles: [affiliato],\n              component: GestioneScorteAff\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoGestioneMovimentazioni,\n              roles: [affiliato],\n              component: GestioneMovimentazioni\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoGestioneDocumenti,\n              roles: [affiliato],\n              component: GestioneDocumentiAFF\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoLogistica,\n              roles: [affiliato],\n              component: Approvvigionamento\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: affiliatoRiepilogo,\n              roles: [affiliato],\n              component: RiepilogoApprovvigionamento\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoDocumentiOrdineAcquisto,\n              roles: [affiliato],\n              component: UfficioAcquisti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoListiniAcquisto,\n              roles: [affiliato],\n              component: ListiniAcquisto,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoFornitori,\n              roles: [affiliato],\n              component: GestioneFornitori,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoOrdina,\n              roles: [affiliato],\n              component: marketplaceUffAcq,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: affiliatoMessaggi,\n              roles: [affiliato],\n              component: Messaggi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteDashboard,\n              roles: [agente],\n              component: DashboardAgente,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteGestioneOrdiniAgente,\n              roles: [agente],\n              component: GestioneOdiniAgente,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteGestioneClientiAgente,\n              roles: [agente],\n              component: GestioneClientiAgente,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteDettagliOrdine,\n              roles: [agente],\n              component: dettagliOrdineAgente,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteCreaOrdine,\n              roles: [agente],\n              component: AggiungiOrdineAgente,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteOrdineDiretto,\n              roles: [agente],\n              component: OrdineDiretto,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteCompletaOrdine,\n              roles: [agente],\n              component: CompletaOrdine,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteVisualizzaListini,\n              roles: [agente],\n              component: VisualizzaListino,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteDettaglioCliente,\n              roles: [agente],\n              component: DettagliClienti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteStoricoDocumenti,\n              roles: [agente],\n              component: GestioneDocumentiAFF,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteMerceInevasa,\n              roles: [agente],\n              component: MerceInevasa,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteMessaggi,\n              roles: [agente],\n              component: Messaggi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: agenteGestionePDVAutonoma,\n              roles: [agente],\n              component: GestionePDVAutonoma,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), Object.entries(menu.agente || {}).map((el, index) => {\n              if (el[0] === sistemiEsterni) {\n                Object.keys(el[1]).forEach((element, key) => {\n                  if (element !== visibility) {\n                    return /*#__PURE__*/_jsxDEV(Link, {\n                      to: agenteExternalsystems + element\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"span\", {}, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 29\n                  }, this);\n                });\n              }\n              return /*#__PURE__*/_jsxDEV(\"span\", {}, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 25\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: \"/agente/externalsystems/:element\",\n              roles: [agente],\n              component: Alyante,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: logisticaGestioneLogistica,\n              roles: [logistica],\n              component: GestioneLogistica,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: autistaGestioneConsegne,\n              roles: [autista],\n              component: GestioneConsegne,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: autistaDatiConsegnaAutista,\n              roles: [autista],\n              component: DatiConsegnaAutista,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: pdvDashboard,\n              roles: [pdv],\n              component: DashboardPDV,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: pdvListinoProdotti,\n              restricted: true,\n              roles: [pdv],\n              component: Marketplace\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: pdvGestioneLogisticaOrdini,\n              restricted: true,\n              roles: [pdv],\n              component: GestioneOrdiniPDV\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: pdvCarrello,\n              restricted: true,\n              roles: [pdv],\n              component: Cart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: pdvMessaggi,\n              roles: [pdv],\n              component: Messaggi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: pdvStoricoDocumenti,\n              roles: [pdv],\n              component: GestioneDocumentiPDV,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: respMag,\n              roles: [resp_mag],\n              component: DashboardRespMagazzino,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magInventario,\n              roles: [resp_mag],\n              component: Inventario,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magMovimentazioneInIngresso,\n              roles: [resp_mag],\n              component: MovimentazioneInIngresso,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magMovimentazioneInUscita,\n              roles: [resp_mag],\n              component: MovimentazioneInUscita,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magGestioneLavorazioni,\n              roles: [resp_mag],\n              component: GestioneLavorazioni,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magOperatori,\n              roles: [resp_mag],\n              component: GestioneOperatori,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magComposizioneMagazzino,\n              roles: [resp_mag],\n              component: ComposizioneMagazzino,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magGestioneProdottiPosizionati,\n              roles: [resp_mag],\n              component: GestioneProdPos,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magControlloLottiEScadenze,\n              roles: [resp_mag],\n              component: ControlloLottiEScadenze,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magGestioneProdotti,\n              roles: [resp_mag],\n              component: GestioneProdotti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: resp_magConfrontoDispQta,\n              roles: [resp_mag],\n              component: ConfrontoDispQta,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: resp_magGestioneRotture,\n              roles: [resp_mag],\n              component: DocumentiDiRottura\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: resp_magGestioneResi,\n              roles: [resp_mag],\n              component: DocumentiDiReso\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), Object.entries(menu.resp_mag || {}).map((el, index) => {\n              if (el[0] === sistemiEsterni) {\n                Object.keys(el[1]).forEach((element, key) => {\n                  if (element !== visibility) {\n                    return /*#__PURE__*/_jsxDEV(Link, {\n                      to: resp_magExternalsystems + element\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"span\", {}, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 29\n                  }, this);\n                });\n              }\n              return /*#__PURE__*/_jsxDEV(\"span\", {}, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 25\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: \"/resp_mag/externalsystems/:element\",\n              roles: [resp_mag],\n              component: Alyante,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainDashboard,\n              roles: [chain],\n              component: DashboardChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGenerali,\n              roles: [chain],\n              component: AnagraficheChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestionePVendita,\n              roles: [chain],\n              component: GestionePDV,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainmagazzinoContoTerzi,\n              roles: [chain],\n              component: magazzinoContoTerzi,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneMagazzini,\n              roles: [chain],\n              component: GestioneMagazziniChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneScorte,\n              roles: [chain],\n              component: GestioneScorteChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneProdotti,\n              roles: [chain],\n              component: GestioneProd,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainDocumentiOrdineVendita,\n              roles: [chain],\n              component: DocumentiVendite,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainDocumentiOrdineAcquisto,\n              roles: [chain],\n              component: DocumentiAcquisti,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneConsegne,\n              roles: [chain],\n              component: AssegnaConsegneChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainFornitori,\n              roles: [chain],\n              component: GestioneFornitoriChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainOrdina,\n              roles: [chain],\n              component: marketplaceUffAcqChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainListiniAcquisto,\n              roles: [chain],\n              component: ListiniAcquistoChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: chainCarrello,\n              roles: [chain],\n              component: RiepilogoOrdineAff\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainControlloLottiEScadenze,\n              roles: [chain],\n              component: ControlloLottiEScadenzeChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainConfrontoDispQta,\n              roles: [chain],\n              component: ConfrontoDispQtaChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneAutisti,\n              roles: [chain],\n              component: GestioneAutistiChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneLogistica,\n              roles: [chain],\n              component: GestioneLogisticaUtentiChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainGestioneMagazzinieri,\n              roles: [chain],\n              component: GestioneMagazzinieriChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: chainComposizioneMagazzino,\n              roles: [chain],\n              component: ComposizioneMagazzinoChain,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          FallbackComponent: ErrorFallback,\n          children: /*#__PURE__*/_jsxDEV(Provider, {\n            store: store,\n            children: [/*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringDashboard,\n              roles: [ring],\n              component: DashboardRING,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: ringListinoProdotti,\n              restricted: true,\n              roles: [ring],\n              component: MarketplaceRing\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringCaricoMagazzino,\n              roles: [ring],\n              component: CaricoMagRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringScaricoMagazzino,\n              roles: [ring],\n              component: ScaricoMagRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: ringCarrello,\n              restricted: true,\n              roles: [ring],\n              component: Cart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringGestioneScorte,\n              roles: [ring],\n              component: GestioneScorteRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringControlloLottiEScadenze,\n              roles: [ring],\n              component: ControlloLottiEScadenzeRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringConfrontoDispQta,\n              roles: [ring],\n              component: ConfrontoDispQtaRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringOrdinato,\n              roles: [ring],\n              component: OrdinatoRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              path: ringComposizioneMagazzino,\n              roles: [ring],\n              component: ComposizioneMagazzinoRing,\n              exact: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              exact: true,\n              path: scaricoMagazzino,\n              roles: [ring],\n              component: ScaricoMagazzinoRing\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GoToTheTop, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackendTestButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"ErrorFallback\");", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Link", "Switch", "Provider", "Error<PERSON>ou<PERSON><PERSON>", "APIRequest", "GoToTheTop", "BackendTestButton", "Home", "<PERSON><PERSON>", "Registrati", "ForgotPwd", "PrivateRoute", "PublicRoute", "AggiungiListini", "ModificaProdottiListino", "AssociaListiniPV", "ModificaProdottiOrdine", "AggiungiDocInevasi", "DashboardDistributore", "Gestione_Prodotti", "Gestione_Affiliati", "GestioneLogistica<PERSON>", "Gestione_Punti_Vendita", "Gestione_Listini", "UfficioVendite", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GestioneAnagrafiche", "GestioneLogistica<PERSON>i", "VisualizzaListiniAssociati", "GestioneMagazzinieri", "GestioneUtentiDistributore", "GestioneMagazzini", "GestioneScorte", "Merce<PERSON>ne<PERSON><PERSON>", "AssegnaConsegne", "Alyante", "GestioneAgenti", "GestioneAutisti", "BuyerPrediction", "StoricoFornitori", "GestioneFornitori", "marketplaceUffAcq", "ListiniAcquisto", "magazzinoConto<PERSON>zi", "FornitoreAffiliato", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RiepilogoApprovvigionamento", "GestioneScorteAff", "GestioneDocumentiAFF", "DashboardAffiliato", "VisualizzaListino", "VisualizzaOrdine", "AggiungiOrdineAffiliato", "RiepilogoOrdineAff", "Approvvigionamento", "GestioneOdiniAgente", "Gestione_PDV2", "dashboardAmministratore", "DashboardPDV", "GestioneOrdiniPDV", "GestioneDocumentiPDV", "<PERSON><PERSON>", "store", "Marketplace", "GestioneUtenti", "GestioneLogistica", "DashboardAgente", "GestioneClientiAgente", "AggiungiOrdineAgente", "dettagliOrdineAgente", "DettagliClienti", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletaOrdine", "GestionePDVAutonoma", "GestioneConsegne", "DatiConsegnaAutista", "DashboardRespMagazzino", "MovimentazioneInIngresso", "MovimentazioneInUscita", "GestioneLavorazioni", "GestioneOperatori", "ControlloLottiEScadenze", "ConfrontoDispQta", "ComposizioneMagazzino", "Inventario", "GestioneProdPos", "GestioneProdotti", "DashboardChain", "GestioneProd", "GestionePDV", "DocumentiVendite", "DocumentiAcquisti", "Anagrafic<PERSON><PERSON><PERSON><PERSON>", "GestioneMagazziniChain", "GestioneScorteChain", "AssegnaConsegneChain", "marketplaceUffAcqChain", "ListiniAcquisto<PERSON>hain", "GestioneFornitoriChain", "ControlloLottiEScadenzeChain", "ConfrontoDispQtaChain", "GestioneAutistiChain", "GestioneLogistica<PERSON>ti<PERSON>hain", "GestioneMagazzinieriChain", "ComposizioneMagazzinoChain", "DashboardRING", "MarketplaceRing", "CaricoMagRing", "ScaricoMagRing", "GestioneScorteRing", "ControlloLottiEScadenzeRing", "ConfrontoDispQtaRing", "OrdinatoRing", "ComposizioneMagazzinoRing", "ScaricoMagazzinoRing", "Footer", "NoMatch", "NewPwd", "menu", "AccountSettings", "FreePriceList", "chain", "chainCarrello", "chainDashboard", "chainDocumentiOrdineAcquisto", "chainDocumentiOrdineVendita", "chainFornitori", "chainGenerali", "chainGestioneConsegne", "chainGestioneMagazzini", "chainGestioneProdotti", "chainGestioneScorte", "chainListiniAcquisto", "chainOrdina", "chainGestionePVendita", "chainmagazzinoContoTerzi", "admin", "adminGestioneUtenti", "affiliato", "affiliatoCarrello", "affiliatoCreaOrdine", "affiliatoDashboard", "affiliatoLogistica", "affiliatoDocumentiOrdine<PERSON>cquisto", "affiliato<PERSON><PERSON><PERSON><PERSON>", "affiliatoGestioneDocumenti", "affiliatoGestioneListiniPDV", "affiliatoGestionePuntiVendita", "affiliatoGestioneScorte", "affiliatoListiniAcquisto", "affiliato<PERSON><PERSON><PERSON>", "affiliatoRiepilogo", "affiliatoVisualizzaListini", "affiliatoVisualizza<PERSON>", "agente", "agenteCompletaOrdine", "agenteCreaOrdine", "agenteDashboard", "agenteDettaglioCliente", "agenteDettagliOrdine", "agenteExternalsystems", "agenteGestioneClientiAgente", "agenteGestioneOrdiniAgente", "agenteGestionePDVAutonoma", "agenteMerceInevasa", "agente<PERSON><PERSON><PERSON>", "agenteStoricoDocumenti", "agenteVisualizzaListini", "au<PERSON>", "autistaDatiConsegnaAutista", "autistaGestioneConsegne", "basePath", "dashboard", "distributore", "distributoreAggiungiDocInevasi", "distributoreAggiungiListini", "distributoreCarrello", "distributoreDashboard", "distributoreDocumentiOrdineAcquisto", "distributoreDocumentiOrdineVendita", "distributoreExternalsystems", "distributoreFornitoreAffiliato", "distributoreFornitori", "distributoreGenerali", "distributoreGestioneAffiliati", "distributoreGestioneAgenti", "distributoreGestioneAutisti", "distributoreGestioneConsegne", "distributoreGestioneListini", "distributoreGestioneLogistica", "distributoreGestioneLogisticaOrdini", "distributoreGestioneMagazzini", "distributoreGestioneMagazzinieri", "distributoreGestioneProdotti", "distributoreGestionePVendita", "distributoreGestioneScorte", "distributoreGestioneUtenti", "distributoreListiniAcquisto", "distributoreListiniAssociati", "distributoremagazzinoContoTerzi", "distributoreMerceInevasa", "distributoreModificaProdottiListino", "distributoreModificaProdottiOrdine", "distributoreOrdina", "distributorePrevisioneAcquisti", "distributoreStoricoFornitori", "error", "listino", "login", "logistica", "logisticaGestioneLogistica", "nuovaPassword", "pdv", "pdvCarrello", "pdvDashboard", "pdvGestioneLogistica<PERSON>rdini", "pdvListino<PERSON>i", "pdvStoricoDocumenti", "recuperaPassword", "registrati", "respMag", "resp_mag", "resp_magComposizioneMagazzino", "resp_magConfrontoDispQta", "resp_magControlloLottiEScadenze", "resp_magExternalsystems", "resp_magGestioneLavorazioni", "resp_magGestioneProdotti", "resp_magGestioneProdottiPosizionati", "resp_magInventario", "resp_magMovimentazioneInIngresso", "resp_magMovimentazioneInUscita", "resp_magO<PERSON>atori", "settings", "<PERSON><PERSON><PERSON>", "visibility", "distributoreGiacenzeProdotti", "ringDashboard", "ringCaricoMagazzino", "ringScaricoMagazzino", "ring", "ring<PERSON><PERSON><PERSON><PERSON><PERSON>", "ringCarrello", "ringGestioneScorte", "ringControlloLottiEScadenze", "ringConfrontoDispQta", "ringOrdinato", "chainControlloLottiEScadenze", "chainConfrontoDispQta", "chainGestioneAutisti", "chainGestioneLogistica", "chainGestioneMagazzinieri", "ringComposizioneMagazzino", "chainComposizioneMagazzino", "scaricoMagazzi<PERSON>", "distributoreGestioneRotture", "distributoreGestioneResi", "resp_magGestioneRotture", "resp_magGestioneResi", "adminGestioneCorporate", "adminGeneratoreDocumenti", "affiliatoGestioneMovimentazioni", "agente<PERSON><PERSON>aggi", "pdvMessaggi", "affilia<PERSON><PERSON><PERSON><PERSON><PERSON>", "DocumentiDiRottura", "DocumentiDiReso", "GestioneCorporate", "GeneratoreDocumenti", "GestioneMovimentazioni", "Messaggi", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "role", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "message", "_c", "App", "componentDidMount", "window", "location", "pathname", "user", "localStorage", "getItem", "JSON", "parse", "then", "res", "data", "toLowerCase", "Object", "entries", "for<PERSON>ach", "element", "el", "externalSistemName", "e", "console", "warn", "render", "restricted", "path", "exact", "component", "roles", "FallbackComponent", "map", "index", "keys", "key", "to", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/src/App.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Switch } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ErrorBoundary } from 'react-error-boundary';\nimport { APIRequest } from './components/generalizzazioni/apireq';\nimport { GoToTheTop } from './components/generalizzazioni/icone fisse/goToTopOfPage';\n// import BackendHealthCheck from './components/BackendHealthCheck';\nimport BackendTestButton from './components/BackendTestButton';\nimport Home from './components/Home';\nimport Login from './components/Login';\nimport Registrati from './components/registrati';\nimport ForgotPwd from './components/forgotPwd';\nimport PrivateRoute from './components/PrivateRoute';\nimport PublicRoute from './components/PublicRoute';\n\nimport AggiungiListini from './aggiunta_dati/aggiungiListino';\nimport ModificaProdottiListino from './aggiunta_dati/aggiungiProdottiListino';\nimport AssociaListiniPV from './aggiunta_dati/associaListinoPV';\nimport ModificaProdottiOrdine from './aggiunta_dati/modificaProdOrdine';\nimport AggiungiDocInevasi from './aggiunta_dati/aggiungiDocInevasi';\n\nimport DashboardDistributore from './common/distributore/dashboard/dashboardDistributore';\nimport Gestione_Prodotti from './common/distributore/gestioneProdotti';\nimport Gestione_Affiliati from './common/distributore/gestioneAffiliati';\nimport GestioneLogisticaUtenti from './common/distributore/gestioneOPLogistica';\nimport Gestione_Punti_Vendita from './common/distributore/gestionePVendita';\nimport Gestione_Listini from './common/distributore/gestioneListini';\nimport UfficioVendite from './common/distributore/ufficioVendite';\nimport UfficioAcquisti from './common/distributore/ufficioAcquisti';\nimport GestioneAnagrafiche from './common/distributore/gestioneClienti';\nimport GestioneLogisticaOdini from './common/distributore/gestioneLogisticaOrdini';\nimport VisualizzaListiniAssociati from './common/distributore/visualizzaListiniAssociati';\nimport GestioneMagazzinieri from './common/distributore/gestioneMagazzinieri';\nimport GestioneUtentiDistributore from './common/distributore/gestioneUtentiDistributore';\nimport GestioneMagazzini from './common/distributore/gestioneMagazzini';\nimport GestioneScorte from './common/distributore/gestioneScorte';\nimport MerceInevasa from './common/distributore/gestioneDocInevasi';\nimport AssegnaConsegne from './common/distributore/gestioneConsegne';\nimport Alyante from './common/distributore/alyante';\nimport GestioneAgenti from './common/distributore/gestioneAgenti';\nimport GestioneAutisti from './common/distributore/gestioneAutisti';\nimport BuyerPrediction from './common/distributore/previsioneAcquisti';\nimport StoricoFornitori from './common/distributore/storicoFornitori';\nimport GestioneFornitori from './common/distributore/gestioneFornitori';\nimport marketplaceUffAcq from './common/distributore/marketplaceUffAcq';\nimport ListiniAcquisto from './common/distributore/listiniAcquisto';\nimport magazzinoContoTerzi from './common/distributore/magazzinoContoTerzi';\nimport FornitoreAffiliato from './common/distributore/fornitoreAffiliato';\nimport GiacenzeProdotti from './common/distributore/giacenzeProdotti';\n\nimport RiepilogoApprovvigionamento from './common/affiliato/riepilogoApprovvigionamento';\nimport GestioneScorteAff from './common/affiliato/gestioneScorte';\nimport GestioneDocumentiAFF from './common/affiliato/gestioneDocumenti';\nimport DashboardAffiliato from './common/affiliato/dashboard/dashboardAffiliato';\nimport VisualizzaListino from './common/affiliato/visualizzaListini';\nimport VisualizzaOrdine from './common/affiliato/visualizzaOrdini';\nimport AggiungiOrdineAffiliato from './common/affiliato/creaOrdine';\nimport RiepilogoOrdineAff from './common/affiliato/riepilogoOrdineAff';\nimport Approvvigionamento from './common/affiliato/approvvigionamento';\nimport GestioneOdiniAgente from './common/agenti/gestioneOrdiniAgenti';\nimport Gestione_PDV2 from './common/affiliato/gestionePuntiVendita';\nimport dashboardAmministratore from './common/amministratore/DashboardAmministratore';\n\nimport DashboardPDV from './common/PDV/dashboardPDV/dashboardPDV';\nimport GestioneOrdiniPDV from './common/PDV/gestioneOrdiniPDV';\nimport GestioneDocumentiPDV from './common/PDV/gestioneDocumenti';\nimport Cart from './common/PDV/carrello/riepilogoOrdinePDV';\nimport store from './common/PDV/carrello/store.jsx';\nimport Marketplace from './common/PDV/listino/listinoPDV';\n\nimport GestioneUtenti from './common/amministratore/GestioneUtenti';\n\nimport GestioneLogistica from './common/logistica/gestioneLogistica';\n\nimport DashboardAgente from './common/agenti/dashboardAgenti/dashboardAgente';\nimport GestioneClientiAgente from './common/agenti/gestioneClientiAgente';\nimport AggiungiOrdineAgente from './common/agenti/listinoAgente';\nimport dettagliOrdineAgente from './common/agenti/dettagliOrdineAgente';\nimport DettagliClienti from './common/agenti/dettagliClienti';\nimport OrdineDiretto from './common/agenti/ordineDiretto';\nimport CompletaOrdine from './common/agenti/completaOrdine';\nimport GestionePDVAutonoma from './common/agenti/GestionePDVAutonoma.jsx';\n\nimport GestioneConsegne from './common/autista/gestioneConsegne';\nimport DatiConsegnaAutista from './common/autista/riepilogoConsegna';\n\nimport DashboardRespMagazzino from './common/respMagazzino/dashboardRespMagazzino';\nimport MovimentazioneInIngresso from './common/respMagazzino/movimentazioneInIngresso';\nimport MovimentazioneInUscita from './common/respMagazzino/movimentazioneInUscita';\nimport GestioneLavorazioni from './common/respMagazzino/gestioneLavorazioni';\nimport GestioneOperatori from './common/respMagazzino/gestioneOperatori';\nimport ControlloLottiEScadenze from './common/respMagazzino/controlloLottiEScadenze';\nimport ConfrontoDispQta from './common/respMagazzino/confrontoDispQta';\nimport ComposizioneMagazzino from './common/respMagazzino/composizioneMagazzino';\nimport Inventario from './common/respMagazzino/inventario';\nimport GestioneProdPos from './common/respMagazzino/gestioneProdPos';\nimport GestioneProdotti from './common/respMagazzino/gestioneProdotti';\n\nimport DashboardChain from './common/chain/dashboard/dashboardChain';\nimport GestioneProd from './common/chain/gestioneProdotti';\nimport GestionePDV from './common/chain/gestionePuntiVendita';\nimport DocumentiVendite from './common/chain/ufficioVendite';\nimport DocumentiAcquisti from './common/chain/ufficioAcquisti';\nimport AnagraficheChain from './common/chain/gestioneClienti';\nimport GestioneMagazziniChain from './common/chain/gestioneMagazzini';\nimport GestioneScorteChain from './common/chain/gestioneScorte';\nimport AssegnaConsegneChain from './common/chain/gestioneConsegne';\nimport marketplaceUffAcqChain from './common/chain/marketplaceUffAcq';\nimport ListiniAcquistoChain from './common/chain/listiniAcquisto';\nimport GestioneFornitoriChain from './common/chain/gestioneFornitori';\nimport ControlloLottiEScadenzeChain from './common/chain/controlloLottiEScadenze';\nimport ConfrontoDispQtaChain from './common/chain/confrontoDispQta';\nimport GestioneAutistiChain from './common/chain/gestioneAutisti';\nimport GestioneLogisticaUtentiChain from './common/chain/gestioneOPLogistica';\nimport GestioneMagazzinieriChain from './common/chain/gestioneMagazzinieri';\nimport ComposizioneMagazzinoChain from './common/chain/composizioneMagazzino';\n\nimport DashboardRING from './common/ring/dashboard/dashboardRing';\nimport MarketplaceRing from './common/ring/listinoRing';\nimport CaricoMagRing from './common/ring/caricoMagRing';\nimport ScaricoMagRing from './common/ring/scaricoMagRing';\nimport GestioneScorteRing from './common/ring/gestioneScorte';\nimport ControlloLottiEScadenzeRing from './common/ring/controlloLottiEScadenze';\nimport ConfrontoDispQtaRing from './common/ring/confrontoDispQta';\nimport OrdinatoRing from './common/ring/ordinatoRing';\nimport ComposizioneMagazzinoRing from './common/ring/composizioneMagazzino';\nimport ScaricoMagazzinoRing from './common/ring/scaricoMagazzino';\n\nimport Footer from './components/footer/footer';\nimport NoMatch from './components/handleError';\nimport NewPwd from './components/newPwd';\nimport menu from './components/generalizzazioni/menu.json';\nimport AccountSettings from './components/accountSettings';\nimport FreePriceList from './components/freePriceList/freePriceList';\nimport './css/frontend.css';\nimport './App.css';\nimport {\n  chain,\n  chainCarrello,\n  chainDashboard,\n  chainDocumentiOrdineAcquisto,\n  chainDocumentiOrdineVendita,\n  chainFornitori,\n  chainGenerali,\n  chainGestioneConsegne,\n  chainGestioneMagazzini,\n  chainGestioneProdotti,\n  chainGestioneScorte,\n  chainListiniAcquisto,\n  chainOrdina,\n  chainGestionePVendita,\n  chainmagazzinoContoTerzi,\n  admin,\n  adminGestioneUtenti,\n  affiliato,\n  affiliatoCarrello,\n  affiliatoCreaOrdine,\n  affiliatoDashboard,\n  affiliatoLogistica,\n  affiliatoDocumentiOrdineAcquisto,\n  affiliatoFornitori,\n  affiliatoGestioneDocumenti,\n  affiliatoGestioneListiniPDV,\n  affiliatoGestionePuntiVendita,\n  affiliatoGestioneScorte, affiliatoListiniAcquisto, affiliatoOrdina, affiliatoRiepilogo,\n  affiliatoVisualizzaListini,\n  affiliatoVisualizzaOrdini,\n  agente,\n  agenteCompletaOrdine,\n  agenteCreaOrdine,\n  agenteDashboard,\n  agenteDettaglioCliente,\n  agenteDettagliOrdine,\n  agenteExternalsystems,\n  agenteGestioneClientiAgente,\n  agenteGestioneOrdiniAgente,\n  agenteGestionePDVAutonoma,\n  agenteMerceInevasa,\n  agenteOrdineDiretto,\n  agenteStoricoDocumenti,\n  agenteVisualizzaListini,\n  autista,\n  autistaDatiConsegnaAutista,\n  autistaGestioneConsegne,\n  basePath,\n  dashboard,\n  distributore,\n  distributoreAggiungiDocInevasi,\n  distributoreAggiungiListini,\n  distributoreCarrello,\n  distributoreDashboard,\n  distributoreDocumentiOrdineAcquisto,\n  distributoreDocumentiOrdineVendita,\n  distributoreExternalsystems,\n  distributoreFornitoreAffiliato,\n  distributoreFornitori,\n  distributoreGenerali,\n  distributoreGestioneAffiliati,\n  distributoreGestioneAgenti,\n  distributoreGestioneAutisti,\n  distributoreGestioneConsegne,\n  distributoreGestioneListini,\n  distributoreGestioneLogistica,\n  distributoreGestioneLogisticaOrdini,\n  distributoreGestioneMagazzini,\n  distributoreGestioneMagazzinieri,\n  distributoreGestioneProdotti,\n  distributoreGestionePVendita,\n  distributoreGestioneScorte,\n  distributoreGestioneUtenti,\n  distributoreListiniAcquisto,\n  distributoreListiniAssociati,\n  distributoremagazzinoContoTerzi,\n  distributoreMerceInevasa,\n  distributoreModificaProdottiListino,\n  distributoreModificaProdottiOrdine,\n  distributoreOrdina,\n  distributorePrevisioneAcquisti,\n  distributoreStoricoFornitori,\n  error,\n  listino,\n  login,\n  logistica,\n  logisticaGestioneLogistica,\n  nuovaPassword,\n  pdv,\n  pdvCarrello,\n  pdvDashboard,\n  pdvGestioneLogisticaOrdini,\n  pdvListinoProdotti,\n  pdvStoricoDocumenti,\n  recuperaPassword,\n  registrati,\n  respMag,\n  resp_mag,\n  resp_magComposizioneMagazzino,\n  resp_magConfrontoDispQta,\n  resp_magControlloLottiEScadenze,\n  resp_magExternalsystems,\n  resp_magGestioneLavorazioni,\n  resp_magGestioneProdotti,\n  resp_magGestioneProdottiPosizionati,\n  resp_magInventario,\n  resp_magMovimentazioneInIngresso,\n  resp_magMovimentazioneInUscita,\n  resp_magOperatori,\n  settings,\n  sistemiEsterni,\n  visibility,\n  distributoreGiacenzeProdotti,\n  ringDashboard,\n  ringCaricoMagazzino,\n  ringScaricoMagazzino,\n  ring,\n  ringListinoProdotti,\n  ringCarrello,\n  ringGestioneScorte,\n  ringControlloLottiEScadenze,\n  ringConfrontoDispQta,\n  ringOrdinato,\n  chainControlloLottiEScadenze,\n  chainConfrontoDispQta,\n  chainGestioneAutisti,\n  chainGestioneLogistica,\n  chainGestioneMagazzinieri,\n  ringComposizioneMagazzino,\n  chainComposizioneMagazzino,\n  scaricoMagazzino,\n  distributoreGestioneRotture,\n  distributoreGestioneResi,\n  resp_magGestioneRotture,\n  resp_magGestioneResi,\n  adminGestioneCorporate,\n  adminGeneratoreDocumenti,\n  affiliatoGestioneMovimentazioni,\n  agenteMessaggi,\n  pdvMessaggi,\n  affiliatoMessaggi\n} from './components/route';\nimport DocumentiDiRottura from './common/distributore/gestioneDocumentiDiRottura';\nimport DocumentiDiReso from './common/distributore/gestioneDocumentiDiReso';\nimport GestioneCorporate from './common/amministratore/GestioneCorporates';\nimport GeneratoreDocumenti from './common/amministratore/GeneratoreDocumenti';\nimport GestioneMovimentazioni from './common/affiliato/movimentazioni';\nimport Messaggi from './common/agenti/messaggi.jsx';\n\nfunction ErrorFallback({ error }) {\n  return (\n    <div role=\"alert\">\n      <p>Qualcosa è andato storto:</p>\n      <pre style={{ color: 'red' }}>{error.message}</pre>\n    </div>\n  )\n}\n\nclass App extends Component {\n\n  async componentDidMount() {\n    if (window.location.pathname !== login && window.location.pathname !== listino && window.location.pathname !== recuperaPassword && window.location.pathname !== registrati) {\n      var user = localStorage.getItem(\"user\") ? JSON.parse(localStorage.getItem(\"user\")) : ''\n      if (user && user.role === distributore) {\n        try {\n          await APIRequest('GET', 'externalsystems/')\n            .then(res => {\n              if (res && res.data) {\n                var role = localStorage.getItem(\"role\").toLowerCase()\n                Object.entries(menu[role]).forEach(element => {\n                  if (element[0] === sistemiEsterni) {\n                    element[1] = []\n                    element[1].visibility = 'true'\n                    res.data.forEach(el => {\n                      if (el && el.externalSistemName) {\n                        element[1][el.externalSistemName.toLowerCase()] = {\n                          'icona': 'cloud-download',\n                          'visibility': 'true'\n                        }\n                      }\n                    })\n                    menu[role][element[0]] = element[1]\n                  }\n                })\n              }\n            })\n        } catch (e) {\n          console.warn('External systems endpoint not available:', e.message);\n          // Continue without external systems - this is not critical for basic functionality\n        }\n      }\n    }\n  }\n\n  render() {\n    return (\n      <BrowserRouter>\n        <Switch>\n          <Provider store={store}>\n            <PublicRoute restricted={false} path={basePath} exact component={Home} />\n            <PublicRoute restricted={true} path={login} exact component={Login} />\n            <PublicRoute restricted={true} path={registrati} exact component={Registrati} />\n            <PublicRoute restricted={true} path={recuperaPassword} exact component={ForgotPwd} />\n            <PublicRoute restricted={true} path={nuovaPassword} exact component={NewPwd} />\n            <PublicRoute restricted={false} path={listino} exact component={FreePriceList} />\n            <PrivateRoute exact path={error} restricted={true} component={NoMatch} />\n            <PrivateRoute exact path={settings} restricted={true} component={AccountSettings} />\n          </Provider>\n        </Switch>\n\n        <Switch>\n          <Provider store={store}>\n            <PrivateRoute exact path={dashboard} restricted={true} roles={[admin]} component={dashboardAmministratore} />\n            <PrivateRoute exact path={adminGestioneUtenti} restricted={true} roles={[admin]} component={GestioneUtenti} />\n            <PrivateRoute exact path={adminGestioneCorporate} restricted={true} roles={[admin]} component={GestioneCorporate} />\n            <PrivateRoute exact path={adminGeneratoreDocumenti} restricted={true} roles={[admin]} component={GeneratoreDocumenti} />\n          </Provider>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={distributoreDashboard} roles={[distributore]} component={DashboardDistributore} exact />\n              <PrivateRoute path={distributoreGenerali} roles={[distributore]} component={GestioneAnagrafiche} exact />\n              <PrivateRoute path={distributoreGestioneAffiliati} roles={[distributore]} component={Gestione_Affiliati} exact />\n              <PrivateRoute path={distributoreGestioneUtenti} roles={[distributore]} component={GestioneUtentiDistributore} exact />\n              <PrivateRoute path={distributoreGestioneAgenti} roles={[distributore]} component={GestioneAgenti} exact />\n              <PrivateRoute path={distributoreGestioneAutisti} roles={[distributore]} component={GestioneAutisti} exact />\n              <PrivateRoute path={distributoreGestioneLogistica} roles={[distributore]} component={GestioneLogisticaUtenti} exact />\n              <PrivateRoute path={distributoreGestioneMagazzinieri} roles={[distributore]} component={GestioneMagazzinieri} exact />\n              <PrivateRoute path={distributoreGestioneMagazzini} roles={[distributore]} component={GestioneMagazzini} exact />\n              <PrivateRoute path={distributoreGestioneScorte} roles={[distributore]} component={GestioneScorte} exact />\n              {/* <PrivateRoute path={distributoreGestioneLavorazioni} roles={[distributore]} component={GestioneTask} exact /> */}\n              <PrivateRoute path={distributoreGestionePVendita} roles={[distributore]} component={Gestione_Punti_Vendita} exact />\n              <PrivateRoute path={distributoreGestioneProdotti} roles={[distributore]} component={Gestione_Prodotti} exact />\n              <PrivateRoute path={distributoreGestioneListini} roles={[distributore]} component={Gestione_Listini} exact />\n              <PrivateRoute path={distributoreListiniAssociati} roles={[distributore]} component={VisualizzaListiniAssociati} exact />\n              <PrivateRoute path={distributoreAggiungiListini} roles={[distributore]} component={AggiungiListini} exact />\n              <PrivateRoute path={distributoreModificaProdottiListino} roles={[distributore]} component={ModificaProdottiListino} exact />\n              <PrivateRoute path={distributoreModificaProdottiOrdine} roles={[distributore]} component={ModificaProdottiOrdine} exact />\n              <PrivateRoute path={distributoreDocumentiOrdineVendita} roles={[distributore]} component={UfficioVendite} exact />\n              <PrivateRoute path={distributoreDocumentiOrdineAcquisto} roles={[distributore]} component={UfficioAcquisti} exact />\n              <PrivateRoute path={distributoreGestioneLogisticaOrdini} roles={[distributore]} component={GestioneLogisticaOdini} exact />\n              <PrivateRoute path={distributoreMerceInevasa} roles={[distributore]} component={MerceInevasa} exact />\n              <PrivateRoute path={distributoreAggiungiDocInevasi} roles={[distributore]} component={AggiungiDocInevasi} exact />\n              <PrivateRoute path={distributoreGestioneConsegne} roles={[distributore]} component={AssegnaConsegne} exact />\n              <PrivateRoute path={distributorePrevisioneAcquisti} roles={[distributore]} component={BuyerPrediction} exact />\n              <PrivateRoute path={distributoreStoricoFornitori} roles={[distributore]} component={StoricoFornitori} exact />\n              <PrivateRoute path={distributoreFornitori} roles={[distributore]} component={GestioneFornitori} exact />\n              <PrivateRoute path={distributoreOrdina} roles={[distributore]} component={marketplaceUffAcq} exact />\n              <PrivateRoute path={distributoreListiniAcquisto} roles={[distributore]} component={ListiniAcquisto} exact />\n              <PrivateRoute path={distributoremagazzinoContoTerzi} roles={[distributore]} component={magazzinoContoTerzi} exact />\n              <PrivateRoute path={distributoreFornitoreAffiliato} roles={[distributore]} component={FornitoreAffiliato} exact />\n              <PrivateRoute exact path={distributoreCarrello} roles={[distributore]} component={RiepilogoOrdineAff} />\n              <PrivateRoute exact path={distributoreGiacenzeProdotti} roles={[distributore]} component={GiacenzeProdotti} />\n              <PrivateRoute exact path={distributoreGestioneRotture} roles={[distributore]} component={DocumentiDiRottura} />\n              <PrivateRoute exact path={distributoreGestioneResi} roles={[distributore]} component={DocumentiDiReso} />\n              {Object.entries(menu.distributore || {}).map((el, index) => {\n                if (el[0] === sistemiEsterni) {\n                  Object.keys(el[1]).forEach((element, key) => {\n                    if (element !== visibility) {\n                      return (\n                        <Link to={distributoreExternalsystems + element} />\n                      )\n                    }\n                    return (<span key={key}></span>)\n                  })\n                }\n                return (<span key={index}></span>)\n              })}\n              <PrivateRoute path=\"/distributore/externalsystems/:element\" roles={[distributore]} component={Alyante} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={affiliatoDashboard} roles={affiliato} component={DashboardAffiliato} exact />\n              <PrivateRoute path={affiliatoVisualizzaListini} roles={affiliato} component={VisualizzaListino} exact />\n              <PrivateRoute path={affiliatoGestioneListiniPDV} roles={affiliato} component={AssociaListiniPV} exact />\n              <PrivateRoute path={affiliatoVisualizzaOrdini} roles={[affiliato]} component={VisualizzaOrdine} exact />\n              <PrivateRoute path={affiliatoGestionePuntiVendita} roles={[affiliato]} component={Gestione_PDV2} exact />\n              <PrivateRoute path={affiliatoCreaOrdine} roles={[affiliato]} component={AggiungiOrdineAffiliato} exact />\n              <PrivateRoute exact path={affiliatoCarrello} roles={[affiliato]} component={RiepilogoOrdineAff} />\n              <PrivateRoute exact path={affiliatoGestioneScorte} roles={[affiliato]} component={GestioneScorteAff} />\n              <PrivateRoute exact path={affiliatoGestioneMovimentazioni} roles={[affiliato]} component={GestioneMovimentazioni} />\n              <PrivateRoute exact path={affiliatoGestioneDocumenti} roles={[affiliato]} component={GestioneDocumentiAFF} />\n              <PrivateRoute exact path={affiliatoLogistica} roles={[affiliato]} component={Approvvigionamento} />\n              <PrivateRoute exact path={affiliatoRiepilogo} roles={[affiliato]} component={RiepilogoApprovvigionamento} />\n              <PrivateRoute path={affiliatoDocumentiOrdineAcquisto} roles={[affiliato]} component={UfficioAcquisti} exact />\n              <PrivateRoute path={affiliatoListiniAcquisto} roles={[affiliato]} component={ListiniAcquisto} exact />\n              <PrivateRoute path={affiliatoFornitori} roles={[affiliato]} component={GestioneFornitori} exact />\n              <PrivateRoute path={affiliatoOrdina} roles={[affiliato]} component={marketplaceUffAcq} exact />\n              <PrivateRoute path={affiliatoMessaggi} roles={[affiliato]} component={Messaggi} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={agenteDashboard} roles={[agente]} component={DashboardAgente} exact />\n              <PrivateRoute path={agenteGestioneOrdiniAgente} roles={[agente]} component={GestioneOdiniAgente} exact />\n              <PrivateRoute path={agenteGestioneClientiAgente} roles={[agente]} component={GestioneClientiAgente} exact />\n              <PrivateRoute path={agenteDettagliOrdine} roles={[agente]} component={dettagliOrdineAgente} exact />\n              <PrivateRoute path={agenteCreaOrdine} roles={[agente]} component={AggiungiOrdineAgente} exact />\n              <PrivateRoute path={agenteOrdineDiretto} roles={[agente]} component={OrdineDiretto} exact />\n              <PrivateRoute path={agenteCompletaOrdine} roles={[agente]} component={CompletaOrdine} exact />\n              <PrivateRoute path={agenteVisualizzaListini} roles={[agente]} component={VisualizzaListino} exact />\n              <PrivateRoute path={agenteDettaglioCliente} roles={[agente]} component={DettagliClienti} exact />\n              <PrivateRoute path={agenteStoricoDocumenti} roles={[agente]} component={GestioneDocumentiAFF} exact />\n              <PrivateRoute path={agenteMerceInevasa} roles={[agente]} component={MerceInevasa} exact />\n              <PrivateRoute path={agenteMessaggi} roles={[agente]} component={Messaggi} exact />\n              <PrivateRoute path={agenteGestionePDVAutonoma} roles={[agente]} component={GestionePDVAutonoma} exact />\n              {Object.entries(menu.agente || {}).map((el, index) => {\n                if (el[0] === sistemiEsterni) {\n                  Object.keys(el[1]).forEach((element, key) => {\n                    if (element !== visibility) {\n                      return (\n                        <Link to={agenteExternalsystems + element} />\n                      )\n                    }\n                    return (<span key={key}></span>)\n                  })\n                }\n                return (<span key={index}></span>)\n              })}\n              <PrivateRoute path=\"/agente/externalsystems/:element\" roles={[agente]} component={Alyante} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={logisticaGestioneLogistica} roles={[logistica]} component={GestioneLogistica} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={autistaGestioneConsegne} roles={[autista]} component={GestioneConsegne} exact />\n              <PrivateRoute path={autistaDatiConsegnaAutista} roles={[autista]} component={DatiConsegnaAutista} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={pdvDashboard} roles={[pdv]} component={DashboardPDV} exact />\n              <PrivateRoute exact path={pdvListinoProdotti} restricted={true} roles={[pdv]} component={Marketplace} />\n              <PrivateRoute exact path={pdvGestioneLogisticaOrdini} restricted={true} roles={[pdv]} component={GestioneOrdiniPDV} />\n              <PrivateRoute exact path={pdvCarrello} restricted={true} roles={[pdv]} component={Cart} />\n              <PrivateRoute path={pdvMessaggi} roles={[pdv]} component={Messaggi} exact />\n              <PrivateRoute path={pdvStoricoDocumenti} roles={[pdv]} component={GestioneDocumentiPDV} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={respMag} roles={[resp_mag]} component={DashboardRespMagazzino} exact />\n              <PrivateRoute path={resp_magInventario} roles={[resp_mag]} component={Inventario} exact />\n              <PrivateRoute path={resp_magMovimentazioneInIngresso} roles={[resp_mag]} component={MovimentazioneInIngresso} exact />\n              <PrivateRoute path={resp_magMovimentazioneInUscita} roles={[resp_mag]} component={MovimentazioneInUscita} exact />\n              <PrivateRoute path={resp_magGestioneLavorazioni} roles={[resp_mag]} component={GestioneLavorazioni} exact />\n              <PrivateRoute path={resp_magOperatori} roles={[resp_mag]} component={GestioneOperatori} exact />\n              <PrivateRoute path={resp_magComposizioneMagazzino} roles={[resp_mag]} component={ComposizioneMagazzino} exact />\n              <PrivateRoute path={resp_magGestioneProdottiPosizionati} roles={[resp_mag]} component={GestioneProdPos} exact />\n              <PrivateRoute path={resp_magControlloLottiEScadenze} roles={[resp_mag]} component={ControlloLottiEScadenze} exact />\n              <PrivateRoute path={resp_magGestioneProdotti} roles={[resp_mag]} component={GestioneProdotti} exact />\n              <PrivateRoute path={resp_magConfrontoDispQta} roles={[resp_mag]} component={ConfrontoDispQta} exact />\n              <PrivateRoute exact path={resp_magGestioneRotture} roles={[resp_mag]} component={DocumentiDiRottura} />\n              <PrivateRoute exact path={resp_magGestioneResi} roles={[resp_mag]} component={DocumentiDiReso} />\n              {Object.entries(menu.resp_mag || {}).map((el, index) => {\n                if (el[0] === sistemiEsterni) {\n                  Object.keys(el[1]).forEach((element, key) => {\n                    if (element !== visibility) {\n                      return (\n                        <Link to={resp_magExternalsystems + element} />\n                      )\n                    }\n                    return (<span key={key}></span>)\n                  })\n                }\n                return (<span key={index}></span>)\n              })}\n              <PrivateRoute path=\"/resp_mag/externalsystems/:element\" roles={[resp_mag]} component={Alyante} exact />\n              {/* <PrivateRoute path=\"/resp_mag/alyante\" roles={[resp_mag]} component={Alyante} exact />\n              <PrivateRoute path=\"/resp_mag/mrwine\" roles={[resp_mag]} component={MrWine} exact />\n              <PrivateRoute path=\"/resp_mag/alcolicartigianale\" roles={[resp_mag]} component={AlcolicArtigianale} exact />\n              <PrivateRoute path=\"/resp_mag/mail\" roles={[resp_mag]} component={Scraper} exact /> */}\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={chainDashboard} roles={[chain]} component={DashboardChain} exact />\n              <PrivateRoute path={chainGenerali} roles={[chain]} component={AnagraficheChain} exact />\n              <PrivateRoute path={chainGestionePVendita} roles={[chain]} component={GestionePDV} exact />\n              <PrivateRoute path={chainmagazzinoContoTerzi} roles={[chain]} component={magazzinoContoTerzi} exact />\n              <PrivateRoute path={chainGestioneMagazzini} roles={[chain]} component={GestioneMagazziniChain} exact />\n              <PrivateRoute path={chainGestioneScorte} roles={[chain]} component={GestioneScorteChain} exact />\n              <PrivateRoute path={chainGestioneProdotti} roles={[chain]} component={GestioneProd} exact />\n              <PrivateRoute path={chainDocumentiOrdineVendita} roles={[chain]} component={DocumentiVendite} exact />\n              <PrivateRoute path={chainDocumentiOrdineAcquisto} roles={[chain]} component={DocumentiAcquisti} exact />\n              <PrivateRoute path={chainGestioneConsegne} roles={[chain]} component={AssegnaConsegneChain} exact />\n              <PrivateRoute path={chainFornitori} roles={[chain]} component={GestioneFornitoriChain} exact />\n              <PrivateRoute path={chainOrdina} roles={[chain]} component={marketplaceUffAcqChain} exact />\n              <PrivateRoute path={chainListiniAcquisto} roles={[chain]} component={ListiniAcquistoChain} exact />\n              <PrivateRoute exact path={chainCarrello} roles={[chain]} component={RiepilogoOrdineAff} />\n              <PrivateRoute path={chainControlloLottiEScadenze} roles={[chain]} component={ControlloLottiEScadenzeChain} exact />\n              <PrivateRoute path={chainConfrontoDispQta} roles={[chain]} component={ConfrontoDispQtaChain} exact />\n              <PrivateRoute path={chainGestioneAutisti} roles={[chain]} component={GestioneAutistiChain} exact />\n              <PrivateRoute path={chainGestioneLogistica} roles={[chain]} component={GestioneLogisticaUtentiChain} exact />\n              <PrivateRoute path={chainGestioneMagazzinieri} roles={[chain]} component={GestioneMagazzinieriChain} exact />\n              <PrivateRoute path={chainComposizioneMagazzino} roles={[chain]} component={ComposizioneMagazzinoChain} exact />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n        <Switch>\n          <ErrorBoundary FallbackComponent={ErrorFallback}>\n            <Provider store={store}>\n              <PrivateRoute path={ringDashboard} roles={[ring]} component={DashboardRING} exact />\n              <PrivateRoute exact path={ringListinoProdotti} restricted={true} roles={[ring]} component={MarketplaceRing} />\n              <PrivateRoute path={ringCaricoMagazzino} roles={[ring]} component={CaricoMagRing} exact />\n              <PrivateRoute path={ringScaricoMagazzino} roles={[ring]} component={ScaricoMagRing} exact />\n              <PrivateRoute exact path={ringCarrello} restricted={true} roles={[ring]} component={Cart} />\n              <PrivateRoute path={ringGestioneScorte} roles={[ring]} component={GestioneScorteRing} exact />\n              <PrivateRoute path={ringControlloLottiEScadenze} roles={[ring]} component={ControlloLottiEScadenzeRing} exact />\n              <PrivateRoute path={ringConfrontoDispQta} roles={[ring]} component={ConfrontoDispQtaRing} exact />\n              <PrivateRoute path={ringOrdinato} roles={[ring]} component={OrdinatoRing} exact />\n              <PrivateRoute path={ringComposizioneMagazzino} roles={[ring]} component={ComposizioneMagazzinoRing} exact />\n              <PrivateRoute exact path={scaricoMagazzino} roles={[ring]} component={ScaricoMagazzinoRing} />\n            </Provider>\n          </ErrorBoundary>\n        </Switch>\n\n\n        {/* <IconAssistance /> */}\n        <GoToTheTop />\n        {/* <Ricerca /> */}\n        <BackendTestButton />\n        <Footer />\n      </BrowserRouter >\n    );\n  }\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,EAAEC,IAAI,EAAEC,MAAM,QAAQ,kBAAkB;AAC9D,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,UAAU,QAAQ,yDAAyD;AACpF;AACA,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAElD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAOC,kBAAkB,MAAM,oCAAoC;AAEnE,OAAOC,qBAAqB,MAAM,uDAAuD;AACzF,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,uBAAuB,MAAM,2CAA2C;AAC/E,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,gBAAgB,MAAM,uCAAuC;AACpE,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,0BAA0B,MAAM,kDAAkD;AACzF,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,0BAA0B,MAAM,kDAAkD;AACzF,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,YAAY,MAAM,0CAA0C;AACnE,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,OAAO,MAAM,+BAA+B;AACnD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,0CAA0C;AACtE,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,gBAAgB,MAAM,wCAAwC;AAErE,OAAOC,2BAA2B,MAAM,gDAAgD;AACxF,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,kBAAkB,MAAM,iDAAiD;AAChF,OAAOC,iBAAiB,MAAM,sCAAsC;AACpE,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,uBAAuB,MAAM,+BAA+B;AACnE,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,uBAAuB,MAAM,iDAAiD;AAErF,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,IAAI,MAAM,0CAA0C;AAC3D,OAAOC,KAAK,MAAM,iCAAiC;AACnD,OAAOC,WAAW,MAAM,iCAAiC;AAEzD,OAAOC,cAAc,MAAM,wCAAwC;AAEnE,OAAOC,iBAAiB,MAAM,sCAAsC;AAEpE,OAAOC,eAAe,MAAM,iDAAiD;AAC7E,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,mBAAmB,MAAM,yCAAyC;AAEzE,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,mBAAmB,MAAM,oCAAoC;AAEpE,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,wBAAwB,MAAM,iDAAiD;AACtF,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,uBAAuB,MAAM,gDAAgD;AACpF,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,qBAAqB,MAAM,8CAA8C;AAChF,OAAOC,UAAU,MAAM,mCAAmC;AAC1D,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,gBAAgB,MAAM,yCAAyC;AAEtE,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,mBAAmB,MAAM,+BAA+B;AAC/D,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,4BAA4B,MAAM,wCAAwC;AACjF,OAAOC,qBAAqB,MAAM,iCAAiC;AACnE,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,4BAA4B,MAAM,oCAAoC;AAC7E,OAAOC,yBAAyB,MAAM,qCAAqC;AAC3E,OAAOC,0BAA0B,MAAM,sCAAsC;AAE7E,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,OAAOC,2BAA2B,MAAM,uCAAuC;AAC/E,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,yBAAyB,MAAM,qCAAqC;AAC3E,OAAOC,oBAAoB,MAAM,gCAAgC;AAEjE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,yCAAyC;AAC1D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,oBAAoB;AAC3B,OAAO,WAAW;AAClB,SACEC,KAAK,EACLC,aAAa,EACbC,cAAc,EACdC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,cAAc,EACdC,aAAa,EACbC,qBAAqB,EACrBC,sBAAsB,EACtBC,qBAAqB,EACrBC,mBAAmB,EACnBC,oBAAoB,EACpBC,WAAW,EACXC,qBAAqB,EACrBC,wBAAwB,EACxBC,KAAK,EACLC,mBAAmB,EACnBC,SAAS,EACTC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,kBAAkB,EAClBC,gCAAgC,EAChCC,kBAAkB,EAClBC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,uBAAuB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,kBAAkB,EACtFC,0BAA0B,EAC1BC,yBAAyB,EACzBC,MAAM,EACNC,oBAAoB,EACpBC,gBAAgB,EAChBC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,yBAAyB,EACzBC,kBAAkB,EAClBC,mBAAmB,EACnBC,sBAAsB,EACtBC,uBAAuB,EACvBC,OAAO,EACPC,0BAA0B,EAC1BC,uBAAuB,EACvBC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,oBAAoB,EACpBC,qBAAqB,EACrBC,mCAAmC,EACnCC,kCAAkC,EAClCC,2BAA2B,EAC3BC,8BAA8B,EAC9BC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,gCAAgC,EAChCC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,+BAA+B,EAC/BC,wBAAwB,EACxBC,mCAAmC,EACnCC,kCAAkC,EAClCC,kBAAkB,EAClBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,0BAA0B,EAC1BC,aAAa,EACbC,GAAG,EACHC,WAAW,EACXC,YAAY,EACZC,0BAA0B,EAC1BC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,6BAA6B,EAC7BC,wBAAwB,EACxBC,+BAA+B,EAC/BC,uBAAuB,EACvBC,2BAA2B,EAC3BC,wBAAwB,EACxBC,mCAAmC,EACnCC,kBAAkB,EAClBC,gCAAgC,EAChCC,8BAA8B,EAC9BC,iBAAiB,EACjBC,QAAQ,EACRC,cAAc,EACdC,UAAU,EACVC,4BAA4B,EAC5BC,aAAa,EACbC,mBAAmB,EACnBC,oBAAoB,EACpBC,IAAI,EACJC,mBAAmB,EACnBC,YAAY,EACZC,kBAAkB,EAClBC,2BAA2B,EAC3BC,oBAAoB,EACpBC,YAAY,EACZC,4BAA4B,EAC5BC,qBAAqB,EACrBC,oBAAoB,EACpBC,sBAAsB,EACtBC,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,2BAA2B,EAC3BC,wBAAwB,EACxBC,uBAAuB,EACvBC,oBAAoB,EACpBC,sBAAsB,EACtBC,wBAAwB,EACxBC,+BAA+B,EAC/BC,cAAc,EACdC,WAAW,EACXC,iBAAiB,QACZ,oBAAoB;AAC3B,OAAOC,kBAAkB,MAAM,kDAAkD;AACjF,OAAOC,eAAe,MAAM,+CAA+C;AAC3E,OAAOC,iBAAiB,MAAM,4CAA4C;AAC1E,OAAOC,mBAAmB,MAAM,6CAA6C;AAC7E,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,aAAaA,CAAAC,IAAA,EAAY;EAAA,IAAX;IAAEpE;EAAM,CAAC,GAAAoE,IAAA;EAC9B,oBACEF,OAAA;IAAKG,IAAI,EAAC,OAAO;IAAAC,QAAA,gBACfJ,OAAA;MAAAI,QAAA,EAAG;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAChCR,OAAA;MAAKS,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAN,QAAA,EAAEtE,KAAK,CAAC6E;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC;AAEV;AAACI,EAAA,GAPQX,aAAa;AAStB,MAAMY,GAAG,SAAS/R,SAAS,CAAC;EAE1B,MAAMgS,iBAAiBA,CAAA,EAAG;IACxB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKjF,KAAK,IAAI+E,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKlF,OAAO,IAAIgF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKvE,gBAAgB,IAAIqE,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKtE,UAAU,EAAE;MAC1K,IAAIuE,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;MACvF,IAAIF,IAAI,IAAIA,IAAI,CAACf,IAAI,KAAKtG,YAAY,EAAE;QACtC,IAAI;UACF,MAAMzK,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC,CACxCmS,IAAI,CAACC,GAAG,IAAI;YACX,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE;cACnB,IAAItB,IAAI,GAAGgB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAACM,WAAW,CAAC,CAAC;cACrDC,MAAM,CAACC,OAAO,CAACtL,IAAI,CAAC6J,IAAI,CAAC,CAAC,CAAC0B,OAAO,CAACC,OAAO,IAAI;gBAC5C,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAKpE,cAAc,EAAE;kBACjCoE,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;kBACfA,OAAO,CAAC,CAAC,CAAC,CAACnE,UAAU,GAAG,MAAM;kBAC9B6D,GAAG,CAACC,IAAI,CAACI,OAAO,CAACE,EAAE,IAAI;oBACrB,IAAIA,EAAE,IAAIA,EAAE,CAACC,kBAAkB,EAAE;sBAC/BF,OAAO,CAAC,CAAC,CAAC,CAACC,EAAE,CAACC,kBAAkB,CAACN,WAAW,CAAC,CAAC,CAAC,GAAG;wBAChD,OAAO,EAAE,gBAAgB;wBACzB,YAAY,EAAE;sBAChB,CAAC;oBACH;kBACF,CAAC,CAAC;kBACFpL,IAAI,CAAC6J,IAAI,CAAC,CAAC2B,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;gBACrC;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACN,CAAC,CAAC,OAAOG,CAAC,EAAE;UACVC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,CAAC,CAACtB,OAAO,CAAC;UACnE;QACF;MACF;IACF;EACF;EAEAyB,MAAMA,CAAA,EAAG;IACP,oBACEpC,OAAA,CAACjR,aAAa;MAAAqR,QAAA,gBACZJ,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC9Q,QAAQ;UAAC4D,KAAK,EAAEA,KAAM;UAAAsN,QAAA,gBACrBJ,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,KAAM;YAACC,IAAI,EAAE3I,QAAS;YAAC4I,KAAK;YAACC,SAAS,EAAEjT;UAAK;YAAA8Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzER,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,IAAK;YAACC,IAAI,EAAEtG,KAAM;YAACuG,KAAK;YAACC,SAAS,EAAEhT;UAAM;YAAA6Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtER,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,IAAK;YAACC,IAAI,EAAE3F,UAAW;YAAC4F,KAAK;YAACC,SAAS,EAAE/S;UAAW;YAAA4Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChFR,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,IAAK;YAACC,IAAI,EAAE5F,gBAAiB;YAAC6F,KAAK;YAACC,SAAS,EAAE9S;UAAU;YAAA2Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFR,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,IAAK;YAACC,IAAI,EAAEnG,aAAc;YAACoG,KAAK;YAACC,SAAS,EAAEnM;UAAO;YAAAgK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ER,OAAA,CAACpQ,WAAW;YAACyS,UAAU,EAAE,KAAM;YAACC,IAAI,EAAEvG,OAAQ;YAACwG,KAAK;YAACC,SAAS,EAAEhM;UAAc;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjFR,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAExG,KAAM;YAACuG,UAAU,EAAE,IAAK;YAACG,SAAS,EAAEpM;UAAQ;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzER,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAE7E,QAAS;YAAC4E,UAAU,EAAE,IAAK;YAACG,SAAS,EAAEjM;UAAgB;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC9Q,QAAQ;UAAC4D,KAAK,EAAEA,KAAM;UAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAE1I,SAAU;YAACyI,UAAU,EAAE,IAAK;YAACI,KAAK,EAAE,CAACjL,KAAK,CAAE;YAACgL,SAAS,EAAE/P;UAAwB;YAAA4N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAE7K,mBAAoB;YAAC4K,UAAU,EAAE,IAAK;YAACI,KAAK,EAAE,CAACjL,KAAK,CAAE;YAACgL,SAAS,EAAExP;UAAe;YAAAqN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9GR,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAEnD,sBAAuB;YAACkD,UAAU,EAAE,IAAK;YAACI,KAAK,EAAE,CAACjL,KAAK,CAAE;YAACgL,SAAS,EAAE7C;UAAkB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;YAAC4S,KAAK;YAACD,IAAI,EAAElD,wBAAyB;YAACiD,UAAU,EAAE,IAAK;YAACI,KAAK,EAAE,CAACjL,KAAK,CAAE;YAACgL,SAAS,EAAE5C;UAAoB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAErI,qBAAsB;cAACwI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEtS,qBAAsB;cAACqS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/H,oBAAqB;cAACkI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE9R,mBAAoB;cAAC6R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9H,6BAA8B;cAACiI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEpS,kBAAmB;cAACmS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAElH,0BAA2B;cAACqH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE1R,0BAA2B;cAACyR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7H,0BAA2B;cAACgI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEpR,cAAe;cAACmR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE5H,2BAA4B;cAAC+H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEnR,eAAgB;cAACkR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzH,6BAA8B;cAAC4H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEnS,uBAAwB;cAACkS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEtH,gCAAiC;cAACyH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE3R,oBAAqB;cAAC0R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvH,6BAA8B;cAAC0H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEzR,iBAAkB;cAACwR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnH,0BAA2B;cAACsH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAExR,cAAe;cAACuR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpH,4BAA6B;cAACuH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAElS,sBAAuB;cAACiS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAErH,4BAA6B;cAACwH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAErS,iBAAkB;cAACoS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1H,2BAA4B;cAAC6H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEjS,gBAAiB;cAACgS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhH,4BAA6B;cAACmH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE5R,0BAA2B;cAAC2R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvI,2BAA4B;cAAC0I,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE3S,eAAgB;cAAC0S,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7G,mCAAoC;cAACgH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE1S,uBAAwB;cAACyS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5HR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE5G,kCAAmC;cAAC+G,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAExS,sBAAuB;cAACuS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1HR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnI,kCAAmC;cAACsI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEhS,cAAe;cAAC+R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpI,mCAAoC;cAACuI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE/R,eAAgB;cAAC8R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExH,mCAAoC;cAAC2H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE7R,sBAAuB;cAAC4R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3HR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9G,wBAAyB;cAACiH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEvR,YAAa;cAACsR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExI,8BAA+B;cAAC2I,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEvS,kBAAmB;cAACsS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE3H,4BAA6B;cAAC8H,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEtR,eAAgB;cAACqR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1G,8BAA+B;cAAC6G,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAElR,eAAgB;cAACiR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzG,4BAA6B;cAAC4G,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEjR,gBAAiB;cAACgR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhI,qBAAsB;cAACmI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEhR,iBAAkB;cAAC+Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE3G,kBAAmB;cAAC8G,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE/Q,iBAAkB;cAAC8Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjH,2BAA4B;cAACoH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE9Q,eAAgB;cAAC6Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/G,+BAAgC;cAACkH,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE7Q,mBAAoB;cAAC4Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjI,8BAA+B;cAACoI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE5Q,kBAAmB;cAAC2Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClHR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEtI,oBAAqB;cAACyI,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAEnQ;YAAmB;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE1E,4BAA6B;cAAC6E,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE3Q;YAAiB;cAAAwO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9GR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEvD,2BAA4B;cAAC0D,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE/C;YAAmB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/GR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEtD,wBAAyB;cAACyD,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAE9C;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxGmB,MAAM,CAACC,OAAO,CAACtL,IAAI,CAACuD,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC8I,GAAG,CAAC,CAACZ,EAAE,EAAEa,KAAK,KAAK;cAC1D,IAAIb,EAAE,CAAC,CAAC,CAAC,KAAKrE,cAAc,EAAE;gBAC5BiE,MAAM,CAACkB,IAAI,CAACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,CAACC,OAAO,EAAEgB,GAAG,KAAK;kBAC3C,IAAIhB,OAAO,KAAKnE,UAAU,EAAE;oBAC1B,oBACEqC,OAAA,CAAChR,IAAI;sBAAC+T,EAAE,EAAE3I,2BAA2B,GAAG0H;oBAAQ;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAEvD;kBACA,oBAAQR,OAAA,aAAW8C,GAAG;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBACjC,CAAC,CAAC;cACJ;cACA,oBAAQR,OAAA,aAAW4C,KAAK;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YACnC,CAAC,CAAC,eACFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAC,wCAAwC;cAACG,KAAK,EAAE,CAAC5I,YAAY,CAAE;cAAC2I,SAAS,EAAErR,OAAQ;cAACoR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzK,kBAAmB;cAAC4K,KAAK,EAAE/K,SAAU;cAAC8K,SAAS,EAAEvQ,kBAAmB;cAACsQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9J,0BAA2B;cAACiK,KAAK,EAAE/K,SAAU;cAAC8K,SAAS,EAAEtQ,iBAAkB;cAACqQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpK,2BAA4B;cAACuK,KAAK,EAAE/K,SAAU;cAAC8K,SAAS,EAAEzS,gBAAiB;cAACwS,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7J,yBAA0B;cAACgK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAErQ,gBAAiB;cAACoQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnK,6BAA8B;cAACsK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAEhQ,aAAc;cAAC+P,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1K,mBAAoB;cAAC6K,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAEpQ,uBAAwB;cAACmQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE3K,iBAAkB;cAAC8K,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAEnQ;YAAmB;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAElK,uBAAwB;cAACqK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAEzQ;YAAkB;cAAAsO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEjD,+BAAgC;cAACoD,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE3C;YAAuB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAErK,0BAA2B;cAACwK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAExQ;YAAqB;cAAAqO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAExK,kBAAmB;cAAC2K,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAElQ;YAAmB;cAAA+N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE/J,kBAAmB;cAACkK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE1Q;YAA4B;cAAAuO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvK,gCAAiC;cAAC0K,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE/R,eAAgB;cAAC8R,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjK,wBAAyB;cAACoK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE9Q,eAAgB;cAAC6Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEtK,kBAAmB;cAACyK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAEhR,iBAAkB;cAAC+Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhK,eAAgB;cAACmK,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE/Q,iBAAkB;cAAC8Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9C,iBAAkB;cAACiD,KAAK,EAAE,CAAC/K,SAAS,CAAE;cAAC8K,SAAS,EAAE1C,QAAS;cAACyC,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzJ,eAAgB;cAAC4J,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEtP,eAAgB;cAACqP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpJ,0BAA2B;cAACuJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEjQ,mBAAoB;cAACgQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAErJ,2BAA4B;cAACwJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAErP,qBAAsB;cAACoP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvJ,oBAAqB;cAAC0J,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEnP,oBAAqB;cAACkP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1J,gBAAiB;cAAC6J,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEpP,oBAAqB;cAACmP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjJ,mBAAoB;cAACoJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEjP,aAAc;cAACgP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE3J,oBAAqB;cAAC8J,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEhP,cAAe;cAAC+O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/I,uBAAwB;cAACkJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEtQ,iBAAkB;cAACqQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExJ,sBAAuB;cAAC2J,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAElP,eAAgB;cAACiP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhJ,sBAAuB;cAACmJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAExQ,oBAAqB;cAACuQ,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAElJ,kBAAmB;cAACqJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAEvR,YAAa;cAACsR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhD,cAAe;cAACmD,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAE1C,QAAS;cAACyC,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnJ,yBAA0B;cAACsJ,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAE/O,mBAAoB;cAAC8O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACvGmB,MAAM,CAACC,OAAO,CAACtL,IAAI,CAACoC,MAAM,IAAI,CAAC,CAAC,CAAC,CAACiK,GAAG,CAAC,CAACZ,EAAE,EAAEa,KAAK,KAAK;cACpD,IAAIb,EAAE,CAAC,CAAC,CAAC,KAAKrE,cAAc,EAAE;gBAC5BiE,MAAM,CAACkB,IAAI,CAACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,CAACC,OAAO,EAAEgB,GAAG,KAAK;kBAC3C,IAAIhB,OAAO,KAAKnE,UAAU,EAAE;oBAC1B,oBACEqC,OAAA,CAAChR,IAAI;sBAAC+T,EAAE,EAAE/J,qBAAqB,GAAG8I;oBAAQ;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAEjD;kBACA,oBAAQR,OAAA,aAAW8C,GAAG;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBACjC,CAAC,CAAC;cACJ;cACA,oBAAQR,OAAA,aAAW4C,KAAK;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YACnC,CAAC,CAAC,eACFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAC,kCAAkC;cAACG,KAAK,EAAE,CAAC/J,MAAM,CAAE;cAAC8J,SAAS,EAAErR,OAAQ;cAACoR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,eACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpG,0BAA2B;cAACuG,KAAK,EAAE,CAACxG,SAAS,CAAE;cAACuG,SAAS,EAAEvP,iBAAkB;cAACsP,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE5I,uBAAwB;cAAC+I,KAAK,EAAE,CAACjJ,OAAO,CAAE;cAACgJ,SAAS,EAAE9O,gBAAiB;cAAC6O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7I,0BAA2B;cAACgJ,KAAK,EAAE,CAACjJ,OAAO,CAAE;cAACgJ,SAAS,EAAE7O,mBAAoB;cAAC4O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhG,YAAa;cAACmG,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAE9P,YAAa;cAAC6P,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE9F,kBAAmB;cAAC6F,UAAU,EAAE,IAAK;cAACI,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAEzP;YAAY;cAAAsN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE/F,0BAA2B;cAAC8F,UAAU,EAAE,IAAK;cAACI,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAE7P;YAAkB;cAAA0N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtHR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEjG,WAAY;cAACgG,UAAU,EAAE,IAAK;cAACI,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAE3P;YAAK;cAAAwN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/C,WAAY;cAACkD,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAE1C,QAAS;cAACyC,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5ER,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7F,mBAAoB;cAACgG,KAAK,EAAE,CAACrG,GAAG,CAAE;cAACoG,SAAS,EAAE5P,oBAAqB;cAAC2P,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1F,OAAQ;cAAC6F,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAE5O,sBAAuB;cAAC2O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjF,kBAAmB;cAACoF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAEpO,UAAW;cAACmO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhF,gCAAiC;cAACmF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAE3O,wBAAyB;cAAC0O,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/E,8BAA+B;cAACkF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAE1O,sBAAuB;cAACyO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpF,2BAA4B;cAACuF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAEzO,mBAAoB;cAACwO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9E,iBAAkB;cAACiF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAExO,iBAAkB;cAACuO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExF,6BAA8B;cAAC2F,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAErO,qBAAsB;cAACoO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAElF,mCAAoC;cAACqF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAEnO,eAAgB;cAACkO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEtF,+BAAgC;cAACyF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAEvO,uBAAwB;cAACsO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnF,wBAAyB;cAACsF,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAElO,gBAAiB;cAACiO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvF,wBAAyB;cAAC0F,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAEtO,gBAAiB;cAACqO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAErD,uBAAwB;cAACwD,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAE/C;YAAmB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEpD,oBAAqB;cAACuD,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAE9C;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChGmB,MAAM,CAACC,OAAO,CAACtL,IAAI,CAACuG,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC8F,GAAG,CAAC,CAACZ,EAAE,EAAEa,KAAK,KAAK;cACtD,IAAIb,EAAE,CAAC,CAAC,CAAC,KAAKrE,cAAc,EAAE;gBAC5BiE,MAAM,CAACkB,IAAI,CAACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,CAACC,OAAO,EAAEgB,GAAG,KAAK;kBAC3C,IAAIhB,OAAO,KAAKnE,UAAU,EAAE;oBAC1B,oBACEqC,OAAA,CAAChR,IAAI;sBAAC+T,EAAE,EAAE9F,uBAAuB,GAAG6E;oBAAQ;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAEnD;kBACA,oBAAQR,OAAA,aAAW8C,GAAG;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBACjC,CAAC,CAAC;cACJ;cACA,oBAAQR,OAAA,aAAW4C,KAAK;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YACnC,CAAC,CAAC,eACFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAC,oCAAoC;cAACG,KAAK,EAAE,CAAC5F,QAAQ,CAAE;cAAC2F,SAAS,EAAErR,OAAQ;cAACoR,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAK/F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE3L,cAAe;cAAC8L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEjO,cAAe;cAACgO,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvL,aAAc;cAAC0L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE5N,gBAAiB;cAAC2N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhL,qBAAsB;cAACmL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE/N,WAAY;cAAC8N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/K,wBAAyB;cAACkL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE7Q,mBAAoB;cAAC4Q,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAErL,sBAAuB;cAACwL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE3N,sBAAuB;cAAC0N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnL,mBAAoB;cAACsL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE1N,mBAAoB;cAACyN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEpL,qBAAsB;cAACuL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEhO,YAAa;cAAC+N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzL,2BAA4B;cAAC4L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE9N,gBAAiB;cAAC6N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1L,4BAA6B;cAAC6L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAE7N,iBAAkB;cAAC4N,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEtL,qBAAsB;cAACyL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEzN,oBAAqB;cAACwN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExL,cAAe;cAAC2L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEtN,sBAAuB;cAACqN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjL,WAAY;cAACoL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAExN,sBAAuB;cAACuN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAElL,oBAAqB;cAACqL,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEvN,oBAAqB;cAACsN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnGR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAE5L,aAAc;cAAC+L,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEnQ;YAAmB;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE/D,4BAA6B;cAACkE,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAErN,4BAA6B;cAACoN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE9D,qBAAsB;cAACiE,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEpN,qBAAsB;cAACmN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE7D,oBAAqB;cAACgE,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEnN,oBAAqB;cAACkN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE5D,sBAAuB;cAAC+D,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAElN,4BAA6B;cAACiN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE3D,yBAA0B;cAAC8D,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEjN,yBAA0B;cAACgN,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzD,0BAA2B;cAAC4D,KAAK,EAAE,CAAChM,KAAK,CAAE;cAAC+L,SAAS,EAAEhN,0BAA2B;cAAC+M,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETR,OAAA,CAAC/Q,MAAM;QAAAmR,QAAA,eACLJ,OAAA,CAAC7Q,aAAa;UAACuT,iBAAiB,EAAEzC,aAAc;UAAAG,QAAA,eAC9CJ,OAAA,CAAC9Q,QAAQ;YAAC4D,KAAK,EAAEA,KAAM;YAAAsN,QAAA,gBACrBJ,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEzE,aAAc;cAAC4E,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE/M,aAAc;cAAC8M,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAErE,mBAAoB;cAACoE,UAAU,EAAE,IAAK;cAACI,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE9M;YAAgB;cAAA2K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9GR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAExE,mBAAoB;cAAC2E,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE7M,aAAc;cAAC4M,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEvE,oBAAqB;cAAC0E,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE5M,cAAe;cAAC2M,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAEpE,YAAa;cAACmE,UAAU,EAAE,IAAK;cAACI,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE3P;YAAK;cAAAwN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEnE,kBAAmB;cAACsE,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE3M,kBAAmB;cAAC0M,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9FR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAElE,2BAA4B;cAACqE,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAE1M,2BAA4B;cAACyM,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChHR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEjE,oBAAqB;cAACoE,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAEzM,oBAAqB;cAACwM,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClGR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAEhE,YAAa;cAACmE,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAExM,YAAa;cAACuM,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFR,OAAA,CAACrQ,YAAY;cAAC2S,IAAI,EAAE1D,yBAA0B;cAAC6D,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAEvM,yBAA0B;cAACsM,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GR,OAAA,CAACrQ,YAAY;cAAC4S,KAAK;cAACD,IAAI,EAAExD,gBAAiB;cAAC2D,KAAK,EAAE,CAACzE,IAAI,CAAE;cAACwE,SAAS,EAAEtM;YAAqB;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAITR,OAAA,CAAC3Q,UAAU;QAAAgR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEdR,OAAA,CAAC1Q,iBAAiB;QAAA+Q,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBR,OAAA,CAAC7J,MAAM;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAErB;AACF;AAEA,eAAeK,GAAG;AAAC,IAAAD,EAAA;AAAAoC,YAAA,CAAApC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}