{"version": 3, "file": "run.mjs", "sourceRoot": "", "sources": ["../../src/run.mts"], "names": [], "mappings": ";AACA;;GAEG;AACH,YAAY;AACZ,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAA;AAExC,sEAAsE;AACtE,mEAAmE;AACnE,mEAAmE;AACnE,0DAA0D;AAC1D,6CAA6C;AAE7C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AAC9C,uBAAuB;AACvB,MAAM,MAAM,CACV,MAAM,CACJ,MAAM,aAAa,CACjB,YAAY,EACZ,MAAM,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAC3D,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CACtB,CACF,CACF,CACF,CAAA", "sourcesContent": ["#!/usr/bin/env node\n/**\n * just load the `@tapjs/run` module, which does all the work\n */\n//@ts-ignore\nprocess.setSourceMapsEnabled(true)\nimport { resolve } from 'node:path'\nimport { pathToFileURL } from 'node:url'\n\n// Try to load the tap runner that's installed in the current project,\n// if present. Running plugin builds etc from the global space will\n// never ever work as expected, but people do global installs to be\n// able to run tap without `npm exec` or `npm run-script`,\n// so may as well make it do the right thing.\n\nimport { resolveImport } from 'resolve-import'\n// import('@tapjs/run')\nawait import(\n  String(\n    await resolveImport(\n      '@tapjs/run',\n      await resolveImport('tap', pathToFileURL(resolve('x'))).catch(\n        () => import.meta.url,\n      ),\n    ),\n  )\n)\n"]}