{"ast": null, "code": "import React, { isValidElement } from 'react';\nimport 'prop-types';\nimport treeChanges from 'tree-changes';\nimport is from 'is-lite';\nimport ReactDOM, { createPortal } from 'react-dom';\nimport ExecutionEnvironment from 'exenv';\nimport scroll from 'scroll';\nimport scrollParent from 'scrollparent';\nimport { isValidElementType, Element, ForwardRef, typeOf } from 'react-is';\nimport deepmerge from 'deepmerge';\nimport Floater from 'react-floater';\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nvar ACTIONS = {\n  INIT: 'init',\n  START: 'start',\n  STOP: 'stop',\n  RESET: 'reset',\n  PREV: 'prev',\n  NEXT: 'next',\n  GO: 'go',\n  CLOSE: 'close',\n  SKIP: 'skip',\n  UPDATE: 'update'\n};\nvar EVENTS = {\n  TOUR_START: 'tour:start',\n  STEP_BEFORE: 'step:before',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  STEP_AFTER: 'step:after',\n  TOUR_END: 'tour:end',\n  TOUR_STATUS: 'tour:status',\n  TARGET_NOT_FOUND: 'error:target_not_found',\n  ERROR: 'error'\n};\nvar LIFECYCLE = {\n  INIT: 'init',\n  READY: 'ready',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  COMPLETE: 'complete',\n  ERROR: 'error'\n};\nvar STATUS = {\n  IDLE: 'idle',\n  READY: 'ready',\n  WAITING: 'waiting',\n  RUNNING: 'running',\n  PAUSED: 'paused',\n  SKIPPED: 'skipped',\n  FINISHED: 'finished',\n  ERROR: 'error'\n};\nvar canUseDOM = ExecutionEnvironment.canUseDOM;\nvar isReact16 = createPortal !== undefined;\n/**\n * Get the current browser\n *\n * @param {string} userAgent\n *\n * @returns {String}\n */\n\nfunction getBrowser() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : navigator.userAgent;\n  var browser = userAgent;\n  if (typeof window === 'undefined') {\n    browser = 'node';\n  } else if (document.documentMode) {\n    browser = 'ie';\n  } else if (/Edge/.test(userAgent)) {\n    browser = 'edge';\n  } // Opera 8.0+\n  else if (Boolean(window.opera) || userAgent.indexOf(' OPR/') >= 0) {\n    browser = 'opera';\n  } // Firefox 1.0+\n  else if (typeof window.InstallTrigger !== 'undefined') {\n    browser = 'firefox';\n  } // Chrome 1+\n  else if (window.chrome) {\n    browser = 'chrome';\n  } // Safari (and Chrome iOS, Firefox iOS)\n  else if (/(Version\\/([0-9._]+).*Safari|CriOS|FxiOS| Mobile\\/)/.test(userAgent)) {\n    browser = 'safari';\n  }\n  return browser;\n}\n/**\n * Get the toString Object type\n * @param {*} value\n * @returns {string}\n */\n\nfunction getObjectType(value) {\n  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();\n}\n/**\n * Get text from React components\n *\n * @param {*} root\n *\n * @returns {string}\n */\n\nfunction getText(root) {\n  var content = [];\n  var recurse = function recurse(child) {\n    /* istanbul ignore else */\n    if (typeof child === 'string' || typeof child === 'number') {\n      content.push(child);\n    } else if (Array.isArray(child)) {\n      child.forEach(function (c) {\n        return recurse(c);\n      });\n    } else if (child && child.props) {\n      var children = child.props.children;\n      if (Array.isArray(children)) {\n        children.forEach(function (c) {\n          return recurse(c);\n        });\n      } else {\n        recurse(children);\n      }\n    }\n  };\n  recurse(root);\n  return content.join(' ').trim();\n}\nfunction hasOwnProperty(value, key) {\n  return Object.prototype.hasOwnProperty.call(value, key);\n}\nfunction hasValidKeys(value, keys) {\n  if (!is.plainObject(value) || !is.array(keys)) {\n    return false;\n  }\n  return Object.keys(value).every(function (d) {\n    return keys.indexOf(d) !== -1;\n  });\n}\n/**\n * Convert hex to RGB\n *\n * @param {string} hex\n * @returns {Array}\n */\n\nfunction hexToRGB(hex) {\n  var shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n  var properHex = hex.replace(shorthandRegex, function (m, r, g, b) {\n    return r + r + g + g + b + b;\n  });\n  var result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(properHex);\n  return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];\n}\n/**\n * Decide if the step shouldn't skip the beacon\n * @param {Object} step\n *\n * @returns {boolean}\n */\n\nfunction hideBeacon(step) {\n  return step.disableBeacon || step.placement === 'center';\n}\n/**\n * Compare if two variables are equal\n *\n * @param {*} left\n * @param {*} right\n *\n * @returns {boolean}\n */\n\nfunction isEqual(left, right) {\n  var type;\n  var hasReactElement = /*#__PURE__*/isValidElement(left) || /*#__PURE__*/isValidElement(right);\n  var hasUndefined = is.undefined(left) || is.undefined(right);\n  if (getObjectType(left) !== getObjectType(right) || hasReactElement || hasUndefined) {\n    return false;\n  }\n  if (is.domElement(left)) {\n    return left.isSameNode(right);\n  }\n  if (is.number(left)) {\n    return left === right;\n  }\n  if (is[\"function\"](left)) {\n    return left.toString() === right.toString();\n  }\n  for (var key in left) {\n    /* istanbul ignore else */\n    if (hasOwnProperty(left, key)) {\n      if (typeof left[key] === 'undefined' || typeof right[key] === 'undefined') {\n        return false;\n      }\n      type = getObjectType(left[key]);\n      if (['object', 'array'].indexOf(type) !== -1 && isEqual(left[key], right[key])) {\n        continue;\n      }\n      if (type === 'function' && isEqual(left[key], right[key])) {\n        continue;\n      }\n      if (left[key] !== right[key]) {\n        return false;\n      }\n    }\n  }\n  for (var p in right) {\n    /* istanbul ignore else */\n    if (hasOwnProperty(right, p)) {\n      if (typeof left[p] === 'undefined') {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n/**\n * Detect legacy browsers\n *\n * @returns {boolean}\n */\n\nfunction isLegacy() {\n  return !(['chrome', 'safari', 'firefox', 'opera'].indexOf(getBrowser()) !== -1);\n}\n/**\n * Log method calls if debug is enabled\n *\n * @private\n * @param {Object}       arg\n * @param {string}       arg.title    - The title the logger was called from\n * @param {Object|Array} [arg.data]   - The data to be logged\n * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n */\n\nfunction log(_ref) {\n  var title = _ref.title,\n    data = _ref.data,\n    _ref$warn = _ref.warn,\n    warn = _ref$warn === void 0 ? false : _ref$warn,\n    _ref$debug = _ref.debug,\n    debug = _ref$debug === void 0 ? false : _ref$debug;\n\n  /* eslint-disable no-console */\n  var logFn = warn ? console.warn || console.error : console.log;\n  if (debug) {\n    if (title && data) {\n      console.groupCollapsed(\"%creact-joyride: \".concat(title), 'color: #ff0044; font-weight: bold; font-size: 12px;');\n      if (Array.isArray(data)) {\n        data.forEach(function (d) {\n          if (is.plainObject(d) && d.key) {\n            logFn.apply(console, [d.key, d.value]);\n          } else {\n            logFn.apply(console, [d]);\n          }\n        });\n      } else {\n        logFn.apply(console, [data]);\n      }\n      console.groupEnd();\n    } else {\n      console.error('Missing title or data props');\n    }\n  }\n  /* eslint-enable */\n}\nvar defaultState = {\n  action: '',\n  controlled: false,\n  index: 0,\n  lifecycle: LIFECYCLE.INIT,\n  size: 0,\n  status: STATUS.IDLE\n};\nvar validKeys = ['action', 'index', 'lifecycle', 'status'];\nfunction createStore(props) {\n  var store = new Map();\n  var data = new Map();\n  var Store = /*#__PURE__*/function () {\n    function Store() {\n      var _this = this;\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref$continuous = _ref.continuous,\n        continuous = _ref$continuous === void 0 ? false : _ref$continuous,\n        stepIndex = _ref.stepIndex,\n        _ref$steps = _ref.steps,\n        _steps = _ref$steps === void 0 ? [] : _ref$steps;\n      _classCallCheck(this, Store);\n      _defineProperty(this, \"listener\", void 0);\n      _defineProperty(this, \"setSteps\", function (steps) {\n        var _this$getState = _this.getState(),\n          size = _this$getState.size,\n          status = _this$getState.status;\n        var state = {\n          size: steps.length,\n          status: status\n        };\n        data.set('steps', steps);\n        if (status === STATUS.WAITING && !size && steps.length) {\n          state.status = STATUS.RUNNING;\n        }\n        _this.setState(state);\n      });\n      _defineProperty(this, \"addListener\", function (listener) {\n        _this.listener = listener;\n      });\n      _defineProperty(this, \"update\", function (state) {\n        if (!hasValidKeys(state, validKeys)) {\n          throw new Error(\"State is not valid. Valid keys: \".concat(validKeys.join(', ')));\n        }\n        _this.setState(_objectSpread2({}, _this.getNextState(_objectSpread2(_objectSpread2(_objectSpread2({}, _this.getState()), state), {}, {\n          action: state.action || ACTIONS.UPDATE\n        }), true)));\n      });\n      _defineProperty(this, \"start\", function (nextIndex) {\n        var _this$getState2 = _this.getState(),\n          index = _this$getState2.index,\n          size = _this$getState2.size;\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.START,\n          index: is.number(nextIndex) ? nextIndex : index\n        }, true)), {}, {\n          status: size ? STATUS.RUNNING : STATUS.WAITING\n        }));\n      });\n      _defineProperty(this, \"stop\", function () {\n        var advance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n        var _this$getState3 = _this.getState(),\n          index = _this$getState3.index,\n          status = _this$getState3.status;\n        if ([STATUS.FINISHED, STATUS.SKIPPED].indexOf(status) !== -1) return;\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.STOP,\n          index: index + (advance ? 1 : 0)\n        })), {}, {\n          status: STATUS.PAUSED\n        }));\n      });\n      _defineProperty(this, \"close\", function () {\n        var _this$getState4 = _this.getState(),\n          index = _this$getState4.index,\n          status = _this$getState4.status;\n        if (status !== STATUS.RUNNING) return;\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.CLOSE,\n          index: index + 1\n        })));\n      });\n      _defineProperty(this, \"go\", function (nextIndex) {\n        var _this$getState5 = _this.getState(),\n          controlled = _this$getState5.controlled,\n          status = _this$getState5.status;\n        if (controlled || status !== STATUS.RUNNING) return;\n        var step = _this.getSteps()[nextIndex];\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.GO,\n          index: nextIndex\n        })), {}, {\n          status: step ? status : STATUS.FINISHED\n        }));\n      });\n      _defineProperty(this, \"info\", function () {\n        return _this.getState();\n      });\n      _defineProperty(this, \"next\", function () {\n        var _this$getState6 = _this.getState(),\n          index = _this$getState6.index,\n          status = _this$getState6.status;\n        if (status !== STATUS.RUNNING) return;\n        _this.setState(_this.getNextState({\n          action: ACTIONS.NEXT,\n          index: index + 1\n        }));\n      });\n      _defineProperty(this, \"open\", function () {\n        var _this$getState7 = _this.getState(),\n          status = _this$getState7.status;\n        if (status !== STATUS.RUNNING) return;\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.UPDATE,\n          lifecycle: LIFECYCLE.TOOLTIP\n        })));\n      });\n      _defineProperty(this, \"prev\", function () {\n        var _this$getState8 = _this.getState(),\n          index = _this$getState8.index,\n          status = _this$getState8.status;\n        if (status !== STATUS.RUNNING) return;\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.PREV,\n          index: index - 1\n        })));\n      });\n      _defineProperty(this, \"reset\", function () {\n        var restart = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n        var _this$getState9 = _this.getState(),\n          controlled = _this$getState9.controlled;\n        if (controlled) return;\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.RESET,\n          index: 0\n        })), {}, {\n          status: restart ? STATUS.RUNNING : STATUS.READY\n        }));\n      });\n      _defineProperty(this, \"skip\", function () {\n        var _this$getState10 = _this.getState(),\n          status = _this$getState10.status;\n        if (status !== STATUS.RUNNING) return;\n        _this.setState({\n          action: ACTIONS.SKIP,\n          lifecycle: LIFECYCLE.INIT,\n          status: STATUS.SKIPPED\n        });\n      });\n      this.setState({\n        action: ACTIONS.INIT,\n        controlled: is.number(stepIndex),\n        continuous: continuous,\n        index: is.number(stepIndex) ? stepIndex : 0,\n        lifecycle: LIFECYCLE.INIT,\n        status: _steps.length ? STATUS.READY : STATUS.IDLE\n      }, true);\n      this.setSteps(_steps);\n    }\n    _createClass(Store, [{\n      key: \"setState\",\n      value: function setState(nextState) {\n        var initial = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n        var state = this.getState();\n        var _state$nextState = _objectSpread2(_objectSpread2({}, state), nextState),\n          action = _state$nextState.action,\n          index = _state$nextState.index,\n          lifecycle = _state$nextState.lifecycle,\n          size = _state$nextState.size,\n          status = _state$nextState.status;\n        store.set('action', action);\n        store.set('index', index);\n        store.set('lifecycle', lifecycle);\n        store.set('size', size);\n        store.set('status', status);\n        if (initial) {\n          store.set('controlled', nextState.controlled);\n          store.set('continuous', nextState.continuous);\n        }\n        /* istanbul ignore else */\n\n        if (this.listener && this.hasUpdatedState(state)) {\n          // console.log('▶ ▶ ▶ NEW STATE', this.getState());\n          this.listener(this.getState());\n        }\n      }\n    }, {\n      key: \"getState\",\n      value: function getState() {\n        if (!store.size) {\n          return _objectSpread2({}, defaultState);\n        }\n        return {\n          action: store.get('action') || '',\n          controlled: store.get('controlled') || false,\n          index: parseInt(store.get('index'), 10),\n          lifecycle: store.get('lifecycle') || '',\n          size: store.get('size') || 0,\n          status: store.get('status') || ''\n        };\n      }\n    }, {\n      key: \"getNextState\",\n      value: function getNextState(state) {\n        var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n        var _this$getState11 = this.getState(),\n          action = _this$getState11.action,\n          controlled = _this$getState11.controlled,\n          index = _this$getState11.index,\n          size = _this$getState11.size,\n          status = _this$getState11.status;\n        var newIndex = is.number(state.index) ? state.index : index;\n        var nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);\n        return {\n          action: state.action || action,\n          controlled: controlled,\n          index: nextIndex,\n          lifecycle: state.lifecycle || LIFECYCLE.INIT,\n          size: state.size || size,\n          status: nextIndex === size ? STATUS.FINISHED : state.status || status\n        };\n      }\n    }, {\n      key: \"hasUpdatedState\",\n      value: function hasUpdatedState(oldState) {\n        var before = JSON.stringify(oldState);\n        var after = JSON.stringify(this.getState());\n        return before !== after;\n      }\n    }, {\n      key: \"getSteps\",\n      value: function getSteps() {\n        var steps = data.get('steps');\n        return Array.isArray(steps) ? steps : [];\n      }\n    }, {\n      key: \"getHelpers\",\n      value: function getHelpers() {\n        return {\n          close: this.close,\n          go: this.go,\n          info: this.info,\n          next: this.next,\n          open: this.open,\n          prev: this.prev,\n          reset: this.reset,\n          skip: this.skip\n        };\n      }\n    }]);\n    return Store;\n  }();\n  return new Store(props);\n}\nfunction scrollDoc() {\n  return document.scrollingElement || document.createElement('body');\n}\n/**\n * Find the bounding client rect\n *\n * @private\n * @param {HTMLElement} element - The target element\n * @returns {Object}\n */\n\nfunction getClientRect(element) {\n  if (!element) {\n    return {};\n  }\n  return element.getBoundingClientRect();\n}\n/**\n * Helper function to get the browser-normalized \"document height\"\n * @returns {Number}\n */\n\nfunction getDocumentHeight() {\n  var _document = document,\n    body = _document.body,\n    html = _document.documentElement;\n  if (!body || !html) {\n    return 0;\n  }\n  return Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n}\n/**\n * Find and return the target DOM element based on a step's 'target'.\n *\n * @private\n * @param {string|HTMLElement} element\n *\n * @returns {HTMLElement|null}\n */\n\nfunction getElement(element) {\n  /* istanbul ignore else */\n  if (typeof element === 'string') {\n    return document.querySelector(element);\n  }\n  return element;\n}\n/**\n *  Get computed style property\n *\n * @param {HTMLElement} el\n *\n * @returns {Object}\n */\n\nfunction getStyleComputedProperty(el) {\n  if (!el || el.nodeType !== 1) {\n    return {};\n  }\n  return getComputedStyle(el);\n}\n/**\n * Get scroll parent with fix\n *\n * @param {HTMLElement} element\n * @param {boolean} skipFix\n * @param {boolean} [forListener]\n *\n * @returns {*}\n */\n\nfunction getScrollParent(element, skipFix, forListener) {\n  var parent = scrollParent(element);\n  if (parent.isSameNode(scrollDoc())) {\n    if (forListener) {\n      return document;\n    }\n    return scrollDoc();\n  }\n  var hasScrolling = parent.scrollHeight > parent.offsetHeight;\n  /* istanbul ignore else */\n\n  if (!hasScrolling && !skipFix) {\n    parent.style.overflow = 'initial';\n    return scrollDoc();\n  }\n  return parent;\n}\n/**\n * Check if the element has custom scroll parent\n *\n * @param {HTMLElement} element\n * @param {boolean} skipFix\n *\n * @returns {boolean}\n */\n\nfunction hasCustomScrollParent(element, skipFix) {\n  if (!element) return false;\n  var parent = getScrollParent(element, skipFix);\n  return !parent.isSameNode(scrollDoc());\n}\n/**\n * Check if the element has custom offset parent\n *\n * @param {HTMLElement} element\n *\n * @returns {boolean}\n */\n\nfunction hasCustomOffsetParent(element) {\n  return element.offsetParent !== document.body;\n}\n/**\n * Check if an element has fixed/sticky position\n * @param {HTMLElement|Node} el\n * @param {string} [type]\n *\n * @returns {boolean}\n */\n\nfunction hasPosition(el) {\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'fixed';\n  if (!el || !(el instanceof HTMLElement)) {\n    return false;\n  }\n  var nodeName = el.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(el).position === type) {\n    return true;\n  }\n  return hasPosition(el.parentNode, type);\n}\n/**\n * Check if the element is visible\n *\n * @param {HTMLElement} element\n *\n * @returns {boolean}\n */\n\nfunction isElementVisible(element) {\n  if (!element) return false;\n  var parentElement = element;\n  while (parentElement) {\n    if (parentElement === document.body) break;\n    /* istanbul ignore else */\n\n    if (parentElement instanceof HTMLElement) {\n      var _getComputedStyle = getComputedStyle(parentElement),\n        display = _getComputedStyle.display,\n        visibility = _getComputedStyle.visibility;\n      if (display === 'none' || visibility === 'hidden') {\n        return false;\n      }\n    }\n    parentElement = parentElement.parentNode;\n  }\n  return true;\n}\n/**\n * Find and return the target DOM element based on a step's 'target'.\n *\n * @private\n * @param {string|HTMLElement} element\n * @param {number} offset\n * @param {boolean} skipFix\n *\n * @returns {HTMLElement|undefined}\n */\n\nfunction getElementPosition(element, offset, skipFix) {\n  var elementRect = getClientRect(element);\n  var parent = getScrollParent(element, skipFix);\n  var hasScrollParent = hasCustomScrollParent(element, skipFix);\n  var parentTop = 0;\n  /* istanbul ignore else */\n\n  if (parent instanceof HTMLElement) {\n    parentTop = parent.scrollTop;\n  }\n  var top = elementRect.top + (!hasScrollParent && !hasPosition(element) ? parentTop : 0);\n  return Math.floor(top - offset);\n}\n/**\n * Get the offsetTop of each element up to the body\n *\n * @param {HTMLElement} element\n *\n * @returns {number}\n */\n\nfunction getTopOffset(element) {\n  if (element instanceof HTMLElement) {\n    if (element.offsetParent instanceof HTMLElement) {\n      return getTopOffset(element.offsetParent) + element.offsetTop;\n    }\n    return element.offsetTop;\n  }\n  return 0;\n}\n/**\n * Get the scrollTop position\n *\n * @param {HTMLElement} element\n * @param {number} offset\n * @param {boolean} skipFix\n *\n * @returns {number}\n */\n\nfunction getScrollTo(element, offset, skipFix) {\n  if (!element) {\n    return 0;\n  }\n  var parent = scrollParent(element);\n  var top = getTopOffset(element);\n  if (hasCustomScrollParent(element, skipFix) && !hasCustomOffsetParent(element)) {\n    top -= getTopOffset(parent);\n  }\n  return Math.floor(top - offset);\n}\n/**\n * Scroll to position\n * @param {number} value\n * @param {HTMLElement} element\n * @param {number} scrollDuration\n * @returns {Promise<*>}\n */\n\nfunction scrollTo(value) {\n  var element = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scrollDoc();\n  var scrollDuration = arguments.length > 2 ? arguments[2] : undefined;\n  return new Promise(function (resolve, reject) {\n    var scrollTop = element.scrollTop;\n    var limit = value > scrollTop ? value - scrollTop : scrollTop - value;\n    scroll.top(element, value, {\n      duration: limit < 100 ? 50 : scrollDuration\n    }, function (error) {\n      if (error && error.message !== 'Element already at target scroll position') {\n        return reject(error);\n      }\n      return resolve();\n    });\n  });\n}\nfunction createChainableTypeChecker(validate) {\n  function checkType(isRequired, props, propName, componentName, location, propFullName) {\n    var componentNameSafe = componentName || '<<anonymous>>';\n    var propFullNameSafe = propFullName || propName;\n    /* istanbul ignore else */\n\n    if (props[propName] == null) {\n      if (isRequired) {\n        return new Error(\"Required \".concat(location, \" `\").concat(propFullNameSafe, \"` was not specified in `\").concat(componentNameSafe, \"`.\"));\n      }\n      return null;\n    }\n    for (var _len = arguments.length, args = new Array(_len > 6 ? _len - 6 : 0), _key = 6; _key < _len; _key++) {\n      args[_key - 6] = arguments[_key];\n    }\n    return validate.apply(void 0, [props, propName, componentNameSafe, location, propFullNameSafe].concat(args));\n  }\n  var chainedCheckType = checkType.bind(null, false);\n  chainedCheckType.isRequired = checkType.bind(null, true);\n  return chainedCheckType;\n}\ncreateChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n  var propValue = props[propName];\n  var Component = propValue;\n  if (! /*#__PURE__*/React.isValidElement(propValue) && isValidElementType(propValue)) {\n    var ownProps = {\n      ref: function ref() {},\n      step: {}\n    };\n    Component = /*#__PURE__*/React.createElement(Component, ownProps);\n  }\n  if (is.string(propValue) || is.number(propValue) || !isValidElementType(propValue) || !([Element, ForwardRef].indexOf(typeOf(Component)) !== -1)) {\n    return new Error(\"Invalid \".concat(location, \" `\").concat(propFullName, \"` supplied to `\").concat(componentName, \"`. Expected a React class or forwardRef.\"));\n  }\n  return undefined;\n});\nvar defaultOptions = {\n  arrowColor: '#fff',\n  backgroundColor: '#fff',\n  beaconSize: 36,\n  overlayColor: 'rgba(0, 0, 0, 0.5)',\n  primaryColor: '#f04',\n  spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',\n  textColor: '#333',\n  zIndex: 100\n};\nvar buttonBase = {\n  backgroundColor: 'transparent',\n  border: 0,\n  borderRadius: 0,\n  color: '#555',\n  cursor: 'pointer',\n  fontSize: 16,\n  lineHeight: 1,\n  padding: 8,\n  WebkitAppearance: 'none'\n};\nvar spotlight = {\n  borderRadius: 4,\n  position: 'absolute'\n};\nfunction getStyles() {\n  var stepStyles = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var options = deepmerge(defaultOptions, stepStyles.options || {});\n  var width = 290;\n  if (window.innerWidth > 480) {\n    width = 380;\n  }\n  if (options.width) {\n    if (window.innerWidth < options.width) {\n      width = window.innerWidth - 30;\n    } else {\n      width = options.width; //eslint-disable-line prefer-destructuring\n    }\n  }\n  var overlay = {\n    bottom: 0,\n    left: 0,\n    overflow: 'hidden',\n    position: 'absolute',\n    right: 0,\n    top: 0,\n    zIndex: options.zIndex\n  };\n  var defaultStyles = {\n    beacon: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      display: 'inline-block',\n      height: options.beaconSize,\n      position: 'relative',\n      width: options.beaconSize,\n      zIndex: options.zIndex\n    }),\n    beaconInner: {\n      animation: 'joyride-beacon-inner 1.2s infinite ease-in-out',\n      backgroundColor: options.primaryColor,\n      borderRadius: '50%',\n      display: 'block',\n      height: '50%',\n      left: '50%',\n      opacity: 0.7,\n      position: 'absolute',\n      top: '50%',\n      transform: 'translate(-50%, -50%)',\n      width: '50%'\n    },\n    beaconOuter: {\n      animation: 'joyride-beacon-outer 1.2s infinite ease-in-out',\n      backgroundColor: \"rgba(\".concat(hexToRGB(options.primaryColor).join(','), \", 0.2)\"),\n      border: \"2px solid \".concat(options.primaryColor),\n      borderRadius: '50%',\n      boxSizing: 'border-box',\n      display: 'block',\n      height: '100%',\n      left: 0,\n      opacity: 0.9,\n      position: 'absolute',\n      top: 0,\n      transformOrigin: 'center',\n      width: '100%'\n    },\n    tooltip: {\n      backgroundColor: options.backgroundColor,\n      borderRadius: 5,\n      boxSizing: 'border-box',\n      color: options.textColor,\n      fontSize: 16,\n      maxWidth: '100%',\n      padding: 15,\n      position: 'relative',\n      width: width\n    },\n    tooltipContainer: {\n      lineHeight: 1.4,\n      textAlign: 'center'\n    },\n    tooltipTitle: {\n      fontSize: 18,\n      margin: 0\n    },\n    tooltipContent: {\n      padding: '20px 10px'\n    },\n    tooltipFooter: {\n      alignItems: 'center',\n      display: 'flex',\n      justifyContent: 'flex-end',\n      marginTop: 15\n    },\n    tooltipFooterSpacer: {\n      flex: 1\n    },\n    buttonNext: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      backgroundColor: options.primaryColor,\n      borderRadius: 4,\n      color: '#fff'\n    }),\n    buttonBack: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.primaryColor,\n      marginLeft: 'auto',\n      marginRight: 5\n    }),\n    buttonClose: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.textColor,\n      height: 14,\n      padding: 15,\n      position: 'absolute',\n      right: 0,\n      top: 0,\n      width: 14\n    }),\n    buttonSkip: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.textColor,\n      fontSize: 14\n    }),\n    overlay: _objectSpread2(_objectSpread2({}, overlay), {}, {\n      backgroundColor: options.overlayColor,\n      mixBlendMode: 'hard-light'\n    }),\n    overlayLegacy: _objectSpread2({}, overlay),\n    overlayLegacyCenter: _objectSpread2(_objectSpread2({}, overlay), {}, {\n      backgroundColor: options.overlayColor\n    }),\n    spotlight: _objectSpread2(_objectSpread2({}, spotlight), {}, {\n      backgroundColor: 'gray'\n    }),\n    spotlightLegacy: _objectSpread2(_objectSpread2({}, spotlight), {}, {\n      boxShadow: \"0 0 0 9999px \".concat(options.overlayColor, \", \").concat(options.spotlightShadow)\n    }),\n    floaterStyles: {\n      arrow: {\n        color: options.arrowColor\n      },\n      options: {\n        zIndex: options.zIndex\n      }\n    },\n    options: options\n  };\n  return deepmerge(defaultStyles, stepStyles);\n}\nvar DEFAULTS = {\n  floaterProps: {\n    options: {\n      preventOverflow: {\n        boundariesElement: 'scrollParent'\n      }\n    },\n    wrapperOptions: {\n      offset: -18,\n      position: true\n    }\n  },\n  locale: {\n    back: 'Back',\n    close: 'Close',\n    last: 'Last',\n    next: 'Next',\n    open: 'Open the dialog',\n    skip: 'Skip'\n  },\n  step: {\n    event: 'click',\n    placement: 'bottom',\n    offset: 10\n  }\n};\nfunction getTourProps(props) {\n  var sharedTourProps = ['beaconComponent', 'disableCloseOnEsc', 'disableOverlay', 'disableOverlayClose', 'disableScrolling', 'disableScrollParentFix', 'floaterProps', 'hideBackButton', 'hideCloseButton', 'locale', 'showProgress', 'showSkipButton', 'spotlightClicks', 'spotlightPadding', 'styles', 'tooltipComponent'];\n  return Object.keys(props).filter(function (d) {\n    return sharedTourProps.indexOf(d) !== -1;\n  }).reduce(function (acc, i) {\n    acc[i] = props[i]; //eslint-disable-line react/destructuring-assignment\n\n    return acc;\n  }, {});\n}\nfunction getMergedStep(step, props) {\n  if (!step) return null;\n  var mergedStep = deepmerge.all([getTourProps(props), DEFAULTS.step, step], {\n    isMergeableObject: is.plainObject\n  });\n  var mergedStyles = getStyles(deepmerge(props.styles || {}, step.styles || {}));\n  var scrollParent = hasCustomScrollParent(getElement(step.target), mergedStep.disableScrollParentFix);\n  var floaterProps = deepmerge.all([props.floaterProps || {}, DEFAULTS.floaterProps, mergedStep.floaterProps || {}]); // Set react-floater props\n\n  floaterProps.offset = mergedStep.offset;\n  floaterProps.styles = deepmerge(floaterProps.styles || {}, mergedStyles.floaterStyles || {});\n  delete mergedStyles.floaterStyles;\n  floaterProps.offset += props.spotlightPadding || step.spotlightPadding || 0;\n  if (step.placementBeacon) {\n    floaterProps.wrapperOptions.placement = step.placementBeacon;\n  }\n  if (scrollParent) {\n    floaterProps.options.preventOverflow.boundariesElement = 'window';\n  }\n  return _objectSpread2(_objectSpread2({}, mergedStep), {}, {\n    locale: deepmerge.all([DEFAULTS.locale, props.locale || {}, mergedStep.locale || {}]),\n    floaterProps: floaterProps,\n    styles: mergedStyles\n  });\n}\n/**\n * Validate if a step is valid\n *\n * @param {Object} step - A step object\n * @param {boolean} debug\n *\n * @returns {boolean} - True if the step is valid, false otherwise\n */\n\nfunction validateStep(step) {\n  var debug = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!is.plainObject(step)) {\n    log({\n      title: 'validateStep',\n      data: 'step must be an object',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n  if (!step.target) {\n    log({\n      title: 'validateStep',\n      data: 'target is missing from the step',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n  return true;\n}\n/**\n * Validate if steps is valid\n *\n * @param {Array} steps - A steps array\n * @param {boolean} debug\n *\n * @returns {boolean} - True if the steps are valid, false otherwise\n */\n\nfunction validateSteps(steps) {\n  var debug = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!is.array(steps)) {\n    log({\n      title: 'validateSteps',\n      data: 'steps must be an array',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n  return steps.every(function (d) {\n    return validateStep(d, debug);\n  });\n}\nvar Scope = /*#__PURE__*/_createClass(function Scope(_element) {\n  var _this = this;\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  _classCallCheck(this, Scope);\n  _defineProperty(this, \"element\", void 0);\n  _defineProperty(this, \"options\", void 0);\n  _defineProperty(this, \"canBeTabbed\", function (element) {\n    var tabIndex = element.tabIndex;\n    if (tabIndex === null || tabIndex < 0) tabIndex = undefined;\n    var isTabIndexNaN = isNaN(tabIndex);\n    return !isTabIndexNaN && _this.canHaveFocus(element);\n  });\n  _defineProperty(this, \"canHaveFocus\", function (element) {\n    var validTabNodes = /input|select|textarea|button|object/;\n    var nodeName = element.nodeName.toLowerCase();\n    var res = validTabNodes.test(nodeName) && !element.getAttribute('disabled') || nodeName === 'a' && !!element.getAttribute('href');\n    return res && _this.isVisible(element);\n  });\n  _defineProperty(this, \"findValidTabElements\", function () {\n    return [].slice.call(_this.element.querySelectorAll('*'), 0).filter(_this.canBeTabbed);\n  });\n  _defineProperty(this, \"handleKeyDown\", function (e) {\n    var _this$options$keyCode = _this.options.keyCode,\n      keyCode = _this$options$keyCode === void 0 ? 9 : _this$options$keyCode;\n    /* istanbul ignore else */\n\n    if (e.keyCode === keyCode) {\n      _this.interceptTab(e);\n    }\n  });\n  _defineProperty(this, \"interceptTab\", function (event) {\n    var elements = _this.findValidTabElements();\n    if (!elements.length) {\n      return;\n    }\n    event.preventDefault();\n    var shiftKey = event.shiftKey;\n    var x = elements.indexOf(document.activeElement);\n    if (x === -1 || !shiftKey && x + 1 === elements.length) {\n      x = 0;\n    } else if (shiftKey && x === 0) {\n      x = elements.length - 1;\n    } else {\n      x += shiftKey ? -1 : 1;\n    }\n    elements[x].focus();\n  });\n  _defineProperty(this, \"isHidden\", function (element) {\n    var noSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n    var style = window.getComputedStyle(element);\n    if (noSize && !element.innerHTML) return true;\n    return noSize && style.getPropertyValue('overflow') !== 'visible' || style.getPropertyValue('display') === 'none';\n  });\n  _defineProperty(this, \"isVisible\", function (element) {\n    var parentElement = element;\n    while (parentElement) {\n      /* istanbul ignore else */\n      if (parentElement instanceof HTMLElement) {\n        if (parentElement === document.body) break;\n        /* istanbul ignore else */\n\n        if (_this.isHidden(parentElement)) return false;\n        parentElement = parentElement.parentNode;\n      }\n    }\n    return true;\n  });\n  _defineProperty(this, \"removeScope\", function () {\n    window.removeEventListener('keydown', _this.handleKeyDown);\n  });\n  _defineProperty(this, \"checkFocus\", function (target) {\n    if (document.activeElement !== target) {\n      target.focus();\n      window.requestAnimationFrame(function () {\n        return _this.checkFocus(target);\n      });\n    }\n  });\n  _defineProperty(this, \"setFocus\", function () {\n    var selector = _this.options.selector;\n    if (!selector) return;\n    var target = _this.element.querySelector(selector);\n    /* istanbul ignore else */\n\n    if (target) {\n      window.requestAnimationFrame(function () {\n        return _this.checkFocus(target);\n      });\n    }\n  });\n  if (!(_element instanceof HTMLElement)) {\n    throw new TypeError('Invalid parameter: element must be an HTMLElement');\n  }\n  this.element = _element;\n  this.options = options;\n  window.addEventListener('keydown', this.handleKeyDown, false);\n  this.setFocus();\n});\nvar JoyrideBeacon = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideBeacon, _React$Component);\n  var _super = _createSuper(JoyrideBeacon);\n  function JoyrideBeacon(props) {\n    var _this;\n    _classCallCheck(this, JoyrideBeacon);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"setBeaconRef\", function (c) {\n      _this.beacon = c;\n    });\n    if (!props.beaconComponent) {\n      var head = document.head || document.getElementsByTagName('head')[0];\n      var style = document.createElement('style');\n      var css = \"\\n        @keyframes joyride-beacon-inner {\\n          20% {\\n            opacity: 0.9;\\n          }\\n        \\n          90% {\\n            opacity: 0.7;\\n          }\\n        }\\n        \\n        @keyframes joyride-beacon-outer {\\n          0% {\\n            transform: scale(1);\\n          }\\n        \\n          45% {\\n            opacity: 0.7;\\n            transform: scale(0.75);\\n          }\\n        \\n          100% {\\n            opacity: 0.9;\\n            transform: scale(1);\\n          }\\n        }\\n      \";\n      style.type = 'text/css';\n      style.id = 'joyride-beacon-animation';\n      if (props.nonce !== undefined) {\n        style.setAttribute('nonce', props.nonce);\n      }\n      style.appendChild(document.createTextNode(css));\n      head.appendChild(style);\n    }\n    return _this;\n  }\n  _createClass(JoyrideBeacon, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      var shouldFocus = this.props.shouldFocus;\n      setTimeout(function () {\n        if (is.domElement(_this2.beacon) && shouldFocus) {\n          _this2.beacon.focus();\n        }\n      }, 0);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var style = document.getElementById('joyride-beacon-animation');\n      if (style) {\n        style.parentNode.removeChild(style);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        beaconComponent = _this$props.beaconComponent,\n        locale = _this$props.locale,\n        onClickOrHover = _this$props.onClickOrHover,\n        styles = _this$props.styles;\n      var props = {\n        'aria-label': locale.open,\n        onClick: onClickOrHover,\n        onMouseEnter: onClickOrHover,\n        ref: this.setBeaconRef,\n        title: locale.open\n      };\n      var component;\n      if (beaconComponent) {\n        var BeaconComponent = beaconComponent;\n        component = /*#__PURE__*/React.createElement(BeaconComponent, props);\n      } else {\n        component = /*#__PURE__*/React.createElement(\"button\", _extends({\n          key: \"JoyrideBeacon\",\n          className: \"react-joyride__beacon\",\n          style: styles.beacon,\n          type: \"button\"\n        }, props), /*#__PURE__*/React.createElement(\"span\", {\n          style: styles.beaconInner\n        }), /*#__PURE__*/React.createElement(\"span\", {\n          style: styles.beaconOuter\n        }));\n      }\n      return component;\n    }\n  }]);\n  return JoyrideBeacon;\n}(React.Component);\nvar JoyrideSpotlight = function JoyrideSpotlight(_ref) {\n  var styles = _ref.styles;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    key: \"JoyrideSpotlight\",\n    className: \"react-joyride__spotlight\",\n    style: styles\n  });\n};\nvar _excluded$2 = [\"mixBlendMode\", \"zIndex\"];\nvar JoyrideOverlay = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideOverlay, _React$Component);\n  var _super = _createSuper(JoyrideOverlay);\n  function JoyrideOverlay() {\n    var _this;\n    _classCallCheck(this, JoyrideOverlay);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"_isMounted\", false);\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      mouseOverSpotlight: false,\n      isScrolling: false,\n      showSpotlight: true\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleMouseMove\", function (e) {\n      var mouseOverSpotlight = _this.state.mouseOverSpotlight;\n      var _this$spotlightStyles = _this.spotlightStyles,\n        height = _this$spotlightStyles.height,\n        left = _this$spotlightStyles.left,\n        position = _this$spotlightStyles.position,\n        top = _this$spotlightStyles.top,\n        width = _this$spotlightStyles.width;\n      var offsetY = position === 'fixed' ? e.clientY : e.pageY;\n      var offsetX = position === 'fixed' ? e.clientX : e.pageX;\n      var inSpotlightHeight = offsetY >= top && offsetY <= top + height;\n      var inSpotlightWidth = offsetX >= left && offsetX <= left + width;\n      var inSpotlight = inSpotlightWidth && inSpotlightHeight;\n      if (inSpotlight !== mouseOverSpotlight) {\n        _this.updateState({\n          mouseOverSpotlight: inSpotlight\n        });\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleScroll\", function () {\n      var target = _this.props.target;\n      var element = getElement(target);\n      if (_this.scrollParent !== document) {\n        var isScrolling = _this.state.isScrolling;\n        if (!isScrolling) {\n          _this.updateState({\n            isScrolling: true,\n            showSpotlight: false\n          });\n        }\n        clearTimeout(_this.scrollTimeout);\n        _this.scrollTimeout = setTimeout(function () {\n          _this.updateState({\n            isScrolling: false,\n            showSpotlight: true\n          });\n        }, 50);\n      } else if (hasPosition(element, 'sticky')) {\n        _this.updateState({});\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleResize\", function () {\n      clearTimeout(_this.resizeTimeout);\n      _this.resizeTimeout = setTimeout(function () {\n        if (!_this._isMounted) {\n          return;\n        }\n        _this.forceUpdate();\n      }, 100);\n    });\n    return _this;\n  }\n  _createClass(JoyrideOverlay, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props = this.props;\n      _this$props.debug;\n      _this$props.disableScrolling;\n      var disableScrollParentFix = _this$props.disableScrollParentFix,\n        target = _this$props.target;\n      var element = getElement(target);\n      this.scrollParent = getScrollParent(element, disableScrollParentFix, true);\n      this._isMounted = true;\n      window.addEventListener('resize', this.handleResize);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        lifecycle = _this$props2.lifecycle,\n        spotlightClicks = _this$props2.spotlightClicks;\n      var _treeChanges = treeChanges(prevProps, this.props),\n        changed = _treeChanges.changed;\n      /* istanbul ignore else */\n\n      if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n        this.scrollParent.addEventListener('scroll', this.handleScroll, {\n          passive: true\n        });\n        setTimeout(function () {\n          var isScrolling = _this2.state.isScrolling;\n          if (!isScrolling) {\n            _this2.updateState({\n              showSpotlight: true\n            });\n          }\n        }, 100);\n      }\n      if (changed('spotlightClicks') || changed('disableOverlay') || changed('lifecycle')) {\n        if (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP) {\n          window.addEventListener('mousemove', this.handleMouseMove, false);\n        } else if (lifecycle !== LIFECYCLE.TOOLTIP) {\n          window.removeEventListener('mousemove', this.handleMouseMove);\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      window.removeEventListener('mousemove', this.handleMouseMove);\n      window.removeEventListener('resize', this.handleResize);\n      clearTimeout(this.resizeTimeout);\n      clearTimeout(this.scrollTimeout);\n      this.scrollParent.removeEventListener('scroll', this.handleScroll);\n    }\n  }, {\n    key: \"spotlightStyles\",\n    get: function get() {\n      var showSpotlight = this.state.showSpotlight;\n      var _this$props3 = this.props,\n        disableScrollParentFix = _this$props3.disableScrollParentFix,\n        spotlightClicks = _this$props3.spotlightClicks,\n        spotlightPadding = _this$props3.spotlightPadding,\n        styles = _this$props3.styles,\n        target = _this$props3.target;\n      var element = getElement(target);\n      var elementRect = getClientRect(element);\n      var isFixedTarget = hasPosition(element);\n      var top = getElementPosition(element, spotlightPadding, disableScrollParentFix);\n      return _objectSpread2(_objectSpread2({}, isLegacy() ? styles.spotlightLegacy : styles.spotlight), {}, {\n        height: Math.round(elementRect.height + spotlightPadding * 2),\n        left: Math.round(elementRect.left - spotlightPadding),\n        opacity: showSpotlight ? 1 : 0,\n        pointerEvents: spotlightClicks ? 'none' : 'auto',\n        position: isFixedTarget ? 'fixed' : 'absolute',\n        top: top,\n        transition: 'opacity 0.2s',\n        width: Math.round(elementRect.width + spotlightPadding * 2)\n      });\n    }\n  }, {\n    key: \"updateState\",\n    value: function updateState(state) {\n      if (!this._isMounted) {\n        return;\n      }\n      this.setState(state);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state = this.state,\n        mouseOverSpotlight = _this$state.mouseOverSpotlight,\n        showSpotlight = _this$state.showSpotlight;\n      var _this$props4 = this.props,\n        disableOverlay = _this$props4.disableOverlay,\n        disableOverlayClose = _this$props4.disableOverlayClose,\n        lifecycle = _this$props4.lifecycle,\n        onClickOverlay = _this$props4.onClickOverlay,\n        placement = _this$props4.placement,\n        styles = _this$props4.styles;\n      if (disableOverlay || lifecycle !== LIFECYCLE.TOOLTIP) {\n        return null;\n      }\n      var baseStyles = styles.overlay;\n      /* istanbul ignore else */\n\n      if (isLegacy()) {\n        baseStyles = placement === 'center' ? styles.overlayLegacyCenter : styles.overlayLegacy;\n      }\n      var stylesOverlay = _objectSpread2({\n        cursor: disableOverlayClose ? 'default' : 'pointer',\n        height: getDocumentHeight(),\n        pointerEvents: mouseOverSpotlight ? 'none' : 'auto'\n      }, baseStyles);\n      var spotlight = placement !== 'center' && showSpotlight && /*#__PURE__*/React.createElement(JoyrideSpotlight, {\n        styles: this.spotlightStyles\n      }); // Hack for Safari bug with mix-blend-mode with z-index\n\n      if (getBrowser() === 'safari') {\n        stylesOverlay.mixBlendMode;\n        stylesOverlay.zIndex;\n        var safarOverlay = _objectWithoutProperties(stylesOverlay, _excluded$2);\n        spotlight = /*#__PURE__*/React.createElement(\"div\", {\n          style: _objectSpread2({}, safarOverlay)\n        }, spotlight);\n        delete stylesOverlay.backgroundColor;\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"react-joyride__overlay\",\n        style: stylesOverlay,\n        onClick: onClickOverlay\n      }, spotlight);\n    }\n  }]);\n  return JoyrideOverlay;\n}(React.Component);\nvar _excluded$1 = [\"styles\"],\n  _excluded2 = [\"color\", \"height\", \"width\"];\nvar JoyrideTooltipCloseBtn = function JoyrideTooltipCloseBtn(_ref) {\n  var styles = _ref.styles,\n    props = _objectWithoutProperties(_ref, _excluded$1);\n  var color = styles.color,\n    height = styles.height,\n    width = styles.width,\n    style = _objectWithoutProperties(styles, _excluded2);\n  return /*#__PURE__*/React.createElement(\"button\", _extends({\n    style: style,\n    type: \"button\"\n  }, props), /*#__PURE__*/React.createElement(\"svg\", {\n    width: typeof width === 'number' ? \"\".concat(width, \"px\") : width,\n    height: typeof height === 'number' ? \"\".concat(height, \"px\") : height,\n    viewBox: \"0 0 18 18\",\n    version: \"1.1\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    preserveAspectRatio: \"xMidYMid\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",\n    fill: color\n  }))));\n};\nvar JoyrideTooltipContainer = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideTooltipContainer, _React$Component);\n  var _super = _createSuper(JoyrideTooltipContainer);\n  function JoyrideTooltipContainer() {\n    _classCallCheck(this, JoyrideTooltipContainer);\n    return _super.apply(this, arguments);\n  }\n  _createClass(JoyrideTooltipContainer, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        backProps = _this$props.backProps,\n        closeProps = _this$props.closeProps,\n        continuous = _this$props.continuous,\n        index = _this$props.index,\n        isLastStep = _this$props.isLastStep,\n        primaryProps = _this$props.primaryProps,\n        size = _this$props.size,\n        skipProps = _this$props.skipProps,\n        step = _this$props.step,\n        tooltipProps = _this$props.tooltipProps;\n      var content = step.content,\n        hideBackButton = step.hideBackButton,\n        hideCloseButton = step.hideCloseButton,\n        hideFooter = step.hideFooter,\n        showProgress = step.showProgress,\n        showSkipButton = step.showSkipButton,\n        title = step.title,\n        styles = step.styles;\n      var _step$locale = step.locale,\n        back = _step$locale.back,\n        close = _step$locale.close,\n        last = _step$locale.last,\n        next = _step$locale.next,\n        skip = _step$locale.skip;\n      var output = {\n        primary: close\n      };\n      if (continuous) {\n        output.primary = isLastStep ? last : next;\n        if (showProgress) {\n          output.primary = /*#__PURE__*/React.createElement(\"span\", null, output.primary, \" (\", index + 1, \"/\", size, \")\");\n        }\n      }\n      if (showSkipButton && !isLastStep) {\n        output.skip = /*#__PURE__*/React.createElement(\"button\", _extends({\n          style: styles.buttonSkip,\n          type: \"button\",\n          \"aria-live\": \"off\"\n        }, skipProps), skip);\n      }\n      if (!hideBackButton && index > 0) {\n        output.back = /*#__PURE__*/React.createElement(\"button\", _extends({\n          style: styles.buttonBack,\n          type: \"button\"\n        }, backProps), back);\n      }\n      output.close = !hideCloseButton && /*#__PURE__*/React.createElement(JoyrideTooltipCloseBtn, _extends({\n        styles: styles.buttonClose\n      }, closeProps));\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        key: \"JoyrideTooltip\",\n        className: \"react-joyride__tooltip\",\n        style: styles.tooltip\n      }, tooltipProps), /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipContainer\n      }, title && /*#__PURE__*/React.createElement(\"h4\", {\n        style: styles.tooltipTitle,\n        \"aria-label\": title\n      }, title), /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipContent\n      }, content)), !hideFooter && /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipFooter\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipFooterSpacer\n      }, output.skip), output.back, /*#__PURE__*/React.createElement(\"button\", _extends({\n        style: styles.buttonNext,\n        type: \"button\"\n      }, primaryProps), output.primary)), output.close);\n    }\n  }]);\n  return JoyrideTooltipContainer;\n}(React.Component);\nvar _excluded = [\"beaconComponent\", \"tooltipComponent\"];\nvar JoyrideTooltip = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideTooltip, _React$Component);\n  var _super = _createSuper(JoyrideTooltip);\n  function JoyrideTooltip() {\n    var _this;\n    _classCallCheck(this, JoyrideTooltip);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"handleClickBack\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.prev();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClickClose\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.close();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClickPrimary\", function (e) {\n      e.preventDefault();\n      var _this$props = _this.props,\n        continuous = _this$props.continuous,\n        helpers = _this$props.helpers;\n      if (!continuous) {\n        helpers.close();\n        return;\n      }\n      helpers.next();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClickSkip\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.skip();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getElementsProps\", function () {\n      var _this$props2 = _this.props,\n        continuous = _this$props2.continuous,\n        isLastStep = _this$props2.isLastStep,\n        setTooltipRef = _this$props2.setTooltipRef,\n        step = _this$props2.step;\n      var back = getText(step.locale.back);\n      var close = getText(step.locale.close);\n      var last = getText(step.locale.last);\n      var next = getText(step.locale.next);\n      var skip = getText(step.locale.skip);\n      var primaryText = continuous ? next : close;\n      if (isLastStep) {\n        primaryText = last;\n      }\n      return {\n        backProps: {\n          'aria-label': back,\n          'data-action': 'back',\n          onClick: _this.handleClickBack,\n          role: 'button',\n          title: back\n        },\n        closeProps: {\n          'aria-label': close,\n          'data-action': 'close',\n          onClick: _this.handleClickClose,\n          role: 'button',\n          title: close\n        },\n        primaryProps: {\n          'aria-label': primaryText,\n          'data-action': 'primary',\n          onClick: _this.handleClickPrimary,\n          role: 'button',\n          title: primaryText\n        },\n        skipProps: {\n          'aria-label': skip,\n          'data-action': 'skip',\n          onClick: _this.handleClickSkip,\n          role: 'button',\n          title: skip\n        },\n        tooltipProps: {\n          'aria-modal': true,\n          ref: setTooltipRef,\n          role: 'alertdialog'\n        }\n      };\n    });\n    return _this;\n  }\n  _createClass(JoyrideTooltip, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props3 = this.props,\n        continuous = _this$props3.continuous,\n        index = _this$props3.index,\n        isLastStep = _this$props3.isLastStep,\n        size = _this$props3.size,\n        step = _this$props3.step;\n      step.beaconComponent;\n      var tooltipComponent = step.tooltipComponent,\n        cleanStep = _objectWithoutProperties(step, _excluded);\n      var component;\n      if (tooltipComponent) {\n        var renderProps = _objectSpread2(_objectSpread2({}, this.getElementsProps()), {}, {\n          continuous: continuous,\n          index: index,\n          isLastStep: isLastStep,\n          size: size,\n          step: cleanStep\n        });\n        var TooltipComponent = tooltipComponent;\n        component = /*#__PURE__*/React.createElement(TooltipComponent, renderProps);\n      } else {\n        component = /*#__PURE__*/React.createElement(JoyrideTooltipContainer, _extends({}, this.getElementsProps(), {\n          continuous: continuous,\n          index: index,\n          isLastStep: isLastStep,\n          size: size,\n          step: step\n        }));\n      }\n      return component;\n    }\n  }]);\n  return JoyrideTooltip;\n}(React.Component);\nvar JoyridePortal = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyridePortal, _React$Component);\n  var _super = _createSuper(JoyridePortal);\n  function JoyridePortal(props) {\n    var _this;\n    _classCallCheck(this, JoyridePortal);\n    _this = _super.call(this, props);\n    if (!canUseDOM) return _possibleConstructorReturn(_this);\n    _this.node = document.createElement('div');\n    /* istanbul ignore else */\n\n    if (props.id) {\n      _this.node.id = props.id;\n    }\n    document.body.appendChild(_this.node);\n    return _this;\n  }\n  _createClass(JoyridePortal, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n      if (!isReact16) {\n        this.renderReact15();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!canUseDOM) return;\n      if (!isReact16) {\n        this.renderReact15();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (!canUseDOM || !this.node) return;\n      if (!isReact16) {\n        ReactDOM.unmountComponentAtNode(this.node);\n      }\n      document.body.removeChild(this.node);\n    }\n  }, {\n    key: \"renderReact15\",\n    value: function renderReact15() {\n      if (!canUseDOM) return null;\n      var children = this.props.children;\n      ReactDOM.unstable_renderSubtreeIntoContainer(this, children, this.node);\n      return null;\n    }\n  }, {\n    key: \"renderReact16\",\n    value: function renderReact16() {\n      if (!canUseDOM || !isReact16) return null;\n      var children = this.props.children;\n      return /*#__PURE__*/ReactDOM.createPortal(children, this.node);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!isReact16) {\n        return null;\n      }\n      return this.renderReact16();\n    }\n  }]);\n  return JoyridePortal;\n}(React.Component);\nvar JoyrideStep = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideStep, _React$Component);\n  var _super = _createSuper(JoyrideStep);\n  function JoyrideStep() {\n    var _this;\n    _classCallCheck(this, JoyrideStep);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"scope\", {\n      removeScope: function removeScope() {}\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClickHoverBeacon\", function (e) {\n      var _this$props = _this.props,\n        step = _this$props.step,\n        update = _this$props.update;\n      if (e.type === 'mouseenter' && step.event !== 'hover') {\n        return;\n      }\n      update({\n        lifecycle: LIFECYCLE.TOOLTIP\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleClickOverlay\", function () {\n      var _this$props2 = _this.props,\n        helpers = _this$props2.helpers,\n        step = _this$props2.step;\n      if (!step.disableOverlayClose) {\n        helpers.close();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setTooltipRef\", function (c) {\n      _this.tooltip = c;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setPopper\", function (popper, type) {\n      var _this$props3 = _this.props,\n        action = _this$props3.action,\n        setPopper = _this$props3.setPopper,\n        update = _this$props3.update;\n      if (type === 'wrapper') {\n        _this.beaconPopper = popper;\n      } else {\n        _this.tooltipPopper = popper;\n      }\n      setPopper(popper, type);\n      if (_this.beaconPopper && _this.tooltipPopper) {\n        update({\n          action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : action,\n          lifecycle: LIFECYCLE.READY\n        });\n      }\n    });\n    return _this;\n  }\n  _createClass(JoyrideStep, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props4 = this.props,\n        debug = _this$props4.debug,\n        index = _this$props4.index;\n      log({\n        title: \"step:\".concat(index),\n        data: [{\n          key: 'props',\n          value: this.props\n        }],\n        debug: debug\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props5 = this.props,\n        action = _this$props5.action,\n        callback = _this$props5.callback,\n        continuous = _this$props5.continuous,\n        controlled = _this$props5.controlled,\n        debug = _this$props5.debug,\n        index = _this$props5.index,\n        lifecycle = _this$props5.lifecycle,\n        size = _this$props5.size,\n        status = _this$props5.status,\n        step = _this$props5.step,\n        update = _this$props5.update;\n      var _treeChanges = treeChanges(prevProps, this.props),\n        changed = _treeChanges.changed,\n        changedFrom = _treeChanges.changedFrom;\n      var state = {\n        action: action,\n        controlled: controlled,\n        index: index,\n        lifecycle: lifecycle,\n        size: size,\n        status: status\n      };\n      var skipBeacon = continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV);\n      var hasStoreChanged = changed('action') || changed('index') || changed('lifecycle') || changed('status');\n      var hasStarted = changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT);\n      var isAfterAction = changed('action', [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]);\n      if (isAfterAction && (hasStarted || controlled)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          index: prevProps.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: prevProps.step,\n          type: EVENTS.STEP_AFTER\n        }));\n      }\n      if (changed('index') && index > 0 && lifecycle === LIFECYCLE.INIT && status === STATUS.RUNNING && step.placement === 'center') {\n        update({\n          lifecycle: LIFECYCLE.READY\n        });\n      } // There's a step to use, but there's no target in the DOM\n\n      if (hasStoreChanged && step) {\n        var element = getElement(step.target);\n        var elementExists = !!element;\n        var hasRenderedTarget = elementExists && isElementVisible(element);\n        if (hasRenderedTarget) {\n          if (changedFrom('status', STATUS.READY, STATUS.RUNNING) || changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n            callback(_objectSpread2(_objectSpread2({}, state), {}, {\n              step: step,\n              type: EVENTS.STEP_BEFORE\n            }));\n          }\n        } else {\n          console.warn(elementExists ? 'Target not visible' : 'Target not mounted', step); //eslint-disable-line no-console\n\n          callback(_objectSpread2(_objectSpread2({}, state), {}, {\n            type: EVENTS.TARGET_NOT_FOUND,\n            step: step\n          }));\n          if (!controlled) {\n            update({\n              index: index + ([ACTIONS.PREV].indexOf(action) !== -1 ? -1 : 1)\n            });\n          }\n        }\n      }\n      if (changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n        update({\n          lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON\n        });\n      }\n      if (changed('index')) {\n        log({\n          title: \"step:\".concat(lifecycle),\n          data: [{\n            key: 'props',\n            value: this.props\n          }],\n          debug: debug\n        });\n      }\n      /* istanbul ignore else */\n\n      if (changed('lifecycle', LIFECYCLE.BEACON)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          step: step,\n          type: EVENTS.BEACON\n        }));\n      }\n      if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          step: step,\n          type: EVENTS.TOOLTIP\n        }));\n        this.scope = new Scope(this.tooltip, {\n          selector: '[data-action=primary]'\n        });\n        this.scope.setFocus();\n      }\n      if (changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT)) {\n        this.scope.removeScope();\n        delete this.beaconPopper;\n        delete this.tooltipPopper;\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.scope.removeScope();\n    }\n    /**\n     * Beacon click/hover event listener\n     *\n     * @param {Event} e\n     */\n  }, {\n    key: \"open\",\n    get: function get() {\n      var _this$props6 = this.props,\n        step = _this$props6.step,\n        lifecycle = _this$props6.lifecycle;\n      return !!(hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props7 = this.props,\n        continuous = _this$props7.continuous,\n        debug = _this$props7.debug,\n        helpers = _this$props7.helpers,\n        index = _this$props7.index,\n        lifecycle = _this$props7.lifecycle,\n        nonce = _this$props7.nonce,\n        shouldScroll = _this$props7.shouldScroll,\n        size = _this$props7.size,\n        step = _this$props7.step;\n      var target = getElement(step.target);\n      if (!validateStep(step) || !is.domElement(target)) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: \"JoyrideStep-\".concat(index),\n        className: \"react-joyride__step\"\n      }, /*#__PURE__*/React.createElement(JoyridePortal, {\n        id: \"react-joyride-portal\"\n      }, /*#__PURE__*/React.createElement(JoyrideOverlay, _extends({}, step, {\n        debug: debug,\n        lifecycle: lifecycle,\n        onClickOverlay: this.handleClickOverlay\n      }))), /*#__PURE__*/React.createElement(Floater, _extends({\n        component: /*#__PURE__*/React.createElement(JoyrideTooltip, {\n          continuous: continuous,\n          helpers: helpers,\n          index: index,\n          isLastStep: index + 1 === size,\n          setTooltipRef: this.setTooltipRef,\n          size: size,\n          step: step\n        }),\n        debug: debug,\n        getPopper: this.setPopper,\n        id: \"react-joyride-step-\".concat(index),\n        isPositioned: step.isFixed || hasPosition(target),\n        open: this.open,\n        placement: step.placement,\n        target: step.target\n      }, step.floaterProps), /*#__PURE__*/React.createElement(JoyrideBeacon, {\n        beaconComponent: step.beaconComponent,\n        locale: step.locale,\n        nonce: nonce,\n        onClickOrHover: this.handleClickHoverBeacon,\n        shouldFocus: shouldScroll,\n        styles: step.styles\n      })));\n    }\n  }]);\n  return JoyrideStep;\n}(React.Component);\nvar Joyride = /*#__PURE__*/function (_React$Component) {\n  _inherits(Joyride, _React$Component);\n  var _super = _createSuper(Joyride);\n  function Joyride(props) {\n    var _this;\n    _classCallCheck(this, Joyride);\n    _this = _super.call(this, props);\n    _defineProperty(_assertThisInitialized(_this), \"initStore\", function () {\n      var _this$props = _this.props,\n        debug = _this$props.debug,\n        getHelpers = _this$props.getHelpers,\n        run = _this$props.run,\n        stepIndex = _this$props.stepIndex;\n      _this.store = new createStore(_objectSpread2(_objectSpread2({}, _this.props), {}, {\n        controlled: run && is.number(stepIndex)\n      }));\n      _this.helpers = _this.store.getHelpers();\n      var addListener = _this.store.addListener;\n      log({\n        title: 'init',\n        data: [{\n          key: 'props',\n          value: _this.props\n        }, {\n          key: 'state',\n          value: _this.state\n        }],\n        debug: debug\n      }); // Sync the store to this component's state.\n\n      addListener(_this.syncState);\n      getHelpers(_this.helpers);\n      return _this.store.getState();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"callback\", function (data) {\n      var callback = _this.props.callback;\n      /* istanbul ignore else */\n\n      if (is[\"function\"](callback)) {\n        callback(data);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleKeyboard\", function (e) {\n      var _this$state = _this.state,\n        index = _this$state.index,\n        lifecycle = _this$state.lifecycle;\n      var steps = _this.props.steps;\n      var step = steps[index];\n      var intKey = window.Event ? e.which : e.keyCode;\n      if (lifecycle === LIFECYCLE.TOOLTIP) {\n        if (intKey === 27 && step && !step.disableCloseOnEsc) {\n          _this.store.close();\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"syncState\", function (state) {\n      _this.setState(state);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"setPopper\", function (popper, type) {\n      if (type === 'wrapper') {\n        _this.beaconPopper = popper;\n      } else {\n        _this.tooltipPopper = popper;\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"shouldScroll\", function (disableScrolling, index, scrollToFirstStep, lifecycle, step, target, prevState) {\n      return !disableScrolling && (index !== 0 || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) && step.placement !== 'center' && (!step.isFixed || !hasPosition(target)) &&\n      // fixed steps don't need to scroll\n      prevState.lifecycle !== lifecycle && [LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP].indexOf(lifecycle) !== -1;\n    });\n    _this.state = _this.initStore();\n    return _this;\n  }\n  _createClass(Joyride, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n      var _this$props2 = this.props,\n        disableCloseOnEsc = _this$props2.disableCloseOnEsc,\n        debug = _this$props2.debug,\n        run = _this$props2.run,\n        steps = _this$props2.steps;\n      var start = this.store.start;\n      if (validateSteps(steps, debug) && run) {\n        start();\n      }\n      /* istanbul ignore else */\n\n      if (!disableCloseOnEsc) {\n        document.body.addEventListener('keydown', this.handleKeyboard, {\n          passive: true\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (!canUseDOM) return;\n      var _this$state2 = this.state,\n        action = _this$state2.action,\n        controlled = _this$state2.controlled,\n        index = _this$state2.index,\n        lifecycle = _this$state2.lifecycle,\n        status = _this$state2.status;\n      var _this$props3 = this.props,\n        debug = _this$props3.debug,\n        run = _this$props3.run,\n        stepIndex = _this$props3.stepIndex,\n        steps = _this$props3.steps;\n      var prevSteps = prevProps.steps,\n        prevStepIndex = prevProps.stepIndex;\n      var _this$store = this.store,\n        reset = _this$store.reset,\n        setSteps = _this$store.setSteps,\n        start = _this$store.start,\n        stop = _this$store.stop,\n        update = _this$store.update;\n      var _treeChanges = treeChanges(prevProps, this.props),\n        changedProps = _treeChanges.changed;\n      var _treeChanges2 = treeChanges(prevState, this.state),\n        changed = _treeChanges2.changed,\n        changedFrom = _treeChanges2.changedFrom;\n      var step = getMergedStep(steps[index], this.props);\n      var stepsChanged = !isEqual(prevSteps, steps);\n      var stepIndexChanged = is.number(stepIndex) && changedProps('stepIndex');\n      var target = getElement(step === null || step === void 0 ? void 0 : step.target);\n      if (stepsChanged) {\n        if (validateSteps(steps, debug)) {\n          setSteps(steps);\n        } else {\n          console.warn('Steps are not valid', steps); //eslint-disable-line no-console\n        }\n      }\n      /* istanbul ignore else */\n\n      if (changedProps('run')) {\n        if (run) {\n          start(stepIndex);\n        } else {\n          stop();\n        }\n      }\n      /* istanbul ignore else */\n\n      if (stepIndexChanged) {\n        var nextAction = prevStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;\n        if (action === ACTIONS.STOP) {\n          nextAction = ACTIONS.START;\n        }\n        if (!([STATUS.FINISHED, STATUS.SKIPPED].indexOf(status) !== -1)) {\n          update({\n            action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction,\n            index: stepIndex,\n            lifecycle: LIFECYCLE.INIT\n          });\n        }\n      } // Update the index if the first step is not found\n\n      if (!controlled && status === STATUS.RUNNING && index === 0 && !target) {\n        update({\n          index: index + 1\n        });\n        this.callback(_objectSpread2(_objectSpread2({}, this.state), {}, {\n          type: EVENTS.TARGET_NOT_FOUND,\n          step: step\n        }));\n      }\n      var callbackData = _objectSpread2(_objectSpread2({}, this.state), {}, {\n        index: index,\n        step: step\n      });\n      var isAfterAction = changed('action', [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]);\n      if (isAfterAction && changed('status', STATUS.PAUSED)) {\n        var prevStep = getMergedStep(steps[prevState.index], this.props);\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          index: prevState.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: prevStep,\n          type: EVENTS.STEP_AFTER\n        }));\n      }\n      if (changed('status', [STATUS.FINISHED, STATUS.SKIPPED])) {\n        var _prevStep = getMergedStep(steps[prevState.index], this.props);\n        if (!controlled) {\n          this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n            index: prevState.index,\n            lifecycle: LIFECYCLE.COMPLETE,\n            step: _prevStep,\n            type: EVENTS.STEP_AFTER\n          }));\n        }\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          index: prevState.index,\n          // Return the last step when the tour is finished\n          step: _prevStep,\n          type: EVENTS.TOUR_END\n        }));\n        reset();\n      } else if (changedFrom('status', [STATUS.IDLE, STATUS.READY], STATUS.RUNNING)) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_START\n        }));\n      } else if (changed('status')) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_STATUS\n        }));\n      } else if (changed('action', ACTIONS.RESET)) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_STATUS\n        }));\n      }\n      if (step) {\n        this.scrollToStep(prevState);\n        if (action === ACTIONS.START && lifecycle === LIFECYCLE.INIT && status === STATUS.RUNNING && step.placement === 'center') {\n          update({\n            lifecycle: LIFECYCLE.READY\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var disableCloseOnEsc = this.props.disableCloseOnEsc;\n      /* istanbul ignore else */\n\n      if (!disableCloseOnEsc) {\n        document.body.removeEventListener('keydown', this.handleKeyboard);\n      }\n    }\n  }, {\n    key: \"scrollToStep\",\n    value: function scrollToStep(prevState) {\n      var _this$state3 = this.state,\n        index = _this$state3.index,\n        lifecycle = _this$state3.lifecycle,\n        status = _this$state3.status;\n      var _this$props4 = this.props,\n        debug = _this$props4.debug,\n        disableScrolling = _this$props4.disableScrolling,\n        disableScrollParentFix = _this$props4.disableScrollParentFix,\n        scrollToFirstStep = _this$props4.scrollToFirstStep,\n        scrollOffset = _this$props4.scrollOffset,\n        scrollDuration = _this$props4.scrollDuration,\n        steps = _this$props4.steps;\n      var step = getMergedStep(steps[index], this.props);\n      /* istanbul ignore else */\n\n      if (step) {\n        var target = getElement(step.target);\n        var shouldScroll = this.shouldScroll(disableScrolling, index, scrollToFirstStep, lifecycle, step, target, prevState);\n        if (status === STATUS.RUNNING && shouldScroll) {\n          var hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix);\n          var scrollParent = getScrollParent(target, disableScrollParentFix);\n          var scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;\n          log({\n            title: 'scrollToStep',\n            data: [{\n              key: 'index',\n              value: index\n            }, {\n              key: 'lifecycle',\n              value: lifecycle\n            }, {\n              key: 'status',\n              value: status\n            }],\n            debug: debug\n          });\n          /* istanbul ignore else */\n\n          if (lifecycle === LIFECYCLE.BEACON && this.beaconPopper) {\n            var _this$beaconPopper = this.beaconPopper,\n              placement = _this$beaconPopper.placement,\n              popper = _this$beaconPopper.popper;\n            /* istanbul ignore else */\n\n            if (!(['bottom'].indexOf(placement) !== -1) && !hasCustomScroll) {\n              scrollY = Math.floor(popper.top - scrollOffset);\n            }\n          } else if (lifecycle === LIFECYCLE.TOOLTIP && this.tooltipPopper) {\n            var _this$tooltipPopper = this.tooltipPopper,\n              flipped = _this$tooltipPopper.flipped,\n              _placement = _this$tooltipPopper.placement,\n              _popper = _this$tooltipPopper.popper;\n            if (['top', 'right', 'left'].indexOf(_placement) !== -1 && !flipped && !hasCustomScroll) {\n              scrollY = Math.floor(_popper.top - scrollOffset);\n            } else {\n              scrollY -= step.spotlightPadding;\n            }\n          }\n          scrollY = scrollY >= 0 ? scrollY : 0;\n          /* istanbul ignore else */\n\n          if (status === STATUS.RUNNING) {\n            scrollTo(scrollY, scrollParent, scrollDuration);\n          }\n        }\n      }\n    }\n    /**\n     * Trigger the callback.\n     *\n     * @private\n     * @param {Object} data\n     */\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!canUseDOM) return null;\n      var _this$state4 = this.state,\n        index = _this$state4.index,\n        status = _this$state4.status;\n      var _this$props5 = this.props,\n        continuous = _this$props5.continuous,\n        debug = _this$props5.debug,\n        nonce = _this$props5.nonce,\n        scrollToFirstStep = _this$props5.scrollToFirstStep,\n        steps = _this$props5.steps;\n      var step = getMergedStep(steps[index], this.props);\n      var output;\n      if (status === STATUS.RUNNING && step) {\n        output = /*#__PURE__*/React.createElement(JoyrideStep, _extends({}, this.state, {\n          callback: this.callback,\n          continuous: continuous,\n          debug: debug,\n          setPopper: this.setPopper,\n          helpers: this.helpers,\n          nonce: nonce,\n          shouldScroll: !step.disableScrolling && (index !== 0 || scrollToFirstStep),\n          step: step,\n          update: this.store.update\n        }));\n      }\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"react-joyride\"\n      }, output);\n    }\n  }]);\n  return Joyride;\n}(React.Component);\n_defineProperty(Joyride, \"defaultProps\", {\n  continuous: false,\n  debug: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrolling: false,\n  disableScrollParentFix: false,\n  getHelpers: function getHelpers() {},\n  hideBackButton: false,\n  run: true,\n  scrollOffset: 20,\n  scrollDuration: 300,\n  scrollToFirstStep: false,\n  showSkipButton: false,\n  showProgress: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n  steps: []\n});\nexport { ACTIONS, EVENTS, LIFECYCLE, STATUS, Joyride as default };", "map": {"version": 3, "names": ["React", "isValidElement", "treeChanges", "is", "ReactDOM", "createPortal", "ExecutionEnvironment", "scroll", "scrollParent", "isValidElementType", "Element", "ForwardRef", "typeOf", "deepmerge", "Floater", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_createClass", "protoProps", "staticProps", "prototype", "obj", "value", "_extends", "assign", "hasOwnProperty", "call", "_inherits", "subClass", "superClass", "create", "constructor", "_setPrototypeOf", "_getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "p", "_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "_assertThisInitialized", "self", "ReferenceError", "_possibleConstructorReturn", "_createSuper", "Derived", "hasNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "ACTIONS", "INIT", "START", "STOP", "RESET", "PREV", "NEXT", "GO", "CLOSE", "SKIP", "UPDATE", "EVENTS", "TOUR_START", "STEP_BEFORE", "BEACON", "TOOLTIP", "STEP_AFTER", "TOUR_END", "TOUR_STATUS", "TARGET_NOT_FOUND", "ERROR", "LIFECYCLE", "READY", "COMPLETE", "STATUS", "IDLE", "WAITING", "RUNNING", "PAUSED", "SKIPPED", "FINISHED", "canUseDOM", "isReact16", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "navigator", "browser", "window", "document", "documentMode", "test", "opera", "InstallTrigger", "chrome", "getObjectType", "toString", "slice", "toLowerCase", "getText", "root", "content", "recurse", "child", "Array", "isArray", "c", "children", "join", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plainObject", "array", "every", "d", "hexToRGB", "hex", "shorthandRegex", "properHex", "replace", "m", "r", "g", "b", "exec", "parseInt", "hideBeacon", "step", "disableBea<PERSON>", "placement", "isEqual", "left", "right", "type", "hasReactElement", "hasUndefined", "dom<PERSON>lement", "isSameNode", "number", "isLegacy", "log", "_ref", "title", "data", "_ref$warn", "warn", "_ref$debug", "debug", "logFn", "console", "error", "groupCollapsed", "concat", "groupEnd", "defaultState", "action", "controlled", "index", "lifecycle", "size", "status", "validKeys", "createStore", "store", "Map", "Store", "_this", "_ref$continuous", "continuous", "stepIndex", "_ref$steps", "steps", "_steps", "_this$getState", "getState", "state", "set", "setState", "listener", "Error", "getNextState", "nextIndex", "_this$getState2", "advance", "_this$getState3", "_this$getState4", "_this$getState5", "getSteps", "_this$getState6", "_this$getState7", "_this$getState8", "restart", "_this$getState9", "_this$getState10", "setSteps", "nextState", "initial", "_state$nextState", "hasUpdatedState", "get", "force", "_this$getState11", "newIndex", "Math", "min", "max", "oldState", "before", "JSON", "stringify", "after", "getHelpers", "close", "go", "info", "next", "open", "prev", "reset", "skip", "scrollDoc", "scrollingElement", "createElement", "getClientRect", "element", "getBoundingClientRect", "getDocumentHeight", "_document", "body", "html", "documentElement", "scrollHeight", "offsetHeight", "clientHeight", "getElement", "querySelector", "getStyleComputedProperty", "el", "nodeType", "getComputedStyle", "getScrollParent", "skipFix", "forListener", "parent", "hasScrolling", "style", "overflow", "hasCustomScrollParent", "hasCustomOffsetParent", "offsetParent", "hasPosition", "HTMLElement", "nodeName", "position", "parentNode", "isElementVisible", "parentElement", "_getComputedStyle", "display", "visibility", "getElementPosition", "offset", "elementRect", "hasScrollParent", "parentTop", "scrollTop", "top", "floor", "getTopOffset", "offsetTop", "getScrollTo", "scrollTo", "scrollDuration", "Promise", "resolve", "reject", "limit", "duration", "message", "createChainableTypeChecker", "validate", "checkType", "isRequired", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "componentNameSafe", "propFullNameSafe", "_len", "args", "_key", "chainedCheckType", "bind", "propValue", "Component", "ownProps", "ref", "string", "defaultOptions", "arrowColor", "backgroundColor", "beaconSize", "overlayColor", "primaryColor", "spotlightShadow", "textColor", "zIndex", "buttonBase", "border", "borderRadius", "color", "cursor", "fontSize", "lineHeight", "padding", "WebkitAppearance", "spotlight", "getStyles", "stepStyles", "options", "width", "innerWidth", "overlay", "bottom", "defaultStyles", "beacon", "height", "beaconInner", "animation", "opacity", "transform", "beaconOuter", "boxSizing", "transform<PERSON><PERSON>in", "tooltip", "max<PERSON><PERSON><PERSON>", "tooltipContainer", "textAlign", "tooltipTitle", "margin", "tooltipContent", "tooltipFooter", "alignItems", "justifyContent", "marginTop", "tooltipFooterSpacer", "flex", "buttonNext", "buttonBack", "marginLeft", "marginRight", "buttonClose", "buttonSkip", "mixBlendMode", "overlayLegacy", "overlayLegacyCenter", "spotlightLegacy", "boxShadow", "floaterStyles", "arrow", "DEFAULTS", "floaterProps", "preventOverflow", "boundariesElement", "wrapperOptions", "locale", "back", "last", "event", "getTourProps", "sharedTourProps", "reduce", "acc", "getMergedStep", "mergedStep", "all", "isMergeableObject", "mergedStyles", "styles", "disableScrollParentFix", "spotlightPadding", "placementBeacon", "validateStep", "validateSteps", "<PERSON><PERSON>", "_element", "tabIndex", "isTabIndexNaN", "isNaN", "canHaveFocus", "validTabNodes", "res", "getAttribute", "isVisible", "querySelectorAll", "canBeTabbed", "_this$options$keyCode", "keyCode", "interceptTab", "elements", "findValidTabElements", "preventDefault", "shift<PERSON>ey", "x", "activeElement", "focus", "noSize", "offsetWidth", "innerHTML", "getPropertyValue", "isHidden", "removeEventListener", "handleKeyDown", "requestAnimationFrame", "checkFocus", "selector", "addEventListener", "setFocus", "JoyrideBeacon", "_React$Component", "_super", "beaconComponent", "head", "getElementsByTagName", "css", "id", "nonce", "setAttribute", "append<PERSON><PERSON><PERSON>", "createTextNode", "componentDidMount", "_this2", "shouldFocus", "setTimeout", "componentWillUnmount", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "render", "_this$props", "onClickOrHover", "onClick", "onMouseEnter", "setBeaconRef", "component", "BeaconComponent", "className", "JoyrideSpotlight", "_excluded$2", "JoyrideOverlay", "mouseOverSpotlight", "isScrolling", "showSpotlight", "_this$spotlightStyles", "spotlightStyles", "offsetY", "clientY", "pageY", "offsetX", "clientX", "pageX", "inSpotlightHeight", "inSpotlightWidth", "inSpotlight", "updateState", "clearTimeout", "scrollTimeout", "resizeTimeout", "_isMounted", "forceUpdate", "disableScrolling", "handleResize", "componentDidUpdate", "prevProps", "_this$props2", "spotlightClicks", "_treeChanges", "changed", "handleScroll", "passive", "handleMouseMove", "_this$props3", "isFixedTarget", "round", "pointerEvents", "transition", "_this$state", "_this$props4", "disable<PERSON><PERSON><PERSON>", "disableOverlayClose", "onClickOverlay", "baseStyles", "stylesOverlay", "safarOverlay", "_excluded$1", "_excluded2", "JoyrideTooltipCloseBtn", "viewBox", "version", "xmlns", "preserveAspectRatio", "fill", "JoyrideTooltipContainer", "backProps", "closeProps", "isLastStep", "primaryProps", "skipProps", "tooltipProps", "hideBackButton", "hideClose<PERSON><PERSON>on", "hideFooter", "showProgress", "showSkipButton", "_step$locale", "output", "primary", "_excluded", "JoyrideTooltip", "helpers", "setTooltipRef", "primaryText", "handleClickBack", "role", "handleClickClose", "handleClickPrimary", "handleClickSkip", "tooltipComponent", "cleanStep", "renderProps", "getElementsProps", "TooltipComponent", "JoyridePortal", "node", "renderReact15", "unmountComponentAtNode", "unstable_renderSubtreeIntoContainer", "renderReact16", "JoyrideStep", "removeScope", "update", "popper", "setPopper", "beaconPopper", "tooltipPopper", "_this$props5", "callback", "changedFrom", "skipBeacon", "hasStoreChanged", "hasStarted", "isAfterAction", "elementExists", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scope", "_this$props6", "_this$props7", "shouldScroll", "handleClickOverlay", "getPopper", "isPositioned", "isFixed", "handleClickHoverBeacon", "Joyride", "run", "addListener", "syncState", "intKey", "Event", "which", "disableCloseOnEsc", "scrollToFirstStep", "prevState", "initStore", "start", "handleKeyboard", "_this$state2", "prevSteps", "prevStepIndex", "_this$store", "stop", "changedProps", "_treeChanges2", "stepsChanged", "stepIndexChanged", "nextAction", "callbackData", "prevStep", "_prevStep", "scrollToStep", "_this$state3", "scrollOffset", "hasCustomScroll", "scrollY", "_this$beaconPopper", "_this$tooltipPopper", "flipped", "_placement", "_popper", "_this$state4", "default"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/react-joyride/es/index.js"], "sourcesContent": ["import React, { isValidElement } from 'react';\nimport 'prop-types';\nimport treeChanges from 'tree-changes';\nimport is from 'is-lite';\nimport ReactDOM, { createPortal } from 'react-dom';\nimport ExecutionEnvironment from 'exenv';\nimport scroll from 'scroll';\nimport scrollParent from 'scrollparent';\nimport { isValidElementType, Element, ForwardRef, typeOf } from 'react-is';\nimport deepmerge from 'deepmerge';\nimport Floater from 'react-floater';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar ACTIONS = {\n  INIT: 'init',\n  START: 'start',\n  STOP: 'stop',\n  RESET: 'reset',\n  PREV: 'prev',\n  NEXT: 'next',\n  GO: 'go',\n  CLOSE: 'close',\n  SKIP: 'skip',\n  UPDATE: 'update'\n};\n\nvar EVENTS = {\n  TOUR_START: 'tour:start',\n  STEP_BEFORE: 'step:before',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  STEP_AFTER: 'step:after',\n  TOUR_END: 'tour:end',\n  TOUR_STATUS: 'tour:status',\n  TARGET_NOT_FOUND: 'error:target_not_found',\n  ERROR: 'error'\n};\n\nvar LIFECYCLE = {\n  INIT: 'init',\n  READY: 'ready',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  COMPLETE: 'complete',\n  ERROR: 'error'\n};\n\nvar STATUS = {\n  IDLE: 'idle',\n  READY: 'ready',\n  WAITING: 'waiting',\n  RUNNING: 'running',\n  PAUSED: 'paused',\n  SKIPPED: 'skipped',\n  FINISHED: 'finished',\n  ERROR: 'error'\n};\n\nvar canUseDOM = ExecutionEnvironment.canUseDOM;\nvar isReact16 = createPortal !== undefined;\n/**\n * Get the current browser\n *\n * @param {string} userAgent\n *\n * @returns {String}\n */\n\nfunction getBrowser() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : navigator.userAgent;\n  var browser = userAgent;\n\n  if (typeof window === 'undefined') {\n    browser = 'node';\n  } else if (document.documentMode) {\n    browser = 'ie';\n  } else if (/Edge/.test(userAgent)) {\n    browser = 'edge';\n  } // Opera 8.0+\n  else if (Boolean(window.opera) || userAgent.indexOf(' OPR/') >= 0) {\n    browser = 'opera';\n  } // Firefox 1.0+\n  else if (typeof window.InstallTrigger !== 'undefined') {\n    browser = 'firefox';\n  } // Chrome 1+\n  else if (window.chrome) {\n    browser = 'chrome';\n  } // Safari (and Chrome iOS, Firefox iOS)\n  else if (/(Version\\/([0-9._]+).*Safari|CriOS|FxiOS| Mobile\\/)/.test(userAgent)) {\n    browser = 'safari';\n  }\n\n  return browser;\n}\n/**\n * Get the toString Object type\n * @param {*} value\n * @returns {string}\n */\n\nfunction getObjectType(value) {\n  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();\n}\n/**\n * Get text from React components\n *\n * @param {*} root\n *\n * @returns {string}\n */\n\nfunction getText(root) {\n  var content = [];\n\n  var recurse = function recurse(child) {\n    /* istanbul ignore else */\n    if (typeof child === 'string' || typeof child === 'number') {\n      content.push(child);\n    } else if (Array.isArray(child)) {\n      child.forEach(function (c) {\n        return recurse(c);\n      });\n    } else if (child && child.props) {\n      var children = child.props.children;\n\n      if (Array.isArray(children)) {\n        children.forEach(function (c) {\n          return recurse(c);\n        });\n      } else {\n        recurse(children);\n      }\n    }\n  };\n\n  recurse(root);\n  return content.join(' ').trim();\n}\nfunction hasOwnProperty(value, key) {\n  return Object.prototype.hasOwnProperty.call(value, key);\n}\nfunction hasValidKeys(value, keys) {\n  if (!is.plainObject(value) || !is.array(keys)) {\n    return false;\n  }\n\n  return Object.keys(value).every(function (d) {\n    return keys.indexOf(d) !== -1;\n  });\n}\n/**\n * Convert hex to RGB\n *\n * @param {string} hex\n * @returns {Array}\n */\n\nfunction hexToRGB(hex) {\n  var shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n  var properHex = hex.replace(shorthandRegex, function (m, r, g, b) {\n    return r + r + g + g + b + b;\n  });\n  var result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(properHex);\n  return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];\n}\n/**\n * Decide if the step shouldn't skip the beacon\n * @param {Object} step\n *\n * @returns {boolean}\n */\n\nfunction hideBeacon(step) {\n  return step.disableBeacon || step.placement === 'center';\n}\n/**\n * Compare if two variables are equal\n *\n * @param {*} left\n * @param {*} right\n *\n * @returns {boolean}\n */\n\nfunction isEqual(left, right) {\n  var type;\n  var hasReactElement = /*#__PURE__*/isValidElement(left) || /*#__PURE__*/isValidElement(right);\n  var hasUndefined = is.undefined(left) || is.undefined(right);\n\n  if (getObjectType(left) !== getObjectType(right) || hasReactElement || hasUndefined) {\n    return false;\n  }\n\n  if (is.domElement(left)) {\n    return left.isSameNode(right);\n  }\n\n  if (is.number(left)) {\n    return left === right;\n  }\n\n  if (is[\"function\"](left)) {\n    return left.toString() === right.toString();\n  }\n\n  for (var key in left) {\n    /* istanbul ignore else */\n    if (hasOwnProperty(left, key)) {\n      if (typeof left[key] === 'undefined' || typeof right[key] === 'undefined') {\n        return false;\n      }\n\n      type = getObjectType(left[key]);\n\n      if (['object', 'array'].indexOf(type) !== -1 && isEqual(left[key], right[key])) {\n        continue;\n      }\n\n      if (type === 'function' && isEqual(left[key], right[key])) {\n        continue;\n      }\n\n      if (left[key] !== right[key]) {\n        return false;\n      }\n    }\n  }\n\n  for (var p in right) {\n    /* istanbul ignore else */\n    if (hasOwnProperty(right, p)) {\n      if (typeof left[p] === 'undefined') {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n/**\n * Detect legacy browsers\n *\n * @returns {boolean}\n */\n\nfunction isLegacy() {\n  return !(['chrome', 'safari', 'firefox', 'opera'].indexOf(getBrowser()) !== -1);\n}\n/**\n * Log method calls if debug is enabled\n *\n * @private\n * @param {Object}       arg\n * @param {string}       arg.title    - The title the logger was called from\n * @param {Object|Array} [arg.data]   - The data to be logged\n * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n */\n\nfunction log(_ref) {\n  var title = _ref.title,\n      data = _ref.data,\n      _ref$warn = _ref.warn,\n      warn = _ref$warn === void 0 ? false : _ref$warn,\n      _ref$debug = _ref.debug,\n      debug = _ref$debug === void 0 ? false : _ref$debug;\n\n  /* eslint-disable no-console */\n  var logFn = warn ? console.warn || console.error : console.log;\n\n  if (debug) {\n    if (title && data) {\n      console.groupCollapsed(\"%creact-joyride: \".concat(title), 'color: #ff0044; font-weight: bold; font-size: 12px;');\n\n      if (Array.isArray(data)) {\n        data.forEach(function (d) {\n          if (is.plainObject(d) && d.key) {\n            logFn.apply(console, [d.key, d.value]);\n          } else {\n            logFn.apply(console, [d]);\n          }\n        });\n      } else {\n        logFn.apply(console, [data]);\n      }\n\n      console.groupEnd();\n    } else {\n      console.error('Missing title or data props');\n    }\n  }\n  /* eslint-enable */\n\n}\n\nvar defaultState = {\n  action: '',\n  controlled: false,\n  index: 0,\n  lifecycle: LIFECYCLE.INIT,\n  size: 0,\n  status: STATUS.IDLE\n};\nvar validKeys = ['action', 'index', 'lifecycle', 'status'];\nfunction createStore(props) {\n  var store = new Map();\n  var data = new Map();\n\n  var Store = /*#__PURE__*/function () {\n    function Store() {\n      var _this = this;\n\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref$continuous = _ref.continuous,\n          continuous = _ref$continuous === void 0 ? false : _ref$continuous,\n          stepIndex = _ref.stepIndex,\n          _ref$steps = _ref.steps,\n          _steps = _ref$steps === void 0 ? [] : _ref$steps;\n\n      _classCallCheck(this, Store);\n\n      _defineProperty(this, \"listener\", void 0);\n\n      _defineProperty(this, \"setSteps\", function (steps) {\n        var _this$getState = _this.getState(),\n            size = _this$getState.size,\n            status = _this$getState.status;\n\n        var state = {\n          size: steps.length,\n          status: status\n        };\n        data.set('steps', steps);\n\n        if (status === STATUS.WAITING && !size && steps.length) {\n          state.status = STATUS.RUNNING;\n        }\n\n        _this.setState(state);\n      });\n\n      _defineProperty(this, \"addListener\", function (listener) {\n        _this.listener = listener;\n      });\n\n      _defineProperty(this, \"update\", function (state) {\n        if (!hasValidKeys(state, validKeys)) {\n          throw new Error(\"State is not valid. Valid keys: \".concat(validKeys.join(', ')));\n        }\n\n        _this.setState(_objectSpread2({}, _this.getNextState(_objectSpread2(_objectSpread2(_objectSpread2({}, _this.getState()), state), {}, {\n          action: state.action || ACTIONS.UPDATE\n        }), true)));\n      });\n\n      _defineProperty(this, \"start\", function (nextIndex) {\n        var _this$getState2 = _this.getState(),\n            index = _this$getState2.index,\n            size = _this$getState2.size;\n\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.START,\n          index: is.number(nextIndex) ? nextIndex : index\n        }, true)), {}, {\n          status: size ? STATUS.RUNNING : STATUS.WAITING\n        }));\n      });\n\n      _defineProperty(this, \"stop\", function () {\n        var advance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n        var _this$getState3 = _this.getState(),\n            index = _this$getState3.index,\n            status = _this$getState3.status;\n\n        if ([STATUS.FINISHED, STATUS.SKIPPED].indexOf(status) !== -1) return;\n\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.STOP,\n          index: index + (advance ? 1 : 0)\n        })), {}, {\n          status: STATUS.PAUSED\n        }));\n      });\n\n      _defineProperty(this, \"close\", function () {\n        var _this$getState4 = _this.getState(),\n            index = _this$getState4.index,\n            status = _this$getState4.status;\n\n        if (status !== STATUS.RUNNING) return;\n\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.CLOSE,\n          index: index + 1\n        })));\n      });\n\n      _defineProperty(this, \"go\", function (nextIndex) {\n        var _this$getState5 = _this.getState(),\n            controlled = _this$getState5.controlled,\n            status = _this$getState5.status;\n\n        if (controlled || status !== STATUS.RUNNING) return;\n\n        var step = _this.getSteps()[nextIndex];\n\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.GO,\n          index: nextIndex\n        })), {}, {\n          status: step ? status : STATUS.FINISHED\n        }));\n      });\n\n      _defineProperty(this, \"info\", function () {\n        return _this.getState();\n      });\n\n      _defineProperty(this, \"next\", function () {\n        var _this$getState6 = _this.getState(),\n            index = _this$getState6.index,\n            status = _this$getState6.status;\n\n        if (status !== STATUS.RUNNING) return;\n\n        _this.setState(_this.getNextState({\n          action: ACTIONS.NEXT,\n          index: index + 1\n        }));\n      });\n\n      _defineProperty(this, \"open\", function () {\n        var _this$getState7 = _this.getState(),\n            status = _this$getState7.status;\n\n        if (status !== STATUS.RUNNING) return;\n\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.UPDATE,\n          lifecycle: LIFECYCLE.TOOLTIP\n        })));\n      });\n\n      _defineProperty(this, \"prev\", function () {\n        var _this$getState8 = _this.getState(),\n            index = _this$getState8.index,\n            status = _this$getState8.status;\n\n        if (status !== STATUS.RUNNING) return;\n\n        _this.setState(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.PREV,\n          index: index - 1\n        })));\n      });\n\n      _defineProperty(this, \"reset\", function () {\n        var restart = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n        var _this$getState9 = _this.getState(),\n            controlled = _this$getState9.controlled;\n\n        if (controlled) return;\n\n        _this.setState(_objectSpread2(_objectSpread2({}, _this.getNextState({\n          action: ACTIONS.RESET,\n          index: 0\n        })), {}, {\n          status: restart ? STATUS.RUNNING : STATUS.READY\n        }));\n      });\n\n      _defineProperty(this, \"skip\", function () {\n        var _this$getState10 = _this.getState(),\n            status = _this$getState10.status;\n\n        if (status !== STATUS.RUNNING) return;\n\n        _this.setState({\n          action: ACTIONS.SKIP,\n          lifecycle: LIFECYCLE.INIT,\n          status: STATUS.SKIPPED\n        });\n      });\n\n      this.setState({\n        action: ACTIONS.INIT,\n        controlled: is.number(stepIndex),\n        continuous: continuous,\n        index: is.number(stepIndex) ? stepIndex : 0,\n        lifecycle: LIFECYCLE.INIT,\n        status: _steps.length ? STATUS.READY : STATUS.IDLE\n      }, true);\n      this.setSteps(_steps);\n    }\n\n    _createClass(Store, [{\n      key: \"setState\",\n      value: function setState(nextState) {\n        var initial = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n        var state = this.getState();\n\n        var _state$nextState = _objectSpread2(_objectSpread2({}, state), nextState),\n            action = _state$nextState.action,\n            index = _state$nextState.index,\n            lifecycle = _state$nextState.lifecycle,\n            size = _state$nextState.size,\n            status = _state$nextState.status;\n\n        store.set('action', action);\n        store.set('index', index);\n        store.set('lifecycle', lifecycle);\n        store.set('size', size);\n        store.set('status', status);\n\n        if (initial) {\n          store.set('controlled', nextState.controlled);\n          store.set('continuous', nextState.continuous);\n        }\n        /* istanbul ignore else */\n\n\n        if (this.listener && this.hasUpdatedState(state)) {\n          // console.log('▶ ▶ ▶ NEW STATE', this.getState());\n          this.listener(this.getState());\n        }\n      }\n    }, {\n      key: \"getState\",\n      value: function getState() {\n        if (!store.size) {\n          return _objectSpread2({}, defaultState);\n        }\n\n        return {\n          action: store.get('action') || '',\n          controlled: store.get('controlled') || false,\n          index: parseInt(store.get('index'), 10),\n          lifecycle: store.get('lifecycle') || '',\n          size: store.get('size') || 0,\n          status: store.get('status') || ''\n        };\n      }\n    }, {\n      key: \"getNextState\",\n      value: function getNextState(state) {\n        var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n        var _this$getState11 = this.getState(),\n            action = _this$getState11.action,\n            controlled = _this$getState11.controlled,\n            index = _this$getState11.index,\n            size = _this$getState11.size,\n            status = _this$getState11.status;\n\n        var newIndex = is.number(state.index) ? state.index : index;\n        var nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);\n        return {\n          action: state.action || action,\n          controlled: controlled,\n          index: nextIndex,\n          lifecycle: state.lifecycle || LIFECYCLE.INIT,\n          size: state.size || size,\n          status: nextIndex === size ? STATUS.FINISHED : state.status || status\n        };\n      }\n    }, {\n      key: \"hasUpdatedState\",\n      value: function hasUpdatedState(oldState) {\n        var before = JSON.stringify(oldState);\n        var after = JSON.stringify(this.getState());\n        return before !== after;\n      }\n    }, {\n      key: \"getSteps\",\n      value: function getSteps() {\n        var steps = data.get('steps');\n        return Array.isArray(steps) ? steps : [];\n      }\n    }, {\n      key: \"getHelpers\",\n      value: function getHelpers() {\n        return {\n          close: this.close,\n          go: this.go,\n          info: this.info,\n          next: this.next,\n          open: this.open,\n          prev: this.prev,\n          reset: this.reset,\n          skip: this.skip\n        };\n      }\n    }]);\n\n    return Store;\n  }();\n\n  return new Store(props);\n}\n\nfunction scrollDoc() {\n  return document.scrollingElement || document.createElement('body');\n}\n/**\n * Find the bounding client rect\n *\n * @private\n * @param {HTMLElement} element - The target element\n * @returns {Object}\n */\n\nfunction getClientRect(element) {\n  if (!element) {\n    return {};\n  }\n\n  return element.getBoundingClientRect();\n}\n/**\n * Helper function to get the browser-normalized \"document height\"\n * @returns {Number}\n */\n\nfunction getDocumentHeight() {\n  var _document = document,\n      body = _document.body,\n      html = _document.documentElement;\n\n  if (!body || !html) {\n    return 0;\n  }\n\n  return Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n}\n/**\n * Find and return the target DOM element based on a step's 'target'.\n *\n * @private\n * @param {string|HTMLElement} element\n *\n * @returns {HTMLElement|null}\n */\n\nfunction getElement(element) {\n  /* istanbul ignore else */\n  if (typeof element === 'string') {\n    return document.querySelector(element);\n  }\n\n  return element;\n}\n/**\n *  Get computed style property\n *\n * @param {HTMLElement} el\n *\n * @returns {Object}\n */\n\nfunction getStyleComputedProperty(el) {\n  if (!el || el.nodeType !== 1) {\n    return {};\n  }\n\n  return getComputedStyle(el);\n}\n/**\n * Get scroll parent with fix\n *\n * @param {HTMLElement} element\n * @param {boolean} skipFix\n * @param {boolean} [forListener]\n *\n * @returns {*}\n */\n\nfunction getScrollParent(element, skipFix, forListener) {\n  var parent = scrollParent(element);\n\n  if (parent.isSameNode(scrollDoc())) {\n    if (forListener) {\n      return document;\n    }\n\n    return scrollDoc();\n  }\n\n  var hasScrolling = parent.scrollHeight > parent.offsetHeight;\n  /* istanbul ignore else */\n\n  if (!hasScrolling && !skipFix) {\n    parent.style.overflow = 'initial';\n    return scrollDoc();\n  }\n\n  return parent;\n}\n/**\n * Check if the element has custom scroll parent\n *\n * @param {HTMLElement} element\n * @param {boolean} skipFix\n *\n * @returns {boolean}\n */\n\nfunction hasCustomScrollParent(element, skipFix) {\n  if (!element) return false;\n  var parent = getScrollParent(element, skipFix);\n  return !parent.isSameNode(scrollDoc());\n}\n/**\n * Check if the element has custom offset parent\n *\n * @param {HTMLElement} element\n *\n * @returns {boolean}\n */\n\nfunction hasCustomOffsetParent(element) {\n  return element.offsetParent !== document.body;\n}\n/**\n * Check if an element has fixed/sticky position\n * @param {HTMLElement|Node} el\n * @param {string} [type]\n *\n * @returns {boolean}\n */\n\nfunction hasPosition(el) {\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'fixed';\n\n  if (!el || !(el instanceof HTMLElement)) {\n    return false;\n  }\n\n  var nodeName = el.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n\n  if (getStyleComputedProperty(el).position === type) {\n    return true;\n  }\n\n  return hasPosition(el.parentNode, type);\n}\n/**\n * Check if the element is visible\n *\n * @param {HTMLElement} element\n *\n * @returns {boolean}\n */\n\nfunction isElementVisible(element) {\n  if (!element) return false;\n  var parentElement = element;\n\n  while (parentElement) {\n    if (parentElement === document.body) break;\n    /* istanbul ignore else */\n\n    if (parentElement instanceof HTMLElement) {\n      var _getComputedStyle = getComputedStyle(parentElement),\n          display = _getComputedStyle.display,\n          visibility = _getComputedStyle.visibility;\n\n      if (display === 'none' || visibility === 'hidden') {\n        return false;\n      }\n    }\n\n    parentElement = parentElement.parentNode;\n  }\n\n  return true;\n}\n/**\n * Find and return the target DOM element based on a step's 'target'.\n *\n * @private\n * @param {string|HTMLElement} element\n * @param {number} offset\n * @param {boolean} skipFix\n *\n * @returns {HTMLElement|undefined}\n */\n\nfunction getElementPosition(element, offset, skipFix) {\n  var elementRect = getClientRect(element);\n  var parent = getScrollParent(element, skipFix);\n  var hasScrollParent = hasCustomScrollParent(element, skipFix);\n  var parentTop = 0;\n  /* istanbul ignore else */\n\n  if (parent instanceof HTMLElement) {\n    parentTop = parent.scrollTop;\n  }\n\n  var top = elementRect.top + (!hasScrollParent && !hasPosition(element) ? parentTop : 0);\n  return Math.floor(top - offset);\n}\n/**\n * Get the offsetTop of each element up to the body\n *\n * @param {HTMLElement} element\n *\n * @returns {number}\n */\n\nfunction getTopOffset(element) {\n  if (element instanceof HTMLElement) {\n    if (element.offsetParent instanceof HTMLElement) {\n      return getTopOffset(element.offsetParent) + element.offsetTop;\n    }\n\n    return element.offsetTop;\n  }\n\n  return 0;\n}\n/**\n * Get the scrollTop position\n *\n * @param {HTMLElement} element\n * @param {number} offset\n * @param {boolean} skipFix\n *\n * @returns {number}\n */\n\nfunction getScrollTo(element, offset, skipFix) {\n  if (!element) {\n    return 0;\n  }\n\n  var parent = scrollParent(element);\n  var top = getTopOffset(element);\n\n  if (hasCustomScrollParent(element, skipFix) && !hasCustomOffsetParent(element)) {\n    top -= getTopOffset(parent);\n  }\n\n  return Math.floor(top - offset);\n}\n/**\n * Scroll to position\n * @param {number} value\n * @param {HTMLElement} element\n * @param {number} scrollDuration\n * @returns {Promise<*>}\n */\n\nfunction scrollTo(value) {\n  var element = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scrollDoc();\n  var scrollDuration = arguments.length > 2 ? arguments[2] : undefined;\n  return new Promise(function (resolve, reject) {\n    var scrollTop = element.scrollTop;\n    var limit = value > scrollTop ? value - scrollTop : scrollTop - value;\n    scroll.top(element, value, {\n      duration: limit < 100 ? 50 : scrollDuration\n    }, function (error) {\n      if (error && error.message !== 'Element already at target scroll position') {\n        return reject(error);\n      }\n\n      return resolve();\n    });\n  });\n}\n\nfunction createChainableTypeChecker(validate) {\n  function checkType(isRequired, props, propName, componentName, location, propFullName) {\n    var componentNameSafe = componentName || '<<anonymous>>';\n    var propFullNameSafe = propFullName || propName;\n    /* istanbul ignore else */\n\n    if (props[propName] == null) {\n      if (isRequired) {\n        return new Error(\"Required \".concat(location, \" `\").concat(propFullNameSafe, \"` was not specified in `\").concat(componentNameSafe, \"`.\"));\n      }\n\n      return null;\n    }\n\n    for (var _len = arguments.length, args = new Array(_len > 6 ? _len - 6 : 0), _key = 6; _key < _len; _key++) {\n      args[_key - 6] = arguments[_key];\n    }\n\n    return validate.apply(void 0, [props, propName, componentNameSafe, location, propFullNameSafe].concat(args));\n  }\n\n  var chainedCheckType = checkType.bind(null, false);\n  chainedCheckType.isRequired = checkType.bind(null, true);\n  return chainedCheckType;\n}\n\ncreateChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n  var propValue = props[propName];\n  var Component = propValue;\n\n  if (! /*#__PURE__*/React.isValidElement(propValue) && isValidElementType(propValue)) {\n    var ownProps = {\n      ref: function ref() {},\n      step: {}\n    };\n    Component = /*#__PURE__*/React.createElement(Component, ownProps);\n  }\n\n  if (is.string(propValue) || is.number(propValue) || !isValidElementType(propValue) || !([Element, ForwardRef].indexOf(typeOf(Component)) !== -1)) {\n    return new Error(\"Invalid \".concat(location, \" `\").concat(propFullName, \"` supplied to `\").concat(componentName, \"`. Expected a React class or forwardRef.\"));\n  }\n\n  return undefined;\n});\n\nvar defaultOptions = {\n  arrowColor: '#fff',\n  backgroundColor: '#fff',\n  beaconSize: 36,\n  overlayColor: 'rgba(0, 0, 0, 0.5)',\n  primaryColor: '#f04',\n  spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',\n  textColor: '#333',\n  zIndex: 100\n};\nvar buttonBase = {\n  backgroundColor: 'transparent',\n  border: 0,\n  borderRadius: 0,\n  color: '#555',\n  cursor: 'pointer',\n  fontSize: 16,\n  lineHeight: 1,\n  padding: 8,\n  WebkitAppearance: 'none'\n};\nvar spotlight = {\n  borderRadius: 4,\n  position: 'absolute'\n};\nfunction getStyles() {\n  var stepStyles = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var options = deepmerge(defaultOptions, stepStyles.options || {});\n  var width = 290;\n\n  if (window.innerWidth > 480) {\n    width = 380;\n  }\n\n  if (options.width) {\n    if (window.innerWidth < options.width) {\n      width = window.innerWidth - 30;\n    } else {\n      width = options.width; //eslint-disable-line prefer-destructuring\n    }\n  }\n\n  var overlay = {\n    bottom: 0,\n    left: 0,\n    overflow: 'hidden',\n    position: 'absolute',\n    right: 0,\n    top: 0,\n    zIndex: options.zIndex\n  };\n  var defaultStyles = {\n    beacon: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      display: 'inline-block',\n      height: options.beaconSize,\n      position: 'relative',\n      width: options.beaconSize,\n      zIndex: options.zIndex\n    }),\n    beaconInner: {\n      animation: 'joyride-beacon-inner 1.2s infinite ease-in-out',\n      backgroundColor: options.primaryColor,\n      borderRadius: '50%',\n      display: 'block',\n      height: '50%',\n      left: '50%',\n      opacity: 0.7,\n      position: 'absolute',\n      top: '50%',\n      transform: 'translate(-50%, -50%)',\n      width: '50%'\n    },\n    beaconOuter: {\n      animation: 'joyride-beacon-outer 1.2s infinite ease-in-out',\n      backgroundColor: \"rgba(\".concat(hexToRGB(options.primaryColor).join(','), \", 0.2)\"),\n      border: \"2px solid \".concat(options.primaryColor),\n      borderRadius: '50%',\n      boxSizing: 'border-box',\n      display: 'block',\n      height: '100%',\n      left: 0,\n      opacity: 0.9,\n      position: 'absolute',\n      top: 0,\n      transformOrigin: 'center',\n      width: '100%'\n    },\n    tooltip: {\n      backgroundColor: options.backgroundColor,\n      borderRadius: 5,\n      boxSizing: 'border-box',\n      color: options.textColor,\n      fontSize: 16,\n      maxWidth: '100%',\n      padding: 15,\n      position: 'relative',\n      width: width\n    },\n    tooltipContainer: {\n      lineHeight: 1.4,\n      textAlign: 'center'\n    },\n    tooltipTitle: {\n      fontSize: 18,\n      margin: 0\n    },\n    tooltipContent: {\n      padding: '20px 10px'\n    },\n    tooltipFooter: {\n      alignItems: 'center',\n      display: 'flex',\n      justifyContent: 'flex-end',\n      marginTop: 15\n    },\n    tooltipFooterSpacer: {\n      flex: 1\n    },\n    buttonNext: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      backgroundColor: options.primaryColor,\n      borderRadius: 4,\n      color: '#fff'\n    }),\n    buttonBack: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.primaryColor,\n      marginLeft: 'auto',\n      marginRight: 5\n    }),\n    buttonClose: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.textColor,\n      height: 14,\n      padding: 15,\n      position: 'absolute',\n      right: 0,\n      top: 0,\n      width: 14\n    }),\n    buttonSkip: _objectSpread2(_objectSpread2({}, buttonBase), {}, {\n      color: options.textColor,\n      fontSize: 14\n    }),\n    overlay: _objectSpread2(_objectSpread2({}, overlay), {}, {\n      backgroundColor: options.overlayColor,\n      mixBlendMode: 'hard-light'\n    }),\n    overlayLegacy: _objectSpread2({}, overlay),\n    overlayLegacyCenter: _objectSpread2(_objectSpread2({}, overlay), {}, {\n      backgroundColor: options.overlayColor\n    }),\n    spotlight: _objectSpread2(_objectSpread2({}, spotlight), {}, {\n      backgroundColor: 'gray'\n    }),\n    spotlightLegacy: _objectSpread2(_objectSpread2({}, spotlight), {}, {\n      boxShadow: \"0 0 0 9999px \".concat(options.overlayColor, \", \").concat(options.spotlightShadow)\n    }),\n    floaterStyles: {\n      arrow: {\n        color: options.arrowColor\n      },\n      options: {\n        zIndex: options.zIndex\n      }\n    },\n    options: options\n  };\n  return deepmerge(defaultStyles, stepStyles);\n}\n\nvar DEFAULTS = {\n  floaterProps: {\n    options: {\n      preventOverflow: {\n        boundariesElement: 'scrollParent'\n      }\n    },\n    wrapperOptions: {\n      offset: -18,\n      position: true\n    }\n  },\n  locale: {\n    back: 'Back',\n    close: 'Close',\n    last: 'Last',\n    next: 'Next',\n    open: 'Open the dialog',\n    skip: 'Skip'\n  },\n  step: {\n    event: 'click',\n    placement: 'bottom',\n    offset: 10\n  }\n};\n\nfunction getTourProps(props) {\n  var sharedTourProps = ['beaconComponent', 'disableCloseOnEsc', 'disableOverlay', 'disableOverlayClose', 'disableScrolling', 'disableScrollParentFix', 'floaterProps', 'hideBackButton', 'hideCloseButton', 'locale', 'showProgress', 'showSkipButton', 'spotlightClicks', 'spotlightPadding', 'styles', 'tooltipComponent'];\n  return Object.keys(props).filter(function (d) {\n    return sharedTourProps.indexOf(d) !== -1;\n  }).reduce(function (acc, i) {\n    acc[i] = props[i]; //eslint-disable-line react/destructuring-assignment\n\n    return acc;\n  }, {});\n}\n\nfunction getMergedStep(step, props) {\n  if (!step) return null;\n  var mergedStep = deepmerge.all([getTourProps(props), DEFAULTS.step, step], {\n    isMergeableObject: is.plainObject\n  });\n  var mergedStyles = getStyles(deepmerge(props.styles || {}, step.styles || {}));\n  var scrollParent = hasCustomScrollParent(getElement(step.target), mergedStep.disableScrollParentFix);\n  var floaterProps = deepmerge.all([props.floaterProps || {}, DEFAULTS.floaterProps, mergedStep.floaterProps || {}]); // Set react-floater props\n\n  floaterProps.offset = mergedStep.offset;\n  floaterProps.styles = deepmerge(floaterProps.styles || {}, mergedStyles.floaterStyles || {});\n  delete mergedStyles.floaterStyles;\n  floaterProps.offset += props.spotlightPadding || step.spotlightPadding || 0;\n\n  if (step.placementBeacon) {\n    floaterProps.wrapperOptions.placement = step.placementBeacon;\n  }\n\n  if (scrollParent) {\n    floaterProps.options.preventOverflow.boundariesElement = 'window';\n  }\n\n  return _objectSpread2(_objectSpread2({}, mergedStep), {}, {\n    locale: deepmerge.all([DEFAULTS.locale, props.locale || {}, mergedStep.locale || {}]),\n    floaterProps: floaterProps,\n    styles: mergedStyles\n  });\n}\n/**\n * Validate if a step is valid\n *\n * @param {Object} step - A step object\n * @param {boolean} debug\n *\n * @returns {boolean} - True if the step is valid, false otherwise\n */\n\nfunction validateStep(step) {\n  var debug = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  if (!is.plainObject(step)) {\n    log({\n      title: 'validateStep',\n      data: 'step must be an object',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n\n  if (!step.target) {\n    log({\n      title: 'validateStep',\n      data: 'target is missing from the step',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n\n  return true;\n}\n/**\n * Validate if steps is valid\n *\n * @param {Array} steps - A steps array\n * @param {boolean} debug\n *\n * @returns {boolean} - True if the steps are valid, false otherwise\n */\n\nfunction validateSteps(steps) {\n  var debug = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  if (!is.array(steps)) {\n    log({\n      title: 'validateSteps',\n      data: 'steps must be an array',\n      warn: true,\n      debug: debug\n    });\n    return false;\n  }\n\n  return steps.every(function (d) {\n    return validateStep(d, debug);\n  });\n}\n\nvar Scope = /*#__PURE__*/_createClass(function Scope(_element) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  _classCallCheck(this, Scope);\n\n  _defineProperty(this, \"element\", void 0);\n\n  _defineProperty(this, \"options\", void 0);\n\n  _defineProperty(this, \"canBeTabbed\", function (element) {\n    var tabIndex = element.tabIndex;\n    if (tabIndex === null || tabIndex < 0) tabIndex = undefined;\n    var isTabIndexNaN = isNaN(tabIndex);\n    return !isTabIndexNaN && _this.canHaveFocus(element);\n  });\n\n  _defineProperty(this, \"canHaveFocus\", function (element) {\n    var validTabNodes = /input|select|textarea|button|object/;\n    var nodeName = element.nodeName.toLowerCase();\n    var res = validTabNodes.test(nodeName) && !element.getAttribute('disabled') || nodeName === 'a' && !!element.getAttribute('href');\n    return res && _this.isVisible(element);\n  });\n\n  _defineProperty(this, \"findValidTabElements\", function () {\n    return [].slice.call(_this.element.querySelectorAll('*'), 0).filter(_this.canBeTabbed);\n  });\n\n  _defineProperty(this, \"handleKeyDown\", function (e) {\n    var _this$options$keyCode = _this.options.keyCode,\n        keyCode = _this$options$keyCode === void 0 ? 9 : _this$options$keyCode;\n    /* istanbul ignore else */\n\n    if (e.keyCode === keyCode) {\n      _this.interceptTab(e);\n    }\n  });\n\n  _defineProperty(this, \"interceptTab\", function (event) {\n    var elements = _this.findValidTabElements();\n\n    if (!elements.length) {\n      return;\n    }\n\n    event.preventDefault();\n    var shiftKey = event.shiftKey;\n    var x = elements.indexOf(document.activeElement);\n\n    if (x === -1 || !shiftKey && x + 1 === elements.length) {\n      x = 0;\n    } else if (shiftKey && x === 0) {\n      x = elements.length - 1;\n    } else {\n      x += shiftKey ? -1 : 1;\n    }\n\n    elements[x].focus();\n  });\n\n  _defineProperty(this, \"isHidden\", function (element) {\n    var noSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n    var style = window.getComputedStyle(element);\n    if (noSize && !element.innerHTML) return true;\n    return noSize && style.getPropertyValue('overflow') !== 'visible' || style.getPropertyValue('display') === 'none';\n  });\n\n  _defineProperty(this, \"isVisible\", function (element) {\n    var parentElement = element;\n\n    while (parentElement) {\n      /* istanbul ignore else */\n      if (parentElement instanceof HTMLElement) {\n        if (parentElement === document.body) break;\n        /* istanbul ignore else */\n\n        if (_this.isHidden(parentElement)) return false;\n        parentElement = parentElement.parentNode;\n      }\n    }\n\n    return true;\n  });\n\n  _defineProperty(this, \"removeScope\", function () {\n    window.removeEventListener('keydown', _this.handleKeyDown);\n  });\n\n  _defineProperty(this, \"checkFocus\", function (target) {\n    if (document.activeElement !== target) {\n      target.focus();\n      window.requestAnimationFrame(function () {\n        return _this.checkFocus(target);\n      });\n    }\n  });\n\n  _defineProperty(this, \"setFocus\", function () {\n    var selector = _this.options.selector;\n    if (!selector) return;\n\n    var target = _this.element.querySelector(selector);\n    /* istanbul ignore else */\n\n\n    if (target) {\n      window.requestAnimationFrame(function () {\n        return _this.checkFocus(target);\n      });\n    }\n  });\n\n  if (!(_element instanceof HTMLElement)) {\n    throw new TypeError('Invalid parameter: element must be an HTMLElement');\n  }\n\n  this.element = _element;\n  this.options = options;\n  window.addEventListener('keydown', this.handleKeyDown, false);\n  this.setFocus();\n});\n\nvar JoyrideBeacon = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideBeacon, _React$Component);\n\n  var _super = _createSuper(JoyrideBeacon);\n\n  function JoyrideBeacon(props) {\n    var _this;\n\n    _classCallCheck(this, JoyrideBeacon);\n\n    _this = _super.call(this, props);\n\n    _defineProperty(_assertThisInitialized(_this), \"setBeaconRef\", function (c) {\n      _this.beacon = c;\n    });\n\n    if (!props.beaconComponent) {\n      var head = document.head || document.getElementsByTagName('head')[0];\n      var style = document.createElement('style');\n      var css = \"\\n        @keyframes joyride-beacon-inner {\\n          20% {\\n            opacity: 0.9;\\n          }\\n        \\n          90% {\\n            opacity: 0.7;\\n          }\\n        }\\n        \\n        @keyframes joyride-beacon-outer {\\n          0% {\\n            transform: scale(1);\\n          }\\n        \\n          45% {\\n            opacity: 0.7;\\n            transform: scale(0.75);\\n          }\\n        \\n          100% {\\n            opacity: 0.9;\\n            transform: scale(1);\\n          }\\n        }\\n      \";\n      style.type = 'text/css';\n      style.id = 'joyride-beacon-animation';\n\n      if (props.nonce !== undefined) {\n        style.setAttribute('nonce', props.nonce);\n      }\n\n      style.appendChild(document.createTextNode(css));\n      head.appendChild(style);\n    }\n\n    return _this;\n  }\n\n  _createClass(JoyrideBeacon, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n\n      var shouldFocus = this.props.shouldFocus;\n\n      setTimeout(function () {\n        if (is.domElement(_this2.beacon) && shouldFocus) {\n          _this2.beacon.focus();\n        }\n      }, 0);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var style = document.getElementById('joyride-beacon-animation');\n\n      if (style) {\n        style.parentNode.removeChild(style);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          beaconComponent = _this$props.beaconComponent,\n          locale = _this$props.locale,\n          onClickOrHover = _this$props.onClickOrHover,\n          styles = _this$props.styles;\n      var props = {\n        'aria-label': locale.open,\n        onClick: onClickOrHover,\n        onMouseEnter: onClickOrHover,\n        ref: this.setBeaconRef,\n        title: locale.open\n      };\n      var component;\n\n      if (beaconComponent) {\n        var BeaconComponent = beaconComponent;\n        component = /*#__PURE__*/React.createElement(BeaconComponent, props);\n      } else {\n        component = /*#__PURE__*/React.createElement(\"button\", _extends({\n          key: \"JoyrideBeacon\",\n          className: \"react-joyride__beacon\",\n          style: styles.beacon,\n          type: \"button\"\n        }, props), /*#__PURE__*/React.createElement(\"span\", {\n          style: styles.beaconInner\n        }), /*#__PURE__*/React.createElement(\"span\", {\n          style: styles.beaconOuter\n        }));\n      }\n\n      return component;\n    }\n  }]);\n\n  return JoyrideBeacon;\n}(React.Component);\n\nvar JoyrideSpotlight = function JoyrideSpotlight(_ref) {\n  var styles = _ref.styles;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    key: \"JoyrideSpotlight\",\n    className: \"react-joyride__spotlight\",\n    style: styles\n  });\n};\n\nvar _excluded$2 = [\"mixBlendMode\", \"zIndex\"];\n\nvar JoyrideOverlay = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideOverlay, _React$Component);\n\n  var _super = _createSuper(JoyrideOverlay);\n\n  function JoyrideOverlay() {\n    var _this;\n\n    _classCallCheck(this, JoyrideOverlay);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _defineProperty(_assertThisInitialized(_this), \"_isMounted\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      mouseOverSpotlight: false,\n      isScrolling: false,\n      showSpotlight: true\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleMouseMove\", function (e) {\n      var mouseOverSpotlight = _this.state.mouseOverSpotlight;\n      var _this$spotlightStyles = _this.spotlightStyles,\n          height = _this$spotlightStyles.height,\n          left = _this$spotlightStyles.left,\n          position = _this$spotlightStyles.position,\n          top = _this$spotlightStyles.top,\n          width = _this$spotlightStyles.width;\n      var offsetY = position === 'fixed' ? e.clientY : e.pageY;\n      var offsetX = position === 'fixed' ? e.clientX : e.pageX;\n      var inSpotlightHeight = offsetY >= top && offsetY <= top + height;\n      var inSpotlightWidth = offsetX >= left && offsetX <= left + width;\n      var inSpotlight = inSpotlightWidth && inSpotlightHeight;\n\n      if (inSpotlight !== mouseOverSpotlight) {\n        _this.updateState({\n          mouseOverSpotlight: inSpotlight\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleScroll\", function () {\n      var target = _this.props.target;\n      var element = getElement(target);\n\n      if (_this.scrollParent !== document) {\n        var isScrolling = _this.state.isScrolling;\n\n        if (!isScrolling) {\n          _this.updateState({\n            isScrolling: true,\n            showSpotlight: false\n          });\n        }\n\n        clearTimeout(_this.scrollTimeout);\n        _this.scrollTimeout = setTimeout(function () {\n          _this.updateState({\n            isScrolling: false,\n            showSpotlight: true\n          });\n        }, 50);\n      } else if (hasPosition(element, 'sticky')) {\n        _this.updateState({});\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleResize\", function () {\n      clearTimeout(_this.resizeTimeout);\n      _this.resizeTimeout = setTimeout(function () {\n        if (!_this._isMounted) {\n          return;\n        }\n\n        _this.forceUpdate();\n      }, 100);\n    });\n\n    return _this;\n  }\n\n  _createClass(JoyrideOverlay, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props = this.props;\n          _this$props.debug;\n          _this$props.disableScrolling;\n          var disableScrollParentFix = _this$props.disableScrollParentFix,\n          target = _this$props.target;\n      var element = getElement(target);\n      this.scrollParent = getScrollParent(element, disableScrollParentFix, true);\n      this._isMounted = true;\n\n      window.addEventListener('resize', this.handleResize);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          lifecycle = _this$props2.lifecycle,\n          spotlightClicks = _this$props2.spotlightClicks;\n\n      var _treeChanges = treeChanges(prevProps, this.props),\n          changed = _treeChanges.changed;\n      /* istanbul ignore else */\n\n\n      if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n        this.scrollParent.addEventListener('scroll', this.handleScroll, {\n          passive: true\n        });\n        setTimeout(function () {\n          var isScrolling = _this2.state.isScrolling;\n\n          if (!isScrolling) {\n            _this2.updateState({\n              showSpotlight: true\n            });\n          }\n        }, 100);\n      }\n\n      if (changed('spotlightClicks') || changed('disableOverlay') || changed('lifecycle')) {\n        if (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP) {\n          window.addEventListener('mousemove', this.handleMouseMove, false);\n        } else if (lifecycle !== LIFECYCLE.TOOLTIP) {\n          window.removeEventListener('mousemove', this.handleMouseMove);\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      window.removeEventListener('mousemove', this.handleMouseMove);\n      window.removeEventListener('resize', this.handleResize);\n      clearTimeout(this.resizeTimeout);\n      clearTimeout(this.scrollTimeout);\n      this.scrollParent.removeEventListener('scroll', this.handleScroll);\n    }\n  }, {\n    key: \"spotlightStyles\",\n    get: function get() {\n      var showSpotlight = this.state.showSpotlight;\n      var _this$props3 = this.props,\n          disableScrollParentFix = _this$props3.disableScrollParentFix,\n          spotlightClicks = _this$props3.spotlightClicks,\n          spotlightPadding = _this$props3.spotlightPadding,\n          styles = _this$props3.styles,\n          target = _this$props3.target;\n      var element = getElement(target);\n      var elementRect = getClientRect(element);\n      var isFixedTarget = hasPosition(element);\n      var top = getElementPosition(element, spotlightPadding, disableScrollParentFix);\n      return _objectSpread2(_objectSpread2({}, isLegacy() ? styles.spotlightLegacy : styles.spotlight), {}, {\n        height: Math.round(elementRect.height + spotlightPadding * 2),\n        left: Math.round(elementRect.left - spotlightPadding),\n        opacity: showSpotlight ? 1 : 0,\n        pointerEvents: spotlightClicks ? 'none' : 'auto',\n        position: isFixedTarget ? 'fixed' : 'absolute',\n        top: top,\n        transition: 'opacity 0.2s',\n        width: Math.round(elementRect.width + spotlightPadding * 2)\n      });\n    }\n  }, {\n    key: \"updateState\",\n    value: function updateState(state) {\n      if (!this._isMounted) {\n        return;\n      }\n\n      this.setState(state);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state = this.state,\n          mouseOverSpotlight = _this$state.mouseOverSpotlight,\n          showSpotlight = _this$state.showSpotlight;\n      var _this$props4 = this.props,\n          disableOverlay = _this$props4.disableOverlay,\n          disableOverlayClose = _this$props4.disableOverlayClose,\n          lifecycle = _this$props4.lifecycle,\n          onClickOverlay = _this$props4.onClickOverlay,\n          placement = _this$props4.placement,\n          styles = _this$props4.styles;\n\n      if (disableOverlay || lifecycle !== LIFECYCLE.TOOLTIP) {\n        return null;\n      }\n\n      var baseStyles = styles.overlay;\n      /* istanbul ignore else */\n\n      if (isLegacy()) {\n        baseStyles = placement === 'center' ? styles.overlayLegacyCenter : styles.overlayLegacy;\n      }\n\n      var stylesOverlay = _objectSpread2({\n        cursor: disableOverlayClose ? 'default' : 'pointer',\n        height: getDocumentHeight(),\n        pointerEvents: mouseOverSpotlight ? 'none' : 'auto'\n      }, baseStyles);\n\n      var spotlight = placement !== 'center' && showSpotlight && /*#__PURE__*/React.createElement(JoyrideSpotlight, {\n        styles: this.spotlightStyles\n      }); // Hack for Safari bug with mix-blend-mode with z-index\n\n      if (getBrowser() === 'safari') {\n        stylesOverlay.mixBlendMode;\n            stylesOverlay.zIndex;\n            var safarOverlay = _objectWithoutProperties(stylesOverlay, _excluded$2);\n\n        spotlight = /*#__PURE__*/React.createElement(\"div\", {\n          style: _objectSpread2({}, safarOverlay)\n        }, spotlight);\n        delete stylesOverlay.backgroundColor;\n      }\n\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"react-joyride__overlay\",\n        style: stylesOverlay,\n        onClick: onClickOverlay\n      }, spotlight);\n    }\n  }]);\n\n  return JoyrideOverlay;\n}(React.Component);\n\nvar _excluded$1 = [\"styles\"],\n    _excluded2 = [\"color\", \"height\", \"width\"];\n\nvar JoyrideTooltipCloseBtn = function JoyrideTooltipCloseBtn(_ref) {\n  var styles = _ref.styles,\n      props = _objectWithoutProperties(_ref, _excluded$1);\n\n  var color = styles.color,\n      height = styles.height,\n      width = styles.width,\n      style = _objectWithoutProperties(styles, _excluded2);\n\n  return /*#__PURE__*/React.createElement(\"button\", _extends({\n    style: style,\n    type: \"button\"\n  }, props), /*#__PURE__*/React.createElement(\"svg\", {\n    width: typeof width === 'number' ? \"\".concat(width, \"px\") : width,\n    height: typeof height === 'number' ? \"\".concat(height, \"px\") : height,\n    viewBox: \"0 0 18 18\",\n    version: \"1.1\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    preserveAspectRatio: \"xMidYMid\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",\n    fill: color\n  }))));\n};\n\nvar JoyrideTooltipContainer = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideTooltipContainer, _React$Component);\n\n  var _super = _createSuper(JoyrideTooltipContainer);\n\n  function JoyrideTooltipContainer() {\n    _classCallCheck(this, JoyrideTooltipContainer);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(JoyrideTooltipContainer, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          backProps = _this$props.backProps,\n          closeProps = _this$props.closeProps,\n          continuous = _this$props.continuous,\n          index = _this$props.index,\n          isLastStep = _this$props.isLastStep,\n          primaryProps = _this$props.primaryProps,\n          size = _this$props.size,\n          skipProps = _this$props.skipProps,\n          step = _this$props.step,\n          tooltipProps = _this$props.tooltipProps;\n      var content = step.content,\n          hideBackButton = step.hideBackButton,\n          hideCloseButton = step.hideCloseButton,\n          hideFooter = step.hideFooter,\n          showProgress = step.showProgress,\n          showSkipButton = step.showSkipButton,\n          title = step.title,\n          styles = step.styles;\n      var _step$locale = step.locale,\n          back = _step$locale.back,\n          close = _step$locale.close,\n          last = _step$locale.last,\n          next = _step$locale.next,\n          skip = _step$locale.skip;\n      var output = {\n        primary: close\n      };\n\n      if (continuous) {\n        output.primary = isLastStep ? last : next;\n\n        if (showProgress) {\n          output.primary = /*#__PURE__*/React.createElement(\"span\", null, output.primary, \" (\", index + 1, \"/\", size, \")\");\n        }\n      }\n\n      if (showSkipButton && !isLastStep) {\n        output.skip = /*#__PURE__*/React.createElement(\"button\", _extends({\n          style: styles.buttonSkip,\n          type: \"button\",\n          \"aria-live\": \"off\"\n        }, skipProps), skip);\n      }\n\n      if (!hideBackButton && index > 0) {\n        output.back = /*#__PURE__*/React.createElement(\"button\", _extends({\n          style: styles.buttonBack,\n          type: \"button\"\n        }, backProps), back);\n      }\n\n      output.close = !hideCloseButton && /*#__PURE__*/React.createElement(JoyrideTooltipCloseBtn, _extends({\n        styles: styles.buttonClose\n      }, closeProps));\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        key: \"JoyrideTooltip\",\n        className: \"react-joyride__tooltip\",\n        style: styles.tooltip\n      }, tooltipProps), /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipContainer\n      }, title && /*#__PURE__*/React.createElement(\"h4\", {\n        style: styles.tooltipTitle,\n        \"aria-label\": title\n      }, title), /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipContent\n      }, content)), !hideFooter && /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipFooter\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        style: styles.tooltipFooterSpacer\n      }, output.skip), output.back, /*#__PURE__*/React.createElement(\"button\", _extends({\n        style: styles.buttonNext,\n        type: \"button\"\n      }, primaryProps), output.primary)), output.close);\n    }\n  }]);\n\n  return JoyrideTooltipContainer;\n}(React.Component);\n\nvar _excluded = [\"beaconComponent\", \"tooltipComponent\"];\n\nvar JoyrideTooltip = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideTooltip, _React$Component);\n\n  var _super = _createSuper(JoyrideTooltip);\n\n  function JoyrideTooltip() {\n    var _this;\n\n    _classCallCheck(this, JoyrideTooltip);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickBack\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.prev();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickClose\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.close();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickPrimary\", function (e) {\n      e.preventDefault();\n      var _this$props = _this.props,\n          continuous = _this$props.continuous,\n          helpers = _this$props.helpers;\n\n      if (!continuous) {\n        helpers.close();\n        return;\n      }\n\n      helpers.next();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickSkip\", function (e) {\n      e.preventDefault();\n      var helpers = _this.props.helpers;\n      helpers.skip();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getElementsProps\", function () {\n      var _this$props2 = _this.props,\n          continuous = _this$props2.continuous,\n          isLastStep = _this$props2.isLastStep,\n          setTooltipRef = _this$props2.setTooltipRef,\n          step = _this$props2.step;\n      var back = getText(step.locale.back);\n      var close = getText(step.locale.close);\n      var last = getText(step.locale.last);\n      var next = getText(step.locale.next);\n      var skip = getText(step.locale.skip);\n      var primaryText = continuous ? next : close;\n\n      if (isLastStep) {\n        primaryText = last;\n      }\n\n      return {\n        backProps: {\n          'aria-label': back,\n          'data-action': 'back',\n          onClick: _this.handleClickBack,\n          role: 'button',\n          title: back\n        },\n        closeProps: {\n          'aria-label': close,\n          'data-action': 'close',\n          onClick: _this.handleClickClose,\n          role: 'button',\n          title: close\n        },\n        primaryProps: {\n          'aria-label': primaryText,\n          'data-action': 'primary',\n          onClick: _this.handleClickPrimary,\n          role: 'button',\n          title: primaryText\n        },\n        skipProps: {\n          'aria-label': skip,\n          'data-action': 'skip',\n          onClick: _this.handleClickSkip,\n          role: 'button',\n          title: skip\n        },\n        tooltipProps: {\n          'aria-modal': true,\n          ref: setTooltipRef,\n          role: 'alertdialog'\n        }\n      };\n    });\n\n    return _this;\n  }\n\n  _createClass(JoyrideTooltip, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props3 = this.props,\n          continuous = _this$props3.continuous,\n          index = _this$props3.index,\n          isLastStep = _this$props3.isLastStep,\n          size = _this$props3.size,\n          step = _this$props3.step;\n\n      step.beaconComponent;\n          var tooltipComponent = step.tooltipComponent,\n          cleanStep = _objectWithoutProperties(step, _excluded);\n\n      var component;\n\n      if (tooltipComponent) {\n        var renderProps = _objectSpread2(_objectSpread2({}, this.getElementsProps()), {}, {\n          continuous: continuous,\n          index: index,\n          isLastStep: isLastStep,\n          size: size,\n          step: cleanStep\n        });\n\n        var TooltipComponent = tooltipComponent;\n        component = /*#__PURE__*/React.createElement(TooltipComponent, renderProps);\n      } else {\n        component = /*#__PURE__*/React.createElement(JoyrideTooltipContainer, _extends({}, this.getElementsProps(), {\n          continuous: continuous,\n          index: index,\n          isLastStep: isLastStep,\n          size: size,\n          step: step\n        }));\n      }\n\n      return component;\n    }\n  }]);\n\n  return JoyrideTooltip;\n}(React.Component);\n\nvar JoyridePortal = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyridePortal, _React$Component);\n\n  var _super = _createSuper(JoyridePortal);\n\n  function JoyridePortal(props) {\n    var _this;\n\n    _classCallCheck(this, JoyridePortal);\n\n    _this = _super.call(this, props);\n    if (!canUseDOM) return _possibleConstructorReturn(_this);\n    _this.node = document.createElement('div');\n    /* istanbul ignore else */\n\n    if (props.id) {\n      _this.node.id = props.id;\n    }\n\n    document.body.appendChild(_this.node);\n    return _this;\n  }\n\n  _createClass(JoyridePortal, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n\n      if (!isReact16) {\n        this.renderReact15();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!canUseDOM) return;\n\n      if (!isReact16) {\n        this.renderReact15();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (!canUseDOM || !this.node) return;\n\n      if (!isReact16) {\n        ReactDOM.unmountComponentAtNode(this.node);\n      }\n\n      document.body.removeChild(this.node);\n    }\n  }, {\n    key: \"renderReact15\",\n    value: function renderReact15() {\n      if (!canUseDOM) return null;\n      var children = this.props.children;\n      ReactDOM.unstable_renderSubtreeIntoContainer(this, children, this.node);\n      return null;\n    }\n  }, {\n    key: \"renderReact16\",\n    value: function renderReact16() {\n      if (!canUseDOM || !isReact16) return null;\n      var children = this.props.children;\n      return /*#__PURE__*/ReactDOM.createPortal(children, this.node);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!isReact16) {\n        return null;\n      }\n\n      return this.renderReact16();\n    }\n  }]);\n\n  return JoyridePortal;\n}(React.Component);\n\nvar JoyrideStep = /*#__PURE__*/function (_React$Component) {\n  _inherits(JoyrideStep, _React$Component);\n\n  var _super = _createSuper(JoyrideStep);\n\n  function JoyrideStep() {\n    var _this;\n\n    _classCallCheck(this, JoyrideStep);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _defineProperty(_assertThisInitialized(_this), \"scope\", {\n      removeScope: function removeScope() {}\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickHoverBeacon\", function (e) {\n      var _this$props = _this.props,\n          step = _this$props.step,\n          update = _this$props.update;\n\n      if (e.type === 'mouseenter' && step.event !== 'hover') {\n        return;\n      }\n\n      update({\n        lifecycle: LIFECYCLE.TOOLTIP\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleClickOverlay\", function () {\n      var _this$props2 = _this.props,\n          helpers = _this$props2.helpers,\n          step = _this$props2.step;\n\n      if (!step.disableOverlayClose) {\n        helpers.close();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setTooltipRef\", function (c) {\n      _this.tooltip = c;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setPopper\", function (popper, type) {\n      var _this$props3 = _this.props,\n          action = _this$props3.action,\n          setPopper = _this$props3.setPopper,\n          update = _this$props3.update;\n\n      if (type === 'wrapper') {\n        _this.beaconPopper = popper;\n      } else {\n        _this.tooltipPopper = popper;\n      }\n\n      setPopper(popper, type);\n\n      if (_this.beaconPopper && _this.tooltipPopper) {\n        update({\n          action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : action,\n          lifecycle: LIFECYCLE.READY\n        });\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(JoyrideStep, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props4 = this.props,\n          debug = _this$props4.debug,\n          index = _this$props4.index;\n      log({\n        title: \"step:\".concat(index),\n        data: [{\n          key: 'props',\n          value: this.props\n        }],\n        debug: debug\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props5 = this.props,\n          action = _this$props5.action,\n          callback = _this$props5.callback,\n          continuous = _this$props5.continuous,\n          controlled = _this$props5.controlled,\n          debug = _this$props5.debug,\n          index = _this$props5.index,\n          lifecycle = _this$props5.lifecycle,\n          size = _this$props5.size,\n          status = _this$props5.status,\n          step = _this$props5.step,\n          update = _this$props5.update;\n\n      var _treeChanges = treeChanges(prevProps, this.props),\n          changed = _treeChanges.changed,\n          changedFrom = _treeChanges.changedFrom;\n\n      var state = {\n        action: action,\n        controlled: controlled,\n        index: index,\n        lifecycle: lifecycle,\n        size: size,\n        status: status\n      };\n      var skipBeacon = continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV);\n      var hasStoreChanged = changed('action') || changed('index') || changed('lifecycle') || changed('status');\n      var hasStarted = changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT);\n      var isAfterAction = changed('action', [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]);\n\n      if (isAfterAction && (hasStarted || controlled)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          index: prevProps.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: prevProps.step,\n          type: EVENTS.STEP_AFTER\n        }));\n      }\n\n      if (changed('index') && index > 0 && lifecycle === LIFECYCLE.INIT && status === STATUS.RUNNING && step.placement === 'center') {\n        update({\n          lifecycle: LIFECYCLE.READY\n        });\n      } // There's a step to use, but there's no target in the DOM\n\n\n      if (hasStoreChanged && step) {\n        var element = getElement(step.target);\n        var elementExists = !!element;\n        var hasRenderedTarget = elementExists && isElementVisible(element);\n\n        if (hasRenderedTarget) {\n          if (changedFrom('status', STATUS.READY, STATUS.RUNNING) || changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n            callback(_objectSpread2(_objectSpread2({}, state), {}, {\n              step: step,\n              type: EVENTS.STEP_BEFORE\n            }));\n          }\n        } else {\n          console.warn(elementExists ? 'Target not visible' : 'Target not mounted', step); //eslint-disable-line no-console\n\n          callback(_objectSpread2(_objectSpread2({}, state), {}, {\n            type: EVENTS.TARGET_NOT_FOUND,\n            step: step\n          }));\n\n          if (!controlled) {\n            update({\n              index: index + ([ACTIONS.PREV].indexOf(action) !== -1 ? -1 : 1)\n            });\n          }\n        }\n      }\n\n      if (changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n        update({\n          lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON\n        });\n      }\n\n      if (changed('index')) {\n        log({\n          title: \"step:\".concat(lifecycle),\n          data: [{\n            key: 'props',\n            value: this.props\n          }],\n          debug: debug\n        });\n      }\n      /* istanbul ignore else */\n\n\n      if (changed('lifecycle', LIFECYCLE.BEACON)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          step: step,\n          type: EVENTS.BEACON\n        }));\n      }\n\n      if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n        callback(_objectSpread2(_objectSpread2({}, state), {}, {\n          step: step,\n          type: EVENTS.TOOLTIP\n        }));\n        this.scope = new Scope(this.tooltip, {\n          selector: '[data-action=primary]'\n        });\n        this.scope.setFocus();\n      }\n\n      if (changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT)) {\n        this.scope.removeScope();\n        delete this.beaconPopper;\n        delete this.tooltipPopper;\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.scope.removeScope();\n    }\n    /**\n     * Beacon click/hover event listener\n     *\n     * @param {Event} e\n     */\n\n  }, {\n    key: \"open\",\n    get: function get() {\n      var _this$props6 = this.props,\n          step = _this$props6.step,\n          lifecycle = _this$props6.lifecycle;\n      return !!(hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props7 = this.props,\n          continuous = _this$props7.continuous,\n          debug = _this$props7.debug,\n          helpers = _this$props7.helpers,\n          index = _this$props7.index,\n          lifecycle = _this$props7.lifecycle,\n          nonce = _this$props7.nonce,\n          shouldScroll = _this$props7.shouldScroll,\n          size = _this$props7.size,\n          step = _this$props7.step;\n      var target = getElement(step.target);\n\n      if (!validateStep(step) || !is.domElement(target)) {\n        return null;\n      }\n\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: \"JoyrideStep-\".concat(index),\n        className: \"react-joyride__step\"\n      }, /*#__PURE__*/React.createElement(JoyridePortal, {\n        id: \"react-joyride-portal\"\n      }, /*#__PURE__*/React.createElement(JoyrideOverlay, _extends({}, step, {\n        debug: debug,\n        lifecycle: lifecycle,\n        onClickOverlay: this.handleClickOverlay\n      }))), /*#__PURE__*/React.createElement(Floater, _extends({\n        component: /*#__PURE__*/React.createElement(JoyrideTooltip, {\n          continuous: continuous,\n          helpers: helpers,\n          index: index,\n          isLastStep: index + 1 === size,\n          setTooltipRef: this.setTooltipRef,\n          size: size,\n          step: step\n        }),\n        debug: debug,\n        getPopper: this.setPopper,\n        id: \"react-joyride-step-\".concat(index),\n        isPositioned: step.isFixed || hasPosition(target),\n        open: this.open,\n        placement: step.placement,\n        target: step.target\n      }, step.floaterProps), /*#__PURE__*/React.createElement(JoyrideBeacon, {\n        beaconComponent: step.beaconComponent,\n        locale: step.locale,\n        nonce: nonce,\n        onClickOrHover: this.handleClickHoverBeacon,\n        shouldFocus: shouldScroll,\n        styles: step.styles\n      })));\n    }\n  }]);\n\n  return JoyrideStep;\n}(React.Component);\n\nvar Joyride = /*#__PURE__*/function (_React$Component) {\n  _inherits(Joyride, _React$Component);\n\n  var _super = _createSuper(Joyride);\n\n  function Joyride(props) {\n    var _this;\n\n    _classCallCheck(this, Joyride);\n\n    _this = _super.call(this, props);\n\n    _defineProperty(_assertThisInitialized(_this), \"initStore\", function () {\n      var _this$props = _this.props,\n          debug = _this$props.debug,\n          getHelpers = _this$props.getHelpers,\n          run = _this$props.run,\n          stepIndex = _this$props.stepIndex;\n      _this.store = new createStore(_objectSpread2(_objectSpread2({}, _this.props), {}, {\n        controlled: run && is.number(stepIndex)\n      }));\n      _this.helpers = _this.store.getHelpers();\n      var addListener = _this.store.addListener;\n      log({\n        title: 'init',\n        data: [{\n          key: 'props',\n          value: _this.props\n        }, {\n          key: 'state',\n          value: _this.state\n        }],\n        debug: debug\n      }); // Sync the store to this component's state.\n\n      addListener(_this.syncState);\n      getHelpers(_this.helpers);\n      return _this.store.getState();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"callback\", function (data) {\n      var callback = _this.props.callback;\n      /* istanbul ignore else */\n\n      if (is[\"function\"](callback)) {\n        callback(data);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleKeyboard\", function (e) {\n      var _this$state = _this.state,\n          index = _this$state.index,\n          lifecycle = _this$state.lifecycle;\n      var steps = _this.props.steps;\n      var step = steps[index];\n      var intKey = window.Event ? e.which : e.keyCode;\n\n      if (lifecycle === LIFECYCLE.TOOLTIP) {\n        if (intKey === 27 && step && !step.disableCloseOnEsc) {\n          _this.store.close();\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"syncState\", function (state) {\n      _this.setState(state);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setPopper\", function (popper, type) {\n      if (type === 'wrapper') {\n        _this.beaconPopper = popper;\n      } else {\n        _this.tooltipPopper = popper;\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"shouldScroll\", function (disableScrolling, index, scrollToFirstStep, lifecycle, step, target, prevState) {\n      return !disableScrolling && (index !== 0 || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) && step.placement !== 'center' && (!step.isFixed || !hasPosition(target)) && // fixed steps don't need to scroll\n      prevState.lifecycle !== lifecycle && [LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP].indexOf(lifecycle) !== -1;\n    });\n\n    _this.state = _this.initStore();\n    return _this;\n  }\n\n  _createClass(Joyride, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!canUseDOM) return;\n      var _this$props2 = this.props,\n          disableCloseOnEsc = _this$props2.disableCloseOnEsc,\n          debug = _this$props2.debug,\n          run = _this$props2.run,\n          steps = _this$props2.steps;\n      var start = this.store.start;\n\n      if (validateSteps(steps, debug) && run) {\n        start();\n      }\n      /* istanbul ignore else */\n\n\n      if (!disableCloseOnEsc) {\n        document.body.addEventListener('keydown', this.handleKeyboard, {\n          passive: true\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      if (!canUseDOM) return;\n      var _this$state2 = this.state,\n          action = _this$state2.action,\n          controlled = _this$state2.controlled,\n          index = _this$state2.index,\n          lifecycle = _this$state2.lifecycle,\n          status = _this$state2.status;\n      var _this$props3 = this.props,\n          debug = _this$props3.debug,\n          run = _this$props3.run,\n          stepIndex = _this$props3.stepIndex,\n          steps = _this$props3.steps;\n      var prevSteps = prevProps.steps,\n          prevStepIndex = prevProps.stepIndex;\n      var _this$store = this.store,\n          reset = _this$store.reset,\n          setSteps = _this$store.setSteps,\n          start = _this$store.start,\n          stop = _this$store.stop,\n          update = _this$store.update;\n\n      var _treeChanges = treeChanges(prevProps, this.props),\n          changedProps = _treeChanges.changed;\n\n      var _treeChanges2 = treeChanges(prevState, this.state),\n          changed = _treeChanges2.changed,\n          changedFrom = _treeChanges2.changedFrom;\n\n      var step = getMergedStep(steps[index], this.props);\n      var stepsChanged = !isEqual(prevSteps, steps);\n      var stepIndexChanged = is.number(stepIndex) && changedProps('stepIndex');\n      var target = getElement(step === null || step === void 0 ? void 0 : step.target);\n\n      if (stepsChanged) {\n        if (validateSteps(steps, debug)) {\n          setSteps(steps);\n        } else {\n          console.warn('Steps are not valid', steps); //eslint-disable-line no-console\n        }\n      }\n      /* istanbul ignore else */\n\n\n      if (changedProps('run')) {\n        if (run) {\n          start(stepIndex);\n        } else {\n          stop();\n        }\n      }\n      /* istanbul ignore else */\n\n\n      if (stepIndexChanged) {\n        var nextAction = prevStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;\n\n        if (action === ACTIONS.STOP) {\n          nextAction = ACTIONS.START;\n        }\n\n        if (!([STATUS.FINISHED, STATUS.SKIPPED].indexOf(status) !== -1)) {\n          update({\n            action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction,\n            index: stepIndex,\n            lifecycle: LIFECYCLE.INIT\n          });\n        }\n      } // Update the index if the first step is not found\n\n\n      if (!controlled && status === STATUS.RUNNING && index === 0 && !target) {\n        update({\n          index: index + 1\n        });\n        this.callback(_objectSpread2(_objectSpread2({}, this.state), {}, {\n          type: EVENTS.TARGET_NOT_FOUND,\n          step: step\n        }));\n      }\n\n      var callbackData = _objectSpread2(_objectSpread2({}, this.state), {}, {\n        index: index,\n        step: step\n      });\n\n      var isAfterAction = changed('action', [ACTIONS.NEXT, ACTIONS.PREV, ACTIONS.SKIP, ACTIONS.CLOSE]);\n\n      if (isAfterAction && changed('status', STATUS.PAUSED)) {\n        var prevStep = getMergedStep(steps[prevState.index], this.props);\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          index: prevState.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: prevStep,\n          type: EVENTS.STEP_AFTER\n        }));\n      }\n\n      if (changed('status', [STATUS.FINISHED, STATUS.SKIPPED])) {\n        var _prevStep = getMergedStep(steps[prevState.index], this.props);\n\n        if (!controlled) {\n          this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n            index: prevState.index,\n            lifecycle: LIFECYCLE.COMPLETE,\n            step: _prevStep,\n            type: EVENTS.STEP_AFTER\n          }));\n        }\n\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          index: prevState.index,\n          // Return the last step when the tour is finished\n          step: _prevStep,\n          type: EVENTS.TOUR_END\n        }));\n        reset();\n      } else if (changedFrom('status', [STATUS.IDLE, STATUS.READY], STATUS.RUNNING)) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_START\n        }));\n      } else if (changed('status')) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_STATUS\n        }));\n      } else if (changed('action', ACTIONS.RESET)) {\n        this.callback(_objectSpread2(_objectSpread2({}, callbackData), {}, {\n          type: EVENTS.TOUR_STATUS\n        }));\n      }\n\n      if (step) {\n        this.scrollToStep(prevState);\n\n        if (action === ACTIONS.START && lifecycle === LIFECYCLE.INIT && status === STATUS.RUNNING && step.placement === 'center') {\n          update({\n            lifecycle: LIFECYCLE.READY\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var disableCloseOnEsc = this.props.disableCloseOnEsc;\n      /* istanbul ignore else */\n\n      if (!disableCloseOnEsc) {\n        document.body.removeEventListener('keydown', this.handleKeyboard);\n      }\n    }\n  }, {\n    key: \"scrollToStep\",\n    value: function scrollToStep(prevState) {\n      var _this$state3 = this.state,\n          index = _this$state3.index,\n          lifecycle = _this$state3.lifecycle,\n          status = _this$state3.status;\n      var _this$props4 = this.props,\n          debug = _this$props4.debug,\n          disableScrolling = _this$props4.disableScrolling,\n          disableScrollParentFix = _this$props4.disableScrollParentFix,\n          scrollToFirstStep = _this$props4.scrollToFirstStep,\n          scrollOffset = _this$props4.scrollOffset,\n          scrollDuration = _this$props4.scrollDuration,\n          steps = _this$props4.steps;\n      var step = getMergedStep(steps[index], this.props);\n      /* istanbul ignore else */\n\n      if (step) {\n        var target = getElement(step.target);\n        var shouldScroll = this.shouldScroll(disableScrolling, index, scrollToFirstStep, lifecycle, step, target, prevState);\n\n        if (status === STATUS.RUNNING && shouldScroll) {\n          var hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix);\n          var scrollParent = getScrollParent(target, disableScrollParentFix);\n          var scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;\n          log({\n            title: 'scrollToStep',\n            data: [{\n              key: 'index',\n              value: index\n            }, {\n              key: 'lifecycle',\n              value: lifecycle\n            }, {\n              key: 'status',\n              value: status\n            }],\n            debug: debug\n          });\n          /* istanbul ignore else */\n\n          if (lifecycle === LIFECYCLE.BEACON && this.beaconPopper) {\n            var _this$beaconPopper = this.beaconPopper,\n                placement = _this$beaconPopper.placement,\n                popper = _this$beaconPopper.popper;\n            /* istanbul ignore else */\n\n            if (!(['bottom'].indexOf(placement) !== -1) && !hasCustomScroll) {\n              scrollY = Math.floor(popper.top - scrollOffset);\n            }\n          } else if (lifecycle === LIFECYCLE.TOOLTIP && this.tooltipPopper) {\n            var _this$tooltipPopper = this.tooltipPopper,\n                flipped = _this$tooltipPopper.flipped,\n                _placement = _this$tooltipPopper.placement,\n                _popper = _this$tooltipPopper.popper;\n\n            if (['top', 'right', 'left'].indexOf(_placement) !== -1 && !flipped && !hasCustomScroll) {\n              scrollY = Math.floor(_popper.top - scrollOffset);\n            } else {\n              scrollY -= step.spotlightPadding;\n            }\n          }\n\n          scrollY = scrollY >= 0 ? scrollY : 0;\n          /* istanbul ignore else */\n\n          if (status === STATUS.RUNNING) {\n            scrollTo(scrollY, scrollParent, scrollDuration);\n          }\n        }\n      }\n    }\n    /**\n     * Trigger the callback.\n     *\n     * @private\n     * @param {Object} data\n     */\n\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!canUseDOM) return null;\n      var _this$state4 = this.state,\n          index = _this$state4.index,\n          status = _this$state4.status;\n      var _this$props5 = this.props,\n          continuous = _this$props5.continuous,\n          debug = _this$props5.debug,\n          nonce = _this$props5.nonce,\n          scrollToFirstStep = _this$props5.scrollToFirstStep,\n          steps = _this$props5.steps;\n      var step = getMergedStep(steps[index], this.props);\n      var output;\n\n      if (status === STATUS.RUNNING && step) {\n        output = /*#__PURE__*/React.createElement(JoyrideStep, _extends({}, this.state, {\n          callback: this.callback,\n          continuous: continuous,\n          debug: debug,\n          setPopper: this.setPopper,\n          helpers: this.helpers,\n          nonce: nonce,\n          shouldScroll: !step.disableScrolling && (index !== 0 || scrollToFirstStep),\n          step: step,\n          update: this.store.update\n        }));\n      }\n\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"react-joyride\"\n      }, output);\n    }\n  }]);\n\n  return Joyride;\n}(React.Component);\n\n_defineProperty(Joyride, \"defaultProps\", {\n  continuous: false,\n  debug: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrolling: false,\n  disableScrollParentFix: false,\n  getHelpers: function getHelpers() {},\n  hideBackButton: false,\n  run: true,\n  scrollOffset: 20,\n  scrollDuration: 300,\n  scrollToFirstStep: false,\n  showSkipButton: false,\n  showProgress: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n  steps: []\n});\n\nexport { ACTIONS, EVENTS, LIFECYCLE, STATUS, Joyride as default };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,cAAc,QAAQ,OAAO;AAC7C,OAAO,YAAY;AACnB,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,EAAE,MAAM,SAAS;AACxB,OAAOC,QAAQ,IAAIC,YAAY,QAAQ,WAAW;AAClD,OAAOC,oBAAoB,MAAM,OAAO;AACxC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,YAAY,MAAM,cAAc;AACvC,SAASC,kBAAkB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,QAAQ,UAAU;AAC1E,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,OAAO,MAAM,eAAe;AAEnC,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClDC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MACzD,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAChE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EACrC;EAEA,OAAOH,IAAI;AACb;AAEA,SAASU,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrDA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACzDC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACjKhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAClF,CAAC,CAAC;EACJ;EAEA,OAAON,MAAM;AACf;AAEA,SAASW,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiBA,CAACf,MAAM,EAAEgB,KAAK,EAAE;EACxC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IACzBgB,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IACtDqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IACrD7B,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEiB,UAAU,CAACX,GAAG,EAAEW,UAAU,CAAC;EAC3D;AACF;AAEA,SAASG,YAAYA,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEN,iBAAiB,CAACF,WAAW,CAACU,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEP,iBAAiB,CAACF,WAAW,EAAES,WAAW,CAAC;EAC5DhC,MAAM,CAACoB,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAC9CM,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAON,WAAW;AACpB;AAEA,SAASN,eAAeA,CAACiB,GAAG,EAAElB,GAAG,EAAEmB,KAAK,EAAE;EACxC,IAAInB,GAAG,IAAIkB,GAAG,EAAE;IACdlC,MAAM,CAACoB,cAAc,CAACc,GAAG,EAAElB,GAAG,EAAE;MAC9BmB,KAAK,EAAEA,KAAK;MACZ7B,UAAU,EAAE,IAAI;MAChBsB,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLK,GAAG,CAAClB,GAAG,CAAC,GAAGmB,KAAK;EAClB;EAEA,OAAOD,GAAG;AACZ;AAEA,SAASE,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGpC,MAAM,CAACqC,MAAM,IAAI,UAAU3B,MAAM,EAAE;IAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAEzB,KAAK,IAAIK,GAAG,IAAIF,MAAM,EAAE;QACtB,IAAId,MAAM,CAACiC,SAAS,CAACK,cAAc,CAACC,IAAI,CAACzB,MAAM,EAAEE,GAAG,CAAC,EAAE;UACrDN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;QAC3B;MACF;IACF;IAEA,OAAON,MAAM;EACf,CAAC;EAED,OAAO0B,QAAQ,CAAC5B,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;AACxC;AAEA,SAAS4B,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIlB,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAiB,QAAQ,CAACR,SAAS,GAAGjC,MAAM,CAAC2C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACT,SAAS,EAAE;IACrEW,WAAW,EAAE;MACXT,KAAK,EAAEM,QAAQ;MACfZ,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF5B,MAAM,CAACoB,cAAc,CAACqB,QAAQ,EAAE,WAAW,EAAE;IAC3CZ,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,IAAIa,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASI,eAAeA,CAACC,CAAC,EAAE;EAC1BD,eAAe,GAAG9C,MAAM,CAACgD,cAAc,GAAGhD,MAAM,CAACiD,cAAc,GAAG,SAASH,eAAeA,CAACC,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAIlD,MAAM,CAACiD,cAAc,CAACF,CAAC,CAAC;EAChD,CAAC;EACD,OAAOD,eAAe,CAACC,CAAC,CAAC;AAC3B;AAEA,SAASF,eAAeA,CAACE,CAAC,EAAEI,CAAC,EAAE;EAC7BN,eAAe,GAAG7C,MAAM,CAACgD,cAAc,IAAI,SAASH,eAAeA,CAACE,CAAC,EAAEI,CAAC,EAAE;IACxEJ,CAAC,CAACG,SAAS,GAAGC,CAAC;IACf,OAAOJ,CAAC;EACV,CAAC;EAED,OAAOF,eAAe,CAACE,CAAC,EAAEI,CAAC,CAAC;AAC9B;AAEA,SAASC,yBAAyBA,CAAA,EAAG;EACnC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EACtE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAE5C,IAAI;IACFC,OAAO,CAACxB,SAAS,CAACyB,OAAO,CAACnB,IAAI,CAACc,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASC,6BAA6BA,CAAC9C,MAAM,EAAE+C,QAAQ,EAAE;EACvD,IAAI/C,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIoD,UAAU,GAAG9D,MAAM,CAACD,IAAI,CAACe,MAAM,CAAC;EACpC,IAAIE,GAAG,EAAEL,CAAC;EAEV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,UAAU,CAACjD,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCK,GAAG,GAAG8C,UAAU,CAACnD,CAAC,CAAC;IACnB,IAAIkD,QAAQ,CAACE,OAAO,CAAC/C,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAC3B;EAEA,OAAON,MAAM;AACf;AAEA,SAASsD,wBAAwBA,CAAClD,MAAM,EAAE+C,QAAQ,EAAE;EAClD,IAAI/C,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIJ,MAAM,GAAGkD,6BAA6B,CAAC9C,MAAM,EAAE+C,QAAQ,CAAC;EAE5D,IAAI7C,GAAG,EAAEL,CAAC;EAEV,IAAIX,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIgE,gBAAgB,GAAGjE,MAAM,CAACC,qBAAqB,CAACa,MAAM,CAAC;IAE3D,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,gBAAgB,CAACpD,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CK,GAAG,GAAGiD,gBAAgB,CAACtD,CAAC,CAAC;MACzB,IAAIkD,QAAQ,CAACE,OAAO,CAAC/C,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAAChB,MAAM,CAACiC,SAAS,CAACiC,oBAAoB,CAAC3B,IAAI,CAACzB,MAAM,EAAEE,GAAG,CAAC,EAAE;MAC9DN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC3B;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASyD,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,0BAA0BA,CAACF,IAAI,EAAE7B,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACpE,OAAOA,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIf,SAAS,CAAC,0DAA0D,CAAC;EACjF;EAEA,OAAO2C,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASG,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,yBAAyB,GAAGrB,yBAAyB,CAAC,CAAC;EAE3D,OAAO,SAASsB,oBAAoBA,CAAA,EAAG;IACrC,IAAIC,KAAK,GAAG7B,eAAe,CAAC0B,OAAO,CAAC;MAChCI,MAAM;IAEV,IAAIH,yBAAyB,EAAE;MAC7B,IAAII,SAAS,GAAG/B,eAAe,CAAC,IAAI,CAAC,CAACF,WAAW;MAEjDgC,MAAM,GAAGvB,OAAO,CAACC,SAAS,CAACqB,KAAK,EAAE/D,SAAS,EAAEiE,SAAS,CAAC;IACzD,CAAC,MAAM;MACLD,MAAM,GAAGD,KAAK,CAACnE,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;IACvC;IAEA,OAAO0D,0BAA0B,CAAC,IAAI,EAAEM,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,IAAIE,OAAO,GAAG;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,IAAI;EACRC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;AACV,CAAC;AAED,IAAIC,MAAM,GAAG;EACXC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE,wBAAwB;EAC1CC,KAAK,EAAE;AACT,CAAC;AAED,IAAIC,SAAS,GAAG;EACdpB,IAAI,EAAE,MAAM;EACZqB,KAAK,EAAE,OAAO;EACdR,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBQ,QAAQ,EAAE,UAAU;EACpBH,KAAK,EAAE;AACT,CAAC;AAED,IAAII,MAAM,GAAG;EACXC,IAAI,EAAE,MAAM;EACZH,KAAK,EAAE,OAAO;EACdI,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBV,KAAK,EAAE;AACT,CAAC;AAED,IAAIW,SAAS,GAAG1H,oBAAoB,CAAC0H,SAAS;AAC9C,IAAIC,SAAS,GAAG5H,YAAY,KAAK6H,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAIC,SAAS,GAAGrG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAGsG,SAAS,CAACD,SAAS;EACvG,IAAIE,OAAO,GAAGF,SAAS;EAEvB,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;IACjCD,OAAO,GAAG,MAAM;EAClB,CAAC,MAAM,IAAIE,QAAQ,CAACC,YAAY,EAAE;IAChCH,OAAO,GAAG,IAAI;EAChB,CAAC,MAAM,IAAI,MAAM,CAACI,IAAI,CAACN,SAAS,CAAC,EAAE;IACjCE,OAAO,GAAG,MAAM;EAClB,CAAC,CAAC;EAAA,KACG,IAAI1D,OAAO,CAAC2D,MAAM,CAACI,KAAK,CAAC,IAAIP,SAAS,CAAClD,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;IACjEoD,OAAO,GAAG,OAAO;EACnB,CAAC,CAAC;EAAA,KACG,IAAI,OAAOC,MAAM,CAACK,cAAc,KAAK,WAAW,EAAE;IACrDN,OAAO,GAAG,SAAS;EACrB,CAAC,CAAC;EAAA,KACG,IAAIC,MAAM,CAACM,MAAM,EAAE;IACtBP,OAAO,GAAG,QAAQ;EACpB,CAAC,CAAC;EAAA,KACG,IAAI,qDAAqD,CAACI,IAAI,CAACN,SAAS,CAAC,EAAE;IAC9EE,OAAO,GAAG,QAAQ;EACpB;EAEA,OAAOA,OAAO;AAChB;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASQ,aAAaA,CAACxF,KAAK,EAAE;EAC5B,OAAOnC,MAAM,CAACiC,SAAS,CAAC2F,QAAQ,CAACrF,IAAI,CAACJ,KAAK,CAAC,CAAC0F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,OAAO,GAAG,EAAE;EAEhB,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpC;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC1DF,OAAO,CAAC1H,IAAI,CAAC4H,KAAK,CAAC;IACrB,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MAC/BA,KAAK,CAACpH,OAAO,CAAC,UAAUuH,CAAC,EAAE;QACzB,OAAOJ,OAAO,CAACI,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIH,KAAK,IAAIA,KAAK,CAACzG,KAAK,EAAE;MAC/B,IAAI6G,QAAQ,GAAGJ,KAAK,CAACzG,KAAK,CAAC6G,QAAQ;MAEnC,IAAIH,KAAK,CAACC,OAAO,CAACE,QAAQ,CAAC,EAAE;QAC3BA,QAAQ,CAACxH,OAAO,CAAC,UAAUuH,CAAC,EAAE;UAC5B,OAAOJ,OAAO,CAACI,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLJ,OAAO,CAACK,QAAQ,CAAC;MACnB;IACF;EACF,CAAC;EAEDL,OAAO,CAACF,IAAI,CAAC;EACb,OAAOC,OAAO,CAACO,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;AACjC;AACA,SAASnG,cAAcA,CAACH,KAAK,EAAEnB,GAAG,EAAE;EAClC,OAAOhB,MAAM,CAACiC,SAAS,CAACK,cAAc,CAACC,IAAI,CAACJ,KAAK,EAAEnB,GAAG,CAAC;AACzD;AACA,SAAS0H,YAAYA,CAACvG,KAAK,EAAEpC,IAAI,EAAE;EACjC,IAAI,CAACf,EAAE,CAAC2J,WAAW,CAACxG,KAAK,CAAC,IAAI,CAACnD,EAAE,CAAC4J,KAAK,CAAC7I,IAAI,CAAC,EAAE;IAC7C,OAAO,KAAK;EACd;EAEA,OAAOC,MAAM,CAACD,IAAI,CAACoC,KAAK,CAAC,CAAC0G,KAAK,CAAC,UAAUC,CAAC,EAAE;IAC3C,OAAO/I,IAAI,CAACgE,OAAO,CAAC+E,CAAC,CAAC,KAAK,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,IAAIC,cAAc,GAAG,kCAAkC;EACvD,IAAIC,SAAS,GAAGF,GAAG,CAACG,OAAO,CAACF,cAAc,EAAE,UAAUG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAChE,OAAOF,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;EAC9B,CAAC,CAAC;EACF,IAAI3E,MAAM,GAAG,2CAA2C,CAAC4E,IAAI,CAACN,SAAS,CAAC;EACxE,OAAOtE,MAAM,GAAG,CAAC6E,QAAQ,CAAC7E,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE6E,QAAQ,CAAC7E,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE6E,QAAQ,CAAC7E,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS8E,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,SAAS,KAAK,QAAQ;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC5B,IAAIC,IAAI;EACR,IAAIC,eAAe,GAAG,aAAapL,cAAc,CAACiL,IAAI,CAAC,IAAI,aAAajL,cAAc,CAACkL,KAAK,CAAC;EAC7F,IAAIG,YAAY,GAAGnL,EAAE,CAAC+H,SAAS,CAACgD,IAAI,CAAC,IAAI/K,EAAE,CAAC+H,SAAS,CAACiD,KAAK,CAAC;EAE5D,IAAIrC,aAAa,CAACoC,IAAI,CAAC,KAAKpC,aAAa,CAACqC,KAAK,CAAC,IAAIE,eAAe,IAAIC,YAAY,EAAE;IACnF,OAAO,KAAK;EACd;EAEA,IAAInL,EAAE,CAACoL,UAAU,CAACL,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACM,UAAU,CAACL,KAAK,CAAC;EAC/B;EAEA,IAAIhL,EAAE,CAACsL,MAAM,CAACP,IAAI,CAAC,EAAE;IACnB,OAAOA,IAAI,KAAKC,KAAK;EACvB;EAEA,IAAIhL,EAAE,CAAC,UAAU,CAAC,CAAC+K,IAAI,CAAC,EAAE;IACxB,OAAOA,IAAI,CAACnC,QAAQ,CAAC,CAAC,KAAKoC,KAAK,CAACpC,QAAQ,CAAC,CAAC;EAC7C;EAEA,KAAK,IAAI5G,GAAG,IAAI+I,IAAI,EAAE;IACpB;IACA,IAAIzH,cAAc,CAACyH,IAAI,EAAE/I,GAAG,CAAC,EAAE;MAC7B,IAAI,OAAO+I,IAAI,CAAC/I,GAAG,CAAC,KAAK,WAAW,IAAI,OAAOgJ,KAAK,CAAChJ,GAAG,CAAC,KAAK,WAAW,EAAE;QACzE,OAAO,KAAK;MACd;MAEAiJ,IAAI,GAAGtC,aAAa,CAACoC,IAAI,CAAC/I,GAAG,CAAC,CAAC;MAE/B,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC+C,OAAO,CAACkG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIH,OAAO,CAACC,IAAI,CAAC/I,GAAG,CAAC,EAAEgJ,KAAK,CAAChJ,GAAG,CAAC,CAAC,EAAE;QAC9E;MACF;MAEA,IAAIiJ,IAAI,KAAK,UAAU,IAAIH,OAAO,CAACC,IAAI,CAAC/I,GAAG,CAAC,EAAEgJ,KAAK,CAAChJ,GAAG,CAAC,CAAC,EAAE;QACzD;MACF;MAEA,IAAI+I,IAAI,CAAC/I,GAAG,CAAC,KAAKgJ,KAAK,CAAChJ,GAAG,CAAC,EAAE;QAC5B,OAAO,KAAK;MACd;IACF;EACF;EAEA,KAAK,IAAImC,CAAC,IAAI6G,KAAK,EAAE;IACnB;IACA,IAAI1H,cAAc,CAAC0H,KAAK,EAAE7G,CAAC,CAAC,EAAE;MAC5B,IAAI,OAAO4G,IAAI,CAAC5G,CAAC,CAAC,KAAK,WAAW,EAAE;QAClC,OAAO,KAAK;MACd;IACF;EACF;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASoH,QAAQA,CAAA,EAAG;EAClB,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAACxG,OAAO,CAACiD,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASwD,GAAGA,CAACC,IAAI,EAAE;EACjB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,SAAS,GAAGH,IAAI,CAACI,IAAI;IACrBA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,SAAS;IAC/CE,UAAU,GAAGL,IAAI,CAACM,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,UAAU;;EAEtD;EACA,IAAIE,KAAK,GAAGH,IAAI,GAAGI,OAAO,CAACJ,IAAI,IAAII,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACT,GAAG;EAE9D,IAAIO,KAAK,EAAE;IACT,IAAIL,KAAK,IAAIC,IAAI,EAAE;MACjBM,OAAO,CAACE,cAAc,CAAC,mBAAmB,CAACC,MAAM,CAACV,KAAK,CAAC,EAAE,qDAAqD,CAAC;MAEhH,IAAItC,KAAK,CAACC,OAAO,CAACsC,IAAI,CAAC,EAAE;QACvBA,IAAI,CAAC5J,OAAO,CAAC,UAAU+H,CAAC,EAAE;UACxB,IAAI9J,EAAE,CAAC2J,WAAW,CAACG,CAAC,CAAC,IAAIA,CAAC,CAAC9H,GAAG,EAAE;YAC9BgK,KAAK,CAACxK,KAAK,CAACyK,OAAO,EAAE,CAACnC,CAAC,CAAC9H,GAAG,EAAE8H,CAAC,CAAC3G,KAAK,CAAC,CAAC;UACxC,CAAC,MAAM;YACL6I,KAAK,CAACxK,KAAK,CAACyK,OAAO,EAAE,CAACnC,CAAC,CAAC,CAAC;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLkC,KAAK,CAACxK,KAAK,CAACyK,OAAO,EAAE,CAACN,IAAI,CAAC,CAAC;MAC9B;MAEAM,OAAO,CAACI,QAAQ,CAAC,CAAC;IACpB,CAAC,MAAM;MACLJ,OAAO,CAACC,KAAK,CAAC,6BAA6B,CAAC;IAC9C;EACF;EACA;AAEF;AAEA,IAAII,YAAY,GAAG;EACjBC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAEvF,SAAS,CAACpB,IAAI;EACzB4G,IAAI,EAAE,CAAC;EACPC,MAAM,EAAEtF,MAAM,CAACC;AACjB,CAAC;AACD,IAAIsF,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;AAC1D,SAASC,WAAWA,CAACpK,KAAK,EAAE;EAC1B,IAAIqK,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,IAAIrB,IAAI,GAAG,IAAIqB,GAAG,CAAC,CAAC;EAEpB,IAAIC,KAAK,GAAG,aAAa,YAAY;IACnC,SAASA,KAAKA,CAAA,EAAG;MACf,IAAIC,KAAK,GAAG,IAAI;MAEhB,IAAIzB,IAAI,GAAG7J,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7EuL,eAAe,GAAG1B,IAAI,CAAC2B,UAAU;QACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;QACjEE,SAAS,GAAG5B,IAAI,CAAC4B,SAAS;QAC1BC,UAAU,GAAG7B,IAAI,CAAC8B,KAAK;QACvBC,MAAM,GAAGF,UAAU,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,UAAU;MAEpDjL,eAAe,CAAC,IAAI,EAAE4K,KAAK,CAAC;MAE5BhL,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;MAEzCA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,UAAUsL,KAAK,EAAE;QACjD,IAAIE,cAAc,GAAGP,KAAK,CAACQ,QAAQ,CAAC,CAAC;UACjCf,IAAI,GAAGc,cAAc,CAACd,IAAI;UAC1BC,MAAM,GAAGa,cAAc,CAACb,MAAM;QAElC,IAAIe,KAAK,GAAG;UACVhB,IAAI,EAAEY,KAAK,CAAC1L,MAAM;UAClB+K,MAAM,EAAEA;QACV,CAAC;QACDjB,IAAI,CAACiC,GAAG,CAAC,OAAO,EAAEL,KAAK,CAAC;QAExB,IAAIX,MAAM,KAAKtF,MAAM,CAACE,OAAO,IAAI,CAACmF,IAAI,IAAIY,KAAK,CAAC1L,MAAM,EAAE;UACtD8L,KAAK,CAACf,MAAM,GAAGtF,MAAM,CAACG,OAAO;QAC/B;QAEAyF,KAAK,CAACW,QAAQ,CAACF,KAAK,CAAC;MACvB,CAAC,CAAC;MAEF1L,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU6L,QAAQ,EAAE;QACvDZ,KAAK,CAACY,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,CAAC;MAEF7L,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU0L,KAAK,EAAE;QAC/C,IAAI,CAACjE,YAAY,CAACiE,KAAK,EAAEd,SAAS,CAAC,EAAE;UACnC,MAAM,IAAIkB,KAAK,CAAC,kCAAkC,CAAC3B,MAAM,CAACS,SAAS,CAACrD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClF;QAEA0D,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAACvM,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACnIpB,MAAM,EAAEoB,KAAK,CAACpB,MAAM,IAAIzG,OAAO,CAACU;QAClC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;MAEFvE,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,UAAUgM,SAAS,EAAE;QAClD,IAAIC,eAAe,GAAGhB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCjB,KAAK,GAAGyB,eAAe,CAACzB,KAAK;UAC7BE,IAAI,GAAGuB,eAAe,CAACvB,IAAI;QAE/BO,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UAClEzB,MAAM,EAAEzG,OAAO,CAACE,KAAK;UACrByG,KAAK,EAAEzM,EAAE,CAACsL,MAAM,CAAC2C,SAAS,CAAC,GAAGA,SAAS,GAAGxB;QAC5C,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACbG,MAAM,EAAED,IAAI,GAAGrF,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE;QACzC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFvF,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,IAAIkM,OAAO,GAAGvM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;QAEvF,IAAIwM,eAAe,GAAGlB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCjB,KAAK,GAAG2B,eAAe,CAAC3B,KAAK;UAC7BG,MAAM,GAAGwB,eAAe,CAACxB,MAAM;QAEnC,IAAI,CAACtF,MAAM,CAACM,QAAQ,EAAEN,MAAM,CAACK,OAAO,CAAC,CAAC5C,OAAO,CAAC6H,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;QAE9DM,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UAClEzB,MAAM,EAAEzG,OAAO,CAACG,IAAI;UACpBwG,KAAK,EAAEA,KAAK,IAAI0B,OAAO,GAAG,CAAC,GAAG,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACPvB,MAAM,EAAEtF,MAAM,CAACI;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFzF,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY;QACzC,IAAIoM,eAAe,GAAGnB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCjB,KAAK,GAAG4B,eAAe,CAAC5B,KAAK;UAC7BG,MAAM,GAAGyB,eAAe,CAACzB,MAAM;QAEnC,IAAIA,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE/ByF,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UACnDzB,MAAM,EAAEzG,OAAO,CAACQ,KAAK;UACrBmG,KAAK,EAAEA,KAAK,GAAG;QACjB,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC;MAEFxK,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,UAAUgM,SAAS,EAAE;QAC/C,IAAIK,eAAe,GAAGpB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClClB,UAAU,GAAG8B,eAAe,CAAC9B,UAAU;UACvCI,MAAM,GAAG0B,eAAe,CAAC1B,MAAM;QAEnC,IAAIJ,UAAU,IAAII,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE7C,IAAIkD,IAAI,GAAGuC,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAACN,SAAS,CAAC;QAEtCf,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UAClEzB,MAAM,EAAEzG,OAAO,CAACO,EAAE;UAClBoG,KAAK,EAAEwB;QACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACPrB,MAAM,EAAEjC,IAAI,GAAGiC,MAAM,GAAGtF,MAAM,CAACM;QACjC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEF3F,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,OAAOiL,KAAK,CAACQ,QAAQ,CAAC,CAAC;MACzB,CAAC,CAAC;MAEFzL,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,IAAIuM,eAAe,GAAGtB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCjB,KAAK,GAAG+B,eAAe,CAAC/B,KAAK;UAC7BG,MAAM,GAAG4B,eAAe,CAAC5B,MAAM;QAEnC,IAAIA,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE/ByF,KAAK,CAACW,QAAQ,CAACX,KAAK,CAACc,YAAY,CAAC;UAChCzB,MAAM,EAAEzG,OAAO,CAACM,IAAI;UACpBqG,KAAK,EAAEA,KAAK,GAAG;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFxK,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,IAAIwM,eAAe,GAAGvB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCd,MAAM,GAAG6B,eAAe,CAAC7B,MAAM;QAEnC,IAAIA,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE/ByF,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UACnDzB,MAAM,EAAEzG,OAAO,CAACU,MAAM;UACtBkG,SAAS,EAAEvF,SAAS,CAACN;QACvB,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC;MAEF5E,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,IAAIyM,eAAe,GAAGxB,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClCjB,KAAK,GAAGiC,eAAe,CAACjC,KAAK;UAC7BG,MAAM,GAAG8B,eAAe,CAAC9B,MAAM;QAEnC,IAAIA,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE/ByF,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UACnDzB,MAAM,EAAEzG,OAAO,CAACK,IAAI;UACpBsG,KAAK,EAAEA,KAAK,GAAG;QACjB,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC;MAEFxK,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY;QACzC,IAAI0M,OAAO,GAAG/M,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;QAEvF,IAAIgN,eAAe,GAAG1B,KAAK,CAACQ,QAAQ,CAAC,CAAC;UAClClB,UAAU,GAAGoC,eAAe,CAACpC,UAAU;QAE3C,IAAIA,UAAU,EAAE;QAEhBU,KAAK,CAACW,QAAQ,CAACpM,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACc,YAAY,CAAC;UAClEzB,MAAM,EAAEzG,OAAO,CAACI,KAAK;UACrBuG,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACPG,MAAM,EAAE+B,OAAO,GAAGrH,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACF;QAC5C,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MAEFnF,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY;QACxC,IAAI4M,gBAAgB,GAAG3B,KAAK,CAACQ,QAAQ,CAAC,CAAC;UACnCd,MAAM,GAAGiC,gBAAgB,CAACjC,MAAM;QAEpC,IAAIA,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;QAE/ByF,KAAK,CAACW,QAAQ,CAAC;UACbtB,MAAM,EAAEzG,OAAO,CAACS,IAAI;UACpBmG,SAAS,EAAEvF,SAAS,CAACpB,IAAI;UACzB6G,MAAM,EAAEtF,MAAM,CAACK;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACkG,QAAQ,CAAC;QACZtB,MAAM,EAAEzG,OAAO,CAACC,IAAI;QACpByG,UAAU,EAAExM,EAAE,CAACsL,MAAM,CAAC+B,SAAS,CAAC;QAChCD,UAAU,EAAEA,UAAU;QACtBX,KAAK,EAAEzM,EAAE,CAACsL,MAAM,CAAC+B,SAAS,CAAC,GAAGA,SAAS,GAAG,CAAC;QAC3CX,SAAS,EAAEvF,SAAS,CAACpB,IAAI;QACzB6G,MAAM,EAAEY,MAAM,CAAC3L,MAAM,GAAGyF,MAAM,CAACF,KAAK,GAAGE,MAAM,CAACC;MAChD,CAAC,EAAE,IAAI,CAAC;MACR,IAAI,CAACuH,QAAQ,CAACtB,MAAM,CAAC;IACvB;IAEA1K,YAAY,CAACmK,KAAK,EAAE,CAAC;MACnBjL,GAAG,EAAE,UAAU;MACfmB,KAAK,EAAE,SAAS0K,QAAQA,CAACkB,SAAS,EAAE;QAClC,IAAIC,OAAO,GAAGpN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;QACvF,IAAI+L,KAAK,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC;QAE3B,IAAIuB,gBAAgB,GAAGxN,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAEoB,SAAS,CAAC;UACvExC,MAAM,GAAG0C,gBAAgB,CAAC1C,MAAM;UAChCE,KAAK,GAAGwC,gBAAgB,CAACxC,KAAK;UAC9BC,SAAS,GAAGuC,gBAAgB,CAACvC,SAAS;UACtCC,IAAI,GAAGsC,gBAAgB,CAACtC,IAAI;UAC5BC,MAAM,GAAGqC,gBAAgB,CAACrC,MAAM;QAEpCG,KAAK,CAACa,GAAG,CAAC,QAAQ,EAAErB,MAAM,CAAC;QAC3BQ,KAAK,CAACa,GAAG,CAAC,OAAO,EAAEnB,KAAK,CAAC;QACzBM,KAAK,CAACa,GAAG,CAAC,WAAW,EAAElB,SAAS,CAAC;QACjCK,KAAK,CAACa,GAAG,CAAC,MAAM,EAAEjB,IAAI,CAAC;QACvBI,KAAK,CAACa,GAAG,CAAC,QAAQ,EAAEhB,MAAM,CAAC;QAE3B,IAAIoC,OAAO,EAAE;UACXjC,KAAK,CAACa,GAAG,CAAC,YAAY,EAAEmB,SAAS,CAACvC,UAAU,CAAC;UAC7CO,KAAK,CAACa,GAAG,CAAC,YAAY,EAAEmB,SAAS,CAAC3B,UAAU,CAAC;QAC/C;QACA;;QAGA,IAAI,IAAI,CAACU,QAAQ,IAAI,IAAI,CAACoB,eAAe,CAACvB,KAAK,CAAC,EAAE;UAChD;UACA,IAAI,CAACG,QAAQ,CAAC,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC;QAChC;MACF;IACF,CAAC,EAAE;MACD1L,GAAG,EAAE,UAAU;MACfmB,KAAK,EAAE,SAASuK,QAAQA,CAAA,EAAG;QACzB,IAAI,CAACX,KAAK,CAACJ,IAAI,EAAE;UACf,OAAOlL,cAAc,CAAC,CAAC,CAAC,EAAE6K,YAAY,CAAC;QACzC;QAEA,OAAO;UACLC,MAAM,EAAEQ,KAAK,CAACoC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;UACjC3C,UAAU,EAAEO,KAAK,CAACoC,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK;UAC5C1C,KAAK,EAAEhC,QAAQ,CAACsC,KAAK,CAACoC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;UACvCzC,SAAS,EAAEK,KAAK,CAACoC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE;UACvCxC,IAAI,EAAEI,KAAK,CAACoC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;UAC5BvC,MAAM,EAAEG,KAAK,CAACoC,GAAG,CAAC,QAAQ,CAAC,IAAI;QACjC,CAAC;MACH;IACF,CAAC,EAAE;MACDnN,GAAG,EAAE,cAAc;MACnBmB,KAAK,EAAE,SAAS6K,YAAYA,CAACL,KAAK,EAAE;QAClC,IAAIyB,KAAK,GAAGxN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;QAErF,IAAIyN,gBAAgB,GAAG,IAAI,CAAC3B,QAAQ,CAAC,CAAC;UAClCnB,MAAM,GAAG8C,gBAAgB,CAAC9C,MAAM;UAChCC,UAAU,GAAG6C,gBAAgB,CAAC7C,UAAU;UACxCC,KAAK,GAAG4C,gBAAgB,CAAC5C,KAAK;UAC9BE,IAAI,GAAG0C,gBAAgB,CAAC1C,IAAI;UAC5BC,MAAM,GAAGyC,gBAAgB,CAACzC,MAAM;QAEpC,IAAI0C,QAAQ,GAAGtP,EAAE,CAACsL,MAAM,CAACqC,KAAK,CAAClB,KAAK,CAAC,GAAGkB,KAAK,CAAClB,KAAK,GAAGA,KAAK;QAC3D,IAAIwB,SAAS,GAAGzB,UAAU,IAAI,CAAC4C,KAAK,GAAG3C,KAAK,GAAG8C,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAE,CAAC,CAAC,EAAE3C,IAAI,CAAC;QACpF,OAAO;UACLJ,MAAM,EAAEoB,KAAK,CAACpB,MAAM,IAAIA,MAAM;UAC9BC,UAAU,EAAEA,UAAU;UACtBC,KAAK,EAAEwB,SAAS;UAChBvB,SAAS,EAAEiB,KAAK,CAACjB,SAAS,IAAIvF,SAAS,CAACpB,IAAI;UAC5C4G,IAAI,EAAEgB,KAAK,CAAChB,IAAI,IAAIA,IAAI;UACxBC,MAAM,EAAEqB,SAAS,KAAKtB,IAAI,GAAGrF,MAAM,CAACM,QAAQ,GAAG+F,KAAK,CAACf,MAAM,IAAIA;QACjE,CAAC;MACH;IACF,CAAC,EAAE;MACD5K,GAAG,EAAE,iBAAiB;MACtBmB,KAAK,EAAE,SAAS+L,eAAeA,CAACQ,QAAQ,EAAE;QACxC,IAAIC,MAAM,GAAGC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC;QACrC,IAAII,KAAK,GAAGF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnC,QAAQ,CAAC,CAAC,CAAC;QAC3C,OAAOiC,MAAM,KAAKG,KAAK;MACzB;IACF,CAAC,EAAE;MACD9N,GAAG,EAAE,UAAU;MACfmB,KAAK,EAAE,SAASoL,QAAQA,CAAA,EAAG;QACzB,IAAIhB,KAAK,GAAG5B,IAAI,CAACwD,GAAG,CAAC,OAAO,CAAC;QAC7B,OAAO/F,KAAK,CAACC,OAAO,CAACkE,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;MAC1C;IACF,CAAC,EAAE;MACDvL,GAAG,EAAE,YAAY;MACjBmB,KAAK,EAAE,SAAS4M,UAAUA,CAAA,EAAG;QAC3B,OAAO;UACLC,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBC,EAAE,EAAE,IAAI,CAACA,EAAE;UACXC,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBC,IAAI,EAAE,IAAI,CAACA;QACb,CAAC;MACH;IACF,CAAC,CAAC,CAAC;IAEH,OAAOtD,KAAK;EACd,CAAC,CAAC,CAAC;EAEH,OAAO,IAAIA,KAAK,CAACvK,KAAK,CAAC;AACzB;AAEA,SAAS8N,SAASA,CAAA,EAAG;EACnB,OAAOnI,QAAQ,CAACoI,gBAAgB,IAAIpI,QAAQ,CAACqI,aAAa,CAAC,MAAM,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC9B,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,CAAC,CAAC;EACX;EAEA,OAAOA,OAAO,CAACC,qBAAqB,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;;AAEA,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,IAAIC,SAAS,GAAG1I,QAAQ;IACpB2I,IAAI,GAAGD,SAAS,CAACC,IAAI;IACrBC,IAAI,GAAGF,SAAS,CAACG,eAAe;EAEpC,IAAI,CAACF,IAAI,IAAI,CAACC,IAAI,EAAE;IAClB,OAAO,CAAC;EACV;EAEA,OAAO1B,IAAI,CAACE,GAAG,CAACuB,IAAI,CAACG,YAAY,EAAEH,IAAI,CAACI,YAAY,EAAEH,IAAI,CAACI,YAAY,EAAEJ,IAAI,CAACE,YAAY,EAAEF,IAAI,CAACG,YAAY,CAAC;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,UAAUA,CAACV,OAAO,EAAE;EAC3B;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOvI,QAAQ,CAACkJ,aAAa,CAACX,OAAO,CAAC;EACxC;EAEA,OAAOA,OAAO;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASY,wBAAwBA,CAACC,EAAE,EAAE;EACpC,IAAI,CAACA,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAK,CAAC,EAAE;IAC5B,OAAO,CAAC,CAAC;EACX;EAEA,OAAOC,gBAAgB,CAACF,EAAE,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,eAAeA,CAAChB,OAAO,EAAEiB,OAAO,EAAEC,WAAW,EAAE;EACtD,IAAIC,MAAM,GAAG1R,YAAY,CAACuQ,OAAO,CAAC;EAElC,IAAImB,MAAM,CAAC1G,UAAU,CAACmF,SAAS,CAAC,CAAC,CAAC,EAAE;IAClC,IAAIsB,WAAW,EAAE;MACf,OAAOzJ,QAAQ;IACjB;IAEA,OAAOmI,SAAS,CAAC,CAAC;EACpB;EAEA,IAAIwB,YAAY,GAAGD,MAAM,CAACZ,YAAY,GAAGY,MAAM,CAACX,YAAY;EAC5D;;EAEA,IAAI,CAACY,YAAY,IAAI,CAACH,OAAO,EAAE;IAC7BE,MAAM,CAACE,KAAK,CAACC,QAAQ,GAAG,SAAS;IACjC,OAAO1B,SAAS,CAAC,CAAC;EACpB;EAEA,OAAOuB,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,qBAAqBA,CAACvB,OAAO,EAAEiB,OAAO,EAAE;EAC/C,IAAI,CAACjB,OAAO,EAAE,OAAO,KAAK;EAC1B,IAAImB,MAAM,GAAGH,eAAe,CAAChB,OAAO,EAAEiB,OAAO,CAAC;EAC9C,OAAO,CAACE,MAAM,CAAC1G,UAAU,CAACmF,SAAS,CAAC,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS4B,qBAAqBA,CAACxB,OAAO,EAAE;EACtC,OAAOA,OAAO,CAACyB,YAAY,KAAKhK,QAAQ,CAAC2I,IAAI;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASsB,WAAWA,CAACb,EAAE,EAAE;EACvB,IAAIxG,IAAI,GAAGrJ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO;EAEtF,IAAI,CAAC6P,EAAE,IAAI,EAAEA,EAAE,YAAYc,WAAW,CAAC,EAAE;IACvC,OAAO,KAAK;EACd;EAEA,IAAIC,QAAQ,GAAGf,EAAE,CAACe,QAAQ;EAE1B,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,EAAE;IAC9C,OAAO,KAAK;EACd;EAEA,IAAIhB,wBAAwB,CAACC,EAAE,CAAC,CAACgB,QAAQ,KAAKxH,IAAI,EAAE;IAClD,OAAO,IAAI;EACb;EAEA,OAAOqH,WAAW,CAACb,EAAE,CAACiB,UAAU,EAAEzH,IAAI,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS0H,gBAAgBA,CAAC/B,OAAO,EAAE;EACjC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;EAC1B,IAAIgC,aAAa,GAAGhC,OAAO;EAE3B,OAAOgC,aAAa,EAAE;IACpB,IAAIA,aAAa,KAAKvK,QAAQ,CAAC2I,IAAI,EAAE;IACrC;;IAEA,IAAI4B,aAAa,YAAYL,WAAW,EAAE;MACxC,IAAIM,iBAAiB,GAAGlB,gBAAgB,CAACiB,aAAa,CAAC;QACnDE,OAAO,GAAGD,iBAAiB,CAACC,OAAO;QACnCC,UAAU,GAAGF,iBAAiB,CAACE,UAAU;MAE7C,IAAID,OAAO,KAAK,MAAM,IAAIC,UAAU,KAAK,QAAQ,EAAE;QACjD,OAAO,KAAK;MACd;IACF;IAEAH,aAAa,GAAGA,aAAa,CAACF,UAAU;EAC1C;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASM,kBAAkBA,CAACpC,OAAO,EAAEqC,MAAM,EAAEpB,OAAO,EAAE;EACpD,IAAIqB,WAAW,GAAGvC,aAAa,CAACC,OAAO,CAAC;EACxC,IAAImB,MAAM,GAAGH,eAAe,CAAChB,OAAO,EAAEiB,OAAO,CAAC;EAC9C,IAAIsB,eAAe,GAAGhB,qBAAqB,CAACvB,OAAO,EAAEiB,OAAO,CAAC;EAC7D,IAAIuB,SAAS,GAAG,CAAC;EACjB;;EAEA,IAAIrB,MAAM,YAAYQ,WAAW,EAAE;IACjCa,SAAS,GAAGrB,MAAM,CAACsB,SAAS;EAC9B;EAEA,IAAIC,GAAG,GAAGJ,WAAW,CAACI,GAAG,IAAI,CAACH,eAAe,IAAI,CAACb,WAAW,CAAC1B,OAAO,CAAC,GAAGwC,SAAS,GAAG,CAAC,CAAC;EACvF,OAAO7D,IAAI,CAACgE,KAAK,CAACD,GAAG,GAAGL,MAAM,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASO,YAAYA,CAAC5C,OAAO,EAAE;EAC7B,IAAIA,OAAO,YAAY2B,WAAW,EAAE;IAClC,IAAI3B,OAAO,CAACyB,YAAY,YAAYE,WAAW,EAAE;MAC/C,OAAOiB,YAAY,CAAC5C,OAAO,CAACyB,YAAY,CAAC,GAAGzB,OAAO,CAAC6C,SAAS;IAC/D;IAEA,OAAO7C,OAAO,CAAC6C,SAAS;EAC1B;EAEA,OAAO,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAAC9C,OAAO,EAAEqC,MAAM,EAAEpB,OAAO,EAAE;EAC7C,IAAI,CAACjB,OAAO,EAAE;IACZ,OAAO,CAAC;EACV;EAEA,IAAImB,MAAM,GAAG1R,YAAY,CAACuQ,OAAO,CAAC;EAClC,IAAI0C,GAAG,GAAGE,YAAY,CAAC5C,OAAO,CAAC;EAE/B,IAAIuB,qBAAqB,CAACvB,OAAO,EAAEiB,OAAO,CAAC,IAAI,CAACO,qBAAqB,CAACxB,OAAO,CAAC,EAAE;IAC9E0C,GAAG,IAAIE,YAAY,CAACzB,MAAM,CAAC;EAC7B;EAEA,OAAOxC,IAAI,CAACgE,KAAK,CAACD,GAAG,GAAGL,MAAM,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASU,QAAQA,CAACxQ,KAAK,EAAE;EACvB,IAAIyN,OAAO,GAAGhP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG4O,SAAS,CAAC,CAAC;EAC7F,IAAIoD,cAAc,GAAGhS,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGmG,SAAS;EACpE,OAAO,IAAI8L,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC5C,IAAIV,SAAS,GAAGzC,OAAO,CAACyC,SAAS;IACjC,IAAIW,KAAK,GAAG7Q,KAAK,GAAGkQ,SAAS,GAAGlQ,KAAK,GAAGkQ,SAAS,GAAGA,SAAS,GAAGlQ,KAAK;IACrE/C,MAAM,CAACkT,GAAG,CAAC1C,OAAO,EAAEzN,KAAK,EAAE;MACzB8Q,QAAQ,EAAED,KAAK,GAAG,GAAG,GAAG,EAAE,GAAGJ;IAC/B,CAAC,EAAE,UAAU1H,KAAK,EAAE;MAClB,IAAIA,KAAK,IAAIA,KAAK,CAACgI,OAAO,KAAK,2CAA2C,EAAE;QAC1E,OAAOH,MAAM,CAAC7H,KAAK,CAAC;MACtB;MAEA,OAAO4H,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASK,0BAA0BA,CAACC,QAAQ,EAAE;EAC5C,SAASC,SAASA,CAACC,UAAU,EAAE5R,KAAK,EAAE6R,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;IACrF,IAAIC,iBAAiB,GAAGH,aAAa,IAAI,eAAe;IACxD,IAAII,gBAAgB,GAAGF,YAAY,IAAIH,QAAQ;IAC/C;;IAEA,IAAI7R,KAAK,CAAC6R,QAAQ,CAAC,IAAI,IAAI,EAAE;MAC3B,IAAID,UAAU,EAAE;QACd,OAAO,IAAIvG,KAAK,CAAC,WAAW,CAAC3B,MAAM,CAACqI,QAAQ,EAAE,IAAI,CAAC,CAACrI,MAAM,CAACwI,gBAAgB,EAAE,0BAA0B,CAAC,CAACxI,MAAM,CAACuI,iBAAiB,EAAE,IAAI,CAAC,CAAC;MAC3I;MAEA,OAAO,IAAI;IACb;IAEA,KAAK,IAAIE,IAAI,GAAGjT,SAAS,CAACC,MAAM,EAAEiT,IAAI,GAAG,IAAI1L,KAAK,CAACyL,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MAC1GD,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGnT,SAAS,CAACmT,IAAI,CAAC;IAClC;IAEA,OAAOX,QAAQ,CAAC5S,KAAK,CAAC,KAAK,CAAC,EAAE,CAACkB,KAAK,EAAE6R,QAAQ,EAAEI,iBAAiB,EAAEF,QAAQ,EAAEG,gBAAgB,CAAC,CAACxI,MAAM,CAAC0I,IAAI,CAAC,CAAC;EAC9G;EAEA,IAAIE,gBAAgB,GAAGX,SAAS,CAACY,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;EAClDD,gBAAgB,CAACV,UAAU,GAAGD,SAAS,CAACY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;EACxD,OAAOD,gBAAgB;AACzB;AAEAb,0BAA0B,CAAC,UAAUzR,KAAK,EAAE6R,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC3F,IAAIQ,SAAS,GAAGxS,KAAK,CAAC6R,QAAQ,CAAC;EAC/B,IAAIY,SAAS,GAAGD,SAAS;EAEzB,IAAI,EAAE,aAAarV,KAAK,CAACC,cAAc,CAACoV,SAAS,CAAC,IAAI5U,kBAAkB,CAAC4U,SAAS,CAAC,EAAE;IACnF,IAAIE,QAAQ,GAAG;MACbC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG,CAAC,CAAC;MACtB1K,IAAI,EAAE,CAAC;IACT,CAAC;IACDwK,SAAS,GAAG,aAAatV,KAAK,CAAC6Q,aAAa,CAACyE,SAAS,EAAEC,QAAQ,CAAC;EACnE;EAEA,IAAIpV,EAAE,CAACsV,MAAM,CAACJ,SAAS,CAAC,IAAIlV,EAAE,CAACsL,MAAM,CAAC4J,SAAS,CAAC,IAAI,CAAC5U,kBAAkB,CAAC4U,SAAS,CAAC,IAAI,EAAE,CAAC3U,OAAO,EAAEC,UAAU,CAAC,CAACuE,OAAO,CAACtE,MAAM,CAAC0U,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;IAChJ,OAAO,IAAIpH,KAAK,CAAC,UAAU,CAAC3B,MAAM,CAACqI,QAAQ,EAAE,IAAI,CAAC,CAACrI,MAAM,CAACsI,YAAY,EAAE,iBAAiB,CAAC,CAACtI,MAAM,CAACoI,aAAa,EAAE,0CAA0C,CAAC,CAAC;EAC/J;EAEA,OAAOzM,SAAS;AAClB,CAAC,CAAC;AAEF,IAAIwN,cAAc,GAAG;EACnBC,UAAU,EAAE,MAAM;EAClBC,eAAe,EAAE,MAAM;EACvBC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,oBAAoB;EAClCC,YAAY,EAAE,MAAM;EACpBC,eAAe,EAAE,6BAA6B;EAC9CC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,UAAU,GAAG;EACfP,eAAe,EAAE,aAAa;EAC9BQ,MAAM,EAAE,CAAC;EACTC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,CAAC;EACVC,gBAAgB,EAAE;AACpB,CAAC;AACD,IAAIC,SAAS,GAAG;EACdP,YAAY,EAAE,CAAC;EACfzD,QAAQ,EAAE;AACZ,CAAC;AACD,SAASiE,SAASA,CAAA,EAAG;EACnB,IAAIC,UAAU,GAAG/U,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvF,IAAIgV,OAAO,GAAGlW,SAAS,CAAC6U,cAAc,EAAEoB,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC,CAAC;EACjE,IAAIC,KAAK,GAAG,GAAG;EAEf,IAAIzO,MAAM,CAAC0O,UAAU,GAAG,GAAG,EAAE;IAC3BD,KAAK,GAAG,GAAG;EACb;EAEA,IAAID,OAAO,CAACC,KAAK,EAAE;IACjB,IAAIzO,MAAM,CAAC0O,UAAU,GAAGF,OAAO,CAACC,KAAK,EAAE;MACrCA,KAAK,GAAGzO,MAAM,CAAC0O,UAAU,GAAG,EAAE;IAChC,CAAC,MAAM;MACLD,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,CAAC;IACzB;EACF;EAEA,IAAIE,OAAO,GAAG;IACZC,MAAM,EAAE,CAAC;IACTjM,IAAI,EAAE,CAAC;IACPmH,QAAQ,EAAE,QAAQ;IAClBO,QAAQ,EAAE,UAAU;IACpBzH,KAAK,EAAE,CAAC;IACRsI,GAAG,EAAE,CAAC;IACNyC,MAAM,EAAEa,OAAO,CAACb;EAClB,CAAC;EACD,IAAIkB,aAAa,GAAG;IAClBC,MAAM,EAAEzV,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDlD,OAAO,EAAE,cAAc;MACvBqE,MAAM,EAAEP,OAAO,CAAClB,UAAU;MAC1BjD,QAAQ,EAAE,UAAU;MACpBoE,KAAK,EAAED,OAAO,CAAClB,UAAU;MACzBK,MAAM,EAAEa,OAAO,CAACb;IAClB,CAAC,CAAC;IACFqB,WAAW,EAAE;MACXC,SAAS,EAAE,gDAAgD;MAC3D5B,eAAe,EAAEmB,OAAO,CAAChB,YAAY;MACrCM,YAAY,EAAE,KAAK;MACnBpD,OAAO,EAAE,OAAO;MAChBqE,MAAM,EAAE,KAAK;MACbpM,IAAI,EAAE,KAAK;MACXuM,OAAO,EAAE,GAAG;MACZ7E,QAAQ,EAAE,UAAU;MACpBa,GAAG,EAAE,KAAK;MACViE,SAAS,EAAE,uBAAuB;MAClCV,KAAK,EAAE;IACT,CAAC;IACDW,WAAW,EAAE;MACXH,SAAS,EAAE,gDAAgD;MAC3D5B,eAAe,EAAE,OAAO,CAACrJ,MAAM,CAACrC,QAAQ,CAAC6M,OAAO,CAAChB,YAAY,CAAC,CAACpM,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;MACnFyM,MAAM,EAAE,YAAY,CAAC7J,MAAM,CAACwK,OAAO,CAAChB,YAAY,CAAC;MACjDM,YAAY,EAAE,KAAK;MACnBuB,SAAS,EAAE,YAAY;MACvB3E,OAAO,EAAE,OAAO;MAChBqE,MAAM,EAAE,MAAM;MACdpM,IAAI,EAAE,CAAC;MACPuM,OAAO,EAAE,GAAG;MACZ7E,QAAQ,EAAE,UAAU;MACpBa,GAAG,EAAE,CAAC;MACNoE,eAAe,EAAE,QAAQ;MACzBb,KAAK,EAAE;IACT,CAAC;IACDc,OAAO,EAAE;MACPlC,eAAe,EAAEmB,OAAO,CAACnB,eAAe;MACxCS,YAAY,EAAE,CAAC;MACfuB,SAAS,EAAE,YAAY;MACvBtB,KAAK,EAAES,OAAO,CAACd,SAAS;MACxBO,QAAQ,EAAE,EAAE;MACZuB,QAAQ,EAAE,MAAM;MAChBrB,OAAO,EAAE,EAAE;MACX9D,QAAQ,EAAE,UAAU;MACpBoE,KAAK,EAAEA;IACT,CAAC;IACDgB,gBAAgB,EAAE;MAChBvB,UAAU,EAAE,GAAG;MACfwB,SAAS,EAAE;IACb,CAAC;IACDC,YAAY,EAAE;MACZ1B,QAAQ,EAAE,EAAE;MACZ2B,MAAM,EAAE;IACV,CAAC;IACDC,cAAc,EAAE;MACd1B,OAAO,EAAE;IACX,CAAC;IACD2B,aAAa,EAAE;MACbC,UAAU,EAAE,QAAQ;MACpBrF,OAAO,EAAE,MAAM;MACfsF,cAAc,EAAE,UAAU;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,mBAAmB,EAAE;MACnBC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE/W,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7DP,eAAe,EAAEmB,OAAO,CAAChB,YAAY;MACrCM,YAAY,EAAE,CAAC;MACfC,KAAK,EAAE;IACT,CAAC,CAAC;IACFsC,UAAU,EAAEhX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7DG,KAAK,EAAES,OAAO,CAAChB,YAAY;MAC3B8C,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFC,WAAW,EAAEnX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DG,KAAK,EAAES,OAAO,CAACd,SAAS;MACxBqB,MAAM,EAAE,EAAE;MACVZ,OAAO,EAAE,EAAE;MACX9D,QAAQ,EAAE,UAAU;MACpBzH,KAAK,EAAE,CAAC;MACRsI,GAAG,EAAE,CAAC;MACNuD,KAAK,EAAE;IACT,CAAC,CAAC;IACFgC,UAAU,EAAEpX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7DG,KAAK,EAAES,OAAO,CAACd,SAAS;MACxBO,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFU,OAAO,EAAEtV,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsV,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACvDtB,eAAe,EAAEmB,OAAO,CAACjB,YAAY;MACrCmD,YAAY,EAAE;IAChB,CAAC,CAAC;IACFC,aAAa,EAAEtX,cAAc,CAAC,CAAC,CAAC,EAAEsV,OAAO,CAAC;IAC1CiC,mBAAmB,EAAEvX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsV,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACnEtB,eAAe,EAAEmB,OAAO,CAACjB;IAC3B,CAAC,CAAC;IACFc,SAAS,EAAEhV,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgV,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MAC3DhB,eAAe,EAAE;IACnB,CAAC,CAAC;IACFwD,eAAe,EAAExX,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgV,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjEyC,SAAS,EAAE,eAAe,CAAC9M,MAAM,CAACwK,OAAO,CAACjB,YAAY,EAAE,IAAI,CAAC,CAACvJ,MAAM,CAACwK,OAAO,CAACf,eAAe;IAC9F,CAAC,CAAC;IACFsD,aAAa,EAAE;MACbC,KAAK,EAAE;QACLjD,KAAK,EAAES,OAAO,CAACpB;MACjB,CAAC;MACDoB,OAAO,EAAE;QACPb,MAAM,EAAEa,OAAO,CAACb;MAClB;IACF,CAAC;IACDa,OAAO,EAAEA;EACX,CAAC;EACD,OAAOlW,SAAS,CAACuW,aAAa,EAAEN,UAAU,CAAC;AAC7C;AAEA,IAAI0C,QAAQ,GAAG;EACbC,YAAY,EAAE;IACZ1C,OAAO,EAAE;MACP2C,eAAe,EAAE;QACfC,iBAAiB,EAAE;MACrB;IACF,CAAC;IACDC,cAAc,EAAE;MACdxG,MAAM,EAAE,CAAC,EAAE;MACXR,QAAQ,EAAE;IACZ;EACF,CAAC;EACDiH,MAAM,EAAE;IACNC,IAAI,EAAE,MAAM;IACZ3J,KAAK,EAAE,OAAO;IACd4J,IAAI,EAAE,MAAM;IACZzJ,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,iBAAiB;IACvBG,IAAI,EAAE;EACR,CAAC;EACD5F,IAAI,EAAE;IACJkP,KAAK,EAAE,OAAO;IACdhP,SAAS,EAAE,QAAQ;IACnBoI,MAAM,EAAE;EACV;AACF,CAAC;AAED,SAAS6G,YAAYA,CAACpX,KAAK,EAAE;EAC3B,IAAIqX,eAAe,GAAG,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,kBAAkB,CAAC;EAC3T,OAAO/Y,MAAM,CAACD,IAAI,CAAC2B,KAAK,CAAC,CAACvB,MAAM,CAAC,UAAU2I,CAAC,EAAE;IAC5C,OAAOiQ,eAAe,CAAChV,OAAO,CAAC+E,CAAC,CAAC,KAAK,CAAC,CAAC;EAC1C,CAAC,CAAC,CAACkQ,MAAM,CAAC,UAAUC,GAAG,EAAEtY,CAAC,EAAE;IAC1BsY,GAAG,CAACtY,CAAC,CAAC,GAAGe,KAAK,CAACf,CAAC,CAAC,CAAC,CAAC;;IAEnB,OAAOsY,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,SAASC,aAAaA,CAACvP,IAAI,EAAEjI,KAAK,EAAE;EAClC,IAAI,CAACiI,IAAI,EAAE,OAAO,IAAI;EACtB,IAAIwP,UAAU,GAAGzZ,SAAS,CAAC0Z,GAAG,CAAC,CAACN,YAAY,CAACpX,KAAK,CAAC,EAAE2W,QAAQ,CAAC1O,IAAI,EAAEA,IAAI,CAAC,EAAE;IACzE0P,iBAAiB,EAAEra,EAAE,CAAC2J;EACxB,CAAC,CAAC;EACF,IAAI2Q,YAAY,GAAG5D,SAAS,CAAChW,SAAS,CAACgC,KAAK,CAAC6X,MAAM,IAAI,CAAC,CAAC,EAAE5P,IAAI,CAAC4P,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9E,IAAIla,YAAY,GAAG8R,qBAAqB,CAACb,UAAU,CAAC3G,IAAI,CAACjJ,MAAM,CAAC,EAAEyY,UAAU,CAACK,sBAAsB,CAAC;EACpG,IAAIlB,YAAY,GAAG5Y,SAAS,CAAC0Z,GAAG,CAAC,CAAC1X,KAAK,CAAC4W,YAAY,IAAI,CAAC,CAAC,EAAED,QAAQ,CAACC,YAAY,EAAEa,UAAU,CAACb,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpHA,YAAY,CAACrG,MAAM,GAAGkH,UAAU,CAAClH,MAAM;EACvCqG,YAAY,CAACiB,MAAM,GAAG7Z,SAAS,CAAC4Y,YAAY,CAACiB,MAAM,IAAI,CAAC,CAAC,EAAED,YAAY,CAACnB,aAAa,IAAI,CAAC,CAAC,CAAC;EAC5F,OAAOmB,YAAY,CAACnB,aAAa;EACjCG,YAAY,CAACrG,MAAM,IAAIvQ,KAAK,CAAC+X,gBAAgB,IAAI9P,IAAI,CAAC8P,gBAAgB,IAAI,CAAC;EAE3E,IAAI9P,IAAI,CAAC+P,eAAe,EAAE;IACxBpB,YAAY,CAACG,cAAc,CAAC5O,SAAS,GAAGF,IAAI,CAAC+P,eAAe;EAC9D;EAEA,IAAIra,YAAY,EAAE;IAChBiZ,YAAY,CAAC1C,OAAO,CAAC2C,eAAe,CAACC,iBAAiB,GAAG,QAAQ;EACnE;EAEA,OAAO/X,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0Y,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACxDT,MAAM,EAAEhZ,SAAS,CAAC0Z,GAAG,CAAC,CAACf,QAAQ,CAACK,MAAM,EAAEhX,KAAK,CAACgX,MAAM,IAAI,CAAC,CAAC,EAAES,UAAU,CAACT,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;IACrFJ,YAAY,EAAEA,YAAY;IAC1BiB,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,YAAYA,CAAChQ,IAAI,EAAE;EAC1B,IAAIoB,KAAK,GAAGnK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAErF,IAAI,CAAC5B,EAAE,CAAC2J,WAAW,CAACgB,IAAI,CAAC,EAAE;IACzBa,GAAG,CAAC;MACFE,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,IAAI;MACVE,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,OAAO,KAAK;EACd;EAEA,IAAI,CAACpB,IAAI,CAACjJ,MAAM,EAAE;IAChB8J,GAAG,CAAC;MACFE,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,iCAAiC;MACvCE,IAAI,EAAE,IAAI;MACVE,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6O,aAAaA,CAACrN,KAAK,EAAE;EAC5B,IAAIxB,KAAK,GAAGnK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAErF,IAAI,CAAC5B,EAAE,CAAC4J,KAAK,CAAC2D,KAAK,CAAC,EAAE;IACpB/B,GAAG,CAAC;MACFE,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,IAAI;MACVE,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,OAAO,KAAK;EACd;EAEA,OAAOwB,KAAK,CAAC1D,KAAK,CAAC,UAAUC,CAAC,EAAE;IAC9B,OAAO6Q,YAAY,CAAC7Q,CAAC,EAAEiC,KAAK,CAAC;EAC/B,CAAC,CAAC;AACJ;AAEA,IAAI8O,KAAK,GAAG,aAAa/X,YAAY,CAAC,SAAS+X,KAAKA,CAACC,QAAQ,EAAE;EAC7D,IAAI5N,KAAK,GAAG,IAAI;EAEhB,IAAI0J,OAAO,GAAGhV,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpFS,eAAe,CAAC,IAAI,EAAEwY,KAAK,CAAC;EAE5B5Y,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAExCA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAExCA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU2O,OAAO,EAAE;IACtD,IAAImK,QAAQ,GAAGnK,OAAO,CAACmK,QAAQ;IAC/B,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGhT,SAAS;IAC3D,IAAIiT,aAAa,GAAGC,KAAK,CAACF,QAAQ,CAAC;IACnC,OAAO,CAACC,aAAa,IAAI9N,KAAK,CAACgO,YAAY,CAACtK,OAAO,CAAC;EACtD,CAAC,CAAC;EAEF3O,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,UAAU2O,OAAO,EAAE;IACvD,IAAIuK,aAAa,GAAG,qCAAqC;IACzD,IAAI3I,QAAQ,GAAG5B,OAAO,CAAC4B,QAAQ,CAAC1J,WAAW,CAAC,CAAC;IAC7C,IAAIsS,GAAG,GAAGD,aAAa,CAAC5S,IAAI,CAACiK,QAAQ,CAAC,IAAI,CAAC5B,OAAO,CAACyK,YAAY,CAAC,UAAU,CAAC,IAAI7I,QAAQ,KAAK,GAAG,IAAI,CAAC,CAAC5B,OAAO,CAACyK,YAAY,CAAC,MAAM,CAAC;IACjI,OAAOD,GAAG,IAAIlO,KAAK,CAACoO,SAAS,CAAC1K,OAAO,CAAC;EACxC,CAAC,CAAC;EAEF3O,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,YAAY;IACxD,OAAO,EAAE,CAAC4G,KAAK,CAACtF,IAAI,CAAC2J,KAAK,CAAC0D,OAAO,CAAC2K,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAACpa,MAAM,CAAC+L,KAAK,CAACsO,WAAW,CAAC;EACxF,CAAC,CAAC;EAEFvZ,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU0C,CAAC,EAAE;IAClD,IAAI8W,qBAAqB,GAAGvO,KAAK,CAAC0J,OAAO,CAAC8E,OAAO;MAC7CA,OAAO,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAC1E;;IAEA,IAAI9W,CAAC,CAAC+W,OAAO,KAAKA,OAAO,EAAE;MACzBxO,KAAK,CAACyO,YAAY,CAAChX,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EAEF1C,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,UAAU4X,KAAK,EAAE;IACrD,IAAI+B,QAAQ,GAAG1O,KAAK,CAAC2O,oBAAoB,CAAC,CAAC;IAE3C,IAAI,CAACD,QAAQ,CAAC/Z,MAAM,EAAE;MACpB;IACF;IAEAgY,KAAK,CAACiC,cAAc,CAAC,CAAC;IACtB,IAAIC,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ;IAC7B,IAAIC,CAAC,GAAGJ,QAAQ,CAAC7W,OAAO,CAACsD,QAAQ,CAAC4T,aAAa,CAAC;IAEhD,IAAID,CAAC,KAAK,CAAC,CAAC,IAAI,CAACD,QAAQ,IAAIC,CAAC,GAAG,CAAC,KAAKJ,QAAQ,CAAC/Z,MAAM,EAAE;MACtDma,CAAC,GAAG,CAAC;IACP,CAAC,MAAM,IAAID,QAAQ,IAAIC,CAAC,KAAK,CAAC,EAAE;MAC9BA,CAAC,GAAGJ,QAAQ,CAAC/Z,MAAM,GAAG,CAAC;IACzB,CAAC,MAAM;MACLma,CAAC,IAAID,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IACxB;IAEAH,QAAQ,CAACI,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;EACrB,CAAC,CAAC;EAEFja,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU2O,OAAO,EAAE;IACnD,IAAIuL,MAAM,GAAGvL,OAAO,CAACwL,WAAW,IAAI,CAAC,IAAIxL,OAAO,CAACQ,YAAY,IAAI,CAAC;IAClE,IAAIa,KAAK,GAAG7J,MAAM,CAACuJ,gBAAgB,CAACf,OAAO,CAAC;IAC5C,IAAIuL,MAAM,IAAI,CAACvL,OAAO,CAACyL,SAAS,EAAE,OAAO,IAAI;IAC7C,OAAOF,MAAM,IAAIlK,KAAK,CAACqK,gBAAgB,CAAC,UAAU,CAAC,KAAK,SAAS,IAAIrK,KAAK,CAACqK,gBAAgB,CAAC,SAAS,CAAC,KAAK,MAAM;EACnH,CAAC,CAAC;EAEFra,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU2O,OAAO,EAAE;IACpD,IAAIgC,aAAa,GAAGhC,OAAO;IAE3B,OAAOgC,aAAa,EAAE;MACpB;MACA,IAAIA,aAAa,YAAYL,WAAW,EAAE;QACxC,IAAIK,aAAa,KAAKvK,QAAQ,CAAC2I,IAAI,EAAE;QACrC;;QAEA,IAAI9D,KAAK,CAACqP,QAAQ,CAAC3J,aAAa,CAAC,EAAE,OAAO,KAAK;QAC/CA,aAAa,GAAGA,aAAa,CAACF,UAAU;MAC1C;IACF;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAEFzQ,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,YAAY;IAC/CmG,MAAM,CAACoU,mBAAmB,CAAC,SAAS,EAAEtP,KAAK,CAACuP,aAAa,CAAC;EAC5D,CAAC,CAAC;EAEFxa,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,UAAUP,MAAM,EAAE;IACpD,IAAI2G,QAAQ,CAAC4T,aAAa,KAAKva,MAAM,EAAE;MACrCA,MAAM,CAACwa,KAAK,CAAC,CAAC;MACd9T,MAAM,CAACsU,qBAAqB,CAAC,YAAY;QACvC,OAAOxP,KAAK,CAACyP,UAAU,CAACjb,MAAM,CAAC;MACjC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFO,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY;IAC5C,IAAI2a,QAAQ,GAAG1P,KAAK,CAAC0J,OAAO,CAACgG,QAAQ;IACrC,IAAI,CAACA,QAAQ,EAAE;IAEf,IAAIlb,MAAM,GAAGwL,KAAK,CAAC0D,OAAO,CAACW,aAAa,CAACqL,QAAQ,CAAC;IAClD;;IAGA,IAAIlb,MAAM,EAAE;MACV0G,MAAM,CAACsU,qBAAqB,CAAC,YAAY;QACvC,OAAOxP,KAAK,CAACyP,UAAU,CAACjb,MAAM,CAAC;MACjC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IAAI,EAAEoZ,QAAQ,YAAYvI,WAAW,CAAC,EAAE;IACtC,MAAM,IAAI/P,SAAS,CAAC,mDAAmD,CAAC;EAC1E;EAEA,IAAI,CAACoO,OAAO,GAAGkK,QAAQ;EACvB,IAAI,CAAClE,OAAO,GAAGA,OAAO;EACtBxO,MAAM,CAACyU,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACJ,aAAa,EAAE,KAAK,CAAC;EAC7D,IAAI,CAACK,QAAQ,CAAC,CAAC;AACjB,CAAC,CAAC;AAEF,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DxZ,SAAS,CAACuZ,aAAa,EAAEC,gBAAgB,CAAC;EAE1C,IAAIC,MAAM,GAAG1X,YAAY,CAACwX,aAAa,CAAC;EAExC,SAASA,aAAaA,CAACra,KAAK,EAAE;IAC5B,IAAIwK,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAE0a,aAAa,CAAC;IAEpC7P,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC,IAAI,EAAEb,KAAK,CAAC;IAEhCT,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,cAAc,EAAE,UAAU5D,CAAC,EAAE;MAC1E4D,KAAK,CAACgK,MAAM,GAAG5N,CAAC;IAClB,CAAC,CAAC;IAEF,IAAI,CAAC5G,KAAK,CAACwa,eAAe,EAAE;MAC1B,IAAIC,IAAI,GAAG9U,QAAQ,CAAC8U,IAAI,IAAI9U,QAAQ,CAAC+U,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;MACpE,IAAInL,KAAK,GAAG5J,QAAQ,CAACqI,aAAa,CAAC,OAAO,CAAC;MAC3C,IAAI2M,GAAG,GAAG,ygBAAygB;MACnhBpL,KAAK,CAAChH,IAAI,GAAG,UAAU;MACvBgH,KAAK,CAACqL,EAAE,GAAG,0BAA0B;MAErC,IAAI5a,KAAK,CAAC6a,KAAK,KAAKxV,SAAS,EAAE;QAC7BkK,KAAK,CAACuL,YAAY,CAAC,OAAO,EAAE9a,KAAK,CAAC6a,KAAK,CAAC;MAC1C;MAEAtL,KAAK,CAACwL,WAAW,CAACpV,QAAQ,CAACqV,cAAc,CAACL,GAAG,CAAC,CAAC;MAC/CF,IAAI,CAACM,WAAW,CAACxL,KAAK,CAAC;IACzB;IAEA,OAAO/E,KAAK;EACd;EAEApK,YAAY,CAACia,aAAa,EAAE,CAAC;IAC3B/a,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASwa,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,WAAW,GAAG,IAAI,CAACnb,KAAK,CAACmb,WAAW;MAExCC,UAAU,CAAC,YAAY;QACrB,IAAI9d,EAAE,CAACoL,UAAU,CAACwS,MAAM,CAAC1G,MAAM,CAAC,IAAI2G,WAAW,EAAE;UAC/CD,MAAM,CAAC1G,MAAM,CAACgF,KAAK,CAAC,CAAC;QACvB;MACF,CAAC,EAAE,CAAC,CAAC;IACP;EACF,CAAC,EAAE;IACDla,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAAS4a,oBAAoBA,CAAA,EAAG;MACrC,IAAI9L,KAAK,GAAG5J,QAAQ,CAAC2V,cAAc,CAAC,0BAA0B,CAAC;MAE/D,IAAI/L,KAAK,EAAE;QACTA,KAAK,CAACS,UAAU,CAACuL,WAAW,CAAChM,KAAK,CAAC;MACrC;IACF;EACF,CAAC,EAAE;IACDjQ,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACzb,KAAK;QACxBwa,eAAe,GAAGiB,WAAW,CAACjB,eAAe;QAC7CxD,MAAM,GAAGyE,WAAW,CAACzE,MAAM;QAC3B0E,cAAc,GAAGD,WAAW,CAACC,cAAc;QAC3C7D,MAAM,GAAG4D,WAAW,CAAC5D,MAAM;MAC/B,IAAI7X,KAAK,GAAG;QACV,YAAY,EAAEgX,MAAM,CAACtJ,IAAI;QACzBiO,OAAO,EAAED,cAAc;QACvBE,YAAY,EAAEF,cAAc;QAC5B/I,GAAG,EAAE,IAAI,CAACkJ,YAAY;QACtB7S,KAAK,EAAEgO,MAAM,CAACtJ;MAChB,CAAC;MACD,IAAIoO,SAAS;MAEb,IAAItB,eAAe,EAAE;QACnB,IAAIuB,eAAe,GAAGvB,eAAe;QACrCsB,SAAS,GAAG,aAAa3e,KAAK,CAAC6Q,aAAa,CAAC+N,eAAe,EAAE/b,KAAK,CAAC;MACtE,CAAC,MAAM;QACL8b,SAAS,GAAG,aAAa3e,KAAK,CAAC6Q,aAAa,CAAC,QAAQ,EAAEtN,QAAQ,CAAC;UAC9DpB,GAAG,EAAE,eAAe;UACpB0c,SAAS,EAAE,uBAAuB;UAClCzM,KAAK,EAAEsI,MAAM,CAACrD,MAAM;UACpBjM,IAAI,EAAE;QACR,CAAC,EAAEvI,KAAK,CAAC,EAAE,aAAa7C,KAAK,CAAC6Q,aAAa,CAAC,MAAM,EAAE;UAClDuB,KAAK,EAAEsI,MAAM,CAACnD;QAChB,CAAC,CAAC,EAAE,aAAavX,KAAK,CAAC6Q,aAAa,CAAC,MAAM,EAAE;UAC3CuB,KAAK,EAAEsI,MAAM,CAAC/C;QAChB,CAAC,CAAC,CAAC;MACL;MAEA,OAAOgH,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOzB,aAAa;AACtB,CAAC,CAACld,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAIwJ,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClT,IAAI,EAAE;EACrD,IAAI8O,MAAM,GAAG9O,IAAI,CAAC8O,MAAM;EACxB,OAAO,aAAa1a,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;IAC7C1O,GAAG,EAAE,kBAAkB;IACvB0c,SAAS,EAAE,0BAA0B;IACrCzM,KAAK,EAAEsI;EACT,CAAC,CAAC;AACJ,CAAC;AAED,IAAIqE,WAAW,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC;AAE5C,IAAIC,cAAc,GAAG,aAAa,UAAU7B,gBAAgB,EAAE;EAC5DxZ,SAAS,CAACqb,cAAc,EAAE7B,gBAAgB,CAAC;EAE3C,IAAIC,MAAM,GAAG1X,YAAY,CAACsZ,cAAc,CAAC;EAEzC,SAASA,cAAcA,CAAA,EAAG;IACxB,IAAI3R,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAEwc,cAAc,CAAC;IAErC,KAAK,IAAIhK,IAAI,GAAGjT,SAAS,CAACC,MAAM,EAAEiT,IAAI,GAAG,IAAI1L,KAAK,CAACyL,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGnT,SAAS,CAACmT,IAAI,CAAC;IAC9B;IAEA7H,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC/B,KAAK,CAACyb,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC7Q,MAAM,CAAC0I,IAAI,CAAC,CAAC;IAEtD7S,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,YAAY,EAAE,KAAK,CAAC;IAEnEjL,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,OAAO,EAAE;MACtD4R,kBAAkB,EAAE,KAAK;MACzBC,WAAW,EAAE,KAAK;MAClBC,aAAa,EAAE;IACjB,CAAC,CAAC;IAEF/c,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUvI,CAAC,EAAE;MAC7E,IAAIma,kBAAkB,GAAG5R,KAAK,CAACS,KAAK,CAACmR,kBAAkB;MACvD,IAAIG,qBAAqB,GAAG/R,KAAK,CAACgS,eAAe;QAC7C/H,MAAM,GAAG8H,qBAAqB,CAAC9H,MAAM;QACrCpM,IAAI,GAAGkU,qBAAqB,CAAClU,IAAI;QACjC0H,QAAQ,GAAGwM,qBAAqB,CAACxM,QAAQ;QACzCa,GAAG,GAAG2L,qBAAqB,CAAC3L,GAAG;QAC/BuD,KAAK,GAAGoI,qBAAqB,CAACpI,KAAK;MACvC,IAAIsI,OAAO,GAAG1M,QAAQ,KAAK,OAAO,GAAG9N,CAAC,CAACya,OAAO,GAAGza,CAAC,CAAC0a,KAAK;MACxD,IAAIC,OAAO,GAAG7M,QAAQ,KAAK,OAAO,GAAG9N,CAAC,CAAC4a,OAAO,GAAG5a,CAAC,CAAC6a,KAAK;MACxD,IAAIC,iBAAiB,GAAGN,OAAO,IAAI7L,GAAG,IAAI6L,OAAO,IAAI7L,GAAG,GAAG6D,MAAM;MACjE,IAAIuI,gBAAgB,GAAGJ,OAAO,IAAIvU,IAAI,IAAIuU,OAAO,IAAIvU,IAAI,GAAG8L,KAAK;MACjE,IAAI8I,WAAW,GAAGD,gBAAgB,IAAID,iBAAiB;MAEvD,IAAIE,WAAW,KAAKb,kBAAkB,EAAE;QACtC5R,KAAK,CAAC0S,WAAW,CAAC;UAChBd,kBAAkB,EAAEa;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF1d,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;MACzE,IAAIxL,MAAM,GAAGwL,KAAK,CAACxK,KAAK,CAAChB,MAAM;MAC/B,IAAIkP,OAAO,GAAGU,UAAU,CAAC5P,MAAM,CAAC;MAEhC,IAAIwL,KAAK,CAAC7M,YAAY,KAAKgI,QAAQ,EAAE;QACnC,IAAI0W,WAAW,GAAG7R,KAAK,CAACS,KAAK,CAACoR,WAAW;QAEzC,IAAI,CAACA,WAAW,EAAE;UAChB7R,KAAK,CAAC0S,WAAW,CAAC;YAChBb,WAAW,EAAE,IAAI;YACjBC,aAAa,EAAE;UACjB,CAAC,CAAC;QACJ;QAEAa,YAAY,CAAC3S,KAAK,CAAC4S,aAAa,CAAC;QACjC5S,KAAK,CAAC4S,aAAa,GAAGhC,UAAU,CAAC,YAAY;UAC3C5Q,KAAK,CAAC0S,WAAW,CAAC;YAChBb,WAAW,EAAE,KAAK;YAClBC,aAAa,EAAE;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,MAAM,IAAI1M,WAAW,CAAC1B,OAAO,EAAE,QAAQ,CAAC,EAAE;QACzC1D,KAAK,CAAC0S,WAAW,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;IAEF3d,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;MACzE2S,YAAY,CAAC3S,KAAK,CAAC6S,aAAa,CAAC;MACjC7S,KAAK,CAAC6S,aAAa,GAAGjC,UAAU,CAAC,YAAY;QAC3C,IAAI,CAAC5Q,KAAK,CAAC8S,UAAU,EAAE;UACrB;QACF;QAEA9S,KAAK,CAAC+S,WAAW,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO/S,KAAK;EACd;EAEApK,YAAY,CAAC+b,cAAc,EAAE,CAAC;IAC5B7c,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASwa,iBAAiBA,CAAA,EAAG;MAClC,IAAIQ,WAAW,GAAG,IAAI,CAACzb,KAAK;MACxByb,WAAW,CAACpS,KAAK;MACjBoS,WAAW,CAAC+B,gBAAgB;MAC5B,IAAI1F,sBAAsB,GAAG2D,WAAW,CAAC3D,sBAAsB;QAC/D9Y,MAAM,GAAGyc,WAAW,CAACzc,MAAM;MAC/B,IAAIkP,OAAO,GAAGU,UAAU,CAAC5P,MAAM,CAAC;MAChC,IAAI,CAACrB,YAAY,GAAGuR,eAAe,CAAChB,OAAO,EAAE4J,sBAAsB,EAAE,IAAI,CAAC;MAC1E,IAAI,CAACwF,UAAU,GAAG,IAAI;MAEtB5X,MAAM,CAACyU,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACsD,YAAY,CAAC;IACtD;EACF,CAAC,EAAE;IACDne,GAAG,EAAE,oBAAoB;IACzBmB,KAAK,EAAE,SAASid,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIzC,MAAM,GAAG,IAAI;MAEjB,IAAI0C,YAAY,GAAG,IAAI,CAAC5d,KAAK;QACzBgK,SAAS,GAAG4T,YAAY,CAAC5T,SAAS;QAClC6T,eAAe,GAAGD,YAAY,CAACC,eAAe;MAElD,IAAIC,YAAY,GAAGzgB,WAAW,CAACsgB,SAAS,EAAE,IAAI,CAAC3d,KAAK,CAAC;QACjD+d,OAAO,GAAGD,YAAY,CAACC,OAAO;MAClC;;MAGA,IAAIA,OAAO,CAAC,WAAW,EAAEtZ,SAAS,CAACN,OAAO,CAAC,EAAE;QAC3C,IAAI,CAACxG,YAAY,CAACwc,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC6D,YAAY,EAAE;UAC9DC,OAAO,EAAE;QACX,CAAC,CAAC;QACF7C,UAAU,CAAC,YAAY;UACrB,IAAIiB,WAAW,GAAGnB,MAAM,CAACjQ,KAAK,CAACoR,WAAW;UAE1C,IAAI,CAACA,WAAW,EAAE;YAChBnB,MAAM,CAACgC,WAAW,CAAC;cACjBZ,aAAa,EAAE;YACjB,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;MACT;MAEA,IAAIyB,OAAO,CAAC,iBAAiB,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,IAAIA,OAAO,CAAC,WAAW,CAAC,EAAE;QACnF,IAAIF,eAAe,IAAI7T,SAAS,KAAKvF,SAAS,CAACN,OAAO,EAAE;UACtDuB,MAAM,CAACyU,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC+D,eAAe,EAAE,KAAK,CAAC;QACnE,CAAC,MAAM,IAAIlU,SAAS,KAAKvF,SAAS,CAACN,OAAO,EAAE;UAC1CuB,MAAM,CAACoU,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACoE,eAAe,CAAC;QAC/D;MACF;IACF;EACF,CAAC,EAAE;IACD5e,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAAS4a,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACiC,UAAU,GAAG,KAAK;MACvB5X,MAAM,CAACoU,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACoE,eAAe,CAAC;MAC7DxY,MAAM,CAACoU,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC2D,YAAY,CAAC;MACvDN,YAAY,CAAC,IAAI,CAACE,aAAa,CAAC;MAChCF,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;MAChC,IAAI,CAACzf,YAAY,CAACmc,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACkE,YAAY,CAAC;IACpE;EACF,CAAC,EAAE;IACD1e,GAAG,EAAE,iBAAiB;IACtBmN,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,IAAI6P,aAAa,GAAG,IAAI,CAACrR,KAAK,CAACqR,aAAa;MAC5C,IAAI6B,YAAY,GAAG,IAAI,CAACne,KAAK;QACzB8X,sBAAsB,GAAGqG,YAAY,CAACrG,sBAAsB;QAC5D+F,eAAe,GAAGM,YAAY,CAACN,eAAe;QAC9C9F,gBAAgB,GAAGoG,YAAY,CAACpG,gBAAgB;QAChDF,MAAM,GAAGsG,YAAY,CAACtG,MAAM;QAC5B7Y,MAAM,GAAGmf,YAAY,CAACnf,MAAM;MAChC,IAAIkP,OAAO,GAAGU,UAAU,CAAC5P,MAAM,CAAC;MAChC,IAAIwR,WAAW,GAAGvC,aAAa,CAACC,OAAO,CAAC;MACxC,IAAIkQ,aAAa,GAAGxO,WAAW,CAAC1B,OAAO,CAAC;MACxC,IAAI0C,GAAG,GAAGN,kBAAkB,CAACpC,OAAO,EAAE6J,gBAAgB,EAAED,sBAAsB,CAAC;MAC/E,OAAO/Y,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8J,QAAQ,CAAC,CAAC,GAAGgP,MAAM,CAACtB,eAAe,GAAGsB,MAAM,CAAC9D,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACpGU,MAAM,EAAE5H,IAAI,CAACwR,KAAK,CAAC7N,WAAW,CAACiE,MAAM,GAAGsD,gBAAgB,GAAG,CAAC,CAAC;QAC7D1P,IAAI,EAAEwE,IAAI,CAACwR,KAAK,CAAC7N,WAAW,CAACnI,IAAI,GAAG0P,gBAAgB,CAAC;QACrDnD,OAAO,EAAE0H,aAAa,GAAG,CAAC,GAAG,CAAC;QAC9BgC,aAAa,EAAET,eAAe,GAAG,MAAM,GAAG,MAAM;QAChD9N,QAAQ,EAAEqO,aAAa,GAAG,OAAO,GAAG,UAAU;QAC9CxN,GAAG,EAAEA,GAAG;QACR2N,UAAU,EAAE,cAAc;QAC1BpK,KAAK,EAAEtH,IAAI,CAACwR,KAAK,CAAC7N,WAAW,CAAC2D,KAAK,GAAG4D,gBAAgB,GAAG,CAAC;MAC5D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDzY,GAAG,EAAE,aAAa;IAClBmB,KAAK,EAAE,SAASyc,WAAWA,CAACjS,KAAK,EAAE;MACjC,IAAI,CAAC,IAAI,CAACqS,UAAU,EAAE;QACpB;MACF;MAEA,IAAI,CAACnS,QAAQ,CAACF,KAAK,CAAC;IACtB;EACF,CAAC,EAAE;IACD3L,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAIgD,WAAW,GAAG,IAAI,CAACvT,KAAK;QACxBmR,kBAAkB,GAAGoC,WAAW,CAACpC,kBAAkB;QACnDE,aAAa,GAAGkC,WAAW,CAAClC,aAAa;MAC7C,IAAImC,YAAY,GAAG,IAAI,CAACze,KAAK;QACzB0e,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,mBAAmB,GAAGF,YAAY,CAACE,mBAAmB;QACtD3U,SAAS,GAAGyU,YAAY,CAACzU,SAAS;QAClC4U,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CzW,SAAS,GAAGsW,YAAY,CAACtW,SAAS;QAClC0P,MAAM,GAAG4G,YAAY,CAAC5G,MAAM;MAEhC,IAAI6G,cAAc,IAAI1U,SAAS,KAAKvF,SAAS,CAACN,OAAO,EAAE;QACrD,OAAO,IAAI;MACb;MAEA,IAAI0a,UAAU,GAAGhH,MAAM,CAACxD,OAAO;MAC/B;;MAEA,IAAIxL,QAAQ,CAAC,CAAC,EAAE;QACdgW,UAAU,GAAG1W,SAAS,KAAK,QAAQ,GAAG0P,MAAM,CAACvB,mBAAmB,GAAGuB,MAAM,CAACxB,aAAa;MACzF;MAEA,IAAIyI,aAAa,GAAG/f,cAAc,CAAC;QACjC2U,MAAM,EAAEiL,mBAAmB,GAAG,SAAS,GAAG,SAAS;QACnDlK,MAAM,EAAErG,iBAAiB,CAAC,CAAC;QAC3BkQ,aAAa,EAAElC,kBAAkB,GAAG,MAAM,GAAG;MAC/C,CAAC,EAAEyC,UAAU,CAAC;MAEd,IAAI9K,SAAS,GAAG5L,SAAS,KAAK,QAAQ,IAAImU,aAAa,IAAI,aAAanf,KAAK,CAAC6Q,aAAa,CAACiO,gBAAgB,EAAE;QAC5GpE,MAAM,EAAE,IAAI,CAAC2E;MACf,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIlX,UAAU,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC7BwZ,aAAa,CAAC1I,YAAY;QACtB0I,aAAa,CAACzL,MAAM;QACpB,IAAI0L,YAAY,GAAGzc,wBAAwB,CAACwc,aAAa,EAAE5C,WAAW,CAAC;QAE3EnI,SAAS,GAAG,aAAa5W,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;UAClDuB,KAAK,EAAExQ,cAAc,CAAC,CAAC,CAAC,EAAEggB,YAAY;QACxC,CAAC,EAAEhL,SAAS,CAAC;QACb,OAAO+K,aAAa,CAAC/L,eAAe;MACtC;MAEA,OAAO,aAAa5V,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QAC7CgO,SAAS,EAAE,wBAAwB;QACnCzM,KAAK,EAAEuP,aAAa;QACpBnD,OAAO,EAAEiD;MACX,CAAC,EAAE7K,SAAS,CAAC;IACf;EACF,CAAC,CAAC,CAAC;EAEH,OAAOoI,cAAc;AACvB,CAAC,CAAChf,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAIuM,WAAW,GAAG,CAAC,QAAQ,CAAC;EACxBC,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;AAE7C,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACnW,IAAI,EAAE;EACjE,IAAI8O,MAAM,GAAG9O,IAAI,CAAC8O,MAAM;IACpB7X,KAAK,GAAGsC,wBAAwB,CAACyG,IAAI,EAAEiW,WAAW,CAAC;EAEvD,IAAIvL,KAAK,GAAGoE,MAAM,CAACpE,KAAK;IACpBgB,MAAM,GAAGoD,MAAM,CAACpD,MAAM;IACtBN,KAAK,GAAG0D,MAAM,CAAC1D,KAAK;IACpB5E,KAAK,GAAGjN,wBAAwB,CAACuV,MAAM,EAAEoH,UAAU,CAAC;EAExD,OAAO,aAAa9hB,KAAK,CAAC6Q,aAAa,CAAC,QAAQ,EAAEtN,QAAQ,CAAC;IACzD6O,KAAK,EAAEA,KAAK;IACZhH,IAAI,EAAE;EACR,CAAC,EAAEvI,KAAK,CAAC,EAAE,aAAa7C,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;IACjDmG,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAG,EAAE,CAACzK,MAAM,CAACyK,KAAK,EAAE,IAAI,CAAC,GAAGA,KAAK;IACjEM,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAG,EAAE,CAAC/K,MAAM,CAAC+K,MAAM,EAAE,IAAI,CAAC,GAAGA,MAAM;IACrE0K,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,4BAA4B;IACnCC,mBAAmB,EAAE;EACvB,CAAC,EAAE,aAAaniB,KAAK,CAAC6Q,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa7Q,KAAK,CAAC6Q,aAAa,CAAC,MAAM,EAAE;IACtF5G,CAAC,EAAE,86BAA86B;IACj7BmY,IAAI,EAAE9L;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAI+L,uBAAuB,GAAG,aAAa,UAAUlF,gBAAgB,EAAE;EACrExZ,SAAS,CAAC0e,uBAAuB,EAAElF,gBAAgB,CAAC;EAEpD,IAAIC,MAAM,GAAG1X,YAAY,CAAC2c,uBAAuB,CAAC;EAElD,SAASA,uBAAuBA,CAAA,EAAG;IACjC7f,eAAe,CAAC,IAAI,EAAE6f,uBAAuB,CAAC;IAE9C,OAAOjF,MAAM,CAACzb,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;EACtC;EAEAkB,YAAY,CAACof,uBAAuB,EAAE,CAAC;IACrClgB,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACzb,KAAK;QACxByf,SAAS,GAAGhE,WAAW,CAACgE,SAAS;QACjCC,UAAU,GAAGjE,WAAW,CAACiE,UAAU;QACnChV,UAAU,GAAG+Q,WAAW,CAAC/Q,UAAU;QACnCX,KAAK,GAAG0R,WAAW,CAAC1R,KAAK;QACzB4V,UAAU,GAAGlE,WAAW,CAACkE,UAAU;QACnCC,YAAY,GAAGnE,WAAW,CAACmE,YAAY;QACvC3V,IAAI,GAAGwR,WAAW,CAACxR,IAAI;QACvB4V,SAAS,GAAGpE,WAAW,CAACoE,SAAS;QACjC5X,IAAI,GAAGwT,WAAW,CAACxT,IAAI;QACvB6X,YAAY,GAAGrE,WAAW,CAACqE,YAAY;MAC3C,IAAIvZ,OAAO,GAAG0B,IAAI,CAAC1B,OAAO;QACtBwZ,cAAc,GAAG9X,IAAI,CAAC8X,cAAc;QACpCC,eAAe,GAAG/X,IAAI,CAAC+X,eAAe;QACtCC,UAAU,GAAGhY,IAAI,CAACgY,UAAU;QAC5BC,YAAY,GAAGjY,IAAI,CAACiY,YAAY;QAChCC,cAAc,GAAGlY,IAAI,CAACkY,cAAc;QACpCnX,KAAK,GAAGf,IAAI,CAACe,KAAK;QAClB6O,MAAM,GAAG5P,IAAI,CAAC4P,MAAM;MACxB,IAAIuI,YAAY,GAAGnY,IAAI,CAAC+O,MAAM;QAC1BC,IAAI,GAAGmJ,YAAY,CAACnJ,IAAI;QACxB3J,KAAK,GAAG8S,YAAY,CAAC9S,KAAK;QAC1B4J,IAAI,GAAGkJ,YAAY,CAAClJ,IAAI;QACxBzJ,IAAI,GAAG2S,YAAY,CAAC3S,IAAI;QACxBI,IAAI,GAAGuS,YAAY,CAACvS,IAAI;MAC5B,IAAIwS,MAAM,GAAG;QACXC,OAAO,EAAEhT;MACX,CAAC;MAED,IAAI5C,UAAU,EAAE;QACd2V,MAAM,CAACC,OAAO,GAAGX,UAAU,GAAGzI,IAAI,GAAGzJ,IAAI;QAEzC,IAAIyS,YAAY,EAAE;UAChBG,MAAM,CAACC,OAAO,GAAG,aAAanjB,KAAK,CAAC6Q,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEqS,MAAM,CAACC,OAAO,EAAE,IAAI,EAAEvW,KAAK,GAAG,CAAC,EAAE,GAAG,EAAEE,IAAI,EAAE,GAAG,CAAC;QAClH;MACF;MAEA,IAAIkW,cAAc,IAAI,CAACR,UAAU,EAAE;QACjCU,MAAM,CAACxS,IAAI,GAAG,aAAa1Q,KAAK,CAAC6Q,aAAa,CAAC,QAAQ,EAAEtN,QAAQ,CAAC;UAChE6O,KAAK,EAAEsI,MAAM,CAAC1B,UAAU;UACxB5N,IAAI,EAAE,QAAQ;UACd,WAAW,EAAE;QACf,CAAC,EAAEsX,SAAS,CAAC,EAAEhS,IAAI,CAAC;MACtB;MAEA,IAAI,CAACkS,cAAc,IAAIhW,KAAK,GAAG,CAAC,EAAE;QAChCsW,MAAM,CAACpJ,IAAI,GAAG,aAAa9Z,KAAK,CAAC6Q,aAAa,CAAC,QAAQ,EAAEtN,QAAQ,CAAC;UAChE6O,KAAK,EAAEsI,MAAM,CAAC9B,UAAU;UACxBxN,IAAI,EAAE;QACR,CAAC,EAAEkX,SAAS,CAAC,EAAExI,IAAI,CAAC;MACtB;MAEAoJ,MAAM,CAAC/S,KAAK,GAAG,CAAC0S,eAAe,IAAI,aAAa7iB,KAAK,CAAC6Q,aAAa,CAACkR,sBAAsB,EAAExe,QAAQ,CAAC;QACnGmX,MAAM,EAAEA,MAAM,CAAC3B;MACjB,CAAC,EAAEwJ,UAAU,CAAC,CAAC;MACf,OAAO,aAAaviB,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAEtN,QAAQ,CAAC;QACtDpB,GAAG,EAAE,gBAAgB;QACrB0c,SAAS,EAAE,wBAAwB;QACnCzM,KAAK,EAAEsI,MAAM,CAAC5C;MAChB,CAAC,EAAE6K,YAAY,CAAC,EAAE,aAAa3iB,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QACxDuB,KAAK,EAAEsI,MAAM,CAAC1C;MAChB,CAAC,EAAEnM,KAAK,IAAI,aAAa7L,KAAK,CAAC6Q,aAAa,CAAC,IAAI,EAAE;QACjDuB,KAAK,EAAEsI,MAAM,CAACxC,YAAY;QAC1B,YAAY,EAAErM;MAChB,CAAC,EAAEA,KAAK,CAAC,EAAE,aAAa7L,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QACjDuB,KAAK,EAAEsI,MAAM,CAACtC;MAChB,CAAC,EAAEhP,OAAO,CAAC,CAAC,EAAE,CAAC0Z,UAAU,IAAI,aAAa9iB,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QACnEuB,KAAK,EAAEsI,MAAM,CAACrC;MAChB,CAAC,EAAE,aAAarY,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QACzCuB,KAAK,EAAEsI,MAAM,CAACjC;MAChB,CAAC,EAAEyK,MAAM,CAACxS,IAAI,CAAC,EAAEwS,MAAM,CAACpJ,IAAI,EAAE,aAAa9Z,KAAK,CAAC6Q,aAAa,CAAC,QAAQ,EAAEtN,QAAQ,CAAC;QAChF6O,KAAK,EAAEsI,MAAM,CAAC/B,UAAU;QACxBvN,IAAI,EAAE;MACR,CAAC,EAAEqX,YAAY,CAAC,EAAES,MAAM,CAACC,OAAO,CAAC,CAAC,EAAED,MAAM,CAAC/S,KAAK,CAAC;IACnD;EACF,CAAC,CAAC,CAAC;EAEH,OAAOkS,uBAAuB;AAChC,CAAC,CAACriB,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAI8N,SAAS,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;AAEvD,IAAIC,cAAc,GAAG,aAAa,UAAUlG,gBAAgB,EAAE;EAC5DxZ,SAAS,CAAC0f,cAAc,EAAElG,gBAAgB,CAAC;EAE3C,IAAIC,MAAM,GAAG1X,YAAY,CAAC2d,cAAc,CAAC;EAEzC,SAASA,cAAcA,CAAA,EAAG;IACxB,IAAIhW,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAE6gB,cAAc,CAAC;IAErC,KAAK,IAAIrO,IAAI,GAAGjT,SAAS,CAACC,MAAM,EAAEiT,IAAI,GAAG,IAAI1L,KAAK,CAACyL,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGnT,SAAS,CAACmT,IAAI,CAAC;IAC9B;IAEA7H,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC/B,KAAK,CAACyb,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC7Q,MAAM,CAAC0I,IAAI,CAAC,CAAC;IAEtD7S,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUvI,CAAC,EAAE;MAC7EA,CAAC,CAACmX,cAAc,CAAC,CAAC;MAClB,IAAIqH,OAAO,GAAGjW,KAAK,CAACxK,KAAK,CAACygB,OAAO;MACjCA,OAAO,CAAC9S,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;IAEFpO,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUvI,CAAC,EAAE;MAC9EA,CAAC,CAACmX,cAAc,CAAC,CAAC;MAClB,IAAIqH,OAAO,GAAGjW,KAAK,CAACxK,KAAK,CAACygB,OAAO;MACjCA,OAAO,CAACnT,KAAK,CAAC,CAAC;IACjB,CAAC,CAAC;IAEF/N,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,oBAAoB,EAAE,UAAUvI,CAAC,EAAE;MAChFA,CAAC,CAACmX,cAAc,CAAC,CAAC;MAClB,IAAIqC,WAAW,GAAGjR,KAAK,CAACxK,KAAK;QACzB0K,UAAU,GAAG+Q,WAAW,CAAC/Q,UAAU;QACnC+V,OAAO,GAAGhF,WAAW,CAACgF,OAAO;MAEjC,IAAI,CAAC/V,UAAU,EAAE;QACf+V,OAAO,CAACnT,KAAK,CAAC,CAAC;QACf;MACF;MAEAmT,OAAO,CAAChT,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;IAEFlO,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUvI,CAAC,EAAE;MAC7EA,CAAC,CAACmX,cAAc,CAAC,CAAC;MAClB,IAAIqH,OAAO,GAAGjW,KAAK,CAACxK,KAAK,CAACygB,OAAO;MACjCA,OAAO,CAAC5S,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;IAEFtO,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,kBAAkB,EAAE,YAAY;MAC7E,IAAIoT,YAAY,GAAGpT,KAAK,CAACxK,KAAK;QAC1B0K,UAAU,GAAGkT,YAAY,CAAClT,UAAU;QACpCiV,UAAU,GAAG/B,YAAY,CAAC+B,UAAU;QACpCe,aAAa,GAAG9C,YAAY,CAAC8C,aAAa;QAC1CzY,IAAI,GAAG2V,YAAY,CAAC3V,IAAI;MAC5B,IAAIgP,IAAI,GAAG5Q,OAAO,CAAC4B,IAAI,CAAC+O,MAAM,CAACC,IAAI,CAAC;MACpC,IAAI3J,KAAK,GAAGjH,OAAO,CAAC4B,IAAI,CAAC+O,MAAM,CAAC1J,KAAK,CAAC;MACtC,IAAI4J,IAAI,GAAG7Q,OAAO,CAAC4B,IAAI,CAAC+O,MAAM,CAACE,IAAI,CAAC;MACpC,IAAIzJ,IAAI,GAAGpH,OAAO,CAAC4B,IAAI,CAAC+O,MAAM,CAACvJ,IAAI,CAAC;MACpC,IAAII,IAAI,GAAGxH,OAAO,CAAC4B,IAAI,CAAC+O,MAAM,CAACnJ,IAAI,CAAC;MACpC,IAAI8S,WAAW,GAAGjW,UAAU,GAAG+C,IAAI,GAAGH,KAAK;MAE3C,IAAIqS,UAAU,EAAE;QACdgB,WAAW,GAAGzJ,IAAI;MACpB;MAEA,OAAO;QACLuI,SAAS,EAAE;UACT,YAAY,EAAExI,IAAI;UAClB,aAAa,EAAE,MAAM;UACrB0E,OAAO,EAAEnR,KAAK,CAACoW,eAAe;UAC9BC,IAAI,EAAE,QAAQ;UACd7X,KAAK,EAAEiO;QACT,CAAC;QACDyI,UAAU,EAAE;UACV,YAAY,EAAEpS,KAAK;UACnB,aAAa,EAAE,OAAO;UACtBqO,OAAO,EAAEnR,KAAK,CAACsW,gBAAgB;UAC/BD,IAAI,EAAE,QAAQ;UACd7X,KAAK,EAAEsE;QACT,CAAC;QACDsS,YAAY,EAAE;UACZ,YAAY,EAAEe,WAAW;UACzB,aAAa,EAAE,SAAS;UACxBhF,OAAO,EAAEnR,KAAK,CAACuW,kBAAkB;UACjCF,IAAI,EAAE,QAAQ;UACd7X,KAAK,EAAE2X;QACT,CAAC;QACDd,SAAS,EAAE;UACT,YAAY,EAAEhS,IAAI;UAClB,aAAa,EAAE,MAAM;UACrB8N,OAAO,EAAEnR,KAAK,CAACwW,eAAe;UAC9BH,IAAI,EAAE,QAAQ;UACd7X,KAAK,EAAE6E;QACT,CAAC;QACDiS,YAAY,EAAE;UACZ,YAAY,EAAE,IAAI;UAClBnN,GAAG,EAAE+N,aAAa;UAClBG,IAAI,EAAE;QACR;MACF,CAAC;IACH,CAAC,CAAC;IAEF,OAAOrW,KAAK;EACd;EAEApK,YAAY,CAACogB,cAAc,EAAE,CAAC;IAC5BlhB,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAI2C,YAAY,GAAG,IAAI,CAACne,KAAK;QACzB0K,UAAU,GAAGyT,YAAY,CAACzT,UAAU;QACpCX,KAAK,GAAGoU,YAAY,CAACpU,KAAK;QAC1B4V,UAAU,GAAGxB,YAAY,CAACwB,UAAU;QACpC1V,IAAI,GAAGkU,YAAY,CAAClU,IAAI;QACxBhC,IAAI,GAAGkW,YAAY,CAAClW,IAAI;MAE5BA,IAAI,CAACuS,eAAe;MAChB,IAAIyG,gBAAgB,GAAGhZ,IAAI,CAACgZ,gBAAgB;QAC5CC,SAAS,GAAG5e,wBAAwB,CAAC2F,IAAI,EAAEsY,SAAS,CAAC;MAEzD,IAAIzE,SAAS;MAEb,IAAImF,gBAAgB,EAAE;QACpB,IAAIE,WAAW,GAAGpiB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqiB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAChF1W,UAAU,EAAEA,UAAU;UACtBX,KAAK,EAAEA,KAAK;UACZ4V,UAAU,EAAEA,UAAU;UACtB1V,IAAI,EAAEA,IAAI;UACVhC,IAAI,EAAEiZ;QACR,CAAC,CAAC;QAEF,IAAIG,gBAAgB,GAAGJ,gBAAgB;QACvCnF,SAAS,GAAG,aAAa3e,KAAK,CAAC6Q,aAAa,CAACqT,gBAAgB,EAAEF,WAAW,CAAC;MAC7E,CAAC,MAAM;QACLrF,SAAS,GAAG,aAAa3e,KAAK,CAAC6Q,aAAa,CAACwR,uBAAuB,EAAE9e,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0gB,gBAAgB,CAAC,CAAC,EAAE;UAC1G1W,UAAU,EAAEA,UAAU;UACtBX,KAAK,EAAEA,KAAK;UACZ4V,UAAU,EAAEA,UAAU;UACtB1V,IAAI,EAAEA,IAAI;UACVhC,IAAI,EAAEA;QACR,CAAC,CAAC,CAAC;MACL;MAEA,OAAO6T,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAO0E,cAAc;AACvB,CAAC,CAACrjB,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAI6O,aAAa,GAAG,aAAa,UAAUhH,gBAAgB,EAAE;EAC3DxZ,SAAS,CAACwgB,aAAa,EAAEhH,gBAAgB,CAAC;EAE1C,IAAIC,MAAM,GAAG1X,YAAY,CAACye,aAAa,CAAC;EAExC,SAASA,aAAaA,CAACthB,KAAK,EAAE;IAC5B,IAAIwK,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAE2hB,aAAa,CAAC;IAEpC9W,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC,IAAI,EAAEb,KAAK,CAAC;IAChC,IAAI,CAACmF,SAAS,EAAE,OAAOvC,0BAA0B,CAAC4H,KAAK,CAAC;IACxDA,KAAK,CAAC+W,IAAI,GAAG5b,QAAQ,CAACqI,aAAa,CAAC,KAAK,CAAC;IAC1C;;IAEA,IAAIhO,KAAK,CAAC4a,EAAE,EAAE;MACZpQ,KAAK,CAAC+W,IAAI,CAAC3G,EAAE,GAAG5a,KAAK,CAAC4a,EAAE;IAC1B;IAEAjV,QAAQ,CAAC2I,IAAI,CAACyM,WAAW,CAACvQ,KAAK,CAAC+W,IAAI,CAAC;IACrC,OAAO/W,KAAK;EACd;EAEApK,YAAY,CAACkhB,aAAa,EAAE,CAAC;IAC3BhiB,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASwa,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAC9V,SAAS,EAAE;MAEhB,IAAI,CAACC,SAAS,EAAE;QACd,IAAI,CAACoc,aAAa,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDliB,GAAG,EAAE,oBAAoB;IACzBmB,KAAK,EAAE,SAASid,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACvY,SAAS,EAAE;MAEhB,IAAI,CAACC,SAAS,EAAE;QACd,IAAI,CAACoc,aAAa,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDliB,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAAS4a,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAClW,SAAS,IAAI,CAAC,IAAI,CAACoc,IAAI,EAAE;MAE9B,IAAI,CAACnc,SAAS,EAAE;QACd7H,QAAQ,CAACkkB,sBAAsB,CAAC,IAAI,CAACF,IAAI,CAAC;MAC5C;MAEA5b,QAAQ,CAAC2I,IAAI,CAACiN,WAAW,CAAC,IAAI,CAACgG,IAAI,CAAC;IACtC;EACF,CAAC,EAAE;IACDjiB,GAAG,EAAE,eAAe;IACpBmB,KAAK,EAAE,SAAS+gB,aAAaA,CAAA,EAAG;MAC9B,IAAI,CAACrc,SAAS,EAAE,OAAO,IAAI;MAC3B,IAAI0B,QAAQ,GAAG,IAAI,CAAC7G,KAAK,CAAC6G,QAAQ;MAClCtJ,QAAQ,CAACmkB,mCAAmC,CAAC,IAAI,EAAE7a,QAAQ,EAAE,IAAI,CAAC0a,IAAI,CAAC;MACvE,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjiB,GAAG,EAAE,eAAe;IACpBmB,KAAK,EAAE,SAASkhB,aAAaA,CAAA,EAAG;MAC9B,IAAI,CAACxc,SAAS,IAAI,CAACC,SAAS,EAAE,OAAO,IAAI;MACzC,IAAIyB,QAAQ,GAAG,IAAI,CAAC7G,KAAK,CAAC6G,QAAQ;MAClC,OAAO,aAAatJ,QAAQ,CAACC,YAAY,CAACqJ,QAAQ,EAAE,IAAI,CAAC0a,IAAI,CAAC;IAChE;EACF,CAAC,EAAE;IACDjiB,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACpW,SAAS,EAAE;QACd,OAAO,IAAI;MACb;MAEA,OAAO,IAAI,CAACuc,aAAa,CAAC,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;EAEH,OAAOL,aAAa;AACtB,CAAC,CAACnkB,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAImP,WAAW,GAAG,aAAa,UAAUtH,gBAAgB,EAAE;EACzDxZ,SAAS,CAAC8gB,WAAW,EAAEtH,gBAAgB,CAAC;EAExC,IAAIC,MAAM,GAAG1X,YAAY,CAAC+e,WAAW,CAAC;EAEtC,SAASA,WAAWA,CAAA,EAAG;IACrB,IAAIpX,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAEiiB,WAAW,CAAC;IAElC,KAAK,IAAIzP,IAAI,GAAGjT,SAAS,CAACC,MAAM,EAAEiT,IAAI,GAAG,IAAI1L,KAAK,CAACyL,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGnT,SAAS,CAACmT,IAAI,CAAC;IAC9B;IAEA7H,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC/B,KAAK,CAACyb,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC7Q,MAAM,CAAC0I,IAAI,CAAC,CAAC;IAEtD7S,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDqX,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG,CAAC;IACvC,CAAC,CAAC;IAEFtiB,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,wBAAwB,EAAE,UAAUvI,CAAC,EAAE;MACpF,IAAIwZ,WAAW,GAAGjR,KAAK,CAACxK,KAAK;QACzBiI,IAAI,GAAGwT,WAAW,CAACxT,IAAI;QACvB6Z,MAAM,GAAGrG,WAAW,CAACqG,MAAM;MAE/B,IAAI7f,CAAC,CAACsG,IAAI,KAAK,YAAY,IAAIN,IAAI,CAACkP,KAAK,KAAK,OAAO,EAAE;QACrD;MACF;MAEA2K,MAAM,CAAC;QACL9X,SAAS,EAAEvF,SAAS,CAACN;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF5E,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIoT,YAAY,GAAGpT,KAAK,CAACxK,KAAK;QAC1BygB,OAAO,GAAG7C,YAAY,CAAC6C,OAAO;QAC9BxY,IAAI,GAAG2V,YAAY,CAAC3V,IAAI;MAE5B,IAAI,CAACA,IAAI,CAAC0W,mBAAmB,EAAE;QAC7B8B,OAAO,CAACnT,KAAK,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;IAEF/N,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,eAAe,EAAE,UAAU5D,CAAC,EAAE;MAC3E4D,KAAK,CAACyK,OAAO,GAAGrO,CAAC;IACnB,CAAC,CAAC;IAEFrH,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUuX,MAAM,EAAExZ,IAAI,EAAE;MAClF,IAAI4V,YAAY,GAAG3T,KAAK,CAACxK,KAAK;QAC1B6J,MAAM,GAAGsU,YAAY,CAACtU,MAAM;QAC5BmY,SAAS,GAAG7D,YAAY,CAAC6D,SAAS;QAClCF,MAAM,GAAG3D,YAAY,CAAC2D,MAAM;MAEhC,IAAIvZ,IAAI,KAAK,SAAS,EAAE;QACtBiC,KAAK,CAACyX,YAAY,GAAGF,MAAM;MAC7B,CAAC,MAAM;QACLvX,KAAK,CAAC0X,aAAa,GAAGH,MAAM;MAC9B;MAEAC,SAAS,CAACD,MAAM,EAAExZ,IAAI,CAAC;MAEvB,IAAIiC,KAAK,CAACyX,YAAY,IAAIzX,KAAK,CAAC0X,aAAa,EAAE;QAC7CJ,MAAM,CAAC;UACLjY,MAAM,EAAEA,MAAM,KAAKzG,OAAO,CAACQ,KAAK,GAAGR,OAAO,CAACQ,KAAK,GAAGiG,MAAM;UACzDG,SAAS,EAAEvF,SAAS,CAACC;QACvB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO8F,KAAK;EACd;EAEApK,YAAY,CAACwhB,WAAW,EAAE,CAAC;IACzBtiB,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASwa,iBAAiBA,CAAA,EAAG;MAClC,IAAIwD,YAAY,GAAG,IAAI,CAACze,KAAK;QACzBqJ,KAAK,GAAGoV,YAAY,CAACpV,KAAK;QAC1BU,KAAK,GAAG0U,YAAY,CAAC1U,KAAK;MAC9BjB,GAAG,CAAC;QACFE,KAAK,EAAE,OAAO,CAACU,MAAM,CAACK,KAAK,CAAC;QAC5Bd,IAAI,EAAE,CAAC;UACL3J,GAAG,EAAE,OAAO;UACZmB,KAAK,EAAE,IAAI,CAACT;QACd,CAAC,CAAC;QACFqJ,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/J,GAAG,EAAE,oBAAoB;IACzBmB,KAAK,EAAE,SAASid,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIwE,YAAY,GAAG,IAAI,CAACniB,KAAK;QACzB6J,MAAM,GAAGsY,YAAY,CAACtY,MAAM;QAC5BuY,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChC1X,UAAU,GAAGyX,YAAY,CAACzX,UAAU;QACpCZ,UAAU,GAAGqY,YAAY,CAACrY,UAAU;QACpCT,KAAK,GAAG8Y,YAAY,CAAC9Y,KAAK;QAC1BU,KAAK,GAAGoY,YAAY,CAACpY,KAAK;QAC1BC,SAAS,GAAGmY,YAAY,CAACnY,SAAS;QAClCC,IAAI,GAAGkY,YAAY,CAAClY,IAAI;QACxBC,MAAM,GAAGiY,YAAY,CAACjY,MAAM;QAC5BjC,IAAI,GAAGka,YAAY,CAACla,IAAI;QACxB6Z,MAAM,GAAGK,YAAY,CAACL,MAAM;MAEhC,IAAIhE,YAAY,GAAGzgB,WAAW,CAACsgB,SAAS,EAAE,IAAI,CAAC3d,KAAK,CAAC;QACjD+d,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BsE,WAAW,GAAGvE,YAAY,CAACuE,WAAW;MAE1C,IAAIpX,KAAK,GAAG;QACVpB,MAAM,EAAEA,MAAM;QACdC,UAAU,EAAEA,UAAU;QACtBC,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBC,IAAI,EAAEA,IAAI;QACVC,MAAM,EAAEA;MACV,CAAC;MACD,IAAIoY,UAAU,GAAG5X,UAAU,IAAIb,MAAM,KAAKzG,OAAO,CAACQ,KAAK,KAAKmG,KAAK,GAAG,CAAC,IAAIF,MAAM,KAAKzG,OAAO,CAACK,IAAI,CAAC;MACjG,IAAI8e,eAAe,GAAGxE,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,WAAW,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC;MACxG,IAAIyE,UAAU,GAAGH,WAAW,CAAC,WAAW,EAAE,CAAC5d,SAAS,CAACN,OAAO,EAAEM,SAAS,CAACpB,IAAI,CAAC,EAAEoB,SAAS,CAACpB,IAAI,CAAC;MAC9F,IAAIof,aAAa,GAAG1E,OAAO,CAAC,QAAQ,EAAE,CAAC3a,OAAO,CAACM,IAAI,EAAEN,OAAO,CAACK,IAAI,EAAEL,OAAO,CAACS,IAAI,EAAET,OAAO,CAACQ,KAAK,CAAC,CAAC;MAEhG,IAAI6e,aAAa,KAAKD,UAAU,IAAI1Y,UAAU,CAAC,EAAE;QAC/CsY,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACrDlB,KAAK,EAAE4T,SAAS,CAAC5T,KAAK;UACtBC,SAAS,EAAEvF,SAAS,CAACE,QAAQ;UAC7BsD,IAAI,EAAE0V,SAAS,CAAC1V,IAAI;UACpBM,IAAI,EAAExE,MAAM,CAACK;QACf,CAAC,CAAC,CAAC;MACL;MAEA,IAAI2Z,OAAO,CAAC,OAAO,CAAC,IAAIhU,KAAK,GAAG,CAAC,IAAIC,SAAS,KAAKvF,SAAS,CAACpB,IAAI,IAAI6G,MAAM,KAAKtF,MAAM,CAACG,OAAO,IAAIkD,IAAI,CAACE,SAAS,KAAK,QAAQ,EAAE;QAC7H2Z,MAAM,CAAC;UACL9X,SAAS,EAAEvF,SAAS,CAACC;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;;MAGF,IAAI6d,eAAe,IAAIta,IAAI,EAAE;QAC3B,IAAIiG,OAAO,GAAGU,UAAU,CAAC3G,IAAI,CAACjJ,MAAM,CAAC;QACrC,IAAI0jB,aAAa,GAAG,CAAC,CAACxU,OAAO;QAC7B,IAAIyU,iBAAiB,GAAGD,aAAa,IAAIzS,gBAAgB,CAAC/B,OAAO,CAAC;QAElE,IAAIyU,iBAAiB,EAAE;UACrB,IAAIN,WAAW,CAAC,QAAQ,EAAEzd,MAAM,CAACF,KAAK,EAAEE,MAAM,CAACG,OAAO,CAAC,IAAIsd,WAAW,CAAC,WAAW,EAAE5d,SAAS,CAACpB,IAAI,EAAEoB,SAAS,CAACC,KAAK,CAAC,EAAE;YACpH0d,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACrDhD,IAAI,EAAEA,IAAI;cACVM,IAAI,EAAExE,MAAM,CAACE;YACf,CAAC,CAAC,CAAC;UACL;QACF,CAAC,MAAM;UACLsF,OAAO,CAACJ,IAAI,CAACuZ,aAAa,GAAG,oBAAoB,GAAG,oBAAoB,EAAEza,IAAI,CAAC,CAAC,CAAC;;UAEjFma,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACrD1C,IAAI,EAAExE,MAAM,CAACQ,gBAAgB;YAC7B0D,IAAI,EAAEA;UACR,CAAC,CAAC,CAAC;UAEH,IAAI,CAAC6B,UAAU,EAAE;YACfgY,MAAM,CAAC;cACL/X,KAAK,EAAEA,KAAK,IAAI,CAAC3G,OAAO,CAACK,IAAI,CAAC,CAACpB,OAAO,CAACwH,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;YAChE,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAIwY,WAAW,CAAC,WAAW,EAAE5d,SAAS,CAACpB,IAAI,EAAEoB,SAAS,CAACC,KAAK,CAAC,EAAE;QAC7Dod,MAAM,CAAC;UACL9X,SAAS,EAAEhC,UAAU,CAACC,IAAI,CAAC,IAAIqa,UAAU,GAAG7d,SAAS,CAACN,OAAO,GAAGM,SAAS,CAACP;QAC5E,CAAC,CAAC;MACJ;MAEA,IAAI6Z,OAAO,CAAC,OAAO,CAAC,EAAE;QACpBjV,GAAG,CAAC;UACFE,KAAK,EAAE,OAAO,CAACU,MAAM,CAACM,SAAS,CAAC;UAChCf,IAAI,EAAE,CAAC;YACL3J,GAAG,EAAE,OAAO;YACZmB,KAAK,EAAE,IAAI,CAACT;UACd,CAAC,CAAC;UACFqJ,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA;;MAGA,IAAI0U,OAAO,CAAC,WAAW,EAAEtZ,SAAS,CAACP,MAAM,CAAC,EAAE;QAC1Cke,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACrDhD,IAAI,EAAEA,IAAI;UACVM,IAAI,EAAExE,MAAM,CAACG;QACf,CAAC,CAAC,CAAC;MACL;MAEA,IAAI6Z,OAAO,CAAC,WAAW,EAAEtZ,SAAS,CAACN,OAAO,CAAC,EAAE;QAC3Cie,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACrDhD,IAAI,EAAEA,IAAI;UACVM,IAAI,EAAExE,MAAM,CAACI;QACf,CAAC,CAAC,CAAC;QACH,IAAI,CAACye,KAAK,GAAG,IAAIzK,KAAK,CAAC,IAAI,CAAClD,OAAO,EAAE;UACnCiF,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF,IAAI,CAAC0I,KAAK,CAACxI,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAIiI,WAAW,CAAC,WAAW,EAAE,CAAC5d,SAAS,CAACN,OAAO,EAAEM,SAAS,CAACpB,IAAI,CAAC,EAAEoB,SAAS,CAACpB,IAAI,CAAC,EAAE;QACjF,IAAI,CAACuf,KAAK,CAACf,WAAW,CAAC,CAAC;QACxB,OAAO,IAAI,CAACI,YAAY;QACxB,OAAO,IAAI,CAACC,aAAa;MAC3B;IACF;EACF,CAAC,EAAE;IACD5iB,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAAS4a,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACuH,KAAK,CAACf,WAAW,CAAC,CAAC;IAC1B;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDviB,GAAG,EAAE,MAAM;IACXmN,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,IAAIoW,YAAY,GAAG,IAAI,CAAC7iB,KAAK;QACzBiI,IAAI,GAAG4a,YAAY,CAAC5a,IAAI;QACxB+B,SAAS,GAAG6Y,YAAY,CAAC7Y,SAAS;MACtC,OAAO,CAAC,EAAEhC,UAAU,CAACC,IAAI,CAAC,IAAI+B,SAAS,KAAKvF,SAAS,CAACN,OAAO,CAAC;IAChE;EACF,CAAC,EAAE;IACD7E,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAIsH,YAAY,GAAG,IAAI,CAAC9iB,KAAK;QACzB0K,UAAU,GAAGoY,YAAY,CAACpY,UAAU;QACpCrB,KAAK,GAAGyZ,YAAY,CAACzZ,KAAK;QAC1BoX,OAAO,GAAGqC,YAAY,CAACrC,OAAO;QAC9B1W,KAAK,GAAG+Y,YAAY,CAAC/Y,KAAK;QAC1BC,SAAS,GAAG8Y,YAAY,CAAC9Y,SAAS;QAClC6Q,KAAK,GAAGiI,YAAY,CAACjI,KAAK;QAC1BkI,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxC9Y,IAAI,GAAG6Y,YAAY,CAAC7Y,IAAI;QACxBhC,IAAI,GAAG6a,YAAY,CAAC7a,IAAI;MAC5B,IAAIjJ,MAAM,GAAG4P,UAAU,CAAC3G,IAAI,CAACjJ,MAAM,CAAC;MAEpC,IAAI,CAACiZ,YAAY,CAAChQ,IAAI,CAAC,IAAI,CAAC3K,EAAE,CAACoL,UAAU,CAAC1J,MAAM,CAAC,EAAE;QACjD,OAAO,IAAI;MACb;MAEA,OAAO,aAAa7B,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QAC7C1O,GAAG,EAAE,cAAc,CAACoK,MAAM,CAACK,KAAK,CAAC;QACjCiS,SAAS,EAAE;MACb,CAAC,EAAE,aAAa7e,KAAK,CAAC6Q,aAAa,CAACsT,aAAa,EAAE;QACjD1G,EAAE,EAAE;MACN,CAAC,EAAE,aAAazd,KAAK,CAAC6Q,aAAa,CAACmO,cAAc,EAAEzb,QAAQ,CAAC,CAAC,CAAC,EAAEuH,IAAI,EAAE;QACrEoB,KAAK,EAAEA,KAAK;QACZW,SAAS,EAAEA,SAAS;QACpB4U,cAAc,EAAE,IAAI,CAACoE;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa7lB,KAAK,CAAC6Q,aAAa,CAAC/P,OAAO,EAAEyC,QAAQ,CAAC;QACvDob,SAAS,EAAE,aAAa3e,KAAK,CAAC6Q,aAAa,CAACwS,cAAc,EAAE;UAC1D9V,UAAU,EAAEA,UAAU;UACtB+V,OAAO,EAAEA,OAAO;UAChB1W,KAAK,EAAEA,KAAK;UACZ4V,UAAU,EAAE5V,KAAK,GAAG,CAAC,KAAKE,IAAI;UAC9ByW,aAAa,EAAE,IAAI,CAACA,aAAa;UACjCzW,IAAI,EAAEA,IAAI;UACVhC,IAAI,EAAEA;QACR,CAAC,CAAC;QACFoB,KAAK,EAAEA,KAAK;QACZ4Z,SAAS,EAAE,IAAI,CAACjB,SAAS;QACzBpH,EAAE,EAAE,qBAAqB,CAAClR,MAAM,CAACK,KAAK,CAAC;QACvCmZ,YAAY,EAAEjb,IAAI,CAACkb,OAAO,IAAIvT,WAAW,CAAC5Q,MAAM,CAAC;QACjD0O,IAAI,EAAE,IAAI,CAACA,IAAI;QACfvF,SAAS,EAAEF,IAAI,CAACE,SAAS;QACzBnJ,MAAM,EAAEiJ,IAAI,CAACjJ;MACf,CAAC,EAAEiJ,IAAI,CAAC2O,YAAY,CAAC,EAAE,aAAazZ,KAAK,CAAC6Q,aAAa,CAACqM,aAAa,EAAE;QACrEG,eAAe,EAAEvS,IAAI,CAACuS,eAAe;QACrCxD,MAAM,EAAE/O,IAAI,CAAC+O,MAAM;QACnB6D,KAAK,EAAEA,KAAK;QACZa,cAAc,EAAE,IAAI,CAAC0H,sBAAsB;QAC3CjI,WAAW,EAAE4H,YAAY;QACzBlL,MAAM,EAAE5P,IAAI,CAAC4P;MACf,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,CAAC,CAAC;EAEH,OAAO+J,WAAW;AACpB,CAAC,CAACzkB,KAAK,CAACsV,SAAS,CAAC;AAElB,IAAI4Q,OAAO,GAAG,aAAa,UAAU/I,gBAAgB,EAAE;EACrDxZ,SAAS,CAACuiB,OAAO,EAAE/I,gBAAgB,CAAC;EAEpC,IAAIC,MAAM,GAAG1X,YAAY,CAACwgB,OAAO,CAAC;EAElC,SAASA,OAAOA,CAACrjB,KAAK,EAAE;IACtB,IAAIwK,KAAK;IAET7K,eAAe,CAAC,IAAI,EAAE0jB,OAAO,CAAC;IAE9B7Y,KAAK,GAAG+P,MAAM,CAAC1Z,IAAI,CAAC,IAAI,EAAEb,KAAK,CAAC;IAEhCT,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,WAAW,EAAE,YAAY;MACtE,IAAIiR,WAAW,GAAGjR,KAAK,CAACxK,KAAK;QACzBqJ,KAAK,GAAGoS,WAAW,CAACpS,KAAK;QACzBgE,UAAU,GAAGoO,WAAW,CAACpO,UAAU;QACnCiW,GAAG,GAAG7H,WAAW,CAAC6H,GAAG;QACrB3Y,SAAS,GAAG8Q,WAAW,CAAC9Q,SAAS;MACrCH,KAAK,CAACH,KAAK,GAAG,IAAID,WAAW,CAACrL,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyL,KAAK,CAACxK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAChF8J,UAAU,EAAEwZ,GAAG,IAAIhmB,EAAE,CAACsL,MAAM,CAAC+B,SAAS;MACxC,CAAC,CAAC,CAAC;MACHH,KAAK,CAACiW,OAAO,GAAGjW,KAAK,CAACH,KAAK,CAACgD,UAAU,CAAC,CAAC;MACxC,IAAIkW,WAAW,GAAG/Y,KAAK,CAACH,KAAK,CAACkZ,WAAW;MACzCza,GAAG,CAAC;QACFE,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,CAAC;UACL3J,GAAG,EAAE,OAAO;UACZmB,KAAK,EAAE+J,KAAK,CAACxK;QACf,CAAC,EAAE;UACDV,GAAG,EAAE,OAAO;UACZmB,KAAK,EAAE+J,KAAK,CAACS;QACf,CAAC,CAAC;QACF5B,KAAK,EAAEA;MACT,CAAC,CAAC,CAAC,CAAC;;MAEJka,WAAW,CAAC/Y,KAAK,CAACgZ,SAAS,CAAC;MAC5BnW,UAAU,CAAC7C,KAAK,CAACiW,OAAO,CAAC;MACzB,OAAOjW,KAAK,CAACH,KAAK,CAACW,QAAQ,CAAC,CAAC;IAC/B,CAAC,CAAC;IAEFzL,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,UAAU,EAAE,UAAUvB,IAAI,EAAE;MACzE,IAAImZ,QAAQ,GAAG5X,KAAK,CAACxK,KAAK,CAACoiB,QAAQ;MACnC;;MAEA,IAAI9kB,EAAE,CAAC,UAAU,CAAC,CAAC8kB,QAAQ,CAAC,EAAE;QAC5BA,QAAQ,CAACnZ,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;IAEF1J,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,gBAAgB,EAAE,UAAUvI,CAAC,EAAE;MAC5E,IAAIuc,WAAW,GAAGhU,KAAK,CAACS,KAAK;QACzBlB,KAAK,GAAGyU,WAAW,CAACzU,KAAK;QACzBC,SAAS,GAAGwU,WAAW,CAACxU,SAAS;MACrC,IAAIa,KAAK,GAAGL,KAAK,CAACxK,KAAK,CAAC6K,KAAK;MAC7B,IAAI5C,IAAI,GAAG4C,KAAK,CAACd,KAAK,CAAC;MACvB,IAAI0Z,MAAM,GAAG/d,MAAM,CAACge,KAAK,GAAGzhB,CAAC,CAAC0hB,KAAK,GAAG1hB,CAAC,CAAC+W,OAAO;MAE/C,IAAIhP,SAAS,KAAKvF,SAAS,CAACN,OAAO,EAAE;QACnC,IAAIsf,MAAM,KAAK,EAAE,IAAIxb,IAAI,IAAI,CAACA,IAAI,CAAC2b,iBAAiB,EAAE;UACpDpZ,KAAK,CAACH,KAAK,CAACiD,KAAK,CAAC,CAAC;QACrB;MACF;IACF,CAAC,CAAC;IAEF/N,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUS,KAAK,EAAE;MAC3ET,KAAK,CAACW,QAAQ,CAACF,KAAK,CAAC;IACvB,CAAC,CAAC;IAEF1L,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUuX,MAAM,EAAExZ,IAAI,EAAE;MAClF,IAAIA,IAAI,KAAK,SAAS,EAAE;QACtBiC,KAAK,CAACyX,YAAY,GAAGF,MAAM;MAC7B,CAAC,MAAM;QACLvX,KAAK,CAAC0X,aAAa,GAAGH,MAAM;MAC9B;IACF,CAAC,CAAC;IAEFxiB,eAAe,CAACkD,sBAAsB,CAAC+H,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUgT,gBAAgB,EAAEzT,KAAK,EAAE8Z,iBAAiB,EAAE7Z,SAAS,EAAE/B,IAAI,EAAEjJ,MAAM,EAAE8kB,SAAS,EAAE;MACvJ,OAAO,CAACtG,gBAAgB,KAAKzT,KAAK,KAAK,CAAC,IAAI8Z,iBAAiB,IAAI7Z,SAAS,KAAKvF,SAAS,CAACN,OAAO,CAAC,IAAI8D,IAAI,CAACE,SAAS,KAAK,QAAQ,KAAK,CAACF,IAAI,CAACkb,OAAO,IAAI,CAACvT,WAAW,CAAC5Q,MAAM,CAAC,CAAC;MAAI;MAC/K8kB,SAAS,CAAC9Z,SAAS,KAAKA,SAAS,IAAI,CAACvF,SAAS,CAACP,MAAM,EAAEO,SAAS,CAACN,OAAO,CAAC,CAAC9B,OAAO,CAAC2H,SAAS,CAAC,KAAK,CAAC,CAAC;IACtG,CAAC,CAAC;IAEFQ,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACuZ,SAAS,CAAC,CAAC;IAC/B,OAAOvZ,KAAK;EACd;EAEApK,YAAY,CAACijB,OAAO,EAAE,CAAC;IACrB/jB,GAAG,EAAE,mBAAmB;IACxBmB,KAAK,EAAE,SAASwa,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAC9V,SAAS,EAAE;MAChB,IAAIyY,YAAY,GAAG,IAAI,CAAC5d,KAAK;QACzB4jB,iBAAiB,GAAGhG,YAAY,CAACgG,iBAAiB;QAClDva,KAAK,GAAGuU,YAAY,CAACvU,KAAK;QAC1Bia,GAAG,GAAG1F,YAAY,CAAC0F,GAAG;QACtBzY,KAAK,GAAG+S,YAAY,CAAC/S,KAAK;MAC9B,IAAImZ,KAAK,GAAG,IAAI,CAAC3Z,KAAK,CAAC2Z,KAAK;MAE5B,IAAI9L,aAAa,CAACrN,KAAK,EAAExB,KAAK,CAAC,IAAIia,GAAG,EAAE;QACtCU,KAAK,CAAC,CAAC;MACT;MACA;;MAGA,IAAI,CAACJ,iBAAiB,EAAE;QACtBje,QAAQ,CAAC2I,IAAI,CAAC6L,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC8J,cAAc,EAAE;UAC7DhG,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACD3e,GAAG,EAAE,oBAAoB;IACzBmB,KAAK,EAAE,SAASid,kBAAkBA,CAACC,SAAS,EAAEmG,SAAS,EAAE;MACvD,IAAI,CAAC3e,SAAS,EAAE;MAChB,IAAI+e,YAAY,GAAG,IAAI,CAACjZ,KAAK;QACzBpB,MAAM,GAAGqa,YAAY,CAACra,MAAM;QAC5BC,UAAU,GAAGoa,YAAY,CAACpa,UAAU;QACpCC,KAAK,GAAGma,YAAY,CAACna,KAAK;QAC1BC,SAAS,GAAGka,YAAY,CAACla,SAAS;QAClCE,MAAM,GAAGga,YAAY,CAACha,MAAM;MAChC,IAAIiU,YAAY,GAAG,IAAI,CAACne,KAAK;QACzBqJ,KAAK,GAAG8U,YAAY,CAAC9U,KAAK;QAC1Bia,GAAG,GAAGnF,YAAY,CAACmF,GAAG;QACtB3Y,SAAS,GAAGwT,YAAY,CAACxT,SAAS;QAClCE,KAAK,GAAGsT,YAAY,CAACtT,KAAK;MAC9B,IAAIsZ,SAAS,GAAGxG,SAAS,CAAC9S,KAAK;QAC3BuZ,aAAa,GAAGzG,SAAS,CAAChT,SAAS;MACvC,IAAI0Z,WAAW,GAAG,IAAI,CAACha,KAAK;QACxBuD,KAAK,GAAGyW,WAAW,CAACzW,KAAK;QACzBxB,QAAQ,GAAGiY,WAAW,CAACjY,QAAQ;QAC/B4X,KAAK,GAAGK,WAAW,CAACL,KAAK;QACzBM,IAAI,GAAGD,WAAW,CAACC,IAAI;QACvBxC,MAAM,GAAGuC,WAAW,CAACvC,MAAM;MAE/B,IAAIhE,YAAY,GAAGzgB,WAAW,CAACsgB,SAAS,EAAE,IAAI,CAAC3d,KAAK,CAAC;QACjDukB,YAAY,GAAGzG,YAAY,CAACC,OAAO;MAEvC,IAAIyG,aAAa,GAAGnnB,WAAW,CAACymB,SAAS,EAAE,IAAI,CAAC7Y,KAAK,CAAC;QAClD8S,OAAO,GAAGyG,aAAa,CAACzG,OAAO;QAC/BsE,WAAW,GAAGmC,aAAa,CAACnC,WAAW;MAE3C,IAAIpa,IAAI,GAAGuP,aAAa,CAAC3M,KAAK,CAACd,KAAK,CAAC,EAAE,IAAI,CAAC/J,KAAK,CAAC;MAClD,IAAIykB,YAAY,GAAG,CAACrc,OAAO,CAAC+b,SAAS,EAAEtZ,KAAK,CAAC;MAC7C,IAAI6Z,gBAAgB,GAAGpnB,EAAE,CAACsL,MAAM,CAAC+B,SAAS,CAAC,IAAI4Z,YAAY,CAAC,WAAW,CAAC;MACxE,IAAIvlB,MAAM,GAAG4P,UAAU,CAAC3G,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACjJ,MAAM,CAAC;MAEhF,IAAIylB,YAAY,EAAE;QAChB,IAAIvM,aAAa,CAACrN,KAAK,EAAExB,KAAK,CAAC,EAAE;UAC/B+C,QAAQ,CAACvB,KAAK,CAAC;QACjB,CAAC,MAAM;UACLtB,OAAO,CAACJ,IAAI,CAAC,qBAAqB,EAAE0B,KAAK,CAAC,CAAC,CAAC;QAC9C;MACF;MACA;;MAGA,IAAI0Z,YAAY,CAAC,KAAK,CAAC,EAAE;QACvB,IAAIjB,GAAG,EAAE;UACPU,KAAK,CAACrZ,SAAS,CAAC;QAClB,CAAC,MAAM;UACL2Z,IAAI,CAAC,CAAC;QACR;MACF;MACA;;MAGA,IAAII,gBAAgB,EAAE;QACpB,IAAIC,UAAU,GAAGP,aAAa,GAAGzZ,SAAS,GAAGvH,OAAO,CAACM,IAAI,GAAGN,OAAO,CAACK,IAAI;QAExE,IAAIoG,MAAM,KAAKzG,OAAO,CAACG,IAAI,EAAE;UAC3BohB,UAAU,GAAGvhB,OAAO,CAACE,KAAK;QAC5B;QAEA,IAAI,EAAE,CAACsB,MAAM,CAACM,QAAQ,EAAEN,MAAM,CAACK,OAAO,CAAC,CAAC5C,OAAO,CAAC6H,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;UAC/D4X,MAAM,CAAC;YACLjY,MAAM,EAAEA,MAAM,KAAKzG,OAAO,CAACQ,KAAK,GAAGR,OAAO,CAACQ,KAAK,GAAG+gB,UAAU;YAC7D5a,KAAK,EAAEY,SAAS;YAChBX,SAAS,EAAEvF,SAAS,CAACpB;UACvB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAGF,IAAI,CAACyG,UAAU,IAAII,MAAM,KAAKtF,MAAM,CAACG,OAAO,IAAIgF,KAAK,KAAK,CAAC,IAAI,CAAC/K,MAAM,EAAE;QACtE8iB,MAAM,CAAC;UACL/X,KAAK,EAAEA,KAAK,GAAG;QACjB,CAAC,CAAC;QACF,IAAI,CAACqY,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/D1C,IAAI,EAAExE,MAAM,CAACQ,gBAAgB;UAC7B0D,IAAI,EAAEA;QACR,CAAC,CAAC,CAAC;MACL;MAEA,IAAI2c,YAAY,GAAG7lB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACpElB,KAAK,EAAEA,KAAK;QACZ9B,IAAI,EAAEA;MACR,CAAC,CAAC;MAEF,IAAIwa,aAAa,GAAG1E,OAAO,CAAC,QAAQ,EAAE,CAAC3a,OAAO,CAACM,IAAI,EAAEN,OAAO,CAACK,IAAI,EAAEL,OAAO,CAACS,IAAI,EAAET,OAAO,CAACQ,KAAK,CAAC,CAAC;MAEhG,IAAI6e,aAAa,IAAI1E,OAAO,CAAC,QAAQ,EAAEnZ,MAAM,CAACI,MAAM,CAAC,EAAE;QACrD,IAAI6f,QAAQ,GAAGrN,aAAa,CAAC3M,KAAK,CAACiZ,SAAS,CAAC/Z,KAAK,CAAC,EAAE,IAAI,CAAC/J,KAAK,CAAC;QAChE,IAAI,CAACoiB,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE7a,KAAK,EAAE+Z,SAAS,CAAC/Z,KAAK;UACtBC,SAAS,EAAEvF,SAAS,CAACE,QAAQ;UAC7BsD,IAAI,EAAE4c,QAAQ;UACdtc,IAAI,EAAExE,MAAM,CAACK;QACf,CAAC,CAAC,CAAC;MACL;MAEA,IAAI2Z,OAAO,CAAC,QAAQ,EAAE,CAACnZ,MAAM,CAACM,QAAQ,EAAEN,MAAM,CAACK,OAAO,CAAC,CAAC,EAAE;QACxD,IAAI6f,SAAS,GAAGtN,aAAa,CAAC3M,KAAK,CAACiZ,SAAS,CAAC/Z,KAAK,CAAC,EAAE,IAAI,CAAC/J,KAAK,CAAC;QAEjE,IAAI,CAAC8J,UAAU,EAAE;UACf,IAAI,CAACsY,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;YACjE7a,KAAK,EAAE+Z,SAAS,CAAC/Z,KAAK;YACtBC,SAAS,EAAEvF,SAAS,CAACE,QAAQ;YAC7BsD,IAAI,EAAE6c,SAAS;YACfvc,IAAI,EAAExE,MAAM,CAACK;UACf,CAAC,CAAC,CAAC;QACL;QAEA,IAAI,CAACge,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE7a,KAAK,EAAE+Z,SAAS,CAAC/Z,KAAK;UACtB;UACA9B,IAAI,EAAE6c,SAAS;UACfvc,IAAI,EAAExE,MAAM,CAACM;QACf,CAAC,CAAC,CAAC;QACHuJ,KAAK,CAAC,CAAC;MACT,CAAC,MAAM,IAAIyU,WAAW,CAAC,QAAQ,EAAE,CAACzd,MAAM,CAACC,IAAI,EAAED,MAAM,CAACF,KAAK,CAAC,EAAEE,MAAM,CAACG,OAAO,CAAC,EAAE;QAC7E,IAAI,CAACqd,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACjErc,IAAI,EAAExE,MAAM,CAACC;QACf,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAI+Z,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC5B,IAAI,CAACqE,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACjErc,IAAI,EAAExE,MAAM,CAACO;QACf,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAIyZ,OAAO,CAAC,QAAQ,EAAE3a,OAAO,CAACI,KAAK,CAAC,EAAE;QAC3C,IAAI,CAAC4e,QAAQ,CAACrjB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6lB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACjErc,IAAI,EAAExE,MAAM,CAACO;QACf,CAAC,CAAC,CAAC;MACL;MAEA,IAAI2D,IAAI,EAAE;QACR,IAAI,CAAC8c,YAAY,CAACjB,SAAS,CAAC;QAE5B,IAAIja,MAAM,KAAKzG,OAAO,CAACE,KAAK,IAAI0G,SAAS,KAAKvF,SAAS,CAACpB,IAAI,IAAI6G,MAAM,KAAKtF,MAAM,CAACG,OAAO,IAAIkD,IAAI,CAACE,SAAS,KAAK,QAAQ,EAAE;UACxH2Z,MAAM,CAAC;YACL9X,SAAS,EAAEvF,SAAS,CAACC;UACvB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACDpF,GAAG,EAAE,sBAAsB;IAC3BmB,KAAK,EAAE,SAAS4a,oBAAoBA,CAAA,EAAG;MACrC,IAAIuI,iBAAiB,GAAG,IAAI,CAAC5jB,KAAK,CAAC4jB,iBAAiB;MACpD;;MAEA,IAAI,CAACA,iBAAiB,EAAE;QACtBje,QAAQ,CAAC2I,IAAI,CAACwL,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACmK,cAAc,CAAC;MACnE;IACF;EACF,CAAC,EAAE;IACD3kB,GAAG,EAAE,cAAc;IACnBmB,KAAK,EAAE,SAASskB,YAAYA,CAACjB,SAAS,EAAE;MACtC,IAAIkB,YAAY,GAAG,IAAI,CAAC/Z,KAAK;QACzBlB,KAAK,GAAGib,YAAY,CAACjb,KAAK;QAC1BC,SAAS,GAAGgb,YAAY,CAAChb,SAAS;QAClCE,MAAM,GAAG8a,YAAY,CAAC9a,MAAM;MAChC,IAAIuU,YAAY,GAAG,IAAI,CAACze,KAAK;QACzBqJ,KAAK,GAAGoV,YAAY,CAACpV,KAAK;QAC1BmU,gBAAgB,GAAGiB,YAAY,CAACjB,gBAAgB;QAChD1F,sBAAsB,GAAG2G,YAAY,CAAC3G,sBAAsB;QAC5D+L,iBAAiB,GAAGpF,YAAY,CAACoF,iBAAiB;QAClDoB,YAAY,GAAGxG,YAAY,CAACwG,YAAY;QACxC/T,cAAc,GAAGuN,YAAY,CAACvN,cAAc;QAC5CrG,KAAK,GAAG4T,YAAY,CAAC5T,KAAK;MAC9B,IAAI5C,IAAI,GAAGuP,aAAa,CAAC3M,KAAK,CAACd,KAAK,CAAC,EAAE,IAAI,CAAC/J,KAAK,CAAC;MAClD;;MAEA,IAAIiI,IAAI,EAAE;QACR,IAAIjJ,MAAM,GAAG4P,UAAU,CAAC3G,IAAI,CAACjJ,MAAM,CAAC;QACpC,IAAI+jB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACvF,gBAAgB,EAAEzT,KAAK,EAAE8Z,iBAAiB,EAAE7Z,SAAS,EAAE/B,IAAI,EAAEjJ,MAAM,EAAE8kB,SAAS,CAAC;QAEpH,IAAI5Z,MAAM,KAAKtF,MAAM,CAACG,OAAO,IAAIge,YAAY,EAAE;UAC7C,IAAImC,eAAe,GAAGzV,qBAAqB,CAACzQ,MAAM,EAAE8Y,sBAAsB,CAAC;UAC3E,IAAIna,YAAY,GAAGuR,eAAe,CAAClQ,MAAM,EAAE8Y,sBAAsB,CAAC;UAClE,IAAIqN,OAAO,GAAGtY,IAAI,CAACgE,KAAK,CAACG,WAAW,CAAChS,MAAM,EAAEimB,YAAY,EAAEnN,sBAAsB,CAAC,CAAC,IAAI,CAAC;UACxFhP,GAAG,CAAC;YACFE,KAAK,EAAE,cAAc;YACrBC,IAAI,EAAE,CAAC;cACL3J,GAAG,EAAE,OAAO;cACZmB,KAAK,EAAEsJ;YACT,CAAC,EAAE;cACDzK,GAAG,EAAE,WAAW;cAChBmB,KAAK,EAAEuJ;YACT,CAAC,EAAE;cACD1K,GAAG,EAAE,QAAQ;cACbmB,KAAK,EAAEyJ;YACT,CAAC,CAAC;YACFb,KAAK,EAAEA;UACT,CAAC,CAAC;UACF;;UAEA,IAAIW,SAAS,KAAKvF,SAAS,CAACP,MAAM,IAAI,IAAI,CAAC+d,YAAY,EAAE;YACvD,IAAImD,kBAAkB,GAAG,IAAI,CAACnD,YAAY;cACtC9Z,SAAS,GAAGid,kBAAkB,CAACjd,SAAS;cACxC4Z,MAAM,GAAGqD,kBAAkB,CAACrD,MAAM;YACtC;;YAEA,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC1f,OAAO,CAAC8F,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC+c,eAAe,EAAE;cAC/DC,OAAO,GAAGtY,IAAI,CAACgE,KAAK,CAACkR,MAAM,CAACnR,GAAG,GAAGqU,YAAY,CAAC;YACjD;UACF,CAAC,MAAM,IAAIjb,SAAS,KAAKvF,SAAS,CAACN,OAAO,IAAI,IAAI,CAAC+d,aAAa,EAAE;YAChE,IAAImD,mBAAmB,GAAG,IAAI,CAACnD,aAAa;cACxCoD,OAAO,GAAGD,mBAAmB,CAACC,OAAO;cACrCC,UAAU,GAAGF,mBAAmB,CAACld,SAAS;cAC1Cqd,OAAO,GAAGH,mBAAmB,CAACtD,MAAM;YAExC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC1f,OAAO,CAACkjB,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAACD,OAAO,IAAI,CAACJ,eAAe,EAAE;cACvFC,OAAO,GAAGtY,IAAI,CAACgE,KAAK,CAAC2U,OAAO,CAAC5U,GAAG,GAAGqU,YAAY,CAAC;YAClD,CAAC,MAAM;cACLE,OAAO,IAAIld,IAAI,CAAC8P,gBAAgB;YAClC;UACF;UAEAoN,OAAO,GAAGA,OAAO,IAAI,CAAC,GAAGA,OAAO,GAAG,CAAC;UACpC;;UAEA,IAAIjb,MAAM,KAAKtF,MAAM,CAACG,OAAO,EAAE;YAC7BkM,QAAQ,CAACkU,OAAO,EAAExnB,YAAY,EAAEuT,cAAc,CAAC;UACjD;QACF;MACF;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD5R,GAAG,EAAE,QAAQ;IACbmB,KAAK,EAAE,SAAS+a,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACrW,SAAS,EAAE,OAAO,IAAI;MAC3B,IAAIsgB,YAAY,GAAG,IAAI,CAACxa,KAAK;QACzBlB,KAAK,GAAG0b,YAAY,CAAC1b,KAAK;QAC1BG,MAAM,GAAGub,YAAY,CAACvb,MAAM;MAChC,IAAIiY,YAAY,GAAG,IAAI,CAACniB,KAAK;QACzB0K,UAAU,GAAGyX,YAAY,CAACzX,UAAU;QACpCrB,KAAK,GAAG8Y,YAAY,CAAC9Y,KAAK;QAC1BwR,KAAK,GAAGsH,YAAY,CAACtH,KAAK;QAC1BgJ,iBAAiB,GAAG1B,YAAY,CAAC0B,iBAAiB;QAClDhZ,KAAK,GAAGsX,YAAY,CAACtX,KAAK;MAC9B,IAAI5C,IAAI,GAAGuP,aAAa,CAAC3M,KAAK,CAACd,KAAK,CAAC,EAAE,IAAI,CAAC/J,KAAK,CAAC;MAClD,IAAIqgB,MAAM;MAEV,IAAInW,MAAM,KAAKtF,MAAM,CAACG,OAAO,IAAIkD,IAAI,EAAE;QACrCoY,MAAM,GAAG,aAAaljB,KAAK,CAAC6Q,aAAa,CAAC4T,WAAW,EAAElhB,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACuK,KAAK,EAAE;UAC9EmX,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB1X,UAAU,EAAEA,UAAU;UACtBrB,KAAK,EAAEA,KAAK;UACZ2Y,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBvB,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB5F,KAAK,EAAEA,KAAK;UACZkI,YAAY,EAAE,CAAC9a,IAAI,CAACuV,gBAAgB,KAAKzT,KAAK,KAAK,CAAC,IAAI8Z,iBAAiB,CAAC;UAC1E5b,IAAI,EAAEA,IAAI;UACV6Z,MAAM,EAAE,IAAI,CAACzX,KAAK,CAACyX;QACrB,CAAC,CAAC,CAAC;MACL;MAEA,OAAO,aAAa3kB,KAAK,CAAC6Q,aAAa,CAAC,KAAK,EAAE;QAC7CgO,SAAS,EAAE;MACb,CAAC,EAAEqE,MAAM,CAAC;IACZ;EACF,CAAC,CAAC,CAAC;EAEH,OAAOgD,OAAO;AAChB,CAAC,CAAClmB,KAAK,CAACsV,SAAS,CAAC;AAElBlT,eAAe,CAAC8jB,OAAO,EAAE,cAAc,EAAE;EACvC3Y,UAAU,EAAE,KAAK;EACjBrB,KAAK,EAAE,KAAK;EACZua,iBAAiB,EAAE,KAAK;EACxBlF,cAAc,EAAE,KAAK;EACrBC,mBAAmB,EAAE,KAAK;EAC1BnB,gBAAgB,EAAE,KAAK;EACvB1F,sBAAsB,EAAE,KAAK;EAC7BzK,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAAC,CAAC;EACpC0S,cAAc,EAAE,KAAK;EACrBuD,GAAG,EAAE,IAAI;EACT2B,YAAY,EAAE,EAAE;EAChB/T,cAAc,EAAE,GAAG;EACnB2S,iBAAiB,EAAE,KAAK;EACxB1D,cAAc,EAAE,KAAK;EACrBD,YAAY,EAAE,KAAK;EACnBrC,eAAe,EAAE,KAAK;EACtB9F,gBAAgB,EAAE,EAAE;EACpBlN,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,SAASzH,OAAO,EAAEW,MAAM,EAAEU,SAAS,EAAEG,MAAM,EAAEye,OAAO,IAAIqC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}