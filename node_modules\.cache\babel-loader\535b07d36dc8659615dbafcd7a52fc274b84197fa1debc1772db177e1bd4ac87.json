{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport default function TabPane(_ref) {\n  var prefixCls = _ref.prefixCls,\n    forceRender = _ref.forceRender,\n    className = _ref.className,\n    style = _ref.style,\n    id = _ref.id,\n    active = _ref.active,\n    animated = _ref.animated,\n    destroyInactiveTabPane = _ref.destroyInactiveTabPane,\n    tabKey = _ref.tabKey,\n    children = _ref.children;\n  var _React$useState = React.useState(forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visited = _React$useState2[0],\n    setVisited = _React$useState2[1];\n  React.useEffect(function () {\n    if (active) {\n      setVisited(true);\n    } else if (destroyInactiveTabPane) {\n      setVisited(false);\n    }\n  }, [active, destroyInactiveTabPane]);\n  var mergedStyle = {};\n  if (!active) {\n    if (animated) {\n      mergedStyle.visibility = 'hidden';\n      mergedStyle.height = 0;\n      mergedStyle.overflowY = 'hidden';\n    } else {\n      mergedStyle.display = 'none';\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: _objectSpread(_objectSpread({}, mergedStyle), style),\n    className: classNames(\"\".concat(prefixCls, \"-tabpane\"), active && \"\".concat(prefixCls, \"-tabpane-active\"), className)\n  }, (active || visited || forceRender) && children);\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "React", "classNames", "TabPane", "_ref", "prefixCls", "forceRender", "className", "style", "id", "active", "animated", "destroyInactiveTabPane", "tabKey", "children", "_React$useState", "useState", "_React$useState2", "visited", "setVisited", "useEffect", "mergedStyle", "visibility", "height", "overflowY", "display", "createElement", "concat", "role", "tabIndex"], "sources": ["C:/Users/<USER>/Workspace/cu-frontend-coll/node_modules/rc-tabs/es/TabPanelList/TabPane.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport default function TabPane(_ref) {\n  var prefixCls = _ref.prefixCls,\n      forceRender = _ref.forceRender,\n      className = _ref.className,\n      style = _ref.style,\n      id = _ref.id,\n      active = _ref.active,\n      animated = _ref.animated,\n      destroyInactiveTabPane = _ref.destroyInactiveTabPane,\n      tabKey = _ref.tabKey,\n      children = _ref.children;\n\n  var _React$useState = React.useState(forceRender),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      visited = _React$useState2[0],\n      setVisited = _React$useState2[1];\n\n  React.useEffect(function () {\n    if (active) {\n      setVisited(true);\n    } else if (destroyInactiveTabPane) {\n      setVisited(false);\n    }\n  }, [active, destroyInactiveTabPane]);\n  var mergedStyle = {};\n\n  if (!active) {\n    if (animated) {\n      mergedStyle.visibility = 'hidden';\n      mergedStyle.height = 0;\n      mergedStyle.overflowY = 'hidden';\n    } else {\n      mergedStyle.display = 'none';\n    }\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: _objectSpread(_objectSpread({}, mergedStyle), style),\n    className: classNames(\"\".concat(prefixCls, \"-tabpane\"), active && \"\".concat(prefixCls, \"-tabpane-active\"), className)\n  }, (active || visited || forceRender) && children);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,eAAe,SAASC,OAAOA,CAACC,IAAI,EAAE;EACpC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,WAAW,GAAGF,IAAI,CAACE,WAAW;IAC9BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,EAAE,GAAGL,IAAI,CAACK,EAAE;IACZC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,sBAAsB,GAAGR,IAAI,CAACQ,sBAAsB;IACpDC,MAAM,GAAGT,IAAI,CAACS,MAAM;IACpBC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;EAE5B,IAAIC,eAAe,GAAGd,KAAK,CAACe,QAAQ,CAACV,WAAW,CAAC;IAC7CW,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEpChB,KAAK,CAACmB,SAAS,CAAC,YAAY;IAC1B,IAAIV,MAAM,EAAE;MACVS,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIP,sBAAsB,EAAE;MACjCO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,MAAM,EAAEE,sBAAsB,CAAC,CAAC;EACpC,IAAIS,WAAW,GAAG,CAAC,CAAC;EAEpB,IAAI,CAACX,MAAM,EAAE;IACX,IAAIC,QAAQ,EAAE;MACZU,WAAW,CAACC,UAAU,GAAG,QAAQ;MACjCD,WAAW,CAACE,MAAM,GAAG,CAAC;MACtBF,WAAW,CAACG,SAAS,GAAG,QAAQ;IAClC,CAAC,MAAM;MACLH,WAAW,CAACI,OAAO,GAAG,MAAM;IAC9B;EACF;EAEA,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAC7CjB,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACkB,MAAM,CAAClB,EAAE,EAAE,SAAS,CAAC,CAACkB,MAAM,CAACd,MAAM,CAAC;IACjDe,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAEnB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,iBAAiB,EAAED,EAAE,IAAI,EAAE,CAACkB,MAAM,CAAClB,EAAE,EAAE,OAAO,CAAC,CAACkB,MAAM,CAACd,MAAM,CAAC;IAC9D,aAAa,EAAE,CAACH,MAAM;IACtBF,KAAK,EAAET,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC,EAAEb,KAAK,CAAC;IAC3DD,SAAS,EAAEL,UAAU,CAAC,EAAE,CAACyB,MAAM,CAACtB,SAAS,EAAE,UAAU,CAAC,EAAEK,MAAM,IAAI,EAAE,CAACiB,MAAM,CAACtB,SAAS,EAAE,iBAAiB,CAAC,EAAEE,SAAS;EACtH,CAAC,EAAE,CAACG,MAAM,IAAIQ,OAAO,IAAIZ,WAAW,KAAKQ,QAAQ,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}