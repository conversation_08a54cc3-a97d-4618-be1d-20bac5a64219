{"version": 3, "file": "polyfills.js", "sourceRoot": "", "sources": ["../../src/polyfills.ts"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,OAAO,MAAM,cAAc,CAAA;AAElC,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,MAAM,CAAA;AAE1D,MAAM,OAAO,WAAW;IACtB,IAAI,CAAoB;IACxB,IAAI,CAAQ;IACZ,GAAG,GAAG,IAAI,GAAG,EAAkB,CAAA;IAC/B,YAAY,IAAwB,EAAE,IAAY;QAChD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IACD,OAAO,CAAC,CAAS,EAAE,OAAoB;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QACtD,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,CAAA;QACvC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAM;QAC/B,MAAM,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;QAC3D,MAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAA;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;aACnC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACjD,CAAC;IACD,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;CACF;AAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAsB;IAC7C,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;CAC5C,CAAC,CAAA;AACF,KAAK,MAAM,CAAC,IAAI,gBAAgB;IAC9B,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAA;AAClD,KAAK,MAAM,CAAC,IAAI,WAAW;IACzB,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA;AAE7C,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;IACxB,KAAK,MAAM,EAAE,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACxB,CAAC;AACH,CAAC;AAED,iDAAiD;AACjD,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;IAC7C,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;QAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAC/C,CAAC;AAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;IACnB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,SAAS,CAAC,CAAA;AAChE,CAAC;AAED,eAAe,SAAS,CAAA", "sourcesContent": ["// the modules like -cjs.cts that override a module at .ts\nimport chalk from 'chalk'\nimport config from './config.js'\nimport * as console from './console.js'\nimport sources from './sources.js'\n\nconst { esmDialects = [], commonjsDialects = [] } = config\n\nexport class PolyfillSet {\n  type: 'esm' | 'commonjs'\n  name: string\n  map = new Map<string, string>()\n  constructor(type: 'esm' | 'commonjs', name: string) {\n    this.type = type\n    this.name = name\n  }\n  addFile(f: string, sources: Set<string>) {\n    const dotts = this.type === 'commonjs' ? 'cts' : 'mts'\n    const ending = `-${this.name}.${dotts}`\n    if (!f.endsWith(ending)) return\n    const ts = f.substring(0, f.length - ending.length) + '.ts'\n    const tsx = ts + 'x'\n    if (sources.has(ts)) this.map.set(f, ts)\n    else if (sources.has(tsx)) this.map.set(f, tsx)\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return [this.name, this.map]\n  }\n}\n\nconst polyfills = new Map<string, PolyfillSet>([\n  ['cjs', new PolyfillSet('commonjs', 'cjs')],\n])\nfor (const d of commonjsDialects)\n  polyfills.set(d, new PolyfillSet('commonjs', d))\nfor (const d of esmDialects)\n  polyfills.set(d, new PolyfillSet('esm', d))\n\nfor (const f of sources) {\n  for (const pf of polyfills.values()) {\n    pf.addFile(f, sources)\n  }\n}\n\n// delete any polyfill types that have no entries\nfor (const [name, pf] of polyfills.entries()) {\n  if (pf.map.size === 0) polyfills.delete(name)\n}\n\nif (polyfills.size) {\n  console.debug(chalk.cyan.dim('polyfills detected'), polyfills)\n}\n\nexport default polyfills\n"]}